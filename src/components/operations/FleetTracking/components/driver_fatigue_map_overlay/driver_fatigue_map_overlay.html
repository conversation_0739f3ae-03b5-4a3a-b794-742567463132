<div
  class="driver-fatigue-map-overlay"
  :class="isExpanded ? 'expanded' : 'collapsed'"
>
  <v-layout row wrap class="driver-fatigue-list-container" fill-height>
    <v-flex md12 pl-3 pr-2 py-2>
      <v-layout align-center class="banner-custom">
        <v-layout column>
          <v-layout>
            <span class="title--text pr-3">Driver Fatigue</span>
            <div class="priority-button-group light small" v-if="isExpanded">
              <div
                class="priority-button"
                v-for="option in priorityOptions"
                :key="option.id"
                :class="[option.class ? option.class : '', selectedPriorityOption === option.id ? 'active' : '']"
                @click="setSelectedPriority(option.id)"
              >
                {{option.name}}
              </div>
            </div>
          </v-layout>
          <span class="subtitle--text">
            <span class="error-type mr-2" v-if="nonCompliantCount">
              {{nonCompliantCount}} Require Breaks
            </span>
            <span class="warn-type" v-if="nonCompliantSoonCount">
              {{nonCompliantSoonCount}} Approaching Fatigue
            </span>
          </span>
        </v-layout>

        <span class="pl-2">
          <v-tooltip right>
            <template v-slot:activator="{ on }">
              <v-btn
                flat
                v-on="on"
                icon
                @click="isExpanded = !isExpanded"
                class="ma-0"
                small
              >
                <v-icon
                  size="18"
                  color="black"
                  v-html="isExpanded ? 'fal fa-chevron-up' : 'fal fa-chevron-down'"
                ></v-icon>
              </v-btn>
            </template>
            {{isExpanded ? `Hide list` : `Show list`}}
          </v-tooltip>
        </span>
      </v-layout>
    </v-flex>

    <v-layout
      wrap
      id="fatigue-list-main-content"
      class="driver-fatigue-list-scrollable"
      v-if="isExpanded"
    >
      <v-flex
        v-if="driverFatigueSnapshots && driverFatigueSnapshots.length"
        md12
        v-for="snapshot in driverFatigueSnapshots"
        :key="snapshot.driverId"
        class="driver-fatigue-snapshot__item"
      >
        <DriverFatigueSnapshotCard
          :key="`${snapshot.driverId}-${snapshot.isCompliant}`"
          :snapshot="snapshot"
          :currentSelectedId.sync="currentSelectedId"
        ></DriverFatigueSnapshotCard>
      </v-flex>
      <v-flex
        v-if="!driverFatigueSnapshots.length"
        md12
        class="driver-fatigue-snapshot__item"
      >
        <v-layout align-start justify-center fill-height>
          <span class="title--text pt-4">No Driver Fatigue Data</span>
        </v-layout>
      </v-flex>
    </v-layout>
  </v-layout>
</div>
