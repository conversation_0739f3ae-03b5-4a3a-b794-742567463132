.driver-fatigue-map-overlay {
  $map-padding: 8px;
  position: absolute;
  z-index: 3;

  padding: $map-padding;
  width: 20vw;

  &.expanded {
    height: 100%;
  }
  &.collapsed {
    height: auto;
  }
  .banner-custom {
    .title--text {
      font-size: $font-size-17;
      padding-bottom: 5px;
    }
    .subtitle--text {
      $error-color: #dc4c4c;
      $warn-color: #cd8c43;
      color: rgb(72, 69, 69);
      font-size: $font-size-13;

      .error-type {
        &::before {
          content: '\f071';
          font-family: $font-awesome-family;
          text-decoration: none;
          font-weight: 400;
          color: $error-color;
          font-size: $font-size-12;
        }
      }
      .warn-type {
        &::before {
          content: '\f06a';
          font-family: $font-awesome-family;
          font-weight: 400;
          color: $warn-color;
          font-size: $font-size-12;
        }
      }
    }
  }

  .driver-fatigue-list-container {
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    background-color: rgb(251, 251, 251);
    border-radius: 10px;
    height: 100%;
    position: relative;

    .driver-fatigue-list-scrollable {
      border-top: 1px solid rgb(218, 218, 218);
      height: calc(100% - 80px);
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 12px;
      }
      &::-webkit-scrollbar-track {
        background-color: rgb(145, 150, 187);
        -webkit-box-shadow: none;
      }
      &::-webkit-scrollbar-thumb {
        background: #ffffff;
        border-radius: 10px;
      }
      &::-webkit-scrollbar-thumb:hover {
        background: #e2e2e2; /* Change this to the color you want when hovering */
      }
      .driver-fatigue-snapshot__item {
        $margin-vertical: 4px;
        margin-top: $margin-vertical;
        margin-bottom: $margin-vertical;
        margin-left: 8px;
        margin-right: 8px;
      }
    }
  }
}
