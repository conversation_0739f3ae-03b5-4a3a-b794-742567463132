import DriverFatigueSnapshotCard from '@/components/operations/FleetTracking/components/driver_fatigue_snapshot_card/index.vue';
import DriverFatigueSnapshot from '@/interface-models/Driver/Fatigue/DriverFatigueSnapshot';
import { WorkDiaryActivityType } from '@/interface-models/Driver/WorkDiary/WorkDiaryActivityType';

import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  components: {
    DriverFatigueSnapshotCard,
  },
})
export default class DriverFatigueMapOverlay extends Vue {
  @Prop({ default: false }) public foo: boolean;

  public driverDetailsStore = useDriverDetailsStore();

  public currentSelectedId: string = '';
  public expanded: boolean = true;

  public selectedPriorityOption: number = 1;

  // Options for menu above main content div, selecting what priority we're
  // filtering by. Defaults to 'All'
  public priorityOptions: Array<{
    id: number;
    name: string;
    class?: string;
    priority: number | null;
  }> = [
    {
      id: 1,
      name: 'All',
      priority: null,
    },
    {
      id: 2,
      name: '',
      class: 'error-type',
      priority: 1,
    },
    {
      id: 3,
      name: '',
      class: 'warn-type',
      priority: 2,
    },
  ];

  /**
   * Modelled to icon button in the html. Controls visibility of the list of
   * snapshot cards
   */
  get isExpanded() {
    return this.expanded;
  }
  set isExpanded(value: boolean) {
    this.expanded = value;
  }

  /**
   * Gets the count of drivers who are in breach of fatigue rules.
   */
  get nonCompliantCount(): number {
    const driverFatigueSnapshots: DriverFatigueSnapshot[] = Array.from(
      this.driverDetailsStore.driverFatigueMap.values(),
    );
    return driverFatigueSnapshots.filter(
      (snapshot: DriverFatigueSnapshot) => !snapshot.isCompliant,
    ).length;
  }

  /**
   * Gets the count of drivers who are currently NOT in breach of fatigue rules,
   * but will be approaching the work limit soon.
   */
  get nonCompliantSoonCount() {
    const driverFatigueSnapshots: DriverFatigueSnapshot[] = Array.from(
      this.driverDetailsStore.driverFatigueMap.values(),
    );
    return driverFatigueSnapshots.filter(
      (snapshot: DriverFatigueSnapshot) =>
        snapshot.isCompliant &&
        snapshot.percentageWorked >= 90 &&
        !snapshot.isResting,
    ).length;
  }

  /**
   * Gets a sorted array of driver fatigue snapshots based on the selected
   * priority option.
   * - 1: Sorts non-compliant first, then compliant, then finished work.
   * - 2: Returns only non-compliant snapshots.
   * - 3: Returns snapshots where the driver is compliant, is 90%+ on their way
   *   to the work limit, and is not resting.
   *
   * @returns {DriverFatigueSnapshot[]} Sorted array of driver fatigue
   * snapshots.
   */
  get driverFatigueSnapshots(): DriverFatigueSnapshot[] {
    const snapshots: DriverFatigueSnapshot[] = Array.from(
      this.driverDetailsStore.driverFatigueMap.values(),
    );

    // Snapshots for drivers who have been resting for over 75 minutes
    const finishedWorkSnapshots = snapshots
      .filter((snapshot) => snapshot.isFinishedWork)
      .sort((a, b) => {
        // Sort by totalWorkTime descending
        if (a.totalWorkTime && b.totalWorkTime) {
          return b.totalWorkTime.epoch - a.totalWorkTime.epoch;
        }
        return 0;
      });
    // Snapshots for drivers where isCompliant is false
    const nonCompliantSnapshots = snapshots
      .filter((snapshot) => !snapshot.isCompliant)
      .sort((a, b) => {
        // Sort by totalWorkTime descending
        if (a.totalWorkTime && b.totalWorkTime) {
          return b.totalWorkTime.epoch - a.totalWorkTime.epoch;
        }
        return 0;
      });
    // Snapshots for drivers where isCompliant is true and isFinishedWork is false
    const everythingElse = snapshots
      .filter((snapshot) => !snapshot.isFinishedWork && snapshot.isCompliant)
      .sort((a, b) => {
        // Sort by totalWorkTime descending
        if (a.totalWorkTime && b.totalWorkTime) {
          return b.totalWorkTime.epoch - a.totalWorkTime.epoch;
        }
        return 0;
      });
    // Filter results based on selected priority option
    if (this.selectedPriorityOption === 1) {
      return [
        ...nonCompliantSnapshots,
        ...everythingElse,
        ...finishedWorkSnapshots,
      ];
    } else if (this.selectedPriorityOption === 2) {
      return nonCompliantSnapshots;
    } else if (this.selectedPriorityOption === 3) {
      return snapshots.filter(
        (snapshot) =>
          snapshot.isCompliant &&
          snapshot.percentageWorked >= 90 &&
          !snapshot.isResting,
      );
    } else {
      return snapshots;
    }
  }

  /**
   * Sets the selected priority option and scrolls to the top of the main
   * content div.
   * @param id - The ID of the priority option to be selected.
   */
  public setSelectedPriority(id: number): void {
    if (this.selectedPriorityOption === id) {
      return;
    }
    this.selectedPriorityOption = id;
    // Scroll main-content div back to top
    const mainContent = document.getElementById('fatigue-list-main-content');
    if (mainContent) {
      mainContent.scrollTop = 0;
    }
  }
}
