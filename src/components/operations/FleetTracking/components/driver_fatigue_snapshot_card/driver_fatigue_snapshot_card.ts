import { returnFormattedTime } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import DriverFatigueSnapshot from '@/interface-models/Driver/Fatigue/DriverFatigueSnapshot';
import { WorkDiaryActivityType } from '@/interface-models/Driver/WorkDiary/WorkDiaryActivityType';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useFleetMapStore } from '@/store/modules/FleetMapStore';
import moment from 'moment-timezone';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class DriverFatigueSnapshotCard extends Vue {
  @Prop({ default: false }) public snapshot: DriverFatigueSnapshot;
  // The driverId of the currently selected snapshot card
  @Prop({ default: false }) public currentSelectedId: string;

  public fleetAssetStore = useFleetAssetStore();
  public returnFormattedTime = returnFormattedTime;

  /**
   * Returns a list of details to display in the snapshot card in the
   */
  get snapshotDetails() {
    return [
      this.snapshot.workStartTime
        ? {
            title: 'Start Work',
            value: this.snapshot.workStartTime.readable,
          }
        : null,
      this.snapshot.totalWorkTime
        ? {
            title: 'Total Work Time',
            value: this.snapshot.totalWorkTime.readable,
          }
        : null,
      this.snapshot.totalRestTime
        ? {
            title: 'Total Rest Time',
            value: this.snapshot.totalRestTime.readable,
          }
        : null,
      this.snapshot.complianceStatus &&
      this.snapshot.complianceStatus.restRequiredAt &&
      this.snapshot.complianceStatus.requiredRestDuration
        ? {
            title: this.snapshot.isCompliant
              ? 'Next break due at'
              : 'Break was due at',
            value: `${this.snapshot.complianceStatus.restRequiredAt.readable} (${this.snapshot.complianceStatus.requiredRestDuration.readable})`,
          }
        : null,
      {
        title: 'Compliant',
        value: this.snapshot.isCompliant ? 'YES' : 'NO',
      },
      this.snapshot.complianceStatus
        ? {
            title: 'Description',
            value: this.snapshot.complianceStatus.violationDescription,
          }
        : null,
    ].filter((s) => s !== null);
  }

  /**
   * Returns the snapshot's activityBlocks, but with an additional block added
   * at the beginning and end such that the first block starts at the start of
   * the hour and the last block ends at the end of the hour.
   */
  get paddedActivityBlocks() {
    const firstBlock = this.snapshot.activityBlocks[0];
    const lastBlock =
      this.snapshot.activityBlocks[this.snapshot.activityBlocks.length - 1];
    const startOfFirstBlock = moment.tz(firstBlock.startEpoch, this.userLocale);
    const endOfLastBlock = moment.tz(lastBlock.endEpoch, this.userLocale);
    const startOfHour = startOfFirstBlock.clone().startOf('hour');
    const endOfHour = endOfLastBlock.clone().endOf('hour');

    const paddedBlocks = [...this.snapshot.activityBlocks];

    // If the first block doesn't start at the start of the hour, add a padding
    // block at the start
    if (!startOfFirstBlock.isSame(startOfHour)) {
      const startPaddingBlock = {
        activityType: WorkDiaryActivityType.PADDING,
        startEpoch: startOfHour.valueOf(),
        startReadable: startOfHour.format('h:mm a'),
        endEpoch: startOfFirstBlock.valueOf(),
        endReadable: startOfFirstBlock.format('h:mm a'),
        duration: startOfFirstBlock.diff(startOfHour),
        durationReadable: moment
          .utc(startOfFirstBlock.diff(startOfHour))
          .format('H [hrs], m [min], s [sec]'),
      };
      paddedBlocks.unshift(startPaddingBlock);
    }

    // If the last block doesn't end at the end of the hour, add a padding block
    // at the end
    if (!endOfLastBlock.isSame(endOfHour)) {
      const endPaddingBlock = {
        activityType: WorkDiaryActivityType.PADDING,
        startEpoch: endOfLastBlock.valueOf(),
        startReadable: endOfLastBlock.format('h:mm a'),
        endEpoch: endOfHour.valueOf(),
        endReadable: endOfHour.format('h:mm a'),
        duration: endOfHour.diff(endOfLastBlock),
        durationReadable: moment
          .utc(endOfHour.diff(endOfLastBlock))
          .format('H [hrs], m [min], s [sec]'),
      };
      paddedBlocks.push(endPaddingBlock);
    }

    return paddedBlocks;
  }

  /**
   * Getter and setter for the currentSelectedId prop. If the currentSelectedId
   * matches the snapshot's driverId, then return true. When setting sync to
   * prop in parent
   */
  get isSelected(): boolean {
    return this.currentSelectedId === this.snapshot.driverId;
  }
  set isSelected(newValue: boolean) {
    if (newValue) {
      // If the current card is not already selected, then update the currentSelectedId to be the driverId
      if (!this.isSelected) {
        this.$emit('update:currentSelectedId', this.snapshot.driverId);
      }
    } else {
      // If the current card is already selected, then update the currentSelectedId to be an empty string
      if (this.isSelected) {
        this.$emit('update:currentSelectedId', '');
      }
    }
  }

  get userLocale() {
    return useCompanyDetailsStore().userLocale;
  }

  /**
   * Returns a list of hours in the day, formatted as 'H' (24-hour time).
   */
  get timeLabels() {
    const timezone = this.userLocale;

    const startOfDay = moment.tz(
      moment.min(
        this.snapshot.activityBlocks.map((block) => moment(block.startEpoch)),
      ),
      timezone,
    );

    const endOfDay = moment.tz(
      moment.max(
        this.snapshot.activityBlocks.map((block) => moment(block.endEpoch)),
      ),
      timezone,
    );

    const duration = moment.duration(endOfDay.diff(startOfDay));
    const hours = Math.ceil(duration.asHours());
    const hourLabels = Array.from({ length: hours + 1 }, (_, i) =>
      startOfDay.clone().add(i, 'hours').format('H'),
    );
    return hourLabels;
  }

  /**
   * Returns the total duration of all activity blocks in the snapshot.
   */
  get totalDuration() {
    return this.snapshot.activityBlocks.reduce(
      (total, block) => total + block.duration,
      0,
    );
  }

  /**
   * Calculates the percentage along the full length of the activity blocks
   * progress-bar that the break was due at for a non-compliant driver
   */
  get breakDueAtPercent() {
    if (
      !this.isSelected ||
      this.snapshot.isCompliant ||
      !this.snapshot.complianceStatus ||
      !this.snapshot.workStartTime ||
      this.snapshot.isFinishedWork ||
      !this.snapshot.complianceStatus.restRequiredAt
    ) {
      return 0;
    }
    const restRequiredAt = this.snapshot.complianceStatus.restRequiredAt.epoch;
    const startEpoch = this.paddedActivityBlocks[0].startEpoch;
    const endEpoch =
      this.paddedActivityBlocks[this.paddedActivityBlocks.length - 1].endEpoch;

    // Using the values above, calculate at what percentage of the day the break is due
    return ((restRequiredAt - startEpoch) / (endEpoch - startEpoch)) * 100;
  }

  /**
   * Calculates the percentage along the full length of the activity blocks
   * progress-bar that the startWork time lies at
   */
  get startWorkPercent() {
    if (!this.isSelected || !this.snapshot.workStartTime) {
      return 0;
    }
    const startWorkEpoch = this.snapshot.workStartTime.epoch;
    const startEpoch = this.paddedActivityBlocks[0].startEpoch;
    const endEpoch =
      this.paddedActivityBlocks[this.paddedActivityBlocks.length - 1].endEpoch;

    return ((startWorkEpoch - startEpoch) / (endEpoch - startEpoch)) * 100;
  }

  /**
   * Returns the FleetAssetSummary associated with the driverId of the snapshot
   */
  get associatedVehicle(): FleetAssetSummary | undefined {
    // Find entry in this.fleetAssetStore.fleetAssetActiveDriver map where the value
    // contains the driverId, and return the key
    const driverId = this.snapshot.driverId;
    const fleetAssetActiveDriver = this.fleetAssetStore
      .fleetAssetActiveDriver as Map<string, string>;

    const associatedFleetAssetId = Array.from(
      fleetAssetActiveDriver.entries(),
    ).find(([, value]) => value.includes(driverId));
    return (
      associatedFleetAssetId &&
      this.fleetAssetStore.getFleetAssetFromFleetAssetId(
        associatedFleetAssetId[0],
      )
    );
  }

  /**
   * Using the snapshot geolocation (if available), jump to that location on the
   * map by calling function in FleetMapStore
   */
  public jumpToLastKnownLocation() {
    if (!this.snapshot.geoLocation[0] || !this.snapshot.geoLocation[1]) {
      return;
    }
    useFleetMapStore().setJumpToLocation([
      this.snapshot.geoLocation[0],
      this.snapshot.geoLocation[1],
    ]);
  }

  /**
   * Returns a human-readable string representation of the given activity type.
   * @param activityType - The activity type to convert to a readable string.
   * @returns A string representing the activity type in human-readable format.
   */
  public returnReadableActivityType(
    activityType: WorkDiaryActivityType,
  ): string {
    switch (activityType) {
      case WorkDiaryActivityType.WORK:
        return 'Work';
      case WorkDiaryActivityType.REST:
      case WorkDiaryActivityType.BREAK:
        return 'Resting/On Break';
      case WorkDiaryActivityType.PADDING:
        return '';
      case WorkDiaryActivityType.INVALID_REST:
        return 'Invalid Break (less than 15 min)';
      default:
        return 'Unknown';
    }
  }
}
