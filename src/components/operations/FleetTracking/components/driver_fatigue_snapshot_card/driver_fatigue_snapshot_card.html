<v-layout
  class="driver-fatigue-snapshot-card"
  :class="{'selected': isSelected}"
>
  <v-flex md12>
    <v-layout
      justify-space-between
      align-center
      class="snapshot-card__toprow"
      row
      wrap
      @click="isSelected = !isSelected"
    >
      <v-flex md12 pb-1>
        <v-layout>
          <v-layout column justify-space-between>
            <span class="title-text"
              >{{ snapshot.driverName ? snapshot.driverName : 'N/A' }}</span
            >
            <span class="subtitle-text"
              >Updated: {{ returnFormattedTime(snapshot.lastUpdated, 'HH:mm a')
              }}</span
            >
          </v-layout>
          <v-layout column justify-center align-end>
            <span
              class="icon-container"
              v-if="!snapshot.isCompliant || snapshot.isResting"
              :class="{
                'icon-container--noncompliant': !snapshot.isCompliant,
                'icon-container--resting': snapshot.isResting && !snapshot.isFinishedWork,
                'icon-container--finished': snapshot.isFinishedWork,
              }"
            >
              {{snapshot.statusMessage}}
            </span>
            <span v-else>
              <v-progress-circular
                :size="24"
                :width="4"
                :value="snapshot.percentageWorked"
                color="#cd8c43"
              ></v-progress-circular>
            </span>
          </v-layout>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <div class="time-labels" @click="isSelected = !isSelected">
          <div
            v-for="(time, index) in timeLabels"
            :key="index"
            class="time-label"
            :data-time="time"
          ></div>
        </div>
        <div
          @click="isSelected = !isSelected"
          class="progress-bar"
          :key="snapshot.isCompliant"
          :class="{'non-compliant': !snapshot.isCompliant}"
        >
          <div
            v-for="(block, index) in paddedActivityBlocks"
            :key="index"
            class="progress-bar__block"
            :class="[`progress-bar__block--${block.activityType.toLowerCase()}`]"
            :style="{ flexBasis: `${(block.duration / totalDuration) * 100}%`} "
            :title="`${returnReadableActivityType(block.activityType)} -- Start: ${block.startReadable}, End: ${block.endReadable} (${block.durationReadable}) ${block.note ? '--' : ''} ${block.note ? block.note : ''}`.trim()"
          />
          <div
            v-if="breakDueAtPercent && isSelected"
            class="break-time-line"
            :style="{ left: `${breakDueAtPercent}%` }"
          />
          <div
            v-if="breakDueAtPercent && isSelected"
            class="break-time-label"
            :style="{ left: `${breakDueAtPercent}%` }"
          >
            {{ snapshot.complianceStatus.restRequiredAt.readable }}
          </div>
          <div
            v-if="startWorkPercent && isSelected"
            class="start-work-line"
            :style="{ left: `${startWorkPercent}%` }"
          />
          <div
            v-if="startWorkPercent && isSelected"
            class="start-work-label"
            :style="{ left: `${startWorkPercent}%` }"
          >
            {{ snapshot.workStartTime.readable }}
          </div>
        </div>
      </v-flex>
    </v-layout>

    <v-layout wrap class="details-section" :class="{'hidden': !isSelected}">
      <v-flex md12>
        <v-btn
          small
          block
          flat
          color="blue"
          @click="jumpToLastKnownLocation"
          :disabled="!(snapshot.geoLocation[0] && snapshot.geoLocation[1])"
        >
          <span v-if="snapshot.geoLocation[0] && snapshot.geoLocation[1]"
            >Jump to Last Known Location</span
          >
          <span v-else>Location Unavailable</span>
        </v-btn>
      </v-flex>
      <v-flex md12>
        <v-layout wrap v-if="isSelected">
          <v-flex
            v-for="(detail, index) in snapshotDetails"
            :key="index"
            :class="`snapshot-detail--vertical light ${index === snapshotDetails.length - 1 ? 'md12' : index % 2 === 0 ? 'md5' : 'md7'}`"
          >
            <v-layout class="title-text"> {{ detail.title }} </v-layout>
            <span class="value-text"> {{ detail.value || 'N/A' }} </span>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </v-flex>
</v-layout>
