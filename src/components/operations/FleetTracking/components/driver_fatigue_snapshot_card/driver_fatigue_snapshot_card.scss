.driver-fatigue-snapshot-card {
  background-color: rgb(255, 255, 255);
  border-radius: 8px;
  transition: 0.3s;

  $red-swatch: #ff4d4f;
  $green-swatch: rgb(62, 169, 75);
  $yellow-swatch: rgb(242, 192, 74);

  &.selected {
    border: 2px solid #b8cad5;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  }

  &:hover {
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  }

  .icon-container {
    color: white;
    border-radius: 4px;
    padding: 2px 8px;
    font-size: $font-size-10;
    font-weight: 600;
    white-space: nowrap;
    text-transform: uppercase;

    &--noncompliant {
      background-color: $red-swatch;
    }
    &--resting {
      background-color: white;
      color: $green-swatch;
      border: 1px solid $green-swatch;
    }
    &--finished {
      color: $green-swatch;
      background-color: transparent;
    }
  }

  .snapshot-card__toprow {
    padding: 8px 16px;
    border-radius: 8px;

    &:hover {
      cursor: pointer;
      background-color: rgb(249, 250, 255);
    }

    .title-text {
      font-size: $font-size-13;
      font-weight: 600;
      font-family: $sub-font-family;
    }
    .subtitle-text {
      font-size: $font-size-10;
      font-weight: 400;
    }
    .arrow-icon {
      font-size: $font-size-16;
    }
  }

  .snapshot-detail--vertical {
    padding: 3px 0px;

    &.light {
      .title-text {
        color: #45454c;
      }
      .value-text {
        color: black;
      }
    }
    .title-text {
      color: rgb(176, 176, 189);
      text-transform: uppercase;
      font-size: $font-size-11;
      font-weight: 500;
      // padding-bottom: 2px;
    }
    .value-text {
      font-weight: 400;
      color: white;
      font-size: $font-size-13;
    }
  }

  .progress-bar {
    display: flex;
    position: relative;
    // overflow: hidden;
    border: 1px solid rgb(222, 222, 222);

    &.non-compliant {
      .progress-bar__block--work {
        background-color: $red-swatch;
      }
    }
    $line-height: 15px;
    $line-width: 2px;
    $label-font-size: $font-size-10;
    $label-font-weight: 600;
    $label-font-family: $sub-font-family;
    $position-top: calc(100% + 2px);

    @mixin line {
      position: absolute;
      top: $position-top;
      height: $line-height;
      width: $line-width;
    }

    @mixin label {
      position: absolute;
      top: $position-top;
      white-space: nowrap;
      font-size: $label-font-size;
      font-weight: $label-font-weight;
      font-family: $label-font-family;
    }

    .break-time-line {
      @include line;
      background-color: $red-swatch;
    }
    .break-time-label {
      @include label;
      transform: translateX(-110%);
    }
    .start-work-line {
      @include line;
      background-color: $yellow-swatch;
    }
    .start-work-label {
      @include label;
      transform: translateX(15%);
    }
    .progress-bar__block {
      height: 14px;
      &--work {
        background-color: $yellow-swatch;
      }
      &--rest {
        background-color: $green-swatch;
      }
      &--invalid_rest {
        background: repeating-linear-gradient(
          70deg,
          $green-swatch,
          $green-swatch 5px,
          grey 5px,
          grey 10px
        );
      }
      &--padding {
        background: repeating-linear-gradient(
          70deg,
          white,
          white 5px,
          grey 5px,
          grey 10px
        );
      }
    }
  }

  .time-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 4px;
    .time-label {
      // font-size: $font-size-10;
      // font-weight: 600;
      // font-family: $sub-font-family;
      position: relative;
      top: 5px;
      z-index: 4;
      height: 4px;
      border-left: 1.5px solid rgba(0, 0, 0, 0.2);
      width: 0;
      &:first-child,
      &:last-child {
        border-left: none;
      }

      &::before {
        content: attr(data-time);
        position: absolute;
        font-size: $font-size-10;
        font-weight: 600;
        font-family: $sub-font-family;
        top: -14px;
        left: 50%;
        transform: translateX(-50%);
        text-align: center;
      }
    }
  }

  .details-section {
    border-top: 1px solid rgb(238, 238, 238);
    padding: 8px 16px;
    margin-top: 12px;
    height: 300px;
    overflow: hidden;
    transition: height 0.2s ease-in-out;

    &.hidden {
      height: 0;
      padding: 0;
      border-top: none;
    }
  }
}
