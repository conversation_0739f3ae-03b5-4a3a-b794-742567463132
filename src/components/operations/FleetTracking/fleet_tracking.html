<section class="fleet-tracking" style="color: black">
  <v-layout
    v-if="!fleetAssetTrackingWindowOpen || (windowMode && initialMapDataReceived)"
  >
    <v-flex md12>
      <TrackingMap
        v-if="initialMapCenter && initialMapCenter !== [0,0] && gpsPositionList !== undefined"
        id="trackingMap"
        ref="trackingMap"
        :key="windowMode ? 'window-mode-map' : 'csr-map'"
        class="map-container"
        :windowMode="windowMode"
        :driverGpsCoordinates="gpsPositionList"
        :jumpToLocationCoordinates="jumpToLocationCoordinates"
        :initialMapCenter="initialMapCenter"
        :mapHeight="mapHeight"
        :showVehicleList="windowMode"
        :showClusterGroupings="windowMode"
        @markerSelectedOnMap="markerSelectedOnMap"
        @requestReverseGeocodeList="requestReverseGeocodeList"
      ></TrackingMap>

      <div class="open-window-btn-container" v-if="!windowMode">
        <v-icon
          class="open-window-btn"
          color="#000000"
          size="18"
          @click="openMapWindow"
          >fad fa-window-restore
        </v-icon>
      </div>
    </v-flex>
    <DriverFatigueMapOverlay v-if="windowMode"></DriverFatigueMapOverlay>
  </v-layout>
  <v-layout
    justify-center
    align-center
    class="restore-map-container"
    v-if="fleetAssetTrackingWindowOpen && !windowMode"
  >
    <v-btn @click="restoreMap" color="info" outline>Restore Map</v-btn>
  </v-layout>
</section>
