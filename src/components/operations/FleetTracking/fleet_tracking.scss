.asset-tracking-info-container {
  position: absolute;
  top: 0px;

  .column-header {
    color: $pud-flag;
    font-weight: 500;
  }

  span {
    line-height: 1rem;
    display: block;
  }
}

.fleet-tracking {
  position: relative;
  overflow: hidden;
}

.close-info-btn {
  cursor: pointer;
}

.open-window-btn-container {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;
  top: 185px;
  right: 10px;
  z-index: 10000 !important;
  box-shadow: 0 0 0 2px $shadow-color;
  width: 29px;
  height: 29px;
  border-radius: 4px;
  padding: 0;
  outline: none;
  border: 0;

  cursor: pointer;
}

.restore-map-container {
  height: calc(100vh - 40px);
  width: 100%;
}
