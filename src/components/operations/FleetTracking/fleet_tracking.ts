import DriverFatigueMapOverlay from '@/components/operations/FleetTracking/components/driver_fatigue_map_overlay/index.vue';
import TrackingMap from '@/components/operations/maps/tracking_map/index.vue';
import {
  filterJobsByOperationsChannel,
  filterMatches,
} from '@/helpers/AppLayoutHelpers/OperationsChannelHelpers';
import {
  BroadcastChannelType,
  BroadcastIds,
  FLEET_TRACKING_CHANNEL_ID,
} from '@/helpers/NotificationHelpers/BroadcastChannelHelpers';
import DriverFatigueSnapshot from '@/interface-models/Driver/Fatigue/DriverFatigueSnapshot';
import { BroadcastMessage } from '@/interface-models/Generic/OperationScreenOptions/BroadcastMessage';
import { FleetTrackingMapPayload } from '@/interface-models/Generic/Position/FleetTrackingMapPayload';
import GpsMarkerDetails from '@/interface-models/Generic/Position/GpsMarkerDetails';
import ReverseGeocodeListRequest from '@/interface-models/Generic/Route/Geocode/ReverseGeocodeListRequest';
import { useBroadcastChannelStore } from '@/store/modules/BroadcastChannelStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetMapStore } from '@/store/modules/FleetMapStore';
import { useGpsStore } from '@/store/modules/GpsStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useFilterStore } from '@/store/modules/OperationsFilterStore';
import Mitt from '@/utils/mitt';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component({
  components: {
    TrackingMap,
    DriverFatigueMapOverlay,
  },
})
export default class FleetTracking extends Vue {
  // This component exists in two places:
  // 1. Book Job Index - windowMode = false
  // 2. Separate Route  (in popout window) - windowMode = true
  @Prop({ default: false }) public windowMode: boolean;
  @Prop({ default: true }) public fleetAssetTrackingWindowOpen: boolean;
  public fleetMapStore = useFleetMapStore();
  public companyDetailsStore = useCompanyDetailsStore();
  public driverDetailsStore = useDriverDetailsStore();
  public filterStore = useFilterStore();
  public jobStore = useJobStore();

  public driverGpsCoordinates: GpsMarkerDetails[] = [];

  public updateInterval: ReturnType<typeof setInterval> | null = null;

  public open: boolean = false;

  // Required Parameters for Map. These will be passed in as props and will
  // either be provided from store or will be sent through broadcast channel
  // (for window mode)
  public gpsCoordinatesByDriverIdMap: Map<string, GpsMarkerDetails> = new Map();

  public initialMapDataReceived: boolean = false;

  get mapHeight() {
    return this.windowMode ? '100vh' : '100vh - 60px';
  }

  get gpsPositionList(): GpsMarkerDetails[] | undefined {
    // First check if all filters are empty
    if (!this.filterStore.selectedOperationsChannel?.fleetFilterOptions) {
      return this.driverGpsCoordinates;
    }

    const fleetFilterOptions =
      this.filterStore.selectedOperationsChannel.fleetFilterOptions;

    return this.driverGpsCoordinates.filter((gps: GpsMarkerDetails) => {
      // Vehicle class filter
      const vehicleClassMatch = filterMatches(
        fleetFilterOptions.vehicleClasses,
        gps.truckClass,
      );
      const affiliationMatch = filterMatches(
        fleetFilterOptions.ownerAffiliations,
        gps.truckClass,
      );

      // Client ID filter - check if asset has jobs with selected clients
      // TODO: OPS-1618 what do we want the expected behaviour to be here? Also:
      // should it show vehicles for jobs that are visible, despite not being
      // visible in the fleet filters?
      const clientMatch = filterJobsByOperationsChannel(
        this.jobStore.operationJobsList,
        this.filterStore.selectedOperationsChannel,
      ).some((job) => job.fleetAssetId === gps.fleetAssetId);

      return vehicleClassMatch || clientMatch || affiliationMatch;
    });
  }

  /**
   * Computes coordinates for where the map should jump to.
   * If the defined jumpToLocation coordinates in FleetMapStore is equal to [0,0], we
   * will use the divisions depot coordinates as the default location.
   *
   * @returns {[number, number]}
   */
  get jumpToLocationCoordinates(): [number, number] {
    const jumpToLocation: [number, number] = this.fleetMapStore.jumpToLocation;
    return jumpToLocation[0] !== 0 && jumpToLocation[1] !== 0
      ? this.fleetMapStore.jumpToLocation
      : this.companyDetailsStore.divisionDepotCoordinates;
  }

  // Coordinates for map to initialise as center
  get initialMapCenter(): [number, number] {
    if (
      this.windowMode &&
      !this.jumpToLocationCoordinates[0] &&
      this.gpsPositionList
    ) {
      if (
        this.gpsPositionList[0] &&
        this.gpsPositionList[0].longitude &&
        this.gpsPositionList[0].latitude
      ) {
        return [
          this.gpsPositionList[0].longitude,
          this.gpsPositionList[0].latitude,
        ];
      }
    }

    return this.jumpToLocationCoordinates;
  }

  public breakOffMap() {
    this.open = !this.open;
  }

  public restoreMap() {
    this.fleetMapStore.setFleetTrackingWindow(false);
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.FLEET_TRACKING,
      new BroadcastMessage('closeWindow', true),
    );
    useBroadcastChannelStore().closeChannel(
      BroadcastChannelType.FLEET_TRACKING,
    );
  }

  public openMapWindow() {
    const routeUrl = this.$router.resolve({
      path: '/fleet-tracking',
    });
    window.open(routeUrl.href, 'Fleet Tracking', 'menubar=no');
    this.fleetMapStore.createBroadcastChannel();
    this.fleetMapStore.setFleetTrackingWindow(true);
  }
  // Send message to main window that a marker was selected. Will only be handled
  // if on the Operations page
  public markerSelectedOnMap(gpsString: string) {
    const gpsData: GpsMarkerDetails = JSON.parse(gpsString) as GpsMarkerDetails;
    if (this.windowMode) {
      const data: BroadcastMessage = new BroadcastMessage(
        'markerSelectedOnMap',
        gpsData,
      );
      useBroadcastChannelStore().postMessageToChannel(
        BroadcastChannelType.FLEET_TRACKING,
        data,
      );
    }
  }

  // The locale of the division
  get userLocale() {
    return this.companyDetailsStore.userLocale;
  }

  /**
   * Handles the emit send from the TrackingMap child component. Sends the request to the main window to reverse geocode the list of coordinates.
   * @param request contains the list of coordinates to reverse geocode
   */
  public requestReverseGeocodeList(request: ReverseGeocodeListRequest) {
    console.debug('FleetTracking - sending requestReverseGeocodeList');
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.FLEET_TRACKING,
      new BroadcastMessage('requestReverseGeocodeList', request),
    );
  }

  /**
   * Handles response from BroadcastChannel containing the reverse geocoded list
   * of coordinates. Calls method in child component to handle the response.
   * @param response contains the list of reverse geocoded coordinates
   */
  public handleReverseGeocodeListResponse(response: ReverseGeocodeListRequest) {
    if (response && !!response.responseId) {
      // Find TrackingMap component in children
      const foundTrackingMap = this.$refs.trackingMap as InstanceType<
        typeof TrackingMap
      >;
      if (
        foundTrackingMap &&
        foundTrackingMap.awaitingResId === response.responseId
      ) {
        foundTrackingMap.handleReverseGeocodeListResponse(response);
      }
    }
  }

  /**
   * Sets up the broadcast channel for window mode.
   *
   * If the component is in window mode, it sends a message to the main window
   * to indicate that the window is open, sets up a listener for incoming
   * broadcast messages from the main window, and sets up a handler for the
   * `onbeforeunload` event to indicate when the window is closed.
   *
   * @returns {void}
   */
  public initialiseWindowMode(): void {
    // If we are in windowMode, send message to main window that the window is
    // OPEN
    const data: BroadcastMessage = new BroadcastMessage(
      'fleetTrackingWindowOpen',
      true,
    );
    const broadcast = useBroadcastChannelStore().getBroadcastChannel(
      BroadcastChannelType.FLEET_TRACKING,
    );
    broadcast.postMessage(data);
    // Set listener for incoming broadcast messages from main window
    broadcast.onmessage = this.handleBroadcastMessage.bind(this);
    // Before window unloads, send message to main window that the window is CLOSED
    window.onbeforeunload = this.windowWasClosed;
  }

  /**
   * Updates the compliance status of the driver fatigue snapshots.
   *
   * For all entries in this.driverDetailsStore.driverFatigueMap that are
   * compliant, it checks if the current time is greater than the
   * restRequiredAt. If so, it sets isCompliant to false and updates the store.
   *
   * @returns {void}
   */
  public updateRecordsPastRestRequiredAt(): void {
    const driverFatigueSnapshots: DriverFatigueSnapshot[] = Array.from(
      this.driverDetailsStore.driverFatigueMap.values(),
    );
    const updatedSnapshots = driverFatigueSnapshots
      .map((snapshot: DriverFatigueSnapshot) => {
        if (snapshot.isCompliant && snapshot.complianceStatus.restRequiredAt) {
          const restRequiredAt = snapshot.complianceStatus.restRequiredAt.epoch;
          const now = Date.now();
          if (restRequiredAt < now) {
            snapshot.complianceStatus.isCompliant = false;
            return snapshot;
          }
        }
        return null;
      })
      .filter((snapshot) => snapshot !== null) as DriverFatigueSnapshot[];

    if (updatedSnapshots.length > 0) {
      updatedSnapshots.forEach((snapshot) => {
        this.driverDetailsStore.commitUpdatedDriverFatigueSnapshot(snapshot);
      });
    }
  }

  /**
   * Handles incoming broadcast messages.
   *
   * @param {any} message - The broadcast message to handle.
   * The message object should have a `data` property with an `id` and `value`.
   * The `id` should be a string that identifies the type of the message.
   * The `value` can be any data associated with the message.
   *
   * The function handles the following message types:
   * - 'initialMapData': Sets initial data for the map.
   * - 'driverGpsData': Updates the GPS coordinates of the drivers.
   * - 'responseJumpToLocation': Sets the location to jump to on the map.
   * - 'closeWindow': Closes the window.
   * - 'driverFatigueSnapshots': Updates the fatigue snapshots of the drivers.
   * - 'updatedDriverFatigueSnapshot': Updates a specific fatigue snapshot.
   */
  public handleBroadcastMessage(message: any): void {
    console.log(`FleetTracking - Received... '${message.data.id}'`);
    switch (message.data.id) {
      case BroadcastIds.FLEET_TRACKING.TO_EXTERNAL.INITIAL_MAP_DATA:
        // Set initial data for the map
        const payload: FleetTrackingMapPayload = message.data.value;
        this.companyDetailsStore.setUserLocale(payload.userLocale);
        this.fleetMapStore.setJumpToLocation(payload.initialMapCenter);
        this.driverGpsCoordinates = payload.gpsData;
        this.addMarkersToGpsCoordinatesMap(payload.gpsData);

        // Set operations channels to store
        this.filterStore.setOperationsChannels(
          payload.operationsChannels ?? [],
        );
        this.filterStore.setSelectedOperationsChannelIds(
          payload.selectedOperationsChannelIds ?? null,
        );

        // If there are fatigueSnapshots, add additional fields and commit to store
        if (payload.fatigueSnapshots) {
          this.addFieldsToDriverFatigueSnapshotList(payload.fatigueSnapshots);
          this.driverDetailsStore.commitDriverFatigueSnapshotList(
            payload.fatigueSnapshots,
          );
          // After committing to store, re-commit any records that are past
          // their due break time
          this.updateRecordsPastRestRequiredAt();
        }
        this.initialMapDataReceived = true;
        break;
      case BroadcastIds.FLEET_TRACKING.TO_EXTERNAL.DRIVER_GPS_DATA:
        if (message.data.value) {
          this.driverGpsCoordinates = message.data.value;
          this.addMarkersToGpsCoordinatesMap(message.data.value);
        }
        break;
      case BroadcastIds.FLEET_TRACKING.TO_EXTERNAL.RESPONSE_JUMP_TO_LOCATION:
        this.fleetMapStore.setJumpToLocation(message.data.value);
        break;
      case BroadcastIds.FLEET_TRACKING.TO_EXTERNAL.CLOSE_WINDOW:
        window.close();
        break;
      case BroadcastIds.FLEET_TRACKING.TO_EXTERNAL.OPERATIONS_CHANNEL_UPDATED:
        this.setUpdatedOperationsChannels(message.data.value);
        break;
      case BroadcastIds.FLEET_TRACKING.TO_EXTERNAL.DRIVER_FATIGUE_SNAPSHOTS:
        // Handles list of DriverFatigueSnapshot sent from the main window
        let fatigueSnapshots = message.data.value as DriverFatigueSnapshot[];
        // Initialise as DriverFatigueSnapshot objects
        fatigueSnapshots = fatigueSnapshots.map((snapshot) => {
          return Object.assign(new DriverFatigueSnapshot(), snapshot);
        });

        const isFirstCommit =
          this.driverDetailsStore.driverFatigueMap.size === 0;
        // Use additional fields using the GpsMarkerDetails objects then commit
        // to store
        this.addFieldsToDriverFatigueSnapshotList(fatigueSnapshots);
        this.driverDetailsStore.commitDriverFatigueSnapshotList(
          fatigueSnapshots,
        );

        // If it's the first commit, update and re-commit any records that are
        // past their due break time
        if (isFirstCommit) {
          this.updateRecordsPastRestRequiredAt();
        }
        break;
      case BroadcastIds.FLEET_TRACKING.TO_EXTERNAL
        .UPDATED_DRIVER_FATIGUE_SNAPSHOT:
        // Handles single DriverFatigueSnapshot sent from the main window
        const updatedSnapshot: DriverFatigueSnapshot = Object.assign(
          new DriverFatigueSnapshot(),
          message.data.value,
        );
        const marker = this.gpsCoordinatesByDriverIdMap.get(
          updatedSnapshot.driverId,
        );
        if (marker) {
          this.updateSnapshotFromMarker(updatedSnapshot, marker);
        }
        this.driverDetailsStore.commitUpdatedDriverFatigueSnapshot(
          updatedSnapshot,
        );
        break;
      case BroadcastIds.FLEET_TRACKING.TO_EXTERNAL
        .REVERSE_GEOCODE_LIST_RESPONSE:
        this.handleReverseGeocodeListResponse(message.data.value);
        break;
    }
  }

  /**
   * Window mode only. Called when 'fleetFilterUpdated' broadcast message is
   * received. Updates the fleet filter in the store and refreshes the map
   * data to reflect the filtered list.
   * @param channelIds - The list of fleet IDs to filter by.
   */
  public setUpdatedOperationsChannels(channelIds: string[] | null) {
    this.filterStore.setSelectedOperationsChannelIds(channelIds);

    // Use nextTick to ensure the filtered data has recomputed
    this.$nextTick(() => {
      const foundTrackingMap = this.$refs.trackingMap as InstanceType<
        typeof TrackingMap
      >;
      if (foundTrackingMap) {
        // Refresh map with updated client filter
        foundTrackingMap.refreshMapData(foundTrackingMap.mapboxObject);
      }
    });
  }

  /**
   * Adds additional fields to each DriverFatigueSnapshot in the
   * provided list. The additional fields are taken from the corresponding
   * GpsMarkerDetails object in the gpsCoordinatesByDriverIdMap.
   *
   * @param {DriverFatigueSnapshot[]} fatigueSnapshots - The list of
   * DriverFatigueSnapshot objects to be updated.
   */
  public addFieldsToDriverFatigueSnapshotList(
    fatigueSnapshots: DriverFatigueSnapshot[],
  ) {
    fatigueSnapshots.forEach((snapshot: DriverFatigueSnapshot) => {
      const marker = this.gpsCoordinatesByDriverIdMap.get(snapshot.driverId);
      if (marker) {
        this.updateSnapshotFromMarker(snapshot, marker);
      }
    });
  }

  /**
   * Updates the list of DriverFatigueSnapshot objects in the driverFatigueMap
   * based on the provided GpsMarkerDetails objects. It then commits the updated
   * list of DriverFatigueSnapshot objects to the store.
   *
   * @param {GpsMarkerDetails[]} markers - The list of GpsMarkerDetails objects
   * used to update the DriverFatigueSnapshot objects.
   */
  public updateDriverFatigueSnapshotsFromGpsMarker(
    markers: GpsMarkerDetails[],
  ) {
    const snapshots: DriverFatigueSnapshot[] = markers.flatMap(
      (marker: GpsMarkerDetails) => {
        const snapshot = this.driverDetailsStore.driverFatigueMap.get(
          marker.driverId,
        );
        if (snapshot) {
          this.updateSnapshotFromMarker(snapshot, marker);
          return [snapshot];
        }
        return [];
      },
    );
    this.driverDetailsStore.commitDriverFatigueSnapshotList(snapshots);
  }

  /**
   * If the component is in window mode, it sends a message to the main window
   * to indicate that the window is open and sets up a listener for incoming
   * broadcast messages from the main window.
   *
   * If the component is not in window mode, it sets up the store subscription,
   * and sets the jumpToLocation coordinates to the division depot coordinates
   * if they're not already set.
   *
   * @returns {void}
   */
  public mounted(): void {
    if (this.windowMode) {
      this.initialiseWindowMode();
    } else {
      this.updateGpsCoordinates();
      Mitt.on('gpsListUpdated', this.updateGpsCoordinates);
      if (
        this.fleetMapStore.jumpToLocation[0] === 0 &&
        this.fleetMapStore.jumpToLocation[1] === 0
      ) {
        this.fleetMapStore.setJumpToLocation(
          this.companyDetailsStore.divisionDepotCoordinates,
        );
      }
    }
  }

  /**
   * Sets the driverGpsCoordinates to the values from the map in the store.
   * Called in non-window mode version of the component, triggered by mitt
   * event.
   */
  public updateGpsCoordinates() {
    this.driverGpsCoordinates = Array.from(
      useGpsStore().allGpsPositions.values(),
    );
  }

  public beforeDestroy() {
    // Clear interval if not null
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }
    Mitt.off('gpsListUpdated', this.updateGpsCoordinates);
  }
  // Tell the main window that the popout map is closed
  public windowWasClosed() {
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.FLEET_TRACKING,
      new BroadcastMessage('windowClosed', false),
    );
    return null;
  }
  /**
   * Adds GPS markers to the gpsCoordinatesByDriverIdMap, such that we can
   * access them in a performant way when we need to update the driver fatigue
   * snapshots.
   *
   * @param {GpsMarkerDetails[]} gpsData - An array of GPS marker details. Each
   * GpsMarkerDetails object should have a `driverId` property.
   *
   * @returns {void}
   */
  private addMarkersToGpsCoordinatesMap(gpsData: GpsMarkerDetails[]): void {
    const gpsCoordinatesByDriverIdMap = new Map();
    gpsData.forEach((gps: GpsMarkerDetails) => {
      gpsCoordinatesByDriverIdMap.set(gps.driverId, gps);
    });
    this.gpsCoordinatesByDriverIdMap = gpsCoordinatesByDriverIdMap;
    this.updateDriverFatigueSnapshotsFromGpsMarker(gpsData);
  }
  /**
   * Updates a driver fatigue snapshot with data from a GPS marker.
   *
   * @param {DriverFatigueSnapshot} snapshot - The driver fatigue snapshot to update.
   * @param {GpsMarkerDetails} marker - The GPS marker to get the data from.
   *
   * @returns {void}
   */
  private updateSnapshotFromMarker(
    snapshot: DriverFatigueSnapshot,
    marker: GpsMarkerDetails,
  ): void {
    snapshot.geoLocation = [marker.longitude, marker.latitude];
    snapshot.driverName = marker.driverName;
    snapshot.csrAssignedId = marker.csrAssignedId;
    snapshot.jobDisplayId = marker.jobDisplayId;
  }
}
