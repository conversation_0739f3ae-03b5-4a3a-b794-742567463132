import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { Validation } from '@/interface-models/Generic/Validation';
import RateDetails from '@/interface-models/Jobs/PUD/RateDetails';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import ZoneRateType from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRateType';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ZonePud extends Vue {
  @Prop() public zones: ZoneRateType[];
  @Prop() public selectedZone: number | null;
  @Prop() public detailedView: boolean;
  @Prop() public soloInput: boolean;
  @Prop({ default: false }) public formDisabled: boolean;
  @Prop({ default: null }) public validationRules: Validation;

  get zone() {
    return this.selectedZone;
  }

  set zone(value) {
    this.$emit('update:selectedZone', value);
  }

  get validate(): Validation {
    return this.validationRules != null
      ? this.validationRules
      : validationRules;
  }
}
