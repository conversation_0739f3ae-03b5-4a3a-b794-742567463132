<v-layout column class="pt-2">
  <v-flex md12 v-if="!isPudItem">
    <v-select
      label="Select Unit Rate Type"
      v-model="unitTypeToApply"
      :items="clientUnitRates"
      :rules="[validate.required]"
      item-text="longName"
      :disabled="disableUnitTypeChange"
      item-value="id"
      color="orange"
    >
    </v-select>
  </v-flex>

  <v-flex md12 v-if="isPudItem">
    <v-layout row :wrap="!horizontalView" :align-start="horizontalView">
      <v-flex :class="horizontalView ? 'md6 px-1' : 'md12'">
        <v-select
          v-if="!hasDynamicRateType"
          label="Associated Zone"
          class="ma-0 pa-0 pb-2 unit-rate-select"
          :class="soloInput ? 'v-solo-custom' : ''"
          :solo="soloInput"
          :flat="soloInput"
          :items="unitRates"
          item-value="zoneId"
          :rules="[validate.required]"
          color="orange"
          v-model="zone"
          :attach="increaseScrollableHeight ? '.unit-rate-select' : false"
          hint="Please add the associated Zone to this stop."
          persistent-hint
        >
          <template slot="selection" slot-scope="data">
            <span
              >{{ data.item.zoneName }} - {{data.item.unitTypeName ?
              data.item.unitTypeName : 'units' }}</span
            >
          </template>
          <template slot="item" slot-scope="data">
            <span
              >{{ data.item.zoneName }} - {{data.item.unitTypeName ?
              data.item.unitTypeName : 'units' }}
            </span>
          </template>
        </v-select>
      </v-flex>
      <v-flex :class="horizontalView ? 'md2 px-1' : 'md12'" v-if="isPickup">
        <v-text-field
          label="Unit Amount"
          :class="soloInput ? 'v-solo-custom' : ''"
          :solo="soloInput"
          :flat="soloInput"
          type="number"
          color="orange"
          hint="The total quantity of units"
          persistent-hint
          :suffix="unitRateSuffix"
          min="0"
          :disabled="!zone && !hasDynamicRateType || formDisabled"
          v-model.number="amountPickUp"
          @focus="$event.target.select()"
        >
        </v-text-field>
      </v-flex>
      <v-flex :class="horizontalView ? 'md2 px-1' : 'md12'" v-if="!isPickup">
        <v-text-field
          label="Unit Amount"
          type="number"
          :class="soloInput ? 'v-solo-custom' : ''"
          :solo="soloInput"
          :flat="soloInput"
          color="orange"
          hint="The total quantity of units"
          persistent-hint
          :suffix="unitRateSuffix"
          min="0"
          v-model.number="amountDropOff"
          :disabled="formDisabled"
          @focus="$event.target.select()"
        >
        </v-text-field>
      </v-flex>
      <v-flex :class="horizontalView ? 'md2 px-1' : 'md12'">
        <v-checkbox
          v-model="isForklift"
          label="Forklift Required"
          hint="Forklift will be required for this load"
          persistent-hint
          color="warning"
          :disabled="formDisabled"
          :class="horizontalView ? 'mt-0' : ''"
        />
      </v-flex>
      <v-flex :class="horizontalView ? 'md2 px-1' : 'md12'" v-if="!isPickup">
        <v-checkbox
          v-model="dangerousGoods"
          label="Dangerous Goods"
          hint="Dangerous goods will be a part of the payload."
          persistent-hint
          color="red"
          :disabled="formDisabled"
          :class="horizontalView ? 'mt-0' : ''"
        />
      </v-flex>
    </v-layout>
  </v-flex>
</v-layout>
