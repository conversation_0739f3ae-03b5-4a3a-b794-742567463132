import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import ShortLongName from '@/interface-models/Generic/ShortLongName/ShortLongName';
import { Validation } from '@/interface-models/Generic/Validation';
import UnitRate from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRate';

import unitRateTypes from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRateTypes';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class UnitRateBooking extends Vue {
  @Prop() public unitRates: UnitRate[];
  @Prop() public selectedZone: number | null;
  @Prop({ default: 1 }) public unitAmountDropOff: number;
  @Prop({ default: 1 }) public unitAmountPickUp: number;
  @Prop() public detailedView: boolean;
  @Prop({ default: false }) public soloInput: boolean;
  @Prop({ default: false }) public isDangerousGoods: boolean;
  @Prop({ default: false }) public forkliftRequired: boolean;
  @Prop() public disableUnitTypeChange: boolean;
  @Prop({ default: false }) public isPudItem: boolean;
  @Prop({ default: () => [] }) public allUnitRates: UnitRate[];
  @Prop({ default: false }) public isPickup: boolean;
  @Prop({ default: -1 }) public selectedUnitTypeId: number;
  @Prop({ default: false }) public hasDynamicRateType: boolean;
  @Prop({ default: false }) public formDisabled: boolean;
  @Prop({ default: false }) public increaseScrollableHeight: boolean;
  @Prop({ default: false }) public horizontalView: boolean;

  get unitRateSuffix(): string | null {
    return this.selectedUnitRate && this.selectedUnitRate.unitTypeName
      ? this.selectedUnitRate.unitTypeName
      : '';
  }

  get validate(): Validation {
    return validationRules;
  }

  get selectedUnitRate() {
    if (!this.unitRates) {
      return;
    }
    return this.unitRates.find((x: UnitRate) => x.zoneId === this.selectedZone);
  }

  get amountPickUp() {
    return this.unitAmountPickUp;
  }
  set amountPickUp(value: number) {
    this.$emit('update:unitAmountPickUp', value);
    if (this.selectedUnitRate && this.selectedUnitRate.unitTypeId === 2) {
      this.$emit('update:pudWeight', value);
    }
  }

  public unitAmountChanged(value: number) {
    this.$emit('update:pudWeight', value);
  }

  get amountDropOff() {
    return this.unitAmountDropOff;
  }
  set amountDropOff(value: number) {
    this.$emit('update:unitAmountDropOff', value);
    if (this.selectedUnitRate && this.selectedUnitRate.unitTypeId === 2) {
      this.$emit('update:pudWeight', value);
    }
  }

  get zone() {
    return this.selectedZone;
  }
  set zone(value) {
    if (!this.unitRates) {
      return;
    }
    const lastSelectedUnit = this.unitRates.find(
      (x: UnitRate) => x.zoneId === this.selectedZone,
    );
    // const preserveUnitCount: boolean =
    //   this.selectedZone === null &&
    //   ((this.isPickup && this.unitAmountPickUp > 0) ||
    //     (!this.isPickup && this.unitAmountDropOff > 0));

    this.$emit('update:selectedZone', value);
    const selectedUnitRate = this.unitRates.find(
      (x: UnitRate) => x.zoneId === value,
    );

    if (!selectedUnitRate) {
      return;
    }

    this.$emit('update:selectedUnitTypeId', selectedUnitRate.unitTypeId);

    // // if the unitType was changed we reset all unit rate information fields. This includes the weight.
    // if (!preserveUnitCount) {
    //   this.$emit('update:unitAmountDropOff', 0);
    //   this.$emit('update:unitAmountPickUp', 0);
    // }

    // if user changes unit rate from weight to dynamic type we clear the weight
    // value in the pudItem.
    if (
      lastSelectedUnit &&
      lastSelectedUnit.unitTypeId === 2 &&
      selectedUnitRate.unitTypeId !== 2
    ) {
      this.$emit('update:pudWeight', 0);
    }
    // If the old selected unit type was no KG, and the new value is KG, then we
    // should set the weight with the value of the pickups/dropoffs
    if (
      lastSelectedUnit &&
      lastSelectedUnit.unitTypeId !== 2 &&
      selectedUnitRate.unitTypeId === 2
    ) {
      const unitCount = this.isPickup
        ? this.unitAmountPickUp
        : this.unitAmountDropOff;
      this.$emit('update:pudWeight', unitCount);
    }
  }

  get dangerousGoods(): boolean {
    return this.isDangerousGoods;
  }
  set dangerousGoods(value: boolean) {
    this.$emit('update:isDangerousGoods', value);
  }

  get isForklift(): boolean {
    return this.forkliftRequired;
  }
  set isForklift(value: boolean) {
    this.$emit('update:forkliftRequired', value);
  }

  get clientUnitRates() {
    const validUnitRateTypes: ShortLongName[] = [];

    for (let i = 0; i < unitRateTypes.length; i++) {
      const element: ShortLongName = unitRateTypes[i];
      if (
        this.allUnitRates.find(
          (ur: UnitRate) => ur.unitTypeId === element.id,
        ) !== undefined
      ) {
        validUnitRateTypes.push(element);
      }
    }
    return validUnitRateTypes;
  }
}
