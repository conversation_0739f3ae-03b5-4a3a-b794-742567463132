import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class TripRateBooking extends Vue {
  @Prop() public title: string;
  @Prop({ default: false }) public enableTripRate: boolean;
  @Prop({ default: false }) public fuelSurchargeRate: boolean;
  @Prop({ default: 0 }) public rateAmount: number;
  @Prop({ default: true }) public showSwitch: boolean;
  @Prop({ default: false }) public isDirectToInvoice: boolean;

  get enableRate() {
    return this.enableTripRate;
  }
  set enableRate(value) {
    this.$emit('update:enableTripRate', value);
  }

  get fuelSurcharge() {
    return this.fuelSurchargeRate;
  }
  set fuelSurcharge(value) {
    this.$emit('update:fuelSurchargeRate', value);
    // Emit to parent letting it know that a value has changed and some logic
    // has to be performed. Used in AdjustServiceDetailsDialog component where
    // the FleetAsset rate is not reactive
    this.$emit('tripRateUpdated');
  }

  get rate() {
    return this.rateAmount;
  }
  set rate(value) {
    this.$emit('update:rateAmount', value);
    // Emit to parent letting it know that a value has changed and some logic
    // has to be performed. Used in AdjustServiceDetailsDialog component where
    // the FleetAsset rate is not reactive
    this.$emit('tripRateUpdated');
  }

  get validate() {
    return validationRules;
  }
}
