<v-layout wrap class="service-rate-selector__container">
  <v-flex md12>
    <v-layout
      justify-space-between
      align-center
      px-2
      pt-1
      style="position: relative"
    >
      <span class="subheader">{{title}}</span>

      <v-switch
        v-if="showSwitch"
        class="task-bar-checkbox ma-0"
        v-model="enableRate"
        color="orange"
        hide-details
        :disabled="title === 'Client' || isDirectToInvoice"
      />
    </v-layout>
  </v-flex>
  <v-flex md12>
    <v-divider></v-divider>
  </v-flex>
  <v-flex md12>
    <v-layout class="px-2 pt-2 pb-2" column>
      <v-flex>
        <v-text-field
          :disabled="!enableRate"
          type="number"
          min="0"
          persistent-hint
          hint="Excluding GST"
          :rules="enableRate ? [validate.required, validate.nonNegative] : []"
          :class="enableRate ? 'form-field-required' : ''"
          v-model.number="rate"
          color="orange"
          :label="title + ' Rate ($)'"
          outline
          hide-details
          class="v-solo-custom"
        />
        <v-checkbox
          :disabled="!enableRate"
          v-model="fuelSurcharge"
          label="Fuel Surcharge"
          hide-details
          style="flex-shrink: 0; flex-grow: 0"
          color="accent"
        />
      </v-flex>
    </v-layout>
  </v-flex>
</v-layout>
