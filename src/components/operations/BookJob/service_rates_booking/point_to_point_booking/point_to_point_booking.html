<v-layout wrap class="service-rate-selector__container mb-2" v-if="rateToApply">
  <v-flex md12>
    <v-select
      label="Select Point to Point Rate"
      v-model="pointToPointRate"
      :items="rateToApply.rateTypeObject"
      return-object
      color="white"
      @input="pointToPointSelected"
      :solo="soloInput"
      flat
      :persistent-hint="soloInput"
      :hint="detailedView ? '' : 'Select point to point rate'"
      hide-details
    >
      <template slot="selection" slot-scope="data">
        <span
          ><strong>
            {{ returnAddressName(data.item.fromAddressReference) }}
          </strong>
          to
          <strong>{{returnAddressName(data.item.toAddressReference)}}</strong>
        </span>
      </template>
      <template slot="item" slot-scope="data">
        <span
          :style="pointToPointRate == data.item ? 'color: orange !important' : ''"
          ><strong>
            {{ returnAddressName(data.item.fromAddressReference) }}
          </strong>
          to
          <strong>{{returnAddressName(data.item.toAddressReference)}}</strong>
        </span>
      </template>
    </v-select>
  </v-flex>
  <v-flex md12 v-if="fromAddress && toAddress">
    <v-expansion-panel
      :class="detailedView ? 'expansion-panel-container mb-2' : ''"
      focusable
    >
      <v-expansion-panel-content class="expansion-item-container">
        <template v-slot:header>
          <v-layout justify-start align-center>
            <span class="section-subheading">Booking Time</span>
            <v-sheet
              :class="detailedView ? 'white--text  amber darken-2 ' : ' blue-grey lighten-4'"
              tile
              class="ml-2"
              v-if="isAsapTimeDefinition"
              style="padding: 2px 6px; border-radius: 2px"
              ><span
                style="
                  font-size: 12px;
                  font-weight: 600;
                  text-transform: uppercase;
                "
                >ASAP</span
              >
            </v-sheet>
          </v-layout>
        </template>
        <v-card
          :color="detailedView ? '#2e2d33' : 'blue-grey lighten-4'"
          class="pa-3"
        >
          <v-layout class="mb-3" align-center>
            <v-switch
              label="ASAP"
              hide-details
              v-model="isAsapTimeDefinition"
              class="v-switch__remove-padding pr"
            ></v-switch>
          </v-layout>

          <v-layout>
            <DatePickerBasic
              @setEpoch="setEpoch"
              :labelName="'Select a Date'"
              :epochTime="pudDate"
              :formDisabled="isAsapTimeDefinition"
              class="px"
            >
            </DatePickerBasic>
            <v-text-field
              class=""
              v-model="pudTime"
              :rules="[validation.twentyFourHourTime]"
              hint="24-hour time"
              mask="##:##"
              persistent-hint
              :disabled="isAsapTimeDefinition"
              label="Arrival Time"
              @focus="$event.target.select()"
            />
          </v-layout>
        </v-card>
      </v-expansion-panel-content>
    </v-expansion-panel>
    <v-slide-y-transition>
      <v-layout wrap class="mt-2">
        <v-flex md2>
          <v-layout align-center fill-height justify-center>
            <v-btn flat large icon color="teal" @click="reorderPointToPoint">
              <v-icon size="28">cached</v-icon>
            </v-btn>
          </v-layout>
        </v-flex>
        <v-flex md10>
          <v-text-field
            hide-details
            class="mb-2"
            :value="fromAddress.address.formattedAddress"
            return-object
            disabled
            label="Pickup from"
            outline
          ></v-text-field>
          <PudRelatedContactSelect
            ref="fromPudRelatedContactRef"
            :client_id="client_id"
            :commonAddress="fromAddress"
            @addRelatedContactToLeg="addFromRelatedContactToLeg"
            :contactName="fromRelatedContact.relatedContactName"
            :contactMobileNumber="fromRelatedContact.relatedContactMobileNumber"
            :contactLandlineNumber="fromRelatedContact.relatedContactLandLineNumber"
            outline
          />
          <v-divider class="ma-2" />
          <v-text-field
            hide-details
            class="mb-2"
            :value="toAddress.address.formattedAddress"
            disabled
            label="Dropoff to"
            outline
          ></v-text-field>
          <PudRelatedContactSelect
            ref="toPudRelatedContactRef"
            :client_id="client_id"
            :commonAddress="toAddress"
            @addRelatedContactToLeg="addToRelatedContactToLeg"
            :contactName="toRelatedContact.relatedContactName"
            :contactMobileNumber="toRelatedContact.relatedContactMobileNumber"
            :contactLandlineNumber="toRelatedContact.relatedContactLandLineNumber"
            outline
          />
        </v-flex>

        <v-flex md12>
          <v-btn
            color="teal accent-3"
            @click="addSelectedPointsToJob"
            depressed
            class="mt-3"
            block
            outline
            ><span v-if="pudItems.length === 0">Add Stops To Job</span>
            <span v-else>Update Stops In Job</span>
            <v-icon size="14" class="pl-3">fas fa-arrow-right</v-icon></v-btn
          >
        </v-flex>
      </v-layout>
    </v-slide-y-transition>
  </v-flex>
</v-layout>
