import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import { PointToPointRateType } from '@/interface-models/ServiceRates/ServiceTypes/PointToPointServiceRate/PointToPointRateType';
import ServiceTypeObject from '@/interface-models/Generic/ServiceTypes/ServiceTypeObject';
import RateDetails from '@/interface-models/Jobs/PUD/RateDetails';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import moment from 'moment-timezone';
import JobReferenceDetails from '@/interface-models/Jobs/JobReferenceDetails';
import ClientReferenceDetails from '@/interface-models/Client/ClientDetails/References/ClientReferenceDetails';
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import JobPrimaryRate from '@/interface-models/Generic/Accounting/JobPrimaryRate';
import { returnStartOfDayFromEpoch } from '@/helpers/DateTimeHelpers/DateTimeHelpers';

import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { Validation } from '@/interface-models/Generic/Validation';
import { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import { ClientPerson } from '@/interface-models/User/ClientPerson';
import PudRelatedContactSelect from '@/components/operations/BookJob/pud_related_contact_select/pud_related_contact_select.vue';
import { ClientRelatedContact } from '@/interface-models/Client/ClientRelatedContact';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';

interface JobSiteContact {
  relatedContactName: string;
  relatedContactMobileNumber: string;
  relatedContactLandLineNumber: string;
}

@Component({
  components: { DatePickerBasic, PudRelatedContactSelect },
})
export default class PointToPointBooking extends Vue {
  @Prop() public client_id: string;
  @Prop() public clientId: string;
  @Prop() public rateInformation: ServiceTypeObject;
  @Prop() public rateDetails: RateDetails;
  @Prop() public formDisabled: boolean;
  @Prop({ default: () => [] }) public validate: Validation;
  @Prop() public rateToApply: RateTableItems;
  @Prop({ default: true }) public detailedView: boolean;
  @Prop({ default: false }) public soloInput: boolean;
  @Prop() public pudItems: PUDItem[];
  @Prop() public clientReferences: ClientReferenceDetails[];
  @Prop() public isNewJob: boolean;
  @Prop() public assignedPointToPointRate: JobPrimaryRate[];

  public companyDetailsStore = useCompanyDetailsStore();

  public pointToPointRate: PointToPointRateType = new PointToPointRateType();
  public showSelectedPoints: boolean = false;
  public selectedToRelatedContact: ClientPerson = new ClientPerson();
  public isAsapTimeDefinition: boolean = true;
  public pudDate: number | null = null;
  public pudTime: string = '';
  public firstPudEpochTime = 0;

  public fromRelatedContact: JobSiteContact = {
    relatedContactName: '',
    relatedContactMobileNumber: '',
    relatedContactLandLineNumber: '',
  };
  public toRelatedContact: JobSiteContact = {
    relatedContactName: '',
    relatedContactMobileNumber: '',
    relatedContactLandLineNumber: '',
  };

  public $refs!: {
    fromPudRelatedContactRef: any;
    toPudRelatedContactRef: any;
  };

  /**
   * Returns a list of the selected clients common address list
   *  @returns {ClientCommonAddress[]} List of ClientCommonAddress
   */
  get clientCommonAddressList(): ClientCommonAddress[] {
    const clientCommonAddressList: ClientCommonAddress[] =
      useClientDetailsStore().clientCommonAddresses;
    if (
      !clientCommonAddressList ||
      clientCommonAddressList.length === 0 ||
      clientCommonAddressList[0].clientId !== this.clientId
    ) {
      return [];
    }
    return clientCommonAddressList;
  }

  public addFromRelatedContactToLeg(
    clientRelatedContact: ClientRelatedContact,
  ) {
    this.fromRelatedContact.relatedContactName = (
      clientRelatedContact.firstName +
      ' ' +
      clientRelatedContact.lastName
    ).trim();
    this.fromRelatedContact.relatedContactMobileNumber =
      clientRelatedContact.contactMobileNumber.trim();
    this.fromRelatedContact.relatedContactLandLineNumber =
      clientRelatedContact.contactLandlineNumber;
  }

  public addToRelatedContactToLeg(clientRelatedContact: ClientRelatedContact) {
    this.toRelatedContact.relatedContactName = (
      clientRelatedContact.firstName +
      ' ' +
      clientRelatedContact.lastName
    ).trim();
    this.toRelatedContact.relatedContactMobileNumber =
      clientRelatedContact.contactMobileNumber.trim();
    this.toRelatedContact.relatedContactLandLineNumber =
      clientRelatedContact.contactLandlineNumber;
  }

  public returnAddressName(addressReferenceId: string) {
    const nicknamedAddress = this.clientCommonAddressList.find(
      (address: ClientCommonAddress) => address._id === addressReferenceId,
    );
    if (nicknamedAddress !== undefined) {
      return nicknamedAddress.addressNickname;
    }
  }

  get fromAddress(): ClientCommonAddress | undefined {
    return this.clientCommonAddressList.find(
      (x: ClientCommonAddress) =>
        x._id === this.pointToPointRate.fromAddressReference,
    );
  }

  get validation(): Validation {
    return validationRules;
  }

  get toAddress(): ClientCommonAddress | undefined {
    return this.clientCommonAddressList.find(
      (x: ClientCommonAddress) =>
        x._id === this.pointToPointRate.toAddressReference,
    );
  }

  public reorderPointToPoint(): void {
    const fromAddress = JSON.parse(
      JSON.stringify(this.pointToPointRate.fromAddressReference),
    );
    const toAddress = JSON.parse(
      JSON.stringify(this.pointToPointRate.toAddressReference),
    );

    // Swap from and to locations
    this.pointToPointRate.fromAddressReference = toAddress;
    this.pointToPointRate.toAddressReference = fromAddress;
    const fromContactPerson = JSON.parse(
      JSON.stringify(this.fromRelatedContact),
    );
    const toContactPerson = JSON.parse(JSON.stringify(this.toRelatedContact));

    this.fromRelatedContact = toContactPerson;
    this.toRelatedContact = fromContactPerson;
  }

  public pointToPointSelected(): void {
    this.showSelectedPoints = true;
    setTimeout(() => {
      if (this.fromAddress && this.fromAddress.defaultContact) {
        this.$refs.fromPudRelatedContactRef.commonAddressSelected(
          this.fromAddress.defaultContact.contactId,
        );
      }
      if (this.toAddress && this.toAddress.defaultContact) {
        this.$refs.toPudRelatedContactRef.commonAddressSelected(
          this.toAddress.defaultContact.contactId,
        );
      }
    }, 200);
  }

  public addSelectedPointsToJob(): void {
    this.showSelectedPoints = false;
    for (let i = this.pudItems.length - 1; i >= 0; i--) {
      this.pudItems.splice(i, 1);
    }
    this.addPudItem(true);
    this.addPudItem(false);
    this.$emit('pudPointsAddedToJob');
  }

  @Watch('isAsapTimeDefinition')
  public timeDefinitionChanged(isAsap: boolean) {
    if (isAsap) {
      this.setDateAndTimeStringValues(this.pudItems[0] ? true : false, true);
    }
  }

  public addPudItem(isFromPoint: boolean) {
    if (!this.fromAddress || !this.toAddress) {
      showNotification('Something went wrong.', {
        title: 'Point To Point Rate',
        type: HealthLevel.ERROR,
      });
      return;
    }
    const pudItem = new PUDItem();
    pudItem.legTypeFlag = isFromPoint ? 'P' : 'D';

    pudItem.pickupDate = moment(this.pudDate)
      .tz(this.companyDetailsStore.userLocale)
      .startOf('day')
      .valueOf();
    pudItem.epochTime = isFromPoint ? this.firstPudEpochTime : 0;
    pudItem.timeDefinition = isFromPoint && this.isAsapTimeDefinition ? 9 : 0;

    const locationContact = isFromPoint
      ? this.fromRelatedContact
      : this.toRelatedContact;
    pudItem.siteContactName = locationContact.relatedContactName;
    pudItem.siteContactLandLineNumber =
      locationContact.relatedContactLandLineNumber;
    pudItem.siteContactMobileNumber =
      locationContact.relatedContactMobileNumber;

    for (const reference of this.clientReferences) {
      const jobReference = new JobReferenceDetails();
      jobReference.referenceTypeId = reference.referenceTypeId as number;
      if (isFromPoint) {
        pudItem.pickupReference.push(jobReference);
      } else {
        pudItem.dropoffReference.push(jobReference);
      }
    }
    pudItem.address = isFromPoint
      ? this.fromAddress.address
      : this.toAddress.address;

    this.pudItems.push(pudItem);
  }
  public setEpoch(epoch: number | null): void {
    this.pudDate = epoch;
  }

  public setDateAndTimeStringValues(readFromPud: boolean, forceAsap: boolean) {
    // set job date
    const currentDateStartOfDay = returnStartOfDayFromEpoch();
    this.pudDate =
      forceAsap || (!readFromPud && this.isAsapTimeDefinition)
        ? currentDateStartOfDay
        : this.pudItems[0]
          ? returnStartOfDayFromEpoch(this.pudItems[0].epochTime)
          : currentDateStartOfDay;
    if (!this.pudDate) {
      return;
    }
    // find the difference between the start of the day and the hour of day the job is booked for.
    const startTime: any =
      forceAsap || (!readFromPud && this.isAsapTimeDefinition)
        ? moment().tz(this.companyDetailsStore.userLocale).valueOf()
        : this.pudItems[0]
          ? this.pudItems[0].epochTime
          : moment().tz(this.companyDetailsStore.userLocale);
    const difference = startTime - this.pudDate;
    // convert the difference into 24 hour clock time. eg "13:30"
    const hours = ('0' + moment.duration(difference).hours()).slice(-2);
    const minutes = ('0' + moment.duration(difference).minutes()).slice(-2);
    this.pudTime = hours + minutes;
  }

  get hour() {
    const a = this.pudTime[0];
    const b = this.pudTime[1];
    const hourString = a + b;

    const hour = parseInt(hourString, 10);
    return hour;
  }

  get minutes() {
    const c = this.pudTime[2];
    const d = this.pudTime[3];
    const hourString = c + d;
    const hour = parseInt(hourString, 10);
    return hour;
  }

  get correctEpochTime() {
    if (this.pudDate !== null) {
      const dateStr = moment(this.pudDate);

      return dateStr
        .add(this.hour, 'hours')
        .add(this.minutes, 'minutes')
        .valueOf();
    }
  }

  @Watch('correctEpochTime')
  public epochChange(val: number) {
    this.firstPudEpochTime = val;
  }

  public mounted() {
    this.setDateAndTimeStringValues(this.pudItems[0] ? true : false, false);
    if (!this.isNewJob) {
      const pointToPointRateInJob: PointToPointRateType[] = this
        .assignedPointToPointRate[0].rate
        .rateTypeObject as PointToPointRateType[];

      if (this.assignedPointToPointRate[0] && pointToPointRateInJob[0]) {
        this.pointToPointRate = JSON.parse(
          JSON.stringify(pointToPointRateInJob[0]),
        );
      }
      this.isAsapTimeDefinition =
        this.pudItems[0] && this.pudItems[0].timeDefinition === 9
          ? true
          : false;
    }
  }
}
