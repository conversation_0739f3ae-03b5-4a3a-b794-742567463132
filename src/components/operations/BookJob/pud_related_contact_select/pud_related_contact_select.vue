<template>
  <div class="flex-row content-center">
    <v-autocomplete
      v-model="syncedModelValue"
      :items="siteContactSelectList"
      :label="legType === 'P' ? 'Pickup Site Contact' : 'Delivery Site Contact'"
      :rules="[]"
      item-value="value"
      item-text="key"
      browser-autocomplete="off"
      :solo="sessionManager.isClientPortal()"
      auto-select-first
      clearable
      :hint="
        sessionManager.isClientPortal() ? 'Delivery Site Contact' : props.hint
      "
      :persistent-hint="sessionManager.isClientPortal() || !!props.hint"
      :disabled="formDisabled"
      outline
      class="v-solo-custom"
      color="Orange"
      flat
    >
      <template v-slot:no-data>
        <v-list-tile
          class="add-siteContact"
          @click="newSiteContactDialog"
          style="cursor: pointer"
        >
          <strong> Create New Site Contact</strong>
          <v-spacer></v-spacer>
          <v-icon size="14">fa-light fa-user-plus</v-icon>
        </v-list-tile>
      </template>
    </v-autocomplete>
    <CreateSiteContactDialog
      :client_id="client_id"
      :clientId="clientId"
      :clientName="clientName"
      :clientPersonIds="clientPersonIds"
      :defaultDispatcherId="defaultDispatcherId"
      :dialogIsActive="dialogIsActive"
      :contactName="contactName"
      @dialogIsActive="dialogIsActive = $event"
      @siteContact="handleNewSiteContact"
      :isAdhocOnly="isAdhocOnly"
    />
    <div class="flex-row align-center pl-2 pb-2">
      <v-tooltip bottom v-if="syncedModelValue">
        <template #activator="{ on: tooltip }">
          <v-btn
            flat
            icon
            v-on="{ ...tooltip }"
            @click="viewSiteContactDetails = true"
            :disabled="!syncedModelValue"
            class="pl-1"
          >
            <v-icon size="24">far fa-info-circle</v-icon>
          </v-btn>
        </template>
        <span>View Site Contact</span>
      </v-tooltip>
      <v-tooltip bottom v-else>
        <template #activator="{ on: tooltip }">
          <v-btn
            icon
            flat
            v-on="{ ...tooltip }"
            @click="(dialogIsActive = true), (isAdhocOnly = true)"
            class="ma-0"
          >
            <v-icon size="26">add_circle</v-icon>
          </v-btn>
        </template>
        <span>Add New Adhoc Site Contact</span>
      </v-tooltip>
    </div>
    <GDialog
      v-if="viewSiteContactDetails && selectedNonAdhocSiteContact"
      :title="'Site Contact Details'"
      :confirmBtnText="'Okay'"
      :confirmDisabled="false"
      :width="'600px'"
      :cancelBtnText="'Cancel'"
      :isActionable="false"
      @closeDialog="viewSiteContactDetails = false"
      @confirm="viewSiteContactDetails = false"
      :isLoading="false"
    >
      <div class="pa-3">
        <GTitle
          class="mb-2"
          title="Site Contact"
          subtitle="Information about the site contact on this leg."
          :divider="false"
        />
        <GTextField
          :value="selectedNonAdhocSiteContact.firstName"
          :placeholder="'First Name'"
          :disabled="true"
        ></GTextField>

        <GTextField
          :value="selectedNonAdhocSiteContact.lastName"
          :placeholder="'Last Name'"
          :disabled="true"
        ></GTextField>

        <GTextField
          :value="selectedNonAdhocSiteContact.contactMobileNumber"
          :placeholder="'Mobile Number'"
          :disabled="true"
          v-mask="'0### ### ###'"
        ></GTextField>

        <GTextField
          :value="selectedNonAdhocSiteContact.contactLandlineNumber"
          :placeholder="'Landline Number'"
          :disabled="true"
          :rules="[rules.numbers]"
          v-mask="'## #### ####'"
        ></GTextField>

        <GTextField
          :value="selectedNonAdhocSiteContact.emailAddress[0]"
          :placeholder="'Email Address'"
          :disabled="true"
        ></GTextField>
      </div>
      <div class="pa-3">
        <v-btn
          block
          solo
          :color="'primary'"
          @click="viewSiteContactDetails = false"
          class="btn mb-2"
          >Okay</v-btn
        >
      </div>
    </GDialog>
  </div>
</template>

<script setup lang="ts">
import CreateSiteContactDialog from '@/components/operations/BookJob/pud_related_contact_select/create_site_contact_dialog.vue';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { ClientRelatedContact } from '@/interface-models/Client/ClientRelatedContact';
import { KeyValue } from '@/interface-models/Generic/KeyValue/KeyValue';
import { Validation } from '@/interface-models/Generic/Validation';
import {
  ClientIdAndClientPersonWithAuthDetailsList,
  ClientPersonWithAuthDetails,
} from '@/interface-models/User/ClientPerson';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useUserManagementStore } from '@/store/modules/UserManagementStore';
import { sessionManager } from '@/store/session/SessionState';
import { v4 as uuidv4 } from 'uuid';
import { ComputedRef, Ref, WritableComputedRef, computed, ref } from 'vue';

const userManagementStore = useUserManagementStore();
const clientDetailsStore = useClientDetailsStore();

const emit = defineEmits<{
  (event: 'addRelatedContactToLeg', payload: ClientRelatedContact): void;
}>();

interface IProps {
  clientName: string;
  clientId: string;
  client_id: string;
  clientPersonIds: string[];
  defaultDispatcherId?: string | null;
  // commonAddress: ClientCommonAddress | null;
  contactName?: string;
  contactMobileNumber?: string;
  contactLandlineNumber?: string;
  outline?: boolean;
  hint?: string;
  formDisabled: boolean;
  legType: string;
}
const props = withDefaults(defineProps<IProps>(), {
  defaultDispatcherId: '',
  client_id: '',
  clientId: '',
  contactName: '',
  contactMobileNumber: '',
  contactLandlineNumber: '',
  outline: false,
  hint: '',
  legType: 'P',
});

const adhocSiteContact: Ref<ClientRelatedContact | null> = ref(null);
const adhocSiteContactId: string = uuidv4().replace(/-/g, '');
const viewSiteContactDetails: Ref<boolean> = ref(false);
const dialogIsActive: Ref<boolean> = ref(false);
const isAdhocOnly: Ref<boolean> = ref(false);

function newSiteContactDialog(): void {
  isAdhocOnly.value = false;
  dialogIsActive.value = true;
}
/**
 * A writable computed reference that provides a getter and setter to manage the site contact's ID.
 * The getter returns the ad-hoc site contact ID or the site contact ID on the PUD based on certain conditions.
 * The setter updates the site contact by emitting an event with the new site contact information.
 * @returns {WritableComputedRef<string>} The computed reference for the site contact's ID.
 */
const syncedModelValue: WritableComputedRef<string> = computed({
  get(): string {
    if (isAdhocSiteContact.value) {
      return adhocSiteContactId;
    } else {
      return siteContactOrClientPersonIdOnPud.value;
    }
  },
  set(siteContact_id: string): void {
    if (!siteContact_id) {
      emit('addRelatedContactToLeg', new ClientRelatedContact());
    }
    const foundSiteContact: ClientRelatedContact | undefined =
      clientDetailsStore.clientRelatedContacts.find(
        (x: ClientRelatedContact) => x._id === siteContact_id,
      );
    if (foundSiteContact) {
      emit('addRelatedContactToLeg', foundSiteContact);
      return;
    }
    const clientPersonList: ClientIdAndClientPersonWithAuthDetailsList | null =
      userManagementStore.clientIdAndClientPersonWithAuthDetailsList;
    if (clientPersonList) {
      const foundClientPerson =
        clientPersonList.clientPersonWithAuthDetailsList.find(
          (x: ClientPersonWithAuthDetails) =>
            x.clientPerson._id === siteContact_id,
        );
      if (foundClientPerson) {
        const siteContact: ClientRelatedContact = new ClientRelatedContact();
        siteContact.firstName = foundClientPerson.clientPerson.firstName;
        siteContact.lastName = foundClientPerson.clientPerson.lastName;
        siteContact.contactMobileNumber =
          foundClientPerson.clientPerson.contactMobileNumber;
        siteContact.contactLandlineNumber =
          foundClientPerson.clientPerson.contactLandlineNumber;
        emit('addRelatedContactToLeg', siteContact);
        return;
      }
    }
  },
});

/**
 * Retrieves the MongoDB ID of the site contact associated with the current PUD item.
 * This method is used to initialize the site contact input when a PUD item is loaded.
 * It matches the site contact name saved in the PUD item with the corresponding MongoDB ID
 * from the client's site contacts. This is necessary because only the name, not the MongoDB ID,
 * is stored in the job information.
 *
 * @returns {string} The MongoDB ID of the matched client person or site contact. Returns empty string
 *                          if no matching contact is found.
 */
const siteContactOrClientPersonIdOnPud: ComputedRef<string> = computed(() => {
  try {
    const clientPersonList: ClientIdAndClientPersonWithAuthDetailsList | null =
      userManagementStore.clientIdAndClientPersonWithAuthDetailsList;

    if (clientPersonList) {
      const foundClientPerson =
        clientPersonList.clientPersonWithAuthDetailsList.find(
          (x: ClientPersonWithAuthDetails) =>
            x.clientPerson.firstName &&
            x.clientPerson.lastName &&
            x.clientPerson.firstName + x.clientPerson.lastName ===
              props.contactName.trim().replace(/\s/g, ''),
        );
      if (foundClientPerson && foundClientPerson.clientPerson._id) {
        return foundClientPerson.clientPerson._id;
      }
    }

    const selectedSiteContact: ClientRelatedContact | undefined =
      clientDetailsStore.clientRelatedContacts.find(
        (x: ClientRelatedContact) =>
          x.firstName &&
          x.lastName &&
          x.firstName + x.lastName ===
            props.contactName.trim().replace(/\s/g, ''),
      );
    if (selectedSiteContact && selectedSiteContact._id) {
      return selectedSiteContact._id;
    }
    return '';
  } catch (e) {
    console.error(e);
    return '';
  }
});

/**
 * Computes and returns a non-adhoc site contact based on the site contact or client person ID
 * associated with the PUD. It first attempts to find a matching site contact from the client
 * details module. If not found, it looks for a matching client person within the user management
 * store and converts it to a ClientRelatedContact object. If no matches are found in either case,
 * it returns null.
 *
 * @returns {ComputedRef<ClientRelatedContact | null>} A computed reference to the selected
 *          non-adhoc site contact if found, or null otherwise.
 */
const selectedNonAdhocSiteContact: ComputedRef<ClientRelatedContact | null> =
  computed(() => {
    const foundSiteContact = clientDetailsStore.clientRelatedContacts.find(
      (x: ClientRelatedContact) =>
        x._id === siteContactOrClientPersonIdOnPud.value,
    );
    if (foundSiteContact) {
      return foundSiteContact;
    }

    if (
      userManagementStore.clientIdAndClientPersonWithAuthDetailsList &&
      userManagementStore.clientIdAndClientPersonWithAuthDetailsList
        .client_id === props.client_id
    ) {
      const clientPerson =
        userManagementStore.clientIdAndClientPersonWithAuthDetailsList.clientPersonWithAuthDetailsList.find(
          (x: ClientPersonWithAuthDetails) =>
            x.clientPerson._id === siteContactOrClientPersonIdOnPud.value,
        );

      if (clientPerson) {
        const siteContact: ClientRelatedContact = new ClientRelatedContact();
        siteContact.firstName = clientPerson.clientPerson.firstName;
        siteContact.lastName = clientPerson.clientPerson.lastName;
        siteContact.contactMobileNumber =
          clientPerson.clientPerson.contactMobileNumber;
        siteContact.contactLandlineNumber =
          clientPerson.clientPerson.contactLandlineNumber;
        siteContact.emailAddress[0] = clientPerson.clientPerson.emailAddress[0];

        return siteContact;
      }
    }
    if (adhocSiteContact.value) {
      const adhoc: ClientRelatedContact = new ClientRelatedContact();
      adhoc.firstName = adhocSiteContact.value.firstName;
      adhoc.lastName = adhocSiteContact.value.lastName;
      adhoc.contactMobileNumber = adhocSiteContact.value.contactMobileNumber;
      adhoc.contactLandlineNumber =
        adhocSiteContact.value.contactLandlineNumber;
      adhoc.emailAddress[0] = adhocSiteContact.value.emailAddress[0];
      return adhoc;
    }
    return null;
  });

/**
 * Computes a list of key-value pairs for site contact selection.
 * @returns {ComputedRef<KeyValue[]>} A list of site contacts and client persons for selection.
 */
const siteContactSelectList = computed((): KeyValue[] => {
  try {
    if (!props.client_id) {
      return [];
    }
    // const associatedSiteContacts: string[] = props.commonAddress
    //   ? props.commonAddress.addressContacts.map(
    //       (x: ClientCommonAddressContact) => x.contactId,
    //     )
    //   : [];
    const siteContactsSelectList: KeyValue[] = [];
    // If the pud has an adhoc site contact we will add this site contact to the selectable list.
    if (isAdhocSiteContact.value) {
      siteContactsSelectList.push({
        key: props.contactName,
        value: adhocSiteContactId,
      });
    }

    const siteContacts: KeyValue[] =
      clientDetailsStore.clientRelatedContacts.map(
        (x: ClientRelatedContact) => {
          return {
            key: x.firstName + ' ' + x.lastName,
            value: x._id!,
          };
        },
      );
    let clientPersons: KeyValue[] = [];
    if (
      userManagementStore.clientIdAndClientPersonWithAuthDetailsList &&
      userManagementStore.clientIdAndClientPersonWithAuthDetailsList
        .client_id === props.client_id
    ) {
      clientPersons =
        userManagementStore.clientIdAndClientPersonWithAuthDetailsList.clientPersonWithAuthDetailsList.map(
          (x: ClientPersonWithAuthDetails) => {
            return {
              key: x.clientPerson.firstName + ' ' + x.clientPerson.lastName,
              value: x.clientPerson._id!,
            };
          },
        );
    }
    const allSiteContactsList = siteContacts.concat(clientPersons);
    // filter clients site contacts and client persons by associated site contacts at this address.
    // const filtered = allSiteContactsList.filter((x: KeyValue) =>
    //   associatedSiteContacts.includes(x.value as string),
    // );
    return siteContactsSelectList.concat(allSiteContactsList);
  } catch (e: unknown) {
    console.error(e);
    return [];
  }
});

/**
 * Computes the validation rules from the store.
 * @returns {ComputedRef<Validation>} The computed validation rules.
 */
const rules: ComputedRef<Validation> = computed(() => {
  return validationRules;
});

/**
 * Determines if the site contact is an ad-hoc site contact.
 * @returns {ComputedRef<boolean>} True if the site contact is ad-hoc, false otherwise.
 */
const isAdhocSiteContact: ComputedRef<boolean> = computed(() => {
  return !siteContactOrClientPersonIdOnPud.value && props.contactName !== '';
});

/**
 * Adds the ad-hoc sitecontact to the job. Validates the ad-hoc sitecontact data before emitting
 * an 'addSiteContactToLeg' event with the ad-hoc sitecontact's value. Resets the ad-hoc sitecontact to null afterwards.
 */
function handleNewSiteContact(newSiteContact: ClientRelatedContact) {
  const newItem = {
    value: newSiteContact._id ?? adhocSiteContactId,
    key: `${newSiteContact.firstName} ${newSiteContact.lastName}`,
  };

  if (
    !siteContactSelectList.value.find((item) => item.value === newItem.value)
  ) {
    siteContactSelectList.value.push(newItem);
  }

  adhocSiteContact.value = newSiteContact;
  syncedModelValue.value = newItem.value;

  // Update the prop value for v-autocomplete
  emit('addRelatedContactToLeg', newSiteContact);
  // close Dialog
  dialogIsActive.value = false;
  isAdhocOnly.value = false;
}

/**
 * Actioned from parent component via ref tag on common address selection.
 * @param {string} contactId - The ID of the selected contact.
 */
function commonAddressSelected(contactId: string) {
  syncedModelValue.value = contactId;
}

/**
 * Exposes specific methods to the parent component.
 */
defineExpose({
  commonAddressSelected,
});
</script>

<style scoped lang="scss">
@media (min-width: $user-portal-desktop-breakpoint) {
}

.add-siteContact {
  cursor: pointer;
  &:hover {
    color: var(--primary) !important;
    v-icon {
      color: var(--primary) !important;
      fill: var(--primary) !important;
    }
  }
}

.btn {
  margin: 4px;
}
</style>
