<template>
  <GDialog
    v-if="isActive"
    :width="'720px'"
    ref="dialogRef"
    :title="'Create New Site Contact'"
    :confirmBtnText="`Create ${computedTitle}`"
    :confirmDisabled="false"
    :isDelete="true"
    deleteBtnText="Back"
    @closeDialog="cancelMaintenance"
    :isActionable="isContactTypeSelected"
    @confirm="saveNewSiteContact"
    @deleteItem="
      () => {
        isAdhocOnly ? closeDialog() : (isContactTypeSelected = false);
      }
    "
    :isLoading="false"
  >
    <div
      v-if="!isContactTypeSelected"
      class="grid grid-cols-2 gap-4 px-3 py-3"
      md8
    >
      <GTitle
        title="Select Site Contact Type"
        subtitle="Select Site Contact type to create"
        :divider="true"
      />
      <v-flex
        v-for="item in selectItems"
        :key="item.value"
        @click="selectOption(item.value)"
        :class="[
          'select-contact-type-items',
          {
            disabled:
              item.key === 'Login Site Contact' &&
              sessionManager.isClientPortal() &&
              !isAccount,
          },
        ]"
      >
        <h3 class="item-value">{{ item.key }}</h3>
        <p class="item-des">{{ item.description }}</p>
        <div
          v-if="
            item.key === 'Login Site Contact' &&
            sessionManager.isClientPortal() &&
            !isAccount
          "
          class="tooltiptxt"
        >
          <v-icon color="white">priority_high</v-icon>
          ACCOUNT MANAGEMENT ROLE REQUIRED
        </div>
      </v-flex>

      <v-divider></v-divider>
      <div class="dialog-actions-container">
        <GButton
          class="action-btn"
          :color="'error'"
          outlined
          @click="cancelMaintenance"
          >Cancel</GButton
        >
      </div>
    </div>

    <div v-else>
      <div class="px-3 py-3" md8>
        <h1 class="header--light">{{ computedSubtitle }}</h1>
        <v-divider class="mt-2"></v-divider>
      </div>
      <div
        class="pa-3"
        v-if="selectedContactType === DispatcherType.Adhoc && adhocSiteContact"
      >
        <GTitle
          class="mb-2"
          title="Site Contact"
          subtitle="Manually add a site contact to the leg. If a mobile number is provided, they will be notified when the driver is on their way to their location."
          :divider="false"
        />
        <GTextField
          v-model="adhocSiteContact.firstName"
          :placeholder="'Site Contact Name'"
          :autofocus="true"
          :rules="[rules.required]"
        ></GTextField>

        <GTextField
          v-model="adhocSiteContact.contactMobileNumber"
          :placeholder="'Contact Mobile Number'"
          v-mask="'0### ### ###'"
          :rules="[rules.numbers]"
        ></GTextField>

        <GTextField
          v-model="adhocSiteContact.contactLandlineNumber"
          :placeholder="'Contact Landline Number'"
          v-mask="'## #### ####'"
          :rules="[rules.numbers]"
        ></GTextField>
      </div>
      <div v-if="selectedContactType === DispatcherType.ClientRelatedPersons">
        <ClientDetailsRelatedContactMaintenance
          :clientRelatedContact="clientRelatedContact"
          :relatedContactsAssociatedCommonAddressIds.sync="
            relatedContactsAssociatedCommonAddressIds
          "
          :relatedContactsDefaultCommonAddressIds.sync="
            relatedContactsDefaultCommonAddressIds
          "
          @cancelMaintenance="cancelMaintenance"
          :clientsCommonAddresses="clientsCommonAddresses"
        />
      </div>
      <div v-if="selectedContactType === DispatcherType.CompanyContacts">
        <ClientPersonMaintenance
          ref="clientPersonMaintenanceDialogRef"
          :client_id="client_id"
          :clientId="clientId"
          :clientName="clientName"
          :clientPersonWithAuthDetails="clientPersonWithAuthDetails"
          @cancelMaintenance="cancelMaintenance"
          @setIsEmailVerificationLoading="setIsEmailVerificationLoading"
          @setIsMobileVerificationLoading="setIsMobileVerificationLoading"
          @setDefaultDispatcher="setDefaultDispatcher"
          :defaultDispatcherId="defaultDispatcherId"
          :clientPersonIds="clientPersonIds"
          :dispatcherOnly="true"
          @addClientPersonDispatcher="addClientPersonDispatcher"
        >
        </ClientPersonMaintenance>
      </div>
    </div>
  </GDialog>
</template>

<script setup lang="ts">
import ClientPersonMaintenance from '@/components/admin/ClientDetails/components/client_details_contacts/client_details_client_person_maintenance.vue';
import ClientDetailsRelatedContactMaintenance from '@/components/admin/ClientDetails/components/client_details_contacts/client_details_related_contact_maintenance.vue';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAccountManagementRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import {
  validate,
  validationRules,
} from '@/helpers/ValidationHelpers/ValidationHelpers';
import { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import {
  ClientCommonAddressRelatedContactSave,
  saveCommonAddressAndRelatedContact,
} from '@/interface-models/Client/ClientCommonAddressRelatedContactSave';
import { ClientRelatedContact } from '@/interface-models/Client/ClientRelatedContact';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import {
  ClientPerson,
  ClientPersonWithAuthDetails,
  ClientRoleStatus,
  SaveNewClientPersonRequest,
} from '@/interface-models/User/ClientPerson';
import { UserRoleStatus } from '@/interface-models/User/UserRoleStatus';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useWebsocketStore } from '@/store/modules/WebsocketStore';
import { sessionManager } from '@/store/session/SessionState';
import { computed, ComputedRef, onMounted, Ref, ref, watch } from 'vue';

const rules = validationRules;

enum DispatcherType {
  Adhoc = 'Adhoc',
  ClientRelatedPersons = 'ClientRelatedPersons',
  CompanyContacts = 'CompanyContacts',
}
// Define props
const props = withDefaults(
  defineProps<{
    clientName: string;
    clientId: string;
    client_id: string;
    clientPersonIds: string[];
    defaultDispatcherId?: string | null;
    contactName?: string;
    dialogIsActive: boolean;
    isAdhocOnly?: boolean;
  }>(),
  {
    defaultDispatcherId: '',
    client_id: '',
    clientId: '',
    contactName: '',
    job_id: undefined,
    required: false,
    isAdhocOnly: false,
  },
);

const isActive = computed(() => props.dialogIsActive);
const dialogRef = ref(null);
const adhocDispatcher: Ref<ClientPerson | null> = ref(null);
const clientRelatedContact: Ref<ClientRelatedContact | null> = ref(null);
const clientPersonMaintenanceDialogRef = ref(null);
const clientsCommonAddresses: Ref<ClientCommonAddress[] | null> = ref([]);
const relatedContactsAssociatedCommonAddressIds: Ref<string[]> = ref([]);
const relatedContactsDefaultCommonAddressIds: Ref<string[]> = ref([]);
const adhocSiteContact: Ref<ClientRelatedContact | null> = ref(null);
const isContactTypeSelected = ref(false);
const selectedContactType: Ref<DispatcherType> = ref(DispatcherType.Adhoc);

const isDefaultDispatcher: Ref<boolean> = ref(false);
const isEmailVerificationLoading: Ref<boolean> = ref(false);
const isMobileVerificationLoading: Ref<boolean> = ref(false);
const clientPersonWithAuthDetails: Ref<ClientPersonWithAuthDetails | null> =
  ref(null);
const isSaveNewClientPersonRequest: Ref<boolean> = ref(false);

const isAccount = computed(() => hasAccountManagementRole());

const emit = defineEmits([
  'siteContact',
  'addRelatedContactToLeg',
  'dialogIsActive',
]);

// selection Create ContactType options
const selectItems = computed(() => [
  {
    key: 'Adhoc Site Contact',
    value: DispatcherType.Adhoc,
    description: `An ad-hoc Site Contact should be used if this person does not regularly book jobs on behalf of the customer. Ad-hoc Site Contact will not be saved into the platform for re-use. `,
  },
  {
    key: 'Non-Login Site Contact',
    value: DispatcherType.ClientRelatedPersons,
    description: `A non-login Site Contact should be used if this person does not require login access to the client portal. These users will have their details saved into the platform in order to be able to easily add their details to future deliveries. `,
  },
  {
    key: 'Login Site Contact',
    value: DispatcherType.CompanyContacts,
    description: `Do not create a login user for a site contact unless they are part of the client's organisation. A login user should be created if this user requires login access to the client portal. These users will be able to book new jobs and view jobs in progress in real-time. These users will have their details saved into the platform in order to be able to easily add their details to future deliveries.
`,
  },
]);

// select Contact type and load selected view
const selectOption = (value: DispatcherType) => {
  selectedContactType.value = value;
  loadSelectedContent();
};

const loadSelectedContent = () => {
  if (selectedContactType.value) {
    isContactTypeSelected.value = true;
  }
};

// Computed property for the Dialog buttons
const computedTitle = computed(() => {
  switch (selectedContactType.value as DispatcherType) {
    case DispatcherType.Adhoc:
      return 'Adhoc Site Contact';
    case DispatcherType.ClientRelatedPersons:
      return 'Client Related Site Contact';
    case DispatcherType.CompanyContacts:
      return 'Company Site Contact';
    default:
      return '';
  }
});

// Computed property for the Dialog Title
const computedSubtitle = computed(() => {
  switch (selectedContactType.value as DispatcherType) {
    case DispatcherType.Adhoc:
      return 'Creates One-off Adhoc Site Contact';
    case DispatcherType.ClientRelatedPersons:
      return 'Creates New Client Related Contact As Site Contact';
    case DispatcherType.CompanyContacts:
      return 'Creates New Client Contact As Site Contact';
    default:
      return '';
  }
});
// selected Dispatcher type from selector
function selectedDispatcherType(type: DispatcherType) {
  switch (type) {
    case DispatcherType.Adhoc:
      newAdhocDispatcher();
      break;
    case DispatcherType.ClientRelatedPersons:
      addNewClientRelatedContact();
      break;
    case DispatcherType.CompanyContacts:
      addNewClientPerson();
      break;
  }
}

// create new sitecontact based on selection
function saveNewSiteContact() {
  switch (selectedContactType.value as DispatcherType) {
    case DispatcherType.Adhoc:
      addAdhocDispatcherToJob();
      break;
    case DispatcherType.ClientRelatedPersons:
      saveClientRelatedContact();
      break;
    case DispatcherType.CompanyContacts:
      saveClientPerson();
      break;
    default:
      addAdhocDispatcherToJob();
      break;
  }
}

// Close the dialog emit for parent component
function closeDialog() {
  emit('dialogIsActive', false); // Emit `false` to close the dialog
}

/**
 * Initializes a new ad-hoc sitecontact by creating a new instance of ClientPerson.
 */
function newAdhocDispatcher(): void {
  // Initialize a new ClientPerson instance
  adhocSiteContact.value = new ClientRelatedContact();
}
/**
 * Adds a new client related contact.
 * Initializes a new client related contact and sets the client ID and role as Dispatcher.
 */
function addNewClientRelatedContact() {
  // Call getClientsCommonAddresses before creating a new ClientRelatedContact
  getClientsCommonAddresses();
  clientRelatedContact.value = new ClientRelatedContact();
  clientRelatedContact.value.clientId = props.clientId;
  clientRelatedContact.value.roleIds = [11];
}

/**
 * Computed ref that determines if the current client person is new.
 *
 * @returns {ComputedRef<boolean>} True if the client person is new (without an ID), false otherwise.
 */
const isNewClientPerson: ComputedRef<boolean> = computed(() => {
  if (!clientPersonWithAuthDetails.value) {
    return false;
  }
  return !clientPersonWithAuthDetails.value.clientPerson._id;
});

/**
 * Saves the client person details.
 * Validates the input and sends a WebSocket request to save the new client person.
 * Handles the response by adding the new client person to the user management store.
 */
function saveClientPerson() {
  if (
    !clientPersonWithAuthDetails.value ||
    !validate(clientPersonMaintenanceDialogRef.value)
  ) {
    return;
  }

  isSaveNewClientPersonRequest.value = true;

  const rolesToAdd: number[] = isNewClientPerson.value
    ? [
        ...new Set(
          clientPersonWithAuthDetails.value.clientRoles.map(
            (x: ClientRoleStatus) => x.roleId,
          ),
        ),
      ]
    : [];

  const saveNewClientPersonRequest: SaveNewClientPersonRequest = {
    clientPerson: clientPersonWithAuthDetails.value.clientPerson,
    clientId: props.client_id,
    rolesToAdd,
    defaultDispatcher: isDefaultDispatcher.value,
  };

  useWebsocketStore().sendWebsocketRequest(
    new WebSocketRequest(
      sessionManager.isClientPortal()
        ? `/user/${sessionManager.getUserName()}/clientDetails/clientPersonDispatchers/saveNewClientPerson`
        : '/clientDetails/clientPersonDispatchers/saveNewClientPerson',
      saveNewClientPersonRequest,
      true,
    ),
  );
  emit('siteContact', clientPersonWithAuthDetails.value.clientPerson);
  cancelMaintenance();
}

/**
 * Adds the ad-hoc sitecontact to the job. Validates the ad-hoc sitecontact data before emitting
 * an 'input' (v-model) event with the ad-hoc sitecontact's information. Resets the local
 * ad-hoc sitecontact state to null afterwards.
 */
function addAdhocDispatcherToJob() {
  if (!validate(dialogRef.value)) {
    return;
  }
  emit('siteContact', adhocSiteContact.value);
  cancelMaintenance();
}

/**
 * Cancels and closes the ad-hoc sitecontact dialog by setting it to null.
 */
function cancelMaintenance(): void {
  switch (selectedContactType.value as DispatcherType) {
    case DispatcherType.Adhoc:
      adhocSiteContact.value = null;
      break;
    /**
     * Cancels the related contact maintenance operation.
     * Clears the current related contact information and associated default common address IDs.
     */
    case DispatcherType.ClientRelatedPersons:
      clientRelatedContact.value = null;
      relatedContactsAssociatedCommonAddressIds.value = [];
      relatedContactsDefaultCommonAddressIds.value = [];
      break;
    default:
      adhocDispatcher.value = null;
      break;
  }
  isContactTypeSelected.value = false;
  closeDialog();
}

/**
 * Saves the client related contact details. Validates the form data before saving.
 * Constructs the save request with the related contact and associated address information.
 * If validation fails or the related contact does not match the client ID, it aborts the save operation.
 */
function saveClientRelatedContact() {
  if (
    !validate(dialogRef.value) ||
    !clientRelatedContact.value ||
    !props.clientId ||
    clientRelatedContact.value.clientId !== props.clientId
  ) {
    return;
  }
  const request: ClientCommonAddressRelatedContactSave = {
    clientId: props.clientId,
    clientCommonAddress: null,
    clientRelatedContact: clientRelatedContact.value,
    relatedContactsAssociatedCommonAddressIds:
      relatedContactsAssociatedCommonAddressIds.value,
    relatedContactsDefaultCommonAddressIds:
      relatedContactsDefaultCommonAddressIds.value,
    updatedClientCommonAddresses: [],
  };

  const savedSiteContact = saveCommonAddressAndRelatedContact(request);

  if (!savedSiteContact) {
    showNotification('Something went wrong.', {
      title: 'Error',
      type: HealthLevel.ERROR,
    });
  }
  // emit('clientPerson', clientRelatedContact.value);
  emit('siteContact', clientRelatedContact.value);
  cancelMaintenance();
}

/**
 * Fetches common addresses associated with the client.
 * Retrieves and sets the client's common addresses.
 * Logs an error if the fetch operation fails.
 */
async function getClientsCommonAddresses(): Promise<void> {
  // fetch client related contacts
  const commonAddresses: ClientCommonAddress[] | null =
    await useClientDetailsStore().getClientCommonAddressesByClientId(
      props.clientId,
    );
  if (!commonAddresses) {
    console.error(
      "Something went wrong when fetching client's common addresses.",
    );
    return;
  }
  clientsCommonAddresses.value = commonAddresses;
}

/**
 * Sets the loading state for email verification.
 *
 * @param {boolean} isLoading - True if email verification is in progress, false otherwise.
 */
function setIsEmailVerificationLoading(isLoading: boolean): void {
  isEmailVerificationLoading.value = isLoading;
}

/**
 * Sets the loading state for mobile verification.
 *
 * @param {boolean} isLoading - True if mobile verification is in progress, false otherwise.
 */
function setIsMobileVerificationLoading(isLoading: boolean): void {
  isMobileVerificationLoading.value = isLoading;
}

/**
 * Set the default dispatcher boolean. Emitted from child.
 */
function setDefaultDispatcher(value: boolean): void {
  isDefaultDispatcher.value = value;
}

// Function to be called when isActive changes
const onIsActiveChange = () => {
  selectedDispatcherType(selectedContactType.value as DispatcherType);
};

/**
 * Event emitted back from maintenance component when adding an existing client person to this client.
 * @param {string} clientPerson_id - The client person id that will be added to this clients dispatchers
 * array.
 * @returns {void}
 */
function addClientPersonDispatcher(clientPerson): void {
  clientPersonWithAuthDetails.value = new ClientPersonWithAuthDetails();
  clientPersonWithAuthDetails.value.clientPerson = clientPerson;
}

/**
 * Initializes a new client person with authorization details.
 * Resets the `clientPersonWithAuthDetails` ref to a new instance of `ClientPersonWithAuthDetails` and role as Dispatcher.
 */
function addNewClientPerson(): void {
  clientPersonWithAuthDetails.value = new ClientPersonWithAuthDetails();
  // Ensure clientRoles is initialized
  if (!clientPersonWithAuthDetails.value.clientRoles) {
    clientPersonWithAuthDetails.value.clientRoles = [];
  }
  // Add a new ClientRoleStatus with roleId 1
  clientPersonWithAuthDetails.value.clientRoles.push({
    roleId: 1,
    roleName: 'Dispatcher',
    status: UserRoleStatus.PENDING,
  } as ClientRoleStatus);
}

// Watcher for selected sitecontact type
watch(selectedContactType, (newValue: string) => {
  if (newValue) {
    if (newValue === DispatcherType.Adhoc) {
      newAdhocDispatcher();
    }
    selectedDispatcherType(newValue as DispatcherType);
  } else {
    selectedDispatcherType(selectedContactType.value as DispatcherType);
  }
});

// Watch the isActive computed property for dialog
watch(isActive, onIsActiveChange);

watch(
  () => props.isAdhocOnly,
  (newVal) => {
    if (newVal) {
      selectOption(DispatcherType.Adhoc);
    } else {
      isContactTypeSelected.value = false;
    }
  },
);

//  default section when dialog is active
onMounted(() => {
  newAdhocDispatcher();
});
</script>
