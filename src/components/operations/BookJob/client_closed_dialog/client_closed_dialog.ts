import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
@Component({
  components: {},
})
export default class ClientClosedDialog extends Vue {
  @Prop({ default: false }) public clientClosedDialogIsActive: boolean;

  public confirmAndClose() {
    this.$emit('newJob');
    this.dialogIsActive = false;
  }

  get dialogIsActive() {
    return this.clientClosedDialogIsActive;
  }
  set dialogIsActive(value: boolean) {
    this.$emit('update:clientClosedDialogIsActive', value);
  }
}
