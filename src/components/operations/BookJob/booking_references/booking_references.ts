import {
  mapJobReferencesToReferenceSummary,
  ReferenceDetails,
} from '@/helpers/JobBooking/JobReferenceHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import ClientReferenceDetails from '@/interface-models/Client/ClientDetails/References/ClientReferenceDetails';
import {
  referenceTypes,
  ReferenceTypes,
} from '@/interface-models/Generic/ReferenceTypes/ReferenceTypes';
import { Validation } from '@/interface-models/Generic/Validation';
import JobReferenceDetails from '@/interface-models/Jobs/JobReferenceDetails';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import { mask } from 'vue-the-mask';

@Component({
  components: {},
  directives: {
    mask: (el, binding) => {
      if (!binding.value) {
        return;
      }
      mask(el, binding);
    },
  },
})
export default class BookingReferences extends Vue {
  @Prop({ default: () => [] }) public jobReferences: JobReferenceDetails[];
  @Prop({ default: () => [] })
  public clientReferences: ClientReferenceDetails[];
  @Prop({ default: false }) public isPud: boolean;
  @Prop({ default: false }) public isDropoff: boolean;
  @Prop({ default: false }) public isBeforePricing: boolean;
  @Prop({ default: false }) public soloInput: boolean;
  @Prop({ default: false }) public readOnlyView: boolean;

  get referenceTypes(): ReferenceTypes[] {
    return referenceTypes.map((r) => {
      const alreadyHasInvoiceReference =
        r.id === 7 ? this.hasInvoiceReference : false;
      return {
        ...r,
        disabled: !r.isAvailableAdhoc || alreadyHasInvoiceReference,
      };
    });
  }

  get hasInvoiceReference(): boolean {
    return this.jobReferences.some((x) => x.referenceTypeId === 7);
  }

  // Return a list of reference details. This returns ReferenceDetails containing required status and validation status for each of our references.
  get referenceDetails(): ReferenceDetails[] {
    if (!this.jobReferences?.length) {
      return [];
    }
    const referenceInformation = mapJobReferencesToReferenceSummary(
      this.jobReferences,
      this.clientReferences,
      {
        isBeforePricing: this.isBeforePricing,
        isPud: this.isPud,
        isDropoff: this.isDropoff,
      },
    );
    return referenceInformation;
  }

  // returns true of false depending on if all requirements are met.
  get requirementsMet(): boolean[] {
    return this.referenceDetails.map((x: any) => x.requirementsMet);
  }

  // when requirementsMet has changed we emit back to the parent the value;
  @Watch('requirementsMet')
  public requirementsCheck(value: boolean[]) {
    this.$emit(
      'update:requirementsMet',
      value.every((v) => v === true),
    );
  }

  public addReference(): void {
    this.jobReferences.push(new JobReferenceDetails(4, ''));
  }

  public removeReference(index: number): void {
    if (index === 0) {
      // Clear the first reference instead of removing it
      this.jobReferences[0].reference = '';
    } else {
      // Remove the reference for other indices
      this.jobReferences.splice(index, 1);
    }
  }

  get validationRules(): Validation {
    return validationRules;
  }

  // public setIsMinimised(isMinimised: boolean) {
  //   this.isMinimised = isMinimised;
  // }
}
