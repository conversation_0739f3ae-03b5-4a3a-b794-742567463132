<div>
  <div class="custom-scrollbar booking-references-container mt-2">
    <v-layout
      align-center
      v-for="(reference, refIndex) of jobReferences"
      :key="refIndex"
    >
      <v-flex md4>
        <v-select
          :class="referenceDetails[refIndex].required ? 'form-field-required' : ''"
          v-model="reference.referenceTypeId"
          :items="referenceTypes"
          :disabled="readOnlyView || referenceDetails[refIndex].required || reference.referenceTypeId === 8"
          item-text="longName"
          item-value="id"
          label="Reference Type"
          outline
          class="v-solo-custom"
          flat
          solo
          color="orange"
          hide-details
        >
        </v-select>
      </v-flex>
      <v-flex md8 pl-1>
        <v-text-field
          :class="referenceDetails[refIndex].required ? 'form-field-required' : ''"
          :label="referenceDetails[refIndex].labelName"
          v-model="reference.reference"
          :disabled="readOnlyView || reference.referenceTypeId === 8"
          :rules="!referenceDetails[refIndex].requirementsMet ? ['Required'] : [true]"
          v-mask="referenceDetails[refIndex].mask"
          outline
          solo
          flat
          class="v-solo-custom"
          color="Orange"
          hide-details
        >
          <template v-slot:append-outer>
            <v-tooltip bottom>
              <template v-slot:activator="{ on }">
                <v-btn
                  flat
                  v-on="on"
                  icon
                  @click="removeReference(refIndex)"
                  :disabled="readOnlyView || referenceDetails[refIndex].required || reference.referenceTypeId === 8"
                  class="ma-0"
                >
                  <v-icon size="18"> delete</v-icon>
                </v-btn>
              </template>
              Remove Reference
            </v-tooltip>
            <v-tooltip
              bottom
              v-if="isBeforePricing && !referenceDetails[refIndex].canRemove"
            >
              <template v-slot:activator="{ on }">
                <v-icon
                  size="18"
                  class="check-active"
                  :disabled="readOnlyView"
                  v-if="referenceDetails[refIndex].requirementsMet"
                  >far fa-check
                </v-icon>
                <v-icon
                  v-else
                  size="15"
                  :disabled="readOnlyView"
                  color="rgba(255, 255, 255, 0.561)"
                >
                  fas fa-exclamation-triangle
                </v-icon>
              </template>
              <span>Add Reference</span>
            </v-tooltip>
          </template>
        </v-text-field>
      </v-flex>
    </v-layout>
  </div>
  <v-btn
    class="add-ref-btn"
    block
    depressed
    plain
    large
    @click="addReference"
    :disabled="readOnlyView"
  >
    <v-icon size="16" class="pl-1 pr-2">add_circle</v-icon>
    Add reference
  </v-btn>
</div>
