<!-- 
TODO: This doesn't work the same as the multi-vue file component. We will need to rework 
<template>
  <div>
    <div class="custom-scrollbar booking-references-container mt-2">
      <template v-if="!isBeforePricing">
        <v-layout
          align-center
          v-for="(reference, refIndex) of jobReferences"
          :key="refIndex"
        >
          <v-flex md4>
            <v-select
              :class="
                referenceDetails[refIndex].required ? 'form-field-required' : ''
              "
              v-model="reference.referenceTypeId"
              :items="validReferenceTypes"
              :disabled="readOnlyView || referenceDetails[refIndex].required"
              item-text="longName"
              item-value="id"
              label="Reference Type"
              outline
              class="v-solo-custom"
              color="orange"
              hide-details
            >
            </v-select>
          </v-flex>
          <v-flex md8 pl-1>
            <v-text-field
              :class="
                referenceDetails[refIndex].required ? 'form-field-required' : ''
              "
              :label="referenceDetails[refIndex].labelName"
              v-model="reference.reference"
              :solo="soloInput"
              :flat="soloInput"
              :disabled="readOnlyView"
              :rules="
                !referenceDetails[refIndex].requirementsMet
                  ? ['Required']
                  : [true]
              "
              v-mask="referenceDetails[refIndex].mask"
              outline
              class="v-solo-custom"
              color="Orange"
              hide-details
            >
              <template v-slot:append-outer>
                <v-tooltip bottom>
                  <template v-slot:activator="{ on }">
                    <v-btn
                      flat
                      v-on="on"
                      icon
                      @click="removeReference(refIndex)"
                      :disabled="
                        readOnlyView || referenceDetails[refIndex].required
                      "
                      class="ma-0"
                    >
                      <v-icon size="18"> delete</v-icon>
                    </v-btn>
                  </template>
                  Remove Reference
                </v-tooltip>
                <v-tooltip
                  bottom
                  v-if="
                    isBeforePricing && !referenceDetails[refIndex].canRemove
                  "
                >
                  <template v-slot:activator="{ on }">
                    <v-icon
                      size="18"
                      class="check-active"
                      :disabled="readOnlyView"
                      v-if="referenceDetails[refIndex].requirementsMet"
                      v-on="on"
                      >far fa-check
                    </v-icon>
                    <v-icon
                      v-else
                      size="15"
                      :disabled="readOnlyView"
                      color="rgba(255, 255, 255, 0.561)"
                      v-on="on"
                    >
                      fas fa-exclamation-triangle
                    </v-icon>
                  </template>
                  <span>Add Reference</span>
                </v-tooltip>
              </template>
            </v-text-field>
          </v-flex>
        </v-layout>
      </template>
    </div>
    <v-btn
      class="add-ref-btn"
      block
      depressed
      plain
      large
      @click="addReference"
      :disabled="readOnlyView"
    >
      <v-icon>add</v-icon>
      Add reference
    </v-btn>
  </div>
</template>

<script setup lang="ts">
import {
  mapJobReferencesToReferenceSummary,
  ReferenceDetails,
} from '@/helpers/JobBooking/JobReferenceHelpers';
import ClientReferenceDetails from '@/interface-models/Client/ClientDetails/References/ClientReferenceDetails';
import {
  referenceTypes,
  ReferenceTypes,
} from '@/interface-models/Generic/ReferenceTypes/ReferenceTypes';
import JobReferenceDetails from '@/interface-models/Jobs/JobReferenceDetails';
import { computed, ComputedRef, Ref, ref, toRef, watch } from 'vue';

const emit = defineEmits<{
  (event: 'update:isDialogOpen', payload: boolean): void;
  (event: 'update:requirementsMet', payload: boolean): void;
}>();

const props = withDefaults(
  defineProps<{
    jobReferences: JobReferenceDetails[];
    clientReferences: ClientReferenceDetails[];
    isPud?: boolean;
    isDropoff?: boolean;
    isBeforePricing?: boolean;
    soloInput?: boolean;
    readOnlyView?: boolean;
  }>(),
  {
    isPud: false,
    isDropoff: false,
    isBeforePricing: false,
    soloInput: false,
    readOnlyView: false,
  },
);

const readOnlyView: Ref<boolean> = toRef(props, 'readOnlyView');
const jobReferences: Ref<JobReferenceDetails[]> = toRef(props, 'jobReferences');

const validReferenceTypes: ComputedRef<ReferenceTypes[]> = computed(() => {
  return referenceTypes.map((r) => ({
    ...r,
    disabled: r.id !== 7 ? false : hasInvoiceReference.value,
  }));
});

const hasInvoiceReference: ComputedRef<boolean> = computed(() => {
  return props.jobReferences.some((x) => x.referenceTypeId === 7);
});

// Return a list of reference details. This returns ReferenceDetails containing required status and validation status for each of our references.
const referenceDetails: ComputedRef<ReferenceDetails[]> = computed(() => {
  if (!props.jobReferences.length) {
    return [];
  }
  const referenceInformation = mapJobReferencesToReferenceSummary(
    props.jobReferences,
    props.clientReferences,
    {
      isBeforePricing: props.isBeforePricing,
      isPud: props.isPud,
      isDropoff: props.isDropoff,
    },
  );
  return referenceInformation;
});

// returns true of false depending on if all requirements are met.
const requirementsMet: ComputedRef<boolean[]> = computed(() => {
return referenceDetails.value.map((x: any) => x.requirementsMet);
});

// when requirementsMet has changed we emit back to the parent the value;
watch(requirementsMet, (value: boolean[]) => {
  emit(
    'update:requirementsMet',
    value.every((v) => v === true),
  );
});

function addReference(): void {
  props.jobReferences.push(new JobReferenceDetails(4, ''));
}

function removeReference(index: number): void {
  if (index === 0) {
    // Clear the first reference instead of removing it
    props.jobReferences[0].reference = '';
  } else {
    // Remove the reference for other indices
    props.jobReferences.splice(index, 1);
  }
}
</script>
<style scoped lang="scss">
.check-active {
  color: rgb(125, 236, 208) !important;
}

.booking-references-container {
  height: 300px;
  max-height: 100%;
  .v-select {
    text-overflow: hidden;
    overflow: hidden;
    text-wrap: wrap;
  }
  .v-solo-custom {
    margin-bottom: 6px;
  }
}

.add-ref-btn {
  border-radius: 14px;
  color: $accent !important;
  padding: 0px;
  margin: 0px;
  height: 34px;
  background-color: transparent !important;
  border: 1px solid $accent;
  &:disabled {
    border: none;
  }
}
</style> -->
