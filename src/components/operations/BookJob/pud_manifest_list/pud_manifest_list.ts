import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import { Portal } from '@/interface-models/Generic/Portal';
import { Manifest } from '@/interface-models/Jobs/Manifest';
import { sessionManager } from '@/store/session/SessionState';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  components: { ConfirmationDialog, InformationTooltip },
})
export default class PudManifestList extends Vue {
  @Prop({ default: () => [] }) public manifestList: Manifest[];

  public manifestPayload(dimension: number, units: string = 'm') {
    if (dimension && dimension !== 0) {
      return dimension + units;
    } else {
      return ' - ';
    }
  }

  get isClientPortal(): boolean {
    return sessionManager.getPortalType() === Portal.CLIENT;
  }
  // Checks whether the list contains non-empty values
  get hasValidManifestInformation() {
    if (!this.manifestList || !this.manifestList.length) {
      return false;
    }
    // If the list is not empty, but all elements in list have no dimensions,
    // description or quantity, then do not display anything
    const allFieldsEmpty = this.manifestList.every(
      (m) =>
        !m.description &&
        !m.height &&
        !m.weight &&
        !m.length &&
        !m.width &&
        !m.quantity,
    );
    return !allFieldsEmpty;
  }
}
