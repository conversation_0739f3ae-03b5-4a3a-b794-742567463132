<v-layout class="pud-manifest-list" wrap>
  <v-flex md12 v-if="!hasValidManifestInformation" pb-1>
    <v-layout justify-center>
      <span class="add-italics faded-text"
        >No Manifest Payload information</span
      >
    </v-layout>
  </v-flex>
  <v-flex
    v-else
    md12
    v-for="(manifest, index) in manifestList"
    :key="manifest.id"
    pb-1
  >
    <v-layout>
      <strong class="pr-1" :class="{'client-portal': isClientPortal}"
        >{{index + 1}}.</strong
      >
      <span
        v-if="manifest.description"
        :class="{'client-portal': isClientPortal}"
        >{{manifest.description ? manifest.description : '-'}}</span
      >
      <span v-else class="add-italics">No description provided</span>
      <span class="faded-text pl-1" v-if="manifest.quantity">
        [x{{manifest.quantity}}]</span
      >
    </v-layout>
    <v-layout v-if="manifest.instructions">
      <span class="faded-text add-italics"> {{manifest.instructions}}</span>
    </v-layout>
    <v-layout
      v-if="manifest.height || manifest.length || manifest.width"
      class="manifest-dimension-txt"
    >
      <span class="faded-text pr-1">
        {{manifestPayload(manifest.length)}}(L) x</span
      >
      <span class="faded-text pr-1">
        {{manifestPayload(manifest.width)}}(W) x</span
      >
      <span class="faded-text pr-1">
        {{manifestPayload(manifest.height)}}(H) x</span
      >
      <v-spacer></v-spacer>
      <span v-if="manifest.weight" class="faded-text pr-1">
        {{manifest.weight}}(KG)</span
      >
    </v-layout>
    <v-layout v-else>
      <span class="faded-text pr-1"> No manifest dimensions provided</span>
    </v-layout>
    <v-divider></v-divider>
  </v-flex>
</v-layout>
