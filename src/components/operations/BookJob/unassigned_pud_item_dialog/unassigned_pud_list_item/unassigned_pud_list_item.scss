.unassigned-pud-list-item {
  padding: auto;
  font-size: $font-size-11;
  $font-size-med: 12px;
  $font-size-small: 11px;
  $font-light-text: rgb(164, 167, 182);
  .upi-date {
    font-size: $font-size-med;
    color: white;
    font-weight: 700;
  }
  .upi-description__row {
    // padding-bottom: 1px;

    .upi-description__key {
      font-size: $font-size-small;
      color: $font-light-text;
      font-weight: 600;
    }
    .upi-description__value {
      font-size: $font-size-small;
      color: #ffffff;
      font-weight: 500;
    }
  }

  .bottom-divider {
    border-top: 1px solid $app-dark-primary-600;
  }
}

.title-text {
  color: #d6d5db;
  text-transform: uppercase;
  font-size: $font-size-12;
  font-weight: 600;
}

.pud-linking-header-container {
  background-color: #29282e !important;
  border: 1px solid $app-dark-primary-600;
}
