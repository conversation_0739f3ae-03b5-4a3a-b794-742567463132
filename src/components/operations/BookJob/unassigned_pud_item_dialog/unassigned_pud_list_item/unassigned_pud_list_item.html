<section class="unassigned-pud-list-item">
  <v-layout row wrap px-1 :class="noMarginTop ? '' : 'mt-1'">
    <!-- <v-flex md12 px-1 py-1 v-if="enableCheckbox">
      <v-layout align-center>
        <v-checkbox class="ma-0" v-model="isSelectedGroup" hide-details />

        <div class="amber--text" v-if="jobContainsGroupMembers">
          <span class="pr-2"
            >This job has at least one point that is part of this group</span
          >
          <v-icon size="14" color="amber">fas fa-exclamation-triangle</v-icon>
        </div>
      </v-layout>
    </v-flex> -->
    <v-flex
      md12
      v-for="(upiSelection, index) in upiSelectionList"
      :key="upiSelection.unassignedPudItem.id"
    >
      <v-layout row wrap>
        <v-flex
          md12
          class="mt-2"
          style="background-color:#1c1c1f"
          v-if="!isMatching && index === 0 || !isMatching && upiSelectionList[index - 1].unassignedPudItem.groupReferenceId !== upiSelectionList[index].unassignedPudItem.groupReferenceId"
        >
          <v-layout
            class="job-details-pud-matching-info align-center pa-2"
            wrap
          >
            <v-flex md12>
              <v-layout justify-space-between align-center>
                <div>
                  <span class="title-text">
                    Group Reference:
                    <span style="color: #ffb917"
                      >{{upiSelection.unassignedPudItem.groupReferenceId}} -
                      {{upiSelection.unassignedPudItem.driverName}}</span
                    >
                  </span>
                </div>
                <div>
                  <v-btn
                    small
                    depressed
                    color="info"
                    class="ma-0 pt-0"
                    v-if="!selectedGroupIds.includes(upiSelection.unassignedPudItem.groupReferenceId)"
                    @click="selectAllInGroup(upiSelection.unassignedPudItem.groupReferenceId, true)"
                    hide-details
                    >Select Group</v-btn
                  >
                  <v-btn
                    small
                    depressed
                    color="info"
                    class="ma-0 pt-0"
                    v-if="selectedGroupIds.includes(upiSelection.unassignedPudItem.groupReferenceId)"
                    @click="selectAllInGroup(upiSelection.unassignedPudItem.groupReferenceId, false)"
                    hide-details
                    >Remove group</v-btn
                  >
                </div>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>

        <v-flex md12>
          <v-layout
            class="bottom-divider  app-bgcolor--250 app-borderside--t app-bordercolor--600"
          >
            <v-flex md4 px-2 pt-2>
              <v-layout justify-end align-center pb-1>
                <v-checkbox
                  class="ma-0"
                  v-model="upiSelection.isSelected"
                  hide-details
                ></v-checkbox>

                <span class="upi-date"
                  >{{upiSelection.unassignedPudItem.pudDetails.epochTime ?
                  returnFormattedDate(upiSelection.unassignedPudItem.pudDetails.epochTime,
                  `DD/MM/YY HH:mm`) : '-'}}</span
                >
              </v-layout>

              <v-layout
                class="upi-description__row"
                justify-space-between
                align-start
                v-for="item in returnSummaryInformationForUpi(upiSelection.unassignedPudItem)"
                :key="item.id"
              >
                <span class="upi-description__key">{{item.title}} </span>
                <span class="upi-description__value">{{item.value}}</span>
              </v-layout>
            </v-flex>
            <v-flex md8>
              <PudCard
                :index="0"
                :pudItem="upiSelection.unassignedPudItem.pudDetails"
                :editingPudIndex="-1"
                :rateTypeId="1"
                :unassignedPudType="true"
                :unassignedPudItem="upiSelection.unassignedPudItem"
              ></PudCard>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>
  </v-layout>
</section>
