import { Component, Prop, Vue } from 'vue-property-decorator';
import PudCard from '@/components/operations/BookJob/pud_item_container/pud_card/index.vue';
import UnassignedPudItem from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudItem';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  UpiGroupSelection,
  UpiSelection,
} from '@/interface-models/Jobs/PUD/UnassignedPudItem/UpiSelection';

interface KeyValuePair {
  id: string;
  title: string;
  value: string;
}
@Component({
  components: { PudCard },
})
export default class UnassignedPudListItem extends Vue {
  @Prop() public upiSelectionList: UpiSelection[];
  @Prop({ default: true }) public enableCheckbox: boolean;
  @Prop({ default: '' }) public selectedSingleId: string;
  @Prop({ default: false }) public jobContainsGroupMembers: boolean;
  @Prop({ default: false }) public noMarginTop: boolean;
  @Prop({ default: false }) public isMatching: boolean;
  @Prop({ default: () => [] }) public selectedGroupIds: string[];
  @Prop({ default: null }) public preferredDate: number | null;

  public returnFormattedDate = returnFormattedDate;

  public groupId: string = '';

  public summaryInformation: KeyValuePair[] = [];

  public selectAllInGroup(groupId: string, addGroup: boolean) {
    const groupedUpis = this.upiSelectionList.filter(
      (x: UpiSelection) => x.unassignedPudItem.groupReferenceId === groupId,
    );
    for (const upi of groupedUpis) {
      upi.isSelected = addGroup;
    }
    const upiGroupSelection: UpiGroupSelection = {
      groupId,
      addGroup,
    };

    this.$emit('selectAllByGroupId', upiGroupSelection);
  }

  public returnSummaryInformationForUpi(
    unassignedPudItem: UnassignedPudItem,
  ): KeyValuePair[] {
    const summaryInformation: KeyValuePair[] = [];

    summaryInformation.push({
      id: 'clientSuppliedId',
      title: 'Reference',
      value: unassignedPudItem.clientSuppliedId
        ? unassignedPudItem.clientSuppliedId
        : '-',
    });
    if (unassignedPudItem.additionalJobData) {
      summaryInformation.push({
        id: 'driverName',
        title: 'Sugg. Driver',
        value: unassignedPudItem.additionalJobData.driverName
          ? unassignedPudItem.additionalJobData.driverName
          : '-',
      });
      summaryInformation.push({
        id: 'csrAssignedId',
        title: 'Sugg. Fleet',
        value: unassignedPudItem.additionalJobData.csrAssignedId
          ? unassignedPudItem.additionalJobData.csrAssignedId
          : '-',
      });
    }
    return summaryInformation;
  }
}
