.unassigned-pud-item-dialog {
  padding: 0;
  position: relative;
}
.unassigned-pud-dialog-content {
  &__column {
    $body-height: 75vh;
    max-height: $body-height;
    overflow-y: scroll;
  }
}

.link-icon-text {
  font-size: $font-size-12;
  font-weight: 600;
  text-transform: uppercase;
}

.pud-row__content {
  width: 652px;

  transition: 200ms cubic-bezier(0.59, 0.12, 0.34, 0.95);
  transition-property: opacity, transform;
}

.unassigned-enter-active {
  transition-delay: 100ms !important;
  transition: 200ms cubic-bezier(0.59, 0.12, 0.34, 0.95);
  position: absolute;
  width: 661px;
}

.unassigned-move {
  transition: 200ms cubic-bezier(0.59, 0.12, 0.34, 0.95);
  transition-property: opacity, transform;
}

.unassigned-enter {
  opacity: 0;
  height: 0px;
}

.unassigned-enter-to {
  opacity: 1;
  height: 0px;
}

.unassigned-leave-to {
  opacity: 0;
}

.job-pud-move {
  transition: 250ms cubic-bezier(0.59, 0.12, 0.34, 0.95);
  transition-property: opacity, transform;
}

.job-pud-enter {
  opacity: 0;
  transition-delay: 1000ms !important;
}

.job-pud-enter-to {
  opacity: 1;
}

.job-pud-leave-active {
  position: absolute;
}

.job-pud-leave-to {
  opacity: 0;
  height: 0;
}

.extend-dialog-height {
  height: 80vh;
  width: 100%;
}

.unassigned-pud-list-matching {
  max-height: calc(80vh - 274px);
  overflow-y: scroll;
}
.unassigned-pud-list-add {
  max-height: calc(80vh - 28px);
  overflow-y: scroll;
}

.job-pud-list {
  max-height: calc(80vh - 86px);
  overflow-y: scroll;
}
.job-details-pud-matching-info {
  background-color: #29282e !important;
  border: 1px solid $app-dark-primary-600;
}

.job-id-text {
  color: #ffb917;
}

.title-text {
  color: #d6d5db;
  text-transform: uppercase;
  font-size: $font-size-12;
  font-weight: 600;
}

.pud-linking-header-container {
  background-color: #29282e !important;
  border: 1px solid $app-dark-primary-600;
}
