<div class="unassigned-pud-item-dialog">
  <v-dialog
    v-if="showDialog"
    v-model="showDialog"
    :width="'679px'"
    persistent
    content-class="v-dialog-custom"
    key="unassigned-pud-item-dialog"
  >
    <v-layout
      justify-space-between
      align-center
      class="task-bar app-theme__center-content--header no-highlight"
    >
      <div>
        <span class="pr-2"
          >Point Manager - <span v-if="!isMatching">Add Leg to job</span
          ><span v-if="isMatching">Leg Matching</span></span
        >
        <InformationTooltip :bottom="true">
          <v-layout slot="content" v-if="!isMatching"
            >To add a leg to the job, please select your legs you wish to add
            and then proceed with selecting the "Add Selected" button
            below.</v-layout
          >
          <v-layout slot="content" v-if="isMatching"
            >Leg Matching allows you to link a single imported leg with an
            already existing leg on the job, to do this please select a leg on
            the job and then select one or more legs that you wish to
            link.</v-layout
          >
        </InformationTooltip>
      </div>

      <div
        class="app-theme__center-content--closebutton"
        @click="closeUnassignedPudDialog"
      >
        <v-icon class="app-theme__center-content--closebutton--icon"
          >fal fa-times</v-icon
        >
      </div>
    </v-layout>

    <v-layout class="app-theme__center-content--body" wrap>
      <div
        :class="isMatching ? 'extend-dialog-height' : 'extend-dialog-height'"
      >
        <v-flex md12 class="px-2 pb-2" v-if="isMatching">
          <v-layout
            class="job-details-pud-matching-info align-center pa-2"
            wrap
          >
            <v-flex md12>
              <div>
                <span class="title-text">
                  Job
                  <span class="job-id-text"
                    >#{{jobDetails ? jobDetails.jobId : ""}}
                  </span>
                  - {{jobDetails.displayId}} {{jobDetails ?
                  jobDetails.client.clientName : ""}} ({{jobDetails ?
                  jobDetails.client.id : ""}})
                </span>
              </div>
            </v-flex>
            <v-flex md12>
              <div>
                <span class="title-text">Driver:</span>
                {{jobDetails.additionalJobData ?
                jobDetails.additionalJobData.driverName : '-'}}
              </div>
            </v-flex>
            <v-flex md12>
              <div>
                <span class="title-text">Reference:</span>
                {{referencesOnJob}}
              </div>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout wrap>
            <v-flex md12 class="pa-2">
              <div class="job-pud-list">
                <TransitionGroup name="job-pud" tag="div" v-if="isMatching">
                  <v-layout
                    wrap
                    v-for="(pudItem, index) in pudItems"
                    class="pud-row__content"
                    :class="index === 0 ? 'pt-0' : 'pt-2'"
                    v-if="selectedPudIdToLink === null || selectedPudIdToLink === pudItem.pudId"
                    :key="pudItem.pudId"
                  >
                    <v-flex md12>
                      <v-layout
                        justify-space-between
                        align-center
                        class="pa-2 pud-linking-header-container"
                      >
                        <div>{{index + 1}}.</div>
                        <div class="link-icon-container">
                          <v-layout align-center>
                            <span
                              v-if="selectedPudIdToLink === null"
                              class="link-icon-text pr-2"
                              >{{pudItem.unassignedPudItemReference &&
                              pudItem.unassignedPudItemReference.length > 0 ?
                              'Edit Matches' : 'Match Leg'}}</span
                            >
                            <span
                              v-if="selectedPudIdToLink === pudItem.pudId"
                              class="link-icon-text pr-2"
                              >Select Matching Leg From Below</span
                            >
                            <v-icon
                              :color="pudItem.unassignedPudItemReference && pudItem.unassignedPudItemReference.length > 0 ? 'amber' : 'green'"
                              size="14"
                              :disabled="selectedPudIdToLink !== null"
                              @click="setPudLinkSelection(pudItem.pudId)"
                              >fas fa-link</v-icon
                            >
                          </v-layout>
                        </div>
                      </v-layout>
                    </v-flex>

                    <v-flex md12>
                      <v-layout>
                        <v-flex
                          md12
                          v-if="pudItem.legTypeFlag === 'P' || pudItem.legTypeFlag === 'D'"
                        >
                          <PudCard
                            :index="index"
                            :pudItem="pudItem"
                            :editingPudIndex="-1"
                            :previousPudLoadTime="null"
                          />
                        </v-flex>
                      </v-layout>
                    </v-flex>
                  </v-layout>
                </TransitionGroup>
              </div>
              <Transition name="unassigned" tag="div">
                <v-layout
                  v-if="selectedPudIdToLink || !isMatching"
                  wrap
                  class="mt-2"
                  :class="isMatching ? 'unassigned-pud-list-matching' : 'unassigned-pud-list-add'"
                >
                  <v-flex md12>
                    <UnassignedPudListItem
                      v-if="upiSelectionList.length > 0"
                      :upiSelectionList="upiSelectionList"
                      :selectedSingleId="selectedSingleId"
                      :enableCheckbox="true"
                      :isMatching="isMatching"
                      :selectedGroupIds="selectedGroupIds"
                      :jobContainsGroupMembers="true ? false : checkJobForGroupMembers(null)"
                      :preferredDate="jobDateEpoch"
                      @selectAllByGroupId="selectAllByGroupId"
                      @setSelectedSingleId="setSelectedSingleId"
                    ></UnassignedPudListItem>

                    <v-layout
                      v-if="!upiSelectionList || upiSelectionList.length === 0"
                      justify-center
                      class="pa-2"
                    >
                      <h6>No Records Found</h6>
                    </v-layout>
                  </v-flex>
                </v-layout>
              </Transition>
            </v-flex>
          </v-layout>
        </v-flex>
      </div>
    </v-layout>
    <v-layout style="background-color: #1c1c1f" justify-space-between>
      <v-btn
        outline
        color="red"
        v-if="!selectedPudIdToLink || singlePudView"
        @click="closeUnassignedPudDialog"
        >Cancel</v-btn
      >

      <v-btn
        outline
        color="red"
        v-if="isMatching && selectedPudIdToLink && !singlePudView"
        @click="cancelUnassignedPudLinking"
        >Back</v-btn
      >

      <!-- Booking screen - Adding brand new clean PudItems from UnassignedPudItem -->
      <v-btn
        v-if="!isMatching"
        depressed
        color="blue"
        @click="convertAndEmitPudItems"
        >Add Selected</v-btn
      >
      <!-- Pud matching - Linking several UnassignedPudItem to pud -->
      <v-btn
        v-else-if="isMatching && selectedPudIdToLink"
        depressed
        color="blue"
        @click="linkAllSelectedPoints"
        >Update Linked Points</v-btn
      >

      <v-btn
        depressed
        color="blue"
        v-if="isMatching && !selectedPudIdToLink"
        @click="saveJobDetails"
      >
        save
      </v-btn>
    </v-layout>
  </v-dialog>
</div>
