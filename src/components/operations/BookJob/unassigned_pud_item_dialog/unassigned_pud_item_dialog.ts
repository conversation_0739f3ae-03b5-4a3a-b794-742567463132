import { Component, Prop, Vue } from 'vue-property-decorator';

import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import PudCard from '@/components/operations/BookJob/pud_item_container/pud_card/index.vue';
import UnassignedPudListItem from '@/components/operations/BookJob/unassigned_pud_item_dialog/unassigned_pud_list_item/index.vue';
import { hasAdminOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import JobReferenceDetails from '@/interface-models/Jobs/JobReferenceDetails';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import UnassignedPudItem from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudItem';
import { UnassignedPudItemStatus } from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudItemStatus';
import {
  UpiAddUnassignedPuds,
  UpiGroupSelection,
  UpiSelection,
} from '@/interface-models/Jobs/PUD/UnassignedPudItem/UpiSelection';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import Fuse from 'fuse.js';

import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import {
  returnFormattedDate,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';

@Component({
  components: {
    UnassignedPudListItem,
    PudCard,
    ConfirmationDialog,
    InformationTooltip,
  },
})
export default class UnassignedPudItemDialog
  extends Vue
  implements IUserAuthority
{
  @Prop() public showUnassignedPudDialog: boolean;
  @Prop() public clientId: string;
  @Prop() public unassignedPudItemList: UnassignedPudItem[];
  @Prop() public currentStagedPudItemIds: string[];
  @Prop() public itemsForQuickAdd: string[];
  @Prop({ default: false }) public isMatching: boolean;
  @Prop({ default: null }) public pudDetails: PUDItem | null;
  @Prop({ default: () => [] }) public jobReferences: JobReferenceDetails[];
  @Prop({ default: () => [] }) public pudItems: PUDItem[];
  @Prop({ default: null }) public selectedPudIdToLink: string | null;
  @Prop({ default: false }) public singlePudView: boolean;
  @Prop({ default: null }) public jobDetails: JobDetails | null;
  @Prop({ default: () => [] }) public currentUnassignedPudIds: string[];
  public unstagedPudItemList: UnassignedPudItem[] = [];
  public stagedPudItemList: UnassignedPudItem[] = [];

  public currentStagedGroupIds: string[] = [];

  public selectedUnstagedItems: string[] = [];
  public selectedStagedItems: string[] = [];

  public selectedGroupIds: string[] = [];

  public selectedSingleId: string = '';

  public upiSelectionList: UpiSelection[] = [];

  get showDialog() {
    return this.showUnassignedPudDialog;
  }
  set showDialog(value: boolean) {
    if (!value) {
      this.$emit('closeDialog');
    }
  }
  // Return the time that we wish to sort UPI's against. If we're leg matching,
  // then use the Start of Day epochTime from the pud item. If we're not leg
  // matching, use the date of the job (from the first pud)
  get jobDateEpoch() {
    let dateToCompare: number;
    if (this.isMatching && this.pudDetails) {
      dateToCompare = this.pudDetails.pickupDate
        ? this.pudDetails.pickupDate
        : returnStartOfDayFromEpoch();
    } else {
      dateToCompare =
        this.jobDetails &&
        this.jobDetails.pudItems[0] &&
        this.jobDetails.pudItems[0].pickupDate
          ? this.jobDetails.pudItems[0].pickupDate
          : returnStartOfDayFromEpoch();
    }
    return dateToCompare;
  }

  public setUpiSelectionList(): void {
    if (this.isMatching && this.pudDetails) {
      const assignedUpis: string[] = [];
      const otherPudItems = this.pudItems.filter(
        (x: PUDItem) => x.pudId !== this.pudDetails!.pudId,
      );
      for (const pud of otherPudItems) {
        if (Array.isArray(pud.unassignedPudItemReference)) {
          assignedUpis.concat(pud.unassignedPudItemReference);
        }
      }
      this.upiSelectionList = this.sortUnassignedPudByBestMatch(
        this.unstagedPudItemList,
      )
        .filter((y: UnassignedPudItem) => !assignedUpis.includes(y.id))
        .map((upi: UnassignedPudItem) => {
          return {
            unassignedPudItem: upi,
            isSelected:
              !this.selectedPudIdToLink ||
              !this.pudDetails ||
              !Array.isArray(this.pudDetails.unassignedPudItemReference)
                ? false
                : this.pudDetails.unassignedPudItemReference.includes(upi.id),
            isGroupSelected: false,
          };
        });
    } else {
      let upiGroupedSelectionList: UnassignedPudItem[] = [];
      // get all group reference ids
      let groupReferenceIds: string[] = this.unstagedPudItemList.map(
        (x: UnassignedPudItem) => x.groupReferenceId,
      );
      // we need to find assigned unassigned pud items that were recently unassigned but not yet saved.
      const allUnassignedPudIds: string[] = this.pudItems
        .filter(
          (p) =>
            p.unassignedPudItemReference !== null &&
            p.unassignedPudItemReference !== undefined &&
            p.unassignedPudItemReference.length,
        )
        .flatMap((p) =>
          p.unassignedPudItemReference ? p.unassignedPudItemReference : '',
        );
      const removedIds = this.currentUnassignedPudIds.filter(
        (id) => !allUnassignedPudIds.includes(id),
      );

      // remove duplicates
      groupReferenceIds = [...new Set(groupReferenceIds)];
      for (const groupId of groupReferenceIds) {
        // find all unassigned puds for this group id
        const group: UnassignedPudItem[] = this.unstagedPudItemList.filter(
          (x: UnassignedPudItem) =>
            (x.groupReferenceId === groupId &&
              x.assignedStatus === 'UNASSIGNED') ||
            (x.groupReferenceId === groupId && removedIds.includes(x.id)),
        );
        // push group into array if there is at least one filtered result
        if (group.length > 0) {
          upiGroupedSelectionList = upiGroupedSelectionList.concat(group);
        }
      }
      // Sort the UPI list such that dates closer to the matching date (job
      // date/date of pud we're matching) are prioritised
      upiGroupedSelectionList = upiGroupedSelectionList.sort(
        (a: UnassignedPudItem, b: UnassignedPudItem) =>
          this.sortByDistanceFromDate(
            a.pudDetails.epochTime,
            b.pudDetails.epochTime,
          ),
      );
      // Map to interface used to display
      this.upiSelectionList = upiGroupedSelectionList.map(
        (upi: UnassignedPudItem) => {
          return {
            unassignedPudItem: upi,
            isSelected: false,
            isGroupSelected: this.selectedGroupIds.includes(
              upi.groupReferenceId,
            ),
          };
        },
      );
    }
  }

  public selectAllByGroupId(groupSelection: UpiGroupSelection) {
    if (groupSelection.addGroup) {
      if (!this.selectedGroupIds.includes(groupSelection.groupId)) {
        this.selectedGroupIds.push(groupSelection.groupId);
      }
    } else {
      const foundIndex = this.selectedGroupIds.findIndex(
        (x: string) => x === groupSelection.groupId,
      );
      if (foundIndex !== -1) {
        this.selectedGroupIds.splice(foundIndex, 1);
      }
    }
  }

  public setSelectedSingleId(value: string) {
    this.selectedSingleId = value;
  }

  public checkJobForGroupMembers(upiList: UnassignedPudItem[]): boolean {
    const idList = upiList.map((upi) => upi.groupReferenceId);
    const stagedAndSelectedIds = this.currentStagedGroupIds.concat(
      this.stagedPudItemList.map((item) =>
        item.groupReferenceId ? item.groupReferenceId : item.groupReferenceId,
      ),
    );
    return idList.some((id) => stagedAndSelectedIds.includes(id));
  }

  public setDefaultPudItemLists() {
    const unassignedItemsForClient = this.unassignedPudItemList;
    const currentStagedItems = this.currentStagedPudItemIds;

    // filter out unassigned pud items that are currently assigned to this job
    this.unstagedPudItemList = unassignedItemsForClient.filter(
      (p) => !currentStagedItems.includes(p.id),
    );

    // Pre-stage any quick-add items if they exist, otherwise return a clean list
    this.stagedPudItemList =
      this.itemsForQuickAdd && this.itemsForQuickAdd.length > 0
        ? unassignedItemsForClient.filter((p) =>
            this.itemsForQuickAdd.includes(p.id),
          )
        : [];

    // Find the distinct groupIds for currentStagedPudItemIds and the current
    const currentGroupIds: string[] = [];
    currentStagedItems.forEach((id) => {
      const foundUpi = unassignedItemsForClient.find((upi) => upi.id === id);
      if (
        foundUpi &&
        foundUpi.groupReferenceId &&
        !currentGroupIds.includes(foundUpi.groupReferenceId)
      ) {
        currentGroupIds.push(foundUpi.groupReferenceId);
      }
    });
    this.currentStagedGroupIds = currentGroupIds;
    this.setUpiSelectionList();
  }

  // pull all job references out of job as one string. Used for display and in our best match search query
  get referencesOnJob(): string {
    if (!this.jobReferences) {
      return '';
    }
    return this.jobReferences
      .map((x: JobReferenceDetails) => x.reference)
      .join(' ');
  }

  public sortByDistanceFromDate(epochA: number, epochB: number) {
    const dateToCompare = this.jobDateEpoch;
    // Get startOfDay from epoch time (currently pickupDate is not reliable for UPI)
    const aSod = returnStartOfDayFromEpoch(epochA);
    const bSod = returnStartOfDayFromEpoch(epochB);
    // Calculate delta of start of day times and the time we're comparing
    const ad = dateToCompare
      ? Math.abs(dateToCompare - aSod) / dateToCompare
      : 0;
    const bd = dateToCompare
      ? Math.abs(dateToCompare - bSod) / dateToCompare
      : 0;

    return ad - bd;
  }

  public sortUnassignedPudByBestMatch(
    upiList: UnassignedPudItem[],
  ): UnassignedPudItem[] {
    if (!this.pudDetails) {
      return upiList;
    }
    const upiListSortedByDate = upiList;
    // construct a query that we wish to sort by.
    // get pudPostcode query string
    const pudPostcode: string = this.pudDetails.address.postcode;

    const driverName: string =
      this.jobDetails &&
      this.jobDetails.driverId &&
      this.jobDetails.additionalJobData &&
      this.jobDetails.additionalJobData.driverName &&
      this.jobDetails.additionalJobData.driverName !== '-'
        ? this.jobDetails.additionalJobData.driverName
        : '';

    // Create query string, including the date of the job/pud we're adding to,
    // postcode of address, all references and driver name
    const filterQuery =
      `'${returnFormattedDate(this.jobDateEpoch)}` +
      ' ' +
      pudPostcode +
      ' ' +
      this.referencesOnJob +
      ' ' +
      driverName;

    // Apply our field search weights. Date is highest to prioritize results
    // similar to the date we're matching against.
    const fuse = new Fuse(upiListSortedByDate, {
      includeScore: true,
      keys: [
        // { name: 'formattedPickupDate', weight: 10 },

        {
          name: 'pudDetails.address.postcode',
          weight: 10,
        },
        { name: 'clientSuppliedId', weight: 4 },
        { name: 'driverName', weight: 1 },
      ],
      minMatchCharLength: 2,
      threshold: 1,
    });

    // our best matches
    const fuseSearchRes = fuse.search(filterQuery);

    // Perform secondary sorting so that results of the same score are sorted by date
    // 1. Get list of all unique scores
    // 2. Sort by score asc
    // 3. For each score, filter for results of that score
    // 4. Sort that list by date delta from matching date
    // 5. Return the UPI and flatten map
    const fuseResult = [...new Set(fuseSearchRes.map((s) => s.score))]
      .sort((a, b) => {
        if (a === undefined) {
          return -1;
        } else if (b === undefined) {
          return 1;
        } else {
          return a - b;
        }
      })
      .map((s) =>
        fuseSearchRes
          .filter((r) => r.score === s)
          .sort((a, b) =>
            this.sortByDistanceFromDate(
              a.item.pudDetails.epochTime,
              b.item.pudDetails.epochTime,
            ),
          )
          .map((i) => i.item),
      )
      .flat(2);

    // Now that we have our best matches we should add all remaining unassigned
    // puds to the list. We do this in the case no matches found.
    for (const upi of upiListSortedByDate) {
      const existsAsBestMatch = fuseResult.find(
        (x: UnassignedPudItem) => x.id === upi.id,
      );
      if (existsAsBestMatch) {
        continue;
      }
      fuseResult.push(upi);
    }

    // return a list of unassigned puds with best matches first
    return fuseResult.filter((upi: UnassignedPudItem) => {
      // We should return
      const isInCurrentPud = (
        Array.isArray(this.pudDetails!.unassignedPudItemReference)
          ? this.pudDetails!.unassignedPudItemReference
          : []
      ).includes(upi.id);
      // Filter for puds other than the current one
      const otherPuds = this.pudItems.filter(
        (p) => p.pudId !== this.pudDetails!.pudId,
      );
      // Check that the current UPI is not assigned to any other puds on this job
      const currentUnassignedPudIds = otherPuds.flatMap((p) =>
        p.unassignedPudItemReference ? p.unassignedPudItemReference : [],
      );

      // Check for unassigned and completed, assigned statuses. In case of completed and assigned we also need to confirm that they are assigned/completed for this job and not another job.
      const isUnassigned =
        upi.assignedStatus === UnassignedPudItemStatus.UNASSIGNED;

      const isCompleted =
        upi.assignedStatus === UnassignedPudItemStatus.COMPLETED &&
        this.jobDetails &&
        upi.jobId === this.jobDetails.jobId;

      const isAssigned =
        upi.assignedStatus === UnassignedPudItemStatus.ASSIGNED &&
        this.jobDetails &&
        upi.jobId === this.jobDetails.jobId;

      return (
        !currentUnassignedPudIds.includes(upi.id) &&
        (isInCurrentPud || isUnassigned || isCompleted || isAssigned)
      );
    });
  }

  public convertAndEmitPudItems() {
    // filter upis that are selected
    const selectedUnassignedPuds = this.upiSelectionList.filter(
      (x: UpiSelection) => x.isSelected,
    );

    // map selected upi list to a list of pudItems
    const selectedPudItems = selectedUnassignedPuds.map((x: UpiSelection) =>
      x.unassignedPudItem.asPudItem(),
    );

    // find the current grouping Ids. These should be added to the job as references
    const selectedGroupIds = [
      ...new Set(
        selectedUnassignedPuds.map(
          (x: UpiSelection) => x.unassignedPudItem.groupReferenceId,
        ),
      ),
    ];

    // initialise payload that will be emitted
    const upiAddUnassignedPuds: UpiAddUnassignedPuds = {
      groupIds: selectedGroupIds,
      pudItems: selectedPudItems,
    };

    this.$emit('addPudItemsFromUnassigned', upiAddUnassignedPuds);
  }

  public linkAllSelectedPoints() {
    // The linking view should only be available for stops that have already be
    // saved. If the pudId is not available, we should not be able to link
    // points, so return and show an error.
    if (!this.pudDetails?.pudId) {
      console.error(
        'No pudId found for current PUD. Cannot link points. pudId: ',
        this.pudDetails?.pudId,
      );
      return;
    }
    const selectedUnassignedPudIds: string[] = this.upiSelectionList
      .filter((x: UpiSelection) => x.isSelected)
      .map((upi: UpiSelection) => upi.unassignedPudItem.id);

    this.$emit('linkSelectedPointList', {
      pudId: this.pudDetails.pudId,
      unassignedPudIds: selectedUnassignedPudIds,
    });
    if (this.isMatching) {
      this.selectedUnstagedItems = [];
    }
  }

  public closeUnassignedPudDialog() {
    this.showDialog = false;
  }

  public setPudLinkSelection(pudId: string | null) {
    if (pudId !== null) {
      const foundPud = this.jobDetails
        ? this.jobDetails.pudItems.find((p) => p.pudId === pudId)
        : undefined;

      if (foundPud) {
        this.selectedUnstagedItems = [
          ...this.selectedUnstagedItems,
          ...(foundPud.unassignedPudItemReference
            ? foundPud.unassignedPudItemReference
            : []),
        ];
      }
    }
    this.$emit('update:selectedPudIdToLink', pudId);

    setTimeout(() => {
      this.setUpiSelectionList();
    }, 100);
  }

  public unassignPud(pudId: string) {
    this.$emit('unassignPud', pudId);
  }

  public cancelUnassignedPudLinking() {
    this.selectedSingleId = '';
    this.setPudLinkSelection(null);
  }

  // Save jobDetails gets utilised when isJobPudLinking is true. This will emit an event back to this components parent to send the save request against the updated jobDetails
  public saveJobDetails() {
    this.$emit('saveJobDetails');
  }

  // Mounted called when dialog is open
  // Cases:
  // 1. We are booking a new job and we are adding UnassignedPudItems to a clean job
  //   - Pull in UnassignedPudItems for clientId and status UNASSIGNED
  //   - Pull display list in booking
  // 2. We select a single UnassignedPudItem that we want to add to new job
  //   - Jumps to booking screen with client selected
  public mounted() {
    if (!this.clientId) {
      this.closeUnassignedPudDialog();
    }
    this.setDefaultPudItemLists();
  }

  public isAuthorised(): boolean {
    return hasAdminOrTeamLeaderOrBranchManagerRole();
  }
}
