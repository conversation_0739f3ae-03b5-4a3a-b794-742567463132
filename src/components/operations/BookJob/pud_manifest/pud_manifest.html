<div class="pud-manifest-management-container">
  <v-layout justify-space-between pt-2 :class="{'px-3': !isJobDialog}">
    <div>
      <v-layout align-center>
        <h6
          class="pr-2 pt-2"
          :class="isClientPortal ? 'client-portal-text' : ''"
        >
          Manifest
        </h6>
        <InformationTooltip :right="true">
          <v-layout slot="content"
            >Description, quantity and dimensions for individual manifest items.
          </v-layout>
        </InformationTooltip>
      </v-layout>
    </div>

    <div v-if="!isDisabled">
      <v-tooltip bottom>
        <template v-slot:activator="{ on }">
          <v-icon @click="addManifest" size="18" v-on="on"> far fa-plus</v-icon>
        </template>
        <span>Add Manifest Item</span>
      </v-tooltip>
    </div>
  </v-layout>
  <v-layout
    v-if="manifestList"
    v-for="(manifestItem, manifestItemIndex) of manifestList"
    :key="manifestItemIndex"
    class="manifest-item-container"
    :class="manifestItemIndex === manifestList.length - 1 ? '' : 'mb-2'"
  >
    <v-layout
      justify-space-between
      class="manifest-action-task-bar px-2 task-bar app-theme__center-content--header"
      :class="isClientPortal ? 'client-portal-manifest-taskbar' : ''"
      align-center
    >
      <span
        class="subheader--faded pr-2 pt-2"
        :class="isClientPortal ? 'client-portal-text' : ''"
      >
        {{manifestItemIndex + 1}}.</span
      >
      <div>
        <v-tooltip bottom v-if="!isClientPortal">
          <template v-slot:activator="{ on }">
            <span v-on="on">
              <ConfirmationDialog
                :message="'Please confirm removal of this manifest item.'"
                title="Manifest Item Removal"
                @confirm="removeManifest(manifestItemIndex)"
                :isIcon="true"
                :faIconName="'far fa-minus'"
                :buttonDisabled="manifestList.length <= 1"
                :isOutlineButton="false"
                :isBlockButton="false"
                :buttonColor="'white'"
                :confirmationButtonText="'Confirm'"
                :isCheckboxList="false"
                :dialogIsActive="true"
              >
              </ConfirmationDialog>
            </span>
          </template>
          <span>Remove Manifest Item</span>
        </v-tooltip>

        <v-tooltip bottom v-if="isClientPortal">
          <template v-slot:activator="{ on }">
            <span v-on="on">
              <v-icon
                :size="14"
                :disabled="manifestList.length <= 1"
                @click="removeManifest(manifestItemIndex)"
                >far fa-minus</v-icon
              >
            </span>
          </template>
          <span>Remove Manifest Item</span>
        </v-tooltip>
      </div>
    </v-layout>

    <v-flex
      class="app-bordercolor--600 app-borderside--a px-3"
      :class="isClientPortal ? 'client-portal-manifest-content' : ''"
      style="padding-top: 40px"
    >
      <v-layout wrap>
        <v-flex md8>
          <v-text-field
            :counter="100"
            v-model="manifestItem.description"
            label="Description"
            rows="2"
            maxlength="100"
            :solo="soloInput"
            flat
            :hint="soloInput ? 'Payload Description' : ''"
            :disabled="isDisabled"
            class="v-solo-custom pr-1"
            outline
          ></v-text-field>
        </v-flex>
        <v-flex md4>
          <v-text-field
            label="Quantity"
            v-model.number="manifestItem.quantity"
            :solo="soloInput"
            flat
            step="1"
            :min="0"
            type="number"
            :hint="soloInput ? 'Quantity (#)' : ''"
            :disabled="isDisabled"
            class="v-solo-custom pl-1"
            outline
          >
          </v-text-field>
        </v-flex>

        <v-flex md4>
          <v-text-field
            class="pr-1 v-solo-custom"
            label="Length (m)"
            v-model.number="manifestItem.length"
            :solo="soloInput"
            flat
            type="number"
            :min="0"
            :hint="soloInput ? 'Length (m)' : ''"
            :disabled="isDisabled"
            @input="setVolume(manifestItemIndex)"
            outline
          >
          </v-text-field>
        </v-flex>
        <v-flex md4>
          <v-text-field
            class="px-1 v-solo-custom"
            label="Width (m)"
            v-model.number="manifestItem.width"
            :solo="soloInput"
            flat
            type="number"
            :min="0"
            :hint="soloInput ? 'Width (m)' : ''"
            :disabled="isDisabled"
            @input="setVolume(manifestItemIndex)"
            outline
          >
          </v-text-field>
        </v-flex>
        <v-flex md4>
          <v-text-field
            class="px-1 v-solo-custom"
            label="Height (m)"
            v-model.number="manifestItem.height"
            :solo="soloInput"
            flat
            type="number"
            :min="0"
            :hint="soloInput ? 'Height (m)' : ''"
            :disabled="isDisabled"
            @input="setVolume(manifestItemIndex)"
            outline
          >
          </v-text-field>
        </v-flex>
        <!-- 
        <v-flex md4>
          <v-text-field
            class="pr-2"
            label="Weight (kg)"
            v-model.number="manifestItem.weight"
            color="orange darken-2"
            :solo="soloInput"
            flat
            :min="0"
            type="number"
            :hint="soloInput ? 'Weight (kg)' : ''"
            :disabled="true"
          >
          </v-text-field>
        </v-flex> -->

        <v-flex md12>
          <v-text-field
            class="pr-1 v-solo-custom"
            label="Barcode"
            v-model="manifestItem.barcode"
            :solo="soloInput"
            flat
            :min="0"
            :hint="soloInput ? 'Barcode' : ''"
            :disabled="isDisabled"
            outline
          >
          </v-text-field>
        </v-flex>
      </v-layout>
    </v-flex>
  </v-layout>
</div>

<!-- 
   
        <v-text-field
          label="Weight (kg)"
          type="number"
          v-model.number="manifestItem.weight"
          color="orange darken-2"
          :solo="soloInput"
          flat
          min="0"
          @input="weightChanged"
          :class="weightIsRequired ? 'form-field-required' : ''"
          :rules="weightIsRequired ? [validationRules.required] : []"
          :hint="soloInput ? 'Weight (kg)' : ''"
          :disabled="formDisabled"
        >
        </v-text-field> -->
