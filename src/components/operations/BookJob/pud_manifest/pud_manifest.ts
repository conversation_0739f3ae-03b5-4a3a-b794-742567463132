import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import { RoundCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { Portal } from '@/interface-models/Generic/Portal';
import { Manifest } from '@/interface-models/Jobs/Manifest';
import { sessionManager } from '@/store/session/SessionState';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  components: { ConfirmationDialog, InformationTooltip },
})
export default class PudManifest extends Vue {
  @Prop({ default: () => [] }) public manifestList: Manifest[];
  @Prop({ default: false }) public soloInput: boolean;
  @Prop({ default: false }) public descriptionRequired: boolean;
  @Prop({ default: false }) public isDisabled: boolean;
  @Prop({ default: false }) public isJobDialog: boolean;

  public mounted() {
    if (this.manifestList && this.manifestList.length < 1) {
      this.manifestList.push(new Manifest());
    }
  }

  public addManifest() {
    if (!this.manifestList) {
      return;
    }
    this.manifestList.push(new Manifest());
  }

  public removeManifest(index: number) {
    this.manifestList.splice(index, 1);
  }
  // If width, height and length are non-null and non-zero, then multiply them
  // together to get the volume for one unit
  public setVolume(index: number) {
    const manifest = this.manifestList[index];
    if (manifest && manifest.width && manifest.height && manifest.length) {
      const vol = RoundCurrencyValue(
        manifest.width * manifest.height * manifest.length,
        3,
      );
      manifest.volume = vol;
    }
  }

  get isClientPortal(): boolean {
    return sessionManager.getPortalType() === Portal.CLIENT;
  }
}
