<template>
  <div class="flex-row content-center">
    <v-autocomplete
      v-model="syncedModelValue"
      :items="clientPersonSelectList"
      label="Client Dispatcher"
      :class="{ 'form-field-required': props.required }"
      class="v-solo-custom"
      :rules="props.required ? [validationRules.required] : []"
      item-value="value"
      item-text="key"
      browser-autocomplete="on"
      :auto-select-first="true"
      clearable
      solo
      flat
      color="Orange"
      :disabled="!props.client_id || clientContactsIsLoading || disabled"
      :hide-details="true"
    >
      <template v-slot:no-data>
        <v-list-tile
          class="add-dispatcher"
          @click="newDispatcherDialog"
          style="cursor: pointer"
        >
          <strong> Create New Dispatcher</strong>
          <v-spacer></v-spacer>
          <v-icon size="14">fa-light fa-user-plus</v-icon>
        </v-list-tile>
      </template>
    </v-autocomplete>
    <CreateDispatcherDialog
      :job_id="job_id"
      :client_id="client_id"
      :clientId="clientId"
      :clientName="clientName"
      :defaultDispatcherId="defaultDispatcherId"
      :clientPersonIds="clientPersonIds"
      :required="true"
      :value="value"
      :dialogIsActive="dialogIsActive"
      @clientPerson="handleNewDispatcher"
      @dialogIsActive="dialogIsActive = $event"
      :isAdhocOnly="isAdhocOnly"
    />

    <div class="flex-row align-center pl-2">
      <v-tooltip bottom v-if="syncedModelValue">
        <template #activator="{ on: tooltip }">
          <v-btn
            flat
            icon
            v-on="{ ...tooltip }"
            @click="viewDispatcherContactDetails = true"
            :disabled="!syncedModelValue"
            class="ma-0"
          >
            <v-icon size="20">far fa-info-circle</v-icon>
          </v-btn>
        </template>
        <span>View Dispatcher</span>
      </v-tooltip>
      <v-tooltip bottom v-else>
        <template #activator="{ on: tooltip }">
          <v-btn
            icon
            flat
            v-on="{ ...tooltip }"
            @click="(dialogIsActive = true), (isAdhocOnly = true)"
            class="ma-0"
          >
            <v-icon size="26">add_circle</v-icon>
          </v-btn>
        </template>
        <span>Add New Adhoc Dispatcher</span>
      </v-tooltip>
    </div>
    <GDialog
      v-if="viewDispatcherContactDetails && props.value"
      :width="'650px'"
      :title="'Client Dispatcher Details'"
      :confirmBtnText="'Okay'"
      :cancelBtnText="'Cancel'"
      :confirmDisabled="false"
      :isActionable="false"
      @closeDialog="viewDispatcherContactDetails = false"
      @confirm="viewDispatcherContactDetails = false"
      :isLoading="false"
    >
      <div class="pa-3">
        <GTitle
          class="mb-4"
          title="Client Dispatcher"
          subtitle="Information about the dispatcher for this job."
          :divider="false"
        />
        <!-- First Name and Last Name -->
        <div class="d-flex flex-wrap mb-4">
          <div class="flex-grow-1">
            <label class="label-txt">First Name</label>
            <div class="value-txt">{{ props.value.firstName }}</div>
          </div>
          <div class="flex-grow-1">
            <label class="label-txt">Last Name</label>
            <div class="value-txt">{{ props.value.lastName }}</div>
          </div>
        </div>
        <!-- Contact Numbers -->
        <div class="d-flex flex-wrap mb-4">
          <div class="flex-grow-1">
            <label class="label-txt">Mobile Number</label>
            <div class="value-txt">
              {{
                props.value.contactMobileNumber
                  ? formatPhoneNumber(props.value.contactMobileNumber)
                  : '-'
              }}
            </div>
          </div>
          <div class="flex-grow-1">
            <label class="label-txt">Landline Number</label>
            <div class="value-txt">
              {{
                props.value.contactLandlineNumber
                  ? formatPhoneNumber(props.value.contactLandlineNumber)
                  : '-'
              }}
            </div>
          </div>
        </div>

        <!-- Email Address -->
        <div v-if="props.value.emailAddress.length > 0" class="mb-3">
          <label class="label-txt">Email Address</label>
          <div class="value-txt">
            {{
              props.value.emailAddress[0] ? props.value.emailAddress[0] : '-'
            }}
          </div>
        </div>
      </div>

      <div class="pa-3">
        <v-btn
          block
          solo
          :color="'primary'"
          @click="viewDispatcherContactDetails = false"
          class="btn mb-2"
          >Close</v-btn
        >
      </div>
    </GDialog>
  </div>
</template>

<script setup lang="ts">
import { formatPhoneNumber } from '@/helpers/StringHelpers/StringHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { ClientRelatedContact } from '@/interface-models/Client/ClientRelatedContact';
import { KeyValue } from '@/interface-models/Generic/KeyValue/KeyValue';
import { Validation } from '@/interface-models/Generic/Validation';
import {
  ClientIdAndClientPersonWithAuthDetailsList,
  ClientPerson,
  ClientPersonWithAuthDetails,
} from '@/interface-models/User/ClientPerson';
import { useUserManagementStore } from '@/store/modules/UserManagementStore';
import { v4 as uuidv4 } from 'uuid';
import {
  computed,
  ComputedRef,
  Ref,
  ref,
  watch,
  WritableComputedRef,
} from 'vue';

import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import CreateDispatcherDialog from './create_dispatcher_dialog.vue';
const userManagementStore = useUserManagementStore();

const emit = defineEmits(['input']);
defineExpose({ refreshDefaultDispatcher });

interface IProps {
  clientName: string;
  clientId: string;
  client_id: string;
  clientPersonIds: string[];
  defaultDispatcherId?: string | null;
  job_id?: string;
  value:
    | ClientPerson
    | ClientRelatedContact
    | ClientPersonWithAuthDetails['clientPerson'];
  required: boolean;
  disabled: boolean;
}
const props = withDefaults(defineProps<IProps>(), {
  defaultDispatcherId: '',
  client_id: '',
  clientId: '',
  job_id: undefined,
  required: false,
  disabled: false,
});

const adhocDispatcher: Ref<ClientPerson | null> = ref(null);
const adhocDispatcherId: string = uuidv4().replace(/-/g, '');
const viewDispatcherContactDetails: Ref<boolean> = ref(false);
const dialogIsActive: Ref<boolean> = ref(false);
const isAdhocOnly: Ref<boolean> = ref(false);
/**
 * A writable computed property that syncs the components v-modeled client person's ID (props.value).
 * It provides a getter and a setter for handling the client person ID.
 *
 * - The getter returns the `_id` of the client person if it exists, otherwise an empty string.
 * - The setter updates the client person based on the provided `clientPerson_id`.
 *   If the ID is empty, it emits a new `ClientPerson` object. If a matching client person is found,
 *   it emits that client person.
 *
 * @returns {WritableComputedRef<string>} A writable computed reference to the client person's ID.
 */
const syncedModelValue: WritableComputedRef<string> = computed({
  get(): string {
    if (isAdhocDispatcher.value) {
      return adhocDispatcherId;
    } else {
      return props.value && props.value._id ? props.value._id : '';
    }
  },
  set(clientContact: string): void {
    if (!clientContact) {
      emit('input', new ClientPerson());
    }
    let foundContact: ClientPerson | undefined = clientContacts.value.find(
      (x: ClientPerson) => x._id === clientContact,
    );
    // If we don't find the selected dispatcher Id within the client persons list we will attempt to find the selected id in the related contacts list.
    if (!foundContact) {
      const foundRelatedContact: ClientRelatedContact | undefined =
        clientRelatedContacts.value.find(
          (x: ClientRelatedContact) => x._id === clientContact,
        );

      if (foundRelatedContact) {
        // Construct clientPerson from clientRelatedContact so there are no issues when the related contact gets added to the job.
        foundContact = {
          _id: foundRelatedContact._id,
          company: foundRelatedContact.company,
          firstName: foundRelatedContact.firstName,
          lastName: foundRelatedContact.lastName,
          emailAddress: foundRelatedContact.emailAddress,
          contactLandlineNumber: foundRelatedContact.contactLandlineNumber,
          contactMobileNumber: foundRelatedContact.contactMobileNumber,
          receivesEmails: foundRelatedContact.receivesEmails,
          authRefId: null,
        };
      }
    }

    if (foundContact) {
      emit('input', foundContact);
    }
  },
});

/**
 * Emit the selected client contact back to parent via v-model
 * @return {void}
 */
function clientPersonSelected(clientPerson_id: string): void {
  const foundContact = clientContacts.value.find(
    (x: ClientPerson) => x._id === clientPerson_id,
  );
  if (foundContact) {
    emit('input', foundContact);
  }
}

/**
 * Return a list of client persons from our ClientIdAndClientPersonWithAuthDetailsList interface
 * @return {ClientPerson[]}
 */
const clientContacts = computed((): ClientPerson[] => {
  if (!props.client_id) {
    return [];
  }
  const clientContacts: ClientIdAndClientPersonWithAuthDetailsList | null =
    userManagementStore.clientIdAndClientPersonWithAuthDetailsList;
  if (!clientContacts || clientContacts.client_id !== props.client_id) {
    return [];
  }
  return clientContacts.clientPersonWithAuthDetailsList
    .filter((c) => c.clientRoles.some((r) => r.roleId === 1))
    .map((x: ClientPersonWithAuthDetails) => x.clientPerson);
});

const clientRelatedContacts = computed((): ClientRelatedContact[] => {
  const clientDetailsStore = useClientDetailsStore();
  if (
    !clientDetailsStore.clientRelatedContacts ||
    clientDetailsStore.clientRelatedContacts.length === 0 ||
    clientDetailsStore.clientRelatedContacts[0].clientId !== props.clientId
  ) {
    return [];
  }
  const clientRelatedContacts = useClientDetailsStore()
    .clientRelatedContacts as ClientRelatedContact[];

  return clientRelatedContacts.filter((x: ClientRelatedContact) =>
    x.roleIds.includes(1),
  );
});

/**
 * Return a keyValue list of our client's contacts. Utilised in the autocomplete input.
 * @return {KeyValue} KeyValue for our html autocomplete select input
 */
const clientPersonSelectList = computed((): KeyValue[] => {
  const dispatcherSelectList: KeyValue[] = [];

  // If the job has an adhoc dispatcher we will add this dispatcher to the selectable list.
  if (isAdhocDispatcher.value) {
    const currentAdhocDispatcher = JSON.parse(JSON.stringify(props.value));
    dispatcherSelectList.push({
      value: adhocDispatcherId,
      key:
        currentAdhocDispatcher.firstName +
        ' ' +
        currentAdhocDispatcher.lastName,
    });
  }

  const clientPersonSelectList: KeyValue[] = clientContacts.value.map(
    (x: ClientPerson) => {
      return {
        value: x._id ? x._id : '',
        key: x.firstName + ' ' + x.lastName,
      };
    },
  );

  const relatedContacts: KeyValue[] = clientRelatedContacts.value.map(
    (x: ClientRelatedContact) => {
      return {
        value: x._id ? x._id : '',
        key: x.firstName + ' ' + x.lastName,
      };
    },
  );

  return dispatcherSelectList
    .concat(clientPersonSelectList)
    .concat(relatedContacts);
});

/**
 * Computed boolean on whether we are loading the client's contacts. We will watch this value and set
 * the default dispatcher to the job
 * @return {boolean} boolean to indicate the user is waiting for the client's contacts response
 */
const clientContactsIsLoading = computed((): boolean => {
  return userManagementStore.isLoadingClientPersonAuthDetailsList;
});

/**
 * Watch clientContactsIsLoading and set default dispatcher from client's contacts.
 * @return {void}
 */
watch(clientContactsIsLoading, (newValue: boolean, oldValue: boolean) => {
  if (!props.job_id && oldValue && !newValue) {
    refreshDefaultDispatcher();
  }
});

/**
 * Called when a change of loading state is detected. If the loading state has changed from true to false,
 * the default dispatcher selection is refreshed.
 */
function refreshDefaultDispatcher(): void {
  if (props.defaultDispatcherId) {
    const defaultDispatcher = clientContacts.value.find(
      (x: ClientPerson) => x._id === props.defaultDispatcherId,
    );

    if (defaultDispatcher && defaultDispatcher._id) {
      syncedModelValue.value = defaultDispatcher._id;
      clientPersonSelected(defaultDispatcher._id);
    }
  }
}

/**
 * Computes the validation rules from the store.
 * @returns {ComputedRef<Validation>} A computed reference to the validation rules.
 */
const rules: ComputedRef<Validation> = computed(() => {
  return validationRules;
});

/**
 * A computed property to determine if the dispatcher is an ad-hoc dispatcher.
 * Checks if the dispatcher does not have an ID and has a first name.
 * @returns {ComputedRef<boolean>} True if the dispatcher is ad-hoc, false otherwise.
 */
const isAdhocDispatcher: ComputedRef<boolean> = computed(() => {
  return !props.value._id && props.value.firstName !== '';
});

function newDispatcherDialog(): void {
  isAdhocOnly.value = false;
  dialogIsActive.value = true;
}

watch([clientContacts, clientRelatedContacts], () => {}, { immediate: true });

function handleNewDispatcher(newDispatcher) {
  const newItem = {
    value: newDispatcher._id ?? adhocDispatcherId,
    key: `${newDispatcher.firstName} ${newDispatcher.lastName}`,
  };

  if (
    !clientPersonSelectList.value.find((item) => item.value === newItem.value)
  ) {
    clientPersonSelectList.value.push(newItem);
  }

  adhocDispatcher.value = newDispatcher;
  syncedModelValue.value = newItem.value;

  // Update the prop value for v-autocomplete
  emit('input', newDispatcher);
  // close Dialog
  dialogIsActive.value = false;
  isAdhocOnly.value = false;
}
</script>

<style scoped lang="scss">
@media (min-width: $user-portal-desktop-breakpoint) {
}

.add-dispatcher {
  cursor: pointer;
  &:hover {
    color: var(--primary);
    v-icon {
      color: var(--primary) !important;
      fill: var(--primary) !important;
    }
  }
}

.flex-grow-1 {
  margin: 4px;
}

.label-txt {
  margin-left: 6px;
  color: var(--heading-text-color);
}

.value-txt {
  background-color: var(--background-color-300);
  border-radius: $border-radius-sm;
  padding: 10px;
  margin-top: 4px;
  font-size: $font-size-18;
  color: var(--text-color);
}
</style>
