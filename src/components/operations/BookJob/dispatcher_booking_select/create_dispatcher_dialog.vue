<template>
  <GDialog
    v-if="isActive"
    :width="'720px'"
    ref="dialogRef"
    :title="'Create New Dispatcher'"
    :confirmBtnText="`Create ${computedTitle}`"
    :confirmDisabled="false"
    :isActionable="isContactTypeSelected"
    :isDelete="true"
    deleteBtnText="Back"
    @closeDialog="cancelMaintenance"
    @confirm="saveNewDispatcher"
    @deleteItem="
      () => {
        isAdhocOnly ? closeDialog() : (isContactTypeSelected = false);
      }
    "
    :isLoading="false"
  >
    <div
      v-if="!isContactTypeSelected"
      class="grid grid-cols-2 gap-4 px-3 py-3"
      md8
    >
      <GTitle
        title="Select Dispatcher Type"
        subtitle="Select dispatcher contact type to create"
        :divider="true"
      />
      <v-flex
        v-for="item in selectItems"
        :key="item.value"
        @click="selectOption(item.value)"
        :class="[
          'select-contact-type-items',
          {
            disabled:
              item.key === 'Login Dispatcher' &&
              sessionManager.isClientPortal() &&
              !isAccount,
          },
        ]"
      >
        <h3 class="item-value">{{ item.key }}</h3>
        <p class="item-des">{{ item.description }}</p>
        <div
          v-if="
            item.key === 'Login Dispatcher' &&
            sessionManager.isClientPortal() &&
            !isAccount
          "
          class="tooltiptxt"
        >
          <v-icon>priority_high</v-icon>
          ACCOUNT MANAGEMENT ROLE REQUIRED
        </div>
      </v-flex>

      <v-divider></v-divider>
      <div class="dialog-actions-container">
        <GButton
          class="action-btn"
          :color="'error'"
          outlined
          @click="cancelMaintenance"
          >Cancel</GButton
        >
      </div>
    </div>

    <div v-else>
      <div class="px-3 py-3" md8>
        <h1 class="header--light">{{ computedSubtitle }}</h1>
        <v-divider class="mt-2"></v-divider>
        <v-flex
          md12
          v-if="selectedClientDispatcherType === DispatcherType.CompanyContacts"
        >
          <v-alert type="info" value="true">
            <strong class="pr-2"
              >Info: This Dispatcher will is be created as New Company
              Contact.</strong
            >
            <ul class="pt-1">
              <li>
                The Registered Email will receive Account Activation Email.
              </li>
              <li>User can login to Client Portal after Activation.</li>
            </ul>
          </v-alert>
        </v-flex>
      </div>
      <div
        class="px-3 py-3"
        v-if="
          selectedClientDispatcherType === DispatcherType.Adhoc &&
          adhocDispatcher
        "
      >
        <GTitle
          class="mb-2"
          title="Client Adhoc Dispatcher"
          subtitle="Manually add a one - off client dispatcher to the job."
          :divider="false"
        />
        <GTextField
          v-model="adhocDispatcher.firstName"
          :placeholder="'First Name'"
          :rules="[rules.required]"
        ></GTextField>

        <GTextField
          v-model="adhocDispatcher.lastName"
          :placeholder="'Last Name'"
        ></GTextField>

        <GTextField
          v-model="adhocDispatcher.emailAddress[0]"
          :placeholder="'Email Address'"
        ></GTextField>

        <GTextField
          v-model="adhocDispatcher.contactMobileNumber"
          :placeholder="'Mobile Number'"
          v-mask="'0### ### ###'"
          :rules="[rules.numbers]"
        ></GTextField>

        <GTextField
          v-model="adhocDispatcher.contactLandlineNumber"
          :placeholder="'Landline Number'"
          v-mask="'## #### ####'"
          :rules="[rules.numbers]"
        ></GTextField>
      </div>
      <div
        v-if="
          selectedClientDispatcherType === DispatcherType.ClientRelatedPersons
        "
      >
        <ClientDetailsRelatedContactMaintenance
          :clientRelatedContact="clientRelatedContact"
          :relatedContactsAssociatedCommonAddressIds.sync="
            relatedContactsAssociatedCommonAddressIds
          "
          :relatedContactsDefaultCommonAddressIds.sync="
            relatedContactsDefaultCommonAddressIds
          "
          @cancelMaintenance="cancelMaintenance"
          :clientsCommonAddresses="clientsCommonAddresses"
        />
      </div>

      <div
        v-if="selectedClientDispatcherType === DispatcherType.CompanyContacts"
      >
        <ClientPersonMaintenance
          ref="clientPersonMaintenanceDialogRef"
          :client_id="client_id"
          :clientId="clientId"
          :clientName="clientName"
          :clientPersonWithAuthDetails="clientPersonWithAuthDetails"
          @cancelMaintenance="cancelMaintenance"
          @setIsEmailVerificationLoading="setIsEmailVerificationLoading"
          @setIsMobileVerificationLoading="setIsMobileVerificationLoading"
          @setDefaultDispatcher="setDefaultDispatcher"
          :defaultDispatcherId="defaultDispatcherId"
          :clientPersonIds="clientPersonIds"
          :dispatcherOnly="true"
          @addClientPersonDispatcher="addClientPersonDispatcher"
        >
        </ClientPersonMaintenance>
      </div>
    </div>
  </GDialog>
</template>

<script setup lang="ts">
import ClientPersonMaintenance from '@/components/admin/ClientDetails/components/client_details_contacts/client_details_client_person_maintenance.vue';
import ClientDetailsRelatedContactMaintenance from '@/components/admin/ClientDetails/components/client_details_contacts/client_details_related_contact_maintenance.vue';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAccountManagementRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import {
  validate,
  validationRules,
} from '@/helpers/ValidationHelpers/ValidationHelpers';
import { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import {
  ClientCommonAddressRelatedContactSave,
  saveCommonAddressAndRelatedContact,
} from '@/interface-models/Client/ClientCommonAddressRelatedContactSave';
import { ClientRelatedContact } from '@/interface-models/Client/ClientRelatedContact';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import {
  ClientPerson,
  ClientPersonWithAuthDetails,
  ClientRoleStatus,
  SaveNewClientPersonRequest,
} from '@/interface-models/User/ClientPerson';
import { UserRoleStatus } from '@/interface-models/User/UserRoleStatus';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useWebsocketStore } from '@/store/modules/WebsocketStore';
import { sessionManager } from '@/store/session/SessionState';
import { computed, ComputedRef, onMounted, Ref, ref, watch } from 'vue';

const rules = validationRules;

enum DispatcherType {
  Adhoc = 'Adhoc',
  ClientRelatedPersons = 'ClientRelatedPersons',
  CompanyContacts = 'CompanyContacts',
}
// Define props
const props = withDefaults(
  defineProps<{
    clientName: string;
    clientId: string;
    client_id: string;
    clientPersonIds: string[];
    defaultDispatcherId?: string | null;
    job_id?: string;
    value:
      | ClientPerson
      | ClientRelatedContact
      | ClientPersonWithAuthDetails['clientPerson'];
    required: boolean;
    dialogIsActive: boolean;
    isAdhocOnly?: boolean;
  }>(),
  {
    defaultDispatcherId: '',
    client_id: '',
    clientId: '',
    job_id: undefined,
    required: false,
    isAdhocOnly: false,
  },
);

// Local state for dialog editing
const isActive = computed(() => props.dialogIsActive);

const dialogRef = ref(null);
const clientPersonMaintenanceDialogRef = ref(null);
const isContactTypeSelected = ref(false);
const adhocDispatcher: Ref<ClientPerson | null> = ref(null);
const clientRelatedContact: Ref<ClientRelatedContact | null> = ref(null);
const clientPersonWithAuthDetails: Ref<ClientPersonWithAuthDetails | null> =
  ref(null);
const clientsCommonAddresses: Ref<ClientCommonAddress[] | null> = ref([]);

const relatedContactsAssociatedCommonAddressIds: Ref<string[]> = ref([]);
const relatedContactsDefaultCommonAddressIds: Ref<string[]> = ref([]);

const isSaveNewClientPersonRequest: Ref<boolean> = ref(false);
const isDefaultDispatcher: Ref<boolean> = ref(false);
const isEmailVerificationLoading: Ref<boolean> = ref(false);
const isMobileVerificationLoading: Ref<boolean> = ref(false);

const selectedClientDispatcherType: Ref<DispatcherType> = ref(
  DispatcherType.Adhoc,
);

const isAccount = computed(() => hasAccountManagementRole());

const emit = defineEmits([
  'clientPerson',
  'addClientPersonDispatcherId',
  'dialogIsActive',
]);

// selection Create ContactType options
const selectItems = computed(() => [
  {
    key: 'Adhoc Dispatcher',
    value: DispatcherType.Adhoc,
    description: `An ad-hoc dispatcher should be used if this person does not regularly book jobs on behalf of the customer. Ad-hoc dispatchers will not be saved into the platform for re-use.`,
  },
  {
    key: 'Non-Login Dispatcher',
    value: DispatcherType.ClientRelatedPersons,
    description: `A non-login Dispatcher should be used if this person does not require login access to the client portal. These users will have their details saved into the platform in order to be able to easily add their details to future deliveries.`,
  },
  {
    key: 'Login Dispatcher',
    value: DispatcherType.CompanyContacts,
    description: `A login Dispatcher should be created if this user requires login access to the client portal. These Dispatcher will be able to book new jobs and view jobs in progress in real-time. These users will have their details saved into the platform in order to be able to easily add their details to future deliveries.`,
  },
]);

// select Contact type and load selected view
const selectOption = (value: DispatcherType) => {
  selectedClientDispatcherType.value = value;
  loadSelectedContent();
};

const loadSelectedContent = () => {
  if (selectedClientDispatcherType.value) {
    isContactTypeSelected.value = true;
  }
};

// Computed property for the Dialog buttons
const computedTitle = computed(() => {
  switch (selectedClientDispatcherType.value as DispatcherType) {
    case DispatcherType.Adhoc:
      return 'Adhoc Dispatcher';
    case DispatcherType.ClientRelatedPersons:
      return 'Client Related Dispatcher';
    case DispatcherType.CompanyContacts:
      return 'Company Dispatcher';
    default:
      return '';
  }
});

// Computed property for the Dialog Title
const computedSubtitle = computed(() => {
  switch (selectedClientDispatcherType.value as DispatcherType) {
    case DispatcherType.Adhoc:
      return 'Create One-off Dispatcher';
    case DispatcherType.ClientRelatedPersons:
      return 'Creates New Client Related Contact As Dispatcher';
    case DispatcherType.CompanyContacts:
      return 'Create New Client Contact As Dispatcher';
    default:
      return '';
  }
});
// selected Dispatcher type from selector
function selectedDispatcherType(type: DispatcherType) {
  switch (type) {
    case DispatcherType.Adhoc:
      newAdhocDispatcher();
      break;
    case DispatcherType.ClientRelatedPersons:
      addNewClientRelatedContact();
      break;
    case DispatcherType.CompanyContacts:
      addNewClientPerson();
      break;
  }
}

// create new dispatcher based on selection
function saveNewDispatcher() {
  switch (selectedClientDispatcherType.value as DispatcherType) {
    case DispatcherType.Adhoc:
      addAdhocDispatcherToJob();
      break;
    case DispatcherType.ClientRelatedPersons:
      saveClientRelatedContact();
      break;
    case DispatcherType.CompanyContacts:
      saveClientPerson();
      break;
    default:
      addAdhocDispatcherToJob();
      break;
  }
}

// Close the dialog emit for parent component
function closeDialog() {
  emit('dialogIsActive', false); // Emit `false` to close the dialog
}

/**
 * Initializes a new ad-hoc dispatcher by creating a new instance of ClientPerson.
 */
function newAdhocDispatcher(): void {
  // Initialize a new ClientPerson instance
  adhocDispatcher.value = new ClientPerson();
}
/**
 * Adds a new client related contact.
 * Initializes a new client related contact and sets the client ID and role as Dispatcher.
 */
function addNewClientRelatedContact() {
  // Call getClientsCommonAddresses before creating a new ClientRelatedContact
  getClientsCommonAddresses();
  clientRelatedContact.value = new ClientRelatedContact();
  clientRelatedContact.value.clientId = props.clientId;
  clientRelatedContact.value.roleIds = [1];
}

/**
 * Initializes a new client person with authorization details.
 * Resets the `clientPersonWithAuthDetails` ref to a new instance of `ClientPersonWithAuthDetails` and role as Dispatcher.
 */
function addNewClientPerson(): void {
  clientPersonWithAuthDetails.value = new ClientPersonWithAuthDetails();
  // Ensure clientRoles is initialized
  if (!clientPersonWithAuthDetails.value.clientRoles) {
    clientPersonWithAuthDetails.value.clientRoles = [];
  }
  // Add a new ClientRoleStatus with roleId 1
  clientPersonWithAuthDetails.value.clientRoles.push({
    roleId: 1,
    roleName: 'Dispatcher',
    status: UserRoleStatus.PENDING,
  } as ClientRoleStatus);
}

/**
 * Adds the ad-hoc dispatcher to the job. Validates the ad-hoc dispatcher data before emitting
 * an 'input' (v-model) event with the ad-hoc dispatcher's information. Resets the local
 * ad-hoc dispatcher state to null afterwards.
 */
function addAdhocDispatcherToJob() {
  if (!validate(dialogRef.value)) {
    return;
  }
  emit('clientPerson', adhocDispatcher.value);
  cancelMaintenance();
}

/**
 * Cancels and closes the ad-hoc dispatcher dialog by setting it to null.
 */
function cancelMaintenance(): void {
  switch (selectedClientDispatcherType.value as DispatcherType) {
    case DispatcherType.Adhoc:
      adhocDispatcher.value = null;
      break;
    /**
     * Cancels the related contact maintenance operation.
     * Clears the current related contact information and associated default common address IDs.
     */
    case DispatcherType.ClientRelatedPersons:
      clientRelatedContact.value = null;
      relatedContactsAssociatedCommonAddressIds.value = [];
      relatedContactsDefaultCommonAddressIds.value = [];
      break;
    /**
     * Cancels the current client person maintenance.
     * Resets the `clientPersonWithAuthDetails` ref to null.
     */
    case DispatcherType.CompanyContacts:
      clientPersonWithAuthDetails.value = null;
      break;
    default:
      adhocDispatcher.value = null;
      break;
  }
  isContactTypeSelected.value = false;
  closeDialog();
}

/**
 * Saves the client related contact details. Validates the form data before saving.
 * Constructs the save request with the related contact and associated address information.
 * If validation fails or the related contact does not match the client ID, it aborts the save operation.
 */
function saveClientRelatedContact() {
  if (
    !validate(dialogRef.value) ||
    !clientRelatedContact.value ||
    !props.client_id ||
    clientRelatedContact.value.clientId !== props.clientId
  ) {
    return;
  }
  const request: ClientCommonAddressRelatedContactSave = {
    clientId: props.clientId,
    clientCommonAddress: null,
    clientRelatedContact: clientRelatedContact.value,
    relatedContactsAssociatedCommonAddressIds:
      relatedContactsAssociatedCommonAddressIds.value,
    relatedContactsDefaultCommonAddressIds:
      relatedContactsDefaultCommonAddressIds.value,
    updatedClientCommonAddresses: [],
  };

  const savedSiteContact = saveCommonAddressAndRelatedContact(request);

  if (!savedSiteContact) {
    showNotification('Something went wrong.', {
      title: 'Error',
      type: HealthLevel.ERROR,
    });
  }
  emit('clientPerson', clientRelatedContact.value);
  cancelMaintenance();
}

/**
 * Fetches common addresses associated with the client.
 * Retrieves and sets the client's common addresses.
 * Logs an error if the fetch operation fails.
 */
async function getClientsCommonAddresses(): Promise<void> {
  // fetch client related contacts
  const commonAddresses: ClientCommonAddress[] | null =
    await useClientDetailsStore().getClientCommonAddressesByClientId(
      props.clientId,
    );
  if (!commonAddresses) {
    console.error(
      "Something went wrong when fetching client's common addresses.",
    );
    return;
  }
  clientsCommonAddresses.value = commonAddresses;
}

/**
 * Event emitted back from maintenance component when adding an existing client person to this client.
 * @param {string} clientPerson_id - The client person id that will be added to this clients dispatchers
 * array.
 * @returns {void}
 */
function addClientPersonDispatcher(clientPerson): void {
  clientPersonWithAuthDetails.value = new ClientPersonWithAuthDetails();
  clientPersonWithAuthDetails.value.clientPerson = clientPerson;
}

/**
 * Computed ref that determines if the current client person is new.
 *
 * @returns {ComputedRef<boolean>} True if the client person is new (without an ID), false otherwise.
 */
const isNewClientPerson: ComputedRef<boolean> = computed(() => {
  if (!clientPersonWithAuthDetails.value) {
    return false;
  }
  return !clientPersonWithAuthDetails.value.clientPerson._id;
});

/**
 * Saves the client person details.
 * Validates the input and sends a WebSocket request to save the new client person.
 * Handles the response by adding the new client person to the user management store.
 */
function saveClientPerson() {
  if (
    !clientPersonWithAuthDetails.value ||
    !validate(clientPersonMaintenanceDialogRef.value)
  ) {
    return;
  }

  isSaveNewClientPersonRequest.value = true;

  const rolesToAdd: number[] = isNewClientPerson.value
    ? [
        ...new Set(
          clientPersonWithAuthDetails.value.clientRoles.map(
            (x: ClientRoleStatus) => x.roleId,
          ),
        ),
      ]
    : [];

  const saveNewClientPersonRequest: SaveNewClientPersonRequest = {
    clientPerson: clientPersonWithAuthDetails.value.clientPerson,
    clientId: props.client_id,
    rolesToAdd,
    defaultDispatcher: isDefaultDispatcher.value,
  };

  useWebsocketStore().sendWebsocketRequest(
    new WebSocketRequest(
      sessionManager.isClientPortal()
        ? `/user/${sessionManager.getUserName()}/clientDetails/clientPersonDispatchers/saveNewClientPerson`
        : '/clientDetails/clientPersonDispatchers/saveNewClientPerson',
      saveNewClientPersonRequest,
      true,
    ),
  );
  emit('clientPerson', clientPersonWithAuthDetails.value.clientPerson);
  cancelMaintenance();
}

/**
 * Sets the loading state for email verification.
 *
 * @param {boolean} isLoading - True if email verification is in progress, false otherwise.
 */
function setIsEmailVerificationLoading(isLoading: boolean): void {
  isEmailVerificationLoading.value = isLoading;
}

/**
 * Sets the loading state for mobile verification.
 *
 * @param {boolean} isLoading - True if mobile verification is in progress, false otherwise.
 */
function setIsMobileVerificationLoading(isLoading: boolean): void {
  isMobileVerificationLoading.value = isLoading;
}

/**
 * Set the default dispatcher boolean. Emitted from child.
 */
function setDefaultDispatcher(value: boolean): void {
  isDefaultDispatcher.value = value;
}

// Function to be called when isActive changes
const onIsActiveChange = () => {
  selectedDispatcherType(selectedClientDispatcherType.value as DispatcherType);
};

// Watcher for selected dispatcher type
watch(selectedClientDispatcherType, (newValue: string) => {
  if (newValue) {
    if (newValue === DispatcherType.Adhoc) {
      newAdhocDispatcher();
    }
    selectedDispatcherType(newValue as DispatcherType);
  } else {
    selectedDispatcherType(
      selectedClientDispatcherType.value as DispatcherType,
    );
  }
});

// Watch the isActive computed property for dialog
watch(isActive, onIsActiveChange);

// watch isAdhoc only prop to set option type to ADHOC and jump to add adhoc form
watch(
  () => props.isAdhocOnly,
  (newVal) => {
    if (newVal) {
      selectOption(DispatcherType.Adhoc);
    } else {
      isContactTypeSelected.value = false;
    }
  },
);

//  default section when dialog is active
onMounted(() => {
  newAdhocDispatcher();
});
</script>
