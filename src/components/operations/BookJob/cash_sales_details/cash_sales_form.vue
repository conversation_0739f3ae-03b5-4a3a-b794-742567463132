<template>
  <div class="px-3">
    <v-form ref="form">
      <v-layout wrap>
        <v-flex md12 pb-1>
          <v-text-field
            label="Contact Name"
            class="v-solo-custom form-field-required"
            :rules="[validate.required]"
            v-model="clientDetails.contactName"
            outline
            hide-details
            :disabled="isDisplayOnly"
          />
        </v-flex>
        <v-flex md12 pb-1>
          <v-text-field
            label="Email"
            class="v-solo-custom form-field-required"
            outline
            hide-details
            :rules="[validate.required, validate.email]"
            v-model="clientDetails.contactEmail"
            :disabled="isDisplayOnly"
          />
        </v-flex>
        <v-flex md6 pb-1 class="pr-1">
          <v-text-field
            label="Landline"
            :disabled="isDisplayOnly"
            :rules="
              clientDetails.contactMobile === '' ? [validate.required] : []
            "
            persistent-hint
            :class="[
              { 'form-field-required': clientDetails.contactMobile === '' },
            ]"
            :hint="'One landline or mobile number required.'"
            v-model="clientDetails.contactNumber"
            v-mask="'## #### ####'"
            class="v-solo-custom"
            outline
            hide-details
          />
        </v-flex>
        <v-flex md6 pb-1 class="pl-1">
          <v-text-field
            :disabled="isDisplayOnly"
            label="Mobile"
            :rules="
              clientDetails.contactNumber === '' ? [validate.required] : []
            "
            persistent-hint
            :class="{
              'form-field-required': clientDetails.contactNumber === '',
            }"
            :hint="'One landline or mobile number required.'"
            v-mask="'#### ### ###'"
            v-model="clientDetails.contactMobile"
            class="v-solo-custom"
            outline
            hide-details
          />
        </v-flex>
        <v-flex md6 pb-1class="pr-1">
          <v-text-field
            :disabled="isDisplayOnly"
            label="ABN"
            v-mask="'## ### ### ###'"
            v-model="clientDetails.abn"
            class="v-solo-custom"
            outline
            hide-details
          />
        </v-flex>
        <v-flex md6 pb-1 class="pl-1">
          <v-text-field
            :disabled="isDisplayOnly"
            label="ACN"
            v-mask="'### ### ###'"
            v-model="clientDetails.acn"
            class="v-solo-custom"
            outline
            hide-details
          />
        </v-flex>
        <v-flex md12>
          <v-text-field
            :disabled="isDisplayOnly"
            label="Company Name"
            v-model="clientDetails.companyName"
            class="v-solo-custom"
            outline
            hide-details
          />
        </v-flex>
        <v-flex md12>
          <AddressSearchAU
            :formDisabled="false || isDisplayOnly"
            :address="clientDetails.locationAddress"
            :enableSuburbSelect="false"
            :enableReturnToDefaultDispatchAddress="false"
            :enableNicknamedAddress="false"
            :label="'Company Address'"
            :isClientLocation="true"
          />
        </v-flex>

        <v-flex md12>
          <v-layout wrap class="pb-3">
            <v-flex md6>
              <v-text-field
                :disabled="isDisplayOnly"
                label="Photos Required"
                type="number"
                hide-details
                class="form-field-required v-solo-custom"
                :rules="[validate.required]"
                v-model.number="proofOfDelivery.pudPhotos"
                outline
              />
            </v-flex>
            <v-flex md6 class="pl-4">
              <v-checkbox
                :disabled="isDisplayOnly"
                hide-details
                v-model="proofOfDelivery.pudPaperwork"
                label="Leg Paperwork"
                color="orange"
              ></v-checkbox>
            </v-flex>
            <v-flex md6>
              <v-checkbox
                :disabled="isDisplayOnly"
                hide-details
                v-model="proofOfDelivery.jobPaperwork"
                label="Job Paperwork"
                color="orange"
              ></v-checkbox>
            </v-flex>
            <v-flex md6 class="pl-4">
              <v-checkbox
                :disabled="isDisplayOnly"
                hide-details
                v-model="proofOfDelivery.deviceSignature"
                label="Driver Signature"
                color="orange"
              ></v-checkbox>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-form>
  </div>
</template>

<script setup lang="ts">
import AddressSearchAU from '@/components/common/addressing/address-search-au/index.vue';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { ProofOfDelivery } from '@/interface-models/Generic/ProofOfDelivery/ProofOfDelivery';
import { CashSaleClientDetails } from '@/interface-models/Jobs/CashSalesDetails/CashSalesDetails';
import { Ref, ref } from 'vue';

const emit = defineEmits<{
  (event: 'update:isDialogOpen', payload: boolean): void;
}>();

const validate = validationRules;
const form: Ref<any> = ref(null);

const props = withDefaults(
  defineProps<{
    isDisplayOnly: boolean;
    clientDetails: CashSaleClientDetails;
    proofOfDelivery: ProofOfDelivery;
  }>(),
  {
    isDisplayOnly: false,
  },
);

// Function to emit validation form status
function validateForm(): boolean {
  if (!form.value) {
    return false;
  }
  return form.value.validate();
}

defineExpose({
  validateForm,
});
</script>
