import { Component, Prop, Vue } from 'vue-property-decorator';

import AddressSearchAU from '@/components/common/addressing/address-search-au/index.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import CashSalesForm from '@/components/operations/BookJob/cash_sales_details/cash_sales_form.vue';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import ProofOfDelivery from '@/interface-models/Generic/ProofOfDelivery/ProofOfDelivery';
import { Validation } from '@/interface-models/Generic/Validation';
import { CashSaleClientDetails } from '@/interface-models/Jobs/CashSalesDetails/CashSalesDetails';
import { ErrorMessage } from '@/interface-models/Jobs/PUD/PudValidationError';

@Component({
  components: { AddressSearchAU, ContentDialog, CashSalesForm },
})
export default class CashSalesDetails extends Vue {
  @Prop() public clientDetails: CashSaleClientDetails;
  @Prop() public proofOfDelivery: ProofOfDelivery;
  @Prop() public validate: Validation;
  @Prop() public cashSaleClientExpansionPanelOpen: boolean[];
  @Prop({ default: true }) public isDisplayOnly: boolean;
  @Prop({ default: false }) public cashSalesDialog: boolean;

  public dialogActive: boolean = false;
  public isFormValid: boolean = true;
  public errorMessages: ErrorMessage[] = [];

  get expansionPanel(): boolean[] {
    if (this.isDisplayOnly) {
      return [true];
    }
    return this.cashSaleClientExpansionPanelOpen;
  }

  set expansionPanel(value: boolean[]) {
    if (this.isDisplayOnly) {
      return;
    }
    this.$emit('setCashSaleExpansionPanel', value);
  }

  get openDialog(): boolean {
    return this.dialogActive;
  }

  set openDialog(value: boolean) {
    this.$emit('input', value);
  }

  // Open/Close Cash Sales Dialog
  public toggleDialog(): void {
    this.dialogActive = !this.dialogActive;
  }

  /**
   * Shows a notification with the provided text and type
   * @param {string} text The text to display in the notification
   * @param {HealthLevel} type The type of notification to display
   */
  public showAppNotification(text: string, type?: HealthLevel): void {
    showNotification(text, {
      type,
      title: 'Job Booking - Cash Sales',
    });
  }

  /**
   * Handle form validation for cash sales dialog
   */
  public handleValidation() {
    // Check if the ref exists and is the correct type
    const cashForm = this.$refs.cashForm as InstanceType<typeof Vue> & {
      validateForm: () => boolean;
    };

    if (cashForm && typeof cashForm.validateForm === 'function') {
      this.isFormValid = cashForm.validateForm();

      // Check if the form is valid before closing the dialog
      if (this.isFormValid) {
        this.toggleDialog(); // Close the dialog if the form is valid
      } else {
        this.showAppNotification(FORM_VALIDATION_FAILED_MESSAGE); // Show notification if validation fails
      }
    }
  }

  /**
   * Handle form validation for cash sales DisplayOnly Form
   * Open Dialog if Validation fails
   */
  public validateCashSalesForm(): boolean {
    const cashForm = this.$refs.cashForm as InstanceType<typeof Vue> & {
      validateForm: () => boolean;
    };

    if (cashForm && typeof cashForm.validateForm === 'function') {
      this.toggleDialog();
      return cashForm.validateForm();
    }
    return false;
  }

  mounted() {
    this.dialogActive = this.cashSalesDialog;
  }
}
