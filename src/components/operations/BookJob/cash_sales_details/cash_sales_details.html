<v-layout>
  <ContentDialog
    :showDialog.sync="openDialog"
    title="Cash sale job - Client Contact Details"
    width="35%"
    contentPadding="pa-0"
    @cancel="toggleDialog"
    @confirm="handleValidation"
    :showActions="true"
    :isConfirmUnsaved="false"
    :isDisabled="false"
    :isLoading="false"
  >
    <v-layout class="app-theme__center-content--body px-4 pt-2 pb-2" wrap>
      <CashSalesForm
        ref="cashForm"
        :isDisplayOnly="false"
        :clientDetails="this.clientDetails"
        :proofOfDelivery="this.proofOfDelivery"
      ></CashSalesForm>
    </v-layout>
  </ContentDialog>

  <v-flex>
    <v-layout id="cash-sales-details">
      <v-expansion-panel
        v-model="expansionPanel"
        class="expansion-panel-container mb-2"
      >
        <v-expansion-panel-content class="expansion-item-container">
          <template v-slot:actions>
            <div align-center class="expand-icon">
              <v-icon size="15">keyboard_arrow_down</v-icon>
            </div>
          </template>
          <template v-slot:header>
            <v-layout justify-start align-start>
              <span
                class="expansion-header"
                :class="!isFormValid ? 'invalid': ''"
                >Client Contact Details *</span
              >
            </v-layout>
          </template>
          <CashSalesForm
            ref="cashForm"
            :isDisplayOnly="true"
            :clientDetails="this.clientDetails"
            :proofOfDelivery="this.proofOfDelivery"
          ></CashSalesForm>
        </v-expansion-panel-content>
      </v-expansion-panel>

      <v-tooltip bottom>
        <template v-slot:activator="{ on }">
          <v-btn icon v-on="on" @click="toggleDialog">
            <v-icon size="26"> edit </v-icon>
          </v-btn></template
        >
        <span>Edit Cash Sales</span>
      </v-tooltip>
    </v-layout>
  </v-flex>
</v-layout>
