.expansion-panel-container {
  border: 1px solid $border-color;
  border-radius: $border-radius-sm;
  .expansion-item-container {
    border-radius: $border-radius-sm !important;
    background-color: var(--background-color-300) !important;

    .expand-icon {
      background-color: var(--accent-secondary);
      border-radius: 20px;
      height: 20px;
      width: 20px;
      padding: 2px !important;
      .v-icon {
        position: absolute;
        color: black !important;
        font-weight: 800;
      }
    }

    .expansion-header {
      font-size: $font-size-16;
      font-weight: 400;
      color: var(--accent-secondary);
      &.invalid {
        color: $error !important;
      }
    }
    .lineitem__label {
      font-size: $font-size-12;
      text-transform: uppercase;
      font-weight: 600;
      color: var(--light-text-color);
    }
    .lineitem__value {
      font-size: $font-size-14;
      font-weight: 400;
    }
  }
}
