.pud-card {
  margin-bottom: 6px;
  box-shadow: $box-shadow-dark;

  &.unassigned-type {
    border-bottom: none;
    margin-bottom: 0px;

    border-left: 1px solid var(--background-color-600);

    .pud-card__primary-content {
      background-color: transparent !important;
      border: none;
    }
  }

  .pud-card__primary-content {
    background-color: var(--background-color-300) !important;
    border: 1px solid $translucent;

    &.action-required {
      border: 2px solid $warning;
    }

    &.new-pud-item {
      border-left: 2px solid var(--background-color-650);
    }

    &.pud-item-is-edited {
      border-left: 3px solid $average !important;
      background-color: var(--background-color-600) !important;
      box-shadow: 4px 6px 6px var(--shadow-color) !important;

      transition: 0.15s;
    }

    .card-contents__row {
      padding-bottom: 1px;
    }

    $label-container-dim: 20px;
    $font-size-med: $font-size-12;
    $font-size-small: $font-size-11;

    .pud-legflag {
      font-size: $font-size-med;
      color: var(--text-color);
      text-align: center;
      border-radius: $border-radius-sm;
      font-weight: 800;
      min-height: 21px !important;
      min-width: 21px !important;
      margin-right: 4px;

      &.pickup--type {
        background: $pickup;
        box-shadow: 0px 0px 6px 0px $pickup;
        color: white;
      }

      &.dropoff--type {
        background: $drop;
        box-shadow: 0px 0px 6px 0px $drop;
        color: white;
      }
    }

    .zone-container {
      font-size: $font-size-small;
      color: var(--text-color);
      text-align: center;
      border-radius: 2px;
      font-weight: 700;
      height: $label-container-dim;
      margin-right: 5px;
      padding: 1px 4px;
      border: 1px solid #c7dcff;
    }

    .pud-delivery-name {
      font-weight: 500;
      font-size: $font-size-med;
      color: var(--light-text-color);

      &__primary {
        font-size: $font-size-13;
        font-weight: 600;
        color: var(--text-color);
      }
    }

    .secondary-address-info {
      font-size: $font-size-med;
      color: var(--text-color);
    }

    .pud-faded-text {
      font-size: $font-size-small;
      color: var(--light-text-color);
    }

    .pud-site-contact {
      padding: 0px 2px;
      font-weight: 500;
      font-size: $font-size-small;
    }

    .arrival-departure-container {
      .arrival-info-container {
        display: flex;
        align-items: center;
      }

      .arrival-time {
        font-size: $font-size-11;
        color: var(--bg-light);
        padding-right: 4px;
        font-weight: 500;
      }
    }
  }

  .standby-rate-alert {
    color: #f44336;
    font-size: 0.85em;
    padding: 2px 0;
  }

  .pud-time {
    font-weight: 500;
    font-size: 1.05em;
  }

  .pud-formatted-address {
    font-weight: 300;
    font-size: $font-size-medium;
  }

  .pud-contact-details {
    font-weight: 400;
    font-size: 0.75em;
    color: grey;
  }

  .pud-formatted-address {
    font-weight: 400;
    color: rgb(167, 167, 167);
    font-size: 0.85em;

    &.accent-text {
      text-transform: uppercase;
      color: rgb(253, 203, 66) !important;
      font-weight: 500;
      padding-top: 3px;
    }
  }

  .dropoff-reference-text {
    font-size: 0.75em;

    text-transform: uppercase;
    font-weight: 500;
    padding-top: 4px;
  }

  .payload-description {
    font-size: $font-size-12;
  }

  .arrival-pud-text {
    color: $drop-highlight;
    font-size: $font-size-12;
    font-weight: 600;
    padding: 10px 4px;
  }
  .depart-pud-text {
    color: $pickup-highlight;
    font-size: $font-size-12;
    font-weight: 600;
    padding: 10px 4px;
  }
}
