import { Component, Prop, Vue } from 'vue-property-decorator';
import { PUDItem } from '@/interface-models/Jobs/PUD/PUDItem';
import ORSRoute from '@/interface-models/Generic/Route/ORSRoute';
import moment from 'moment-timezone';
import {
  TimeDefinitions,
  timeDefinitions,
} from '@/interface-models/Generic/TimeDefinitions/TimeDefinitions';
import {
  TimeVariation,
  timeVariations,
} from '@/interface-models/Generic/TimeVariation/TimeVariation';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import NotesList from '@/components/common/notes_list/notes_list.vue';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import ZoneRateType from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRateType';
import UnitRate from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRate';
import {
  PudPropertyChecker,
  returnAddressErrorSummary,
  returnPudValidationSummary,
} from '@/helpers/JobDataHelpers/JobDataHelpers';
import { WeightRequirement } from '@/interface-models/Jobs/WeightRequirement/WeightRequirement';
import { PudMaintenanceDialogConfig } from '@/interface-models/Jobs/PUD/UnassignedPudItem/PudMaintenanceDialogConfig';
import { PudMaintenanceType } from '@/interface-models/Jobs/PUD/UnassignedPudItem/PudMaintenanceType';
import { JobOperationType } from '@/interface-models/Jobs/JobOperationType';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import UnassignedPudItem from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudItem';
import { JobRouteProgress } from '@/interface-models/Jobs/JobRouteProgress';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';

@Component({
  components: {
    NotesList,
  },
})
export default class PudCard extends Vue {
  @Prop() public pudItem: PUDItem;
  @Prop() public index: number;
  @Prop() public firstPudTimeDefinition: number;
  @Prop() public editingPudIndex: number;
  @Prop() public rateToApply: RateTableItems;
  @Prop() public savedRoute: ORSRoute;
  @Prop() public previousPudArrivalTime: number | null;
  @Prop() public previousPudLoadTime: number | null;
  @Prop() public firstPudArrivalTime: number;
  @Prop({ default: 1 }) public rateTypeId: number;
  @Prop({ default: false }) public unassignedPudType: boolean;
  @Prop({ default: null }) public weightRequirement: WeightRequirement | null;
  @Prop({ default: false }) public isFirstPickupInJob: boolean;
  @Prop({ default: null }) public unassignedPudItem: UnassignedPudItem | null;
  @Prop({ default: null }) public jobRouteProgress: JobRouteProgress[];
  @Prop({ default: false }) public isIntegrationRequirementDisplay: boolean;
  public timeDefinitions: TimeDefinitions[] = timeDefinitions;
  public timeVariations: TimeVariation[] = timeVariations;

  public companyDetailsStore = useCompanyDetailsStore();

  get arrivalDefinition() {
    const foundDefinition = this.timeDefinitions.find(
      (item) => item.id === this.pudItem.timeDefinition,
    );
    if (foundDefinition) {
      if (foundDefinition.longName === 'Early Morning') {
        return 'Early AM';
      }
      return foundDefinition.longName;
    }
    return '';
  }

  get hasErrors(): PudPropertyChecker | null {
    if (this.unassignedPudType) {
      return null;
    }
    return returnPudValidationSummary(
      this.pudItem,
      this.rateTypeId,
      this.isFirstPickupInJob,
      this.weightRequirement,
    );
  }

  get pudTypeName() {
    return `${this.pudItem.legTypeFlag === 'P' ? 'P ' : 'D '}`;
  }

  get addressErrors(): string {
    return returnAddressErrorSummary(this.pudItem.address);
  }

  get isImportedPudItem(): boolean {
    return this.pudItem
      ? this.pudItem.unassignedPudItemReference !== null &&
          this.pudItem.unassignedPudItemReference !== undefined &&
          this.pudItem.unassignedPudItemReference.length > 0
      : false;
  }

  public pudItemPayload(dimension: string | number, units: string = 'm') {
    if (dimension && dimension !== '') {
      return `${dimension}${units}`;
    } else {
      return ' - ';
    }
  }

  get showFlexTimeIndicator() {
    if (
      this.index === 0 &&
      this.pudItem.timeDefinition > 3 &&
      this.pudItem.timeDefinition !== 9
    ) {
      return true;
    } else {
      return false;
    }
  }

  get references() {
    const pudReferences = this.pudItem.pickupReference.concat(
      this.pudItem.dropoffReference,
    );
    const builtStr = pudReferences
      .map((ref) => ref.reference)
      .filter((ref) => ref !== '')
      .join(', ');
    return builtStr;
  }

  public returnCorrectTravelTime(time: any) {
    let seconds: number = time;
    let hours: any = 0;
    let minutes: any = 0;
    hours = moment.duration(seconds, 'seconds').hours();
    seconds =
      seconds - moment.duration(parseInt(hours, 10), 'hours').asSeconds();
    minutes = moment.duration(seconds, 'seconds').asMinutes().toFixed(0);
    if (hours < 10) {
      hours = '0' + hours;
    }
    if (minutes < 10) {
      minutes = '0' + minutes;
    }
    return hours + ':' + minutes;
  }

  get routeInformationAvailable() {
    return (
      this.savedRoute &&
      this.savedRoute.routes &&
      this.savedRoute.routes.length > 0 &&
      this.savedRoute.routes[0].segments &&
      this.index > 0 &&
      this.savedRoute.routes[0].segments[this.index - 1]
    );
  }

  get arrivalTimeFound() {
    if (
      !this.pudItem.epochTime ||
      !this.routeInformationAvailable ||
      !this.previousPudArrivalTime ||
      !this.previousPudLoadTime
    ) {
      return null;
    }
    const differenceInSetTimes: number =
      this.pudItem.epochTime - (this.previousPudArrivalTime - 60000);
    const routeDuration =
      this.savedRoute.routes[0]!.segments![this.index - 1].duration;
    const routeTravelTime = routeDuration * 1000 + this.previousPudLoadTime;
    if (this.pudItem.timeDefinition !== 3) {
      if (differenceInSetTimes <= routeTravelTime) {
        return true;
      } else {
        return false;
      }
    }
    if (this.pudItem.timeDefinition === 3) {
      if (differenceInSetTimes <= routeTravelTime) {
        return false;
      } else {
        return true;
      }
    }
    return null;
  }

  get alertMessage() {
    if (!this.previousPudLoadTime) {
      return '';
    }
    let differenceInSetTimes;
    const EarlierTime: any = this.previousPudArrivalTime;
    const CurrentTime = this.pudItem.epochTime;

    if (CurrentTime !== null) {
      differenceInSetTimes = CurrentTime - EarlierTime;
    }

    if (
      this.savedRoute.routes !== null &&
      this.savedRoute.routes[0] !== undefined &&
      this.savedRoute.routes[0].segments !== undefined &&
      differenceInSetTimes !== undefined &&
      CurrentTime !== null
    ) {
      const routeDuration =
        this.savedRoute.routes[0].segments[this.index - 1].duration;
      const routeTravelTime = routeDuration * 1000 + this.previousPudLoadTime;

      if (differenceInSetTimes < routeTravelTime) {
        const leaveBeforeTime = CurrentTime - routeTravelTime;
        const readableTime = moment(leaveBeforeTime)
          .tz(this.companyDetailsStore.userLocale)
          .format('HH:mm a');
        return 'Departure of previous stop by: ' + readableTime + ' required.';
      } else if (differenceInSetTimes > routeTravelTime) {
        const leaveBeforeTime = CurrentTime - routeTravelTime;
        const readableTime = moment(leaveBeforeTime)
          .tz(this.companyDetailsStore.userLocale)
          .format('HH:mm a');
        return (
          'Departure of previous stop should be after ' + readableTime + '.'
        );
      }
    }
  }

  get zoneName() {
    let zoneName = '';
    if (this.unassignedPudType) {
      return zoneName;
    }
    if (!this.rateToApply) {
      return '-';
    }
    switch (this.rateToApply.rateTypeId) {
      case 2:
        zoneName = this.pudItem.rateDetails.zoneName(
          this.rateToApply.rateTypeObject as ZoneRateType[],
        );
        break;
      case 5:
        if (
          this.pudItem.legTypeFlag === 'P' ||
          this.pudItem.legTypeFlag === 'D'
        ) {
          const amount =
            this.pudItem.legTypeFlag === 'P'
              ? this.pudItem.rateDetails.unitPickUps
              : this.pudItem.rateDetails.unitDropOffs;
          zoneName =
            this.pudItem.rateDetails.unitZoneName(
              this.rateToApply.rateTypeObject as UnitRate[],
            ) +
            ' (' +
            amount +
            ')';
        }
        break;
    }
    return zoneName;
  }

  get flexLeaveMessage() {
    if (this.showFlexLeaveTime) {
      const pudType = this.pudItem.legTypeFlag;
      const foundDefinition = this.timeDefinitions.find(
        (item) => item.id === this.firstPudTimeDefinition,
      );
      if (foundDefinition !== undefined) {
        const dateStr = returnFormattedDate(
          this.pudItem.epochTime,
          'DD/MM/YYYY',
        );
        const midnight = moment(dateStr, 'DD/MM/YYYY')
          .tz(this.companyDetailsStore.userLocale)
          .valueOf();

        const leavingAt = moment(midnight)
          .tz(this.companyDetailsStore.userLocale)
          .add(foundDefinition.end)
          .format('HH:mm');

        return (
          `${pudType === 'P' ? 'Pickup ' : 'Dropoff '}` +
          'arrival time is based on a departure of ' +
          leavingAt +
          ' from previous pud.'
        );
      }
    } else if (this.showAsapTimeIndicator) {
      return `Booked at ${this.pudItem.pickupTime} for completion ASAP.`;
    } else {
      return '';
    }
  }

  get showAsapTimeIndicator() {
    if (this.index === 0 && this.pudItem.timeDefinition === 9) {
      return true;
    } else {
      return false;
    }
  }
  get showFlexLeaveTime() {
    if (
      this.index === 1 &&
      this.pudItem.timeDefinition > 3 &&
      this.pudItem.timeDefinition !== 9
    ) {
      return true;
    } else {
      return false;
    }
  }

  get arrivalTime() {
    return this.pudItem.epochTime
      ? returnFormattedDate(this.pudItem.epochTime, 'HH:mm')
      : '- -';
  }
  public returnCorrectPickupTime(epoch: number | null, index: number) {
    if (epoch) {
      return epoch ? returnFormattedDate(epoch, 'HH:mm') : '- -';
    }
  }

  get pudDepartureTime() {
    const epochTime = this.pudItem.epochTime + this.pudItem.loadTime;
    return returnFormattedDate(epochTime, 'HH:mm');
  }

  // // View selected UPI in dialog in a read-only view
  // public viewUnassignedPudItem() {
  //   if (!this.unassignedPudItem) {
  //     return;
  //   }
  //   const operationsStore = useOperationsStore();
  //   const config: PudMaintenanceDialogConfig = {
  //     operationType: JobOperationType.VIEW,
  //     pudType: PudMaintenanceType.UNASSIGNED,
  //     unassignedPudId: this.unassignedPudItem.id,
  //     unassignedPudItem:
  //       this.unassignedPudItem !== undefined
  //         ? this.unassignedPudItem
  //         : undefined,
  //   };
  //   operationsStore.setPudMaintenanceDialogConfig(config);
  //   operationsStore.setViewingPudMaintenanceDialog(true);
  // }

  get pudRouteProgress(): JobRouteProgress | null {
    if (!this.jobRouteProgress || this.jobRouteProgress.length === 0) {
      return null;
    }
    const jobRouteProgress = this.jobRouteProgress.find(
      (x: JobRouteProgress) => x.pudId === this.pudItem.pudId,
    );
    return jobRouteProgress ? jobRouteProgress : null;
  }
}
