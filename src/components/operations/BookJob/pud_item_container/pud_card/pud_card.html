<section
  class="pud-card"
  :class="unassignedPudType && !isIntegrationRequirementDisplay ? 'unassigned-type' : ''"
>
  <v-card
    class="pud-card__primary-content pa-1"
    :class="[pudItem.createdByDriver ? 'action-required' : '', editingPudIndex === index ? 'pud-item-is-edited' : '', 'pud-item-' + index, pudItem.pudId === '' && !unassignedPudType ? 'new-pud-item' : '']"
    elevation="0"
  >
    <v-layout>
      <v-flex>
        <v-layout align-center>
          <span
            class="pud-legflag"
            :class="[pudItem.legTypeFlag === 'P' ? 'pickup--type' : '', pudItem.legTypeFlag === 'D' ? 'dropoff--type' : '']"
            >{{pudTypeName}}
          </span>
          <span class="zone-container" v-if="zoneName !== ''"
            >{{zoneName}}</span
          >
          <span
            class="pud-delivery-name__primary"
            v-if="!addressErrors"
            style="display: flex; align-items: center"
          >
            {{ pudItem.address.suburb}} {{ pudItem.address.state}} {{
            pudItem.address.postcode}}
          </span>
          <v-spacer></v-spacer>
          <span v-if="pudRouteProgress && pudItem.pudId">
            <span
              class="arrival-pud-text"
              v-if="pudRouteProgress.actualArrivalTimeReadable && pudRouteProgress.actualArrivalTimeReadable !== 'Invalid date'"
              >Arrived:{{pudRouteProgress.actualArrivalTimeReadable}}
            </span>
            <span
              class="arrival-pud-text"
              v-if="pudRouteProgress.expectedArrivalTimeReadable && pudRouteProgress.expectedArrivalTimeReadable !== 'Invalid date'"
              >Arrival:{{pudRouteProgress.expectedArrivalTimeReadable}}</span
            >
            <span
              class="depart-pud-text"
              v-if="pudRouteProgress.actualDepartureTimeReadable && pudRouteProgress.actualDepartureTimeReadable !== 'Invalid date'"
              >Departed:{{pudRouteProgress.actualDepartureTimeReadable}}</span
            >
            <span
              v-if="pudRouteProgress.expectedDepartureTimeReadable && pudRouteProgress.expectedDepartureTimeReadable !== 'Invalid date'"
              class="depart-pud-text"
            >
              Departure:{{pudRouteProgress.expectedDepartureTimeReadable}}</span
            >
          </span>
          <!-- <v-tooltip bottom>
            <template v-slot:activator="{ on }">
              <v-icon
                class="pr-1"
                size="12"
                v-on="on"
                color="#ffb917"
                v-if="unassignedPudType && !isIntegrationRequirementDisplay"
                @click="viewUnassignedPudItem"
              >
                fa fa-eye
              </v-icon>
            </template>
            <span>View Leg Details</span>
          </v-tooltip> -->

          <span
            v-if="unassignedPudItem && isIntegrationRequirementDisplay"
            class="arrival-pud-text pr-1"
            >{{unassignedPudItem.assignedStatus}} {{unassignedPudItem.jobId ?
            ('- JOB #' + unassignedPudItem.jobId) : ''}}</span
          >

          <span v-if="hasErrors && hasErrors.isVisible" class="pl-2">
            <v-tooltip bottom>
              <template v-slot:activator="{ on }">
                <v-icon size="14" v-on="on" :color="hasErrors.tooltipColor"
                  >{{hasErrors.tooltipType}}</v-icon
                >
              </template>
              <div class="driver-list__legend">
                <v-layout
                  align-center
                  :class="errorItemIndex !== hasErrors.items.length -1 ? 'pb-1' : ''"
                  v-for="(errorItem, errorItemIndex) in hasErrors.items"
                  :key="errorItem.key"
                >
                  <v-icon size="10" color="error"> {{errorItem.value}} </v-icon>
                  <span class="driver-list__legend--label pl-2"
                    >{{errorItem.description}}</span
                  >
                </v-layout>
              </div>
            </v-tooltip>
          </span>

          <v-icon
            class="pl-2"
            color="success"
            v-if="pudItem.status === 'FINISHED'"
            size="16"
            >fas fa-check-circle
          </v-icon>
        </v-layout>
        <v-layout class="pt-1"><v-divider></v-divider></v-layout>
        <v-layout row wrap>
          <v-flex md12 pt-1 px-1>
            <v-layout align-center justify-start class="card-contents__row">
              <span class="secondary-address-info pr-1">
                {{ pudItem.address.addressLine1}} {{
                pudItem.address.addressLine2}}
              </span>
              <span class="pud-delivery-name">
                - {{ pudItem.customerDeliveryName &&
                pudItem.customerDeliveryName !== '' ?
                pudItem.customerDeliveryName : '-'}}
              </span>
            </v-layout>
            <v-layout class="card-contents__row">
              <span
                class="pud-faded-text pr-1"
                v-if="pudItem.dimensions?.length"
              >
                {{pudItemPayload(pudItem.dimensions.length)}}(L) x</span
              >
              <span
                class="pud-faded-text pr-1"
                v-if="pudItem.dimensions?.width"
              >
                {{pudItemPayload(pudItem.dimensions.width)}}(W) x</span
              >
              <span
                class="pud-faded-text pr-1"
                v-if="pudItem.dimensions?.height"
              >
                {{pudItemPayload(pudItem.dimensions.height)}}(H) x</span
              >
              <span class="pud-faded-text pr-1">
                {{pudItemPayload(pudItem.weight, 'KG')}}(Wgt)</span
              >
              <v-spacer></v-spacer>
              <span class="pud-faded-text pr-1"
                >{{ pudItem.siteContactName !== '' && pudItem.siteContactName ?
                pudItem.siteContactName : '-' }}</span
              >
            </v-layout>
            <v-layout class="card-contents__row align-center">
              <span class="pud-faded-text"
                >{{references ? `# ${references}` : 'No References'}}</span
              >
              <v-spacer></v-spacer>
              <span>
                <span class="pud-faded-text pr-1"
                  >{{pudItem.siteContactMobileNumber &&
                  pudItem.siteContactMobileNumber !== '' ?
                  pudItem.siteContactMobileNumber : '-' }} /</span
                >

                <span class="pud-faded-text"
                  >{{ pudItem.siteContactLandLineNumber !== '' &&
                  pudItem.siteContactLandLineNumber ?
                  pudItem.siteContactLandLineNumber : '-' }}</span
                ></span
              >
            </v-layout>

            <v-layout align-center justify-start class="card-contents__row">
            </v-layout>
          </v-flex>
        </v-layout>

        <v-flex
          md12
          class="arrival-departure-container px-1"
          v-if="!unassignedPudType"
        >
          <v-layout class="arrival-info-container" align-center>
            <span class="arrival-time"
              >{{arrivalDefinition}} Arrival: {{ arrivalTime }}
            </span>
            <span class="arrival-time">Departure: {{pudDepartureTime}}</span>
            <v-tooltip bottom v-if="index !== 0 && arrivalTimeFound">
              <template v-slot:activator="{ on }">
                <v-icon color="grey lighten-1" size="11" v-on="on"
                  >fad fa-exclamation</v-icon
                >
              </template>
              <span>{{alertMessage}}</span>
            </v-tooltip>
            <v-tooltip bottom v-if="showFlexLeaveTime">
              <template v-slot:activator="{ on }">
                <v-icon color="warning" size="12" v-on="on"
                  >fal fa-question-circle</v-icon
                >
              </template>
              <span class="header__flex-time">{{flexLeaveMessage}}</span>
            </v-tooltip>
            <v-tooltip bottom v-if="showFlexTimeIndicator">
              <template v-slot:activator="{ on }">
                <v-icon color="warning" size="12" v-on="on"
                  >fal fa-question-circle</v-icon
                >
              </template>
              <span>{{flexTimeMessage}}</span>
            </v-tooltip>
            <v-tooltip bottom v-if="showAsapTimeIndicator">
              <template v-slot:activator="{ on }">
                <v-icon color="warning" size="12" v-on="on"
                  >fal fa-question-circle</v-icon
                >
              </template>
              <span class="header__flex-time">{{flexLeaveMessage}}</span>
            </v-tooltip>

            <v-spacer></v-spacer>
            <span
              class="arrival-time"
              v-if="pudRouteProgress && pudRouteProgress.arrivalDifferenceInMins && !pudRouteProgress.arrivalDifferenceInMins.includes('NaN')"
              >{{pudRouteProgress.arrivalDifferenceInMins}}</span
            >
          </v-layout>
        </v-flex>
      </v-flex>
    </v-layout>
  </v-card>
</section>
