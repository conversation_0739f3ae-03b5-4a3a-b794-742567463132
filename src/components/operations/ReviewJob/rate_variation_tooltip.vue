<template>
  <v-tooltip v-if="variationMessage" bottom>
    <template #activator="{ on }">
      <span v-on="on" class="asterisk">*</span>
    </template>
    <div style="max-width: 320px">
      <div style="font-weight: 600; margin-bottom: 0.5em">
        {{ variationMessage }}
      </div>
      <ul v-if="adjustedValueDescriptions.length">
        <li v-for="desc in adjustedValueDescriptions" :key="desc">
          {{ desc }}
        </li>
      </ul>
    </div>
  </v-tooltip>
</template>

<script setup lang="ts">
import { computed, ComputedRef } from 'vue';
import { ClientServiceRateVariations } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariations';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';

const props = defineProps<{
  rateVariation: ClientServiceRateVariations;
  type: RateEntityType;
  rateTypeId: JobRateType | null;
  applyRateVariationsToDemurrage?: boolean;
}>();

/**
 * Computed property to determine the human-readable message for the rate variation.
 */
const variationMessage: ComputedRef<string | null> = computed(() => {
  // Rate variations are not applicable for point-to-point or trip rates.
  if (
    props.rateTypeId === JobRateType.POINT_TO_POINT ||
    props.rateTypeId === JobRateType.TRIP
  ) {
    return null;
  }
  let percent: number | null = null;
  if (props.type === RateEntityType.CLIENT) {
    percent = props.rateVariation.clientAdjustmentPercentage ?? null;
  } else if (props.type === RateEntityType.FLEET_ASSET) {
    percent = props.rateVariation.fleetAssetAdjustmentPercentage ?? null;
  }
  if (percent === null || percent === 0) {
    return null;
  }
  const absPercent = Math.abs(percent).toFixed(2).replace(/\.00$/, '');
  const isSurcharge = percent > 0;
  const adjType = isSurcharge ? 'surcharge' : 'discount';
  const entity =
    props.type === RateEntityType.CLIENT ? 'Client' : 'Fleet Asset';

  return `All ${entity} rates are inclusive of ${absPercent}% ${adjType}.`;
});

/**
 * Computed property to provide a list of human-readable strings describing what values are adjusted for the given rateTypeId.
 */
const adjustedValueDescriptions: ComputedRef<string[]> = computed(() => {
  switch (props.rateTypeId) {
    case JobRateType.TIME:
      return ['Hourly rate', 'Standby Rate'];
    case JobRateType.ZONE: {
      const listItems = [
        'Zone base charge ($)',
        'Additional Stop Flagfall ($)',
        'Fleet Asset pay if applicable (%)',
      ];
      if (props.applyRateVariationsToDemurrage) {
        listItems.push('Demurrage charge ($)');
      }
      return listItems;
    }
    case JobRateType.DISTANCE:
      return [
        'Per km rate ($)',
        'Minimum charge ($)',
        'Base Freight charge ($)',
      ];
    case JobRateType.UNIT: {
      const listItems = [
        'Per unit rate ($)',
        'Pickup/delivery flagfall (hand) ($)',
        'Pickup/delivery flagfall (forklift) ($)',
        'Dangerous Goods (DG) charge ($)',
        'Fleet Asset pay if applicable (%)',
      ];
      if (props.applyRateVariationsToDemurrage) {
        listItems.push('Demurrage charge ($)');
      }
      return listItems;
    }
    case JobRateType.ZONE_TO_ZONE: {
      const listItems = [
        'Zone charges ($)',
        'Fleet Asset pay if applicable (%)',
      ];
      if (props.applyRateVariationsToDemurrage) {
        listItems.push('Demurrage charge ($)');
      }
      return listItems;
    }
    default:
      return [];
  }
});
</script>
<style scoped lang="scss">
.asterisk {
  color: var(--primary-light);
  font-size: $font-size-18;
  font-weight: 600;
  cursor: pointer;
}
</style>
