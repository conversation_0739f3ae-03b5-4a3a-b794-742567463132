<template>
  <section class="route-time-management">
    <v-layout row wrap class="time-management pa-2">
      <v-layout justify-end align-center class="status-menu">
        <v-btn
          elevation="2"
          small
          color="orange"
          outline
          depressed
          v-if="enableTimesCopy"
          @click="copyTimeRateDataFromOther"
        >
          <span v-if="type === RateEntityType.CLIENT">
            Copy Times from Fleet Asset
          </span>
          <span v-else> Copy Times from Client </span>
        </v-btn>
        <v-menu
          open-on-hover
          open-delay="500"
          :close-on-click="false"
          :close-on-content-click="false"
          bottom
          left
          v-if="!readOnly"
        >
          <template v-slot:activator="{ on }">
            <v-btn
              flat
              icon
              :color="minimumDurationMet ? 'light-blue' : 'red accent-3'"
              class="ma-0"
              dark
              tabindex="-1"
              v-on="on"
            >
              <v-icon size="16">
                {{
                  minimumDurationMet
                    ? 'fas fa-info-circle'
                    : 'fas fa-exclamation-triangle'
                }}
              </v-icon>
            </v-btn>
          </template>

          <div
            class="pa-2 app-borderside--a app-bordercolor--600 rate-hover__container"
          >
            <v-layout row wrap>
              <v-flex md12 v-if="minChargeMultiplier && minChargeTime">
                <v-layout align-center>
                  <span class="lineitem__label"> Min Time </span>
                  <v-spacer></v-spacer>
                  <span class="lineitem__value">
                    {{ minChargeTime }} {{ minChargeMultiplier.longName }}
                  </span>
                  <v-icon
                    size="13"
                    class="pl-1 check-active"
                    v-if="minimumDurationMet"
                    >far fa-check
                  </v-icon>
                  <v-icon v-else size="13" class="pl-1" color="error">
                    fas fa-exclamation-triangle
                  </v-icon>
                </v-layout>
                <v-layout>
                  <v-divider class="my-2"></v-divider>
                </v-layout>
              </v-flex>
              <v-flex
                md12
                v-if="chargeMultiplier && chargeRate && graceTypeName"
              >
                <v-layout align-center>
                  <span class="lineitem__label"> Grace Type </span>
                  <v-spacer></v-spacer>
                  <span class="lineitem__value">
                    {{ chargeRate }} {{ chargeMultiplier.longName }}
                    {{ graceTypeName }}
                  </span>
                </v-layout>
                <v-layout>
                  <v-divider class="my-2"></v-divider>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-layout align-center>
                  <span class="lineitem__label"> Leg Adjustments </span>
                  <v-spacer></v-spacer>
                  <span class="lineitem__value pr-2">
                    {{ firstLegDuration }} / {{ lastLegDuration }}
                  </span>
                </v-layout>
                <v-layout>
                  <v-divider class="my-2"></v-divider>
                </v-layout>
              </v-flex>
            </v-layout>
          </div>
        </v-menu>
      </v-layout>
      <v-flex md2 offset-md1 class="pl-1">
        <v-text-field
          class="v-solo-custom"
          v-model="startDateInput"
          :rules="[validationRules.formattedDate]"
          hint="Start Date"
          :disabled="readOnly"
          mask="##/##/####"
          persistent-hint
          box
          label="Start Date"
          @focus="$event.target.select()"
          color="orange"
          outline
        />
      </v-flex>
      <v-flex md2 class="pl-1">
        <v-text-field
          class="v-solo-custom"
          v-model="startTimeInput"
          :disabled="readOnly"
          :rules="[validationRules.twentyFourHourTime]"
          hint="24-hour time"
          mask="##:##"
          persistent-hint
          color="orange"
          outline
          label="Start"
          @focus="$event.target.select()"
        />
      </v-flex>
      <v-flex md2 class="pl-1">
        <v-text-field
          v-model="finishDateInput"
          :disabled="readOnly"
          hint="Finish Date"
          mask="##/##/####"
          persistent-hint
          color="orange"
          outline
          :messages="
            startDateInput !== finishDateInput
              ? 'Warning: Finish Date Differs from Start Date'
              : []
          "
          label="Finish Date"
          @focus="$event.target.select()"
          class="v-solo-custom"
        >
        </v-text-field>
      </v-flex>
      <v-flex md2 class="pl-1">
        <v-text-field
          class="v-solo-custom"
          v-model="finishTimeInput"
          :disabled="readOnly"
          :rules="[validationRules.twentyFourHourTime]"
          hint="24-hour time"
          mask="##:##"
          persistent-hint
          color="orange"
          outline
          @focus="$event.target.select()"
          label="Finish"
        />
      </v-flex>

      <v-flex
        md3
        class="pl-2"
        :class="{ 'break-edit': !readOnly }"
        @click="!readOnly ? openBreakDurationDialog() : null"
      >
        <v-text-field
          hint="In minutes"
          v-model.number="breakDurationInput"
          disabled
          persistent-hint
          color="orange"
          outline
          label="Break Duration"
          type="number"
          class="v-solo-custom"
        >
          <template v-slot:append v-if="!readOnly">
            <v-tooltip bottom>
              <template v-slot:activator="{ on }">
                <v-btn icon v-on="on" flat class="ma-0">
                  <v-icon size="20" color="grey lighten-2">far fa-edit</v-icon>
                </v-btn>
              </template>
              Edit Break Times
            </v-tooltip>
          </template>
        </v-text-field>
      </v-flex>
    </v-layout>
    <v-layout wrap>
      <v-flex md6 offset-md6 class="expansion-panel-container" mt-2 px-1>
        <v-layout class="expansion-item-container" wrap>
          <v-flex md12 px-2>
            <v-layout justify-space-between>
              <span class="lineitem__label"> Total Duration </span>
              <span
                class="lineitem__value"
                :class="totalDurationOfJob.includes('d') ? 'error-value' : ''"
              >
                {{ totalDurationOfJob }}
              </span>
            </v-layout>
            <v-layout>
              <v-divider class="my-2"></v-divider>
            </v-layout>

            <v-layout justify-space-between align-center>
              <v-layout align-center>
                <span class="lineitem__label"> TRANSIT </span>
                <span class="lineitem__label" style="font-size: 10px"
                  >({{ easyReadBreakDown }})</span
                >
              </v-layout>
              <span class="lineitem__value">
                ${{ DisplayCurrencyValue(freightCharge ?? 0) }}
              </span>
            </v-layout>
            <v-layout>
              <v-divider
                :class="!standbyRatesApply ? 'mt-2' : 'my-2'"
              ></v-divider>
            </v-layout>
            <v-layout justify-space-between v-if="standbyRatesApply">
              <v-layout align-center>
                <span class="lineitem__label"> STANDBY </span>
                <span class="lineitem__label" style="font-size: 10px"
                  >({{ totalEditedStandbyDuration }})</span
                >
                <v-tooltip bottom v-if="standbyFuelSurchargeApplies">
                  <template v-slot:activator="{ on }">
                    <v-icon
                      size="6"
                      color="info"
                      v-on="on"
                      style="
                        cursor: pointer;
                        padding-left: 2px;
                        padding-bottom: 4px;
                      "
                    >
                      fas fa-asterisk
                    </v-icon>
                  </template>

                  <span>Fuel Surcharge Applies</span>
                </v-tooltip>
                <v-icon
                  v-if="!readOnly"
                  @click="setStandbyDialog"
                  size="12"
                  class="edit-break-icon"
                  >fal fa-edit</v-icon
                >
                <v-icon
                  v-if="readOnly"
                  @click="setStandbyDialog"
                  size="12"
                  class="edit-break-icon"
                  >fal fa-search</v-icon
                >
                <v-dialog
                  v-if="standbyDialogController"
                  v-model="standbyDialogController"
                  :width="500"
                  content-class="v-dialog-custom"
                  class="ma-0"
                >
                  <v-card color="#242329">
                    <v-layout
                      justify-space-between
                      align-center
                      class="task-bar app-theme__center-content--header no-highlight"
                    >
                      <span>Standby Durations</span>

                      <div
                        class="app-theme__center-content--closebutton"
                        @click="standbyDialogController = false"
                      >
                        <v-icon
                          class="app-theme__center-content--closebutton--icon"
                          >fal fa-times</v-icon
                        >
                      </div>
                    </v-layout>
                    <v-alert
                      type="warning"
                      class="ma-3"
                      :value="standbyDates.length > 1"
                    >
                      <span>Warning:</span>
                      <ul>
                        <li>
                          Your current date selection spans multiple days ({{
                            standbyDates.join(', ')
                          }}). If this is incorrect, please review and adjust
                          the date(s) in Status Management before proceeding.
                        </li>
                      </ul>
                    </v-alert>
                    <v-layout wrap class="pa-2">
                      <v-flex
                        md12
                        v-for="(
                          duration, durationIndex
                        ) of standbyDurationInputs"
                        px-2
                        :key="durationIndex"
                      >
                        <v-layout>
                          <span
                            class="pa-1 subheader--faded"
                            style="display: block"
                            >{{ getSuburbNameByPudId(duration.pudId) }}</span
                          >
                          <v-spacer></v-spacer>

                          <span
                            class="pa-1"
                            style="font-size: 12px"
                            v-if="
                              standbyOverlapsByPud &&
                              standbyOverlapsByPud.get(duration.pudId)
                            "
                          >
                            * Includes
                            <span class="accent-text--primary">
                              {{
                                returnDurationFromMilliseconds(
                                  standbyOverlapsByPud.get(duration.pudId) ?? 0,
                                )
                              }}
                            </span>
                            of Break
                          </span>
                        </v-layout>
                        <v-layout wrap>
                          <v-flex md12> </v-flex>
                          <v-flex md6 class="pr-1">
                            <v-text-field
                              v-model="
                                standbyDurationInputs[durationIndex]
                                  .arrivalTimeInput
                              "
                              :disabled="readOnly"
                              :rules="[validationRules.twentyFourHourTime]"
                              hint="24-hour time"
                              mask="##:##"
                              persistent-hint
                              box
                              :label="`Arrival (${duration.arrivalDate})`"
                              @change="
                                updateStandbyDuration(
                                  $event,
                                  durationIndex,
                                  true,
                                )
                              "
                              @focus="$event.target.select()"
                            />
                          </v-flex>
                          <v-flex md6 class="pl-1">
                            <v-text-field
                              v-model="
                                standbyDurationInputs[durationIndex]
                                  .departureTimeInput
                              "
                              :disabled="readOnly"
                              :rules="[validationRules.twentyFourHourTime]"
                              hint="24-hour time"
                              mask="##:##"
                              persistent-hint
                              box
                              @change="
                                updateStandbyDuration(
                                  $event,
                                  durationIndex,
                                  false,
                                )
                              "
                              :label="`Departure (${duration.departureDate})`"
                              @focus="$event.target.select()"
                            />
                          </v-flex>
                          <v-flex
                            md12
                            v-if="
                              durationIndex !== standbyDurationInputs.length - 1
                            "
                          >
                            <v-divider class="my-1" />
                          </v-flex>
                        </v-layout>
                      </v-flex>
                    </v-layout>
                    <v-divider class="mt-2"></v-divider>
                    <v-layout align-center>
                      <v-spacer></v-spacer>
                      <v-btn
                        depressed
                        color="blue"
                        @click="standbyDialogController = false"
                        >Finish</v-btn
                      >
                    </v-layout>
                  </v-card>
                </v-dialog>
              </v-layout>
              <span class="lineitem__value">
                ${{ DisplayCurrencyValue(standbyCharge) }}
              </span>
            </v-layout>
            <v-layout v-if="standbyRatesApply">
              <v-divider class="mt-2"></v-divider>
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </section>
</template>

<script setup lang="ts">
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import {
  nearestFutureMinutes,
  nearestMinutes,
  returnDurationFromMilliseconds,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { returnCorrectDuration } from '@/helpers/DateTimeHelpers/DurationHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { computeStandbyBreakOverlap } from '@/helpers/RateHelpers/StandbyHelpers';
import { timeRateDescription } from '@/helpers/RateHelpers/TimeRateHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import WorkDiarySummary from '@/interface-models/Driver/WorkDiary/WorkDiarySummary';
import ChargeTypeSubtotal from '@/interface-models/Generic/Accounting/JobAccountingTotals/ChargeTypeSubtotal';
import JobPrimaryRate from '@/interface-models/Generic/Accounting/JobPrimaryRate';
import { TimeTypeJobRateData } from '@/interface-models/Generic/Accounting/JobRateData/TimeTypeJobRateData';
import PudDuration, {
  PudDurationInput,
} from '@/interface-models/Generic/Accounting/Standby/PudDuration';
import StandbyDuration from '@/interface-models/Generic/Accounting/Standby/StandbyDuration';
import { EditHistory } from '@/interface-models/Generic/EditHistory/EditHistory';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import {
  graceTypes,
  GraceTypes,
} from '@/interface-models/Generic/ServiceTypes/GraceTypes';
import {
  rateMultipliers,
  RateMultipliers,
} from '@/interface-models/Generic/ServiceTypes/RateMultipliers';
import JobStatusUpdate from '@/interface-models/Jobs/Event/JobStatusUpdate';
import JobDurationData from '@/interface-models/Jobs/FinishedJobDetails/JobDurationData';
import LegDuration from '@/interface-models/Jobs/LegDuration';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { ClientServiceRateVariations } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariations';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import TimeRateType from '@/interface-models/ServiceRates/ServiceTypes/TimeServiceRate/TimeRateType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { sessionManager } from '@/store/session/SessionState';
import moment from 'moment-timezone';
import { computed, ComputedRef, onMounted, ref, Ref, watch } from 'vue';

const emit = defineEmits<{
  (event: 'setClientMinimumDurationMet', payload: boolean): void;
  (event: 'setDriverMinimumDurationMet', payload: boolean): void;
  (event: 'setViewingBreakDurationDialog', payload: boolean): void;
  (event: 'refreshAccountingTotals'): void;
  (event: 'copyTimeRateDataFromOther', payload: boolean): void;
}>();

const props = withDefaults(
  defineProps<{
    type: RateEntityType;
    freightCharges: ChargeTypeSubtotal;
    readOnly?: boolean;
    rateDetails: JobPrimaryRate;
    legDurations: LegDuration;
    readOnlyStartTime?: number;
    readOnlyEndTime?: number;
    durationData: JobDurationData;
    standbyCharge: number;
    pudItems: PUDItem[];
    standbyDurations: StandbyDuration;
    isEquipmentHire?: boolean;
    jobEventList: JobStatusUpdate[];
    enableTimesCopy?: boolean;
    workDiaryList?: WorkDiarySummary[];
    rateVariation?: ClientServiceRateVariations | undefined;
  }>(),
  {
    readOnly: false,
    enableTimesCopy: false,
    readOnlyStartTime: 0,
    readOnlyEndTime: 0,
    rateVariation: undefined,
    isEquipmentHire: false,
    workDiaryList: () => [],
  },
);

defineExpose({
  pushTimesFromParent,
});

const companyDetailsStore = useCompanyDetailsStore();

const startTimeInput: Ref<string> = ref('');
const startDateInput: Ref<string> = ref('');
const finishTimeInput: Ref<string> = ref('');
const finishDateInput: Ref<string> = ref('');

const minimumDurationMet: Ref<boolean> = ref(false);

const standbyDurationInputs: Ref<PudDurationInput[]> = ref([]);
const showStandbyDialog: Ref<boolean> = ref(false);

/**
 * Controls the standby dialog open/close state and sets standby duration inputs.
 */
const standbyDialogController = computed<boolean>({
  get() {
    return showStandbyDialog.value;
  },
  set(value: boolean) {
    if (value) {
      setStandbyDurationInputs();
    } else {
      standbyDurationInputs.value = [];
    }
    showStandbyDialog.value = value;
  },
});

/**
 * Returns a map of each pudId and its respective break overlap.
 */
const standbyOverlapsByPud: ComputedRef<Map<string, number> | null> = computed(
  () => {
    if (
      !standbyDialogController.value ||
      !props.standbyDurations ||
      !props.standbyDurations.durations ||
      !props.workDiaryList
    ) {
      return null;
    }
    const overlapMap: Map<string, number> = new Map<string, number>();
    props.standbyDurations.durations.forEach((standby) => {
      let totalDur = 0;
      props.workDiaryList.forEach((w) => {
        if (props.type === RateEntityType.CLIENT && !w.chargeClient) {
          totalDur += w.returnOverlapMillis(
            standby.arrivalTime,
            standby.departureTime,
          );
        }
        if (props.type === RateEntityType.FLEET_ASSET && !w.payFleetAsset) {
          totalDur += w.returnOverlapMillis(
            standby.arrivalTime,
            standby.departureTime,
          );
        }
      });
      overlapMap.set(standby.pudId, totalDur);
    });
    return overlapMap;
  },
);

/**
 * Returns true if standby rates apply.
 */
const standbyRatesApply: ComputedRef<boolean> = computed(() => {
  return (
    (props.rateDetails.rate.rateTypeObject as TimeRateType).standbyRate > 0 &&
    isStandbyRate.value
  );
});

/**
 * Returns the correct freight charge for the client or fleet asset.
 */
const freightCharge: ComputedRef<number | null> = computed(() => {
  if (!props.freightCharges) {
    return null;
  }
  return props.type === RateEntityType.CLIENT
    ? props.freightCharges.client
    : props.freightCharges.fleetAsset;
});

/**
 * Returns true if any PUD item is a standby rate.
 */
const isStandbyRate: ComputedRef<boolean> = computed(() => {
  if (!props.pudItems) {
    return false;
  }
  return props.pudItems.some((p) => p.isStandbyRate);
});

/**
 * Returns true if standby fuel surcharge applies.
 */
const standbyFuelSurchargeApplies: ComputedRef<boolean> = computed(() => {
  return (props.rateDetails.rate.rateTypeObject as TimeRateType)
    .standbyFuelSurchargeApplies;
});

/**
 * Returns the formatted first leg duration.
 */
const firstLegDuration: ComputedRef<string | undefined> = computed(() => {
  if (!props.rateDetails) {
    return;
  }
  const time: TimeRateType = props.rateDetails.rate
    .rateTypeObject as TimeRateType;
  let duration: number = 0;
  switch (time.firstLegTypeId) {
    case 2:
      duration = props.legDurations.depotToFirstLeg;
      break;
  }
  return returnCorrectDuration(duration);
});

/**
 * Returns the formatted last leg duration.
 */
const lastLegDuration: ComputedRef<string | undefined> = computed(() => {
  if (!props.rateDetails) {
    return;
  }
  const time: TimeRateType = props.rateDetails.rate
    .rateTypeObject as TimeRateType;
  let duration: number = 0;
  switch (time.lastLegTypeId) {
    case 2:
      duration = props.legDurations.depotToLastLeg;
      break;
    case 4:
      duration = props.legDurations.returnToFirstPud;
      break;
  }
  return returnCorrectDuration(duration);
});

/**
 * Returns the charge multiplier id.
 */
const chargeMultiplierId: ComputedRef<number | null> = computed(() => {
  if (props.rateDetails) {
    return (props.rateDetails.rate.rateTypeObject as TimeRateType)
      .chargeIncrementMultiplier;
  }
  return null;
});

/**
 * Returns the standby charge multiplier id.
 */
const standbyChargeMultiplierId: ComputedRef<number | null> = computed(() => {
  if (props.rateDetails) {
    return (props.rateDetails.rate.rateTypeObject as TimeRateType)
      .standbyIncrementMultiplier;
  }
  return null;
});

/**
 * Returns the min charge multiplier id.
 */
const minChargeMultiplierId: ComputedRef<number | null> = computed(() => {
  if (props.rateDetails && props.rateDetails.rate.rateTypeId === 1) {
    return (props.rateDetails.rate.rateTypeObject as TimeRateType)
      .minChargeMultiplier;
  }
  return null;
});

/**
 * Returns the min charge multiplier object.
 */
const minChargeMultiplier = computed(() => {
  if (props.rateDetails && chargeMultiplierId.value !== null) {
    return (
      rateMultipliers.find(
        (rateMultiplier: RateMultipliers) =>
          rateMultiplier.id === minChargeMultiplierId.value,
      ) ?? null
    );
  }
  return null;
});

/**
 * Returns the charge multiplier object.
 */
const chargeMultiplier = computed(() => {
  if (chargeMultiplierId.value !== null) {
    return (
      rateMultipliers.find(
        (rateMultiplier: RateMultipliers) =>
          rateMultiplier.id === chargeMultiplierId.value,
      ) ?? null
    );
  }
  return null;
});

/**
 * Returns the standby charge multiplier object.
 */
const standbyChargeMultiplier = computed(() => {
  if (standbyChargeMultiplierId.value !== null) {
    return (
      rateMultipliers.find(
        (rateMultiplier: RateMultipliers) =>
          rateMultiplier.id === standbyChargeMultiplierId.value,
      ) ?? null
    );
  }
  return null;
});

/**
 * Returns the grace type id.
 */
const graceTypeId: ComputedRef<number | null> = computed(() => {
  if (props.rateDetails) {
    return (props.rateDetails.rate.rateTypeObject as TimeRateType).graceType;
  }
  return null;
});

/**
 * Returns the standby grace type id.
 */
const standbyGraceTypeId: ComputedRef<number | null> = computed(() => {
  if (!props.rateDetails) {
    return null;
  }
  return (props.rateDetails.rate.rateTypeObject as TimeRateType)
    .standbyGraceType;
});

/**
 * Returns the grace type name.
 */
const graceTypeName: ComputedRef<string | null> = computed(() => {
  if (graceTypeId.value === null) {
    return null;
  }
  const graceType = graceTypes.find(
    (x: GraceTypes) => x.id === graceTypeId.value,
  );
  return graceType ? graceType.longName : null;
});

/**
 * Returns the standby grace type name.
 */
const standbyGraceTypeName: ComputedRef<string | null> = computed(() => {
  if (standbyGraceTypeId.value === null) {
    return null;
  }
  const graceType = graceTypes.find(
    (x: GraceTypes) => x.id === standbyGraceTypeId.value,
  );
  return graceType ? graceType.longName : null;
});

/**
 * Returns the charge rate.
 */
const chargeRate = computed(() => {
  if (props.rateDetails) {
    return (props.rateDetails.rate.rateTypeObject as TimeRateType)
      .chargeIncrement;
  }
  return null;
});

/**
 * Returns the standby charge rate.
 */
const standbyChargeRate = computed(() => {
  if (props.rateDetails) {
    return (props.rateDetails.rate.rateTypeObject as TimeRateType)
      .standbyIncrement;
  }
  return null;
});

/**
 * Returns the min charge time.
 */
const minChargeTime = computed(() => {
  if (props.rateDetails) {
    return (props.rateDetails.rate.rateTypeObject as TimeRateType).minCharge;
  }
  return null;
});

/**
 * Returns the rounding value.
 */
const roundingValue = computed(() => {
  if (
    chargeMultiplier.value !== null &&
    chargeMultiplier.value !== undefined &&
    chargeRate.value !== null
  ) {
    return (chargeMultiplier.value.multiplier * chargeRate.value) / 60000;
  }
  return null;
});

/**
 * Returns the standby rounding value.
 */
const standbyRoundingValue = computed(() => {
  if (
    standbyChargeMultiplier.value !== null &&
    standbyChargeMultiplier.value !== undefined &&
    standbyChargeRate.value !== null
  ) {
    return (
      (standbyChargeMultiplier.value.multiplier * standbyChargeRate.value) /
      60000
    );
  }
  return null;
});

/**
 * Returns a list of all unique dates in standby duration inputs.
 */
const standbyDates: ComputedRef<string[]> = computed(() => {
  if (!standbyDurationInputs.value || !standbyDurationInputs.value.length) {
    return [];
  }
  return [
    ...new Set(
      standbyDurationInputs.value
        .map((s) => [s.arrivalDate, s.departureDate])
        .flat(),
    ),
  ];
});

/**
 * Toggles the standby dialog open/close.
 */
function setStandbyDialog(): void {
  standbyDialogController.value = !standbyDialogController.value;
}

/**
 * Apply grace type to the jobs original standby durations and set state.
 */
function setGraceTypeOnOriginalStandbyDurations(
  refreshParent: boolean = true,
): void {
  props.standbyDurations.totalEditedDuration = 0;
  if (!props.standbyDurations.durations) {
    props.standbyDurations.durations = [];
  }
  for (const duration of props.standbyDurations.durations) {
    duration.arrivalTime = applyStandbyGraceAdjustment(
      standbyGraceTypeName.value,
      standbyRoundingValue.value,
      duration.arrivalTime,
    );
    duration.departureTime = applyStandbyGraceAdjustment(
      standbyGraceTypeName.value,
      standbyRoundingValue.value,
      duration.departureTime,
    );
    const durationWithGraceApplied = standbyRatesApply.value
      ? Math.max(0, duration.departureTime - duration.arrivalTime)
      : 0;
    props.standbyDurations.totalEditedDuration += durationWithGraceApplied;
    props.standbyDurations.totalEditedDuration -=
      duration.breakOverlapDurationInMilliseconds
        ? duration.breakOverlapDurationInMilliseconds
        : 0;
    duration.editedDuration = applyStandbyGraceAdjustment(
      standbyGraceTypeName.value,
      standbyRoundingValue.value,
      durationWithGraceApplied,
    );
  }
  props.standbyDurations.totalEditedDuration = applyStandbyGraceAdjustment(
    standbyGraceTypeName.value,
    standbyRoundingValue.value,
    props.standbyDurations.totalEditedDuration,
  );
  if (refreshParent) {
    refreshTotalsInParent();
  }
}

/**
 * Update PudDuration[] with edited values from inputs.
 * The inputted value is in 24hr time so we must convert this to epoch before calculating durations.
 */
function updateStandbyDuration(
  value: string,
  index: number,
  isArrival: boolean,
): void {
  const standbyDuration = props.standbyDurations.durations[index];
  const date = moment(standbyDuration.arrivalTime)
    .tz(userTimeZone.value)
    .format('DDMMYYYY');
  const epochDateToSet = moment
    .tz(date + ' ' + value, 'DDMMYYYY HHmm', userTimeZone.value)
    .valueOf();
  if (isArrival) {
    standbyDuration.arrivalTime = epochDateToSet;
  } else {
    standbyDuration.departureTime = epochDateToSet;
  }
  standbyDuration.breakOverlapDurationInMilliseconds =
    computeStandbyBreakOverlap(
      standbyDuration,
      props.workDiaryList,
      props.type === RateEntityType.CLIENT
        ? RateEntityType.CLIENT
        : RateEntityType.FLEET_ASSET,
    );
  standbyDuration.editedDuration = Math.max(
    0,
    standbyDuration.departureTime - standbyDuration.arrivalTime,
  );
  props.standbyDurations.totalEditedDuration = 0;
  for (const duration of props.standbyDurations.durations) {
    props.standbyDurations.totalEditedDuration += duration.editedDuration;
    props.standbyDurations.totalEditedDuration -=
      duration.breakOverlapDurationInMilliseconds
        ? duration.breakOverlapDurationInMilliseconds
        : 0;
  }
  props.standbyDurations.totalEditedDuration = applyStandbyGraceAdjustment(
    standbyGraceTypeName.value,
    standbyRoundingValue.value,
    props.standbyDurations.totalEditedDuration,
  );
  refreshTotalsInParent();
}

/**
 * Sets the inputs to the correct minutes from milliseconds defined in our PudDuration[].
 */
function setStandbyDurationInputs(): void {
  standbyDurationInputs.value = props.standbyDurations.durations.map(
    (x: PudDuration) => {
      const arrivalGraceTypeApplied: number = applyStandbyGraceAdjustment(
        standbyGraceTypeName.value,
        standbyRoundingValue.value,
        x.arrivalTime,
      );
      const departureGraceTypeApplied: number = applyStandbyGraceAdjustment(
        standbyGraceTypeName.value,
        standbyRoundingValue.value,
        x.departureTime,
      );
      const pudId = x.pudId;
      const originalDuration = x.originalDuration;
      const breakOverlapDurationInMilliseconds =
        x.breakOverlapDurationInMilliseconds;
      const editedDuration: number = moment
        .duration(
          Math.max(0, departureGraceTypeApplied - arrivalGraceTypeApplied),
        )
        .asMinutes();
      const arrivalTimeInput = moment(arrivalGraceTypeApplied)
        .tz(companyDetailsStore.userLocale)
        .format('HH:mm');
      const arrivalDate = moment(arrivalGraceTypeApplied)
        .tz(companyDetailsStore.userLocale)
        .format('DD/MM/YY');
      const departureTimeInput = moment(departureGraceTypeApplied)
        .tz(companyDetailsStore.userLocale)
        .format('HH:mm');
      const departureDate = moment(departureGraceTypeApplied)
        .tz(companyDetailsStore.userLocale)
        .format('DD/MM/YY');
      return {
        pudId,
        originalDuration,
        editedDuration,
        arrivalTime: x.arrivalTime,
        arrivalTimeInput,
        arrivalDate,
        departureTime: x.departureTime,
        departureTimeInput,
        departureDate,
        breakOverlapDurationInMilliseconds,
      };
    },
  );
}

/**
 * Returns the total duration of the job as a formatted string.
 */
const totalDurationOfJob: ComputedRef<string> = computed(() => {
  const durationInMilliseconds =
    props.durationData.actualBilledDuration +
    props.durationData.actualStandbyDuration;
  return returnCorrectDuration(durationInMilliseconds);
});

/**
 * Applies grace adjustment to a given time in milliseconds.
 */
function applyStandbyGraceAdjustment(
  graceTypeName: string | null,
  interval: number | null,
  milliseconds: number,
): number {
  if (!graceTypeName || !interval) {
    return milliseconds;
  }
  const millisecondsAsMoment = moment(milliseconds);
  let appliedGraceTime: number = 0;
  switch (graceTypeName) {
    case 'Round Up':
      appliedGraceTime = nearestFutureMinutes(
        interval,
        millisecondsAsMoment,
      ).valueOf();
      break;
    case 'Round Down':
      appliedGraceTime = nearestFutureMinutes(
        interval,
        millisecondsAsMoment,
      ).valueOf();
      break;
    case 'Nearest':
      appliedGraceTime = nearestMinutes(
        interval,
        millisecondsAsMoment,
      ).valueOf();
      break;
  }
  return appliedGraceTime;
}

/**
 * Returns the suburb name for a given PUD ID.
 */
function getSuburbNameByPudId(pudId: string): string {
  const pudDetails: PUDItem | undefined = props.pudItems.find(
    (x: PUDItem) => x.pudId === pudId,
  );
  let legNumber: number = props.pudItems.findIndex(
    (x: PUDItem) => x.pudId === pudId,
  );
  legNumber = legNumber !== -1 ? legNumber + 1 : 0;
  return pudDetails
    ? pudDetails.address.suburb.toLowerCase().charAt(0).toUpperCase() +
        pudDetails.address.suburb.slice(1).toLowerCase() +
        ' (' +
        'Leg: ' +
        legNumber +
        ')'
    : '-';
}

/**
 * Returns the break duration input value.
 */
const breakDurationInput = computed(() => {
  if (props.readOnly) {
    const duration = moment.duration(
      props.durationData.actualBreakDuration
        ? props.durationData.actualBreakDuration
        : 0,
    );
    return duration.asMinutes();
  } else {
    const breakMinutes = moment
      .duration(props.rateDetails.breakDuration.currentDuration)
      .asMinutes();
    return breakMinutes ? Math.round(breakMinutes) : 0;
  }
});

/**
 * Emit to parent to open the break duration dialog.
 */
function openBreakDurationDialog(): void {
  emit('setViewingBreakDurationDialog', true);
}

/**
 * Computes whether the minimum job duration is met.
 */
function computeMinimumJobDurationMet(): void {
  if (minChargeMultiplier.value === null || minChargeTime.value === null) {
    return;
  }
  const clientEpochMinCharge =
    minChargeMultiplier.value.multiplier * minChargeTime.value;
  const clientData: TimeTypeJobRateData[] = props.rateDetails.rateData;
  const clientStart = props.rateDetails.rateData[0].mostRecentBegin;
  const clientEnd =
    props.rateDetails.rateData[props.rateDetails.rateData.length - 1]
      .finishTime;
  const foundClientBreakRate: TimeTypeJobRateData | undefined = clientData.find(
    (rate: TimeTypeJobRateData) => rate.legTypeFlag === 'B',
  );
  let clientBreakDurationMilliseconds = 0;
  if (foundClientBreakRate) {
    const clientBreakLoadTime = foundClientBreakRate.mostRecentLoading;
    clientBreakDurationMilliseconds = clientBreakLoadTime;
  }
  const clientJobDuration =
    clientEnd - clientStart - clientBreakDurationMilliseconds;
  minimumDurationMet.value = clientJobDuration >= clientEpochMinCharge;
}

/**
 * Watch for changes in minimumDurationMet and emit to parent.
 */
watch(minimumDurationMet, (value: boolean) => {
  if (props.type === RateEntityType.CLIENT) {
    emit('setClientMinimumDurationMet', value);
  } else {
    emit('setDriverMinimumDurationMet', value);
  }
});

/**
 * Adjusts rate data for grace type.
 */
function rateGraceTypeAdjustments(rateData: TimeTypeJobRateData[]): void {
  const startTime = rateData[0].mostRecentBegin;
  const mostRecentBegin = JSON.parse(
    JSON.stringify(rateData[0].mostRecentBegin),
  );
  rateData[0].beginEditList[rateData[0].beginEditList.length - 1].newValue =
    applyStandbyGraceAdjustment(
      graceTypeName.value,
      roundingValue.value,
      startTime,
    );
  const compareMostRecentBegin = rateData[0].mostRecentBegin;
  if (mostRecentBegin !== compareMostRecentBegin) {
    startTimeInput.value = moment(compareMostRecentBegin).format('HHmm');
  }
  const endData = rateData[rateData.length - 1];
  const endTime = endData.mostRecentBegin;
  const endEditList =
    rateData[rateData.length - 1].beginEditList[
      rateData[rateData.length - 1].beginEditList.length - 1
    ];
  if (!endEditList) {
    rateData[rateData.length - 1].beginEditList.push(new EditHistory());
  }
  rateData[rateData.length - 1].beginEditList[
    rateData[rateData.length - 1].beginEditList.length - 1
  ].newValue = applyStandbyGraceAdjustment(
    graceTypeName.value,
    roundingValue.value,
    endTime,
  );
}

/**
 * Returns the active user.
 */
const activeUser = computed(() => sessionManager.getActiveUser());
/**
 * Returns the user's timezone.
 */
const userTimeZone = computed(() => companyDetailsStore.userLocale);

/**
 * Edits the time for a given rate data index, date, and time.
 */
function editTime(dataIndex: number, date: string, time: string): void {
  const timeData = props.rateDetails.rateData[dataIndex];
  const newEdit = new EditHistory();
  newEdit.user = activeUser.value;
  if (timeData.beginEditList.length < 1) {
    newEdit.oldValue = props.rateDetails.rateData[dataIndex].begin;
  } else {
    newEdit.oldValue =
      timeData.beginEditList[timeData.beginEditList.length - 1].newValue;
  }
  newEdit.newValue = moment
    .tz(date + ' ' + time, 'DDMMYYYY HHmm', userTimeZone.value)
    .valueOf();
  timeData.beginEditList.push(newEdit);
  props.rateDetails.rateData[dataIndex] = timeData;
  rateGraceTypeAdjustments(props.rateDetails.rateData);
  setStartDateInputs();
  setFinishDateInputs();
  refreshTotalsInParent();
}

/**
 * Refreshes totals in parent and emits.
 */
function refreshTotalsInParent(): void {
  computeMinimumJobDurationMet();
  emit('refreshAccountingTotals');
}

/**
 * Sets start date/time inputs from rate data.
 */
function setStartDateInputs(): void {
  const epochStart = props.readOnly
    ? props.readOnlyStartTime
    : props.rateDetails.rateData[0].mostRecentBegin;
  startTimeInput.value = moment
    .tz(epochStart, userTimeZone.value)
    .format('HHmm');
  startDateInput.value = moment
    .tz(epochStart, userTimeZone.value)
    .format('DDMMYYYY');
}

/**
 * Sets finish date/time inputs from rate data.
 */
function setFinishDateInputs(): void {
  const beginEditListLength =
    props.rateDetails.rateData[props.rateDetails.rateData.length - 1]
      .beginEditList.length;
  const epochFinish = props.readOnly
    ? props.readOnlyEndTime
    : props.rateDetails.rateData[props.rateDetails.rateData.length - 1]
          .beginEditList.length < 1
      ? props.rateDetails.rateData[props.rateDetails.rateData.length - 1]
          .finishTime
      : props.rateDetails.rateData[props.rateDetails.rateData.length - 1]
          .beginEditList[beginEditListLength - 1].newValue;
  finishTimeInput.value = moment
    .tz(epochFinish, userTimeZone.value)
    .format('HHmm');
  finishDateInput.value = moment
    .tz(epochFinish, userTimeZone.value)
    .format('DDMMYYYY');
}

/**
 * Watch for changes in start date/time and update rate data.
 */
watch([startDateInput, startTimeInput], () => {
  validateAndAddDateTimeEdit(startDateInput.value, startTimeInput.value, 0);
});

/**
 * Watch for changes in finish date/time and update rate data.
 */
watch([finishDateInput, finishTimeInput], () => {
  validateAndAddDateTimeEdit(
    finishDateInput.value,
    finishTimeInput.value,
    props.rateDetails.rateData.length - 1,
  );
});

/**
 * Validates and adds a date/time edit if valid.
 */
function validateAndAddDateTimeEdit(
  date: string,
  time: string,
  index: number,
): void {
  if (props.readOnly) {
    return;
  }
  if (!dateAndTimeIsValid(date, time)) {
    return;
  }
  editTime(index, date, time);
}

/**
 * Emits to parent to copy times from the other entity.
 */
function copyTimeRateDataFromOther(): void {
  emit('copyTimeRateDataFromOther', props.type === RateEntityType.CLIENT);
}

/**
 * Called when copying times from Client > FA and FA > Client.
 * When copying from Client to FA, the ref for FA is used to call this method with the rate data from JobPricingManagement.
 */
function pushTimesFromParent(rateDetails: JobPrimaryRate): void {
  const rateData = rateDetails.rateData;
  const tz = userTimeZone.value;
  const epochStart = rateData[0].mostRecentBegin;
  const epochFinish = rateData[rateData.length - 1].finishTime;
  startTimeInput.value = moment.tz(epochStart, tz).format('HHmm');
  startDateInput.value = moment.tz(epochStart, tz).format('DDMMYYYY');
  validateAndAddDateTimeEdit(startDateInput.value, startTimeInput.value, 0);
  finishTimeInput.value = moment.tz(epochFinish, tz).format('HHmm');
  finishDateInput.value = moment.tz(epochFinish, tz).format('DDMMYYYY');
  validateAndAddDateTimeEdit(
    finishDateInput.value,
    finishTimeInput.value,
    rateData.length - 1,
  );
  if (isStandbyRate.value) {
    props.standbyDurations.durations = rateDetails.standbyDuration.durations;
    setGraceTypeOnOriginalStandbyDurations(false);
  }
  showNotification(
    `Copied Times: ${
      props.type === RateEntityType.CLIENT
        ? 'Fleet Asset > Client'
        : 'Client  > Fleet Asset'
    }`,
    { type: HealthLevel.INFO },
  );
}

/**
 * Checks if date and time are valid.
 */
function dateAndTimeIsValid(date: string, time: string): boolean {
  const patternTime = /^([01]\d|2[0-3])([0-5]\d)$/;
  const patternDate =
    /^((((0[1-9]|[1-2][0-9]|3[0-1])(0[13578]|10|12))|((0[1-9]|[1-2][0-9])(02))|(((0[1-9])|([1-2][0-9])|(30))(0[469]|11)))((19\d{2})|(2[012]\d{2})))$/;
  return patternTime.test(time) && patternDate.test(date);
}

/**
 * Returns a human-readable breakdown of the time rate.
 */
const easyReadBreakDown: ComputedRef<string> = computed(() => {
  const timeRate = props.rateDetails.rate.rateTypeObject as TimeRateType;
  const rateMultiplierId = timeRate.rateMultiplier;
  const variancePct =
    (props.type === RateEntityType.CLIENT
      ? props.rateVariation?.clientAdjustmentPercentage
      : props.rateVariation?.fleetAssetAdjustmentPercentage) ?? 0;
  const rate = timeRate.rate;
  if (
    rateMultiplierId === undefined ||
    props.durationData.actualBilledDuration === undefined
  ) {
    return '';
  }
  const jobDuration = returnCorrectDuration(
    props.durationData.actualBilledDuration,
  );
  const rateString = timeRateDescription({
    rate,
    multiplier: rateMultiplierId,
    variancePct,
  });
  return `${rateString} @ ${jobDuration}`;
});

/**
 * Returns a formatted string for total edited standby duration.
 */
const totalEditedStandbyDuration = computed(() => {
  const timeRate = props.rateDetails.rate.rateTypeObject as TimeRateType;
  const rateMultiplier = rateMultipliers.find(
    (x: RateMultipliers) => x.id === timeRate.standbyMultiplier,
  );
  const variancePct =
    (props.type === RateEntityType.CLIENT
      ? props.rateVariation?.clientAdjustmentPercentage
      : props.rateVariation?.fleetAssetAdjustmentPercentage) ?? 0;
  const standbyRate = timeRate.standbyRate;
  const standbyMultiplierId = timeRate.standbyMultiplier;
  if (
    !rateMultiplier ||
    props.durationData.actualStandbyDuration === null ||
    props.durationData.actualStandbyDuration === undefined
  ) {
    return '';
  }
  const rateString = timeRateDescription({
    rate: standbyRate,
    multiplier: standbyMultiplierId,
    variancePct,
  });
  const standbyDuration = returnCorrectDuration(
    props.durationData.actualStandbyDuration,
  );
  return `${rateString} @ ${standbyDuration}`;
});

/**
 * When component mounts, set all date and time inputs from our time rate data.
 */
onMounted(() => {
  setStartDateInputs();
  setFinishDateInputs();
  setGraceTypeOnOriginalStandbyDurations();
  if (props.readOnly) {
    return;
  }
  if (props.type === RateEntityType.CLIENT) {
    emit('setClientMinimumDurationMet', minimumDurationMet.value);
  } else {
    emit('setDriverMinimumDurationMet', minimumDurationMet.value);
  }
});
</script>
<style scoped lang="scss">
.route-time-management {
  height: 100%;
  position: relative;

  .button-left {
    position: absolute;
    left: -3px;
  }

  .break-edit {
    &:hover {
      cursor: pointer;
    }
  }

  .edit-list {
    .edit-list__sheet {
      background-color: var(--background-color-300);
    }

    &__revert {
      font-size: 0.75em;
      font-weight: 600;
      text-transform: uppercase;
      margin-top: 4px;
      margin-right: 3px;
      cursor: pointer;
      letter-spacing: 0.02em;
      color: $warning;
      z-index: 3;

      &:hover {
        color: $average;
      }
    }

    .edit-list__value {
      font-size: 1.05em;
      font-weight: 400;
    }

    .edit-list__datetime {
      font-size: 0.7em;
      color: $table-overflow;
      font-weight: 500;
    }
    .edit-list__user {
      font-size: 0.85em;
      color: $text-color-dark;
      font-weight: 500;
    }
  }

  .resetButton {
    max-height: 20px;
  }

  .time-management {
    position: relative;

    .header-section {
      background-color: #393939;
      color: #e48e1d;
      font-weight: 500;
      font-size: $font-size-large;
      text-transform: uppercase;
    }

    .bottom-sheet-fixed {
      width: 100%;
      position: absolute;
      background-color: $border-dark;
      bottom: 0px;
      border-bottom-right-radius: 3px;
      border-bottom-left-radius: 3px;
    }

    .time-management__header {
      width: 100%;
      text-align: right;
      font-size: 1em;
      text-transform: uppercase;
      font-weight: 500;
    }

    .time-row__content {
      position: relative;
      width: 100%;
      // height: 56px;
    }

    .time-row__text-field {
      position: relative;
      top: 14px;
    }

    .time-row__address-container {
      width: 100%;
      padding-left: 4px;
      line-height: 1;

      .time-row__address-text {
        color: #6d6d6d;
        font-weight: 600;
        font-size: $font-size-medium;
        text-transform: uppercase;
        padding: 3px 0;
      }

      &.break-type {
        .time-row__address-text {
          color: rgb(0, 202, 141);
        }
      }
    }

    .time-row__content--text {
      font-size: 1.3em;
      font-weight: 600;
      height: 40px;
    }

    .hoverStyle {
      &:hover {
        background-color: $translucent-highlight;
        cursor: pointer;
      }
    }
  }
}

.center-copy-container {
  top: 49px;
  right: 50%;
  transform: translateX(50%);
  width: 16.666666666666664%;
  height: 35.5vh;
  position: absolute;

  z-index: 10;

  .divider {
    position: absolute;
    width: 2px;
    background-color: $secondary;
    height: 100%;
    right: 50%;
    transform: translateX(50%);
  }
}

.justify-end {
  justify-content: flex-end !important;
}

.side-panel__section {
  background-color: var(--background-color-300);
  border: 1px solid #585858;
  padding: 4px;

  .side-panel__section--header {
    font-size: 1em;
    text-transform: uppercase;
    font-weight: 500;
    color: var(--text-color);

    .side-panel__section--label {
      font-size: $font-size-small;
      text-transform: uppercase;
      font-weight: 600;
      color: $heading-text-color;
    }

    .side-panel__section--value {
      font-size: $font-size-small;
      text-transform: uppercase;
      font-weight: 400;
    }
    .side-panel__section--row {
      padding: 3px 6px;
    }
  }
}

.rate-container {
  height: 100%;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  display: flex;
  font-size: $font-size-large;
}

.rate {
  font-size: 0.95em;
  text-transform: uppercase;

  color: $pud-flag;
  line-height: 1;
  display: flex;
  font-weight: bold;
}
.rate-value {
  font-size: 1em;
  margin-left: 5px;
  text-transform: uppercase;
  color: var(--text-color);
  font-weight: 600;
  display: block;
  line-height: 1;
}

.check-active {
  color: rgb(125, 236, 208) !important;
}

.check-error {
  cursor: pointer;
}

.applied-rate-value {
  font-size: $font-size-11;
}

.expansion-panel-container {
  // border: 1px solid $translucent;
  padding: 4px;
  // border-radius: $border-radius-sm;
  .expansion-item-container {
    border-radius: $border-radius-sm !important;
    // background-color: var(--background-color-300) !important;

    .lineitem__label {
      font-size: $font-size-13;
      text-transform: uppercase;
      font-weight: 600;
      color: var(--heading-text-color);
      padding-left: 4px;
      margin-top: 4px;
    }
    .lineitem__value {
      font-size: $font-size-13;
      font-weight: 500;
      padding-right: 4px;
      margin-top: 4px;
      color: var(--text-color);

      &.error-value {
        padding: 0px 8px;
        // font-weight: 600;
        background-color: var(--error);
        border-radius: 2px;
        // letter-spacing: 0.03em;
      }
    }
  }
}

.lineitem__label {
  color: var(--light-text-color);
}
.lineitem__value {
  color: var(--text-color);
}

.status-menu {
  position: absolute;
  right: 20px;
  top: -35px;
}

.edit-break-icon {
  margin-left: 14px;
  border-radius: 100px;
  background-color: var(--background-color-100);
  color: $warning !important;
  transition: all 0.3s;

  &:hover {
    scale: 1.4;
  }
}
</style>
