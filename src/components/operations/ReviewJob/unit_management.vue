<template>
  <div class="pallet-management">
    <v-layout px-2>
      <v-spacer></v-spacer>

      <v-flex md6 style="padding-left: 12px">
        <v-layout justify-end align-center wrap v-if="unitRateData">
          <v-flex md12>
            <v-layout
              style="border-bottom: 2px solid hsla(0, 0%, 100%, 0.12)"
              class="mb-1"
            >
              <v-flex md5>
                <span
                  class="column-header"
                  style="padding-left: 3px; padding-right: 3px"
                  >Unit</span
                >
              </v-flex>
              <v-flex md3>
                <span class="column-header">Rate</span>
              </v-flex>
              <v-flex md4>
                <v-layout justify-end>
                  <span class="column-header">Amount</span>
                </v-layout>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12 v-if="type === RateEntityType.CLIENT">
            <v-layout row wrap v-for="zone of tableData" :key="zone.pudId">
              <v-tooltip left>
                <template v-slot:activator="{ on }">
                  <v-flex md12 v-on="on" style="cursor: pointer">
                    <v-layout wrap>
                      <v-flex md12 v-if="!zone.isPickup">
                        <v-layout
                          justify-space-between
                          align-center
                          wrap
                          class="line-item"
                        >
                          <v-flex md5>
                            {{ zone.unitTypeName }} - {{ zone.pud.unit }}
                          </v-flex>

                          <v-flex md3> ${{ zone.pud.rate }} </v-flex>

                          <v-flex md4>
                            <v-layout justify-end>
                              ${{ zone.pud.amount }}
                            </v-layout>
                          </v-flex>
                        </v-layout>
                      </v-flex>
                      <v-flex md12>
                        <v-layout
                          justify-space-between
                          align-center
                          wrap
                          class="line-item"
                        >
                          <v-flex md5> {{ zone.load.unit }} </v-flex>
                          <v-flex md3> ${{ zone.load.rate }} </v-flex>
                          <v-flex md4>
                            <v-layout justify-end>
                              ${{ zone.load.amount }}
                            </v-layout>
                          </v-flex>
                        </v-layout>
                      </v-flex>
                      <v-flex md12 v-if="zone.isDangerousGoods">
                        <v-layout
                          justify-space-between
                          align-center
                          wrap
                          class="line-item"
                        >
                          <v-flex md5> {{ zone.dangerousGoods.unit }} </v-flex>
                          <v-flex md3> ${{ zone.dangerousGoods.rate }} </v-flex>
                          <v-flex md4>
                            <v-layout justify-end>
                              ${{ zone.dangerousGoods.amount }}
                            </v-layout>
                          </v-flex>
                        </v-layout>
                      </v-flex>

                      <v-flex md12 v-if="zone.demurrage.rate !== '-'">
                        <v-layout
                          justify-space-between
                          align-center
                          wrap
                          class="line-item"
                        >
                          <v-flex md5>
                            <v-layout>
                              {{ zone.demurrage.unit }}
                              <v-tooltip
                                bottom
                                v-if="zone.demurrage.fuelSurchargeApplies"
                              >
                                <template v-slot:activator="{ on: onTooltip }">
                                  <v-icon
                                    size="6"
                                    color="info"
                                    v-on="onTooltip"
                                    style="
                                      cursor: pointer;
                                      padding-left: 2px;
                                      padding-top: 2px;
                                    "
                                  >
                                    fas fa-asterisk
                                  </v-icon>
                                </template>

                                <span>Fuel Surcharge Applies</span>
                              </v-tooltip>
                            </v-layout>
                          </v-flex>
                          <v-flex md3> {{ zone.demurrage.rate }} </v-flex>
                          <v-flex md4>
                            <v-layout justify-end>
                              {{ zone.demurrage.amount }}
                            </v-layout>
                          </v-flex>
                        </v-layout>
                      </v-flex>
                      <v-flex md12>
                        <v-divider class="my-2"></v-divider>
                      </v-flex>
                    </v-layout>
                  </v-flex>
                </template>
                <span>{{ zoneName(zone.zoneId) }} - {{ zone.suburb }}</span>
              </v-tooltip>
            </v-layout>
            <v-layout wrap justify-space-between align-center>
              <v-flex md5 class="main-line-item"> Freight Total: </v-flex>
              <v-flex md3></v-flex>
              <v-layout md4 justify-end class="value-txt">
                ${{ DisplayCurrencyValue(unitRateData.chargesTotalExclGst) }}
              </v-layout>
              <v-flex md12>
                <v-divider class="my-2"></v-divider>
              </v-flex>
            </v-layout>
            <v-layout wrap justify-space-between align-center>
              <v-flex md5 class="main-line-item">
                <v-layout>
                  Demurrage Total:
                  <DemurrageDuration
                    style="padding-top: 2px"
                    :unitRates="unitRates"
                    :demurrageRateData="demurrageRateData"
                    @refreshAccountingTotals="refreshAccountingTotals"
                    :pudItems="pudItems"
                    :isClient="true"
                    :readOnly="readOnlyView"
                    :workDiaryList="workDiaryList"
                    :fleetDemurrageRateData="
                      accounting.finishedJobData.fleetAssetDemurrageBreakdown
                    "
                  />
                </v-layout>
              </v-flex>
              <v-flex md3> </v-flex>
              <v-flex md4>
                <v-layout justify-end style="padding-right: 3px">
                  <span> {{ demurrageTotalExclGst }}</span>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-divider class="mt-2"></v-divider>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12 v-if="type === RateEntityType.FLEET_ASSET">
            <v-layout row wrap>
              <v-flex md12>
                <v-layout
                  justify-space-between
                  align-center
                  wrap
                  class="line-item"
                >
                  <v-flex md5>
                    ${{
                      DisplayCurrencyValue(unitRateData.chargesTotalExclGst)
                    }}
                  </v-flex>

                  <v-flex md3>
                    <v-layout
                      align-center
                      v-if="
                        isUnitRateTypeObject(
                          rates[0].rate.rateTypeId,
                          rates[0].rate.rateTypeObject,
                        )
                      "
                      >{{
                        rates[0].rate.rateTypeObject[0].fleetAssetPercentage
                      }}%
                      <v-icon
                        size="12"
                        style="position: relative; top: -1px"
                        class="pl-1"
                        v-if="!readOnlyView"
                        :disabled="readOnlyView"
                        @click="showUnitRatePercentageDialog = true"
                        >fal fa-edit</v-icon
                      >
                      <v-dialog
                        v-if="showUnitRatePercentageDialog"
                        v-model="showUnitRatePercentageDialog"
                        :width="350"
                        class="ma-0"
                        persistent
                      >
                        <v-card color="#242329">
                          <v-layout
                            justify-space-between
                            align-center
                            class="task-bar app-theme__center-content--header no-highlight"
                          >
                            <span>Unit Rate Percentage edit</span>
                            <v-icon
                              size="14"
                              @click="showUnitRatePercentageDialog = false"
                              >fal fa-times</v-icon
                            >
                          </v-layout>
                          <v-layout wrap class="pa-2">
                            <v-flex md12>
                              <v-form ref="fleetAssetPercentageForm">
                                <v-text-field
                                  type="number"
                                  :rules="[validate.required]"
                                  box
                                  v-model.number="editedUnitRatePercentage"
                                  persistent-hint
                                  hint="The unit rate percentage that will be applied to the fleet asset."
                                  label="Unit Rate Percentage"
                                />
                              </v-form>
                            </v-flex>
                          </v-layout>
                          <v-layout class="px-2 pb-2" justify-space-between>
                            <v-btn
                              class="ma-0"
                              outline
                              color="error"
                              small
                              @click="cancelFleetAssetPercentageEdit"
                            >
                              cancel</v-btn
                            >
                            <v-btn
                              class="ma-0"
                              color="blue"
                              small
                              @click="confirmAndSetUnitRatePercentage"
                            >
                              confirm</v-btn
                            >
                          </v-layout>
                        </v-card>
                      </v-dialog>
                    </v-layout>
                  </v-flex>
                  <v-flex md4>
                    <v-layout justify-end>
                      ${{
                        DisplayCurrencyValue(unitRateData.chargesTotalExclGst)
                      }}
                    </v-layout>
                  </v-flex>
                </v-layout>

                <v-layout
                  justify-space-between
                  align-center
                  wrap
                  v-for="(pud, pudIndex) of fleetAssetDemurrageLineItems"
                  :key="pudIndex"
                  class="line-item"
                >
                  <v-flex md5>
                    <v-layout>
                      {{ pud.unit }}
                      <v-tooltip
                        bottom
                        v-if="pud.demurrageFuelSurchargeApplies"
                      >
                        <template v-slot:activator="{ on }">
                          <v-icon
                            size="6"
                            color="info"
                            v-on="on"
                            style="
                              cursor: pointer;
                              padding-left: 2px;
                              padding-top: 2px;
                            "
                          >
                            fas fa-asterisk
                          </v-icon>
                        </template>

                        <span>Fuel Surcharge Applies</span>
                      </v-tooltip>
                    </v-layout>
                  </v-flex>

                  <v-flex md3> {{ pud.rate }} </v-flex>

                  <v-flex md4>
                    <v-layout justify-end> ${{ pud.amount }} </v-layout>
                  </v-flex>
                </v-layout>
              </v-flex>

              <v-flex md12>
                <v-divider class="my-2"></v-divider>
              </v-flex>
            </v-layout>

            <v-layout justify-space-between align-center wrap>
              <v-flex md5 class="main-line-item">
                <v-layout>
                  Demurrage Total:
                  <DemurrageDuration
                    style="padding-top: 2px"
                    :unitRates="unitRates"
                    :demurrageRateData="demurrageRateData"
                    @refreshAccountingTotals="refreshAccountingTotals"
                    :pudItems="pudItems"
                    :readOnly="readOnlyView"
                    :isClient="false"
                    :workDiaryList="workDiaryList"
                    :clientDemurrageRateData="
                      accounting.finishedJobData.clientDemurrageBreakdown
                    "
                  />
                </v-layout>
              </v-flex>
              <v-flex md3> </v-flex>
              <v-flex md4>
                <v-layout justify-end style="padding-right: 3px">
                  <span> {{ demurrageTotalExclGst }}</span>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-divider class="my-2"></v-divider>
              </v-flex>
            </v-layout>

            <v-layout wrap justify-space-between align-center>
              <v-flex md5 class="main-line-item"> Freight Total: </v-flex>
              <v-flex md3> </v-flex>
              <v-flex md4>
                <v-layout justify-end class="value-txt">
                  ${{ DisplayCurrencyValue(unitRateData.chargesTotalExclGst) }}
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-divider class="mt-2"></v-divider>
              </v-flex>
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import DemurrageDuration from '@/components/operations/ReviewJob/demurrage_duration/demurrage_duration.vue';
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { deepCopy } from '@/helpers/JsonHelpers/JsonHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { isUnitRateTypeObject } from '@/helpers/RateHelpers/RateTableItemHelpers';
import { unitRateCalculation } from '@/helpers/RateHelpers/UnitRateHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import WorkDiarySummary from '@/interface-models/Driver/WorkDiary/WorkDiarySummary';
import { JobAccountingDetails } from '@/interface-models/Generic/Accounting/JobAccountingDetails';
import JobPrimaryRate from '@/interface-models/Generic/Accounting/JobPrimaryRate';
import ShortLongName from '@/interface-models/Generic/ShortLongName/ShortLongName';
import { Validation } from '@/interface-models/Generic/Validation';
import UnitRateData from '@/interface-models/Jobs/FinishedJobDetails/UnitRateData';
import ZonedUnitRateData from '@/interface-models/Jobs/FinishedJobDetails/ZonedUnitRateData';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { DemurrageRateData } from '@/interface-models/ServiceRates/Demurrage/DemurrageRateData';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import UnitRate from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRate';
import unitRateTypes from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRateTypes';
import moment from 'moment-timezone';
import { computed, ComputedRef, onMounted, Ref, ref } from 'vue';

const emit = defineEmits<{
  (event: 'update:isDialogOpen', payload: boolean): void;
  (event: 'refreshAccountingTotals', copySource?: RateEntityType): void;
}>();

const props = withDefaults(
  defineProps<{
    rates: JobPrimaryRate[];
    pudItems: PUDItem[];
    accounting: JobAccountingDetails;
    driverRegisteredForGst?: boolean;
    type: RateEntityType;
    readOnlyView?: boolean;
  }>(),
  {
    driverRegisteredForGst: false,
    readOnlyView: false,
  },
);

const showUnitRatePercentageDialog: Ref<boolean> = ref(false);
const originalFleetAssetPercentage: Ref<number | null> = ref(null);
const editedUnitRatePercentage: Ref<number | null> = ref(null);

const fleetAssetPercentageForm: Ref<any> = ref(null);

/**
 * Returns the pallet zone rates for the job.
 */
const unitRateData: ComputedRef<UnitRateData | undefined> = computed(() => {
  const clientCharge = unitRateCalculation(
    props.pudItems,
    props.accounting.clientRates[0].rate.rateTypeObject as UnitRate[],
    RateEntityType.CLIENT,
    null,
    null,
    props.accounting.clientServiceRateVariations?.clientAdjustmentPercentage,
  );
  if (props.type === RateEntityType.CLIENT) {
    return clientCharge;
  } else {
    return unitRateCalculation(
      props.pudItems,
      props.rates[0].rate.rateTypeObject as UnitRate[],
      RateEntityType.FLEET_ASSET,
      clientCharge?.chargesTotalExclGst ?? 0,
      props.driverRegisteredForGst,
      props.accounting.clientServiceRateVariations
        ?.fleetAssetAdjustmentPercentage,
    );
  }
});

/**
 * Returns the demurrage rate data for the job.
 */
const demurrageRateData: ComputedRef<DemurrageRateData[]> = computed(() => {
  return props.type === RateEntityType.CLIENT
    ? props.accounting.finishedJobData.clientDemurrageBreakdown
    : props.accounting.finishedJobData.fleetAssetDemurrageBreakdown;
});

/**
 * Returns the demurrage total excluding GST as a formatted string.
 */
const demurrageTotalExclGst: ComputedRef<string> = computed(() => {
  if (props.type === RateEntityType.CLIENT) {
    return (
      '$' +
      DisplayCurrencyValue(
        props.accounting.totals.subtotals.demurrageChargeTotals.client,
      )
    );
  }
  return (
    '$' +
    DisplayCurrencyValue(
      props.accounting.totals.subtotals.demurrageChargeTotals.fleetAsset,
    )
  );
});

/**
 * Returns the work diary records for the job.
 */
const workDiaryList: ComputedRef<WorkDiarySummary[]> = computed(() => {
  if (props.type === RateEntityType.CLIENT) {
    return props.accounting.clientRates[0]?.breakDuration
      ? props.accounting.clientRates[0].breakDuration.breakSummaryList
      : [];
  }
  return props.accounting.fleetAssetRates[0]?.breakDuration
    ? props.accounting.fleetAssetRates[0].breakDuration.breakSummaryList
    : [];
});

/**
 * Emits an event to refresh accounting totals.
 */
function refreshAccountingTotals(): void {
  emit('refreshAccountingTotals', props.type);
}

/**
 * Returns the table data for the unit rates.
 */
const tableData: ComputedRef<any[]> = computed(() => {
  if (!unitRateData.value) {
    return [];
  }

  return unitRateData.value.zonedUnitRateData.map((x: ZonedUnitRateData) => {
    const stop = props.pudItems.find((p: PUDItem) => p.pudId === x.pudId);
    const unitRate: UnitRate | undefined = (
      props.rates[0].rate.rateTypeObject as UnitRate[]
    ).find((u: UnitRate) => u.zoneId === x.zoneId);

    const unitName =
      unitRateTypes.find((x: ShortLongName) => unitRate?.unitTypeId === x.id)
        ?.plural || 'units';

    const fuelAppliedToZone = unitRate && unitRate.appliedFuelSurchargeId === 1;
    const fuelAppliedToFlagFall =
      unitRate && fuelAppliedToZone && unitRate.fuelIsAppliedToFlagFalls;

    const demurrage: DemurrageRateData | undefined =
      demurrageRateData.value.find(
        (d: DemurrageRateData) => d.pudId === x.pudId,
      );

    const demurrageHours: number =
      demurrage &&
      moment.duration(demurrage.demurrageDurationInMilliseconds).hours() > 0
        ? moment.duration(demurrage.demurrageDurationInMilliseconds).hours()
        : 0;

    const demurrageMinutes: number =
      demurrage &&
      moment.duration(demurrage.demurrageDurationInMilliseconds).minutes() > 0
        ? moment.duration(demurrage.demurrageDurationInMilliseconds).minutes()
        : 0;

    return {
      pudId: x.pudId,
      zoneId: x.zoneId,
      pud: {
        unit: x.quantityOfUnits + ' ' + unitName,
        rate: x.appliedRangeRate,
        amount: DisplayCurrencyValue(x.freightChargesTotalExclGst),
      },
      load: {
        unit:
          (stop && stop.rateDetails.forkLiftRequired
            ? 'Forklift Load'
            : 'Hand Load') + (fuelAppliedToFlagFall ? '*' : ''),
        rate: DisplayCurrencyValue(x.loadChargesTotalExclGst),
        amount: DisplayCurrencyValue(x.loadChargesTotalExclGst),
      },
      dangerousGoods: {
        unit: 'DG',
        rate: DisplayCurrencyValue(x.dangerousGoodsChargesTotalExclGst),
        amount: DisplayCurrencyValue(x.dangerousGoodsChargesTotalExclGst),
      },
      suburb: stop ? stop.address.suburb : '',
      unitTypeName: x.unitTypeName + (fuelAppliedToZone ? '*' : ''),
      isDangerousGoods: stop ? !!stop.rateDetails.isDangerousGoods : false,
      isPickup: stop ? stop.legTypeFlag === 'P' : false,
      demurrage: {
        unit: 'demurrage',
        rate:
          unitRate && demurrage
            ? '$' +
              DisplayCurrencyValue(demurrage.rate) +
              ' (' +
              demurrageHours +
              'h ' +
              demurrageMinutes +
              'm)'
            : '-',
        amount: demurrage
          ? '$' + DisplayCurrencyValue(demurrage.demurrageChargeExclGst)
          : '-',
        fuelSurchargeApplies: unitRate
          ? unitRate.appliedFuelSurchargeId === 1 &&
            unitRate.demurrage.demurrageFuelSurchargeApplies
          : false,
      },
    };
  });
});

/**
 * Returns the unit rates for the job.
 */
const unitRates: ComputedRef<UnitRate[]> = computed(() => {
  return props.rates[0].rate.rateTypeObject as UnitRate[];
});

/**
 * Returns the demurrage rate by zone ID and PUD ID.
 * @param zoneId - Zone identifier.
 * @param pudId - PUD identifier.
 */
function demurrageRateByZoneIdAndPudId(zoneId: number, pudId: string): string {
  let unitRate: UnitRate | undefined;
  if (props.type === RateEntityType.CLIENT) {
    unitRate = (props.rates[0].rate.rateTypeObject as UnitRate[]).find(
      (x: UnitRate) => x.zoneId === zoneId,
    );
  } else {
    unitRate = (props.rates[0].rate.rateTypeObject as UnitRate[])[0];
  }
  if (!unitRate) {
    return '-';
  }
  const demurrage: DemurrageRateData | undefined = demurrageRateData.value.find(
    (x: DemurrageRateData) => x.pudId === pudId,
  );
  if (!demurrage) {
    return '-';
  }
  return (
    '$' +
    DisplayCurrencyValue(unitRate.demurrage.rate) +
    ' (' +
    moment.duration(demurrage.demurrageDurationInMilliseconds).hours() +
    ' h ' +
    moment.duration(demurrage.demurrageDurationInMilliseconds).minutes() +
    'm)'
  );
}

/**
 * Returns the fleet asset demurrage line items.
 */
const fleetAssetDemurrageLineItems: ComputedRef<any[]> = computed(() => {
  if (props.type === RateEntityType.CLIENT) {
    return [];
  }
  return demurrageRateData.value.map((x: DemurrageRateData) => {
    const pud = props.pudItems.find(
      (pudItem: PUDItem) => pudItem.pudId === x.pudId,
    );
    const suburb = pud ? pud.address.suburb : '';
    const unit = 'Demurrage - ' + suburb;
    const rate = demurrageRateByZoneIdAndPudId(
      x.zoneId ? x.zoneId : -1,
      pud && pud.pudId ? pud.pudId : '',
    );
    const amount = DisplayCurrencyValue(x.demurrageChargeExclGst);
    return {
      unit,
      rate,
      amount,
      demurrageFuelSurchargeApplies: x.demurrageFuelSurchargeApplies,
    };
  });
});

/**
 * Returns the zone name for a given zone ID.
 * @param id - Zone identifier.
 */
function zoneName(id: number): string {
  const zoneRates = props.rates[0].rate.rateTypeObject as UnitRate[];
  for (const rate of zoneRates) {
    if (rate.zoneId === id) {
      return rate.zoneName;
    }
  }
  return '';
}

/**
 * Returns the validation rules.
 */
const validate: ComputedRef<Validation> = computed(() => validationRules);

/**
 * Confirms and sets the unit rate percentage.
 */
function confirmAndSetUnitRatePercentage(): void {
  const form = fleetAssetPercentageForm.value;
  if (!form || !form.validate()) {
    showNotification(`A percentage must be provided.`);
    return;
  }
  (props.rates[0].rate.rateTypeObject as UnitRate[])[0].fleetAssetPercentage =
    JSON.parse(JSON.stringify(editedUnitRatePercentage.value));
  showUnitRatePercentageDialog.value = false;
}

/**
 * Cancels editing of the fleet asset percentage.
 */
function cancelFleetAssetPercentageEdit(): void {
  if (
    editedUnitRatePercentage.value === null &&
    editedUnitRatePercentage.value !== 0
  ) {
    editedUnitRatePercentage.value = originalFleetAssetPercentage.value =
      JSON.parse(JSON.stringify(originalFleetAssetPercentage.value));
  }
  showUnitRatePercentageDialog.value = false;
}

/**
 * Sets original and edited fleet asset percentage values on mount.
 */
onMounted(() => {
  originalFleetAssetPercentage.value = deepCopy(
    (props.rates[0].rate.rateTypeObject as UnitRate[])[0].fleetAssetPercentage,
  );
  editedUnitRatePercentage.value = deepCopy(
    (props.rates[0].rate.rateTypeObject as UnitRate[])[0].fleetAssetPercentage,
  );
});
</script>
<style scoped lang="scss">
.rate-title {
  font-size: $font-size-small;
  text-transform: uppercase;
  color: rgb(175, 171, 171);
  font-weight: 600;
  display: block;
  line-height: 1;
}

.rate {
  font-size: 1.05em;
  text-transform: uppercase;

  color: var(--heading-text-color);
  font-weight: 600;
  line-height: 1;
  padding-right: 10px;
}

.rate-value {
  line-height: 1;
  font-weight: 600;
  letter-spacing: 1px;
  display: block;
  color: var(--text-color);
  font-size: 1.15em;
}

.rate-container {
  padding: 12px;
  position: absolute;
  bottom: 0px;
  width: 100%;
}

.column-header {
  color: var(--text-color);
  text-transform: uppercase;
  font-size: $font-size-13;
  font-weight: 600;
}

.line-item {
  color: var(--light-text-color);
  text-transform: uppercase;
  font-size: $font-size-12;
  font-weight: 600;
  padding-left: 3px;
  padding-right: 3px;
}

.main-line-item {
  font-size: $font-size-13;
  text-transform: uppercase;
  font-weight: 600;
  color: var(--light-text-color);
  padding-left: 3px;
  padding-right: 3px;
}
</style>
