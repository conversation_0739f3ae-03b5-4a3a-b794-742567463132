<template>
  <v-layout>
    <v-icon
      v-if="showActivator"
      icon
      small
      text
      class="demurrage-icon"
      @click="dialogIsOpen = true"
      >{{ !readOnly ? 'fal fa-edit' : 'fal fa-search' }}</v-icon
    >
    <!-- <v-icon size="12" v-if="readOnly" class="ml-1" @click="dialogIsOpen = true"
      >fal fa-search</v-icon
    > -->
    <v-dialog
      v-model="dialogIsOpen"
      content-class="v-dialog-custom"
      width="75%"
      persistent
    >
      <v-layout
        justify-space-between
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>Demurrage Durations</span>
        <div
          class="app-theme__center-content--closebutton"
          @click="closeDemurrageDuration"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <div class="app-theme__center-content--body dialog-content">
        <v-form ref="demurrageDurationForm">
          <v-layout wrap class="px-4 py-2">
            <h3 class="subheader--light mb-2">Stops</h3>
            <table class="simple-data-table">
              <thead>
                <tr>
                  <th>Stop</th>
                  <th>Status</th>
                  <th>Grace</th>
                  <th>Break Overlap</th>
                  <th>Arrival (24hr)</th>
                  <th>Departure (24hr)</th>
                  <th
                    v-if="
                      demurrageTimes.some(
                        (d) => d.travelDelayDurationInMins !== undefined,
                      )
                    "
                  >
                    Travel Delay Duration (mins)
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(demurrage, demurrageIndex) of demurrageTimes"
                  :key="demurrageIndex"
                >
                  <td :class="demurrage.legFlagType">
                    <h6>
                      {{ demurrage.legInfo }}
                    </h6>
                  </td>
                  <td>
                    {{ demurrage.pudStatus }}
                  </td>
                  <td>
                    {{ demurrage.graceInfo }}
                  </td>
                  <td>
                    <span
                      v-if="
                        demurrageOverlapsByPud &&
                        demurrageOverlapsByPud.get(demurrage.pudId)
                      "
                      class="break-txt"
                    >
                      * Includes
                      <span class="accent-text--primary" v-if="demurrage.pudId">
                        {{
                          returnCorrectDuration(
                            demurrageOverlapsByPud.get(demurrage.pudId) ?? 0,
                          )
                        }}
                      </span>
                      of Break
                    </span>
                    <span v-else> - </span>
                  </td>
                  <td class="input-cell">
                    <v-text-field
                      solo
                      flat
                      class="v-solo-custom"
                      persistent-hint
                      v-model="
                        demurrageTimes[demurrageIndex].startTimeInput
                          .readableTime
                      "
                      :disabled="
                        readOnly || !demurrage.startTimeInput.isEnabled
                      "
                      :rules="[
                        readOnly || !demurrage.startTimeInput.isEnabled
                          ? true
                          : validate.twentyFourHourTime,
                      ]"
                      label="Arrival"
                      mask="##:##"
                      @input="checkOverlaps"
                      :error-messages="errorMessages[demurrageIndex].start"
                      :hint="demurrage.startTimeInput.hintText"
                      @focus="$event.target.select()"
                    />
                  </td>
                  <td class="input-cell">
                    <v-text-field
                      solo
                      flat
                      class="v-solo-custom"
                      v-model="
                        demurrageTimes[demurrageIndex].endTimeInput.readableTime
                      "
                      :disabled="readOnly || !demurrage.endTimeInput.isEnabled"
                      :rules="[
                        readOnly || !demurrage.endTimeInput.isEnabled
                          ? true
                          : validate.twentyFourHourTime,
                      ]"
                      label="Departure"
                      @input="checkOverlaps"
                      mask="##:##"
                      persistent-hint
                      :error-messages="errorMessages[demurrageIndex].end"
                      :hint="demurrage.endTimeInput.hintText"
                      @focus="$event.target.select()"
                    />
                  </td>
                  <td
                    v-if="demurrage.travelDelayDurationInMins !== undefined"
                    class="input-cell"
                  >
                    <v-text-field
                      class="v-solo-custom"
                      color="light-blue"
                      solo
                      flat
                      label="Delay Duration (mins)"
                      type="number"
                      :disabled="
                        readOnly ||
                        !travelDelayEnabled ||
                        !demurrage.isTravelDelayInputEnabled
                      "
                      suffix="Min"
                      v-model.number="demurrage.travelDelayDurationInMins"
                      :rules="[validate.required, validate.number]"
                      :hint="
                        demurrage.isTravelDelayInputEnabled
                          ? 'Travel Delay (this stop to following)'
                          : 'Travel not yet commenced'
                      "
                      persistent-hint
                      @focus="$event.target.select()"
                    ></v-text-field>
                  </td>
                </tr>
              </tbody>
            </table>
          </v-layout>
          <v-layout
            row
            wrap
            v-if="!readOnly && isUnitRate && demurrageEnabled"
            class="unit-rate-container px-4"
          >
            <v-flex md6 class="pr-2">
              <h3 class="subheader--light">Client Demurrage Rate</h3>
              <v-text-field
                solo
                flat
                class="v-solo-custom pt-2"
                persistent-hint
                label="Client Demurrage Rate (per hour)"
                hint="Client Demurrage Rate (per hour)"
                :prefix="'$'"
                type="number"
                :disabled="readOnly || !demurrageEnabled"
                v-model.number="clientDemurrageUnitRate"
                :rules="[validate.required, validate.number]"
              >
              </v-text-field>
            </v-flex>
            <v-flex md6 class="pl-2">
              <h3 class="subheader--light">Fleet Demurrage Rate</h3>
              <v-text-field
                solo
                flat
                class="v-solo-custom pt-2"
                persistent-hint
                label="Fleet Demurrage Rate (per hour)"
                hint="Fleet Demurrage Rate (per hour)"
                :prefix="'$'"
                type="number"
                :disabled="readOnly || !demurrageEnabled"
                v-model.number="fleetDemurrageUnitRate"
                :rules="[validate.required, validate.number]"
              >
              </v-text-field>
            </v-flex>
          </v-layout>
          <v-divider class="mt-2" v-if="!readOnly"></v-divider>

          <v-layout v-if="!readOnly" align-center justify-end>
            <v-btn depressed color="info" @click="updateDemurrageTimes">
              Finish
            </v-btn>
          </v-layout>
        </v-form>
      </div>
    </v-dialog>
  </v-layout>
</template>

<script setup lang="ts">
import {
  returnFormattedDate,
  returnFormattedTime,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { returnCorrectDuration } from '@/helpers/DateTimeHelpers/DurationHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { computeDemurrageBreakOverlap } from '@/helpers/RateHelpers/DemurrageHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import WorkDiarySummary from '@/interface-models/Driver/WorkDiary/WorkDiarySummary';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { JobDelayType } from '@/interface-models/ServiceRates/Demurrage/applicableDemurrages';
import { DemurrageRateData } from '@/interface-models/ServiceRates/Demurrage/DemurrageRateData';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import UnitRate from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRate';
import { TravelDelayRateData } from '@/interface-models/ServiceRates/TravelDelay/TravelDelayRateData';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import moment from 'moment-timezone';
import { computed, ComputedRef, Ref, ref, WritableComputedRef } from 'vue';

interface InputProps {
  readableTime: string;
  isEnabled: boolean;
  hintText: string;
}

interface DemurrageTimes {
  pudId: string;
  legFlagType: string;
  pudStatus: string;
  legInfo: string;
  graceInfo: string;
  startTimeInput: InputProps;
  endTimeInput: InputProps;
  travelDelayDurationInMins: number | undefined;
  isTravelDelayInputEnabled: boolean;
  breakOverlapDurationInMilliseconds: number;
}

interface ErrorMessage {
  start: string;
  end: string;
}

/**
 * When isClient props is True, `demurrageRateData` is ClientDemurrages else demurrageRateData is FleetDemurrages
 * when `demurrageRateData` is ClientDemurrage, `fleetDemurrageRateData` is passed with FleetDemurrages
 * when `demurrageRateData` is FleetDemurrage, `clientDemurrageRateData` is passed with ClientDemurrages
 */
const props = withDefaults(
  defineProps<{
    demurrageRateData: DemurrageRateData[];
    pudItems: PUDItem[];
    readOnly?: boolean;
    isClient?: boolean;
    unitRates?: UnitRate[];
    workDiaryList?: WorkDiarySummary[];
    fleetDemurrageRateData?: DemurrageRateData[];
    clientDemurrageRateData?: DemurrageRateData[];
    showActivator?: boolean;

    // Travel delay
    travelDelayRateData?: TravelDelayRateData[];

    // Enabled delay types
    enabledDelayTypes?: JobDelayType[];
  }>(),
  {
    readOnly: true,
    isClient: false,
    unitRates: () => [],
    workDiaryList: () => [],
    fleetDemurrageRateData: () => [],
    clientDemurrageRateData: () => [],
    showActivator: true,
    travelDelayRateData: () => [],
    enabledDelayTypes: () => [
      JobDelayType.DEMURRAGE,
      JobDelayType.TRAVEL_DELAY,
    ],
  },
);

const emit = defineEmits<{
  (e: 'refreshAccountingTotals'): void;
}>();

const validate = validationRules;

const companyDetailsStore = useCompanyDetailsStore();

const demurrageTimes: Ref<DemurrageTimes[]> = ref([]);

const dialogIsOpened: Ref<boolean> = ref(false);
const clientDemurrageUnitRate: Ref<number> = ref(0.0);
const fleetDemurrageUnitRate: Ref<number> = ref(0.0);

const errorMessages: Ref<ErrorMessage[]> = ref([]);
const demurrageDurationForm: Ref<any> = ref(null);

const isUnitRate = computed(() => {
  return props.unitRates.length > 0;
});

const dialogIsOpen: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return dialogIsOpened.value;
  },
  set(value: boolean): void {
    dialogIsOpened.value = value;
    if (value) {
      setInitialDemurrageTimes();
      setDemurrageUnitRate();
    }
  },
});

/**
 * Opens the dialog for editing. This function is exposed such that it can be
 * called from parent components.
 */
function openDialog(): void {
  dialogIsOpen.value = true;
}

defineExpose({ openDialog });

// sync demurrage unit rate from client/fleet demurrage unit rate input in dialog
function setDemurrageUnitRate(): void {
  if (props.clientDemurrageRateData.length > 0) {
    clientDemurrageUnitRate.value = props.clientDemurrageRateData[0].rate;
    fleetDemurrageUnitRate.value = props.demurrageRateData[0].rate;
  } else if (props.fleetDemurrageRateData.length > 0) {
    fleetDemurrageUnitRate.value = props.fleetDemurrageRateData[0].rate;
    clientDemurrageUnitRate.value = props.demurrageRateData[0].rate;
  }
}

/**
 * Initializes the demurrageTimes and errorMessages arrays based on the current props.
 * This prepares the local state for editing demurrage and travel delay times.
 */
function setInitialDemurrageTimes(): void {
  // Reset error messages and demurrageTimes to avoid accumulation on repeated dialog opens
  errorMessages.value = [];
  demurrageTimes.value = [];

  // Pre-fill error messages for each PUD item
  for (let i = 0; i < props.pudItems.length; i++) {
    errorMessages.value.push({ start: '', end: '' });
  }

  demurrageTimes.value = props.pudItems.map(
    (pudItem: PUDItem, pudIndex: number) => {
      // Find demurrage data for this PUD
      const foundDemurrageRateData = props.demurrageRateData.find(
        (x: DemurrageRateData) =>
          (!!pudItem.pudId && x.pudId === pudItem.pudId) ||
          x.pudId === `${pudIndex}`,
      );

      // Prepare time input objects
      const startTimeInput: InputProps = {
        readableTime: '',
        isEnabled: true,
        hintText: '',
      };
      const endTimeInput: InputProps = {
        readableTime: '',
        isEnabled: true,
        hintText: '',
      };

      // Set time input values and hints
      if (foundDemurrageRateData) {
        startTimeInput.readableTime = returnFormattedTime(
          foundDemurrageRateData.startTimeInEpoch,
        );
        endTimeInput.readableTime = returnFormattedTime(
          foundDemurrageRateData.endTimeInEpoch,
        );

        startTimeInput.hintText = `Arrival (${returnFormattedDate(
          foundDemurrageRateData.startTimeInEpoch,
        )})`;
        endTimeInput.hintText = `Departure (${returnFormattedDate(
          foundDemurrageRateData.endTimeInEpoch,
        )})`;
      } else if (demurrageEnabled.value) {
        // If demurrageEnabled is true (pricing screen), and we didn't find
        // existing demurrage data, then it means the rate probably isn't
        // configured for demurrage
        startTimeInput.hintText = 'Demurrage not applicable';
        endTimeInput.hintText = 'Demurrage not applicable';
        startTimeInput.isEnabled = false;
        endTimeInput.isEnabled = false;
      } else {
        // If demurrageEnabled is false, it means we're in the booking screen
        // where demurrageRateData hasn't been computed use. Use estimated
        // times
        startTimeInput.readableTime = returnFormattedTime(pudItem.epochTime);
        endTimeInput.readableTime = returnFormattedTime(
          pudItem.epochTime + pudItem.loadTime,
        );
        startTimeInput.hintText = 'Estimate only';
        endTimeInput.hintText = 'Estimate only';
        startTimeInput.isEnabled = false;
        endTimeInput.isEnabled = false;
      }

      // Build leg info
      const suburb = pudItem.address?.suburb || '';
      const legInfo = `Leg ${pudIndex + 1}: ${suburb
        .charAt(0)
        .toUpperCase()}${suburb.slice(1).toLowerCase()}`;

      // Build grace info
      const demurrage = props.demurrageRateData.find(
        (x: DemurrageRateData) => x.pudId === (pudItem.pudId || `${pudIndex}`),
      );
      const graceTimeAllowed = demurrage
        ? returnCorrectDuration(demurrage.graceDurationInMilliseconds)
        : '';
      const graceInfo = graceTimeAllowed
        ? `${graceTimeAllowed} grace applied`
        : '-';

      // Travel delay setup
      let travelDelayDurationInMins: number | undefined = undefined;
      let isTravelDelayInputEnabled = false;
      const foundTravelDelayRateData = props.travelDelayRateData?.find(
        (travelRateData: TravelDelayRateData) =>
          (!!pudItem.pudId && travelRateData.pudId === pudItem.pudId) ||
          travelRateData.pudId === `${pudIndex}`,
      );
      if (foundTravelDelayRateData && foundTravelDelayRateData.rate !== 0) {
        isTravelDelayInputEnabled = pudItem.status === 'FINISHED';
        travelDelayDurationInMins = Math.round(
          (foundTravelDelayRateData.totalDurationInMilliseconds -
            foundTravelDelayRateData.graceDurationInMilliseconds) /
            60000,
        );
      }

      return {
        pudId: pudItem.pudId || `${pudIndex}`,
        pudStatus: pudItem.status || 'Not Actioned',
        legFlagType: pudItem.legTypeFlag,
        startTimeInput,
        endTimeInput,
        legInfo,
        graceInfo,
        breakOverlapDurationInMilliseconds:
          foundDemurrageRateData?.breakOverlapDurationInMilliseconds || 0,
        travelDelayDurationInMins,
        isTravelDelayInputEnabled,
      };
    },
  );

  checkOverlaps();
}

// When the dialog is open, return a map of each pudIds and its respective break overlap
const demurrageOverlapsByPud: ComputedRef<Map<string, number> | null> =
  computed(() => {
    if (
      !dialogIsOpen.value ||
      !demurrageTimes.value ||
      !props.workDiaryList ||
      !props.demurrageRateData?.length
    ) {
      return null;
    }
    const overlapMap: Map<string, number> = new Map<string, number>();
    demurrageTimes.value.forEach((demurrage, index) => {
      if (!props.demurrageRateData[index]) {
        return;
      }
      const arrivalDate: string = moment(
        props.demurrageRateData[index].startTimeInEpoch,
      )
        .tz(companyDetailsStore.userLocale)
        .format('DDMMYYYY');

      const arrivalInputDate = moment
        .tz(
          arrivalDate + ' ' + demurrage.startTimeInput.readableTime,
          'DDMMYYYY HHmm',
          companyDetailsStore.userLocale,
        )
        .valueOf();

      const departureInputDate = moment
        .tz(
          arrivalDate + ' ' + demurrage.endTimeInput.readableTime,
          'DDMMYYYY HHmm',
          companyDetailsStore.userLocale,
        )
        .valueOf();
      let totalDur = 0;
      props.workDiaryList.forEach((w) => {
        if (props.isClient && !w.chargeClient) {
          totalDur += w.returnOverlapMillis(
            arrivalInputDate,
            departureInputDate,
          );
        }
        if (!props.isClient && !w.payFleetAsset) {
          totalDur += w.returnOverlapMillis(
            arrivalInputDate,
            departureInputDate,
          );
        }
      });
      overlapMap.set(demurrage.pudId, totalDur);
    });
    return overlapMap;
  });

const overlapExists = computed(() => {
  let overlapExists: boolean = false;
  for (let i = 0; i < errorMessages.value.length; i++) {
    overlapExists =
      errorMessages.value[i].start !== '' || errorMessages.value[i].end !== '';

    if (overlapExists) {
      break;
    }
  }
  return overlapExists;
});

// method that calls our overlap validation. This will validate against all fields and is called when a time input is updated.
function checkOverlaps() {
  if (!props.demurrageRateData || props.demurrageRateData.length === 0) {
    return;
  }
  for (let i = 0; i < demurrageTimes.value.length; i++) {
    overlapValidation(
      demurrageTimes.value[i].startTimeInput.readableTime.toString(),
      i,
      true,
    );
    overlapValidation(
      demurrageTimes.value[i].endTimeInput.readableTime.toString(),
      i,
      false,
    );
  }
}

// checks if there are overlapping times entered. If there are it will set our errorMessage array.
function overlapValidation(value: string, index: number, isStartTime: boolean) {
  // if the time entered does not match 24hr time we should not check
  // overlapValidation. We also do not apply validation against the very first
  // and very last time.
  if (!value || value.length < 4 || (index === 0 && isStartTime)) {
    return;
  }
  const rateDataAtIndex = props.demurrageRateData[index];
  if (!rateDataAtIndex) {
    return;
  }

  // convert inputed date to Epoch
  const input24HrTime = value;
  const inputDateFormatted = moment(rateDataAtIndex.startTimeInEpoch)
    .tz(companyDetailsStore.userLocale)
    .format('DDMMYYYY');
  const inputDate = moment
    .tz(
      inputDateFormatted + ' ' + input24HrTime,
      'DDMMYYYY HHmm',
      companyDetailsStore.userLocale,
    )
    .valueOf();

  // convert the previous time to check against to Epoch
  const previous24HrTime: string =
    isStartTime && index !== 0
      ? demurrageTimes.value[index - 1].endTimeInput.readableTime
      : isStartTime && index === 0
        ? demurrageTimes.value[index].startTimeInput.readableTime
        : demurrageTimes.value[index].startTimeInput.readableTime;

  const previousDateFormatted =
    isStartTime && index !== 0
      ? moment(rateDataAtIndex.endTimeInEpoch)
          .tz(companyDetailsStore.userLocale)
          .format('DDMMYYYY')
      : moment(rateDataAtIndex.startTimeInEpoch)
          .tz(companyDetailsStore.userLocale)
          .format('DDMMYYYY');

  const previousDate = moment
    .tz(
      previousDateFormatted + ' ' + previous24HrTime,
      'DDMMYYYY HHmm',
      companyDetailsStore.userLocale,
    )
    .valueOf();

  const next24HrTime =
    !isStartTime && index !== demurrageTimes.value.length - 1
      ? demurrageTimes.value[index + 1].startTimeInput.readableTime
      : !isStartTime && index === demurrageTimes.value.length - 1
        ? demurrageTimes.value[index].endTimeInput.readableTime
        : demurrageTimes.value[index].endTimeInput.readableTime;

  const nextDateFormatted = isStartTime
    ? moment(rateDataAtIndex.endTimeInEpoch)
        .tz(companyDetailsStore.userLocale)
        .format('DDMMYYYY')
    : moment(rateDataAtIndex.startTimeInEpoch)
        .tz(companyDetailsStore.userLocale)
        .format('DDMMYYYY');

  const nextDate = moment
    .tz(
      nextDateFormatted + ' ' + next24HrTime,
      'DDMMYYYY HHmm',
      companyDetailsStore.userLocale,
    )
    .valueOf();

  if (inputDate < previousDate || inputDate > nextDate) {
    const message: string = 'Time overlap not allowed.';
    if (isStartTime) {
      errorMessages.value[index].start = message;
    } else {
      errorMessages.value[index].end = message;
    }
  } else {
    if (isStartTime) {
      errorMessages.value[index].start = '';
    } else {
      errorMessages.value[index].end = '';
    }
  }
}
// Updates our demurrageRageData with the new edited values. Finishes with
// refreshing accounting totals
function updateDemurrageTimes(): void {
  if (!demurrageDurationForm.value.validate() || overlapExists.value) {
    showNotification(
      'Please fill in all required fields and verify no time overlaps exist.',
      { title: 'Add/Update Demurrage' },
    );
    return;
  }

  if (isUnitRate.value && demurrageEnabled.value) {
    for (const unitRate of props.unitRates) {
      if (props.isClient) {
        unitRate.demurrage.rate = clientDemurrageUnitRate.value;
      } else {
        unitRate.demurrage.rate = fleetDemurrageUnitRate.value;
      }
    }
  }
  // we set the inputted value and calculate the duration
  demurrageTimes.value.forEach((item, index) => {
    // Only update demurrage values if demurrageEnabled
    if (
      demurrageEnabled.value &&
      props.demurrageRateData.length === props.pudItems.length
    ) {
      const startDate = moment(props.demurrageRateData[index].startTimeInEpoch)
        .tz(companyDetailsStore.userLocale)
        .format('DDMMYYYY');

      const endDate = moment(props.demurrageRateData[index].endTimeInEpoch)
        .tz(companyDetailsStore.userLocale)
        .format('DDMMYYYY');

      const startEpochDateToSet = moment
        .tz(
          startDate + ' ' + item.startTimeInput.readableTime,
          'DDMMYYYY HHmm',
          companyDetailsStore.userLocale,
        )
        .valueOf();
      const endEpochDateToSet = moment
        .tz(
          endDate + ' ' + item.endTimeInput.readableTime,
          'DDMMYYYY HHmm',
          companyDetailsStore.userLocale,
        )
        .valueOf();

      props.demurrageRateData[index].startTimeInEpoch = startEpochDateToSet;
      props.demurrageRateData[index].endTimeInEpoch = endEpochDateToSet;

      props.demurrageRateData[index].breakOverlapDurationInMilliseconds =
        computeDemurrageBreakOverlap(
          startEpochDateToSet,
          endEpochDateToSet,
          props.workDiaryList,
          props.isClient ? RateEntityType.CLIENT : RateEntityType.FLEET_ASSET,
        );

      // Check isClient Prop and Set respective Demurrage Rate for client and fleet unit rate input
      if (isUnitRate.value) {
        if (props.isClient) {
          props.demurrageRateData[index].rate = clientDemurrageUnitRate.value;
          props.fleetDemurrageRateData[index].rate =
            fleetDemurrageUnitRate.value;
        } else {
          props.demurrageRateData[index].rate = fleetDemurrageUnitRate.value;
          props.clientDemurrageRateData[index].rate =
            clientDemurrageUnitRate.value;
        }
      }
    }

    // If travelDelayDurationInMins is defined, update the corresponding travel
    // delay data (converting from mins to ms)
    if (
      item.travelDelayDurationInMins !== undefined &&
      props.travelDelayRateData &&
      travelDelayEnabled.value
    ) {
      const foundTravelDelayRateData = props.travelDelayRateData.find(
        (travelData: TravelDelayRateData) => travelData.pudId === item.pudId,
      );
      if (foundTravelDelayRateData) {
        // We set the travel delay by setting the allowable grace time to be the
        // total time minus the user-entered travel time. The calculations will
        // set the delay duration based on these values.
        let travelDelayMs = item.travelDelayDurationInMins * 60000;
        if (
          travelDelayMs > foundTravelDelayRateData.totalDurationInMilliseconds
        ) {
          travelDelayMs = foundTravelDelayRateData.totalDurationInMilliseconds;
        }
        const graceDuration =
          foundTravelDelayRateData.totalDurationInMilliseconds - travelDelayMs;

        // Set the user-entered value and grace to the original rate data object
        foundTravelDelayRateData.demurrageDurationInMilliseconds =
          travelDelayMs;
        foundTravelDelayRateData.graceDurationInMilliseconds = graceDuration;
      }
    }
  });

  emit('refreshAccountingTotals');
  closeDemurrageDuration();
}

function closeDemurrageDuration() {
  demurrageTimes.value = [];
  errorMessages.value = [];
  dialogIsOpen.value = false;
}

const demurrageEnabled = computed(() =>
  props.enabledDelayTypes.includes(JobDelayType.DEMURRAGE),
);
const travelDelayEnabled = computed(() =>
  props.enabledDelayTypes.includes(JobDelayType.TRAVEL_DELAY),
);
</script>

<style lang="scss" scoped>
.P {
  border-left: 4px solid $pickup;
  padding-left: 8px;
}
.D {
  border-left: 4px solid $drop-highlight;
  padding-left: 8px;
}

.break-txt {
  font-weight: 400;

  .accent-text--primary {
    font-weight: 600;
  }
}

.unit-rate-container {
  margin-top: 12px;
  background-color: var(--background-color-300);
  border-top: 1px solid $translucent;
  padding: 14px 0;
}

.demurrage-icon {
  margin-left: 18px;
  border-radius: 100px;
  background-color: var(--background-color-200);
  color: $warning !important;
  transition: all 0.3s;

  &:hover {
    scale: 1.4;
  }
}
.input-cell {
  min-width: 120px;
  max-width: 200px;
}
</style>
