<ContentDialog
  v-if="dialogIsOpen"
  :showDialog.sync="dialogIsOpen"
  :title="'Electronic Data Interchange Requirements - Job #' + jobId"
  :isConfirmUnsaved="false"
  :width="'1400px'"
  :confirmBtnText="'Continue'"
  @confirm="confirm"
  :isLoading="false"
  @cancel="dialogIsOpen = false"
  :isDisabled="false"
  :contentPadding="'pa-0'"
  :showCancelBtn="false"
>
  <div class="pt-3">
    <v-layout class="header-text">
      <v-flex md4 class="pr pl-3">Legs on Job</v-flex>
      <v-flex md4 class="pl">Current Associated Links</v-flex>
      <v-flex md4 class="pl"
        ><span class="pr-1">Remaining In Groups On Job</span>
        <InformationTooltip :bottom="true">
          <v-layout slot="content"
            >A list of points that are associated with existing links on the
            job.</v-layout
          >
        </InformationTooltip></v-flex
      >
    </v-layout>
    <v-layout>
      <v-divider />
    </v-layout>
    <v-layout>
      <v-flex md8 class="pt-2">
        <v-layout
          wrap
          v-for="(pudItemLink, pudItemLinkIndex) of pudItemLinks"
          :key="pudItemLink.pudItem.pudId"
        >
          <v-flex md6 class="pr pl-3">
            <PudCard
              :index="0"
              :pudItem="pudItemLink.pudItem"
              :editingPudIndex="-1"
              :rateTypeId="1"
              :unassignedPudType="true"
              :isIntegrationRequirementDisplay="true"
            ></PudCard>
          </v-flex>

          <v-flex class="pl pr-3" md6>
            <PudCard
              v-for="unassignedPud of pudItemLink.currentLinks"
              :key="unassignedPud.id"
              :index="0"
              :pudItem="unassignedPud.pudDetails"
              :editingPudIndex="-1"
              :rateTypeId="1"
              :unassignedPudType="true"
              :isIntegrationRequirementDisplay="true"
            ></PudCard>

            <div
              class="missing-link-container"
              v-if="pudItemLink.currentLinks.length === 0"
            >
              <div class="missing-link-content">
                <v-icon color="error" class="mb-2"
                  >fad fa-exclamation-triangle</v-icon
                >
                <span>Missing Link</span>
              </div>
            </div>
          </v-flex>

          <v-flex
            md12
            :class="pudItemLinkIndex === pudItemLinks.length - 1 ? 'pb-2' : ''"
            ><v-divider
              v-if="pudItemLinkIndex !== pudItemLinks.length - 1"
              class="my-2"
          /></v-flex>
        </v-layout>
      </v-flex>
      <v-divider vertical class="mr-3" style="border-width:2px" />
      <v-flex md4 class="pt-2 pr-3">
        <v-layout wrap class="pb-2">
          <v-flex
            md12
            v-for="unassignedPudItem of remainingInGroupThatAreNotLinked"
            :key="unassignedPudItem.id"
          >
            <PudCard
              :index="0"
              :pudItem="unassignedPudItem.pudDetails"
              :unassignedPudItem="unassignedPudItem"
              :editingPudIndex="-1"
              :rateTypeId="1"
              :unassignedPudType="true"
              :isIntegrationRequirementDisplay="true"
            ></PudCard>
          </v-flex>

          <v-flex md12>
            <div
              class="missing-link-container"
              v-if="remainingInGroupThatAreNotLinked.length === 0"
            >
              <div class="missing-link-content">
                <span>No associated points to display</span>
              </div>
            </div>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </div>
</ContentDialog>
