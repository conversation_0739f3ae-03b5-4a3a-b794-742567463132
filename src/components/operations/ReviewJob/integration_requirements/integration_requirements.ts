import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import PudCard from '@/components/operations/BookJob/pud_item_container/pud_card/index.vue';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import UnassignedPudItem from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudItem';
import { useDataImportStore } from '@/store/modules/DataImportStore';
import { Component, Prop, Vue } from 'vue-property-decorator';

interface PudItemLink {
  pudItem: PUDItem;
  currentLinks: UnassignedPudItem[];
}
@Component({
  components: { ContentDialog, PudCard, InformationTooltip },
})
export default class IntegrationRequirements extends Vue {
  @Prop({ default: () => [] }) public pudItems: PUDItem[];
  @Prop() public jobId: number;
  public isLoading: boolean = false;
  public requiredUnassignedPuds: UnassignedPudItem[] = [];
  public dialogIsOpen: boolean = false;

  // Find and request all unassigned puds that are currently assigned on the
  // job. This will include all unassigned puds referenced in pudItems and will
  // also include unassigned puds that are associated with existing linked
  // unassigned puds that are not on the job.
  public async getAllUnassignedPudItemsByGroupIdsOnJob() {
    this.isLoading = true;
    let allCurrentAssignedPudsOnJob: string[] = [];
    for (let i = 0; i < this.pudItems.length; i++) {
      if (!this.pudItems[i] || !this.pudItems[i].unassignedPudItemReference) {
        continue;
      }
      allCurrentAssignedPudsOnJob = allCurrentAssignedPudsOnJob.concat(
        this.pudItems[i].unassignedPudItemReference as string[],
      );
    }
    if (allCurrentAssignedPudsOnJob.length < 1) {
      this.dialogIsOpen = true;
      return;
    }

    // Make request and handle response
    const results =
      await useDataImportStore().getUnassignedPudItemsByGroupIdList(
        allCurrentAssignedPudsOnJob,
      );
    this.setRequiredUnassignedPuds(results ?? []);
  }

  // Handler for response from getAllUnassignedPudItemsByGroupIdsOnJob. Sets the
  // response list and opens the dialog.
  public setRequiredUnassignedPuds(unassignedPuds: UnassignedPudItem[]) {
    this.requiredUnassignedPuds = unassignedPuds;
    this.dialogIsOpen = true;
  }

  public mounted() {
    this.getAllUnassignedPudItemsByGroupIdsOnJob();
  }

  // Return unassigned puds that are currently assigned to legs on the job
  get pudItemLinks(): PudItemLink[] {
    const pudItemLinks: PudItemLink[] = [];
    for (const pudItem of this.pudItems) {
      const unassignedPudIdReferences = Array.isArray(
        pudItem.unassignedPudItemReference,
      )
        ? pudItem.unassignedPudItemReference
        : [];
      const currentlyAssigned = this.requiredUnassignedPuds.filter(
        (x: UnassignedPudItem) => unassignedPudIdReferences.includes(x.id),
      );
      const pudItemLink: PudItemLink = {
        pudItem,
        currentLinks: currentlyAssigned,
      };
      pudItemLinks.push(pudItemLink);
    }
    return pudItemLinks;
  }

  // Return unassigned puds that are associated with the groups on the job but
  // are yet to be linked to this job.
  get remainingInGroupThatAreNotLinked(): UnassignedPudItem[] {
    const remainingUnassignedPudsInGroup: UnassignedPudItem[] = [];
    for (const unassignedPud of this.requiredUnassignedPuds) {
      let existsInPud: boolean = false;
      for (const pudItem of this.pudItems) {
        if (existsInPud) {
          continue;
        }
        if (Array.isArray(pudItem.unassignedPudItemReference)) {
          existsInPud = pudItem.unassignedPudItemReference.includes(
            unassignedPud.id,
          );
        }
      }
      if (!existsInPud) {
        remainingUnassignedPudsInGroup.push(unassignedPud);
      }
    }
    return remainingUnassignedPudsInGroup;
  }

  public confirm() {
    this.dialogIsOpen = false;
  }
}
