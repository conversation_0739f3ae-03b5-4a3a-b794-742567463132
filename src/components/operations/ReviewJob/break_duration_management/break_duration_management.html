<section class="break-duration-management">
  <v-dialog
    v-model="dialogController"
    width="55%"
    class="ma-0"
    persistent
    no-click-animation
    content-class="v-dialog-custom"
  >
    <v-flex md12>
      <v-layout
        justify-space-between
        align-center
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>Manage Driver Breaks</span>
        <div
          class="app-theme__center-content--closebutton"
          @click="dialogController = false"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <v-layout row wrap class="app-theme__center-content--body dialog-content">
        <v-flex md12 v-if="workDiaryRecordsClean !== null">
          <v-form ref="breakDurationManagementForm">
            <v-layout px-3 row wrap class="body-scrollable--65">
              <v-flex md12 py-3
                ><v-layout align-center>
                  <h5 class="subheader--bold pr-3 pt-1">
                    Recorded Breaks ({{workDiaryRecordsClean.length}})
                  </h5>
                  <v-flex>
                    <v-divider></v-divider>
                  </v-flex>
                  <span v-if="!readOnly">
                    <v-btn
                      small
                      outline
                      @click="isViewingAddRecordDialog = true"
                      color="#49b9ff"
                      >Add New Break
                      <v-icon right size="16">fal fa-plus </v-icon>
                    </v-btn>
                  </span>
                </v-layout>
              </v-flex>

              <v-flex
                md12
                px-2
                v-for="(record, index) in workDiaryRecordsClean"
                :key="record.recordId"
              >
                <v-divider v-if="index !== 0" class="mb-3 mt-1"></v-divider>
                <BreakDurationLineItem
                  :workDiarySummary="record"
                  :readOnly="readOnly"
                  @breakDurationUpdated="totalBreakDurationUpdated"
                ></BreakDurationLineItem>
              </v-flex>
            </v-layout>
          </v-form>
        </v-flex>
        <v-flex md12>
          <v-divider></v-divider>
          <v-layout align-center v-if="workDiaryRecordsClean !== null">
            <v-btn flat color="red" @click="cancelEdits">Cancel</v-btn>
            <v-spacer></v-spacer>

            <ConfirmationDialog
              :buttonText="!readOnly ? 'Save Changes' : 'Finish'"
              message="Please note that this will update the driver's Electronic Work Diary (EWD) with any updated times, or newly added breaks. Do you wish to proceed?"
              title="Update Driver Work Diary"
              @confirm="saveWorkDiaryRecords"
              :buttonDisabled="readOnly || workDiaryRecordsClean.length === 0"
              :isDepressedButton="true"
              buttonColor="blue"
              confirmationButtonText="Save Changes"
              :dialogIsActive="!readOnly"
            />
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>
  </v-dialog>
  <AddWorkDiaryRecordDialog
    :isViewingDialog.sync="isViewingAddRecordDialog"
    :defaultStartTime="startTime ? startTime : 0"
    @saveWorkDiarySummary="addNewWorkDiarySummary"
  ></AddWorkDiaryRecordDialog>
</section>
