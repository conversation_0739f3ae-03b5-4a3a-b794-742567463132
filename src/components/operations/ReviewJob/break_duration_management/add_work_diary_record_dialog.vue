<template>
  <section class="add-work-diary-record-dialog">
    <v-dialog
      v-model="dialogController"
      width="600px"
      class="ma-0"
      persistent
      no-click-animation
      content-class="v-dialog-custom"
    >
      <v-flex md12>
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight"
        >
          <span>Add New Break</span>
          <div
            class="app-theme__center-content--closebutton"
            @click="dialogController = false"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>
        <v-layout
          row
          wrap
          class="app-theme__center-content--body"
          v-if="workDiarySummary"
        >
          <v-flex md12>
            <v-form ref="addWorkDiaryRecordForm">
              <v-layout row wrap class="body-scrollable--70" pa-3>
                <v-flex md12>
                  <v-flex md12>
                    <v-alert type="info" class="ma-3" :value="true">
                      <strong>Please note:</strong>
                      <ul>
                        <li>
                          This will also create an entry in the selected
                          driver's Electronic Work Diary. Please ensure entered
                          times are accurate.
                        </li>
                      </ul>
                    </v-alert>
                  </v-flex>
                </v-flex>
                <BreakDurationLineItem
                  :workDiarySummary="workDiarySummary"
                  :isNewBreak="true"
                  :autofocusTime="false"
                  uniqueKey="addWorkDiaryDialog"
                ></BreakDurationLineItem>
              </v-layout>
            </v-form>
          </v-flex>
          <v-flex md12>
            <v-divider></v-divider>
            <v-layout align-center>
              <v-btn flat color="red" @click="dialogController = false"
                >Cancel</v-btn
              >
              <v-spacer></v-spacer>
              <v-btn
                depressed
                color="blue"
                @click="saveWorkDiaryRecord"
                class="v-btn-confirm-custom"
                >Save New Break</v-btn
              >
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-dialog>
  </section>
</template>

<script setup lang="ts">
import BreakDurationLineItem from '@/components/operations/ReviewJob/break_duration_management/break_duration_line_item/index.vue';
import { roundUpToNearestHour } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import WorkDiarySummary from '@/interface-models/Driver/WorkDiary/WorkDiarySummary';
import { computed, ref, Ref, watch } from 'vue';

const emit = defineEmits<{
  (event: 'update:isViewingDialog', payload: boolean): void;
  (event: 'saveWorkDiarySummary', payload: WorkDiarySummary): void;
}>();

const props = withDefaults(
  defineProps<{
    isViewingDialog?: boolean;
    defaultStartTime?: number;
  }>(),
  {
    isViewingDialog: false,
    defaultStartTime: 0,
  },
);

const workDiarySummary: Ref<WorkDiarySummary | null> = ref(null);
const focusTextfieldTimeout: Ref<ReturnType<typeof setTimeout> | null> =
  ref(null);

const addWorkDiaryRecordForm: Ref<any> = ref(null);

/**
 * Controls dialog visibility, and resets local working variables on close.
 */
const dialogController = computed<boolean>({
  get(): boolean {
    const value = props.isViewingDialog;
    return value;
  },
  set(value: boolean): void {
    emit('update:isViewingDialog', value);
  },
});

watch(dialogController, (value: boolean) => {
  if (value) {
    if (workDiarySummary.value === null) {
      workDiarySummary.value = new WorkDiarySummary();
      if (props.defaultStartTime) {
        // Prefill fields but set start and end to same time to force user to change times
        workDiarySummary.value.startEpoch = roundUpToNearestHour(
          props.defaultStartTime,
        );
        workDiarySummary.value.endEpoch = workDiarySummary.value.startEpoch;
        workDiarySummary.value.durationInMilliseconds = 0;
      }
    }
    setFocusOnTimeTextfield();
  } else {
    workDiarySummary.value = null;
  }
});

/**
 * Focus textfield in child after short delay.
 */
function setFocusOnTimeTextfield(): void {
  if (focusTextfieldTimeout.value) {
    clearTimeout(focusTextfieldTimeout.value);
  }
  focusTextfieldTimeout.value = setTimeout(() => {
    const el = document.getElementById('addWorkDiaryDialog-time') as
      | HTMLInputElement
      | null
      | undefined;
    if (el) {
      el.focus();
      el.select();
    }
  }, 300);
}

/**
 * Save the work diary record, validating form and data.
 */
function saveWorkDiaryRecord(): void {
  if (!workDiarySummary.value) {
    showNotification('Please review the selected times and try again.', {
      title: 'Add/Update Driver Break',
    });
    return;
  }

  if (!addWorkDiaryRecordForm.value?.validate()) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE, {
      title: 'Add/Update Driver Break',
    });
    return;
  }
  if (
    !workDiarySummary.value ||
    !workDiarySummary.value.durationInMilliseconds
  ) {
    showNotification(
      'Please ensure the times and duration you have entered are correct.',
      { title: 'Add/Update Driver Break' },
    );
    return;
  }
  emit('saveWorkDiarySummary', workDiarySummary.value);
}
</script>
