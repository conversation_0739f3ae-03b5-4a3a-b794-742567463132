<v-layout class="break-duration-line-item" row wrap>
  <v-flex :md4="!isNewBreak" :md12="isNewBreak">
    <v-layout v-if="!isNewBreak" row wrap>
      <v-flex md12>
        <v-layout>
          <v-switch
            label="Charge Client"
            :disabled="readOnly"
            persistent-hint
            :hint="workDiarySummary.chargeClient ? `The Client will be charged for this break` : `The duration will be deducted from the Client's charges` "
            v-model="workDiarySummary.chargeClient"
          ></v-switch>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-switch
            label="Pay Fleet Asset"
            :disabled="readOnly"
            v-model="workDiarySummary.payFleetAsset"
            persistent-hint
            :hint="workDiarySummary.payFleetAsset ? `The Fleet Asset will be paid for this break` : `The duration will be deducted from the Fleet Asset's pay`"
          ></v-switch>
        </v-layout>
      </v-flex>
    </v-layout>
    <v-layout v-else row wrap>
      <v-flex md12>
        <v-layout>
          <v-flex md5>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Charge Client</h6>
            </v-layout>
          </v-flex>
          <v-flex md7>
            <v-switch
              v-model="workDiarySummary.chargeClient"
              persistent-hint
              :hint="workDiarySummary.chargeClient ? `The Client will be charged for this break` : `The duration will be deducted from the Client's charges` "
            >
            </v-switch>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md5>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Pay Fleet Asset</h6>
            </v-layout>
          </v-flex>
          <v-flex md7
            ><v-switch
              v-model="workDiarySummary.payFleetAsset"
              persistent-hint
              :hint="workDiarySummary.payFleetAsset ? `The Fleet Asset will be paid for this break` : `The duration will be deducted from the Driver's pay.` "
            ></v-switch
          ></v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 my-3>
        <v-divider></v-divider>
      </v-flex>
    </v-layout>
  </v-flex>
  <v-flex :md8="!isNewBreak" :md12="isNewBreak">
    <v-layout>
      <v-flex md5 v-if="isNewBreak">
        <v-layout align-center class="form-field-label-container">
          <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
            Break Start
          </h6>
        </v-layout>
      </v-flex>
      <v-flex :md7="isNewBreak" :md12="!isNewBreak">
        <DateTimeInputs
          :key="workDiarySummary.recordId + '-start'"
          :epochTime.sync="workDiarySummary.startEpoch"
          :enableValidation="true"
          dateLabel="Break Start Date"
          timeLabel="Break Start Time"
          :soloInput="isNewBreak"
          :boxInput="!isNewBreak"
          :readOnly="readOnly"
          :maximumEpochTime="maximumAllowedEpochTime"
          :autofocusTime="autofocusTime"
          :uniqueKey="uniqueKey"
          @dateTimeUpdated="dateTimeUpdated(workDiarySummary.recordId)"
        ></DateTimeInputs>
      </v-flex>
    </v-layout>
    <v-layout>
      <v-flex md5 v-if="isNewBreak">
        <v-layout align-center class="form-field-label-container">
          <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
            Break End
          </h6>
        </v-layout>
      </v-flex>
      <v-flex :md7="isNewBreak" :md12="!isNewBreak">
        <DateTimeInputs
          :key="workDiarySummary.recordId + '-end'"
          :epochTime.sync="workDiarySummary.endEpoch"
          :enableValidation="true"
          dateLabel="Break End Date"
          timeLabel="Break End Time"
          :soloInput="isNewBreak"
          :boxInput="!isNewBreak"
          :readOnly="readOnly"
          :minimumEpochTime="workDiarySummary.startEpoch ? workDiarySummary.startEpoch : undefined"
          :maximumEpochTime="maximumAllowedEpochTime"
          minComparisonType="GREATER_OR_EQUAL"
          @dateTimeUpdated="dateTimeUpdated(workDiarySummary.recordId)"
        ></DateTimeInputs>
      </v-flex>
    </v-layout>
    <v-layout v-if="!isNewBreak">
      <v-flex md6 offset-md6>
        <v-text-field
          hint="In minutes"
          v-model.number="workDiarySummary.durationInMinutes"
          :disabled="true"
          persistent-hint
          box
          label="Break Duration"
          type="number"
          class="pl-1"
          readonly
        />
      </v-flex>
    </v-layout>
    <v-layout v-else row wrap>
      <v-flex md12 mb-3>
        <v-divider></v-divider>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md5>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Break Duration</h6>
            </v-layout>
          </v-flex>
          <v-flex md7>
            <v-text-field
              hint="In minutes"
              v-model.number="workDiarySummary.durationInMinutes"
              :disabled="true"
              persistent-hint
              color="light-blue"
              solo
              flat
              class="v-solo-custom"
              label="Break Duration"
              type="number"
              readonly
            />
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </v-flex>
</v-layout>
