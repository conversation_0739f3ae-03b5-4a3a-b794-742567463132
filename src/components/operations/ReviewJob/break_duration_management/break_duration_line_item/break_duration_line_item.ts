import { Component, Prop, Vue } from 'vue-property-decorator';
import DateTimeInputs from '@/components/common/date-picker/date_time_inputs.vue';

import WorkDiarySummary from '@/interface-models/Driver/WorkDiary/WorkDiarySummary';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import {
  millisecondsInOneDay,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';

@Component({
  components: {
    DateTimeInputs,
  },
})
export default class BreakDurationLineItem extends Vue {
  @Prop({ default: '' }) public uniqueKey: string;
  @Prop() public workDiarySummary: WorkDiarySummary;
  @Prop({ default: false }) public isNewBreak: boolean;
  @Prop({ default: false }) public readOnly: boolean;
  @Prop({ default: false }) public autofocusDate: boolean;
  @Prop({ default: false }) public autofocusTime: boolean;

  public maximumAllowedEpochTime: number | null = null;

  public dateTimeUpdated() {
    const duration =
      this.workDiarySummary.endEpoch - this.workDiarySummary.startEpoch;
    if (duration >= 0) {
      this.workDiarySummary.durationInMilliseconds = duration;
    } else {
      this.workDiarySummary.durationInMilliseconds = 0;
    }
  }

  // Return true if the user is an ADMIN or HEAD OFFICE user. Used to determine
  // whether maximum epochTime values should be enforced on the
  // DateTimeTextFields component
  public isAuthorised() {
    return hasAdminOrHeadOfficeRole();
  }

  public mounted() {
    if (!this.isAuthorised()) {
      // Set the maximum epochTime for the inputs to be the start of tomorrow
      this.maximumAllowedEpochTime =
        returnStartOfDayFromEpoch() + millisecondsInOneDay;
    }
  }
}
