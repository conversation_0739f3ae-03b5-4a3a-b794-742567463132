import DateTimeInputs from '@/components/common/date-picker/date_time_inputs.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import AddWorkDiaryRecordDialog from '@/components/operations/ReviewJob/break_duration_management/add_work_diary_record_dialog.vue';
import BreakDurationLineItem from '@/components/operations/ReviewJob/break_duration_management/break_duration_line_item/index.vue';
import { roundDownToNearestMinute } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { WorkDiaryActivityType } from '@/interface-models/Driver/WorkDiary/WorkDiaryActivityType';
import WorkDiaryRecord from '@/interface-models/Driver/WorkDiary/WorkDiaryRecord';
import WorkDiaryRecordHistoryRequest from '@/interface-models/Driver/WorkDiary/WorkDiaryRecordHistoryRequest';
import WorkDiarySummary from '@/interface-models/Driver/WorkDiary/WorkDiarySummary';
import { JobAccountingDetails } from '@/interface-models/Generic/Accounting/JobAccountingDetails';
import JobPrimaryRate from '@/interface-models/Generic/Accounting/JobPrimaryRate';
import { TimeTypeJobRateData } from '@/interface-models/Generic/Accounting/JobRateData/TimeTypeJobRateData';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import moment from 'moment-timezone';
import { v4 as uuidv4 } from 'uuid';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component({
  components: {
    DateTimeInputs,
    BreakDurationLineItem,
    AddWorkDiaryRecordDialog,
    ConfirmationDialog,
  },
})
export default class BreakDurationManagement extends Vue {
  @Prop() public driverId: string;
  @Prop() public jobAccountingDetails: JobAccountingDetails;
  @Prop() public jobDetails: JobDetails;
  @Prop({ default: false }) public isViewingDialog: boolean;
  @Prop({ default: false }) public readOnly: boolean;

  public driverDetailsStore = useDriverDetailsStore();

  public startTime: number = 0;
  public endTime: number = 0;

  public isViewingAddEditDialog: boolean = false;
  public isAwaitingSaveResponse: boolean = false;

  public isViewingAddRecordDialog: boolean = false;

  // Used to display the WorkDiary list in the dialog when adding/editing
  // breaks. Is non-null when the dialog is open, and is set to null when the
  // dialog is closed
  public workDiaryRecordsClean: WorkDiarySummary[] | null = null;

  public awaitingRecordsMap: Map<string, WorkDiarySummary | null> = new Map<
    string,
    WorkDiarySummary | null
  >();
  // Stores the WorkDiarySummary objects that are synced to the parent
  public workDiaryRecordMap: Map<string, WorkDiarySummary> = new Map<
    string,
    WorkDiarySummary
  >();

  // Controls dialog visibility, and resets local working variables on close
  get dialogController() {
    // If readOnly view then
    if (
      this.readOnly &&
      this.jobAccountingDetails &&
      this.jobAccountingDetails.fleetAssetRates[0]
    ) {
      this.workDiaryRecordsClean =
        this.jobAccountingDetails.fleetAssetRates[0].breakDuration.breakSummaryList;
      return this.isViewingDialog;
    }
    if (this.isViewingDialog && this.workDiaryRecordsClean === null) {
      this.workDiaryRecordsClean = [...this.workDiaryRecordMap.values()]
        .sort((a, b) => a.startEpoch - b.startEpoch)
        .map((r) => {
          const copy = Object.assign(new WorkDiarySummary(), r);
          return copy;
        });
    }
    return this.isViewingDialog;
  }
  set dialogController(value: boolean) {
    if (!value) {
      this.workDiaryRecordsClean = null;
      this.awaitingRecordsMap.clear();
    }
    this.$emit('update:isViewingDialog', value);
  }

  public cancelEdits() {
    this.dialogController = false;
  }
  // Save any work diary records that have changes detected
  public saveWorkDiaryRecords() {
    if (this.readOnly) {
      this.dialogController = false;
      return;
    }
    if (!(this.$refs.breakDurationManagementForm as any).validate()) {
      showNotification(FORM_VALIDATION_FAILED_MESSAGE, {
        title: 'Driver Break Management',
      });
      return;
    }
    if (this.workDiaryRecordsClean === null) {
      return;
    }
    const toBeSaved: WorkDiarySummary[] = this.workDiaryRecordsClean.filter(
      (r) => {
        // Look for original record in map
        const orig = this.workDiaryRecordMap.get(r.recordId);
        if (!orig) {
          // We can't find in original - it must be new
          return true;
        }
        return this.checkWorkDiarySummaryForChanges(r, orig);
      },
    );
    if (!toBeSaved.length) {
      // None changed, don't need to save. Recalculate amounts in case the
      // switches have changed
      this.workDiaryRecordsClean.forEach((r) => {
        if (this.workDiaryRecordMap.has(r.recordId)) {
          this.workDiaryRecordMap.set(r.recordId, r);
        }
      });
      this.totalBreakDurationUpdated();
      this.dialogController = false;
      return;
    }
    const toBeSavedRecordIds: string[] = toBeSaved.map((tbs) => tbs.recordId);
    // Add records that AREN'T going to be saved in case switches or values have
    // changed
    this.workDiaryRecordsClean.forEach((r) => {
      // Filter for recordId NOT in toBeSaved
      if (
        !toBeSavedRecordIds.includes(r.recordId) &&
        this.workDiaryRecordMap.has(r.recordId)
      ) {
        this.workDiaryRecordMap.set(r.recordId, r);
      }
    });
    // Records not being saved have been added to workDiaryRecordMap. Now we
    // must save the toBeSaved records. Add to awaitingRecordsMap map to
    // indicate that we're waiting for a response for these, then send save
    // request for each individual record
    this.isAwaitingSaveResponse = true;
    toBeSaved.forEach((r) => {
      this.awaitingRecordsMap.set(r.recordId, null);
      r.updateAndSaveWorkDiary().then(this.handleSaveRecordResponse);
    });
  }

  // Returns true if the two provided records have differences in values
  public checkWorkDiarySummaryForChanges(
    newRecord: WorkDiarySummary,
    originalRecord: WorkDiarySummary,
  ): boolean {
    return (
      newRecord.startEpoch !== originalRecord.startEpoch ||
      newRecord.durationInMilliseconds !== originalRecord.durationInMilliseconds
    );
  }

  // Handler for the client break amount. V-modelled to the client break input,
  // which is visible if syncClientWithDriver is false
  get clientBreakDuration(): number {
    if (this.readOnly) {
      return 0;
    } else {
      const jad = this.jobAccountingDetails;
      if (!jad || !jad.clientRates[0]) {
        return 0;
      }
      return jad.clientRates[0].breakDuration.currentDuration;
    }
  }
  set clientBreakDuration(durationInMilliseconds: number) {
    if (this.readOnly) {
      return;
    }
    this.addHistoryEditToList(
      durationInMilliseconds,
      this.jobAccountingDetails.clientRates[0],
    );
  }
  // Handler for the fleetAsset break amount
  get fleetAssetBreakDuration(): number {
    if (this.readOnly) {
      return 0;
    } else {
      const jad = this.jobAccountingDetails;
      if (!jad || !jad.fleetAssetRates[0]) {
        return 0;
      }
      return this.minsFromDurationEpoch(
        jad.fleetAssetRates[0].breakDuration.currentDuration,
      );
    }
  }
  set fleetAssetBreakDuration(durationInMilliseconds: number) {
    if (this.readOnly) {
      return;
    }
    this.addHistoryEditToList(
      durationInMilliseconds,
      this.jobAccountingDetails.fleetAssetRates[0],
    );
  }

  // Constructs a history edit and pushes it into breakDuration array in the supplied list
  public addHistoryEditToList(
    durationInMilliseconds: number,
    primaryRate: JobPrimaryRate,
  ) {
    primaryRate.breakDuration.duration = durationInMilliseconds;
  }

  // Search for break work diary that are between the current start and end times
  public async requestWorkDiaryHistory(rateData: TimeTypeJobRateData[]) {
    if (!rateData.length || !this.driverId) {
      return;
    }
    const startTime = rateData[0].mostRecentBegin;
    const endTime = rateData[rateData.length - 1].mostRecentBegin;
    if (startTime === this.startTime && endTime === this.endTime) {
      return;
    }
    this.startTime = startTime;
    this.endTime = endTime;

    const historyRequest: WorkDiaryRecordHistoryRequest =
      new WorkDiaryRecordHistoryRequest();
    historyRequest.driverId = this.driverId;
    historyRequest.startEpoch = this.startTime;
    historyRequest.endEpoch = this.endTime;
    historyRequest.activityType = WorkDiaryActivityType.BREAK;

    // Request work diary records and process response
    const results =
      await this.driverDetailsStore.searchWorkDiaryRecords(historyRequest);
    this.setRecordsFromResponse(results ?? []);
  }

  public refreshTotalsInParent() {
    this.$emit(
      'breakDurationsUpdated',
      [...this.workDiaryRecordMap.values()]
        .sort((a, b) => a.startEpoch - b.startEpoch)
        .map((r) => {
          const copy = Object.assign(new WorkDiarySummary(), r);
          return copy;
        }),
    );
  }
  // Refresh the total and update accounting details
  public totalBreakDurationUpdated() {
    if (
      !this.jobAccountingDetails ||
      !this.jobAccountingDetails.fleetAssetRates[0]
    ) {
      return;
    }
    const summaryList: WorkDiarySummary[] = [
      ...this.workDiaryRecordMap.values(),
    ];
    // Get the total durations for all break objects
    const totalClientDuration = summaryList
      .filter((r) => !r.chargeClient)
      .map((r) => r.durationInMilliseconds)
      .reduce((prev, curr) => prev + curr, 0);
    const totalFleetDuration = summaryList
      .filter((r) => !r.payFleetAsset)
      .map((r) => r.durationInMilliseconds)
      .reduce((prev, curr) => prev + curr, 0);
    // Set to fleet asset breakDuration. If we're syncing with client, then also
    // set to client.
    this.fleetAssetBreakDuration = totalFleetDuration;
    this.clientBreakDuration = totalClientDuration;

    this.refreshTotalsInParent();
  }

  // Handle response from API
  public setRecordsFromResponse(incomingRecords: WorkDiaryRecord[]) {
    // Init the list of records and convert to summary object
    const summaryList: WorkDiarySummary[] = incomingRecords.map((r) => {
      const record = Object.assign(new WorkDiaryRecord(), r);
      return this.returnWorkDiarySummaryForRecord(record);
    });
    // Create new map and add in all incoming records
    const incomingSummaryMap: Map<string, WorkDiarySummary> = new Map<
      string,
      WorkDiarySummary
    >();
    // Add all incoming keys to map
    summaryList.forEach((summ) => {
      incomingSummaryMap.set(summ.recordId, summ);
    });
    // Add all existing map values
    this.workDiaryRecordMap.forEach(
      (oldSumm: WorkDiarySummary, key: string) => {
        // If the new map has the key, then check if we should replace incoming
        // value with old value
        if (incomingSummaryMap.has(key)) {
          const incomingSumm = incomingSummaryMap.get(key);
          if (incomingSumm) {
            // If the duration OR start time is different, then we should put
            // back old value as it contains any edits that the user may have made
            const hasChanges = this.checkWorkDiarySummaryForChanges(
              incomingSumm,
              oldSumm,
            );
            if (hasChanges) {
              incomingSummaryMap.set(key, oldSumm);
            }
          }
        }
      },
    );
    // These incoming values will have had their boolean switch values reset to
    // defaults. Iterate over the WorkDiarySummary items that are currently in
    // JobAccounting, so we can set these restore these booleans to their proper
    // values
    if (
      this.jobAccountingDetails &&
      this.jobAccountingDetails.fleetAssetRates[0] &&
      this.jobAccountingDetails.fleetAssetRates[0].breakDuration
    ) {
      // Find summary in job accounting
      const accSummList = this.jobAccountingDetails.fleetAssetRates[0]
        .breakDuration.breakSummaryList
        ? this.jobAccountingDetails.fleetAssetRates[0].breakDuration
            .breakSummaryList
        : [];
      // For each accounting summary, set booleans from accounting back to
      // incoming

      accSummList.forEach((summInAcc: WorkDiarySummary) => {
        const incomingSumm = incomingSummaryMap.get(summInAcc.recordId);
        if (incomingSumm) {
          incomingSumm.payFleetAsset = summInAcc.payFleetAsset;
          incomingSumm.chargeClient = summInAcc.chargeClient;
          incomingSummaryMap.set(summInAcc.recordId, incomingSumm);
        }
      });
    }

    this.workDiaryRecordMap = incomingSummaryMap;
    this.totalBreakDurationUpdated();
  }
  // Constructs WorkDiarySummary for provided WorkDiaryRecord
  public returnWorkDiarySummaryForRecord(record: WorkDiaryRecord) {
    const startTime = roundDownToNearestMinute(record.activityTimestamp);
    const duration = roundDownToNearestMinute(record.duration);

    const summary: WorkDiarySummary = new WorkDiarySummary(
      record._id ? record._id : '',
      record.recordId,
      startTime,
      startTime + duration,
      duration,
      false,
      false,
      record,
    );
    return summary;
  }

  // Handles response for saved work diary record
  // Checks if we are awaiting any more responses
  public handleSaveRecordResponse(record: WorkDiaryRecord | null) {
    if (!record) {
      return;
    }
    record = Object.assign(new WorkDiaryRecord(), record);
    if (this.awaitingRecordsMap.has(record.recordId)) {
      const summ = this.returnWorkDiarySummaryForRecord(record);
      // chargeClient and payFleetAsset aren't returned in save. We should check
      // the old copy in the clean list and copy these values to the returned
      // response.
      const foundCleanRecord =
        this.workDiaryRecordsClean &&
        this.workDiaryRecordsClean.find((r) => r.recordId === summ.recordId);
      // If we find a match, set the chargeClient and payFleetAsset properties
      if (foundCleanRecord) {
        summ.chargeClient = foundCleanRecord.chargeClient;
        summ.payFleetAsset = foundCleanRecord.payFleetAsset;
      }
      this.awaitingRecordsMap.set(record.recordId, summ);
    }

    // All responses received when the map values are all non-null
    const allReceived: boolean = [...this.awaitingRecordsMap.values()].every(
      (e) => e !== null,
    );
    if (allReceived) {
      this.awaitingRecordsMap.forEach((value, key) => {
        if (value) {
          this.workDiaryRecordMap.set(key, value);
        }
      });

      this.isAwaitingSaveResponse = false;
      this.totalBreakDurationUpdated();
      this.dialogController = false;
    }
  }
  // Receive new WorkDiary from emit from AddWorkDiaryDialog component
  public addNewWorkDiarySummary(summ: WorkDiarySummary) {
    const recordId = uuidv4().replace(/-/g, '');
    summ.recordId = recordId;
    summ.workDiary = summ.workDiary ? summ.workDiary : new WorkDiaryRecord();
    summ.workDiary.driverId = this.driverId;
    summ.workDiary.recordId = recordId;
    if (this.workDiaryRecordsClean) {
      this.workDiaryRecordsClean.push(summ);
      this.workDiaryRecordsClean.sort((a, b) => a.startEpoch - b.startEpoch);
      this.isViewingAddRecordDialog = false;
    }
  }

  public minsFromDurationEpoch(epoch: number) {
    const duration = moment.duration(epoch ? epoch : 0);
    return Math.round(duration.asMinutes());
  }
}
