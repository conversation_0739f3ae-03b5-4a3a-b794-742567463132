<v-layout class="equipment-hire-allocation-container pa-2">
  <v-flex md12>
    <v-data-table
      :headers="tableHeaders"
      class="gd-dark-theme"
      :items="hireContractTableList"
      hide-actions
    >
      <template v-slot:items="props">
        <tr class="table-row">
          <td>{{props.item.ownerName}}</td>
          <td>{{props.item.csrAssignedId}}</td>
          <td>{{props.item.documentNumber}}</td>
          <td>{{props.item.contractNumber}}</td>
          <td>{{props.item.validFromDate}}</td>
          <td>{{props.item.validToDate}}</td>
          <td>
            <v-btn
              small
              outline
              :disabled="contractAlreadyInUse(props.item.hireContractId)"
              color="teal accent-3"
              :loading="isLoading && props.item.fleetAssetId === selectedFleetAssetIdForAllocation"
              @click="selectContract(props.item, props.item.ownerId)"
            >
              <span>Allocate</span>
            </v-btn>
          </td>
        </tr>
      </template>
    </v-data-table>
  </v-flex>
</v-layout>
