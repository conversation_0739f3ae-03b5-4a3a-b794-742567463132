import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import HireContract from '@/interface-models/FleetAsset/EquipmentHire/HireContract';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { VForm } from '@/interface-models/Generic/VForm/VForm';
import { Validation } from '@/interface-models/Generic/Validation';
import AdditionalAsset from '@/interface-models/Jobs/AdditionalAsset';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useJobStore } from '@/store/modules/JobStore';
import { Component, Prop, Vue } from 'vue-property-decorator';

interface HireContractTable {
  ownerId: string;
  ownerName: string;
  fleetAssetId: string;
  csrAssignedId: string;
  registrationNumber: string;
  hireContractId: string | undefined;
  contractNumber: string;
  documentNumber: string;
  validFromDate: string;
  validToDate: string;
}
@Component({
  components: {},
})
export default class EquipmentHireAllocation extends Vue {
  @Prop() public jobDetails: JobDetails;

  public fleetAssetStore = useFleetAssetStore();

  public formatDate = returnFormattedDate;

  public divisionContracts: HireContract[] = [];

  public isAllocation: boolean = false;
  public selectedFleetAssetIdForAllocation: string | null = null;
  public isLoading: boolean = false;

  public $refs!: {
    contractForm: VForm;
  };
  get hireContractTableList(): HireContractTable[] {
    const hireOwners = useFleetAssetOwnerStore().getOwnerList.filter(
      (owner: FleetAssetOwnerSummary) => owner.affiliation === '2',
    );
    const fleetAssets: FleetAssetSummary[] =
      this.fleetAssetStore.getAllFleetAssetList;
    const hireContractTable: HireContractTable[] = [];
    for (const owner of hireOwners) {
      const hireAssets = fleetAssets.filter(
        (x: FleetAssetSummary) => x.fleetAssetOwnerId === owner.ownerId,
      );
      for (const asset of hireAssets) {
        const hireContract: HireContract | undefined =
          this.divisionContracts.find(
            (contract: HireContract) =>
              contract.fleetAssetId === asset.fleetAssetId,
          );

        if (!hireContract) {
          continue;
        }
        const hireContractTableItem: HireContractTable = {
          ownerId: owner.ownerId,
          ownerName: owner.name,
          fleetAssetId: asset.fleetAssetId,
          csrAssignedId: asset.csrAssignedId,
          registrationNumber: asset.registrationNumber,
          hireContractId: hireContract._id,
          contractNumber: hireContract.contractNumber,
          documentNumber: hireContract.documentNumber,
          validFromDate: hireContract.validFromDate
            ? returnFormattedDate(hireContract.validFromDate)
            : '',
          validToDate: hireContract.validToDate
            ? returnFormattedDate(hireContract.validToDate)
            : '',
        };
        hireContractTable.push(hireContractTableItem);
      }
    }
    return hireContractTable;
  }

  public async selectContract(contract: HireContractTable, ownerId: string) {
    if (contract && contract.fleetAssetId && this.jobDetails) {
      this.jobDetails.additionalAssets.push(
        new AdditionalAsset(2, contract.fleetAssetId!, contract.hireContractId),
      );
      this.selectedFleetAssetIdForAllocation = contract.fleetAssetId;
      this.isLoading = true;
      // Send request
      const result = await useJobStore().updateJobDetails(this.jobDetails);
      if (!result) {
        showNotification(GENERIC_ERROR_MESSAGE, {
          title: 'Equipment Hire Allocation',
        });
      }
      this.selectedFleetAssetIdForAllocation = null;
      this.isLoading = false;
    }
  }

  get validate(): Validation {
    return validationRules;
  }

  public contractAlreadyInUse(contractId: string) {
    return this.jobDetails.additionalAssets.find(
      (x: AdditionalAsset) => x.contractId === contractId,
    )
      ? true
      : false;
  }

  get tableHeaders(): TableHeader[] {
    const tableHeaders: TableHeader[] = [
      {
        text: 'Owner',
        align: 'left',
        sortable: true,
        value: 'ownerName',
        visible: true,
      },
      {
        text: 'Asset',
        align: 'left',
        sortable: true,
        value: 'ownerName',
        visible: true,
      },
      {
        text: 'Document Number',
        align: 'left',
        sortable: true,
        value: 'documentNumber',
        visible: true,
      },
      {
        text: 'Contract Number',
        align: 'left',
        sortable: true,
        value: 'contractNumber',
        visible: true,
      },
      {
        text: 'Valid From',
        align: 'left',
        sortable: true,
        value: 'validFromDate',
        visible: true,
      },
      {
        text: 'Valid To',
        align: 'left',
        sortable: true,
        value: 'validToDate',
        visible: true,
      },
      {
        text: 'Allocation',
        align: 'left',
        sortable: true,
        value: '',
        visible: true,
      },
    ];
    return tableHeaders;
  }

  public async mounted() {
    if (this.jobDetails.jobRunEpoch) {
      this.divisionContracts =
        (await this.fleetAssetStore.requestActiveHireContracts(
          this.jobDetails.jobRunEpoch,
        )) ?? [];
    }
  }
}
