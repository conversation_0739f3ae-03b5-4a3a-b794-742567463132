<section class="finalise-job-table-row pt-0">
  <table class="custom-table-body--secondary">
    <!--------------------------------------------------->
    <!-------- Client Freight Rows --------->
    <!--------------------------------------------------->
    <tr v-if="!outsideMetroType">
      <!--------------------------->
      <!-------- COLUMN 1 --------->
      <!--------------------------->
      <td class="td--secondary column1" :rowspan="freightRowSpan">
        <v-layout nowrap align-center justify-start>
          <v-flex v-if="additionalChargeType">
            {{ returnAdditionalChargeType(chargeItem._id,
            chargeItem.typeReferenceId) }}
          </v-flex>
          <v-flex v-if="fuelLevyType"> Fuel Levy </v-flex>
          <v-flex v-if="freightChargeType"> Freight Charge </v-flex>
          <v-flex v-if="isStandbyRate"> Standby Charge </v-flex>

          <v-flex v-if="isDemurrageRate"> Demurrage Charge </v-flex>
          <v-flex v-if="isTravelDelayRate"> Travel Delay Charge </v-flex>
        </v-layout>
      </td>

      <!--------------------------->
      <!-------- COLUMN2 --------->
      <!--------------------------->
      <td class="td--secondary column2">
        <v-layout justify-start align-center>
          <v-icon size="12" class="pr-1 td--secondary-icon">
            fal fa-user
          </v-icon>
        </v-layout>
      </td>

      <!--------------------------->
      <!-------- COLUMN3 --------->
      <!--------------------------->
      <td
        class="td--secondary column3"
        :rowspan="returnMatchingValueCheck() ? '2' : '1'"
      >
        <v-layout justify-start align-center>
          <span v-if="additionalChargeType">{{ chargeItem.longName }}</span>
          <span v-if="fuelLevyType">{{clientAppliedFuelSurchargeType}}</span>
          <span
            v-if="freightChargeType || isStandbyRate || isDemurrageRate || isTravelDelayRate"
            >{{ job.serviceTypeShortName}} - {{job.rateTypeName}}</span
          >
        </v-layout>
      </td>

      <!--------------------------->
      <!-------- COLUMN4 --------->
      <!--------------------------->
      <td
        class="td--secondary column4"
        v-if="!freightChargeType && !isStandbyRate && !isDemurrageRate && !isTravelDelayRate"
        :rowspan="returnMatchingValueCheck(true) ? '2' : '1'"
      >
        <span v-if="chargeItem && chargeItem.clientPricingType !== 3"
          >{{chargeItem.quantity}}</span
        >
        <span v-if="fuelLevyType"
          >${{displayCurrencyValue(clientFuelSurchargeQuantity)}}</span
        >

        <span v-if="chargeItem && chargeItem.clientPricingType === 3"
          >{{clientMultiplierQuantity}}</span
        >
      </td>
      <td class="td--secondary column4" v-if="freightChargeType">
        <span v-if="chargeItem.rate.rateTypeId === 1"
          >{{ finishedJobData.clientDurations.readableBilledDuration }}
        </span>
        <span v-if="chargeItem.rate.rateTypeId === 2"> Zone </span>
        <div v-if="chargeItem.rate.rateTypeId === 4">1</div>
        <div
          v-if="chargeItem.rate.rateTypeId === 5 || chargeItem.rate.rateTypeId === 3"
        >
          {{clientFreightQuantity}}
        </div>
        <span v-if="chargeItem.rate.rateTypeId === 6"> Quoted Rate </span>
      </td>

      <td class="td--secondary column4" v-if="isStandbyRate">
        {{finishedJobData.clientDurations.readableStandbyDuration}}
      </td>

      <td class="td--secondary column4" v-if="isDemurrageRate">
        {{demurrageQuantity.client}}
      </td>
      <td class="td--secondary column4" v-if="isTravelDelayRate">
        {{travelDelayTotalDuration.client}}
      </td>

      <!--------------------------->
      <!-------- COLUMN5 --------->
      <!--------------------------->
      <td class="td--secondary column5" v-if="additionalChargeType">
        {{chargeItem.returnReadableChargeValue('CLIENT')}}
      </td>

      <td class="td--secondary column5" v-if="fuelLevyType">
        <span
          >{{ displayCurrencyValue(chargeItem.rateBrackets[0].rate) }}%</span
        >
      </td>

      <td class="td--secondary column5" v-if="freightChargeType">
        {{ clientFreightRate }}
      </td>
      <td class="td--secondary column5" v-if="isStandbyRate">
        {{clientStandbyRate}}
      </td>

      <td class="td--secondary column5" v-if="isDemurrageRate">
        {{demurrageRate.client}}
      </td>
      <td class="td--secondary column5" v-if="isTravelDelayRate">
        {{travelDelayRate.client}}
      </td>

      <!--------------------------->
      <!-------- column6 --------->
      <!--------------------------->
      <td
        class="td--secondary column6"
        v-if="!isStandbyRate && !isDemurrageRate && !isTravelDelayRate"
      >
        ${{ displayCurrencyValue(currentClientRate) }}
      </td>

      <td class="td--secondary column6" v-if="isStandbyRate">
        ${{displayCurrencyValue(totals.subtotals.standbyChargeTotals.client)}}
      </td>

      <td class="td--secondary column6" v-if="isDemurrageRate">
        ${{displayCurrencyValue(totals.subtotals.demurrageChargeTotals.client)}}
      </td>

      <td class="td--secondary column6" v-if="isTravelDelayRate">
        ${{displayCurrencyValue(totals.subtotals.travelDelayChargeTotals?.client
        ?? 0)}}
      </td>

      <!--------------------------->
      <!-------- COLUMN7 --------->
      <!--------------------------->

      <!-- <td class="td--secondary column7" v-if="!isStandbyRate">
        <v-icon class="td--secondary__gst-icon" v-html="hasGstApplied() ?  'fal fa-check' : 'fal fa-times'"></v-icon>
      </td>

      <td class="td--secondary column7" v-if="isStandbyRate">
        <v-icon class="td--secondary__gst-icon" v-html="hasGstApplied() ?  'fal fa-check' : 'fal fa-times'"></v-icon>
      </td> -->
      <td class="td--secondary column7">
        <v-icon
          class="td--secondary__gst-icon"
          v-html="hasGstApplied() ?  'fal fa-check' : 'fal fa-times'"
        ></v-icon>
      </td>

      <!--------------------------->
      <!-------- COLUMN8 --------->
      <!--------------------------->
      <td
        class="td--secondary column8"
        :rowspan="freightRowSpan"
        v-if="freightMargin"
      >
        <span style="padding-right: 3px">
          $ {{displayCurrencyValue(freightMargin.dollar)}}
        </span>
        (
        <span
          style="opacity: 0.6"
          v-if="freightMargin.percent !== -Infinity && !isNaN(freightMargin.percent)"
        >
          {{freightMargin.percent}}%
        </span>
        <span
          style="opacity: 0.6; color: red"
          v-if="freightMargin.percent === -Infinity || isNaN(freightMargin.percent)"
        >
          N/A </span
        >)
      </td>
    </tr>

    <!--------------------------------------------------->
    <!-------- Driver Freight Rows --------->
    <!--------------------------------------------------->
    <tr class="subitem body" v-if="!outsideMetroType">
      <!--------------------------->
      <!-------- COLUMN2 --------->
      <!--------------------------->
      <td class="td--secondary column2">
        <v-layout justify-start align-center>
          <v-icon size="12" class="pr-1 td--secondary-icon">
            fal fa-steering-wheel
          </v-icon>
        </v-layout>
      </td>

      <!--------------------------->
      <!-------- COLUMN3 --------->
      <!--------------------------->
      <td class="td--secondary column3" v-if="!returnMatchingValueCheck()">
        <v-layout justify-start align-center>
          <span v-if="additionalChargeType">{{ chargeItem.longName }}</span>
          <span v-if="fuelLevyType">{{driverAppliedFuelSurchargeType}}</span>
          <span
            v-if="freightChargeType || isStandbyRate || isDemurrageRate || isTravelDelayRate"
            >{{job.fleetAssetServiceTypeShortName}} -
            {{job.fleetAssetRateTypeName}}</span
          >
        </v-layout>
      </td>

      <!--------------------------->
      <!-------- COLUMN4 --------->
      <!--------------------------->
      <td
        class="td--secondary column4"
        v-if="!freightChargeType && !returnMatchingValueCheck(true) && !isStandbyRate && !isDemurrageRate && !isTravelDelayRate"
      >
        <span v-if="chargeItem.driverPricingType !== 3"
          >{{chargeItem.quantity}}</span
        >
        <span v-if="chargeItem.driverPricingType === 3"
          >{{fleetAssetMultiplierQuantity}}</span
        >
        <span v-if="fuelLevyType"
          >${{displayCurrencyValue(driverStandbyFuelSurchargeQuantity)}}</span
        >
      </td>

      <td class="td--secondary column4" v-if="freightChargeType" rowspan="1">
        <span v-if="secondaryChargeItem.rate.rateTypeId === 1"
          >{{finishedJobData.fleetAssetDurations.readableBilledDuration}}
        </span>
        <span v-if="secondaryChargeItem.rate.rateTypeId === 2">Zone</span>
        <span v-if="secondaryChargeItem.rate.rateTypeId === 3"
          >{{fleetAssetFreightQuantity}}</span
        >
        <span v-if="secondaryChargeItem.rate.rateTypeId === 5"
          >{{fleetAssetUnitFreightQuantity}}</span
        >
        <span v-if="secondaryChargeItem.rate.rateTypeId === 6">
          Quoted Rate
        </span>
        <span v-if="secondaryChargeItem.rate.rateTypeId === 4"> 1 </span>
      </td>

      <td class="td--secondary column4" v-if="isStandbyRate">
        {{finishedJobData.fleetAssetDurations.readableStandbyDuration}}
      </td>
      <td class="td--secondary column4" v-if="isDemurrageRate">
        {{demurrageQuantity.fleetAsset}}
      </td>
      <td class="td--secondary column4" v-if="isTravelDelayRate">
        {{travelDelayTotalDuration.fleetAsset}}
      </td>

      <!--------------------------->
      <!-------- COLUMN5 --------->
      <!--------------------------->
      <td class="td--secondary column5" v-if="additionalChargeType">
        {{chargeItem.returnReadableChargeValue('FLEET_ASSET')}}
      </td>
      <td class="td--secondary column5" v-if="fuelLevyType">
        <span>{{ fleetAssetFuelSurcharge.rateDescription }} </span>
      </td>
      <td class="td--secondary column5" v-if="freightChargeType">
        {{ fleetAssetFreightRate }}
      </td>
      <td class="td--secondary column5" v-if="isStandbyRate">
        {{fleetAssetStandbyRate}}
      </td>

      <td class="td--secondary column5" v-if="isDemurrageRate">
        {{demurrageRate.fleetAsset}}
      </td>
      <td class="td--secondary column5" v-if="isTravelDelayRate">
        {{travelDelayRate.fleetAsset}}
      </td>

      <!--------------------------->
      <!-------- COLUMN6 --------->
      <!--------------------------->
      <td class="td--secondary column6" v-if="!isStandbyRate">
        ${{ currentFleetAssetRate.toFixed(2) }}
      </td>

      <td
        class="td--secondary column6"
        v-if="isStandbyRate && !additionalChargeType"
      >
        ${{displayCurrencyValue(totals.subtotals.standbyChargeTotals.fleetAsset)}}
      </td>
      <!--------------------------->
      <!-------- COLUMN7 --------->
      <!--------------------------->
      <td class="td--secondary column7" v-if="!isStandbyRate">
        <v-icon
          class="td--secondary__gst-icon"
          v-html="hasGstApplied(true) ?  'fal fa-check' : 'fal fa-times'"
        >
        </v-icon>
      </td>
      <td class="td--secondary column7" v-if="isStandbyRate">
        <v-icon
          class="td--secondary__gst-icon"
          v-html="hasGstApplied(true) ?  'fal fa-check' : 'fal fa-times'"
        >
        </v-icon>
      </td>
    </tr>

    <!--------------------------------------------------->
    <!-------- Outside Metro on Freight Charge Client --------->
    <!--------------------------------------------------->
    <tr
      class="subitem body"
      v-if="freightChargeType && outsideMetroType && outsideMetroCharge && (outsideMetroCharge.isOutsideMetroChargeClient || outsideMetroCharge.isOutsideMetroChargeDriver)"
    >
      <td class="td--secondary column1" rowspan="2">
        <v-layout nowrap align-center justify-start>
          <v-flex> Outside Metro </v-flex>
        </v-layout>
      </td>
      <!--------------------------->
      <!-------- COLUMN2 --------->
      <!--------------------------->
      <td class="td--secondary column2">
        <v-layout justify-start align-center>
          <v-icon size="12" class="pr-1 td--secondary-icon">
            fal fa-user
          </v-icon>
        </v-layout>
      </td>
      <!--------------------------->
      <!-------- COLUMN3 --------->
      <!--------------------------->
      <td class="td--secondary column3">
        <v-layout justify-start align-center>
          <span>Outside Metro</span>
        </v-layout>
      </td>

      <!--------------------------->
      <!-------- COLUMN4 --------->
      <!--------------------------->
      <td class="td--secondary column4">
        <v-layout justify-start align-center>
          <span>{{outsideMetroCharge.clientQuantity}}</span>
        </v-layout>
      </td>

      <!--------------------------->
      <!-------- COLUMN5 --------->
      <!--------------------------->
      <td class="td--secondary column5">
        <v-layout justify-start align-center>
          <span>{{outsideMetroCharge.clientRate}}%</span>
        </v-layout>
      </td>

      <!--------------------------->
      <!-------- COLUMN6 --------->
      <!--------------------------->
      <td class="td--secondary column6">
        <v-layout justify-start align-center>
          <span
            >${{displayCurrencyValue(outsideMetroCharge.clientAmount)}}</span
          >
        </v-layout>
      </td>

      <!--------------------------->
      <!-------- COLUMN7 --------->
      <!--------------------------->
      <td class="td--secondary column7">
        <v-layout justify-start align-center>
          <v-icon
            class="td--secondary__gst-icon"
            v-html="hasGstApplied() ?  'fal fa-check' : 'fal fa-times'"
          ></v-icon>
        </v-layout>
      </td>
      <td class="td--secondary column8" rowspan="2">
        <span style="padding-right: 3px">
          $ {{displayCurrencyValue(freightMargin.dollar)}}
        </span>
        (
        <span style="opacity: 0.6" v-if="freightMargin.percent !== -Infinity">
          {{freightMargin.percent}}%
        </span>
        <span
          style="opacity: 0.6; color: red"
          v-if="freightMargin.percent === -Infinity"
        >
          N/A </span
        >)
      </td>
    </tr>

    <!--------------------------------------------------->
    <!-------- Outside Metro on Freight Charge Driver --------->
    <!--------------------------------------------------->
    <tr
      class="subitem body"
      v-if="freightChargeType && outsideMetroType && outsideMetroCharge && (outsideMetroCharge.isOutsideMetroChargeClient || outsideMetroCharge.isOutsideMetroChargeDriver)"
    >
      <!--------------------------->
      <!-------- COLUMN2 --------->
      <!--------------------------->
      <td class="td--secondary column2">
        <v-layout justify-start align-center>
          <v-icon size="12" class="pr-1 td--secondary-icon">
            fal fa-steering-wheel
          </v-icon>
        </v-layout>
      </td>
      <!--------------------------->
      <!-------- COLUMN3 --------->
      <!--------------------------->
      <td class="td--secondary column3">
        <v-layout justify-start align-center>
          <span>Outside Metro</span>
        </v-layout>
      </td>

      <!--------------------------->
      <!-------- COLUMN4 --------->
      <!--------------------------->
      <td class="td--secondary column4">
        <v-layout justify-start align-center>
          <span>{{outsideMetroCharge.driverQuantity}}</span>
        </v-layout>
      </td>

      <!--------------------------->
      <!-------- COLUMN5 --------->
      <!--------------------------->
      <td class="td--secondary column5">
        <v-layout justify-start align-center>
          <span>{{outsideMetroCharge.driverRate}}%</span>
        </v-layout>
      </td>

      <!--------------------------->
      <!-------- COLUMN6 --------->
      <!--------------------------->
      <td class="td--secondary column6">
        <v-layout justify-start align-center>
          <span>${{outsideMetroCharge.driverAmount}}</span>
        </v-layout>
      </td>

      <!--------------------------->
      <!-------- COLUMN7 --------->
      <!--------------------------->
      <td class="td--secondary column7">
        <v-layout justify-start align-center>
          <v-icon
            class="td--secondary__gst-icon"
            v-html="hasGstApplied(true) ?  'fal fa-check' : 'fal fa-times'"
          >
          </v-icon>
        </v-layout>
      </td>
    </tr>

    <!--------------------------------------------------->
    <!-------- Equipment Hire on Freight Charge --------->
    <!--------------------------------------------------->

    <v-tooltip bottom>
      <template v-slot:activator="{ on }">
        <tr
          class="subitem body"
          v-if="freightChargeType && isEquipmentHire"
          v-on="on"
        >
          <!--------------------------->
          <!-------- COLUMN2 --------->
          <!--------------------------->
          <td class="td--secondary column2 funny-money">
            <v-layout justify-start align-center>
              <v-icon size="12" class="pr-1 td--secondary-icon">
                fad fa-file-contract
              </v-icon>
            </v-layout>
          </td>
          <!--------------------------->
          <!-------- COLUMN3 --------->
          <!--------------------------->
          <td class="td--secondary column3 funny-money">
            <v-layout justify-start align-center>
              <span>EQUIPMENT HIRE</span>
            </v-layout>
          </td>

          <!--------------------------->
          <!-------- COLUMN4 --------->
          <!--------------------------->
          <td class="td--secondary column4 funny-money">
            <v-layout justify-start align-center>
              <span>{{equipmentHireQuantity}}</span>
            </v-layout>
          </td>

          <!--------------------------->
          <!-------- COLUMN5 --------->
          <!--------------------------->
          <td class="td--secondary column5 funny-money">
            <v-layout justify-start align-center>
              <span v-if="equipmentHireQuantity > 1">Various</span>
              <span v-if="equipmentHireQuantity === 1"
                >${{equipmentHireRate}}</span
              >
            </v-layout>
          </td>

          <!--------------------------->
          <!-------- COLUMN6 --------->
          <!--------------------------->
          <td class="td--secondary column6 funny-money">
            <v-layout justify-start align-center>
              <span
                >${{displayCurrencyValue(totals.subtotals.equipmentHireTotal)}}</span
              >
            </v-layout>
          </td>

          <!--------------------------->
          <!-------- COLUMN7 --------->
          <!--------------------------->
          <td class="td--secondary column7 funny-money">
            <v-layout justify-start align-center>
              <v-icon class="td--secondary__gst-icon">fal fa-times</v-icon>
            </v-layout>
          </td>
        </tr>
      </template>
      <span>The margin for this job is an estimate only.</span>
    </v-tooltip>
  </table>
</section>
