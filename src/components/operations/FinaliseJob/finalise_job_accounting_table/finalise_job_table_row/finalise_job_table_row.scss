.finalise-job-table-row {
  font-size: $font-size-13;
  $table-base: #393939;
  $table-base-dark: #2b2b2b;

  table {
    width: 100%;
    border-spacing: 0px;
    border-collapse: collapse;
  }

  // tbody {
  //   padding-top: 20px !important;
  // }

  th,
  td {
    font-weight: unset;
    text-align: left;
    // padding-left: 8px;
  }

  td.column8,
  td.column9 {
    padding-left: 10px;
  }

  th {
    // padding-right: 20px;
    // padding-left: 6px;
    padding-top: 4px;
  }

  .td--subheader {
    padding: 10px 10px 6px 12px;
    color: #a7a7a7;
    font-weight: 500;
    font-size: 0.85em;
    letter-spacing: 0.02em;
    text-transform: uppercase;
  }

  .layout-padding {
    padding-left: 40px;
  }

  .left-align {
    width: 36px;
    height: 100%;
    background-color: #303030;
    position: absolute;
    left: 0px;
    top: 0px;
  }

  .column0 {
    width: 20px;
    // text-transform: uppercase;
  }

  .column1 {
    width: 14%;
    padding-left: 8px;
    transition: 0.15s;

    &.td--secondary {
      background-color: var(--background-color-300);
      border-right: 1px solid #484a51;
      &.selected {
        background-color: var(--background-color-300);
        border-right: 1px solid #484a51;
      }
    }
    // text-transform: uppercase;
  }
  .column2 {
    width: 1%;
  }
  .column3 {
    width: 17%;
  }
  .column4 {
    width: 10%;
  }

  .column5 {
    width: 10%;
  }

  .column6 {
    width: 12%;
  }

  .column7 {
    width: 8%;
    &.td--secondary {
      border-right: 1px solid #3f3530;
      padding-right: 2px;
    }
  }

  .column8 {
    width: 13%;
  }

  .td--secondary {
    position: relative;
    font-size: $font-size-14;
    font-weight: 500;
    color: var(--text-color);
    .td--secondary__gst-icon {
      font-size: $font-size-11;
      color: var(--light-text-color);
    }
    .td--secondary__check-icon {
      color: var(--light-text-color);
    }
  }

  .custom-table-body--secondary td {
    padding-top: 8px;
    padding-bottom: 6px;
  }
  .custom-table-body--secondary {
    transition: 0.15s;
  }

  .custom-table-body--secondary.inactive-charge {
    background-color: #202020;
    $inactive-text: #2b2b2b;
    cursor: default;
    .column1 {
      &.td--secondary {
        background-color: #202020;
        border-right-color: $inactive-text;
      }
    }

    .td--secondary {
      color: $inactive-text;
      .td--secondary__check-icon {
        color: $inactive-text;
      }
      .td--secondary-icon {
        color: $inactive-text;
      }
    }
  }

  .totals td {
    padding-right: 24px;
    padding-top: 14px;
    padding-bottom: 10px;
  }

  .td--secondary-icon {
    margin-left: 7px;
    margin-right: 8px;
    color: orange;
    font-weight: 200;
  }
}

.funny-money {
  background-color: rgba(209, 14, 14, 0.2) !important;

  border-top: 1px solid rgba(0, 0, 0, 0.12);
}
