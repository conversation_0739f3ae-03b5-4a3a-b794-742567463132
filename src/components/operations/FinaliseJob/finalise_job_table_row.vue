<!-- <template>
  <section class="finalise-job-table-row pt-0">
    <table class="custom-table-body--secondary">
      <~~-------------------------------------------------~~>
      <~~-------- Client Freight Rows -------~~>
      <~~-------------------------------------------------~~>
      <tr v-if="!outsideMetroType">
        <~~-------------------------~~>
        <~~-------- COLUMN 1 -------~~>
        <~~-------------------------~~>
        <td class="td--secondary column1" :rowspan="freightRowSpan">
          <v-layout nowrap align-center justify-start>
            <v-flex v-if="additionalChargeType && !isAdjustmentTypeCharge">
              {{
                returnAdditionalChargeType(
                  chargeItem._id,
                  chargeItem.typeReferenceId,
                )
              }}
            </v-flex>
            <v-flex v-if="fuelLevyType"> Fuel Levy </v-flex>
            <v-flex v-if="freightChargeType"> Freight Charge </v-flex>
            <v-flex v-if="isAdjustmentTypeCharge">
              <span v-if="chargeItem.clientPricingType === 6">Freight</span>
              <span v-else> Overall</span> Adjustment
            </v-flex>
            <v-flex v-if="isStandbyRate"> Standby Charge </v-flex>

            <v-flex v-if="isDemurrageRate"> Demurrage Charge </v-flex>
          </v-layout>
        </td>

        <~~-------------------------~~>
        <~~-------- COLUMN2 -------~~>
        <~~-------------------------~~>
        <td class="td--secondary column2">
          <v-layout justify-start align-center>
            <v-icon size="12" class="pr-1 td--secondary-icon">
              fal fa-user
            </v-icon>
          </v-layout>
        </td>

        <~~-------------------------~~>
        <~~-------- COLUMN3 -------~~>
        <~~-------------------------~~>
        <td
          class="td--secondary column3"
          :rowspan="returnMatchingValueCheck() ? '2' : '1'"
        >
          <v-layout justify-start align-center>
            <span v-if="additionalChargeType">{{ chargeItem.longName }}</span>
            <span v-if="fuelLevyType">{{
              clientAppliedFuelSurchargeType
            }}</span>
            <span v-if="freightChargeType || isStandbyRate || isDemurrageRate"
              >{{ job.serviceTypeShortName }} - {{ job.rateTypeName }}</span
            >
          </v-layout>
        </td>

        <~~-------------------------~~>
        <~~-------- COLUMN4 -------~~>
        <~~-------------------------~~>
        <td
          class="td--secondary column4"
          v-if="!freightChargeType && !isStandbyRate && !isDemurrageRate"
          :rowspan="returnMatchingValueCheck(true) ? '2' : '1'"
        >
          <span v-if="chargeItem && chargeItem.clientPricingType !== 3">{{
            chargeItem.quantity
          }}</span>
          <span v-if="fuelLevyType"
            >${{ DisplayCurrencyValue(clientFuelSurchargeQuantity) }}</span
          >

          <span v-if="chargeItem && chargeItem.clientPricingType === 3">{{
            clientMultiplierQuantity
          }}</span>
        </td>
        <td class="td--secondary column4" v-if="freightChargeType">
          <span v-if="chargeItem.rate.rateTypeId === 1"
            >{{ finishedJobData.clientDurations.readableBilledDuration }}
          </span>
          <span v-if="chargeItem.rate.rateTypeId === 2"> Zone </span>
          <div v-if="chargeItem.rate.rateTypeId === 4">1</div>
          <div
            v-if="
              chargeItem.rate.rateTypeId === 5 ||
              chargeItem.rate.rateTypeId === 3
            "
          >
            {{ clientFreightQuantity }}
          </div>
          <span v-if="chargeItem.rate.rateTypeId === 6"> Quoted Rate </span>
        </td>

        <td class="td--secondary column4" v-if="isStandbyRate">
          {{ finishedJobData.clientDurations.readableStandbyDuration }}
        </td>

        <td class="td--secondary column4" v-if="isDemurrageRate">
          {{ demurrageQuantity.client }}
        </td>

        <~~-------------------------~~>
        <~~-------- COLUMN5 -------~~>
        <~~-------------------------~~>
        <td class="td--secondary column5" v-if="additionalChargeType">
          <span
            v-if="
              chargeItem.clientPricingType === 0 &&
              chargeItem._id !== tollAdminAndHandlingId
            "
            >{{ chargeItem.clientRate.toFixed(1) }}%</span
          >

          <span
            v-if="
              chargeItem.clientPricingType === 1 ||
              chargeItem._id === tollAdminAndHandlingId
            "
            >${{ DisplayCurrencyValue(chargeItem.clientRate) }}</span
          >
          <span v-if="chargeItem.clientPricingType === 2"
            >{{ chargeItem.clientRate.toFixed(1) }}%</span
          >
          <span v-if="chargeItem.clientPricingType === 3"
            >${{ chargeItem.clientRate.toFixed(1) }}</span
          >
          <span
            v-if="
              chargeItem.clientPricingType === 5 ||
              chargeItem.clientPricingType === 6
            "
          >
            {{ returnRateString(currentClientRate) }}</span
          >
        </td>

        <td class="td--secondary column5" v-if="fuelLevyType">
          <span>{{ chargeItem.fuelSurchargeRate }}%</span>
        </td>

        <td class="td--secondary column5" v-if="freightChargeType">
          {{ clientFreightRate }}
        </td>
        <td class="td--secondary column5" v-if="isStandbyRate">
          {{ clientStandbyRate }}
        </td>

        <td class="td--secondary column5" v-if="isDemurrageRate">
          {{ demurrageRate.client }}
        </td>

        <~~-------------------------~~>
        <~~-------- column6 -------~~>
        <~~-------------------------~~>
        <td
          class="td--secondary column6"
          v-if="!isAdjustmentTypeCharge && !isStandbyRate && !isDemurrageRate"
        >
          ${{ DisplayCurrencyValue(currentClientRate) }}
        </td>
        <td class="td--secondary column6" v-if="isAdjustmentTypeCharge">
          {{ returnRateString(currentClientRate) }}
        </td>

        <td class="td--secondary column6" v-if="isStandbyRate">
          ${{
            DisplayCurrencyValue(totals.subtotals.standbyChargeTotals.client)
          }}
        </td>

        <td class="td--secondary column6" v-if="isDemurrageRate">
          ${{
            DisplayCurrencyValue(totals.subtotals.demurrageChargeTotals.client)
          }}
        </td>

        <~~-------------------------~~>
        <~~-------- COLUMN7 -------~~>
        <~~-------------------------~~>

        <~~-- <td class="td--secondary column7" v-if="!isStandbyRate">
        <v-icon class="td--secondary__gst-icon" v-html="hasGstApplied() ?  'fal fa-check' : 'fal fa-times'"></v-icon>
      </td>

      <td class="td--secondary column7" v-if="isStandbyRate">
        <v-icon class="td--secondary__gst-icon" v-html="hasGstApplied() ?  'fal fa-check' : 'fal fa-times'"></v-icon>
      </td> ~~>
        <td class="td--secondary column7">
          <v-icon class="td--secondary__gst-icon">
            {{ hasGstApplied() ? 'fal fa-check' : 'fal fa-times' }}
          </v-icon>
        </td>

        <~~-------------------------~~>
        <~~-------- COLUMN8 -------~~>
        <~~-------------------------~~>
        <td
          class="td--secondary column8"
          :rowspan="freightRowSpan"
          v-if="freightMargin"
        >
          <span style="padding-right: 3px">
            $ {{ DisplayCurrencyValue(freightMargin.dollar) }}
          </span>
          (
          <span
            style="opacity: 0.6"
            v-if="
              freightMargin.percent !== -Infinity &&
              !isNaN(freightMargin.percent)
            "
          >
            {{ freightMargin.percent }}%
          </span>
          <span
            style="opacity: 0.6; color: red"
            v-if="
              freightMargin.percent === -Infinity ||
              isNaN(freightMargin.percent)
            "
          >
            N/A </span
          >)
        </td>
      </tr>

      <~~-------------------------------------------------~~>
      <~~-------- Driver Freight Rows -------~~>
      <~~-------------------------------------------------~~>
      <tr class="subitem body" v-if="!outsideMetroType">
        <~~-------------------------~~>
        <~~-------- COLUMN2 -------~~>
        <~~-------------------------~~>
        <td class="td--secondary column2">
          <v-layout justify-start align-center>
            <v-icon size="12" class="pr-1 td--secondary-icon">
              fal fa-steering-wheel
            </v-icon>
          </v-layout>
        </td>

        <~~-------------------------~~>
        <~~-------- COLUMN3 -------~~>
        <~~-------------------------~~>
        <td class="td--secondary column3" v-if="!returnMatchingValueCheck()">
          <v-layout justify-start align-center>
            <span v-if="additionalChargeType">{{ chargeItem.longName }}</span>
            <span v-if="fuelLevyType">{{
              driverAppliedFuelSurchargeType
            }}</span>
            <span v-if="freightChargeType || isStandbyRate || isDemurrageRate"
              >{{ job.fleetAssetServiceTypeShortName }} -
              {{ job.fleetAssetRateTypeName }}</span
            >
          </v-layout>
        </td>

        <~~-------------------------~~>
        <~~-------- COLUMN4 -------~~>
        <~~-------------------------~~>
        <td
          class="td--secondary column4"
          v-if="
            !freightChargeType &&
            !returnMatchingValueCheck(true) &&
            !isStandbyRate &&
            !isDemurrageRate
          "
        >
          <span v-if="chargeItem.driverPricingType !== 3">{{
            chargeItem.quantity
          }}</span>
          <span v-if="chargeItem.driverPricingType === 3">{{
            fleetAssetMultiplierQuantity
          }}</span>
          <span v-if="fuelLevyType"
            >${{
              DisplayCurrencyValue(driverStandbyFuelSurchargeQuantity)
            }}</span
          >
        </td>

        <td class="td--secondary column4" v-if="freightChargeType" rowspan="1">
          <span v-if="secondaryChargeItem.rate.rateTypeId === 1"
            >{{ finishedJobData.fleetAssetDurations.readableBilledDuration }}
          </span>
          <span v-if="secondaryChargeItem.rate.rateTypeId === 2">Zone</span>
          <span v-if="secondaryChargeItem.rate.rateTypeId === 3">{{
            fleetAssetFreightQuantity
          }}</span>
          <span v-if="secondaryChargeItem.rate.rateTypeId === 5">{{
            fleetAssetUnitFreightQuantity
          }}</span>
          <span v-if="secondaryChargeItem.rate.rateTypeId === 6">
            Quoted Rate
          </span>
          <span v-if="secondaryChargeItem.rate.rateTypeId === 4"> 1 </span>
        </td>

        <td class="td--secondary column4" v-if="isStandbyRate">
          {{ finishedJobData.fleetAssetDurations.readableStandbyDuration }}
        </td>
        <td class="td--secondary column4" v-if="isDemurrageRate">
          {{ demurrageQuantity.fleetAsset }}
        </td>

        <~~-------------------------~~>
        <~~-------- COLUMN5 -------~~>
        <~~-------------------------~~>
        <td class="td--secondary column5" v-if="additionalChargeType">
          <span v-if="chargeItem.driverPricingType === 2"
            >{{ chargeItem.driverRate.toFixed(2) }}%</span
          >
          <span v-if="chargeItem.driverPricingType === 3"
            >${{ chargeItem.driverRate.toFixed(2) }}</span
          >
          <span
            v-if="
              chargeItem.driverPricingType === 1 ||
              chargeItem._id === tollAdminAndHandlingId
            "
            >${{ chargeItem.driverRate.toFixed(2) }}</span
          >
          <span
            v-if="
              chargeItem.driverPricingType === 0 &&
              chargeItem._id !== tollAdminAndHandlingId
            "
            >${{ chargeItem.driverRate.toFixed(2) }}%</span
          >
          <span
            v-if="
              chargeItem.driverPricingType === 5 ||
              chargeItem.driverPricingType === 6
            "
          >
            {{ returnRateString(currentFleetAssetRate) }}</span
          >
        </td>
        <td class="td--secondary column5" v-if="fuelLevyType">
          <span>{{ fleetAssetFuelSurcharge.fuelSurchargeRate }}%</span>
        </td>
        <td class="td--secondary column5" v-if="freightChargeType">
          {{ fleetAssetFreightRate }}
        </td>
        <td class="td--secondary column5" v-if="isStandbyRate">
          {{ fleetAssetStandbyRate }}
        </td>

        <td class="td--secondary column5" v-if="isDemurrageRate">
          {{ demurrageRate.fleetAsset }}
        </td>

        <~~-------------------------~~>
        <~~-------- COLUMN6 -------~~>
        <~~-------------------------~~>
        <td
          class="td--secondary column6"
          v-if="!isAdjustmentTypeCharge && !isStandbyRate"
        >
          ${{ currentFleetAssetRate.toFixed(2) }}
        </td>
        <td class="td--secondary column6" v-if="isAdjustmentTypeCharge">
          {{ returnRateString(currentFleetAssetRate) }}
        </td>

        <td
          class="td--secondary column6"
          v-if="isStandbyRate && !additionalChargeType"
        >
          ${{
            DisplayCurrencyValue(
              totals.subtotals.standbyChargeTotals.fleetAsset,
            )
          }}
        </td>
        <~~-------------------------~~>
        <~~-------- COLUMN7 -------~~>
        <~~-------------------------~~>
        <td class="td--secondary column7" v-if="!isStandbyRate">
          <v-icon class="td--secondary__gst-icon">
            {{ hasGstApplied(true) ? 'fal fa-check' : 'fal fa-times' }}
          </v-icon>
        </td>
        <td class="td--secondary column7" v-if="isStandbyRate">
          <v-icon class="td--secondary__gst-icon">
            {{ hasGstApplied(true) ? 'fal fa-check' : 'fal fa-times' }}
          </v-icon>
        </td>
      </tr>

      <~~-------------------------------------------------~~>
      <~~-------- Outside Metro on Freight Charge Client -------~~>
      <~~-------------------------------------------------~~>
      <tr
        class="subitem body"
        v-if="
          freightChargeType &&
          outsideMetroType &&
          outsideMetroCharge &&
          (outsideMetroCharge.isOutsideMetroChargeClient ||
            outsideMetroCharge.isOutsideMetroChargeDriver)
        "
      >
        <td class="td--secondary column1" rowspan="2">
          <v-layout nowrap align-center justify-start>
            <v-flex> Outside Metro </v-flex>
          </v-layout>
        </td>
        <~~-------------------------~~>
        <~~-------- COLUMN2 -------~~>
        <~~-------------------------~~>
        <td class="td--secondary column2">
          <v-layout justify-start align-center>
            <v-icon size="12" class="pr-1 td--secondary-icon">
              fal fa-user
            </v-icon>
          </v-layout>
        </td>
        <~~-------------------------~~>
        <~~-------- COLUMN3 -------~~>
        <~~-------------------------~~>
        <td class="td--secondary column3">
          <v-layout justify-start align-center>
            <span>Outside Metro</span>
          </v-layout>
        </td>

        <~~-------------------------~~>
        <~~-------- COLUMN4 -------~~>
        <~~-------------------------~~>
        <td class="td--secondary column4">
          <v-layout justify-start align-center>
            <span>{{ outsideMetroCharge.clientQuantity }}</span>
          </v-layout>
        </td>

        <~~-------------------------~~>
        <~~-------- COLUMN5 -------~~>
        <~~-------------------------~~>
        <td class="td--secondary column5">
          <v-layout justify-start align-center>
            <span>{{ outsideMetroCharge.clientRate }}%</span>
          </v-layout>
        </td>

        <~~-------------------------~~>
        <~~-------- COLUMN6 -------~~>
        <~~-------------------------~~>
        <td class="td--secondary column6">
          <v-layout justify-start align-center>
            <span
              >${{
                DisplayCurrencyValue(outsideMetroCharge.clientAmount)
              }}</span
            >
          </v-layout>
        </td>

        <~~-------------------------~~>
        <~~-------- COLUMN7 -------~~>
        <~~-------------------------~~>
        <td class="td--secondary column7">
          <v-layout justify-start align-center>
            <v-icon class="td--secondary__gst-icon">
              {{ hasGstApplied() ? 'fal fa-check' : 'fal fa-times' }}
            </v-icon>
          </v-layout>
        </td>
        <td class="td--secondary column8" rowspan="2">
          <span style="padding-right: 3px">
            $ {{ DisplayCurrencyValue(freightMargin.dollar) }}
          </span>
          (
          <span style="opacity: 0.6" v-if="freightMargin.percent !== -Infinity">
            {{ freightMargin.percent }}%
          </span>
          <span
            style="opacity: 0.6; color: red"
            v-if="freightMargin.percent === -Infinity"
          >
            N/A </span
          >)
        </td>
      </tr>

      <~~-------------------------------------------------~~>
      <~~-------- Outside Metro on Freight Charge Driver -------~~>
      <~~-------------------------------------------------~~>
      <tr
        class="subitem body"
        v-if="
          freightChargeType &&
          outsideMetroType &&
          outsideMetroCharge &&
          (outsideMetroCharge.isOutsideMetroChargeClient ||
            outsideMetroCharge.isOutsideMetroChargeDriver)
        "
      >
        <~~-------------------------~~>
        <~~-------- COLUMN2 -------~~>
        <~~-------------------------~~>
        <td class="td--secondary column2">
          <v-layout justify-start align-center>
            <v-icon size="12" class="pr-1 td--secondary-icon">
              fal fa-steering-wheel
            </v-icon>
          </v-layout>
        </td>
        <~~-------------------------~~>
        <~~-------- COLUMN3 -------~~>
        <~~-------------------------~~>
        <td class="td--secondary column3">
          <v-layout justify-start align-center>
            <span>Outside Metro</span>
          </v-layout>
        </td>

        <~~-------------------------~~>
        <~~-------- COLUMN4 -------~~>
        <~~-------------------------~~>
        <td class="td--secondary column4">
          <v-layout justify-start align-center>
            <span>{{ outsideMetroCharge.driverQuantity }}</span>
          </v-layout>
        </td>

        <~~-------------------------~~>
        <~~-------- COLUMN5 -------~~>
        <~~-------------------------~~>
        <td class="td--secondary column5">
          <v-layout justify-start align-center>
            <span>{{ outsideMetroCharge.driverRate }}%</span>
          </v-layout>
        </td>

        <~~-------------------------~~>
        <~~-------- COLUMN6 -------~~>
        <~~-------------------------~~>
        <td class="td--secondary column6">
          <v-layout justify-start align-center>
            <span>${{ outsideMetroCharge.driverAmount }}</span>
          </v-layout>
        </td>

        <~~-------------------------~~>
        <~~-------- COLUMN7 -------~~>
        <~~-------------------------~~>
        <td class="td--secondary column7">
          <v-layout justify-start align-center>
            <v-icon class="td--secondary__gst-icon">
              {{ hasGstApplied(true) ? 'fal fa-check' : 'fal fa-times' }}
            </v-icon>
          </v-layout>
        </td>
      </tr>

      <~~-------------------------------------------------~~>
      <~~-------- Equipment Hire on Freight Charge -------~~>
      <~~-------------------------------------------------~~>

      <v-tooltip bottom>
        <template v-slot:activator="{ on }">
          <tr
            class="subitem body"
            v-if="freightChargeType && isEquipmentHire"
            v-on="on"
          >
            <~~-------------------------~~>
            <~~-------- COLUMN2 -------~~>
            <~~-------------------------~~>
            <td class="td--secondary column2 funny-money">
              <v-layout justify-start align-center>
                <v-icon size="12" class="pr-1 td--secondary-icon">
                  fad fa-file-contract
                </v-icon>
              </v-layout>
            </td>
            <~~-------------------------~~>
            <~~-------- COLUMN3 -------~~>
            <~~-------------------------~~>
            <td class="td--secondary column3 funny-money">
              <v-layout justify-start align-center>
                <span>EQUIPMENT HIRE</span>
              </v-layout>
            </td>

            <~~-------------------------~~>
            <~~-------- COLUMN4 -------~~>
            <~~-------------------------~~>
            <td class="td--secondary column4 funny-money">
              <v-layout justify-start align-center>
                <span>{{ equipmentHireQuantity }}</span>
              </v-layout>
            </td>

            <~~-------------------------~~>
            <~~-------- COLUMN5 -------~~>
            <~~-------------------------~~>
            <td class="td--secondary column5 funny-money">
              <v-layout justify-start align-center>
                <span v-if="equipmentHireQuantity > 1">Various</span>
                <span v-if="equipmentHireQuantity === 1"
                  >${{ equipmentHireRate }}</span
                >
              </v-layout>
            </td>

            <~~-------------------------~~>
            <~~-------- COLUMN6 -------~~>
            <~~-------------------------~~>
            <td class="td--secondary column6 funny-money">
              <v-layout justify-start align-center>
                <span
                  >${{
                    DisplayCurrencyValue(totals.subtotals.equipmentHireTotal)
                  }}</span
                >
              </v-layout>
            </td>

            <~~-------------------------~~>
            <~~-------- COLUMN7 -------~~>
            <~~-------------------------~~>
            <td class="td--secondary column7 funny-money">
              <v-layout justify-start align-center>
                <v-icon class="td--secondary__gst-icon">fal fa-times</v-icon>
              </v-layout>
            </td>
          </tr>
        </template>
        <span>The margin for this job is an estimate only.</span>
      </v-tooltip>
    </table>
  </section>
</template>

<script setup lang="ts">
interface ClientAndFleetAsset {
  client: string;
  fleetAsset: string;
}
import {
  DisplayCurrencyValue,
  RoundCurrencyValue,
} from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import { isDistanceRateData } from '@/helpers/RateHelpers/RateDataHelpers';
import { isDistanceRateTypeObject } from '@/helpers/RateHelpers/RateTableItemHelpers';
import { AdditionalChargeItem } from '@/interface-models/AdditionalCharges/AdditionalChargeItem';
import { AdditionalChargeRateBasis } from '@/interface-models/AdditionalCharges/AdditionalChargeRateBasis';
import { AdditionalChargeType } from '@/interface-models/AdditionalCharges/AdditionalChargeType';
import { pricingTypes } from '@/interface-models/AdditionalCharges/pricingTypes';
import FinishedJobData from '@/interface-models/Generic/Accounting/FinishedJobDetails/FinishedJobData';
import { AdditionalChargeSubtotal } from '@/interface-models/Generic/Accounting/JobAccountingTotals/AdditionalChargeSubtotal';
import JobAccountingMargin from '@/interface-models/Generic/Accounting/JobAccountingTotals/JobAccountingMargin';
import JobPrimaryRate from '@/interface-models/Generic/Accounting/JobPrimaryRate';
import {
  RateMultipliers,
  rateMultipliers,
} from '@/interface-models/Generic/ServiceTypes/RateMultipliers';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import ShortLongName from '@/interface-models/Generic/ShortLongName/ShortLongName';
import UnitRateData from '@/interface-models/Jobs/FinishedJobDetails/UnitRateData';
import ZonedUnitRateData from '@/interface-models/Jobs/FinishedJobDetails/ZonedUnitRateData';
import ZoneRateData from '@/interface-models/Jobs/FinishedJobDetails/zoneRateData';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import { DemurrageRateData } from '@/interface-models/ServiceRates/Demurrage/DemurrageRateData';
import FleetAssetFuelSurchargeRate from '@/interface-models/ServiceRates/FleetAssetOwner/AdditionalCharges/FuelSurcharge/FleetAssetFuelSurchargeRate';
import { FuelSurchargeBreakdown } from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeBreakdown';
import applicableFuelSurcharges from '@/interface-models/ServiceRates/FuelSurcharges/applicableFuelSurcharges';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import RangedRate from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/RangedRate';
import PointToPointRateType from '@/interface-models/ServiceRates/ServiceTypes/PointToPointServiceRate/PointToPointRateType';
import TimeRateType from '@/interface-models/ServiceRates/ServiceTypes/TimeServiceRate/TimeRateType';
import TripRate from '@/interface-models/ServiceRates/ServiceTypes/TripRate/TripRate';
import UnitRate from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRate';
import ZoneRateType from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRateType';
import { useRootStore } from '@/store/modules/RootStore';
import moment from 'moment-timezone';
import { computed, ComputedRef } from 'vue';

const props = withDefaults(
  defineProps<{
    chargeItem: AdditionalChargeItem | ClientFuelSurchargeRate | JobPrimaryRate;
    secondaryChargeItem: JobPrimaryRate;
    fuelLevyType?: boolean;
    additionalChargeType?: boolean;
    freightChargeType?: boolean;
    outsideMetroType?: boolean;
    clientRate: any;
    fleetAssetRate: any;
    job: JobDetails;
    fleetAssetFuelSurcharge: FleetAssetFuelSurchargeRate;
    clientGstRegistered?: boolean;
    fleetAssetGstRegistered: boolean;
    finishedJobData: FinishedJobData;
    isStandbyRate?: boolean;
    isEquipmentHire: boolean;
    outsideMetroCharge: any;
    isDemurrageRate?: boolean;
  }>(),
  {
    fuelLevyType: false,
    additionalChargeType: false,
    freightChargeType: false,
    outsideMetroType: false,
    clientGstRegistered: true,
    isStandbyRate: false,
    isDemurrageRate: false,
  },
);

const additionalChargeItems: ComputedRef<AdditionalChargeItem[]> = computed(
  () => {
    return useRootStore().additionalChargeItemList;
  },
);

const additionalChargeTypes: ComputedRef<AdditionalChargeType[]> = computed(
  () => {
    return useRootStore().additionalChargeTypeList;
  },
);

function returnAdditionalChargeType(id: string, typeId: string): string {
  const foundChargeItem = additionalChargeItems.value.find(
    (item) => item._id === id,
  );
  if (foundChargeItem) {
    return foundChargeItem.longName;
  } else {
    const foundChargeType = additionalChargeTypes.value.find(
      (x: AdditionalChargeType) => x._id === typeId,
    );

    if (foundChargeType) {
      return foundChargeType.longName;
    }
  }
  return '';
}

const clientAppliedFuelSurchargeType: ComputedRef<string> = computed(() => {
  if (!props.fuelLevyType || props.job.accounting.clientRates.length === 0) {
    return '';
  }
  const rate: RateTableItems = props.job.accounting.clientRates[0].rate;
  return getAppliedFuelSurchargeTypeDisplayName(rate, false);
});

const driverAppliedFuelSurchargeType: ComputedRef<string> = computed(() => {
  if (
    !props.fuelLevyType ||
    props.job.accounting.fleetAssetRates.length === 0
  ) {
    return '';
  }
  const rate: RateTableItems = props.job.accounting.fleetAssetRates[0].rate;
  const zeroPercentClientRate: boolean =
    props.job.accounting.additionalCharges.clientFuelSurcharge &&
    props.job.accounting.additionalCharges.clientFuelSurcharge
      .fuelSurchargeRate === 0
      ? true
      : false;
  return getAppliedFuelSurchargeTypeDisplayName(rate, zeroPercentClientRate);
});

const demurrageQuantity: ComputedRef<ClientAndFleetAsset> = computed(() => {
  const clientDemurrageTimes: number[] =
    props.finishedJobData.clientDemurrageBreakdown.map(
      (x: DemurrageRateData) => x.demurrageDurationInMilliseconds,
    );
  const fleetAssetDemurrageTimes: number[] =
    props.finishedJobData.fleetAssetDemurrageBreakdown.map(
      (x: DemurrageRateData) => x.demurrageDurationInMilliseconds,
    );

  const clientTotalDurationInMilliseconds = clientDemurrageTimes.reduce(
    (accumulator, curr) => accumulator + curr,
  );
  const fleetAssetDurationInMilliseconds = fleetAssetDemurrageTimes.reduce(
    (accumulator, curr) => accumulator + curr,
  );

  return {
    client:
      moment.duration(clientTotalDurationInMilliseconds).hours() +
      'h ' +
      moment.duration(clientTotalDurationInMilliseconds).minutes() +
      'm',
    fleetAsset:
      moment.duration(fleetAssetDurationInMilliseconds).hours() +
      'h ' +
      moment.duration(fleetAssetDurationInMilliseconds).minutes() +
      'm',
  };
});

const demurrageRate: ComputedRef<ClientAndFleetAsset> = computed(() => {
  const clientDemurrageRates: number[] =
    props.finishedJobData.clientDemurrageBreakdown.map(
      (x: DemurrageRateData) => x.rate,
    );
  const fleetAssetDemurrageRates: number[] =
    props.finishedJobData.fleetAssetDemurrageBreakdown.map(
      (x: DemurrageRateData) => x.rate,
    );

  const clientIsVariousRates = !clientDemurrageRates.every(
    (val, i, arr) => val === arr[0],
  );
  const fleetAssetIsVariousRates = !fleetAssetDemurrageRates.every(
    (val, i, arr) => val === arr[0],
  );

  const clientRateTypeId = (props.chargeItem as JobPrimaryRate).rate.rateTypeId;
  const fleetAssetRateTypeId = (props.secondaryChargeItem as JobPrimaryRate)
    .rate.rateTypeId;
  let clientRate = 0;
  let fleetAssetRate = 0;

  if (!clientIsVariousRates && clientDemurrageRates.length > 0) {
    if (clientRateTypeId === 4) {
      clientRate = (
        (props.chargeItem as JobPrimaryRate).rate
          .rateTypeObject as PointToPointRateType
      ).demurrage.rate;
    } else {
      clientRate = clientDemurrageRates[0];
    }
  }

  if (!fleetAssetIsVariousRates && fleetAssetDemurrageRates.length > 0) {
    if (fleetAssetRateTypeId === 4) {
      fleetAssetRate = (
        (props.secondaryChargeItem as JobPrimaryRate).rate
          .rateTypeObject as PointToPointRateType
      ).demurrage.rate;
    } else {
      fleetAssetRate = fleetAssetDemurrageRates[0];
    }
  }

  return {
    client: clientIsVariousRates
      ? 'Various'
      : '$' + DisplayCurrencyValue(clientRate) + '/ph',
    fleetAsset: fleetAssetIsVariousRates
      ? 'Various'
      : '$' + DisplayCurrencyValue(fleetAssetRate) + '/ph',
  };
});

function getAppliedFuelSurchargeTypeDisplayName(
  rate: RateTableItems,
  zeroPercentClientRate: boolean,
): string {
  let displayName = 'Various';
  let appliedFuelSurchargeId: number = -1;
  switch (rate.rateTypeId) {
    case 1:
      appliedFuelSurchargeId = (rate.rateTypeObject as TimeRateType)
        .appliedFuelSurchargeId;
      break;
    case 6:
      appliedFuelSurchargeId = rate.fuelSurcharge ? 1 : 3;
      break;
  }

  const appliedFuelSurcharge = applicableFuelSurcharges.find(
    (x: ShortLongName) => x.id === appliedFuelSurchargeId,
  );

  if (!appliedFuelSurcharge) {
    return displayName;
  }

  switch (appliedFuelSurchargeId) {
    case 1:
      displayName = 'Fuel Applied';
      break;
    case 2:
      if (zeroPercentClientRate) {
        // tslint:disable-next-line
        displayName = "Don't Apply";
      } else {
        displayName = 'Fuel Applied';
      }

      break;
    case 3:
      // tslint:disable-next-line
      displayName = "Don't Apply";
      break;
  }
  return displayName;
}

const fleetAssetUnitFreightQuantity: ComputedRef<string> = computed(() => {
  return (
    '$' +
    DisplayCurrencyValue(
      props.job.accounting.totals.subtotals.freightCharges.client,
    )
  );
});

const clientFuelSurchargeQuantity: ComputedRef<number> = computed(() => {
  if (!props.fuelLevyType || props.job.accounting.clientRates.length === 0) {
    return 0;
  }
  const rate: RateTableItems = props.job.accounting.clientRates[0].rate;

  let freight = props.job.accounting.totals.subtotals.freightCharges.client;

  switch (rate.rateTypeId) {
    case 2:
      freight = 0;
      const zoneRateData = props.job.accounting.finishedJobData
        .clientRateData as ZoneRateData[];
      for (let i = 0; i < zoneRateData.length - 1; i++) {
        const zone: ZoneRateType | undefined = (
          props.job.accounting.clientRates[0].rate
            .rateTypeObject as ZoneRateType[]
        ).find((z: ZoneRateType) => zoneRateData[i].zoneId === z.zone);
        if (zone && zone.appliedFuelSurchargeId === 1) {
          freight += zone.rate;
        }
      }
      break;
    case 5:
      freight = 0;
      const unitRateData: ZonedUnitRateData[] = (
        props.job.accounting.finishedJobData.clientRateData as UnitRateData
      ).zonedUnitRateData;
      for (let i = 0; i < unitRateData.length; i++) {
        const unit: UnitRate | undefined = (
          props.job.accounting.clientRates[0].rate.rateTypeObject as UnitRate[]
        ).find((u: UnitRate) => unitRateData[i].zoneId === u.zoneId);
        if (unit && unit.appliedFuelSurchargeId === 1) {
          freight += unitRateData[i].freightChargesTotalExclGst;
          if (unit.fuelIsAppliedToFlagFalls) {
            freight += unitRateData[i].loadChargesTotalExclGst;
          }
        }
      }
      break;
    case 4:
      freight = 0;
      const pointToPointRate: PointToPointRateType = props.job.accounting
        .clientRates[0].rate.rateTypeObject as PointToPointRateType;
      freight =
        pointToPointRate.appliedFuelSurchargeId === 1
          ? props.job.accounting.totals.subtotals.freightCharges.client
          : 0;
      break;
  }

  const outsideMetro =
    props.job.accounting.totals.subtotals.outsideMetroChargeTotals.client;
  const standby =
    props.job.accounting.totals.subtotals.standbyChargeTotals.client;
  const freightAdjustment: number =
    props.job.accounting.totals.subtotals.freightAdjustmentCharges.client;
  const clientDemurrageRates =
    props.job.accounting.finishedJobData.clientDemurrageBreakdown;
  const clientDemurrageFuelSurcharges =
    props.job.accounting.finishedJobData.clientDemurrageFuelSurchargeBreakdown;
  let demurrageCharge: number = 0;
  for (const demurrage of clientDemurrageRates) {
    const demurrageFuelBreakdown = clientDemurrageFuelSurcharges.find(
      (x: FuelSurchargeBreakdown) => x.pudId === demurrage.pudId,
    );
    if (!demurrageFuelBreakdown) {
      continue;
    }
    if (
      demurrage.demurrageFuelSurchargeApplies &&
      demurrageFuelBreakdown.fuelSurchargeRate > 0 &&
      demurrage.demurrageChargeExclGst > 0
    ) {
      demurrageCharge += demurrage.demurrageChargeExclGst;
    }
  }
  return getStandbyFuelSurchargeQuantity(
    rate,
    freight,
    outsideMetro,
    standby,
    demurrageCharge,
    freightAdjustment,
  );
});

const driverStandbyFuelSurchargeQuantity: ComputedRef<number> = computed(() => {
  if (
    !props.fuelLevyType ||
    props.job.accounting.fleetAssetRates.length === 0
  ) {
    return 0;
  }
  const rate: RateTableItems = props.job.accounting.fleetAssetRates[0].rate;

  let freight = props.job.accounting.totals.subtotals.freightCharges.fleetAsset;

  const clientFuelSurchargeRate = (props.chargeItem as ClientFuelSurchargeRate)
    .fuelSurchargeRate;
  switch (rate.rateTypeId) {
    case 2:
      freight = 0;
      const zoneRateData = props.job.accounting.finishedJobData
        .fleetAssetRateData as ZoneRateData[];
      for (let i = 0; i < zoneRateData.length; i++) {
        const zone: ZoneRateType = (
          props.job.accounting.fleetAssetRates[0].rate
            .rateTypeObject as ZoneRateType[]
        )[0];
        if (
          zone &&
          (zone.appliedFuelSurchargeId === 1 ||
            (zone.appliedFuelSurchargeId === 2 && clientFuelSurchargeRate > 0))
        ) {
          freight +=
            props.job.accounting.totals.subtotals.freightCharges.fleetAsset;
        }
      }
      break;
    case 5:
      freight = 0;
      const unit: UnitRate | undefined = (
        props.job.accounting.fleetAssetRates[0].rate
          .rateTypeObject as UnitRate[]
      )[0];
      if (
        unit &&
        (unit.appliedFuelSurchargeId === 1 ||
          (unit.appliedFuelSurchargeId === 2 && clientFuelSurchargeRate > 0))
      ) {
        freight +=
          props.job.accounting.totals.subtotals.freightCharges.fleetAsset;
      }
      break;
    case 4:
      freight = 0;
      const pointToPointRate: PointToPointRateType = props.job.accounting
        .fleetAssetRates[0].rate.rateTypeObject as PointToPointRateType;
      freight =
        pointToPointRate.appliedFuelSurchargeId === 1 ||
        (pointToPointRate.appliedFuelSurchargeId === 2 &&
          clientFuelSurchargeRate > 0)
          ? props.job.accounting.totals.subtotals.freightCharges.fleetAsset
          : 0;
      break;
  }

  const outsideMetro =
    props.job.accounting.totals.subtotals.outsideMetroChargeTotals.fleetAsset;
  const standby =
    props.job.accounting.totals.subtotals.standbyChargeTotals.fleetAsset;

  const freightAdjustment: number =
    props.job.accounting.totals.subtotals.freightAdjustmentCharges.fleetAsset;

  const driverDemurrageRates =
    props.job.accounting.finishedJobData.fleetAssetDemurrageBreakdown;
  const driverDemurrageFuelSurcharges =
    props.job.accounting.finishedJobData
      .fleetAssetDemurrageFuelSurchargeBreakdown;

  let demurrageCharge: number = 0;

  for (const demurrage of driverDemurrageRates) {
    const demurrageFuelBreakdown = driverDemurrageFuelSurcharges.find(
      (x: FuelSurchargeBreakdown) => x.pudId === demurrage.pudId,
    );

    if (!demurrageFuelBreakdown) {
      continue;
    }
    if (
      demurrage.demurrageFuelSurchargeApplies &&
      demurrageFuelBreakdown.fuelSurchargeRate > 0 &&
      demurrage.demurrageChargeExclGst > 0
    ) {
      demurrageCharge += demurrage.demurrageChargeExclGst;
    }
  }

  return getStandbyFuelSurchargeQuantity(
    rate,
    freight,
    outsideMetro,
    standby,
    demurrageCharge,
    freightAdjustment,
  );
});

// returns the total charge that fuel surcharge is applied against.
function getStandbyFuelSurchargeQuantity(
  rate: RateTableItems,
  freightCharge: number,
  outsideMetroCharge: number,
  strandbyCharge: number,
  demurrageCharge: number,
  freightAdjustment: number,
): number {
  let quantity: number = RoundCurrencyValue(
    freightCharge + outsideMetroCharge + demurrageCharge + freightAdjustment,
  );

  switch (rate.rateTypeId) {
    case 1:
      const standbyFuelSurchargeApplies = (rate.rateTypeObject as TimeRateType)
        .standbyFuelSurchargeApplies;
      if (standbyFuelSurchargeApplies) {
        quantity = RoundCurrencyValue(
          freightCharge +
            outsideMetroCharge +
            strandbyCharge +
            freightAdjustment,
        );
      }
      break;
  }
  return quantity;
}

function returnMatchingValueCheck(quantity: boolean = false): boolean {
  if (!quantity) {
    if (props.freightChargeType) {
      if (
        props.chargeItem instanceof JobPrimaryRate &&
        props.chargeItem.rate.rateTypeId ===
          props.secondaryChargeItem.rate.rateTypeId
      ) {
        return true;
      } else {
        return false;
      }
    } else if (props.additionalChargeType) {
      if (props.chargeItem instanceof AdditionalChargeItem) {
        return true;
      } else {
        return false;
      }
    } else if (props.fuelLevyType) {
      if (props.chargeItem instanceof ClientFuelSurchargeRate) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  } else {
    if (props.freightChargeType) {
      return true;
    } else {
      return false;
    }
  }
}

const freightRowSpan: ComputedRef<number> = computed(() => {
  let rowSpan = 3;

  if (props.freightChargeType) {
    if (
      props.outsideMetroCharge &&
      props.outsideMetroCharge.isOutsideMetroChargeDriver
    ) {
      rowSpan++;
    }
    if (
      props.outsideMetroCharge &&
      props.outsideMetroCharge.isOutsideMetroChargeClient
    ) {
      rowSpan++;
    }

    if (props.isEquipmentHire) {
      rowSpan++;
    }
  }

  return rowSpan;
});

const clientStandbyRate: ComputedRef<string> = computed(() => {
  const foundMultiplier = rateMultipliers.find(
    (x: RateMultipliers) => x.id === props.clientRate.standbyMultiplier,
  );
  if (!foundMultiplier) {
    return '0.00';
  }

  if (!props.isStandbyRate) {
    return '0.00';
  }

  let rateString = '';

  rateString +=
    '$' +
    DisplayCurrencyValue(props.clientRate.standbyRate) +
    (foundMultiplier.longName.toLowerCase() === 'hour'
      ? '/ph'
      : ' per ' + foundMultiplier.longName.toLowerCase());
  return rateString;
});

const fleetAssetStandbyRate: ComputedRef<string> = computed(() => {
  const foundMultiplier = rateMultipliers.find(
    (x: RateMultipliers) => x.id === props.fleetAssetRate.standbyMultiplier,
  );
  if (!props.isStandbyRate) {
    return '0.00';
  }
  if (!foundMultiplier) {
    return '0.00';
  }

  let rateString = '';

  rateString +=
    '$' +
    DisplayCurrencyValue(props.fleetAssetRate.standbyRate) +
    (foundMultiplier.longName.toLowerCase() === 'hour'
      ? '/ph'
      : ' per ' + foundMultiplier.longName.toLowerCase());
  return rateString;
});

const clientFreightQuantity: ComputedRef<string> = computed(() => {
  if (!props.job.accounting.finishedJobData) {
    return '';
  }

  if (
    props.job.accounting?.clientRates?.[0]?.rate.rateTypeId === JobRateType.UNIT
  ) {
    const unitRateData = props.job.accounting.finishedJobData
      .clientRateData as UnitRateData;
    const unitCount = unitRateData.zonedUnitRateData.reduce(
      (count: number, currentValue: ZonedUnitRateData) => {
        return count + currentValue.quantityOfUnits;
      },
      0,
    );
    return `${unitCount}`;
  } else if (
    isDistanceRateData(
      props.job.accounting?.clientRates?.[0]?.rate.rateTypeId,
      props.job.accounting.finishedJobData.clientRateData,
    )
  ) {
    return `${props.job.accounting.finishedJobData.clientRateData.calculatedTotalDistance}km`;
  }

  return '0';
});
const clientFreightRate: ComputedRef<string> = computed(() => {
  if (!props.freightChargeType) {
    return '';
  }
  try {
    const jobPrimaryRate = props.chargeItem as JobPrimaryRate;
    const finishedJobData = props.job.accounting.finishedJobData;

    switch (jobPrimaryRate.rate.rateTypeId) {
      case JobRateType.UNIT:
      case JobRateType.ZONE:
        return 'Various;';
      case JobRateType.DISTANCE:
        if (
          isDistanceRateData(
            jobPrimaryRate.rate.rateTypeId,
            finishedJobData.clientRateData,
          ) &&
          isDistanceRateTypeObject(
            jobPrimaryRate.rate.rateTypeId,
            jobPrimaryRate.rate.rateTypeObject,
          )
        ) {
          if (finishedJobData.clientRateData.rangeSubtotals.length === 1) {
            const rangeSubtotal =
              finishedJobData.clientRateData.rangeSubtotals[0];
            const foundRate = jobPrimaryRate.rate.rateTypeObject.rates.find(
              (x: RangedRate) => x.id === rangeSubtotal.rangeRateId,
            );
            return foundRate
              ? `$${DisplayCurrencyValue(foundRate.rate)}/km`
              : '';
          } else {
            return 'Various';
          }
        }
        return 'DISTANCE';
      case JobRateType.TIME:
        return `$${DisplayCurrencyValue(
          (jobPrimaryRate.rate.rateTypeObject as TimeRateType).rate,
        )}`;
      case JobRateType.TRIP:
        return `$${DisplayCurrencyValue(
          (jobPrimaryRate.rate.rateTypeObject as TripRate).rate,
        )}`;
      default:
        return '';
    }
  } catch (error) {
    logConsoleError('Something went wrong displaying client rate', error);
    return 'N/A';
  }
});

const fleetAssetFreightQuantity: ComputedRef<string> = computed(() => {
  if (!props.job.accounting.finishedJobData) {
    return '';
  }
  // TODO - combine other rate types into this getter
  if (
    isDistanceRateData(
      props.job.accounting?.fleetAssetRates?.[0]?.rate.rateTypeId,
      props.job.accounting.finishedJobData.fleetAssetRateData,
    )
  ) {
    return `${props.job.accounting.finishedJobData.fleetAssetRateData.calculatedTotalDistance}km`;
  }

  return '0';
});

const fleetAssetFreightRate: ComputedRef<string> = computed(() => {
  if (!props.freightChargeType) {
    return '';
  }
  try {
    const jobPrimaryRate = props.secondaryChargeItem as JobPrimaryRate;
    const finishedJobData = props.job.accounting.finishedJobData;

    switch (jobPrimaryRate.rate.rateTypeId) {
      case JobRateType.UNIT:
        return `${
          (props.secondaryChargeItem.rate.rateTypeObject as UnitRate[])[0]
            .fleetAssetPercentage
        }%`;
      case JobRateType.ZONE:
        return `${
          (props.secondaryChargeItem.rate.rateTypeObject as ZoneRateType[])[0]
            .percentage
        }%`;
      case JobRateType.DISTANCE:
        if (
          isDistanceRateData(
            jobPrimaryRate.rate.rateTypeId,
            finishedJobData.fleetAssetRateData,
          ) &&
          isDistanceRateTypeObject(
            jobPrimaryRate.rate.rateTypeId,
            jobPrimaryRate.rate.rateTypeObject,
          )
        ) {
          if (finishedJobData.fleetAssetRateData.rangeSubtotals.length === 1) {
            const rangeSubtotal =
              finishedJobData.fleetAssetRateData.rangeSubtotals[0];
            const foundRate = jobPrimaryRate.rate.rateTypeObject.rates.find(
              (x: RangedRate) => x.id === rangeSubtotal.rangeRateId,
            );
            return foundRate
              ? `$${DisplayCurrencyValue(foundRate.rate)}/km`
              : '';
          } else {
            return 'Various';
          }
        }
        return 'DISTANCE';
      case JobRateType.TIME:
        return `$${DisplayCurrencyValue(
          (jobPrimaryRate.rate.rateTypeObject as TimeRateType).rate,
        )}`;
      case JobRateType.TRIP:
        return `$${DisplayCurrencyValue(
          (jobPrimaryRate.rate.rateTypeObject as TripRate).rate,
        )}`;
      default:
        return '';
    }
  } catch (error) {
    logConsoleError('Something went wrong displaying fleet asset rate', error);
    return 'N/A';
  }
});

const totals: ComputedRef<any> = computed(() => {
  return props.job.accounting.totals;
});

const equipmentHireQuantity: ComputedRef<number | undefined> = computed(() => {
  if (props.isEquipmentHire) {
    return totals.value.subtotals.equipmentHireCharges.length;
  }
  return undefined;
});

const equipmentHireRate: ComputedRef<string | undefined> = computed(() => {
  if (equipmentHireQuantity.value === 1) {
    const equipment = totals.value.subtotals.equipmentHireCharges[0];
    let chargedAt: string = '';
    chargedAt += equipment.contractJobRate.jobRate + ' per ';
    const foundMultiplier = rateMultipliers.find(
      (x: RateMultipliers) =>
        x.id === equipment.contractJobRate.jobRateMultiplier,
    );
    if (foundMultiplier) {
      chargedAt += foundMultiplier.longName;
    }
    return chargedAt;
  }
  return undefined;
});

const freightMargin: ComputedRef<JobAccountingMargin> = computed(() => {
  if (
    (currentClientRate.value || currentClientRate.value === 0) &&
    (currentFleetAssetRate.value || currentFleetAssetRate.value === 0)
  ) {
    if (props.freightChargeType) {
      if (!props.outsideMetroType) {
        const clientFreightTotal = totals.value.subtotals.freightCharges.client;
        const fleetFreightTotal =
          totals.value.subtotals.freightCharges.fleetAsset;
        return new JobAccountingMargin(
          RoundCurrencyValue(clientFreightTotal - fleetFreightTotal),
          RoundCurrencyValue(
            ((clientFreightTotal - fleetFreightTotal) / clientFreightTotal) *
              100,
          ),
        );
      } else {
        const clientOutsideMetroTotal = props.outsideMetroCharge.clientAmount;
        const fleetOutsideMetroTotal = props.outsideMetroCharge.driverAmount;

        return new JobAccountingMargin(
          RoundCurrencyValue(clientOutsideMetroTotal - fleetOutsideMetroTotal),
          RoundCurrencyValue(
            ((clientOutsideMetroTotal - fleetOutsideMetroTotal) /
              clientOutsideMetroTotal) *
              100,
          ),
        );
      }
    } else {
      const clientFreight = currentClientRate.value;
      const fleetAssetFreight = currentFleetAssetRate.value;
      const overallMargin = new JobAccountingMargin(
        RoundCurrencyValue(clientFreight - fleetAssetFreight),
        RoundCurrencyValue(
          ((clientFreight - fleetAssetFreight) / clientFreight) * 100,
        ),
      );
      return overallMargin;
    }
  }
  return new JobAccountingMargin(0, 0);
});

const additionalChargeItemList: ComputedRef<any> = computed(() => {
  return useRootStore().additionalChargeItemList;
});

const tollAdminAndHandlingId: ComputedRef<string | undefined> = computed(() => {
  return useRootStore().tollAdminAndHandlingId;
});

const currentClientRate: ComputedRef<number> = computed(() => {
  if (props.additionalChargeType) {
    const chargeItem: AdditionalChargeItem =
      props.chargeItem as AdditionalChargeItem;

    if (
      chargeItem.isClientFreightAdjustment &&
      chargeItem.client.chargeBasis === AdditionalChargeRateBasis.PERCENTAGE
    ) {
      const isTollAdminAndHandling =
        chargeItem._id === tollAdminAndHandlingId.value;
      return !isTollAdminAndHandling
        ? (chargeItem.client.charge / 100) *
            totals.value.subtotals.freightCharges.client
        : chargeItem.client.charge;
    }
    if (
      chargeItem.isClientOverallAdjustment &&
      chargeItem.client.chargeBasis === AdditionalChargeRateBasis.FIXED
    ) {
      const chargeCategory =
        props.job.accounting.totals.subtotals.additionalChargeItems.find(
          (x: AdditionalChargeSubtotal) =>
            x.chargeRef === chargeItem.typeReferenceId,
        );

      if (chargeCategory) {
        const charge = chargeCategory.items.find(
          (x: AdditionalChargeSubtotal) => x.chargeRef === chargeItem._id,
        );

        if (charge) {
          return charge.total.client;
        }
      }
    }
    if (
      chargeItem.isClientOverallAdjustment &&
      chargeItem.client.chargeBasis === AdditionalChargeRateBasis.PERCENTAGE
    ) {
      return (
        (chargeItem.client.charge / 100) * totals.value.subtotals.lessGst.client
      );
    }
    return chargeItem.client.charge;
  }
  if (props.fuelLevyType) {
    return totals.value.subtotals.fuelSurcharges.client;
  }
  if (props.freightChargeType) {
    return totals.value.subtotals.freightCharges.client;
  }

  if (props.isStandbyRate) {
    return totals.value.subtotals.standbyChargeTotals.client;
  }
  if (props.isDemurrageRate) {
    return totals.value.subtotals.demurrageChargeTotals.client;
  }
  return 0;
});

const isAdjustmentTypeCharge: ComputedRef<boolean> = computed(() => {
  if (!props.additionalChargeType) {
    return false;
  }
  const freightAdjType = pricingTypes.find(
    (type) => type.shortName === 'F-ADJ',
  );
  const overallAdjType = pricingTypes.find(
    (type) => type.shortName === 'O-ADJ',
  );
  const freightTypeId = freightAdjType ? freightAdjType.id : 6;
  const overallTypeId = overallAdjType ? overallAdjType.id : 5;
  const charge = props.chargeItem as AdditionalChargeItem;
  // TODO: check what we want to show here for refactored logic
  // if (
  //   charge.clientPricingType === freightTypeId ||
  //   charge.clientPricingType === overallTypeId
  // ) {
  //   return true;
  // }
  return false;
});
const currentFleetAssetRate: ComputedRef<number> = computed(() => {
  if (props.additionalChargeType) {
    const chargeItem: AdditionalChargeItem =
      props.chargeItem as AdditionalChargeItem;
    if (
      chargeItem.fleetAsset.chargeBasis === AdditionalChargeRateBasis.FIXED &&
      chargeItem.isFleetAssetOverallAdjustment
    ) {
      const chargeCategory =
        props.job.accounting.totals.subtotals.additionalChargeItems.find(
          (x: AdditionalChargeSubtotal) =>
            x.chargeRef === chargeItem.typeReferenceId,
        );

      if (chargeCategory) {
        const charge = chargeCategory.items.find(
          (x: AdditionalChargeSubtotal) => x.chargeRef === chargeItem._id,
        );

        if (charge) {
          return charge.total.fleetAsset;
        }
      }
    }
    if (
      chargeItem.fleetAsset.chargeBasis ===
        AdditionalChargeRateBasis.PERCENTAGE &&
      chargeItem.isFleetAssetFreightAdjustment
    ) {
      const isTollAdminAndHandling =
        chargeItem._id === tollAdminAndHandlingId.value;
      return !isTollAdminAndHandling
        ? (chargeItem.fleetAsset.charge / 100) *
            totals.value.subtotals.freightCharges.fleetAsset
        : chargeItem.fleetAsset.charge;
    }
    if (
      chargeItem.fleetAsset.chargeBasis ===
        AdditionalChargeRateBasis.PERCENTAGE &&
      chargeItem.isFleetAssetOverallAdjustment
    ) {
      return (
        (chargeItem.fleetAsset.charge / 100) *
        totals.value.subtotals.lessGst.fleetAsset
      );
    }
    return chargeItem.fleetAsset.charge;
  }
  if (props.fuelLevyType) {
    return totals.value.subtotals.fuelSurcharges.fleetAsset;
  }
  if (props.freightChargeType) {
    return totals.value.subtotals.freightCharges.fleetAsset;
  }
  if (props.isStandbyRate) {
    return totals.value.subtotals.standbyChargeTotals.fleetAsset;
  }
  if (props.isDemurrageRate) {
    return totals.value.subtotals.demurrageChargeTotals.fleetAsset;
  }
  return 0;
});

function hasGstApplied(fleetAsset: boolean = false) {
  return !fleetAsset
    ? props.clientGstRegistered
    : props.fleetAssetGstRegistered;
}

function returnRateString(value: number) {
  if (!isAdjustmentTypeCharge.value) {
    return '';
  }

  const isPositive = Math.sign(value) >= 0;

  if (isPositive) {
    return '$' + DisplayCurrencyValue(Math.abs(value));
  } else {
    return '- $' + DisplayCurrencyValue(Math.abs(value));
  }
}
</script>
<style scoped lang="scss">
.finalise-job-table-row {
  font-size: $font-size-13;
  $table-base: #393939;
  $table-base-dark: #2b2b2b;

  table {
    width: 100%;
    border-spacing: 0px;
    border-collapse: collapse;
  }

  // tbody {
  //   padding-top: 20px !important;
  // }

  th,
  td {
    font-weight: unset;
    text-align: left;
    // padding-left: 8px;
  }

  td.column8,
  td.column9 {
    padding-left: 10px;
  }

  th {
    // padding-right: 20px;
    // padding-left: 6px;
    padding-top: 4px;
  }

  .td--subheader {
    padding: 10px 10px 6px 12px;
    color: #a7a7a7;
    font-weight: 500;
    font-size: 0.85em;
    letter-spacing: 0.02em;
    text-transform: uppercase;
  }

  .layout-padding {
    padding-left: 40px;
  }

  .left-align {
    width: 36px;
    height: 100%;
    background-color: #303030;
    position: absolute;
    left: 0px;
    top: 0px;
  }

  .column0 {
    width: 20px;
    // text-transform: uppercase;
  }

  .column1 {
    width: 14%;
    padding-left: 8px;
    transition: 0.15s;

    &.td--secondary {
      background-color: var(--background-color-300);
      border-right: 1px solid #484a51;
      &.selected {
        background-color: var(--background-color-300);
        border-right: 1px solid #484a51;
      }
    }
    // text-transform: uppercase;
  }
  .column2 {
    width: 1%;
  }
  .column3 {
    width: 17%;
  }
  .column4 {
    width: 10%;
  }

  .column5 {
    width: 10%;
  }

  .column6 {
    width: 12%;
  }

  .column7 {
    width: 8%;
    &.td--secondary {
      border-right: 1px solid #3f3530;
      padding-right: 2px;
    }
  }

  .column8 {
    width: 13%;
  }

  .td--secondary {
    position: relative;
    font-size: 0.92em;
    font-weight: 500;
    color: #e6eaf1;
    .td--secondary__gst-icon {
      font-size: $font-size-11;
      color: #d4d4d4;
    }
    .td--secondary__check-icon {
      color: #cecece;
    }
  }

  .custom-table-body--secondary td {
    padding-top: 8px;
    padding-bottom: 6px;
  }
  .custom-table-body--secondary {
    transition: 0.15s;
  }

  .custom-table-body--secondary.inactive-charge {
    background-color: #202020;
    $inactive-text: #2b2b2b;
    cursor: default;
    .column1 {
      &.td--secondary {
        background-color: #202020;
        border-right-color: $inactive-text;
      }
    }

    .td--secondary {
      color: $inactive-text;
      .td--secondary__check-icon {
        color: $inactive-text;
      }
      .td--secondary-icon {
        color: $inactive-text;
      }
    }
  }

  .totals td {
    padding-right: 24px;
    padding-top: 14px;
    padding-bottom: 10px;
  }

  .td--secondary-icon {
    margin-left: 7px;
    margin-right: 8px;
    color: orange;
  }
}

.funny-money {
  background-color: rgba(209, 14, 14, 0.2) !important;

  border-top: 1px solid rgba(0, 0, 0, 0.12);
}
</style> -->
