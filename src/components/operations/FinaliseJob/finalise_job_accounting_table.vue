<template>
  <section class="finalise-job-accounting-table">
    <v-container class="pa-0">
      <v-layout row wrap>
        <v-flex md12>
          <div class="custom-table ver3">
            <div class="custom-table-head">
              <v-layout class="header-actions" align-center>
                <span
                  class="header-actions__title"
                  :class="
                    jobDetails.recurringJobId
                      ? 'recurring-job-id__highlight'
                      : ''
                  "
                  ># {{ jobDetails.displayId }}
                  <span class="subtitle"
                    >{{ jobDetails.client.clientName }}
                  </span></span
                >
                <v-spacer></v-spacer>
                <v-btn
                  v-for="item in filterOptions"
                  :key="item.id"
                  class="filter-button mx-2 px-3"
                  :class="item.id === filterIndex ? 'active' : ''"
                  height="40"
                  @click="updateChargeFilter(item.id)"
                >
                  <v-layout
                    column
                    style="height: 100%"
                    justify-center
                    align-center
                  >
                    <div>
                      {{ item.title }}
                    </div>
                  </v-layout>
                </v-btn>
              </v-layout>
            </div>

            <div
              class="custom-table-body--primary js-pscroll"
              :class="scrollableBody ? 'scrollable-body' : ''"
            >
              <!-- FLEET CHARGES -->
              <div v-if="filterIndex === 3" class="charge-container">
                <v-flex class="charge-title"><span>Fleet Charges</span></v-flex>
                <FinaliseJobPricingSummary
                  :serviceTypeId="jobDetails.serviceTypeId"
                  :pudItems="jobDetails.pudItems"
                  :accounting="jobDetails.accounting"
                  :entityType="RateEntityType.FLEET_ASSET"
                ></FinaliseJobPricingSummary>
              </div>
              <!-- CLIENT CHARGES -->
              <div v-if="filterIndex === 2" class="charge-container">
                <v-flex class="charge-title"
                  ><span>Client Charges</span></v-flex
                >
                <FinaliseJobPricingSummary
                  :serviceTypeId="jobDetails.serviceTypeId"
                  :pudItems="jobDetails.pudItems"
                  :accounting="jobDetails.accounting"
                  :entityType="RateEntityType.CLIENT"
                ></FinaliseJobPricingSummary>
              </div>
              <!-- ALL CHARGES -->
              <table v-if="filterIndex === 1">
                <thead>
                  <tr>
                    <th class="column1 pl-2">Category</th>
                    <th class="column3">Item</th>
                    <th class="column4">Qty</th>
                    <th class="column5">Rate</th>
                    <th class="column6">Amount ($)</th>
                    <th class="column7">GST</th>
                    <th class="column8">Margin ($)</th>
                  </tr>
                </thead>
                <tr class="rowbackground--primary no-hover">
                  <td colspan="9" class="td--subheader">
                    <v-layout align-center justify-space-between>
                      <span>Primary Rate</span>
                      <v-btn
                        icon
                        small
                        class="ma-0"
                        @click="viewingPrimaryRates = !viewingPrimaryRates"
                      >
                        <v-icon
                          size="14"
                          :color="
                            viewingPrimaryRates
                              ? 'grey lighten-1'
                              : 'amber accent-3'
                          "
                        >
                          {{
                            viewingPrimaryRates
                              ? 'far fa-window-minimize'
                              : 'fas fa-plus-square'
                          }}
                        </v-icon>
                      </v-btn>
                    </v-layout>
                  </td>
                </tr>

                <tr
                  class="rowbackground--primary hover"
                  v-if="viewingPrimaryRates"
                >
                  <td colspan="9" class="td--primary">
                    <FinaliseJobTableRow
                      :job="job"
                      :chargeItem="jobDetails.accounting.clientRates[0]"
                      :secondaryChargeItem="
                        jobDetails.accounting.fleetAssetRates[0]
                      "
                      :isEquipmentHire="isEquipmentHire"
                      :finishedJobData="jobDetails.accounting.finishedJobData"
                      :freightChargeType="true"
                      :outsideMetroCharge="outsideMetroCharge"
                      :fleetAssetGstRegistered="fleetAssetGstRegistered"
                    >
                    </FinaliseJobTableRow>
                  </td>
                </tr>

                <tr
                  class="rowbackground--primary hover"
                  v-if="viewingAdditionalRates && isDemurrageRate"
                >
                  <td colspan="9" class="td--primary">
                    <FinaliseJobTableRow
                      :job="job"
                      :chargeItem="jobDetails.accounting.clientRates[0]"
                      :secondaryChargeItem="
                        jobDetails.accounting.fleetAssetRates[0]
                      "
                      :fleetAssetRate="
                        jobDetails.accounting.fleetAssetRates[0].rate
                          .rateTypeObject
                      "
                      :clientRate="
                        jobDetails.accounting.clientRates[0].rate.rateTypeObject
                      "
                      :isDemurrageRate="true"
                      :finishedJobData="jobDetails.accounting.finishedJobData"
                      :fleetAssetGstRegistered="fleetAssetGstRegistered"
                    >
                    </FinaliseJobTableRow>
                  </td>
                </tr>
                <tr
                  class="rowbackground--primary hover"
                  v-if="viewingAdditionalRates && isTravelDelayRate"
                >
                  <td colspan="9" class="td--primary">
                    <FinaliseJobTableRow
                      :job="job"
                      :chargeItem="jobDetails.accounting.clientRates[0]"
                      :secondaryChargeItem="
                        jobDetails.accounting.fleetAssetRates[0]
                      "
                      :fleetAssetRate="
                        jobDetails.accounting.fleetAssetRates[0].rate
                          .rateTypeObject
                      "
                      :clientRate="
                        jobDetails.accounting.clientRates[0].rate.rateTypeObject
                      "
                      :isTravelDelayRate="true"
                      :finishedJobData="jobDetails.accounting.finishedJobData"
                      :fleetAssetGstRegistered="fleetAssetGstRegistered"
                    >
                    </FinaliseJobTableRow>
                  </td>
                </tr>

                <tr
                  class="rowbackground--primary hover"
                  v-if="viewingPrimaryRates && isStandbyRate"
                >
                  <td colspan="9" class="td--primary">
                    <FinaliseJobTableRow
                      :job="job"
                      :chargeItem="jobDetails.accounting.clientRates[0]"
                      :secondaryChargeItem="
                        jobDetails.accounting.fleetAssetRates[0]
                      "
                      :isStandbyRate="isStandbyRate"
                      :fleetAssetRate="
                        jobDetails.accounting.fleetAssetRates[0].rate
                          .rateTypeObject
                      "
                      :clientRate="
                        jobDetails.accounting.clientRates[0].rate.rateTypeObject
                      "
                      :finishedJobData="jobDetails.accounting.finishedJobData"
                      :fleetAssetGstRegistered="fleetAssetGstRegistered"
                    >
                    </FinaliseJobTableRow>
                  </td>
                </tr>

                <template v-if="viewingPrimaryRates">
                  <tr
                    class="rowbackground--primary hover"
                    v-for="item in freightAdjustmentCharges"
                    :key="item._id"
                  >
                    <td colspan="9" class="td--primary">
                      <FinaliseJobTableRow
                        :job="job"
                        :chargeItem="item"
                        :additionalChargeType="true"
                        :fleetAssetGstRegistered="fleetAssetGstRegistered"
                      >
                      </FinaliseJobTableRow>
                    </td>
                  </tr>
                </template>

                <tr
                  class="rowbackground--primary hover"
                  v-if="viewingPrimaryRates"
                >
                  <td colspan="9" class="td--primary">
                    <FinaliseJobTableRow
                      :job="job"
                      :chargeItem="jobDetails.accounting.clientRates[0]"
                      :secondaryChargeItem="
                        jobDetails.accounting.fleetAssetRates[0]
                      "
                      :isEquipmentHire="false"
                      :finishedJobData="jobDetails.accounting.finishedJobData"
                      :freightChargeType="true"
                      :outsideMetroType="true"
                      :outsideMetroCharge="outsideMetroCharge"
                      :fleetAssetGstRegistered="fleetAssetGstRegistered"
                    >
                    </FinaliseJobTableRow>
                  </td>
                </tr>

                <tr class="rowbackground--primary no-hover">
                  <td colspan="9" class="td--subheader">
                    <v-layout align-center justify-space-between>
                      <span>Additional Charges</span>
                      <v-btn
                        icon
                        small
                        class="ma-0"
                        @click="
                          viewingAdditionalRates = !viewingAdditionalRates
                        "
                      >
                        <v-icon
                          size="14"
                          :color="
                            viewingAdditionalRates
                              ? 'grey lighten-1'
                              : 'amber accent-3'
                          "
                        >
                          {{
                            viewingAdditionalRates
                              ? 'far fa-window-minimize'
                              : 'fas fa-plus-square'
                          }}
                        </v-icon>
                      </v-btn>
                    </v-layout>
                  </td>
                </tr>

                <tr
                  class="rowbackground--primary hover"
                  v-if="viewingAdditionalRates"
                >
                  <td colspan="9" class="td--primary">
                    <FinaliseJobTableRow
                      :job="job"
                      :chargeItem="
                        jobDetails.accounting.additionalCharges
                          .clientFuelSurcharge
                      "
                      :fleetAssetFuelSurcharge="
                        jobDetails.accounting.additionalCharges
                          .fleetAssetFuelSurcharge
                      "
                      :fuelLevyType="true"
                      :fleetAssetGstRegistered="fleetAssetGstRegistered"
                    >
                    </FinaliseJobTableRow>
                  </td>
                </tr>
                <template
                  v-if="
                    viewingAdditionalRates &&
                    filteredAdditionalCharges.length > 0
                  "
                >
                  <tr
                    class="rowbackground--primary hover"
                    v-for="(item, index) in filteredAdditionalCharges"
                    :key="index"
                  >
                    <td colspan="9" class="td--primary">
                      <FinaliseJobTableRow
                        :job="job"
                        :chargeItem="item"
                        :additionalChargeType="true"
                        :fleetAssetGstRegistered="fleetAssetGstRegistered"
                      >
                      </FinaliseJobTableRow>
                    </td>
                  </tr>
                </template>
              </table>
            </div>

            <div
              class="table-footer"
              :class="isEquipmentHire ? 'table-footer-hire-margin' : ''"
              v-show="filterIndex === 1"
            >
              <div class="footer-title-container">
                <div
                  class="hire-margin-style"
                  v-if="isEquipmentHire"
                  :style="{ backgroundColor: hireMarginStatusColor }"
                >
                  <span>Equipment Hire Margin:</span>
                </div>
                <span
                  class="total--text"
                  :style="{ height: clientFleetfooterHeight }"
                  >Margin
                  <p>(Freight Charges excl. GST Only)</p></span
                >
              </div>
              <div class="footer-totals-container">
                <v-tooltip bottom v-if="isEquipmentHire">
                  <template v-slot:activator="{ on }">
                    <div class="hire-totals" v-on="on">
                      <div
                        class="hire-amounts"
                        :style="{ backgroundColor: hireMarginStatusColor }"
                      >
                        <div class="icon-container">
                          <v-icon
                            size="12"
                            class="td--secondary-icon"
                            color="orange"
                            >fal fa-file-contract</v-icon
                          >
                        </div>
                        <span class="footer--subtotal"
                          >{{
                            jobDetails.accounting.totals.subtotals.equipmentHireTotal.toFixed(
                              2,
                            )
                          }}
                        </span>
                      </div>
                      <div
                        class="margins-container"
                        :style="{ backgroundColor: hireMarginStatusColor }"
                      >
                        <span
                          >${{
                            DisplayCurrencyValue(
                              jobDetails.accounting.totals.hireMargin.dollar,
                            )
                          }}</span
                        >
                        <span
                          >{{
                            jobDetails.accounting.totals.hireMargin.percent
                              ? jobDetails.accounting.totals.hireMargin.percent.toFixed(
                                  1,
                                )
                              : 'N/A'
                          }}%</span
                        >
                      </div>
                    </div>
                  </template>
                  <span>Margin including the cost of hire.</span>
                </v-tooltip>
                <div
                  class="client-fleet-totals"
                  :style="{ height: clientFleetfooterHeight }"
                >
                  <div
                    class="value-totals"
                    :class="isEquipmentHire ? 'client-asset-rate-margin' : ''"
                  >
                    <div
                      class="entity-amount"
                      :style="{ height: clientFleetSinglefooterHeight }"
                    >
                      <div class="icon-container">
                        <v-icon
                          size="12"
                          class="td--secondary-icon"
                          color="orange"
                          >fal fa-user</v-icon
                        >
                      </div>

                      <span class="footer--subtotal">{{
                        clientFreightMarginTotal
                      }}</span>
                    </div>
                    <div
                      class="entity-amount"
                      :style="{ height: clientFleetSinglefooterHeight }"
                    >
                      <div class="icon-container">
                        <v-icon
                          size="12"
                          class="td--secondary-icon"
                          color="orange"
                          >fal fa-steering-wheel</v-icon
                        >
                      </div>
                      <span class="footer--subtotal">{{
                        fleetAssetFreightMarginTotal
                      }}</span>
                    </div>
                  </div>
                  <div
                    class="margins-container"
                    :style="{
                      height: clientFleetfooterHeight,
                      backgroundColor: marginStatusColor,
                    }"
                  >
                    <span class="margin-value"
                      >${{
                        DisplayCurrencyValue(
                          jobDetails.accounting.totals.margin.dollar,
                        )
                      }}</span
                    >
                    <span class="margin-value"
                      >{{
                        jobDetails.accounting.totals.margin.percent
                          ? jobDetails.accounting.totals.margin.percent.toFixed(
                              1,
                            )
                          : 'N/A'
                      }}%</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </v-flex>
      </v-layout>
    </v-container>
  </section>
</template>

<script setup lang="ts">
import FinaliseJobTableRow from '@/components/operations/FinaliseJob/finalise_job_accounting_table/finalise_job_table_row/index.vue';
import {
  DisplayCurrencyValue,
  RoundCurrencyValue,
} from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { initialiseAdditionalChargeItem } from '@/helpers/classInitialisers/InitialiseAdditionalChargeItem';
import { initialiseJobDetails } from '@/helpers/classInitialisers/InitialiseJobDetails';
import { reorderAdditionalChargeItems } from '@/helpers/RateHelpers/AdditionalChargeHelpers';
import { AdditionalChargeItem } from '@/interface-models/AdditionalCharges/AdditionalChargeItem';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import { useRootStore } from '@/store/modules/RootStore';
import { ComputedRef, Ref, computed, onMounted, ref } from 'vue';
import FinaliseJobPricingSummary from './finalise_job_pricing_summary.vue';

const props = withDefaults(
  defineProps<{
    job: JobDetails;
    clientGstRegistered?: boolean;
    fleetAssetGstRegistered: boolean;
    isEquipmentHire: boolean;
    scrollableBody?: boolean;
  }>(),
  {
    clientGstRegistered: true,
    scrollableBody: true,
  },
);

const rootStore = useRootStore();

const viewingPrimaryRates: Ref<boolean> = ref(true);
const viewingAdditionalRates: Ref<boolean> = ref(true);
const filterIndex: Ref<number> = ref(1);
const originalAdditionalChargeList: Ref<AdditionalChargeItem[]> = ref([]);
const filterOptions: Ref<{ id: number; title: string }[]> = ref([
  { id: 1, title: 'All' },
  { id: 2, title: 'Client Charges' },
  { id: 3, title: 'Fleet Charges' },
]);

const jobDetails: ComputedRef<JobDetails> = computed(() =>
  initialiseJobDetails(props.job),
);

const clientFleetfooterHeight: ComputedRef<string> = computed(() =>
  props.isEquipmentHire ? '42.6px' : '64px',
);

const clientFleetSinglefooterHeight: ComputedRef<string> = computed(() =>
  props.isEquipmentHire ? '21.3px' : '32px',
);

const hireMarginStatusColor: ComputedRef<string> = computed(() =>
  Math.sign(jobDetails.value.accounting.totals.hireMargin.dollar) === -1
    ? '#4A2323'
    : '#4A2323',
);

const marginStatusColor: ComputedRef<string> = computed(() =>
  Math.sign(jobDetails.value.accounting.totals.margin.dollar) === -1
    ? 'rgba(209, 14, 14, 0.2)'
    : '#0ea548',
);

const isDemurrageRate: ComputedRef<boolean> = computed(
  () =>
    jobDetails.value.accounting.totals.subtotals.demurrageChargeTotals.client >
      0 ||
    jobDetails.value.accounting.totals.subtotals.demurrageChargeTotals
      .fleetAsset > 0,
);

const isTravelDelayRate: ComputedRef<boolean> = computed(
  () =>
    (jobDetails.value.accounting.totals.subtotals.travelDelayChargeTotals
      ?.client ?? 0) > 0 ||
    (jobDetails.value.accounting.totals.subtotals.travelDelayChargeTotals
      ?.fleetAsset ?? 0) > 0,
);

const isStandbyRate: ComputedRef<boolean> = computed(() => {
  if (props.job.pudItems === undefined) {
    return false;
  }
  const standbyPudExists = props.job.pudItems.find(
    (pud: PUDItem) => pud.isStandbyRate,
  );

  const clientIsTimeRate =
    jobDetails.value.accounting.clientRates[0].rate.rateTypeId === 1;
  const fleetAssetIsTimeRate =
    jobDetails.value.accounting.fleetAssetRates[0].rate.rateTypeId === 1;

  return !!standbyPudExists && (clientIsTimeRate || fleetAssetIsTimeRate);
});

const outsideMetroCharge = computed(() => {
  const accountingDetails = jobDetails.value.accounting;
  const driverRates = accountingDetails.fleetAssetRates[0];
  const clientRates = accountingDetails.clientRates[0];
  return {
    isOutsideMetroChargeDriver:
      driverRates.outsideMetroRate && driverRates.outsideMetroRate > 0,
    isOutsideMetroChargeClient:
      clientRates.outsideMetroRate && clientRates.outsideMetroRate > 0,
    driverQuantity:
      driverRates.outsideMetroRate && driverRates.outsideMetroRate > 0 ? 1 : 0,
    clientQuantity:
      clientRates.outsideMetroRate && clientRates.outsideMetroRate > 0 ? 1 : 0,
    driverRate: driverRates.outsideMetroRate ? driverRates.outsideMetroRate : 0,
    clientRate: clientRates.outsideMetroRate ? clientRates.outsideMetroRate : 0,
    clientAmount: accountingDetails.totals.subtotals.outsideMetroChargeTotals
      ? accountingDetails.totals.subtotals.outsideMetroChargeTotals.client
      : 0,
    driverAmount: accountingDetails.totals.subtotals.outsideMetroChargeTotals
      ? accountingDetails.totals.subtotals.outsideMetroChargeTotals.fleetAsset
      : 0,
    clientGst: accountingDetails.totals.subtotals.outsideMetroChargeGstTotals
      ? accountingDetails.totals.subtotals.outsideMetroChargeGstTotals.client
      : 0,
    driverGst: accountingDetails.totals.subtotals.outsideMetroChargeGstTotals
      ? accountingDetails.totals.subtotals.outsideMetroChargeGstTotals
          .fleetAsset
      : 0,
  };
});

/**
 * Returns a filtered and sorted list of NON_FREIGHT type additional charges.
 * Used in the Freight section of the table under the base freight row.
 */
const freightAdjustmentCharges = computed(() => {
  const applicableChargeItems = originalAdditionalChargeList.value.filter(
    (charge) => charge.isClientFreightAdjustment,
  );

  return reorderAdditionalChargeItems(
    applicableChargeItems,
    rootStore.tollChargeTypeId ?? '',
    rootStore.tollAdminAndHandlingId ?? '',
  );
});

/**
 * Returns a filtered and sorted list of NON_FREIGHT type additional charges.
 * Used in the Additional Charges section of the table.
 */
const filteredAdditionalCharges = computed(() => {
  const applicableChargeItems = originalAdditionalChargeList.value.filter(
    (charge) =>
      charge.isClientOverallAdjustment ||
      charge._id === rootStore.tollAdminAndHandlingId,
  );

  return reorderAdditionalChargeItems(
    applicableChargeItems,
    rootStore.tollChargeTypeId ?? '',
    rootStore.tollAdminAndHandlingId ?? '',
  );
});

const clientFreightMarginTotal = computed(() => {
  const freight = jobDetails.value.accounting.totals.subtotals
    .freightChargeTotals.client
    ? jobDetails.value.accounting.totals.subtotals.freightChargeTotals.client
    : 0;
  const standby = jobDetails.value.accounting.totals.subtotals
    .standbyChargeTotals.client
    ? jobDetails.value.accounting.totals.subtotals.standbyChargeTotals.client
    : 0;
  const demurrage = jobDetails.value.accounting.totals.subtotals
    .demurrageChargeTotals.client
    ? jobDetails.value.accounting.totals.subtotals.demurrageChargeTotals.client
    : 0;
  const travelDelay = jobDetails.value.accounting.totals.subtotals
    .travelDelayChargeTotals?.client
    ? jobDetails.value.accounting.totals.subtotals.travelDelayChargeTotals
        .client
    : 0;
  return DisplayCurrencyValue(
    RoundCurrencyValue(freight + standby + demurrage + travelDelay),
  );
});

const fleetAssetFreightMarginTotal = computed(() => {
  const freight = jobDetails.value.accounting.totals.subtotals
    .freightChargeTotals.fleetAsset
    ? jobDetails.value.accounting.totals.subtotals.freightChargeTotals
        .fleetAsset
    : 0;
  const standby = jobDetails.value.accounting.totals.subtotals
    .standbyChargeTotals.fleetAsset
    ? jobDetails.value.accounting.totals.subtotals.standbyChargeTotals
        .fleetAsset
    : 0;
  const demurrage = jobDetails.value.accounting.totals.subtotals
    .demurrageChargeTotals.fleetAsset
    ? jobDetails.value.accounting.totals.subtotals.demurrageChargeTotals
        .fleetAsset
    : 0;

  const travelDelay = jobDetails.value.accounting.totals.subtotals
    .travelDelayChargeTotals?.fleetAsset
    ? jobDetails.value.accounting.totals.subtotals.travelDelayChargeTotals
        .fleetAsset
    : 0;
  return DisplayCurrencyValue(
    RoundCurrencyValue(freight + standby + demurrage + travelDelay),
  );
});

function updateChargeFilter(value: number): void {
  viewingPrimaryRates.value = true;
  viewingAdditionalRates.value = true;
  filterIndex.value = value;
}

onMounted(() => {
  originalAdditionalChargeList.value = [
    ...jobDetails.value.accounting.additionalCharges.chargeList.map((charge) =>
      initialiseAdditionalChargeItem(charge),
    ),
  ];
});
</script>

<style scoped lang="scss">
.finalise-job-accounting-table {
  font-family: $font-sans;
}
.custom-table .ps__rail-y {
  width: 9px;
  background-color: transparent;
  opacity: 1 !important;
  right: 5px;
}

.custom-table .ps__rail-y::before {
  content: '';
  display: block;
  position: absolute;
  background-color: #ebebeb;
  border-radius: 5px;
  width: 100%;
  height: calc(100% - 30px);
  left: 0;
  top: 15px;
}

.custom-table .ps__rail-y .ps__thumb-y {
  width: 100%;
  right: 0;
  background-color: transparent;
  opacity: 1 !important;
}

.custom-table .ps__rail-y .ps__thumb-y::before {
  content: '';
  display: block;
  position: absolute;
  background-color: #cccccc;
  border-radius: 5px;
  width: 100%;
  height: calc(100% - 30px);
  left: 0;
  top: 15px;
}

/*//////////////////////////////////////////////////////////////////
[ Table ]*/
table {
  width: 100%;
  border-spacing: 0px;
  border-collapse: collapse;
}

th,
td {
  font-weight: unset;
  text-align: left;
}

td.column8,
td.column9 {
  padding-left: 24px;
}

th {
  padding-right: 20px;
  padding-left: 6px;
  padding-top: 12px;
}

.td--subheader {
  padding: 10px 10px 6px 12px;
  color: var(--bg-light);
  font-weight: 600;
  font-size: 0.85em;
  letter-spacing: 0.02em;
  text-transform: uppercase;
}

.column1 {
  width: 15%;
  padding-left: 8px;

  &.td--secondary {
    background-color: var(--background-color-200);
    border-right: 1px solid var(--background-color-600);
  }

  // text-transform: uppercase;
}

.column3 {
  width: 17%;
}

.column4 {
  width: 10%;
}

.column5 {
  width: 10%;
}

.column6 {
  width: 12%;
}

.column7 {
  width: 8%;
  &.td--secondary {
    border-right: 1px solid var(--background-color-600);
    padding-right: 2px;
  }
}

.column8 {
  width: 13%;
}

.custom-table-head th {
  padding-top: 12px;
  padding-bottom: 12px;

  &:first-child {
    text-align: left;
  }
}

.custom-table-body--primary td {
  // padding-top: 10px;

  &:first-child {
    text-align: left;
  }

  .td--secondary {
    position: relative;
    font-size: 1em;
    font-weight: 600;
    color: var(--light-text-color);
  }
}

.table-footer-hire-margin {
  height: 74px !important;
}

.client-asset-rate-margin {
  height: 42px;
}

.table-footer {
  height: 64px;
  display: flex;
  background-color: var(--background-color-300);
  align-items: center;
  width: 100%;
  border-top: 1px solid var(--background-color-600);
  .total--text {
    font-size: $font-size-18;
    font-weight: 700;
    display: flex;
    align-items: center;
    padding-left: 15px;

    p {
      font-size: $font-size-16;
      font-weight: 500;
      display: flex;
      align-items: center;
      padding-left: 6px;
      margin-top: 17px;
      color: var(--light-text-color);
    }
  }
  .footer-totals-container {
    .client-fleet-totals {
      width: 270px;
      display: flex;
      align-items: center;
      .entity-amount {
        display: flex;
        align-items: center;
        width: 100px;
      }
    }
    .margins-container {
      display: flex;
      width: 170px !important;
      padding: 0 15px 0 15px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .margin-value {
        font-size: $font-size-18;
        font-weight: 600;
      }
    }
  }
  .footer-title-container {
    flex-grow: 1;
    .hire-margin-style {
      border-bottom: 1px solid var(--background-color-600);
      width: 100%;
      // height: 21.3px;
      height: 30px;
      padding-left: 15px;
      font-size: $font-size-13;
      display: flex;
      line-height: 1;
      align-items: center;
    }
  }

  .icon-container {
    width: 35px;
    display: flex;
    align-items: center;
  }
  .hire-totals {
    height: 30px;
    display: flex;
    border-bottom: 1px solid var(--background-color-600);

    .hire-amounts {
      display: flex;
      width: 100px;
      align-items: center;
    }
  }
  .footer--subtotal {
    font-weight: 700;
    font-size: $font-size-13;
  }
}
.custom-table-body--secondary td {
  // padding-right: 4px;
  padding-top: 8px;
  padding-bottom: 6px;
}

.totals td {
  padding-right: 24px;
  padding-top: 14px;
  padding-bottom: 10px;
}

.td--secondary-icon {
  color: var(--warning) !important;
  margin-left: 7px;
  margin-right: 8px;
  font-weight: 600;
}

/*==================================================================
[ Fix header ]*/
.custom-table {
  position: relative;
  padding-top: 80px;
}

.custom-table-head {
  position: absolute;
  width: 100%;
  top: 0px;
  left: 0;
}

.custom-table .custom-table-body--primary {
  background-color: var(--background-color-400);

  &.scrollable-body {
    overflow-y: auto;
    overscroll-behavior-y: contain;
    min-height: calc(85vh - 70px - 42px - 40px - 64px - 32px) !important;
    max-height: calc(85vh - 70px - 42px - 40px - 64px - 32px) !important;
  }
}

.header-actions {
  padding: 14px 16px;
  height: 70px;
  background-color: $app-dark-primary-250;

  &__title {
    color: var(--light-text-color);
    font-size: $font-size-18 !important;
    font-weight: 700;

    .subtitle {
      color: var(--text-color);
      padding-left: 8px;
      font-weight: 700;
    }
  }

  .filter-button {
    background-color: $app-dark-primary-250 !important;
    border: 1px solid $border-color !important;
    border-radius: 10px;
    font-size: $font-size-14;
    font-weight: 600;
    transition: all 0.2s;
    cursor: pointer;
    box-shadow: none !important;
    &:hover {
      border-color: #ff9204 !important;
      scale: 1.1;
      box-shadow: var(--box-shadow);
    }

    &.active {
      background-color: #ffc404 !important;
      border: 2px solid #ff9204 !important;
      color: var(--background-color-100);
      box-shadow: var(--box-shadow);
      &:hover {
        color: var(--background-color-100) !important;
      }
    }

    .button-chip {
      background-color: #654b2a;
      border-radius: 20px;
      padding: 2px 5px;
      color: white;
      font-size: $font-size-small;
      font-weight: 600;
    }
  }
}

.custom-table.ver3 {
  background-color: var(--background-color-400);
}

.custom-table.ver3 th {
  font-weight: 500;
  font-size: $font-size-15;
  color: #eca31c;
  line-height: 2;
  padding: 12px;
  text-transform: uppercase;
  background-color: var(--background-color-400);
  border-bottom: 2px solid var(--background-color-600);
}

.custom-table.ver3 td {
  font-size: $font-size-14;
  line-height: 1;
  background-color: var(--background-color-300);
}

.custom-table-body--primary table {
  margin-top: 0px;
}

.rowbackground--primary {
  transition: 0.15s;
  border-bottom: 1px solid var(--background-color-600);

  &.selected {
    background-color: var(--background-color-400);

    &.hover {
      &:hover {
        background-color: var(--background-color-200);
        cursor: pointer;
      }
    }
  }

  &.hover {
    &:hover {
      background-color: var(--background-color-300);
      cursor: pointer;
    }
  }

  &.no-hover {
    background-color: var(--background-color-400);
  }
}

.subitem,
.primaryitem {
  position: relative;
  transition: 0.2s;

  &:hover {
    cursor: pointer;
  }
}

.custom-table.ver3 {
  border-radius: 3px;
  overflow: hidden;
}

.custom-table.ver3 .ps__rail-y {
  right: 5px;
}

.custom-table.ver3 .ps__rail-y::before {
  background-color: #4e4e4e;
}

.custom-table.ver3 .ps__rail-y .ps__thumb-y::before {
  background-color: #00ad5f;
}

.list-item {
  display: inline-block;
  margin-right: 10px;
}

.list-enter-active,
.list-leave-active {
  transition: all 1s;
}

.list-enter,
.list-leave-to

/* .list-leave-active below version 2.1.8 */ {
  opacity: 0;
  transform: translateY(30px);
}

.charge-container {
  .charge-title {
    font-weight: 600;
    font-size: $font-size-17;
    color: var(--light-text-color);
    line-height: 1.2;
    padding: 14px;
    text-transform: uppercase;
    background-color: var(--background-color-400);
    border-bottom: 2px solid var(--background-color-600);
  }
}
</style>
