<template>
  <ContentDialog
    :showDialog.sync="dialogController"
    title="Dashboard Filters"
    width="35%"
    contentPadding="pa-0"
    @cancel="closeDialog"
    @confirm="applyFilters"
    @action="clearFilters"
    :showActions="true"
    :isConfirmUnsaved="false"
    :isDisabled="false"
    :isLoading="false"
    :showActionButton="true"
    actionBtnText="Restore to Default"
    :actionRequiresConfirmation="true"
    actionConfirmationMessage="Are you sure you want to restore the default filters?"
    :confirmBtnText="isFormDisabled ? 'Apply' : 'Save and Apply'"
  >
    <v-layout>
      <v-flex md12 class="body-scrollable--75 pa-3">
        <h3 class="subheader--light mb-3">Select a Channel</h3>
        <div class="filter-container">
          <v-layout md12>
            <v-autocomplete
              v-model="selectedOperationsChannels"
              :items="operationsChannelList"
              label="Select a Channel"
              item-text="selectName"
              item-value="_id"
              multiple
              chips
              deletable-chips
              class="v-solo-custom"
              :disabled="!isFormDisabled"
              solo
              flat
              :menu-props="{ closeOnContentClick: true }"
            >
            </v-autocomplete>
            <v-tooltip right>
              <template v-slot:activator="{ on }">
                <v-btn
                  flat
                  v-on="on"
                  icon
                  @click="selectedOperationsChannels = null"
                >
                  <v-icon size="24" color="grey lighten-1">fal fa-times</v-icon>
                </v-btn>
              </template>
              Restore to Default
            </v-tooltip>
          </v-layout>
        </div>
        <v-layout justify-start align-center>
          <h3 v-if="appliedOperationsChannel" class="subheader--light">
            {{ formHeaderText }}
          </h3>
          <template v-if="appliedOperationsChannel && allowChannelEdit">
            <v-spacer></v-spacer>
            <span v-if="!isFormDisabled">
              <v-tooltip left>
                <template v-slot:activator="{ on }">
                  <v-btn
                    flat
                    v-on="on"
                    icon
                    class="ma-0"
                    @click="cancelOperationsChannelEdits"
                  >
                    <v-icon size="20" color="error">fal fa-times</v-icon>
                  </v-btn>
                </template>
                Discard Changes
              </v-tooltip>
            </span>
            <span v-if="allowChannelEdit && appliedOperationsChannel.isMine">
              <v-tooltip left>
                <template v-slot:activator="{ on }">
                  <v-btn
                    flat
                    v-on="on"
                    icon
                    class="ma-0"
                    :disabled="!isFormDisabled"
                    @click="editOperationsChannel(appliedOperationsChannel)"
                  >
                    <v-icon size="17" color="accent">far fa-edit</v-icon>
                  </v-btn>
                </template>
                Edit Channel
              </v-tooltip>
            </span>
            <span v-if="allowChannelEdit">
              <v-tooltip left>
                <template v-slot:activator="{ on }">
                  <v-btn
                    flat
                    v-on="on"
                    icon
                    class="ma-0"
                    :disabled="!isFormDisabled"
                    @click="cloneOperationsChannel(appliedOperationsChannel)"
                  >
                    <v-icon size="17" color="accent">far fa-clone</v-icon>
                  </v-btn>
                </template>
                Clone as Custom Channel
              </v-tooltip>
            </span>
          </template>
        </v-layout>
        <OperationsChannelForm
          v-if="appliedOperationsChannel"
          ref="operationsChannelDialogForm"
          :key="operationsChannelFormIncrementer"
          :isFormDisabled="isFormDisabled"
          :operationsChannel="appliedOperationsChannel"
          :isDashboardView="true"
        ></OperationsChannelForm>
      </v-flex>
    </v-layout>
  </ContentDialog>
</template>

<script setup lang="ts">
import OperationsChannelForm from '@/components/common/operations_channel/operations_channel_form.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import {
  combineOperationsChannels,
  getCustomChannelFromLocalStorage,
} from '@/helpers/AppLayoutHelpers/OperationsChannelHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  GENERIC_ERROR_MESSAGE,
  GENERIC_SUCCESS_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import OperationsDashboardSetting from '@/interface-models/Generic/OperationScreenOptions/OperationsDashboardSetting';
import { OperationsChannel } from '@/interface-models/OperationsChannels/OperationsChannel';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useFilterStore } from '@/store/modules/OperationsFilterStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { sessionManager } from '@/store/session/SessionState';
import {
  computed,
  ComputedRef,
  nextTick,
  onMounted,
  Ref,
  ref,
  watch,
  WritableComputedRef,
} from 'vue';

const props = withDefaults(
  defineProps<{
    value: boolean;
    isMap: boolean;
  }>(),
  {
    isMap: false,
  },
);

enum EditMode {
  NONE,
  NEW,
  EDIT,
  CLONE,
}

const filterStore = useFilterStore();
const operationsStore = useOperationsStore();

const emit = defineEmits(['input']);

const operationsChannelDialogForm: Ref<any> = ref(null);

// default settings for fleet list table
const operationsChannelList: Ref<OperationsChannel[]> = ref([]);
const selectedChannelIds: Ref<string[] | null> = ref([]);

const appliedOperationsChannel: Ref<OperationsChannel | null> = ref(null);
const operationsChannelFormIncrementer: Ref<number> = ref(0);

const formEditMode: Ref<EditMode> = ref(EditMode.NONE);
const isAwaitingSaveResponse: Ref<boolean> = ref(false);

const selectedOperationsChannels: WritableComputedRef<string[] | null> =
  computed({
    get(): string[] {
      return selectedChannelIds.value || [];
    },
    set(value: string[] | null): void {
      selectedChannelIds.value = value;
      setSelectedOperationsChannel(value, operationsChannelList.value);
    },
  });

function closeDialog() {
  dialogController.value = false;
}

const isFormDisabled: ComputedRef<boolean> = computed(() => {
  return formEditMode.value === EditMode.NONE || isAwaitingSaveResponse.value;
});

const formHeaderText: ComputedRef<string> = computed(() => {
  if (!appliedOperationsChannel.value) {
    return '';
  }
  switch (formEditMode.value) {
    case EditMode.NEW:
      return 'Create New Channel';
    case EditMode.EDIT:
      return `Editing Channel: ${appliedOperationsChannel.value.name}`;
    case EditMode.CLONE:
      return `New Custom Channel (from ${appliedOperationsChannel.value.name})`;
    default:
      return `Channel: ${appliedOperationsChannel.value.name}`;
  }
});

// computed list of fleetAssetListSettings from operationsStore dashboard Settings
const fleetAssetListSettings = computed<OperationsDashboardSetting[]>(() => {
  return JSON.parse(
    JSON.stringify(operationsStore.dashboardSettings.fleetAssetList),
  );
});

// Applies the selected filters and closes the filter dialog
async function applyFilters() {
  if (!isFormDisabled.value && allowChannelEdit.value) {
    await saveOperationsChannelInChild();
    return;
  }

  filterStore.setSelectedOperationsChannelIds(selectedChannelIds.value);
  nextTick(() => {
    dialogController.value = false;
  });
}

/**
 * Handles saving the Operations Channel from the child dialog form.
 *
 * - Validates the form before proceeding.
 * - Shows a notification if validation fails.
 * - Sets the loading state while awaiting the save response.
 * - Attempts to save the Operations Channel via the filter store.
 * - On success, shows a success notification, updates the selected channel IDs, and closes the dialog.
 * - On failure, shows an error notification.
 */
async function saveOperationsChannelInChild() {
  if (
    !operationsChannelDialogForm.value?.validate() ||
    !operationsChannelDialogForm.value.localOperationsChannel
  ) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE, {
      title: 'Operations Channel Maintenance',
      type: HealthLevel.ERROR,
    });
    return;
  }

  isAwaitingSaveResponse.value = true;

  const formToSave: OperationsChannel =
    operationsChannelDialogForm.value.localOperationsChannel;
  if (!(formToSave instanceof OperationsChannel)) {
    logConsoleError('Form to save is not an instance of OperationsChannel');
    return;
  }
  const result = await filterStore.saveOperationsChannel(formToSave);
  if (result?._id) {
    showNotification(GENERIC_SUCCESS_MESSAGE, {
      title: 'Save Operations Channel',
      type: HealthLevel.SUCCESS,
    });

    filterStore.setSelectedOperationsChannelIds([result._id]);
    dialogController.value = false;
  } else {
    showNotification(GENERIC_ERROR_MESSAGE, {
      title: 'Save Operations Channel',
      type: HealthLevel.ERROR,
    });
  }
  isAwaitingSaveResponse.value = false;
}

// Clears selected filters
function clearFilters() {
  setAppliedOperationsChannel(null);
  filterStore.setSelectedOperationsChannelIds(null);
}

const dialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.value;
  },
  set(value: boolean): void {
    emit('input', value);
  },
});

/**
 * Sets the selected operations channel(s) based on the provided channel IDs.
 *
 * - If `channelIds` is null or empty, clears the applied operations channel.
 * - If a single channel ID is provided, finds and applies the corresponding OperationsChannel.
 * - If multiple channel IDs are provided, combines the corresponding OperationsChannels into a single filter and applies it.
 * - Handles errors by logging and clearing the applied operations channel.
 *
 * @param {string[] | null} channelIds - Array of OperationsChannel IDs to select, or null to clear selection.
 * @param {OperationsChannel[]} allChannelsList - List of all available OperationsChannel objects.
 * @returns {void}
 */
function setSelectedOperationsChannel(
  channelIds: string[] | null,
  allChannelsList: OperationsChannel[],
): void {
  try {
    if (channelIds === null || channelIds.length === 0) {
      setAppliedOperationsChannel(null);
    } else if (channelIds.length === 1) {
      const foundChannel = allChannelsList.find(
        (channel) => channel._id === channelIds[0],
      );
      if (!foundChannel) {
        throw new Error(
          'No OperationsChannel object found for _id: ' + channelIds[0],
        );
      }

      setAppliedOperationsChannel(foundChannel);
    } else if (channelIds.length > 1) {
      // Merge all OperationsChannels into a single combined filter
      setAppliedOperationsChannel(
        combineOperationsChannels(
          channelIds
            .map((id) => allChannelsList.find((channel) => channel._id === id))
            .filter(
              (channel): channel is OperationsChannel => channel !== undefined,
            ),
        ),
      );
    }
  } catch (error) {
    logConsoleError(`Error setting selected operations channel`, error);
    setAppliedOperationsChannel(null);
  }
}

/**
 * Sets the currently applied operations channel and disables the operations
 * channel form. Increments the form incrementer to trigger any watchers or
 * updates.
 *
 * @param {OperationsChannel | null} channel - The operations channel to apply,
 * or null to clear.
 */
function setAppliedOperationsChannel(channel: OperationsChannel | null) {
  formEditMode.value = EditMode.NONE;
  appliedOperationsChannel.value = channel;
  operationsChannelFormIncrementer.value++;
}

const allowChannelEdit: ComputedRef<boolean> = computed(() => {
  return (
    useCompanyDetailsStore().divisionCustomConfig?.operations
      ?.allowCustomOperationsChannel ?? false
  );
});

/**
 * Clones an OperationsChannel. Allowed for both user-owned and division-level
 * channels.
 * - Removes _id, assigns current user, marks as non-default.
 * - Updates form state for cloning.
 *
 * @param {OperationsChannel} channel - Channel to clone.
 */
function cloneOperationsChannel(channel: OperationsChannel) {
  // Clone the existing channel to prevent edits from the original list
  const channelToEdit = new OperationsChannel(channel);
  delete channelToEdit._id;
  channelToEdit.companyUserId = sessionManager.getUserId();
  channelToEdit.isDefaultChannel = false;

  // Set to working variable
  appliedOperationsChannel.value = channelToEdit;

  // Increment key to force re-mount
  operationsChannelFormIncrementer.value++;
  formEditMode.value = EditMode.CLONE;
}

/**
 * Prepares an OperationsChannel for editing if owned by the user.
 * Clones the channel, updates state, and sets form to edit mode.
 *
 * @param {OperationsChannel} channel - Channel to edit.
 */
function editOperationsChannel(channel: OperationsChannel) {
  if (!channel.isMine) {
    return;
  }
  // Clone the existing channel to prevent edits from the original list
  const channelToEdit = new OperationsChannel(channel);

  // Set to working variable
  appliedOperationsChannel.value = channelToEdit;

  // Increment key to force re-mount
  operationsChannelFormIncrementer.value++;
  formEditMode.value = EditMode.EDIT;
}

function cancelOperationsChannelEdits() {
  setSelectedOperationsChannel(
    selectedChannelIds.value,
    operationsChannelList.value,
  );
  formEditMode.value = EditMode.NONE;
}

/**
 * Initializes the list of available operations channels.
 * Combines channels from the store and any custom channel from local storage.
 * Sets the selected channel IDs from the store and applies the current filter.
 */
function setOperationsChannelsList() {
  const storedChannels = filterStore.operationsChannels.map(
    (c) => new OperationsChannel(c),
  );
  const channelList = [...storedChannels];

  const localStorageChannel: OperationsChannel | null =
    getCustomChannelFromLocalStorage();
  if (localStorageChannel) {
    channelList.push(localStorageChannel);
  }
  operationsChannelList.value = channelList;

  // Set the selectedChannelIds default value from the store
  selectedChannelIds.value = [
    ...(filterStore.selectedOperationsChannelIds ?? []),
  ];

  // Set the current applied filter based on the selectedChannels
  setSelectedOperationsChannel(
    selectedChannelIds.value,
    operationsChannelList.value,
  );
}

// watch for dialog, apply settings when dialog opens
watch(dialogController, (newValue: boolean) => {
  if (newValue) {
    // When the dialog is opened, set grouped settings
    setOperationsChannelsList();
  } else {
    // When the dialog is closed, clear grouped settings
    operationsChannelList.value = [];
    selectedChannelIds.value = [];
    appliedOperationsChannel.value = null;
    formEditMode.value = EditMode.NONE;
    operationsChannelFormIncrementer.value = 0;
  }
});

// checks that `fleetAssetListSettings` is defined.
onMounted(() => {
  if (!fleetAssetListSettings.value) {
    return;
  }
});
</script>

<style scoped lang="scss">
.filter-container {
  margin: 0 6px;
  padding: 0 6px;
}
.filter-options {
  position: relative;
  bottom: 22px;
}
.select-all-btn {
  position: relative;
  padding: 0px;
  bottom: 26px;
  margin: 0px;
}

.title-txt {
  font-size: $font-size-18;
  font-weight: 500;
  color: var(--primary);
  padding-top: 4px;
  margin-bottom: 6px;
  padding-left: 12px;
}

.sub-heading-txt {
  font-size: $font-size-14;
  color: var(--light-text-color);
  padding-left: 12px;
  margin-bottom: 14px;
}

.action-row {
  padding: 4px;
}
</style>
