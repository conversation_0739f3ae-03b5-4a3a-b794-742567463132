<template>
  <div class="operation-dashboard">
    <v-layout fill-height>
      <AssetInformationDialog
        v-if="showAssetInformationDialog && selectedFleetAssetId"
        :fleetAssetId="selectedFleetAssetId"
        :showAssetInformationDialog="showAssetInformationDialog"
      ></AssetInformationDialog>

      <v-flex md12>
        <!-- Spotlight Search Button -->
        <v-btn small outline class="search-btn" @click="openSpotlight"
          ><v-icon size="20" class="pr-2">search</v-icon>Quick Job Search</v-btn
        >
        <v-layout column fill-height class="column-container">
          <v-flex class="one-third-section">
            <JobList
              :class="{
                'jobs-list-container':
                  filterStore.isOperationsChannelFilterApplied,
              }"
              @openJobList="openJobList"
              :windowMode="false"
              :selectedJobDetails="jobDetails"
              :currentIndex="currentIndex"
              ref="operationsDashboardJobList"
              @closeAllExpandedRows="closeAllExpandedRows"
            />
          </v-flex>
          <v-flex
            class="one-third-section"
            v-if="selectedFleetAssetId && selectedDriverId"
          >
            <v-layout fill-height>
              <AllocatedWork
                ref="operationsDashboardAllocatedWork"
                :selectedJobDetails="jobDetails"
                :selectedFleetAssetId="selectedFleetAssetId"
                :selectedDriverId="selectedDriverId"
                @closeAllExpandedRows="closeAllExpandedRows"
              />
            </v-layout>
          </v-flex>
          <v-flex class="one-third-section" v-if="selectedJobId !== -1">
            <v-layout fill-height>
              <JobDetailsInformation
                :key="selectedJobId"
                :jobDetails="jobDetails"
                :selectedJobId="selectedJobId"
                @closeAllExpandedRows="closeAllExpandedRows"
              />
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
      <JobDetailsDialog
        v-if="showJobDetailsDialog && selectedJobDetails"
        :jobDetails="selectedJobDetails"
        :showJobDetailsDialog="showJobDetailsDialog"
      ></JobDetailsDialog>
      <!-- QuickJobSearch Overlay -->
      <OperationDashboardQuickJobSearch
        :visible="spotlightVisible"
        :jobs="jobsForSpotlight"
        @close="closeSpotlight"
        @selectedJobId="handleSpotlightJobSelect"
      />
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  Ref,
  ComputedRef,
  onBeforeUnmount,
  onMounted,
} from 'vue';
import AllocatedWork from '@/components/operations/OperationDashboard/components/AllocatedWork/index.vue';
import AssetInformationDialog from '@/components/operations/OperationDashboard/components/AssetInformationDialog/index.vue';
import JobDetailsInformation from '@/components/operations/OperationDashboard/components/JobDetails/index.vue';
import JobDetailsDialog from '@/components/operations/OperationDashboard/components/JobDetailsDialog/index.vue';
import JobList from '@/components/operations/OperationDashboard/components/JobList/index.vue';
import OperationDashboardQuickJobSearch from '@/components/operations/OperationDashboard/components/operation_dashboard_quick_job_search.vue';
import OperationDashboardFilters from '@/components/operations/OperationDashboard/operation_dashboard_filters.vue';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { useClientPortalStore } from '@/store/modules/ClientPortalStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useFilterStore } from '@/store/modules/OperationsFilterStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import { useMittListener } from '@/utils/useMittListener';
import { useRoute, useRouter } from 'vue-router/composables';

const props = defineProps<{
  currentIndex: number;
  jobDetails: JobDetails;
}>();

const filterStore = useFilterStore();
const operationsStore = useOperationsStore();
const jobStore = useJobStore();
const router = useRouter();
const route = useRoute();

const spotlightVisible: Ref<boolean> = ref(false);

const operationsDashboardJobList: Ref<any> = ref(null);
const operationsDashboardAllocatedWork: Ref<any> = ref(null);

/**
 * Returns the currently selected job ID.
 */
const selectedJobId: ComputedRef<number> = computed(
  () => operationsStore.selectedJobId,
);

/**
 * Opens the job list in a new window and creates a broadcast channel.
 */
function openJobList(): void {
  operationsStore.createBroadcastChannel(route.name);
  const routeUrl = router.resolve({ path: '/job-list' });
  if (routeUrl) {
    window.open(routeUrl.href, 'Jobs List', 'menubar=no');
  }
}

/**
 * Returns true if the asset information dialog should be shown.
 */
const showAssetInformationDialog: ComputedRef<boolean> = computed(
  () => operationsStore.viewingAssetInformationDialog,
);

/**
 * Returns the selected fleet asset ID, or an empty string if not set.
 */
const selectedFleetAssetId: ComputedRef<string> = computed(
  () => operationsStore.selectedFleetAssetId ?? '',
);

/**
 * Returns the selected driver ID, or an empty string if not set.
 */
const selectedDriverId: ComputedRef<string> = computed(
  () => operationsStore.selectedDriverId ?? '',
);

/**
 * Returns the jobs available for the spotlight search.
 */
const jobsForSpotlight: ComputedRef<any[]> = computed(
  () => jobStore.operationJobsList,
);

/**
 * Opens the spotlight (quick job search) overlay.
 */
function openSpotlight(): void {
  spotlightVisible.value = true;
}

/**
 * Closes the spotlight (quick job search) overlay.
 */
function closeSpotlight(): void {
  spotlightVisible.value = false;
}

/**
 * Handles selection of a job from the spotlight search.
 * @param jobId - The selected job ID.
 */
async function handleSpotlightJobSelect(jobId: number): Promise<void> {
  if (sessionManager.isClientPortal()) {
    useClientPortalStore().setSelectedJobId(jobId);
  } else {
    operationsStore.setSelectedJobId(jobId);
    const result = await operationsStore.getFullJobDetails(jobId);
    if (result) {
      operationsStore.setViewingJobDetailsDialog(true);
    }
  }
  closeSpotlight();
}

/**
 * Minimizes all expanded rows in job list and allocated work tables except the selected type.
 * @param selectedType - The type to keep expanded.
 */
function closeAllExpandedRows(selectedType: string | null): void {
  if (selectedType === null) {
    return;
  }
  if (operationsDashboardJobList.value?.closeExpandedRows) {
    operationsDashboardJobList.value.closeExpandedRows(selectedType);
  }
  if (operationsDashboardAllocatedWork.value?.closeExpandedRows) {
    operationsDashboardAllocatedWork.value.closeExpandedRows(selectedType);
  }
}

/**
 * Returns true if the job details dialog should be shown.
 */
const showJobDetailsDialog: ComputedRef<boolean> = computed(
  () => operationsStore.viewingJobDetailsDialog,
);

/**
 * Returns the selected job details if the dialog is open and a job is selected, otherwise null.
 */
const selectedJobDetails: ComputedRef<JobDetails | null> = computed(() => {
  if (!showJobDetailsDialog.value || operationsStore.selectedJobId === -1) {
    return null;
  }
  return operationsStore.selectedJobDetails;
});

useMittListener('closeDashboardJobRows', closeAllExpandedRows);

// Lifecycle hooks
onMounted(() => {
  Mitt.on('closeDashboardJobRows', closeAllExpandedRows);
});
onBeforeUnmount(() => {
  Mitt.off('closeDashboardJobRows', closeAllExpandedRows);
});
</script>
<style scoped lang="scss">
.operation-dashboard {
  height: calc(100vh - 60px);
  margin-right: 4px;
  display: flex;
  flex-direction: column;

  .search-btn {
    position: fixed;
    border-radius: 14px;
    z-index: 200;
    right: 760px;
    top: 0px;
    padding: 12px 36px;
    color: rgb(185, 185, 201);
    border: 1.5px solid rgb(185, 185, 201);
    font-weight: 700;
    .v-icon {
      font-weight: 800;
    }
  }

  .column-container {
    .one-third-section {
      &:first-child {
        margin-top: 0px;
      }
      height: 33.33%;
      margin: 1px;
      background-color: var(--background-color-200);
      border-radius: $border-radius-sm;
      resize: vertical;
      overflow: auto; // To allow resizing
      min-height: 3.33%;
      z-index: 0;
    }
  }
  .jobs-list-container {
    border-top: 1px solid $warning;
  }
}
</style>
