import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import DialogHeaderBar from '@/components/common/dialog_header_bar.vue';
import SelectEntity from '@/components/common/inputs/select_entity/index.vue';
import PointManagerImportDialog from '@/components/common/point_manager_import_dialog/index.vue';
import { jobListHeadersPointManagerSearch } from '@/components/operations/OperationDashboard/components/JobList/job_list_headers';
import UnassignedPudListActionButtons from '@/components/operations/OperationDashboard/components/JobList/unassigned_pud_list_action_buttons/index.vue';
import ImportRequiresAttention from '@/components/operations/OperationDashboard/components/UnassignedPudItemSearch/ImportRequiresAttention/index.vue';
import {
  returnDurationFromMilliseconds,
  returnEndOfDayFromEpoch,
  returnFormattedDate,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import DialogHeader from '@/interface-models/Generic/Dialog/DialogHeader';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { SearchByOption } from '@/interface-models/Generic/SearchByOption';
import { JobOperationType } from '@/interface-models/Jobs/JobOperationType';
import { OperationJobSummary } from '@/interface-models/Jobs/OperationJobSummary';
import {
  BookJobWithUnassignedPudConfig,
  PudMaintenanceDialogConfig,
} from '@/interface-models/Jobs/PUD/UnassignedPudItem/PudMaintenanceDialogConfig';
import { PudMaintenanceType } from '@/interface-models/Jobs/PUD/UnassignedPudItem/PudMaintenanceType';
import UnassignedPudItemResponse from '@/interface-models/Jobs/PUD/UnassignedPudItem/SaveUnassignedPudItemResponse';
import UnassignedPudItem from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudItem';
import { UnassignedPudItemRequest } from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudItemRequest';
import { UnassignedPudItemStatus } from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudItemStatus';
import { UnassignedPudAssociatedClientJobs } from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudJobDetailsRequest';
import UnassignedPudStatusUpdateRequest from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudStatusUpdate';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useDataImportStore } from '@/store/modules/DataImportStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { v4 as uuidv4 } from 'uuid';
import { Component, Prop, Vue } from 'vue-property-decorator';

enum DateSearchOptions {
  CREATED = 'CREATED',
  SCHEDULED = 'SCHEDULED',
}
enum SearchCategoryOptions {
  CLIENT = 'Client',
  REFERENCE = 'Reference Number',
  JOBID = 'Job ID',
  STATUS = 'Status',
  GROUP = 'Group',
}

@Component({
  components: {
    DatePickerBasic,
    UnassignedPudListActionButtons,
    PointManagerImportDialog,
    DialogHeaderBar,
    ImportRequiresAttention,
    SelectEntity,
  },
})
export default class UnassignedPudSearch extends Vue {
  @Prop({ default: false }) public viewingUnassignedPudSearchDialog: boolean;
  public entityType = EntityType;
  public unassignedPudItemList: UnassignedPudItem[] = [];

  public dataImportStore = useDataImportStore();
  public operationsStore = useOperationsStore();

  public returnFormattedDate = returnFormattedDate;
  public returnDurationFromMilliseconds = returnDurationFromMilliseconds;

  public previousSearchRequest: UnassignedPudItemRequest | null = null;
  public previousDateSearchOption: DateSearchOptions | null = null;
  public previousSearchCategoryOption: SearchCategoryOptions | null = null;

  public selectedTableItems: UnassignedPudItem[] = [];

  public statusSelectList: string[] = Object.values(UnassignedPudItemStatus);

  // Selections from DROP DOWNS
  public selectedSearchByOption: SearchCategoryOptions =
    SearchCategoryOptions.STATUS;
  public selectedDateSearchType: DateSearchOptions =
    DateSearchOptions.SCHEDULED;

  public searchByOptionValue: string = 'UNASSIGNED';

  public tableSearch: string = '';
  public isLoadingTableData: boolean = false;

  public startDate: number = returnStartOfDayFromEpoch();
  public endDate: number = returnEndOfDayFromEpoch();

  public viewingJobSelectionDialog: boolean = false;
  public jobListForClient: UnassignedPudAssociatedClientJobs[] | null = null;
  public selectedJobId: number = -1;

  public previouslyMadeRequest: UnassignedPudItemRequest | null = null;

  public selectedDialogTab: string = 'search';

  public dateSearchTypes = [
    {
      id: DateSearchOptions.SCHEDULED,
      longName: 'Scheduled Time',
    },
    {
      id: DateSearchOptions.CREATED,
      longName: 'Created Time',
    },
  ];

  public searchByOptions: SearchByOption[] = [
    {
      id: SearchCategoryOptions.CLIENT,
      longName: 'Client',
    },
    {
      id: SearchCategoryOptions.REFERENCE,
      longName: 'Reference',
    },
    {
      id: SearchCategoryOptions.JOBID,
      longName: 'Job ID',
    },
    {
      id: SearchCategoryOptions.STATUS,
      longName: 'Status',
    },
    {
      id: SearchCategoryOptions.GROUP,
      longName: 'Group',
    },
  ];

  get dialogHeaders(): DialogHeader[] {
    return [
      {
        id: 'search',
        title: 'Point Manager - Search',
      },
      {
        id: 'requiresAttention',
        title: 'Req. Attention',
      },
    ];
  }

  // Controls visibility of the job selection dialog
  // When set as true, set the job list to that of the client
  // When set as false, reset job list and selected job variable
  get jobSelectionDialogController() {
    return this.viewingJobSelectionDialog;
  }
  set jobSelectionDialogController(value: boolean) {
    if (value) {
      this.jobListForClient = this.setJobSummaryListForClient();
    } else {
      this.jobListForClient = null;
      this.selectedJobId = -1;
    }
    this.viewingJobSelectionDialog = value;
  }

  get showDialog() {
    return this.viewingUnassignedPudSearchDialog;
  }
  set showDialog(value: boolean) {
    this.operationsStore.setViewingPudSearchDialog(value);
  }
  // Check if selected UPIs are for the same client, then open the dialog if true
  public viewJobSelectionDialog() {
    if (!this.validateSelectionForClient(this.selectedTableItems)) {
      return;
    }
    this.jobSelectionDialogController = true;
  }
  // Set the local job list to filter for in-progress/unallocated jobs for the selected client
  public setJobSummaryListForClient(): UnassignedPudAssociatedClientJobs[] {
    if (!this.selectedTableItems || !this.selectedTableItems.length) {
      return [];
    }
    const clientId = this.selectedTableItems[0].clientId;
    const allJobs: UnassignedPudAssociatedClientJobs[] = (
      useJobStore().operationJobsList as OperationJobSummary[]
    )
      .filter(
        (job: OperationJobSummary) =>
          job.jobId &&
          job.clientId === clientId &&
          job.workStatus >= WorkStatus.BOOKED &&
          job.workStatus <= WorkStatus.IN_PROGRESS,
      )
      .map((job) => {
        const firstPudEpoch = job.date ? job.date : 0;
        return {
          jobId: job.jobId ? job.jobId : 0,
          displayId: job.displayId ? job.displayId : '',
          dateString: firstPudEpoch ? returnFormattedDate(firstPudEpoch) : '',
          driverName:
            job.driverName && job.driverName !== '-' ? job.driverName : '',
          csrAssignedId:
            job.csrAssignedId && job.csrAssignedId !== '-'
              ? job.csrAssignedId
              : '',
        };
      });

    return allJobs;
  }

  // value returned is the index of the selected tab. We should set the selected tab index to be the index of dialogHeaders id
  public tabItemSelected(selectedDialogHeaderIndex: number) {
    this.selectedDialogTab = this.dialogHeaders[selectedDialogHeaderIndex].id;
  }

  // Close dialog, jump to booking screen and stage the selected UPI for it to be added to job
  public addToNewJobInBookingScreen() {
    const clientId = this.selectedTableItems[0].clientId;
    const idList: string[] = this.selectedTableItems.map((i) => i.id);
    const config: BookJobWithUnassignedPudConfig = {
      clientId,
      operationType: JobOperationType.NEW,
      unassignedPudIds: idList,
    };
    this.operationsStore.setViewingPudSearchDialog(false);
    this.dataImportStore.setCreatingJobFromUnassignedPudItems(true);
    this.operationsStore.setBookJobWithUnassignedPudConfig(config);
  }
  // After selection of a job, open that job in the booking screen and stage the selected UPI to be added as a PUD
  public addToExistingJobInBookingScreen() {
    if (this.selectedJobId === -1) {
      console.error('Please select a valid job');
      return;
    }
    if (!this.selectedTableItems || !this.selectedTableItems.length) {
      return;
    }

    const jobId: number = this.selectedJobId;

    const clientId = this.selectedTableItems[0].clientId;
    const idList: string[] = this.selectedTableItems.map((i) => i.id);

    const config: BookJobWithUnassignedPudConfig = {
      clientId,
      operationType: JobOperationType.EDIT,
      jobId,
      unassignedPudIds: idList,
    };
    this.jobSelectionDialogController = false;
    this.operationsStore.setViewingPudSearchDialog(false);
    this.dataImportStore.setCreatingJobFromUnassignedPudItems(true);
    this.operationsStore.setBookJobWithUnassignedPudConfig(config);
  }

  public closeDialog() {
    this.showDialog = false;
  }

  public resetTextInputField() {
    this.searchByOptionValue = '';
  }
  // Handle startDate selection from date picker
  public setStartDate(startDateEpoch: number) {
    this.startDate = startDateEpoch;
  }
  // Handle endDate selection from date picker
  public setEndDate(endDateEpoch: number) {
    this.endDate = endDateEpoch;
  }

  // // Open the add pud dialog for adding NEW UPI
  // public createNewUnassignedPudItem() {
  //   const config: PudMaintenanceDialogConfig = {
  //     operationType: JobOperationType.NEW,
  //     pudType: PudMaintenanceType.UNASSIGNED,
  //   };
  //   this.operationsStore.setPudMaintenanceDialogConfig(config);
  //   this.operationsStore.setViewingPudMaintenanceDialog(true);
  // }
  /**
   * Request and response for updating a list of UnassignedPudItem documents to
   * a new status.
   * @param status - The new status to update the selected items to
   */
  public async updateUnassignedPudStatus(status: UnassignedPudItemStatus) {
    if (!this.selectedTableItems) {
      return;
    }
    const idList = this.selectedTableItems.map((i) => i.id);
    if (idList.length === 0) {
      return;
    }
    const request: UnassignedPudStatusUpdateRequest = {
      unassignedPudIds: idList,
      updatedStatus: status,
    };
    this.handleStatusUpdateResponse(
      await this.dataImportStore.updateUnassignedPudItemStatus(request),
    );
    this.selectedTableItems = [];
  }
  /**
   * Handle response from any status updates that are received while the window
   * is open
   * @param response - The response from the status update request
   */
  public handleStatusUpdateResponse(
    response: UnassignedPudStatusUpdateRequest | null,
  ) {
    if (!response) {
      return;
    }
    // If we are currently searching by STATUS, we should check if there is an
    // affected UPI in results
    if (this.selectedSearchByOption === SearchCategoryOptions.STATUS) {
      if (
        this.unassignedPudItemList.some((upi) =>
          response.unassignedPudIds.includes(upi.id),
        )
      ) {
        // If there is at least one matched id, then we just redo same search
        // This will ensure we get all latest updates for current criteria
        this.searchWithPreviousCriteria();
      }
    } else {
      // If we're NOT searching by STATUS, then we look for matches in current results
      // and update the assignedStatus of matched results
      response.unassignedPudIds.forEach((id) => {
        const foundUnassignedPudItem = this.unassignedPudItemList.find(
          (upi) => upi.id === id,
        );
        if (foundUnassignedPudItem) {
          foundUnassignedPudItem.assignedStatus = response.updatedStatus;
          this.deselectUnassignedPudItem(id);
        }
      });
    }
  }

  // Check that the provided UPIs are all for the same client
  public validateSelectionForClient(pudItemList: UnassignedPudItem[]): boolean {
    const clientIds: string[] = pudItemList.map((p) => p.clientId);
    if (
      clientIds.length > 0 &&
      !clientIds.every((id, i, arr) => id === arr[0])
    ) {
      showNotification(
        'Please ensure all selected Points are for the same client.',
      );
      return false;
    } else {
      return true;
    }
  }
  // Change search parameters to search for items in specified group
  public searchForGroupId(groupId: string) {
    this.selectedSearchByOption = SearchCategoryOptions.GROUP;
    this.searchByOptionValue = groupId;
    this.searchWithCurrentCriteria(false);
  }
  /**
   * Dispatch request to update status of all selected items to COMPLETED
   */
  public async addSelectedItemsToGroup() {
    if (!this.selectedTableItems) {
      return;
    }
    const pudItemList: UnassignedPudItem[] = JSON.parse(
      JSON.stringify(this.selectedTableItems),
    );
    if (!this.validateSelectionForClient(pudItemList)) {
      return;
    }
    const groupId = uuidv4().replace(/-/g, '');
    pudItemList.forEach((i) => {
      i.groupReferenceId = groupId;
    });
    // Send request and handle response
    this.handleUpdatedUnassignedPudItemResponse(
      await this.dataImportStore.saveUnassignedPudItemList(pudItemList),
    );
  }

  // Remove references to group from UPI and save
  public async removePointFromGroup(id: string) {
    const foundUnassignedPudItem = this.unassignedPudItemList.find(
      (p) => p.id === id,
    );
    if (foundUnassignedPudItem) {
      const clonedPudItem = Object.assign(
        new UnassignedPudItem(),
        foundUnassignedPudItem,
      );
      clonedPudItem.groupReferenceId = '';
      clonedPudItem.groupReferenceIndex = 0;

      // Send request and handle response
      this.handleUpdatedUnassignedPudItemResponse(
        await this.dataImportStore.saveUnassignedPudItem(clonedPudItem),
      );
    }
  }

  // Handle response from any UPI updates that are received while the window is open
  public handleUpdatedUnassignedPudItemResponse(
    response: UnassignedPudItemResponse | null,
  ) {
    if (!response?.unassignedPudItemList?.length) {
      return;
    }
    // Initialise UPI list to add additional data
    const upiList = this.initialiseUnassignedPudListFromResponse(
      response.unassignedPudItemList,
    );

    // Iterate over list from response. If the UPI is currently in the
    // unassignedPudItemList, then we should replace it with the updated
    // version. Else if the UPI is current NOT in the list, we should check it
    // for suitability based on the previous search
    upiList.forEach((upi) => {
      // Check if exists in list
      const foundExistingIndex = this.unassignedPudItemList.findIndex(
        (pud) => pud.id === upi.id,
      );
      if (foundExistingIndex !== -1) {
        // UPI exists in current list. Replace it with the newer version.
        this.unassignedPudItemList.splice(foundExistingIndex, 1, upi);
        this.deselectUnassignedPudItem(upi.id);
        return;
      }
      // The UPI does not exist in the current list. We should check the item
      // for suitability against the previous search

      const prevSearch = this.previousSearchRequest;
      if (!prevSearch) {
        return;
      }
      // Return if we searched by Created Epoch, and the UPI creationTime is out
      // of range
      if (
        this.previousDateSearchOption === DateSearchOptions.CREATED &&
        prevSearch.createdStartEpoch &&
        prevSearch.createdEndEpoch &&
        (upi.creationTime < prevSearch.createdStartEpoch ||
          upi.creationTime > prevSearch.createdEndEpoch)
      ) {
        return;
      }
      // Return if we searched by Pud Epoch, and the UPI pudDetails.epochTime is
      // out of range
      if (
        this.previousDateSearchOption === DateSearchOptions.SCHEDULED &&
        prevSearch.pudStartEpoch &&
        prevSearch.pudEndEpoch &&
        (upi.pudDetails.epochTime < prevSearch.pudStartEpoch ||
          upi.pudDetails.epochTime > prevSearch.pudEndEpoch)
      ) {
        return;
      }
      // If we reached here then we have filtered out new UPI's that don't meet
      // the time range criteria. Next we should filter for validity based upon
      // other provided search parameters

      if (this.previousSearchCategoryOption !== null) {
        let isValid = true;

        // Use switch statement to decide what values that we want to compare
        // between previous search and currently iterated UPI
        let prevSearchVal: string | number | null | UnassignedPudItemStatus[] =
          null;
        let upiValueToCheck: string | number | null = null;

        switch (this.previousSearchCategoryOption) {
          case SearchCategoryOptions.CLIENT:
            // Searching by CLIENT - check clientId
            prevSearchVal = prevSearch.clientId;
            upiValueToCheck = upi.clientId;
            break;
          case SearchCategoryOptions.REFERENCE:
            // Searching by REFERENCE - check clientSuppliedId
            prevSearchVal = prevSearch.clientSuppliedId;
            upiValueToCheck = upi.clientSuppliedId;
            break;
          case SearchCategoryOptions.JOBID:
            // Searching by JOBID - check jobId
            prevSearchVal = prevSearch.jobId;
            upiValueToCheck = upi.jobId;
            break;
          case SearchCategoryOptions.STATUS:
            // Searching by STATUS - check assignedStatus
            prevSearchVal = prevSearch.assignedStatus;
            upiValueToCheck = upi.assignedStatus;
            break;
          case SearchCategoryOptions.GROUP:
            // Searching by GROUP - check groupReferenceId
            prevSearchVal = prevSearch.groupReferenceId;
            upiValueToCheck = upi.groupReferenceId;
            break;
        }
        // If prevSearchVal was null, empty or zero,
        // OR if prevSearchVal was NOT null, empty or zero AND matched upiValueToCheck
        // Then we should push in to current list
        isValid =
          prevSearchVal === null ||
          prevSearchVal === '' ||
          prevSearchVal === 0 ||
          (prevSearchVal !== null &&
            prevSearchVal !== '' &&
            prevSearchVal !== 0 &&
            prevSearchVal === upiValueToCheck);
        if (isValid) {
          this.unassignedPudItemList.push(upi);
        }
      }
    });
  }

  // Dispatch a search request using the config used in the last search
  public async searchWithPreviousCriteria(): Promise<void> {
    if (this.previousSearchRequest !== null) {
      this.isLoadingTableData = true;
      // Set target destination to UnassignedPudDestination.TABLE to indicate
      // the response should be used in this component
      this.selectedTableItems = [];
      // Send request and reset any selection
      const result = await this.dataImportStore.requestUnassignedPudItemList(
        this.previousSearchRequest,
      );
      this.setDefaultPudItemLists(result);
      this.isLoadingTableData = false;
    } else {
      // If no previous search is found, then re-search using current search
      this.searchWithCurrentCriteria();
    }
  }
  // Dispatch search using current selected categories and input values
  public async searchWithCurrentCriteria(
    searchWithDate: boolean = true,
  ): Promise<void> {
    const request: UnassignedPudItemRequest = {
      createdStartEpoch: null,
      createdEndEpoch: null,
      pudStartEpoch: null,
      pudEndEpoch: null,
      clientId: null,
      assignedStatus: [],
      groupReferenceId: null,
      clientSuppliedId: null,
      jobId: null,
    };
    // If searching by SCHEDULED time, use epochTime in pudItem.
    // Else, use the created time in UPI
    if (searchWithDate) {
      if (this.selectedDateSearchType === DateSearchOptions.SCHEDULED) {
        request.pudStartEpoch = this.startDate;
        request.pudEndEpoch = returnEndOfDayFromEpoch(this.endDate);
        request.createdStartEpoch = null;
        request.createdEndEpoch = null;
      }
      if (this.selectedDateSearchType === DateSearchOptions.CREATED) {
        request.pudStartEpoch = null;
        request.pudEndEpoch = null;
        request.createdStartEpoch = this.startDate;
        request.createdEndEpoch = returnEndOfDayFromEpoch(this.endDate);
      }
    }
    // Conditionally set the appropriate field in request object depending on
    // which option is selected in the UI dropdown select
    const searchValue: string = this.searchByOptionValue;
    switch (this.selectedSearchByOption) {
      case SearchCategoryOptions.CLIENT:
        request.clientId = searchValue ? searchValue : null;
        break;
      case SearchCategoryOptions.REFERENCE:
        request.clientSuppliedId = searchValue ? searchValue : null;
        break;
      case SearchCategoryOptions.JOBID:
        const jobId = parseInt(searchValue, 10);
        if (!isNaN(jobId)) {
          request.jobId = jobId;
        } else {
          console.error('JobId must be a number');
        }
        break;
      case SearchCategoryOptions.STATUS:
        if (this.statusSelectList.includes(searchValue)) {
          request.assignedStatus = [searchValue as UnassignedPudItemStatus];
        }
        break;
      case SearchCategoryOptions.GROUP:
        request.groupReferenceId = searchValue;
        break;
    }
    this.isLoadingTableData = true;
    // Set target destination to UnassignedPudDestination.TABLE to indicate the
    // response should be used in this component
    this.previouslyMadeRequest = request;
    // Save previous search request and reset table selection
    this.previousSearchRequest = request;
    this.previousDateSearchOption = this.selectedDateSearchType;
    this.previousSearchCategoryOption = this.selectedSearchByOption;
    this.selectedTableItems = [];
    // Send request and reset any selection
    const result =
      await this.dataImportStore.requestUnassignedPudItemList(request);
    this.setDefaultPudItemLists(result);
    this.isLoadingTableData = false;
  }
  // Initialise the UnassignedPudItem so we can add additionalData
  public initialiseUnassignedPudListFromResponse(
    unassignedPudItemList: UnassignedPudItem[],
  ): UnassignedPudItem[] {
    const initList: UnassignedPudItem[] = [];
    unassignedPudItemList.forEach((unassignedPudItem) => {
      if (unassignedPudItem === null) {
        return;
      }
      const newPudItem = Object.assign(
        new UnassignedPudItem(),
        unassignedPudItem,
      );
      newPudItem.addAdditionalJobData();
      initList.push(newPudItem);
    });
    return initList;
  }
  // Handle response from search API
  public setDefaultPudItemLists(response: UnassignedPudItemResponse | null) {
    if (!response?.unassignedPudItemList) {
      return;
    }
    this.unassignedPudItemList = this.initialiseUnassignedPudListFromResponse(
      response.unassignedPudItemList,
    );
  }

  // Deselect UPI with provided id from selected list (if it is selected)
  public deselectUnassignedPudItem(id: string) {
    const foundSelectedIndex = this.selectedTableItems.findIndex(
      (item) => item.id === id,
    );
    if (foundSelectedIndex !== -1) {
      this.selectedTableItems.splice(foundSelectedIndex, 1);
    }
  }

  // make initial default call
  public requestDefaultUnassignedPudList() {
    this.searchWithCurrentCriteria();
  }

  public mounted() {
    this.requestDefaultUnassignedPudList();
  }

  get tableHeaders() {
    return jobListHeadersPointManagerSearch;
  }

  public openPointManagerImport() {
    this.dataImportStore.setPointManagerImportDialogIsOpen(true);
  }

  get pointManagerDialogIsOpen() {
    return this.dataImportStore.pointManagerImportDialogIsOpen;
  }
}
