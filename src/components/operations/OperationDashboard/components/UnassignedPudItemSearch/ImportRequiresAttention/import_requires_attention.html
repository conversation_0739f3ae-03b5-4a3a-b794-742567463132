<v-layout class="import-requires-attention-container">
  <v-flex md12>
    <v-data-table
      :headers="tableHeaders"
      :items="importsThatRequireAttention"
      class="accounting-data-table imports-require-attention-table"
      :rows-per-page-items="[50, 100]"
      :loading="isLoading"
    >
      <template v-slot:progress>
        <v-progress-linear
          height="4"
          color="#ffa000"
          indeterminate
        ></v-progress-linear>
      </template>
      <template v-slot:items="props">
        <tr class="job-search-table-row">
          <td>{{props.item.clientName}}</td>
          <td>{{props.item.importDate}}</td>
          <td style="text-align: center">
            <v-icon
              size="16"
              @click="downloadSourceDocument(props.item.documentId)"
            >
              fas fa-download
            </v-icon>
          </td>
          <td>
            <v-layout justify-center>
              <ConfirmationDialog
                buttonText="Resolve"
                message="Please confirm that you have resolved all issues with this import."
                title="Confirm Approval of Dummy Invoices"
                @confirm="resolveProblemImport(props.item.documentId)"
                :isSmallButton="true"
                :buttonDisabled="false"
                :isOutlineButton="true"
                :isBlockButton="false"
                :buttonColor="'teal accent-3'"
                :confirmationButtonText="'confirm'"
                :isCheckboxList="false"
                :dialogIsActive="true"
              >
              </ConfirmationDialog>
            </v-layout>
          </td>
        </tr>
      </template>
    </v-data-table>
  </v-flex>
</v-layout>
