import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import ClientSearchSummary from '@/interface-models/Client/ClientDetails/ClientSearchSummary';
import ImportsThatRequireAttention from '@/interface-models/Generic/ImportThatRequireAttention/ImportThatRequireAttention';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useDataImportStore } from '@/store/modules/DataImportStore';
import { Component, Vue } from 'vue-property-decorator';
interface ProblemImportsTable {
  clientName: string;
  importDate: string;
}

@Component({ components: { ConfirmationDialog } })
export default class ImportRequiresAttention extends Vue {
  public dataImportStore = useDataImportStore();

  public isLoading: boolean = false;

  public importDocuments: ImportsThatRequireAttention[] = [];
  get tableHeaders(): TableHeader[] {
    return [
      {
        text: 'Client',
        align: 'left',
        sortable: true,
        value: 'clientName',
      },
      {
        text: 'Upload Date',
        align: 'left',
        sortable: true,
        value: 'clientName',
      },

      {
        text: 'Download Source',
        align: 'center',
        sortable: true,
        value: 'clientName',
      },
      {
        text: 'Mark as Resolved',
        align: 'center',
        sortable: true,
        value: 'clientName',
      },
    ];
  }

  public getClientNameByClientId(clientId: string) {
    const client = useClientDetailsStore().clientSummaryList.find(
      (x: ClientSearchSummary) => x.clientId === clientId,
    );
    return client ? client.clientDisplayName : '-';
  }

  get importsThatRequireAttention(): ProblemImportsTable[] {
    return this.importDocuments.map((x) => {
      return {
        documentId: x.documentId,
        clientName: this.getClientNameByClientId(x.clientId),
        importDate: x.importDate
          ? returnFormattedDate(x.importDate, 'DD/MM/YY HH:mm')
          : '-',
      };
    });
  }

  /**
   * Sends a request a request to mark a problem import as resolved by sending a
   * request to the backend.
   * @param documentId mongo ID of document to mark as resolved
   */
  public async resolveProblemImport(documentId: string) {
    const result =
      await this.dataImportStore.markProblemImportAsResolved(documentId);
    // Remove from the importDocuments list
    if (result) {
      this.importDocuments = this.importDocuments.filter(
        (x) => x.documentId !== documentId,
      );
      showNotification('Import marked as resolved.', {
        type: HealthLevel.SUCCESS,
      });
    }
  }

  /**
   * Send request to download the source document for the import.
   * @param documentId document id of the source document
   */
  public downloadSourceDocument(documentId: string) {
    this.dataImportStore.downloadImportSourceDocumentById(documentId);
  }

  // Request recurring job list on mount
  public async mounted() {
    this.isLoading = true;
    const importDocuments =
      await this.dataImportStore.requestImportsThatRequireAttention();
    this.importDocuments = importDocuments ?? [];
    this.isLoading = false;
    // Show notification if the request failed or if there were no documents to
    // display
    if (!this.importDocuments.length) {
      showNotification('No imports require attention.', {
        type: HealthLevel.INFO,
      });
    }
  }
}
