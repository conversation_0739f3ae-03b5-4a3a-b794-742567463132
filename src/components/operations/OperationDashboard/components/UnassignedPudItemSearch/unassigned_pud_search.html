<v-dialog
  v-model="showDialog"
  width="85%"
  persistent
  class="ma-0"
  content-class="v-dialog-custom"
>
  <PointManagerImportDialog
    v-if="pointManagerDialogIsOpen"
    :pointManagerDialogIsOpen="pointManagerDialogIsOpen"
  />
  <v-card color="#242329">
    <DialogHeaderBar
      :dialogHeaders="dialogHeaders"
      @closeDialog="closeDialog"
      @tabItemSelected="tabItemSelected"
    />
    <v-layout
      wrap
      pt-2
      class="job-search"
      pa-3
      v-if="selectedDialogTab === 'search'"
    >
      <v-flex md12 class="job-search-header-section">
        <v-form ref="form">
          <v-layout wrap class="mb-2">
            <v-flex md2 px-1>
              <v-select
                solo
                flat
                label="Search By"
                v-model="selectedDateSearchType"
                :items="dateSearchTypes"
                item-value="id"
                item-text="longName"
                background-color="blue"
                color="blue"
                hint="Date Type"
                persistent-hint
              ></v-select>
            </v-flex>
            <v-flex md2 px-1>
              <DatePickerBasic
                @setEpoch="setStartDate"
                :soloInput="true"
                :labelName="'Start Date'"
                :yearOnly="false"
                :epochTime="startDate"
                :isRequired="true"
              />
            </v-flex>
            <v-flex md2 px-1>
              <DatePickerBasic
                @setEpoch="setEndDate"
                :soloInput="true"
                :labelName="'End Date'"
                :yearOnly="false"
                :epochTime="endDate"
                :isRequired="true"
              />
            </v-flex>
            <v-flex md2 px-1>
              <v-select
                solo
                flat
                label="Search By"
                v-model="selectedSearchByOption"
                :items="searchByOptions"
                item-value="id"
                item-text="longName"
                background-color="blue"
                color="blue"
                hint="Search by"
                persistent-hint
                @change="resetTextInputField"
              ></v-select>
            </v-flex>

            <v-flex md2 px-1>
              <v-text-field
                v-if="selectedSearchByOption !== 'Client' && selectedSearchByOption !== 'Status'"
                solo
                flat
                :label="selectedSearchByOption"
                v-model="searchByOptionValue"
                :disabled="selectedSearchByOption === 'Group'"
                clearable
              >
              </v-text-field>
              <SelectEntity
                v-if="selectedSearchByOption === 'Client'"
                :entityTypes="[entityType.CLIENT]"
                :id.sync="searchByOptionValue"
              />
              <v-autocomplete
                v-if="selectedSearchByOption === 'Status'"
                solo
                flat
                v-model="searchByOptionValue"
                :items="statusSelectList"
                return-object
                color="orange"
                label="Status Type"
                hint="Status Type"
                class="v-solo-custom"
                persistent-hint
              >
                <template slot="selection" slot-scope="data">
                  <span>{{ data.item.replace(/_/g, " ") }}</span>
                </template>
                <template slot="item" slot-scope="data">
                  <span>{{ data.item.replace(/_/g, " ") }}</span>
                </template>
              </v-autocomplete>
            </v-flex>
            <v-flex md2 px-2>
              <v-btn
                block
                @click="searchWithCurrentCriteria"
                class="view-details-button"
                >Search</v-btn
              >
            </v-flex>
            <v-flex md12>
              <v-divider></v-divider>
            </v-flex>
            <!-- <v-flex md12>
            <v-layout>
              <v-flex md2>
                <v-text-field
                  v-model="tableSearch"
                  append-icon="search"
                  label="Filter Results"
                  single-line
                  hide-details
                ></v-text-field>
              </v-flex>
              <v-spacer></v-spacer>
              <v-flex md3>
                <v-btn outline block>Run Query</v-btn>
              </v-flex>
            </v-layout>
          </v-flex> -->
          </v-layout>
        </v-form>
      </v-flex>

      <v-flex md12>
        <v-layout align-center class="pb-2">
          <v-menu right>
            <template v-slot:activator="{ on }">
              <v-icon size="22" class="pl-3 pr-1" v-on="on"
                >far fa-ellipsis-v</v-icon
              >
            </template>
            <v-list class="v-list-custom">
              <v-list-tile
                :disabled="!selectedTableItems.length"
                @click="addToNewJobInBookingScreen"
              >
                <v-list-tile-title>
                  Create Job from Selected Points
                  <span v-if="selectedTableItems.length >= 2" class="pl-1"
                    >({{selectedTableItems.length}})</span
                  >
                </v-list-tile-title>
              </v-list-tile>
              <v-list-tile
                :disabled="!selectedTableItems.length"
                @click="viewJobSelectionDialog"
              >
                <v-list-tile-title>
                  Add Selected Points to Job
                  <span v-if="selectedTableItems.length >= 2" class="pl-1"
                    >({{selectedTableItems.length}})</span
                  >
                </v-list-tile-title>
              </v-list-tile>
              <v-divider></v-divider>
              <v-list-tile
                @click="updateUnassignedPudStatus('CANCELLED')"
                :disabled="!selectedTableItems.length"
              >
                <v-list-tile-title>
                  Cancel Selected Points
                  <span v-if="!selectedTableItems.length" class="pl-1"
                    >({{selectedTableItems.length}})</span
                  >
                </v-list-tile-title>
              </v-list-tile>
              <v-list-tile
                :disabled="!selectedTableItems.length"
                @click="addSelectedItemsToGroup"
              >
                <v-list-tile-title>
                  Group Selected Points
                  <span v-if="selectedTableItems.length >= 2" class="pl-1"
                    >({{selectedTableItems.length}})</span
                  >
                </v-list-tile-title>
              </v-list-tile>
              <v-list-tile @click="openPointManagerImport">
                <v-list-tile-title>
                  <v-layout align-center> Data Import </v-layout>
                </v-list-tile-title>
              </v-list-tile>
            </v-list>
          </v-menu>

          <v-spacer></v-spacer>
        </v-layout>
      </v-flex>
      <v-flex md12 class="point-manager-table">
        <v-data-table
          v-model="selectedTableItems"
          :headers="tableHeaders"
          :items="unassignedPudItemList"
          class="accounting-data-table"
          :loading="isLoadingTableData"
          item-key="id"
          :rows-per-page-items="[50, 100]"
        >
          <v-progress-linear
            v-slot:progress
            color="#ffa000"
            indeterminate
          ></v-progress-linear>
          <template v-slot:items="props">
            <tr class="job-search-table-row">
              <td>
                <v-layout justify-start align-center>
                  <v-checkbox
                    color="light-blue"
                    v-model="props.selected"
                    hide-details
                    :disabled="props.item.assignedStatus !== 'UNASSIGNED'"
                  ></v-checkbox>

                  <span>
                    {{ props.item.pudDetails.epochTime ?
                    returnFormattedDate(props.item.pudDetails.epochTime,
                    `DD/MM/YY HH:mm`) : '-' }}
                  </span>
                  <v-spacer></v-spacer>
                </v-layout>
              </td>
              <td>
                {{ props.item.additionalJobData &&
                props.item.additionalJobData.clientName ?
                props.item.additionalJobData.clientName : '-'}}
              </td>
              <td>
                {{ props.item.clientSuppliedId ? props.item.clientSuppliedId
                :'-' }}
              </td>
              <td>{{ props.item.pudDetails.address.suburb }}</td>
              <td>
                {{ props.item.additionalJobData &&
                props.item.additionalJobData.driverName ?
                props.item.additionalJobData.driverName : '-' }}
              </td>
              <td>
                {{ props.item.additionalJobData &&
                props.item.additionalJobData.csrAssignedId ?
                `${props.item.additionalJobData.csrAssignedId}` : '-' }}
              </td>
              <td>
                <v-icon
                  :color="props.item.groupReferenceId ? 'light-blue' : 'grey'"
                  v-html="props.item.groupReferenceId ? 'fas fa-check' : 'fal fa-times'"
                  size="14"
                >
                </v-icon>
              </td>
              <td>
                {{ props.item.creationTime ?
                returnFormattedDate(props.item.creationTime, 'DD/MM/YY HH:mm') :
                '-' }}
              </td>
              <td>{{ props.item.createdBy ? props.item.createdBy : '-' }}</td>
              <td>
                <v-layout justify-space-between>
                  <span>{{ props.item.assignedStatus }}</span>
                  <UnassignedPudListActionButtons
                    :unassignedPudId="props.item.id"
                    :clientId="props.item.clientId"
                    :assignedStatus="props.item.assignedStatus"
                    :menuView="true"
                    :unassignedPudItem="props.item"
                    :groupReferenceId="props.item.groupReferenceId"
                    @viewAllForGroup="searchForGroupId"
                    @removePointFromGroup="removePointFromGroup"
                  ></UnassignedPudListActionButtons>
                </v-layout>
              </td>
            </tr>
          </template>
        </v-data-table>
      </v-flex>
    </v-layout>

    <v-layout
      wrap
      pt-2
      class="hide-on-print"
      pa-3
      v-if="selectedDialogTab === 'requiresAttention'"
    >
      <ImportRequiresAttention :selectedDialogTab="selectedDialogTab" />
    </v-layout>
  </v-card>
  <v-dialog
    v-model="jobSelectionDialogController"
    width="30%"
    v-if="jobSelectionDialogController"
    content-class="v-dialog-custom"
  >
    <v-layout
      justify-space-between
      class="task-bar app-theme__center-content--header no-highlight"
    >
      <span>Select a Job</span>
      <div
        class="app-theme__center-content--closebutton"
        @click="jobSelectionDialogController = false"
      >
        <v-icon class="app-theme__center-content--closebutton--icon"
          >fal fa-times</v-icon
        >
      </div>
    </v-layout>

    <v-layout class="app-theme__center-content--body">
      <v-flex md12>
        <v-layout pt-3 px-3>
          <v-autocomplete
            v-if="jobListForClient"
            solo
            flat
            v-model="selectedJobId"
            :items="jobListForClient"
            item-value="jobId"
            item-text="displayId"
            color="orange"
            label="Search for a Job"
          >
            <template slot="selection" slot-scope="data">
              <span>{{ data.item.displayId }}</span>
              <span class="pl-1"> ({{ data.item.dateString }}</span>
              <span
                v-if="data.item.driverName && data.item.csrAssignedId"
                class="pl-1"
              >
                {{ `- ${data.item.csrAssignedId} ${data.item.driverName}`
                }}</span
              >)
            </template>
            <template slot="item" slot-scope="data">
              <span>{{ data.item.displayId }}</span>
              <span class="pl-1"> ({{ data.item.dateString }}</span>
              <span
                v-if="data.item.driverName && data.item.csrAssignedId"
                class="pl-1"
              >
                {{ `- ${data.item.csrAssignedId} ${data.item.driverName}`
                }}</span
              >)
            </template>
          </v-autocomplete>
        </v-layout>
        <v-layout>
          <v-divider></v-divider>
        </v-layout>
        <v-layout justify-space-between px-3 py-2>
          <v-btn
            depressed
            color="white"
            flat
            @click="jobSelectionDialogController = false"
          >
            Cancel</v-btn
          >
          <v-btn
            :disabled="selectedJobId === -1"
            depressed
            color="blue"
            @click="addToExistingJobInBookingScreen"
            >Add to Selected Job</v-btn
          >
        </v-layout>
      </v-flex>
    </v-layout>
  </v-dialog>
</v-dialog>
