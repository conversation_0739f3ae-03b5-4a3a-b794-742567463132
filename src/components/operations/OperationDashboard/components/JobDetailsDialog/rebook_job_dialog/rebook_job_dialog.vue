<template>
  <v-dialog
    v-model="showDialog"
    width="700px"
    class="ma-0"
    persistent
    no-click-animation
    content-class="v-dialog-custom"
  >
    <v-flex md12>
      <v-layout
        justify-space-between
        align-center
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>job #{{ jobDetailsClone.displayId }} - Rebook</span>
        <div
          class="app-theme__center-content--closebutton"
          @click="showDialog = false"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <v-layout row wrap class="app-theme__center-content--body dialog-content">
        <v-flex md12 v-if="jobDetails">
          <v-form ref="formRef">
            <v-layout row wrap class="body-scrollable--75" pa-3>
              <v-flex md12>
                <v-alert
                  type="info"
                  :value="
                    jobDetails.serviceTypeObject.rateTypeId ===
                      JobRateType.TRIP ||
                    jobDetails.serviceTypeObject.rateTypeId ===
                      JobRateType.UNIT ||
                    jobDetails.workStatus === WorkStatus.CANCELLED
                  "
                >
                  <p class="ma-0">Please note:</p>
                  <ul>
                    <li v-if="jobDetails.serviceTypeObject.rateTypeId === 6">
                      You are about to rebook a
                      <strong>QUOTED RATE</strong> job. Current Quoted Rate
                      amounts for Client and Fleet Asset (where applicable) will
                      be preserved where possible. Please review the dollar
                      amounts after rebooking.
                    </li>
                    <li v-if="jobDetails.serviceTypeObject.rateTypeId === 5">
                      You are about to rebook a <strong>UNIT RATE</strong> job.
                      All current Zone and Unit values will be included in the
                      rebooked job.
                    </li>
                    <li
                      v-if="
                        jobDetails.serviceTypeObject.rateTypeId === 5 &&
                        reverseLegOrderController
                      "
                    >
                      All Zone and Unit rate information will be included when
                      reversing leg order (all Zones will also be in reverse).
                      Please closely review the job to ensure all Zone and Unit
                      information is correct.
                    </li>
                    <li v-if="jobDetails.workStatus === 0">
                      You are rebooking a <strong>CANCELLED</strong> job. Some
                      rate information may be missing or incomplete. Please
                      review the rebooked job to ensure all information is
                      correct.
                    </li>
                  </ul>
                </v-alert>
              </v-flex>
              <v-flex md12 class="pt-2">
                <v-layout align-center>
                  <h5 class="subheader--bold pr-3">Booking Options</h5>
                  <v-flex>
                    <v-divider></v-divider>
                  </v-flex>
                  <div class="pl-2">
                    <InformationTooltip :bottom="true">
                      <v-layout slot="content"
                        >Rebook includes allows you to define what should and
                        should not be copied across to the new job.</v-layout
                      >
                    </InformationTooltip>
                  </div>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-layout>
                  <v-flex md4>
                    <v-layout align-center class="form-field-label-container">
                      <h6 class="subheader--faded pr-3 pb-0">
                        Job References:
                      </h6>
                    </v-layout>
                  </v-flex>
                  <v-flex md8>
                    <v-checkbox
                      v-model="includeJobReferences"
                      label="Include existing Job References"
                      color="light-blue"
                      persistent-hint
                      hint="Rebook this job with its current Job References"
                      class="mt-2"
                    />
                  </v-flex>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-layout>
                  <v-flex md4>
                    <v-layout align-center class="form-field-label-container">
                      <h6 class="subheader--faded pr-3 pb-0">
                        Pickup/Drop References:
                      </h6>
                    </v-layout>
                  </v-flex>
                  <v-flex md8>
                    <v-checkbox
                      v-model="includePickupDropReferences"
                      label="Include existing Pickup/Drop References"
                      color="light-blue"
                      persistent-hint
                      hint="Rebook this job with its current Pickup/Dropoff references for all stops"
                      class="mt-2"
                    />
                  </v-flex>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-layout>
                  <v-flex md4>
                    <v-layout align-center class="form-field-label-container">
                      <h6 class="subheader--faded pr-3 pb-0">
                        Payload (Manifest Items):
                      </h6>
                    </v-layout>
                  </v-flex>
                  <v-flex md8>
                    <v-checkbox
                      label="Include Manifest Payload Information"
                      v-model="includeManifestPayloadInformation"
                      color="light-blue"
                      persistent-hint
                      hint="Rebook this job with all current Manifest Payload Details for all stops"
                      class="mt-2"
                    />
                  </v-flex>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-layout>
                  <v-flex md4>
                    <v-layout align-center class="form-field-label-container">
                      <h6 class="subheader--faded pr-3 pb-0">
                        Payload (Overall):
                      </h6>
                    </v-layout>
                  </v-flex>
                  <v-flex md8>
                    <v-checkbox
                      label="Include Overall Payload Information"
                      v-model="includeOverallPayloadInformation"
                      color="light-blue"
                      persistent-hint
                      hint="Rebook this job with its current values for Overall Payload"
                      class="mt-2"
                    />
                  </v-flex>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-layout>
                  <v-flex md4>
                    <v-layout align-center class="form-field-label-container">
                      <h6 class="subheader--faded pr-3 pb-0">Job Notes:</h6>
                    </v-layout>
                  </v-flex>
                  <v-flex md8>
                    <v-checkbox
                      label="Include all existing Job Notes"
                      v-model="includeJobNotes"
                      color="light-blue"
                      persistent-hint
                      hint="Rebook this job with all current Job Notes"
                      class="mt-2"
                    />
                  </v-flex>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-layout>
                  <v-flex md4>
                    <v-layout align-center class="form-field-label-container">
                      <h6 class="subheader--faded pr-3 pb-0">
                        Pickup/Drop Notes:
                      </h6>
                    </v-layout>
                  </v-flex>
                  <v-flex md8>
                    <v-checkbox
                      label="Include all existing Pickup/Drop Notes"
                      v-model="includePickupDropNotes"
                      color="light-blue"
                      persistent-hint
                      hint="Rebook this job with all current Pickup/Drop notes, for all stops"
                      class="mt-2"
                    />
                  </v-flex>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-layout>
                  <v-flex md4>
                    <v-layout align-center class="form-field-label-container">
                      <h6 class="subheader--faded pr-3 pb-0">Leg Order:</h6>
                    </v-layout>
                  </v-flex>
                  <v-flex md8>
                    <v-checkbox
                      label="Rebook with reversed Leg Order"
                      v-model="reverseLegOrderController"
                      color="light-blue"
                      persistent-hint
                      :disabled="!jobDetails.pudItems.length"
                      hint="Rebook with the same stops in the reverse order. This is useful for creating a return trip."
                      class="mt-2"
                    />
                  </v-flex>
                </v-layout>
              </v-flex>

              <v-flex md12 class="pt-4 pb-2">
                <v-layout align-center>
                  <h5 class="subheader--bold pr-3">Booking Date and Time</h5>
                  <v-flex>
                    <v-divider></v-divider>
                  </v-flex>
                  <div class="pl-2">
                    <InformationTooltip :bottom="true">
                      <v-layout slot="content"
                        >The date the new job should be booked for.</v-layout
                      >
                    </InformationTooltip>
                  </div>
                </v-layout>
              </v-flex>
              <v-flex md6 class="pr-1">
                <DatePickerBasic
                  @setEpoch="setMainJobDate"
                  v-model="mainJobDate"
                  :labelName="'Please select the first leg arrival time'"
                  :epochTime="mainJobDate"
                  :hideIcon="false"
                  hint="Please select the first leg arrival time"
                  class="v-solo-custom"
                  :solo-input="true"
                  :allowNegativeEpoch="false"
                  persistent-hint
                >
                </DatePickerBasic>
              </v-flex>
              <v-flex md6 class="pl-1">
                <v-text-field
                  v-model="firstPudArrival"
                  :rules="[validate.twentyFourHourTime]"
                  hint="Arrival Time (24-hour time)"
                  mask="##:##"
                  persistent-hint
                  label="Arrival Time"
                  @focus="$event.target.select()"
                  class="v-solo-custom"
                  flat
                  solo
                  color="white"
                  prepend-icon="schedule"
                />
              </v-flex>

              <v-flex md12 class="pt-4">
                <v-layout align-center>
                  <h5 class="subheader--bold pr-3">
                    Driver Pre-Allocation (Optional)
                  </h5>
                  <v-flex>
                    <v-divider></v-divider>
                  </v-flex>
                  <v-btn
                    v-if="hasValidAllocationSelections"
                    small
                    flat
                    outline
                    class="v-btn-rounded"
                    color="accent"
                    @click="deallocateCurrentSelection"
                    ><v-icon left size="16">fal fa-times</v-icon
                    >Deallocate</v-btn
                  >
                  <div class="pl-2">
                    <InformationTooltip :bottom="true">
                      <v-layout slot="content"
                        >The driver that you wish to pre-allocate to the new
                        job. This is not required to rebook.</v-layout
                      >
                    </InformationTooltip>
                  </div>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <AllocateDriver
                  ref="allocateDriverComponent"
                  :type="ObjectToAllocate.JOB_DETAILS"
                  :onValidSelection="OnValidAllocationTarget.EMIT"
                  :fleetAssetId.sync="fleetAssetToPreAllocate"
                  :driverId.sync="driverToPreAllocate"
                  :serviceTypeId="jobDetails.serviceTypeId"
                  :rateTypeId="rateToApplyToDriver"
                  :clientId="jobDetails.client.id"
                  :fleetAssetRates="fleetAssetAccountingRates"
                  :searchDate="jobDetails.jobRunEpoch"
                  :isFormDisabled="!isAllocationEnabled"
                  @submitPreallocation="handleAllocationDetails"
                />
              </v-flex>
            </v-layout>
          </v-form>
        </v-flex>
        <v-flex md12>
          <v-divider></v-divider>
          <v-layout align-center>
            <v-btn flat color="red" @click="showDialog = false">Cancel</v-btn>
            <v-spacer></v-spacer>

            <v-btn
              depressed
              color="blue"
              @click="bookJob"
              :disabled="requestingPlannedRoute || requestingDepotDurations"
              :loading="isAwaitingSaveResponse"
              class="v-btn-confirm-custom"
              >{{
                hasValidAllocationSelections
                  ? `Pre-allocate & Book`
                  : `Book Job`
              }}</v-btn
            >
          </v-layout>
        </v-flex>

        <JobBookingSuccessDialog
          v-if="successDialogIsOpen && successDialogConfig"
          class="success-dialog"
          :isDialogOpen="successDialogIsOpen"
          :bookingDetails="successDialogConfig"
          @bookNewJob="(successDialogIsOpen = false), (showDialog = false)"
          @editRebookJob="editJob"
          @returnToDashboard="
            (successDialogIsOpen = false), (showDialog = false)
          "
        />

        <ConfirmationDialog
          v-if="editJobRequiresConfirmation"
          :requiresButtonToOpen="false"
          title="Leave this page ?"
          buttonText="Confirm"
          message="You are about to leave the current page and go to the job booking page. Any unsaved changes will be lost. Do you wish to continue?"
          @confirm="confirmEditJob"
          confirmationButtonText="Confirm"
          @closeDialog="editJobRequiresConfirmation = false"
          :dialogIsActive="true"
        />
      </v-layout>
    </v-flex>
  </v-dialog>
</template>

<script lang="ts" setup>
import JobBookingSuccessDialog from '@/components/booking/job_booking_success_dialog.vue';
import AllocateDriver from '@/components/common/allocate_driver/allocate_driver.vue';
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import {
  ObjectToAllocate,
  OnValidAllocationTarget,
} from '@/helpers/AllocationHelpers.ts/AllocationHelpers';
import {
  returnFormattedDate,
  returnFormattedTime,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { copyLegSpecificValuesToNewType } from '@/helpers/JobDataHelpers/JobDataHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showJobBookedNotification,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { returnFuelSurchargeForAccounting } from '@/helpers/RateHelpers/FuelSurchargeHelpers';
import {
  getClientPrimaryTripRate,
  getFleetAssetPrimaryTripRate,
} from '@/helpers/RateHelpers/TripRateHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { initialiseJobDetails } from '@/helpers/classInitialisers/InitialiseJobDetails';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import { JobAccountingDetails } from '@/interface-models/Generic/Accounting/JobAccountingDetails';
import JobPrimaryRate from '@/interface-models/Generic/Accounting/JobPrimaryRate';
import Communication from '@/interface-models/Generic/Communication/Communication';
import Dimensions from '@/interface-models/Generic/Dimensions/Dimensions';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { Validation } from '@/interface-models/Generic/Validation';
import { AllocationSummary } from '@/interface-models/Jobs/Allocation/AllocateJobRequest';
import JobStatusUpdate from '@/interface-models/Jobs/Event/JobStatusUpdate';
import {
  JobBookingType,
  JobSuccessDialogConfig,
} from '@/interface-models/Jobs/JobBookingType';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { JobOperationType } from '@/interface-models/Jobs/JobOperationType';
import JobReferenceDetails from '@/interface-models/Jobs/JobReferenceDetails';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import { ClientServiceRateVariations } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariations';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import { useAppNavigationStore } from '@/store/modules/AppNavigationStore';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { useServiceRateStore } from '@/store/modules/ServiceRateStore';
import { sessionManager } from '@/store/session/SessionState';
import moment from 'moment-timezone';
import {
  computed,
  ComputedRef,
  onBeforeMount,
  ref,
  Ref,
  WritableComputedRef,
} from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';

const props = defineProps<{
  jobToRebook: JobDetails;
  showRebookDialog: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:showRebookDialog', value: boolean): void;
  (e: 'closeDialog', value: boolean): void;
}>();

const router = useRouter();
const route = useRoute();

const serviceRateStore = useServiceRateStore();
const operationsStore = useOperationsStore();

const requestingFuelRates: Ref<boolean> = ref(false);
const requestingClientServiceRates: Ref<boolean> = ref(false);

const clientDetails: Ref<ClientDetails | null> = ref(null);
const clientServiceRate: Ref<ClientServiceRate | null> = ref(null);
const clientFuelSurcharges: Ref<ClientFuelSurchargeRate[] | null> = ref([]);
const clientServiceRateVariations: Ref<ClientServiceRateVariations[] | null> =
  ref([]);

const firstPudArrival: Ref<string> = ref('');
const mainJobDate: Ref<number | null> = ref(null);
const rateToApply: Ref<RateTableItems | null> = ref(null);
const fleetAssetToPreAllocate: Ref<string> = ref('');
const driverToPreAllocate: Ref<string> = ref('');
const fleetAssetAccountingRates: Ref<JobPrimaryRate[]> = ref([]);
const isAllocationEnabled: Ref<boolean> = ref(true);
const allocateDriverComponent = ref<InstanceType<typeof AllocateDriver> | null>(
  null,
);

const includeJobReferences: Ref<boolean> = ref(true);
const includePickupDropReferences: Ref<boolean> = ref(true);
const includeManifestPayloadInformation: Ref<boolean> = ref(true);
const includeOverallPayloadInformation: Ref<boolean> = ref(true);
const includeJobNotes: Ref<boolean> = ref(false);
const includePickupDropNotes: Ref<boolean> = ref(false);
const reverseLegOrder: Ref<boolean> = ref(false);

const jobDetailsClone: Ref<JobDetails> = ref(new JobDetails());
const requestingPlannedRoute: Ref<boolean> = ref(false);
const requestingDepotDurations: Ref<boolean> = ref(false);
const isAwaitingSaveResponse: Ref<boolean> = ref(false);

const successDialogIsOpen: Ref<boolean> = ref(false);
const successDialogConfig: Ref<JobSuccessDialogConfig | null> = ref(null);
const reBookJobId: Ref<number | null> = ref(null);
const clientId: Ref<string | null> = ref(null);
const savedJobDetails: Ref<JobDetails> = ref(new JobDetails());
const editJobRequiresConfirmation: Ref<boolean> = ref(false);

/** Validation rules for the form */
const validate: ComputedRef<Validation> = computed(() => validationRules);

/** Returns the name of the current route */
const currentRouteName: ComputedRef<string> = computed((): string => {
  return route?.name || '';
});

/** Whether the current route is operations */
const onOperations: ComputedRef<boolean> = computed((): boolean => {
  return currentRouteName.value === 'operations_index';
});

/** Computed value of the new job that will be rebooked */
const jobDetails: ComputedRef<JobDetails | undefined> = computed(() => {
  if (!props.showRebookDialog) {
    return;
  }
  const jobDetails = initialiseJobDetails(jobDetailsClone.value);

  delete jobDetails.jobId;
  delete jobDetails._id;
  jobDetails.recurringJobId = null;
  jobDetails.recurringJobReference = null;
  jobDetails.statusList = [25];
  jobDetails.accounting = new JobAccountingDetails();

  jobDetails.jobSourceType = null;
  jobDetails.exportType = null;

  if (!includeJobReferences.value) {
    jobDetails.jobReference = [new JobReferenceDetails()];
  }

  jobDetails.eventList = [];
  jobDetails.additionalAssets = [];
  if (clientServiceRate.value && rateToApply.value) {
    jobDetails.addClientRateToJob(clientServiceRate.value, rateToApply.value);

    // Add client fuel surcharge and rate variations to accounting object
    jobDetails.accounting.additionalCharges.clientFuelSurcharge =
      returnFuelSurchargeForAccounting({
        jobDetails,
        isFuelApplicable: rateToApply.value.isClientFuelApplied,
        fuelSurcharges: clientFuelSurcharges.value,
      });
    jobDetails.accounting.clientServiceRateVariations =
      rateToApply.value.getRateVariation(
        clientServiceRateVariations.value ?? [],
      );
  }

  if (
    jobDetailsClone.value.serviceTypeObject.rateTypeId === JobRateType.TRIP &&
    clientTripRate.value
  ) {
    const primaryRate = getClientPrimaryTripRate(
      clientTripRate.value,
      jobDetailsClone.value.serviceTypeId,
    );
    jobDetails.accounting.clientRates.push(primaryRate);
  }
  if (rateToApplyToDriver.value === JobRateType.TRIP) {
    const primaryRate: JobPrimaryRate | null = getFleetAssetPrimaryTripRate(
      driversTripRate.value,
    );
    if (primaryRate) {
      jobDetails.accounting.fleetAssetRates.push(primaryRate);
    }
  }

  // If we have all required details (fleet asset id, driver id and rates), then
  // we can save the job as preallocated
  if (hasValidAllocationSelections.value) {
    if (rateToApplyToDriver.value !== 6) {
      jobDetails.accounting.fleetAssetRates = fleetAssetAccountingRates.value;
    }

    jobDetails.driverId = driverToPreAllocate.value;
    jobDetails.fleetAssetId = fleetAssetToPreAllocate.value;
    jobDetails.statusList = [];
    jobDetails.statusList.push(26);
  } else {
    jobDetails.driverId = '';
    jobDetails.fleetAssetId = '';
    jobDetails.accounting.fleetAssetRates = [];
  }

  const puds = jobDetails.pudItems.filter(
    (x: PUDItem) => x.legTypeFlag === 'P' || x.legTypeFlag === 'D',
  );
  if (jobDetails.pudItems.length === 0) {
    return jobDetails;
  }

  jobDetails.pudItems = setNewPudTimes(puds);

  jobDetails.pudItems.forEach((pud: PUDItem) => {
    pud.pudId = '';
    pud.attachments = [];
    pud.status = null;
    pud.pickupDate = returnStartOfDayFromEpoch(pud.epochTime);
    pud.pickupTime = returnFormattedTime(pud.epochTime);
    if (!includePickupDropReferences.value) {
      pud.pickupReference = [];
      pud.dropoffReference = [];
    }

    if (!includeManifestPayloadInformation.value) {
      pud.manifest = [];
    }

    if (!includeOverallPayloadInformation.value) {
      pud.weight = null;
      pud.dimensions = new Dimensions();
    }

    if (!includePickupDropNotes.value) {
      pud.notes = [];
    }

    pud.unassignedPudItemReference = [];
  });

  if (includeJobNotes.value) {
    jobDetails.notes = jobDetails.notes.filter(
      (x: Communication) => x.type && x.type.id !== 1,
    );
  } else {
    jobDetails.notes = jobDetails.notes.filter(
      (x: Communication) => x.type?.id === 2,
    );
  }

  return jobDetails;
});

/** Controls dialog visibility */
const showDialog: WritableComputedRef<boolean> = computed({
  get() {
    return props.showRebookDialog;
  },
  set(value: boolean) {
    emit('update:showRebookDialog', value);
  },
});

/**
 * Returns true if all required fields have been returned from the
 * AllocateDriver component to satisfy pre-allocation
 */
const hasValidAllocationSelections: ComputedRef<boolean> = computed(() => {
  return (
    fleetAssetToPreAllocate.value !== '' &&
    driverToPreAllocate.value !== '' &&
    fleetAssetAccountingRates.value.length > 0
  );
});

/**
 * Handles the emit from the AllocateDriver component containing the details of
 * an allocation. If the selection is not valid, it resets the fleet asset and
 * driver IDs in the current edited recurring job.
 * @param isValid - boolean indicating whether the selection is valid or not.
 */
function handleAllocationDetails(allocationDetails: AllocationSummary | null) {
  const isValid = allocationDetails !== null;
  // If the selection is not valid, reset the fleet asset and driver IDs
  if (isValid) {
    driverToPreAllocate.value = allocationDetails.driverId;
    fleetAssetToPreAllocate.value = allocationDetails.fleetAssetId;
    fleetAssetAccountingRates.value = allocationDetails.fleetAssetRate;
  } else {
    driverToPreAllocate.value = '';
    fleetAssetToPreAllocate.value = '';
    fleetAssetAccountingRates.value = [];
  }
  isAllocationEnabled.value = !isValid;
}

/**
 * Clears the fleet asset and driver from the currently edited recurring job.
 */
function deallocateCurrentSelection() {
  allocateDriverComponent.value?.clearInputs();
  isAllocationEnabled.value = true;
}

/** Controller for reverseLegOrder boolean. */
const reverseLegOrderController: WritableComputedRef<boolean> = computed({
  get() {
    return reverseLegOrder.value;
  },
  set(value: boolean) {
    jobDetailsClone.value.pudItems.reverse();
    jobDetailsClone.value.pudItems.forEach((pudItem) => {
      const switchToLegType: string =
        pudItem.legTypeFlag === 'P' ? 'dropoff' : 'pickup';
      copyLegSpecificValuesToNewType(
        switchToLegType,
        pudItem,
        clientDetails.value,
        jobDetailsClone.value.serviceTypeId,
      );
    });
    updateRouteAndDurations();
    reverseLegOrder.value = value;
  },
});

/**
 * Updates the planned route and depot durations for the job.
 */
async function updateRouteAndDurations(): Promise<void> {
  requestingPlannedRoute.value = true;
  requestingDepotDurations.value = true;
  await Promise.all([
    getPlannedRoute(),
    jobDetailsClone.value.requestDepotDurations(),
  ]);
  requestingPlannedRoute.value = false;
  requestingDepotDurations.value = false;
}

/**
 * Sends a request to get the job's planned route.
 */
async function getPlannedRoute(): Promise<void> {
  const result = await jobDetailsClone.value.getPlannedRoute();
  if (result) {
    jobDetailsClone.value.plannedRoute = result;
    jobDetailsClone.value.setPudTimesFromPlannedRoute(
      jobDetailsClone.value.plannedRoute,
    );
  }
}

/** Returns the rate type to apply to the driver based on job details. */
const rateToApplyToDriver: ComputedRef<JobRateType> = computed(() => {
  if (
    jobDetailsClone.value.accounting.fleetAssetRates?.[0]?.rate?.rateTypeId ===
    JobRateType.TRIP
  ) {
    return JobRateType.TRIP;
  } else if (
    jobDetailsClone.value.serviceTypeObject.rateTypeId === JobRateType.TRIP
  ) {
    return (
      useCompanyDetailsStore().divisionCustomConfig?.operations
        ?.defaultRateTypeId ?? JobRateType.TIME
    );
  } else {
    return jobDetailsClone.value.serviceTypeObject.rateTypeId;
  }
});

/** Returns the client trip rate if applicable. */
const clientTripRate: ComputedRef<RateTableItems | null> = computed(() => {
  if (
    jobDetailsClone.value.serviceTypeObject.rateTypeId === JobRateType.TRIP &&
    jobDetailsClone.value.accounting?.clientRates?.[0]?.rate
  ) {
    return jobDetailsClone.value.accounting.clientRates[0].rate;
  }
  return null;
});

/** Returns the driver's trip rate if applicable. */
const driversTripRate: ComputedRef<RateTableItems | null> = computed(() => {
  if (
    rateToApplyToDriver.value === JobRateType.TRIP &&
    jobDetailsClone.value.accounting?.fleetAssetRates?.[0]?.rate
  ) {
    return jobDetailsClone.value.accounting.fleetAssetRates[0].rate;
  }
  return null;
});

/**
 * Initialises the component state for rebooking.
 */
async function initialiseComponent(): Promise<void> {
  jobDetailsClone.value = initialiseJobDetails(props.jobToRebook);
  setDateAndTime();

  if (jobDetailsClone.value.serviceTypeObject.rateTypeId === JobRateType.TRIP) {
    return;
  }

  requestingClientServiceRates.value = true;
  requestingFuelRates.value = true;

  const searchQuery = {
    clientId:
      jobDetailsClone.value.client.id === 'CS'
        ? '0'
        : jobDetailsClone.value.client.id,
    searchDate: moment().valueOf(),
  };
  if (jobDetailsClone.value.client.id === 'CS') {
    const defaultRates =
      await serviceRateStore.getCurrentDivisionDefaultRates();
    setServiceRate(defaultRates?.clientServiceRate);
    return;
  }

  // Fetch service rates, fuel and rate variations
  const [
    clientDetailsResult,
    { clientRates, clientFuelRates, serviceRateVariations },
  ] = await Promise.all([
    useClientDetailsStore().requestClientDetailsByClientId(
      jobDetailsClone.value.client.id,
    ),
    serviceRateStore.getCurrentRatesAndFuelForClientId(
      searchQuery.clientId,
      searchQuery.searchDate,
    ),
  ]);
  if (clientDetailsResult) {
    clientDetails.value = clientDetailsResult;
  }
  setServiceRate(clientRates?.clientServiceRate);
  clientFuelSurcharges.value = clientFuelRates || [];
  clientServiceRateVariations.value = serviceRateVariations || [];
}

/**
 * Sets new PUD times based on the first PUD epoch time and time differences.
 */
function setNewPudTimes(puds: PUDItem[]): PUDItem[] {
  const timeDifferences: number[] = [];
  for (let i = 1; i < puds.length; i++) {
    timeDifferences.push(puds[i].epochTime - puds[i - 1].epochTime);
  }
  puds[0].epochTime = firstPudEpochTime.value;
  for (let i = 1; i < puds.length; i++) {
    puds[i].epochTime = puds[i - 1].epochTime + timeDifferences[i - 1];
    puds[i].status = null;
  }
  return puds;
}

/**
 * Sets the main job date.
 */
function setMainJobDate(epochDate: any): void {
  mainJobDate.value = epochDate;
}

/**
 * Sets initial values for date and time of first PUD.
 */
function setDateAndTime(): void {
  const currentTime = moment().valueOf();
  firstPudArrival.value = returnFormattedDate(currentTime, 'HHmm');
  mainJobDate.value = currentTime;
}

/** Computed epoch date of user's entered date and time. */
const firstPudEpochTime: ComputedRef<number> = computed(() => {
  if (!mainJobDate.value || !firstPudArrival.value) {
    return 0;
  }
  return moment
    .tz(
      returnFormattedDate(mainJobDate.value, 'DDMMYYYY') +
        ' ' +
        firstPudArrival.value,
      'DDMMYYYY HHmm',
      useCompanyDetailsStore().userLocale,
    )
    .valueOf();
});

/**
 * Books the job and handles the result.
 */
async function bookJob(): Promise<void> {
  if (
    !jobDetails.value ||
    (jobDetails.value.pudItems.length > 1 && !jobDetails.value.plannedRoute)
  ) {
    showNotification(`Job booking failed. No route data.`, {
      title: 'Rebook Job',
    });
    logConsoleError(
      `Failed to rebook job from ${jobDetails.value?.displayId}, with addresses:`,
      jobDetails.value?.pudItems.map((x) => x.address),
    );
    return;
  }
  isAwaitingSaveResponse.value = true;

  if ('lastUpdated' in jobDetails.value) {
    delete (jobDetails.value as any).lastUpdated;
  }

  const result = await useJobStore().saveJobDetails(jobDetails.value);
  if (result?.jobDetails) {
    reBookJobId.value = result.jobId;
    clientId.value = result.jobDetails.client.id;
    savedJobDetails.value = result.jobDetails;

    const bookedEvent = result.jobDetails.eventList.find(
      (x: JobStatusUpdate) => x.updatedStatus === 'Booked',
    );
    if (bookedEvent?.editedBy !== sessionManager.getActiveUser()) {
      showJobBookedNotification({
        jobId: result.jobId,
        jobDate: result.jobDetails.jobDate,
        clientName: result.jobDetails.client.clientName,
      });
    } else {
      successDialogConfig.value = {
        type: JobBookingType.REBOOK,
        details: result?.jobDetails,
        clientDetails: clientDetails.value,
      };
      successDialogIsOpen.value = true;
    }
  } else {
    showNotification(GENERIC_ERROR_MESSAGE, { title: 'Rebook Job' });
    showDialog.value = false;
  }
  isAwaitingSaveResponse.value = false;
}

/**
 * Sets the client service rate for the job.
 */
function setServiceRate(serviceRate?: ClientServiceRate | null): void {
  if (!serviceRate) {
    showNotification('No service rates available.', { title: 'Rebook Job' });
    showDialog.value = false;
    return;
  }
  clientServiceRate.value = serviceRate;
  const rate: RateTableItems | null = serviceRate.rateToApplyToJob(
    jobDetailsClone.value.serviceTypeId,
    jobDetailsClone.value.serviceTypeObject.rateTypeId,
  );
  if (!rate) {
    showDialog.value = false;
  } else {
    rateToApply.value = rate;
  }
  requestingClientServiceRates.value = false;
}

/**
 * Closes all open dialogs, including success and parent jobDetails dialogs.
 */
function closeAllDialog(): void {
  closeSuccessDialog();
  emit('closeDialog', true);
}

/**
 * Closes the success dialog and resets its configuration.
 */
function closeSuccessDialog(): void {
  successDialogIsOpen.value = false;
  successDialogConfig.value = null;
  showDialog.value = false;
}

/**
 * Handles the edit job action.
 */
function editJob(): void {
  if (!onOperations.value) {
    editJobRequiresConfirmation.value = true;
    return;
  } else {
    proceedToEditJob();
  }
}

/**
 * Proceeds with job editing after user confirms.
 */
function confirmEditJob(): void {
  editJobRequiresConfirmation.value = false;
  const reBookJobBookingPageConfig = {
    jobId: reBookJobId.value ?? 0,
    operationType: JobOperationType.EDIT,
    jobDetails: savedJobDetails.value,
    clientId: clientId.value ?? '',
  };

  operationsStore.setReBookJobBookingPageConfig(reBookJobBookingPageConfig);

  router.push('/operations').then(() => {
    useAppNavigationStore().setCurrentRouteTitle('operations_index');
    useAppNavigationStore().setCurrentComponentId('#job-booking');
  });
}

/**
 * Executes the final job editing logic by closing dialogs
 * and setting the selected job ID in the booking screen.
 */
function proceedToEditJob(): void {
  closeAllDialog();
  if (reBookJobId.value) {
    operationsStore.setSelectedBookingScreenJobId(reBookJobId.value);
  }
}

onBeforeMount(() => {
  initialiseComponent();
});
</script>
