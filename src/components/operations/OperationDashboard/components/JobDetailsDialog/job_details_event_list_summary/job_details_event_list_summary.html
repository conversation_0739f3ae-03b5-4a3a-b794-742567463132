<section
  class="job-details-event-list-summary"
  :class="denseList ? 'pa-1' : 'pa-3'"
>
  <!-- Tabs for switching between List and Timeline views -->
  <v-flex md12 class="header-container">
    <v-layout row wrap align-center class="view-toggle-buttons">
      <v-btn
        :class="{ 'v-btn--active': activeTab === 'list' }"
        @click="activeTab = 'list'"
      >
        <v-icon left class="mr-2">format_list_bulleted</v-icon>
        Event List
      </v-btn>
      <v-btn
        :class="{ 'v-btn--active': activeTab === 'timeline' }"
        :disabled="isOutsideHire"
        @click="activeTab = 'timeline'"
      >
        <v-icon left class="mr-2">timeline</v-icon>
        Timeline
      </v-btn>
    </v-layout>
  </v-flex>

  <!-- List View -->
  <v-layout
    v-show="activeTab === 'list'"
    :class="denseList ? '' : 'pa-2 app-bgcolor--400 app-bordercolor--600 app-borderside--a'"
  >
    <v-flex class="md12">
      <v-layout v-if="showTitle">
        <v-flex md12 px-2 pb-3 v-if="isOutsideHire">
          <v-alert type="warning" class="ma-0" :value="isOutsideHire"
            >This job is allocated to an OUTSIDE HIRE. The list of times below
            may be incomplete or inaccurate.
          </v-alert>
        </v-flex>
      </v-layout>
      <v-layout row wrap>
        <v-flex md6 v-if="splitColumnView" pr-1>
          <v-layout px-2 pt-1 v-if="showTitle">
            <h6>Pickup/Dropoff Status History</h6>
          </v-layout>
          <v-layout pb-2 v-if="showTitle">
            <v-divider class="ml-2"></v-divider>
          </v-layout>
          <v-layout
            v-for="(eventItem, index) in pudEventList"
            :key="eventItem.id"
            class="eventlist-item"
            :class="[allowSelection ? 'with-hover' : '', denseList ? 'dense-list align-center' : 'py-1 pl-2 align-start', selectedEventItemId === eventItem.id ? 'eventlist-item--selected' : '']"
            @click="allowSelection ? emitStartAndEndEpoch(eventItem) : null"
            row
            wrap
          >
            <v-flex md12>
              <v-layout>
                <span
                  class="eventlist-item__date"
                  :class="eventItem.differentDateFromPrevious"
                  v-if="!showTravelDuration"
                  ><span v-if="eventItem.differentDateFromPrevious"
                    >{{eventItem.readableDate}}</span
                  ></span
                >
                <span
                  class="eventlist-item__time"
                  :class="[eventItem.eventType === 'JobEvent' ? 'jobtype' : '', eventItem.eventType  === 'PudEvent' ? 'pudtype' : '', eventItem.eventType === 'ChatMessage' ? 'chattype' : '', eventItem.eventType === 'DisplayEvent' || eventItem.eventType === 'EDIUpdate'  ? 'displaytype' : '',
                  eventItem.eventType === 'TravelTime' ? 'traveltype' : '']"
                  >{{eventItem.readableTime}}</span
                >
                <v-flex
                  v-if="eventItem.eventType === 'JobEvent' || eventItem.eventType === 'DisplayEvent' || eventItem.eventType === 'EDIUpdate'"
                >
                  <span class="eventlist-item__title">
                    {{eventItem.eventTitle}}
                    <span
                      v-if="eventItem.numberOfDuplicateEvents !== 0 && eventItem.eventType === 'DisplayEvent'"
                      >({{eventItem.numberOfDuplicateEvents + 1}})</span
                    >
                  </span>
                  <span
                    class="eventlist-item__connector-text"
                    v-if="showActionedBy"
                  >
                    by
                  </span>
                  <span class="eventlist-item__username" v-if="showActionedBy">
                    {{eventItem.actionedBy}}
                  </span>
                </v-flex>
                <v-flex v-if="eventItem.eventType === 'PudEvent'">
                  <span class="eventlist-item__title">
                    {{eventItem.eventTitle}}
                  </span>
                  <span
                    class="eventlist-item__connector-text"
                    v-if="eventItem.eventTitle === 'ARRIVED'"
                  >
                    at
                  </span>
                  <span
                    class="eventlist-item__connector-text"
                    v-if="eventItem.eventTitle === 'DEPARTED'"
                  >
                    from
                  </span>
                  <span
                    class="eventlist-item__connector-text"
                    v-if="eventItem.eventTitle === 'RESET'"
                  >
                    progress of
                  </span>
                  <span class="eventlist-item__title">
                    {{eventItem.eventSubtitle}}
                  </span>
                  <span
                    class="eventlist-item__chat-content pl-1"
                    v-if="showOnSiteDuration"
                  >
                    {{eventItem.longContent}}
                  </span>
                  <span
                    class="eventlist-item__connector-text"
                    v-if="showActionedBy"
                  >
                    by
                  </span>
                  <span class="eventlist-item__username" v-if="showActionedBy">
                    {{eventItem.actionedBy}}
                  </span>
                </v-flex>
                <v-flex v-if="eventItem.eventType === 'ChatMessage'">
                  <v-layout>
                    <span class="eventlist-item__title">
                      {{eventItem.eventTitle}}
                    </span>
                    <span
                      class="eventlist-item__connector-text"
                      v-if="showActionedBy"
                    >
                      from
                    </span>
                    <span
                      class="eventlist-item__username"
                      v-if="showActionedBy"
                    >
                      {{eventItem.actionedBy}}
                    </span>
                  </v-layout>
                  <v-layout class="eventlist-item__chat-container">
                    <span class="eventlist-item__chat-content"
                      >{{eventItem.longContent}}</span
                    >
                  </v-layout>
                </v-flex>
                <v-flex v-if="eventItem.eventType === 'TravelTime'">
                  <v-layout>
                    <span class="eventlist-item__title">
                      {{eventItem.eventTitle}}
                    </span>
                    <span class="eventlist-item__chat-content pl-1"
                      >{{eventItem.eventSubtitle}}</span
                    >
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12 v-if="eventItem.loadTimeDescription">
              <v-layout style="padding-left: 40px">
                <span class="eventlist-item__date"> </span>
                <span class="eventlist-item__change-list-text">
                  Load time: {{ eventItem.loadTimeDescription }}
                </span>
              </v-layout>
            </v-flex>
            <v-flex md12 v-if="eventItem.travelTimeDescription">
              <v-layout style="padding-left: 40px">
                <span class="eventlist-item__date"> </span>
                <span
                  class="eventlist-item__change-list-text show-asterisk-marker-left"
                >
                  Travel time: {{ eventItem.travelTimeDescription }}
                </span>
              </v-layout>
            </v-flex>
            <v-flex
              md12
              v-if="eventItem.changeHistory && eventItem.changeHistory.length > 0"
              v-for="changeHistory in eventItem.changeHistory"
              :key="changeHistory.id"
            >
              <v-layout style="padding-left: 40px">
                <span class="eventlist-item__date"> </span>
                <span
                  class="eventlist-item__change-list-text"
                  v-if="changeHistory.isOriginal"
                >
                  - Actioned at {{ changeHistory.changedTime }} ({{
                  changeHistory.changedBy }})
                </span>
                <span class="eventlist-item__change-list-text" v-else>
                  - {{ changeHistory.newValue ? `Changed to
                  ${changeHistory.newValue}` : `Changed to NOT
                  ${eventItem.eventTitle}` }} from {{ changeHistory.oldValue ?
                  changeHistory.oldValue : `NOT ${eventItem.eventTitle}` }} ({{
                  changeHistory.changedBy }} - {{ changeHistory.changedTime }} )
                </span>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex
          :md12="!splitColumnView"
          :md6="splitColumnView"
          :pl-1="splitColumnView"
        >
          <v-layout px-2 pt-1 v-if="showTitle">
            <h6>Job Status History</h6>
          </v-layout>
          <v-layout pb-2 v-if="showTitle">
            <v-divider class="ml-2"></v-divider>
          </v-layout>
          <v-layout
            justify-center
            class="has-edit-message"
            v-if="jobEventList?.some(e => e.hasEdits)"
          >
            <span class="edit-marker">*</span>Event times have been manually
            adjusted.
          </v-layout>
          <v-layout
            justify-center
            class="has-edit-message"
            v-if="!jobLegsInCorrectOrder"
          >
            <span class="edit-marker">*</span>Stops are not in the correct
            order. Travel times may be missing or inaccurate.
          </v-layout>

          <v-layout
            v-for="(eventItem, index) in jobEventList"
            :key="eventItem.id"
            class="eventlist-item"
            :class="[allowSelection ? 'with-hover' : '', denseList ? 'dense-list align-center' : 'py-1 pl-2 align-start', selectedEventItemId === eventItem.id ? 'eventlist-item--selected' : '', eventItem.hasEdits ? 'show-edited-marker' : '']"
            @click="allowSelection ? emitStartAndEndEpoch(eventItem) : null"
          >
            <span
              class="eventlist-item__date"
              :class="eventItem.differentDateFromPrevious"
              v-if="!showTravelDuration"
              ><span v-if="eventItem.differentDateFromPrevious"
                >{{eventItem.readableDate}}</span
              ></span
            >
            <span
              class="eventlist-item__time"
              :class="[eventItem.eventType === 'JobEvent' ? 'jobtype' : '', eventItem.eventType  === 'PudEvent' ? 'pudtype' : '', eventItem.eventType === 'ChatMessage' ? 'chattype' : '', eventItem.eventType === 'DisplayEvent' || eventItem.eventType === 'EDIUpdate'  ? 'displaytype' : '',
              eventItem.eventType === 'TravelTime' ? 'traveltype' : '']"
              >{{eventItem.readableTime}}</span
            >
            <v-flex
              v-if="eventItem.eventType === 'JobEvent' || eventItem.eventType === 'DisplayEvent' || eventItem.eventType === 'EDIUpdate'"
            >
              <span class="eventlist-item__title">
                {{eventItem.eventTitle}}
                <span
                  v-if="eventItem.numberOfDuplicateEvents !== 0 && eventItem.eventType === 'DisplayEvent'"
                  >({{eventItem.numberOfDuplicateEvents + 1}})</span
                >
              </span>
              <span
                class="eventlist-item__connector-text"
                v-if="showActionedBy"
              >
                by
              </span>
              <span class="eventlist-item__username" v-if="showActionedBy">
                {{eventItem.actionedBy}}
              </span>
            </v-flex>
            <v-flex v-if="eventItem.eventType === 'PudEvent'">
              <span class="eventlist-item__title">
                {{eventItem.eventTitle}}
              </span>
              <span
                class="eventlist-item__connector-text"
                v-if="eventItem.eventTitle === 'ARRIVED'"
              >
                at
              </span>
              <span
                class="eventlist-item__connector-text"
                v-if="eventItem.eventTitle === 'DEPARTED'"
              >
                from
              </span>
              <span
                class="eventlist-item__connector-text"
                v-if="eventItem.eventTitle === 'RESET'"
              >
                progress of
              </span>
              <span class="eventlist-item__title">
                {{eventItem.eventSubtitle}}
              </span>
              <span
                class="eventlist-item__chat-content pl-1"
                v-if="showOnSiteDuration"
              >
                {{eventItem.longContent}}
              </span>
              <span
                class="eventlist-item__connector-text"
                v-if="showActionedBy"
              >
                by
              </span>
              <span class="eventlist-item__username" v-if="showActionedBy">
                {{eventItem.actionedBy}}
              </span>
            </v-flex>
            <v-flex v-if="eventItem.eventType === 'ChatMessage'">
              <v-layout>
                <span class="eventlist-item__title">
                  {{eventItem.eventTitle}}
                </span>
                <span
                  class="eventlist-item__connector-text"
                  v-if="showActionedBy"
                >
                  from
                </span>
                <span class="eventlist-item__username" v-if="showActionedBy">
                  {{eventItem.actionedBy}}
                </span>
              </v-layout>
              <v-layout class="eventlist-item__chat-container">
                <span class="eventlist-item__chat-content"
                  >{{eventItem.longContent}}</span
                >
              </v-layout>
            </v-flex>
            <v-flex v-if="eventItem.eventType === 'TravelTime'">
              <v-layout>
                <span class="eventlist-item__title">
                  {{eventItem.eventTitle}}
                </span>
                <span class="eventlist-item__chat-content pl-1"
                  >{{eventItem.eventSubtitle}}</span
                >
              </v-layout>
              <v-layout
                class="eventlist-item__chat-container"
                v-if="jobLegsInCorrectOrder"
              >
                <span class="eventlist-item__chat-content"
                  >{{eventItem.longContent}}</span
                >
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>
  </v-layout>

  <!-- Timeline View -->
  <v-layout
    v-show="activeTab === 'timeline'"
    row
    wrap
    class="pa-2 app-bgcolor--400 app-bordercolor--600 app-borderside--a"
  >
    <v-flex md12>
      <v-layout justify-space-between align-center px-2 pt-1>
        <h6>Event Timeline</h6>
      </v-layout>
    </v-flex>
    <v-flex md12>
      <v-divider></v-divider>
    </v-flex>
    <v-flex md12>
      <job-event-timeline
        :key="jobDetails.jobId"
        :job="jobDetails"
        :jobRouteProgress="jobDetails.additionalJobData.routeProgress"
        max-height="75vh"
      ></job-event-timeline
    ></v-flex>
  </v-layout>
</section>
