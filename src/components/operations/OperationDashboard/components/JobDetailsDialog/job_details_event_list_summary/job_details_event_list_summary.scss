.job-details-event-list-summary {
  padding: 0;
  position: relative;
  overflow-y: scroll;

  // Tab styling
  .event-view-tabs {
    .v-tabs__bar {
      background-color: transparent;
    }

    .v-tab {
      text-transform: none;
      font-weight: 500;
      color: var(--text-color);

      &:hover {
        color: var(--primary-color);
      }

      &.v-tab--active {
        color: var(--primary-color);
      }

      &:disabled {
        color: var(--light-text-color);
        opacity: 0.5;
      }
    }

    .v-tabs__slider {
      background-color: var(--primary-color);
    }
  }

  // View toggle button styling

  .header-container {
    position: relative;
    top: -8px;
    border: 1px solid var(--border-color);
    border-radius: 16px;
    max-width: fit-content;
    margin: 0 auto;

    .view-toggle-buttons {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;

      .v-btn {
        background-color: $app-dark-primary-200 !important;
        border-radius: 16px;
        font-size: $font-size-12;
        font-weight: 600;
        transition: all 0.2s;
        cursor: pointer;
        box-shadow: none !important;
        min-width: auto;
        padding: 8px 16px;
        text-transform: none;
        margin: 0;
        flex: 1;

        &:hover {
          border-color: #ff9204 !important;
          scale: 1.05;
          box-shadow: var(--box-shadow);
          color: #ffc404;
        }

        &.v-btn--active {
          background-color: #ffc404 !important;
          border: 1px solid #ff9204 !important;
          color: var(--background-color-100);
          box-shadow: var(--box-shadow);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }

  .has-edit-message {
    font-size: $font-size-11;
    color: var(--light-text-color);
    margin: 0px;
    padding: 0px 8px;
    text-align: center;

    .edit-marker {
      padding: 2px 4px;
      color: var(--primary-light);
      font-size: $font-size-14;
      font-weight: 600;
    }
  }
  .eventlist-item {
    font-size: $font-size-12;
    color: var(--light-text-color);
    position: relative;
    &.with-hover {
      &:hover {
        filter: brightness(115%);
        background-color: $app-dark-primary-600;
        cursor: pointer;
      }
    }
    &.dense-list {
      font-size: $font-size-11;
      padding: 2px 0px;
    }
    &--selected {
      background-color: #484858;
    }

    &.show-edited-marker {
      // Add a marker to show that the event has been edited
      &:after {
        content: '*';
        color: var(--primary-light);
        position: absolute;
        font-size: $font-size-18;
        font-weight: 600;
        left: 0px;
        top: 0px;
      }
    }
    .eventlist-item__time {
      padding: 2px 4px;
      font-size: $font-size-11;
      border-radius: $border-radius-base;
      font-weight: 600;
      color: white;
      margin-left: 6px;
      margin-right: 6px;
      position: relative;
      white-space: nowrap;

      $primary-swatch: $primary-light;
      &.jobtype {
        background-color: $warning;
        border: 1px solid $warning-type;
        color: black;
      }
      &.displaytype {
        background-color: #2aae4f;
      }
      &.pudtype {
        // background-color: green;
        border: 1px solid #5c74cc;
        background-color: var(--accent);
        color: black;
      }
      &.chattype {
        // background-color: blue;
        margin-left: 32px;
      }
      &.traveltype {
        // background-color: green;
        border-left: 2px dashed rgb(189, 193, 208);
        margin-left: 12px;
        padding-left: 8px;
      }
    }

    .eventlist-item__date {
      color: grey;
      min-width: 55px;
      padding: 2px 4px;
      font-size: $font-size-11;
      font-weight: 600;
      text-align: right;
    }

    .eventlist-item__title {
      // padding-left: 3px;
      text-transform: uppercase;
      font-weight: 600;
      color: var(--text-color);
    }
    .eventlist-item__connector-text {
      padding-left: 3px;
    }
    .eventlist-item__change-list-text {
      position: relative;
      padding-left: 3px;

      font-size: $font-size-11;
      color: rgb(165, 169, 186);
    }
    .eventlist-item__username {
      padding-left: 3px;
    }

    .eventlist-item__chat-container {
      border-left: 1px solid grey;
      .eventlist-item__chat-content {
        padding: 2px 6px;
        font-style: italic;
      }
    }
  }
}
