import JobEventTimeline from '@/components/common/job_event_timeline/job_event_timeline.vue';
import {
  returnFormattedDate,
  returnFormattedTime,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { returnCorrectDuration } from '@/helpers/DateTimeHelpers/DurationHelpers';
import {
  displayNameFromEvent,
  isDisplayTypeEvent,
} from '@/helpers/StatusHelpers/StatusHelpers';
import ChatMessage from '@/interface-models/Generic/ChatConversation/ChatMessage';
import Communication from '@/interface-models/Generic/Communication/Communication';
import JobStatusUpdate from '@/interface-models/Jobs/Event/JobStatusUpdate';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { v4 as uuidv4 } from 'uuid';
import { Component, Prop, Vue } from 'vue-property-decorator';

enum EventListType {
  JobEvent = 'JobEvent',
  PudEvent = 'PudEvent',
  ChatMessage = 'ChatMessage',
  DisplayEvent = 'DisplayEvent',
  TravelTime = 'TravelTime',
  EDIUpdate = 'EDIUpdate',
}

interface EventListItem {
  id: string;
  pudId?: string;
  epochTime: number;
  endTime: number;
  readableTime: string;
  readableDate: string;
  eventType: EventListType;
  actionedBy: string;
  eventTitle: string;
  eventSubtitle: string;
  longContent: string;
  differentDateFromPrevious: boolean;
  numberOfDuplicateEvents: number;
  changeHistory: EventListItemChangeHistory[];
  loadTimeDescription?: string;
  travelTimeDescription?: string;
  hasEdits?: boolean;
}

interface EventListItemChangeHistory {
  id: string;
  oldValue: string;
  newValue: string;
  changedBy: string;
  changedTime: string;
  isOriginal: boolean;
}

@Component({
  components: { JobEventTimeline },
})
export default class JobDetailsEventListSummary extends Vue {
  @Prop() public jobDetails: JobDetails;
  @Prop({ default: '' }) public selectedEventItemId: string;
  @Prop({ default: false }) public showTimeline: boolean;
  @Prop({ default: true }) public showTitle: boolean;
  @Prop({ default: true }) public showActionedBy: boolean;
  @Prop({ default: true }) public showJobEvents: boolean;
  @Prop({ default: true }) public showIndividualPudEvents: boolean;
  @Prop({ default: true }) public showChatMessages: boolean;
  @Prop({ default: false }) public showTravelDuration: boolean;
  @Prop({ default: false }) public showOnSiteDuration: boolean;
  @Prop({ default: false }) public allowSelection: boolean;
  @Prop({ default: false }) public denseList: boolean;
  @Prop({ default: false }) public splitColumn: boolean;

  public jobEventList: EventListItem[] = [];
  public pudEventList: EventListItem[] = [];
  public isOutsideHire: boolean = false;
  public activeTab: string = 'list'; // Default to list view

  // Used to indicate whether the order of the pudItems on the job is consistent
  // with the order of the eventList items
  public jobLegsInCorrectOrder: boolean = true;

  public returnFormattedTime = returnFormattedTime;
  public returnFormattedDate = returnFormattedDate;

  get splitColumnView() {
    return this.splitColumn && this.pudEventList.length > 0;
  }

  public constructJobLevelEventItems(
    items: JobStatusUpdate[],
  ): EventListItem[] {
    const eventList: EventListItem[] = [];

    for (let i = 0; i < items.length; i++) {
      const jobEvent = items[i];
      const eventTitle = displayNameFromEvent(jobEvent.updatedStatus);
      if (!eventTitle) {
        continue;
      }

      const numberOfDuplicateEvents = this.getNumberOfDuplicateEdiEvents(
        items[i].updatedStatus,
        i,
        items,
      );

      const eventListItem: EventListItem = {
        id: `${jobEvent.correctEventTime}-${jobEvent.updatedStatus}`,
        epochTime: jobEvent.correctEventTime,
        endTime: jobEvent.correctEventTime,
        readableTime: returnFormattedTime(jobEvent.correctEventTime),
        readableDate: returnFormattedDate(jobEvent.correctEventTime),
        eventType: !isDisplayTypeEvent(jobEvent.updatedStatus)
          ? EventListType.JobEvent
          : EventListType.DisplayEvent,
        actionedBy: jobEvent.editedBy,
        eventTitle,
        eventSubtitle: '',
        longContent: '',
        differentDateFromPrevious: false,
        numberOfDuplicateEvents,
        changeHistory: [],
      };

      if (eventList.length === 0) {
        eventList.push(eventListItem);
      } else if (eventTitle !== eventList[eventList.length - 1].eventTitle) {
        eventList.push(eventListItem);
      }
    }
    return eventList;
  }
  // instead of showing duplicate EDI uploads we should group them and show the number of duplicates as a display.
  public getNumberOfDuplicateEdiEvents(
    currentEventName: string,
    currentIteration: number,
    eventList: JobStatusUpdate[],
  ) {
    let numberOfDuplicateEvents = 0;
    for (let x = currentIteration + 1; x < eventList.length; x++) {
      if (
        currentEventName === 'CLIENT_EDI_UPDATED' &&
        eventList[x].updatedStatus === 'CLIENT_EDI_UPDATED'
      ) {
        numberOfDuplicateEvents++;
      } else {
        break;
      }
    }

    return numberOfDuplicateEvents;
  }

  public constructPudLevelEventItems(
    items: JobStatusUpdate[],
  ): EventListItem[] {
    const eventList: EventListItem[] = [];

    const formatTime = (e: number) =>
      this.returnFormattedTime(e, 'DD/MM/YY HH:mm');

    const filteredPudItems = this.jobDetails.pudItems.filter(
      (p) => p.legTypeFlag === 'P' || p.legTypeFlag === 'D',
    );

    for (let i = 0; i < items.length; i++) {
      const jobEvent: JobStatusUpdate = items[i];
      const foundIndex = filteredPudItems.findIndex(
        (p) => p.pudId === jobEvent.pudId,
      );

      if (foundIndex === -1) {
        continue;
      }
      const foundPud = filteredPudItems[foundIndex];

      let statusValue = '';
      if (jobEvent.updatedStatus) {
        if (jobEvent.updatedStatus === 'ARRIVED') {
          statusValue = jobEvent.updatedStatus;
        } else if (jobEvent.updatedStatus === 'FINISHED') {
          statusValue = 'DEPARTED';
        } else if (jobEvent.updatedStatus === 'CLIENT_EDI_UPDATED') {
          statusValue = 'CLIENT EDI UPDATED';
        }
      } else {
        statusValue = 'RESET';
      }
      const changeList: EventListItemChangeHistory[] = [];
      if (jobEvent.adjustmentList && jobEvent.adjustmentList.length) {
        const originalVal: EventListItemChangeHistory = {
          id: uuidv4().replace(/-/g, ''),
          oldValue: '',
          newValue: formatTime(jobEvent.changeTime),
          changedBy: jobEvent.editedBy,
          changedTime: formatTime(jobEvent.changeTime),
          isOriginal: true,
        };
        changeList.push(originalVal);

        jobEvent.adjustmentList.forEach((a, idx, arr) => {
          let changeItem: EventListItemChangeHistory;
          if (idx === 0) {
            changeItem = {
              id: uuidv4().replace(/-/g, ''),
              oldValue: formatTime(jobEvent.changeTime),
              newValue: formatTime(a.updatedValue),
              changedBy: a.adjustedBy,
              changedTime: formatTime(a.adjustmentTime),
              isOriginal: false,
            };
          } else {
            changeItem = {
              id: uuidv4().replace(/-/g, ''),
              oldValue: arr[idx - 1].updatedValue
                ? formatTime(arr[idx - 1].updatedValue)
                : '',
              newValue: a.updatedValue ? formatTime(a.updatedValue) : '',
              changedBy: a.adjustedBy,
              changedTime: formatTime(a.adjustmentTime),
              isOriginal: false,
            };
          }
          changeList.push(changeItem);
        });
      }

      // If the event is an EDI update, we don't want to show some fields (such
      // as load and travel times)
      const isEdiUpdate = jobEvent.updatedStatus === 'CLIENT_EDI_UPDATED';

      const eventListItem: EventListItem = {
        id: `${jobEvent.correctEventTime}-${
          jobEvent.pudId ? jobEvent.pudId : ''
        }-${jobEvent.updatedStatus}`,
        epochTime: jobEvent.correctEventTime,
        endTime: jobEvent.correctEventTime,
        readableTime: jobEvent.correctEventTime
          ? returnFormattedTime(jobEvent.correctEventTime)
          : '',
        readableDate: jobEvent.correctEventTime
          ? returnFormattedDate(jobEvent.correctEventTime)
          : '',
        eventType: isEdiUpdate
          ? EventListType.EDIUpdate
          : EventListType.PudEvent,
        actionedBy: jobEvent.editedBy,
        eventTitle: statusValue,
        eventSubtitle: isEdiUpdate
          ? ''
          : `${foundPud.address.suburb} (Leg ${foundIndex + 1})`,
        longContent: '',
        differentDateFromPrevious: false,
        numberOfDuplicateEvents: 0,
        changeHistory: changeList.length > 0 ? changeList : [],
        loadTimeDescription: isEdiUpdate
          ? ''
          : this.returnLoadTimeStringForPudItem(
              jobEvent.updatedStatus,
              jobEvent.pudId,
            ),
        travelTimeDescription: isEdiUpdate
          ? ''
          : this.returnTravelTimeDescriptionForPudItem(
              jobEvent.updatedStatus,
              jobEvent.pudId,
            ),
      };
      eventList.push(eventListItem);
    }
    return eventList;
  }

  public constructChatMessageEventItems(
    items: Communication[],
  ): EventListItem[] {
    const eventList: EventListItem[] = [];

    for (let i = 0; i < items.length; i++) {
      const chatMessage = items[i];
      const commObject = chatMessage.type!.communicationDetails as ChatMessage;
      const eventListItem: EventListItem = {
        id: commObject._id ? commObject._id : `${commObject.timestamp}`,
        epochTime: commObject.timestamp,
        endTime: commObject.timestamp,
        readableTime: returnFormattedTime(commObject.timestamp),
        readableDate: returnFormattedDate(commObject.timestamp),
        eventType: EventListType.ChatMessage,
        actionedBy: commObject.senderName,
        eventTitle: 'Message',
        eventSubtitle: '',
        longContent: commObject.content,
        differentDateFromPrevious: false,
        numberOfDuplicateEvents: 0,
        changeHistory: [],
      };
      eventList.push(eventListItem);
    }

    return eventList;
  }
  // Construct an event summary for a single pud, that provides details the pud
  // was arrived at and departed. This is an alternative to presenting the
  // information on two lines. NOTE: Because this method is used on the JOB
  // ROUTE MAP, we need to display the TRUE event times rather than edits, as
  // these times will correlate with the GPS data incurred by the driver
  public constructSingleLinePudEventItems(
    items: JobStatusUpdate[],
  ): EventListItem[] {
    const eventList: EventListItem[] = [];

    const arrivedEvents = items.filter((i) => i.updatedStatus === 'ARRIVED');
    const finishedEvents = items.filter((i) => i.updatedStatus === 'FINISHED');

    const filteredPudItems = this.jobDetails.pudItems.filter(
      (p) => p.legTypeFlag === 'P' || p.legTypeFlag === 'D',
    );

    for (let i = 0; i < arrivedEvents.length; i++) {
      const arrivedEvent: JobStatusUpdate = arrivedEvents[i];

      const foundIndex = filteredPudItems.findIndex(
        (p) => p.pudId === arrivedEvent.pudId,
      );
      if (foundIndex === -1) {
        continue;
      }
      const foundPud = filteredPudItems[foundIndex];

      const foundFinishedEvent = finishedEvents.find(
        (e) => e.pudId === arrivedEvent.pudId,
      );

      const startTime = returnFormattedTime(arrivedEvent.correctEventTime);
      const endTime = foundFinishedEvent
        ? returnFormattedTime(foundFinishedEvent.correctEventTime)
        : '';

      const readableTime =
        endTime !== '' ? `${startTime} - ${endTime}` : startTime;
      let duration: string;

      if (foundFinishedEvent && arrivedEvent.correctEventTime) {
        const actualDuration =
          foundFinishedEvent.correctEventTime - arrivedEvent.correctEventTime;

        duration = `${returnCorrectDuration(
          actualDuration,
        )} (est. ${returnCorrectDuration(foundPud.loadTime)})`;
      } else {
        duration = '';
      }

      const eventListItem: EventListItem = {
        id: `${arrivedEvent.correctEventTime}-${
          arrivedEvent.pudId ? arrivedEvent.pudId : ''
        }-${arrivedEvent.updatedStatus}`,
        pudId: foundPud.pudId,
        epochTime: arrivedEvent.correctEventTime,
        endTime: foundFinishedEvent
          ? foundFinishedEvent.correctEventTime
          : arrivedEvent.correctEventTime,
        readableTime,
        readableDate: returnFormattedDate(arrivedEvent.correctEventTime),
        eventType: EventListType.PudEvent,
        actionedBy: arrivedEvent.editedBy,
        eventTitle: '',
        eventSubtitle: `${foundPud.address.suburb}`,
        longContent: duration,
        differentDateFromPrevious: false,
        numberOfDuplicateEvents: 0,
        changeHistory: [],
        hasEdits:
          !!arrivedEvent.adjustmentList?.length ||
          !!foundFinishedEvent?.adjustmentList?.length,
      };
      eventList.push(eventListItem);
    }
    return eventList;
  }

  // Fill in the gaps between each of the event times
  // Requires endTime to be defined in EventListItem, so this should only
  public constructTravelTimeEventItems(
    pudEventList: EventListItem[],
  ): EventListItem[] {
    const eventList: EventListItem[] = [];

    // Start iterating at index one of list, and find endTime of previous pud.
    for (let i = 1; i < pudEventList.length; i++) {
      const prevPud: EventListItem = pudEventList[i - 1];
      const nextPud: EventListItem = pudEventList[i];

      // Use end of last pud and start of current pud to construct start and finish times for travel
      const startTime: number = prevPud.endTime;
      const endTime: number = nextPud.epochTime;

      const readableTime = `${returnFormattedTime(
        startTime,
      )} - ${returnFormattedTime(endTime)}`;

      const duration = returnCorrectDuration(endTime - startTime);
      const durationVariance = this.returnTravelTimeDescriptionForPudItem(
        '',
        nextPud.pudId,
        false,
      );

      const eventListItem: EventListItem = {
        id: `${startTime}`,
        epochTime: startTime,
        endTime,
        readableTime,
        readableDate: returnFormattedDate(startTime),
        eventType: EventListType.TravelTime,
        actionedBy: '',
        eventTitle: 'TRAVEL',
        eventSubtitle: `in ${duration}`,
        longContent: durationVariance,
        differentDateFromPrevious: false,
        numberOfDuplicateEvents: 0,
        changeHistory: [],
      };
      eventList.push(eventListItem);
    }
    return eventList;
  }

  public emitStartAndEndEpoch(eventItem: EventListItem) {
    if (eventItem.id !== this.selectedEventItemId) {
      this.$emit('update:selectedEventItemId', eventItem.id);
      this.$emit('setHighlightBracket', {
        startEpoch: eventItem.epochTime,
        endEpoch: eventItem.endTime,
      });
    } else {
      this.$emit('update:selectedEventItemId', '');
      this.$emit('setHighlightBracket', {
        startEpoch: null,
        endEpoch: null,
      });
    }
  }

  public prepareData() {
    this.jobLegsInCorrectOrder = this.jobDetails.isProgressInOrder();
    this.isOutsideHire =
      this.jobDetails.additionalJobData !== undefined &&
      this.jobDetails.additionalJobData.isOutsideHire;

    const jobLevelEvents: JobStatusUpdate[] = [];
    const pudLevelEvents: JobStatusUpdate[] = [];

    for (let i = 0; i < this.jobDetails.eventList.length; i++) {
      const event = this.jobDetails.eventList[i];
      if (event.pudId) {
        const foundPudIndex = pudLevelEvents.findIndex(
          (p) =>
            p.pudId &&
            p.pudId === event.pudId &&
            p.updatedStatus === event.updatedStatus,
        );
        if (foundPudIndex === -1) {
          pudLevelEvents.push(event);
        } else {
          pudLevelEvents.splice(foundPudIndex, 1, event);
        }
      } else {
        jobLevelEvents.push(event);
      }
    }

    const chatMessages: Communication[] = this.jobDetails.notes.filter(
      (n) => n.type?.id === 1,
    );
    let unsortedCombinedList: EventListItem[] = [];
    let unsortedCol2: EventListItem[] = [];
    if (this.showJobEvents) {
      const jobEventListItems: EventListItem[] =
        this.constructJobLevelEventItems(jobLevelEvents);
      unsortedCombinedList = unsortedCombinedList.concat(jobEventListItems);
    }
    if (!this.splitColumn) {
      unsortedCombinedList = unsortedCombinedList.concat(
        this.constructPudEvents(pudLevelEvents),
      );
    } else {
      unsortedCol2 = this.constructPudEvents(pudLevelEvents);
    }

    if (this.showChatMessages) {
      const chatEventListItems: EventListItem[] =
        this.constructChatMessageEventItems(chatMessages);
      unsortedCombinedList = unsortedCombinedList.concat(chatEventListItems);
    }

    this.jobEventList = this.sortAndAddDateToListItems(unsortedCombinedList);
    this.pudEventList = this.sortAndAddDateToListItems(unsortedCol2);
  }
  // Return a list of pud events depending on which component type we are viewing
  public constructPudEvents(
    pudLevelEvents: JobStatusUpdate[],
  ): EventListItem[] {
    let unsortedCombinedList: EventListItem[] = [];
    // JOB DIALOG type component
    if (this.showIndividualPudEvents) {
      const pudEventListItems: EventListItem[] =
        this.constructPudLevelEventItems(pudLevelEvents);
      unsortedCombinedList = unsortedCombinedList.concat(pudEventListItems);
    } else {
      // JOB MAP type component
      const singleLinePudEvents: EventListItem[] =
        this.constructSingleLinePudEventItems(pudLevelEvents);
      unsortedCombinedList = unsortedCombinedList.concat(singleLinePudEvents);
      if (this.showTravelDuration) {
        const travelTimeEvents =
          this.constructTravelTimeEventItems(singleLinePudEvents);
        unsortedCombinedList = unsortedCombinedList.concat(travelTimeEvents);
      }
    }
    return unsortedCombinedList.filter((u) => u.epochTime !== 0);
  }
  // Sort the provided eventList into chronological order
  // Iterate over result and tag with time
  public sortAndAddDateToListItems(
    eventList: EventListItem[],
  ): EventListItem[] {
    const combinedEventList = eventList.sort(
      (a, b) => a.epochTime - b.epochTime,
    );
    let currentDateString: string = '';
    for (let i = 0; i < combinedEventList.length; i++) {
      const item = combinedEventList[i];
      if (i === 0) {
        item.differentDateFromPrevious = true;
        currentDateString = item.readableDate;
      } else {
        if (item.readableDate !== currentDateString) {
          item.differentDateFromPrevious = true;
          currentDateString = item.readableDate;
        } else {
          item.differentDateFromPrevious = false;
        }
      }
    }
    return combinedEventList;
  }

  /**
   * Returns a formatted string describing the load time for a given PUD item.
   * If route progress data is available, shows actual load time and percent difference from estimate.
   * Otherwise, shows the estimated load time.
   * Only applies to dropoff legs.
   *
   * @param pudId - The PUD item ID.
   * @returns A string describing the load time, or an empty string if not applicable.
   */
  public returnLoadTimeStringForPudItem(eventType: string, pudId?: string) {
    // Only show travel time for FINISHED events
    if (!pudId || eventType === 'ARRIVED') {
      return '';
    }
    const foundPudIndex = this.jobDetails.pudItems.findIndex(
      (p) => p.pudId === pudId,
    );
    if (foundPudIndex === -1) {
      return '';
    }
    const pudItem = this.jobDetails.pudItems[foundPudIndex];
    const foundRouteProgress =
      this.jobDetails.additionalJobData?.routeProgress?.find(
        (r) => r.pudId === pudId,
      );
    if (!foundRouteProgress?.differenceInLoadTimePercentReadable) {
      return `Est. ${returnCorrectDuration(pudItem.loadTime)} load`;
    }
    const loadTimePercent =
      foundRouteProgress.differenceInLoadTimePercentReadable;

    return `${returnCorrectDuration(
      foundRouteProgress.actualLoadTime ?? 0,
    )} (${loadTimePercent} of ${returnCorrectDuration(
      pudItem.loadTime,
    )} estimate)`;
  }

  /**
   * Returns a formatted string describing the travel time to a given PUD item.
   * Only applies to pickup legs (not the first PUD).
   * If route progress data is available, shows actual travel time and percent difference from estimate.
   *
   * @param pudId - The PUD item ID.
   * @returns A string describing the travel time, or an empty string if not applicable.
   */
  public returnTravelTimeDescriptionForPudItem(
    eventType: string,
    pudId?: string,
    includeDuration: boolean = true,
  ) {
    // Only show travel time for ARRIVED events
    if (!pudId || eventType === 'FINISHED') {
      return '';
    }
    const foundPudIndex = this.jobDetails.pudItems.findIndex(
      (p) => p.pudId === pudId,
    );
    // Return nothing if pud not found OR if index 0,  as travel durations are
    // only calculated from the 2nd pud onwards (index 1)
    if (foundPudIndex < 1) {
      return '';
    }
    const foundRouteProgress =
      this.jobDetails.additionalJobData?.routeProgress?.find(
        (r) => r.pudId === pudId,
      );
    if (!foundRouteProgress) {
      return '';
    }
    const estimatedTravelTime = foundRouteProgress.estimatedTravelTimeReadable;
    const actualTravelTimeReadable =
      foundRouteProgress.actualTravelTimeReadable ?? '';
    const travelTimePercent =
      foundRouteProgress.differenceInTravelTimePercentReadable ?? '';

    return includeDuration
      ? `${actualTravelTimeReadable} (${travelTimePercent} of ${estimatedTravelTime} estimate)`
      : `${travelTimePercent} of ${estimatedTravelTime} estimate`;
  }

  public mounted() {
    this.prepareData();
  }
}
