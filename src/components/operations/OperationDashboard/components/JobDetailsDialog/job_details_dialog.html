<div class="job-details-dialog">
  <JobContentDialog
    :showDialog.sync="showDialog"
    :title="'Job Details - ' + jobDetails.displayId"
    width="90%"
    contentPadding="pa-0"
    :isConfirmUnsaved="showDialogCloseConfirmation"
    :isDisabled="isLoading"
    :isLoading="isLoading"
    :showActions="false"
    :showCancelBtn="true"
    contentClass="v-dialog-custom"
    :isContentDialog="false"
    :jobDetails="jobDetails"
    :clientDetails="clientDetails"
    :driverId="jobDetails.driverId"
    :jobId="jobDetails.jobId"
    :showExpandIcon="true"
    @expand="selectedViewType = 'MSG'"
    @cancel="closeJobDetailsDialog"
    @confirm="closeJobDetailsDialog"
  >
    <template #default>
      <v-layout
        justify-space-between
        align-center
        class="task-bar app-theme__center-content--header no-highlight"
        :class="[isOutsideHire ? 'header-border-highlight outside-hire' : '', !isOutsideHire && manualProgressionRequired ? 'header-border-highlight untracked' : '']"
      >
        <span @click="logJobIdentifiers"
          >Job Details - {{jobDetails.displayId}}</span
        >
        <span
          v-if="jobTrackingTypeLabel"
          class="ml-3 accent-text--card"
          :class="[isOutsideHire ? 'warning-type-outline' : '', !isOutsideHire && manualProgressionRequired ? 'untracked-job-type' : '']"
        >
          {{jobTrackingTypeLabel}}
        </span>
        <v-spacer v-if="jobTrackingTypeLabel"></v-spacer>
        <ConfirmationDialog
          buttonText="Cancel"
          message="You currently have unsaved changes. Please confirm that you wish to proceed without saving."
          title="Unsaved Changes"
          @confirm="closeJobDetailsDialog"
          :cancelButtonText="'Go Back'"
          :isSmallButton="false"
          :isOutlineButton="true"
          :buttonColor="'error'"
          :confirmationButtonText="'proceed without saving'"
          :dialogIsActive="showDialogCloseConfirmation"
          :confirmationButtonColor="'error'"
          :cancelButtonColor="'blue'"
          :isDialogCloseConfirmation="true"
          confirmButtonId="job-dialog-close-button"
        ></ConfirmationDialog>
      </v-layout>
      <v-layout class="app-theme__center-content--body dialog-content">
        <!-- <v-flex md3 class="side-column app-bordercolor--600 app-borderside--r-shadow"
          > -->
        <v-flex
          md3
          class="side-column app-bgcolor--400 app-bordercolor--600 app-borderside--r-shadow"
        >
          <v-flex>
            <v-flex
              md12
              class="app-bgcolor--250 app-bordercolor--600 app-borderside--b"
            >
              <v-layout row wrap py-2 px-2>
                <v-flex md12 class="side-column__summaryitem">
                  <v-layout justify-space-between align-start>
                    <span class="side-column__summaryitem--key"> Status </span>
                    <span
                      class="side-column__summaryitem--value status-container"
                    >
                      {{jobDetails.currentExactJobStatus}}
                    </span>
                  </v-layout>
                </v-flex>
                <v-flex
                  md12
                  class="side-column__summaryitem"
                  v-if="jobDetails.accounting.invoiceId"
                >
                  <v-layout justify-space-between align-start>
                    <span class="side-column__summaryitem--key">
                      Invoice No.
                    </span>
                    <span class="side-column__summaryitem--value">
                      {{jobDetails.accounting.invoiceId}}
                    </span>
                  </v-layout>
                </v-flex>
                <v-flex
                  md12
                  class="side-column__summaryitem"
                  v-if="jobDetails.accounting.rctiId"
                >
                  <v-layout justify-space-between align-start>
                    <span class="side-column__summaryitem--key">
                      RCTI No.
                    </span>
                    <span class="side-column__summaryitem--value">
                      {{jobDetails.accounting.rctiId}}
                    </span>
                  </v-layout>
                </v-flex>
                <v-flex
                  md12
                  class="side-column__summaryitem"
                  v-if="jobDetails.completedLegs && jobDetails.workStatus >= WorkStatus.ACCEPTED"
                >
                  <v-layout justify-space-between align-start>
                    <span class="side-column__summaryitem--key">
                      Completed Legs
                    </span>
                    <span class="side-column__summaryitem--value">
                      {{jobDetails.completedLegs}}
                    </span>
                  </v-layout>
                </v-flex>
                <v-flex md12 px-2 pt-2 pb-1>
                  <v-divider class="ma-0"></v-divider>
                </v-flex>
                <v-flex
                  md12
                  class="side-column__summaryitem"
                  v-if="summaryInfoList"
                  v-for="(infoItem, infoIndex) in summaryInfoList"
                  :key="infoItem.id"
                >
                  <v-layout justify-space-between align-start>
                    <span class="side-column__summaryitem--key">
                      {{infoItem.title}}
                    </span>
                    <span class="side-column__summaryitem--value">
                      {{infoItem.value}}
                    </span>
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>

            <v-flex
              md12
              v-for="(menuItem, index) in menuOptions"
              :key="menuItem.id"
              class="app-bgcolor--300 app-bordercolor--600 app-borderside--b side-column__button"
              :class="[selectedViewType === menuItem.id ? 'active-state' : '', menuItem.isActive ? 'menu-item-selectable' : 'menu-item-disabled']"
              v-if="!menuItem.isHidden"
              @click="menuItem.isActive ? setSelectedView(menuItem.id) : null"
            >
              <v-layout justify-space-between align-center
                ><span class="button-label">{{menuItem.title}}</span>
                <span
                  v-if="menuItem.hasCompletionIndicator && !isJobSearchScreen"
                  class="button-icon__container"
                >
                  <v-tooltip
                    max-width="400px"
                    right
                    v-if="menuItem.inactiveDescription && menuItem.inactiveDescription.length"
                  >
                    <template v-slot:activator="{ on }">
                      <span v-on="on" class="pl-3 py-2">
                        <v-icon
                          color="amber"
                          class="button-icon__icon icon-pending"
                          >fas fa-info-circle</v-icon
                        >
                      </span>
                    </template>
                    <span>
                      <ul>
                        <li
                          v-for="desc in menuItem.inactiveDescription"
                          :key="desc"
                        >
                          {{desc}}
                        </li>
                      </ul>
                    </span>
                  </v-tooltip>
                  <v-icon
                    v-else-if="menuItem.isComplete"
                    class="button-icon__icon icon-done"
                  >
                    far fa-check-circle
                  </v-icon>
                  <v-icon
                    v-else-if="!menuItem.isComplete"
                    class="button-icon__icon icon-pending"
                  >
                    far fa-circle
                  </v-icon>
                </span>
              </v-layout>
            </v-flex>
          </v-flex>
        </v-flex>

        <v-flex md9 class="dialog-content__scrollable">
          <v-layout
            class="dialog-toolbar center-section__top-toolbar"
            align-center
          >
            <!-- <v-badge
                color="amber"
                class="badge-custom"
                :value="manualProgressionRequired && (jobDetails.workStatus === WorkStatus.ALLOCATED || jobDetails.workStatus === WorkStatus.IN_PROGRESS)"
              >
                <template v-slot:badge>
                  <span>!</span>
                </template> -->
            <v-menu
              right
              style="z-index: 1000"
              :key="jobDetails.currentExactJobStatus + 'dialog-menu'"
            >
              <template v-slot:activator="{ on: menu }">
                <v-tooltip bottom style="z-index: 1000">
                  <template v-slot:activator="{ on: tooltip }">
                    <v-btn
                      flat
                      icon
                      v-on="{ ...tooltip, ...menu }"
                      class="ma-0"
                    >
                      <v-icon size="16">fas fa-ellipsis-v </v-icon>
                    </v-btn>
                  </template>
                  <span>View Job Actions</span>
                </v-tooltip>
              </template>
              <v-list class="v-list-custom">
                <ConfirmationDialog
                  v-if="manualProgressionRequired && jobDetails.workStatus === WorkStatus.ALLOCATED"
                  :key="jobDetails.jobId"
                  :buttonText="`Start ${jobTrackingTypeLabel} Job`"
                  :message="`You are about to mark an ${jobTrackingTypeLabel.toUpperCase()} job as Started. Please confirm the following:`"
                  title="Confirm Job Commencement"
                  @confirm="updateJobStatusStarted"
                  :buttonDisabled="false"
                  :isOutlineButton="true"
                  :buttonColor="'teal accent-2'"
                  confirmationButtonText="Confirm and Start"
                  :isCheckbox="true"
                  :isListTile="true"
                  :listTileAccent="true"
                  checkboxLabel="I have contacted the driver and confirmed with them that they are on-site and have started work on this job."
                  :dialogIsActive="true"
                />
                <ConfirmationDialog
                  v-if="manualProgressionRequired && jobDetails.workStatus === WorkStatus.IN_PROGRESS"
                  :key="jobDetails.jobId"
                  :buttonText="`Complete ${jobTrackingTypeLabel} Job`"
                  :message="`You are about to mark an ${jobTrackingTypeLabel.toUpperCase()} job as Completed. Please confirm the following:`"
                  title="Confirm Job Completion`"
                  @confirm="updateJobStatusComplete"
                  :buttonDisabled="false"
                  :isOutlineButton="true"
                  :buttonColor="'teal accent-2'"
                  confirmationButtonText="Confirm and Complete"
                  :isCheckbox="true"
                  :isListTile="true"
                  :listTileAccent="true"
                  checkboxLabel="I have received confirmation from the Driver that the job has been completed."
                  :dialogIsActive="true"
                />
                <v-list-tile
                  dense
                  @click="editJobInBookingScreen(jobDetails.jobId)"
                  v-if="!isJobSearchScreen && jobDetails.workStatus < WorkStatus.REVIEWED"
                >
                  <v-list-tile-title class="pr-2">
                    Edit Job Details
                  </v-list-tile-title>
                </v-list-tile>

                <v-list-tile
                  dense
                  @click="setRebookDialog"
                  :disabled="rebookJobDisabled"
                >
                  <v-list-tile-title class="pr-2">
                    Rebook Job
                  </v-list-tile-title>
                </v-list-tile>
                <v-list-tile
                  dense
                  @click="setShowServiceDetailsDialog"
                  v-if="jobDetails.workStatus >= WorkStatus.BOOKED && jobDetails.workStatus <= WorkStatus.DRIVER_COMPLETED && !isJobSearchScreen"
                >
                  <v-list-tile-title class="pr-2">
                    Adjust Service Details
                  </v-list-tile-title>
                </v-list-tile>

                <v-list-tile
                  v-if="clientDetails && jobDetails.workStatus !== WorkStatus.CANCELLED"
                  dense
                  @click="showUnassignedPudLinkingMaintenance = true"
                >
                  Leg Matching
                </v-list-tile>
                <ConfirmationDialog
                  v-if="showPreloadLegButton"
                  key="PRELOAD"
                  :buttonText="`Book Follow-On Job (pre-loaded)`"
                  :message="`You are about to book on a follow-on job for this pre-loaded job. Please confirm the following:`"
                  title="Book a Follow-On Job`"
                  @confirm="bookPreloadJob"
                  :buttonDisabled="false"
                  :isOutlineButton="true"
                  :buttonColor="'teal accent-2'"
                  confirmationButtonText="Confirm and Book"
                  :isCheckbox="true"
                  :isListTile="true"
                  :listTileAccent="true"
                  :isLoading="awaitingSaveResponse"
                  checkboxLabel="I have confirmed with the dispatcher and driver that a follow-on job is required."
                  :dialogIsActive="true"
                />
                <v-list-tile
                  dense
                  @click="refreshRatesToDefault"
                  v-if="jobDetails.workStatus === WorkStatus.DRIVER_COMPLETED && selectedViewType === 'PRI'"
                >
                  <v-list-tile-title class="pr-2">
                    Refresh Rates (Restore to Defaults)
                  </v-list-tile-title>
                </v-list-tile>
                <v-divider
                  v-if="jobDetails.workStatus >= WorkStatus.ALLOCATED && jobDetails.workStatus <= WorkStatus.REVIEWED && !isJobSearchScreen"
                ></v-divider>
                <v-subheader
                  v-if="jobDetails.workStatus >= WorkStatus.ALLOCATED && jobDetails.workStatus <= WorkStatus.REVIEWED && !isJobSearchScreen"
                  >Driver
                </v-subheader>
                <v-list-tile
                  dense
                  @click="showMessageHistoryDialog = true"
                  v-if="jobDetails.workStatus >= WorkStatus.ALLOCATED && jobDetails.workStatus <= WorkStatus.REVIEWED && !isJobSearchScreen"
                >
                  <v-list-tile-title>
                    View Driver Chat History
                  </v-list-tile-title>
                </v-list-tile>
                <v-list-tile
                  dense
                  @click="requestDriverDeviceInfo"
                  v-if="jobDetails.workStatus >= WorkStatus.ALLOCATED && jobDetails.workStatus <= WorkStatus.DRIVER_COMPLETED && !isJobSearchScreen"
                >
                  <v-list-tile-title> Query Driver Device </v-list-tile-title>
                </v-list-tile>
                <v-divider v-if="isAuthorisedAdminAndWorkStatus"></v-divider>
                <v-subheader v-if="isAuthorisedAdminAndWorkStatus"
                  >Admin Tools</v-subheader
                >
                <ConfirmationDialog
                  key="REQUEST_LOGOUT"
                  title="Send REQUEST LOGOUT Request"
                  v-if="isAuthorisedAdminAndWorkStatus"
                  buttonText="Request Logout (ADMIN)"
                  message="Are you sure you wish to send a 'Request Logout' request to the selected driver"
                  @confirm="pushDriverDeviceEvent('REQUEST_LOGOUT')"
                  confirmationButtonText="Send Request"
                  :isListTile="true"
                  :listTileAccent="true"
                  :dialogIsActive="true"
                />
                <ConfirmationDialog
                  key="FORCE_LOGOUT"
                  title="Send FORCE LOGOUT Request"
                  v-if="isAuthorisedAdminAndWorkStatus"
                  buttonText="Force Logout (ADMIN)"
                  message="Are you sure you wish to send a 'Force Logout' request to the selected driver"
                  @confirm="pushDriverDeviceEvent('FORCE_LOGOUT')"
                  confirmationButtonText="Send Request"
                  :isListTile="true"
                  :listTileAccent="true"
                  :dialogIsActive="true"
                />
                <ConfirmationDialog
                  key="REQUEST_CLEAR_APP_DATA"
                  title="Send REQUEST CLEAR APP DATA Request"
                  v-if="isAuthorisedAdminAndWorkStatus"
                  buttonText="Request Clear App Data (ADMIN)"
                  message="Are you sure you wish to send a 'Request Clear App Data' request to the selected driver"
                  @confirm="pushDriverDeviceEvent('REQUEST_CLEAR_APP_DATA')"
                  confirmationButtonText="Send Request"
                  :isListTile="true"
                  :listTileAccent="true"
                  :dialogIsActive="true"
                />
                <ConfirmationDialog
                  key="FORCE_CLEAR_APP_DATA"
                  title="Send FORCE CLEAR APP DATA Request"
                  v-if="isAuthorisedAdminAndWorkStatus"
                  buttonText="Force Clear App Data (ADMIN)"
                  message="Are you sure you wish to send a 'Force Clear App Data' request to the selected driver"
                  @confirm="pushDriverDeviceEvent('FORCE_CLEAR_APP_DATA')"
                  confirmationButtonText="Send Request"
                  :isListTile="true"
                  :listTileAccent="true"
                  :dialogIsActive="true"
                />
              </v-list>
            </v-menu>
            <v-spacer></v-spacer>
            <v-btn
              dense
              outline
              small
              solo
              @click="editJobInBookingScreen(jobDetails.jobId)"
              class="v-btn-confirm-custom"
              v-if="!isJobSearchScreen && !editingReferenceFormController"
            >
              <v-icon size="12" class="pr-2">edit</v-icon>
              <span
                v-if="jobDetails.workStatus === WorkStatus.DRIVER_COMPLETED"
              >
                Edit Job Details (Add Leg)
              </span>
              <span v-else-if="jobDetails.workStatus === WorkStatus.REVIEWED">
                Release for Editing
              </span>
              <span v-else> Edit Job Details </span>
            </v-btn>
            <v-btn
              v-if="editingReferenceFormController && selectedViewType === 'REF' && jobDetails.workStatus !== WorkStatus.REVIEWED && !isJobSearchScreen"
              flat
              small
              color="error"
              @click="editingReferenceFormController = false"
              >Cancel Changes</v-btn
            >
            <v-menu
              open-on-hover
              :close-on-click="false"
              content-class="pendingClientReviewJobWarning"
              :close-on-content-click="false"
              bottom
              left
              v-if="hasPendingClientStatus && (selectedViewType === 'REF' || selectedViewType === 'PRI') && jobDetails.workStatus === WorkStatus.DRIVER_COMPLETED"
            >
              <template v-slot:activator="{ on }">
                <v-btn flat icon color="error" class="ma-0" dark v-on="on">
                  <v-icon size="16">fas fa-info-circle</v-icon>
                </v-btn>
              </template>

              <div
                class="pa-2 app-borderside--a app-bordercolor--600 rate-hover__container"
                style="cursor: pointer"
              >
                <v-layout row wrap
                  >"{{jobDetails.client.clientName}}" is currently in a pending
                  state. Please contact head office.
                </v-layout>
              </div>
            </v-menu>
            <span
              v-if="!requiredBreakDurationsMet && selectedViewType === 'PRI' && jobDetails.workStatus === WorkStatus.DRIVER_COMPLETED"
              class="mr-1 pt-1"
            >
              <v-tooltip left z-index="1000">
                <template v-slot:activator="{ on }">
                  <v-icon size="22" class="pl-1" color="amber" v-on="on">
                    fas fa-exclamation-triangle
                  </v-icon>
                </template>
                <span
                  >Minimum break of {{requiredBreakForJobLength ?
                  returnDurationFromMilliseconds(requiredBreakForJobLength) :
                  '-'}} is required.</span
                >
              </v-tooltip>
            </span>
            <ConfirmationDialog
              v-if="selectedViewType === 'PRI' && jobDetails.workStatus === WorkStatus.DRIVER_COMPLETED"
              :key="confirmationDialogKey"
              buttonText="Mark as Reviewed  ✓ "
              message="Please confirm the following:"
              title="Mark as Reviewed"
              @confirm="markJobAsReviewed"
              :buttonDisabled="!jobReferenceRequirementsMet || !pudReferenceRequirementsMet || !jobWeightRequirementsMet || !accountingTotalsComputed ||!minimumDurationRequirementsMet || hasPendingClientStatus || !allHireContractsValid"
              :isSmallButton="true"
              :isOutlineButton="true"
              :buttonColor="'#00e676'"
              confirmationButtonText="Mark as Reviewed"
              :confirmationButtonColor="'success'"
              :isCheckbox="false"
              :isCheckboxList="true"
              :isLoading="awaitingJobUpdateResponse"
              :checkboxLabelList="pricingConfirmationCheckList"
              :dialogIsActive="isOutsideHire || clientHasReferenceRequirements || !requiredBreakDurationsMet"
            />
            <template
              v-if="selectedViewType === 'REF' && jobDetails.workStatus !== WorkStatus.REVIEWED && !isJobSearchScreen"
            >
              <v-btn
                v-if="!editingReferenceFormController"
                depressed
                small
                color="info"
                class="v-btn-confirm-custom"
                @click="editingReferenceFormController = true"
              >
                Edit Requirements
              </v-btn>
              <v-btn
                v-if="editingReferenceFormController"
                depressed
                small
                :loading="awaitingJobReferencesSaveRequest"
                color="success"
                class="v-btn-confirm-custom"
                @click="saveJobRequirements"
              >
                Save Requirements
              </v-btn>
            </template>

            <v-btn
              v-if="selectedViewType === 'TAB' && jobDetails.workStatus === WorkStatus.REVIEWED"
              :disabled="!isAuthorised()"
              @click="markJobAsFinalised"
              small
              dense
              :loading="awaitingJobUpdateResponse"
              color="#00521f"
              class="v-btn-confirm-custom"
            >
              <v-icon right dark class="pr-2"> done_all </v-icon>
              Mark as Finalised
            </v-btn>
          </v-layout>
          <v-layout
            class="dialog-content__scrollable-section"
            v-if="!isLoading"
          >
            <JobDetailsInformation
              v-if="selectedViewType === 'SUM'"
              class="px-1"
              :jobDetails="jobDetails"
              :key="jobDetails.jobId"
              :isJobDetailsDialog="true"
              :isJobSearch="isJobSearchScreen"
            />

            <v-flex md12 v-if="selectedViewType === 'CSH'">
              <CashSalesDetails
                :clientDetails="jobDetails.cashSaleClientDetails"
                :proofOfDelivery="jobDetails.proofOfDelivery"
                :cashSaleClientExpansionPanelOpen="[true]"
                :isDisplayOnly="true"
              />
            </v-flex>

            <AssetManagement
              v-if="selectedViewType === 'ASM'"
              :job="jobDetails"
              :client="clientDetails"
            />
            <JobMapRoute
              v-if="selectedViewType === 'MAP' && jobDetails.plannedRoute && !isRequestingJobGpsData"
              mapId="jobDialogMap"
              :key="jobDetails.jobId"
              :jobDetails="jobDetails"
              :gpsPositionData="jobGpsData"
              :mapConfig="jobMapConfig"
            >
            </JobMapRoute>
            <JobPricingManagement
              class="pa-3"
              v-if="selectedViewType === 'PRI' && (jobDetails.client.id === 'CS' || clientDetails && clientDetails.clientId) && truckDetails && fleetAssetOwnerDetails"
              ref="jobPricingManagement"
              :key="`pricing-${jobDetails.jobId}-${jobDetails.currentExactJobStatus}`"
              :jobAccountingDetails="jobAccountingDetails"
              :clientDetails="clientDetails"
              :jobDetails="jobDetails"
              :driverRegisteredForGst="driverRegisteredForGst"
              :truckDetails="truckDetails"
              :fleetAssetOwner="fleetAssetOwnerDetails"
              :accountingTotalsComputed="accountingTotalsComputed"
              :clientMinimumDurationMet="clientMinimumDurationMet"
              :driverMinimumDurationMet="driverMinimumDurationMet"
              :readOnlyView="readOnlyView"
              :showEDIConfirmation="showEDIConfirmation"
              :hireContracts="appliedHireContracts"
              @accountingTotalsComputedUpdated="accountingTotalsComputedUpdated"
              @updateFullJobAccountingDetails="updateFullJobAccountingDetails"
              @setClientMinimumDurationMet="setClientMinimumDurationMet"
              @setDriverMinimumDurationMet="setDriverMinimumDurationMet"
            >
            </JobPricingManagement>
            <JobStatusManagement
              v-if="selectedViewType === 'STM'"
              :jobDetails="jobDetails"
              :isDialog="true"
            >
            </JobStatusManagement>
            <JobRequirementManagement
              v-if="selectedViewType === 'REF'"
              :jobDetails="jobDetails"
              :readOnlyView="!isEditingForm || readOnlyView || isJobSearchScreen"
              :clientDetails="clientDetails"
              :pudItems="pudItems"
              :jobReferences="jobReferences"
              :weightRequirement="weightRequirement"
            ></JobRequirementManagement>
            <JobAttachmentApproval
              v-if="selectedViewType === 'ATT'"
              :jobDetails="jobDetails"
              :key="jobDetails.jobId"
              :imageSliderExpanded="false"
              :isOutsideHire="isOutsideHire"
            >
            </JobAttachmentApproval>
            <FinaliseJobAccountingTable
              v-if="selectedViewType === 'TAB'"
              :key="jobDetails.jobId"
              :isEquipmentHire="isEquipmentHire"
              :invoiceDetails="jobDetails.accounting"
              :job="jobDetails"
              :fleetAssetGstRegistered="isFleetOwnerRegisteredForGst"
              :scrollableBody="true"
              class="ma-3 app-borderside--a app-bordercolor--600"
            >
            </FinaliseJobAccountingTable>
            <DriverConversation
              v-if="selectedViewType === 'MSG' && jobDetails.driverId"
              :driverId="jobDetails.driverId"
            ></DriverConversation>
            <JobDetailsEventListSummary
              v-if="selectedViewType === 'EVT'"
              :jobDetails="jobDetails"
              :showTimeline="!isJobSearchScreen && jobDetails.workStatus >= WorkStatus.IN_PROGRESS"
              :splitColumn="true"
            >
            </JobDetailsEventListSummary>
          </v-layout>
          <v-layout
            align-center
            class="dialog-content__scrollable-section pa-3"
            v-else
          >
            <img
              src="@/static/loader/infinity-loader-light.svg"
              height="80px"
              width="80px"
            />
          </v-layout>
        </v-flex>
      </v-layout>
    </template>
  </JobContentDialog>

  <ContentDialog
    v-if="isViewingDriverDeviceInfoDialog"
    :showDialog.sync="isViewingDriverDeviceInfoDialog"
    title="Driver Device Info"
    :isConfirmUnsaved="false"
    width="40%"
    confirmBtnText="Done"
    @confirm="cancelDriverDeviceInfoView"
    @cancel="cancelDriverDeviceInfoView"
    :showCancelBtn="false"
  >
    <DriverDeviceSnapshotSummary
      :driverDeviceSnapshot="driverDeviceSnapshot"
    ></DriverDeviceSnapshotSummary>
  </ContentDialog>
  <RebookJobDialog
    v-if="showRebookDialog"
    :jobToRebook="jobDetails"
    :showRebookDialog.sync="showRebookDialog"
    @closeDialog="closeJobDetailsDialog"
  />
  <AdjustServiceDialogsDialog
    v-if="showServiceDetailsDialog"
    :jobDetails="jobDetails"
    :clientDetails="clientDetails"
    :showServiceDetailsDialog.sync="showServiceDetailsDialog"
    @refreshAccountingInParent="refreshAccountingInParent"
  />

  <UnassignedPudLinkingMaintenance
    v-if="showUnassignedPudLinkingMaintenance"
    :staticJobDetails="jobDetails"
    @closeUnassignedPudLinkingMaintenance="showUnassignedPudLinkingMaintenance = false"
  />
  <v-dialog
    v-model="showMessageHistoryDialog"
    v-if="jobDetails.driverId && showMessageHistoryDialog"
    width="800px"
    class="ma-0"
    persistent
    no-click-animation
    content-class="v-dialog-custom"
  >
    <v-flex md12>
      <v-layout
        justify-space-between
        align-center
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>Driver Message History</span>
        <div
          class="app-theme__center-content--closebutton"
          @click="showMessageHistoryDialog = false"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <v-layout row wrap class="app-theme__center-content--body dialog-content">
        <v-flex md12>
          <v-form ref="formRef">
            <v-layout row wrap>
              <DriverChatHistory
                :singleDriverType="true"
                :driverId="jobDetails.driverId"
              ></DriverChatHistory>
            </v-layout>
          </v-form>
        </v-flex>
        <v-flex md12>
          <v-divider></v-divider>
          <v-layout align-center>
            <v-btn flat color="error" @click="showMessageHistoryDialog = false"
              >Cancel</v-btn
            >
            <v-spacer></v-spacer>
            <v-btn outline color="white" @click="">Edit</v-btn>
            <v-btn
              depressed
              color="success"
              @click=""
              class="v-btn-confirm-custom"
              >Save</v-btn
            >
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>
  </v-dialog>
  <ConfirmationDialog
    title="Requires Review"
    v-if="jobRequiresReviewConfirmation"
    :requiresButtonToOpen="false"
    message="The job must be reviewed via the edit job screen before any service rate adjustments can be made."
    @confirm="confirmJobRequiresReview"
    confirmationButtonText="Okay"
    @closeDialog="confirmJobRequiresReview"
    :dialogIsActive="true"
  />
</div>
