import JobDetailsReport from '@/components/admin/Administration/reporting/reports/job_details_report.vue';
import JobBookingPricingSummary from '@/components/booking/pricing_details/job_booking_pricing_summary.vue';
import NotesList from '@/components/common/notes_list/notes_list.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import ConsignmentNoteDialog from '@/components/operations/OperationDashboard/components/JobDetails/consignment_note_dialog/index.vue';
import JobDetailsPudItem from '@/components/operations/OperationDashboard/components/JobDetails/job_details_pud_item/job_details_pud_item.vue';
import JobListActionButtons from '@/components/operations/OperationDashboard/components/JobList/job_list_action_buttons/job_list_action_buttons.vue';
import JobNoteDialog from '@/components/operations/OperationDashboard/components/JobNoteDialog/job_note_dialog.vue';
import { downloadAttachment } from '@/helpers/AttachmentHelpers/AttachmentHelpers';
import { editNoteInDialog } from '@/helpers/CommunicationHelpers/CommunicationHelpers';
import {
  returnFormattedDate,
  returnFormattedTime,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { returnAttachmentTypeName } from '@/helpers/JobDataHelpers/JobDataHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  returnEquipmentTypeFromId,
  returnServiceTypeShortNameFromId,
} from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { returnStatusConfigFromId } from '@/helpers/StatusHelpers/StatusHelpers';
import { hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { LedgerType } from '@/interface-models/Accounting/LedgerType';
import { DivisionReportSettings } from '@/interface-models/Company/DivisionCustomConfig/DivisionCustomConfig';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import Attachment from '@/interface-models/Generic/Attachment/Attachment';
import {
  ReferenceTypes,
  referenceTypes,
} from '@/interface-models/Generic/ReferenceTypes/ReferenceTypes';
import serviceTypeRates, {
  ServiceTypeRates,
} from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import ShortLongName from '@/interface-models/Generic/ShortLongName/ShortLongName';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import GenerateReportRequest from '@/interface-models/Reporting/GenerateReportRequest';
import { ProofOfDeliveryRequest } from '@/interface-models/Reporting/ProofOfDeliveryRequest';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import unitRateTypes from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRateTypes';
import StatusConfig from '@/interface-models/Status/StatusConfig';
import { useAttachmentStore } from '@/store/modules/AttachmentStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetMapStore } from '@/store/modules/FleetMapStore';
import { useGpsStore } from '@/store/modules/GpsStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { useRootStore } from '@/store/modules/RootStore';
import Mitt from '@/utils/mitt';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';

interface JobDetailsLineItem {
  id: string;
  title: string;
  value: string | number;
}
@Component({
  components: {
    JobDetailsPudItem,
    JobListActionButtons,
    NotesList,
    JobNoteDialog,
    JobDetailsReport,
    ContentDialog,
    ConsignmentNoteDialog,
    JobBookingPricingSummary,
  },
})
export default class JobDetailsInformation extends Vue {
  @Prop() public selectedJobId: number;
  @Prop({ default: false }) public isJobSearch: boolean;
  @Prop({ default: false }) public isJobDetailsDialog: boolean;
  @Prop() public jobDetails: JobDetails;

  public attachmentStore = useAttachmentStore();
  public operationsStore = useOperationsStore();

  public company = useCompanyDetailsStore().companyDetails;
  public reportsSettings: DivisionReportSettings | null =
    this.company?.divisions?.[0]?.customConfig?.reports ?? null;

  public docMenuOpen: boolean = false;

  public editNoteInDialog = editNoteInDialog;

  public rateTypes: ServiceTypeRates[] = serviceTypeRates;
  public referenceTypes: ReferenceTypes[] = referenceTypes;
  public unitRateTypes: ShortLongName[] = unitRateTypes;
  public settingsView: boolean = false;
  public settingsReferences: boolean = true;
  public settingsNotes: boolean = true;
  public settingsPricingDetails: boolean = true;
  public settingsJobTimes: boolean = true;
  public settingsSignatures: boolean = true;
  public settingsAllocationDetails: boolean = true;
  public settingsWeightAndPieces: boolean = true;
  public settingsModButtons: boolean = true;
  public settingsBiggerPrice: boolean = true;
  public settingsShowVertical: boolean = true;
  public settingsBiggerAddress: boolean = true;
  public settingsBiggerDriverNumber: boolean = true;
  public settingsShowQuantity: boolean = true;
  public settingsShowFiles: boolean = true;
  public settingsShowAssets: boolean = true;
  public showDialog: boolean = false;
  public podLoading: boolean = false;

  public isRequestingFullSizeAttachment: boolean = false;
  public jobReportDialogActive: boolean = false;

  public returnFormattedTime = returnFormattedTime;
  public returnAttachmentTypeName = returnAttachmentTypeName;
  public routeProgressInterval: ReturnType<typeof setInterval> | null = null;

  public isViewingConsignmentNoteDialog: boolean = false;
  public jobReportLoading: boolean = false;

  public gpsStore = useGpsStore();
  public fleetMapStore = useFleetMapStore();

  // store last gps location of driver
  public recentGpsLocation: string | null = '';

  get isOutsideHire() {
    if (!this.jobDetails) {
      return false;
    }
    return (
      this.jobDetails.additionalJobData !== undefined &&
      this.jobDetails.additionalJobData.isOutsideHire
    );
  }

  get serviceTypeShortName() {
    if (!this.jobDetails) {
      return '-';
    }
    return returnServiceTypeShortNameFromId(this.jobDetails.serviceTypeId);
  }
  get showJobNoteDialog() {
    return this.operationsStore.viewingJobNotesDialog;
  }

  get podReady() {
    if (!this.jobDetails) {
      return false;
    }
    return this.jobDetails.workStatus >= WorkStatus.DRIVER_COMPLETED;
  }

  get driverFuelSurchargeRate(): number {
    const rate = this.jobDetails!.accounting.fleetAssetRates[0];
    if (
      rate &&
      rate.rate.isDriverFuelApplied(this.clientFuelSurchargeRate) &&
      this.jobDetails!.accounting.additionalCharges?.fleetAssetFuelSurcharge
    ) {
      const fuelSurcharge =
        this.jobDetails!.accounting.additionalCharges.fleetAssetFuelSurcharge;

      if (fuelSurcharge && fuelSurcharge.appliedFuelSurchargeRate) {
        return fuelSurcharge.appliedFuelSurchargeRate;
      }
    }
    return 0;
  }

  get clientFuelSurchargeRate(): number {
    const rate = this.jobDetails!.accounting.clientRates[0];
    if (rate && rate.rate.isClientFuelApplied) {
      const fuelSurcharge =
        this.jobDetails!.accounting.additionalCharges.clientFuelSurcharge;
      if (fuelSurcharge?.appliedFuelSurchargeRate) {
        return fuelSurcharge.appliedFuelSurchargeRate;
      }
    }
    return 0;
  }

  // return data that is displayed in the left panel as key value pairs
  get massagedJobDetailsSummary() {
    if (!this.jobDetails) {
      return [];
    }
    const job: JobDetails = this.jobDetails;
    const lineItemList: JobDetailsLineItem[] = [];
    if (job.accounting.invoiceId) {
      lineItemList.push({
        id: 'Invoice No',
        title: 'Invoice No',
        value: job.accounting.invoiceId,
      });
    }
    if (job.accounting.rctiId) {
      lineItemList.push({
        id: 'RCTI No',
        title: 'RCTI No',
        value: job.accounting.rctiId,
      });
    }

    lineItemList.push({
      id: 'Status',
      title: 'Status',
      value: this.jobDetails!.currentExactJobStatus,
    });

    lineItemList.push({
      id: 'Optr',
      title: 'Optr',
      value: this.jobDetails!.recurringJobId ? 'Permanent' : 'Ad Hoc',
    });

    lineItemList.push({
      id: 'Driver',
      title: 'Driver',
      value: job.driverName,
    });

    lineItemList.push({
      id: 'Fleet Asset',
      title: 'Fleet Asset',
      value: this.isOutsideHire
        ? `O/H: ${
            job.additionalJobData ? job.additionalJobData.ownerName : '-'
          }`
        : job.csrAssignedId,
    });
    if (this.settingsWeightAndPieces) {
      lineItemList.push({
        id: 'Weight',
        title: 'Weight',
        value: `${this.totalWeight} kg`,
      });
      if (this.jobDetails!.serviceTypeObject.rateTypeId === 5) {
        lineItemList.push({
          id: 'Pieces',
          title: 'Pieces',
          value: this.totalPieces,
        });
      }
    }
    lineItemList.push({
      id: 'Equipment',
      title: 'Req Equipmt',
      value: this.additionalEquipment,
    });
    lineItemList.push({
      id: 'Date',
      title: 'Job Date',
      value: this.readyDate,
    });
    lineItemList.push({
      id: 'Start',
      title: 'Start',
      value: this.readyTime,
    });
    lineItemList.push({
      id: 'Finish',
      title: 'Finish',
      value: this.finishTime,
    });
    lineItemList.push({
      id: 'Booked',
      title: 'Booked',
      value: this.bookedEvent
        ? returnFormattedDate(
            this.bookedEvent.correctEventTime,
            'ddd Do MMM  hh:mm a',
          )
        : '-',
    });
    lineItemList.push({
      id: 'Booked By',
      title: 'Booked By',
      value: this.bookedEvent ? this.bookedEvent.editedBy : '-',
    });
    if (this.settingsAllocationDetails) {
      lineItemList.push({
        id: 'Allocated',
        title: 'Allocated',
        value: this.allocationDate,
      });
      lineItemList.push({
        id: 'Allocated By',
        title: 'Allocated By',
        value: this.allocatedBy,
      });
      lineItemList.push({
        id: 'Reviewed By',
        title: 'Reviewed By',
        value: this.reviewedBy,
      });
      if (this.finalisedBy) {
        lineItemList.push({
          id: 'Finalised By',
          title: 'Finalised By',
          value: this.finalisedBy,
        });
      }
    }

    lineItemList.push({
      id: 'Client Rates',
      title: 'Client Rates',
      value: '-',
    });
    lineItemList.push({
      id: 'Client Rate Type',
      title: 'Type',
      value: this.clientRateType,
    });
    lineItemList.push({
      id: 'Client Total ',
      title: 'Total',
      value: this.clientChargeTotal,
    });
    lineItemList.push({
      id: 'Client Freight',
      title: 'Freight',
      value: this.clientChargeFreight,
    });
    lineItemList.push({
      id: 'Client Fuel',
      title: 'Fuel',
      value: this.clientFuelSurchargeRate
        ? `${this.clientFuelSurchargeRate}%`
        : '-',
    });

    lineItemList.push({
      id: 'Driver Rates',
      title: 'Driver Rates',
      value: '',
    });
    lineItemList.push({
      id: 'Driver Rate Type',
      title: 'Type',
      value: this.driverRateType,
    });
    lineItemList.push({
      id: 'Driver Total',
      title: 'Total',
      value: this.driverPayTotal,
    });
    lineItemList.push({
      id: 'Driver Freight',
      title: 'Freight',
      value: this.driverPayFreight,
    });

    lineItemList.push({
      id: 'Driver Fuel',
      title: 'Fuel',
      value: this.driverFuelSurchargeRate
        ? `${this.driverFuelSurchargeRate}%`
        : '-',
    });

    return lineItemList;
  }

  get driverRateType() {
    const rate = this.jobDetails!.accounting.fleetAssetRates[0];
    if (rate) {
      const foundRate = this.rateTypes.find(
        (x: ServiceTypeRates) => x.rateTypeId === rate.rate.rateTypeId,
      );

      if (foundRate) {
        return foundRate.longName;
      } else {
        return '-';
      }
    } else {
      return '-';
    }
  }

  public referenceTypeName(refId: number) {
    const foundReference = this.referenceTypes.find(
      (x: ReferenceTypes) => x.id === refId,
    );
    if (foundReference) {
      return foundReference.longName;
    }
  }

  get jobStatus() {
    if (!this.jobDetails) {
      return '-';
    }
    const allStatusList: StatusConfig[] = useRootStore().statusTypeList;
    const statusItem = returnStatusConfigFromId(
      allStatusList,
      this.jobDetails.statusList[0],
    );
    if (statusItem) {
      return statusItem.enumValue;
    }
  }

  get clientRateType() {
    if (!this.jobDetails) {
      return '';
    }

    const foundRate = this.rateTypes.find(
      (x: ServiceTypeRates) =>
        x.rateTypeId === this.jobDetails!.serviceTypeObject.rateTypeId,
    );
    if (!foundRate) {
      return '-';
    }
    return foundRate.longName;
  }
  // Return a sum of all pudItem unitPickups values
  get totalPieces() {
    // If it is a UNIT rate type job then sum the unitPickups that are defined
    // as DYNAMIC type
    if (this.jobDetails!.serviceTypeObject.rateTypeId === 5) {
      return this.jobDetails!.pudItems.reduce(
        (prev, curr) =>
          prev +
          (curr.rateDetails && curr.rateDetails.unitTypeId === 1
            ? curr.rateDetails.unitPickUps
              ? curr.rateDetails.unitPickUps
              : 0
            : 0),
        0,
      );
    }
    return 0;
  }
  // Return a sum of all pudItem weight values for all PICKUP type legs
  get totalWeight(): number {
    return this.jobDetails!.pudItems.reduce(
      (prev, curr) =>
        prev + (curr.legTypeFlag === 'P' && curr.weight ? curr.weight : 0),
      0,
    );
  }

  get additionalEquipment(): string {
    const additionalEquipment = this.jobDetails?.additionalEquipments ?? [];

    if (!additionalEquipment.length) {
      return '-';
    }

    const equipmentNames = additionalEquipment.map((e) =>
      returnEquipmentTypeFromId(e),
    );

    const firstTwo = equipmentNames.slice(0, 1);
    const remainingCount = equipmentNames.length - 1;

    if (remainingCount > 0) {
      return `${firstTwo.join(', ')} +${remainingCount} more`;
    }

    return firstTwo.join(', ');
  }

  get fullAdditionalEquipment(): string {
    const additionalEquipment = this.jobDetails?.additionalEquipments ?? [];

    if (!additionalEquipment.length) {
      return '-';
    }

    return additionalEquipment
      .map((e) => returnEquipmentTypeFromId(e))
      .join(', ');
  }

  get bookedEvent() {
    if (this.jobDetails) {
      return this.jobDetails.returnSpecifiedEvent('Booked');
    }
  }

  get clientChargeTotal() {
    return this.jobDetails!.accounting.totals &&
      this.jobDetails!.accounting.totals.finalTotal.client
      ? '$' + this.jobDetails!.accounting.totals.finalTotal.client
      : '-';
  }
  get clientChargeFreight() {
    return this.jobDetails!.accounting.totals &&
      this.jobDetails!.accounting.totals.subtotals.freightCharges.client
      ? '$' + this.jobDetails!.accounting.totals.subtotals.freightCharges.client
      : '-';
  }

  get driverPayTotal() {
    return this.jobDetails!.accounting.totals &&
      this.jobDetails!.accounting.totals.finalTotal.fleetAsset
      ? '$' + this.jobDetails!.accounting.totals.finalTotal.fleetAsset
      : '-';
  }
  get driverPayFreight() {
    return this.jobDetails!.accounting.totals &&
      this.jobDetails!.accounting.totals.subtotals.freightCharges.fleetAsset
      ? '$' +
          this.jobDetails!.accounting.totals.subtotals.freightCharges.fleetAsset
      : '-';
  }

  get allocationDate() {
    const allocatedBy = this.jobDetails!.returnSpecifiedEvent('AllocateJob');
    if (allocatedBy) {
      return returnFormattedDate(
        allocatedBy.correctEventTime,
        'ddd Do MMM  hh:mm a',
      );
    }
    return '-';
  }

  get allocatedBy() {
    const allocatedBy = this.jobDetails!.returnSpecifiedEvent('AllocateJob');
    if (allocatedBy) {
      return allocatedBy.editedBy;
    }
    return '-';
  }

  get reviewedBy() {
    const reviewedEvent = this.jobDetails!.returnSpecifiedEvent('REVIEWED');
    if (reviewedEvent) {
      return reviewedEvent.editedBy;
    } else {
      return '-';
    }
  }
  get jobHasActiveServiceFailure() {
    if (!this.jobDetails) {
      return false;
    }
    return this.jobDetails.serviceFailure;
  }

  get driverName(): string {
    if (!this.jobDetails) {
      return '-';
    }
    const driver = useDriverDetailsStore().getDriverFromDriverId(
      this.jobDetails!.driverId,
    );
    return driver?.displayName ?? '-';
  }

  get finalisedBy() {
    const finalisedEvent = this.jobDetails!.returnSpecifiedEvent(
      'READY_FOR_INVOICING',
    );
    if (finalisedEvent) {
      return finalisedEvent.editedBy;
    } else {
      return '';
    }
  }

  get readyTime() {
    return returnFormattedDate(
      this.jobDetails!.pudItems[0].epochTime,
      'hh:mm a',
    );
  }

  get finishTime() {
    return returnFormattedDate(
      this.jobDetails!.pudItems[this.jobDetails!.pudItems.length - 1]
        .epochTime +
        this.jobDetails!.pudItems[this.jobDetails!.pudItems.length - 1]
          .loadTime,
      'hh:mm a',
    );
  }

  /**
   * Resets the selectedJobId value in the store to -1, which hides the Job
   * Details section on the dashboard.
   */
  public deselectJob() {
    this.$emit('closeAllExpandedRows', 'job-details-panel');
    this.operationsStore.setSelectedJobId(-1);
  }

  get readyDate() {
    return returnFormattedDate(
      this.jobDetails!.pudItems[0].epochTime,
      'ddd Do MMM',
    );
  }

  public closeJobDetails() {
    this.$emit('closeJobDetails');
  }

  get statusListEnumValue() {
    if (!this.jobDetails) {
      return;
    }
    const allStatusList: StatusConfig[] = useRootStore().statusTypeList;
    const statusItem = returnStatusConfigFromId(
      allStatusList,
      this.jobDetails.statusList[0],
    );
    if (statusItem) {
      return statusItem.enumValue;
    }
  }

  public allocatePreallocatedJob(jobIds: number[]) {
    // console.log('allocatePreallocatedJob', jobIds);
  }

  public addNoteToJob(
    jobId: number,
    serviceFailure: boolean = false,
    cancelJob: boolean = false,
  ): void {
    this.operationsStore.setSelectedJobId(jobId);
    this.operationsStore.setViewingJobNotesDialog(true);
    this.operationsStore.setJobServiceFailure(serviceFailure);
    this.operationsStore.setJobCancellation(cancelJob);
  }

  public viewJobDetailsDialog(jobId: number): void {
    this.operationsStore.setSelectedJobId(jobId);
    this.operationsStore.setViewingJobDetailsDialog(true);
  }

  get jobAttachments(): Attachment[] {
    return this.jobDetails && this.jobDetails.attachments
      ? this.jobDetails.attachments
      : [];
  }

  // return the current/next active pud id.
  get activePudId() {
    if (!this.jobDetails) {
      return null;
    }
    const validPudItems = this.jobDetails.pudItems.filter(
      (p) => p.legTypeFlag === 'P' || p.legTypeFlag === 'D',
    );
    const allPudsComplete = validPudItems.every(
      (pud) => pud.status === 'FINISHED',
    );
    if (allPudsComplete) {
      return null;
    }
    let latestPudArrivalId: string | null = null;
    let latestPudFinishedId: string | null = null;
    let nextActivePud: string | null = null;

    for (const pud of validPudItems) {
      if (pud.status === 'ARRIVED') {
        latestPudArrivalId = pud.pudId;
      }
      if (pud.status === 'FINISHED') {
        latestPudFinishedId = pud.pudId;
      }
      if (!nextActivePud && !pud.status) {
        nextActivePud = pud.pudId;
      }
    }
    if (!latestPudArrivalId && !latestPudFinishedId) {
      return null;
    }

    if (latestPudArrivalId) {
      return latestPudArrivalId;
    }
    return nextActivePud;
  }

  /**
   * Scroll down the scrolling-div element to the current PUD Item (currently on
   * site or next in schedule)
   */
  public scrollToActiveDrop() {
    if (!this.activePudId) {
      return;
    }
    const myElement: any = document.getElementById(this.activePudId);
    const topPos = myElement.offsetTop;
    (document.getElementById('scrolling-div') as any).scrollTop = topPos - 6;
  }

  public async requestFullSizeImage(img: Attachment) {
    this.isRequestingFullSizeAttachment = true;
    const att = await this.attachmentStore.getAttachmentById(img.id);
    if (att) {
      downloadAttachment(att);
    } else {
      showNotification('Download failed. Please try again later.');
    }
    this.isRequestingFullSizeAttachment = false;
  }

  get canDownloadInvoiceAndRCTI() {
    return hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole();
  }

  /**
   * Dispatches request over websocket using the clientId and the invoiceId
   * to download the attachments that were sent
   */
  public downloadInvoice(accessMethod: ReportAccessMethodTypes) {
    this.docMenuOpen = false;
    if (
      !this.jobDetails ||
      !this.jobDetails.accounting.invoiceId ||
      !this.jobDetails.client.id
    ) {
      return;
    }
    // Dispatch request
    this.attachmentStore.requestInvoices({
      type: LedgerType.CLIENT_INVOICE,
      invoiceNumber: this.jobDetails.accounting.invoiceId,
      entityId: this.jobDetails.client.id,
      accessType: accessMethod,
    });
  }

  /**
   * Dispatches request over websocket using the rctiId and the ownerId
   * to download the attachments that were sent
   */
  public downloadRCTI(accessMethod: ReportAccessMethodTypes) {
    this.docMenuOpen = false;
    if (!this.jobDetails || !this.jobDetails.accounting.rctiId) {
      return;
    }
    const fleetAssetOwner: FleetAssetOwnerSummary | undefined =
      useFleetAssetOwnerStore().getOwnerList.find((x: FleetAssetOwnerSummary) =>
        x.associatedFleetAssets.includes(this.jobDetails.fleetAssetId),
      );
    if (!fleetAssetOwner) {
      return;
    }
    // Dispatch request
    this.attachmentStore.requestInvoices({
      type: LedgerType.RCTI,
      invoiceNumber: this.jobDetails.accounting.rctiId,
      entityId: fleetAssetOwner.ownerId,
      accessType: accessMethod,
    });
  }

  /**
   * Callback for mitt listener
   * @param response response payload containing report
   */
  public handleReportResponse(): void {
    this.podLoading = false;
    this.jobReportLoading = false;
    this.jobReportDialogActive = false;
    this.setResponseListener(false);
  }

  /**
   * When we make the request to download POD, we turn on the mitt listener so
   * we can reset the loader when the response comes in
   * @param value boolean value to set the listener
   */
  public setResponseListener(value: boolean) {
    if (value) {
      Mitt.on('encodedReport', this.handleReportResponse);
    } else {
      Mitt.off('encodedReport', this.handleReportResponse);
    }
  }

  /**
   * Sets loaders and listeners, and sends the request to generate the job Proof
   * of Delivery
   */
  public getProofOfDelivery(accessMethod: ReportAccessMethodTypes) {
    this.docMenuOpen = false;
    if (!this.jobDetails?.jobId) {
      return;
    }
    this.podLoading = true;
    this.setResponseListener(true);
    const request: ProofOfDeliveryRequest = {
      jobId: this.jobDetails.jobId,
      accessType: accessMethod,
    };
    this.attachmentStore.generateProofOfDelivery(request);
  }

  /**
   * Shows the consignment dialog
   */
  public showConsignmentNoteDialog() {
    this.isViewingConsignmentNoteDialog = true;
  }

  /**
   * Sets loaders and listeners, and sends the request to generate the job
   * details report
   * @param request GenerateReportRequest object containing the parameters to
   * generate the report
   */
  public generateJobDetailsReport(request: GenerateReportRequest) {
    this.docMenuOpen = false;
    this.jobReportLoading = true;
    this.setResponseListener(true);
    this.attachmentStore.generateJobDetailsReport(request);
  }

  // To get the most up to date estimates we should update the jobs route
  // progress information. Every minute there will be a request go out to get
  // this updated information
  public setRouteProgressInterval() {
    this.routeProgressInterval = setInterval(() => {
      if (this.operationsStore.selectedJobDetails) {
        this.operationsStore.selectedJobDetails.getRouteProgressMatrix();
      }
    }, 60000);
  }

  public mounted() {
    this.setRouteProgressInterval();
  }

  public beforeDestroy() {
    // clear the route progress interval
    if (this.routeProgressInterval) {
      clearInterval(this.routeProgressInterval);
    }
    // When the component is destroyed, we remove the mitt listener
    this.setResponseListener(false);
  }

  // watch for jobDetails.fleetAssetId changes to get the latest GPS location
  @Watch('jobDetails.fleetAssetId', { immediate: true })
  async onFleetAssetIdChanged() {
    if (this.jobDetails) {
      this.recentGpsLocation = await this.gpsStore.returnFormattedGpsLocation(
        this.jobDetails.fleetAssetId,
      );
    }
  }
}
