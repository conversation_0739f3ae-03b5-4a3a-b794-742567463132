<template>
  <section class="job-details-pud-item">
    <v-layout
      class="pud-item"
      v-if="
        (jobDetails && pudItem.legTypeFlag === 'P') ||
        pudItem.legTypeFlag === 'D'
      "
      :id="pudItem.pudId"
      :class="[
        activePudId === pudItem.pudId ? 'activePudBorder' : '',
        detailedView ? 'detailed-view' : 'simple-view',
      ]"
      row
      wrap
    >
      <v-flex md12 class="pud-item__location-details">
        <v-layout justify-space-between align-center>
          <span class="pud-item__location-details--suburb"
            >{{ pudItem.address.suburb ? pudItem.address.suburb : '- ' }}
            {{ pudItem.address.postcode ? pudItem.address.postcode : '' }}</span
          >

          <StreetViewLink
            v-if="!isClientPortal"
            class="pr-2"
            style="padding-bottom: 4px"
            :coordinates="pudItem.address.geoLocation"
          />

          <v-layout>
            <v-layout row class="pud-item__location-details--customer-details">
              <span
                class="pud-item__location-details--detail-item"
                v-if="pudItem.customerDeliveryName"
                >{{ pudItem.customerDeliveryName }}</span
              >
              <span
                class="pud-item__location-details--detail-item"
                v-if="pudItem.address.addressLine1"
                >{{ pudItem.address.addressLine1 }}
                {{ pudItem.address.addressLine2 }}</span
              >
              <span
                class="pud-item__location-details--detail-item"
                v-if="pudItem.payloadDescription"
                >{{ pudItem.payloadDescription }}</span
              >
            </v-layout>
          </v-layout>

          <v-spacer></v-spacer>
          <span
            class="tag-accent-card green-fill mr-2 black--text"
            v-if="pudItem.status === 'FINISHED'"
          >
            FINISHED
            <v-icon class="finished-pud-icon black--text" size="10"
              >fad fa-check</v-icon
            >
          </span>
          <span
            class="tag-accent-card green-outline mr-2"
            v-if="pudItem.status === 'ARRIVED'"
          >
            ARRIVED
          </span>
          <JobBookingProgressChips
            v-if="pudRouteProgress"
            :pudRouteProgress="pudRouteProgress"
            :pudStatus="pudItem.status"
            :isNextStop="nextStopPudId === pudItem.pudId"
            :isClientPortal="isClientPortal"
            :createdByDriver="pudItem.createdByDriver"
            :isOutsideMetro="pudItem.isOutsideMetro"
            :isStandbyRate="pudItem.isStandbyRate"
            :driverLocationUnknown="pudRouteProgress.driverLocationUnknown"
          >
          </JobBookingProgressChips>
        </v-layout>
      </v-flex>
      <v-layout md12>
        <v-flex>
          <v-layout
            class="job-details-card__keyvalue"
            v-for="(item, index) in eventsForPud"
            :key="item.key + index"
          >
            <span class="job-details-card__keyvalue--key">
              {{ item.key }}:
            </span>
            <span class="job-details-card__keyvalue--value">
              {{ item.value }}
            </span>
          </v-layout>
        </v-flex>
        <v-flex md10 class="pud-item__attachmentcontainer pl-2">
          <v-layout row wrap>
            <v-tooltip
              bottom
              max-width="220px"
              close-delay="0"
              v-for="img in attachmentImageData"
              :key="img.id"
              content-class="v-tooltip__small-text"
            >
              <template v-slot:activator="{ on }">
                <div
                  class="image-container__container"
                  @click="requestFullSizeImage(img)"
                  v-on="on"
                >
                  <div
                    class="image-container"
                    :class="
                      img.attachment.documentTypeId === 14
                        ? 'signature-image'
                        : ''
                    "
                  >
                    <v-img
                      v-if="img.attachment"
                      :src="
                        img.attachment.data &&
                        img.attachment.mimeType.includes('image') &&
                        !img.attachment.mimeType.includes('heic') &&
                        !img.attachment.mimeType.includes('heif')
                          ? img.attachment.data
                          : ''
                      "
                      aspect-ratio="1"
                    >
                      <template v-slot:placeholder>
                        <v-layout
                          justify-center
                          align-center
                          fill-height
                          column
                        >
                          <v-icon size="30"
                            >{{ getIconByMimeType(img.attachment.mimeType) }}
                          </v-icon>
                        </v-layout>
                      </template>
                    </v-img>
                  </div>
                </div>
              </template>
              <span
                ><v-layout row wrap>
                  <v-flex md12>
                    <v-layout>
                      Attachment Type:
                      <v-spacer></v-spacer
                      >{{
                        returnAttachmentTypeName(img.attachment.documentTypeId)
                      }}
                    </v-layout></v-flex
                  >
                  <v-flex md12 v-if="img.attachment.signatureName">
                    <v-layout>
                      Signed By:
                      <v-spacer></v-spacer
                      >{{
                        img.attachment.signatureName
                          ? img.attachment.signatureName
                          : '-'
                      }}
                    </v-layout></v-flex
                  >
                  <v-flex md12>
                    <v-layout>
                      Created: <v-spacer></v-spacer
                      >{{
                        img.attachment.timestamp
                          ? returnFormattedTime(
                              img.attachment.timestamp,
                              `HH:mm
                    DD/MM/YY`,
                            )
                          : '-'
                      }}
                    </v-layout></v-flex
                  >
                  <v-flex md12>
                    <v-layout>
                      Longitude: <v-spacer></v-spacer
                      >{{
                        img.attachment.gpsLocation &&
                        img.attachment.gpsLocation[0]
                          ? img.attachment.gpsLocation[0].toFixed(4)
                          : 'Unknown'
                      }}
                    </v-layout></v-flex
                  >
                  <v-flex md12>
                    <v-layout>
                      Latitude: <v-spacer></v-spacer
                      >{{
                        img.attachment.gpsLocation &&
                        img.attachment.gpsLocation[1]
                          ? img.attachment.gpsLocation[1].toFixed(4)
                          : 'Unknown'
                      }}
                    </v-layout></v-flex
                  >
                </v-layout>
              </span>
            </v-tooltip>
          </v-layout>

          <v-layout md12 class="pt-2">
            <v-flex md5>
              <p class="ma-0 mt-2 ml-2 subheader--text pb-0">
                Payload Information
              </p>
              <v-divider class="mb-2"></v-divider>
              <div class="scrollable-panel">
                <PudManifestList
                  :manifestList="pudItem.manifest ? pudItem.manifest : []"
                ></PudManifestList>
              </div>
            </v-flex>

            <v-flex ma-1>
              <v-divider vertical></v-divider>
            </v-flex>

            <v-flex md7>
              <p class="ma-0 mt-2 ml-2 subheader--text pb-0">
                {{
                  pudItem.legTypeFlag === 'P'
                    ? 'Pickup'
                    : pudItem.legTypeFlag === 'D'
                      ? 'Dropoff'
                      : 'Leg'
                }}
                Notes
              </p>
              <v-divider class="mb-2"></v-divider>
              <div class="scrollable-panel">
                <NotesList
                  :isBookingScreen="false"
                  :communications="getVisibleNotesByUserRole(pudItem.notes)"
                  :allowEdit="true"
                  :jobId="jobDetails.jobId"
                  :pudId="pudItem.pudId"
                  :showVisibilityTypeName="true"
                  @editNote="
                    editNoteInDialog($event, jobDetails.jobId, pudItem.pudId)
                  "
                />
              </div>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-layout>

    <v-layout class="mb-1 px-4" v-if="showEstimateTimeWarning">
      <span class="estimated-time-note"
        >Estimated times are estimates only and are not guaranteed to be 100%
        accurate.
      </span>
    </v-layout>
  </section>
</template>

<script setup lang="ts">
import { Ref, ComputedRef, ref, computed } from 'vue';
import JobBookingProgressChips from '@/components/booking/delivery_details/job_booking_progress_chips.vue';
import NotesList from '@/components/common/notes_list/notes_list.vue';
import StreetViewLink from '@/components/common/street_view_link.vue';
import PudManifestList from '@/components/operations/BookJob/pud_manifest_list/index.vue';
import {
  downloadAttachment,
  getIconByMimeType,
} from '@/helpers/AttachmentHelpers/AttachmentHelpers';
import { editNoteInDialog } from '@/helpers/CommunicationHelpers/CommunicationHelpers';
import {
  returnDurationFromMilliseconds,
  returnFormattedTime,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  getVisibleNotesByUserRole,
  returnAttachmentTypeName,
} from '@/helpers/JobDataHelpers/JobDataHelpers';
import Attachment from '@/interface-models/Generic/Attachment/Attachment';
import { KeyValue } from '@/interface-models/Generic/KeyValue/KeyValue';
import { Portal } from '@/interface-models/Generic/Portal';
import {
  ReferenceTypes,
  referenceTypes,
} from '@/interface-models/Generic/ReferenceTypes/ReferenceTypes';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { JobRouteProgress } from '@/interface-models/Jobs/JobRouteProgress';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useAttachmentStore } from '@/store/modules/AttachmentStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { sessionManager } from '@/store/session/SessionState';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';

interface AttachmentGroup {
  id: string;
  fileName: string;
  attachment: Attachment;
  idList: string[];
}

const props = withDefaults(
  defineProps<{
    jobDetails: JobDetails;
    pudItem: PUDItem;
    activePudId?: string | null;
    detailedView?: boolean;
  }>(),
  {
    activePudId: null,
    detailedView: true,
  },
);

/** Reference types for display */
const referenceTypesRef: Ref<ReferenceTypes[]> = ref(referenceTypes);

/** Whether a full-size attachment is being requested */
const isRequestingFullSizeAttachment: Ref<boolean> = ref(false);

/**
 * Computed: Key-value event data for this PUD
 * @returns KeyValue[]
 */
const eventsForPud: ComputedRef<KeyValue[]> = computed((): KeyValue[] => {
  const dataList: KeyValue[] = [];
  const pudItem = props.pudItem;
  const jobDetails = props.jobDetails;

  dataList.push({
    key: 'Stop',
    value: pudItem.legTypeFlag === 'P' ? 'Pickup' : 'Drop-off',
  });

  const foundArrivedEvent = jobDetails.returnSpecifiedEvent(
    'ARRIVED',
    pudItem.pudId,
  );
  if (foundArrivedEvent) {
    dataList.push({
      key: 'Arrived',
      value:
        returnFormattedTime(foundArrivedEvent.correctEventTime, 'hh:mm a') ||
        '-',
    });
  }
  const foundFinishedEvent = jobDetails.returnSpecifiedEvent(
    'FINISHED',
    pudItem.pudId,
  );
  if (foundFinishedEvent) {
    dataList.push({
      key: 'Departed',
      value:
        returnFormattedTime(foundFinishedEvent.correctEventTime, 'hh:mm a') ||
        '-',
    });
  }
  if (foundArrivedEvent && foundFinishedEvent) {
    const duration =
      foundFinishedEvent.correctEventTime - foundArrivedEvent.correctEventTime;
    dataList.push({
      key: 'Duration',
      value: returnDurationFromMilliseconds(duration) || '-',
    });
  }
  dataList.push({
    key: 'Length',
    value:
      (pudItem.dimensions &&
        pudItem.dimensions.length &&
        `${pudItem.dimensions.length}m`) ||
      '-',
  });
  dataList.push({
    key: 'Width',
    value:
      (pudItem.dimensions &&
        pudItem.dimensions.width &&
        `${pudItem.dimensions.width}m`) ||
      '-',
  });
  dataList.push({
    key: 'Height',
    value:
      (pudItem.dimensions &&
        pudItem.dimensions.height &&
        `${pudItem.dimensions.height}m`) ||
      '-',
  });

  dataList.push({
    key: 'Weight',
    value: `${pudItem.weight || 0} kg`,
  });

  // If this is a UNIT rate job then show the PIECES line item
  if (jobDetails.serviceTypeObject.rateTypeId === JobRateType.UNIT) {
    let payloadUnits: number = 0;
    let unitsSuffix: string = '';
    // If it is a WEIGHT UnitRateType then show KG
    if (pudItem.rateDetails.unitTypeId === 2) {
      unitsSuffix = 'kg';
      payloadUnits += pudItem.weight || 0;
    } else {
      // If it is a DYNAMIC UnitRateType then use either unitPickups or unitDropoffs depending on the legTypeFlag
      payloadUnits +=
        pudItem.legTypeFlag === 'P'
          ? pudItem.rateDetails.unitPickUps
          : pudItem.rateDetails.unitDropOffs;
    }
    dataList.push({
      key: 'Pieces',
      value: `${payloadUnits || 0} ${unitsSuffix}`.trim(),
    });
  }
  const references = (pudItem.pickupReference || []).concat(
    pudItem.dropoffReference || [],
  );
  for (const reference of references) {
    const foundReference = referenceTypesRef.value.find(
      (x: ReferenceTypes) => x.id === reference.referenceTypeId,
    );
    if (foundReference) {
      dataList.push({
        key: foundReference.longName || '-',
        value: reference.reference || '-',
      });
    }
  }

  dataList.push({
    key: 'Contact',
    value: pudItem.siteContactName || '-',
  });
  dataList.push({
    key: 'Mobile',
    value: pudItem.siteContactMobileNumber || '-',
  });
  dataList.push({
    key: 'Landline',
    value: pudItem.siteContactLandLineNumber || '-',
  });
  return dataList;
});

/**
 * Computed: Attachment groups for this PUD
 * @returns AttachmentGroup[]
 */
const attachmentImageData: ComputedRef<AttachmentGroup[]> = computed(
  (): AttachmentGroup[] => {
    const pudItem = props.pudItem;
    if (!pudItem) {
      return [];
    }
    const attachments = pudItem.attachments ? pudItem.attachments : [];
    const uniqueFileNames: string[] = [];
    attachments.forEach((att) => {
      if (!uniqueFileNames.includes(att.name)) {
        uniqueFileNames.push(att.name);
      }
    });

    const attGroupList: AttachmentGroup[] = [];

    uniqueFileNames.forEach((name) => {
      const attList = attachments.filter((att) => att.name === name);
      if (attList.length > 0) {
        const firstAtt = attList[0];

        const group: AttachmentGroup = {
          id: firstAtt.id,
          fileName: name,
          attachment: firstAtt,
          idList: attList.map((a) => a.id),
        };
        attGroupList.push(group);
      }
    });

    return attGroupList;
  },
);

/**
 * Request full-size images for an attachment group and download them.
 * @param imgGroup - The group of attachments to download.
 */
async function requestFullSizeImage(imgGroup: AttachmentGroup): Promise<void> {
  isRequestingFullSizeAttachment.value = true;
  const attPromises = imgGroup.idList.map((id) =>
    useAttachmentStore().getAttachmentById(id),
  );
  const results = await Promise.all(attPromises);
  results.forEach((result) => {
    if (result?.data && attachmentListIncludesId(result.id)) {
      downloadAttachment(result);
    }
  });
  isRequestingFullSizeAttachment.value = false;
}

/**
 * Checks if an attachment ID exists in the current PUD's attachments.
 * @param id - The attachment ID to check.
 * @returns True if the ID exists, false otherwise.
 */
function attachmentListIncludesId(id: string): boolean {
  const pudItem = props.pudItem;
  if (!pudItem) {
    return false;
  }
  return pudItem.attachments.map((pud) => pud.id).includes(id);
}

/**
 * Computed: Whether the current portal is a client portal.
 * @returns boolean
 */
const isClientPortal: ComputedRef<boolean> = computed((): boolean => {
  return sessionManager.getPortalType() === Portal.CLIENT;
});

/**
 * Computed: Route progress for this PUD.
 * @returns JobRouteProgress | null
 */
const pudRouteProgress: ComputedRef<JobRouteProgress | null> = computed(
  (): JobRouteProgress | null => {
    const operationsStore = useOperationsStore();
    const isClient = isClientPortal.value;
    if (
      (!isClient &&
        (!operationsStore.selectedJobDetails ||
          !operationsStore.selectedJobDetails.additionalJobData)) ||
      (isClient && !props.jobDetails)
    ) {
      return null;
    }

    const jobDetails: JobDetails | null = !isClient
      ? operationsStore.selectedJobDetails
      : props.jobDetails;

    if (!jobDetails || !jobDetails.additionalJobData) {
      return null;
    }

    const jobRouteProgress = jobDetails.additionalJobData.routeProgress.find(
      (x: JobRouteProgress) => x.pudId === props.pudItem.pudId,
    );
    return jobRouteProgress ? jobRouteProgress : null;
  },
);

/**
 * Computed: The next stop PUD ID.
 * @returns string | null
 */
const nextStopPudId: ComputedRef<string | null> = computed(
  (): string | null => {
    const nextPud = props.jobDetails.pudItems.find((x: PUDItem) => !x.status);
    return nextPud ? nextPud.pudId : null;
  },
);

/**
 * Computed: Whether to show the estimated time warning.
 * @returns boolean
 */
const showEstimateTimeWarning: ComputedRef<boolean> = computed((): boolean => {
  const foundFinishedEvent = props.jobDetails.returnSpecifiedEvent(
    'FINISHED',
    props.pudItem.pudId,
  );
  const jobStarted = props.jobDetails.workStatus === WorkStatus.IN_PROGRESS;
  return jobStarted && !foundFinishedEvent && isClientPortal.value;
});
</script>
<style scoped lang="scss">
.job-details-pud-item {
  width: 100%;
  $text-size: 10px;
  // max-height: 10px;

  .activePudBorder {
    border-style: solid !important;
    border-color: $success-type !important;
    border-width: 1px !important;
  }

  .tag-accent-card {
    border-radius: $border-radius-base;
    font-size: $font-size-10;
    padding: 1px 5px;
    font-weight: 500;
    display: flex;
    align-items: center;

    &.green-outline {
      outline: 1px solid $toast-success-bg;
    }

    &.green-fill {
      background-color: $success-type;
      padding: 2px 6px;
    }

    &.blue-outline {
      outline: 1px solid $highlight-dark;
    }

    &.amber-outline {
      outline: 1px solid $warning;
    }

    &.white-outline {
      outline: 1px solid $bg-light;
    }

    &.red-outline {
      outline: 1px solid $error;
    }
  }

  .finished-pud-icon {
    padding-left: 2px;
    padding-bottom: 1px;
  }

  .pud-item {
    margin-bottom: 14px;
    padding: 6px 12px;

    &.detailed-view {
      background-color: var(--background-color-300);
      border-radius: $border-radius-base;

      box-shadow:
        inset 6px 6px 10px 0 var(--background-color-250),
        inset -6px -6px 10px 0 var(--background-color-250);
      border: 1px solid $translucent;
    }

    .subheader--text {
      color: var(--primary-light);
    }

    .scrollable-panel {
      max-height: 250px;
      overflow-y: auto;
      padding-right: 8px;
    }

    // &.break-type {
    //   background-color: green;
    // }

    .pud-item__location-details {
      border-bottom: 1px solid $translucent;
      margin-bottom: 6px;
      padding-bottom: 6px;
      .pud-item__location-details--suburb {
        font-size: $font-size-12;
        font-weight: 600;
        color: var(--accent);
        text-transform: uppercase;
        padding-right: 12px;
        margin-top: 4px;
      }
    }

    .info-label {
      font-size: $font-size-12 !important;
    }

    .pud-item__attachmentcontainer {
      $thumbnail-res: 50px;

      .image-container__container {
        height: $thumbnail-res;
        width: $thumbnail-res;
        margin: 0px 5px;
        border-radius: 50%;
        overflow: hidden;
        cursor: pointer;

        &:hover {
          cursor: pointer;

          .image-container__info {
            visibility: visible;
          }
        }

        .image-container__info {
          position: absolute;
          bottom: 0px;
          right: 0px;
          visibility: hidden;
          padding: 3px;
          transition: 0s;
        }

        .image-container {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          overflow: hidden;

          &.center-content {
            align-items: center;
            justify-content: center;
            flex-direction: column;
          }

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
          }
        }

        .delete-button {
          visibility: hidden;
          position: absolute;
          padding: 6px;
          top: 0px;
          right: 4px;
          cursor: pointer;
        }

        &:hover {
          .delete-button {
            visibility: visible;
          }
        }
      }
    }
  }

  .pud-item__location-details--customer-details {
    width: 100%;
    color: var(--heading-text-color);

    .pud-item__location-details--detail-item {
      font-size: $font-size-12;
      font-weight: 500;
      margin-top: 2px;
      padding-right: 8px;
    }
  }

  .job-details-card__keyvalue {
    display: flex;
    margin-right: 2px;
    padding-right: 2px;
    border-right: 1px solid var(--border-color);

    .job-details-card__keyvalue--key {
      flex: 0 0 80px;
      font-size: $font-size-12;
      text-transform: uppercase;
      text-align: left;
      color: var(--light-text-color);
      opacity: 0.9;
    }

    .job-details-card__keyvalue--value {
      flex: 1;
      font-size: $font-size-12;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-bottom: 8px;
      color: var(--text-color);
    }
  }
}

.signature-image {
  background-color: var(--light-text-color) !important;
}

.arrival-pud-text {
  color: var(--light-text-color);
  font-size: $font-size-10;
  padding-bottom: 10px;
}
.estimated-time-note {
  font-size: $font-size-10;
  font-weight: 400;
  color: var(--light-text-color);
  font-style: italic;
}
</style>
