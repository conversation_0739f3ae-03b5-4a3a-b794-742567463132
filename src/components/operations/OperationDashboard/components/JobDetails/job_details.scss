.job-details-component {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: $border-radius-sm;
  border: 1px solid $translucent;

  &.allow-overflow {
    overflow: visible;
  }

  &.overflow-hidden {
    overflow: hidden;
    resize: vertical;
  }

  .task-bar {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
  }

  .close-details:hover {
    color: #ffffff;
  }
  .expanded-content__icon-container {
    min-height: 36px;
    padding: 8px;
    margin: 0px 3px;
    min-width: 36px;
    border: 2px solid var(--primary);
    border-radius: $border-radius-Xlg;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    &:hover {
      background-color: var(--primary-dark) !important;
    }
    &.add-note {
      border-color: $warning-type;
      &:hover {
        background-color: $toast-warning-bg !important;
      }
    }
    &.download {
      border-color: $info;
      &:hover {
        background-color: $toast-info-border !important;
      }
    }
    &.active-drop {
      border-color: $toast-success-bg;
      &:hover {
        background-color: $toast-success-border !important;
      }
    }

    .expanded-content__text {
      font-size: $font-size-11;
      font-weight: 600;
      text-transform: uppercase;
      color: var(--text-color);
      padding-right: 5px;
    }

    &.disabled {
      pointer-events: none;
      opacity: 0.4;
    }

    &:hover {
      cursor: pointer;
      filter: brightness(150%);

      .expanded-content__icon-tooltip {
        visibility: visible;
      }
    }
    .expanded-content__icon {
      font-size: $font-size-14;
      color: var(--light-text-color);
    }
    .expanded-content__icon-tooltip {
      position: absolute;
      visibility: hidden;
      padding: 8px;
      background-color: var(--primary-dark);
      border: 1px solid $translucent;
      color: white;
      border-radius: 20px;
      box-shadow: 0px 8px 32px var(--shadow-color) !important;
      font-size: 1em;
      font-weight: 500;
      width: 120px;
      top: 122%;
      text-align: center;
      z-index: 10000000000000000000;

      &.left-align {
        left: 0px;
      }
      &.right-align {
        right: 0px;
      }
      &.add-note {
        color: black;
        background-color: $toast-warning-bg;
      }
      &.download {
        background-color: $toast-info-border;
      }
      &.active-drop {
        background-color: $toast-success-border;
      }
    }
  }

  .content {
    height: calc(100% - 25px);
    position: relative;
    overflow: hidden;
    display: flex;

    .settings-panel {
      min-height: 100%;
      background-color: var(--background-color-200);

      &.slideout {
        border-right: 3px solid darken(#2b2a30, 2%);
      }

      &.slidein {
        overflow-x: hidden;
        white-space: nowrap;
      }
    }

    .job-details__mod-buttons {
      min-height: 100%;

      .mod-buttons__container {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow-y: auto;
        overflow-x: hidden;

        .mod-buttons__icon-container {
          padding: 6px 8px;
          border-radius: 2px;
          position: relative;

          &.disabled {
            pointer-events: none;
            opacity: 0.4;
          }

          &:hover {
            cursor: pointer;
            filter: brightness(120%);
            background-color: #32333a;

            // .mod-buttons__icon-tooltip {
            //   visibility: visible;
            // }
          }
          .mod-buttons__icon {
            font-size: $font-size-12;
            color: var(--light-text-color);
          }
          .mod-buttons__icon-tooltip {
            position: absolute;
            visibility: hidden;
            padding: 4px 6px;
            background-color: #25262a;
            border: 1px solid #4d4d4e;
            color: white;
            border-radius: 20px;
            box-shadow: 0px 8px 32px var(--shadow-color) !important;
            font-size: 1em;
            font-weight: 500;
            width: 80px;
            top: 0px;
            text-align: center;
            z-index: 10000000000000000000;
            left: 100%;
            // &.left-align {
            //   left: 0px;
            // }
            // &.right-align {
            //   right: 0px;
            // }
          }
        }
      }
    }
  }

  .slidein,
  .slideout {
    max-width: 0;
    min-width: 0;
    overflow-x: hidden;
    -webkit-transition: max-width 0.1s ease-in-out;
    -moz-transition: max-width 0.1s ease-in-out;
    -o-transition: max-width 0.1s ease-in-out;
    transition: max-width 0.1s ease-in-out;
  }
  .slideout {
    min-width: 240px;
    max-width: 240px;
  }
  .fixWidth {
    max-width: calc(100% - 30px);
  }
  .content-scrollable {
    max-height: 100%;

    flex-direction: column;
    overflow-y: auto;
    display: flex;
    flex-grow: 1;

    .job-details-card {
      $text-size: 10px;
      min-height: calc(33vh - 60px);

      .job-details-card__cornerheader {
        padding: 4px 10px;
        margin: 2px;
        color: var(--text-color);
        border-radius: $border-radius-sm;
        border-bottom: 1px solid $translucent;
        border-right: 1px solid $translucent;
        box-shadow: -2px -2px 10px 0 var(--background-color-250);
        .job-details-card__mainheader {
          font-size: $font-size-22;
          font-weight: 600;
        }
        .job-details-card__subheader {
          font-size: $font-size-14;
          font-weight: 500;
          color: var(--accent-secondary);
        }

        .fixed-txt {
          color: var(--text-color);
          opacity: 0.5;
          font-weight: 500;
          padding: 4px;
          font-size: $font-size-14;
        }
      }

      .job-details-card__header {
        font-size: $font-size-16;
        font-weight: 600;
        color: var(--primary-light);
      }

      .job-details-card__body-side {
        width: 100%;

        .job-details-card__keyvalue {
          display: flex;
          margin-bottom: 2px;
          width: 100%;

          &--key {
            width: 40%;
            font-size: $text-size;
            color: var(--light-text-color);
            text-align: left;
            white-space: nowrap;
            opacity: 0.8;
            text-transform: uppercase;
          }

          &--value {
            color: var(--text-color);
            width: 60%;
            font-size: $text-size;
            text-align: left;
            white-space: wrap;
            overflow: visible;
            text-overflow: ellipsis;
            padding-left: 26px;
          }
        }
      }

      .job-details-card__body-side-dialog {
        padding-left: 6px;

        .job-details-card__keyvalue {
          display: flex;
          flex-direction: column;
          flex-wrap: wrap;
          margin-bottom: 6px;

          .job-details-card__keyvalue--key {
            font-size: $font-size-10;
            color: var(--light-text-color);
            text-transform: uppercase;
            display: flex;
            opacity: 0.8;
          }

          .job-details-card__keyvalue--value {
            color: var(--text-color);
            font-size: $font-size-12;
            display: flex;
          }
        }
      }

      .job-details-card__body-top {
        width: 100%;
        margin-left: 12px;
        border-bottom: 1px solid $translucent;

        .job-details-card__keyvalue {
          display: flex;
          flex-direction: column;
          flex-wrap: wrap;

          .job-details-card__keyvalue--key {
            font-size: $font-size-10;
            color: var(--light-text-color);
            text-transform: uppercase;
            text-align: right;
            display: flex;
            opacity: 0.8;
          }

          .job-details-card__keyvalue--value {
            font-size: $font-size-10;
            padding-bottom: 8px;
            display: flex;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            max-width: 95%;
            color: var(--text-color);
          }
        }

        .key-value-pairs {
          padding-right: 12px;
        }
      }
    }
  }

  .image-container__container {
    $thumbnail-res: 65px;
    width: $thumbnail-res;
    height: $thumbnail-res;
    position: relative;
    margin: 0px 2px;
    cursor: pointer;
    .image-container {
      height: $thumbnail-res;
      width: $thumbnail-res;
      min-width: $thumbnail-res;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #1f1f24;
      border: 1px solid #72727a;

      &.center-content {
        align-items: center;
        justify-content: center;
        flex-direction: column;
      }
    }
    .delete-button {
      visibility: hidden;
      position: absolute;
      padding: 6px;
      top: 0px;
      right: 4px;
      cursor: pointer;
    }

    &:hover {
      .delete-button {
        visibility: visible;
      }
    }
  }

  .image-container__container {
    $thumbnail-res: 65px;
    height: $thumbnail-res;
    width: $thumbnail-res;
    position: relative;
    margin: 0px 2px;

    &:hover {
      cursor: pointer;
      .image-container__info {
        visibility: visible;
      }
    }

    .image-container__info {
      position: absolute;
      bottom: 0px;
      right: 0px;
      visibility: hidden;
      padding: 3px;
      transition: 0s;
    }
    .image-container {
      height: $thumbnail-res;
      width: $thumbnail-res;
      display: flex;
      background-color: #1d1d25;
      border: 1px solid #72727a;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      &.center-content {
        align-items: center;
        justify-content: center;
        flex-direction: column;
      }

      img {
        max-width: 100%;
        height: 100%;
        object-fit: contain !important;
      }
    }
    .delete-button {
      visibility: hidden;
      position: absolute;
      padding: 6px;
      top: 0px;
      right: 4px;
      cursor: pointer;
    }

    &:hover {
      .delete-button {
        visibility: visible;
      }
    }
  }
}

.v-tooltip__content {
  max-width: 300px !important;
}

.driver-info-wrapper {
  display: flex;
  justify-content: right; /* center horizontally */
  align-items: center;
  .driver-coordinates {
    background-color: var(--hover-bg);
    height: 20px;
    font-size: $font-size-10;
    border-radius: 20px 20px 0 0;
    min-width: 320px;
    margin-right: 8px;
    display: flex;
    justify-content: right; /* center horizontally */
    align-items: center;

    .key-txt {
      // color: var(--text-color);
      // // margin-right: 12px;
      .v-icon {
        color: var(--success);
        margin-right: 4px;
        margin-bottom: 1px;
        font-weight: 600;
      }
    }
    .value-txt {
      color: var(--light-text-color);
      margin-right: 24px;
    }
  }
}

.info-txt {
  margin-top: 18px;
  display: block;
  font-weight: 600;
  font-size: $font-size-16;
  color: var(--error);
  text-align: center;
}

.faded-text {
  color: var(--light-text-color);
  font-style: italic;
}

.accent-text--primary {
  color: var(--primary-light) !important;
}
