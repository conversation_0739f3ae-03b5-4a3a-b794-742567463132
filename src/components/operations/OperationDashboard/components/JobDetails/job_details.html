<div
  class="job-details-component"
  :class="isJobDetailsDialog ? 'allow-overflow' : 'overflow-hidden'"
>
  <div
    class="task-bar py-0 app-theme__center-content--header no-highlight"
    v-if="!isJobDetailsDialog"
  >
    <span class="ma-0">Job Details</span>
    <span v-if="jobDetails" class="pl-2 pr-2 accent-text--primary">
      - Job #{{ jobDetails.displayId }} ({{jobDetails.client.clientName}})
    </span>
    <span v-if="isOutsideHire" class="pl-2 pr-2 amber--text text--lighten-1">
      [ OUTSIDE HIRE ]
    </span>
    <v-spacer />
    <v-icon
      v-if="!isJobSearch"
      size="14"
      class="icon-hover--primary mr-3"
      :class="settingsView ? 'active-state' : ''"
      @click="settingsView = !settingsView"
      >fas fa-sliders-h</v-icon
    >
    <v-tooltip right v-if="!isJobDetailsDialog && !isJobSearch">
      <template v-slot:activator="{ on }">
        <v-btn small flat v-on="on" icon @click="deselectJob" class="ma-0">
          <v-icon size="16" color="grey lighten-1">fal fa-times</v-icon>
        </v-btn>
      </template>
      Unselect job and hide Job Details panel
    </v-tooltip>
    <div>
      <v-icon
        v-if="isJobSearch"
        size="14"
        class="app-theme__center-content--settings-icon close-details"
        @click="closeJobDetails"
        >fas fa-times</v-icon
      >
    </div>
  </div>

  <div class="content app-theme__center-content--body">
    <div class="settings-panel slidein" :class="settingsView ? 'slideout' : ''">
      <v-layout column justify-start px-3 py-2>
        <span class="custom-input">
          <input
            id="job-details-settings-settingsReferences"
            type="checkbox"
            v-model="settingsReferences"
          />
          <label for="job-details-settings-settingsReferences"
            >References</label
          >
        </span>
        <span class="custom-input">
          <input
            id="job-details-setti-settingsNotesngs"
            type="checkbox"
            v-model="settingsNotes"
          />
          <label for="job-details-settings-settingsNotes">Show Notes</label>
        </span>

        <span class="custom-input">
          <input
            id="job-details-settings-settingsPricingDetails"
            type="checkbox"
            v-model="settingsPricingDetails"
          />
          <label for="job-details-settings-settingsPricingDetails"
            >Show Pricing Details</label
          >
        </span>

        <span class="custom-input">
          <input
            id="job-details-settings-settingsJobTimes"
            type="checkbox"
            v-model="settingsJobTimes"
          />
          <label for="job-details-settings-settingsJobTimes"
            >Show Job Times</label
          >
        </span>

        <span class="custom-input">
          <input
            id="job-details-settings-settingsSignatures"
            type="checkbox"
            v-model="settingsSignatures"
          />
          <label for="job-details-settings-settingsSignatures"
            >Show Settings Signatures</label
          >
        </span>

        <span class="custom-input">
          <input
            id="job-details-settings-settingsAllocationDetails"
            type="checkbox"
            v-model="settingsAllocationDetails"
          />
          <label for="job-details-settings-settingsAllocationDetails"
            >Show Allocation Details</label
          >
        </span>

        <span class="custom-input">
          <input
            id="job-details-settings-settingsWeightAndPieces"
            type="checkbox"
            v-model="settingsWeightAndPieces"
          />
          <label for="job-details-settings-settingsWeightAndPieces"
            >Show Weights and Pieces</label
          >
        </span>

        <span class="custom-input">
          <input
            id="job-details-settings-settingsModButtons"
            type="checkbox"
            v-model="settingsModButtons"
          />
          <label for="job-details-settings-settingsModButtons"
            >Show Mod Buttons</label
          >
        </span>

        <span class="custom-input">
          <input
            id="job-details-settings-settingsBiggerPrice"
            type="checkbox"
            v-model="settingsBiggerPrice"
          />
          <label for="job-details-settings-settingsBiggerPrice"
            >Show Bigger Price</label
          >
        </span>
        <span class="custom-input">
          <input
            id="job-details-settings-settingsShowVertical"
            type="checkbox"
            v-model="settingsShowVertical"
          />
          <label for="job-details-settings-settingsShowVertical"
            >Show Vertical</label
          >
        </span>
        <span class="custom-input">
          <input
            id="job-details-settings-settingsBiggerAddress"
            type="checkbox"
            v-model="settingsBiggerAddress"
          />
          <label for="job-details-settings-settingsBiggerAddress"
            >Show Bigger Address</label
          >
        </span>

        <span class="custom-input">
          <input
            id="job-details-settings-settingsBiggerDriverNumber"
            type="checkbox"
            v-model="settingsBiggerDriverNumber"
          />
          <label for="job-details-settings-settingsBiggerDriverNumber"
            >Show Bigger Driver Number</label
          >
        </span>

        <span class="custom-input">
          <input
            id="job-details-settings-settingsShowQuantity"
            type="checkbox"
            v-model="settingsShowQuantity"
          />
          <label for="job-details-settings-settingsShowQuantity"
            >Show Quantity</label
          >
        </span>
        <span class="custom-input">
          <input
            id="job-details-settings-settingsShowFiles"
            type="checkbox"
            v-model="settingsShowFiles"
          />
          <label for="job-details-settings-settingsShowFiles">Show Files</label>
        </span>

        <span class="custom-input">
          <input
            id="job-details-settings-settingsShowAssets"
            type="checkbox"
            v-model="settingsShowAssets"
          />
          <label for="job-details-settings-settingsShowAssets"
            >Show Assets</label
          >
        </span>
      </v-layout>
    </div>
    <div
      class="job-details__mod-buttons app-theme__center-content--side-bar"
      v-if="settingsModButtons && jobDetails !== null && !isJobSearch && !isJobDetailsDialog"
    >
      <JobListActionButtons
        :jobDetails="jobDetails"
        :jobId="jobDetails.jobId"
        :statusList="jobDetails.statusList"
        :isVertical="true"
        @allocatePreallocatedJob="allocatePreallocatedJob"
      >
      </JobListActionButtons>
    </div>
    <div
      class="content-scrollable"
      :class="jobDetails && !isJobSearch && !isJobDetailsDialog ? 'fixWidth' : ''"
      id="scrolling-div"
    >
      <v-layout>
        <v-flex md12>
          <div class="job-details-card pa-2">
            <v-layout v-if="jobDetails !== null">
              <v-flex>
                <v-layout
                  row
                  wrap
                  justify-end
                  class="job-details-card__cornerheader"
                >
                  <v-flex md12>
                    <v-layout
                      justify-end
                      class="job-details-card__mainheader"
                      :class="jobDetails.recurringJobId ? 'recurring-job-id__highlight' : ''"
                    >
                      <span class="fixed-txt">Job ID:</span>
                      <v-spacer />
                      <span>{{jobDetails.displayId}}</span>
                    </v-layout>
                  </v-flex>
                  <v-flex md12>
                    <v-layout justify-end class="job-details-card__subheader">
                      <span class="fixed-txt">Service :</span>
                      <v-spacer />
                      <span>{{serviceTypeShortName}}</span>
                    </v-layout>
                  </v-flex>
                </v-layout>
              </v-flex>
              <v-flex md11 class="job-details-card__body-top">
                <v-layout row>
                  <v-flex md3 class="key-value-pairs">
                    <v-layout class="job-details-card__keyvalue">
                      <span class="job-details-card__keyvalue--key">
                        CLNO:
                      </span>
                      <span class="job-details-card__keyvalue--value">
                        {{jobDetails.client.id}}
                      </span>
                    </v-layout>
                    <v-layout class="job-details-card__keyvalue">
                      <span class="job-details-card__keyvalue--key">
                        Customer Name:
                      </span>
                      <span class="job-details-card__keyvalue--value">
                        {{jobDetails.client.clientName}}
                      </span>
                    </v-layout>
                  </v-flex>
                  <v-flex md3>
                    <v-layout class="job-details-card__keyvalue">
                      <span class="job-details-card__keyvalue--key">
                        Landline:
                      </span>
                      <span class="job-details-card__keyvalue--value">
                        {{ jobDetails.clientDispatcher.contactLandlineNumber ?
                        jobDetails.clientDispatcher.contactLandlineNumber : '-'
                        }}
                      </span>
                    </v-layout>
                    <v-layout class="job-details-card__keyvalue">
                      <span class="job-details-card__keyvalue--key">
                        Mobile:
                      </span>
                      <span class="job-details-card__keyvalue--value">
                        {{ jobDetails.clientDispatcher.contactMobileNumber ?
                        jobDetails.clientDispatcher.contactMobileNumber : '-' }}
                      </span>
                    </v-layout>
                  </v-flex>
                  <v-flex md3>
                    <v-layout class="job-details-card__keyvalue">
                      <span class="job-details-card__keyvalue--key">
                        Driver:
                      </span>
                      <span class="job-details-card__keyvalue--value">
                        {{jobDetails.driverName}}
                      </span>
                    </v-layout>
                    <v-layout wrap class="pb-1">
                      <v-flex md12>
                        <v-layout class="job-details-card__keyvalue">
                          <span class="job-details-card__keyvalue--key">
                            Reference:
                          </span>
                          <span class="job-details-card__keyvalue--value">
                            <template
                              v-if="jobDetails.jobReference && jobDetails.jobReference.length > 0"
                            >
                              {{ jobDetails.jobReference[0].reference }}
                              <template
                                v-if="jobDetails.jobReference.length > 1"
                              >
                                (+{{ jobDetails.jobReference.length - 1 }})
                              </template>
                            </template>
                            <template v-else> - </template>
                          </span>
                        </v-layout>
                      </v-flex>
                    </v-layout>
                  </v-flex>
                  <v-flex md3>
                    <v-layout class="job-details-card__keyvalue">
                      <span class="job-details-card__keyvalue--key">
                        Caller:
                      </span>
                      <span class="job-details-card__keyvalue--value">
                        {{jobDetails.clientDispatcher.firstName }}
                        {{jobDetails.clientDispatcher.lastName }}
                      </span>
                    </v-layout>
                    <v-layout class="job-details-card__keyvalue">
                      <span class="job-details-card__keyvalue--key">
                        Legs:
                      </span>
                      <span class="job-details-card__keyvalue--value">
                        {{jobDetails.numberOfLegs}}
                      </span>
                    </v-layout>
                  </v-flex>
                  <v-flex md3>
                    <v-layout justify-end align-center>
                      <span
                        class="expanded-content__icon-container"
                        v-if="jobHasActiveServiceFailure && isJobSearch"
                      >
                        <v-icon class="expanded-content__icon" color="error">
                          fad fa-exclamation-circle
                        </v-icon>
                        <span
                          class="expanded-content__icon-tooltip right-align"
                        >
                          Job was a service failure
                        </span>
                      </span>

                      <JobNoteDialog
                        v-if="isJobSearch && showJobNoteDialog && jobDetails"
                        :jobDetails="jobDetails"
                        :showJobNoteDialog="showJobNoteDialog"
                      >
                      </JobNoteDialog>
                      <span class="pr-2" v-if="isOutsideHire">
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on }">
                            <v-icon v-on="on" color="amber" size="16">
                              fas fa-exclamation-circle
                            </v-icon>
                          </template>
                          This job is allocated to an OUTSIDE HIRE.
                        </v-tooltip></span
                      >
                      <span
                        class="expanded-content__icon-container active-drop"
                        v-if="activePudId"
                        @click="scrollToActiveDrop"
                      >
                        <v-icon
                          class="expanded-content__icon"
                          :disabled="!activePudId"
                        >
                          fal fa-search-location
                        </v-icon>
                        <span
                          class="expanded-content__icon-tooltip active-drop right-align"
                        >
                          Active Drop
                        </span>
                      </span>

                      <span
                        class="expanded-content__icon-container add-note"
                        v-if="!isJobSearch"
                        @click="addNoteToJob(jobDetails.jobId)"
                      >
                        <v-icon class="expanded-content__icon">
                          far fa-sticky-note
                        </v-icon>
                        <span
                          class="expanded-content__icon-tooltip add-note right-align"
                        >
                          Add Note
                        </span>
                      </span>
                      <v-menu
                        left
                        v-model="docMenuOpen"
                        :close-on-content-click="false"
                      >
                        <template v-slot:activator="{ on: menu }">
                          <span
                            class="expanded-content__icon-container download"
                            v-on="menu"
                          >
                            <v-icon class="expanded-content__icon"
                              >fas fa-file-download</v-icon
                            >
                            <span
                              class="expanded-content__icon-tooltip download right-align"
                            >
                              Generate Documents
                            </span>
                          </span>
                        </template>

                        <v-list dense class="v-list-custom">
                          <!-- Client Invoice -->
                          <v-menu offset-x :close-on-content-click="false">
                            <template v-slot:activator="{ on: submenu }">
                              <v-list-tile
                                v-on="submenu"
                                :disabled="!(jobDetails.accounting.invoiceId && canDownloadInvoiceAndRCTI)"
                              >
                                <v-list-tile-title
                                  >Client Invoice
                                  <v-icon
                                    size="14"
                                    class="ml-2"
                                    :disabled="!(jobDetails.accounting.invoiceId && canDownloadInvoiceAndRCTI)"
                                    >fa-chevron-right</v-icon
                                  >
                                </v-list-tile-title>
                              </v-list-tile>
                            </template>

                            <v-list dense class="v-list-custom">
                              <v-list-tile
                                @click.stop.prevent="downloadInvoice('DOWNLOAD')"
                                v-if="
                                reportsSettings?.allowedAccessMethods?.includes(
                                  'DOWNLOAD',
                                )
                              "
                              >
                                <v-icon class="pr-2" size="20"
                                  >downloading</v-icon
                                >
                                <v-list-tile-title> Download</v-list-tile-title>
                              </v-list-tile>

                              <v-list-tile
                                @click.stop.prevent="downloadInvoice('EMAIL')"
                                v-if="
                                reportsSettings?.allowedAccessMethods?.includes(
                                  'EMAIL',
                                )
                              "
                              >
                                <v-icon class="pr-2" size="20"
                                  >forward_to_inbox</v-icon
                                >
                                <v-list-tile-title>Email</v-list-tile-title>
                              </v-list-tile>
                            </v-list>
                          </v-menu>

                          <!-- RCTI -->
                          <v-menu offset-x :close-on-content-click="false">
                            <template v-slot:activator="{ on: submenu }">
                              <v-list-tile
                                v-on="submenu"
                                :disabled="!(jobDetails.accounting.rctiId && canDownloadInvoiceAndRCTI)"
                              >
                                <v-list-tile-title
                                  >RCTI
                                  <v-icon
                                    size="14"
                                    class="ml-2"
                                    :disabled="!(jobDetails.accounting.rctiId && canDownloadInvoiceAndRCTI)"
                                    >fa-chevron-right</v-icon
                                  >
                                </v-list-tile-title>
                              </v-list-tile>
                            </template>

                            <v-list dense class="v-list-custom">
                              <v-list-tile
                                @click.stop.prevent="downloadRCTI('DOWNLOAD')"
                                v-if="
                                reportsSettings?.allowedAccessMethods?.includes(
                                  'DOWNLOAD',
                                )
                              "
                              >
                                <v-icon class="pr-2" size="20"
                                  >downloading</v-icon
                                >
                                <v-list-tile-title> Download</v-list-tile-title>
                              </v-list-tile>
                              <v-list-tile
                                @click.stop.prevent="downloadRCTI('EMAIL')"
                                v-if="
                                reportsSettings?.allowedAccessMethods?.includes(
                                  'EMAIL',
                                )
                              "
                              >
                                <v-icon class="pr-2" size="20"
                                  >forward_to_inbox</v-icon
                                >
                                <v-list-tile-title>Email</v-list-tile-title>
                              </v-list-tile>
                            </v-list>
                          </v-menu>

                          <v-divider />

                          <!-- Proof of Delivery with nested menu -->
                          <v-menu offset-x :close-on-content-click="false">
                            <template v-slot:activator="{ on: submenu }">
                              <v-list-tile v-on="submenu" :disabled="!podReady">
                                <v-list-tile-title>
                                  Proof of Delivery
                                  <v-icon size="14" class="ml-2"
                                    >fa-chevron-right</v-icon
                                  >
                                </v-list-tile-title>
                              </v-list-tile>
                            </template>

                            <v-list dense class="v-list-custom">
                              <!-- Download POD -->
                              <v-list-tile
                                v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  'DOWNLOAD',
                )
              "
                                @click.stop.prevent="getProofOfDelivery('DOWNLOAD')"
                                :disabled="!podReady || podLoading"
                              >
                                <v-icon
                                  :disabled="!podReady || podLoading"
                                  class="pr-2"
                                  size="20"
                                  >downloading</v-icon
                                >
                                <v-list-tile-title> Download</v-list-tile-title>
                              </v-list-tile>

                              <!-- Email POD -->
                              <v-list-tile
                                v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  'EMAIL',
                )
              "
                                @click.stop.prevent="getProofOfDelivery('EMAIL')"
                                :disabled="!podReady || podLoading"
                              >
                                <v-icon
                                  :disabled="!podReady || podLoading"
                                  class="pr-2"
                                  size="20"
                                  >forward_to_inbox</v-icon
                                >
                                <v-list-tile-title>Email</v-list-tile-title>
                              </v-list-tile>
                            </v-list>
                          </v-menu>

                          <!-- Consignment Note -->
                          <v-list-tile @click="showConsignmentNoteDialog">
                            <v-list-tile-title
                              >Consignment Note</v-list-tile-title
                            >
                          </v-list-tile>

                          <!-- Job Details Report -->
                          <v-list-tile @click="jobReportDialogActive = true">
                            <v-list-tile-title
                              >Job Details Report</v-list-tile-title
                            >
                          </v-list-tile>
                        </v-list>
                      </v-menu>

                      <span
                        class="expanded-content__icon-container"
                        v-if="!isJobSearch && !isJobDetailsDialog"
                        @click="viewJobDetailsDialog(jobDetails.jobId)"
                      >
                        <v-layout>
                          <v-icon
                            class="expanded-content__icon"
                            style="padding-top: 1px"
                          >
                            far fa-eye
                          </v-icon>
                          <span
                            class="expanded-content__icon-tooltip right-align"
                          >
                            View Job Details
                          </span>
                        </v-layout>
                      </span>
                    </v-layout>
                  </v-flex>
                </v-layout>
              </v-flex>
            </v-layout>
            <v-layout v-if="jobDetails !== null">
              <v-flex
                md2
                :class="isJobDetailsDialog ? 'job-details-card__body-side-dialog' : 'job-details-card__body-side'"
              >
                <v-layout
                  class="job-details-card__keyvalue"
                  v-for="(item, index) in massagedJobDetailsSummary"
                  :key="item.id"
                >
                  <span class="job-details-card__keyvalue--key">
                    {{ item.title }}:
                  </span>

                  <span class="job-details-card__keyvalue--value">
                    <v-tooltip
                      v-if="item.id === 'Equipment'"
                      bottom
                      class="equipment-tooltip"
                    >
                      <template #activator="{ on, attrs }">
                        <span v-bind="attrs" v-on="on">{{ item.value }}</span>
                      </template>
                      <span>{{ fullAdditionalEquipment }}</span>
                    </v-tooltip>

                    <template v-else> {{ item.value }} </template>
                  </span>
                </v-layout>
              </v-flex>
              <v-flex md10 class="job-details-card__body" pt-2>
                <div
                  class="driver-info-wrapper"
                  v-if="recentGpsLocation && jobDetails.workStatus === 160"
                >
                  <div class="driver-coordinates">
                    <span class="key-txt">
                      <v-icon class="pl-2 pr-1" size="10"
                        >fal fa-compass</v-icon
                      >
                    </span>
                    <span class="value-txt">{{ recentGpsLocation }}</span>
                  </div>
                </div>
                <v-layout row wrap>
                  <v-flex
                    md12
                    class="job-details-card__body--puditem"
                    v-for="(pudItem, index) in jobDetails.pudItems"
                    :key="pudItem.pudId"
                  >
                    <job-details-pud-item
                      :jobDetails="jobDetails"
                      :pudItem="pudItem"
                      :activePudId="activePudId"
                    >
                    </job-details-pud-item>
                  </v-flex>
                  <v-flex md12 pt-2>
                    <span class="job-details-card__header"
                      >Job Communication</span
                    >
                  </v-flex>
                  <v-flex class="md12"
                    ><v-divider class="mb-2 mt-1"></v-divider
                  ></v-flex>
                  <v-flex md12>
                    <NotesList
                      :communications="jobDetails.notes"
                      :allowEdit="true"
                      :jobId="jobDetails.jobId"
                      :showVisibilityTypeName="true"
                      @editNote="editNoteInDialog($event, jobDetails.jobId)"
                    >
                    </NotesList>
                  </v-flex>
                  <v-flex md12 pt-2>
                    <span class="job-details-card__header"
                      >Supporting Documents</span
                    >
                  </v-flex>
                  <v-flex md12>
                    <v-divider class="mb-2 mt-1"></v-divider
                  ></v-flex>
                  <v-flex md12>
                    <v-layout>
                      <v-tooltip
                        bottom
                        max-width="220px"
                        close-delay="0"
                        v-for="(img, index) in jobAttachments"
                        :key="img.id"
                        v-if="img.mimeType === 'application/pdf' || img.data"
                        content-class="v-tooltip__small-text"
                      >
                        <template v-slot:activator="{ on }">
                          <div
                            class="image-container__container"
                            @click="requestFullSizeImage(img)"
                            v-on="on"
                          >
                            <div
                              class="image-container"
                              :class="img.documentTypeId === 14 ?'signature-image' : ''"
                              v-if="img.data && img.mimeType !== 'application/pdf'"
                            >
                              <img :src="img.data" />
                            </div>
                            <div
                              class="image-container"
                              v-if="img.mimeType === 'application/pdf'"
                            >
                              <v-icon size="28">fa fa-file-pdf</v-icon>
                            </div>
                          </div>
                        </template>
                        <span
                          ><v-layout row wrap>
                            <v-flex md12>
                              <v-layout>
                                Attachment Type:
                                <v-spacer></v-spacer
                                >{{returnAttachmentTypeName(img.documentTypeId)}}
                              </v-layout></v-flex
                            >
                            <v-flex md12 v-if="img.signatureName">
                              <v-layout>
                                Signed By:
                                <v-spacer></v-spacer>{{img.signatureName ?
                                img.signatureName : '-'}}
                              </v-layout></v-flex
                            >
                            <v-flex md12>
                              <v-layout>
                                Created:
                                <v-spacer></v-spacer>{{img.timestamp ?
                                returnFormattedTime(img.timestamp, `HH:mm
                                DD/MM/YY`) : '-'}}
                              </v-layout></v-flex
                            >
                            <v-flex md12>
                              <v-layout>
                                Longitude: <v-spacer></v-spacer>{{
                                (img.gpsLocation && img.gpsLocation[0]) ?
                                img.gpsLocation[0].toFixed(4) : 'Unknown'}}
                              </v-layout></v-flex
                            >
                            <v-flex md12>
                              <v-layout>
                                Latitude: <v-spacer></v-spacer>{{
                                (img.gpsLocation && img.gpsLocation[1]) ?
                                img.gpsLocation[1].toFixed(4) : 'Unknown'}}
                              </v-layout></v-flex
                            >
                          </v-layout>
                        </span>
                      </v-tooltip>
                    </v-layout>
                    <v-flex
                      class="ma-0"
                      v-if="jobAttachments.length === 0"
                      style="font-style: italic"
                    >
                      <v-layout md12 align-center justify-center pt-2>
                        <span class="ma-0 faded-text"
                          >No documents available</span
                        >
                      </v-layout>
                    </v-flex>
                  </v-flex>

                  <v-flex md12 pt-2>
                    <span class="job-details-card__header"
                      >Pricing Summary</span
                    >
                  </v-flex>
                  <v-flex md12>
                    <v-divider class="mb-2 mt-1"></v-divider
                  ></v-flex>
                  <v-flex
                    md12
                    class="ma-2"
                    v-if="jobDetails.serviceTypeId && jobDetails.rateTypeId && jobDetails.accounting.clientRates.length > 0 && jobDetails.pudItems.length > 1 && jobDetails.accounting.totals.finalTotal.client !== 0"
                  >
                    <JobBookingPricingSummary
                      :serviceTypeId="jobDetails.serviceTypeId"
                      :pudItems="jobDetails.pudItems"
                      :accounting="jobDetails.accounting"
                      :readOnly="true"
                      :showInfoText="false"
                    ></JobBookingPricingSummary>
                    <span class="info-txt" v-if="jobDetails.workStatus <= 180">
                      Pricing is estimated and subject to change.
                      <p>
                        Fleet Pricing is not available until job pricing has
                        been completed.
                      </p>
                    </span>
                  </v-flex>
                  <v-layout
                    v-else-if="!jobDetails.serviceTypeId"
                    md12
                    align-center
                    justify-center
                    pt-2
                  >
                    <span class="ma-0 faded-text">
                      Pricing Summary is unavailable with incomplete job
                      details. Please select a valid service to show pricing
                      information.
                    </span>
                  </v-layout>
                  <v-layout
                    v-else-if="jobDetails.pudItems.length <= 1"
                    md12
                    align-center
                    justify-center
                    pt-2
                  >
                    <span class="ma-0 faded-text">
                      Pricing Summary is unavailable with incomplete job
                      details. <b>Add more legs</b> to the job to show pricing
                      information.
                    </span>
                  </v-layout>
                  <v-layout v-else md12 align-center justify-center pt-2>
                    <span class="ma-0 faded-text">
                      Pricing Summary is unavailable with incomplete job
                      details. To view pricing estimate, please update the job
                      from the Booking Screen.
                    </span>
                  </v-layout>
                </v-layout>
              </v-flex>
            </v-layout>
          </div>
        </v-flex>
      </v-layout>
    </div>
  </div>
  <ConsignmentNoteDialog
    v-if="isViewingConsignmentNoteDialog && jobDetails"
    :jobDetails="jobDetails"
    :isDialogOpen.sync="isViewingConsignmentNoteDialog"
  ></ConsignmentNoteDialog>

  <ContentDialog
    v-if="jobDetails !== null"
    :showDialog.sync="jobReportDialogActive"
    :title="'Job Details Report'"
    :isConfirmUnsaved="false"
    :width="'600px'"
    @cancel="jobReportDialogActive = false"
    :showActions="false"
  >
    <JobDetailsReport
      :isDialog="true"
      :jobId="jobDetails.jobId"
      @generateReport="generateJobDetailsReport"
      :isLoading.sync="jobReportLoading"
    />
  </ContentDialog>
</div>
