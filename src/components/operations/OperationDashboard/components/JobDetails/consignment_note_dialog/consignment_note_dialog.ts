import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { SearchByOption } from '@/interface-models/Generic/SearchByOption';
import { VForm } from '@/interface-models/Generic/VForm/VForm';
import { Validation } from '@/interface-models/Generic/Validation';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import {
  ConsignmentNoteGenerateOptions,
  ConsignmentNoteStop,
  ConsignmentNoteStopCount,
  GenerateConsignmentNoteRequest,
} from '@/interface-models/Reporting/ConsignmentNote';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { useAttachmentStore } from '@/store/modules/AttachmentStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import Mitt from '@/utils/mitt';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component({
  components: {
    ContentDialog,
    InformationTooltip,
  },
})
export default class ConsignmentNoteDialog extends Vue {
  @Prop() public jobDetails: JobDetails;
  @Prop({ default: false }) public isDialogOpen: boolean;
  @Prop({ default: false }) public isClientPortal: boolean;

  public isAwaitingResponse: boolean = false;
  public pudIdSelectionType: ConsignmentNoteGenerateOptions =
    ConsignmentNoteGenerateOptions.FULL_JOB;

  public selectedPudIds: string[] = [];
  public stopsList: ConsignmentNoteStop[] = [];

  public $refs: {
    consignmentNoteDialogForm: VForm;
  };

  public company = useCompanyDetailsStore().companyDetails;

  public runReportType: ReportAccessMethodTypes =
    ReportAccessMethodTypes.DOWNLOAD;

  public get reportsSettings() {
    return this.company?.divisions?.[0]?.customConfig?.reports;
  }

  public triggerReport(accessMethod: ReportAccessMethodTypes) {
    this.runReportType = accessMethod;
    this.sendRequest();
  }

  get selectionTypeController(): ConsignmentNoteGenerateOptions {
    return this.pudIdSelectionType;
  }
  set selectionTypeController(value: ConsignmentNoteGenerateOptions) {
    switch (value) {
      case ConsignmentNoteGenerateOptions.FULL_JOB:
        // Set all to true
        this.stopsList.forEach((s) => {
          s.active = true;
          s.itemCount = !!s.itemCount ? s.itemCount : 1;
        });
        break;
      case ConsignmentNoteGenerateOptions.DELIVERIES:
        // Set all deliveries to true (legTypeFlag === D)
        this.stopsList.forEach((s) => {
          if (s.legTypeFlag === 'D') {
            s.active = true;
            s.itemCount = !!s.itemCount ? s.itemCount : 1;
          } else {
            s.active = false;
            s.itemCount = 0;
          }
        });
        break;
      case ConsignmentNoteGenerateOptions.PICKUPS:
        // Set all pickups to true (legTypeFlag === P)
        this.stopsList.forEach((s) => {
          if (s.legTypeFlag === 'P') {
            s.active = true;
            s.itemCount = !!s.itemCount ? s.itemCount : 1;
          } else {
            s.active = false;
            s.itemCount = 0;
          }
        });
        break;
      case ConsignmentNoteGenerateOptions.SPECIFIC:
        // Set all to false and let them enable the ones they want
        this.stopsList.forEach((s) => {
          s.active = false;
          s.itemCount = 0;
        });
        break;
    }
    this.pudIdSelectionType = value;
  }

  // Used as items in v-select
  public selectionTypeOptions: SearchByOption[] = [
    { id: ConsignmentNoteGenerateOptions.FULL_JOB, longName: 'Entire Job' },
    {
      id: ConsignmentNoteGenerateOptions.DELIVERIES,
      longName: 'Only Deliveries',
    },
    { id: ConsignmentNoteGenerateOptions.PICKUPS, longName: 'Only Pickups' },
    {
      id: ConsignmentNoteGenerateOptions.SPECIFIC,
      longName: 'Select Specific Stops',
    },
  ];
  // When we're doing a custom selection of puds, return mapped values of
  // pudItems containing the pudId and a display name, to be used as items in a
  // v-select
  get pudItemOptions() {
    if (this.pudIdSelectionType !== ConsignmentNoteGenerateOptions.SPECIFIC) {
      return [];
    }
    return this.jobDetails.pudItems.map((p) => {
      const pudPosition: number = this.findPositionForPudId(
        this.jobDetails.pudItems,
        p.pudId,
      );
      const positionStr =
        pudPosition !== -1 ? `${p.legTypeFlag}${pudPosition}` : '-';
      const locationName = !!p.customerDeliveryName
        ? p.customerDeliveryName
        : p.address.suburb;
      return {
        pudId: p.pudId,
        longName: `${positionStr} - ${locationName}`,
      };
    });
  }
  // Controls dialog visibility. Emits to parent to sync value with prop
  get showDialog() {
    return this.isDialogOpen;
  }
  set showDialog(value: boolean) {
    this.$emit('update:isDialogOpen', value);
  }

  // Called when a checkbox is changed. See if we need to update the
  // pudIdSelectionType value based on the updated values
  public updateSelectionTypeOnChange() {
    // const allChecked = this.stopsList.every(s => s.active);
    const totalCount = this.stopsList.length;
    let totalPickups = 0;
    let totalDropoffs = 0;
    let totalChecked = 0;
    let pickupsChecked = 0;
    let dropoffsChecked = 0;

    this.stopsList.forEach((s) => {
      if (s.legTypeFlag === 'P') {
        totalPickups++;
        if (s.active) {
          pickupsChecked++;
          totalChecked++;
        }
      } else {
        totalDropoffs++;
        if (s.active) {
          dropoffsChecked++;
          totalChecked++;
        }
      }
      if (s.active && !s.itemCount) {
        s.itemCount = 1;
      } else if (!s.active) {
        s.itemCount = 0;
      }
    });
    if (totalChecked === totalCount) {
      this.pudIdSelectionType = ConsignmentNoteGenerateOptions.FULL_JOB;
    } else if (pickupsChecked === totalPickups && dropoffsChecked === 0) {
      this.pudIdSelectionType = ConsignmentNoteGenerateOptions.PICKUPS;
    } else if (dropoffsChecked === totalDropoffs && pickupsChecked === 0) {
      this.pudIdSelectionType = ConsignmentNoteGenerateOptions.DELIVERIES;
    } else {
      this.pudIdSelectionType = ConsignmentNoteGenerateOptions.SPECIFIC;
    }
  }

  // Sends the generateConsignmentNote request. Conditionally sets the property
  // for pudIdList based on the pudIdSelectionType value.
  public sendRequest() {
    const includedStops: ConsignmentNoteStopCount[] = this.stopsList
      .filter((s) => s.active && !!s.itemCount)
      .map((s) => {
        return {
          pudId: s.pudId,
          itemCount: s.itemCount,
        };
      });

    if (!includedStops.length) {
      this.$refs.consignmentNoteDialogForm.validate();
      showNotification('Please select at least 1 stop.');
      return;
    }
    if (!this.$refs.consignmentNoteDialogForm.validate()) {
      showNotification(FORM_VALIDATION_FAILED_MESSAGE);
      return;
    }
    // Construct request
    const request: GenerateConsignmentNoteRequest = {
      jobId: this.jobDetails.jobId!,
      includedStops,
      accessType: this.runReportType,
    };
    // Set awaiting variable and dispatch request
    this.isAwaitingResponse = true;
    this.setResponseListener(true);
    useAttachmentStore().generateConsignmentNote(request);
  }

  public getRandomInteger(min: number, max: number): number {
    // Generate a random number between min (inclusive) and max (inclusive)
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  // Validation rules from store to apply as form field rules
  get validate(): Validation {
    return validationRules;
  }

  /**
   * Finds the position of the given PUD ID within the list of PICKUPs or
   * DELIVERYs.
   *
   * @param pudItems    The list of PUDItem objects to search within.
   * @param targetPudId The PUD ID to find.
   * @returns The position (count) of the targetPudId within the list. Returns -1
   * if not found.
   */
  public findPositionForPudId(
    pudItems: PUDItem[],
    targetPudId: string,
  ): number {
    let pickupCount: number = 0;
    let deliveryCount: number = 0;

    for (const item of pudItems) {
      if (item.legTypeFlag === 'P') {
        pickupCount++;
        if (targetPudId === item.pudId) {
          return pickupCount;
        }
      } else if (item.legTypeFlag === 'D') {
        deliveryCount++;
        if (targetPudId === item.pudId) {
          return deliveryCount;
        }
      }
    }
    // If the pudId is not found, you can return -1 or throw an exception
    return -1;
  }

  /**
   * Callback for mitt listener
   * @param response response payload containing report
   */
  public handleReportResponse(): void {
    this.isAwaitingResponse = false;
    this.showDialog = false;
    this.setResponseListener(false);
  }
  /**
   * When we make the request to download a report, we turn on the mitt listener so
   * we can reset the loader when the response comes in
   * @param value boolean value to set the listener
   */
  public setResponseListener(value: boolean) {
    if (value) {
      Mitt.on('encodedReport', this.handleReportResponse);
    } else {
      Mitt.off('encodedReport', this.handleReportResponse);
    }
  }
  /**
   * When the component is destroyed, we remove the mitt listener
   */
  public beforeDestroy() {
    this.setResponseListener(false);
  }

  public setConsignmentNoteStopsList() {
    this.stopsList = this.jobDetails.pudItems.map((pud) => {
      const pudPosition: number = this.findPositionForPudId(
        this.jobDetails.pudItems,
        pud.pudId,
      );
      const positionStr =
        pudPosition !== -1 ? `${pud.legTypeFlag}${pudPosition}` : '-';
      const locationName = !!pud.customerDeliveryName
        ? pud.customerDeliveryName
        : pud.address.suburb;
      return {
        pudId: pud.pudId,
        legTypeFlag: pud.legTypeFlag,
        position: positionStr,
        locationName,
        active: true,
        itemCount: 1,
      };
    });
  }

  // Set store subscription on mount
  public mounted() {
    this.setConsignmentNoteStopsList();
  }
}
