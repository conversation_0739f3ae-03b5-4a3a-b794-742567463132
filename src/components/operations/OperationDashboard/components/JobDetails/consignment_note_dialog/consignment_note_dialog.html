<div class="consignment-note-dialog">
  <ContentDialog
    v-if="showDialog"
    :showDialog.sync="showDialog"
    title="Generate Consignment Note"
    :width="'800px'"
    :confirmBtnText="'Submit'"
    @confirm="sendRequest"
    :isLoading="isAwaitingResponse"
    :isClientPortal="isClientPortal"
    :showActions="false"
    @cancel="showDialog = false"
  >
    <v-form ref="consignmentNoteDialogForm">
      <v-layout row wrap pa-3>
        <v-flex md12>
          <v-layout>
            <v-flex md4>
              <v-layout align-center class="form-field-label-container">
                <h6 class="subheader--faded pr-3 pb-0">Included Stops:</h6>
              </v-layout>
            </v-flex>
            <v-flex md8>
              <v-select
                class="v-solo-custom"
                solo
                flat
                color="light-blue"
                label="Type"
                :items="selectionTypeOptions"
                item-value="id"
                item-text="longName"
                :disabled="isAwaitingResponse"
                v-model="selectionTypeController"
              ></v-select>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12 pb-3
          ><v-layout align-center>
            <h5 class="subheader--bold pr-3 pt-1">Included Stops:</h5>
            <v-flex>
              <v-divider></v-divider>
            </v-flex>
            <span class="pl-2">
              <InformationTooltip :left="true" tooltipType="info">
                <v-layout slot="content" row wrap>
                  <v-flex md12>
                    <p class="mb-1">
                      Select the stops you wish to be printed on the Consignment
                      Note, as well as the number of items at each stop. A page
                      will be printed for each item.
                    </p>
                    <p class="mb-0">
                      <strong>Please note:</strong> this is a point-in-time
                      report. You may need to re-download the Consignment Note
                      if the job is updated with new details.
                    </p>
                  </v-flex>
                </v-layout>
              </InformationTooltip>
            </span>
          </v-layout>
        </v-flex>

        <v-flex md12 py-2 px-2>
          <table class="simple-data-table">
            <thead>
              <tr>
                <th class="text-xs-left">Include</th>
                <th class="text-xs-left">Stop</th>
                <th class="text-xs-left">Location</th>
                <th class="text-xs-right">Items (#)</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="item in stopsList" :key="item.pudId">
                <td class="text-xs-left">
                  <v-checkbox
                    color="light-blue"
                    class="mt-2"
                    v-model="item.active"
                    tabindex="-1"
                    @change="updateSelectionTypeOnChange"
                  />
                </td>
                <td class="text-xs-left">{{item.position}}</td>
                <td class="text-xs-left">
                  {{item.locationName ? item.locationName.toUpperCase() :
                  'Unknown'}}
                </td>
                <td class="text-xs-right pl-4">
                  <v-text-field
                    class="v-solo-custom"
                    solo
                    flat
                    type="number"
                    min="1"
                    :rules="[validate.required, validate.number, validate.nonNegative]"
                    v-model.number="item.itemCount"
                    color="light-blue"
                    label="Items"
                    hide-details
                    :disabled="!item.active"
                    @change="() => {item.active = !item.itemCount ? false : true}"
                    @focus="$event.target.select()"
                  ></v-text-field>
                </td>
              </tr>
            </tbody>
          </table>
        </v-flex>
      </v-layout>
      <v-layout align-center justify-end>
        <v-btn
          v-if="
          reportsSettings?.allowedAccessMethods?.includes(
            'DOWNLOAD',
          )
        "
          color="primary"
          :loading="isAwaitingResponse"
          :disabled="isAwaitingResponse"
          @click="triggerReport('DOWNLOAD')"
        >
          <v-icon class="pr-2" size="20">downloading</v-icon>
          Download Report
        </v-btn>

        <v-btn
          v-if="
          reportsSettings?.allowedAccessMethods?.includes(
            'EMAIL',
          ) && !isClientPortal
        "
          color="orange"
          :loading="isAwaitingResponse"
          :disabled="isAwaitingResponse"
          @click="triggerReport('EMAIL')"
        >
          <v-icon class="pr-2" size="20">forward_to_inbox</v-icon>
          Email Report
        </v-btn>
      </v-layout>
    </v-form>
  </ContentDialog>
</div>
