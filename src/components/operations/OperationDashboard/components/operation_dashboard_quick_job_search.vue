<template>
  <div v-if="visible" class="spotlight-overlay" @click="handleOverlayClick">
    <div class="spotlight-modal" @click.stop>
      <v-text-field
        v-model="searchTerm"
        @input="onSearch"
        @keydown="handleKeydown"
        class="v-solo-custom"
        placeholder="Search jobs by #, client, vehicle, driver..."
        autofocus
        flat
        solo
        clearable
        appendIcon="search"
      />
      <div class="results-scroll-area">
        <div v-if="!searchTerm">
          <div class="search-summary">
            <div class="search-summary-title">
              <span class="material-icons search-summary-icon">search</span>
              Quick Job Search
            </div>
            <div class="search-summary-list">
              <div class="search-summary-item">
                <v-icon>tag</v-icon>
                <span>Job ID</span>
              </div>
              <div class="search-summary-item">
                <v-icon>person</v-icon>
                <span>Client</span>
              </div>
              <div class="search-summary-item">
                <v-icon>directions_car</v-icon>
                <span>Vehicle</span>
              </div>
              <div class="search-summary-item">
                <v-icon>fas fa-steering-wheel</v-icon>
                <span>Driver</span>
              </div>
            </div>
            <div
              class="search-summary-hint"
              style="display: flex; align-items: center; gap: 6px"
            >
              <span
                >Start typing to search for jobs available in the Operations
                Dashboard by any of the above categories.</span
              >
              <v-tooltip bottom>
                <template #activator="{ on, attrs }">
                  <v-icon
                    v-bind="attrs"
                    v-on="on"
                    size="17"
                    style="cursor: pointer; color: var(--info)"
                  >
                    info
                  </v-icon>
                </template>
                <span>Jobs with status 'Booked' through to 'Reviewed'</span>
              </v-tooltip>
            </div>
          </div>
        </div>
        <div v-else-if="searchTerm && noResults" class="no-results">
          No results found.
        </div>
        <div v-else-if="searchTerm && !noResults">
          <div
            v-for="group in resultGroups"
            :key="group.label"
            class="results-group"
            :class="{ expanded: expandedGroups[group.label] }"
          >
            <div class="results-title section-heading sticky-section-header">
              <v-icon
                class="section-heading-icon"
                v-if="group.label === 'Matching Job ID'"
                >tag</v-icon
              >
              <v-icon
                class="section-heading-icon"
                v-else-if="group.label === 'Matching Vehicle'"
                >directions_car</v-icon
              >
              <v-icon
                class="section-heading-icon"
                v-else-if="group.label === 'Matching Driver'"
                >fas fa-steering-wheel</v-icon
              >
              <v-icon
                class="section-heading-icon"
                v-else-if="group.label === 'Matching Client'"
                >person</v-icon
              >
              {{ group.label }}
              <template v-if="group.jobs.length > 3">
                <v-btn
                  small
                  text
                  class="view-toggle-btn"
                  @click.stop="toggleGroup(group.label)"
                >
                  {{ expandedGroups[group.label] ? 'Show Less' : 'View All' }}
                </v-btn>
              </template>
            </div>
            <div
              v-if="expandedGroups[group.label]"
              class="results-group-list scrollable-group-list"
            >
              <template v-if="group.jobs.length">
                <div
                  v-for="job in group.jobs"
                  :key="group.label + '-' + (job.displayId || job.jobId)"
                  class="result-item"
                  :class="{
                    'result-item-selected':
                      selectedIndex >= 0 &&
                      getResultIndex(job) === selectedIndex,
                  }"
                  @click="selectJob(job.jobId)"
                >
                  <div class="job-details-vertical">
                    <div class="job-line">
                      <div class="job-line-left">
                        <span class="job-label">Job:</span>
                        <span
                          class="job-number"
                          v-if="group.label === 'Matching Job ID'"
                        >
                          #
                          <span
                            v-for="(part, idx) in getHighlightedParts(
                              getDisplayId(job).toString(),
                              searchTerm,
                            )"
                            :key="idx"
                          >
                            <mark v-if="part.match" class="highlighted-match">{{
                              part.text
                            }}</mark>
                            <span v-else>{{ part.text }}</span>
                          </span>
                        </span>
                        <span class="job-number" v-else
                          ># {{ getDisplayId(job) }}</span
                        >
                        <span class="job-date">{{
                          returnFormattedDate(job.date)
                        }}</span>
                      </div>
                      <span
                        class="job-status"
                        :class="getStatusClass(job.status)"
                        >{{ job.status }}</span
                      >
                    </div>
                    <div class="client-driver-line">
                      <span class="job-label">Client: </span>
                      <span
                        class="job-client"
                        v-if="group.label === 'Matching Client'"
                      >
                        <span
                          v-for="(part, idx) in getHighlightedParts(
                            `${job.clientId} - ${job.clientName}`,
                            searchTerm,
                          )"
                          :key="idx"
                        >
                          <mark v-if="part.match" class="highlighted-match">{{
                            part.text
                          }}</mark>
                          <span v-else>{{ part.text }}</span>
                        </span>
                      </span>
                      <span class="job-client" v-else>{{
                        job.clientName
                      }}</span>
                    </div>
                    <div
                      class="driver-fleetasset-line"
                      v-if="
                        job.csrAssignedId &&
                        job.csrAssignedId !== '-' &&
                        (job.vehicleRegistrationNumber || job.vehicle)
                      "
                    >
                      <span class="job-label">Vehicle: </span>
                      <span
                        class="job-vehicle"
                        v-if="group.label === 'Matching Vehicle'"
                      >
                        <span
                          v-for="(part, idx) in getHighlightedParts(
                            `${job.csrAssignedId || '-'} - ${
                              job.vehicleRegistrationNumber || job.vehicle
                            }`,
                            searchTerm,
                          )"
                          :key="idx"
                        >
                          <mark v-if="part.match" class="highlighted-match">{{
                            part.text
                          }}</mark>
                          <span v-else>{{ part.text }}</span>
                        </span>
                      </span>
                      <span class="job-vehicle" v-else
                        >{{ job.csrAssignedId || '-' }} -
                        {{ job.vehicleRegistrationNumber || job.vehicle }}</span
                      >
                      <span class="job-label" style="margin-left: 20px"
                        >Driver:
                      </span>
                      <span
                        class="job-driver"
                        v-if="group.label === 'Matching Driver'"
                      >
                        <span
                          v-for="(part, idx) in getHighlightedParts(
                            job.driverName || '',
                            searchTerm,
                          )"
                          :key="idx"
                        >
                          <mark v-if="part.match" class="highlighted-match">{{
                            part.text
                          }}</mark>
                          <span v-else>{{ part.text }}</span>
                        </span>
                      </span>
                      <span class="job-driver" v-else>{{
                        job.driverName
                      }}</span>
                    </div>
                  </div>
                </div>
              </template>
              <div v-else class="no-results">No results</div>
            </div>
            <div v-else class="results-group-list">
              <template v-if="group.jobs.length">
                <div
                  v-for="job in group.jobs.slice(0, 3)"
                  :key="group.label + '-' + (job.displayId || job.jobId)"
                  class="result-item"
                  :class="{
                    'result-item-selected':
                      selectedIndex >= 0 &&
                      getResultIndex(job) === selectedIndex,
                  }"
                  @click="selectJob(job.jobId)"
                >
                  <div class="job-details-vertical">
                    <div class="job-line">
                      <div class="job-line-left">
                        <span
                          class="job-number"
                          v-if="group.label === 'Matching Job ID'"
                        >
                          #
                          <span
                            v-for="(part, idx) in getHighlightedParts(
                              getDisplayId(job).toString(),
                              searchTerm,
                            )"
                            :key="idx"
                          >
                            <mark v-if="part.match" class="highlighted-match">{{
                              part.text
                            }}</mark>
                            <span v-else>{{ part.text }}</span>
                          </span>
                        </span>
                        <span class="job-number" v-else
                          ># {{ getDisplayId(job) }}</span
                        >
                        <span class="job-date">{{
                          returnFormattedDate(job.date)
                        }}</span>
                      </div>
                      <span
                        class="job-status"
                        :class="getStatusClass(job.status)"
                        >{{ job.status }}</span
                      >
                    </div>
                    <div class="client-driver-line">
                      <span class="job-label">Client: </span>
                      <span
                        class="job-client"
                        v-if="group.label === 'Matching Client'"
                      >
                        <span
                          v-for="(part, idx) in getHighlightedParts(
                            `${job.clientId} - ${job.clientName}`,
                            searchTerm,
                          )"
                          :key="idx"
                        >
                          <mark v-if="part.match" class="highlighted-match">{{
                            part.text
                          }}</mark>
                          <span v-else>{{ part.text }}</span>
                        </span>
                      </span>
                      <span class="job-client" v-else>{{
                        job.clientName
                      }}</span>
                    </div>
                    <div
                      class="driver-fleetasset-line"
                      v-if="
                        job.csrAssignedId &&
                        job.csrAssignedId !== '-' &&
                        (job.vehicleRegistrationNumber || job.vehicle)
                      "
                    >
                      <span class="job-label">Vehicle: </span>
                      <span
                        class="job-vehicle"
                        v-if="group.label === 'Matching Vehicle'"
                      >
                        <span
                          v-for="(part, idx) in getHighlightedParts(
                            `${job.csrAssignedId || '-'} - ${
                              job.vehicleRegistrationNumber || job.vehicle
                            }`,
                            searchTerm,
                          )"
                          :key="idx"
                        >
                          <mark v-if="part.match" class="highlighted-match">{{
                            part.text
                          }}</mark>
                          <span v-else>{{ part.text }}</span>
                        </span>
                      </span>
                      <span class="job-vehicle" v-else
                        >{{ job.csrAssignedId || '-' }} -
                        {{ job.vehicleRegistrationNumber || job.vehicle }}</span
                      >
                      <span class="job-label" style="margin-left: 20px"
                        >Driver:
                      </span>
                      <span
                        class="job-driver"
                        v-if="group.label === 'Matching Driver'"
                      >
                        <span
                          v-for="(part, idx) in getHighlightedParts(
                            job.driverName || '',
                            searchTerm,
                          )"
                          :key="idx"
                        >
                          <mark v-if="part.match" class="highlighted-match">{{
                            part.text
                          }}</mark>
                          <span v-else>{{ part.text }}</span>
                        </span>
                      </span>
                      <span class="job-driver" v-else>{{
                        job.driverName
                      }}</span>
                    </div>
                  </div>
                </div>
              </template>
              <div v-else class="no-results">No results</div>
            </div>
          </div>
        </div>
      </div>
      <div class="button-row">
        <v-btn
          outline
          small
          class="advance-search-btn"
          @click="goToAdvanceSearch"
          >Go to Advanced Search</v-btn
        >
        <v-btn outline small class="close-btn" @click="close">Close</v-btn>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { OperationJobSummary } from '@/interface-models/Jobs/OperationJobSummary';
import {
  defineEmits,
  defineProps,
  onBeforeUnmount,
  onMounted,
  Ref,
  ref,
  watch,
} from 'vue';
import { useRouter } from 'vue-router/composables';

interface ResultGroup {
  label: string;
  jobs: OperationJobSummary[];
}

/**
 * Represents a job result with an additional group label for display context.
 */
interface VisibleResult extends OperationJobSummary {
  groupLabel: string;
}

const props = defineProps<{
  visible: boolean;
  jobs: OperationJobSummary[];
}>();

const router = useRouter();

const searchTerm: Ref<string> = ref('');
const noResults: Ref<boolean> = ref(false);
const selectedIndex: Ref<number> = ref(-1);

const resultGroups: Ref<ResultGroup[]> = ref([]);
const expandedGroups: Ref<Record<string, boolean>> = ref({});

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'selectedJobId', jobId: number): void;
}>();

function getDisplayId(job: OperationJobSummary) {
  return job.displayId || job.jobId || '-';
}

/**
 * Splits a string into parts, highlighting matches with the search term.
 * @param text The text to highlight
 * @param term The search term
 * @returns Array of parts with match flag
 */
function getHighlightedParts(text: string, term: string) {
  if (!term) {
    return [{ text, match: false }];
  }
  const regex = new RegExp(
    `(${term.replace(/[.*+?^${}()|[\\]\\]/g, '\\$&')})`,
    'ig',
  );
  const parts = [] as { text: string; match: boolean }[];
  let lastIndex = 0;
  let match: RegExpExecArray | null;
  while ((match = regex.exec(text)) !== null) {
    if (match.index > lastIndex) {
      parts.push({ text: text.slice(lastIndex, match.index), match: false });
    }
    parts.push({ text: match[0], match: true });
    lastIndex = regex.lastIndex;
  }
  if (lastIndex < text.length) {
    parts.push({ text: text.slice(lastIndex), match: false });
  }
  return parts;
}

/**
 * Returns a CSS class for the job status badge.
 */
function getStatusClass(status: string) {
  const s = (status || '').toLowerCase();
  if (
    ['completed', 'done', 'finished', 'delivered'].some((k) => s.includes(k))
  ) {
    return 'status-completed';
  }
  if (
    ['in progress', 'active', 'ongoing', 'processing'].some((k) =>
      s.includes(k),
    )
  ) {
    return 'status-inprogress';
  }
  if (['cancel', 'fail', 'rejected', 'error'].some((k) => s.includes(k))) {
    return 'status-cancelled';
  }
  return 'status-other';
}

/**
 * Returns the visible results for keyboard navigation (first 3 of each group).
 * @returns Array of visible job results with group label
 */
function getVisibleResults(): VisibleResult[] {
  const visibleResults: VisibleResult[] = [];
  resultGroups.value.forEach((group) => {
    // Always show only first 3 results, regardless of expanded state
    const jobsToShow = group.jobs.slice(0, 3);
    jobsToShow.forEach((job) => {
      visibleResults.push(
        Object.assign({}, job, { groupLabel: group.label }) as VisibleResult,
      );
    });
  });
  return visibleResults;
}

/**
 * Handles keyboard navigation (up/down/enter) in the search results.
 */
function handleKeydown(event: KeyboardEvent) {
  const visibleResults = getVisibleResults();
  if (!visibleResults.length) {
    return;
  }

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault();
      selectedIndex.value = Math.min(
        selectedIndex.value + 1,
        visibleResults.length - 1,
      );
      break;
    case 'ArrowUp':
      event.preventDefault();
      selectedIndex.value = Math.max(selectedIndex.value - 1, -1);
      break;
    case 'Enter':
      event.preventDefault();
      if (
        selectedIndex.value >= 0 &&
        selectedIndex.value < visibleResults.length
      ) {
        const selectedJob = visibleResults[selectedIndex.value];
        selectJob(selectedJob.jobId);
      }
      break;
  }
}

/**
 * Filters jobs into result groups based on the search term.
 * Updates resultGroups, noResults, and resets selection/expansion.
 */
function onSearch() {
  const term = searchTerm.value.trim().toLowerCase();
  if (!term) {
    resultGroups.value = [];
    noResults.value = false;
    resetExpandedGroups();
    selectedIndex.value = -1;
    return;
  }

  // Match lists for each group
  const jobIdMatches: OperationJobSummary[] = [];
  const vehicleMatches: OperationJobSummary[] = [];
  const driverMatches: OperationJobSummary[] = [];
  const clientMatches: OperationJobSummary[] = [];

  props.jobs.forEach((job) => {
    const displayId = getDisplayId(job).toString().toLowerCase();
    const clientId =
      job.clientId !== undefined && job.clientId !== null
        ? job.clientId.toString()
        : '';
    const clientName = job.clientName || '';
    const clientDisplay = `${clientId} - ${clientName}`.trim();
    const clientDisplayLower = clientDisplay.toLowerCase();

    // Fleet asset display and search (CSR Assigned ID - REGO)
    const csrAssignedId = job.csrAssignedId || '-';
    const rego = job.vehicleRegistrationNumber || job.vehicle || '';
    const fleetAssetDisplay = `${csrAssignedId} - ${rego}`.trim();
    const fleetAssetDisplayLower = fleetAssetDisplay.toLowerCase();

    // Driver display and search (OperationJobSummary.driverName)
    const driverName = job.driverName || '';
    const driverDisplay = driverName;
    const driverDisplayLower = driverDisplay.toLowerCase();

    if (displayId?.includes(term)) {
      jobIdMatches.push(job);
    }
    if (fleetAssetDisplayLower.includes(term)) {
      vehicleMatches.push(job);
    }
    if (driverDisplayLower.includes(term)) {
      driverMatches.push(job);
    }
    if (clientDisplayLower.includes(term)) {
      clientMatches.push(job);
    }
  });

  // Always show all groups, even if empty
  const groups: { label: string; jobs: OperationJobSummary[] }[] = [
    { label: 'Matching Job ID', jobs: jobIdMatches },
    { label: 'Matching Vehicle', jobs: vehicleMatches },
    { label: 'Matching Driver', jobs: driverMatches },
    { label: 'Matching Client', jobs: clientMatches },
  ];

  // Sort groups by number of jobs descending, but keep all groups
  groups.sort((a, b) => b.jobs.length - a.jobs.length);

  resultGroups.value = groups;
  // noResults is true only if all groups are empty
  noResults.value = groups.every((g) => g.jobs.length === 0);
  resetExpandedGroups();
  selectedIndex.value = -1;
}

/**
 * Emits the selected job ID and closes the search.
 */
function selectJob(jobId: number | undefined) {
  if (jobId) {
    emit('selectedJobId', jobId);
    close();
  }
}

// Resets the search state and closes the modal.
function close() {
  searchTerm.value = '';
  resultGroups.value = [];
  noResults.value = false;
  resetExpandedGroups();
  selectedIndex.value = -1;
  emit('close');
}

function goToAdvanceSearch() {
  close();
  router.push('/job-search/');
}

/**
 * Handles overlay click to close the search if the background is clicked.
 */
function handleOverlayClick(event: Event) {
  if (event.target === event.currentTarget) {
    close();
  }
}
/**
 * Returns the index of a job in the visible results (for selection highlighting).
 */
function getResultIndex(job: OperationJobSummary) {
  const visibleResults = getVisibleResults();
  return visibleResults.findIndex((result) => result.jobId === job.jobId);
}

//Toggles the expanded/collapsed state of a result group.
function toggleGroup(label: string) {
  expandedGroups.value = {
    ...expandedGroups.value,
    [label]: !expandedGroups.value[label],
  };
}
function resetExpandedGroups() {
  expandedGroups.value = {};
}

// Add global ESC key handler (commented out for now)
function handleGlobalKeydown(event: KeyboardEvent) {
  if (event.key === 'Escape' && props.visible) {
    close();
  }
}

// Watch for modal visibility changes to reset state
watch(
  () => props.visible,
  (val) => {
    if (!val) {
      searchTerm.value = '';
      resultGroups.value = [];
      noResults.value = false;
      resetExpandedGroups();
      selectedIndex.value = -1;
    }
  },
);

onMounted(() => {
  document.addEventListener('keydown', handleGlobalKeydown);
});

onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleGlobalKeydown);
});
</script>

<style scoped lang="scss">
.spotlight-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(10, 10, 20, 0.45);
  z-index: 2000;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 5vh;
  backdrop-filter: blur(6px);
}
.spotlight-modal {
  background: rgba(26, 26, 27, 0.4);
  border-radius: 16px;
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.85);
  width: 850px;
  max-width: 90vw;
  padding: 32px 24px 16px 24px;
  display: flex;
  flex-direction: column;
  /* Fixed height and scrollable results */
  height: fit-content;
  max-height: 90vh;
  overflow: hidden;
  backdrop-filter: blur(18px);

  border: 1px solid var(--border-color);
}

.results-scroll-area {
  flex: 1 1 auto;
  overflow-y: auto;
  min-height: 0;
  max-height: 100%;
  margin-bottom: 12px;
  padding-right: 2px;
  scroll-behavior: smooth;
  &::-webkit-scrollbar {
    width: 6px;
  }
  .results-group {
    margin-bottom: 18px;
    max-height: none;
    overflow: visible;
    padding: 4px;
    margin-right: 8px;
    border-radius: 4px;
    background: rgba(21, 21, 21, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
    &.expanded {
      background: rgba(21, 21, 11, 0.4);
      border-radius: 4px;
      transition: background 0.2s;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
      padding-bottom: 8px;
    }
    .sticky-section-header {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding-right: 0.5em;
      background: rgba(20, 20, 20, 0.8);
      border-bottom: 1px solid rgba(255, 255, 255, 0.12);
      padding-bottom: 4px;
      margin-bottom: 10px;
      position: sticky;
      top: 0;
      z-index: 10;
      backdrop-filter: blur(8px);
      border-radius: 4px 4px 0 0;
      padding: 8px 12px 4px 12px;
      margin: -4px -4px 10px -4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      transition: box-shadow 0.2s ease;
    }
    .view-toggle-btn {
      margin-left: auto;
      font-size: inherit;
      font-weight: inherit;
      color: #12ff85;
      background: none !important;
      border: none !important;
      box-shadow: none !important;
      text-transform: none;
      padding: 0 8px;
      min-width: unset;
      height: unset;
      line-height: inherit;
      transition: text-decoration 0.2s;
    }
    .view-toggle-btn:hover {
      text-decoration: underline;
    }
    .scrollable-group-list {
      max-height: 300px;
      overflow-y: auto;
    }
  }
}
.result-item {
  padding: 4px 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.07);
  cursor: pointer;
  transition: background 0.15s;
  border-radius: 4px;
  &:hover {
    background: rgba(80, 90, 120, 0.22);
  }
}

.job-number {
  color: var(--accent);
  font-size: 1.2rem;
  font-weight: 700;
}
.job-date {
  color: var(--bg-light);
  opacity: 0.9;
}
.job-client,
.job-vehicle,
.job-driver {
  color: var(--text-color);
  font-weight: 700;
}
.button-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 12px;
  margin-top: 18px;
  .close-btn {
    color: var(--error);
    padding: 14px;
    border-radius: 12px;
    font-weight: 700;
  }
  .advance-search-btn {
    color: var(--primary-light);
    font-weight: 700;
    padding: 14px;
    border-radius: 12px;
  }
}
.no-results {
  color: #ffb4b4;
  margin-bottom: 12px;
  text-align: center;
}
.marked,
.highlighted-match {
  background: var(--primary-light);
  color: #222;
  border-radius: 3px;
  padding: 0 2px;
}
.section-heading {
  font-size: 1.2rem;
  font-weight: 700;
  color: #12ff85;
  margin-bottom: 12px;
  margin-left: 6px;
  letter-spacing: 0.03em;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;

  .section-heading-icon {
    color: #12ff85;
    font-size: 1.2rem;
  }
}
.job-details-vertical {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 8px;
}
.job-line {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  font-size: 1.05rem;
  color: var(--text-color);
  font-weight: 500;
  margin-bottom: 2px;
}
.job-line-left {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
}
.job-status {
  font-weight: 700;
  border-radius: 4px;
  padding: 1px 4px;
  font-size: 12px;
}
.status-completed {
  background: #1de9b6;
  color: #003c2f;
}
.status-inprogress {
  background: #ffe066;
  color: #7a5d00;
}
.status-cancelled {
  background: #ff5252;
  color: #fff;
}
.status-other {
  background: var(--light-text-color);
  color: #222;
}
.job-label {
  color: var(--light-text-color);
  font-size: 0.98rem;
  font-weight: 500;
}

// NO SEARCH
.search-summary {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 0 24px 0;
  color: var(--light-text-color);
  text-align: center;
}
.search-summary-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 18px;
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--text-color);
}
.search-summary-icon {
  font-size: 2.2rem;
  color: #12ff85;
}
.search-summary-list {
  display: flex;
  flex-direction: row;
  gap: 28px;
  margin-bottom: 18px;
}
.search-summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  font-size: 16px;
  .v-icon {
    font-size: 2rem;
    color: var(--accent);
  }
}
.search-summary-hint {
  font-size: 14px;
  color: var(--light-text-color);
  margin-top: 8px;
}
.view-all-row {
  text-align: right;
  margin-top: 4px;
}
.driver-fleetasset-line {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 2px;
}
.result-item-selected {
  background: rgba(80, 90, 120, 0.4) !important;
  border-left: 3px solid var(--accent);
}
</style>
