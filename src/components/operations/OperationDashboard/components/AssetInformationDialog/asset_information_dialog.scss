.asset-information-dialog {
  padding: 0;
  position: relative;
}

.dialog-content {
  $body-height: 85vh;
  height: $body-height;
  position: relative;
  overflow: hidden;
  .side-column {
    height: 100%;

    .side-column__label {
      font-size: $font-size-12;
      text-transform: uppercase;
      font-weight: 600;
      color: $light-text-color !important;
      padding-right: 12px;
      padding-top: 3px;
      letter-spacing: 0.02em;
    }

    .side-column__value {
      font-size: $font-size-12;
      font-weight: 400;
      padding-top: 3px;
    }

    .side-column__button {
      font-size: $font-size-12;
      text-transform: uppercase;
      font-weight: 600;
      color: var(--light-text-color);
      padding-right: 12px;

      letter-spacing: 0.02em;
      text-align: center;
      padding: 10px 0px;

      &.active-state {
        background-color: var(--primary);
        color: white;
        .button-label {
          color: white;
        }
      }

      &:hover {
        filter: brightness(110%);
        color: white;
        cursor: pointer;
      }
    }
  }

  .dialog-content__scrollable {
    height: 100%;
    max-height: 100%;
    overflow-y: scroll;
  }

  .puditem__container {
    padding: 14px;
    margin: 3px 0px;

    .puditem__suburb {
      font-size: $font-size-15;
      text-transform: uppercase;
      font-weight: 500;
      color: white;
    }

    .puditem__count {
      padding-left: 8px;
      font-size: $font-size-15;
      text-transform: uppercase;
      font-weight: 400;
      color: var(--light-text-color);
    }
  }
}
