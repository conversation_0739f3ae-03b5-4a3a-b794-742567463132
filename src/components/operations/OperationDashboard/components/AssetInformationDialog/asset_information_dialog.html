<div class="asset-information-dialog">
  <v-dialog
    v-model="showDialog"
    width="75%"
    persistent
    content-class="v-dialog-custom"
  >
    <v-layout
      justify-space-between
      class="task-bar app-theme__center-content--header no-highlight"
    >
      <span>Driver and Asset Information</span>
      <span v-if="fleetAssetDriverLabel" class="pl-2 accent-text--primary"
        >{{ fleetAssetDriverLabel }}</span
      >
      <v-spacer></v-spacer>
      <div class="app-theme__center-content--closebutton" @click="closeDialog">
        <v-icon class="app-theme__center-content--closebutton--icon"
          >fal fa-times</v-icon
        >
      </div>
    </v-layout>
    <v-layout class="app-theme__center-content--body dialog-content">
      <v-flex
        md3
        class="side-column app-bgcolor--400 app-bordercolor--600 app-borderside--a"
      >
        <v-layout row wrap>
          <v-flex
            md12
            class="app-bgcolor--300 app-bordercolor--600 app-borderside--b side-column__button"
            v-for="(menuItem, index) in menuOptions"
            v-if="menuItem.visible"
            :key="menuItem.id"
            :class="selectedTabId === menuItem.id ? 'active-state' : ''"
            @click="selectedTabId = menuItem.id"
          >
            <v-layout align-center>
              <span class="button-label"> {{ menuItem.title }} </span>
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md9 class="dialog-content__scrollable app-bgcolor--200">
        <AssetInformation
          class="py-2 px-4"
          v-if="selectedTabId === 'ASSET_DETAILS' || selectedTabId === 'ALLOCATED_WORK'"
          :fleetAssetId="fleetAssetId"
          :driverId="selectedDriverId"
          :key="fleetAssetId"
          :isDialog="true"
          :dialogTabIndex="selectedTabId"
        />
        <GpsPositionHistorySearch
          v-if="selectedTabId === 'GPS_HISTORY'"
          :fleetAssetId="fleetAssetId"
          :driverId="selectedDriverId"
        ></GpsPositionHistorySearch>
        <DriverConversation
          v-if="selectedTabId === 'DRIVER_CONVERSATION'"
          :driverId="selectedDriverId"
          @jumpToHistoryComponent="selectedTabId = 'MESSAGE_HISTORY'"
        ></DriverConversation>
        <DriverChatHistory
          v-if="selectedTabId === 'MESSAGE_HISTORY'"
          :singleDriverType="true"
          :driverId="selectedDriverId"
        ></DriverChatHistory>
        <DriverWorkDiaryHistory
          v-if="selectedTabId === 'WORK_DIARY'"
          :singleDriverType="true"
          :driverId="selectedDriverId"
        ></DriverWorkDiaryHistory>
        <DriverAppNotificationHistory
          v-if="selectedTabId === 'APP_STATUS_HISTORY'"
          :driverId="selectedDriverId"
        ></DriverAppNotificationHistory>
        <DriverDeviceSnapshotHistory
          v-if="selectedTabId === 'SNAPSHOT_HISTORY'"
          :driverId="selectedDriverId"
        ></DriverDeviceSnapshotHistory>
      </v-flex>
    </v-layout>
  </v-dialog>
</div>
