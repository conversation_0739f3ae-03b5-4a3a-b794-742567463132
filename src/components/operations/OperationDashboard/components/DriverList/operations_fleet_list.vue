<template>
  <div class="driver-list-component">
    <v-layout align-center class="task-bar app-theme__center-content--header">
      <!-- clear filters tooltip -->
      <v-tooltip bottom v-if="filterStore.isOperationsChannelFilterApplied">
        <template #activator="{ on }">
          <v-btn
            v-on="on"
            @click="filterStore.clearDriverFilters"
            class="filter-btn clear"
            fab
            outline
          >
            <v-icon size="16">clear</v-icon>
          </v-btn>
        </template>
        <span>Clear Fleet Asset Filters</span>
      </v-tooltip>

      <v-tabs
        height="26"
        :active-class="'tab-active'"
        hide-slider
        class="tab-select-container"
        v-model="selectedListTypeTabIndex"
      >
        <v-tab
          class="ma-0 tab-selector"
          :value="SubcontractorEntityType.FLEET_ASSET"
          v-show="$vuetify.breakpoint.lgAndUp || !searchInputIsFocused"
          ><p class="ma-0 tab-text">Vehicles</p></v-tab
        >
        <v-tab
          class="ma-0 pl-2 tab-selector"
          :value="SubcontractorEntityType.DRIVER"
          v-show="$vuetify.breakpoint.lgAndUp || !searchInputIsFocused"
          ><p class="ma-0 tab-text">Drivers</p></v-tab
        >
      </v-tabs>

      <div
        class="pl-1"
        v-show="$vuetify.breakpoint.lgAndUp || !searchInputIsFocused"
      >
        <v-tooltip bottom>
          <template v-slot:activator="{ on }">
            <v-icon size="14" v-on="on" color="#64c3ff"
              >far fa-question-circle</v-icon
            >
          </template>
          <div class="driver-list__legend">
            <v-layout justify-center
              ><strong style="font-size: 13px; text-transform: uppercase"
                >Legend</strong
              ></v-layout
            >
            <v-divider class="pb-1"></v-divider>
            <v-layout align-center class="pb-1">
              <v-icon size="10" color="#0dffa2"> fas fa-location </v-icon>
              <span class="driver-list__legend--label pl-2"
                >Transmitting GPS Location</span
              >
            </v-layout>
            <v-layout align-center class="pb-1">
              <v-icon size="10" color="#23f323"> fas fa-circle </v-icon>
              <span class="driver-list__legend--label pl-2"
                >Online (Logged In)</span
              >
            </v-layout>
            <v-layout align-center class="pb-1">
              <v-icon size="10" color="grey"> fas fa-circle </v-icon>
              <span class="driver-list__legend--label pl-2">Offline</span>
            </v-layout>
            <v-layout align-center class="pb-1">
              <v-icon size="12" color="accent">fas fa-steering-wheel</v-icon>
              <span class="driver-list__legend--label pl-2"
                >Default Driver</span
              >
            </v-layout>
            <v-layout py-1>
              <v-divider></v-divider>
            </v-layout>
            <v-layout align-center class="pb-1">
              <div
                class="indicator-icon-container fleet-availability-full"
              ></div>
              <span class="driver-list__legend--label pl-2"
                >Available (No Allocated Work)</span
              >
            </v-layout>
            <v-layout align-center class="pb-1">
              <div
                class="indicator-icon-container fleet-availability-partial"
              ></div>
              <span class="driver-list__legend--label pl-2"
                >Allocated Work</span
              >
            </v-layout>
            <v-layout align-center class="pb-1">
              <div
                class="indicator-icon-container fleet-availability-none"
              ></div>
              <span class="driver-list__legend--label pl-2">
                Currently On Job
              </span>
            </v-layout>
          </div>
        </v-tooltip>
      </div>
      <!-- 
      Leaving in for future debugging
      <span class="hidden-fleet-txt">
        Valid Fleet: {{ validFleetAssetCount }}</span
      >
      <span class="hidden-fleet-txt">
        Valid Drivers: {{ validDriverCount }}</span
      > -->
      <span class="hidden-fleet-txt" v-if="getHiddenListItemCount() > 0"
        >( {{ getHiddenListItemCount() }} Drivers Hidden )</span
      >
      <v-spacer />
      <!-- search input -->
      <div @click="focusSearchInput">
        <v-icon size="14" class="search-icon"> fas fa-search </v-icon>
        <input
          type="search"
          id="driver-list__search-input"
          class="driver-list__search-input ml-1 pr-1"
          v-show="$vuetify.breakpoint.lgAndUp || searchInputIsFocused"
          @keypress="handleEnterEvent"
          @focus="searchInputIsFocused = true"
          @blur="searchInputIsFocused = false"
          v-model="filterSearchStringController"
        />
      </div>

      <!-- Expand/Collapse Tooltip -->
      <v-tooltip bottom>
        <template #activator="{ on }">
          <v-btn
            class="filter-btn expandCollapse"
            @click="togglePanel"
            fab
            plain
            flat
            v-on="on"
          >
            <v-icon size="18">expand</v-icon>
          </v-btn>
        </template>
        <span>Expand / Collapse</span>
      </v-tooltip>
      <!-- ReOrder Tooltip -->
      <v-tooltip bottom>
        <template #activator="{ on }">
          <v-btn
            v-on="on"
            class="filter-btn reorder"
            @click="reorderDialogController = true"
            plain
            flat
            fab
          >
            <v-icon size="15">fal fa-sort</v-icon>
          </v-btn>
        </template>
        <span>Re-order list</span>
      </v-tooltip>
      <!-- Table Settings -->
      <OperationsSettingsDialog
        key="fleet-asset-list-settings-dialog"
        buttonText=""
        title="Fleet List - Table Settings"
        @confirm="tableSettingsUpdated"
        :buttonDisabled="false"
        faIconName="fal fa-sliders-h"
        :settingsList="fleetAssetListSettings"
        :isIcon="true"
        class="pr-3"
        filterTitle="Driver"
        :defaultSettingsList="fleetAssetListDefaultSetting"
      >
      </OperationsSettingsDialog>
    </v-layout>
    <div class="asset-list-container">
      <div class="content-scrollable app-bgcolor--400">
        <v-expansion-panel v-model="panel" expand :key="selectedListType">
          <v-expansion-panel-content
            v-for="(group, index) of filteredListItemGroups"
            :key="group.name || index"
            :id="group.name"
            :class="index === filteredListItemGroups.length - 1 ? 'mb-1' : ''"
            class="expansion-panel"
          >
            <template v-slot:actions>
              <div align-center class="expand-icon">
                <v-icon size="15">keyboard_arrow_down</v-icon>
              </div>
            </template>
            <template v-slot:header>
              <v-flex
                md12
                class="service-type__header-container app-bgcolor--400"
              >
                <span class="subHeader" :class="group.groupType">{{
                  group.groupType
                }}</span>
                <p class="ma-0 service-type__header">{{ group.name }}</p>
                <p class="ma-0 service-type__count">
                  {{ group.assets.length }}
                </p>
              </v-flex>
            </template>
            <FleetAssetListItem
              v-for="asset of group.assets"
              draggable="true"
              :key="asset.entityId"
              :id="asset.entityId"
              :serviceTypeId="group.serviceTypeId"
              :serviceTypeName="group.groupType"
              :asset="asset"
              :gridWidth="gridWidth"
              :selected="
                selectedListType === SubcontractorEntityType.FLEET_ASSET
                  ? asset.entityId === selectedFleetAsset
                  : asset.entityId === selectedDriverId
              "
              :fleetAssetTrackingWindowOpen="fleetAssetTrackingWindowOpen"
              :isInternalActive="isInternalActive"
              @selectListItem="selectListItem"
              @viewAssetInformation="viewAssetInformation"
              @openChatMessage="openChatMessage"
              @setFleetAssetActiveDriver="setFleetAssetActiveDriver"
            >
            </FleetAssetListItem>
          </v-expansion-panel-content>
        </v-expansion-panel>
      </div>
    </div>
    <DriverListOrderDialog
      :isOpen.sync="reorderDialogController"
      :listOrder="categoryListOrder"
      :listOrderCounts="listOrderCounts"
      @updatedOrder="updatedListOrder"
      @restoreDefaults="restoreDefaultListOrder"
    ></DriverListOrderDialog>
  </div>
</template>

<script setup lang="ts">
import OperationsSettingsDialog from '@/components/common/ui-elements/operations_settings_dialog.vue';
import DriverListOrderDialog from '@/components/operations/OperationDashboard/components/DriverList/driver_list_order_dialog.vue';
import FleetAssetListItem from '@/components/operations/OperationDashboard/components/DriverList/fleet_asset_list_item.vue';
import {
  getSelectedFleetListViewFromLocalStorage,
  LOCAL_STORAGE_DRIVER_LIST_ORDER,
  setSelectedFleetListViewToLocalStorage,
} from '@/helpers/AppLayoutHelpers/LocalStorageHelpers';
import {
  returnEndOfDayFromEpoch,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { deepCopy } from '@/helpers/JsonHelpers/JsonHelpers';
import {
  DRIVER_LIST_ORDER_CLIENT,
  DRIVER_LIST_ORDER_EQUIPMENT_HIRE,
  DRIVER_LIST_ORDER_INTERNAL,
  DRIVER_LIST_ORDER_NATIONAL_CLIENT,
  DRIVER_LIST_ORDER_OUTSIDE_HIRE,
  DRIVER_LIST_ORDER_SUBCONTRACTOR,
  getDefaultDriverListOrder,
  returnServiceTypeObjectForStaticType,
  staticServiceTypes,
} from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { JobAllocationOrder } from '@/interface-models/Company/DivisionCustomConfig/Operations/JobAllocationOrder';
import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import DriverOperationsListSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverOperationsListSummary';
import type FleetAssetDriverPair from '@/interface-models/FleetAsset/FleetAssetDriverPair';
import type FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import FleetOperationsListSummary from '@/interface-models/FleetAsset/Summary/FleetOperationsListSummary';
import type FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import type OperationsDashboardSetting from '@/interface-models/Generic/OperationScreenOptions/OperationsDashboardSetting';
import { fleetAssetListDefaultSettings } from '@/interface-models/Generic/OperationScreenOptions/OperationsDashboardSetting';
import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import { SubcontractorEntityType } from '@/interface-models/InvoiceAdjustment/EntityTypes/SubcontractorEntityType';
import { OperationJobSummary } from '@/interface-models/Jobs/OperationJobSummary';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { OperationsChannel } from '@/interface-models/OperationsChannels/OperationsChannel';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useDriverAppStore } from '@/store/modules/DriverAppStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useDriverMessageStore } from '@/store/modules/DriverMessageStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useGpsStore } from '@/store/modules/GpsStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useFilterStore } from '@/store/modules/OperationsFilterStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { useRootStore } from '@/store/modules/RootStore';
import Mitt from '@/utils/mitt';
import {
  computed,
  nextTick,
  onBeforeMount,
  onMounted,
  ref,
  watch,
  type ComputedRef,
  type Ref,
  type WritableComputedRef,
} from 'vue';
import {
  applyInclusionsAndExclusions,
  buildFinalGroups,
  FleetAssetList,
  FleetListKeyData,
  getAffiliationGroups,
  getClientFilteredFleetAssets,
  getClientPreferredGroups,
  getNationalClientGroups,
  getServiceTypeGroups,
  GroupType,
  isSettingActive,
} from './Helpers/FleetListHelpers';

// --- Props ---
const props = defineProps<{
  currentIndex: number;
  fleetAssetTrackingWindowOpen?: boolean;
}>();

// --- Stores ---
const fleetAssetStore = useFleetAssetStore();
const filterStore = useFilterStore();
const companyDetailsStore = useCompanyDetailsStore();
const rootStore = useRootStore();
const operationsStore = useOperationsStore();
const fleetAssetOwnerStore = useFleetAssetOwnerStore();
const jobStore = useJobStore();
const clientDetailsStore = useClientDetailsStore();
const driverDetailsStore = useDriverDetailsStore();

const isDebug = false; // Set to true to enable timing logs

// --- State ---
const panel: Ref<boolean[]> = ref([]);
const fleetAssetListDefaultSetting: Ref<OperationsDashboardSetting[]> = ref(
  fleetAssetListDefaultSettings,
);
const filterSearchString: Ref<string> = ref('');
const filterSearchTimeout: Ref<ReturnType<typeof setTimeout> | null> =
  ref(null);
const equipmentHireOwners: Ref<FleetAssetOwnerSummary[]> = ref([]);
const activeFleetAssets: Ref<FleetOperationsListSummary[]> = ref([]);
const driverSummaryList: Ref<DriverOperationsListSummary[]> = ref([]);

const validFleetAssetCount: Ref<number> = ref(0);
const validDriverCount: Ref<number> = ref(0);

const updatingSelectedIdLocally: Ref<boolean> = ref(false);
const showReorderDialog: Ref<boolean> = ref(false);
const categoryListOrder: Ref<number[]> = ref([]);
const listOrderCounts: Ref<Map<number, number>> = ref(
  new Map<number, number>(),
);
const searchInputIsFocused: Ref<boolean> = ref(false);
const nationalClientNames: Ref<Map<string, string>> = ref(new Map());

const _selectedListType: Ref<
  SubcontractorEntityType.FLEET_ASSET | SubcontractorEntityType.DRIVER
> = ref(
  getSelectedFleetListViewFromLocalStorage(
    useCompanyDetailsStore().divisionCustomConfig?.operations
      ?.jobAllocationOrder ?? JobAllocationOrder.VEHICLE_FIRST,
  ),
);

// --- Computed ---
const isInternalActive: ComputedRef<boolean> = computed(() => {
  return (
    isSettingActive('showInternalDrivers', fleetAssetListSettings.value) ||
    isSettingActive(
      'showNationalClientPreferredDrivers',
      fleetAssetListSettings.value,
    ) ||
    isSettingActive('showClientPreferredDrivers', fleetAssetListSettings.value)
  );
});

const updateCounter: ComputedRef<number> = computed(
  () => rootStore.driverListUpdateTrigger,
);

const fleetAssetListSettings: ComputedRef<OperationsDashboardSetting[]> =
  computed(() => {
    return deepCopy(operationsStore.dashboardSettings.fleetAssetList);
  });

/**
 * Used as the v-model for the v-tabs which use the index of the tabs as their
 * value. Gets and sets the selected list type tab index based on the
 * selected list type.
 */
const selectedListTypeTabIndex: WritableComputedRef<number> = computed({
  get(): number {
    return selectedListType.value === SubcontractorEntityType.FLEET_ASSET
      ? 0
      : 1;
  },
  set(value: number): void {
    if (value === 0) {
      selectListTypeTab(SubcontractorEntityType.FLEET_ASSET);
    } else if (value === 1) {
      selectListTypeTab(SubcontractorEntityType.DRIVER);
    }
  },
});

/**
 * Gets and sets the the selected list type from the local variable. When setting,
 * it also updates the local storage to persist the selection.
 */
const selectedListType: WritableComputedRef<
  SubcontractorEntityType.FLEET_ASSET | SubcontractorEntityType.DRIVER
> = computed({
  get(): SubcontractorEntityType.FLEET_ASSET | SubcontractorEntityType.DRIVER {
    return _selectedListType.value;
  },
  set(
    value: SubcontractorEntityType.FLEET_ASSET | SubcontractorEntityType.DRIVER,
  ): void {
    setSelectedFleetListViewToLocalStorage(value);
    _selectedListType.value = value;
  },
});

/**
 * Used to encapsulate all data used in the fleet grouping process. This object
 * is the main source of reactivity, as it will update when the key data changes
 * and force the ‘fleetAssets’ computed property to recompute, which calls the
 * per driver/vehicle grouping methods.
 */
const keyData: ComputedRef<FleetListKeyData> = computed(() => {
  if (!updateCounter.value) {
    return {
      allFleetAssets: [],
      gpsData: new Map(),
      onlineDrivers: new Map(),
      inProgressJobs: [],
      allocatedWork: [],
      settings: [],
      fleetActiveDrivers: new Map(),
      ownerAffiliationMap: new Map(),
      nationalClientNames: new Map(),
      serviceTypesMap: new Map(),
      divisionContracts: [],
      equipmentHireOwners: [],
      categoryListOrder: [],
      allDrivers: [],
      operationsChannel: null,
    };
  }
  const selectedOperationsChannel: OperationsChannel | null =
    filterStore.selectedOperationsChannel;

  // Determine which map to use for active drivers based on the selected list
  // type
  const activeEntityMap =
    selectedListType.value === SubcontractorEntityType.FLEET_ASSET
      ? fleetAssetStore.fleetAssetActiveDriver
      : fleetAssetStore.driverActiveFleetAsset;
  return {
    allFleetAssets: activeFleetAssets.value,
    gpsData: useGpsStore().allGpsPositions,
    onlineDrivers: useDriverAppStore().onlineDriverMap,
    inProgressJobs: inProgressJobs.value,
    allocatedWork: allocatedWorkToday.value,
    settings: fleetAssetListSettings.value,
    fleetActiveDrivers: activeEntityMap,
    ownerAffiliationMap: fleetAssetOwnerStore.ownerAffiliationMap,
    nationalClientNames: nationalClientNames.value,
    serviceTypesMap: companyDetailsStore.serviceTypesMap,
    divisionContracts: fleetAssetStore.hireContracts,
    equipmentHireOwners: equipmentHireOwners.value,
    categoryListOrder: categoryListOrder.value,
    allDrivers: driverSummaryList.value,
    operationsChannel: selectedOperationsChannel,
  };
});

const gridWidth = computed(() => {
  if (props.currentIndex !== 0) {
    return 'lg6 md12';
  } else {
    return 'lg4 md12';
  }
});

const reorderDialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return showReorderDialog.value;
  },
  set(value: boolean): void {
    // If opening dialog, then set the number of assets for each service type so
    // we can pass it as a prop to the dialog
    if (value) {
      listOrderCounts.value = new Map<number, number>();

      categoryListOrder.value.forEach((o: number) => {
        let count = 0;

        // Handle special cases for client/national client groups
        if (o === DRIVER_LIST_ORDER_CLIENT) {
          // Sum all client groups
          count = allListItemGroups.value
            .filter((f) => f.groupType === GroupType.client)
            .reduce((sum, current) => sum + current.assets.length, 0);
        } else if (o === DRIVER_LIST_ORDER_NATIONAL_CLIENT) {
          // Sum all national client groups
          count = allListItemGroups.value
            .filter((f) => f.groupType === GroupType.nationalClient)
            .reduce((sum, current) => sum + current.assets.length, 0);
        } else {
          // Existing service type logic
          const foundFa = allListItemGroups.value.find(
            (f) => f.serviceTypeId === o,
          );
          count = foundFa ? foundFa.assets.length : 0;
        }

        listOrderCounts.value.set(o, count);
      });
    } else {
      // Else reset the map
      listOrderCounts.value = new Map<number, number>();
    }
    showReorderDialog.value = value;
  },
});

function setNationalClientNames(): void {
  nationalClientNames.value = new Map(
    useClientDetailsStore()
      .nationalClientsList.filter((c) => !!c._id)
      .map(({ _id, name }) => [_id!, name]),
  );
}

// --- Helper Functions ---
function getOrderedServiceTypeList(
  categoryListOrder: number[],
  serviceTypesMap: Map<number, ServiceTypes>,
): ServiceTypes[] {
  return categoryListOrder.flatMap((s) => {
    if (
      s === DRIVER_LIST_ORDER_CLIENT ||
      s === DRIVER_LIST_ORDER_NATIONAL_CLIENT
    ) {
      return returnServiceTypeObjectForStaticType(s) ?? [];
    }
    return (
      serviceTypesMap.get(s) ?? returnServiceTypeObjectForStaticType(s) ?? []
    );
  });
}

/**
 * The main unfiltered list of fleet assets or drivers. Passes in the selected
 * grouping type (selectedListType) and the key data into
 * returnFleetDriverListForType which returns the entities sorted appropriately.
 * Recomputes when keyData changes.
 * Logs the time taken to compute the list.
 */
const allListItemGroups: ComputedRef<FleetAssetList[]> = computed(() => {
  const result = returnFleetDriverListForType(
    selectedListType.value,
    keyData.value,
  );
  return result;
});

/**
 * Returns a sorted list of vehicles or drivers depending on the provided
 * sorting type ‘type’.
 * @param type - The type of entity to return (DRIVER or FLEET_ASSET).
 * @param keyData - The key data containing all necessary information for sorting.
 */
function returnFleetDriverListForType(
  type: SubcontractorEntityType.DRIVER | SubcontractorEntityType.FLEET_ASSET,
  keyData: FleetListKeyData,
): FleetAssetList[] {
  if (type === SubcontractorEntityType.DRIVER) {
    return returnListGroupedByDriver(keyData);
  } else if (type === SubcontractorEntityType.FLEET_ASSET) {
    return returnListGroupedByFleet(keyData);
  } else {
    throw new Error(
      `Invalid SubcontractorEntityType: ${type}. Expected DRIVER or FLEET_ASSET.`,
    );
  }
}

/**
 * Builds a list of grouped fleet assets based on a combination of user settings,
 * service types, and client preferences.
 *
 * The final result is a categorised list of fleet groups used for UI display.
 *
 * @param {FleetListKeyData} keyData - A container of all context/state needed to build the fleet list, including:
 *    - `allFleetAssets`: the raw unfiltered fleet assets
 *    - `settings`: feature flags determining what to show
 *    - `categoryListOrder`: desired category display order
 *    - `serviceTypesMap`: map of service types for categorisation
 *    - `selectedClientIds`: active client filters (if any)
 * @returns {FleetAssetList[]} - A fully grouped, categorised, and filtered list of fleet assets for rendering
 */
function returnListGroupedByFleet(keyData: FleetListKeyData): FleetAssetList[] {
  // Step 1: Get fleet filter object
  const fleetFilterOptions = keyData.operationsChannel?.fleetFilterOptions;

  const showClientGroups =
    fleetFilterOptions?.clientFilter?.clientIds?.isAny ||
    fleetFilterOptions?.clientFilter?.clientIds?.isOnly ||
    false;
  const showNationalClientPreferred =
    fleetFilterOptions?.clientFilter?.nationalClientIds?.isAny ||
    fleetFilterOptions?.clientFilter?.nationalClientIds?.isOnly ||
    false;

  // Step 3: Get an ordered list of service types based on configured category order
  const orderedServiceTypeList: ServiceTypes[] = getOrderedServiceTypeList(
    keyData.categoryListOrder,
    keyData.serviceTypesMap,
  );

  // Step 4: Create groups based on service types
  const filteredServiceTypes: FleetAssetList[] = getServiceTypeGroups({
    entitiesByType: {
      type: SubcontractorEntityType.FLEET_ASSET,
      entityList: keyData.allFleetAssets,
    },
    serviceTypeList: orderedServiceTypeList,
    keyData: keyData,
  });

  // Step 4: Create groups based on service types
  const filteredByAffiliation: FleetAssetList[] = getAffiliationGroups({
    entitiesByType: {
      type: SubcontractorEntityType.FLEET_ASSET,
      entityList: keyData.allFleetAssets,
    },
    keyData: keyData,
  });

  // Step 6: If enabled, build groups for fleets preferred by the client
  const clientPreferredGroups: FleetAssetList[] = showClientGroups
    ? getClientPreferredGroups({
        entitiesByType: {
          type: SubcontractorEntityType.FLEET_ASSET,
          entityList: keyData.allFleetAssets,
        },
        clientSummaryList: clientDetailsStore.clientSummaryList,
        keyData: keyData,
      })
    : [];

  // Step 7: If enabled, build groups for fleets preferred at a national level
  const nationalClientGroups: FleetAssetList[] = showNationalClientPreferred
    ? getNationalClientGroups({
        entitiesByType: {
          type: SubcontractorEntityType.FLEET_ASSET,
          entityList: keyData.allFleetAssets,
        },
        clientSummaryList: clientDetailsStore.clientSummaryList,
        keyData: keyData,
      })
    : [];

  // Step 8: Merge all groups together in the configured category order
  let finalGroups: FleetAssetList[] = buildFinalGroups({
    categoryListOrder: keyData.categoryListOrder,
    clientPreferredGroups,
    nationalClientGroups,
    filteredServiceTypes,
    filteredByAffiliation,
  });

  // Step 9: Append any missing groups (e.g. fallback or uncategorised items)
  finalGroups = applyInclusionsAndExclusions({
    type: SubcontractorEntityType.FLEET_ASSET,
    finalGroups,
    keyData,
  });

  // Final output is returned to the caller for rendering
  return finalGroups;
}

/**
 * Builds a list of grouped **driver** entities based on user settings and client preferences.
 *
 * This function handles the grouping logic for the driver list view in the operations UI,
 * taking into account client preference toggles and fallback grouping when no category applies.
 *
 * @param {FleetListKeyData} keyData - Contextual data including driver list, filters, settings, and category ordering.
 * @returns {FleetAssetList[]} - Final grouped and categorised driver list ready for display.
 */
function returnListGroupedByDriver(
  keyData: FleetListKeyData,
): FleetAssetList[] {
  // Step 1: Determine whether to show client preferred or national preferred drivers
  const fleetFilterOptions = keyData.operationsChannel?.fleetFilterOptions;

  const showClientGroups =
    fleetFilterOptions?.clientFilter?.clientIds?.isAny ||
    fleetFilterOptions?.clientFilter?.clientIds?.isOnly ||
    false;
  const showNationalClientPreferred =
    fleetFilterOptions?.clientFilter?.nationalClientIds?.isAny ||
    fleetFilterOptions?.clientFilter?.nationalClientIds?.isOnly ||
    false;

  // NOTE: Drivers don't support "showAllDrivers" logic for now (commented out)
  // const showAllDrivers = !isSettingActive('showAllDrivers', keyData.settings) &&
  //   (showClientPreferred || showNationalClientPreferred);

  // Step 2: Filter the raw driver list based on selected clients and job list
  const clientFilteredDrivers: DriverOperationsListSummary[] =
    getClientFilteredFleetAssets({
      toFilter: {
        type: SubcontractorEntityType.DRIVER,
        entityList: keyData.allDrivers,
      },
      operationsChannel: keyData.operationsChannel,
      operationsJobList: jobStore.operationJobsList,
    });

  // Step 3: Group drivers by client preference if enabled
  const clientPreferredGroups: FleetAssetList[] = showClientGroups
    ? getClientPreferredGroups({
        entitiesByType: {
          type: SubcontractorEntityType.DRIVER,
          entityList: clientFilteredDrivers,
        },
        clientSummaryList: clientDetailsStore.clientSummaryList,
        keyData: keyData,
      })
    : [];
  // Step 4: Group drivers by national preferences if applicable
  const nationalClientGroups: FleetAssetList[] = showNationalClientPreferred
    ? getNationalClientGroups({
        entitiesByType: {
          type: SubcontractorEntityType.DRIVER,
          entityList: clientFilteredDrivers,
        },
        clientSummaryList: clientDetailsStore.clientSummaryList,
        keyData: keyData,
      })
    : [];

  // Step 3: Get an ordered list of service types based on configured category order
  const orderedServiceTypeList: ServiceTypes[] = getOrderedServiceTypeList(
    keyData.categoryListOrder,
    keyData.serviceTypesMap,
  );

  // Step 4: Create groups based on service types
  const serviceTypeGroups: FleetAssetList[] = getServiceTypeGroups({
    entitiesByType: {
      type: SubcontractorEntityType.DRIVER,
      entityList: keyData.allDrivers,
    },
    serviceTypeList: orderedServiceTypeList,
    keyData: keyData,
  });

  // Step 5: Group drivers by affiliation
  const filteredByAffiliation: FleetAssetList[] = getAffiliationGroups({
    entitiesByType: {
      type: SubcontractorEntityType.DRIVER,
      entityList: keyData.allDrivers,
    },
    keyData: keyData,
  });

  // Step 6: Build final list from all major groups (no service types for drivers)
  let finalGroups: FleetAssetList[] = buildFinalGroups({
    categoryListOrder: keyData.categoryListOrder,
    clientPreferredGroups,
    nationalClientGroups,
    filteredServiceTypes: serviceTypeGroups,
    filteredByAffiliation: filteredByAffiliation,
  });
  // Step 7: Append any remaining unmatched drivers into the list
  finalGroups = applyInclusionsAndExclusions({
    type: SubcontractorEntityType.DRIVER,
    finalGroups,
    keyData,
  });
  return finalGroups;
}

/**
 * Used to render html list of driver cards. If there is a filter string
 * entered, this returns the filtered list of Fleet Asset Groups. If no filter
 * string exists, then return the original fleet asset list.
 * Optimized with caching and early returns.
 */
const filteredListItemGroups: ComputedRef<FleetAssetList[]> = computed(() => {
  let startTime: number | undefined;
  if (isDebug) {
    startTime = performance.now();
  }
  const hasFilterText =
    filterSearchString.value && filterSearchString.value.length > 1;

  let result: FleetAssetList[];

  if (!hasFilterText) {
    // Early return for no filter case - simple visibility check
    result = allListItemGroups.value.filter((l) =>
      l.assets.some((a) => a.isVisible),
    );
  } else {
    // Filter with optimized search
    result = allListItemGroups.value
      .map((l) => {
        const filteredAssets = l.assets.filter((a) => {
          if (!a.isVisible) {
            return false;
          }

          // Check main asset properties
          if (compareSearchString(a.displayName)) {
            return true;
          }
          if (
            a.registrationNumber &&
            compareSearchString(a.registrationNumber)
          ) {
            return true;
          }

          // Check associated items (drivers/vehicles)
          return a.associatedListItems.some(
            (d) =>
              compareSearchString(d.displayName) ||
              (d.contactNumber && compareSearchString(d.contactNumber)),
          );
        });

        return {
          serviceTypeId: l.serviceTypeId,
          name: l.name,
          groupType: l.groupType,
          assets: filteredAssets,
        };
      })
      .filter((l) => l.assets.length > 0);
  }
  if (isDebug && typeof startTime === 'number') {
    const endTime = performance.now();
    const countOfGroups = result.length;
    const countOfAssets = result.reduce(
      (sum, group) => sum + group.assets.length,
      0,
    );
    // eslint-disable-next-line no-console
    console.log(
      `[filteredListItemGroups] Found ${countOfGroups} groups and ${countOfAssets} assets`,
    );
    // eslint-disable-next-line no-console
    console.log(
      `[filteredListItemGroups] Computed in ${(endTime - startTime).toFixed(
        2,
      )} ms`,
    );
  }
  return result;
});

/**
 * Returns the count of fleet assets that aren’t currently visible. Used in the
 * template when filters have been applied, such that the user is aware of what
 * they’re not currently seeing.
 */
function getHiddenListItemCount(): number {
  // Collect all visible entityIds in a Set to deduplicate
  const visibleEntityIds = new Set<string>();
  filteredListItemGroups.value.forEach((serviceType) => {
    serviceType.assets?.forEach((asset) => {
      if (asset.isVisible) {
        visibleEntityIds.add(asset.entityId);
      }
    });
  });

  if (selectedListType.value === SubcontractorEntityType.DRIVER) {
    return validDriverCount.value - visibleEntityIds.size;
  } else {
    return validFleetAssetCount.value - visibleEntityIds.size;
  }
}

/**
 * Sets the pinned fleet/driver combo in the store. Calls method in the store to
 * set the value in the appropriate map depending on which list type we’re
 * currently viewing, such that the pinned entity combinations are unique
 * between list types.
 *
 * Called from template via emit from FleetAssetListItem component
 * @param assetDetails - The details of the fleet asset and driver to set as
 * pinned
 */
function setFleetAssetActiveDriver(assetDetails: FleetAssetDriverPair) {
  if (!assetDetails || !assetDetails.driverId || !assetDetails.fleetAssetId) {
    return;
  }
  const fleetAssetId = assetDetails.fleetAssetId;
  const driverId = assetDetails.driverId ? assetDetails.driverId : '';
  if (
    operationsStore.selectedFleetAssetId === fleetAssetId &&
    operationsStore.selectedDriverId !== driverId
  ) {
    operationsStore.setSelectedDriverId(driverId);
  }
  fleetAssetStore.setFleetAssetActiveDriver(
    {
      fleetAssetId,
      driverId,
    },
    selectedListType.value,
  );
}

// Returns a list of jobs that are at IN PROGRESS status.
const inProgressJobs: ComputedRef<OperationJobSummary[]> = computed(() => {
  return jobStore.operationJobsList.filter(
    (job) => job.workStatus === WorkStatus.IN_PROGRESS,
  );
});

// Returns a list of jobs at ALLOCATED or PREALLOCATED status for today.
const allocatedWorkToday: ComputedRef<OperationJobSummary[]> = computed(() => {
  const startOfDay = returnStartOfDayFromEpoch();
  const endOfDay = returnEndOfDayFromEpoch();
  return jobStore.operationJobsList.filter(
    (job) =>
      job.workStatus >= WorkStatus.PREALLOCATED &&
      job.workStatus <= WorkStatus.ACCEPTED &&
      job.date &&
      job.date >= startOfDay &&
      job.date < endOfDay,
  );
});

const selectedFleetAsset: ComputedRef<string> = computed(() => {
  return operationsStore.selectedFleetAssetId || '';
});

const selectedDriverId: ComputedRef<string> = computed(() => {
  return operationsStore.selectedDriverId || '';
});

/**
 * When the value of selectedFleetAsset is changed, scroll the element to the
 * top of the scroll view. This only occurs if the update was triggered from
 * an external component.
 *
 * @param newValue - The new value to scroll to.
 * @param oldValue - The old value.
 */
watch(selectedFleetAsset, (newValue: string, oldValue: string) => {
  // If the update was triggered from an external component, use the id to
  // find the element and scroll it into view
  if (!updatingSelectedIdLocally.value && !!newValue && oldValue !== newValue) {
    scrollToElement(newValue);
  }
  updatingSelectedIdLocally.value = false;
});

/**
 * Scrolls the element with the given ID into view smoothly.
 *
 * @param elementId - The ID of the element to scroll into view.
 */
function scrollToElement(elementId: string): void {
  const el = document.getElementById(elementId);
  if (el) {
    if (el) {
      nextTick(() =>
        // set timeOut for expansion panel to open first to scrollIntoView.
        setTimeout(() => {
          el.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }, 500),
      );
    }
  }
}

function selectListTypeTab(
  type: SubcontractorEntityType.FLEET_ASSET | SubcontractorEntityType.DRIVER,
): void {
  // If the selected type is already selected, do nothing
  if (selectedListType.value === type) {
    return;
  }
  // Set the selected list type to the new value
  selectedListType.value = type;

  // Reset the search string and panel state
  clearSearchInput();
  panel.value = new Array(filteredListItemGroups.value.length).fill(true);
}

// Set the current store value for selected FleetAsset and Driver
// This will update components on the dashboard.
function selectListItem(
  assetDetails: {
    fleetAssetId?: string;
    driverId?: string;
    type: 'truck' | 'trailer';
  },
  allowDeselection: boolean = true,
): void {
  if (!assetDetails || !assetDetails.fleetAssetId) {
    return;
  }

  const fleetAssetId = assetDetails.fleetAssetId;
  const driverId = assetDetails.driverId ? assetDetails.driverId : '';
  const type = assetDetails.type ? assetDetails.type : 'truck';

  if (filterSearchString.value !== '') {
    scrollToElement(fleetAssetId);
  }
  clearSearchInput();
  panel.value = new Array(filteredListItemGroups.value.length).fill(true);

  // Deselect the current driver and fleet asset if the incoming values are
  // the same as the current selected values
  if (
    allowDeselection &&
    operationsStore.selectedFleetAssetId === fleetAssetId &&
    operationsStore.selectedDriverId === driverId
  ) {
    operationsStore.setSelectedFleetAssetId('');
    operationsStore.setSelectedDriverId('');
    return;
  }

  // Set updatingSelectedIdLocally to true so we know the id is being updated
  // from within this component
  updatingSelectedIdLocally.value = true;
  operationsStore.setSelectedFleetAssetId(fleetAssetId);
  if (type === 'trailer') {
    operationsStore.setSelectedDriverId('');
    return;
  }
  operationsStore.setSelectedDriverId(driverId);
  setSelectedJobForFleetAsset(fleetAssetId, driverId);
  return;
}

/**
 * For the provided fleetAssetId and driverId, tries to find a current job to
 * set to display in the Job Details panel of the dashboard. Called when
 * selecting a list item from the Fleet Asset List
 *
 * First checks if there is any in-progress work for the given fleet asset and
 * driver, then tries to find an ALLOCATED or PREALLOCATED job for today, then
 * tries to find ANY job for today. If still no job is found, it tries to find
 * ANY job for the driver.
 *
 * @param {string} fleetAssetId - The id of the fleet asset.
 * @param {string} driverId - The id of the driver.
 */
function setSelectedJobForFleetAsset(fleetAssetId: string, driverId: string) {
  // 1. Check if there is any in-progress work for this fleet/driver
  let activeJob = inProgressJobs.value.find(
    (job: OperationJobSummary) =>
      job.fleetAssetId === fleetAssetId && job.driverId === driverId,
  );

  // 2. If we didn't find any, filter for all jobs for the driver
  if (!activeJob) {
    const startDay = returnStartOfDayFromEpoch();
    const endDay = returnEndOfDayFromEpoch();

    // Get all jobs for driver
    const jobsForDriver: OperationJobSummary[] =
      jobStore.operationJobsList.filter(
        (job: OperationJobSummary) =>
          job.fleetAssetId === fleetAssetId && job.driverId === driverId,
      );

    // Separate jobs for today and other days
    const todaysJobs = jobsForDriver.filter(
      (job: OperationJobSummary) =>
        job.date && job.date > startDay && job.date < endDay,
    );
    const allocatedJobsToday = todaysJobs.filter(
      (job: OperationJobSummary) =>
        job.workStatus >= WorkStatus.PREALLOCATED &&
        job.workStatus <= WorkStatus.ACCEPTED,
    );

    // 3. Try to find an ALLOCATED or PREALLOCATED job for today
    if (allocatedJobsToday.length > 0) {
      activeJob = allocatedJobsToday[0];
    } else if (todaysJobs.length > 0) {
      // 4. If we don't find any, find ANY job for today
      activeJob = todaysJobs[0];
    } else if (jobsForDriver.length > 0) {
      // 5. If we still don't find any, find ANY job for the driver
      activeJob = jobsForDriver[0];
    }
  }

  // If we have a job, then set it to the store

  Mitt.emit('closeDashboardJobRows', 'driver-list');
  if (activeJob?.jobId) {
    operationsStore.getFullJobDetails(activeJob.jobId);
    operationsStore.setSelectedJobId(activeJob.jobId);
  } else {
    operationsStore.setSelectedJobId(-1);
  }
}

const filterSearchStringController: WritableComputedRef<string> = computed({
  get(): string {
    return filterSearchString.value;
  },
  set(value: string): void {
    if (filterSearchTimeout.value) {
      clearTimeout(filterSearchTimeout.value);
    }
    filterSearchTimeout.value = setTimeout(() => {
      filterSearchString.value = value;
    }, 150);
  },
});

// Handle enter pressed event from inside search field
function handleEnterEvent(e: KeyboardEvent) {
  if (e.keyCode === 13) {
    selectFirstSearchResult();
  }
}

// Select first available search result from the driver list
// If no matched trucks or trailers are available, do nothing
function selectFirstSearchResult() {
  // Check fleet assets
  if (filteredListItemGroups.value?.length) {
    const serviceType = filteredListItemGroups.value[0];
    if (serviceType.assets.length) {
      const asset = serviceType.assets[0];
      let fleetAssetId = asset.entityId;
      let driverId = '';
      if (selectedListType.value === SubcontractorEntityType.DRIVER) {
        // If we are in driver view, the entityId is the driverId
        driverId = asset.entityId;
        fleetAssetId = asset.activeItem ? asset.activeItem.entityId : '';
      } else {
        // If we are in fleet asset view, the entityId is the fleetAssetId
        fleetAssetId = asset.entityId;
        driverId = asset.activeItem ? asset.activeItem.entityId : '';
      }
      const type: 'truck' | 'trailer' = !asset.isEquipmentHire
        ? 'truck'
        : 'trailer';
      const assetDetails = {
        fleetAssetId,
        driverId,
        type,
      };
      selectListItem(assetDetails, false);
      scrollToElement(fleetAssetId);
    }
  }
}

// Clear the search input field and unfocus input element
function clearSearchInput(): void {
  filterSearchString.value = '';
  const el = document.getElementById('driver-list__search-input');
  if (el) {
    (el as HTMLInputElement).blur();
  }
}

// Focus the search input element
function focusSearchInput(): void {
  const el = document.getElementById('driver-list__search-input');
  if (el) {
    searchInputIsFocused.value = true;
    nextTick(() => {
      el.focus();
    });
  }
}

// Open the details view for the selected fleet asset
function viewAssetInformation(assetDetails: FleetAssetDriverPair) {
  operationsStore.setSelectedFleetAssetId(assetDetails.fleetAssetId);
  operationsStore.setSelectedDriverId(assetDetails.driverId);
  operationsStore.setViewingAssetInformationDialog(true);
}

// Handle emit from settings dialog component
function tableSettingsUpdated(settingsList: OperationsDashboardSetting[]) {
  // Check if both Online and offline Driver are inactive
  const onlineDriver = settingsList.some((s) => s.groupId === 3 && !s.active);
  const offlineDriver = settingsList.some((s) => s.groupId === 4 && !s.active);
  // Check if both hasWork and NoWork Driver are inactive
  const hasWork = settingsList.some((s) => s.groupId === 5 && !s.active);
  const noWork = settingsList.some((s) => s.groupId === 6 && !s.active);
  // if show online and offline driver checkbox both are false, make both true.
  if (onlineDriver && offlineDriver) {
    settingsList.forEach((s) => {
      if (s.groupId === 3 || s.groupId === 4) {
        s.active = true;
      }
    });
  }
  // if show hasNoWork and AllocatedWork driver checkboxes are false, make both true.
  if (hasWork && noWork) {
    settingsList.forEach((s) => {
      if (s.groupId === 5 || s.groupId === 6) {
        s.active = true;
      }
    });
  }
  // update local settings in store
  operationsStore.updateDashboardSettingsFleetAssetList(settingsList);
}

/**
 * Captures emit from DriverListOrderDialog component. Sets the categoryListOrder
 * to the incoming value emitted from the component
 */
function updatedListOrder(listOrder: number[]) {
  categoryListOrder.value = listOrder;
  reorderDialogController.value = false;
}

/**
 * Captures emit from DriverListOrderDialog component. Sets the categoryListOrder
 * back to the default ordering
 */
function restoreDefaultListOrder() {
  categoryListOrder.value = getDefaultDriverListOrder(
    companyDetailsStore.getServiceTypesList,
  );
  reorderDialogController.value = false;
}

/**
 * Display the conversation for the selected asset/active driver in the chat
 * component in the bottom right of the operations screen
 *
 * @param assetDetails - The details of the asset and driver to open the chat
 * for.
 */
function openChatMessage(assetDetails: FleetAssetDriverPair) {
  if (!assetDetails || !assetDetails.driverId) {
    return;
  }
  const fleetAssetId = assetDetails.fleetAssetId;
  const driverId = assetDetails.driverId ? assetDetails.driverId : '';
  operationsStore.setSelectedFleetAssetId(fleetAssetId);
  operationsStore.setSelectedDriverId(driverId);

  if (driverId) {
    // Emit event to open the chat component with the selected driver
    Mitt.emit('openInDriverMessages', {
      driverId: driverId,
      messageContent: '',
    });
  }
}

/**
 * Compare the provided string with the current value for filterSearchString
 * Trim and convert to uppercase for comparison, then return bool as result True
 * if filter string is empty OR if str is substring of filter string False
 * otherwise
 * @param str - The string to compare against the filter search string
 */
function compareSearchString(str: string): boolean {
  if (!filterSearchString.value || filterSearchString.value.length === 1) {
    return true;
  }
  return str
    .trim()
    .toUpperCase()
    .startsWith(filterSearchString.value.trim().toUpperCase());
}

// Set owners with affiliation of 2 for EQUIPMENT HIRE
function setEquipmentHireOwners() {
  const allOwners = fleetAssetOwnerStore.getOwnerList;
  equipmentHireOwners.value = allOwners.filter(
    (owner: FleetAssetOwnerSummary) => owner.affiliation === '2',
  );
}

/**
 * Sets the active fleet assets in the store based on the current fleet asset
 * list. Filters the assets to include only those that are active for
 * allocation.
 */
function setBaseEntityLists() {
  const activeDrivers = driverDetailsStore.getDriverList.filter(
    (driver: DriverDetailsSummary) => driver.isActive,
  );

  // Set the active fleet assets in the store
  activeFleetAssets.value = fleetAssetStore.getAllFleetAssetList
    .filter((asset: FleetAssetSummary) => asset.isActiveForAllocation)
    .map((asset: FleetAssetSummary) => {
      const associatedDriverSummaries = activeDrivers
        .filter((driver: DriverDetailsSummary) =>
          asset.associatedDrivers.includes(driver.driverId),
        )
        .map((driver: DriverDetailsSummary) => {
          return new DriverOperationsListSummary(driver, []);
        });
      return new FleetOperationsListSummary(asset, associatedDriverSummaries);
    })
    .filter(
      (asset: FleetOperationsListSummary) =>
        asset.associatedDriverSummaries.length > 0,
    );
  validFleetAssetCount.value = activeFleetAssets.value.length;

  // Set driver summary list
  driverSummaryList.value = driverDetailsStore.getDriverList
    .map((driver: DriverDetailsSummary) => {
      const fleetAssets = activeFleetAssets.value.filter(
        (asset: FleetAssetSummary) =>
          asset.associatedDrivers.some((d) => d === driver.driverId),
      );
      return new DriverOperationsListSummary(driver, fleetAssets);
    })
    .filter(
      (driver: DriverOperationsListSummary) =>
        driver.associatedFleetAssets.length > 0,
    );
  const validDrivers = driverSummaryList.value.filter(
    (driver: DriverOperationsListSummary) => driver.isActive,
  );
  validDriverCount.value = validDrivers.length;
}

// function to open/close all expansion panels
function togglePanel() {
  // Check if the panel is currently an empty array or not
  if (panel.value.length === 0) {
    // If it's empty, fill it with `true` for each service type
    panel.value = new Array(filteredListItemGroups.value.length).fill(true);
  } else {
    // If it's not empty, clear the panel
    panel.value = [];
  }
}

onBeforeMount(() => {
  setNationalClientNames();
  setEquipmentHireOwners();
  setBaseEntityLists();

  // Get any custom ordering from local storage if it exists
  const listOrderFromLocalStorage = localStorage.getItem(
    LOCAL_STORAGE_DRIVER_LIST_ORDER,
  );

  let listOrder: number[] = [];

  // Include static service type IDs for Client and National Client
  const staticServiceTypeIds = [
    DRIVER_LIST_ORDER_INTERNAL,
    DRIVER_LIST_ORDER_EQUIPMENT_HIRE,
    DRIVER_LIST_ORDER_OUTSIDE_HIRE,
    DRIVER_LIST_ORDER_NATIONAL_CLIENT,
    DRIVER_LIST_ORDER_CLIENT,
    DRIVER_LIST_ORDER_SUBCONTRACTOR,
  ];
  // Combine keys from the serviceTypesMap with the static ones
  const allServiceTypeIds = Array.from(
    companyDetailsStore.serviceTypesMap.keys(),
  ).concat(staticServiceTypeIds);

  if (listOrderFromLocalStorage) {
    listOrder = JSON.parse(listOrderFromLocalStorage);
    // Add any missing IDs from allServiceTypeIds into the order
    const extraIds: number[] = allServiceTypeIds.filter(
      (id) => !listOrder.includes(id),
    );
    listOrder = listOrder.concat(extraIds);

    // Add any missing IDS from the list of static types into the order
    const extraStaticIds: number[] = staticServiceTypes.filter(
      (id) => !listOrder.includes(id),
    );
    listOrder = listOrder.concat(extraStaticIds);
  } else {
    listOrder = getDefaultDriverListOrder(
      companyDetailsStore.getServiceTypesList,
    );
  }

  categoryListOrder.value = listOrder;
});

// TODO: ADD MITT LISTENERS TO UPDATE LOCAL STATIC LISTS

onMounted(() => {
  // Called after component is mounted
  // Focus search input if needed
  panel.value = new Array(
    allListItemGroups.value.length + filteredListItemGroups.value.length,
  ).fill(true);
});
</script>
<style scoped lang="scss">
.driver-list-component {
  border-left: 1px solid var(--border-color);
  border-radius: $border-radius-base 0px;
  height: 100%;

  .task-bar {
    border-radius: 10px 10px 0 0;
    height: 38px;
  }

  .filter-btn {
    &.clear {
      border-radius: 18px;
      background: none;
      color: $warning;
      height: 22px !important;
      width: 22px !important;
      border: 1.5px solid $warning;
    }
    &.reorder {
      border-radius: 18px;
      background: none;
      height: 24px !important;
      width: 34px !important;
      margin-right: 12px;
      .v-icon {
        color: var(--text-color) !important;
      }
    }
    &.expandCollapse {
      border-radius: 18px;
      background: none;
      height: 24px !important;
      width: 30px !important;
      margin-right: 0px !important;
      padding: 0 !important;

      .v-icon {
        color: var(--text-color) !important;
      }
    }
  }

  .search-icon {
    color: var(--light-text-color);
  }

  .driver-list__search-input {
    background: none;
    box-shadow: none;
    border: 1px solid var(--light-text-color);
    border-radius: $border-radius-base;
    padding: 2px 8px 2px 8px;
    font: inherit;
    width: auto;
    margin: 0;
  }

  .content-scrollable {
    max-height: 100%;
    flex-direction: column;
    overflow-y: auto;
    display: flex;
    flex-grow: 1;

    .service-type__header-container {
      display: flex;
      flex-direction: row;
      .subHeader {
        font-size: $font-size-10;
        font-weight: 600;
        text-transform: uppercase;
        border-bottom: 1px solid;
        margin-left: -8px;
        color: $info;
        border-color: $info;

        &.Service {
          color: $info;
          border-color: $info;
        }
        &.Client {
          color: var(--accent-secondary);
          border-color: var(--accent-secondary);
        }
        &.National {
          color: $bg-light-blue;
          border-color: $bg-light-blue;
        }
        &.Affiliation {
          color: $bg-light-red;
          border-color: $bg-light-red;
          opacity: 0.8;
        }
      }
      .service-type__header {
        font-size: $font-size-14;
        font-family: $sub-font-family;
        text-transform: uppercase;
        color: var(--bg-light);
        padding-left: 14px;
        letter-spacing: 1px;
        display: inline;
        font-weight: 900;
      }
      &.outside {
        color: $average;
        border-bottom: 1px solid $average;
      }
      .service-type__count {
        display: inline;
        position: absolute;
        font-size: $font-size-11;
        background-color: var(--bg-light);
        color: var(--text-color-reverse);
        margin: 0px 4px 0px 4px;
        padding: 0px 6px 0px 14px;
        height: 18px;
        border-radius: 0 10px 10px 0;
        right: 6px;
        font-weight: 800;
      }
    }

    .expansion-panel {
      background-color: var(--background-color-400) !important;
      min-height: max-content;
    }
  }

  .asset-list-container {
    height: calc(100% - 32px);
  }
}

.v-item-group.v-expansion-panels {
  & .v-expansion-panel,
  & .v-expansion-panel-header,
  & .v-expansion-panel-content {
    transition: none !important;
  }
}

.expand-icon {
  background-color: var(--bg-light);
  border-radius: 10px 0 0 10px;
  height: 18px;
  width: 30px;
  padding: 1px 1px 1px 4px !important;
  justify-content: center;
  margin-bottom: 3px;
  .v-icon {
    position: absolute;
    color: var(--text-color-reverse) !important;
    font-weight: 800;
  }
}

.hidden-fleet-txt {
  margin-left: 10px;
  font-size: $font-size-12;
  color: var(--light-text-color);
  text-transform: capitalize;
}

// Responsive Styles
@media (max-width: 1500px) {
  .driver-list__search-input {
    width: 80px !important;
  }
}

@media (max-width: 1024px) {
  .top-bar-title-txt {
    font-size: $font-size-12 !important;
  }
  .v-btn {
    font-size: small !important;
  }
}
</style>
