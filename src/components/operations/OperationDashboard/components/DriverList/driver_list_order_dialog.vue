<template>
  <ContentDialog
    :showDialog.sync="showDialog"
    title="Re-order Fleet Asset List"
    width="600px"
    contentPadding="pa-0"
    @cancel="showDialog = false"
    @confirm="confirm"
    @action="restoreDefaults"
    :showActions="true"
    :isConfirmUnsaved="isConfirmUnsaved"
    confirmBtnText="Confirm"
    :showActionButton="true"
    actionBtnText="Restore Default"
    :actionRequiresConfirmation="true"
    actionConfirmationMessage="This will remove all custom ordering and restore the default order. Are you sure you want to continue?"
  >
    <v-layout>
      <v-flex
        id="driver-list-order-scrollable"
        md12
        class="app-bgcolor--400 body-scrollable--75 pa-3"
        v-if="editingServiceTypeList !== null"
      >
        <draggable
          v-model="editingServiceTypeList"
          class="draggable-container"
          draggable=".dashboard-card"
          handle=".arrow-icon-container"
          v-bind="dragOptions()"
        >
          <transition-group>
            <div
              class="app-bgcolor--500 dashboard-card no-hover"
              style="margin: 6px 0px"
              md12
              v-for="serviceType in editingServiceTypeList"
              :key="serviceType.serviceTypeId"
            >
              <v-layout
                justify-space-between
                align-center
                class="dashboard-card__toprow"
              >
                <div
                  class="dashboard-card__bottomrow"
                  style="user-select: none"
                >
                  <div
                    class="icon-container"
                    :class="{
                      disabled:
                        listOrderCounts.get(serviceType.serviceTypeId) === 0,
                    }"
                  >
                    <span
                      class="header-icon"
                      style="font-size: 12px; font-weight: 700"
                      >{{ serviceType.shortServiceTypeName }}</span
                    >
                  </div>
                </div>
                <v-flex pl-2 pr-4>
                  <v-layout column>
                    <span
                      class="title-text"
                      :class="{
                        disabled:
                          listOrderCounts.get(serviceType.serviceTypeId) === 0,
                      }"
                      >{{ serviceType.longServiceTypeName }}</span
                    >
                    <span style="font-size: 10px; color: rgb(190, 192, 205)">
                      {{ listOrderCounts.get(serviceType.serviceTypeId) }}
                      Assets
                      {{
                        listOrderCounts.get(serviceType.serviceTypeId) === 0
                          ? '(hidden)'
                          : ''
                      }}
                    </span>
                  </v-layout>
                </v-flex>

                <v-tooltip bottom>
                  <template v-slot:activator="{ on }">
                    <v-btn
                      flat
                      v-on="on"
                      icon
                      @click="sendToTop(serviceType.serviceTypeId)"
                      class="my-0"
                    >
                      <v-icon class="arrow-icon">far fa-arrow-to-top</v-icon>
                    </v-btn>
                  </template>
                  Send to Top
                </v-tooltip>
                <v-tooltip bottom>
                  <template v-slot:activator="{ on }">
                    <v-btn
                      flat
                      v-on="on"
                      icon
                      @click="sendToBottom(serviceType.serviceTypeId)"
                      class="my-0"
                    >
                      <v-icon class="arrow-icon">far fa-arrow-to-bottom</v-icon>
                    </v-btn>
                  </template>
                  Send to Bottom
                </v-tooltip>
                <v-divider vertical></v-divider>
                <v-btn flat icon class="my-0 arrow-icon-container">
                  <v-icon class="arrow-icon">far fa-grip-lines</v-icon>
                </v-btn>
              </v-layout>
            </div>
          </transition-group>
        </draggable>
        <!-- <v-layout row wrap v-if="editingServiceTypeList !== null" md12>
      </v-layout> -->
      </v-flex>
    </v-layout>
  </ContentDialog>
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  Ref,
  ComputedRef,
  WritableComputedRef,
  watch,
} from 'vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { LOCAL_STORAGE_DRIVER_LIST_ORDER } from '@/helpers/AppLayoutHelpers/LocalStorageHelpers';
import {
  DRIVER_LIST_ORDER_CLIENT,
  DRIVER_LIST_ORDER_NATIONAL_CLIENT,
  isStaticServiceType,
  returnServiceTypeObjectForStaticType,
} from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import draggable from 'vuedraggable';

// Props
const props = defineProps<{
  isOpen: boolean;
  listOrder: number[];
  listOrderCounts: Map<number, number>;
}>();

const emit = defineEmits<{
  (event: 'update:isOpen', payload: boolean): void;
  (event: 'updatedOrder', payload: number[]): void;
  (event: 'restoreDefaults'): void;
}>();

const originalServiceTypeList: Ref<ServiceTypes[] | null> = ref(null);
const editingServiceTypeList: Ref<ServiceTypes[] | null> = ref(null);

/**
 * Returns the prop isOpen and syncs the value to the parent when showDialog is updated
 */
const showDialog: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.isOpen;
  },
  set(value: boolean): void {
    emit('update:isOpen', value);
  },
});

/**
 * Modelled to ContentDialog component. Used to show a confirmation dialog when changes have been made
 */
const isConfirmUnsaved: ComputedRef<boolean> = computed(() => {
  return (
    JSON.stringify(editingServiceTypeList.value) !==
    JSON.stringify(originalServiceTypeList.value)
  );
});

/**
 * Returns the drag options for the draggable component
 */
function dragOptions(): { chosenClass: string } {
  return {
    chosenClass: 'app-bgcolor--700',
  };
}

/**
 * When showDialog changes, set the editingServiceTypeList to the listOrder.
 * editingServiceTypeList is modelled to the draggable list in the HTML and
 * will update as the user drags and drops items
 */
watch(
  () => showDialog.value,
  function setServiceTypeList(newValue: boolean): void {
    if (newValue) {
      editingServiceTypeList.value = props.listOrder
        .map((o) => {
          if (
            isStaticServiceType(o) ||
            o === DRIVER_LIST_ORDER_CLIENT ||
            o === DRIVER_LIST_ORDER_NATIONAL_CLIENT
          ) {
            return returnServiceTypeObjectForStaticType(o);
          } else {
            const s = useCompanyDetailsStore().serviceTypesMap.get(o);
            if (s && s.longServiceTypeName === 'UNIT RATE') {
              return;
            }
            return s;
          }
        })
        .filter((s) => s !== undefined) as ServiceTypes[];
      originalServiceTypeList.value = JSON.parse(
        JSON.stringify(editingServiceTypeList.value),
      );
    } else {
      editingServiceTypeList.value = null;
      originalServiceTypeList.value = null;
    }
  },
  { immediate: true },
);

/**
 * Moves the service type with the specified ID to the top of the list.
 * @param serviceTypeId - The ID of the service type to move.
 */
function sendToTop(serviceTypeId: number): void {
  if (!editingServiceTypeList.value) {
    return;
  }
  const serviceTypeIndex = editingServiceTypeList.value.findIndex(
    (s) => s.serviceTypeId === serviceTypeId,
  );
  if (serviceTypeIndex === -1) {
    return;
  }
  const serviceType = editingServiceTypeList.value.splice(serviceTypeIndex, 1);
  editingServiceTypeList.value.unshift(serviceType[0]);
}

/**
 * Moves the service type with the specified ID to the bottom of the list.
 * @param serviceTypeId - The ID of the service type to move.
 */
function sendToBottom(serviceTypeId: number): void {
  if (!editingServiceTypeList.value) {
    return;
  }
  const serviceTypeIndex = editingServiceTypeList.value.findIndex(
    (s) => s.serviceTypeId === serviceTypeId,
  );
  if (serviceTypeIndex === -1) {
    return;
  }
  const serviceType = editingServiceTypeList.value.splice(serviceTypeIndex, 1);
  editingServiceTypeList.value.push(serviceType[0]);
}

function confirm(): void {
  if (!editingServiceTypeList.value) {
    return;
  }
  const updatedList: number[] = editingServiceTypeList.value.map((s) => {
    return s.serviceTypeId;
  });
  localStorage.setItem(
    LOCAL_STORAGE_DRIVER_LIST_ORDER,
    JSON.stringify(updatedList),
  );
  emit('updatedOrder', updatedList);
}

function restoreDefaults(): void {
  if (!originalServiceTypeList.value) {
    return;
  }
  localStorage.removeItem(LOCAL_STORAGE_DRIVER_LIST_ORDER);
  emit('restoreDefaults');
}
</script>

<style scoped>
.dashboard-card {
  cursor: move;
}
</style>
