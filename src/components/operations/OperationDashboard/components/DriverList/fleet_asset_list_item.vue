<template>
  <v-flex
    draggable="true"
    @dragstart="onDragStart($event, asset)"
    align-center
    class="fleet-asset-list-item"
    :class="[gridWidth, selected ? 'selected' : '']"
  >
    <v-flex @click="selectListItem(asset)">
      <!-- Fleet Item -->
      <v-layout
        class="fleet-item__label-container"
        :class="{
          stack: asset.associatedListItems.length > 1,
          online: stackStatus.online,
          allocatedWork: stackStatus.allocatedWork,
          onlineAllocatedWork: stackStatus.onlineAllocatedWork,
        }"
      >
        <!-- Fleet Label -->
        <span
          class="label-text fleet-label"
          :class="
            asset.type === SubcontractorEntityType.FLEET_ASSET ? '' : 'driver'
          "
        >
          <span
            class="icon-fleet"
            :class="
              asset.type === SubcontractorEntityType.FLEET_ASSET
                ? 'truck'
                : 'steering'
            "
          ></span>

          <span
            v-if="props.serviceTypeName !== 'service' && props.isInternalActive"
            class="internal-truck-class"
            >{{ asset.truckClass }}
          </span>
          {{ asset.displayName }}
        </span>
        <!-- Work Status Icon -->
        <span
          class="indicator-icon-container"
          :class="[
            asset.allocationAvailability === 0 ? 'fleet-availability-full' : '',
            asset.allocationAvailability === 1
              ? 'fleet-availability-partial'
              : '',
            asset.allocationAvailability === 2 ? 'fleet-availability-none' : '',
          ]"
        ></span>
        <!-- divider -->
        <div class="vl"></div>
        <!-- driver online status -->
        <span
          class="online-status-icon-container"
          v-if="!asset.isEquipmentHire && !asset.isOutsideHire"
        >
          <span
            v-if="
              activeGpsData(asset.activeItem?.lastLocationUpdate) &&
              asset.activeItem?.isOnline
            "
            class="icon-location online-status-icon"
          ></span>
          <span
            v-else-if="
              asset.activeItem?.isOnline &&
              !activeGpsData(asset.activeItem?.lastLocationUpdate)
            "
            class="icon-circle online-status-icon online"
          ></span>
          <span v-else class="icon-circle online-status-icon offline"></span>
        </span>
        <!-- default driver icon BLUE : TRUE -->
        <span
          class="icon-default-driver"
          :class="!asset.activeItem?.isDefault ? '' : 'is-default'"
        >
          <span
            class="icon-fleet"
            :class="
              asset.type === SubcontractorEntityType.DRIVER
                ? 'truck'
                : 'steering'
            "
          ></span>
        </span>
        <!-- Driver Name -->
        <span class="label-text driver-name-label" v-if="!asset.isEquipmentHire"
          >{{ asset.activeItem?.displayName ?? '' }}
        </span>
        <span v-else-if="asset.isEquipmentHire && asset.ownerName"
          >{{ asset.ownerName }}
        </span>
        <v-spacer></v-spacer>
        <!-- action buttons -->
        <span class="action-buttons">
          <div
            v-if="!asset.isEquipmentHire && !asset.isOutsideHire"
            class="view-icon message custom-btn"
            @click="
              openChatMessage(asset);
              selectListItem(asset);
            "
          >
            <span class="icon-message"></span>
          </div>

          <div
            class="view-icon eye custom-btn"
            @click="
              viewAssetInformation(asset);
              selectListItem(asset);
            "
          >
            <span class="icon-eye"></span>
          </div>

          <div
            v-if="asset.associatedListItems.length > 1"
            class="view-icon expand custom-btn"
            @click="(isExpanded = !isExpanded), selectListItem(asset)"
          >
            <span
              :class="isExpanded ? 'icon-arrow-up' : 'icon-arrow-down'"
            ></span>
          </div>
        </span>
      </v-layout>
      <!-- Expanded column associatedDrivers -->
      <div
        v-if="isExpanded && asset.associatedListItems.length > 1"
        id="expand-container"
      >
        <div
          class="custom-list"
          id="expand-contract"
          :class="{
            collapsed: !isExpanded,
            expanded: isExpanded,
          }"
        >
          <div
            class="custom-list-header"
            v-if="!asset.isEquipmentHire && !asset.isOutsideHire"
          >
            {{
              asset.type === SubcontractorEntityType.FLEET_ASSET
                ? `Set Pinned Driver`
                : `Set Pinned Vehicle`
            }}
          </div>
          <template v-if="!asset.isEquipmentHire && !asset.isOutsideHire">
            <div
              v-for="associatedEntity in asset.associatedListItems"
              :key="associatedEntity.entityId"
              class="custom-list-item"
              :class="{
                inactive:
                  asset.activeItem?.entityId === associatedEntity.entityId,
              }"
              @click="
                setFleetAssetActiveDriver(asset, associatedEntity.entityId);
                selectListItem(asset);
              "
            >
              <span
                v-if="associatedEntity.isPinned"
                class="icon-pin pin-icon"
              ></span>
              <span
                class="indicator-icon-container"
                :class="[
                  associatedEntity.allocationAvailability === 0
                    ? 'fleet-availability-full'
                    : '',
                  associatedEntity.allocationAvailability === 1
                    ? 'fleet-availability-partial'
                    : '',
                  associatedEntity.allocationAvailability === 2
                    ? 'fleet-availability-none'
                    : '',
                ]"
              ></span>
              <div class="vl"></div>
              <span
                v-if="!asset.isEquipmentHire && !asset.isOutsideHire"
                class="icon-circle online-status-icon"
                :class="associatedEntity.isOnline ? 'online' : 'offline'"
              ></span>
              <span
                class="icon-steering pr-1 pl-1"
                :class="!associatedEntity.isDefault ? 'grey' : 'accent'"
                style="font-size: 11px"
              ></span>
              <div class="custom-list-item-content">
                <div
                  class="custom-list-item-title"
                  :class="
                    associatedEntity.entityId === asset.activeItem?.entityId
                      ? 'pinned-bg'
                      : ''
                  "
                >
                  <span
                    :class="
                      associatedEntity.entityId === asset.activeItem?.entityId
                        ? 'pinned'
                        : 'accDriverName'
                    "
                  >
                    {{ associatedEntity.displayName }}
                  </span>
                  <span class="phone-txt pl-2">
                    <span
                      class="phone-txt"
                      :class="
                        asset.type === SubcontractorEntityType.DRIVER
                          ? 'truck'
                          : 'icon-phone'
                      "
                    ></span>
                    {{
                      associatedEntity.contactNumber
                        ? formatPhoneNumber(associatedEntity.contactNumber)
                        : ''
                    }}
                  </span>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </v-flex>
  </v-flex>
</template>

<script setup lang="ts">
import { formatPhoneNumber } from '@/helpers/StringHelpers/StringHelpers';
import type FleetAssetDriverListSummary from '@/interface-models/FleetAsset/FleetAssetDriverListSummary';
import type FleetAssetDriverPair from '@/interface-models/FleetAsset/FleetAssetDriverPair';
import { SubcontractorEntityType } from '@/interface-models/InvoiceAdjustment/EntityTypes/SubcontractorEntityType';
import moment from 'moment';
import { computed, ComputedRef, defineProps, ref, withDefaults } from 'vue';

const props = withDefaults(
  defineProps<{
    asset: FleetAssetDriverListSummary;
    serviceTypeName: string;
    selected: boolean;
    gridWidth: string;
    fleetAssetTrackingWindowOpen: boolean;
    serviceTypeId: number;
    isInternalActive: boolean;
  }>(),
  {
    selected: false,
    gridWidth: 'md6',
    fleetAssetTrackingWindowOpen: false,
    serviceTypeId: 0,
    isInternalActive: false,
    serviceTypeName: '',
  },
);
const isExpanded = ref(false);

const emit = defineEmits<{
  (
    event: 'selectListItem',
    payload: {
      fleetAssetId: string;
      driverId: string;
      type: 'truck' | 'trailer';
    },
  ): void;
  (event: 'viewAssetInformation', payload: FleetAssetDriverPair): void;
  (event: 'openChatMessage', payload: FleetAssetDriverPair): void;
  (event: 'setFleetAssetActiveDriver', payload: FleetAssetDriverPair): void;
}>();

/**
 * Called when clicking on the primary container div of the fleet asset list
 * item. Emits the selected fleet asset id and driver id to the parent
 * component, such that we can display their allocated work on the dashboard
 * @param fleetAssetId - The id of the fleet asset
 * @param driverId - The id of the active/pinned driver
 * @param type - either 'truck' or 'trailer'
 */
function selectListItem(asset: FleetAssetDriverListSummary) {
  if (asset.type === SubcontractorEntityType.FLEET_ASSET) {
    emit('selectListItem', {
      fleetAssetId: asset.entityId,
      driverId: asset.activeItem?.entityId || '',
      type: !asset.isEquipmentHire ? 'truck' : 'trailer',
    });
  } else {
    if (!asset.activeItem?.entityId) {
      return;
    }
    emit('selectListItem', {
      fleetAssetId: asset.activeItem.entityId,
      driverId: asset.entityId,
      type: !asset.isEquipmentHire ? 'truck' : 'trailer',
    });
  }
}

/**
 * Emits to parent that we want to view the asset information information dialog
 * for the provided fleet asset
 * @param fleetAsset - The fleet asset id to view information for
 * @param type - either 'truck' or 'trailer'
 */
function viewAssetInformation(asset: FleetAssetDriverListSummary) {
  let fleetAssetId = '';
  let driverId = '';
  // Check the type of the main entity and set fleetAssetId and driverId
  // accordingly. If the type is FLEET_ASSET, then the newEntityId is the
  // driverId, otherwise it is the fleetAssetId.
  if (asset.type === SubcontractorEntityType.FLEET_ASSET) {
    fleetAssetId = asset.entityId;
    driverId = asset.activeItem?.entityId || '';
  } else {
    fleetAssetId = asset.activeItem?.entityId || '';
    driverId = asset.entityId;
  }
  if (!fleetAssetId || !driverId) {
    console.warn(
      `Missing fleetAssetId or driverId in viewAssetInformation. fleetAssetId: ${fleetAssetId}, driverId: ${driverId}`,
    );
    return;
  }
  const payload: FleetAssetDriverPair = { fleetAssetId, driverId };
  emit('viewAssetInformation', payload);
}

/**
 * Emits to parent that we want to open the current fleet asset and driver
 * combination in the chat section of the dashboard.
 * @param driverId - The id of the driver to open the chat for
 * @param fleetAssetId - The id of the fleet asset to open the chat for
 */
function openChatMessage(asset: FleetAssetDriverListSummary) {
  let fleetAssetId = '';
  let driverId = '';
  // Check the type of the main entity and set fleetAssetId and driverId
  // accordingly. If the type is FLEET_ASSET, then the newEntityId is the
  // driverId, otherwise it is the fleetAssetId.
  if (asset.type === SubcontractorEntityType.FLEET_ASSET) {
    fleetAssetId = asset.entityId;
    driverId = asset.activeItem?.entityId || '';
  } else {
    fleetAssetId = asset.activeItem?.entityId || '';
    driverId = asset.entityId;
  }
  if (!fleetAssetId || !driverId) {
    console.warn(
      `Missing fleetAssetId or driverId in openChatMessage. fleetAssetId: ${fleetAssetId}, driverId: ${driverId}`,
    );
    return;
  }
  const payload: FleetAssetDriverPair = { fleetAssetId, driverId };
  emit('openChatMessage', payload);
}

/**
 * Sets the pinned driver for the provided fleet asset. Only available if a
 * fleet asset has more than one driver. Emits the driver id and fleet asset id
 * to the parent component, which is stored in state.
 * @param driverId - The id of the driver to set as the pinned driver
 * @param fleetAssetId - The id of the fleet asset to set the pinned driver for
 */
function setFleetAssetActiveDriver(
  asset: FleetAssetDriverListSummary,
  newEntityId: string,
) {
  let fleetAssetId = '';
  let driverId = '';
  // Check the type of the main entity and set fleetAssetId and driverId
  // accordingly. If the type is FLEET_ASSET, then the newEntityId is the
  // driverId, otherwise it is the fleetAssetId.
  if (asset.type === SubcontractorEntityType.FLEET_ASSET) {
    fleetAssetId = asset.entityId;
    driverId = newEntityId || asset.activeItem?.entityId || '';
  } else {
    fleetAssetId = newEntityId || asset.activeItem?.entityId || '';
    driverId = asset.entityId;
  }
  const payload: FleetAssetDriverPair = { fleetAssetId, driverId };
  emit('setFleetAssetActiveDriver', payload);
}

/**
 * Returns true if the provided timestamp is within 5 minutes of the current
 * time.
 * @param timestamp - The timestamp to check
 */
function activeGpsData(timestamp?: number | null) {
  if (!timestamp) {
    return false;
  }
  const currentTime = moment().valueOf();
  const gpsTimestamp = timestamp;
  const difference = Math.abs(currentTime - gpsTimestamp);
  return difference < moment.duration(10, 'minutes').asMilliseconds();
}

const stackStatus: ComputedRef<{
  online: boolean;
  allocatedWork: boolean;
  onlineAllocatedWork: boolean;
}> = computed(() => {
  const associatedDrivers = props.asset.associatedListItems.filter(
    (d) => d.entityId !== props.asset.activeItem?.entityId,
  );

  const online = associatedDrivers.some((driver) => driver.isOnline);
  const allocatedWork = associatedDrivers.some(
    (driver) =>
      driver.allocationAvailability === 1 ||
      driver.allocationAvailability === 2,
  );

  const onlineAllocatedWork = online && allocatedWork;

  return { online, allocatedWork, onlineAllocatedWork };
});

function onDragStart(event: DragEvent, asset: FleetAssetDriverListSummary) {
  // Save driver data in the drag event
  event.dataTransfer?.setData('application/json', JSON.stringify(asset));
}
</script>

<style scoped lang="scss">
.fleet-asset-list-item {
  position: relative;
  display: inline-block;
  padding-left: 12px;
  padding-bottom: 12px;
  box-sizing: border-box;
  /* Minimum width before it collapses to a new row */
  min-width: 384px;
  max-width: 100%;

  &.selected {
    .fleet-item__label-container {
      background-color: var(--hover-bg);
      border-color: $border-color;
    }
    .label-text {
      color: var(--text-color) !important;
    }
  }

  .fleet-item__label-container {
    align-items: center;
    justify-content: start;
    background-color: var(--background-color-300);
    border-radius: $border-radius-lg;
    border: 1.2px solid $translucent;
    padding: 2.5px;
    /* Stack Effect */
    &.stack {
      cursor: pointer;
      border-bottom: none !important;
      margin-bottom: 8px;
      box-shadow:
        0 1px 1px rgba(0, 0, 0, 0.15),
        0 10px 0 -5px var(--background-color-200),
        0 13px 2px -8px var(--background-color-650),
        0 10px 1px -4px rgba(0, 0, 0, 0.15),
        0 20px 0 -10px var(--background-color-200),
        0 20px 1px -9px hsla(0, 0%, 0%, 0.129);
      &.allocatedWork {
        box-shadow:
          12px 13px 1px -12px $warning,
          0 1px 1px rgba(0, 0, 0, 0.15),
          0 10px 0 -5px var(--background-color-200),
          0 13px 2px -8px var(--background-color-650),
          0 10px 1px -4px rgba(0, 0, 0, 0.15),
          0 20px 0 -10px var(--background-color-200),
          0 20px 1px -9px hsla(0, 0%, 0%, 0.129);
      }
      &.online {
        box-shadow:
          0 1px 1px rgba(0, 0, 0, 0.15),
          0 10px 0 -5px var(--background-color-200),
          0 10px 1px -4px rgba(0, 0, 0, 0.15),
          0 13px 1px -7px $success,
          0 20px 0 -10px var(--background-color-200),
          0 20px 1px -9px hsla(0, 0%, 0%, 0.129);
      }
      &.onlineAllocatedWork {
        box-shadow:
          12px 13px 1px -12px $warning,
          0 1px 1px rgba(0, 0, 0, 0.15),
          0 10px 0 -5px var(--background-color-200),
          0 10px 1px -4px rgba(0, 0, 0, 0.15),
          0 13px 0px -7px $success,
          0 20px 0 -10px var(--background-color-200),
          0 20px 1px -9px hsla(0, 0%, 0%, 0.129);
      }
    }
    .active {
      background-color: var(--hover-bg);
      box-shadow: none;
    }

    .label-text {
      text-transform: uppercase;

      &.fleet-label {
        font-weight: 500;
        white-space: nowrap;
        font-size: $font-size-12;
        color: var(--light-text-color);
        overflow: hidden;
        text-overflow: ellipsis;
        color: var(--light-text-color);
        padding-right: 3px;
        &.driver {
          min-width: 120px;
          max-width: 120px;
        }
      }
      &.driver-name-label {
        white-space: nowrap;
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: $font-size-12;
        color: var(--light-text-color);
      }
    }

    .online-status-icon-container {
      min-width: 15px;
      max-width: 15px;
      text-align: center;
      .online-status-icon {
        padding-bottom: 3.3px;
      }
      .online-status-icons {
        margin: 16.3px !important;
      }
    }
  }

  .pinned-bg {
    border-bottom: 1px solid var(--table-row);
  }
  .pinned {
    color: grey !important;
  }

  .action-buttons {
    display: inline-flex;
  }
  .view-icon {
    margin: 0;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    border-radius: 50px;

    &.custom-btn {
      margin: 1px;
      background: transparent;
      border: none;
      outline: none;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: var(--primary-light);
        transform: scale(1.05);
      }

      &:active {
        transform: scale(0.95);
      }

      span {
        display: inline-block;
        transition: color 0.2s ease;
      }
    }

    &.message,
    &.eye {
      padding-right: 1px;
      opacity: 0.1;
    }
    &.expand {
      opacity: 0.5;
    }
  }
  &:hover {
    cursor: pointer;

    .view-icon {
      opacity: 1;
    }
  }
}

// Generic icon base class for all icons
%fa-icon-base {
  display: inline-block;
  font-family: $font-awesome-family;
  font-weight: 600;
  vertical-align: middle;
}

// Icon classes
.icon-fleet {
  @extend %fa-icon-base;
  padding: 4px;
  &.truck:before {
    content: '\f4df'; // fad fa-truck-moving
    font-size: $font-size-11;
    color: $light-text-color;
  }
  &.steering:before {
    content: '\f622'; // fas fa-steering-wheel
    font-size: $font-size-11;
    color: $accent;
  }
}
.icon-location {
  @extend %fa-icon-base;
  &:before {
    content: '\f601'; // fas fa-location-dot
    font-size: $font-size-10;
  }
}
.icon-circle {
  @extend %fa-icon-base;
  color: #3e3e4e;
  &:before {
    content: '\f111'; // fas fa-circle
    font-size: $font-size-10;
  }
  &.online {
    color: #0dffa2;
  }
  &.offline {
    color: #3e3e4e;
  }
}
.icon-default-driver.is-default .icon-fleet,
.icon-steering.accent {
  color: var(--accent);
}
.icon-pin {
  @extend %fa-icon-base;
  margin-right: 2px;
  &:before {
    content: '\f08d'; // fas fa-thumbtack
    font-size: $font-size-12;
  }
}
.icon-eye {
  @extend %fa-icon-base;
  &:before {
    content: '\f06e'; // fas fa-eye
    font-size: $font-size-14;
  }
}
.icon-message {
  @extend %fa-icon-base;
  &:before {
    content: '\f27a'; // fas fa-comment-alt
    font-size: $font-size-14;
  }
}
.icon-arrow-up {
  @extend %fa-icon-base;
  &:before {
    content: '\f077'; // fas fa-arrow-up
    font-size: $font-size-12;
  }
}
.icon-arrow-down {
  @extend %fa-icon-base;
  &:before {
    content: '\f078'; // fas fa-arrow-down
    font-size: $font-size-12;
  }
}
.icon-phone {
  @extend %fa-icon-base;
  &:before {
    content: '\f095'; // fas fa-phone
    font-size: $font-size-14;
  }
}

.vl {
  height: 18px;
  border-left: 1px solid $translucent-light;
  opacity: 0.4;
  padding: 1.5px;
}

.internal-truck-class {
  font-weight: 700;
  font-size: $font-size-11;
  padding-left: 2px;
  color: $warning-type;
  font-family: $sub-font-family;
  letter-spacing: 2px;
  z-index: 2;
  border-radius: 20px;
  margin: 2px;
}

.pin-icon {
  position: absolute;
  left: 4px;
  transform: rotate(-20deg);
  color: var(--text-color);
}

// Custom list styles to replace v-list and v-list-tile
.custom-list {
  &.expanded {
    border-radius: 0px 0px 20px 20px;
    background-color: var(--hover-bg);
    border-top: none !important;
    position: relative;
    z-index: 100;
    margin: 0px 12px 6px 12px;
    padding: 0 12px 0 12px;

    .phone-txt {
      color: var(--light-text-color) !important;
    }
  }
}

.custom-list-header {
  padding: 2px;
  font-size: $font-size-12;
  font-weight: 600;
  color: var(--light-text-color);
  text-transform: uppercase;
  border-bottom: 1px solid var(--table-row);
}

.custom-list-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;

  &:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }

  &.inactive {
    pointer-events: none;
  }

  .custom-list-item-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .custom-list-item-title {
    display: flex;
    align-items: center;
    font-size: $font-size-12;
    color: var(--text-color);
  }
}

// Responsive Styles
@media (max-width: 1500px) {
  .fleet-asset-list-item {
    min-width: 340px !important;
    max-width: 340px !important;
  }

  .label-text {
    &.fleet-label {
      font-size: $font-size-10 !important;
    }
    &.driver-name-label {
      font-size: $font-size-10 !important;
    }
  }
}

@media (max-width: 1200px) {
  .fleet-asset-list-item {
    min-width: 300px !important;
    max-width: 300px !important;
  }

  .label-text {
    &.fleet-label {
      font-size: 9px !important;
    }
    &.driver-name-label {
      font-size: 9px !important;
    }
  }
}
</style>
