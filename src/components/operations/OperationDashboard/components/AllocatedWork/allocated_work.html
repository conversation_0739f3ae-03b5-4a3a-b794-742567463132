<div class="job-list-container">
  <div class="task-bar py-0 app-theme__center-content--header no-highlight">
    <span>Allocated Work</span>
    <span
      v-if="selectedFleetAndDriver || activeSettingsGroupType === 'showAllDrivers'"
      class="pl-2 text--primary"
    >
      <span v-if="activeSettingsGroupType === 'showAllDrivers'"
        >ALL DRIVERS</span
      >
      <span v-else class="text--primary"
        >{{selectedFleetAndDriver.fleetAsset.csrAssignedId}} -
        <span v-if="selectedFleetAndDriver.fleetAsset.registrationNumber">
          <v-icon class="pl-1 pr-1" size="15" color="grey lighten-1"
            >fal fa-car</v-icon
          >
          ({{selectedFleetAndDriver.fleetAsset.registrationNumber}})</span
        >
        <span class="driver-txt pl-1" v-if="!showDriverColumn"
          >- {{ selectedFleetAndDriver.driver.displayName }}</span
        ></span
      >
    </span>
    <span
      v-if="selectedFleetAndDriver.fleetAsset.registrationNumber"
      class="text--primary"
    >
      <v-icon class="pl-2 pr-1" size="14" color="grey lighten-1"
        >fal fa-phone</v-icon
      >
      <span class="driver-txt">({{selectedFleetAndDriver.driver.mobile}})</span>
    </span>
    <span v-if="vehicleSummaryInfo" class="pr-2 pl-4">
      <span
        v-for="(info, index) in vehicleSummaryInfo"
        :key="info"
        class="vehicle-summary-info"
      >
        {{info}}
      </span>
    </span>
    <v-spacer />
    <v-spacer />
    <div class="filter-icon">
      <OperationsSettingsDialog
        key="allocated-work-settings-dialog"
        buttonText="Table Settings"
        title="Allocated Work - Table Settings"
        @confirm="tableSettingsUpdated"
        :buttonDisabled="false"
        buttonColor="white"
        faIconName="fas fa-sliders-h"
        :isIcon="true"
        :settingsList="allocatedWorkSettings"
        :defaultSettingsList="allocatedWorkSetting"
      >
      </OperationsSettingsDialog>
    </div>
    <v-tooltip right>
      <template v-slot:activator="{ on }">
        <v-btn
          small
          flat
          v-on="on"
          icon
          @click="deselectSelectedFleetAndDriver"
          class="ma-0"
        >
          <v-icon size="16" color="grey lighten-1">fal fa-times</v-icon>
        </v-btn>
      </template>
      Clear selected Fleet Asset/Driver and hide Allocated Work table
    </v-tooltip>
  </div>
  <div class="driver-coordinates" v-if="recentGpsLocation">
    <span class="key-txt">
      <v-icon class="pl-2 pr-1" size="13">fal fa-compass</v-icon>Last GPS
      Location:
    </span>
    <span class="value-txt">{{ recentGpsLocation }}</span>
  </div>
  <div class="content">
    <div class="content-scrollable">
      <v-card flat>
        <v-data-table
          class="headers-only-table"
          :pagination.sync="pagination"
          :headers="headers"
          :items="[]"
          hide-actions
        >
          <template v-slot:no-data>
            <span></span>
          </template>
        </v-data-table>
      </v-card>
      <v-card
        flat
        class="serviceTypeJobs"
        v-for="category in massagedJobData"
        :key="category.type"
      >
        <v-layout class="service-name" align-center>
          <p class="px-4">{{category.longName}}</p>
        </v-layout>
        <v-data-table
          :headers="headers"
          :items="category.data"
          :pagination.sync="pagination"
          hide-actions
          item-key="jobId"
          ref="expandableDataTable"
          :data-key="category.type"
        >
          <template v-slot:items="props">
            <tr
              :class="[
            props.expanded ? 'table-row__expanded' : '',
            'jobList--table-row',
            props.index % 2 === 0 ? 'even-row' : 'odd-row'
          ]"
              class="jobList--table-row"
              @click="props.expanded = !props.expanded; setActiveJobInDashboardView(props.item.jobId, props.expanded); triggerRowCollapse(category.type)"
              style="cursor: pointer"
            >
              <td class="text-xs-left pl-2">
                <v-icon
                  class="job__visualindicators"
                  :class="props.item.customerContacted ? 'displayicon' : 'hidden'"
                  color="green accent-3"
                  size="12"
                >
                  fas fa-circle
                </v-icon>
                <v-icon
                  class="job__visualindicators"
                  :class="props.item.driverContacted ? 'displayicon' : 'hidden'"
                  color="blue accent-3"
                  size="12"
                >
                  fas fa-circle
                </v-icon>
                <v-icon
                  class="job__visualindicators"
                  :class="props.item.jobActioned ? 'displayicon' : 'hidden'"
                  color="yellow accent-3"
                  size="12"
                >
                  fas fa-circle
                </v-icon>
                <v-icon
                  class="job__visualindicators"
                  :class="props.item.jobDelayed ? 'displayicon' : 'hidden'"
                  color="red accent-3"
                  size="12"
                >
                  fas fa-circle
                </v-icon>
              </td>
              <td
                class="text-xs-left"
                :class="props.item.isRecurring ? 'recurring-job-id__highlight' : ''"
              >
                {{ props.item.displayId }}
              </td>
              <td class="text-xs-left" v-if="showDriverColumn">
                {{ props.item.driverName }}
              </td>
              <td class="text-xs-left">{{ props.item.clientName }}</td>
              <td class="text-xs-left">
                {{ props.item.from ? props.item.from.toUpperCase() : '-' }}
              </td>
              <td class="text-xs-left">
                {{ props.item.to ? props.item.to.toUpperCase() : '-' }}
              </td>
              <td class="text-xs-left">{{props.item.readableJobDate}}</td>
              <td class="text-xs-left">
                {{ props.item.numOfCompletedLegs }} of
                {{props.item.totalNumberOfLegs}}
              </td>
              <td class="text-xs-left">{{ props.item.service }}</td>
              <td class="text-xs-left">{{ props.item.status }}</td>
            </tr>
          </template>

          <template v-slot:expand="props">
            <job-list-action-buttons
              v-if="selectedJobDetails !== null && selectedJobDetails.jobId === props.item.jobId"
              :jobDetails="selectedJobDetails"
              :jobId="props.item.jobId"
              :isOutsideHire="props.item.isOutsideHire"
              :statusList="props.item.statusList"
              :driverId="props.item.driverId"
              :driverIsTripRate="props.item.driverIsTripRate"
              :clientRateTypeId="props.item.clientRateTypeId"
              :fleetAssetId="props.item.fleetAssetId"
              :clientId="props.item.clientId"
              :serviceTypeId="props.item.serviceTypeId"
              :jobDate="props.item.date"
              :key="props.item.status"
              :workStatus="props.item.workStatus"
            >
            </job-list-action-buttons>
          </template>
          <template v-slot:no-data>
            <v-layout justify-center>
              No jobs found. Please ensure you have a driver selected.
            </v-layout>
          </template>
        </v-data-table>
      </v-card>
    </div>
  </div>
</div>
