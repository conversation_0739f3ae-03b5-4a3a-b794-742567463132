import JobDetails from '@/interface-models/Jobs/JobDetails';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';

import { useOperationsStore } from '@/store/modules/OperationsStore';

import OperationsSettingsDialog from '@/components/common/ui-elements/operations_settings_dialog.vue';
import JobListActionButtons from '@/components/operations/OperationDashboard/components/JobList/job_list_action_buttons/job_list_action_buttons.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { returnStatusConfigFromId } from '@/helpers/StatusHelpers/StatusHelpers';
import Dimensions from '@/interface-models/Generic/Dimensions/Dimensions';
import OperationsDashboardSetting, {
  allocatedWorkDefaultSettings,
} from '@/interface-models/Generic/OperationScreenOptions/OperationsDashboardSetting';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import StatusConfig from '@/interface-models/Status/StatusConfig';
import { useRootStore } from '@/store/modules/RootStore';

import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { VPagination } from '@/interface-models/Generic/VPagination';
import { JobEventType } from '@/interface-models/Jobs/Event/JobEventType';
import { OperationJobSummary } from '@/interface-models/Jobs/OperationJobSummary';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useFleetMapStore } from '@/store/modules/FleetMapStore';
import { useGpsStore } from '@/store/modules/GpsStore';
import { useJobStore } from '@/store/modules/JobStore';
import { deepCopy } from '@/helpers/JsonHelpers/JsonHelpers';

interface TableCategory {
  type: string;
  longName: string;
  data: OperationJobSummary[];
}
@Component({ components: { JobListActionButtons, OperationsSettingsDialog } })
export default class AllocatedWork extends Vue {
  @Prop() public selectedJobDetails: JobDetails;
  @Prop({ default: '' }) public selectedFleetAssetId: string;
  @Prop({ default: '' }) public selectedDriverId: string;

  public jobStore = useJobStore();
  public fleetAssetStore = useFleetAssetStore();
  public operationsStore = useOperationsStore();
  public gpsStore = useGpsStore();
  public fleetMapStore = useFleetMapStore();

  public allocatedWorkSetting: OperationsDashboardSetting[] =
    allocatedWorkDefaultSettings;

  // store last gps location of driver
  public recentGpsLocation: string | null = '';

  get allocatedWorkSettings(): OperationsDashboardSetting[] {
    return deepCopy(this.operationsStore.dashboardSettings.allocatedWork);
  }
  get activeSettingsGroupType(): string {
    const radioOptions = this.allocatedWorkSettings.filter(
      (s) => s.groupId === 0,
    );
    const foundActive = radioOptions.find((s) => s.active);
    const activeType = foundActive
      ? foundActive.id
      : radioOptions[0]
        ? radioOptions[0].id
        : '';
    return activeType;
  }

  get selectedFleetAndDriver() {
    if (this.selectedFleetAssetId) {
      const foundFleetAsset =
        this.fleetAssetStore.getFleetAssetFromFleetAssetId(
          this.selectedFleetAssetId,
        );
      if (!foundFleetAsset) {
        return;
      }
      if (
        foundFleetAsset.associatedDrivers.length < 1 ||
        foundFleetAsset.isTrailer
      ) {
        return;
      }
      // Use selected driver id from operations module
      // Else use default driver
      // Else use first associatedDriver
      const driverId = this.selectedDriverId
        ? this.selectedDriverId
        : foundFleetAsset.defaultDriver
          ? foundFleetAsset.defaultDriver
          : foundFleetAsset.associatedDrivers[0];
      const foundDriver =
        useDriverDetailsStore().getDriverFromDriverId(driverId);
      if (!foundDriver) {
        return;
      }
      return {
        fleetAsset: foundFleetAsset,
        driver: foundDriver,
      };
    }
  }

  get vehicleSummaryInfo() {
    if (!this.selectedFleetAndDriver) {
      return;
    }
    const fleetAsset = this.selectedFleetAndDriver.fleetAsset;
    let vehiclePayloadWeight = '-';
    let vehicleDimensions = '-';

    vehiclePayloadWeight = fleetAsset.payload ? `${fleetAsset.payload}` : '-';
    const dim: Dimensions = fleetAsset.dimensions;
    vehicleDimensions = `${dim.length ? dim.length : '-'}(L) x ${
      dim.width ? dim.width : '-'
    }(W) x ${dim.height ? dim.height : '-'}(H)`;

    return [
      `Max Payload (kg): ${vehiclePayloadWeight}`,
      `Dimensions (m): ${vehicleDimensions}`,
    ];
  }

  public pagination: VPagination = {
    sortBy: 'jobId',
    descending: false,
    rowsPerPage: -1,
  };

  get filteredJobs(): OperationJobSummary[] {
    const jobStore = useJobStore();
    if (this.activeSettingsGroupType === 'showAllDrivers') {
      return jobStore.operationJobsList;
    } else if (this.activeSettingsGroupType === 'showAllWorkForTruck') {
      return jobStore.operationJobsList.filter(
        (job: OperationJobSummary) =>
          job.fleetAssetId === this.selectedFleetAssetId,
      );
    } else {
      return jobStore.operationJobsList.filter(
        (job: OperationJobSummary) =>
          job.fleetAssetId === this.selectedFleetAssetId &&
          job.driverId === this.selectedDriverId,
      );
    }
  }

  get headers(): TableHeader[] {
    const headers: TableHeader[] = [
      {
        text: 'Actions',
        align: 'left',
        sortable: false,
        width: '80',
        value: 'displayId',
        class: 'job-actions-table-header',
      },
      {
        text: 'Job no',
        align: 'left',
        sortable: true,
        value: 'displayId',
        width: '80',
        class: 'job-actions-table-header',
      },
      {
        text: 'Client',
        align: 'left',
        sortable: true,
        value: 'clientName',
        width: '200',
        class: 'allocated-job-table-header',
      },

      {
        text: 'From',
        align: 'left',
        sortable: true,
        value: 'from',
        width: '130',
        class: 'allocated-job-table-header',
      },
      {
        text: 'To',
        align: 'left',
        sortable: true,
        value: 'to',
        width: '130',
        class: 'allocated-job-table-header',
      },
      {
        text: 'Booked For',
        align: 'left',
        sortable: true,
        value: 'readableJobDate',
        width: '100',
        class: 'allocated-job-table-header',
      },
      {
        text: 'Completed Legs',
        align: 'left',
        sortable: true,
        value: 'numOfCompletedLegs',
        width: '120',
        class: 'allocated-job-table-header',
      },
      {
        text: 'Service',
        align: 'left',
        sortable: true,
        value: 'service',
        width: '120',
        class: 'allocated-job-table-header',
      },
      {
        text: 'Status',
        align: 'left',
        sortable: true,
        value: 'status',
        width: '120',
        class: 'allocated-job-table-header',
      },
    ];
    if (this.showDriverColumn) {
      const driverNameHeader: TableHeader = {
        text: 'Driver',
        align: 'left',
        sortable: true,
        value: 'driverName',
        width: '100',
        class: 'allocated-job-table-header',
      };
      headers.splice(1, 0, driverNameHeader);
    }
    return headers;
  }

  get showDriverColumn() {
    return (
      this.activeSettingsGroupType === 'showAllDrivers' ||
      this.activeSettingsGroupType === 'showAllWorkForTruck'
    );
  }

  get massagedJobData(): TableCategory[] {
    const sortedJobList: TableCategory[] = this.sortFilteredJobs(
      this.filteredJobs,
    );
    return sortedJobList;
  }
  // Handle emit from settings dialog component
  public tableSettingsUpdated(settingsList: OperationsDashboardSetting[]) {
    this.operationsStore.updateDashboardSettingsAllocatedWork(settingsList);
  }

  // Handle emit from settings dialog component
  public isSettingActive(settingId: string): boolean {
    const settings = this.allocatedWorkSettings;
    const foundSetting = settings.find((s) => s.id === settingId);
    return foundSetting !== undefined && foundSetting.active;
  }

  public sortFilteredJobs(jobList: OperationJobSummary[]): TableCategory[] {
    const categoryList: TableCategory[] = [];

    const showPreallocatedWork = this.isSettingActive('showPreallocatedWork');
    const showAllocatedWork = this.isSettingActive('showAllocatedWork');
    const showInProgressWork = this.isSettingActive('showInProgressWork');
    const showCompletedWork = this.isSettingActive('showCompletedWork');
    const showReviewedWork = this.isSettingActive('showReviewedWork');

    if (showAllocatedWork || showPreallocatedWork || showInProgressWork) {
      let workStatusMin: WorkStatus = WorkStatus.ALLOCATED;
      let workStatusMax: WorkStatus = WorkStatus.ACCEPTED;

      if (showPreallocatedWork) {
        workStatusMin = WorkStatus.PREALLOCATED;
      }
      if (showInProgressWork) {
        workStatusMax = WorkStatus.IN_PROGRESS;
      }

      const result: OperationJobSummary[] = jobList.filter(
        (job) =>
          job.workStatus >= workStatusMin && job.workStatus <= workStatusMax,
      );

      const category: TableCategory = {
        type: 'ALLOCATED_WORK_TYPE',
        longName: 'Allocated Work',
        data: result,
      };
      categoryList.push(category);
    }
    if (showCompletedWork || showReviewedWork) {
      let result: OperationJobSummary[] = [];
      if (showCompletedWork && !showReviewedWork) {
        result = jobList.filter(
          (job) => job.workStatus === WorkStatus.DRIVER_COMPLETED,
        );
      } else if (showReviewedWork && !showCompletedWork) {
        result = jobList.filter(
          (job) => job.workStatus === WorkStatus.REVIEWED,
        );
      } else {
        result = jobList.filter(
          (job) =>
            job.workStatus >= WorkStatus.DRIVER_COMPLETED &&
            job.workStatus <= WorkStatus.REVIEWED,
        );
      }
      const category: TableCategory = {
        type: 'COMPLETED_WORK_TYPE',
        longName: 'Completed Work',
        data: result,
      };
      categoryList.push(category);
    }

    return categoryList;
  }

  // emit to parent to trigger collapse of all rows in job_list and allocated_work tables where data-key is not equal to selectedType
  public triggerRowCollapse(selectedType: string) {
    this.$emit('closeAllExpandedRows', selectedType);
  }

  // Minimise all expanded rows in other tables (other than the table for the name provided)
  // ie. If you expand a row in the 1T section in the table, it should minimize rows in all other tables and expand the selected
  public closeExpandedRows(selectedType: string) {
    const foundDataTables = this.$refs.expandableDataTable as any[];
    if (!foundDataTables) {
      return;
    }
    const filteredTables = foundDataTables.filter(
      (dt) => dt.$attrs['data-key'] !== selectedType,
    );

    filteredTables.forEach((table: any) => {
      if (table.expanded) {
        Object.keys(table.expanded).forEach((key) => {
          table.expanded[key] = false;
        });
      }
    });
  }

  // returns the time for the a jobs first pud item.
  public bookedFor(pudItems: PUDItem[]): string {
    if (pudItems[0]) {
      return returnFormattedDate(pudItems[0].epochTime, 'DD/MM/YY HH:mm');
    } else {
      return '-';
    }
  }

  public returnStatusConfig(sid: number): StatusConfig | undefined {
    const allStatusList: StatusConfig[] = useRootStore().statusTypeList;
    const statusItem = returnStatusConfigFromId(allStatusList, sid);

    return statusItem;
  }

  public setActiveJobInDashboardView(jobId: number, isExpanded: boolean) {
    // if the row item is opened we get the full job details. If the user is just closing the expanded panel we do not require a request for the full job details as it will already exist
    if (isExpanded) {
      this.operationsStore.setSelectedJobId(jobId);
      this.operationsStore.getFullJobDetails(jobId);
    }
  }

  /**
   * Deselects the selected fleet and driver by setting the values in the store
   * to empty strings.
   */
  public deselectSelectedFleetAndDriver() {
    this.operationsStore.setSelectedFleetAssetId('');
    this.operationsStore.setSelectedDriverId('');
  }

  public deallocateJob(jobId: number): void {
    this.jobStore.updateJobStatus(jobId, JobEventType.DeallocateJob);
  }

  public viewSelectedJobInReview(jobId: number) {
    const jobDetails: OperationJobSummary | undefined = this.filteredJobs.find(
      (job) => job.jobId === jobId,
    );
    if (!jobDetails) {
      return;
    }
    if (jobDetails.workStatus === WorkStatus.REVIEWED) {
      this.jobStore.updateJobStatus(jobId, JobEventType.CompletedJob);
    }
  }

  // watch for changes in selectedFleetAssetId and fetch recent GPS location
  @Watch('selectedFleetAssetId', { immediate: true })
  async onSelectedFleetAssetIdChanged() {
    this.recentGpsLocation = await this.gpsStore.returnFormattedGpsLocation(
      this.selectedFleetAssetId,
    );
  }
}
