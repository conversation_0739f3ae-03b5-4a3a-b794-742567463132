.job-list-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: var(--background-color-200);
  border-radius: $border-radius-sm;
  border: 1px solid $translucent;
  resize: vertical;

  .content {
    height: calc(100% - 25px);
    position: relative;
    overflow: hidden;
    display: flex;

    .settings-panel {
      min-height: 100%;
      background-color: var(--background-color-200);

      &.slideout {
        border-right: 3px solid darken($app-dark-primary-200, 2%);
      }

      &.slidein {
        overflow-x: hidden;
        white-space: nowrap;
      }
    }

    .content-scrollable {
      max-height: 100%;
      flex-direction: column;
      overflow-y: auto;
      display: flex;
      flex-grow: 1;
    }
  }

  .text--primary {
    span {
      color: var(--highlight) !important;
    }
  }
}

.driver-coordinates {
  background-color: var(--hover-bg);
  height: 26px;
  padding: 2px 12px;
  font-size: $font-size-12;
  // font-weight: 600;
  display: flex;
  justify-content: left; /* center horizontally */
  align-items: center;

  .key-txt {
    color: $success;
    margin-right: 12px;
    .v-icon {
      color: $success;
      margin-right: 4px;
      font-weight: 600;
      margin-bottom: 1px;
    }
  }
  .value-txt {
    color: var(--light-text-color);
  }
}

.job__visualindicators {
  visibility: visible;
  margin-left: 1px;

  &.hidden {
    visibility: hidden;
  }
}

.task-bar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  inline-size: 100%;
  overflow: hidden;
  .driver-txt {
    text-wrap: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .vehicle-summary-info {
    font-size: $font-size-11;
    text-transform: none;
    color: var(--light-text-color) !important;
    font-weight: 400;
    padding-right: 6px;
  }
  .filter-icon {
    margin-left: 20px;
    margin-right: 20px;
  }
}

.theme--dark.v-table {
  color: var(--text-color) !important;
}

.jobList--table-row {
  color: var(--text-color) !important;
  position: relative;
  &:hover {
    background-color: var(--background-color-300) !important;
  }
  &.even-row {
    background-color: var(--background-color-450);
    &:hover {
      background-color: var(--background-color-300) !important;
    }
  }
}

.table-row__expanded {
  td {
    background-color: var(--hover-bg) !important;
  }
}

/* Table body text color */

.service-name {
  height: 26px;
  background-color: var(--background-color-200);
  position: relative;
  z-index: 1;
  border-radius: 0 !important;

  p {
    margin: 0;
    line-height: 1;
    font-size: $font-size-12;
    text-transform: uppercase;
    font-weight: 600;
    color: var(--bg-light);
  }
}
