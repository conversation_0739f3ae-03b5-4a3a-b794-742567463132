<template>
  <section class="adjust-service-details-dialog">
    <JobContentDialog
      :showDialog.sync="showDialog"
      :title="`job #${jobDetails.displayId} - Adjust Job Service Details`"
      width="70%"
      contentPadding="pa-0"
      :hideDriverChat="true"
      :isContentDialog="true"
      :showActions="false"
      class="v-dialog-custom"
      :jobDetails="jobDetails"
      :clientDetails="targetClientDetails || clientDetails"
      @cancel="showDialog = false"
      :sidePanelDoesNotExpandContent="true"
    >
      <v-flex md12>
        <v-layout row wrap class="app-theme__center-content--body">
          <v-flex class="md12">
            <v-layout
              row
              wrap
              class="body-scrollable--65 body-min-height--65"
              pa-3
            >
              <v-flex>
                <v-layout
                  v-if="isRequestingJobRateData"
                  align-center
                  justify-center
                >
                  <v-progress-circular
                    indeterminate
                    color="white"
                  ></v-progress-circular>
                </v-layout>
                <v-form ref="adjustServiceDetailsForm" v-else>
                  <v-flex md12>
                    <v-layout align-center>
                      <v-layout column>
                        <h3>Adjust Service Details</h3>
                        <h4>
                          Update job #{{ jobDetails.displayId }}'s client,
                          service and rate details.
                        </h4>
                      </v-layout>
                    </v-layout>
                    <v-divider class="my-2"></v-divider>
                    <v-layout v-if="rateRetrievalErrors.length === 0">
                      <v-flex md12 px-4 py-3>
                        <table class="simple-data-table">
                          <thead>
                            <tr>
                              <th>Entity</th>
                              <th>Name</th>
                              <th></th>
                              <th>New</th>
                              <th>Input</th>
                            </tr>
                          </thead>
                          <tbody>
                            <!-- Client Row -->
                            <tr>
                              <td colspan="5">
                                <v-layout row wrap>
                                  <v-flex md12 pb-1
                                    ><v-layout align-center>
                                      <h5 class="subheader--bold">
                                        Change Client
                                      </h5>
                                      <v-flex>
                                        <v-divider></v-divider>
                                      </v-flex>
                                    </v-layout>
                                  </v-flex>
                                  <v-flex md12 class="dialoglineitem">
                                    <v-layout row align-center>
                                      <v-flex md4>
                                        <v-layout row align-center> </v-layout>
                                      </v-flex>
                                      <v-flex md8>
                                        <v-layout column>
                                          <v-layout row align-center>
                                            <v-flex md4 pr-3> </v-flex>
                                            <v-flex md8> </v-flex>
                                          </v-layout>
                                        </v-layout>
                                      </v-flex>
                                    </v-layout>
                                  </v-flex>
                                </v-layout>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <h6>Current:</h6>
                              </td>
                              <td>
                                <span
                                  class="dialoglineitem__value accent-text--primary"
                                  >{{
                                    jobDetails
                                      ? `${jobDetails.client.clientName}`
                                      : '-'
                                  }}</span
                                >
                                <span
                                  class="pl-2"
                                  v-if="
                                    originalJobValues.clientId !==
                                    targetJobValues.clientId
                                  "
                                >
                                  <InformationTooltip
                                    :right="true"
                                    :tooltipType="HealthLevel.WARNING"
                                  >
                                    <v-layout slot="content" row wrap>
                                      <v-flex md12>
                                        <ul>
                                          <li
                                            v-if="jobDetails.client.id === 'CS'"
                                          >
                                            Please contact Head Office to change
                                            the Client of this
                                            <strong>CASH SALE</strong> job.
                                          </li>
                                          <li
                                            v-if="jobDetails.client.id === 'CS'"
                                          >
                                            If the job is changed to a different
                                            Client, all current
                                            <strong>CASH SALE</strong>
                                            information will be permanently
                                            lost.
                                          </li>
                                          <li>
                                            Existing notes on jobs will remain
                                            on jobs. If any existing notes are
                                            client specific the visibility of
                                            the note will need to be changed
                                            manually
                                          </li>
                                        </ul>
                                      </v-flex>
                                    </v-layout>
                                  </InformationTooltip>
                                </span>
                              </td>
                              <td>
                                <v-icon class="pl-3 pr-2" size="22"
                                  >fal fa-arrow-right</v-icon
                                >
                              </td>
                              <td>
                                <h6>New Client:</h6>
                              </td>
                              <td>
                                <v-autocomplete
                                  v-model="targetJobValues.clientId"
                                  :items="activeClientList"
                                  item-value="clientId"
                                  item-text="clientSearchCriteria"
                                  color="light-blue"
                                  label="Client Select"
                                  @change="getSelectedClientDetails"
                                  :rules="[validate.required]"
                                  browser-autocomplete="off"
                                  :disabled="disableClientSelect"
                                  class="v-solo-custom"
                                  solo
                                  flat
                                  auto-select-first
                                  :messages="
                                    isRecurringJob
                                      ? 'Note: Client cannot be changed for Permanent Jobs'
                                      : originalJobValues.clientId !==
                                          targetJobValues.clientId
                                        ? 'Notes reflect selected client'
                                        : ''
                                  "
                                >
                                  <template #selection="data">
                                    <span>{{
                                      data.item.clientDisplayName
                                    }}</span>
                                  </template>
                                  <template #item="data">
                                    <span
                                      >{{ data.item.clientDisplayName }}
                                    </span>
                                  </template>
                                </v-autocomplete>
                              </td>
                            </tr>
                          </tbody>
                          <!-- Service Type Row -->
                          <tbody>
                            <tr>
                              <td colspan="5">
                                <v-layout align-center>
                                  <h5 class="subheader--bold">
                                    Change Job Service Type
                                  </h5>
                                  <v-flex>
                                    <v-divider></v-divider>
                                  </v-flex>
                                  <span class="pl-2" v-if="isAllocated">
                                    <v-switch
                                      color="light-blue"
                                      label="Sync Client/Fleet Asset"
                                      v-model="syncServiceTypeIdsController"
                                    >
                                    </v-switch>
                                  </span>
                                  <span class="pl-2 pb-2" v-if="isAllocated">
                                    <InformationTooltip :left="true">
                                      <v-layout slot="content" row wrap>
                                        <v-flex md12 pb-1>
                                          <strong class="pr-1">ON: </strong>When
                                          pricing this job, the Fleet Asset will
                                          be paid using the same Service Type as
                                          the Client.
                                        </v-flex>
                                        <v-flex md12>
                                          <strong class="pr-1">OFF: </strong
                                          >Turn this off if you wish to pay the
                                          Fleet Asset for a different Service
                                          Type. For example, the Job/Client
                                          Service Type is 8T, but you wish to
                                          pay the Fleet Asset using their 6T
                                          rate.
                                        </v-flex>
                                      </v-layout>
                                    </InformationTooltip>
                                  </span>
                                </v-layout>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <h6 class="pr-3">
                                  {{
                                    syncServiceTypeIdsController
                                      ? 'Current'
                                      : 'Client'
                                  }}:
                                </h6>
                              </td>
                              <td>
                                <span
                                  class="dialoglineitem__value accent-text--primary"
                                >
                                  {{
                                    jobDetails
                                      ? jobDetails.serviceTypeLongName
                                      : '-'
                                  }}
                                </span>
                                <span class="pl-1">
                                  <InformationTooltip :right="true">
                                    <v-layout slot="content">
                                      The primary Job Service Type. This is the
                                      service that the Client will be charged
                                      against, as well as the Service Type that
                                      will appear on invoices and reports.
                                    </v-layout>
                                  </InformationTooltip>
                                </span>
                              </td>
                              <td>
                                <v-icon class="pl-3 pr-2" size="22"
                                  >fal fa-arrow-right</v-icon
                                >
                              </td>
                              <td>
                                <h6>New Service Type:</h6>
                              </td>
                              <td>
                                <v-autocomplete
                                  key="service-type-select"
                                  v-model="targetServiceTypeIdController"
                                  :items="validServiceTypes"
                                  :disabled="isRequestingClientServiceRates"
                                  item-text="optionSelectName"
                                  item-value="serviceTypeId"
                                  color="light-blue"
                                  class="v-solo-custom"
                                  solo
                                  flat
                                  label="Service Type"
                                  :rules="
                                    syncServiceTypeIdsController
                                      ? [
                                          validate.required,
                                          clientRateValidation,
                                          fleetAssetRateValidation,
                                        ]
                                      : [
                                          validate.required,
                                          clientRateValidation,
                                        ]
                                  "
                                >
                                </v-autocomplete>
                              </td>
                            </tr>
                            <tr
                              v-show="!syncServiceTypeIdsController"
                              v-if="isAllocated"
                            >
                              <td><h6>Fleet Asset:</h6></td>
                              <td>
                                <span
                                  class="dialoglineitem__value accent-text--primary"
                                >
                                  {{
                                    jobDetails
                                      ? jobDetails.fleetAssetServiceTypeLongName
                                      : '-'
                                  }}
                                </span>
                                <span class="pl-1">
                                  <InformationTooltip :right="true">
                                    <v-layout slot="content"
                                      >The Service Type that the Fleet Asset
                                      will be priced against.
                                    </v-layout>
                                  </InformationTooltip>
                                </span>
                              </td>
                              <td>
                                <v-icon class="pl-3 pr-2" size="22"
                                  >fal fa-arrow-right</v-icon
                                >
                              </td>
                              <td>
                                <h6>New Service Type:</h6>
                              </td>
                              <td>
                                <v-autocomplete
                                  key="service-type-select-fleet"
                                  v-model="
                                    targetJobValues.fleetAssetServiceTypeId
                                  "
                                  :items="validServiceTypes"
                                  :disabled="isRequestingClientServiceRates"
                                  item-text="optionSelectName"
                                  item-value="serviceTypeId"
                                  color="light-blue"
                                  class="v-solo-custom"
                                  solo
                                  flat
                                  label="Service Type"
                                  @change="calculateFleetRateToApply"
                                  :rules="[
                                    validate.required,
                                    fleetAssetRateValidation,
                                  ]"
                                >
                                </v-autocomplete>
                              </td>
                            </tr>
                          </tbody>
                          <!-- Rate Type Row -->
                          <tbody>
                            <tr>
                              <td colspan="5">
                                <v-layout align-center>
                                  <h5 class="subheader--bold">
                                    Change Job Rate Type
                                  </h5>
                                  <v-flex>
                                    <v-divider></v-divider>
                                  </v-flex>
                                  <span class="pl-2" v-if="isAllocated">
                                    <v-switch
                                      color="light-blue"
                                      label="Sync Client/Fleet Asset"
                                      v-model="syncRateTypeIdsController"
                                      :disabled="
                                        targetFleetAssetRateAppliedType ===
                                        'FALLBACK_RATE'
                                      "
                                    >
                                    </v-switch>
                                  </span>
                                  <span class="pl-2 pb-2" v-if="isAllocated">
                                    <InformationTooltip :left="true">
                                      <v-layout slot="content" row wrap>
                                        <v-flex md12 pb-1>
                                          <strong class="pr-1">ON: </strong>The
                                          Client and Fleet Asset will be
                                          charged/paid using the same Rate Type.
                                        </v-flex>
                                        <v-flex md12>
                                          <strong class="pr-1">OFF: </strong
                                          >Turn this off if you wish to price
                                          the job using a different Rate Type
                                          for the Fleet Asset. This is useful if
                                          you wish to apply a TIME RATE to the
                                          Fleet Asset for the purposes of
                                          pricing.
                                        </v-flex>
                                      </v-layout>
                                    </InformationTooltip>
                                  </span>
                                </v-layout>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <h6 class="pr-3">
                                  {{
                                    syncRateTypeIdsController
                                      ? 'Current'
                                      : 'Client'
                                  }}:
                                </h6>
                              </td>
                              <td>
                                <span
                                  class="dialoglineitem__value accent-text--primary"
                                >
                                  {{
                                    jobDetails ? jobDetails.rateTypeName : '-'
                                  }}
                                </span>
                                <span class="pl-1">
                                  <InformationTooltip :right="true">
                                    <v-layout slot="content">
                                      The primary Job Rate Type. This is the
                                      rate that the Client will be charged
                                      against, as well as the Rate Type that the
                                      job will appear as on invoices and
                                      reports.
                                    </v-layout>
                                  </InformationTooltip>
                                </span>
                              </td>
                              <td>
                                <v-icon class="pl-3 pr-2" size="22"
                                  >fal fa-arrow-right</v-icon
                                >
                              </td>
                              <td>
                                <h6>New Rate Type:</h6>
                              </td>
                              <td>
                                <v-select
                                  key="rate-type-select"
                                  :items="availableTargetRateTypes"
                                  label="Rate Type"
                                  item-text="longName"
                                  item-value="rateTypeId"
                                  v-model="targetRateTypeIdController"
                                  :disabled="isRequestingClientServiceRates"
                                  :rules="[
                                    validate.required,
                                    clientRateValidation,
                                    fleetAssetRateValidation,
                                    zoneRateValidation,
                                    unitRateValidation,
                                  ]"
                                  class="v-solo-custom"
                                  color="light-blue"
                                  solo
                                  flat
                                />
                              </td>
                            </tr>
                            <tr
                              v-show="!syncRateTypeIdsController"
                              v-if="isAllocated"
                            >
                              <td>
                                <h6>Fleet Asset:</h6>
                              </td>
                              <td>
                                <span
                                  class="dialoglineitem__value accent-text--primary"
                                >
                                  {{
                                    jobDetails
                                      ? jobDetails.fleetAssetRateTypeName
                                      : '-'
                                  }}
                                </span>
                                <span class="pl-1">
                                  <InformationTooltip :right="true">
                                    <v-layout slot="content"
                                      >The Rate Type that the Fleet Asset will
                                      be priced against.
                                    </v-layout>
                                  </InformationTooltip>
                                </span>
                              </td>
                              <td>
                                <v-icon class="pl-3 pr-2" size="22"
                                  >fal fa-arrow-right</v-icon
                                >
                              </td>
                              <td>
                                <h6>New Rate Type:</h6>
                              </td>
                              <td>
                                <v-select
                                  :key="`rate-type-select-fleet`"
                                  :items="fleetAssetRateTypesList"
                                  label="Rate Type"
                                  item-text="longName"
                                  item-value="rateTypeId"
                                  v-model="targetJobValues.fleetAssetRateTypeId"
                                  @change="calculateFleetRateToApply"
                                  :disabled="isRequestingClientServiceRates"
                                  :rules="
                                    fleetAssetsUnsyncedRateListSelectValidationRules
                                  "
                                  class="v-solo-custom"
                                  color="light-blue"
                                  solo
                                  flat
                                />
                              </td>
                            </tr>
                          </tbody>

                          <!-- ========================= -->
                          <!--  TRIP/QUOTED RATE DETAILS -->
                          <!-- ========================= -->
                          <tbody
                            v-if="
                              targetRateTypeIdController === JobRateType.TRIP ||
                              targetJobValues.fleetAssetRateTypeId ===
                                JobRateType.TRIP
                            "
                          >
                            <tr>
                              <td colspan="5">
                                <v-layout align-center>
                                  <h5 class="subheader--bold">
                                    Add/Edit Quoted Rate Details
                                  </h5>
                                  <v-flex>
                                    <v-divider></v-divider>
                                  </v-flex>
                                </v-layout>
                              </td>
                            </tr>
                            <tr>
                              <td colspan="5">
                                <v-layout row>
                                  <v-flex md6 class="pr-1">
                                    <TripRateBooking
                                      v-if="targetRateTypeIdController === 6"
                                      :title="'Client'"
                                      :enableTripRate.sync="
                                        tripRateInformation.client
                                      "
                                      :fuelSurchargeRate.sync="
                                        tripRateInformation.clientFuelSurcharge
                                      "
                                      :rateAmount.sync="
                                        tripRateInformation.clientRate
                                      "
                                      :showSwitch="false"
                                      @tripRateUpdated="
                                        calculateClientRateToApply
                                      "
                                    />
                                  </v-flex>
                                  <v-flex
                                    md6
                                    class="pl-1"
                                    v-if="
                                      targetJobValues.fleetAssetRateTypeId === 6
                                    "
                                  >
                                    <TripRateBooking
                                      :title="'Driver'"
                                      :enableTripRate.sync="
                                        tripRateInformation.fleetAsset
                                      "
                                      :fuelSurchargeRate.sync="
                                        tripRateInformation.fleetAssetFuelSurcharge
                                      "
                                      :rateAmount.sync="
                                        tripRateInformation.fleetAssetRate
                                      "
                                      :showSwitch="false"
                                      @tripRateUpdated="
                                        calculateFleetRateToApply
                                      "
                                    />
                                  </v-flex>
                                </v-layout>
                              </td>
                            </tr>
                          </tbody>

                          <!-- ================== -->
                          <!--  ZONE RATE DETAILS -->
                          <!-- ================== -->
                          <tbody
                            v-if="
                              targetRateTypeIdController === JobRateType.ZONE &&
                              targetZoneRateInformation !== null &&
                              clientRateToApply &&
                              isZoneRateTypeObject(
                                clientRateToApply.rateTypeId,
                                clientRateToApply.rateTypeObject,
                              ) &&
                              clientRateToApply.rateTypeObject.length
                            "
                          >
                            <tr>
                              <td colspan="5">
                                <v-layout align-center>
                                  <h5 class="subheader--bold">
                                    Add/Edit Zone Rate Details
                                  </h5>
                                  <v-flex>
                                    <v-divider></v-divider>
                                  </v-flex>
                                </v-layout>
                              </td>
                            </tr>
                            <tr>
                              <td colspan="5">
                                <JobZoneRateDetails
                                  :zones="clientRateToApply.rateTypeObject"
                                  :zoneRateInformation="
                                    targetZoneRateInformation
                                  "
                                  @updateZoneRateInformation="
                                    updateZoneRateInformation
                                  "
                                >
                                </JobZoneRateDetails>
                              </td>
                            </tr>
                          </tbody>

                          <!-- ================== -->
                          <!--  UNIT RATE DETAILS -->
                          <!-- ================== -->
                          <tbody
                            v-if="
                              targetRateTypeIdController === JobRateType.UNIT &&
                              targetUnitRateInformation !== null &&
                              clientRateToApply &&
                              isUnitRateTypeObject(
                                clientRateToApply.rateTypeId,
                                clientRateToApply.rateTypeObject,
                              ) &&
                              clientRateToApply.rateTypeObject.length
                            "
                          >
                            <tr>
                              <td colspan="5">
                                <v-layout align-center>
                                  <h5 class="subheader--bold">
                                    Add/Edit Unit Rate Details
                                  </h5>
                                  <v-flex>
                                    <v-divider></v-divider>
                                  </v-flex>
                                </v-layout>
                              </td>
                            </tr>
                            <tr>
                              <td colspan="5">
                                <JobUnitRateDetails
                                  :unitRates="clientRateToApply.rateTypeObject"
                                  :unitRateInformation="
                                    targetUnitRateInformation
                                  "
                                  @updateUnitRateInformation="
                                    updateUnitRateInformation
                                  "
                                >
                                </JobUnitRateDetails>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </v-flex>
                    </v-layout>
                    <v-layout v-else justify-center>
                      <v-flex offset-md1 md8>
                        <v-alert type="warning" :value="true">
                          <v-flex md12>
                            <v-layout pb-2>
                              <span style="font-weight: 600">
                                The following issue(s) must be resolved before
                                this job may be adjusted:
                              </span>
                            </v-layout>
                            <v-layout
                              pt-1
                              v-for="(error, index) in rateRetrievalErrors"
                              :key="error"
                            >
                              <span class="pl-3 pr-2">{{ index + 1 }}.</span>
                              {{ error }}
                            </v-layout>
                          </v-flex>
                        </v-alert>
                      </v-flex>
                    </v-layout>
                  </v-flex>
                </v-form>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-divider></v-divider>
            <v-layout row align-center>
              <v-btn @click="showDialog = false" outline color="red">
                Cancel
              </v-btn>
              <v-spacer></v-spacer>
              <v-layout row align-center>
                <div v-if="clientRateToApply" class="d-flex align-center">
                  <h2 class="ma-0">Client Rate</h2>
                  <v-tooltip
                    :z-index="2147483647"
                    top
                    v-if="clientRateToApply"
                    max-width="60%"
                  >
                    <template v-slot:activator="{ on }">
                      <v-icon
                        size="14"
                        v-on="on"
                        color="info"
                        v-if="
                          clientRateToApply ||
                          targetJobValues.jobRateTypeId === 6
                        "
                        style="pointer-events: auto; cursor: pointer"
                        class="pl-1"
                      >
                        fa fa-info-circle
                      </v-icon>
                    </template>
                    <AppliedRateDetails
                      :clientCommonAddresses="clientCommonAddressList"
                      :isTripRate="targetJobValues.jobRateTypeId === 6"
                      :appliedRate="clientRateToApply"
                    />
                  </v-tooltip>
                </div>
                <div v-if="fleetRateToApply" class="d-flex align-center ml-4">
                  <h2 class="ma-0">Fleet Asset Rate</h2>
                  <v-tooltip :z-index="2147483647" top max-width="60%">
                    <template v-slot:activator="{ on }">
                      <v-icon
                        size="14"
                        v-on="on"
                        color="info"
                        v-if="
                          fleetRateToApply ||
                          targetJobValues.fleetAssetRateTypeId === 6
                        "
                        style="pointer-events: auto; cursor: pointer"
                        class="pl-1"
                      >
                        fa fa-info-circle
                      </v-icon>
                    </template>
                    <AppliedRateDetails
                      :isTripRate="targetJobValues.fleetAssetRateTypeId === 6"
                      :appliedRate="fleetRateToApply"
                      :rateEntityType="RateEntityType.FLEET_ASSET"
                    />
                  </v-tooltip>
                </div>
              </v-layout>
              <ConfirmationDialog
                :key="jobDetails.jobId"
                buttonText="Apply All Changes"
                title="Confirm Service Changes"
                message="Are you sure wish to apply these changes?"
                @confirm="applySelectedServiceChanges"
                :buttonDisabled="!allRequirementsMet"
                :isDepressedButton="true"
                buttonColor="blue"
                confirmationButtonText="Save Updated Details"
                :dialogIsActive="true"
                :dialogWidth="500"
                :isLoading="isAwaitingJobSaveResponse"
              >
                <span
                  slot="confirmation-dialog-content"
                  style="font-weight: 600"
                >
                  <v-layout v-if="appliedAdjustmentChangeList.length" row wrap>
                    <v-flex
                      md12
                      v-for="adjustment in appliedAdjustmentChangeList"
                      :key="adjustment.id"
                    >
                      <v-layout>
                        <span style="font-weight: 400" class="pr-2"
                          >{{ adjustment.label }}:
                        </span>
                        {{ adjustment.oldValue }}
                        <v-icon
                          v-if="adjustment.newValue"
                          size="12"
                          class="pt-1 px-2"
                          color="light-blue lighten-2"
                          >fas fa-arrow-right</v-icon
                        >
                        <span v-if="adjustment.newValue">{{
                          adjustment.newValue
                        }}</span>
                      </v-layout>
                    </v-flex>
                  </v-layout>
                </span>
              </ConfirmationDialog>
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
    </JobContentDialog>
  </section>
</template>
<script setup lang="ts">
import AppliedRateDetails from '@/components/common/applied_rate_details.vue';
import JobUnitRateDetails from '@/components/common/job_unit_rate_details.vue';
import JobZoneRateDetails from '@/components/common/job_zone_rate_details.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import JobContentDialog from '@/components/common/ui-elements/job_content_dialog.vue';
import TripRateBooking from '@/components/operations/BookJob/service_rates_booking/trip_rate_booking/index.vue';
import {
  mapPudItemToUnitRateSummary,
  mapPudItemToZoneRateSummary,
  updateRateDetailsInPuds,
} from '@/helpers/JobBooking/JobBookingPudHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { returnFuelSurchargeForAccounting } from '@/helpers/RateHelpers/FuelSurchargeHelpers';
import {
  JobKeyRateData,
  JobRateDataType,
  retrieveJobRateDataForPricing,
} from '@/helpers/RateHelpers/PricingRateRequestHelpers';
import {
  isUnitRateTypeObject,
  isZoneRateTypeObject,
} from '@/helpers/RateHelpers/RateTableItemHelpers';
import { addOrReplaceRateTableItems } from '@/helpers/RateHelpers/ServiceRateHelpers';
import { requestZoneToZoneRatesForPudItems } from '@/helpers/RateHelpers/ZoneToZoneRateHelpers';
import {
  returnRateTypeLongNameFromId,
  returnServiceTypeLongNameFromId,
} from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { initialiseJobDetails } from '@/helpers/classInitialisers/InitialiseJobDetails';
import { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import ClientPerson from '@/interface-models/Client/ClientDetails/ClientPerson/ClientPerson';
import ClientSearchSummary from '@/interface-models/Client/ClientDetails/ClientSearchSummary';
import { returnAdditionalAccountingData } from '@/interface-models/Generic/Accounting/AdditionalAccountingData';
import JobPrimaryRate from '@/interface-models/Generic/Accounting/JobPrimaryRate';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import serviceTypeRates, {
  JobRateType,
  ServiceTypeRates,
} from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import { Validation } from '@/interface-models/Generic/Validation';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import { ClientServiceRateVariations } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariations';
import FleetAssetFuelSurchargeRate from '@/interface-models/ServiceRates/FleetAssetOwner/AdditionalCharges/FuelSurcharge/FleetAssetFuelSurchargeRate';
import FleetAssetServiceRate from '@/interface-models/ServiceRates/FleetAssetOwner/FleetAssetServiceRate/FleetAssetServiceRate';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import TripRate from '@/interface-models/ServiceRates/ServiceTypes/TripRate/TripRate';
import { UnitRatePudSummary } from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRatePudSummary';
import { ZoneRatePudSummary } from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRatePudSummary';
import { ZoneToZoneRateType } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneToZoneRateType';
import TripRateInformation from '@/interface-models/ServiceRates/TripRateInformation';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useFuelLevyStore } from '@/store/modules/FuelLevyStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useRootStore } from '@/store/modules/RootStore';
import { useServiceRateStore } from '@/store/modules/ServiceRateStore';
import moment from 'moment-timezone';
import {
  computed,
  ComputedRef,
  onMounted,
  Ref,
  ref,
  WritableComputedRef,
} from 'vue';

interface JobServiceConfig {
  clientId: string;
  jobServiceTypeId: number;
  fleetAssetServiceTypeId: number;
  jobRateTypeId: number;
  fleetAssetRateTypeId: number;
}
enum FleetRateAppliedType {
  CORRECT_RATE = 'CORRECT_RATE',
  FALLBACK_RATE = 'FALLBACK_RATE',
  NO_RATE_FOUND = 'NO_RATE_FOUND',
}
interface AdjustedValue {
  id: string;
  label: string;
  oldValue: string;
  newValue: string;
}

const emit = defineEmits<{
  (event: 'update:showServiceDetailsDialog', value: boolean): void;
  (event: 'refreshAccountingInParent', value: any): void;
}>();

const props = withDefaults(
  defineProps<{
    jobDetails: JobDetails;
    clientDetails: ClientDetails;
    showServiceDetailsDialog: boolean;
  }>(),
  {
    showServiceDetailsDialog: false,
  },
);
/**
 * The title of the component.
 */
const componentTitle: Ref<string> = ref('Adjust Service Details');

/**
 * Store instances.
 */
const serviceRateStore = useServiceRateStore();
const fuelLevyStore = useFuelLevyStore();
const clientDetailsStore = useClientDetailsStore();

/**
 * List of available target rate types.
 */
const availableTargetRateTypes: Ref<ServiceTypeRates[]> = ref([]);

/**
 * 'Original' variables are the service rates etc. as they are in the job.
 */
const originalJobValues: Ref<JobServiceConfig> = ref({
  clientId: '',
  jobServiceTypeId: 1,
  fleetAssetServiceTypeId: 1,
  jobRateTypeId: 1,
  fleetAssetRateTypeId: 1,
});

/**
 * 'Target' variables are what the new values will be if saved.
 */
const targetJobValues: Ref<JobServiceConfig> = ref({
  clientId: '',
  jobServiceTypeId: 1,
  fleetAssetServiceTypeId: 1,
  jobRateTypeId: 1,
  fleetAssetRateTypeId: 1,
});

/**
 * The type of fleet rate applied.
 */
const targetFleetAssetRateAppliedType: Ref<FleetRateAppliedType> = ref(
  FleetRateAppliedType.CORRECT_RATE,
);

/**
 * Original client and fleet asset service rates and fuel surcharges.
 */
const originalClientServiceRate: Ref<ClientServiceRate | null> = ref(null);
const originalClientFuelSurcharges: Ref<ClientFuelSurchargeRate[] | null> =
  ref(null);
const originalFleetAssetServiceRate: Ref<FleetAssetServiceRate | null> =
  ref(null);
const originalFleetAssetFuelSurcharges: Ref<
  FleetAssetFuelSurchargeRate[] | null
> = ref(null);

const clientServiceRateVariations: Ref<ClientServiceRateVariations[] | null> =
  ref(null);

/**
 * Flags for syncing service type and rate type IDs.
 */
const syncServiceTypeIds: Ref<boolean> = ref(true);
const syncRateTypeIds: Ref<boolean> = ref(true);

/**
 * Target client details and rates.
 */
const targetClientDetails: Ref<ClientDetails | null> = ref(null);
const targetClientServiceRate: Ref<ClientServiceRate | null> = ref(null);
const targetClientFuelSurchargeList: Ref<ClientFuelSurchargeRate[] | null> =
  ref(null);

/**
 * Target zone and unit rate information.
 */
const targetZoneRateInformation: Ref<ZoneRatePudSummary[] | null> = ref(null);
const targetUnitRateInformation: Ref<UnitRatePudSummary[] | null> = ref(null);

/**
 * Flags indicating if client and fleet asset rates were found.
 */
const targetClientRateFound: Ref<boolean | null> = ref(null);
const targetFleetAssetRateFound: Ref<boolean | null> = ref(null);

/**
 * Trip rate information.
 */
const tripRateInformation: Ref<TripRateInformation> = ref(
  new TripRateInformation(),
);

/**
 * Flags for API and UI state.
 */
const isAwaitingJobSaveResponse: Ref<boolean> = ref(false);
const isRequestingClientServiceRates: Ref<boolean> = ref(false);
const isRequestingJobRateData: Ref<boolean> = ref(false);
const rateRetrievalErrors: Ref<string[]> = ref([]);

/**
 * Rate tables to apply.
 */
const clientRateToApply: Ref<RateTableItems | null> = ref(null);
const fleetAssetRateTypesList: Ref<ServiceTypeRates[]> = ref([]);
const fleetRateToApply: Ref<RateTableItems | null> = ref(null);

/**
 * List of client common addresses.
 */
const clientCommonAddressList: Ref<ClientCommonAddress[]> = ref([]);

const adjustServiceDetailsForm: Ref<any> = ref(null);

/**
 * Used to sync and un-sync the job rateTypeId and the fleetAssetRateTypeId.
 * This is v-modelled to a switch which is only visible for certain rate
 * types, when a time rate fallback is available.
 */
const syncRateTypeIdsController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return syncRateTypeIds.value;
  },
  set(value: boolean): void {
    if (value) {
      targetJobValues.value.fleetAssetRateTypeId =
        targetJobValues.value.jobRateTypeId;
      recalculateRatesToApply();
    }
    syncRateTypeIds.value = value;
  },
});

/**
 * Used to sync and un-sync the job serviceTypeId and the fleetAssetServiceTypeId.
 * This is v-modelled to a switch which is only visible for certain service
 * types.
 */
const syncServiceTypeIdsController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return syncServiceTypeIds.value;
  },
  set(value: boolean): void {
    syncServiceTypeIds.value = value;
    if (value) {
      targetJobValues.value.fleetAssetServiceTypeId =
        targetJobValues.value.jobServiceTypeId;
      targetJobValues.value.fleetAssetRateTypeId =
        targetJobValues.value.jobRateTypeId;
      recalculateRatesToApply();
    }
    // Validate the form if available
    adjustServiceDetailsForm.value?.validate();
  },
});

/**
 * Return the value for targetJobValues.jobServiceTypeId. When setting, assign the incoming
 * value to targetJobValues.jobServiceTypeId AND targetClientServiceTypeId as these should
 * always be kept in sync. If syncServiceTypeIdsController is TRUE, then we
 * must also assign the incoming value to targetJobValues.fleetAssetServiceTypeId.
 */
const targetServiceTypeIdController: WritableComputedRef<number> = computed({
  get(): number {
    return targetJobValues.value.jobServiceTypeId;
  },
  set(value: number): void {
    syncRateTypeIdsController.value = true;
    targetJobValues.value.jobServiceTypeId = value;
    calculateClientRateToApply();
    if (syncServiceTypeIdsController.value) {
      targetJobValues.value.fleetAssetServiceTypeId = value;
      calculateFleetRateToApply();
    }
  },
});

/**
 * Recalculate both client and fleet rates to apply.
 */
async function recalculateRatesToApply(): Promise<void> {
  await calculateClientRateToApply();
  await calculateFleetRateToApply();
}

/**
 * Returns true if the job is allocated (has fleetAssetId and driverId).
 */
const isAllocated: ComputedRef<boolean> = computed(() => {
  return (
    props.jobDetails.fleetAssetId !== '' && props.jobDetails.driverId !== ''
  );
});

/**
 * Return true if the recurringJobId is defined, indicating that this is a permanent job.
 */
const isRecurringJob: ComputedRef<boolean> = computed(() => {
  return ![null, undefined, ''].includes(props.jobDetails.recurringJobId);
});

/**
 * Returns the value for targetJobValues.jobRateTypeId. When a value is set to this
 * controller targeting a ZONE or UNIT rate job, then also initializes local
 * variables specific for those rate types. Else, sets them to null.
 */
const targetRateTypeIdController: WritableComputedRef<number> = computed({
  get(): number {
    return targetJobValues.value.jobRateTypeId;
  },
  set(rateTypeId: number): void {
    if (rateTypeId === targetJobValues.value.jobRateTypeId) {
      return;
    }
    targetZoneRateInformation.value = null;
    targetUnitRateInformation.value = null;
    if (rateTypeId === 2) {
      setTargetZoneRateInformation();
    } else if (rateTypeId === 5) {
      setTargetUnitRateInformation();
    } else if (rateTypeId === 6) {
      tripRateInformation.value = new TripRateInformation();
      if (
        syncRateTypeIdsController.value ||
        targetJobValues.value.fleetAssetRateTypeId === 6
      ) {
        tripRateInformation.value.fleetAsset = true;
      }
    }
    targetJobValues.value.jobRateTypeId = rateTypeId;
    calculateClientRateToApply();
    if (syncRateTypeIdsController.value) {
      targetJobValues.value.fleetAssetRateTypeId = rateTypeId;
      calculateFleetRateToApply();
    }
  },
});

/**
 * Populate targetZoneRateInformation with values from PudItems.rateDetails.
 * This is passed into JobZoneRateDetails as a prop and used as a working copy
 * which is either saved (and set to job) or discarded.
 */
function setTargetZoneRateInformation(): void {
  targetZoneRateInformation.value = props.jobDetails.pudItems.map(
    (pudItem: PUDItem) =>
      mapPudItemToZoneRateSummary(
        pudItem,
        originalJobValues.value.jobRateTypeId === 2 &&
          !!pudItem.rateDetails?.zoneReference,
      ),
  );
}

/**
 * Populate targetUnitRateInformation with values from PudItems.rateDetails.
 * This is passed into JobUnitRateDetails as a prop and used as a working copy
 * which is either saved (and set to job) or discarded.
 */
function setTargetUnitRateInformation(): void {
  targetUnitRateInformation.value = props.jobDetails.pudItems.map(
    (pudItem: PUDItem) =>
      mapPudItemToUnitRateSummary(
        pudItem,
        originalJobValues.value.jobRateTypeId === 5 &&
          !!pudItem.rateDetails?.zoneReference,
      ),
  );
}

/**
 * Used for validation to indicate if a valid RateTableItem was found for the
 * given serviceTypeId and rateTypeId combination. If none is found, return an
 * error message to be displayed in inputs.
 */
const clientRateValidation: ComputedRef<true | string> = computed(() => {
  if (targetClientRateFound.value) {
    return true;
  }
  const serviceTypeName = returnServiceTypeLongNameFromId(
    targetJobValues.value.jobServiceTypeId,
    'Unknown',
  );
  const rateTypeName = returnRateTypeLongNameFromId(
    targetJobValues.value.jobRateTypeId,
    'Unknown',
  );
  return `Client does not have ${serviceTypeName} ${rateTypeName} rate available.`;
});

/**
 * Used for validation to indicate if a valid RateTableItem was found for
 * FleetAsset rates using the given serviceTypeId and rateTypeId combination.
 * If none is found, return an error message to be displayed in inputs.
 */
const fleetAssetRateValidation: ComputedRef<boolean | string> = computed(() => {
  if (targetFleetAssetRateFound.value || !isAllocated.value) {
    return true;
  }
  const serviceTypeName: string = returnServiceTypeLongNameFromId(
    targetJobValues.value.fleetAssetServiceTypeId,
    'Unknown',
  );
  const rateTypeName: string = returnRateTypeLongNameFromId(
    targetJobValues.value.fleetAssetRateTypeId,
    'Unknown',
  );
  const errorMessage: string =
    targetJobValues.value.fleetAssetRateTypeId === 1
      ? `Fleet Asset does not have ${serviceTypeName} ${rateTypeName} rate available.`
      : `Fleet Asset does not have ${serviceTypeName} ${rateTypeName} OR ${serviceTypeName} Time rates available.`;
  return errorMessage;
});

/**
 * When changing to a zone rate, display an error message when at least one of
 * the legs does not have a valid zoneReference.
 */
const zoneRateValidation: ComputedRef<boolean | string> = computed(() => {
  let result: boolean = true;
  if (targetZoneRateInformation.value !== null) {
    result = targetZoneRateInformation.value.every(
      (z) => z.zoneReference !== null,
    );
  }
  return result || 'Please select a valid ZONE for each stop.';
});

/**
 * When changing to a unit rate, display an error message when at least one of
 * the legs does not have a valid zoneReference.
 */
const unitRateValidation: ComputedRef<boolean | string> = computed(() => {
  let result: boolean = true;
  if (targetUnitRateInformation.value !== null) {
    result = targetUnitRateInformation.value.every(
      (z) => z.zoneReference !== null,
    );
  }
  return result || 'Please select a valid ZONE for each stop.';
});

/**
 * Set the to be used in the Fleet Asset specific rateTypes select list. Only
 * display if 'allowUnsyncedRateTypeIds' is true. Allows user to switch
 * between TIME rate and the current rate type if it is currently ZONE, UNIT
 * or TRIP/Quoted rate.
 */
function setFleetAssetRateTypesList(): void {
  if (!originalFleetAssetServiceRate.value) {
    fleetAssetRateTypesList.value = [];
    return;
  }

  const rateTypeId = targetJobValues.value.jobRateTypeId;
  const isUnitRate = rateTypeId === JobRateType.UNIT;

  // Find the drivers Rate by the rateId on the job. If the rate is a unit rate we are required to search the unit rate against serviceTypeId 4 otherwise the unit rate will not be found.
  const foundRate = originalFleetAssetServiceRate.value.rateTableItems.find(
    (r) =>
      r.rateTypeId === rateTypeId && !isUnitRate
        ? r.serviceTypeId === targetJobValues.value.jobServiceTypeId
        : r.serviceTypeId === 4,
  );

  let optionList: number[] = [JobRateType.TIME, JobRateType.TRIP];

  if (foundRate) {
    if (rateTypeId === JobRateType.TIME) {
      optionList = [JobRateType.TIME, JobRateType.TRIP];
    } else if (rateTypeId === JobRateType.ZONE) {
      optionList = [JobRateType.TIME, JobRateType.ZONE, JobRateType.TRIP];
    } else if (rateTypeId === JobRateType.UNIT) {
      optionList = [JobRateType.TIME, JobRateType.UNIT, JobRateType.TRIP];
    } else if (rateTypeId === JobRateType.DISTANCE) {
      optionList = [JobRateType.TIME, JobRateType.DISTANCE, JobRateType.TRIP];
    } else if (rateTypeId === JobRateType.ZONE_TO_ZONE) {
      optionList = [
        JobRateType.TIME,
        JobRateType.ZONE_TO_ZONE,
        JobRateType.TRIP,
      ];
    }
  }

  fleetAssetRateTypesList.value = optionList
    .map((id) => serviceTypeRates.find((r) => r.rateTypeId === id))
    .filter((r) => r !== undefined) as ServiceTypeRates[];
}

/**
 * Validation rules for fleet asset unsynced rate list select.
 */
const fleetAssetsUnsyncedRateListSelectValidationRules: ComputedRef<any[]> =
  computed(() => {
    const rules = [validationRules.required, fleetAssetRateValidation.value];
    if (targetJobValues.value.fleetAssetRateTypeId === 2) {
      rules.push(zoneRateValidation.value);
    }
    if (targetJobValues.value.fleetAssetRateTypeId === 5) {
      rules.push(unitRateValidation.value);
    }
    return rules;
  });

/**
 * Returns a series of AdjustedValue objects which list what values have
 * changed between the originalJobValues and the targetJobValues. Displayed in
 * the confirmation dialog when the user attempts to save the changes.
 */
const appliedAdjustmentChangeList: ComputedRef<AdjustedValue[]> = computed(
  () => {
    const adjustmentList: AdjustedValue[] = [];
    // If the client was changed
    if (originalJobValues.value.clientId !== targetJobValues.value.clientId) {
      adjustmentList.push({
        id: 'client',
        label: 'Client',
        oldValue: props.clientDetails.tradingName
          ? props.clientDetails.tradingName
          : props.clientDetails.clientName,
        newValue: targetClientDetails.value
          ? targetClientDetails.value.tradingName
            ? targetClientDetails.value.tradingName
            : targetClientDetails.value.clientName
          : '-',
      });
    }
    // Primary service type
    if (
      originalJobValues.value.jobServiceTypeId !==
      targetJobValues.value.jobServiceTypeId
    ) {
      adjustmentList.push({
        id: 'job-service-type',
        label: 'Job/Client Service Type',
        oldValue: returnServiceTypeLongNameFromId(
          originalJobValues.value.jobServiceTypeId,
        ),
        newValue: returnServiceTypeLongNameFromId(
          targetJobValues.value.jobServiceTypeId,
        ),
      });
    }
    // Fleet Asset Service Type
    if (
      originalJobValues.value.fleetAssetServiceTypeId !==
      targetJobValues.value.fleetAssetServiceTypeId
    ) {
      adjustmentList.push({
        id: 'fleet-service-type',
        label: 'Fleet Asset Service Type',
        oldValue: returnServiceTypeLongNameFromId(
          originalJobValues.value.fleetAssetServiceTypeId,
        ),
        newValue: returnServiceTypeLongNameFromId(
          targetJobValues.value.fleetAssetServiceTypeId,
        ),
      });
    }
    // Primary rate type
    if (
      originalJobValues.value.jobRateTypeId !==
      targetJobValues.value.jobRateTypeId
    ) {
      adjustmentList.push({
        id: 'job-rate-type',
        label: 'Job/Client Rate Type',
        oldValue: returnRateTypeLongNameFromId(
          originalJobValues.value.jobRateTypeId,
        ),
        newValue: returnRateTypeLongNameFromId(
          targetJobValues.value.jobRateTypeId,
        ),
      });
    }

    // Fleet Asset Rate Type
    if (
      originalJobValues.value.fleetAssetRateTypeId !==
      targetJobValues.value.fleetAssetRateTypeId
    ) {
      adjustmentList.push({
        id: 'fleet-rate-type',
        label: 'Fleet Asset Rate Type',
        oldValue: returnRateTypeLongNameFromId(
          originalJobValues.value.fleetAssetRateTypeId,
        ),
        newValue: returnRateTypeLongNameFromId(
          targetJobValues.value.fleetAssetRateTypeId,
        ),
      });
    }

    function addAdjustmentEntry(title: string, rid: number): void {
      if (
        (originalJobValues.value.jobRateTypeId === rid &&
          targetJobValues.value.jobRateTypeId === rid) ||
        (originalJobValues.value.fleetAssetRateTypeId === rid &&
          targetJobValues.value.fleetAssetRateTypeId === rid)
      ) {
        adjustmentList.push({
          id: `${title}-rate-details`,
          label: `${title} Rate Details`,
          oldValue: 'Details updated',
          newValue: '',
        });
      }
    }
    addAdjustmentEntry('Quoted', 6);
    addAdjustmentEntry('Zone', 2);
    addAdjustmentEntry('Unit', 5);

    return adjustmentList;
  },
);

/**
 * Enables or disables the save button from being available, based on whether
 * valid rates have been found for both client and fleet asset.
 */
const allRequirementsMet: ComputedRef<boolean> = computed(() => {
  if (
    targetClientDetails.value &&
    targetClientDetails.value.accountsReceivable.creditStatus === 2 &&
    !targetClientDetails.value.statusList.includes(3)
  ) {
    return false;
  }

  if (isAllocated.value) {
    return (
      clientRateToApply.value !== undefined &&
      fleetRateToApply.value !== undefined
    );
  } else {
    return clientRateToApply.value !== undefined;
  }
});

/**
 * Returns a list of active clients.
 */
const activeClientList: ComputedRef<ClientSearchSummary[]> = computed(() => {
  const allClients = clientDetailsStore.clientSummaryList;
  const activeClients = allClients.filter(
    (x: any) => !x.statusList.includes(13),
  );
  return activeClients;
});

/**
 * Returns the validation rules.
 */
const validate: ComputedRef<Validation> = computed(() => validationRules);

/**
 * Disable the Client dropdown menu if we're adjusting a Recurring Job, OR if
 * this job is a Cash Sale and the user is not authorised to make changes to
 * the client.
 */
const disableClientSelect: ComputedRef<boolean> = computed(() => {
  const isRecurringJob = ![null, undefined, ''].includes(
    props.jobDetails.recurringJobId,
  );
  const isCashSaleJob = props.jobDetails.client.id === 'CS';

  // Assuming isAuthorised is a function in this context
  // If not, you may need to define it or import it
  return isRecurringJob || (isCashSaleJob && !hasAdminOrHeadOfficeRole());
});

/**
 * Controls the visibility of the dialog.
 */
const showDialog: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.showServiceDetailsDialog;
  },
  set(value: boolean): void {
    emit('update:showServiceDetailsDialog', value);
  },
});

/**
 * Re-calculates the client rates to apply based on the target job values.
 * Called when the inputs are updated in the template. Sets the result to
 * clientRateToApply.
 */
async function calculateClientRateToApply(): Promise<void> {
  const setRateTableItem = (
    rateTypeObject: TripRate | ZoneToZoneRateType[],
    isFuelApplicable: boolean,
  ): void => {
    const rateTableItem = new RateTableItems();
    rateTableItem.rateTypeId = targetRateTypeIdController.value;
    rateTableItem.serviceTypeId = targetJobValues.value.jobServiceTypeId;
    rateTableItem.fuelSurcharge = isFuelApplicable;
    rateTableItem.rateTypeObject = rateTypeObject;
    clientRateToApply.value = rateTableItem;

    // If we're not going to recompute the fleet rate, then we need to update
    // the available rate types for fleet asset
    if (!syncRateTypeIdsController.value) {
      setFleetAssetRateTypesList();
    }
  };

  // If trip/Quoted rate, then we don't need to look at the rate tables
  if (targetRateTypeIdController.value === JobRateType.TRIP) {
    const tripRate = new TripRate(
      tripRateInformation.value.clientRate
        ? tripRateInformation.value.clientRate
        : 0,
    );
    setRateTableItem(tripRate, tripRateInformation.value.clientFuelSurcharge);
    targetClientRateFound.value = true;
    return;
  }
  // For zone to zone rate, we should request the rates from the API
  if (targetRateTypeIdController.value === JobRateType.ZONE_TO_ZONE) {
    tripRateInformation.value.client = false;

    const result = await returnZoneToZoneRateTableItems(
      RateEntityType.CLIENT,
      targetJobValues.value.clientId,
      targetJobValues.value.jobServiceTypeId,
      props.jobDetails.pudItems,
    );
    if (result) {
      setRateTableItem(result, false);
    }
    targetClientRateFound.value = result !== null;
    return;
  }

  // If we're looking at rates within the same Client
  const rateToApply =
    originalJobValues.value.clientId === targetJobValues.value.clientId
      ? originalClientServiceRate.value?.rateToApplyToJob(
          targetJobValues.value.jobServiceTypeId,
          targetRateTypeIdController.value,
        )
      : targetClientServiceRate.value?.rateToApplyToJob(
          targetJobValues.value.jobServiceTypeId,
          targetRateTypeIdController.value,
        );

  // Set found result to local variable to improve performance on
  // clientRateValidation check
  targetClientRateFound.value = rateToApply !== null;
  clientRateToApply.value = rateToApply || null;

  // If we're not going to recompute the fleet rate, then we need to update
  // the available rate types for fleet asset
  if (!syncRateTypeIdsController.value) {
    setFleetAssetRateTypesList();
  }
}

/**
 * Re-calculates the fleet asset rates to apply based on the target job values.
 * Called when the inputs are updated in the template. Sets the result to
 * fleetRateToApply.
 */
async function calculateFleetRateToApply(): Promise<void> {
  // Do nothing if not allocated
  if (!isAllocated.value) {
    return;
  }
  if (!originalFleetAssetServiceRate.value?._id) {
    targetFleetAssetRateFound.value = false;
    return;
  }

  const rateTypeId = targetJobValues.value.fleetAssetRateTypeId;
  const serviceTypeId = targetJobValues.value.fleetAssetServiceTypeId;

  function setRateTableItem(
    rateTypeObject: TripRate | ZoneToZoneRateType[],
    isFuelApplicable: boolean,
  ): void {
    const rateTableItem = new RateTableItems();
    rateTableItem.rateTypeId = rateTypeId;
    rateTableItem.serviceTypeId = serviceTypeId;
    rateTableItem.fuelSurcharge = isFuelApplicable;
    rateTableItem.rateTypeObject = rateTypeObject;
    fleetRateToApply.value = rateTableItem;
    targetFleetAssetRateFound.value = true;
    targetFleetAssetRateAppliedType.value = FleetRateAppliedType.CORRECT_RATE;
    setFleetAssetRateTypesList();
  }

  if (rateTypeId === JobRateType.TRIP) {
    tripRateInformation.value.fleetAsset = true;
    // If trip/Quoted rate and tripRateInformation.fleetAsset is TRUE
    const tripRate = new TripRate(
      tripRateInformation.value.fleetAssetRate || 0,
    );
    setRateTableItem(
      tripRate,
      tripRateInformation.value.fleetAssetFuelSurcharge,
    );
    return;
  } else {
    tripRateInformation.value.fleetAsset = false;
  }

  if (rateTypeId === JobRateType.ZONE_TO_ZONE) {
    const result = await returnZoneToZoneRateTableItems(
      RateEntityType.FLEET_ASSET,
      props.jobDetails.fleetAssetId,
      serviceTypeId,
      props.jobDetails.pudItems,
    );
    if (result) {
      setRateTableItem(result, false);
      return;
    }
    targetFleetAssetRateAppliedType.value = FleetRateAppliedType.NO_RATE_FOUND;
    fleetRateToApply.value = null;
    targetFleetAssetRateFound.value = false;
  }

  // Look for the RateTableItem in the fleet asset service rate table
  const rateToApply = originalFleetAssetServiceRate.value.rateToApplyToJob({
    serviceTypeId,
    rateTypeId,
    operationsCustomConfig:
      useCompanyDetailsStore().divisionCustomConfig?.operations,
  });
  // Set found result to local variable to improve performance on fleetAssetRateValidation check
  targetFleetAssetRateFound.value = rateToApply !== null;
  const rateTypeIdFound: number | null = rateToApply
    ? rateToApply.rateTypeId
    : null;

  // Check if the rateTypeId that returned is the same as the one we were looking for.
  // If not then set targetJobValues.fleetAssetRateTypeId to be the one that was returned.
  if (rateTypeIdFound !== null) {
    if (rateTypeIdFound !== rateTypeId) {
      // Set targetFleetAssetRateAppliedType to indicate we didn't find the rate we were looking for,
      // but we found a suitable fallback Time rate
      targetFleetAssetRateAppliedType.value =
        FleetRateAppliedType.FALLBACK_RATE;
      targetJobValues.value.fleetAssetRateTypeId = rateTypeIdFound;
      // Set syncRateTypeIdsController to false so switch reflects mismatching rateTypeIds
      syncRateTypeIdsController.value =
        rateTypeIdFound === targetJobValues.value.jobRateTypeId;
    } else {
      // Set targetFleetAssetRateAppliedType to indicate we found the rateTableItem we were looking for
      targetFleetAssetRateAppliedType.value = FleetRateAppliedType.CORRECT_RATE;
      targetJobValues.value.fleetAssetRateTypeId = rateTypeIdFound;
    }
  } else {
    // Set targetFleetAssetRateAppliedType to indicate we didn't find any suitable RateTableItem
    targetFleetAssetRateAppliedType.value = FleetRateAppliedType.NO_RATE_FOUND;
    syncRateTypeIds.value = false;
  }

  setFleetAssetRateTypesList();
  fleetRateToApply.value = rateToApply;
}

/**
 * Fetches and updates the zone-to-zone rate table items for the specified
 * entity and service type.
 *
 * @param type - The type of rate entity (e.g., CLIENT, FLEET_ASSET).
 * @param entityId - The ID of the entity for which to fetch the rate table
 * items.
 * @param serviceTypeId - The target service type id.
 * @param pudItems - The list of PUD items from the job.
 * @returns array of ZoneToZoneRateType objects or null if an error occurs or
 * the response is invalid.
 *
 * @throws {Error} - Throws an error if the zone-to-zone rate response is
 * empty.
 */
async function returnZoneToZoneRateTableItems(
  type: RateEntityType,
  entityId: string,
  serviceTypeId: number,
  pudItems: PUDItem[],
): Promise<ZoneToZoneRateType[] | null> {
  try {
    if (!pudItems?.length || pudItems.length < 2) {
      showNotification('Zone to Zone rate requires at least 2 stops.');
      return null;
    }
    const zzRateTableItems = await requestZoneToZoneRatesForPudItems({
      type: type,
      entityId: entityId,
      pudItems: pudItems,
      serviceTypeId: serviceTypeId,
    });
    if (!zzRateTableItems) {
      throw new Error(
        'Zone to Zone rates not available for the specified service type or locations.',
      );
    }
    // Add or replace the zone to zone rate table items in the service rates
    if (type === RateEntityType.CLIENT && targetClientServiceRate.value) {
      addOrReplaceRateTableItems(
        targetClientServiceRate.value.rateTableItems,
        zzRateTableItems,
      );
    } else if (
      type === RateEntityType.FLEET_ASSET &&
      originalFleetAssetServiceRate.value
    ) {
      addOrReplaceRateTableItems(
        originalFleetAssetServiceRate.value.rateTableItems,
        zzRateTableItems,
      );
    }

    // Validate response, then replace the rateTypeObject in the clientRates
    if (
      zzRateTableItems.length === 1 &&
      zzRateTableItems[0].serviceTypeId === serviceTypeId
    ) {
      return zzRateTableItems[0].rateTypeObject as ZoneToZoneRateType[];
    }
    return null;
  } catch (error) {
    if (error instanceof Error) {
      logConsoleError(error.message, error);
    } else {
      logConsoleError('Error updating zone to zone rate table items', error);
    }
    return null;
  }
}

/**
 * Returns the valid service types for the current client selection.
 */
const validServiceTypes: ComputedRef<ServiceTypes[]> = computed(() => {
  if (originalJobValues.value.clientId === targetJobValues.value.clientId) {
    if (!originalClientServiceRate.value) {
      return [];
    }
    return originalClientServiceRate.value.availableServiceTypes;
  }
  if (!targetClientServiceRate.value) {
    return [];
  }
  return targetClientServiceRate.value.availableServiceTypes;
});

/**
 * Request the full Client Details for the selected clientId.
 * @param clientId - The client ID to fetch details for.
 */
async function getSelectedClientDetails(clientId: string): Promise<void> {
  if (clientId !== undefined) {
    resetTargetValues();
    const clientDetails =
      await clientDetailsStore.requestClientDetailsByClientId(clientId);
    if (clientDetails) {
      await setSelectedClientDetails(clientDetails);
    }
  }
}

/**
 * Resets the target job values and related state.
 */
function resetTargetValues(): void {
  syncServiceTypeIdsController.value = true;
  targetJobValues.value.jobRateTypeId = 1;
  targetJobValues.value.fleetAssetRateTypeId = 1;
  targetJobValues.value.jobServiceTypeId = 1;
  syncServiceTypeIdsController.value = true;
  syncRateTypeIds.value = true;
  targetZoneRateInformation.value = null;
  targetUnitRateInformation.value = null;
}

/**
 * Request associated rate data for the selected client.
 * @param clientDetails - The client details to set as selected.
 */
async function setSelectedClientDetails(
  clientDetails: ClientDetails,
): Promise<void> {
  // If the client has the flag of "SEE ACCOUNTS" we should block the changing of service rates.
  if (
    clientDetails &&
    clientDetails.accountsReceivable.creditStatus === 2 &&
    !clientDetails.statusList.includes(3)
  ) {
    const dialogNotificationMessage =
      'Booking unavailable for ' +
      (clientDetails.tradingName
        ? clientDetails.tradingName
        : clientDetails.clientName) +
      '. Please see Accounts.';
    useRootStore().setDialogNotification([dialogNotificationMessage]);
    targetJobValues.value.clientId = originalJobValues.value.clientId;
    return;
  }

  targetClientDetails.value = clientDetails;
  if (!targetClientDetails.value) {
    return;
  }
  const clientId = clientDetails.clientId;
  const searchDate = props.jobDetails.workDate || moment().valueOf();
  isRequestingClientServiceRates.value = true;
  // Request client service rates and common addresses. Try first to find client's custom merged
  // rate card. If we can't find that, use division defaults
  const [clientRates, clientFuelRates, clientCommonAddresses] =
    await Promise.all([
      serviceRateStore.getMergedClientRatesOrDefault(clientId, searchDate),
      fuelLevyStore.getCurrentClientFuelSurcharges(clientId, searchDate),
      clientDetailsStore.requestCommonAddressesByClientId(
        clientDetails.clientId,
      ),
    ]);

  // Handle service rate response
  if (clientRates?.clientServiceRate) {
    setServiceRate(clientRates.clientServiceRate);
  }
  // Handle fuel levy response
  if (clientFuelRates !== null) {
    setClientFuelSurcharges(clientFuelRates);
  } else {
    showNotification('No Fuel Surcharge rate found.');
  }
  clientCommonAddressList.value = clientCommonAddresses ?? [];
  // Set loading to false
  isRequestingClientServiceRates.value = false;
}
/**
 * Sets the target client service rate.
 * @param serviceRate - The client service rate to set.
 */
function setServiceRate(serviceRate?: ClientServiceRate | null): void {
  if (!serviceRate) {
    showAppNotification('Rate Card could not be found for this client.');
    return;
  }
  targetClientServiceRate.value = serviceRate;
}

/**
 * Sets the target client fuel surcharge.
 * @param clientFuelSurcharge - The client fuel surcharge to set.
 */
function setClientFuelSurcharges(
  clientFuelSurcharge: ClientFuelSurchargeRate[],
): void {
  targetClientFuelSurchargeList.value = clientFuelSurcharge;
}

/**
 * Initialise the default 'new' values from the original Job. These can then
 * be changed by the user to new values.
 */
function setTargetDefaultValues(): void {
  originalJobValues.value.clientId = props.jobDetails.client.id;
  originalJobValues.value.jobServiceTypeId = props.jobDetails.serviceTypeId;
  originalJobValues.value.jobRateTypeId =
    props.jobDetails.serviceTypeObject.rateTypeId;
  // Set original values from fleet asset accounting
  originalJobValues.value.fleetAssetServiceTypeId =
    props.jobDetails.fleetAssetServiceTypeId !== null
      ? props.jobDetails.fleetAssetServiceTypeId
      : originalJobValues.value.jobServiceTypeId;
  originalJobValues.value.fleetAssetRateTypeId =
    props.jobDetails.fleetAssetRateTypeId !== null
      ? props.jobDetails.fleetAssetRateTypeId
      : originalJobValues.value.jobRateTypeId;

  // Set default values for 'sync' switches
  syncRateTypeIds.value =
    originalJobValues.value.jobRateTypeId ===
    originalJobValues.value.fleetAssetRateTypeId;
  syncServiceTypeIds.value =
    originalJobValues.value.jobServiceTypeId ===
    originalJobValues.value.fleetAssetServiceTypeId;

  // Set the target values to default to the originals
  targetJobValues.value.clientId = originalJobValues.value.clientId;
  targetJobValues.value.jobServiceTypeId =
    originalJobValues.value.jobServiceTypeId;
  targetJobValues.value.jobRateTypeId = originalJobValues.value.jobRateTypeId;
  targetJobValues.value.fleetAssetServiceTypeId =
    originalJobValues.value.fleetAssetServiceTypeId;
  targetJobValues.value.fleetAssetRateTypeId =
    originalJobValues.value.fleetAssetRateTypeId;
  targetRateTypeIdController.value = originalJobValues.value.jobRateTypeId;

  // If we're targeting a ZONE or UNIT rate, then init the working copies of those arrays
  if (targetJobValues.value.jobRateTypeId === JobRateType.ZONE) {
    setTargetZoneRateInformation();
  } else if (targetJobValues.value.jobRateTypeId === JobRateType.UNIT) {
    setTargetUnitRateInformation();
  }
  tripRateInformation.value = new TripRateInformation();

  if (targetRateTypeIdController.value === JobRateType.TRIP) {
    const clientRate =
      props.jobDetails.accounting.clientRates &&
      props.jobDetails.accounting.clientRates[0]
        ? props.jobDetails.accounting.clientRates[0]
        : null;
    if (clientRate !== null) {
      const rate = clientRate.rate;
      const isTrip = rate.rateTypeId === JobRateType.TRIP;
      tripRateInformation.value.client = isTrip;
      tripRateInformation.value.clientRate = isTrip
        ? rate.rateTypeObject
          ? (rate.rateTypeObject as TripRate).rate
          : null
        : null;
      tripRateInformation.value.clientFuelSurcharge = rate.fuelSurcharge;
    }
  }

  if (targetJobValues.value.fleetAssetRateTypeId === JobRateType.TRIP) {
    const fleetRate =
      props.jobDetails.accounting.fleetAssetRates &&
      props.jobDetails.accounting.fleetAssetRates[0]
        ? props.jobDetails.accounting.fleetAssetRates[0]
        : null;

    if (fleetRate !== null) {
      const rate = fleetRate.rate;
      const isTrip = rate.rateTypeId === JobRateType.TRIP;
      tripRateInformation.value.fleetAsset = isTrip;
      tripRateInformation.value.fleetAssetRate = isTrip
        ? rate.rateTypeObject
          ? (rate.rateTypeObject as TripRate).rate
          : null
        : null;
      tripRateInformation.value.fleetAssetFuelSurcharge = rate.fuelSurcharge;
    }
  }
}

/**
 * Called when the client's service rates list is received. Sets the available rate types based on what rates the client has available.
 * @param clientServiceRate - The client service rate to use.
 */
function setAvailableRateTypesFromServiceRate(
  clientServiceRate: ClientServiceRate,
): void {
  const rateTypes: ServiceTypeRates[] = serviceTypeRates.filter(
    (st) =>
      st.rateTypeId !== JobRateType.POINT_TO_POINT &&
      clientServiceRate.rateTableItems.some(
        (r) => r.rateTypeId === st.rateTypeId,
      ),
  );

  // Add trip/Quoted rate to the end of the list
  if (!rateTypes.find((r) => r.rateTypeId === JobRateType.TRIP)) {
    const tripType = serviceTypeRates.find(
      (r) => r.rateTypeId === JobRateType.TRIP,
    );
    if (tripType) {
      rateTypes.push(tripType);
    }
  }
  availableTargetRateTypes.value = rateTypes;
}

/**
 * Applies the selected service changes to the job.
 */
function applySelectedServiceChanges(): void {
  if (!adjustServiceDetailsForm.value?.validate()) {
    showAppNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }

  const targetJobDetails: JobDetails = initialiseJobDetails(props.jobDetails);

  let atLeastOne: boolean = false;
  if (originalJobValues.value.clientId !== targetJobValues.value.clientId) {
    const appliedClientChanges: boolean = applyClientChanges(targetJobDetails);
    if (!appliedClientChanges) {
      logConsoleError('Applying Client Changes failed.');
      return;
    }
    atLeastOne = true;
  }
  const original = originalJobValues.value;
  const target = targetJobValues.value;

  if (
    original.jobServiceTypeId !== target.jobServiceTypeId ||
    original.jobRateTypeId !== target.jobRateTypeId ||
    original.fleetAssetServiceTypeId !== target.fleetAssetServiceTypeId ||
    original.fleetAssetRateTypeId !== target.fleetAssetRateTypeId ||
    (original.jobRateTypeId === JobRateType.ZONE &&
      targetRateTypeIdController.value === JobRateType.ZONE) ||
    (original.jobRateTypeId === JobRateType.UNIT &&
      targetRateTypeIdController.value === JobRateType.UNIT) ||
    (original.jobRateTypeId === JobRateType.TRIP &&
      targetRateTypeIdController.value === JobRateType.TRIP)
  ) {
    const appliedServiceTypeChanges: boolean =
      applyServiceTypeChanges(targetJobDetails);
    if (!appliedServiceTypeChanges) {
      logConsoleError('Applying Service Type failed.');
      return;
    }
    atLeastOne = true;
  }
  if (atLeastOne) {
    saveJobDetails(targetJobDetails);
  } else {
    showAppNotification('No changes detected.', HealthLevel.INFO);
  }
}

/**
 * Dispatch request to save JobDetails.
 * @param job - The job details to save.
 */
async function saveJobDetails(job: JobDetails): Promise<void> {
  isAwaitingJobSaveResponse.value = true;
  const result = await useJobStore().updateJobDetails(job);
  if (result) {
    showAppNotification(
      `Successfully updated Job #${props.jobDetails.displayId}.`,
      HealthLevel.SUCCESS,
    );

    // If jobDetails is included in the payload, then we should initialise the
    // additionalAccountingData within the accounting object, such that it's
    // ready to use in the JobDetailsDialog
    if (result.jobDetails?.accounting) {
      const additionalData = await returnAdditionalAccountingData({
        jobDetails: result.jobDetails,
        applicableRateTypes: [JobRateType.DISTANCE],
        forceRequest: false,
      });
      result.jobDetails.accounting.additionalData = additionalData;
    }
  } else {
    showAppNotification(`Failed to update Job ${job.displayId}.`);
  }
  isAwaitingJobSaveResponse.value = false;

  // Emit update accounting details object to parent
  emit('refreshAccountingInParent', result?.jobDetails?.accounting ?? null);
  showDialog.value = false;
}

/**
 * Copy across the new client information to the job copy.
 * @param job - The job details to update.
 * @returns true if changes were applied, false otherwise.
 */
function applyClientChanges(job: JobDetails): boolean {
  if (
    targetJobValues.value.clientId &&
    targetClientDetails.value &&
    targetJobValues.value.clientId === targetClientDetails.value.clientId
  ) {
    // If the job is currently a Cash Sale, then we should set cashSaleClientDetails to null as it will no longer be a Cash Sale
    if (job.client.id === 'CS') {
      if (!isAuthorised()) {
        showAppNotification(
          'Please contact Head Office to change the Client of this CASH SALE job.',
        );
        return false;
      }
      job.cashSaleClientDetails = null;
    }
    const client: ClientDetails = targetClientDetails.value;
    const clientId: string = targetJobValues.value.clientId;

    // JobDetails.client
    job.client.id = clientId;
    job.client.clientName = client.tradingName
      ? client.tradingName
      : client.clientName;

    // JobDetails.clientDispatcher
    const foundDefaultClientPerson: ClientPerson | undefined =
      clientDetailsStore.clientPersons.find(
        (person) => person._id === props.clientDetails.defaultDispatcherId,
      );
    if (foundDefaultClientPerson) {
      job.clientDispatcher = foundDefaultClientPerson;
    } else {
      job.clientDispatcher = clientDetailsStore.clientPersons[0]
        ? clientDetailsStore.clientPersons[0]
        : new ClientPerson();
    }

    // JobDetails.notes
    // Loop through notes, find existing client instructions, and remove.
    if (client.specialInstructions && client.specialInstructions.length > 0) {
      // Add new Client's instructions to the job
      client.specialInstructions.forEach((n) => {
        useJobStore().addNoteToJob({
          jobId: job.jobId!,
          note: n,
        });
      });
    }

    // JobDetails.proofOfDelivery
    job.proofOfDelivery = client.proofOfDelivery;

    return true;
  } else {
    // NOTIFICATION
    return false;
  }
}

/**
 * Change the service type and rate type.
 * @param job - The job details to update.
 * @returns true if changes were applied, false otherwise.
 */
function applyServiceTypeChanges(job: JobDetails): boolean {
  // Set the updated serviceTypeId and rateTypeId
  job.serviceTypeId = targetJobValues.value.jobServiceTypeId;
  job.serviceTypeObject.rateTypeId = targetRateTypeIdController.value;

  // If a valid client rate object has been found, set that to job.accounting
  if (clientRateToApply.value) {
    if (job.accounting.clientRates && job.accounting.clientRates[0]) {
      const clientRate = job.accounting.clientRates[0];
      const serviceRate =
        originalJobValues.value.clientId === targetJobValues.value.clientId
          ? originalClientServiceRate.value
          : targetClientServiceRate.value;
      const fuelSurcharges =
        originalJobValues.value.clientId === targetJobValues.value.clientId
          ? originalClientFuelSurcharges.value
          : targetClientFuelSurchargeList.value;

      if (!serviceRate) {
        return false;
      }
      job.accounting.additionalCharges.clientFuelSurcharge =
        returnFuelSurchargeForAccounting({
          jobDetails: job,
          isFuelApplicable: clientRateToApply.value.isClientFuelApplied,
          fuelSurcharges,
        });
      clientRate.rate = clientRateToApply.value;
      if (clientRate.rate.rateTypeId === 6) {
        (clientRate.rate.rateTypeObject as TripRate).rate = tripRateInformation
          .value.clientRate
          ? tripRateInformation.value.clientRate
          : 0;
      }
      clientRate.rateTableName = serviceRate.name;
      clientRate.validFromDate = serviceRate.validFromDate;
      clientRate.validToDate = serviceRate.validToDate;
      clientRate.outsideMetroRate = serviceRate.outsideMetroRate;

      // If there are any rate variations, then we should
      // find the correct rate variation to apply and set it to the job
      const rateVariation = clientRateToApply.value.getRateVariation(
        clientServiceRateVariations.value ?? [],
      );
      job.accounting.clientServiceRateVariations = rateVariation;
    }
  }
  // If the job is allocated AND a valid fleet rate object has been found, set that to job.accounting
  if (isAllocated.value && fleetRateToApply.value) {
    if (job.accounting.fleetAssetRates && job.accounting.fleetAssetRates[0]) {
      const fleetRate = job.accounting.fleetAssetRates[0];
      const serviceRate = originalFleetAssetServiceRate.value;
      const fleetAssetFuelSurcharges = originalFleetAssetFuelSurcharges.value;

      if (!serviceRate) {
        return false;
      }

      job.accounting.additionalCharges.fleetAssetFuelSurcharge =
        returnFuelSurchargeForAccounting({
          jobDetails: job,
          isFuelApplicable: fleetRateToApply.value.isClientFuelApplied,
          fuelSurcharges: fleetAssetFuelSurcharges,
        });

      fleetRate.rate = fleetRateToApply.value;
      fleetRate.rateTableName = serviceRate.name;
      fleetRate.validFromDate = serviceRate.validFromDate;
      fleetRate.validToDate = serviceRate.validToDate;
      fleetRate.outsideMetroRate = serviceRate.outsideMetroRate;
    }
  } else {
    // If the job is UNALLOCATED, there are NO RATES, the target rate type is a TRIP/Quoted RATE and the trip/Quoted rate APPLIES TO THE FLEET ASSET, we should push in a trip/Quoted rate item into the fleet asset rates.
    if (!isAllocated.value && targetRateTypeIdController.value === 6) {
      const tripRate = tripRateInformation.value;
      if (tripRate.fleetAsset) {
        const jobRate: JobPrimaryRate = new JobPrimaryRate();
        jobRate.rate = new RateTableItems(
          job.serviceTypeObject.rateTypeId,
          new TripRate(tripRate.fleetAssetRate ? tripRate.fleetAssetRate : 0),
          targetJobValues.value.jobServiceTypeId,
          tripRate.fleetAssetFuelSurcharge,
        );
        job.accounting.fleetAssetRates = [];
        job.accounting.fleetAssetRates.push(jobRate);
      }
    }
  }
  updateRateDetailsInPuds(
    job.pudItems,
    targetRateTypeIdController.value,
    targetZoneRateInformation.value,
    targetUnitRateInformation.value,
  );
  return true;
}

/**
 * Response from ServiceRateRetriever component, containing rate objects.
 * @param rateData - The rate data received.
 */
async function receivedOriginalRateResponses(
  rateData: JobKeyRateData,
): Promise<void> {
  if (rateData === null) {
    return;
  }
  // Check if any of the rates have error messages. If so, return early
  const errorMessages: string[] = [
    ...(rateData.errorMessages ?? []),
    ...(rateData.clientServiceRate.errorMessages ?? []),
    ...(rateData.clientFuelSurcharges.errorMessages ?? []),
  ];
  if (isAllocated.value) {
    errorMessages.push(
      ...(rateData.fleetAssetServiceRate.errorMessages ?? []),
      ...(rateData.fleetAssetFuelSurcharges.errorMessages ?? []),
    );
  }

  if (errorMessages.length > 0) {
    rateRetrievalErrors.value = errorMessages;
    return;
  }
  // Valid emitted payloads and set their values to local variables
  // Client service rate
  if (
    rateData.clientServiceRate.type === JobRateDataType.CLIENT_SERVICE_RATE &&
    rateData.clientServiceRate.data !== null
  ) {
    originalClientServiceRate.value = rateData.clientServiceRate.data;
  }

  // Client fuel surcharge
  if (
    rateData.clientFuelSurcharges.type ===
      JobRateDataType.CLIENT_FUEL_SURCHARGE &&
    rateData.clientFuelSurcharges.data !== null
  ) {
    originalClientFuelSurcharges.value = rateData.clientFuelSurcharges.data;
  }

  // Rate variations
  if (
    rateData.clientServiceRateVariations?.type ===
      JobRateDataType.CLIENT_SERVICE_RATE_VARIATIONS &&
    rateData.clientServiceRateVariations.data !== null
  ) {
    clientServiceRateVariations.value =
      rateData.clientServiceRateVariations.data;
  }

  if (isAllocated.value) {
    // Fleet asset service rate
    if (
      rateData.fleetAssetServiceRate.type ===
        JobRateDataType.FLEET_ASSET_SERVICE_RATE &&
      rateData.fleetAssetServiceRate.data !== null
    ) {
      originalFleetAssetServiceRate.value = rateData.fleetAssetServiceRate.data;
    }

    // Fleet asset fuel surcharge
    if (
      rateData.fleetAssetFuelSurcharges.type ===
        JobRateDataType.FLEET_ASSET_FUEL_SURCHARGE &&
      rateData.fleetAssetFuelSurcharges.data !== null
    ) {
      originalFleetAssetFuelSurcharges.value =
        rateData.fleetAssetFuelSurcharges.data;
    }
  }

  if (originalClientServiceRate.value) {
    setAvailableRateTypesFromServiceRate(originalClientServiceRate.value);
  }

  await recalculateRatesToApply();
}

/**
 * Sets the initial data for the dialog.
 */
async function setInitialData(): Promise<void> {
  if (!props.jobDetails) {
    showDialog.value = false;
  }

  setTargetDefaultValues();
  rateRetrievalErrors.value = [];
  isRequestingJobRateData.value = true;
  // Request all client and fleet asset rates
  const keyRateData: JobKeyRateData = await retrieveJobRateDataForPricing(
    props.jobDetails,
    props.jobDetails.workDate,
  );
  await receivedOriginalRateResponses(keyRateData);
  isRequestingJobRateData.value = false;
}

/**
 * Captures emit from JobZoneRateDetails component, which emits an updated value. Set to the local zone rate information.
 * @param zoneRateInfo - The updated zone rate information.
 */
function updateZoneRateInformation(zoneRateInfo: ZoneRatePudSummary[]): void {
  targetZoneRateInformation.value = zoneRateInfo;
}

/**
 * Captures emit from JobUnitRateDetails component, which emits an updated value. Set to the local unit rate information.
 * @param unitRateInformation - The updated unit rate information.
 */
function updateUnitRateInformation(
  unitRateInformation: UnitRatePudSummary[],
): void {
  targetUnitRateInformation.value = unitRateInformation;
}

onMounted(() => {
  // Set initial data
  setInitialData();

  // If the jobDetails prop is not set, then we should close the dialog
  if (!props.jobDetails) {
    showDialog.value = false;
  }
});

/**
 * Return true if the user is an ADMIN or HEAD OFFICE user. Used to determine
 * whether a Cash Sale type job can be changed to a different Client.
 */
function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}

/**
 * Trigger app notification. Defaults to ERROR type message, but type can be
 * provided to produce other types. Includes componentTitle as a title for the
 * notification.
 * @param text - The notification text.
 * @param type - The notification type.
 */
function showAppNotification(text: string, type?: HealthLevel): void {
  showNotification(text, {
    type,
    title: componentTitle.value,
  });
}
</script>
<style scoped lang="scss">
// tr.subheader-row {
//   .td {
//     background-color: transparent;
//   }
//   font-weight: bold;
// }
.simple-data-table {
  tbody {
    color: var(--text-color);
    tr:nth-child(odd) {
      background-color: transparent;
    }
    tr:first-child {
      background-color: var(--table-row);
    }
  }
}
</style>
