export const jobListHeadersWithStatus: any[] = [
  {
    text: 'Actions',
    align: 'left',
    sortable: false,
    class: 'job-actions-table-header',
  },
  {
    text: 'Time',
    align: 'left',
    sortable: true,
    value: 'date',
    class: 'job-table-header',
  },
  {
    text: 'Job',
    align: 'left',
    sortable: true,
    value: 'displayId',
    class: 'job-table-header',
  },
  {
    text: 'Client',
    align: 'left',
    sortable: true,
    value: 'clientName',
    class: 'job-table-header',
  },
  {
    text: 'From',
    align: 'left',
    sortable: true,
    value: 'from',
    class: 'job-table-header',
  },
  {
    text: 'To',
    align: 'left',
    sortable: true,
    value: 'to',
    class: 'job-table-header',
  },
  {
    text: 'Service',
    align: 'left',
    sortable: true,
    value: 'service',
    class: 'job-table-header',
  },
  {
    text: 'Vehicle',
    align: 'left',
    sortable: true,
    value: 'csrAssignedId',

    class: 'job-table-header',
  },
  {
    text: 'Driver',
    align: 'left',
    sortable: true,
    value: 'driverName',
    class: 'job-table-header',
  },
  {
    text: 'Status',
    align: ' d-none',
    sortable: false,
    value: 'status',
    class: 'job-table-header',
  },
  {
    text: 'Dispatch Note',
    align: ' d-none',
    sortable: false,
    value: 'dispatchNote',
    class: 'job-table-header',
  },
];

export const jobListHeadersPointManager: any[] = [
  {
    text: 'Time',
    align: 'left',
    sortable: true,
    value: 'time',
    class: 'job-table-header--7',
  },
  {
    text: 'Client',
    align: 'left',
    sortable: true,
    value: 'client',
    class: 'job-table-header--7',
  },
  {
    text: 'Ref',
    align: 'left',
    sortable: true,
    value: 'primaryReference',
    class: 'job-table-header--7',
  },
  {
    text: 'Location',
    align: 'left',
    sortable: true,
    value: 'from.suburb',
    class: 'job-table-header--7',
  },
  {
    text: 'Driver',
    align: 'left',
    sortable: true,
    value: 'driver',
    class: 'job-table-header--7',
  },
];
export const jobListHeadersPointManagerSearch: any[] = [
  {
    text: 'Scheduled',
    align: 'left',
    sortable: true,
    value: 'pudDetails.epochTime',
  },
  {
    text: 'Client',
    align: 'left',
    sortable: true,
    value: 'client',
  },
  {
    text: 'Ref',
    align: 'left',
    sortable: true,
    value: 'primaryReference',
  },
  {
    text: 'Location',
    align: 'left',
    sortable: true,
    value: 'from.suburb',
  },
  {
    text: 'Driver',
    align: 'left',
    sortable: true,
    value: 'driver',
  },
  {
    text: 'Vehicle',
    align: 'left',
    sortable: true,
    value: 'vehicle',
  },
  {
    text: 'Grouped',
    align: 'left',
    sortable: true,
    value: 'groupReferenceId',
  },
  {
    text: 'Created Time',
    align: 'left',
    sortable: true,
    value: 'createdTime',
  },
  {
    text: 'Created By',
    align: 'left',
    sortable: true,
    value: 'createdBy',
  },
  {
    text: 'Status',
    align: 'left',
    sortable: true,
    value: 'assignedStatus',
  },
];
