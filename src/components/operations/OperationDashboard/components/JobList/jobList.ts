import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import OperationsSettingsDialog from '@/components/common/ui-elements/operations_settings_dialog.vue';
import JobListActionButtons from '@/components/operations/OperationDashboard/components/JobList/job_list_action_buttons/job_list_action_buttons.vue';
import {
  jobListHeadersPointManager,
  jobListHeadersWithStatus,
} from '@/components/operations/OperationDashboard/components/JobList/job_list_headers';
import {
  filterJobsBySelectedStatistic,
  filterJobsBySelectedStatusType,
  JobCategoriesAndCounts,
  jobFilterHandler,
  JobListGroup,
  JobListTabs,
  statusTypes,
} from '@/components/operations/OperationDashboard/components/JobList/JobListHelpers';
import UnassignedPudListActionButtons from '@/components/operations/OperationDashboard/components/JobList/unassigned_pud_list_action_buttons/index.vue';
import {
  initJobListGroupingFromLocalStorage,
  initOperationsDashboardSettingsFromLocalStorage,
  LOCAL_STORAGE_POPOUT_JOB_LIST_GROUPING,
  LOCAL_STORAGE_POPOUT_JOB_LIST_SETTINGS,
  SESSION_STORAGE_TOKEN,
} from '@/helpers/AppLayoutHelpers/LocalStorageHelpers';
import { filterJobsByOperationsChannel } from '@/helpers/AppLayoutHelpers/OperationsChannelHelpers';
import { initialiseFleetAssetSummary } from '@/helpers/classInitialisers/InitialiseFleetAsset';
import { initialiseJobDetails } from '@/helpers/classInitialisers/InitialiseJobDetails';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import {
  BroadcastChannelType,
  BroadcastIds,
  returnOperationsBroadcastChannelId,
} from '@/helpers/NotificationHelpers/BroadcastChannelHelpers';
import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import { JobStatistics } from '@/interface-models/Generic/DivisionJobsSummary/JobsStatistics';
import { BroadcastMessage } from '@/interface-models/Generic/OperationScreenOptions/BroadcastMessage';
import { JobListStatusGrouping } from '@/interface-models/Generic/OperationScreenOptions/JobListStatusGrouping';
import OperationsDashboardSetting, {
  jobListDefaultSettings,
} from '@/interface-models/Generic/OperationScreenOptions/OperationsDashboardSetting';
import { VPagination } from '@/interface-models/Generic/VPagination';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { JobSourceType } from '@/interface-models/Jobs/JobSourceType';
import { OperationJobSummary } from '@/interface-models/Jobs/OperationJobSummary';
import { PUDItemShort } from '@/interface-models/Jobs/PUD/PUDItemShort';
import UnassignedPudItemResponse from '@/interface-models/Jobs/PUD/UnassignedPudItem/SaveUnassignedPudItemResponse';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { OperationsChannel } from '@/interface-models/OperationsChannels/OperationsChannel';
import { useBroadcastChannelStore } from '@/store/modules/BroadcastChannelStore';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useDataImportStore } from '@/store/modules/DataImportStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useJobStatisticsStore } from '@/store/modules/JobStatisticsStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useFilterStore } from '@/store/modules/OperationsFilterStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { useRootStore } from '@/store/modules/RootStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import * as jwt from 'jose';
import moment from 'moment-timezone';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';

@Component({
  components: {
    JobListActionButtons,
    DatePickerBasic,
    OperationsSettingsDialog,
    UnassignedPudListActionButtons,
  },
})
export default class JobMonitoringWindow extends Vue {
  @Prop() public windowMode: boolean;
  @Prop() public currentIndex: number;
  @Prop() public selectedJobDetails: JobDetails;

  public fleetAssetStore = useFleetAssetStore();
  public companyDetailsStore = useCompanyDetailsStore();
  public dataImportStore = useDataImportStore();
  public operationsStore = useOperationsStore();
  public driverDetailsStore = useDriverDetailsStore();
  public filterStore = useFilterStore();

  public JobSourceType = JobSourceType;
  public WorkStatus = WorkStatus;
  public filterSettings: OperationsDashboardSetting[] = jobListDefaultSettings;
  public statusTypes = statusTypes;

  public driverInputText: string = '';
  public fleetAssetInputText: string = '';
  public currentServiceTypeId: number = 0;
  public currentRateTypeId: number = 0;

  public rerenderTrigger: boolean = true;
  public mainWindowRoute: string = 'operations_index';
  public openWindowActionIncrementer: number = 0;
  public selectedJobTypeTab: JobListTabs = JobListTabs.JOB_LIST;

  public requestingJobId: number = -1;

  public showActionsDisabledAlert: boolean = false;
  public actionsDisabledAlertTimeout: ReturnType<typeof setTimeout>;

  public localJobDetails: JobDetails | null = null;

  public activeUser = sessionManager.getActiveUser();

  get jobListSettings(): OperationsDashboardSetting[] {
    return JSON.parse(
      JSON.stringify(this.operationsStore.dashboardSettings.jobList),
    );
  }

  get jobDetails() {
    if (this.windowMode) {
      return this.localJobDetails;
    } else {
      return this.selectedJobDetails;
    }
  }

  get selectedStatusToFilter(): JobListStatusGrouping {
    return this.operationsStore.selectedJobListStatusGrouping;
  }
  set selectedStatusToFilter(value: JobListStatusGrouping) {
    if (value) {
      this.operationsStore.updateJobListStatusGrouping(value);
    }
  }

  get activeSettingsGroupType(): string {
    const foundActive = this.jobListSettings.find((s) => s.active);
    const activeType = foundActive
      ? foundActive.id
      : this.jobListSettings[0]
        ? this.jobListSettings[0].id
        : '';
    this.scrollToListTop();
    return activeType;
  }

  get tableHeaders() {
    let headers: any[] = [];
    if (this.selectedTabController !== JobListTabs.POINT_MANAGER) {
      headers = jobListHeadersWithStatus;
      let colCount = 9;
      const foundStatusItem = headers.find((h) => h.value === 'status');
      if (foundStatusItem) {
        if (this.showStatusColumn) {
          colCount++;
          foundStatusItem.align = 'left';
        } else {
          foundStatusItem.align = ' d-none';
        }
      }
      const foundDispatchNoteItem = headers.find(
        (h) => h.value === 'dispatchNote',
      );
      if (foundDispatchNoteItem) {
        if (this.windowMode) {
          colCount++;
          foundDispatchNoteItem.align = 'left';
        } else {
          foundDispatchNoteItem.align = ' d-none';
        }
      }
      headers.map((h) => {
        h.class = `job-table-header--${colCount}`;
        return h;
      });
      return headers;
    } else {
      return jobListHeadersPointManager;
    }
  }

  /**
   * Returns a boolean which determines whether the job list table displays the
   * current status of the job. This is only displayed when the job list when
   * the grouping on the table doesn't make it clear what status hte job is at,
   * and only in the job list popout where we have the additional space.
   */
  get showStatusColumn(): boolean {
    const isAllJobsView =
      this.selectedTabController === JobListTabs.JOB_LIST &&
      this.selectedStatusToFilter === JobListStatusGrouping.ALL_JOBS;
    const isRequiresAttention =
      this.selectedTabController === JobListTabs.REQUIRES_ATTENTION;
    return (isAllJobsView || isRequiresAttention) && this.windowMode;
  }

  public pagination: VPagination = {
    sortBy: 'jobId',
    descending: false,
    rowsPerPage: -1,
  };

  get driverList(): DriverDetailsSummary[] {
    return this.driverDetailsStore.getDriverList;
  }

  // Handler to scroll to top on tab change
  get selectedTabController(): JobListTabs {
    return this.selectedJobTypeTab;
  }
  set selectedTabController(value: JobListTabs) {
    if (value !== this.selectedJobTypeTab) {
      this.scrollToListTop();
    }
    // If the newly selected tab is not a filtered selection we clear the old selection in useJobStatisticsStore
    if (value !== JobListTabs.FILTERED_SELECTION) {
      useJobStatisticsStore().setFilteredSelection(null);
    }
    this.selectedJobTypeTab = value;
  }
  // Scrolls component back to top of list
  public scrollToListTop(): void {
    const element = document.getElementById('job-list-table-content');
    if (element) {
      element.scrollTop = 0;
    }
  }

  // List of all Fleet Assets
  get fleetAssetList() {
    return this.fleetAssetStore.getAllFleetAssetList ?? [];
  }

  get dateRange() {
    return this.operationsStore.jobListDateFilter;
  }
  // Handles emit from DatePickerBasic component. Uses the epochTime to set the
  // date range through which jobs are filtered
  public setDateRange(epoch: number) {
    this.operationsStore.setJobListDateFilter(epoch);
  }

  // Handle emit from settings dialog component
  // Update Operations Module
  public tableSettingsUpdated(settingsList: OperationsDashboardSetting[]) {
    this.operationsStore.updateDashboardSettingsJobList(settingsList);
  }

  public closeDriverValue() {
    this.driverInputText = '';
    this.fleetAssetInputText = '';
  }

  public openJobList() {
    this.$emit('openJobList');
  }
  // public openPudMaintenanceDialog() {
  //   const config: PudMaintenanceDialogConfig = {
  //     operationType: JobOperationType.NEW,
  //     pudType: PudMaintenanceType.UNASSIGNED,
  //   };
  //   this.operationsStore.setPudMaintenanceDialogConfig(config);
  //   this.operationsStore.setViewingPudMaintenanceDialog(true);
  // }

  get timezone() {
    return this.companyDetailsStore.userLocale;
  }

  public showLateJobHighlight(
    epochTime: number,
    status: string,
    minutesMinimum: number = 60,
  ): boolean {
    const allowedStatuses = [
      'UNALLOCATED',
      'DEALLOCATED',
      'ALLOCATED',
      'PREALLOCATED',
      'ACCEPTED',
    ];
    if (!allowedStatuses.includes(status)) {
      return false;
    }
    return this.returnTimeDifference(epochTime) > minutesMinimum;
  }
  public returnTimeDifference(epochTime: number | null): number {
    if (!epochTime) {
      return 0;
    }
    const timeNow = moment.tz(this.timezone).valueOf();
    const oneHour = moment.duration(8, 'hours').asMilliseconds();

    if (timeNow > epochTime && timeNow < epochTime + oneHour) {
      const lateness = timeNow - epochTime;
      const latenessAsMinutes = Math.floor(
        moment.duration(lateness).asMinutes(),
      );
      if (latenessAsMinutes > 0) {
        return latenessAsMinutes;
      }
    }
    return 0;
  }

  get jobStatisticsFilteredSelection(): JobStatistics | null {
    return useJobStatisticsStore().filteredSelection;
  }

  @Watch('jobStatisticsFilteredSelection')
  public jobStatisticsFilteredSelectionChange(value: JobStatistics) {
    if (value !== null) {
      this.selectedTabController = JobListTabs.FILTERED_SELECTION;
    }
  }

  // smallerWidthRequired is for width of the status filter select. It helps fix a design problem. eg. when "All Jobs" is selected there is a large space between the text and the drop down arrow. We use this computed property to set correct class name
  get smallerWidthRequired() {
    return this.selectedStatusToFilter !== JobListStatusGrouping.UNALLOCATED;
  }

  get totalUnassignedPudItems(): number {
    // Prevent unassignedPudListForToday getter from being called before the timezone broadcast message has arrived
    if (!this.isValidUserTimeZone) {
      return 0;
    }
    return this.dataImportStore.unassignedPudListForToday.length;
  }

  get filteredJobList(): OperationJobSummary[] {
    const {
      requiresAttention,
      completedWork,
      startOfDayCheckList,
      bookedByMe,
      filteredBySelectedStatistic,
      filteredBySelectedStatusType,
    } = this.jobCategoriesAndCounts;

    switch (this.selectedTabController) {
      case JobListTabs.REQUIRES_ATTENTION:
        return requiresAttention;
      case JobListTabs.COMPLETED_WORK:
        return completedWork;
      case JobListTabs.START_OF_DAY_CHECKLIST:
        return startOfDayCheckList;
      case JobListTabs.BOOKED_BY_ME:
        return bookedByMe;
      case JobListTabs.FILTERED_SELECTION:
        return filteredBySelectedStatistic;
      default:
        return filteredBySelectedStatusType;
    }
  }

  /**
   * Computes categorized job lists and their counts for use in tabs and filtering.
   * Each group is only filtered once for performance.
   *
   * Returns an object with:
   *  - requiresAttention: jobs needing attention
   *  - completedWork: jobs that are completed
   *  - startOfDayCheckList: jobs requiring start-of-day check
   *  - bookedByMe: jobs booked by the active user
   *  - filteredBySelectedStatistic: jobs matching the selected statistic filter
   *  - filteredBySelectedStatusType: jobs matching the selected status filter
   *  - counts: object with counts for each group
   */
  get jobCategoriesAndCounts(): JobCategoriesAndCounts {
    const allJobs = useJobStore().operationJobsList;
    const channels = this.filterStore.selectedOperationsChannel;
    // Filter the jobList by the currently selected OperationsChannel (if applicable)
    const jobsForChannel = filterJobsByOperationsChannel(allJobs, channels);

    // Jobs that require attention (status 57, 45, or outside hire)
    const requiresAttention = jobsForChannel.filter(
      (x: OperationJobSummary) =>
        x.statusList.includes(57) ||
        x.statusList.includes(45) ||
        x.isOutsideHire,
    );

    // Jobs that are completed (workStatus > IN_PROGRESS or all legs complete
    // but not driver completed)
    const completedWork = jobsForChannel.filter(
      (x: OperationJobSummary) =>
        x.workStatus > WorkStatus.IN_PROGRESS ||
        (x.numOfCompletedLegs === x.totalNumberOfLegs &&
          x.workStatus < WorkStatus.DRIVER_COMPLETED),
    );

    // Jobs that require a start-of-day checklist
    const startOfDayCheckList = jobsForChannel.filter(
      (x: OperationJobSummary) => x.startOfDayCheckRequired,
    );

    // Jobs booked by the active user
    const bookedByMe = jobsForChannel.filter(
      (j) => j.bookedBy === this.activeUser,
    );

    // Jobs filtered by the selected statistic (jobStatisticsFilteredSelection)
    const filteredBySelectedStatistic = filterJobsBySelectedStatistic(
      jobsForChannel,
      this.jobStatisticsFilteredSelection,
      this.activeUser,
    );

    // Jobs filtered by the selected status type (ALL )
    const filteredBySelectedStatusType = filterJobsBySelectedStatusType(
      jobsForChannel,
      this.dateRange,
      this.selectedStatusToFilter,
    );

    return {
      requiresAttention,
      completedWork,
      startOfDayCheckList,
      bookedByMe,
      filteredBySelectedStatistic,
      filteredBySelectedStatusType,
      counts: {
        requiresAttention: requiresAttention.length,
        completedWork: completedWork.length,
        startOfDayCheckList: startOfDayCheckList.length,
        bookedByMe: bookedByMe.length,
        filteredBySelectedStatistic: filteredBySelectedStatistic.length,
        filteredBySelectedStatusType: filteredBySelectedStatusType.length,
      },
    };
  }

  public triggerRerender() {
    this.rerenderTrigger = false;
    this.$nextTick(() => {
      this.rerenderTrigger = true;
    });
  }

  get operationsHasDialogOpen() {
    return (
      this.operationsStore.viewingJobDetailsDialog ||
      this.operationsStore.viewingJobNotesDialog ||
      this.operationsStore.viewingOutsideHireDetailsDialog
    );
  }

  get jobTableData(): OperationJobSummary[] {
    if (this.windowMode && this.timezone === '') {
      return [];
    }
    if (this.selectedTabController !== JobListTabs.POINT_MANAGER) {
      return this.filteredJobList;
    }

    // If we are looking at point manager we convert the UnassignedPudItem into a OperationJobSummary
    const tableData: OperationJobSummary[] = [];
    // Prevent unassignedPudListForToday getter from being called before the timezone broadcast message has arrived
    if (!this.isValidUserTimeZone) {
      return [];
    }
    this.dataImportStore.unassignedPudListForToday.forEach((item) => {
      if (!item.additionalJobData) {
        return;
      }

      const unassignedPudDetails: PUDItemShort = {
        pudId: '',
        suburb: item.pudDetails.address.suburb,
        createdByDriver: false,
        status: item.pudDetails.status,
        geoLocation: item.pudDetails.address.geoLocation,
        timeDefinition: item.pudDetails.timeDefinition,
      };
      const tableItem: OperationJobSummary = Object.assign(
        new OperationJobSummary(),
        {
          _id: item.id,
          recurringJobId: null,
          driverId: '',
          fleetAssetId: '',
          date: item.pudDetails.epochTime,
          startOfDayCheckRequired: false,
          jobId: item.jobId ? item.jobId : 0,
          clientId: item.clientId,
          clientName: item.additionalJobData.clientName
            ? item.additionalJobData.clientName
            : '-',
          serviceTypeId: -1,
          isUnassignedPudType: true,
          reference: item.clientSuppliedId ? item.clientSuppliedId : '-',
          statusList: [],
          driverIsTripRate: false,
          clientRateTypeId: -1,
          additionalEquipment: [],
          pudItems: [unassignedPudDetails],
          driverName: item.driverName,
        },
      );
      tableData.push(tableItem);
    });

    return tableData;
  }

  get serviceTypeJobList(): JobListGroup[] {
    if (!this.jobTableData) {
      return [];
    }
    const filteredServiceTypes: JobListGroup[] = jobFilterHandler({
      jobTableData: this.jobTableData,
      selectedTab: this.selectedTabController,
      activeGroupingType: this.activeSettingsGroupType,
    });
    return filteredServiceTypes;
  }

  public jobSelected(jobId: number, isExpanded: boolean) {
    if (this.windowMode) {
      // Show warning and return if we are unable to perform actions from
      // window, based on the current main window route
      if (this.disableJobListActions) {
        this.showActionsDisabledAlert = true;
        if (this.actionsDisabledAlertTimeout) {
          clearTimeout(this.actionsDisabledAlertTimeout);
        }
        this.actionsDisabledAlertTimeout = setTimeout(() => {
          this.showActionsDisabledAlert = false;
        }, 5000);
        return;
      }

      const data = new BroadcastMessage(
        BroadcastIds.JOB_LIST.TO_MAIN.SELECTED_JOB_ID,
        jobId,
      );
      useBroadcastChannelStore().postMessageToChannel(
        BroadcastChannelType.JOB_LIST,
        data,
      );
    } else {
      // if the row item is opened we get the full job details. If the user is
      // just closing the expanded panel we do not require a request for the
      // full job details as it will already exist
      if (isExpanded) {
        this.operationsStore.getFullJobDetails(jobId);
        this.operationsStore.setSelectedJobId(jobId);
      }
    }
  }

  // Find and replace the selected JobDetails in the list
  public processUpdatedJobDetails(operationJobSummary: OperationJobSummary) {
    operationJobSummary.setAdditionalInformation();
    useJobStore().updateOperationsSummaryState(operationJobSummary);
    this.openWindowActionIncrementer++;
  }

  public editJobInBookingScreen(jobId: number) {
    const data = new BroadcastMessage('editJobInBookingScreen', jobId);
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.JOB_LIST,
      data,
    );
  }
  public allocatePreallocatedJob(jobIds: number[]) {
    const data = new BroadcastMessage('allocatePreallocatedJob', jobIds);
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.JOB_LIST,
      data,
    );
  }
  public deallocateJob(jobId: number) {
    const data = new BroadcastMessage('deallocateJob', jobId);
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.JOB_LIST,
      data,
    );
  }
  public addNoteToJob(jobNoteInformation: {
    jobId: number;
    serviceFailure: boolean;
    cancelJob: boolean;
    dispatchNote: boolean;
    startOfDayCheckNote: boolean;
  }) {
    const data = new BroadcastMessage('addNoteToJob', jobNoteInformation);
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.JOB_LIST,
      data,
    );
  }
  public sendMessageToDriver(jobId: number) {
    const data = new BroadcastMessage('sendMessageToDriver', jobId);
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.JOB_LIST,
      data,
    );
  }
  public viewSelectedJobInReview(jobId: number) {
    const data = new BroadcastMessage('viewSelectedJobInReview', jobId);
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.JOB_LIST,
      data,
    );
  }
  public viewJobDetailsDialog(jobId: number) {
    const data = new BroadcastMessage('viewJobDetailsDialog', jobId);
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.JOB_LIST,
      data,
    );
  }

  // If not window mode, emit to parent to trigger collapse of all rows in
  // job_list and allocated_work tables where data-key is not equal to
  // selectedType If window mode, just collapse within this component
  public triggerRowCollapse(selectedType: string) {
    if (!this.windowMode) {
      this.$emit('closeAllExpandedRows', selectedType);
      return;
    } else {
      this.closeExpandedRows(selectedType);
    }
  }

  // Minimise all expanded rows in other tables (other than the table for the
  // name provided) ie. If you expand a row in the 1T section in the table, it
  // should minimize rows in all other tables and expand the selected
  public closeExpandedRows(selectedType: string) {
    const foundDataTables = this.$refs.expandableDataTable as any[];
    if (!foundDataTables) {
      return;
    }
    const filteredTables = foundDataTables.filter(
      (dt) => dt.$attrs['data-key'] !== selectedType,
    );

    filteredTables.forEach((table: any) => {
      if (table.expanded) {
        Object.keys(table.expanded).forEach((key) => {
          table.expanded[key] = false;
        });
      }
    });
  }

  get isValidUserTimeZone() {
    if (!this.windowMode) {
      return true;
    } else if (this.timezone === '') {
      return false;
    } else {
      return true;
    }
  }
  get jobListWindowOpen() {
    return this.operationsStore.operationOptions.jobListWindowOpen;
  }

  get disableJobListActions(): boolean {
    if (!this.windowMode) {
      return false;
    }
    const isOperationsPage = this.mainWindowRoute === 'operations_index';
    if (isOperationsPage && this.actionsDisabledAlertTimeout !== null) {
      this.showActionsDisabledAlert = false;
      clearTimeout(this.actionsDisabledAlertTimeout);
    }
    return !isOperationsPage;
  }

  public broadcastWindowClosed() {
    const data = new BroadcastMessage('jobListWindowOpen', false);
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.JOB_LIST,
      data,
    );
  }
  // Refresh popout window. Closes broadcast channel and removes eventListener
  // so 'jobListWindowOpen' message is not sent to main window (which would
  // close popout window)
  public refreshPopoutWindow() {
    useBroadcastChannelStore().closeChannel(BroadcastChannelType.JOB_LIST);
    window.removeEventListener('beforeunload', this.broadcastWindowClosed);
    // Refresh
    location.reload();
  }

  public created() {
    if (this.windowMode) {
      this.setUserCredentialsForWindowMode();
      const data = new BroadcastMessage('jobListWindowOpen', true);
      const broadcast = useBroadcastChannelStore().initBroadcastChannel(
        BroadcastChannelType.JOB_LIST,
      );
      broadcast.postMessage(data);

      broadcast.onmessage = (message: any) => {
        console.log(`JobList window - '${message.data.id}'`);
        switch (message.data.id) {
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.FULL_JOB_LIST:
            this.operationsStore.setJobListWindow(true);
            useJobStore().updateOperationsJobListState(
              message.data.value,
              false,
            );
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.UNASSIGNED_PUD_ITEM_LIST:
            if (message.data.value && message.data.value.length) {
              // Construct UnassignedPudItem list as response model so we can
              // pass into mutation
              const asResponseObject: UnassignedPudItemResponse = {
                unassignedPudItemList: message.data.value,
              };
              this.dataImportStore.setUnassignedPudItemList(asResponseObject);
            }
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.FLEET_ASSET_LIST:
            const fleetAssetList: FleetAssetSummary[] = (
              message.data.value ?? []
            ).map(initialiseFleetAssetSummary);
            this.fleetAssetStore.initialiseFleetAssetSummaryMap(fleetAssetList);
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.ALL_DRIVERS:
            this.driverDetailsStore.setDriverSummaryList(
              message.data.value,
              false,
            );
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.STATUS_TYPE_LIST:
            useRootStore().updateStatusConfigListState(message.data.value);
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.SERVICE_TYPES_LIST:
            this.companyDetailsStore.setServiceTypeList(message.data.value);
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.CLIENT_LIST:
            useClientDetailsStore().setClientSummaryList(
              message.data.value ?? [],
            );
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.UPDATED_JOB_DETAILS:
            this.processUpdatedJobDetails(
              Object.assign(new OperationJobSummary(), message.data.value),
            );
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.DIVISION_DETAILS:
            if (!message.data.value) {
              logConsoleError(
                'JobList - Division details not found in message data.',
              );
              return;
            }
            this.companyDetailsStore.setDivisionDetails(message.data.value);
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.ACTIVE_USER_NAME:
            if (message.data.value) {
              sessionManager.setActiveUser(message.data.value);
            }
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.ROUTE_CHANGED:
            this.mainWindowRoute = message.data.value;
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.INITIAL_DATE_FILTER:
            this.operationsStore.setJobListDateFilter(
              message.data.value.startDate,
            );
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.CLOSE_WINDOW:
            window.close();
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.SELECTED_JOB_DETAILS:
            this.localJobDetails = initialiseJobDetails(message.data.value);
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.OWNER_LIST:
            useFleetAssetOwnerStore().setFleetAssetOwnerSummaryList(
              message.data.value,
            );
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.OPERATIONS_CHANNEL_LIST:
            let channelList = message.data.value;
            if (Array.isArray(channelList)) {
              channelList = channelList.map(
                (channel) => new OperationsChannel(channel),
              );
            }
            useFilterStore().setOperationsChannels(channelList);
            break;
          case BroadcastIds.JOB_LIST.TO_EXTERNAL.OPERATIONS_CHANNEL_UPDATED:
            useFilterStore().setSelectedOperationsChannelIds(
              message.data.value,
            );
            break;
        }
      };
      // Add eventListener that sends a message to the main window when the
      // Before mounting of component, check Local Storage for existing settings and
      // set to store. This only occurs in windowMode when we don't have access to
      // the same store data.
      initOperationsDashboardSettingsFromLocalStorage(
        LOCAL_STORAGE_POPOUT_JOB_LIST_SETTINGS,
      );
      initJobListGroupingFromLocalStorage(
        LOCAL_STORAGE_POPOUT_JOB_LIST_GROUPING,
      );

      // popout is closed, such that the boolean in the OperationsModule can be
      // updated
      window.addEventListener('beforeunload', this.broadcastWindowClosed);
    } else {
      Mitt.on('perMinuteTrigger', this.triggerRerender);
    }
  }
  public beforeDestroy() {
    Mitt.off('perMinuteTrigger', this.triggerRerender);
  }
  // In the popout window we need to commit token to the store so we can use the
  // values to create the BroadcastChannel. Session storage with the popout
  // window so we are able to access and commit to store.
  public setUserCredentialsForWindowMode() {
    try {
      // Get from session storage
      const token = sessionStorage.getItem(SESSION_STORAGE_TOKEN);
      if (token === null) {
        throw new Error('No token found in session storage for window mode.');
      }
      // Decode and commit to store in same format as the response after login
      const decoded = jwt.decodeJwt(token);
      sessionManager.setPropertiesFromToken({
        access_token: token,
        ...decoded,
      });

      // Construct id and commit to store
      const broadcastId = returnOperationsBroadcastChannelId(
        sessionManager.getUserName(),
        sessionManager.getJtiSessionId(),
      );

      useBroadcastChannelStore().setOperationsBroadcastId(broadcastId);
      // Create broadcast channel
      useBroadcastChannelStore().initBroadcastChannel(
        BroadcastChannelType.JOB_LIST,
      );
    } catch (error) {
      logConsoleError(
        'Error setting user credentials for window mode: ',
        error,
      );
    }
  }
}
