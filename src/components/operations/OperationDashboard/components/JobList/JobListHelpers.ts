import { returnStartOfDayFromEpoch } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { returnCurrentExactJobStatus } from '@/helpers/StatusHelpers/StatusHelpers';
import { DateRange } from '@/interface-models/Generic/DateRange/DateRange';
import { JobStatistics } from '@/interface-models/Generic/DivisionJobsSummary/JobsStatistics';
import { JobListStatusGrouping } from '@/interface-models/Generic/OperationScreenOptions/JobListStatusGrouping';
import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import { AccountingStatus } from '@/interface-models/Jobs/AccountingStatus';
import { OperationJobSummary } from '@/interface-models/Jobs/OperationJobSummary';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';

export interface JobCategoriesAndCounts {
  requiresAttention: OperationJobSummary[];
  completedWork: OperationJobSummary[];
  startOfDayCheckList: OperationJobSummary[];
  bookedByMe: OperationJobSummary[];
  filteredBySelectedStatistic: OperationJobSummary[];
  filteredBySelectedStatusType: OperationJobSummary[];
  counts: {
    requiresAttention: number;
    completedWork: number;
    startOfDayCheckList: number;
    bookedByMe: number;
    filteredBySelectedStatistic: number;
    filteredBySelectedStatusType: number;
  };
}
export interface JobListGroup {
  name: string;
  jobs: OperationJobSummary[];
}

export enum JobListTabs {
  JOB_LIST = 'JOB_LIST',
  REQUIRES_ATTENTION = 'REQUIRES_ATTENTION',
  COMPLETED_WORK = 'COMPLETED_WORK',
  POINT_MANAGER = 'POINT_MANAGER',
  START_OF_DAY_CHECKLIST = 'START_OF_DAY_CHECKLIST',
  BOOKED_BY_ME = 'BOOKED_BY_ME',
  FILTERED_SELECTION = 'FILTERED_SELECTION',
}

export interface StatusFilterMapping {
  id: JobListStatusGrouping;
  match: WorkStatus | null;
  from: WorkStatus | null;
  to: WorkStatus | null;
  name: string;
}

export const statusTypes: StatusFilterMapping[] = [
  {
    id: JobListStatusGrouping.ALL_JOBS,
    match: null,
    from: WorkStatus.BOOKED,
    to: WorkStatus.REVIEWED,
    name: 'All Jobs',
  },
  {
    id: JobListStatusGrouping.UNALLOCATED,
    match: null,
    from: WorkStatus.BOOKED,
    to: WorkStatus.PREALLOCATED,
    name: 'Unallocated',
  },
  {
    id: JobListStatusGrouping.ALLOCATED,
    match: WorkStatus.ALLOCATED,
    from: null,
    to: null,
    name: 'Allocated',
  },
  {
    id: JobListStatusGrouping.ACCEPTED,
    match: WorkStatus.ACCEPTED,
    from: null,
    to: null,
    name: 'Accepted',
  },
  {
    id: JobListStatusGrouping.STARTED,
    match: WorkStatus.IN_PROGRESS,
    from: null,
    to: null,
    name: 'In-Progress',
  },
  {
    id: JobListStatusGrouping.COMPLETED,
    match: WorkStatus.DRIVER_COMPLETED,
    from: null,
    to: null,
    name: 'Completed',
  },
  {
    id: JobListStatusGrouping.REVIEWED,
    match: WorkStatus.REVIEWED,
    from: null,
    to: null,
    name: 'Reviewed',
  },
];

export function jobFilterHandler({
  jobTableData,
  activeGroupingType,
  selectedTab,
}: {
  jobTableData: OperationJobSummary[];
  selectedTab: JobListTabs;
  activeGroupingType: string;
}): JobListGroup[] {
  const isCompletedWorkTab = selectedTab === JobListTabs.COMPLETED_WORK;
  const isPointManagerTab = selectedTab === JobListTabs.POINT_MANAGER;
  const isStartOfDayChecklistTab =
    selectedTab === JobListTabs.START_OF_DAY_CHECKLIST;
  const isBookedByMeTab = selectedTab === JobListTabs.BOOKED_BY_ME;

  let groupingType = activeGroupingType;
  const jobListGroups: JobListGroup[] = [];
  // If viewing the COMPLETED WORK tab, create three groups only
  if (isCompletedWorkTab) {
    jobListGroups.push({
      name: 'NO REMAINING STOPS',
      jobs: jobTableData.filter(
        (j) =>
          j.numOfCompletedLegs === j.totalNumberOfLegs &&
          j.workStatus < WorkStatus.DRIVER_COMPLETED,
      ),
    });
    jobListGroups.push({
      name: 'DRIVER COMPLETED',
      jobs: jobTableData.filter(
        (j) => j.workStatus === WorkStatus.DRIVER_COMPLETED,
      ),
    });
    jobListGroups.push({
      name: 'REVIEWED',
      jobs: jobTableData.filter((j) => j.workStatus === WorkStatus.REVIEWED),
    });
    return jobListGroups;
  }

  if (isBookedByMeTab) {
    jobListGroups.push({
      name: 'BOOKED BY ME',
      jobs: jobTableData,
    });
    return jobListGroups;
  }
  if (isStartOfDayChecklistTab) {
    const startOfDayCheckListWorkStatuses: WorkStatus[] = [
      WorkStatus.BOOKED,
      WorkStatus.PREALLOCATED,
      WorkStatus.ALLOCATED,
      WorkStatus.ACCEPTED,
      WorkStatus.IN_PROGRESS,
    ];
    startOfDayCheckListWorkStatuses.forEach((workStatus) => {
      const name: string = returnCurrentExactJobStatus(
        workStatus,
        AccountingStatus.NOT_REQUIRED,
        AccountingStatus.NOT_REQUIRED,
      );

      const jobs = jobTableData.filter((j) => j.status === name);

      if (jobs.length > 0) {
        jobListGroups.push({
          name,
          jobs,
        });
      }
    });

    return jobListGroups;
  }
  // If viewing the POINT MANAGER tab, disregard settings and sort by client only
  if (isPointManagerTab) {
    groupingType = 'sortByClient';
  }
  // Otherwise we should look at whatever the active grouping type filter
  if (groupingType === 'sortByServiceType') {
    // Add a blank service type. This so we can display jobs that do not
    // currently have a service type associated with it. (Imports)
    const blankServiceType: ServiceTypes = {
      _id: '',
      company: '',
      division: '',
      serviceTypeId: 0,
      shortServiceTypeName: 'NONE',
      longServiceTypeName: 'No Service Type',
      availableJobInputScreens: [1, 2],
      optionSelectName: 'NONE',
      recurringRequirement: false,
      fuelSurcharge: true,
      divisionService: true,
      allocationTypes: [
        {
          fleetAssetType: 1,
          subTypes: [],
        },
      ],
      displayOrder: 0,
    };
    const serviceTypeList = [
      blankServiceType,
      ...useCompanyDetailsStore().getServiceTypesList,
    ];
    // Return a group for each unique service type, where at least one exists
    // for that service type.
    for (const type of serviceTypeList) {
      const filteredJobs = jobTableData.filter(
        (job) => job.serviceTypeId === type.serviceTypeId,
      );
      if (filteredJobs.length > 0) {
        const filteredItem: JobListGroup = {
          name: type.longServiceTypeName,
          jobs: filteredJobs,
        };
        jobListGroups.push(filteredItem);
      }
    }
  } else if (groupingType === 'sortByClient') {
    // Return a group for each unique client trading name
    const uniqueClientIds = [...new Set(jobTableData.map((s) => s.clientId))];
    uniqueClientIds.forEach((clientId) => {
      let clientName = '';
      const jobsForClient = jobTableData.filter(
        (job) => job.clientId === clientId,
      );
      if (jobsForClient.length > 0) {
        clientName = jobsForClient[0] ? jobsForClient[0].clientName : '-';
        const filteredClientItem = {
          name: clientName,
          jobs: jobsForClient,
        };
        jobListGroups.push(filteredClientItem);
      }
    });
  } else if (groupingType === 'sortByFleetAsset') {
    const allocatedWork = jobTableData.filter((jtd) => jtd.isAllocated);
    const unallocatedWork = jobTableData.filter((jtd) => !jtd.isAllocated);
    const uniqueFleetAssetIds = [
      ...new Set(allocatedWork.map((s) => s.vehicle)),
    ];
    if (unallocatedWork.length > 0) {
      const filteredClientItem = {
        name: 'UNALLOCATED',
        jobs: unallocatedWork,
      };
      jobListGroups.push(filteredClientItem);
    }
    uniqueFleetAssetIds.forEach((csrAssignedId) => {
      const jobsForFleet = allocatedWork.filter(
        (job) => job.vehicle === csrAssignedId,
      );
      if (jobsForFleet.length > 0) {
        const filteredClientItem = {
          name: csrAssignedId,
          jobs: jobsForFleet,
        };
        jobListGroups.push(filteredClientItem);
      }
    });
  } else if (groupingType === 'sortByJobStatus') {
    // Return a group for each status that contains jobs. Note that we require filtering on COMPLETED_ACTION_REQUIRED. This is currently problematic as COMPLETED_ACTION_REQUIRED is not a main job status.
    const defaultStatusOrder: Array<WorkStatus | string> = [
      WorkStatus.BOOKED,
      WorkStatus.PREALLOCATED,
      WorkStatus.ALLOCATED,
      WorkStatus.ACCEPTED,
      WorkStatus.IN_PROGRESS,
      WorkStatus.DRIVER_COMPLETED,
      'COMPLETED - ACTION REQUIRED',
      WorkStatus.REVIEWED,
    ];
    defaultStatusOrder.forEach((status) => {
      const isCompletedActionRequired =
        status === 'COMPLETED - ACTION REQUIRED';

      const jobsForStatus: OperationJobSummary[] = jobTableData.filter((job) =>
        !isCompletedActionRequired
          ? job.workStatus === status && !job.statusList.includes(45)
          : job.statusList.includes(45),
      );
      // If a category has at least 1 job then add it to the list to be displayed
      if (jobsForStatus.length > 0) {
        const filteredClientItem = {
          name: !isCompletedActionRequired
            ? jobsForStatus[0].status
            : 'COMPLETED - ACTION REQUIRED',
          jobs: jobsForStatus,
        };

        jobListGroups.push(filteredClientItem);
      }
    });
  } else if (groupingType === 'unsorted') {
    const allJobItems = {
      name: 'All Jobs',
      jobs: jobTableData,
    };
    jobListGroups.push(allJobItems);
  }
  return jobListGroups;
}

// returns jobs based on the selected job statistics filtering
export function filterJobsBySelectedStatistic(
  allJobsList: OperationJobSummary[],
  statisticsFilteredSelection: JobStatistics | null,
  activeUser: string,
): OperationJobSummary[] {
  if (!statisticsFilteredSelection) {
    return [];
  }

  const { key, workStatus, startDate, endDate } = statisticsFilteredSelection;
  const isNoDateRangeApplied = startDate === 0 && endDate === 0;

  return allJobsList.filter((job: OperationJobSummary) => {
    const isWithinDateRange =
      isNoDateRangeApplied ||
      (returnStartOfDayFromEpoch(job.date) >= startDate &&
        returnStartOfDayFromEpoch(job.date) <= endDate);

    const isWithinTodayDateRange =
      isNoDateRangeApplied ||
      (returnStartOfDayFromEpoch(job.bookedAt) >= startDate &&
        returnStartOfDayFromEpoch(job.bookedAt) <= endDate);

    if (key === 'Booked Today By Me') {
      return job.bookedBy === activeUser && isWithinTodayDateRange;
    }

    if (!workStatus) {
      return isWithinDateRange;
    }

    return job.workStatus === workStatus && isWithinDateRange;
  });
}

// Returns a list of jobs that fit the selectedStatusToFilter. If the status filter fails it will return jobs filtered via date range.
export function filterJobsBySelectedStatusType(
  allJobsList: OperationJobSummary[],
  dateRange: DateRange | null,
  selectedStatusGrouping: JobListStatusGrouping,
): OperationJobSummary[] {
  // If there is no date range and also no filter options, return all jobs.
  if (!dateRange) {
    return allJobsList;
  }
  // filter jobs by the date range
  const jobListByDate = allJobsList.filter((job) => {
    const startDate = dateRange.startDate;
    const endDate = dateRange.endDate;
    return job.date >= startDate && job.date <= endDate;
  });

  const statusFilterMapping: StatusFilterMapping | undefined = statusTypes.find(
    (mapping) => mapping.id === selectedStatusGrouping,
  );

  if (!statusFilterMapping) {
    return jobListByDate;
  }
  const { match, from, to } = statusFilterMapping;

  return jobListByDate.filter((job: OperationJobSummary) => {
    const workStatus = job.workStatus;
    const isEmptyFilter: boolean = !match && !from && !to;
    if (isEmptyFilter) {
      return false;
    }
    const isWorkStatusMatch = workStatus === match;

    if (isWorkStatusMatch) {
      return true;
    }

    if (!from && !to) {
      return false;
    }

    if ((from && workStatus < from) || (to && workStatus > to)) {
      return false;
    }

    return true;
  });
}
