<div class="job-list-container" :class="windowMode ? 'window-mode' : ''">
  <div
    style="position: absolute; bottom: 0px; z-index: 10; padding: 8px"
    v-if="windowMode"
  >
    <v-alert
      v-if
      type="warning"
      color="amber darken-2"
      :value="showActionsDisabledAlert"
      >Jobs may only be actioned in the Operations Screen. Please return the
      Main Window to the Operations Screen, then try again.
    </v-alert>
  </div>
  <v-layout
    class="task-bar app-theme__center-content--header no-highlight"
    style="padding-left: 0px; min-height: 30px"
    id="jobs-list-task-bar"
  >
    <v-tabs
      height="26"
      v-model="selectedTabController"
      :active-class="'tab-active'"
      hide-slider
      class="tab-select-container"
    >
      <v-tooltip right v-if="filterStore.isOperationsChannelFilterApplied">
        <template #activator="{ on }">
          <v-btn
            v-on="on"
            @click="filterStore.setSelectedOperationsChannelIds(null)"
            class="filter-btn clear"
            fab
            outline
          >
            <v-icon size="14">clear</v-icon>
          </v-btn>
        </template>
        <span>Clear Job Filters</span>
      </v-tooltip>
      <v-tab href="#JOB_LIST" class="tab-selector">
        <p class="ma-0 tab-text">Job List</p>
      </v-tab>

      <v-tab href="#REQUIRES_ATTENTION" class="tab-selector">
        <p class="ma-0 tab-text">
          Req. Attention ({{jobCategoriesAndCounts.counts.requiresAttention}})
        </p>
      </v-tab>
      <v-tab href="#COMPLETED_WORK" class="tab-selector">
        <p class="ma-0 tab-text">
          Completed ({{jobCategoriesAndCounts.counts.completedWork}})
        </p>
      </v-tab>
      <v-tab
        href="#POINT_MANAGER"
        class="tab-selector"
        v-if="totalUnassignedPudItems || selectedTabController === 'POINT_MANAGER'"
      >
        <p class="ma-0 tab-text">Point Manager ({{totalUnassignedPudItems}})</p>
      </v-tab>
      <v-tab
        href="#START_OF_DAY_CHECKLIST"
        class="tab-selector"
        v-if="jobCategoriesAndCounts.counts.startOfDayCheckList || selectedTabController === 'START_OF_DAY_CHECKLIST'"
      >
        <p class="ma-0 tab-text">
          Start of Day ({{jobCategoriesAndCounts.counts.startOfDayCheckList}})
        </p>
      </v-tab>
      <v-tab
        href="#FILTERED_SELECTION"
        class="tab-selector"
        v-if="!windowMode && jobStatisticsFilteredSelection"
      >
        <p class="ma-0 tab-text">
          {{jobStatisticsFilteredSelection.key}}
          ({{jobCategoriesAndCounts.counts.filteredBySelectedStatistic}})
        </p>
      </v-tab>
      <v-tab
        href="#BOOKED_BY_ME"
        class="tab-selector"
        v-if="selectedTabController === 'BOOKED_BY_ME'"
      >
        <p class="ma-0 tab-text">
          Booked by Me ({{jobCategoriesAndCounts.counts.bookedByMe}})
        </p>
      </v-tab>
    </v-tabs>
    <v-spacer />
    <v-sheet
      color="amber darken-2"
      class="black--text mr-3 px-2"
      style="border-radius: 2px"
      v-if="disableJobListActions"
    >
      Return to the <strong>Operations Screen</strong> to action jobs
    </v-sheet>

    <div
      class="date-filter-container pr-4"
      v-if="dateRange && isValidUserTimeZone && selectedTabController === 'JOB_LIST'"
    >
      <DatePickerBasic
        @setEpoch="setDateRange"
        :boxInput="true"
        labelName=""
        :formDisabled="false"
        :yearOnly="false"
        :timeZone="timezone"
        :epochTime="dateRange.startDate"
        :hideIcon="true"
        :showNextPrevDayArrows="true"
      >
      </DatePickerBasic>
    </div>

    <div
      id="status-select-container"
      :class="smallerWidthRequired ? 'smallerWidth' : ''"
      v-if="selectedTabController === 'JOB_LIST'"
    >
      <v-select
        box
        :items="statusTypes"
        color="#64ffda"
        hide-details
        item-text="name"
        item-value="id"
        v-model="selectedStatusToFilter"
        class="v-solo-custom"
      />
    </div>

    <v-menu left :key="activeSettingsGroupType">
      <template v-slot:activator="{ on }">
        <v-icon size="14" class="filter-icon" v-on="on"
          >fas fa-sliders-h
        </v-icon>
      </template>
      <v-list class="v-list-custom" dense>
        <v-list-tile v-if="windowMode" @click="refreshPopoutWindow">
          <v-list-tile-avatar>
            <v-icon size="16">fal fa-sync</v-icon>
          </v-list-tile-avatar>
          <v-list-tile-title class="pr-2"> Refresh Job List </v-list-tile-title>
        </v-list-tile>
        <v-list-tile @click.stop="" v-if="$vuetify.breakpoint.mdAndDown">
          <v-list-tile-avatar>
            <v-icon size="16">fal fa-calendar</v-icon>
          </v-list-tile-avatar>
          <v-list-tile-content class="pr-2">
            <div class="date-filter-menu-container">
              <DatePickerBasic
                @setEpoch="setDateRange"
                :boxInput="false"
                labelName=""
                :formDisabled="false"
                :yearOnly="false"
                :timeZone="timezone"
                :epochTime="dateRange.startDate"
                :hideIcon="true"
                :showNextPrevDayArrows="true"
                :soloInput="true"
              ></DatePickerBasic>
            </div>
          </v-list-tile-content>
        </v-list-tile>
        <v-list-tile
          @click="openJobList"
          :disabled="jobListWindowOpen"
          v-if="!windowMode"
        >
          <v-list-tile-avatar>
            <v-icon size="16">fal fa-expand</v-icon>
          </v-list-tile-avatar>
          <v-list-tile-title class="pr-2">
            Open in New Window
          </v-list-tile-title>
        </v-list-tile>

        <!-- <v-list-tile @click="openPudMaintenanceDialog" v-if="!windowMode">
          <v-list-tile-avatar>
            <v-icon size="16">fal fa-plus</v-icon>
          </v-list-tile-avatar>
          <v-list-tile-title class="pr-2">
            Add New Point
          </v-list-tile-title>
        </v-list-tile> -->
        <v-divider></v-divider>
        <v-list-tile @click="selectedTabController = 'JOB_LIST'">
          <v-list-tile-avatar>
            <v-icon
              :color="selectedTabController === 'JOB_LIST' ? '#64ffda' : '#1c1c1f'"
              size="14"
              >fas fa-circle</v-icon
            >
          </v-list-tile-avatar>
          <v-list-tile-title class="pr-2"> All Jobs </v-list-tile-title>
        </v-list-tile>
        <v-list-tile @click="selectedTabController = 'REQUIRES_ATTENTION'">
          <v-list-tile-avatar>
            <v-icon
              :color="selectedTabController === 'REQUIRES_ATTENTION' ? '#64ffda' : '#1c1c1f'"
              size="14"
              >fas fa-circle</v-icon
            >
          </v-list-tile-avatar>
          <v-list-tile-title class="pr-2">
            Req. Attention ({{jobCategoriesAndCounts.counts.requiresAttention}})
          </v-list-tile-title>
        </v-list-tile>
        <v-list-tile @click="selectedTabController = 'COMPLETED_WORK'">
          <v-list-tile-avatar>
            <v-icon
              :color="selectedTabController === 'COMPLETED_WORK' ? '#64ffda' : '#1c1c1f'"
              size="14"
              >fas fa-circle</v-icon
            >
          </v-list-tile-avatar>
          <v-list-tile-title class="pr-2">
            Completed Jobs ({{jobCategoriesAndCounts.counts.completedWork}})
          </v-list-tile-title>
        </v-list-tile>
        <v-list-tile @click="selectedTabController = 'POINT_MANAGER'">
          <v-list-tile-avatar>
            <v-icon
              :color="selectedTabController === 'POINT_MANAGER' ? '#64ffda' : '#1c1c1f'"
              size="14"
              >fas fa-circle</v-icon
            >
          </v-list-tile-avatar>
          <v-list-tile-title class="pr-2">
            Point Manager ({{totalUnassignedPudItems}})
          </v-list-tile-title>
        </v-list-tile>
        <v-list-tile @click="selectedTabController = 'START_OF_DAY_CHECKLIST'">
          <v-list-tile-avatar>
            <v-icon
              :color="selectedTabController === 'START_OF_DAY_CHECKLIST' ? '#64ffda' : '#1c1c1f'"
              size="14"
              >fas fa-circle</v-icon
            >
          </v-list-tile-avatar>
          <v-list-tile-title class="pr-2">
            Start of Day Checklist
            ({{jobCategoriesAndCounts.counts.startOfDayCheckList}})
          </v-list-tile-title>
        </v-list-tile>
        <v-list-tile @click="selectedTabController = 'BOOKED_BY_ME'">
          <v-list-tile-avatar>
            <v-icon
              :color="selectedTabController === 'BOOKED_BY_ME' ? '#64ffda' : '#1c1c1f'"
              size="14"
              >fas fa-circle</v-icon
            >
          </v-list-tile-avatar>
          <v-list-tile-title class="pr-2"> Booked by Me </v-list-tile-title>
        </v-list-tile>
        <v-divider></v-divider>
        <OperationsSettingsDialog
          key="job-list-settings-dialog"
          buttonText="Table Settings"
          title="Job List - Table Settings"
          @confirm="tableSettingsUpdated"
          :buttonDisabled="false"
          buttonColor="teal accent-3"
          faIconName="fal fa-sliders-h"
          :isListTile="true"
          :useLeadingIcon="true"
          :settingsList="jobListSettings"
          :defaultSettingsList="filterSettings"
        >
        </OperationsSettingsDialog>
      </v-list>
    </v-menu>
  </v-layout>
  <div class="table-content">
    <div class="table-scrollable" id="job-list-table-content">
      <v-card flat>
        <v-data-table
          class="headers-only-table"
          :pagination.sync="pagination"
          :headers="tableHeaders"
          :items="[]"
          hide-actions
        >
          <template v-slot:no-data>
            <span></span>
          </template>
        </v-data-table>
      </v-card>
      <v-card
        flat
        class="serviceTypeJobs"
        v-for="(service, index) of serviceTypeJobList"
        :key="service.name"
      >
        <v-layout class="service-name" align-center>
          <p class="px-4">{{service.name}}</p>
        </v-layout>
        <v-data-table
          :headers="tableHeaders"
          :items="service.jobs"
          hide-actions
          item-key="_id"
          :key="selectedJobTypeTab"
          :pagination.sync="pagination"
          ref="expandableDataTable"
          :data-key="service.name"
          no-data-text="No jobs found for this category."
        >
          <template v-slot:items="props">
            <tr
              :class="[
                showLateJobHighlight(props.item.date, props.item.status) ? 'table-row__highlight' : '',
                props.expanded ? 'table-row__expanded' : '',
                props.index % 2 === 0 ? 'even-row' : 'odd-row',
                props.item.creditStatus === 2 ? 'muted-text' : ''
              ]"
              style="cursor: pointer"
              class="jobList--table-row"
              @click="props.expanded = !props.expanded; props.item.isUnassignedPudType ? null : jobSelected(props.item.jobId, props.expanded); triggerRowCollapse(service.name)"
            >
              <td
                class="text-xs-left pl-2"
                v-if="!props.item.isUnassignedPudType"
              >
                <v-icon
                  class="job__visualindicators"
                  :class="props.item.customerContacted ? 'displayicon' : 'hidden'"
                  color="green accent-3"
                  size="12"
                >
                  fas fa-circle
                </v-icon>
                <v-icon
                  class="job__visualindicators"
                  :class="props.item.driverContacted ? 'displayicon' : 'hidden'"
                  color="blue accent-3"
                  size="12"
                >
                  fas fa-circle
                </v-icon>
                <v-icon
                  class="job__visualindicators"
                  :class="props.item.jobActioned ? 'displayicon' : 'hidden'"
                  color="yellow accent-3"
                  size="12"
                >
                  fas fa-circle
                </v-icon>
                <v-icon
                  class="job__visualindicators"
                  :class="props.item.jobDelayed ? 'displayicon' : 'hidden'"
                  color="red accent-3"
                  size="12"
                >
                  fas fa-circle
                </v-icon>
                <v-tooltip
                  bottom
                  v-if="props.item.jobSourceType === JobSourceType.CLIENT"
                >
                  <template v-slot:activator="{ on }">
                    <v-icon
                      v-on="on"
                      class="job__visualindicators"
                      color="purple accent-1"
                      size="12"
                    >
                      fas fa-concierge-bell
                    </v-icon>
                  </template>
                  Booked By Client
                </v-tooltip>
                <v-tooltip
                  bottom
                  v-if="props.item.jobSourceType === JobSourceType.IMPORT || props.item.jobSourceType === JobSourceType.API"
                >
                  <template v-slot:activator="{ on }">
                    <v-icon
                      v-on="on"
                      class="job__visualindicators"
                      color="cyan accent-2"
                      size="12"
                    >
                      fas fa-file-import
                    </v-icon>
                  </template>
                  Booked By Importer
                </v-tooltip>
                <v-tooltip bottom style="z-index: 200">
                  <template v-slot:activator="{ on }">
                    <v-icon
                      v-on="on"
                      class="job__visualindicators"
                      :class="props.item.pudAddedByDriver ? 'displayicon' : 'hidden'"
                      color="green accent-2"
                      size="12"
                    >
                      fas fa-steering-wheel
                    </v-icon>
                  </template>
                  Driver Created Pickup/Dropoff
                </v-tooltip>
                <v-tooltip bottom>
                  <template v-slot:activator="{ on }">
                    <v-icon
                      v-on="on"
                      class="job__visualindicators"
                      :class="props.item.isOutsideHire ? 'displayicon' : 'hidden'"
                      color="lime accent-2"
                      size="12"
                    >
                      fas fa-user-friends
                    </v-icon>
                  </template>
                  Outside Hire
                </v-tooltip>
              </td>

              <td class="text-xs-left job-list-table-column">
                <span
                  v-if="selectedTabController === 'REQUIRES_ATTENTION' ||
              selectedTabController === 'COMPLETED_WORK' ||
              selectedTabController === 'FILTERED_SELECTION' || selectedTabController === 'BOOKED_BY_ME'"
                  >{{ props.item.readableJobDate }}</span
                ><span v-else> {{props.item.readable24HrTime}} </span>
                <v-icon
                  size="14"
                  color="green"
                  class="ml-1 mr-1"
                  v-if="props.item.pudItems[0].timeDefinition === 9"
                  >timer</v-icon
                >
                <span
                  class="pr-1"
                  v-if="rerenderTrigger && !props.item.isUnassignedPudType"
                >
                  {{showLateJobHighlight(props.item.date, props.item.status, 0)
                  ? `-${returnTimeDifference(props.item.date)}` : '' }}
                </span>
              </td>
              <td
                class="text-xs-left"
                :class="props.item.isRecurring ? 'recurring-job-id__highlight' : ''"
                v-if="!props.item.isUnassignedPudType"
              >
                {{ props.item.displayId }}
              </td>
              <td class="text-xs-left">{{ props.item.clientName }}</td>
              <td class="text-xs-left" v-if="props.item.isUnassignedPudType">
                {{ props.item.reference }}
              </td>
              <td class="text-xs-left">
                <span v-if="props.item.from">
                  <span style="text-transform: uppercase">
                    {{ props.item.from }}
                  </span>
                </span>

                <span style="text-transform: uppercase" v-else>Unknown</span>
              </td>

              <td class="text-xs-left" v-if="!props.item.isUnassignedPudType">
                <v-layout column>
                  <span v-if="props.item.to && props.item.to !== '-'">
                    <span style="text-transform: uppercase">
                      {{ props.item.to }}
                      <span v-if="props.item.additionalLegCount"
                        >+{{props.item.additionalLegCount}}</span
                      >
                    </span>
                  </span>
                  <span style="text-transform: uppercase" v-else>Unknown</span>
                </v-layout>
              </td>
              <td class="text-xs-left" v-if="!props.item.isUnassignedPudType">
                {{ props.item.service }}
              </td>
              <td class="text-xs-left" v-if="!props.item.isUnassignedPudType">
                <span
                  :class="[!props.item.isUnassignedPudType && props.item.workStatus === WorkStatus.PREALLOCATED ? 'pre-allocated' : '', props.item.isOutsideHire ? 'add-italics' : '']"
                  >{{ props.item.vehicle ? props.item.vehicle : '-' }}</span
                >
              </td>
              <td class="text-xs-left">
                <span
                  :class="[!props.item.isUnassignedPudType && props.item.workStatus === WorkStatus.PREALLOCATED ? 'pre-allocated' : '', props.item.isOutsideHire ? 'add-italics' : '']"
                >
                  {{ props.item.driverName ? props.item.driverName.toUpperCase()
                  : '-' }}
                </span>
              </td>
              <td class="text-xs-left" v-if="showStatusColumn">
                <span> {{props.item.status ? props.item.status : '-'}}</span>
              </td>
              <td
                class="text-xs-left"
                v-if="windowMode && !props.item.isUnassignedPudType"
              >
                <span>
                  {{props.item.dispatchNote ? props.item.dispatchNote :
                  '-'}}</span
                >
              </td>
              <div
                class="dispatchnote-container"
                v-if="!props.item.isUnassignedPudType"
              >
                <v-tooltip
                  left
                  v-if="(!windowMode && (props.item.dispatchNote || props.item.requiredEquipment)) || (windowMode && props.item.requiredEquipment)"
                  class="tooltip-custom"
                >
                  <template v-slot:activator="{ on }">
                    <v-icon color="amber" v-on="on" size="13"
                      >fad fa-clipboard</v-icon
                    >
                  </template>
                  <v-layout wrap style="max-width: 300px">
                    <v-flex md12 v-if="props.item.dispatchNote && !windowMode">
                      <strong class="pr-2">DISPATCH NOTE:</strong
                      >{{props.item.dispatchNote}}
                    </v-flex>
                    <v-flex md12 v-if="props.item.requiredEquipment">
                      <strong class="pr-2">REQ. EQUIPMENT:</strong
                      >{{props.item.requiredEquipment}}
                    </v-flex>
                  </v-layout>
                </v-tooltip>
              </div>
            </tr>
          </template>
          <template v-slot:expand="props" v-if="!disableJobListActions">
            <JobListActionButtons
              v-if="!props.item.isUnassignedPudType"
              ref="jobListActionButtons"
              :jobDetails="jobDetails"
              :windowMode="windowMode"
              :currentIndex="currentIndex"
              :openWindowActionIncrementer="openWindowActionIncrementer"
              :currentJobListTab="selectedTabController"
              :hideTextInputs="operationsHasDialogOpen"
              :data-key="props.item.jobId"
              :key="props.item.status + '-' + props.item.workStatus"
              :isOutsideHire="props.item.isOutsideHire"
              :statusList="props.item.statusList"
              :workStatus="props.item.workStatus"
              :jobId="props.item.jobId"
              :driverId="props.item.driverId"
              :driverIsTripRate="props.item.driverIsTripRate"
              :clientRateTypeId="props.item.clientRateTypeId"
              :fleetAssetId="props.item.fleetAssetId"
              :clientId="props.item.clientId"
              :serviceTypeId="props.item.serviceTypeId"
              :jobDate="props.item.date"
              :selectedJobId="jobDetails ? jobDetails.jobId : null"
              :isCreditStatusValid="props.item.creditStatus !== 2"
              @allocatePreallocatedJob="allocatePreallocatedJob"
              @deallocateJob="deallocateJob"
              @addNoteToJob="addNoteToJob"
              @sendMessageToDriver="sendMessageToDriver"
              @editJobInBookingScreen="editJobInBookingScreen"
              @viewSelectedJobInReview="viewSelectedJobInReview"
              @viewJobDetailsDialog="viewJobDetailsDialog"
            >
            </JobListActionButtons>
            <UnassignedPudListActionButtons
              v-if="props.item.isUnassignedPudType"
              :unassignedPudId="props.item._id"
              :clientId="props.item.clientId"
              :assignedStatus="props.item.assignedStatus"
              :windowMode="windowMode"
            ></UnassignedPudListActionButtons>
          </template>
        </v-data-table>
      </v-card>
    </div>
  </div>
</div>
