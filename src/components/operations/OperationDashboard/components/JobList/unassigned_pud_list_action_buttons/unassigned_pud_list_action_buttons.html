<section class="unassigned-pud-list-action-buttons">
  <v-layout class="expanded-content__container" align-center v-if="!menuView">
    <v-layout>
      <v-icon color="info" size="18">fas fa-exclamation-triangle</v-icon>
      <span class="pl-2 white--text" style="font-size: 14px; font-weight: 500"
        >This point is currently UNASSIGNED. Please find the associated job and
        use the Leg Matching feature to assign it to an appropriate pickup or
        delivery.
      </span>
    </v-layout>

    <!-- <span
      class="expanded-content__icon-container"
      @click="addToNewJobInBookingScreen"
    >
      <v-icon class="expanded-content__icon">
        fas fa-plus-square
      </v-icon>
      <span class="expanded-content__icon-tooltip left-align">
        Create Job from Point
      </span>
    </span>
    <span
      class="expanded-content__icon-container"
      @click="jobSelectionDialogController = true"
    >
      <v-icon class="expanded-content__icon">
        far fa-plus
      </v-icon>
      <span class="expanded-content__icon-tooltip left-align">
        Add Point to Job
      </span>
    </span> -->
    <v-spacer></v-spacer>
    <!-- <span
      class="expanded-content__icon-container"
      @click="updateUnassignedPudStatus('CANCELLED')"
    >
      <v-icon class="expanded-content__icon">
        far fa-trash
      </v-icon>
      <span class="expanded-content__icon-tooltip right-align">
        Remove
      </span>
    </span> 
    <span
      class="expanded-content__icon-container"
      @click="editUnassignedPudItem(unassignedPudId)"
    >
      <v-icon class="expanded-content__icon">
        far fa-edit
      </v-icon>
      <span class="expanded-content__icon-tooltip right-align">
        Edit Details
      </span>
    </span>
     <span
      class="expanded-content__icon-container"
      @click="viewUnassignedPudItem(unassignedPudId)"
    >
      <v-icon class="expanded-content__icon"> far fa-eye </v-icon>
      <span class="expanded-content__icon-tooltip right-align">
        View Details
      </span>
    </span> -->
  </v-layout>
  <v-menu left v-if="menuView">
    <template v-slot:activator="{ on }">
      <v-icon size="20" class="pl-3 pr-1" v-on="on">far fa-ellipsis-v</v-icon>
    </template>
    <v-list class="v-list-custom" dense>
      <v-list-tile
        @click="addToNewJobInBookingScreen"
        v-if="assignedStatus === 'UNASSIGNED'"
      >
        <v-list-tile-avatar class="pa-0">
          <v-icon size="16"> fas fa-plus-square </v-icon>
        </v-list-tile-avatar>
        <v-list-tile-title class="pr-2">
          Create Job from Point
        </v-list-tile-title>
      </v-list-tile>
      <v-list-tile
        @click="jobSelectionDialogController = true"
        v-if="assignedStatus === 'UNASSIGNED'"
      >
        <v-list-tile-avatar class="pa-0">
          <v-icon size="16"> far fa-plus </v-icon>
        </v-list-tile-avatar>
        <v-list-tile-title class="pr-2"> Add Point to Job </v-list-tile-title>
      </v-list-tile>
      <v-list-tile
        @click="updateUnassignedPudStatus('CANCELLED')"
        v-if="assignedStatus === 'UNASSIGNED'"
      >
        <v-list-tile-avatar class="pa-0">
          <v-icon size="16"> far fa-trash </v-icon>
        </v-list-tile-avatar>
        <v-list-tile-title class="pr-2"> Cancel Point </v-list-tile-title>
      </v-list-tile>
      <v-list-tile
        @click="updateUnassignedPudStatus('UNASSIGNED')"
        v-if="assignedStatus === 'CANCELLED'"
      >
        <v-list-tile-avatar class="pa-0">
          <v-icon size="16"> far fa-trash-restore </v-icon>
        </v-list-tile-avatar>
        <v-list-tile-title class="pr-2"> Un-Cancel Point </v-list-tile-title>
      </v-list-tile>
      <v-list-tile @click="viewAllForGroup" v-if="groupReferenceId">
        <v-list-tile-avatar class="pa-0">
          <v-icon size="16"> far fa-eye </v-icon>
        </v-list-tile-avatar>
        <v-list-tile-title class="pr-2">
          View All in Point Group
        </v-list-tile-title>
      </v-list-tile>
      <v-list-tile
        @click="removePointFromGroup"
        v-if="groupReferenceId && assignedStatus === 'UNASSIGNED'"
      >
        <v-list-tile-avatar class="pa-0">
          <v-icon size="16"> far fa-object-ungroup </v-icon>
        </v-list-tile-avatar>
        <v-list-tile-title class="pr-2">
          Remove from Point Group
        </v-list-tile-title>
      </v-list-tile>
      <!-- <v-list-tile
        @click="editUnassignedPudItem"
        v-if="assignedStatus === 'UNASSIGNED'"
      >
        <v-list-tile-avatar class="pa-0">
          <v-icon size="16">
            far fa-edit
          </v-icon>
        </v-list-tile-avatar>
        <v-list-tile-title class="pr-2">
          Edit Details
        </v-list-tile-title>
      </v-list-tile>
      <v-list-tile @click="viewUnassignedPudItem">
        <v-list-tile-avatar class="pa-0">
          <v-icon size="16"> far fa-eye </v-icon>
        </v-list-tile-avatar>
        <v-list-tile-title class="pr-2"> View Details </v-list-tile-title>
      </v-list-tile> -->
      <v-list-tile
        @click="downloadSourceCsv"
        v-if="unassignedPudItem.submissionType === 'CLIENT_FTP'"
      >
        <v-list-tile-avatar class="pa-0">
          <v-icon size="16"> fas fa-download </v-icon>
        </v-list-tile-avatar>
        <v-list-tile-title class="pr-2"> Download Source </v-list-tile-title>
      </v-list-tile>
    </v-list>
  </v-menu>
  <v-dialog
    v-model="jobSelectionDialogController"
    width="30%"
    v-if="jobSelectionDialogController"
    content-class="v-dialog-custom"
  >
    <v-layout
      justify-space-between
      class="task-bar app-theme__center-content--header no-highlight"
    >
      <span>Select a Job</span>

      <div
        class="app-theme__center-content--closebutton"
        @click="jobSelectionDialogController = false"
      >
        <v-icon class="app-theme__center-content--closebutton--icon"
          >fal fa-times</v-icon
        >
      </div>
    </v-layout>

    <v-layout class="app-theme__center-content--body">
      <v-flex md12>
        <v-layout pt-3 px-3>
          <v-autocomplete
            v-if="jobListForClient"
            solo
            flat
            v-model="selectedJobId"
            :items="jobListForClient"
            item-value="jobId"
            item-text="displayId"
            color="orange"
            label="Search for a Job"
          >
            <template slot="selection" slot-scope="data">
              <span>{{ data.item.displayId }}</span>
              <span class="pl-1"> ({{ data.item.dateString }}</span>
              <span
                v-if="data.item.driverName && data.item.csrAssignedId"
                class="pl-1"
              >
                {{ `- ${data.item.csrAssignedId} ${data.item.driverName}`
                }}</span
              >)
            </template>
            <template slot="item" slot-scope="data">
              <span>{{ data.item.displayId }}</span>
              <span class="pl-1"> ({{ data.item.dateString }}</span>
              <span
                v-if="data.item.driverName && data.item.csrAssignedId"
                class="pl-1"
              >
                {{ `- ${data.item.csrAssignedId} ${data.item.driverName}`
                }}</span
              >)
            </template>
          </v-autocomplete>
        </v-layout>
        <v-layout>
          <v-divider></v-divider>
        </v-layout>
        <v-layout justify-space-between px-3 py-2>
          <v-btn
            depressed
            color="white"
            flat
            @click="jobSelectionDialogController = false"
          >
            Cancel</v-btn
          >
          <v-btn
            :disabled="selectedJobId === -1"
            depressed
            color="blue"
            @click="addToExistingJobInBookingScreen"
            >Add to Selected Job</v-btn
          >
        </v-layout>
      </v-flex>
    </v-layout>
  </v-dialog>
</section>
