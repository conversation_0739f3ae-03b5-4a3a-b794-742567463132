import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { JobOperationType } from '@/interface-models/Jobs/JobOperationType';
import { OperationJobSummary } from '@/interface-models/Jobs/OperationJobSummary';
import {
  BookJobWithUnassignedPudConfig,
  PudMaintenanceDialogConfig,
} from '@/interface-models/Jobs/PUD/UnassignedPudItem/PudMaintenanceDialogConfig';
import { PudMaintenanceType } from '@/interface-models/Jobs/PUD/UnassignedPudItem/PudMaintenanceType';
import UnassignedPudItem from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudItem';
import { UnassignedPudItemStatus } from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudItemStatus';
import { UnassignedPudAssociatedClientJobs } from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudJobDetailsRequest';
import UnassignedPudStatusUpdateRequest from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudStatusUpdate';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useDataImportStore } from '@/store/modules/DataImportStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component({
  components: {
    ConfirmationDialog,
  },
})
export default class UnassignedPudListActionButtons extends Vue {
  @Prop({ default: '' }) public clientId: string;
  @Prop({ default: '' }) public unassignedPudId: string;
  @Prop() public unassignedPudItem: UnassignedPudItem;
  @Prop({ default: false }) public windowMode: boolean;
  @Prop({ default: UnassignedPudItemStatus.UNASSIGNED })
  public assignedStatus: string;
  @Prop({ default: '' }) public groupReferenceId: string;
  @Prop({ default: false }) public menuView: boolean;

  public dataImportStore = useDataImportStore();
  public operationsStore = useOperationsStore();

  public selectedJobId: number = -1;
  public showUpdateStatusDialog: boolean = false;
  public viewingJobSelectionDialog: boolean = false;

  public jobListForClient: UnassignedPudAssociatedClientJobs[] | null = null;

  // Controls visibility of the job selection dialog
  // When set as true, set the job list to that of the client
  // When set as false, reset job list and selected job variable
  get jobSelectionDialogController() {
    return this.viewingJobSelectionDialog;
  }
  set jobSelectionDialogController(value: boolean) {
    if (value) {
      this.jobListForClient = this.setJobSummaryListForClient();
    } else {
      this.jobListForClient = null;
      this.selectedJobId = -1;
    }
    this.viewingJobSelectionDialog = value;
  }
  // Set the local job list to filter for in-progress/unallocated jobs for the selected client
  public setJobSummaryListForClient(): UnassignedPudAssociatedClientJobs[] {
    const allJobs: UnassignedPudAssociatedClientJobs[] = useJobStore()
      .operationJobsList.filter(
        (job: OperationJobSummary) =>
          job.jobId &&
          job.clientId === this.clientId &&
          job.workStatus >= WorkStatus.BOOKED &&
          job.workStatus <= WorkStatus.IN_PROGRESS,
      )
      .map((job) => {
        const firstPudEpoch = job.date ? job.date : 0;
        return {
          jobId: job.jobId ? job.jobId : 0,
          displayId: job.displayId,
          dateString: firstPudEpoch ? returnFormattedDate(firstPudEpoch) : '',
          driverName:
            job.driverName && job.driverName !== '-' ? job.driverName : '',
          csrAssignedId:
            job.csrAssignedId && job.csrAssignedId !== '-'
              ? job.csrAssignedId
              : '',
        };
      });

    return allJobs;
  }

  public viewAllForGroup() {
    this.$emit('viewAllForGroup', this.groupReferenceId);
  }
  public removePointFromGroup() {
    this.$emit('removePointFromGroup', this.unassignedPudId);
  }

  public updateUnassignedPudStatus(status: UnassignedPudItemStatus) {
    const id = this.unassignedPudId;
    const request: UnassignedPudStatusUpdateRequest = {
      unassignedPudIds: [id],
      updatedStatus: status,
    };
    this.dataImportStore.updateUnassignedPudItemStatus(request);
  }
  // // View selected UPI in dialog in a read-only view
  // public viewUnassignedPudItem() {
  //   const id = this.unassignedPudId;
  //   const config: PudMaintenanceDialogConfig = {
  //     operationType: JobOperationType.VIEW,
  //     pudType: PudMaintenanceType.UNASSIGNED,
  //     unassignedPudId: id,
  //     unassignedPudItem:
  //       this.menuView && this.unassignedPudItem !== undefined
  //         ? this.unassignedPudItem
  //         : undefined,
  //   };
  //   if (!this.windowMode) {
  //     this.operationsStore.setPudMaintenanceDialogConfig(config);
  //     this.operationsStore.setViewingPudMaintenanceDialog(true);
  //   } else {
  //     this.$emit('viewPudMaintenanceDialog', config);
  //   }
  // }
  // Edit selected UPI in dialog
  // public editUnassignedPudItem() {
  //   const id = this.unassignedPudId;
  //   const config: PudMaintenanceDialogConfig = {
  //     operationType: JobOperationType.EDIT,
  //     pudType: PudMaintenanceType.UNASSIGNED,
  //     unassignedPudId: id,
  //     unassignedPudItem:
  //       this.menuView && this.unassignedPudItem !== undefined
  //         ? this.unassignedPudItem
  //         : undefined,
  //   };
  //   this.operationsStore.setPudMaintenanceDialogConfig(config);
  //   this.operationsStore.setViewingPudMaintenanceDialog(true);
  // }

  // Close dialog, jump to booking screen and stage the selected UPI for it to be added to job
  public addToNewJobInBookingScreen() {
    const id: string = this.unassignedPudId;
    const config: BookJobWithUnassignedPudConfig = {
      clientId: this.clientId,
      operationType: JobOperationType.NEW,
      unassignedPudIds: [id],
    };
    this.operationsStore.setViewingPudSearchDialog(false);
    this.dataImportStore.setCreatingJobFromUnassignedPudItems(true);
    this.operationsStore.setBookJobWithUnassignedPudConfig(config);
  }
  // After selection of a job, open that job in the booking screen and stage the selected UPI to be added as a PUD
  public addToExistingJobInBookingScreen() {
    if (this.selectedJobId === -1) {
      console.error('Please select a valid job');
      return;
    }
    const jobId: number = this.selectedJobId;
    const id: string = this.unassignedPudId;

    const config: BookJobWithUnassignedPudConfig = {
      clientId: this.clientId,
      operationType: JobOperationType.EDIT,
      jobId,
      unassignedPudIds: [id],
    };
    this.jobSelectionDialogController = false;
    this.operationsStore.setViewingPudSearchDialog(false);
    this.dataImportStore.setCreatingJobFromUnassignedPudItems(true);
    this.operationsStore.setBookJobWithUnassignedPudConfig(config);
  }

  /**
   * Sends a request to fetch the full document associated with the batchJobsId.
   * Only available on FTP import jobs.
   */
  public downloadSourceCsv() {
    if (!this.unassignedPudItem.batchJobsId) {
      showNotification(
        'Something went wrong. The document(s) could not be downloaded.',
      );
      return;
    }
    useDataImportStore().downloadImportSourceDocumentById(
      this.unassignedPudItem.batchJobsId,
    );
  }
}
