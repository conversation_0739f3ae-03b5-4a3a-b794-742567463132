.unassigned-pud-list-action-buttons {
  .expanded-content__container {
    padding: 8px 8px 30px 8px;
    background-color: #232429;
    width: 100%;
    display: flex;
    height: 80px;

    &.vertical-list {
      padding: 0px;
      background-color: transparent;
      position: relative;
      .expanded-content__icon-container {
        padding: 4px 8px;
        margin: 0px;
        border: none;
        border-radius: 0px;
        background-color: transparent;
        width: 100%;
        text-align: center;
        display: block;
        &:hover {
          background-color: #303238;
          .expanded-content__icon-tooltip {
            right: 0% !important;
            visibility: visible;
            z-index: 10000000000000000000;
            &.left-align {
              left: 115%;
              top: 0px;
            }
            &.right-align {
              left: 115%;
              top: 0px;
            }
          }
        }
      }
    }

    .expanded-content__icon-container {
      padding: 4px 8px;
      margin: 0px 2px;
      background-color: #32333a;
      border: 1px solid #6b6d80;
      border-radius: 2px;
      position: relative;
      display: block;

      .expanded-content__text {
        font-size: $font-size-11;
        font-weight: 600;
        text-transform: uppercase;
        color: rgb(198, 199, 230);
        // padding-top: 3px;
        padding-right: 5px;
      }

      &.disabled {
        pointer-events: none;
        opacity: 0.4;
      }

      &:hover {
        cursor: pointer;
        filter: brightness(120%);

        .expanded-content__icon-tooltip {
          top: 115%;
          visibility: visible;
          z-index: 10000000000000000000;
          &.left-align {
            left: 0px;
          }
          &.right-align {
            right: 0px;
          }
        }
      }
      .expanded-content__icon {
        font-size: $font-size-11;
        color: rgb(198, 199, 230);
      }
      .expanded-content__icon-tooltip {
        display: block;
        position: absolute;
        visibility: hidden;
        padding: 4px 6px;
        background-color: #25262a;
        border: 1px solid #4d4d4e;
        color: white;
        border-radius: 3px;
        font-size: 1em;
        font-weight: 500;
        width: 80px;

        text-align: center;
        z-index: 10000000000000000000;

        &.left-align {
          left: 0px;
        }
        &.right-align {
          right: 0px;
        }
      }
    }
  }
}
