<template>
  <v-dialog
    v-model="dialogOpen"
    content-class="v-dialog-custom"
    persistent
    width="700px"
  >
    <v-layout md12>
      <v-flex
        justify-space-between
        align-center
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>Dashboard Filters</span>
        <div
          class="app-theme__center-content--closebutton"
          @click="dialogOpen = false"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-flex>
    </v-layout>
    <v-layout
      row
      wrap
      class="app-theme__center-content--body"
      style="position: relative"
    >
      <v-flex
        md12
        justify-end
        align-center
        class="body-scrollable--75 body-min-height--75 pa-2"
      >
        <div v-if="!props.isMap">
          <div class="title-txt">Job List Filters</div>
          <div class="filter-container">
            <v-layout md12>
              <v-autocomplete
                v-model="localSelectedServiceType"
                :items="serviceTypeOptions"
                label="Select Service Type"
                item-text="optionSelectName"
                item-value="serviceTypeId"
                multiple
                clearable
                chips
                deletable-chips
                small-chips
                class="v-solo-custom"
                outline
              />
            </v-layout>
            <v-btn
              text
              solo
              flat
              small
              color="accent"
              class="select-all-btn"
              v-if="!props.isMap && localSelectedServiceType.length < 1"
              @click="selectAllServiceTypes"
              >Select All</v-btn
            >
          </div>
          <v-divider></v-divider>
        </div>
        <div class="title-txt">Fleet List Filters</div>
        <div class="sub-heading-txt">
          The below filters will filter the Fleet List and Map to only display
          Vehicle+Driver combinations that match the selections below. For
          example, when a client filter is selected, the Fleet List and Map will
          only show Vehicle+Driver combinations with currently in-progress jobs
          for that client.
        </div>
        <div class="filter-container">
          <v-layout md12>
            <v-autocomplete
              v-model="localSelectedClientIds"
              :items="clientOptions"
              label="Select Client"
              item-text="name"
              item-value="id"
              multiple
              clearable
              chips
              deletable-chips
              small-chips
              class="v-solo-custom"
              outline
            />
          </v-layout>
          <v-layout md12>
            <v-autocomplete
              v-model="localSelectedVehicleClass"
              :items="vehicleClassOptions"
              item-text="displayText"
              item-value="truckClass"
              label="Select Vehicle Class"
              multiple
              clearable
              chips
              deletable-chips
              small-chips
              class="v-solo-custom"
              outline
            />
          </v-layout>
          <v-layout
            class="select-all-btn"
            v-if="localSelectedVehicleClass.length < 1"
          >
            <v-btn
              text
              solo
              flat
              small
              color="accent"
              class="ma-1"
              @click="selectAllVehicleClasses"
            >
              Select All</v-btn
            ></v-layout
          >
          <v-flex md12>
            <v-form ref="formRef">
              <v-layout class="filter-options">
                <v-flex md12>
                  <v-layout
                    justify-start
                    v-for="settingGroup in groupedSettingsList"
                    :key="settingGroup.id"
                  >
                    <v-flex md12 v-if="settingGroup.isRadio">
                      <v-layout>
                        <v-radio-group v-model="settingGroup.selectedRadioId">
                          <v-radio
                            v-for="settingItem in settingGroup.settings"
                            :key="settingItem.id"
                            :label="settingItem.longName"
                            :value="settingItem.id"
                            color="blue lighten-1"
                          ></v-radio>
                        </v-radio-group>
                      </v-layout>
                      <v-layout>
                        <v-divider
                          v-if="settingGroup.isRadio"
                          class="py-1"
                        ></v-divider>
                      </v-layout>
                    </v-flex>
                    <v-flex
                      md12
                      v-if="!settingGroup.isRadio && settingGroup.settings[0]"
                    >
                      <div
                        class="pt-2"
                        v-if="
                          settingGroup.settings[0].id === 'showOnlineDrivers'
                        "
                      >
                        <v-divider></v-divider>
                      </div>
                      <v-checkbox
                        v-model="settingGroup.settings[0].active"
                        hide-details
                        :label="settingGroup.settings[0].longName"
                      ></v-checkbox>
                    </v-flex>
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-form>
          </v-flex>
        </div>
      </v-flex>

      <v-flex>
        <v-divider></v-divider>
        <v-layout class="action-row">
          <v-btn class="ma-1" outline color="red" @click="closeDialog"
            >Cancel</v-btn
          >
          <v-spacer></v-spacer>
          <v-btn text solo flat color="white" class="ma-1" @click="clearFilters"
            >Clear</v-btn
          >
          <v-btn
            text
            solo
            flat
            color="orange"
            class="ma-1"
            @click="restoreDefaults"
            >Restore Default</v-btn
          >
          <v-layout align-center>
            <v-spacer></v-spacer>
            <v-btn
              depressed
              color="blue"
              @click="applyFilters"
              class="v-btn-confirm-custom"
              elevation="10"
              >Apply Filters</v-btn
            >
          </v-layout>
        </v-layout>
      </v-flex>
    </v-layout>
  </v-dialog>
</template>

<script setup lang="ts">
import OperationsDashboardSetting, {
  fleetAssetListDefaultSettings,
} from '@/interface-models/Generic/OperationScreenOptions/OperationsDashboardSetting';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useFilterStore } from '@/store/modules/OperationsFilterStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { computed, onMounted, Ref, ref, watch } from 'vue';

const props = withDefaults(
  defineProps<{
    dialogActive: boolean;
    isMap: boolean;
  }>(),
  {
    dialogActive: false,
    isMap: false,
  },
);

interface SettingGroup {
  id: number;
  isRadio: boolean;
  selectedRadioId: string;
  settings: OperationsDashboardSetting[];
}

const companyStore = useCompanyDetailsStore();
const fleetStore = useFleetAssetStore();
const filterStore = useFilterStore();
const operationsStore = useOperationsStore();
const jobStore = useJobStore();
const dialogOpen = ref(props.dialogActive);

const emit = defineEmits(['input']);
const groupedSettingsList: Ref<SettingGroup[]> = ref([]);

// default settings for fleet list table
const fleetAssetListDefaultSetting: OperationsDashboardSetting[] =
  fleetAssetListDefaultSettings;

// Local state for filters
const localSelectedServiceType = ref<number[]>([
  // ...filterStore.selectedServiceType,
]);
const localSelectedVehicleClass = ref<string[]>([
  // ...filterStore.selectedVehicleClass,
]);
const localSelectedClientIds = ref<string[]>([
  // ...filterStore.selectedClientIds,
]);
const localSelectedFleetIds = ref<string[]>([
  // ...filterStore.selectedFleetIds
]);

/**
 * Computes a unique list of client options based on the operation jobs list.
 * Extracts distinct clients from jobs, ensuring each client appears only once.
 * @returns An array of unique client objects containing 'id' and 'name'.
 */
const clientOptions = computed(() => {
  const clients = new Map<string, { id: string; name: string }>();
  jobStore.operationJobsList.forEach((job) => {
    if (job.clientId && !clients.has(job.clientId)) {
      clients.set(job.clientId, {
        id: job.clientId,
        name: job.clientName || `Client ${job.clientId}`,
      });
    }
  });
  return Array.from(clients.values());
});

/**
 * Computes a unique list of serviceType options based on the company serviceTypesMap.
 * @returns An array of serviceType objects containing 'optionSelectName' and 'serviceTypeId'.
 */
// Computes a list of service type options from the companyStore's serviceTypesMap
const serviceTypeOptions = computed(() => {
  const serviceTypes = Array.from(companyStore.serviceTypesMap.values()).map(
    (serviceType) => ({
      optionSelectName: serviceType.optionSelectName,
      serviceTypeId: serviceType.serviceTypeId,
      shortServiceTypeName: serviceType.shortServiceTypeName,
    }),
  );
  return serviceTypes;
});

// Computes a list of vehicle class options from the fleetStore's fleetAssetSummaryMap
const vehicleClassOptions = computed(() => {
  // Group assets by truckClass and count
  const assetMap = new Map<string, number>();

  const vehicleClasses = Array.from(
    fleetStore.fleetAssetSummaryMap.values(),
  ).filter((asset) => asset.fleetAssetTypeId === 1 && asset.truckClass);

  for (const asset of vehicleClasses) {
    const key = asset.truckClass?.toLowerCase();
    assetMap.set(key as string, (assetMap.get(key as string) || 0) + 1);
  }

  // Build option list with combined longName and count
  const uniqueTruckClasses = Array.from(assetMap.keys()).map((key) => {
    const truckClass = vehicleClasses.find(
      (asset) => asset.truckClass?.toLowerCase() === key,
    )?.truckClass;

    const matchedServiceType = serviceTypeOptions.value.find(
      (serviceType) => serviceType.shortServiceTypeName?.toLowerCase() === key,
    );

    const longName = matchedServiceType?.optionSelectName || '';

    return {
      truckClass,
      longName,
      count: assetMap.get(key) || 0,
      // Create a combined text for item display
      displayText: `${longName} (${assetMap.get(key) || 0})`, // Combined text with longName and count
      sortIndex: matchedServiceType
        ? serviceTypeOptions.value.indexOf(matchedServiceType)
        : serviceTypeOptions.value.length,
    };
  });

  return uniqueTruckClasses.sort((a, b) => a.sortIndex - b.sortIndex);
});

// function select all vehicle classes
function selectAllVehicleClasses() {
  localSelectedVehicleClass.value = Array.from(
    new Set(vehicleClassOptions.value.map((option) => option.truckClass)),
  ) as string[];
}

// function select all Service types
function selectAllServiceTypes() {
  localSelectedServiceType.value = Array.from(
    new Set(serviceTypeOptions.value.map((option) => option.serviceTypeId)),
  );
}

// computed list of fleetAssetListSettings from operationsStore dashboard Settings
const fleetAssetListSettings = computed<OperationsDashboardSetting[]>(() => {
  return JSON.parse(
    JSON.stringify(operationsStore.dashboardSettings.fleetAssetList),
  );
});

/**
 * Updates the fleet settings list by processing grouped settings.
 * Ensures only the selected radio option remains active and removes `excludeIds`
 * from inactive settings. Finally, triggers an update with the modified settings.
 *
 * - For each radio group, sets only the selected option as active.
 * - Flattens grouped settings and removes `excludeIds` from inactive settings.
 * - Calls `tableSettingsUpdated` to apply the changes.
 */
function updateFleetList(): void {
  groupedSettingsList.value.forEach((gsl) => {
    if (gsl.isRadio) {
      const selectedId = gsl.selectedRadioId;
      gsl.settings.forEach((setting) => {
        setting.active = setting.id === selectedId;
      });
    }
  });
  const settingsList = groupedSettingsList.value.flatMap((gsl) => gsl.settings);
  settingsList.forEach((s) => {
    if (!s.active) {
      delete s.excludeIds;
    }
  });
  tableSettingsUpdated(settingsList);
}

/**
 * Updates the dashboard settings for fleet asset list and ensures valid selections.
 *
 * - Checks "Online Driver" and "Offline Driver"
 * - Checks "Has Work" and "No Work" driver
 * - Updates the store with the modified settings list.
 *
 * @param settingsList - The updated list of dashboard settings.
 */
function tableSettingsUpdated(settingsList: OperationsDashboardSetting[]) {
  // Check if both Online and offline Driver are inactive
  const onlineDriver = settingsList.some((s) => s.groupId === 3 && !s.active);
  const offlineDriver = settingsList.some((s) => s.groupId === 4 && !s.active);
  // Check if both hasWork and NoWork Driver are inactive
  const hasWork = settingsList.some((s) => s.groupId === 5 && !s.active);
  const noWork = settingsList.some((s) => s.groupId === 6 && !s.active);
  // if show online and offline driver checkbox both are false, make both true.
  if (onlineDriver && offlineDriver) {
    settingsList.forEach((s) => {
      if (s.groupId === 3 || s.groupId === 4) {
        s.active = true;
      }
    });
  }
  // if show hasNoWork and AllocatedWork driver checkboxes are false, make both true.
  if (hasWork && noWork) {
    settingsList.forEach((s) => {
      if (s.groupId === 5 || s.groupId === 6) {
        s.active = true;
      }
    });
  }
  // update local settings in store
  operationsStore.updateDashboardSettingsFleetAssetList(settingsList);
}

/**
 * Initializes and groups the fleet asset list settings based on their group ID.
 *
 * - Creates a deep copy of `fleetAssetListSettings`
 * - Updates `groupedSettingsList` with the newly formed setting groups.
 *
 * Ensures that the settings are properly structured for UI components
 * that rely on grouped configurations.
 */
function setGroupedSettingsList(): void {
  if (fleetAssetListSettings.value) {
    const settingsList = [
      ...fleetAssetListSettings.value.map((s) => {
        return { ...s };
      }),
    ];
    const uniqueGroups = [...new Set(settingsList.map((s) => s.groupId))];
    const settingGroups: SettingGroup[] = [];
    for (let i = 0; i < uniqueGroups.length; i++) {
      const groupId = uniqueGroups[i];
      const group = settingsList.filter(
        (setting) => setting.groupId === groupId,
      );
      if (group && group.length > 0) {
        const isRadio = group.length > 1;
        let selectedRadioId = '';
        if (isRadio) {
          const foundActive = group.find((j) => j.active);
          if (foundActive) {
            selectedRadioId = foundActive.id;
          }
        }
        const settingGroup: SettingGroup = {
          id: groupId,
          isRadio,
          selectedRadioId,
          settings: group,
        };
        settingGroups.push(settingGroup);
      }
    }
    groupedSettingsList.value = settingGroups;
  }
}

// func to restore default settings for dashboardFilters
// resets localSelectedServiceType localSelectedVehicleClass and localSelectedClientIds
function restoreDefaults() {
  const defaultSettingsList = fleetAssetListDefaultSetting;
  groupedSettingsList.value.forEach((gsl) => {
    gsl.settings.forEach((setting) => {
      const defaultSetting = defaultSettingsList?.find(
        (ds) => ds.id === setting.id,
      );
      if (defaultSetting) {
        setting.active = defaultSetting.active;
        setting.excludeIds = defaultSetting.excludeIds;
      }
    });
  });
  // Emit the default settings list
  localSelectedServiceType.value = [];
  localSelectedVehicleClass.value = [];
  localSelectedClientIds.value = [];
  tableSettingsUpdated(defaultSettingsList);
}

// Applies the selected filters and closes the filter dialog
function applyFilters() {
  updateFleetList();
  // filterStore.setSelectedServiceType(localSelectedServiceType.value);
  // filterStore.setSelectedVehicleClass(localSelectedVehicleClass.value);
  // filterStore.setSelectedClientIds(localSelectedClientIds.value);
  dialogOpen.value = false;
}

// Clears selected filters
function clearFilters() {
  localSelectedServiceType.value = [];
  localSelectedVehicleClass.value = [];
  localSelectedClientIds.value = [];
}

// Clears selected filters and closes the filter dialog
function closeDialog() {
  if (
    localSelectedServiceType.value ||
    localSelectedVehicleClass.value ||
    localSelectedClientIds.value
  ) {
    dialogOpen.value = false;
  } else {
    // filterStore.setSelectedServiceType(localSelectedServiceType.value);
    // filterStore.setSelectedVehicleClass(localSelectedVehicleClass.value);
    // filterStore.setSelectedClientIds(localSelectedClientIds.value);
    // filterStore.setSelectedFleetIds(localSelectedFleetIds.value);
    dialogOpen.value = false;
  }
}

watch(dialogOpen, (val) => {
  emit('input', val);
});

// Watches for changes to the props.dialogActive to control dialog visibility, sync dialogOpen with props.dialogActive
watch(
  () => props.dialogActive,
  (val) => {
    // localSelectedServiceType.value = filterStore.selectedServiceType;
    // localSelectedVehicleClass.value = filterStore.selectedVehicleClass;
    // localSelectedClientIds.value = filterStore.selectedClientIds;
    dialogOpen.value = val;
  },
);

// watch for dialog, apply settings when dialog opens
watch(dialogOpen, (newValue) => {
  if (newValue) {
    // When the dialog is opened, set grouped settings
    setGroupedSettingsList();
  } else {
    // When the dialog is closed, clear grouped settings
    groupedSettingsList.value = [];
  }
});

// checks that `fleetAssetListSettings` is defined.
onMounted(() => {
  if (!fleetAssetListSettings.value) {
    return;
  }
});
</script>

<style scoped lang="scss">
.filter-container {
  margin: 0 6px;
  padding: 0 6px;
}
.filter-options {
  position: relative;
  bottom: 22px;
}
.select-all-btn {
  position: relative;
  padding: 0px;
  bottom: 26px;
  margin: 0px;
}

.title-txt {
  font-size: $font-size-18;
  font-weight: 500;
  color: var(--primary);
  padding-top: 4px;
  margin-bottom: 6px;
  padding-left: 12px;
}

.sub-heading-txt {
  font-size: $font-size-14;
  color: var(--light-text-color);
  padding-left: 12px;
  margin-bottom: 14px;
}

.action-row {
  padding: 4px;
}
</style>
