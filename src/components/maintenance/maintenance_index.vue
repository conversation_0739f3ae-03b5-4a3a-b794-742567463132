<template>
  <v-layout id="dev-maintenance">
    <div class="left-panel dev-maintenance-navigation">
      <v-list class="pt-0">
        <v-list-tile
          v-for="item in navigationData"
          :key="item.title"
          v-model="item.active"
          :prepend-icon="item.action"
          no-action
          @click="setNavigation(item.id)"
        >
          <v-list-tile-content>
            <v-list-tile-title>{{ item.title }}</v-list-tile-title>
          </v-list-tile-content>
        </v-list-tile>
      </v-list>
    </div>

    <v-flex md9 offset-md3>
      <StatusListMaintenance v-if="selectedMenuOption === 'STL'" />
      <UserRolesMaintenance v-if="selectedMenuOption === 'RSR'" />
      <ApiPlayground v-if="selectedMenuOption === 'API'" />
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import ApiPlayground from '@/components/maintenance/api_playground.vue';
import StatusListMaintenance from '@/components/maintenance/status_list_maintenance.vue';
import UserRolesMaintenance from '@/components/maintenance/user_roles_maintenance.vue';
import Environment from '@/configuration/environment';
import { onMounted, Ref, ref } from 'vue';

/**
 * The currently selected menu option.
 */
const selectedMenuOption: Ref<string> = ref('MAP');

/**
 * The navigation data for the left panel.
 */
const navigationData: Ref<
  {
    id: string;
    action: string;
    title: string;
    active: boolean;
  }[]
> = ref([]);

/**
 * Sets the selected navigation item and updates the active state.
 * @param id - The id of the navigation item to activate.
 */
function setNavigation(id: string): void {
  selectedMenuOption.value = id;
  for (const item of navigationData.value) {
    item.active = item.id === id;
  }
  window.scrollTo(0, 0);
}

/**
 * Initializes the navigation data and adds the API Playground tab
 * if the environment is local or staging.
 */
onMounted(() => {
  navigationData.value = [
    {
      id: 'STL',
      action: 'far fa-file-alt',
      title: 'Status Types',
      active: false,
    },
    {
      id: 'RSR',
      action: 'far fa-file-alt',
      title: 'User Roles',
      active: false,
    },
  ];
  const ENV: string | undefined = Environment.value('environment');
  if (ENV === 'local' || ENV === 'staging') {
    navigationData.value.push({
      id: 'API',
      action: 'far fa-file-alt',
      title: 'API Playground',
      active: false,
    });
  }
});
</script>
<style scoped lang="scss">
.left-panel {
  height: calc(100vh - 40px);
  background-color: $app-dark-primary-400;
  width: calc(25% - 14px);
  position: fixed;
  bottom: 0px;
}
</style>
