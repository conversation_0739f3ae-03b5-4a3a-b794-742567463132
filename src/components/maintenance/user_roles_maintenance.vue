<template>
  <div class="user-role-container pa-4">
    <v-layout class="mb-4">
      <p class="title ma-0">User Roles</p>
    </v-layout>
    <v-text-field
      class="mb-4"
      v-model="search"
      append-icon="search"
      label="Search"
      single-line
      hide-details
      box
    >
    </v-text-field>
    <v-data-table
      :headers="headers"
      :search="search"
      :items="tableData"
      hide-actions
      class="gd-dark-theme"
    >
      <template v-slot:items="props">
        <td class="pl-4">{{ props.item.name }}</td>
        <td>{{ props.item.roleId }}</td>
        <td>{{ props.item.collection }}</td>
      </template>
    </v-data-table>
  </div>
</template>

<script setup lang="ts">
import { Ref, ComputedRef, ref, computed } from 'vue';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import UserRole from '@/interface-models/Roles/UserRoles';
import { useRootStore } from '@/store/modules/RootStore';

/**
 * The search string for filtering the user roles table.
 */
const search: Ref<string> = ref('');

/**
 * The headers for the user roles table.
 */
const headers: TableHeader[] = [
  {
    text: 'Role Name',
    align: 'left',
    sortable: true,
    value: 'sid',
  },
  {
    text: 'Role ID',
    align: 'left',
    sortable: false,
    value: 'roleId',
  },
  {
    text: 'Collection',
    align: 'left',
    sortable: false,
    value: 'collection',
  },
];

const tableData: ComputedRef<UserRole[]> = computed(() => {
  return useRootStore().roleList;
});
</script>
<style scoped lang="scss"></style>
