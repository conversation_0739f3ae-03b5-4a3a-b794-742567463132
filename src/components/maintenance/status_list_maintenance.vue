<template>
  <div class="status-list-container pa-4">
    <v-layout class="mb-4">
      <p class="title ma-0">Status Types</p>
    </v-layout>
    <v-text-field
      class="mb-4"
      v-model="search"
      append-icon="search"
      label="Search"
      single-line
      hide-details
      box
    >
    </v-text-field>
    <v-data-table
      :headers="tableHeaders"
      :search="search"
      :items="table"
      hide-actions
      class="gd-dark-theme"
    >
      <template v-slot:items="props">
        <td class="pl-4">{{ props.item.sid }}</td>
        <td>{{ props.item.enumValue }}</td>
        <td>{{ props.item.category }}</td>
        <td>{{ props.item.master }}</td>
      </template>
    </v-data-table>
  </div>
</template>

<script setup lang="ts">
import { Ref, ComputedRef, ref, computed } from 'vue';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { useRootStore } from '@/store/modules/RootStore';
import StatusConfig from '@/interface-models/Status/StatusConfig';

/**
 * The search string for filtering the status types table.
 */
const search: Ref<string> = ref('');

/**
 * The headers for the status types table.
 */
const tableHeaders: TableHeader[] = [
  {
    text: 'ID',
    align: 'left',
    sortable: true,
    value: 'sid',
  },
  {
    text: 'ENUM',
    align: 'left',
    sortable: false,
    value: 'enumValue',
  },
  {
    text: 'category',
    align: 'left',
    sortable: false,
    value: 'category',
  },
  {
    text: 'master',
    align: 'left',
    sortable: false,
    value: 'master',
  },
];

/**
 * Computed list of status types from the root store.
 */
const table: ComputedRef<StatusConfig[]> = computed(
  () => useRootStore().statusTypeList,
);
</script>
<style scoped lang="scss"></style>
