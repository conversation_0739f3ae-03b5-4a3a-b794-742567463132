<div class="division-rates-summary">
  <v-layout row wrap v-if="!isAppStatusBar">
    <v-flex md12 class="item-container">
      <span class="summary-header">Status:</span
      ><span
        class="summary-value"
        :class="serviceRateStatus === 'INACTIVE' ? 'error-text' : 'success-text'"
        >{{serviceRateStatus}}</span
      >
    </v-flex>
    <v-flex md12 class="item-container">
      <span class="summary-header">Name:</span
      ><span class="summary-value">{{serviceRateTableName}}</span>
    </v-flex>
    <v-flex md12 class="item-container">
      <span class="summary-header">Valid From:</span
      ><span class="summary-value">{{serviceRateValidFromDate}}</span>
    </v-flex>
    <v-flex md12 class="item-container">
      <span class="summary-header">Valid To:</span
      ><span class="summary-value">{{serviceRateValidToDate}}</span>
    </v-flex>
    <v-flex md12 class="item-container">
      <span class="summary-header">Active Days Remaining:</span
      ><span
        class="summary-value"
        :class="activeDaysRemaining === 0 ? 'error-text' : activeDaysRemaining <= 5 ? 'warning-text' :  ''"
        >{{activeDaysRemaining}}</span
      >
    </v-flex>
  </v-layout>
  <v-layout v-else align-center fill-height>
    <v-tooltip bottom v-if="!activeDivisionServiceRate" max-width="350px">
      <template v-slot:activator="{ on }">
        <div v-on="on" class="appbar-label-text error-type">
          {{ serviceRateStatus }}
          <v-icon size="22" class="pl-1">far fa-usd-square</v-icon>
        </div>
      </template>
      There is no current Division Rate Card. This may impact your ability to
      book and price jobs. Please contact Head Office.
    </v-tooltip>
    <span v-else>
      <v-tooltip bottom max-width="60%" open-delay="500">
        <template v-slot:activator="{ on }">
          <div v-on="on" class="appbar-label-text">
            Rates
            <v-icon size="22" color="green accent-3" class="pl-1"
              >far fa-usd-square</v-icon
            >
          </div>
        </template>
        <AppliedRateDetails
          :serviceRateTable="activeDivisionServiceRate"
        ></AppliedRateDetails>
      </v-tooltip>
    </span>
  </v-layout>
</div>
