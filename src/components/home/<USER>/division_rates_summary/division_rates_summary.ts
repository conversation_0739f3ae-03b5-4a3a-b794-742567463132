import AppliedRateDetails from '@/components/common/applied_rate_details.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useServiceRateStore } from '@/store/modules/ServiceRateStore';
import moment from 'moment-timezone';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  components: { AppliedRateDetails },
})
export default class DivisionRatesSummary extends Vue {
  @Prop({ default: false }) public isAppStatusBar: boolean;

  public companyDetailsStore = useCompanyDetailsStore();

  get activeDivisionServiceRate(): ClientServiceRate | null {
    return this.companyDetailsStore.activeDivisionServiceRate;
  }

  get serviceRateTableName(): string {
    if (!this.activeDivisionServiceRate) {
      return '-';
    }

    return this.activeDivisionServiceRate.name;
  }

  get serviceRateValidFromDate(): string {
    if (
      !this.activeDivisionServiceRate ||
      this.activeDivisionServiceRate.validFromDate === null
    ) {
      return '-';
    }

    return returnFormattedDate(this.activeDivisionServiceRate.validFromDate);
  }

  get serviceRateValidToDate(): string {
    if (
      !this.activeDivisionServiceRate ||
      this.activeDivisionServiceRate.validToDate === null
    ) {
      return '-';
    }
    return returnFormattedDate(this.activeDivisionServiceRate.validToDate);
  }

  get serviceRateStatus(): string {
    if (!this.activeDivisionServiceRate) {
      return 'INACTIVE';
    }
    return 'ACTIVE';
  }
  // returns the number of days until the service rate expires
  get activeDaysRemaining() {
    if (!this.activeDivisionServiceRate) {
      return '-';
    }
    const currentTime = moment.tz(this.companyDetailsStore.userLocale);
    const validToDate = moment(this.activeDivisionServiceRate.validToDate).tz(
      this.companyDetailsStore.userLocale,
    );
    const duration = moment.duration(validToDate.diff(currentTime));
    return Math.floor(duration.asDays());
  }

  public mounted() {
    if (this.companyDetailsStore.activeDivisionServiceRate === null) {
      useServiceRateStore().getCurrentDivisionDefaultRates();
    }
  }
}
