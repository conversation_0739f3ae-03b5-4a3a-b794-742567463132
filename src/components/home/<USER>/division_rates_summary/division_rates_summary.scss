.division-rates-summary {
  display: flex;
  align-items: center;
  height: 100%;
  .summary-header {
    margin: 18px;
    font-size: $font-size-20;
  }

  .summary-value {
    margin: 18px;
    font-weight: 500;
    font-size: $font-size-20;
    width: fit-content;
  }

  .item-container {
    border-top: 0.5px solid $border-color;
    width: 100%;
    display: flex;
    height: 55px;
    justify-content: space-between;
    align-items: center;
  }

  .appbar-label-text {
    font-family: $sub-font-family;
    font-size: $font-size-20;
    font-weight: 500;
    padding: 2px 10px;

    &:hover {
      filter: brightness(120%);
      background-color: rgba(255, 255, 255, 0.1);
      cursor: context-menu;
    }

    &.error-type {
      background-color: rgb(243, 15, 15);
      border-radius: 2px;
      font-weight: 800;
      &:hover {
        filter: brightness(120%);
        cursor: context-menu;
      }
    }
  }
}
