import AccountingSummary from '@/components/common/accounting_summary.vue';
import ClientStatusSummary from '@/components/common/client_status_summary/client_status_summary.vue';
import JobsSummary from '@/components/common/jobs_summary/jobs_summary.vue';
import DivisionFuelSurchargeSummary from '@/components/home/<USER>/division_fuel_surcharge_summary/division_fuel_surcharge_summary.vue';
import DivisionRatesSummary from '@/components/home/<USER>/division_rates_summary/index.vue';
import { useNetworkingStore } from '@/store/modules/NetworkingStore';
import { useRootStore } from '@/store/modules/RootStore';
import { Component, Vue } from 'vue-property-decorator';
@Component({
  components: {
    JobsSummary,
    ClientStatusSummary,
    AccountingSummary,
    DivisionRatesSummary,
    DivisionFuelSurchargeSummary,
  },
})
export default class Home extends Vue {
  get applicationStateHasLoaded() {
    const data: any = useRootStore().operationsPortalLoadedData;
    return Object.keys(data).every((k) => data[k]);
  }
  get wsDisconnectionDialog() {
    return useNetworkingStore().showConnectionErrorDialog;
  }
}
