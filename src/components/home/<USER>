<div>
  <div class="home-container pa-4">
    <div
      class="content"
      v-if="applicationStateHasLoaded || wsDisconnectionDialog"
    >
      <v-layout md12>
        <v-flex md4 pa-4>
          <v-flex class="summary-card">
            <div class="card-container">
              <v-layout class="card-container--header">
                <h1 class="header--light ma-0">Jobs Summary</h1>
              </v-layout>
              <div class="card-container-content">
                <JobsSummary :isStatusBar="false" />
              </div>
            </div>
          </v-flex>
        </v-flex>
        <v-flex md4 pa-4>
          <v-flex class="summary-card">
            <div class="card-container">
              <v-layout class="card-container--header">
                <h1 class="header--light ma-0">Client Details Summary</h1>
              </v-layout>
              <div class="card-container-content">
                <ClientStatusSummary />
              </div>
            </div>
          </v-flex>

          <v-flex class="summary-card">
            <div class="card-container">
              <v-layout class="card-container--header">
                <h1 class="header--light ma-0">Accounting Summary</h1>
              </v-layout>
              <div class="card-container-content">
                <AccountingSummary />
              </div>
            </div>
          </v-flex>
        </v-flex>

        <v-flex md4 pa-4>
          <v-flex class="summary-card">
            <div class="card-container">
              <v-layout class="card-container--header">
                <h1 class="header--light ma-0">
                  Division Fuel Surcharge Summary
                </h1>
              </v-layout>
              <div class="card-container-content">
                <DivisionFuelSurchargeSummary :isAppStatusBar="false" />
              </div>
            </div>
          </v-flex>

          <v-flex class="summary-card">
            <div class="card-container">
              <v-layout class="card-container--header">
                <h1 class="header--light ma-0">Division Rates Summary</h1>
              </v-layout>
              <div class="card-container-content">
                <DivisionRatesSummary />
              </div>
            </div>
          </v-flex>
        </v-flex>
      </v-layout>
    </div>
  </div>
</div>
