<template>
  <v-layout row wrap class="invoice-index">
    <v-flex md12>
      <v-layout class="invoice-index__header-bar">
        <v-sheet
          v-for="item in navigationOptions"
          class="invoice-index__header-card"
          :class="{
            'active-header__client': item.id === currentView,
          }"
          :key="item.id"
          elevation="0"
          tile
          @click="setCurrentView(item.id)"
        >
          <v-layout style="height: 100%" justify-center align-center>
            <span class="card-text">{{ item.text }}</span>
          </v-layout>
          <div class="offset-chevron"></div>
        </v-sheet>
      </v-layout>
    </v-flex>

    <v-flex
      md12
      class="invoice-index-table-container"
      v-if="!accountingIndexLoading"
    >
      <AccountingReviewedJobs
        v-if="currentView === InvoiceNavigation.REVIEWED"
        :accountingIndexLoading="accountingIndexLoading"
      ></AccountingReviewedJobs>
      <AccountingInvoiceRunJobs
        v-if="currentView === InvoiceNavigation.JOB_LIST"
        :accountingIndexLoading="accountingIndexLoading"
      ></AccountingInvoiceRunJobs>
    </v-flex>
    <v-flex md12 v-else>
      <v-layout justify-center pa-4>
        <img
          src="@/static/loader/infinity-loader-light.svg"
          height="80px"
          width="80px"
        />
      </v-layout>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
interface NavigationOption {
  id: InvoiceNavigation;
  text: string;
}

import AccountingInvoiceRunJobs from '@/components/admin/Accounting/accounting_jobs/components/accounting_invoice_run_jobs.vue';
import AccountingReviewedJobs from '@/components/admin/Accounting/accounting_jobs/components/accounting_reviewed_jobs.vue';
import { InvoiceNavigation } from '@/interface-models/Accounting/InvoiceNavigation';
import { InvoiceStatus } from '@/interface-models/Accounting/InvoiceStatus';
import { useClientInvoiceStore } from '@/store/modules/ClientInvoiceStore';
import { ComputedRef, Ref, computed, ref, toRef } from 'vue';

const props = withDefaults(
  defineProps<{
    accountingIndexLoading?: boolean;
  }>(),
  {
    accountingIndexLoading: true,
  },
);

const clientInvoiceStore = useClientInvoiceStore();

const accountingIndexLoading: Ref<boolean> = toRef(
  props,
  'accountingIndexLoading',
);

const currentView: Ref<InvoiceNavigation> = ref(InvoiceNavigation.REVIEWED);

const navigationOptions: NavigationOption[] = [
  {
    id: InvoiceNavigation.REVIEWED,
    text: 'Reviewed',
  },
  {
    id: InvoiceNavigation.JOB_LIST,
    text: 'Current / Ready',
  },
];

/**
 * Returns the current stage our invoice run is at. Returns null if no invoice
 * run is in progress (ready for invoicing)
 */
const invoiceRunStatus: ComputedRef<InvoiceStatus | null> = computed(
  () => useClientInvoiceStore().invoiceRunStatus,
);

// Sets the main view within the client invoice page. Actioned from the top
// panel navigation
function setCurrentView(navigationType: InvoiceNavigation) {
  currentView.value = navigationType;
}
</script>
