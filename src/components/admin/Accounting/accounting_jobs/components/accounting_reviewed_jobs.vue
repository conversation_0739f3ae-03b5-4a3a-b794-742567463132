<template>
  <v-layout wrap>
    <v-flex md12>
      <v-layout
        justify-space-between
        align-center
        class="accounting-data-table__actions-bar"
      >
        <v-layout align-center>
          <v-menu right ref="optionsMenu">
            <template v-slot:activator="{ on: menu }">
              <v-tooltip bottom>
                <template v-slot:activator="{ on: tooltip }">
                  <v-btn flat icon v-on="{ ...tooltip, ...menu }" class="ma-0">
                    <v-icon size="16">fas fa-ellipsis-v </v-icon>
                  </v-btn>
                </template>
                <span>View Options</span>
              </v-tooltip>
            </template>
            <v-list dense class="v-list-custom">
              <ReleaseForEditingConfirmationDialog
                :selectedJobIds="selectedJobIds"
                @closeMenuOptions="closeMenuOptions"
                @setLoading="setLoading"
              />
            </v-list>
          </v-menu>
          <h2 class="px-2">Reviewed Jobs</h2>
        </v-layout>
        <v-flex md2>
          <v-btn
            block
            color="info"
            :disabled="
              selectedJobIds.length === 0 || !isAuthorised() || isLoading
            "
            @click="setSelectedToReadyForInvoicing"
            :loading="isLoading"
            ><span class="pr-1">Ready for Invoicing</span> (<span>{{
              selectedJobIds.length
            }}</span
            >)
            <template v-slot:loader>
              <span>Loading...</span>
            </template>
          </v-btn>
        </v-flex>
      </v-layout>
    </v-flex>
    <v-flex md12 class="reviewed-jobs-container">
      <JobListTable
        :jobList="isLoading ? [] : jobList"
        :isLoading="isLoading"
        :isClientNameVisible="true"
      />
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import ReleaseForEditingConfirmationDialog from '@/components/admin/Accounting/client_invoice/components/confirmation_dialogs/release_for_editing_confirmation_dialog/index.vue';
import JobListTable from '@/components/admin/Accounting/components/job_list_table/index.vue';
import { getAccountingJobListTableData } from '@/helpers/AccountingHelpers/AccountingHelpers';
import { hasAdminOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { AccountingJobRow } from '@/interface-models/Accounting/AccountingJobRow';
import { AccountingJobSummary } from '@/interface-models/Jobs/AccountingJobSummary';
import { JobEventType } from '@/interface-models/Jobs/Event/JobEventType';
import { JobEventSummary } from '@/interface-models/Jobs/JobEventSummary';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useJobStore } from '@/store/modules/JobStore';
import { ComputedRef, Ref, computed, onMounted, ref, toRef } from 'vue';

const props = withDefaults(
  defineProps<{
    accountingIndexLoading?: boolean;
  }>(),
  {
    accountingIndexLoading: true,
  },
);

const jobStore = useJobStore();

const optionsMenu: Ref<any> = ref(null);

const accountingIndexLoading: Ref<boolean> = toRef(
  props,
  'accountingIndexLoading',
);

const jobList: Ref<AccountingJobRow[]> = ref([]);
const isLoadingJobs: Ref<boolean> = ref(true);

const isLoading: ComputedRef<boolean> = computed(() => {
  return accountingIndexLoading.value || isLoadingJobs.value;
});

// Because the select tile in our menu is a component it does not
// automatically close when selected. We must close the menu manually. Method
// call Emitted from components that act as a tile in our menu.
function closeMenuOptions(): void {
  if (optionsMenu.value) {
    optionsMenu.value.isActive = false;
  }
}

// Returns a list of selected jobIds
const selectedJobIds: ComputedRef<number[]> = computed(() => {
  return jobList.value
    .filter((job: AccountingJobRow) => job.isSelected)
    .map((selected: AccountingJobRow) => selected.jobId);
});

function setLoading(isLoading: boolean): void {
  isLoadingJobs.value = isLoading;
}

/**
 * Fetches and sets the list of jobs that are at REVIEWED work status.
 * It first sets `isLoadingJobs` to true, then fetches the jobs with a work status of 'REVIEWED'.
 * It filters the results to include only jobs with a work status of 'REVIEWED',
 * then formats the results for display in a table and assigns them to `jobList`.
 * Finally, it sets `isLoadingJobs` to false.
 */
async function getReviewedJobs() {
  isLoadingJobs.value = true;
  const results = await jobStore.getAccountingJobSummaryForWorkStatus(
    WorkStatus.REVIEWED,
  );

  if (results) {
    const filteredResults = results.filter(
      (x: AccountingJobSummary) => x.workStatus === WorkStatus.REVIEWED,
    );
    jobList.value = getAccountingJobListTableData(filteredResults, true);
  } else {
    jobList.value = [];
  }

  isLoadingJobs.value = false;
}

/**
 * Updates the selected jobs to the status of COMPLETED. Constructs a list of
 * promises then waits for all promises to resolve before refreshing the job
 * list.
 */
async function setSelectedToReadyForInvoicing(): Promise<void> {
  // Map the jobIds to a list of promises to update the job status to ReadyForInvoicing
  const jobUpdatePromises: Promise<JobEventSummary | null>[] =
    selectedJobIds.value.map((jobId: number) =>
      jobStore
        .updateJobStatus(jobId, JobEventType.ReadyForInvoicing)
        .then((response) => {
          return response;
        }),
    );
  // Wait for all promises to resolve before refreshing the job list
  await Promise.all(jobUpdatePromises).then(() => {
    getReviewedJobs();
  });
}

function isAuthorised(): boolean {
  return hasAdminOrTeamLeaderOrBranchManagerRole();
}

onMounted(() => {
  getReviewedJobs();
});
</script>
<style scoped lang="scss"></style>
