<template>
  <v-layout wrap>
    <v-flex md12>
      <v-layout
        justify-space-between
        align-center
        class="accounting-data-table__actions-bar"
      >
        <v-layout align-center>
          <v-menu right ref="optionsMenu" v-if="isAuthorised()">
            <template v-slot:activator="{ on: menu }">
              <v-tooltip bottom>
                <template v-slot:activator="{ on: tooltip }">
                  <v-btn flat icon v-on="{ ...tooltip, ...menu }" class="ma-0">
                    <v-icon size="16">fas fa-ellipsis-v </v-icon>
                  </v-btn>
                </template>
                <span>View Options</span>
              </v-tooltip>
            </template>
            <v-list dense class="v-list-custom">
              <ReleaseForEditingConfirmationDialog
                :selectedJobIds="selectedJobIds"
                @closeMenuOptions="closeMenuOptions"
                @setLoading="setLoading"
              />
            </v-list>
          </v-menu>
          <span class="week-ending__header label px-2"
            >Current Invoice Run Jobs</span
          >
        </v-layout>
      </v-layout>
    </v-flex>
    <v-flex md12 class="reviewed-jobs-container">
      <JobListTable
        :jobList="isLoading ? [] : jobList"
        :isLoading="isLoadingAllReadyForInvoiceJobList"
        :disabledJobIds="disabledReadyForInvoiceJobIds"
        :isClientNameVisible="true"
      />
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import ReleaseForEditingConfirmationDialog from '@/components/admin/Accounting/client_invoice/components/confirmation_dialogs/release_for_editing_confirmation_dialog/index.vue';
import JobListTable from '@/components/admin/Accounting/components/job_list_table/index.vue';
import { getAccountingJobListTableData } from '@/helpers/AccountingHelpers/AccountingHelpers';
import { hasAdminOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { AccountingJobRow } from '@/interface-models/Accounting/AccountingJobRow';
import { AccountingJobSummary } from '@/interface-models/Jobs/AccountingJobSummary';
import { AccountingStatus } from '@/interface-models/Jobs/AccountingStatus';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useJobStore } from '@/store/modules/JobStore';
import { useMittListener } from '@/utils/useMittListener';
import { ComputedRef, Ref, computed, onMounted, ref, toRef } from 'vue';

const props = withDefaults(
  defineProps<{
    accountingIndexLoading?: boolean;
  }>(),
  {
    accountingIndexLoading: true,
  },
);

const jobStore = useJobStore();

const optionsMenu: Ref<any> = ref(null);

const accountingIndexLoading: Ref<boolean> = toRef(
  props,
  'accountingIndexLoading',
);

// const isLoadingJobs: Ref<boolean> = ref(true);

const jobList: Ref<AccountingJobRow[]> = ref([]);
const isLoadingAllReadyForInvoiceJobList: Ref<boolean> = ref(false);

const isLoading: ComputedRef<boolean> = computed(() => {
  return (
    accountingIndexLoading.value ||
    // isLoadingJobs.value ||
    isLoadingAllReadyForInvoiceJobList.value
  );
});

// Because the select tile in our menu is a component it does not
// automatically close when selected. We must close the menu manually. Method
// call Emitted from components that act as a tile in our menu.
function closeMenuOptions(): void {
  if (optionsMenu.value) {
    optionsMenu.value.isActive = false;
  }
}

// Returns a list of selected jobIds
const selectedJobIds: ComputedRef<number[]> = computed(() => {
  return jobList.value
    .filter((job: AccountingJobRow) => job.isSelected)
    .map((selected: AccountingJobRow) => selected.jobId);
});

function setLoading(isLoading: boolean): void {
  isLoadingAllReadyForInvoiceJobList.value = isLoading;
}

function isAuthorised(): boolean {
  return hasAdminOrTeamLeaderOrBranchManagerRole();
}

/**
 * Returns a list of jobIds that should not be able to be sent back to DRIVER
 * COMPLETED status, based on if they have been actioned in either invoicing
 * or RCTI flows
 */
const disabledReadyForInvoiceJobIds: ComputedRef<number[]> = computed(() => {
  return jobList.value
    .filter(
      (r) =>
        r.workStatus === WorkStatus.FINALISED ||
        r.revenueStatus > AccountingStatus.NOT_ACTIONED ||
        r.expenseStatus > AccountingStatus.NOT_ACTIONED,
    )
    .map((r) => r.jobId);
});

/**
 * Requests job summaries with a work status of 'COMPLETED'.
 * It filters the results to include only jobs with a work status of 'COMPLETED',
 * then formats the results for display in a table and assigns them to `allReadyForInvoiceJobList`.
 */
async function requestJobSummaries() {
  isLoadingAllReadyForInvoiceJobList.value = true;
  const results = await jobStore.getAccountingJobSummaryForWorkStatus(
    WorkStatus.COMPLETED,
  );
  const jobs =
    results?.filter(
      (x: AccountingJobSummary) => x.workStatus === WorkStatus.COMPLETED,
    ) ?? [];
  jobList.value = getAccountingJobListTableData(jobs, true);
  isLoadingAllReadyForInvoiceJobList.value = false;
}

useMittListener('releasedAccountingJobsToCompleted', requestJobSummaries);

onMounted(() => {
  requestJobSummaries();
});
</script>
<style scoped lang="scss"></style>
