<template>
  <v-layout row wrap class="invoice-index">
    <v-flex md12>
      <v-layout class="invoice-index__header-bar">
        <v-sheet
          v-for="item in navigationOptions"
          class="invoice-index__header-card"
          :class="item.id === currentView ? 'active-header__client' : ''"
          :key="item.id"
          elevation="0"
          tile
          @click="setCurrentView(item.id)"
        >
          <v-layout style="height: 100%" justify-center align-center>
            <span class="card-text">{{ item.text }}</span>
          </v-layout>
          <div class="offset-chevron"></div>
        </v-sheet>
      </v-layout>
    </v-flex>

    <v-flex
      md12
      class="invoice-index-table-container"
      v-if="!accountingIndexLoading"
    >
      <template v-if="isViewingStartInvoiceCard">
        <StartInvoiceRunCard
          ref="startInvoiceRunCard"
          :type="AccountingNavigationType.EQUIPMENT_HIRE"
          @confirm="applyRunParameters"
        ></StartInvoiceRunCard>
      </template>
      <template v-else>
        <EquipmentHireReadyForInvoicing
          v-if="currentView === 'READY_FOR_INVOICING'"
          :accountingIndexLoading="accountingIndexLoading"
          @setCurrentView="setCurrentView"
          @editRunParameters="editRunParameters"
        />
        <EquipmentHireDraft
          v-if="currentView === 'DRAFT'"
          :accountingIndexLoading="accountingIndexLoading"
          @setCurrentView="setCurrentView"
        />
        <EquipmentHireDummy
          v-if="currentView === 'DUMMY'"
          :accountingIndexLoading="accountingIndexLoading"
          @setCurrentView="setCurrentView"
        />
        <EquipmentHireApproved
          v-if="currentView === 'APPROVED'"
          :accountingIndexLoading="accountingIndexLoading"
          @setCurrentView="setCurrentView"
        />
      </template>
    </v-flex>
    <v-flex md12 v-else>
      <v-layout justify-center pa-4>
        <img
          src="@/static/loader/infinity-loader-light.svg"
          height="80px"
          width="80px"
        />
      </v-layout>
    </v-flex>

    <v-dialog
      v-if="hireContract"
      v-model="isViewingHireContract"
      width="60%"
      class="hire-contract"
      content-class="v-dialog-custom"
      :transition="false"
    >
      <v-layout
        justify-space-between
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>Equipment Hire Contract</span>
        <div
          class="app-theme__center-content--closebutton"
          @click="isViewingHireContract = false"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <v-layout
        class="app-theme__center-content--body pa-3"
        style="max-height: 85vh; overflow-y: scroll"
      >
        <HireContractComponent
          :fleetAssetId="hireContract.fleetAssetId"
          :contract="hireContract"
          :isAssetInformation="true"
        />
      </v-layout>
    </v-dialog>
  </v-layout>
</template>

<script setup lang="ts">
import StartInvoiceRunCard from '@/components/admin/Accounting/client_invoice/components/start_invoice_run_card.vue';
import EquipmentHireApproved from '@/components/admin/Accounting/equipment_hire/components/equipment_hire_approved.vue';
import EquipmentHireDraft from '@/components/admin/Accounting/equipment_hire/components/equipment_hire_draft.vue';
import EquipmentHireDummy from '@/components/admin/Accounting/equipment_hire/components/equipment_hire_dummy.vue';
import EquipmentHireReadyForInvoicing from '@/components/admin/Accounting/equipment_hire/components/equipment_hire_ready_for_invoicing.vue';
import HireContractComponent from '@/components/admin/FleetAsset/trailer/components/hire_contract.vue';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { AccountingNavigationType } from '@/interface-models/Accounting/AccountingNavigationType';
import { InvoiceNavigation } from '@/interface-models/Accounting/InvoiceNavigation';
import { InvoiceRunParameters } from '@/interface-models/Accounting/InvoiceRunParameters';
import { InvoiceStatus } from '@/interface-models/Accounting/InvoiceStatus';
import HireContract from '@/interface-models/FleetAsset/EquipmentHire/HireContract';
import { useEquipmentHireInvoiceStore } from '@/store/modules/EquipmentHireInvoiceStore';
import { useMittListener } from '@/utils/useMittListener';
import {
  ComputedRef,
  Ref,
  computed,
  nextTick,
  onBeforeMount,
  ref,
  toRef,
  watch,
} from 'vue';

interface NavigationOption {
  id: InvoiceNavigation;
  text: string;
}

const props = withDefaults(
  defineProps<{
    accountingIndexLoading?: boolean;
  }>(),
  {
    accountingIndexLoading: true,
  },
);

const equipmentHireInvoiceStore = useEquipmentHireInvoiceStore();

const accountingIndexLoading: Ref<boolean> = toRef(
  props,
  'accountingIndexLoading',
);

const startInvoiceRunCard: Ref<any> = ref(null);
const isViewingStartInvoiceCard: Ref<boolean> = ref(false);

const currentView: Ref<InvoiceNavigation> = ref(
  InvoiceNavigation.READY_FOR_INVOICING,
);

const hireContract: Ref<HireContract | null> = ref(null);
const isViewingHireContract: Ref<boolean> = ref(false);

const navigationOptions: NavigationOption[] = [
  {
    id: InvoiceNavigation.READY_FOR_INVOICING,
    text: 'Ready for Invoicing',
  },
  {
    id: InvoiceNavigation.DRAFT,
    text: 'Draft',
  },
  {
    id: InvoiceNavigation.DUMMY,
    text: 'Dummy',
  },
  {
    id: InvoiceNavigation.APPROVED,
    text: 'Approved',
  },
];

/**
 * Returns the current stage our invoice run is at. Returns null if no invoice
 * run is in progress (ready for invoicing)
 */
const invoiceRunStatus: ComputedRef<InvoiceStatus | null> = computed(
  () => equipmentHireInvoiceStore.invoiceRunStatus,
);

/**
 * Watches for the loading state of the accounting index page. Display the start
 * invoice run card if there is no invoice run currently in progress
 */
watch(accountingIndexLoading, (newValue) => {
  if (!newValue) {
    setNavigationViewForInvoiceStatus(invoiceRunStatus.value);
  }
});

/**
 * Sets current view based on the provided InvoiceStatus. Called on mount if the
 * parent is already finished loading, otherwise called by the watch on
 * accountingIndexLoading
 */
function setNavigationViewForInvoiceStatus(status: InvoiceStatus | null) {
  switch (status) {
    case InvoiceStatus.DRAFT:
      setCurrentView(InvoiceNavigation.DRAFT);
      break;
    case InvoiceStatus.DUMMY:
      setCurrentView(InvoiceNavigation.DUMMY);
      break;
    case InvoiceStatus.APPROVED:
      setCurrentView(InvoiceNavigation.APPROVED);
      break;
    default:
      setCurrentView(InvoiceNavigation.READY_FOR_INVOICING);
      break;
  }
}

/**
 * Sets the main view within the client invoice page. Actioned from the top
 * panel navigation
 */
function setCurrentView(navigationType: InvoiceNavigation) {
  if (
    navigationType === InvoiceNavigation.READY_FOR_INVOICING &&
    invoiceRunStatus.value === null
  ) {
    isViewingStartInvoiceCard.value = true;
  }
  currentView.value = navigationType;
}

/**
 * Opens the dialog to update the run parameters. Called by emit from
 * ClientInvoiceReadyForInvoicing component.
 */
function editRunParameters() {
  if (
    currentView.value === InvoiceNavigation.READY_FOR_INVOICING &&
    invoiceRunStatus.value === null
  ) {
    isViewingStartInvoiceCard.value = true;
    nextTick(() => {
      const params = {
        billingCycleIds: equipmentHireInvoiceStore.billingCycleIds,
        weekEndingDate: equipmentHireInvoiceStore.weekEndingDate,
        processingDate: equipmentHireInvoiceStore.processingDate,
        includeAdjustments: equipmentHireInvoiceStore.includeAdjustments,
      };
      startInvoiceRunCard.value?.initWithParameters(params);
    });
  }
}

/**
 * Applies the invoice run parameters to the ready for invoicing component
 */
function applyRunParameters(invoiceRunParameters: InvoiceRunParameters) {
  if (
    invoiceRunParameters.billingCycleIds === null ||
    invoiceRunParameters.weekEndingDate === null ||
    invoiceRunParameters.processingDate === null
  ) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }
  equipmentHireInvoiceStore.setWeekEndingDate(
    invoiceRunParameters.weekEndingDate,
  );
  equipmentHireInvoiceStore.setProcessingDate(
    invoiceRunParameters.processingDate,
  );
  equipmentHireInvoiceStore.setBillingCycleIds(
    invoiceRunParameters.billingCycleIds,
  );
  equipmentHireInvoiceStore.setIncludeAdjustments(
    invoiceRunParameters.includeAdjustments ?? false,
  );

  isViewingStartInvoiceCard.value = false;
}

/**
 * Mitt callback for 'getHireContractById' event, which is called by the child
 * components of this component. Listen for the event containing the
 * HireContract we want to view, and show the dialog.
 * @param hireContract HireContract response from the server
 * @returns void
 */
function handleHireContract(contract: HireContract | null) {
  if (contract === null) {
    return;
  }
  hireContract.value = contract;
  isViewingHireContract.value = true;
}

useMittListener('getHireContractById', handleHireContract);

onBeforeMount(() => {
  if (!accountingIndexLoading.value) {
    setNavigationViewForInvoiceStatus(invoiceRunStatus.value);
  }
});
</script>
