<template>
  <v-layout wrap>
    <v-flex md12>
      <v-layout align-center class="accounting-data-table__actions-bar">
        <v-menu right ref="optionsMenu">
          <template v-slot:activator="{ on: menu }">
            <v-tooltip bottom>
              <template v-slot:activator="{ on: tooltip }">
                <v-btn
                  flat
                  icon
                  v-on="{ ...tooltip, ...menu }"
                  class="ma-0"
                  :disabled="invoiceRunStatus !== 'APPROVED' || isLoading"
                >
                  <v-icon size="16">fas fa-ellipsis-v </v-icon>
                </v-btn>
              </template>
              <span>View Options</span>
            </v-tooltip>
          </template>
          <v-list dense class="v-list-custom">
            <InvoiceEventHistoryDialog
              :invoiceEventHistory="invoiceEventHistory"
              :invoiceRunStatus="invoiceRunStatus"
              @closeMenuOptions="closeMenuOptions"
            />
            <v-divider></v-divider>

            <CancelRunConfirmationDialog
              v-if="invoiceRunStatus === 'APPROVED'"
              :runType="'Approved'"
              @closeMenuOptions="closeMenuOptions"
              @cancelRun="cancelApprovedRun"
            />
          </v-list>
        </v-menu>

        <v-layout v-if="invoiceRunStatus === 'APPROVED'">
          <InvoiceRunParametersSummary
            :parameters="{
              billingCycleIds: billingCycleIds,
              weekEndingDate: weekEndingDate,
              processingDate: processingDate,
              includeAdjustments: equipmentHireInvoiceStore.includeAdjustments,
            }"
          ></InvoiceRunParametersSummary>
        </v-layout>

        <v-layout v-if="invoiceRunStatus !== 'APPROVED'">
          <CurrentInvoiceStatus :invoiceRunStatus="invoiceRunStatus" />
        </v-layout>
        <v-spacer />
        <v-flex md2 style="height: 48px">
          <ConfirmationDialog
            buttonText="Approve and Email Equipment Hire"
            message="I declare that:"
            title="Confirm Approval for Live Invoice"
            @confirm="approveAndEmailRctiList"
            :isSmallButton="false"
            :buttonDisabled="!isAuthorised() || invoiceRunStatus !== 'APPROVED'"
            :isOutlineButton="false"
            :isBlockButton="true"
            :buttonColor="'info'"
            :confirmationButtonText="'Approve and Send'"
            :isCheckboxList="true"
            :checkboxLabelList="approveAndEmailConfirmationCheckBoxList"
            :dialogIsActive="true"
          ></ConfirmationDialog>
        </v-flex>
      </v-layout>
    </v-flex>

    <v-flex md12 class="reviewed-jobs-container">
      <EquipmentHireTable
        ref="equipmentHireTable"
        :equipmentHireInvoices="equipmentHireInvoices"
        :isLoading="isLoading"
        :invoiceType="invoiceType"
      />
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import CancelRunConfirmationDialog from '@/components/admin/Accounting/components/cancel_run_confirmation_dialog.vue';
import CurrentInvoiceStatus from '@/components/admin/Accounting/components/current_invoice_status/index.vue';
import InvoiceEventHistoryDialog from '@/components/admin/Accounting/components/invoice_event_history_dialog.vue';
import InvoiceRunParametersSummary from '@/components/admin/Accounting/components/invoice_run_parameters_summary.vue';
import EquipmentHireTable from '@/components/admin/Accounting/equipment_hire/components/equipment_hire_table.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import { getEquipmentHireTableData } from '@/helpers/AccountingHelpers/AccountingHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { CurrentInvoiceRunTypeResponse } from '@/interface-models/Accounting/CurrentInvoiceRunTypeResponse';
import {
  EquipmentHireAccountingTable,
  EquipmentHireOwnerInvoiceRow,
} from '@/interface-models/Accounting/EquipmentHireAccountingTable';
import { EquipmentHireRunAccountingTable } from '@/interface-models/Accounting/EquipmentHireRunAccountingTable';
import { InvoiceEmailSentUpdate } from '@/interface-models/Accounting/InvoiceEmailSentUpdate';
import { InvoiceEvent } from '@/interface-models/Accounting/InvoiceEvent';
import { InvoiceNavigation } from '@/interface-models/Accounting/InvoiceNavigation';
import { InvoiceRunSuccessResponse } from '@/interface-models/Accounting/InvoiceRunSuccessResponse';
import { InvoiceStatus } from '@/interface-models/Accounting/InvoiceStatus';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { useEquipmentHireInvoiceStore } from '@/store/modules/EquipmentHireInvoiceStore';
import { sessionManager } from '@/store/session/SessionState';
import { useMittListener } from '@/utils/useMittListener';
import { computed, ComputedRef, onMounted, ref, Ref } from 'vue';

const emit = defineEmits<{
  (event: 'setCurrentView', payload: InvoiceNavigation): void;
}>();

const props = withDefaults(
  defineProps<{
    accountingIndexLoading: boolean;
  }>(),
  {
    accountingIndexLoading: false,
  },
);

const equipmentHireInvoiceStore = useEquipmentHireInvoiceStore();

const weekEndingDate: Ref<number | null> = ref(null);
const processingDate: Ref<number | null> = ref(null);
const billingCycleIds: Ref<number[] | null> = ref(null);

const equipmentHireInvoices: Ref<EquipmentHireOwnerInvoiceRow[]> = ref([]);
const invoiceEventHistory: Ref<InvoiceEvent[]> = ref([]);
const invoiceType: Ref<InvoiceNavigation> = ref(InvoiceNavigation.APPROVED);
const isLoadingApproved: Ref<boolean> = ref(false);
const isLoadingUserActionRequest: Ref<boolean> = ref(false);

/**
 * List of checkboxes the user will need to confirm before approving and sending the invoices.
 */
const approveAndEmailConfirmationCheckBoxList: string[] = [
  'I understand that an email will be sent to all Subcontractors listed on this page',
  'I understand that by approving the Live Run, they will not be editable in the future',
];

/**
 * Sets the table data for equipment hire invoices.
 */
function setTableData(response: EquipmentHireAccountingTable[]): void {
  if (!processingDate.value && response.length > 0) {
    return;
  }
  equipmentHireInvoices.value = getEquipmentHireTableData(
    response,
    processingDate.value,
  );
  isLoadingApproved.value = false;
}

/**
 * Returns the current stage our invoice run is at.
 */
const invoiceRunStatus: ComputedRef<InvoiceStatus | null> = computed(() => {
  return equipmentHireInvoiceStore.invoiceRunStatus;
});

/**
 * Returns overall loading state for component.
 */
const isLoading: ComputedRef<boolean> = computed(() => {
  return (
    props.accountingIndexLoading ||
    isLoadingApproved.value ||
    isLoadingUserActionRequest.value
  );
});

/**
 * Request for approving the current APPROVED run to be finalised and sent,
 * which will move it to the SENT status.
 */
function approveAndEmailRctiList(): void {
  equipmentHireInvoiceStore.approveLiveRun();
}

/**
 * Because the select tile in our menu is a component it does not automatically close when selected.
 * We must close the menu manually. Method call Emitted from components that act as a tile in our menu.
 */
function closeMenuOptions(): void {
  // If using template refs, you can use a ref here if needed
  // Example: if (optionsMenu.value) { optionsMenu.value.isActive = false; }
}

/**
 * Initial request to get the current approved run.
 */
async function getApprovedRun(): Promise<void> {
  isLoadingApproved.value = true;
  setApprovedRunResponse(
    await equipmentHireInvoiceStore.getEquipmentHireInvoiceRun(
      InvoiceStatus.APPROVED,
    ),
  );
}

/**
 * Request to cancel the approved invoice run.
 */
function cancelApprovedRun(): void {
  isLoadingUserActionRequest.value = true;
  equipmentHireInvoiceStore.cancelApprovedRun();
}

/**
 * Sets the approved run response and updates relevant state.
 */
function setApprovedRunResponse(
  response: EquipmentHireRunAccountingTable | null,
): void {
  if (!response) {
    return;
  }
  weekEndingDate.value = response.weekEndingDate;
  processingDate.value = response.processingDate;
  billingCycleIds.value = response.billingCycleIds;
  invoiceEventHistory.value = response.eventList;
  setTableData(response.equipmentHireAccountingTables);
}

/**
 * Handles the response for user actions and updates state/notifications accordingly.
 */
function setResponseForUserAction(
  response: InvoiceRunSuccessResponse,
  isApprovedCancel: boolean,
  isDummyApproved: boolean,
): void {
  if (!response || !response.success) {
    isLoadingUserActionRequest.value = false;
    showNotification(GENERIC_ERROR_MESSAGE);
    getApprovedRun();
    return;
  }

  if (isApprovedCancel) {
    equipmentHireInvoiceStore.setInvoiceRunStatus(InvoiceStatus.DRAFT);
    equipmentHireInvoiceStore.setProcessingDate(response.processingDate);
    equipmentHireInvoiceStore.setWeekEndingDate(response.weekEndingDate);
    equipmentHireInvoiceStore.setBillingCycleIds(response.billingCycleIds);
  }

  const responseIsForDivisionUsers =
    response.userName !== sessionManager.getUserName();

  if (responseIsForDivisionUsers) {
    if (isApprovedCancel) {
      showNotification('Approved cancelled.', {
        title: 'Approved Run',
        type: HealthLevel.INFO,
      });
    }
    if (isDummyApproved) {
      showNotification('Approved run available.', {
        title: 'Approved Run',
        type: HealthLevel.INFO,
      });
    }
  }
  if (!responseIsForDivisionUsers) {
    if (isApprovedCancel) {
      showNotification('Approved cancelled.', {
        title: 'Approved Run',
        type: HealthLevel.SUCCESS,
      });
      emit('setCurrentView', InvoiceNavigation.DRAFT);
      return;
    }
  }
  isLoadingUserActionRequest.value = false;
  getApprovedRun();
}

/**
 * Updates the status of an invoice row when an email is sent.
 */
function setRctiEmailUpdate(invoiceEmail: InvoiceEmailSentUpdate | null): void {
  if (!invoiceEmail) {
    return;
  }
  const ledgerId: string = invoiceEmail.ledgerId;
  const isSuccessful: boolean = invoiceEmail.isSuccessful;
  const invoice: EquipmentHireOwnerInvoiceRow | undefined =
    equipmentHireInvoices.value.find(
      (x: EquipmentHireOwnerInvoiceRow) => x.ledgerId === ledgerId,
    );

  if (!invoice) {
    return;
  }
  const status: InvoiceStatus = isSuccessful
    ? InvoiceStatus.SENT
    : InvoiceStatus.FAIL;
  invoice.status = status;
}

/**
 * Handles the response when the live run is finished.
 */
function setLiveRunFinishedResponse(
  runTypeResponse: CurrentInvoiceRunTypeResponse | null,
): void {
  if (!runTypeResponse) {
    console.error('Current Invoice Run Type Response was null.');
    return;
  }
  if (
    equipmentHireInvoices.value.length > 0 &&
    !runTypeResponse.equipmentHire
  ) {
    showNotification('Live run complete.', {
      title: 'Live Run',
      type: HealthLevel.SUCCESS,
    });
    getApprovedRun();
  }
}

/**
 * Returns true if the user is authorised.
 */
function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}

/** Mitt callback for cancelling a DUMMY run (transition to APPROVED) */
function handleDummyApproval(payload: InvoiceRunSuccessResponse | null): void {
  if (payload) {
    setResponseForUserAction(payload, false, true);
  }
}

/** Mitt callback for cancelling an APPROVED run (transition to DRAFT) */
function handleApprovedCancel(payload: InvoiceRunSuccessResponse | null): void {
  if (payload) {
    setResponseForUserAction(payload, true, false);
  }
}

useMittListener('currentInvoiceRunType', setLiveRunFinishedResponse);
useMittListener('approveEquipmentHireDummyRunResponse', handleDummyApproval);
useMittListener('cancelEquipmentHireLiveRunResponse', handleApprovedCancel);
useMittListener('equipmentHireEmailSentUpdate', setRctiEmailUpdate);

onMounted(() => {
  getApprovedRun();
});
</script>
