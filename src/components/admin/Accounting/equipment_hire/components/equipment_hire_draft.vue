<template>
  <v-layout wrap>
    <v-flex md12>
      <v-layout align-center class="accounting-data-table__actions-bar">
        <v-menu right ref="optionsMenu">
          <template v-slot:activator="{ on: menu }">
            <v-tooltip bottom>
              <template v-slot:activator="{ on: tooltip }">
                <v-btn
                  flat
                  icon
                  v-on="{ ...tooltip, ...menu }"
                  class="ma-0"
                  :disabled="invoiceRunStatus !== 'DRAFT' || isLoading"
                >
                  <v-icon size="16">fas fa-ellipsis-v </v-icon>
                </v-btn>
              </template>
              <span>View Options</span>
            </v-tooltip>
          </template>
          <v-list dense class="v-list-custom">
            <EquipmentHireSummaryReport
              v-if="weekEndingDate"
              @closeMenuOptions="closeMenuOptions"
              :invoices="equipmentHireInvoices"
              :isDisabled="false"
              :weekEndingDate="weekEndingDate"
            />
            <v-divider></v-divider>
            <ProcessingDateUpdateDialog
              v-if="processingDate !== null && invoiceRunStatus === 'DRAFT'"
              :isDraft="invoiceRunStatus === 'DRAFT'"
              @closeMenuOptions="closeMenuOptions"
              :processingDate.sync="processingDate"
              @updateProcessingDate="updateProcessingDate"
              :isLoadingUserActionRequest.sync="isLoadingUserActionRequest"
            />
            <v-divider></v-divider>

            <DraftJobRemovalConfirmationDialog
              @closeMenuOptions="closeMenuOptions"
              :selectedJobIds="selectedHireContractIds"
              :totalNumberOfJobsInDraft.sync="
                totalNumberOfSubcontractorsInDraft
              "
              @removeSelectedJobsFromDraft="removeSelectedContractsFromDraft"
              @cancelDraftRun="cancelDraftRun"
              :isContracts="true"
            />

            <v-divider></v-divider>

            <InvoiceEventHistoryDialog
              v-if="invoiceRunStatus === 'DRAFT'"
              @closeMenuOptions="closeMenuOptions"
              :invoiceEventHistory="invoiceEventHistory"
              :invoiceRunStatus="invoiceRunStatus"
            />
            <v-divider></v-divider>
            <CancelDraftDialog
              v-if="invoiceRunStatus === 'DRAFT'"
              @closeMenuOptions="closeMenuOptions"
              @cancelDraftRun="cancelDraftRun"
            />
          </v-list>
        </v-menu>
        <v-layout v-if="invoiceRunStatus === 'DRAFT'">
          <InvoiceRunParametersSummary
            :parameters="{
              billingCycleIds: billingCycleIds,
              weekEndingDate: weekEndingDate,
              processingDate: processingDate,
              includeAdjustments: equipmentHireInvoiceStore.includeAdjustments,
            }"
          ></InvoiceRunParametersSummary>
        </v-layout>

        <v-layout v-if="invoiceRunStatus !== 'DRAFT'">
          <CurrentInvoiceStatus :invoiceRunStatus="invoiceRunStatus" />
        </v-layout>
        <v-spacer />
        <v-flex md2>
          <v-btn
            block
            color="info"
            :disabled="
              isLoading ||
              invoiceRunStatus !== 'DRAFT' ||
              equipmentHireInvoices.length < 1 ||
              !isAuthorised()
            "
            :loading="isLoading"
            @click="approveDraftRun"
          >
            Generate Dummy
            <template v-slot:loader>
              <span>Loading...</span>
            </template>
          </v-btn>
        </v-flex>
      </v-layout>
    </v-flex>

    <v-flex md12 class="reviewed-jobs-container">
      <EquipmentHireTable
        ref="equipmentHireTable"
        :equipmentHireInvoices="equipmentHireInvoices"
        :isLoading="isLoading"
        :invoiceType="invoiceType"
      />
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import DraftJobRemovalConfirmationDialog from '@/components/admin/Accounting/client_invoice/components/confirmation_dialogs/draft_job_removal_confirmation_dialog.vue';
import CancelDraftDialog from '@/components/admin/Accounting/components/cancel_draft_dialog.vue';
import CurrentInvoiceStatus from '@/components/admin/Accounting/components/current_invoice_status/index.vue';
import EquipmentHireSummaryReport from '@/components/admin/Accounting/components/equipment_hire_summary_report_dialog.vue';
import InvoiceEventHistoryDialog from '@/components/admin/Accounting/components/invoice_event_history_dialog.vue';
import InvoiceRunParametersSummary from '@/components/admin/Accounting/components/invoice_run_parameters_summary.vue';
import ProcessingDateUpdateDialog from '@/components/admin/Accounting/components/processing_date_update_dialog.vue';
import EquipmentHireTable from '@/components/admin/Accounting/equipment_hire/components/equipment_hire_table.vue';
import { getEquipmentHireTableData } from '@/helpers/AccountingHelpers/AccountingHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import {
  EquipmentContractInvoiceRow,
  EquipmentHireAccountingTable,
  EquipmentHireOwnerInvoiceRow,
} from '@/interface-models/Accounting/EquipmentHireAccountingTable';
import { EquipmentHireRunAccountingTable } from '@/interface-models/Accounting/EquipmentHireRunAccountingTable';
import { InvoiceEvent } from '@/interface-models/Accounting/InvoiceEvent';
import { InvoiceNavigation } from '@/interface-models/Accounting/InvoiceNavigation';
import { InvoiceRunSuccessResponse } from '@/interface-models/Accounting/InvoiceRunSuccessResponse';
import { InvoiceStatus } from '@/interface-models/Accounting/InvoiceStatus';
import { AdjustmentLedgerContractResponse } from '@/interface-models/Generic/Accounting/hireContractChargeHistory/HireContractChargeHistory';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { useEquipmentHireInvoiceStore } from '@/store/modules/EquipmentHireInvoiceStore';
import { sessionManager } from '@/store/session/SessionState';
import { useMittListener } from '@/utils/useMittListener';
import { computed, ComputedRef, onMounted, ref, Ref } from 'vue';

const emit = defineEmits<{
  (event: 'setCurrentView', payload: InvoiceNavigation): void;
}>();

const props = withDefaults(
  defineProps<{
    accountingIndexLoading?: boolean;
  }>(),
  {
    accountingIndexLoading: false,
  },
);

const equipmentHireInvoiceStore = useEquipmentHireInvoiceStore();

const weekEndingDate: Ref<number | null> = ref(null);
const processingDate: Ref<number | null> = ref(null);
const billingCycleIds: Ref<number[] | null> = ref(null);

const equipmentHireInvoices: Ref<EquipmentHireOwnerInvoiceRow[]> = ref([]);
const invoiceEventHistory: Ref<InvoiceEvent[]> = ref([]);
const invoiceType: Ref<InvoiceNavigation> = ref(InvoiceNavigation.DRAFT);
const isLoadingDraft: Ref<boolean> = ref(false);
const isLoadingUserActionRequest: Ref<boolean> = ref(false);

const equipmentHireTable = ref();
const optionsMenu = ref();

/**
 * Returns all the users selected asset/hire contracts.
 */
const selectedHireContractIds: ComputedRef<string[]> = computed(() => {
  const selected: string[] = [];
  for (const invoice of equipmentHireInvoices.value) {
    for (const fleetAsset of invoice.equipmentContracts) {
      if (fleetAsset.isSelected) {
        selected.push(fleetAsset.contractId);
      }
    }
  }
  return selected;
});

/**
 * Returns overall loading state for component.
 */
const isLoading: ComputedRef<boolean> = computed(() => {
  return (
    props.accountingIndexLoading ||
    isLoadingDraft.value ||
    isLoadingUserActionRequest.value
  );
});

/**
 * Returns the current stage our invoice run is at.
 */
const invoiceRunStatus: ComputedRef<InvoiceStatus | null> = computed(() => {
  return equipmentHireInvoiceStore.invoiceRunStatus;
});

/**
 * Returns the total number of jobs currently in the draft run.
 */
const totalNumberOfSubcontractorsInDraft: ComputedRef<number> = computed(() => {
  return equipmentHireInvoices.value.filter(
    (x: EquipmentHireOwnerInvoiceRow) => x.isSelected,
  ).length;
});

/**
 * Because the select tile in our menu is a component it does not automatically close when selected.
 * We must close the menu manually. Method call Emitted from components that act as a tile in our menu.
 */
function closeMenuOptions(): void {
  if (optionsMenu.value) {
    optionsMenu.value.isActive = false;
  }
}

/**
 * Sets the draft run response and updates relevant state.
 */
function setDraftRunResponse(
  response: EquipmentHireRunAccountingTable | null,
): void {
  if (!response) {
    console.error('Draft Run Response is null');
    isLoadingDraft.value = false;
    return;
  }
  weekEndingDate.value = response.weekEndingDate;
  processingDate.value = response.processingDate;
  billingCycleIds.value = response.billingCycleIds;

  equipmentHireInvoiceStore.setIncludeAdjustments(response.includeAdjustments);
  invoiceEventHistory.value = response.eventList;
  setTableData(response.equipmentHireAccountingTables);
}

/**
 * Sets the table data for equipment hire invoices.
 */
function setTableData(response: EquipmentHireAccountingTable[]): void {
  if (!processingDate.value && response.length > 0) {
    return;
  }
  equipmentHireInvoices.value = getEquipmentHireTableData(
    response,
    processingDate.value,
  );
  isLoadingDraft.value = false;
}

/**
 * Initial request to get the current draft run.
 */
async function getDraftRun(): Promise<void> {
  isLoadingDraft.value = true;
  setDraftRunResponse(
    await equipmentHireInvoiceStore.getEquipmentHireInvoiceRun(
      InvoiceStatus.DRAFT,
    ),
  );
}

/**
 * Cancels the draft run.
 */
function cancelDraftRun(): void {
  isLoadingUserActionRequest.value = true;
  equipmentHireInvoiceStore.cancelDraftRun();
}

/**
 * Removes selected contracts from the draft run.
 */
async function removeSelectedContractsFromDraft(): Promise<void> {
  setDraftJobRemoval(
    await equipmentHireInvoiceStore.removeHireContractsFromDraftRun(
      selectedHireContractIds.value,
    ),
  );
}

/**
 * Sets the draft job removal response and refreshes the draft run.
 */
function setDraftJobRemoval(
  jobRemovalResponse: InvoiceRunSuccessResponse | null,
): void {
  if (!jobRemovalResponse) {
    return;
  }
  getDraftRun();
}

/**
 * Approves the draft run.
 */
function approveDraftRun(): void {
  isLoadingUserActionRequest.value = true;
  equipmentHireInvoiceStore.approveDraftRun();
}

/**
 * Updates the processing date for the draft run.
 */
function updateProcessingDate(newProcessingDate: number): void {
  processingDate.value = newProcessingDate;
  isLoadingUserActionRequest.value = true;
  equipmentHireInvoiceStore.updateDraftProcessingDate(processingDate.value);
}

/**
 * Find and replace the row in our table with the updated data.
 * This is utilised when a user is editing/adding an adjustment charge.
 */
function updateEquipmentHireInvoice(
  response: AdjustmentLedgerContractResponse | null,
): void {
  if (!response?.success) {
    return;
  }
  const equipmentHireAccountingTable: EquipmentHireAccountingTable =
    response.equipmentHireAccountingTable;
  const updateAsEquipmentHireInvoiceRow: EquipmentHireOwnerInvoiceRow[] =
    getEquipmentHireTableData(
      [equipmentHireAccountingTable],
      processingDate.value,
    );

  const updatedEquipmentHireInvoiceRow = updateAsEquipmentHireInvoiceRow[0];
  const indexToUpdate: number = equipmentHireInvoices.value.findIndex(
    (x: EquipmentHireOwnerInvoiceRow) =>
      x.ledgerId === updatedEquipmentHireInvoiceRow.ledgerId,
  );
  const equipmentHireInvoiceRowToUpdate: EquipmentHireOwnerInvoiceRow =
    equipmentHireInvoices.value[indexToUpdate];
  updatedEquipmentHireInvoiceRow.isSelected =
    equipmentHireInvoiceRowToUpdate.isSelected;
  updatedEquipmentHireInvoiceRow.partialSelected =
    equipmentHireInvoiceRowToUpdate.partialSelected;
  for (const contract of equipmentHireInvoiceRowToUpdate.equipmentContracts) {
    const updatedContract: EquipmentContractInvoiceRow | undefined =
      updatedEquipmentHireInvoiceRow.equipmentContracts.find(
        (x: EquipmentContractInvoiceRow) =>
          x.contractId === contract.contractId,
      );
    if (!updatedContract) {
      continue;
    }
    updatedContract.isSelected = contract.isSelected;
    updatedContract.partialSelected = contract.partialSelected;
  }

  equipmentHireInvoices.value.splice(
    indexToUpdate,
    1,
    updatedEquipmentHireInvoiceRow,
  );
}

/**
 * Handles the response for user actions and updates state/notifications accordingly.
 */
function setResponseForUserAction(
  response: InvoiceRunSuccessResponse,
  options: {
    isDraftCancel?: boolean;
    isProcessingDateUpdate?: boolean;
    isDraftApproved?: boolean;
    isNewDraftRun?: boolean;
    isDummyCancel?: boolean;
    isApprovedCancel?: boolean;
  } = {},
): void {
  if (!response || !response.success) {
    isLoadingUserActionRequest.value = false;
    showNotification(GENERIC_ERROR_MESSAGE);
    getDraftRun();
    return;
  }
  const {
    isDraftCancel = false,
    isProcessingDateUpdate = false,
    isDraftApproved = false,
    isNewDraftRun = false,
    isDummyCancel = false,
    isApprovedCancel = false,
  } = options;

  if (isDraftCancel) {
    equipmentHireInvoiceStore.setInvoiceRunStatus(null);
    equipmentHireInvoiceStore.setProcessingDate(null);
    equipmentHireInvoiceStore.setWeekEndingDate(null);
    equipmentHireInvoiceStore.setBillingCycleIds(null);
  }

  const responseIsForDivisionUsers =
    response.userName !== sessionManager.getUserName();

  if (responseIsForDivisionUsers) {
    if (isNewDraftRun) {
      showNotification('New draft available.', {
        title: 'Draft Run',
        type: HealthLevel.INFO,
      });
    }
    if (isDraftApproved) {
      isLoadingUserActionRequest.value = false;
      showNotification('New dummy run available.', {
        title: 'Dummy Run',
        type: HealthLevel.INFO,
      });
    }
    if (isProcessingDateUpdate) {
      showNotification('Processing Date Updated.', {
        title: 'Draft Run',
        type: HealthLevel.INFO,
      });
    }
    if (isDraftCancel) {
      showNotification('Draft cancelled.', {
        title: 'Draft Run',
        type: HealthLevel.INFO,
      });
    }
    if (isDummyCancel) {
      showNotification('Dummy cancelled.', {
        title: 'Dummy Run',
        type: HealthLevel.INFO,
      });
    }
    if (isApprovedCancel) {
      showNotification('Approved cancelled.', {
        title: 'Approved Run',
        type: HealthLevel.INFO,
      });
    }
  }
  if (!responseIsForDivisionUsers) {
    if (isDraftApproved) {
      showNotification('Successfully Approved.', {
        title: 'Draft Run',
        type: HealthLevel.SUCCESS,
      });
      emit('setCurrentView', InvoiceNavigation.DUMMY);
      return;
    }
    if (isProcessingDateUpdate) {
      showNotification('Processing Date Updated.', {
        title: 'Draft Run',
        type: HealthLevel.SUCCESS,
      });
    }
    if (isDraftCancel) {
      showNotification('Successfully Cancelled.', {
        title: 'Draft Run',
        type: HealthLevel.SUCCESS,
      });
      emit('setCurrentView', InvoiceNavigation.READY_FOR_INVOICING);
    }
  }
  isLoadingUserActionRequest.value = false;
  getDraftRun();
}

/**
 * Returns true if the user is authorised.
 */
function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}

/** Handles the response from generating an RCTI Draft run. */
function handleDraftRunResponse(
  payload: InvoiceRunSuccessResponse | null,
): void {
  if (payload) {
    setResponseForUserAction(payload, { isNewDraftRun: true });
  }
}
/**
 * Mitt callback for cancelling a Client Invoice Draft run. Used in mitt listener.
 */
function handleDraftCancel(payload: InvoiceRunSuccessResponse | null): void {
  if (payload) {
    setResponseForUserAction(payload, { isDraftCancel: true });
  }
}
/**
 * Mitt callback for updating the processing date of a Client Invoice Draft.
 */
function handleDraftProcessingDate(
  payload: InvoiceRunSuccessResponse | null,
): void {
  if (payload) {
    setResponseForUserAction(payload, { isProcessingDateUpdate: true });
  }
}
/** Mitt callback for approving a DRAFT run (transition to DUMMY) */
function handleDraftApproval(payload: InvoiceRunSuccessResponse | null): void {
  if (payload) {
    setResponseForUserAction(payload, { isDraftApproved: true });
  }
}
/** Mitt callback for cancelling a DUMMY run (transition to DRAFT) */
function handleDummyCancel(payload: InvoiceRunSuccessResponse | null): void {
  if (payload) {
    setResponseForUserAction(payload, { isDummyCancel: true });
  }
}
/** Mitt callback for cancelling an APPROVED run (transition to DRAFT) */
function handleApprovedCancel(payload: InvoiceRunSuccessResponse | null): void {
  if (payload) {
    setResponseForUserAction(payload, { isApprovedCancel: true });
  }
}

useMittListener('equipmentHireDraftRunResponse', handleDraftRunResponse);
useMittListener('approveEquipmentHireDraftRunResponse', handleDraftApproval);
useMittListener('cancelEquipmentHireDummyRunResponse', handleDummyCancel);
useMittListener('cancelEquipmentHireLiveRunResponse', handleApprovedCancel);
useMittListener('cancelEquipmentHireDraftRunResponse', handleDraftCancel);
useMittListener(
  'updateEquipmentHireDraftProcessingDateResponse',
  handleDraftProcessingDate,
);
useMittListener('adjustmentLedgerContractResponse', updateEquipmentHireInvoice);

onMounted(() => {
  getDraftRun();
});
</script>
