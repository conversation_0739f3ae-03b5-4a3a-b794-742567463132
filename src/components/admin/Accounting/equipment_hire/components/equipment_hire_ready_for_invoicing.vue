<template>
  <v-layout wrap>
    <v-flex md12>
      <v-layout align-center class="accounting-data-table__actions-bar">
        <v-layout align-center>
          <div>
            <v-menu right ref="optionsMenu">
              <template v-slot:activator="{ on: menu }">
                <v-tooltip bottom>
                  <template v-slot:activator="{ on: tooltip }">
                    <v-btn
                      flat
                      icon
                      v-on="{ ...tooltip, ...menu }"
                      class="ma-0"
                      :disabled="isLoading"
                    >
                      <v-icon size="16">fas fa-ellipsis-v </v-icon>
                    </v-btn>
                  </template>
                  <span>View Options</span>
                </v-tooltip>
              </template>
              <v-list dense class="v-list-custom">
                <EquipmentHireSummaryReport
                  v-if="weekEndingDate"
                  @closeMenuOptions="closeMenuOptions"
                  :invoices="equipmentHireInvoices"
                  :isDisabled="false"
                  :weekEndingDate="weekEndingDate"
                />
                <v-divider v-if="!invoiceRunStatus"></v-divider>
                <v-list-tile
                  v-if="!invoiceRunStatus"
                  @click="editRunParameters"
                >
                  <v-list-tile-action>
                    <v-icon size="18" class="pr-2">fal fa-sliders-h</v-icon>
                  </v-list-tile-action>
                  <v-list-tile-content>
                    <v-list-tile-title class="pr-2 ma-0">
                      <span
                        class="pr-2"
                        style="text-transform: none; font-weight: 400"
                        >Edit Run Options</span
                      >
                    </v-list-tile-title>
                  </v-list-tile-content>
                </v-list-tile>
              </v-list>
            </v-menu>
          </div>
          <v-layout align-center>
            <InvoiceRunParametersSummary
              :parameters="{
                billingCycleIds: billingCycleIds,
                weekEndingDate: weekEndingDate,
                processingDate: processingDate,
                includeAdjustments: includeAdjustments,
              }"
            ></InvoiceRunParametersSummary>
            <div v-if="invoiceRunStatus">
              <CurrentInvoiceStatus :invoiceRunStatus="invoiceRunStatus" />
            </div>
            <v-spacer />
            <v-flex md2>
              <v-btn
                v-if="!invoiceRunStatus"
                block
                color="info"
                :disabled="
                  numberOfSelectedReadyForDraft === 0 || !isAuthorised()
                "
                @click="setSelectedToDraft"
                :loading="isLoading"
              >
                Create Draft (<span>{{ numberOfSelectedReadyForDraft }}</span
                >)
                <template v-slot:loader>
                  <span>Loading...</span>
                </template>
              </v-btn>

              <v-btn
                v-if="invoiceRunStatus === 'DRAFT'"
                block
                color="info"
                :disabled="
                  numberOfSelectedReadyForDraft === 0 || !isAuthorised()
                "
                :loading="isLoading"
                @click="addSelectedToDraft"
              >
                Add To Draft (<span>{{ numberOfSelectedReadyForDraft }}</span
                >)
                <template v-slot:loader>
                  <span>Loading...</span>
                </template>
              </v-btn>
            </v-flex>
          </v-layout>
        </v-layout>
      </v-layout>
    </v-flex>

    <v-flex md12>
      <EquipmentHireTable
        ref="equipmentHireTable"
        :equipmentHireInvoices="equipmentHireInvoices"
        :isLoading="isLoading"
        :invoiceType="invoiceType"
      />
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import CurrentInvoiceStatus from '@/components/admin/Accounting/components/current_invoice_status/index.vue';
import EquipmentHireSummaryReport from '@/components/admin/Accounting/components/equipment_hire_summary_report_dialog.vue';
import InvoiceRunParametersSummary from '@/components/admin/Accounting/components/invoice_run_parameters_summary.vue';
import EquipmentHireTable from '@/components/admin/Accounting/equipment_hire/components/equipment_hire_table.vue';
import { getEquipmentHireTableData } from '@/helpers/AccountingHelpers/AccountingHelpers';
import { returnEndOfDayFromEpoch } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import {
  EquipmentContractInvoiceRow,
  EquipmentHireAccountingTable,
  EquipmentHireOwnerInvoiceRow,
} from '@/interface-models/Accounting/EquipmentHireAccountingTable';
import { GenerateEquipmentHireDraftRequest } from '@/interface-models/Accounting/GenerateEquipmentHireDraftRequest';
import { InvoiceNavigation } from '@/interface-models/Accounting/InvoiceNavigation';
import { InvoiceRunSuccessResponse } from '@/interface-models/Accounting/InvoiceRunSuccessResponse';
import { InvoiceStatus } from '@/interface-models/Accounting/InvoiceStatus';
import { InvoiceDatesRequest } from '@/interface-models/Accounting/invoiceDatesRequest';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { useEquipmentHireInvoiceStore } from '@/store/modules/EquipmentHireInvoiceStore';
import { sessionManager } from '@/store/session/SessionState';
import { useMittListener } from '@/utils/useMittListener';
import { computed, ComputedRef, onMounted, ref, Ref } from 'vue';

const emit = defineEmits<{
  (event: 'setCurrentView', payload: InvoiceNavigation): void;
  (event: 'editRunParameters'): void;
}>();

const props = withDefaults(
  defineProps<{
    accountingIndexLoading: boolean;
  }>(),
  {
    accountingIndexLoading: false,
  },
);

const equipmentHireInvoiceStore = useEquipmentHireInvoiceStore();

const equipmentHireInvoices: Ref<EquipmentHireOwnerInvoiceRow[]> = ref([]);
const invoiceType: Ref<InvoiceNavigation> = ref(
  InvoiceNavigation.READY_FOR_INVOICING,
);

const weekEndingDate: Ref<number | null> = ref(null);
const processingDate: Ref<number | null> = ref(null);
const billingCycleIds: Ref<number[] | null> = ref(null);

const isLoadingInvoices: Ref<boolean> = ref(true);
const isLoadingUserActionRequest: Ref<boolean> = ref(false);
const includeAdjustments: Ref<boolean> = ref(false);

/**
 * Returns overall loading state for component.
 */
const isLoading: ComputedRef<boolean> = computed(() => {
  return props.accountingIndexLoading || isLoadingInvoices.value;
});

/**
 * Returns the current stage our invoice run is at.
 */
const invoiceRunStatus: ComputedRef<InvoiceStatus | null> = computed(() => {
  return equipmentHireInvoiceStore.invoiceRunStatus;
});

/**
 * Opens the dialog to update the run parameters. Emits to parent to open the StartInvoiceRunCard.
 */
function editRunParameters(): void {
  emit('editRunParameters');
}

/**
 * Sets the initial week ending date and related state from the store.
 */
function setInitialWeekEndingDate(): void {
  includeAdjustments.value =
    currentRunIncludeAdjustments.value === null
      ? true
      : currentRunIncludeAdjustments.value;

  weekEndingDate.value = equipmentHireInvoiceStore.weekEndingDate;
  processingDate.value = equipmentHireInvoiceStore.processingDate;
  billingCycleIds.value = equipmentHireInvoiceStore.billingCycleIds;
  setWeekEndingEpoch(weekEndingDate.value, processingDate.value);
}

/**
 * Because the select tile in our menu is a component it does not automatically close when selected.
 * We must close the menu manually. Method call Emitted from components that act as a tile in our menu.
 */
function closeMenuOptions(): void {
  // If using template refs, you can use a ref here if needed
  // Example: if (optionsMenu.value) { optionsMenu.value.isActive = false; }
}

/**
 * Returns the total number of selected contracts/assets that will be sent to draft.
 * Displayed in "CREATE DRAFT" button.
 */
const numberOfSelectedReadyForDraft: ComputedRef<number> = computed(() => {
  let selectedContractCount: number = 0;
  for (const invoice of equipmentHireInvoices.value) {
    const selectedContracts = invoice.equipmentContracts.filter(
      (x: EquipmentContractInvoiceRow) => x.isSelected,
    );
    selectedContractCount += selectedContracts.length;
  }
  return selectedContractCount;
});

/**
 * Sets the users selected weekending date and makes a request to get jobs
 * that are ready for invoicing based on the selected date.
 */
async function setWeekEndingEpoch(
  weekEnding: number | null,
  processing: number | null,
): Promise<void> {
  if (weekEnding === null || !billingCycleIds.value) {
    console.error(
      `Week ending date or billing cycle ids are null. weekEndingDate: ${weekEnding}, billingCycleIds: ${billingCycleIds.value}`,
    );
    return;
  }
  weekEndingDate.value = returnEndOfDayFromEpoch(weekEnding);
  processingDate.value = processing
    ? returnEndOfDayFromEpoch(processing)
    : returnEndOfDayFromEpoch(weekEnding);

  if (!weekEndingDate.value || !processingDate.value) {
    return;
  }

  isLoadingInvoices.value = true;
  const getReadyForInvoiceRequest: InvoiceDatesRequest = {
    weekEndingDate: weekEndingDate.value,
    processingDate: processingDate.value,
    billingCycleIds: billingCycleIds.value,
    includeAdjustments: includeAdjustments.value,
  };

  const res = await equipmentHireInvoiceStore.getReadyForInvoicingJobs(
    getReadyForInvoiceRequest,
  );
  setReadyForInvoicingTableData(res ?? []);
}

/**
 * Returns all the users selected asset/hire contracts.
 */
const selectedHireContractIds: ComputedRef<string[]> = computed(() => {
  const selected: string[] = [];
  for (const invoice of equipmentHireInvoices.value) {
    for (const fleetAsset of invoice.equipmentContracts) {
      if (fleetAsset.isSelected) {
        selected.push(fleetAsset.contractId);
      }
    }
  }
  return selected;
});

/**
 * Request to generate a draft run.
 */
function setSelectedToDraft(): void {
  if (
    weekEndingDate.value === null ||
    processingDate.value === null ||
    !billingCycleIds.value
  ) {
    console.error(
      `Week ending date, processing date or billing cycle ids are null. weekEndingDate: ${weekEndingDate.value}, processingDate: ${processingDate.value}, billingCycleIds: ${billingCycleIds.value}`,
    );
    return;
  }
  isLoadingUserActionRequest.value = true;
  const req: GenerateEquipmentHireDraftRequest = {
    weekEndingDate: weekEndingDate.value,
    processingDate: processingDate.value,
    billingCycleIds: billingCycleIds.value,
    contractIds: selectedHireContractIds.value,
    includeAdjustments: includeAdjustments.value,
  };
  equipmentHireInvoiceStore.generateEquipmentHireDraftRun(req);
}

/**
 * Makes request to add selected jobs to the current existing draft.
 */
function addSelectedToDraft(): void {
  isLoadingUserActionRequest.value = true;
  equipmentHireInvoiceStore.addHireContractsToDraftRun(
    selectedHireContractIds.value,
  );
}

/**
 * Returns the includeAdjustments flag for the current run.
 */
const currentRunIncludeAdjustments: ComputedRef<boolean | null> = computed(
  () => {
    return equipmentHireInvoiceStore.includeAdjustments;
  },
);

/**
 * Sets the table data for ready for invoicing jobs.
 */
function setReadyForInvoicingTableData(
  response: EquipmentHireAccountingTable[],
): void {
  if (!processingDate.value) {
    return;
  }
  equipmentHireInvoices.value = getEquipmentHireTableData(
    response,
    processingDate.value,
  );
  isLoadingInvoices.value = false;
}

/**
 * Handles the response for user actions and updates state/notifications accordingly.
 */
function setResponseForUserAction(
  response: InvoiceRunSuccessResponse,
  isNewDraftRun: boolean,
  isDraftUpdate: boolean,
): void {
  if (!response || !response.success) {
    isLoadingUserActionRequest.value = false;
    showNotification(GENERIC_ERROR_MESSAGE);
    setInitialWeekEndingDate();
    return;
  }

  const responseIsForDivisionUsers =
    response.userName !== sessionManager.getUserName();

  if (responseIsForDivisionUsers) {
    if (isNewDraftRun) {
      showNotification('A new draft is now available.', {
        title: 'Draft Run',
        type: HealthLevel.INFO,
      });
    }
    if (isDraftUpdate) {
      showNotification('Draft Updated.', {
        type: HealthLevel.INFO,
      });
    }
    setWeekEndingEpoch(response.weekEndingDate, response.processingDate);
    return;
  }

  if (!responseIsForDivisionUsers) {
    if (isNewDraftRun) {
      showNotification('Successfully created.', {
        title: 'Draft Run',
        type: HealthLevel.SUCCESS,
      });
    }
    if (isDraftUpdate) {
      showNotification('Successfully updated.', {
        title: 'Draft Run',
        type: HealthLevel.SUCCESS,
      });
    }
    emit('setCurrentView', InvoiceNavigation.DRAFT);
  }
  isLoadingUserActionRequest.value = false;
}

/**
 * Returns true if the user is authorised.
 */
function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}

/** Handles the response from generating an RCTI Draft run. */
function handleDraftRunResponse(
  payload: InvoiceRunSuccessResponse | null,
): void {
  if (payload) {
    setResponseForUserAction(payload, true, false);
  }
}

/**
 * Handles the response from adding jobs to an existing dummy run.
 */
function handleDraftAddContract(
  payload: InvoiceRunSuccessResponse | null,
): void {
  if (payload) {
    setResponseForUserAction(payload, false, true);
  }
}

useMittListener('equipmentHireDraftRunResponse', handleDraftRunResponse);
useMittListener(
  'cancelEquipmentHireDraftRunResponse',
  setInitialWeekEndingDate,
);
useMittListener(
  'equipmentHireDraftAddContractResponse',
  handleDraftAddContract,
);
useMittListener(
  'equipmentHireDraftRemoveContractResponse',
  handleDraftAddContract,
);

// Lifecycle hooks
onMounted(() => {
  setInitialWeekEndingDate();
});
</script>
