<template>
  <v-layout wrap>
    <v-flex md12>
      <v-layout align-center class="accounting-data-table__actions-bar">
        <v-menu right ref="optionsMenu">
          <template v-slot:activator="{ on: menu }">
            <v-tooltip bottom>
              <template v-slot:activator="{ on: tooltip }">
                <v-btn
                  flat
                  icon
                  v-on="{ ...tooltip, ...menu }"
                  class="ma-0"
                  :disabled="invoiceRunStatus !== 'DUMMY' || isLoading"
                >
                  <v-icon size="16">fas fa-ellipsis-v </v-icon>
                </v-btn>
              </template>
              <span>View Options</span>
            </v-tooltip>
          </template>
          <v-list dense class="v-list-custom">
            <InvoiceEventHistoryDialog
              :invoiceEventHistory="invoiceEventHistory"
              :invoiceRunStatus="invoiceRunStatus"
            />

            <CancelRunConfirmationDialog
              v-if="invoiceRunStatus === 'DUMMY'"
              :runType="'Dummy'"
              @closeMenuOptions="closeMenuOptions"
              @cancelRun="cancelDummyRun"
            />
          </v-list>
        </v-menu>

        <v-layout v-if="invoiceRunStatus === 'DUMMY'">
          <InvoiceRunParametersSummary
            :parameters="{
              billingCycleIds: billingCycleIds,
              weekEndingDate: weekEndingDate,
              processingDate: processingDate,
              includeAdjustments: equipmentHireInvoiceStore.includeAdjustments,
            }"
          ></InvoiceRunParametersSummary>
        </v-layout>

        <v-layout v-if="invoiceRunStatus !== 'DUMMY'">
          <CurrentInvoiceStatus :invoiceRunStatus="invoiceRunStatus" />
        </v-layout>
        <v-spacer />

        <v-flex md2 style="height: 48px">
          <ConfirmationDialog
            v-if="!isLoading"
            buttonText="Approve for Live"
            message="I declare that:"
            title="Confirm Approval of Dummy Invoices"
            @confirm="approveDummyRun"
            :isSmallButton="false"
            :buttonDisabled="
              !isAuthorised() || invoiceRunStatus !== 'DUMMY' || isLoading
            "
            :isOutlineButton="false"
            :isBlockButton="true"
            :buttonColor="'info'"
            :confirmationButtonText="'Approve'"
            :isCheckboxList="true"
            :isLoading="isLoading"
            :checkboxLabelList="approvalConfirmationCheckBoxList"
            :dialogIsActive="true"
          >
          </ConfirmationDialog>
        </v-flex>
      </v-layout>
    </v-flex>

    <v-flex md12 class="reviewed-jobs-container">
      <EquipmentHireTable
        ref="equipmentHireTable"
        :equipmentHireInvoices="equipmentHireInvoices"
        :isLoading="isLoading"
        :invoiceType="invoiceType"
      />
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import CancelRunConfirmationDialog from '@/components/admin/Accounting/components/cancel_run_confirmation_dialog.vue';
import CurrentInvoiceStatus from '@/components/admin/Accounting/components/current_invoice_status/index.vue';
import InvoiceEventHistoryDialog from '@/components/admin/Accounting/components/invoice_event_history_dialog.vue';
import InvoiceRunParametersSummary from '@/components/admin/Accounting/components/invoice_run_parameters_summary.vue';
import EquipmentHireTable from '@/components/admin/Accounting/equipment_hire/components/equipment_hire_table.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import { getEquipmentHireTableData } from '@/helpers/AccountingHelpers/AccountingHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import {
  EquipmentHireAccountingTable,
  EquipmentHireOwnerInvoiceRow,
} from '@/interface-models/Accounting/EquipmentHireAccountingTable';
import { EquipmentHireRunAccountingTable } from '@/interface-models/Accounting/EquipmentHireRunAccountingTable';
import { InvoiceEvent } from '@/interface-models/Accounting/InvoiceEvent';
import { InvoiceNavigation } from '@/interface-models/Accounting/InvoiceNavigation';
import { InvoiceRunSuccessResponse } from '@/interface-models/Accounting/InvoiceRunSuccessResponse';
import { InvoiceStatus } from '@/interface-models/Accounting/InvoiceStatus';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { useEquipmentHireInvoiceStore } from '@/store/modules/EquipmentHireInvoiceStore';
import { sessionManager } from '@/store/session/SessionState';
import { useMittListener } from '@/utils/useMittListener';
import { computed, ComputedRef, onMounted, ref, Ref } from 'vue';

const emit = defineEmits<{
  (event: 'setCurrentView', payload: InvoiceNavigation): void;
}>();

const props = withDefaults(
  defineProps<{
    accountingIndexLoading?: boolean;
  }>(),
  {
    accountingIndexLoading: false,
  },
);

const equipmentHireInvoiceStore = useEquipmentHireInvoiceStore();

const weekEndingDate: Ref<number | null> = ref(null);
const processingDate: Ref<number | null> = ref(null);
const billingCycleIds: Ref<number[] | null> = ref(null);

const equipmentHireInvoices: Ref<EquipmentHireOwnerInvoiceRow[]> = ref([]);
const invoiceEventHistory: Ref<InvoiceEvent[]> = ref([]);
const invoiceType: Ref<InvoiceNavigation> = ref(InvoiceNavigation.DUMMY);
const isLoadingDummy: Ref<boolean> = ref(false);
const isLoadingUserActionRequest: Ref<boolean> = ref(false);

/**
 * Returns the list of approval confirmation checkboxes for the dialog.
 */
const approvalConfirmationCheckBoxList: ComputedRef<string[]> = computed(() => {
  const checkboxList: string[] = [
    'I have reviewed the Remittance Summary Report',
    'I have reviewed the Fleet Asset Owner Pays to the extent that I am satisfied that they are accurate and complete',
  ];
  if (
    weekEndingDate.value !== null &&
    processingDate.value !== null &&
    weekEndingDate.value > processingDate.value
  ) {
    checkboxList.push(
      'I understand that the processing date is before the week ending date',
    );
  }
  checkboxList.push(
    'I understand that by Approving these RCTIs for Live run, they will not be editable in the future',
  );
  return checkboxList;
});

/**
 * Returns the current stage our invoice run is at.
 */
const invoiceRunStatus: ComputedRef<InvoiceStatus | null> = computed(() => {
  return equipmentHireInvoiceStore.invoiceRunStatus;
});

/**
 * Returns overall loading state for component.
 */
const isLoading: ComputedRef<boolean> = computed(() => {
  return (
    props.accountingIndexLoading ||
    isLoadingDummy.value ||
    isLoadingUserActionRequest.value
  );
});

/**
 * Sets the table data for equipment hire invoices.
 */
function setTableData(response: EquipmentHireAccountingTable[]): void {
  if (!processingDate.value && response.length > 0) {
    return;
  }
  equipmentHireInvoices.value = getEquipmentHireTableData(
    response,
    processingDate.value,
  );
  isLoadingDummy.value = false;
}

/**
 * Because the select tile in our menu is a component it does not automatically close when selected.
 * We must close the menu manually. Method call Emitted from components that act as a tile in our menu.
 */
function closeMenuOptions(): void {
  // If using template refs, you can use a ref here if needed
  // Example: if (optionsMenu.value) { optionsMenu.value.isActive = false; }
}

/**
 * Approves the dummy run.
 */
function approveDummyRun(): void {
  isLoadingUserActionRequest.value = true;
  equipmentHireInvoiceStore.approveDummyRun();
}

/**
 * Initial request to get the current dummy run.
 */
async function getDummyRun(): Promise<void> {
  isLoadingDummy.value = true;
  setDummyRunResponse(
    await equipmentHireInvoiceStore.getEquipmentHireInvoiceRun(
      InvoiceStatus.DUMMY,
    ),
  );
}

/**
 * Cancels the dummy run.
 */
function cancelDummyRun(): void {
  isLoadingUserActionRequest.value = true;
  equipmentHireInvoiceStore.cancelDummyRun();
}

/**
 * Sets the dummy run response and updates relevant state.
 */
function setDummyRunResponse(
  response: EquipmentHireRunAccountingTable | null,
): void {
  if (!response || response.type !== InvoiceStatus.DUMMY) {
    return;
  }
  weekEndingDate.value = response.weekEndingDate;
  processingDate.value = response.processingDate;
  billingCycleIds.value = response.billingCycleIds;
  invoiceEventHistory.value = response.eventList;
  setTableData(response.equipmentHireAccountingTables);
}

/**
 * Handles the response for user actions and updates state/notifications accordingly.
 */
function setResponseForUserAction(
  response: InvoiceRunSuccessResponse,
  showDummyApprovedSuccess: boolean,
  showDummyCancelledSuccess: boolean,
  isDraftApproved: boolean,
): void {
  if (!response || !response.success) {
    isLoadingUserActionRequest.value = false;
    showNotification(GENERIC_ERROR_MESSAGE);
    getDummyRun();
    return;
  }

  if (showDummyCancelledSuccess) {
    equipmentHireInvoiceStore.setInvoiceRunStatus(InvoiceStatus.DRAFT);
    equipmentHireInvoiceStore.setProcessingDate(response.processingDate);
    equipmentHireInvoiceStore.setWeekEndingDate(response.weekEndingDate);
    equipmentHireInvoiceStore.setBillingCycleIds(response.billingCycleIds);
  }

  const responseIsForDivisionUsers =
    response.userName !== sessionManager.getUserName();

  if (responseIsForDivisionUsers) {
    if (showDummyApprovedSuccess) {
      showNotification('Dummy run approved.', {
        title: 'Dummy Run',
        type: HealthLevel.INFO,
      });
    }
    if (showDummyCancelledSuccess) {
      showNotification('Dummy Cancelled.', {
        title: 'Dummy Run',
        type: HealthLevel.INFO,
      });
    }
    if (isDraftApproved) {
      showNotification('New Dummy Run Available.', {
        title: 'Dummy Run',
        type: HealthLevel.INFO,
      });
    }
  }
  if (!responseIsForDivisionUsers) {
    if (showDummyApprovedSuccess) {
      showNotification('Successfully Approved.', {
        title: 'Dummy Run',
        type: HealthLevel.SUCCESS,
      });
      emit('setCurrentView', InvoiceNavigation.APPROVED);
      return;
    }
    if (showDummyCancelledSuccess) {
      showNotification('Successfully Cancelled.', {
        title: 'Dummy Run',
        type: HealthLevel.SUCCESS,
      });
    }
  }
  isLoadingUserActionRequest.value = false;
  getDummyRun();
}

/**
 * Returns true if the user is authorised.
 */
function isAuthorised(): boolean {
  return hasAdminOrTeamLeaderOrBranchManagerRole();
}

/**
 * Handles response from updating the processing date of an RCTI Draft.
 */
function handleDraftApproval(payload: InvoiceRunSuccessResponse | null): void {
  if (payload) {
    setResponseForUserAction(payload, false, false, true);
  }
}

/** Mitt callback for cancelling a DUMMY run (transition to DRAFT) */
function handleDummyCancel(payload: InvoiceRunSuccessResponse | null): void {
  if (payload) {
    setResponseForUserAction(payload, false, true, false);
  }
}

/** Mitt callback for approving a DUMMY run (transition to APPROVED) */
function handleDummyApproval(payload: InvoiceRunSuccessResponse | null): void {
  if (payload) {
    setResponseForUserAction(payload, true, false, false);
  }
}

useMittListener('approveEquipmentHireDraftRunResponse', handleDraftApproval);
useMittListener('cancelEquipmentHireDummyRunResponse', handleDummyCancel);
useMittListener('approveEquipmentHireDummyRunResponse', handleDummyApproval);

// Lifecycle hooks
onMounted(() => {
  getDummyRun();
});
</script>
