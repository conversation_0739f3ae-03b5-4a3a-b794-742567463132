<template>
  <v-layout class="job-list-table">
    <div class="table-header-left-action-icon-container">
      <v-checkbox
        v-if="selectionIsEnabled"
        @click.stop
        :indeterminate="invoicesPartiallySelected"
        v-model="allInvoicesCheckBox"
        hide-details
        :ripple="false"
        :disabled="equipmentHireInvoices.length === 0"
        color="info"
      ></v-checkbox>
    </div>
    <v-data-table
      ref="tableComponent"
      :headers="tableHeaders"
      :item-key="'fleetAssetOwnerId'"
      :items="equipmentHireInvoices"
      :rows-per-page-items="[15, 20]"
      hide-actions
      :loading="isLoading"
      expand
      :custom-sort="sortTable"
      class="default-table-dark equipment-hire-accounting-table gd-dark-theme"
    >
      <template v-slot:items="tableProps">
        <tr>
          <td class="inner-table__cell checkbox-type checkbox-column-cell">
            <v-checkbox
              v-if="selectionIsEnabled"
              @click.stop
              v-model="tableProps.item.isSelected"
              hide-details
              :disabled="
                tableProps.item.invalidFields.length > 0 ||
                tableProps.item.equipmentContracts.length ===
                  tableProps.item.invalidContractIds.length ||
                isLoading
              "
              :indeterminate="tableProps.item.partialSelected"
              @change="
                setAllContractsSelectionOnSingleOwnerInvoice(
                  $event,
                  tableProps.item.fleetAssetOwnerId,
                )
              "
              :ripple="false"
              color="info"
            ></v-checkbox>

            <v-icon
              size="18"
              v-if="tableProps.item.status === 'SENT'"
              color="success"
            >
              fas fa-check-circle</v-icon
            >
            <v-tooltip
              bottom
              v-if="
                invoiceType === 'APPROVED' &&
                invoiceRunStatus === 'SENT' &&
                tableProps.item.status === 'FAIL'
              "
            >
              <template v-slot:activator="{ on: tooltip }">
                <div v-on="{ ...tooltip }">
                  <ConfirmationDialog
                    buttonText="Email"
                    :message="
                      'Please confirm that you wish to manually email Equipment Hire ' +
                      tableProps.item.invoiceId +
                      ' (' +
                      tableProps.item.fleetAssetOwnerName +
                      ') to ' +
                      tableProps.item.toEmailIds.join(', ')
                    "
                    :title="'Confirm Manual Emailing'"
                    @confirm="
                      confirmSendingOfSingleInvoice(tableProps.item.ledgerId)
                    "
                    :buttonColor="'error'"
                    :isIcon="true"
                    :iconSize="18"
                    :buttonDisabled="
                      tableProps.item.toEmailIds.length < 1 || !isAuthorised()
                    "
                    :faIconName="'far fa-envelope'"
                    :confirmationButtonText="'Confirm'"
                    :dialogIsActive="true"
                    :noButtonMargin="true"
                  >
                  </ConfirmationDialog>
                </div>
              </template>
              <span v-if="tableProps.item.toEmailIds.length > 0"
                >Resend Email</span
              >
              <span v-if="tableProps.item.toEmailIds.length < 1"
                >No Email Address Available</span
              >
            </v-tooltip>
          </td>

          <td class="inner-table__cell string-type invoiceId-column-cell">
            <span class="pr-2">{{ tableProps.item.invoiceId }}</span>

            <InformationTooltip
              :right="true"
              :tooltipType="HealthLevel.INFO"
              v-if="invoiceType !== 'READY_FOR_INVOICING'"
            >
              <v-layout slot="content" row wrap>
                <v-flex md12>
                  <p class="mb-1" slot="content">
                    This RCTI will be emailed to:
                  </p>

                  <ul>
                    <li
                      v-for="emailAddress of tableProps.item.toEmailIds"
                      :key="emailAddress"
                    >
                      {{ emailAddress }}
                    </li>
                  </ul>
                </v-flex>
              </v-layout>
            </InformationTooltip>
          </td>
          <td class="inner-table__cell string-type ownerName-column-cell">
            <v-layout style="position: relative">
              <div class="invalid-client-exclamation">
                <InvalidFieldsToolTip
                  :ledgerType="LedgerType.EQUIPMENT_HIRE"
                  :invalidFields="tableProps.item.invalidFields"
                  v-if="tableProps.item.invalidFields.length > 0"
                />
              </div>
              {{ tableProps.item.fleetAssetOwnerName }}
            </v-layout>
          </td>

          <td class="inner-table__cell text-left tradingTermName-column-cell">
            {{ tableProps.item.tradingTermName }} /
            {{ tableProps.item.billingCycleName }}
          </td>
          <td class="inner-table__cell text-left dueDate-column-cell">
            {{ tableProps.item.dueDate }}
          </td>
          <td class="inner-table__cell text-left rate-column-cell">-</td>
          <td
            class="inner-table__cell text-left numberOfDaysCharged-column-cell"
          >
            {{ tableProps.item.numberOfDaysCharged }}
          </td>
          <td class="inner-table__cell text-right base-column-cell" id="test">
            {{ tableProps.item.baseEquipmentHireAmount.exclGst }}
          </td>

          <td class="inner-table__cell text-right travel-column-cell">
            {{ tableProps.item.excessTravelAmount.exclGst }}
          </td>
          <td class="inner-table__cell text-right insurance-column-cell">
            {{ tableProps.item.insuranceAmount.exclGst }}
          </td>

          <td class="inner-table__cell text-right administration-column-cell">
            {{ tableProps.item.administrationFeeAmount.exclGst }}
          </td>

          <td class="inner-table__cell text-right adjustment-column-cell">
            {{ tableProps.item.customAdjustmentAmount.exclGst }}
          </td>

          <td class="inner-table__cell text-right gst-column-cell">
            {{ tableProps.item.total.gst }}
          </td>

          <td class="inner-table__cell text-right total-column-cell">
            {{ displayCurrencyValue(tableProps.item.netTotal) }}
          </td>

          <td
            class="inner-table__cell text-right view-job-menu action-column-cell"
          >
            <div>
              <v-tooltip bottom class="pr-2">
                <template v-slot:activator="{ on: tooltip }">
                  <v-btn
                    v-if="
                      reportsSettings?.allowedAccessMethods?.includes(
                        ReportAccessMethodTypes.DOWNLOAD,
                      )
                    "
                    flat
                    @click.stop="
                      viewEquipmentHireDocument(
                        tableProps.item.ledgerId,
                        ReportAccessMethodTypes.DOWNLOAD,
                      )
                    "
                    icon
                    color="accent"
                    v-on="{ ...tooltip }"
                    class="ma-0"
                    :disabled="invoiceType === 'READY_FOR_INVOICING'"
                  >
                    <v-icon size="25"> downloading </v-icon>
                  </v-btn>
                </template>
                <span>Download Invoice</span>
              </v-tooltip>
              <v-tooltip bottom class="pr-1">
                <template v-slot:activator="{ on: tooltip }">
                  <v-btn
                    v-if="
                      reportsSettings?.allowedAccessMethods?.includes(
                        ReportAccessMethodTypes.EMAIL,
                      )
                    "
                    flat
                    @click.stop="
                      viewEquipmentHireDocument(
                        tableProps.item.ledgerId,
                        ReportAccessMethodTypes.EMAIL,
                      )
                    "
                    icon
                    color="orange"
                    v-on="{ ...tooltip }"
                    class="ma-0"
                    :disabled="invoiceType === 'READY_FOR_INVOICING'"
                  >
                    <v-icon size="25"> forward_to_inbox </v-icon>
                  </v-btn>
                </template>
                <span>Email Preview</span>
              </v-tooltip>
            </div>
          </td>
        </tr>
      </template>
      <template v-slot:expand="tableProps">
        <div class="inner-table__container" id="inner-table__container">
          <v-layout class="inner-table__container--row">
            <v-flex md12>
              <EquipmentHireAssetTable
                :columnWidth="columnWidth"
                :invalidContractIds="tableProps.item.invalidContractIds"
                :equipmentHireAssets="tableProps.item.equipmentContracts"
                :selectionIsEnabled="
                  selectionIsEnabled && tableProps.item.invalidFields.length < 1
                "
                :invoiceType="invoiceType"
                :ledgerId="tableProps.item.ledgerId"
                :ownerName="tableProps.item.fleetAssetOwnerName"
              />
            </v-flex>
          </v-layout>
        </div>
      </template>
      <template v-slot:no-data>
        <v-layout justify-center>
          <span v-if="isLoading">Loading...</span>
          <span v-if="!isLoading">No Invoices Available.</span>
        </v-layout>
      </template>
    </v-data-table>
  </v-layout>
</template>

<script setup lang="ts">
import InvalidFieldsToolTip from '@/components/admin/Accounting/components/invalid_fields_tool_tip.vue';
import EquipmentHireAssetTable from '@/components/admin/Accounting/equipment_hire/components/equipment_hire_asset_table.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import {
  EquipmentContractInvoiceRow,
  EquipmentHireOwnerInvoiceRow,
} from '@/interface-models/Accounting/EquipmentHireAccountingTable';
import { InvoiceNavigation } from '@/interface-models/Accounting/InvoiceNavigation';
import { InvoiceStatus } from '@/interface-models/Accounting/InvoiceStatus';
import { LedgerType } from '@/interface-models/Accounting/LedgerType';
import { DivisionReportSettings } from '@/interface-models/Company/DivisionCustomConfig/DivisionCustomConfig';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useEquipmentHireInvoiceStore } from '@/store/modules/EquipmentHireInvoiceStore';
import { computed, ComputedRef, onMounted, ref, Ref, watch } from 'vue';

const emit = defineEmits<{
  (event: 'update:isDialogOpen', payload: boolean): void;
}>();

const props = withDefaults(
  defineProps<{
    accountingIndexLoading?: boolean;
    invoiceType?: InvoiceNavigation;
    isLoading?: boolean;
    equipmentHireInvoices?: EquipmentHireOwnerInvoiceRow[];
  }>(),
  {
    accountingIndexLoading: true,
    invoiceType: undefined,
    isLoading: false,
    equipmentHireInvoices: () => [],
  },
);

const columnWidth: Ref<string> = ref('');
const equipmentHireInvoiceStore = useEquipmentHireInvoiceStore();
const company = useCompanyDetailsStore().companyDetails;
const reportsSettings: Ref<DivisionReportSettings | null> = ref(
  company?.divisions?.[0]?.customConfig?.reports ?? null,
);

const displayCurrencyValue = DisplayCurrencyValue;

const tableComponent = ref<any>(null);

/**
 * Table headers for the equipment hire table.
 */
const tableHeaders: ComputedRef<TableHeader[]> = computed(() => [
  {
    text: '',
    align: 'left',
    value: '',
    class: 'checkbox-column-header',
    sortable: false,
  },
  {
    text: 'Invoice No.',
    align: 'left',
    sortable: true,
    value: 'invoiceId',
    class: 'invoiceId-column-header',
  },
  {
    text: 'Owner',
    align: 'left',
    sortable: true,
    value: 'fleetAssetOwnerName',
    class: 'ownerName-column-header',
  },
  {
    text: 'Trading Terms',
    align: 'left',
    value: 'tradingTermName',
    sortable: false,
    class: 'tradingTermName-column-header',
  },
  {
    text: 'Due Date',
    align: 'left',
    value: 'dueDate',
    sortable: false,
    class: 'dueDate-column-header',
  },
  {
    text: 'Rate',
    align: 'left',
    value: 'dueDate',
    sortable: false,
    class: 'rate-column-header',
  },
  {
    text: 'Days Charged',
    align: 'left',
    value: 'numberOfJobs',
    sortable: false,
    class: 'numberOfDaysCharged-column-header',
  },
  {
    text: 'Base Hire',
    align: 'right',
    value: 'marginAverage',
    sortable: false,
    class: 'base-column-header',
  },
  {
    text: 'Excess Travel',
    align: 'right',
    value: 'additionalCharges',
    sortable: false,
    class: 'travel-column-header',
  },
  {
    text: 'Insurance',
    align: 'right',
    value: 'fuelSurcharge',
    sortable: false,
    class: 'insurance-column-header',
  },
  {
    text: 'Administration',
    align: 'right',
    value: 'gst',
    sortable: false,
    class: 'administration-column-header',
  },
  {
    text: 'Adjustments',
    align: 'right',
    value: 'gst',
    sortable: false,
    class: 'adjustment-column-header',
  },
  {
    text: 'GST',
    align: 'right',
    value: 'gst',
    sortable: false,
    class: 'gst-column-header',
  },
  {
    text: 'Net ($)',
    align: 'right',
    value: 'netTotal',
    sortable: true,
    class: 'total-column-header',
  },
  {
    text: 'Document',
    align: 'right',
    value: '',
    sortable: false,
    class: 'action-column-header',
  },
]);

/**
 * Requests invoice documents using the provided ledgerId.
 * @param ledgerId - the ledgerId to fetch or generate the invoice
 * @param accessMethod  The ReportAccess email/Download of the invoice to generate.
 */
function viewEquipmentHireDocument(
  ledgerId: string,
  accessMethod: ReportAccessMethodTypes,
): void {
  const request = {
    ledgerId: ledgerId,
    accessType: accessMethod,
  };
  equipmentHireInvoiceStore.generateEquipmentHireInvoice(request);
}

/**
 * Returns the current stage our invoice run is at.
 */
const invoiceRunStatus: ComputedRef<InvoiceStatus | null> = computed(() => {
  return equipmentHireInvoiceStore.invoiceRunStatus;
});

/**
 * Returns whether selection is enabled for checkboxes.
 */
const selectionIsEnabled: ComputedRef<boolean> = computed(() => {
  if (
    invoiceRunStatus.value === InvoiceStatus.DUMMY ||
    invoiceRunStatus.value === InvoiceStatus.APPROVED ||
    props.invoiceType === InvoiceNavigation.DUMMY ||
    props.invoiceType === InvoiceNavigation.APPROVED
  ) {
    return false;
  }
  if (
    props.invoiceType === InvoiceNavigation.READY_FOR_INVOICING ||
    props.invoiceType === InvoiceNavigation.DRAFT
  ) {
    return true;
  }
  return false;
});

/**
 * Main v-model controller for our "Select all invoices" checkbox.
 */
const allInvoicesCheckBox = computed<boolean>({
  get(): boolean {
    for (const invoice of props.equipmentHireInvoices) {
      if (!invoice.isSelected) {
        return false;
      }
      for (const fleetAsset of invoice.equipmentContracts) {
        if (!fleetAsset.isSelected) {
          return false;
        }
      }
    }
    return props.equipmentHireInvoices.length > 0;
  },
  set(value: boolean): void {
    for (const invoice of props.equipmentHireInvoices) {
      const invoiceIsSelected =
        invoice.invalidFields.length > 0 ? false : value;
      invoice.isSelected = invoiceIsSelected;
      invoice.partialSelected =
        invoice.invalidContractIds !== null &&
        invoice.invalidContractIds.length > 0 &&
        value
          ? true
          : false;
      for (const contract of invoice.equipmentContracts) {
        contract.isSelected = invoice.invalidContractIds.includes(
          contract.contractId,
        )
          ? false
          : invoiceIsSelected;
      }
    }
  },
});

/**
 * Returns the indeterminate state for the overall invoice selection checkbox.
 */
const invoicesPartiallySelected: ComputedRef<boolean> = computed(() => {
  const selections: boolean[] = [];
  for (const invoice of props.equipmentHireInvoices) {
    for (const fleetAsset of invoice.equipmentContracts) {
      selections.push(fleetAsset.isSelected);
    }
  }
  if (selections.includes(true) && selections.includes(false)) {
    return true;
  }
  if (!selections.includes(true) && selections.includes(false)) {
    return false;
  }
  return false;
});

/**
 * Updates clientInvoices data with selections. Similar to "setJobsCheckBoxForClient".
 * The reason we have this is because emits only allow one arg. Where as the vuetify checkbox requires $event and clientId in this component
 */
function setAllContractsSelectionOnSingleOwnerInvoice(
  value: boolean,
  fleetAssetOwnerId: string,
): void {
  const fleetAssetOwner: EquipmentHireOwnerInvoiceRow | undefined =
    props.equipmentHireInvoices.find(
      (x: EquipmentHireOwnerInvoiceRow) =>
        x.fleetAssetOwnerId === fleetAssetOwnerId,
    );

  if (!fleetAssetOwner) {
    return;
  }

  const invoiceIsSelected =
    fleetAssetOwner.invalidFields.length > 0 ? false : value;

  fleetAssetOwner.isSelected = invoiceIsSelected;
  fleetAssetOwner.partialSelected =
    fleetAssetOwner.invalidContractIds !== null &&
    fleetAssetOwner.invalidContractIds.length > 0 &&
    value
      ? true
      : false;

  for (const contract of fleetAssetOwner.equipmentContracts) {
    contract.isSelected = fleetAssetOwner.invalidContractIds.includes(
      contract.contractId,
    )
      ? false
      : value;
  }
  setPartialSelected();
}

/**
 * Sets indeterminate on invoice table checkboxes and expanded job list.
 */
function setPartialSelected(): void {
  for (const rctiInvoice of props.equipmentHireInvoices) {
    const numberOfContracts = rctiInvoice.equipmentContracts.length;

    const numberOfSelectedFleetAssets = rctiInvoice.equipmentContracts.filter(
      (x: EquipmentContractInvoiceRow) => x.isSelected,
    ).length;

    if (numberOfSelectedFleetAssets === numberOfContracts) {
      rctiInvoice.isSelected = true;
      rctiInvoice.partialSelected = false;
    }

    if (numberOfSelectedFleetAssets === 0) {
      rctiInvoice.isSelected = false;
      rctiInvoice.partialSelected = false;
    }

    if (
      numberOfSelectedFleetAssets > 0 &&
      numberOfSelectedFleetAssets !== numberOfContracts
    ) {
      rctiInvoice.isSelected = true;
      rctiInvoice.partialSelected = true;
    }
  }
}

/**
 * Returns the number of subcontractors (rows).
 */
const numberOfSubcontractors: ComputedRef<number> = computed(() => {
  return props.equipmentHireInvoices.length;
});

/**
 * Expands the table items. This is so the fleet assets will open under each subcontractor line item.
 */
function expandTableRows(): void {
  for (let i = 0; i < props.equipmentHireInvoices.length; i++) {
    if (tableComponent.value && tableComponent.value.expanded) {
      tableComponent.value.expanded[
        props.equipmentHireInvoices[i].fleetAssetOwnerId
      ] = true;
    }
  }
}

/**
 * Finds the element with class name base-column-cell. This element will tell us the width of our parent table column.
 * We apply this width to the expanded table column width. This is so the widths of the parent and child table columns will stay the same size on our window resize event listener.
 */
function resizeTableColumns(): void {
  if (document.getElementsByClassName('base-column-cell').length < 1) {
    return;
  }
  const style = window.getComputedStyle(
    document.getElementsByClassName('base-column-cell')[0],
  );
  columnWidth.value = style.getPropertyValue('width');
}

/**
 * Returns true if the user is authorised.
 */
function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}

/**
 * Using the provided ledgerId, sends a request to re-send the rcti
 * documents associated with that ledgerId to the original recipients.
 * @param ledgerId - the ledgerId to re-send
 */
function confirmSendingOfSingleInvoice(ledgerId: string): void {
  equipmentHireInvoiceStore.resendInvoiceForLedgerId(ledgerId);
}

/**
 * Custom sort function for the table.
 */
function sortTable(items: any, index: string, isDesc: boolean) {
  if (index !== null && isDesc !== null) {
    items.sort(
      (a: EquipmentHireOwnerInvoiceRow, b: EquipmentHireOwnerInvoiceRow) => {
        if (index === 'netTotal') {
          if (!isDesc) {
            return a.netTotal - b.netTotal;
          } else if (isDesc) {
            return b.netTotal - a.netTotal;
          }
        } else if (index === 'fleetAssetOwnerName') {
          if (!isDesc) {
            return a.fleetAssetOwnerName.localeCompare(b.fleetAssetOwnerName);
          } else {
            return b.fleetAssetOwnerName.localeCompare(a.fleetAssetOwnerName);
          }
        } else if (index === 'invoiceId') {
          if (!isDesc) {
            return a.invoiceId.localeCompare(b.invoiceId);
          } else {
            return b.invoiceId.localeCompare(a.invoiceId);
          }
        }
      },
    );
    expandTableRows();
    return items;
  } else {
    expandTableRows();
    return props.equipmentHireInvoices;
  }
}

// Watch for changes in number of subcontractors to expand and resize table
watch(numberOfSubcontractors, () => {
  expandTableRows();
  setTimeout(() => {
    resizeTableColumns();
  }, 1);
});

// Lifecycle hooks
onMounted(() => {
  window.addEventListener('resize', resizeTableColumns);
  expandTableRows();
  resizeTableColumns();
});
</script>
<style scoped lang="scss">
.job-list-table {
  position: relative;
  $background: $app-dark-primary-550;
  $accounting-data-table-background: $app-dark-primary-300;
  padding: 0px;

  .inner-table__cell {
    cursor: pointer;
    padding: 0 4px !important;

    &.integer-type {
      text-align: right;
    }

    &.string-type {
      text-align: left;
    }

    &.add-small-width {
      width: 233px;
    }

    &.checkbox-type {
      padding-left: 12px !important;
    }
  }
}

.table-header-left-action-icon-container {
  position: absolute;
  top: -3px;
  left: 13px;
  z-index: 5;
}

.number-of-jobs-selected-container {
  width: 80px;
  max-width: 80px;
  display: flex;

  .selected-jobs-selected-container {
    width: 30px;
    max-width: 30px;
    text-align: right;
    color: $warning;
  }

  .selected-of-container {
    width: 20px;
    max-width: 20px;
    text-align: center;
  }

  .total-number-of-jobs-container {
    text-align: left;
    width: 30px;
    max-width: 30px;
  }
}

.hide-non-expanded-rows-container {
  width: 100%;
  position: absolute;
  background-color: red;
  height: 20px;
  bottom: 1px;
  background-color: var(--background-color-200);
  border-top: 1px solid $border-color-alternate;
}

.v-table__overflow {
  overflow-y: auto;
  position: relative;
  border: 1px solid red !important;
  border-radius: 3px;
}

.highlight-text {
  color: #ffc400;
}

.inner-table__container {
  background-color: $app-dark-primary-500;
  padding: 4px 10px 10px 150px;
  //   padding: 0 0 0 200px;

  .dummy-invoice-icon {
    font-size: $font-size-16;
    color: rgb(173, 173, 173);
    transition: 0.15s ease;

    &:hover {
      color: white;
    }
  }
}

.invalid-client-exclamation {
  position: absolute;
  left: -25px;
}
</style>
