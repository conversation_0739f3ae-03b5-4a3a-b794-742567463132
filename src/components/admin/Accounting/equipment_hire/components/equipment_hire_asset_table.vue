<template>
  <v-layout>
    <v-data-table
      ref="tableComponent"
      :headers="tableHeaders"
      :item-key="'fleetAssetId'"
      :items="equipmentHireAssets"
      hide-actions
      :rows-per-page-items="[15, 20]"
      :loading="isLoading"
      class="default-table-dark equipment-hire-asset-accounting-table gd-dark-theme"
    >
      <template v-slot:items="tableProps">
        <tr>
          <td class="inner-table__cell checkbox-type checkbox-column-cell">
            <v-checkbox
              v-if="selectionIsEnabled"
              @click.stop
              v-model="tableProps.item.isSelected"
              hide-details
              :disabled="
                invalidContractIds.includes(tableProps.item.contractId)
              "
              :ripple="false"
              color="info"
            ></v-checkbox>
          </td>

          <td class="inner-table__cell text-left csrAssignedId-column-cell">
            <v-tooltip
              right
              v-if="invalidContractIds.includes(tableProps.item.contractId)"
            >
              <template v-slot:activator="{ on }">
                <v-icon size="13" color="warning" v-on="on"
                  >fad fa-exclamation-circle</v-icon
                >
              </template>
              <v-layout>
                <v-flex md12>
                  The following contract is invalid and cannot be added to a
                  draft. Please contact GoDesta support if assistance is
                  required.
                </v-flex>
              </v-layout>
            </v-tooltip>
            {{ tableProps.item.csrAssignedId }}
          </td>
          <td class="inner-table__cell text-left contractNumber-column-cell">
            {{ tableProps.item.contractNumber }}
          </td>
          <td class="inner-table__cell text-left documentNumber-column-cell">
            {{ tableProps.item.documentNumber }}
          </td>
          <td class="inner-table__cell text-left rate-column-cell">
            {{ tableProps.item.rate }}
          </td>
          <td
            class="inner-table__cell text-left numberOfDaysCharged-column-cell"
          >
            <v-tooltip bottom>
              <template v-slot:activator="{ on: tooltip }">
                <span v-on="{ ...tooltip }">
                  {{ tableProps.item.numberOfDaysCharged }}</span
                >
              </template>
              <span>Charged on: {{ tableProps.item.lastChargeDate }} </span>
            </v-tooltip>
          </td>
          <td
            class="inner-table__cell text-right base-column-cell"
            :style="{ width: columnWidth + '!important' }"
          >
            {{ tableProps.item.baseEquipmentHireAmount.exclGst }}
          </td>

          <td
            class="inner-table__cell text-right travel-column-cell"
            :style="{ width: columnWidth + '!important' }"
          >
            <v-tooltip bottom>
              <template v-slot:activator="{ on: tooltip }">
                <span v-on="tooltip">{{
                  tableProps.item.excessTravelAmount.exclGst
                }}</span>
              </template>
              <v-layout wrap>
                <v-flex md12>
                  <span
                    >Excess Travel:
                    {{ tableProps.item.excessTravelDistanceInKm }}</span
                  >
                </v-flex>
                <v-flex md12>
                  <span>Rate: {{ tableProps.item.excessTravelRate }}</span>
                </v-flex>
              </v-layout>
            </v-tooltip>
          </td>
          <td
            class="inner-table__cell text-right insurance-column-cell"
            :style="{ width: columnWidth + '!important' }"
          >
            {{ tableProps.item.insuranceAmount.exclGst }}
          </td>

          <td
            class="inner-table__cell text-right administration-column-cell"
            :style="{ width: columnWidth + '!important' }"
          >
            {{ tableProps.item.administrationFeeAmount.exclGst }}
          </td>

          <td
            class="inner-table__cell text-right adjustment-column-cell"
            :style="{ width: columnWidth + '!important' }"
          >
            {{ tableProps.item.customAdjustmentAmount.exclGst }}
          </td>

          <td
            class="inner-table__cell text-right gst-column-cell"
            :style="{ width: columnWidth + '!important' }"
          >
            {{ tableProps.item.total.gst }}
          </td>

          <td
            class="inner-table__cell text-right total-column-cell"
            :style="{ width: columnWidth + '!important' }"
          >
            {{ tableProps.item.total.total }}
          </td>

          <td
            class="inner-table__cell text-right view-job-menu action-column-cell"
          >
            <div style="padding-right: 13px">
              <v-menu left ref="optionsMenu">
                <template v-slot:activator="{ on: menu }">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on: tooltip }">
                      <v-btn
                        flat
                        icon
                        v-on="{ ...tooltip, ...menu }"
                        class="ma-0"
                        :disabled="isLoading"
                      >
                        <v-icon size="16">fas fa-ellipsis-v </v-icon>
                      </v-btn>
                    </template>
                    <span>View Options</span>
                  </v-tooltip>
                </template>
                <v-list dense class="v-list-custom">
                  <EquipmentHireExcessTravelDialog
                    :excessTravelDistance="
                      tableProps.item.excessTravelDistanceInKm
                    "
                    :excessTravelRate="tableProps.item.excessTravelRate"
                    :isDraft="invoiceType === 'DRAFT'"
                    :contractId="tableProps.item.contractId"
                    :fleetAssetId="tableProps.item.fleetAssetId"
                    :ledgerId="ledgerId"
                  />
                  <v-divider></v-divider>
                  <EquipmentHireCustomAdjustmentDialog
                    :ownerName="ownerName"
                    :csrAssignedId="tableProps.item.csrAssignedId"
                    :fleetAssetId="tableProps.item.fleetAssetId"
                    :contractNumber="tableProps.item.contractNumber"
                    :customAdjustments="tableProps.item.customAdjustments"
                    :baseEquipmentHire="tableProps.item.baseEquipmentHireAmount"
                    :excessTravel="tableProps.item.excessTravelAmount"
                    :insurance="tableProps.item.insuranceAmount"
                    :administrationFee="tableProps.item.administrationFeeAmount"
                    :customAdjustmentCharge="
                      tableProps.item.customAdjustmentAmount
                    "
                    :netTotal="tableProps.item.total"
                    :invoiceType="invoiceType"
                    :contractId="tableProps.item.contractId"
                    :readOnly="invoiceType !== 'DRAFT'"
                    :ledgerId="ledgerId"
                  />

                  <v-divider></v-divider>
                  <v-list-tile
                    @click="viewHireContract(tableProps.item.contractId)"
                  >
                    <v-list-tile-action>
                      <v-icon size="18"> fas fa-file-contract</v-icon>
                    </v-list-tile-action>
                    <v-list-tile-content>
                      <v-list-tile-title class="pr-2 ma-0">
                        <span style="text-transform: none; font-weight: 400"
                          >View Hire Contract</span
                        >
                      </v-list-tile-title>
                    </v-list-tile-content>
                  </v-list-tile>
                </v-list>
              </v-menu>
            </div>
          </td>
        </tr>
      </template>

      <template v-slot:no-data>
        <v-layout justify-center>
          <span v-if="isLoading">Loading...</span>
          <span v-if="!isLoading">No Invoices Available.</span>
        </v-layout>
      </template>
    </v-data-table>
  </v-layout>
</template>

<script setup lang="ts">
import EquipmentHireCustomAdjustmentDialog from '@/components/admin/Accounting/components/equipment_hire_custom_adjustment_dialog/index.vue';
import EquipmentHireExcessTravelDialog from '@/components/admin/Accounting/components/equipment_hire_excess_travel_dialog/index.vue';
import { EquipmentContractInvoiceRow } from '@/interface-models/Accounting/EquipmentHireAccountingTable';
import { InvoiceNavigation } from '@/interface-models/Accounting/InvoiceNavigation';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';

const emit = defineEmits<{
  (event: 'update:isDialogOpen', payload: boolean): void;
}>();

const props = withDefaults(
  defineProps<{
    accountingIndexLoading?: boolean;
    invoiceType?: InvoiceNavigation;
    isLoading?: boolean;
    equipmentHireAssets?: EquipmentContractInvoiceRow[];
    selectionIsEnabled?: boolean;
    columnWidth?: string;
    ownerName?: string;
    ledgerId?: string;
    invalidContractIds?: string[];
    title?: string;
    subtitle?: string;
  }>(),
  {
    accountingIndexLoading: true,
    invoiceType: undefined,
    isLoading: false,
    equipmentHireAssets: () => [],
    selectionIsEnabled: true,
    columnWidth: '',
    ownerName: '',
    ledgerId: '',
    invalidContractIds: () => [],
    title: '',
    subtitle: '',
  },
);

/**
 * Table headers for the equipment hire asset table.
 */
const tableHeaders: TableHeader[] = [
  {
    text: '',
    align: 'left',
    value: '',
    class: 'checkbox-column-header',
    sortable: false,
  },
  {
    text: 'Asset',
    align: 'left',
    sortable: true,
    value: 'csrAssignedId',
    class: 'csrAssignedId-column-header',
  },
  {
    text: 'contract Number',
    align: 'left',
    value: 'numberOfDaysCharged',
    sortable: true,
    class: 'contractNumber-column-header',
  },
  {
    text: 'Document Number',
    align: 'right',
    value: 'marginAverage',
    sortable: false,
    class: 'documentNumber-column-header',
  },
  {
    text: 'Rate',
    align: 'right',
    value: 'marginAverage',
    sortable: false,
    class: 'rate-column-header',
  },
  {
    text: 'Charge Duration',
    align: 'left',
    value: 'numberOfJobs',
    sortable: true,
    class: 'numberOfDaysCharged-column-header',
  },
  {
    text: 'Base Hire Charge',
    align: 'right',
    value: 'marginAverage',
    sortable: false,
    class: 'base-column-header',
  },
  {
    text: 'Excess Travel',
    align: 'right',
    value: 'additionalCharges',
    sortable: false,
    class: 'travel-column-header',
  },
  {
    text: 'Insurance',
    align: 'right',
    value: 'fuelSurcharge',
    sortable: false,
    class: 'insurance-column-header',
  },
  {
    text: 'Administration',
    align: 'right',
    value: 'gst',
    sortable: false,
    class: 'administration-column-header',
  },
  {
    text: 'Adjustments',
    align: 'right',
    value: 'gst',
    sortable: false,
    class: 'adjustment-column-header',
  },
  {
    text: 'GST',
    align: 'right',
    value: 'gst',
    sortable: false,
    class: 'gst-column-header',
  },
  {
    text: 'Net ($)',
    align: 'right',
    value: 'gst',
    sortable: false,
    class: 'total-column-header',
  },
  {
    text: 'Document',
    align: 'right',
    value: '',
    sortable: false,
    class: 'action-column-header',
  },
];

/**
 * Requests and displays the hire contract for the given contractId.
 * @param contractId - The contract ID to view.
 */
function viewHireContract(contractId: string): void {
  useFleetAssetStore().requestHireContractById(contractId);
}
</script>
<style scoped lang="scss">
.table-header-left-action-icon-container {
  position: absolute;
  top: -3px;
  left: 13px;
  z-index: 5;
}

.inner-table__container {
  background-color: var(--background-color-100);
  border-radius: 2px;
  border-style: solid;
  border-color: $app-dark-primary-100;
  padding: 10px 12px;

  .dummy-invoice-icon {
    font-size: $font-size-16;
    color: rgb(173, 173, 173);
    transition: 0.15s ease;

    &:hover {
      color: white;
    }
  }
}

.number-of-jobs-selected-container {
  position: relative;
  width: 80px;
  max-width: 80px;
  display: flex;

  .invalid-jobs-exclamation {
    position: absolute;
  }

  .selected-jobs-selected-container {
    width: 30px;
    max-width: 30px;
    text-align: right;
    color: $warning;
  }

  .selected-of-container {
    width: 20px;
    max-width: 20px;
    text-align: center;
  }

  .total-number-of-jobs-container {
    text-align: left;
    width: 30px;
    max-width: 30px;
  }
}

.hide-non-expanded-rows-container {
  width: 100%;
  position: absolute;
  height: 20px;
  bottom: 19px;
  background-color: var(--background-color-200);
  border-top: 1px solid $border-color-alternate;
}

.v-table__overflow {
  overflow-y: auto;
  position: relative;
  border: 1px solid $border-color !important;
  border-radius: 3px;
}
</style>
