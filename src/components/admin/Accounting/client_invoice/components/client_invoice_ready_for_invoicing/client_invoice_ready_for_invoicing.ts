import ClientInvoiceTable from '@/components/admin/Accounting/client_invoice/components/client_invoice_table/index.vue';
import ClientRctiViewTabMenu from '@/components/admin/Accounting/client_invoice/components/client_rcti_view_tab_menu.vue';
import InvalidClientDataDialog from '@/components/admin/Accounting/client_invoice/components/confirmation_dialogs/invalid_client_data_dialog.vue';
import ReleaseForEditingConfirmationDialog from '@/components/admin/Accounting/client_invoice/components/confirmation_dialogs/release_for_editing_confirmation_dialog/index.vue';
import ClientInvoiceSummaryReportDialog from '@/components/admin/Accounting/components/client_invoice_summary_report_dialog/index.vue';
import CurrentInvoiceStatus from '@/components/admin/Accounting/components/current_invoice_status/index.vue';
import InvoiceRunParametersSummary from '@/components/admin/Accounting/components/invoice_run_parameters_summary.vue';
import JobListTable from '@/components/admin/Accounting/components/job_list_table/index.vue';
import RctiInvoiceSummaryReportDialog from '@/components/admin/Accounting/components/rcti_summary_report_dialog/index.vue';
import RctiTable from '@/components/admin/Accounting/rcti/components/rcti_table/index.vue';
import {
  getClientInvoiceTableData,
  getFleetAssetOwnerRctiTableData,
} from '@/helpers/AccountingHelpers/AccountingHelpers';
import { returnEndOfDayFromEpoch } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { AccountingJobRow } from '@/interface-models/Accounting/AccountingJobRow';
import { ClientAccountingTable } from '@/interface-models/Accounting/ClientAccountingTable';
import {
  ClientInvoiceRctiFormatRequest,
  ClientInvoiceRctiFormatResponse,
} from '@/interface-models/Accounting/ClientInvoiceRCTIFormatRequestResponse';
import { ClientInvoiceRow } from '@/interface-models/Accounting/ClientInvoiceRow';
import { RctiInvoiceRow } from '@/interface-models/Accounting/FleetAssetOwnerAccountingTable';
import { GenerateClientDraftRequest } from '@/interface-models/Accounting/GenerateClientDraftRequest';
import { InvoiceNavigation } from '@/interface-models/Accounting/InvoiceNavigation';
import { InvoiceRunParameters } from '@/interface-models/Accounting/InvoiceRunParameters';
import { InvoiceRunSuccessResponse } from '@/interface-models/Accounting/InvoiceRunSuccessResponse';
import { InvoiceStatus } from '@/interface-models/Accounting/InvoiceStatus';
import { LedgerType } from '@/interface-models/Accounting/LedgerType';
import { InvoiceDatesRequest } from '@/interface-models/Accounting/invoiceDatesRequest';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { useAdjustmentChargeStore } from '@/store/modules/AdjustmentChargeStore';
import { useClientInvoiceStore } from '@/store/modules/ClientInvoiceStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  components: {
    ClientInvoiceTable,
    ReleaseForEditingConfirmationDialog,
    JobListTable,
    CurrentInvoiceStatus,
    ClientRctiViewTabMenu,
    RctiTable,
    ClientInvoiceSummaryReportDialog,
    RctiInvoiceSummaryReportDialog,
    InvalidClientDataDialog,
    InvoiceRunParametersSummary,
  },
})
export default class ClientInvoiceReadyForInvoicing
  extends Vue
  implements IUserAuthority
{
  @Prop({ default: true }) public accountingIndexLoading: boolean;
  @Prop({ default: null })
  public initialRunParameters: InvoiceRunParameters | null;

  public clientInvoiceStore = useClientInvoiceStore();

  public invoiceType: InvoiceNavigation = InvoiceNavigation.READY_FOR_INVOICING;
  public weekEndingDate: number | null = null;
  public processingDate: number | null = null;
  public billingCycleIds: number[] | null = null;

  public clientInvoices: ClientInvoiceRow[] = [];
  public selectedClientId: string | null = null;
  public isLoadingInvoices: boolean = false;
  public isLoadingUserActionRequest: boolean = false;

  public isLoadingRctiComparison: boolean = false;
  public selectedClientRctiViewTab: LedgerType = LedgerType.CLIENT_INVOICE;
  public rctiInvoices: RctiInvoiceRow[] = [];
  public showInvalidClientDataDialog: boolean = false;

  public $refs!: {
    optionsMenu: any;
    clientInvoiceTable: any;
    rctiTable: any;
  };

  // Handles the completed loaded state for this component. The accounting index makes a request to get the current invoice run status (accountingIndexLoading). This component also has the ability to switch to an all jobs view that makes a request (isLoadingAllREadyForInvoiceJobList)
  get isLoading(): boolean {
    return (
      this.accountingIndexLoading ||
      this.isLoadingInvoices ||
      this.isLoadingUserActionRequest ||
      this.isLoadingRctiComparison
    );
  }

  // returns the current stage our invoice run is at.
  get invoiceRunStatus(): InvoiceStatus | null {
    return this.clientInvoiceStore.invoiceRunStatus;
  }

  /**
   * Opens the dialog to update the run parameters. Emits to parent to open the
   * StartInvoiceRunCard
   */
  public editRunParameters() {
    this.$emit('editRunParameters');
  }

  // returns all the users selected jobIds.
  get selectedJobIds(): number[] {
    const selectedClients = this.clientInvoices.filter(
      (x: ClientInvoiceRow) => x.isSelected,
    );
    let selectedJobIds: number[] = [];
    for (const invoice of selectedClients) {
      const jobIds = invoice.jobList
        .filter((x: AccountingJobRow) => x.isSelected)
        .map((y: AccountingJobRow) => y.jobId);

      selectedJobIds = selectedJobIds.concat(jobIds);
    }
    return selectedJobIds;
  }

  public setLoading(isLoading: boolean) {
    this.isLoadingInvoices = isLoading;
  }

  // gets the selected job ids when a user has a client selected and wishes to release the selected jobs back to pricing.
  get selectedJobIdsForSelectedClient(): number[] {
    // Depending on what view we are looking at we are required to filter lists
    // at different locations. The two types of filters are against the job list
    // when the user is viewing the table by jobs and the other list is the
    // jobsList on the client invoice
    if (this.selectedClientId === null) {
      return [];
    }
    const clientInvoice: ClientInvoiceRow | undefined =
      this.clientInvoices.find(
        (x: ClientInvoiceRow) => x.clientId === this.selectedClientId,
      );
    if (!clientInvoice) {
      return [];
    }
    return clientInvoice.jobList
      .filter((x: AccountingJobRow) => x.isSelected)
      .map((y: AccountingJobRow) => y.jobId);
  }

  // sets the users selected weekending date and makes a request to get jobs that are ready for invoicing based on the selected date.
  public async setWeekEndingEpoch(
    epoch: number | null,
    processingDate: number | null,
  ): Promise<void> {
    if (!this.billingCycleIds) {
      console.error(
        `Missing required fields for request. Billing cycle ids: ${this.billingCycleIds}`,
      );
      return;
    }
    if (epoch === null) {
      return;
    }

    this.weekEndingDate = returnEndOfDayFromEpoch(epoch);
    this.processingDate = processingDate
      ? returnEndOfDayFromEpoch(processingDate)
      : returnEndOfDayFromEpoch(epoch);
    if (!this.weekEndingDate || !this.processingDate) {
      return;
    }
    this.isLoadingInvoices = true;
    const getReadyForInvoiceRequest: InvoiceDatesRequest = {
      weekEndingDate: this.weekEndingDate,
      processingDate: this.processingDate,
      billingCycleIds: this.billingCycleIds,
    };

    // Send request to get ready for invoicing jobs based off of the users
    // selected week ending date
    const result = await this.clientInvoiceStore.getReadyForInvoicingJobs(
      getReadyForInvoiceRequest,
    );
    this.setReadyForInvoicingTableData(result ?? []);
  }

  /**
   * Called from parent component to set the billing cycle ids based on user
   * selection, when initiating a new run. After setting this,
   * setWeekEndingEpoch is called to make the initial request.
   * @param billingCycleIds - The billing cycle ids to set.
   */
  public setBillingCycleIds(billingCycleIds: number[]) {
    this.billingCycleIds = billingCycleIds;
  }

  /**
   * Because the select tile in our menu is a component it does not
   * automatically close when selected. We must close the menu manually. Method
   * call Emitted from components that act as a tile in our menu.
   */
  public closeMenuOptions() {
    if (this.$refs.optionsMenu) {
      this.$refs.optionsMenu.isActive = false;
    }
  }

  /**
   * Sends request to generate a draft run.
   */
  public setSelectedToDraft() {
    if (
      this.weekEndingDate === null ||
      this.processingDate === null ||
      !this.billingCycleIds
    ) {
      console.error(
        `Week ending date or billing cycle ids are null. Week ending date: ${this.weekEndingDate} - Billing cycle ids: ${this.billingCycleIds}`,
      );
      return;
    }
    this.isLoadingUserActionRequest = true;
    const generateClientDraftRequest: GenerateClientDraftRequest = {
      weekEndingDate: this.weekEndingDate,
      jobIds: this.selectedJobIds,
      processingDate: this.processingDate,
      billingCycleIds: this.billingCycleIds,
    };
    this.clientInvoiceStore.generateClientDraftRun(generateClientDraftRequest);
  }

  // return the total number of selected clients that will be sent to draft. Displayed in "CREATE DRAFT" button
  get numberOfSelectedReadyForDraft() {
    const selected = this.clientInvoices.filter(
      (x: ClientInvoiceRow) => x.isSelected,
    );
    return selected.length;
  }

  // makes request to add selected jobs to the current existing draft
  public addSelectedToDraft() {
    this.isLoadingUserActionRequest = true;
    this.clientInvoiceStore.addJobsToDraftRun(this.selectedJobIds);
  }

  // resets the user edits back to the table datas default state.
  public resetClientInvoiceSelections() {
    for (const clientInvoice of this.clientInvoices) {
      clientInvoice.isSelected = false;
      clientInvoice.partialSelected = false;
      for (const job of clientInvoice.jobList) {
        job.isSelected = false;
      }
    }
  }

  // converts our ClientAccountingTable[] into ClientInvoiceRow[]
  public setReadyForInvoicingTableData(
    response: ClientAccountingTable[],
  ): void {
    this.clientInvoices = getClientInvoiceTableData(response);
    this.isLoadingInvoices = false;
    this.checkInvalidClientData();
  }

  // opens our invalid client data dialog if any issues are found in the data
  public checkInvalidClientData(): void {
    for (const invoice of this.clientInvoices) {
      if (
        invoice.invalidFields.length > 0 ||
        invoice.invalidJobIds.length > 0
      ) {
        this.showInvalidClientDataDialog = true;
        return;
      }
    }
  }

  public setInvalidClientDataDialog(value: boolean) {
    this.showInvalidClientDataDialog = value;
  }

  /**
   * Handles responses for the users action. Mostly sets required fields so they
   * are updated and also shows notifications based on the user who made the
   * request and all other division users.
   */
  public setResponseForUserAction(
    response: InvoiceRunSuccessResponse,
    isNewDraftRun: boolean,
    isJobAddedToDraft: boolean,
  ) {
    // close any selections the user has made as we will be updating the view.
    this.closeAndResetExpandedClientInvoiceTable();

    // if there was an error force users to refresh page.
    if (!response || !response.success) {
      this.isLoadingUserActionRequest = false;
      showNotification(GENERIC_ERROR_MESSAGE);
      this.setInitialWeekEndingDate();
      return;
    }

    // set whether the current view is the user who made the request or any other division users.
    const responseIsForDivisionUsers =
      response.userName !== sessionManager.getUserName();

    // handle response for division users.
    if (responseIsForDivisionUsers) {
      // show notification specific to the update type
      if (isNewDraftRun) {
        showNotification('New draft run available.', {
          title: 'Client Draft Run',
          type: HealthLevel.INFO,
        });
      }
      if (isJobAddedToDraft) {
        showNotification('Draft Updated.', {
          title: 'Client Draft Run',
          type: HealthLevel.INFO,
        });
      }

      // reload the users view with the correct draft dates and data
      this.setWeekEndingEpoch(response.weekEndingDate, response.processingDate);
      return;
    }

    // handle response for user who made the request.
    if (!responseIsForDivisionUsers) {
      // show notification specific to the update type
      if (isNewDraftRun) {
        showNotification('Successfully created.', {
          title: 'Client Draft Run',
          type: HealthLevel.SUCCESS,
        });
      }

      if (isJobAddedToDraft) {
        showNotification('Successfully updated.', {
          title: 'Client Draft Run',
          type: HealthLevel.SUCCESS,
        });
      }
      // reload the users view with the correct draft dates and data
      this.setWeekEndingEpoch(response.weekEndingDate, response.processingDate);
    }
    this.isLoadingUserActionRequest = false;
  }

  /**
   * Sets the initial week ending date and processing date from the store, and
   * makes initial request with values
   */
  public setInitialWeekEndingDate() {
    this.weekEndingDate = this.clientInvoiceStore.weekEndingDate;
    this.processingDate = this.clientInvoiceStore.processingDate;
    this.billingCycleIds = this.clientInvoiceStore.billingCycleIds;
    this.setWeekEndingEpoch(this.weekEndingDate, this.processingDate);
  }

  // manually update the expanded prop in the child component ClientInvoiceTable
  public closeAndResetExpandedClientInvoiceTable() {
    this.selectedClientId = null;
    if (this.$refs.clientInvoiceTable) {
      Object.keys(
        this.$refs.clientInvoiceTable.$refs.tableComponent.expanded,
      ).forEach((key) => {
        this.$refs.clientInvoiceTable.$refs.tableComponent.expanded[key] =
          false;
      });
    }
  }

  public setReleasedForEditingJobResponse() {
    this.closeAndResetExpandedClientInvoiceTable();
    this.setInitialWeekEndingDate();
  }

  public async setClientRctiViewTab(selectedView: LedgerType) {
    if (!this.weekEndingDate || !this.billingCycleIds) {
      console.error(
        `Week ending date or billing cycle ids are null. Week ending date: ${this.weekEndingDate} - Billing cycle ids: ${this.billingCycleIds}`,
      );
      return;
    }
    this.selectedClientRctiViewTab = selectedView;

    if (this.selectedClientRctiViewTab === LedgerType.RCTI) {
      this.isLoadingRctiComparison = true;
      const request: ClientInvoiceRctiFormatRequest = {
        status: null,
        request: {
          weekEndingDate: this.weekEndingDate,
          processingDate: this.processingDate,
          billingCycleIds: this.billingCycleIds,
        },
      };
      // Send request and handle response
      const response =
        await this.clientInvoiceStore.getJobsInRCTIFormat(request);
      this.setRctiComparisonTable(response);
    }
  }

  public setRctiComparisonTable(
    rctiComparison: ClientInvoiceRctiFormatResponse | null,
  ) {
    if (!rctiComparison) {
      this.isLoadingRctiComparison = false;
      showNotification('Error fetching RCTI comparison.', {
        title: 'Client Draft Run',
      });
      return;
    }
    this.rctiInvoices = getFleetAssetOwnerRctiTableData(
      rctiComparison.fleetAssetOwnerAccountingTables,
      this.weekEndingDate,
      useAdjustmentChargeStore().adjustmentChargeCategories,
    );
    this.isLoadingRctiComparison = false;
  }

  public isAuthorised(): boolean {
    return hasAdminOrHeadOfficeRole();
  }

  /**
   * Handles the response from generating a Client Invoice Draft run.
   */
  private handleDraftRunResponse(payload: InvoiceRunSuccessResponse | null) {
    if (!payload) {
      return;
    }
    this.setResponseForUserAction(payload, true, false);
  }
  /**
   * Handles the response from adding a new job to an existing dummy run
   */
  private handleDraftAddJob(payload: InvoiceRunSuccessResponse | null) {
    if (!payload) {
      return;
    }
    this.setResponseForUserAction(payload, false, true);
  }

  // set our components mitt listeners and initialise the weekEnding date
  public beforeMount() {
    this.setInitialWeekEndingDate();
    Mitt.on('clientDraftRunResponse', this.handleDraftRunResponse);
    Mitt.on('clientDraftAddJobResponse', this.handleDraftAddJob);
    Mitt.on(
      'cancelClientInvoiceDraftRunResponse',
      this.setInitialWeekEndingDate,
    );
    Mitt.on(
      'releasedAccountingJobsToCompleted',
      this.setReleasedForEditingJobResponse,
    );
  }

  public beforeDestroy() {
    Mitt.off('clientDraftRunResponse', this.handleDraftRunResponse);
    Mitt.off('clientDraftAddJobResponse', this.handleDraftAddJob);
    Mitt.off(
      'cancelClientInvoiceDraftRunResponse',
      this.setInitialWeekEndingDate,
    );
    Mitt.off(
      'releasedAccountingJobsToCompleted',
      this.setReleasedForEditingJobResponse,
    );
  }
}
