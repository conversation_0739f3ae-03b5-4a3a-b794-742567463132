<v-layout wrap>
  <v-flex md12>
    <v-layout align-center class="accounting-data-table__actions-bar">
      <v-layout align-center>
        <div>
          <v-menu right ref="optionsMenu">
            <template v-slot:activator="{ on: menu }">
              <v-tooltip bottom>
                <template v-slot:activator="{ on: tooltip }">
                  <v-btn
                    flat
                    icon
                    v-on="{ ...tooltip, ...menu }"
                    class="ma-0"
                    :disabled="isLoading"
                  >
                    <v-icon size="16">fas fa-ellipsis-v </v-icon>
                  </v-btn>
                </template>
                <span>View Options</span>
              </v-tooltip>
            </template>
            <v-list dense class="v-list-custom">
              <v-divider></v-divider>
              <ClientInvoiceSummaryReportDialog
                v-if="selectedClientRctiViewTab === 'CLIENT_INVOICE'"
                :clientInvoices="clientInvoices"
              />

              <RctiInvoiceSummaryReportDialog
                v-if="selectedClientRctiViewTab === 'RCTI'"
                :rctiInvoices="rctiInvoices"
              />
              <v-divider></v-divider>

              <ClientRctiViewTabMenu
                :isDisabled="selectedClientId !== null"
                :selectedClientRctiViewTab="selectedClientRctiViewTab"
                @setClientRctiViewTab="setClientRctiViewTab"
              />
              <v-divider></v-divider>

              <ReleaseForEditingConfirmationDialog
                v-if="selectedClientId"
                :selectedJobIds="selectedJobIdsForSelectedClient"
                @closeMenuOptions="closeMenuOptions"
                @setLoading="setLoading"
              />
              <v-divider v-if="!invoiceRunStatus"></v-divider>
              <v-list-tile
                v-if="!invoiceRunStatus"
                @click="editRunParameters"
                :disabled="selectedClientId !== null"
              >
                <v-list-tile-action>
                  <v-icon size="18" class="pr-2">fal fa-sliders-h</v-icon>
                </v-list-tile-action>
                <v-list-tile-content>
                  <v-list-tile-title class="pr-2 ma-0">
                    <span
                      class="pr-2"
                      style="text-transform: none; font-weight: 400"
                      >Edit Run Options</span
                    >
                  </v-list-tile-title>
                </v-list-tile-content>
              </v-list-tile>
            </v-list>
          </v-menu>
        </div>
        <v-layout align-center>
          <InvoiceRunParametersSummary
            :parameters="{
              billingCycleIds: billingCycleIds,
              weekEndingDate: weekEndingDate,
              processingDate: processingDate,
            }"
          ></InvoiceRunParametersSummary>
          <div v-if="invoiceRunStatus">
            <CurrentInvoiceStatus :invoiceRunStatus="invoiceRunStatus" />
          </div>
        </v-layout>
      </v-layout>
      <v-spacer />
      <v-flex md2>
        <v-btn
          v-if="!invoiceRunStatus"
          block
          color="info"
          :disabled="numberOfSelectedReadyForDraft === 0 || !isAuthorised() || selectedClientId != null || selectedClientRctiViewTab === 'RCTI'"
          @click="setSelectedToDraft"
          :loading="isLoading"
        >
          Create Draft (<span>{{numberOfSelectedReadyForDraft}}</span>)
          <template v-slot:loader>
            <span>Loading...</span>
          </template>
        </v-btn>

        <v-btn
          v-if="invoiceRunStatus === 'DRAFT'"
          block
          color="info"
          :disabled="numberOfSelectedReadyForDraft === 0 || !isAuthorised() || !selectedClientId || selectedClientRctiViewTab === 'RCTI'"
          :loading="isLoading"
          @click="addSelectedToDraft"
        >
          Add To Draft (<span>{{numberOfSelectedReadyForDraft}}</span>)
          <template v-slot:loader>
            <span>Loading...</span>
          </template>
        </v-btn>
      </v-flex>
    </v-layout>
  </v-flex>

  <v-flex md12>
    <ClientInvoiceTable
      v-if="selectedClientRctiViewTab === 'CLIENT_INVOICE'"
      ref="clientInvoiceTable"
      :clientInvoices="clientInvoices"
      :selectedRowId.sync="selectedClientId"
      @resetClientInvoiceSelections="resetClientInvoiceSelections"
      :invoiceType="invoiceType"
      :isLoading="isLoading"
    />

    <RctiTable
      v-if="selectedClientRctiViewTab === 'RCTI'"
      ref="rctiTable"
      :rctiInvoices="rctiInvoices"
      :invoiceType="invoiceType"
      :isClientComparison="true"
      :isLoading="isLoading"
    />
  </v-flex>

  <InvalidClientDataDialog
    :showDialog="showInvalidClientDataDialog"
    @setDialog="setInvalidClientDataDialog"
  />
</v-layout>
