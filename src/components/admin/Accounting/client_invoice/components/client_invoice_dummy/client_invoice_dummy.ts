import ClientInvoiceTable from '@/components/admin/Accounting/client_invoice/components/client_invoice_table/index.vue';
import ClientRctiViewTabMenu from '@/components/admin/Accounting/client_invoice/components/client_rcti_view_tab_menu.vue';
import DraftJobRemovalConfirmationDialog from '@/components/admin/Accounting/client_invoice/components/confirmation_dialogs/draft_job_removal_confirmation_dialog.vue';
import CancelRunConfirmationDialog from '@/components/admin/Accounting/components/cancel_run_confirmation_dialog.vue';
import ClientInvoiceSummaryReportDialog from '@/components/admin/Accounting/components/client_invoice_summary_report_dialog/index.vue';
import CurrentInvoiceStatus from '@/components/admin/Accounting/components/current_invoice_status/index.vue';
import InvoiceEventHistoryDialog from '@/components/admin/Accounting/components/invoice_event_history_dialog.vue';
import InvoiceRunParametersSummary from '@/components/admin/Accounting/components/invoice_run_parameters_summary.vue';
import RctiInvoiceSummaryReportDialog from '@/components/admin/Accounting/components/rcti_summary_report_dialog/index.vue';
import RctiTable from '@/components/admin/Accounting/rcti/components/rcti_table/index.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import {
  getClientInvoiceTableData,
  getFleetAssetOwnerRctiTableData,
} from '@/helpers/AccountingHelpers/AccountingHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { ClientAccountingTable } from '@/interface-models/Accounting/ClientAccountingTable';
import {
  ClientInvoiceRctiFormatRequest,
  ClientInvoiceRctiFormatResponse,
} from '@/interface-models/Accounting/ClientInvoiceRCTIFormatRequestResponse';
import { ClientInvoiceRow } from '@/interface-models/Accounting/ClientInvoiceRow';
import { RctiInvoiceRow } from '@/interface-models/Accounting/FleetAssetOwnerAccountingTable';
import { InvoiceEvent } from '@/interface-models/Accounting/InvoiceEvent';
import { InvoiceNavigation } from '@/interface-models/Accounting/InvoiceNavigation';
import { InvoiceRunAccountingTable } from '@/interface-models/Accounting/InvoiceRunAccountingTable';
import { InvoiceRunSuccessResponse } from '@/interface-models/Accounting/InvoiceRunSuccessResponse';
import { InvoiceStatus } from '@/interface-models/Accounting/InvoiceStatus';
import { LedgerType } from '@/interface-models/Accounting/LedgerType';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { useAdjustmentChargeStore } from '@/store/modules/AdjustmentChargeStore';
import { useClientInvoiceStore } from '@/store/modules/ClientInvoiceStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  components: {
    ClientInvoiceTable,
    DraftJobRemovalConfirmationDialog,
    InvoiceEventHistoryDialog,
    ConfirmationDialog,
    CurrentInvoiceStatus,
    CancelRunConfirmationDialog,
    ClientRctiViewTabMenu,
    RctiTable,
    ClientInvoiceSummaryReportDialog,
    RctiInvoiceSummaryReportDialog,
    InvoiceRunParametersSummary,
  },
})
export default class ClientInvoiceDummy extends Vue implements IUserAuthority {
  @Prop({ default: true }) public accountingIndexLoading: boolean;

  public clientInvoiceStore = useClientInvoiceStore();

  public weekEndingDate: number | null = null;
  public processingDate: number | null = null;
  public billingCycleIds: number[] | null = null;

  public clientInvoices: ClientInvoiceRow[] = [];
  public invoiceEventHistory: InvoiceEvent[] = [];
  public selectedRowId: string | null = null;
  public invoiceType: InvoiceNavigation = InvoiceNavigation.DUMMY;
  public isLoadingDummy: boolean = true;
  public isLoadingUserActionRequest: boolean = false;
  public isLoadingRctiComparison: boolean = false;
  public selectedClientRctiViewTab: LedgerType = LedgerType.CLIENT_INVOICE;
  public rctiInvoices: RctiInvoiceRow[] = [];
  public approvalConfirmationCheckBoxList = [
    'I have reviewed Client Invoice Summary Report',
    'I have reviewed the Client Invoices to the extent that I am satisfied that they are accurate and complete',
    'I have reviewed the Driver Summary Report',
    'I have reviewed the Driver Pays to the extent that I am satisfied that they are accurate and complete',
    'I understand that by Approving these invoices for Live run, they will not be editable in the future',
  ];

  public $refs!: {
    optionsMenu: any;
    rctiTable: any;
  };

  // returns the current stage our invoice run is at.
  get invoiceRunStatus(): InvoiceStatus | null {
    return this.clientInvoiceStore.invoiceRunStatus;
  }

  get isLoading() {
    return (
      this.accountingIndexLoading ||
      this.isLoadingDummy ||
      this.isLoadingUserActionRequest ||
      this.isLoadingRctiComparison
    );
  }

  public setTableData(
    clientAccountingTableData: ClientAccountingTable[],
  ): void {
    this.clientInvoices = getClientInvoiceTableData(clientAccountingTableData);
    this.isLoadingDummy = false;
  }

  // Because the select tile in our menu is a component it does not automatically close when selected. We must close the menu manually. Method call Emitted from components that act as a tile in our menu.
  public closeMenuOptions() {
    if (this.$refs.optionsMenu) {
      this.$refs.optionsMenu.isActive = false;
    }
  }

  /**
   * Sends request to approve the current DUMMY run, which will move it to the
   * APPROVED status.
   */
  public approveDummyRun(): void {
    this.isLoadingUserActionRequest = true;
    this.clientInvoiceStore.approveDummyRun();
  }

  public async getDummyRun() {
    this.isLoadingDummy = true;
    // Make request and handle response
    const response: InvoiceRunAccountingTable | null =
      await this.clientInvoiceStore.getClientInvoiceRun(InvoiceStatus.DUMMY);
    if (!response) {
      this.isLoadingDummy = false;
      showNotification('Error fetching dummy run.', {
        title: 'Client Invoice Dummy Run',
      });
      return;
    }
    this.setDummyRunResponse(response);
  }

  public cancelDummyRun() {
    this.isLoadingUserActionRequest = true;
    this.clientInvoiceStore.cancelDummyRun();
  }

  public setDummyRunResponse(response: InvoiceRunAccountingTable): void {
    if (response.type !== InvoiceStatus.DUMMY) {
      return;
    }
    this.weekEndingDate = response.weekEndingDate;
    this.processingDate = response.processingDate;
    this.billingCycleIds = response.billingCycleIds;
    this.invoiceEventHistory = response.eventList;
    this.setTableData(response.clientAccountingTable);
  }

  // Handles responses for the users action. Mostly sets required fields so they are updated and also shows notifications based on the user who made the request and all other division users.
  public setResponseForUserAction(
    response: InvoiceRunSuccessResponse,
    showDummyApprovedSuccess: boolean,
    showDummyCancelledSuccess: boolean,
    isDraftApproved: boolean,
  ) {
    // if there was an error force users to refresh page.
    if (!response || !response.success) {
      this.isLoadingUserActionRequest = false;
      showNotification(GENERIC_ERROR_MESSAGE);
      this.getDummyRun();
      return;
    }

    // if dummy is canceled by user we should update our invoice status and relevant invoice dates in ClientInvoiceModule
    if (showDummyCancelledSuccess) {
      this.clientInvoiceStore.setInvoiceRunStatus(InvoiceStatus.DRAFT);
      this.clientInvoiceStore.setProcessingDate(response.processingDate);
      this.clientInvoiceStore.setWeekEndingDate(response.weekEndingDate);
      this.clientInvoiceStore.setBillingCycleIds(response.billingCycleIds);
    }

    // set whether the current view is the user who made the request or any other division users.
    const responseIsForDivisionUsers =
      response.userName !== sessionManager.getUserName();

    // handle response for division users.
    if (responseIsForDivisionUsers) {
      // show notification specific to the update type
      if (showDummyApprovedSuccess) {
        showNotification('Dummy run approved.', {
          title: 'Dummy Run',
          type: HealthLevel.INFO,
        });
      }

      if (showDummyCancelledSuccess) {
        showNotification('Dummy Cancelled.', {
          title: 'Dummy Run',
          type: HealthLevel.INFO,
        });
      }

      if (isDraftApproved) {
        showNotification('New Dummy Run Available.', {
          title: 'Dummy Run',
          type: HealthLevel.INFO,
        });
      }
    }
    // handle response for user who made the request.
    if (!responseIsForDivisionUsers) {
      // show notification specific to the update type
      if (showDummyApprovedSuccess) {
        showNotification('Successfully Approved.', {
          title: 'Dummy Run',
          type: HealthLevel.SUCCESS,
        });
        // Push user to the APPROVED tab.
        this.$emit('setCurrentView', InvoiceNavigation.APPROVED);
        return;
      }

      if (showDummyCancelledSuccess) {
        showNotification('Successfully Cancelled.', {
          title: 'Dummy Run',
          type: HealthLevel.SUCCESS,
        });
      }
    }
    this.isLoadingUserActionRequest = false;
    this.getDummyRun();
  }

  public async setClientRctiViewTab(selectedView: LedgerType) {
    if (!this.weekEndingDate || !this.billingCycleIds) {
      console.error(
        `Week ending date or billing cycle ids are null. Week ending date: ${this.weekEndingDate} - Billing cycle ids: ${this.billingCycleIds}`,
      );
      return;
    }
    this.selectedClientRctiViewTab = selectedView;
    if (
      this.selectedClientRctiViewTab === LedgerType.RCTI &&
      this.rctiInvoices.length === 0
    ) {
      this.isLoadingRctiComparison = true;
      const request: ClientInvoiceRctiFormatRequest = {
        status: InvoiceStatus.DUMMY,
        request: {
          weekEndingDate: this.weekEndingDate,
          processingDate: this.processingDate,
          billingCycleIds: this.billingCycleIds,
        },
      };
      // Send request and handle response
      const response =
        await this.clientInvoiceStore.getJobsInRCTIFormat(request);
      this.setRctiComparisonTable(response);
    }
  }

  public setRctiComparisonTable(
    rctiComparison: ClientInvoiceRctiFormatResponse | null,
  ) {
    if (!rctiComparison) {
      this.isLoadingRctiComparison = false;
      showNotification('Error fetching RCTI comparison.', {
        title: 'Dummy Run',
      });
      return;
    }
    this.rctiInvoices = getFleetAssetOwnerRctiTableData(
      rctiComparison.fleetAssetOwnerAccountingTables,
      this.weekEndingDate,
      useAdjustmentChargeStore().adjustmentChargeCategories,
    );
    this.isLoadingRctiComparison = false;
  }

  /**
   * Handles response from updating the processing date of a Client Invoice
   * Draft
   */
  private handleDraftApproval(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, false, false, true);
    }
  }

  /** Mitt callback for cancelling a DUMMY run (transition to DRAFT) */
  private handleDummyCancel(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, false, true, false);
    }
  }

  /** Mitt callback for cancelling a DUMMY run (transition to DRAFT) */
  private handleDummyApproval(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, true, false, false);
    }
  }

  public isAuthorised(): boolean {
    return hasAdminOrTeamLeaderOrBranchManagerRole();
  }

  public mounted() {
    this.getDummyRun();
    Mitt.on('approveClientInvoiceDraftRunResponse', this.handleDraftApproval);
    Mitt.on('cancelClientInvoiceDummyRunResponse', this.handleDummyCancel);
    Mitt.on('approveClientInvoiceDummyRunResponse', this.handleDummyApproval);
  }
  public beforeDestroy() {
    Mitt.off('approveClientInvoiceDraftRunResponse', this.handleDraftApproval);
    Mitt.off('cancelClientInvoiceDummyRunResponse', this.handleDummyCancel);
    Mitt.off('approveClientInvoiceDummyRunResponse', this.handleDummyApproval);
  }
}
