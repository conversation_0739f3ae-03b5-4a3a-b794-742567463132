<v-layout wrap>
  <v-flex md12>
    <v-layout align-center class="accounting-data-table__actions-bar">
      <v-menu right ref="optionsMenu">
        <template v-slot:activator="{ on: menu }">
          <v-tooltip bottom>
            <template v-slot:activator="{ on: tooltip }">
              <v-btn
                flat
                icon
                v-on="{ ...tooltip, ...menu }"
                class="ma-0"
                :disabled="invoiceRunStatus !== 'DUMMY' || isLoading"
              >
                <v-icon size="16">fas fa-ellipsis-v </v-icon>
              </v-btn>
            </template>
            <span>View Options</span>
          </v-tooltip>
        </template>
        <v-list dense class="v-list-custom">
          <ClientInvoiceSummaryReportDialog
            v-if="selectedClientRctiViewTab === 'CLIENT_INVOICE'"
            :clientInvoices="clientInvoices"
          />

          <RctiInvoiceSummaryReportDialog
            v-if="selectedClientRctiViewTab === 'RCTI'"
            :rctiInvoices="rctiInvoices"
          />
          <v-divider></v-divider>
          <ClientRctiViewTabMenu
            :isDisabled="selectedRowId !== null"
            :selectedClientRctiViewTab="selectedClientRctiViewTab"
            @setClientRctiViewTab="setClientRctiViewTab"
          />
          <v-divider></v-divider>
          <InvoiceEventHistoryDialog
            :invoiceEventHistory="invoiceEventHistory"
            :invoiceRunStatus="invoiceRunStatus"
          />
          <v-divider></v-divider>

          <CancelRunConfirmationDialog
            v-if="invoiceRunStatus === 'DUMMY' && selectedClientRctiViewTab === 'CLIENT_INVOICE'"
            :runType="'Dummy'"
            @closeMenuOptions="closeMenuOptions"
            @cancelRun="cancelDummyRun"
          />
        </v-list>
      </v-menu>

      <v-layout v-if="invoiceRunStatus === 'DUMMY'">
        <InvoiceRunParametersSummary
          :parameters="{
            billingCycleIds: billingCycleIds,
            weekEndingDate: weekEndingDate,
            processingDate: processingDate,
          }"
        ></InvoiceRunParametersSummary>
      </v-layout>

      <v-layout v-if="invoiceRunStatus !== 'DUMMY'">
        <CurrentInvoiceStatus :invoiceRunStatus="invoiceRunStatus" />
      </v-layout>
      <v-spacer />

      <v-flex md2 style="height: 48px">
        <ConfirmationDialog
          buttonText="Approve for Live"
          message="I declare that:"
          title="Confirm Approval of Dummy Invoices"
          @confirm="approveDummyRun"
          :isSmallButton="false"
          :buttonDisabled="!isAuthorised() || invoiceRunStatus !== 'DUMMY' || isLoading || selectedClientRctiViewTab === 'RCTI'"
          :isOutlineButton="false"
          :isBlockButton="true"
          :buttonColor="'info'"
          :confirmationButtonText="'Approve'"
          :isCheckboxList="true"
          :isLoading="isLoading"
          :checkboxLabelList="approvalConfirmationCheckBoxList"
          :dialogIsActive="true"
        >
        </ConfirmationDialog>
      </v-flex>
    </v-layout>
  </v-flex>

  <v-flex md12 class="reviewed-jobs-container">
    <ClientInvoiceTable
      v-if="selectedClientRctiViewTab === 'CLIENT_INVOICE'"
      :clientInvoices="clientInvoices"
      :selectedRowId.sync="selectedRowId"
      :invoiceType="invoiceType"
      :isLoading="isLoading"
    />
    <RctiTable
      v-if="selectedClientRctiViewTab === 'RCTI'"
      ref="rctiTable"
      :rctiInvoices="rctiInvoices"
      :invoiceType="invoiceType"
      :isLoading="isLoading"
      :isClientComparison="true"
    />
  </v-flex>
</v-layout>
