import ClientInvoiceTable from '@/components/admin/Accounting/client_invoice/components/client_invoice_table/index.vue';
import ClientRctiViewTabMenu from '@/components/admin/Accounting/client_invoice/components/client_rcti_view_tab_menu.vue';
import DraftJobRemovalConfirmationDialog from '@/components/admin/Accounting/client_invoice/components/confirmation_dialogs/draft_job_removal_confirmation_dialog.vue';
import CancelRunConfirmationDialog from '@/components/admin/Accounting/components/cancel_run_confirmation_dialog.vue';
import ClientInvoiceSummaryReportDialog from '@/components/admin/Accounting/components/client_invoice_summary_report_dialog/index.vue';
import CurrentInvoiceStatus from '@/components/admin/Accounting/components/current_invoice_status/index.vue';
import InvoiceEventHistoryDialog from '@/components/admin/Accounting/components/invoice_event_history_dialog.vue';
import InvoiceRunParametersSummary from '@/components/admin/Accounting/components/invoice_run_parameters_summary.vue';
import RctiInvoiceSummaryReportDialog from '@/components/admin/Accounting/components/rcti_summary_report_dialog/index.vue';
import RctiTable from '@/components/admin/Accounting/rcti/components/rcti_table/index.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import {
  getClientInvoiceTableData,
  getFleetAssetOwnerRctiTableData,
} from '@/helpers/AccountingHelpers/AccountingHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { ClientAccountingTable } from '@/interface-models/Accounting/ClientAccountingTable';
import {
  ClientInvoiceRctiFormatRequest,
  ClientInvoiceRctiFormatResponse,
} from '@/interface-models/Accounting/ClientInvoiceRCTIFormatRequestResponse';
import { ClientInvoiceRow } from '@/interface-models/Accounting/ClientInvoiceRow';
import { CurrentInvoiceRunTypeResponse } from '@/interface-models/Accounting/CurrentInvoiceRunTypeResponse';
import { RctiInvoiceRow } from '@/interface-models/Accounting/FleetAssetOwnerAccountingTable';
import { InvoiceEmailSentUpdate } from '@/interface-models/Accounting/InvoiceEmailSentUpdate';
import { InvoiceEvent } from '@/interface-models/Accounting/InvoiceEvent';
import { InvoiceNavigation } from '@/interface-models/Accounting/InvoiceNavigation';
import { InvoiceRunAccountingTable } from '@/interface-models/Accounting/InvoiceRunAccountingTable';
import { InvoiceRunSuccessResponse } from '@/interface-models/Accounting/InvoiceRunSuccessResponse';
import { InvoiceStatus } from '@/interface-models/Accounting/InvoiceStatus';
import { LedgerType } from '@/interface-models/Accounting/LedgerType';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { useAdjustmentChargeStore } from '@/store/modules/AdjustmentChargeStore';
import { useClientInvoiceStore } from '@/store/modules/ClientInvoiceStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  components: {
    ClientInvoiceTable,
    DraftJobRemovalConfirmationDialog,
    InvoiceEventHistoryDialog,
    ConfirmationDialog,
    CurrentInvoiceStatus,
    CancelRunConfirmationDialog,
    ClientRctiViewTabMenu,
    RctiTable,
    ClientInvoiceSummaryReportDialog,
    RctiInvoiceSummaryReportDialog,
    InvoiceRunParametersSummary,
  },
})
export default class ClientInvoiceApproved
  extends Vue
  implements IUserAuthority
{
  @Prop({ default: true }) public accountingIndexLoading: boolean;

  public clientInvoiceStore = useClientInvoiceStore();

  public weekEndingDate: number | null = null;
  public processingDate: number | null = null;
  public billingCycleIds: number[] | null = null;

  public clientInvoices: ClientInvoiceRow[] = [];
  public invoiceEventHistory: InvoiceEvent[] = [];
  public selectedRowId: string | null = null;
  public invoiceType: InvoiceNavigation = InvoiceNavigation.APPROVED;
  public isLoadingApproved: boolean = true;
  public isLoadingUserActionRequest: boolean = false;
  public isLoadingRctiComparison: boolean = false;
  public selectedClientRctiViewTab: LedgerType = LedgerType.CLIENT_INVOICE;
  public rctiInvoices: RctiInvoiceRow[] = [];
  // list of checkboxes the user will need to confirm before approving and sending the invoices
  public ApproveAndEmailConfirmationCheckBoxList = [
    'I understand that an email will be sent to all clients listed on this page',
    'I understand that by approving the Live Run, they will not be editable in the future',
  ];

  public $refs!: {
    optionsMenu: any;
    rctiTable: any;
  };

  public setTableData(
    clientAccountingTableData: ClientAccountingTable[],
  ): void {
    this.clientInvoices = getClientInvoiceTableData(clientAccountingTableData);
    this.isLoadingApproved = false;
  }

  // returns the current stage our invoice run is at.
  get invoiceRunStatus(): InvoiceStatus | null {
    return this.clientInvoiceStore.invoiceRunStatus;
  }

  get isLoading() {
    return (
      this.accountingIndexLoading ||
      this.isLoadingApproved ||
      this.isLoadingUserActionRequest ||
      this.isLoadingRctiComparison
    );
  }

  /**
   * Request for approving the current APPROVED run to be finalised and sent,
   * which will move it to the SENT status.
   */
  public approveAndEmailInvoices(): void {
    this.clientInvoiceStore.approveLiveRun();
  }

  // Because the select tile in our menu is a component it does not automatically close when selected. We must close the menu manually. Method call Emitted from components that act as a tile in our menu.
  public closeMenuOptions() {
    if (this.$refs.optionsMenu) {
      this.$refs.optionsMenu.isActive = false;
    }
  }

  public async getApprovedRun() {
    this.isLoadingApproved = true;
    const result: InvoiceRunAccountingTable | null =
      await this.clientInvoiceStore.getClientInvoiceRun(InvoiceStatus.APPROVED);
    this.setApprovedRunResponse(result);
  }

  // Request to cancel the approved invoice run
  public cancelApprovedRun() {
    this.isLoadingUserActionRequest = true;
    this.clientInvoiceStore.cancelApprovedRun();
  }

  public setApprovedRunResponse(response: InvoiceRunAccountingTable | null) {
    if (
      !response ||
      (response.type !== InvoiceStatus.APPROVED &&
        response.type !== InvoiceStatus.SENT)
    ) {
      return;
    }
    this.weekEndingDate = response.weekEndingDate;
    this.processingDate = response.processingDate;
    this.billingCycleIds = response.billingCycleIds;
    this.invoiceEventHistory = response.eventList;
    this.setTableData(response.clientAccountingTable);
  }

  public setResponseForUserAction(
    response: InvoiceRunSuccessResponse,
    isApprovedCancel: boolean,
    isDummyApproved: boolean,
  ) {
    // if there was an error force users to refresh page.
    if (!response || !response.success) {
      this.isLoadingUserActionRequest = false;
      showNotification(GENERIC_ERROR_MESSAGE);
      this.getApprovedRun();
      return;
    }

    // If draft is canceled by user we should update our invoice status and
    // relevant invoice dates in ClientInvoiceModule
    if (isApprovedCancel) {
      this.clientInvoiceStore.setInvoiceRunStatus(InvoiceStatus.DRAFT);
      this.clientInvoiceStore.setProcessingDate(response.processingDate);
      this.clientInvoiceStore.setWeekEndingDate(response.weekEndingDate);
      this.clientInvoiceStore.setBillingCycleIds(response.billingCycleIds);
    }

    // set whether the current view is the user who made the request or any other division users.
    const responseIsForDivisionUsers =
      response.userName !== sessionManager.getUserName();

    // handle response for division users.
    if (responseIsForDivisionUsers) {
      // show notification specific to the update type
      if (isApprovedCancel) {
        showNotification('Approved cancelled.', {
          title: 'Approved Run',
          type: HealthLevel.INFO,
        });
      }

      if (isDummyApproved) {
        showNotification('Approved run available.', {
          title: 'Approved Run',
          type: HealthLevel.INFO,
        });
      }
    }
    // handle response for user who made the request.
    if (!responseIsForDivisionUsers) {
      // show notification specific to the update type
      if (isApprovedCancel) {
        showNotification('Approved cancelled.', {
          title: 'Approved Run',
          type: HealthLevel.SUCCESS,
        });
        // Push user to the DUMMY tab.
        this.$emit('setCurrentView', InvoiceNavigation.DRAFT);
        return;
      }
    }
    this.isLoadingUserActionRequest = false;
    this.getApprovedRun();
  }

  public setClientInvoiceEmailUpdate(
    invoiceEmail: InvoiceEmailSentUpdate | null,
  ): void {
    if (!invoiceEmail) {
      return;
    }
    const ledgerId: string = invoiceEmail.ledgerId;
    const isSuccessful: boolean = invoiceEmail.isSuccessful;
    const invoice: ClientInvoiceRow | undefined = this.clientInvoices.find(
      (x: ClientInvoiceRow) => x.ledgerId === ledgerId,
    );

    if (!invoice) {
      return;
    }
    const status: InvoiceStatus = isSuccessful
      ? InvoiceStatus.SENT
      : InvoiceStatus.FAIL;
    invoice.status = status;
  }

  public setLiveRunFinishedResponse(
    runTypeResponse: CurrentInvoiceRunTypeResponse | null,
  ) {
    if (!runTypeResponse) {
      console.error('Current Invoice Run Type Response was null.');
      return;
    }
    // If the client invoice list contains items and the response for client invoice is null (null essentially means ready for invoicing) then the live run is finished. We should update this screen for any users currently active on it.
    if (this.clientInvoices.length > 0 && !runTypeResponse.clientInvoice) {
      showNotification('Live run complete.', {
        title: 'Live Run',
        type: HealthLevel.SUCCESS,
      });
      this.getApprovedRun();
    }
  }

  public async setClientRctiViewTab(selectedView: LedgerType) {
    if (!this.weekEndingDate || !this.billingCycleIds) {
      console.error(
        `Week ending date or billing cycle ids are null. Week ending date: ${this.weekEndingDate} - Billing cycle ids: ${this.billingCycleIds}`,
      );
      return;
    }
    this.selectedClientRctiViewTab = selectedView;

    if (
      this.selectedClientRctiViewTab === LedgerType.RCTI &&
      this.rctiInvoices.length === 0
    ) {
      this.isLoadingRctiComparison = true;
      const request: ClientInvoiceRctiFormatRequest = {
        status: InvoiceStatus.APPROVED,
        request: {
          weekEndingDate: this.weekEndingDate,
          processingDate: this.processingDate,
          billingCycleIds: this.billingCycleIds,
        },
      };
      // Send request and handle response
      const response =
        await this.clientInvoiceStore.getJobsInRCTIFormat(request);
      this.setRctiComparisonTable(response);
    }
  }

  public setRctiComparisonTable(
    rctiComparison: ClientInvoiceRctiFormatResponse | null,
  ) {
    if (!rctiComparison) {
      this.isLoadingRctiComparison = false;
      showNotification('Error fetching RCTI comparison.', {
        title: 'Client Invoice Approval',
      });
      return;
    }
    this.rctiInvoices = getFleetAssetOwnerRctiTableData(
      rctiComparison.fleetAssetOwnerAccountingTables,
      this.weekEndingDate,
      useAdjustmentChargeStore().adjustmentChargeCategories,
    );
    this.isLoadingRctiComparison = false;
  }

  /** Mitt callback for cancelling a DUMMY run (transition to APPROVED) */
  private handleDummyApproval(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, false, true);
    }
  }

  /** Mitt callback for cancelling am APPROVED run (transition to DRAFT) */
  private handleApprovedCancel(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, true, false);
    }
  }

  // Set mitt listeners
  public mounted() {
    this.getApprovedRun();
    Mitt.on('currentInvoiceRunType', this.setLiveRunFinishedResponse);
    Mitt.on('approveClientInvoiceDummyRunResponse', this.handleDummyApproval);
    Mitt.on('cancelClientInvoiceLiveRunResponse', this.handleApprovedCancel);
    Mitt.on('invoiceEmailSentUpdate', this.setClientInvoiceEmailUpdate);
  }
  /**
   * When the component is destroyed, we remove the mitt listener
   */
  public beforeDestroy() {
    Mitt.off('currentInvoiceRunType', this.setLiveRunFinishedResponse);
    Mitt.off('approveClientInvoiceDummyRunResponse', this.handleDummyApproval);
    Mitt.off('cancelClientInvoiceLiveRunResponse', this.handleApprovedCancel);
    Mitt.off('invoiceEmailSentUpdate', this.setClientInvoiceEmailUpdate);
  }

  public isAuthorised(): boolean {
    return hasAdminOrHeadOfficeRole();
  }
}
