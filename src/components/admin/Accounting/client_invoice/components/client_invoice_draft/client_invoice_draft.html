<v-layout wrap>
  <v-flex md12>
    <v-layout align-center class="accounting-data-table__actions-bar">
      <v-menu right ref="optionsMenu">
        <template v-slot:activator="{ on: menu }">
          <v-tooltip bottom>
            <template v-slot:activator="{ on: tooltip }">
              <v-btn
                flat
                icon
                v-on="{ ...tooltip, ...menu }"
                class="ma-0"
                :disabled="invoiceRunStatus !== 'DRAFT' || isLoading"
              >
                <v-icon size="16">fas fa-ellipsis-v </v-icon>
              </v-btn>
            </template>
            <span>View Options</span>
          </v-tooltip>
        </template>
        <v-list dense class="v-list-custom">
          <ClientInvoiceSummaryReportDialog
            v-if="selectedClientRctiViewTab === 'CLIENT_INVOICE'"
            :clientInvoices="clientInvoices"
          />

          <RctiInvoiceSummaryReportDialog
            v-if="selectedClientRctiViewTab === 'RCTI'"
            :rctiInvoices="rctiInvoices"
          />
          <v-divider></v-divider>
          <ClientRctiViewTabMenu
            :isDisabled="selectedRowId !== null"
            :selectedClientRctiViewTab="selectedClientRctiViewTab"
            @setClientRctiViewTab="setClientRctiViewTab"
          />
          <v-divider></v-divider>

          <ProcessingDateUpdateDialog
            v-if="processingDate !== null && invoiceRunStatus === 'DRAFT' && !selectedRowId && selectedClientRctiViewTab === 'CLIENT_INVOICE'"
            :isDraft="invoiceRunStatus === 'DRAFT'"
            @closeMenuOptions="closeMenuOptions"
            :processingDate="processingDate"
            @updateProcessingDate="updateProcessingDate"
            :isLoadingUserActionRequest.sync="isLoadingUserActionRequest"
          />

          <v-divider></v-divider>
          <DraftJobRemovalConfirmationDialog
            v-if="selectedRowId !== null && selectedClientRctiViewTab === 'CLIENT_INVOICE'"
            @closeMenuOptions="closeMenuOptions"
            :selectedJobIds="selectedJobIds"
            :totalNumberOfJobsInDraft.sync="totalNumberOfJobsInDraft"
            @cancelDraftRun="cancelDraftRun"
            @removeSelectedJobsFromDraft="removeSelectedJobsFromDraft"
          />

          <v-divider></v-divider>
          <InvoiceEventHistoryDialog
            @closeMenuOptions="closeMenuOptions"
            :invoiceEventHistory="invoiceEventHistory"
            :invoiceRunStatus="invoiceRunStatus"
          />
          <v-divider></v-divider>
          <CancelDraftDialog
            v-if="invoiceRunStatus === 'DRAFT' && !selectedRowId && selectedClientRctiViewTab === 'CLIENT_INVOICE'"
            @closeMenuOptions="closeMenuOptions"
            @cancelDraftRun="cancelDraftRun"
          />
        </v-list>
      </v-menu>
      <v-layout v-if="invoiceRunStatus === 'DRAFT'">
        <InvoiceRunParametersSummary
          :parameters="{
            billingCycleIds: billingCycleIds,
            weekEndingDate: weekEndingDate,
            processingDate: processingDate,
          }"
        ></InvoiceRunParametersSummary>
      </v-layout>

      <v-layout v-if="invoiceRunStatus !== 'DRAFT'">
        <CurrentInvoiceStatus :invoiceRunStatus="invoiceRunStatus" />
      </v-layout>

      <v-layout> </v-layout>
      <v-spacer />
      <v-flex md2>
        <v-btn
          block
          color="info"
          :disabled="!isAuthorised() || selectedRowId !== null || isLoading || invoiceRunStatus !== 'DRAFT' || clientInvoices.length < 1 || selectedClientRctiViewTab === 'RCTI'"
          :loading="isLoading"
          @click="approveDraftRun"
        >
          Generate Dummy
          <template v-slot:loader>
            <span>Loading...</span>
          </template>
        </v-btn>
      </v-flex>
    </v-layout>
  </v-flex>

  <v-flex md12 class="reviewed-jobs-container">
    <ClientInvoiceTable
      v-if="selectedClientRctiViewTab === 'CLIENT_INVOICE'"
      ref="clientInvoiceTable"
      :clientInvoices="isLoading ? [] : clientInvoices"
      :selectedRowId.sync="selectedRowId"
      :invoiceIdAvailable="false"
      :invoiceType="invoiceType"
      :isLoading="isLoading"
      @resetClientInvoiceSelections="resetClientInvoiceSelections"
    />

    <RctiTable
      v-if="selectedClientRctiViewTab === 'RCTI'"
      ref="rctiTable"
      :isClientComparison="true"
      :rctiInvoices="rctiInvoices"
      :invoiceType="invoiceType"
      :isLoading="isLoading"
    />
  </v-flex>
</v-layout>
