import ClientInvoiceTable from '@/components/admin/Accounting/client_invoice/components/client_invoice_table/index.vue';
import ClientRctiViewTabMenu from '@/components/admin/Accounting/client_invoice/components/client_rcti_view_tab_menu.vue';
import DraftJobRemovalConfirmationDialog from '@/components/admin/Accounting/client_invoice/components/confirmation_dialogs/draft_job_removal_confirmation_dialog.vue';
import CancelDraftDialog from '@/components/admin/Accounting/components/cancel_draft_dialog.vue';
import ClientInvoiceSummaryReportDialog from '@/components/admin/Accounting/components/client_invoice_summary_report_dialog/index.vue';
import CurrentInvoiceStatus from '@/components/admin/Accounting/components/current_invoice_status/index.vue';
import InvoiceEventHistoryDialog from '@/components/admin/Accounting/components/invoice_event_history_dialog.vue';
import InvoiceRunParametersSummary from '@/components/admin/Accounting/components/invoice_run_parameters_summary.vue';
import ProcessingDateUpdateDialog from '@/components/admin/Accounting/components/processing_date_update_dialog.vue';
import RctiInvoiceSummaryReportDialog from '@/components/admin/Accounting/components/rcti_summary_report_dialog/index.vue';
import RctiTable from '@/components/admin/Accounting/rcti/components/rcti_table/index.vue';
import {
  getClientInvoiceTableData,
  getFleetAssetOwnerRctiTableData,
} from '@/helpers/AccountingHelpers/AccountingHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { AccountingJobRow } from '@/interface-models/Accounting/AccountingJobRow';
import { ClientAccountingTable } from '@/interface-models/Accounting/ClientAccountingTable';
import {
  ClientInvoiceRctiFormatRequest,
  ClientInvoiceRctiFormatResponse,
} from '@/interface-models/Accounting/ClientInvoiceRCTIFormatRequestResponse';
import { ClientInvoiceRow } from '@/interface-models/Accounting/ClientInvoiceRow';
import { RctiInvoiceRow } from '@/interface-models/Accounting/FleetAssetOwnerAccountingTable';
import { InvoiceEvent } from '@/interface-models/Accounting/InvoiceEvent';
import { InvoiceNavigation } from '@/interface-models/Accounting/InvoiceNavigation';
import { InvoiceRunAccountingTable } from '@/interface-models/Accounting/InvoiceRunAccountingTable';
import { InvoiceRunSuccessResponse } from '@/interface-models/Accounting/InvoiceRunSuccessResponse';
import { InvoiceStatus } from '@/interface-models/Accounting/InvoiceStatus';
import { LedgerType } from '@/interface-models/Accounting/LedgerType';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { useAdjustmentChargeStore } from '@/store/modules/AdjustmentChargeStore';
import { useClientInvoiceStore } from '@/store/modules/ClientInvoiceStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component({
  components: {
    ClientInvoiceTable,
    DraftJobRemovalConfirmationDialog,
    InvoiceEventHistoryDialog,
    ProcessingDateUpdateDialog,
    CancelDraftDialog,
    CurrentInvoiceStatus,
    ClientRctiViewTabMenu,
    RctiTable,
    ClientInvoiceSummaryReportDialog,
    RctiInvoiceSummaryReportDialog,
    InvoiceRunParametersSummary,
  },
})
export default class ClientInvoiceDraft extends Vue implements IUserAuthority {
  @Prop({ default: true }) public accountingIndexLoading: boolean;

  public clientInvoiceStore = useClientInvoiceStore();

  public weekEndingDate: number | null = null;
  public processingDate: number | null = null;
  public billingCycleIds: number[] | null = null;

  public clientInvoices: ClientInvoiceRow[] = [];
  public invoiceEventHistory: InvoiceEvent[] = [];
  public selectedRowId: string | null = null;
  public invoiceType: InvoiceNavigation = InvoiceNavigation.DRAFT;
  public isLoadingDraft: boolean = true;
  public isLoadingUserActionRequest: boolean = false;
  public isLoadingRctiComparison: boolean = false;
  public selectedClientRctiViewTab: LedgerType = LedgerType.CLIENT_INVOICE;
  public rctiInvoices: RctiInvoiceRow[] = [];

  public $refs!: {
    clientInvoiceTable: any;
    optionsMenu: any;
    rctiTable: any;
  };

  get selectedJobIds() {
    const selectedClients = this.clientInvoices.filter(
      (x: ClientInvoiceRow) => x.isSelected,
    );
    let selectedJobIds: number[] = [];
    for (const invoice of selectedClients) {
      const jobIds = invoice.jobList
        .filter((x: AccountingJobRow) => x.isSelected)
        .map((y: AccountingJobRow) => y.jobId);

      selectedJobIds = selectedJobIds.concat(jobIds);
    }
    return selectedJobIds;
  }

  get isLoading() {
    return (
      this.accountingIndexLoading ||
      this.isLoadingDraft ||
      this.isLoadingUserActionRequest ||
      this.isLoadingRctiComparison
    );
  }

  // returns the current stage our invoice run is at.
  get invoiceRunStatus(): InvoiceStatus | null {
    return this.clientInvoiceStore.invoiceRunStatus;
  }

  get totalNumberOfJobsInDraft(): number {
    let totalNumberOfJobsInDraft = 0;
    for (const clientInvoice of this.clientInvoices) {
      totalNumberOfJobsInDraft += clientInvoice.jobList.length;
    }
    return totalNumberOfJobsInDraft;
  }

  // Because the select tile in our menu is a component it does not automatically close when selected. We must close the menu manually. Method call Emitted from components that act as a tile in our menu.
  public closeMenuOptions() {
    if (this.$refs.optionsMenu) {
      this.$refs.optionsMenu.isActive = false;
    }
  }

  public setDraftRunResponse(response: InvoiceRunAccountingTable): void {
    this.weekEndingDate = response.weekEndingDate;
    this.processingDate = response.processingDate;
    this.billingCycleIds = response.billingCycleIds;
    this.invoiceEventHistory = response.eventList;
    this.setTableData(response.clientAccountingTable);
  }

  public setTableData(
    clientAccountingTableData: ClientAccountingTable[],
  ): void {
    this.clientInvoices = getClientInvoiceTableData(clientAccountingTableData);
    this.isLoadingDraft = false;
  }

  public async getDraftRun() {
    this.isLoadingDraft = true;
    this.selectedRowId = null;

    // Make request and handle response
    const response: InvoiceRunAccountingTable | null =
      await this.clientInvoiceStore.getClientInvoiceRun(InvoiceStatus.DRAFT);
    if (!response) {
      this.isLoadingDraft = false;
      showNotification('Error fetching draft run.', {
        title: 'Client Invoice Draft Run',
      });
      return;
    }
    this.setDraftRunResponse(response);
  }

  public cancelDraftRun() {
    this.isLoadingUserActionRequest = true;
    this.clientInvoiceStore.cancelDraftRun();
  }

  public removeSelectedJobsFromDraft() {
    this.clientInvoiceStore.removeJobsFromDraftRun(this.selectedJobIds);
  }

  // resets the user edits back to the table datas default state.
  public resetClientInvoiceSelections() {
    for (const clientInvoice of this.clientInvoices) {
      clientInvoice.isSelected = false;
      clientInvoice.partialSelected = false;
      for (const job of clientInvoice.jobList) {
        job.isSelected = false;
      }
    }
  }

  public setDraftJobRemoval(
    jobRemovalResponse: InvoiceRunSuccessResponse | null,
  ) {
    if (!jobRemovalResponse) {
      // something went wrong
      return;
    }

    // manually update the expanded prop in the child component ClientInvoiceTable
    if (this.$refs.clientInvoiceTable) {
      Object.keys(
        this.$refs.clientInvoiceTable.$refs.tableComponent.expanded,
      ).forEach((key) => {
        this.$refs.clientInvoiceTable.$refs.tableComponent.expanded[key] =
          false;
      });
    }
    this.getDraftRun();
  }

  public approveDraftRun() {
    this.isLoadingUserActionRequest = true;
    this.clientInvoiceStore.approveDraftRun();
  }

  public updateProcessingDate(processingDate: number) {
    this.processingDate = processingDate;
    this.isLoadingUserActionRequest = true;
    this.clientInvoiceStore.updateDraftProcessingDate(this.processingDate);
  }

  // Handles responses for the users action. Mostly sets required fields so they are updated and also shows notifications based on the user who made the request and all other division users.
  public setResponseForUserAction(
    response: InvoiceRunSuccessResponse,
    options: {
      isDraftCancel?: boolean;
      isProcessingDateUpdate?: boolean;
      isDraftApproved?: boolean;
      isNewDraftRun?: boolean;
      isDummyCancel?: boolean;
      isApprovedCancel?: boolean;
    } = {},
  ): void {
    const {
      isDraftCancel = false,
      isProcessingDateUpdate = false,
      isDraftApproved = false,
      isNewDraftRun = false,
      isDummyCancel = false,
      isApprovedCancel = false,
    } = options;
    // if there was an error force users to refresh page.
    if (!response || !response.success) {
      this.isLoadingUserActionRequest = false;
      showNotification(GENERIC_ERROR_MESSAGE);
      this.getDraftRun();
      return;
    }

    // If draft is canceled by user we should update our invoice status and relevant invoice dates in ClientInvoiceModule
    if (isDraftCancel) {
      this.clientInvoiceStore.setInvoiceRunStatus(null);
      this.clientInvoiceStore.setProcessingDate(null);
      this.clientInvoiceStore.setWeekEndingDate(null);
      this.clientInvoiceStore.setBillingCycleIds(null);
    }

    // set whether the current view is the user who made the request or any other division users.
    const responseIsForDivisionUsers =
      response.userName !== sessionManager.getUserName();

    // handle response for division users.
    if (responseIsForDivisionUsers) {
      // show notification specific to the update type

      if (isNewDraftRun) {
        showNotification('New draft available.', {
          title: 'Draft Run',
          type: HealthLevel.INFO,
        });
      }
      if (isDraftApproved) {
        this.isLoadingUserActionRequest = false;
        showNotification('New dummy run available.', {
          title: 'Dummy Run',
          type: HealthLevel.INFO,
        });
      }

      if (isProcessingDateUpdate) {
        showNotification('Processing Date Updated.', {
          title: 'Draft Run',
          type: HealthLevel.INFO,
        });
      }

      if (isDraftCancel) {
        showNotification('Draft cancelled.', {
          title: 'Draft Run',
          type: HealthLevel.INFO,
        });
      }

      if (isDummyCancel) {
        showNotification('Dummy cancelled.', {
          title: 'Dummy Run',
          type: HealthLevel.INFO,
        });
      }

      if (isApprovedCancel) {
        showNotification('Approved cancelled.', {
          title: 'Approved Run',
          type: HealthLevel.INFO,
        });
      }
    }
    // handle response for user who made the request.
    if (!responseIsForDivisionUsers) {
      // show notification specific to the update type
      if (isDraftApproved) {
        showNotification('Successfully Approved.', {
          title: 'Draft Run',
          type: HealthLevel.SUCCESS,
        });
        // Push user to the DUMMY tab.
        this.$emit('setCurrentView', InvoiceNavigation.DUMMY);
        return;
      }

      if (isProcessingDateUpdate) {
        showNotification('Processing Date Updated.', {
          title: 'Draft Run',
          type: HealthLevel.SUCCESS,
        });
      }

      if (isDraftCancel) {
        showNotification('Successfully Cancelled.', {
          title: 'Draft Run',
          type: HealthLevel.SUCCESS,
        });
        this.$emit('setCurrentView', InvoiceNavigation.READY_FOR_INVOICING);
      }
    }
    this.isLoadingUserActionRequest = false;
    this.getDraftRun();
  }

  public async setClientRctiViewTab(selectedView: LedgerType): Promise<void> {
    if (!this.weekEndingDate || !this.billingCycleIds) {
      console.error(
        `Week ending date or billing cycle ids are null. Week ending date: ${this.weekEndingDate} - Billing cycle ids: ${this.billingCycleIds}`,
      );
      return;
    }
    this.selectedClientRctiViewTab = selectedView;
    if (this.selectedClientRctiViewTab === LedgerType.RCTI) {
      this.isLoadingRctiComparison = true;
      const request: ClientInvoiceRctiFormatRequest = {
        status: InvoiceStatus.DRAFT,
        request: {
          weekEndingDate: this.weekEndingDate,
          processingDate: this.processingDate,
          billingCycleIds: this.billingCycleIds,
        },
      };
      // Send request and handle response
      const response =
        await this.clientInvoiceStore.getJobsInRCTIFormat(request);
      this.setRctiComparisonTable(response);
    } else {
      this.rctiInvoices = [];
    }
  }

  public setRctiComparisonTable(
    rctiComparison: ClientInvoiceRctiFormatResponse | null,
  ) {
    if (!rctiComparison) {
      this.isLoadingRctiComparison = false;
      showNotification('Error fetching RCTI comparison.', {
        title: 'Client Invoice Draft Run',
      });
      return;
    }
    this.rctiInvoices = getFleetAssetOwnerRctiTableData(
      rctiComparison.fleetAssetOwnerAccountingTables,
      this.weekEndingDate,
      useAdjustmentChargeStore().adjustmentChargeCategories,
    );
    this.isLoadingRctiComparison = false;
  }

  /**
   * Mitt callback for generating a Client Invoice Draft run. Used as
   * callback in mitt listener.
   */
  private handleDraftRunResponse(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, { isNewDraftRun: true });
    }
  }
  /**
   * Mitt callback for cancelling a Client Invoice Draft run. Used in
   * mitt listener.
   */
  private handleDraftCancel(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, { isDraftCancel: true });
    }
  }
  /**
   * Mitt callback for updating the processing date of a Client Invoice
   * Draft
   */
  private handleDraftProcessingDate(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, { isProcessingDateUpdate: true });
    }
  }
  /** Mitt callback for approving a DRAFT run (transition to DUMMY) */
  private handleDraftApproval(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, { isDraftApproved: true });
    }
  }
  /** Mitt callback for cancelling a DUMMY run (transition to DRAFT) */
  private handleDummyCancel(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, { isDummyCancel: true });
    }
  }
  /** Mitt callback for cancelling am APPROVED run (transition to DRAFT) */
  private handleApprovedCancel(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, { isApprovedCancel: true });
    }
  }

  public isAuthorised(): boolean {
    return hasAdminOrHeadOfficeRole();
  }

  public mounted() {
    this.getDraftRun();
    Mitt.on('clientDraftRunResponse', this.handleDraftRunResponse);
    Mitt.on('clientDraftRemoveJobResponse', this.setDraftJobRemoval);
    Mitt.on('cancelClientInvoiceDraftRunResponse', this.handleDraftCancel);
    Mitt.on(
      'updateClientDraftProcessingDateResponse',
      this.handleDraftProcessingDate,
    );
    Mitt.on('approveClientInvoiceDraftRunResponse', this.handleDraftApproval);
    Mitt.on('cancelClientInvoiceDummyRunResponse', this.handleDummyCancel);
    Mitt.on('cancelClientInvoiceLiveRunResponse', this.handleApprovedCancel);
  }

  public beforeDestroy() {
    Mitt.off('clientDraftRunResponse', this.handleDraftRunResponse);
    Mitt.off('clientDraftRemoveJobResponse', this.setDraftJobRemoval);
    Mitt.off('cancelClientInvoiceDraftRunResponse', this.handleDraftCancel);
    Mitt.off(
      'updateClientDraftProcessingDateResponse',
      this.handleDraftProcessingDate,
    );
    Mitt.off('approveClientInvoiceDraftRunResponse', this.handleDraftApproval);
    Mitt.off('cancelClientInvoiceDummyRunResponse', this.handleDummyCancel);
    Mitt.off('cancelClientInvoiceLiveRunResponse', this.handleApprovedCancel);
  }
}
