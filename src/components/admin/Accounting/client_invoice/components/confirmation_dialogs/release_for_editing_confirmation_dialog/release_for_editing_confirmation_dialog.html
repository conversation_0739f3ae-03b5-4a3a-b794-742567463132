<v-layout>
  <v-dialog
    v-model="dialogIsActive"
    width="450px"
    class="ma-0"
    persistent
    no-click-animation
    content-class="v-dialog-custom"
  >
    <template v-slot:activator="{ on }">
      <v-list-tile
        v-on="on"
        :disabled="selectedJobIds.length === 0 || !isAuthorised()"
        @click="() => null"
      >
        <v-list-tile-action>
          <v-icon
            size="18"
            class="pr-2"
            :disabled="selectedJobIds.length === 0 || !isAuthorised()"
            >fal fa-file-edit</v-icon
          >
        </v-list-tile-action>
        <v-list-tile-content>
          <v-list-tile-title class="pr-2 ma-0">
            <span class="pr-2" style="text-transform: none; font-weight: 400;"
              >{{tileText}}</span
            >
          </v-list-tile-title>
        </v-list-tile-content>
      </v-list-tile>
    </template>
    <div>
      <v-layout
        justify-space-between
        align-center
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>Release Jobs to Pricing</span>
        <div
          class="app-theme__center-content--closebutton"
          @click="dialogIsActive = false"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <v-layout row wrap class="app-theme__center-content--body dialog-content">
        <v-flex md12>
          <v-layout row wrap class="body-scrollable--65" pa-3>
            <v-flex md12>
              <v-alert type="warning" :value="true">
                <span>{{alertWarningMessage}}</span>

                <ul>
                  <li v-for="listItem of dotPointListItems">
                    <span>
                      {{listItem}}
                    </span>
                  </li>
                </ul>
              </v-alert>
              <v-layout pa-2>
                <span>
                  {{confirmationMessage}}
                </span>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-divider></v-divider>
          <v-layout align-center>
            <v-btn color="error" outline @click="dialogIsActive = false">
              Cancel
            </v-btn>
            <v-spacer></v-spacer>
            <v-btn depressed color="blue" @click="setSelectedToCompleted">
              {{buttonText}}
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
  </v-dialog>
</v-layout>
