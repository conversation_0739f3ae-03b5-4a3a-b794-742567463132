import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { hasAdminOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { useClientInvoiceStore } from '@/store/modules/ClientInvoiceStore';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ReleaseForEditingConfirmationDialog
  extends Vue
  implements IUserAuthority
{
  @Prop({ default: () => [] }) public selectedJobIds: number[];
  @Prop() public isSingleJob: boolean;
  @Prop() public singleJobDisplayId: string;

  public dialogIsActive: boolean = false;

  /**
   * Called on confirm button click. Releases selected jobs to completed.
   */
  public async setSelectedToCompleted() {
    // Show notification if there are no jobs selected. This should never happen
    // because we disable the button if no jobs are selected.
    if (this.selectedJobIds.length === 0) {
      showNotification('No jobs selected for release.', {
        type: HealthLevel.INFO,
        title: 'Release Jobs for Editing',
      });
      return;
    }
    this.$emit('update:isLoading', true);
    const result = await useClientInvoiceStore().releaseJobsToCompleted(
      this.selectedJobIds,
    );

    // If response is null, show error message and return
    if (!result) {
      showNotification('An unexpected error occurred. Please try again.', {
        type: HealthLevel.ERROR,
        title: 'Release Jobs for Editing',
      });
      this.$emit('update:isLoading', false);
      return;
    }

    // Show notification describing the result of the release operation
    const successCount = result.length;
    const failedCount = this.selectedJobIds.length - successCount;
    if (successCount === 0) {
      showNotification('Failed to release jobs for editing.', {
        type: HealthLevel.ERROR,
        title: 'Release Jobs for Editing',
      });
    } else if (failedCount === 0) {
      showNotification(
        `Success! ${this.selectedJobIds.length} jobs returned to the Pricing Screen.`,
        { type: HealthLevel.SUCCESS, title: 'Release Jobs for Editing' },
      );
    } else {
      showNotification(
        `Success! ${successCount} job(s) returned to the Pricing Screen. ${failedCount} job(s) failed and could not be updated.`,
        { type: HealthLevel.WARNING, title: 'Release Jobs for Editing' },
      );
    }

    // Close menu options and hide dialog
    this.$emit('closeMenuOptions');
    this.dialogIsActive = false;
  }

  get tileText(): string {
    if (!this.isSingleJob) {
      return (
        'Release Selected For Editing (' + this.selectedJobIds.length + ')'
      );
    } else {
      return 'Release Job for Editing';
    }
  }

  public isAuthorised(): boolean {
    return hasAdminOrTeamLeaderOrBranchManagerRole();
  }

  get alertWarningMessage(): string {
    const jobPluralText: string =
      this.selectedJobIds.length > 1 ? 'jobs' : 'job';

    let alertWarningMessage: string =
      'You are about to send ' +
      (!this.isSingleJob
        ? this.selectedJobIds.length + ' ' + jobPluralText
        : 'job ' + this.singleJobDisplayId) +
      ' to the Pricing Screen.';

    if (this.dotPointListItems.length > 0) {
      alertWarningMessage += ' Please Note:';
    }
    return alertWarningMessage;
  }

  get confirmationMessage(): string {
    const jobPluralText: string =
      this.selectedJobIds.length > 1 ? 'jobs' : 'job';
    const confirmationMessage: string =
      'Are you sure you wish to release ' +
      (!this.isSingleJob
        ? 'the ' + this.selectedJobIds.length + '  selected ' + jobPluralText
        : 'job ' + this.singleJobDisplayId) +
      ' back to the Pricing Screen? ';

    return confirmationMessage;
  }

  get jobPluralText(): string {
    return this.selectedJobIds.length > 1 ? 'jobs' : 'job';
  }

  get dotPointListItems(): string[] {
    return ['The ' + this.jobPluralText + ' will need to be RE-PRICED.'];
  }

  get buttonText(): string {
    return (
      'Confirm And Release ' +
      this.selectedJobIds.length +
      ' ' +
      this.jobPluralText
    );
  }
}
