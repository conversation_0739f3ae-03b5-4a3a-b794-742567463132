<template>
  <v-list-tile @click="selectView" :disabled="isDisabled">
    <v-list-tile-action>
      <v-icon size="16" :disabled="isDisabled"> {{ tileIcon }}</v-icon>
    </v-list-tile-action>
    <v-list-tile-content>
      <v-list-tile-title class="pr-2 ma-0">
        <span class="pr-2" style="text-transform: none; font-weight: 400">
          {{ tileText }}
        </span>
      </v-list-tile-title>
    </v-list-tile-content>
  </v-list-tile>
</template>

<script setup lang="ts">
import { LedgerType } from '@/interface-models/Accounting/LedgerType';
import { ComputedRef, computed, Ref } from 'vue';

const props = withDefaults(
  defineProps<{
    selectedClientRctiViewTab?: LedgerType;
    isDisabled?: boolean;
  }>(),
  {
    selectedClientRctiViewTab: LedgerType.CLIENT_INVOICE,
    isDisabled: false,
  },
);

const emit = defineEmits(['setClientRctiViewTab']);

function selectView() {
  const viewToSelect: LedgerType =
    props.selectedClientRctiViewTab === LedgerType.CLIENT_INVOICE
      ? LedgerType.RCTI
      : LedgerType.CLIENT_INVOICE;
  emit('setClientRctiViewTab', viewToSelect);
}

const tileIcon: ComputedRef<string> = computed(() => {
  if (props.selectedClientRctiViewTab === LedgerType.CLIENT_INVOICE) {
    return 'far fa-truck-moving';
  } else {
    return 'far fa-briefcase';
  }
});

const tileText: ComputedRef<string> = computed(() => {
  if (props.selectedClientRctiViewTab === LedgerType.CLIENT_INVOICE) {
    return 'View By Subcontractors';
  } else {
    return 'View By Client';
  }
});
</script>
