<template>
  <div class="job-booking-form-container">
    <v-layout class="new-booking-dialog" row wrap>
      <v-flex md12 pt-2>
        <h1>{{ title }}</h1>
        <p>Please select the parameters for the run.</p>
      </v-flex>
      <v-flex md12>
        <v-form class="form-container" ref="startInvoiceRunForm">
          <v-layout row wrap>
            <v-flex md12>
              <v-layout>
                <h6 class="mt-1">Week Ending Date</h6>
              </v-layout>
            </v-flex>
            <v-flex md6>
              <DatePickerBasic
                @setEpoch="weekEndingDate = $event"
                :hideIcon="false"
                labelName="Select Week Ending Date"
                :epochTime="weekEndingDate"
                hint="Select Week Ending Date"
                class="v-solo-custom"
                :solo-input="true"
                :allowNegativeEpoch="false"
                persistent-hint
              >
              </DatePickerBasic>
            </v-flex>
            <v-checkbox
              v-model="syncProcessingDateController"
              label="Same Processing Date"
              color="light-blue"
              class="ml-3 mt-2"
            />
          </v-layout>

          <v-layout v-if="!syncProcessingDateController" row wrap>
            <v-flex md12>
              <v-layout>
                <h6 class="mt-3">Processing Date</h6>
              </v-layout>
            </v-flex>
            <v-flex md6>
              <DatePickerBasic
                @setEpoch="invoiceRunParameters.processingDate = $event"
                :hideIcon="false"
                labelName="Processing Date"
                :epochTime="invoiceRunParameters.processingDate"
                hint="Select Processing Date"
                class="v-solo-custom mb-1"
                :solo-input="true"
                :allowNegativeEpoch="false"
                persistent-hint
              >
              </DatePickerBasic>
            </v-flex>
          </v-layout>

          <v-layout row wrap>
            <v-flex md12>
              <v-layout>
                <h6 class="mt-3">Billing Cycle</h6>
              </v-layout>
            </v-flex>
            <v-flex md9>
              <v-select
                v-model="invoiceRunParameters.billingCycleIds"
                :items="formattedBillingCycles"
                item-text="longName"
                item-value="id"
                label="Billing Cycle"
                multiple
                small-chips
                solo
                flat
                class="v-solo-custom"
                :disabled="!weekEndingDate || awaitingResponse"
                :loading="awaitingResponse"
              >
                <template v-slot:item="slotProps">
                  <div
                    class="d-flex align-center"
                    v-bind="slotProps.attrs"
                    v-on="slotProps.on"
                  >
                    <v-checkbox
                      :input-value="
                        invoiceRunParameters.billingCycleIds?.includes(
                          slotProps.item.id,
                        )
                      "
                      hide-details
                      class="ma-0 pa-0"
                    ></v-checkbox>
                    <span class="flex-grow-1">
                      {{ slotProps.item.longName.split(' [')[0] }}
                      <span
                        v-if="slotProps.item.count"
                        class="invoiceCount-label"
                      >
                        ( {{ slotProps.item.count }}
                        {{
                          props.type === AccountingNavigationType.EQUIPMENT_HIRE
                            ? 'Equipment'
                            : 'Jobs'
                        }}
                        Awaiting Invoicing ) </span
                      ><span v-else class="invoiceNoCount-label">
                        (No
                        {{
                          props.type === AccountingNavigationType.EQUIPMENT_HIRE
                            ? 'Equipment'
                            : 'Jobs'
                        }}
                        Available)
                      </span>
                    </span>
                  </div>
                </template>
              </v-select>
            </v-flex>
            <v-checkbox
              v-if="
                type === AccountingNavigationType.RCTI ||
                type === AccountingNavigationType.EQUIPMENT_HIRE
              "
              v-model="invoiceRunParameters.includeAdjustments"
              label="Include Adjustments"
              color="light-blue"
              class="ml-3 mt-2"
            />
          </v-layout>
        </v-form>
      </v-flex>
      <v-flex md12 mt-4>
        <v-divider></v-divider>
        <v-layout my-2 px-2 justify-end>
          <v-btn
            solo
            color="green"
            class="action-btn mt-2"
            @click="confirm"
            :disabled="
              !weekEndingDate || !invoiceRunParameters.billingCycleIds?.length
            "
            >Confirm</v-btn
          >
        </v-layout>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import { returnEndOfDayFromEpoch } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { AccountingNavigationType } from '@/interface-models/Accounting/AccountingNavigationType';
import { CountPerBillingCycle } from '@/interface-models/Accounting/CountPerBillingCycle';
import { InvoiceRunParameters } from '@/interface-models/Accounting/InvoiceRunParameters';
import { billingCycles } from '@/interface-models/Generic/BillingCycles/BillingCycles';
import { useClientInvoiceStore } from '@/store/modules/ClientInvoiceStore';
import {
  computed,
  ComputedRef,
  onBeforeMount,
  Ref,
  ref,
  WritableComputedRef,
} from 'vue';

const emit = defineEmits<{
  (event: 'confirm', payload: InvoiceRunParameters): void;
}>();

const props = defineProps<{
  type: AccountingNavigationType;
}>();

defineExpose({
  initWithParameters,
});

const invoiceRunParameters: Ref<InvoiceRunParameters> = ref({
  billingCycleIds: [],
  weekEndingDate: null,
  processingDate: null,
});

const startInvoiceRunForm: Ref<any> = ref(null);
const syncProcessingDate: Ref<boolean> = ref(true);
const awaitingResponse: Ref<boolean> = ref(false);

const countPerBillingCycle: Ref<CountPerBillingCycle[] | null> = ref(null);

// format billing cycle to show count of awaiting invoices
const formattedBillingCycles = computed(() => {
  return billingCycles.map((cycle) => {
    const count = countPerBillingCycle.value?.find(
      (c) => c.billingCycleId === cycle.id,
    )?.count;
    return {
      id: cycle.id,
      longName: cycle.longName,
      count: count,
    };
  });
});

/**
 * Fetches the awaiting invoice count per billing cycle.
 * sets countPerBillingCycle to response
 */
async function getCountPerBillingCycle(date: number): Promise<void> {
  awaitingResponse.value = true;
  let response: CountPerBillingCycle[] | null = [];
  if (props.type === AccountingNavigationType.CLIENT_INVOICE) {
    response = await useClientInvoiceStore().getCountPerBillingCycle(date);
  }
  if (props.type === AccountingNavigationType.RCTI) {
    response = await useClientInvoiceStore().getRctiCountPerBillingCycle(date);
  }
  if (props.type === AccountingNavigationType.EQUIPMENT_HIRE) {
    response =
      await useClientInvoiceStore().getEquipmentHireCountPerBillingCycle(date);
  }
  if (!response) {
    showNotification(GENERIC_ERROR_MESSAGE);
  }
  countPerBillingCycle.value = response;
  awaitingResponse.value = false;
}

/**
 * Week ending date for the invoice run. If syncProcessingDate is true, this
 * value will also be set as the processing date
 */
const weekEndingDate: WritableComputedRef<number | null> = computed({
  get(): number | null {
    return invoiceRunParameters.value.weekEndingDate ?? 0;
  },
  set(value: number | null): void {
    if (!value) {
      showNotification('Please select a valid date.');
      return;
    }
    if (syncProcessingDateController.value) {
      invoiceRunParameters.value.processingDate = value;
    }
    invoiceRunParameters.value.weekEndingDate = value;

    // reset the selector on date change
    invoiceRunParameters.value.billingCycleIds = [];
    getCountPerBillingCycle(returnEndOfDayFromEpoch(value));
  },
});

/**
 * Getter and setter for the syncProcessingDate value. If true, the processing
 * date will be set to the week ending date. Modelled to checkbox in template.
 */
const syncProcessingDateController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return syncProcessingDate.value;
  },
  set(value: boolean): void {
    if (value && invoiceRunParameters.value.weekEndingDate) {
      invoiceRunParameters.value.processingDate =
        invoiceRunParameters.value.weekEndingDate;
    }
    syncProcessingDate.value = value;
  },
});

/**
 * Title of the dialog. Changes based on the type of invoice run we are starting
 */
const title: ComputedRef<string> = computed(() => {
  switch (props.type) {
    case AccountingNavigationType.CLIENT_INVOICE:
      return 'Start a new Invoice Run';
    case AccountingNavigationType.RCTI:
      return 'Start a new RCTI Run';
    case AccountingNavigationType.EQUIPMENT_HIRE:
      return 'Start a new Equipment Hire Run';
    default:
      return 'Start a new Invoice Run';
  }
});

/**
 * Called when the confirm button is clicked. Validates the form and emits the
 * confirm event with the selected parameters
 */
function confirm() {
  if (!startInvoiceRunForm.value.validate()) {
    return;
  }
  if (!invoiceRunParameters.value.billingCycleIds) {
    showNotification('Please select a billing cycle.');
    return;
  }
  if (
    !invoiceRunParameters.value.weekEndingDate ||
    !invoiceRunParameters.value.processingDate
  ) {
    showNotification('Please select a week ending date and processing date.');
    return;
  }
  emit('confirm', invoiceRunParameters.value);
}

/**
 * Called from parent component to initialize the dialog with default
 * parameters. This occurs when the dialog is opened to edit previously-selected
 * parameters
 * @param params - The parameters to initialize the dialog with
 */
function initWithParameters(params: InvoiceRunParameters) {
  if (
    !!params?.processingDate &&
    params.processingDate !== params.weekEndingDate
  ) {
    syncProcessingDateController.value = false;
  }
  invoiceRunParameters.value = { ...params };
  if (params.weekEndingDate) {
    getCountPerBillingCycle(params.weekEndingDate);
  }
}

onBeforeMount(() => {
  if (
    props.type === AccountingNavigationType.RCTI ||
    props.type === AccountingNavigationType.EQUIPMENT_HIRE
  ) {
    invoiceRunParameters.value.includeAdjustments = true;
  }
});
</script>
<style scoped lang="scss">
.job-booking-form-container {
  overflow-x: hidden;
  overflow-y: auto;
  height: calc(100vh - 110px);
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  .form-container {
    margin-top: 28px;
    padding-left: 28px;
    padding-right: 28px;
  }
}

.new-booking-dialog {
  max-width: 60%;
  background-color: var(--background-color-400);
  box-shadow: var(--box-shadow);
  border-radius: $border-radius-lg;
  border: 1px solid $border-color;

  h1 {
    font-family: $sub-font-family;
    font-size: $font-size-24;
    color: var(--primary-light);
    padding-left: 30px;
    margin-top: 6px;
  }
  p {
    padding-left: 30px;
    font-size: $font-size-18;
    color: var(--light-text-color);
    margin-bottom: 6px;
  }
}

.action-btn {
  border-radius: $border-radius-btn;
  box-shadow: none !important;
  padding: 22px !important;
  margin-right: 14px;
  margin-left: 14px;
  &:hover {
    box-shadow: $box-shadow !important;
  }
}

.invoiceCount-label {
  font-weight: 600;
  color: var(--primary-light);
}
.invoiceNoCount-label {
  font-size: $font-size-13;
  font-weight: 400;
  color: var(--light-text-color);
  opacity: 0.7;
}
</style>
