import InvalidFieldsToolTip from '@/components/admin/Accounting/components/invalid_fields_tool_tip.vue';
import InvalidJobsToolTip from '@/components/admin/Accounting/components/invalid_jobs_tool_tip.vue';
import JobListTable from '@/components/admin/Accounting/components/job_list_table/index.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import JobDetailsDialog from '@/components/operations/OperationDashboard/components/JobDetailsDialog/index.vue';
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { AccountingJobRow } from '@/interface-models/Accounting/AccountingJobRow';
import { ClientInvoiceRow } from '@/interface-models/Accounting/ClientInvoiceRow';
import { InvoiceNavigation } from '@/interface-models/Accounting/InvoiceNavigation';
import { InvoiceStatus } from '@/interface-models/Accounting/InvoiceStatus';
import { TableJobSelection } from '@/interface-models/Accounting/TableJobSelection';
import { DivisionReportSettings } from '@/interface-models/Company/DivisionCustomConfig/DivisionCustomConfig';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { LedgerReportRequest } from '@/interface-models/Reporting/LedgerReportRequest';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { useClientInvoiceStore } from '@/store/modules/ClientInvoiceStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  components: {
    JobDetailsDialog,
    JobListTable,
    ConfirmationDialog,
    InvalidJobsToolTip,
    InvalidFieldsToolTip,
    InformationTooltip,
  },
})
export default class ClientInvoiceTable extends Vue implements IUserAuthority {
  @Prop() public clientInvoices: ClientInvoiceRow[];
  @Prop() public selectedRowId: string | null;
  @Prop() public invoiceType: InvoiceNavigation;
  @Prop({ default: false }) public isLoading: boolean;

  public displayCurrencyValue = DisplayCurrencyValue;

  public clientInvoiceStore = useClientInvoiceStore();

  public company = useCompanyDetailsStore().companyDetails;
  public reportsSettings: DivisionReportSettings | null =
    this.company?.divisions?.[0]?.customConfig?.reports ?? null;

  public $refs!: {
    tableComponent: any;
  };

  get tableHeaders(): TableHeader[] {
    const tableHeaders: TableHeader[] = [
      {
        text: '',
        align: 'left',
        value: '',
        class: 'client-invoice-checkbox-column-header',
        sortable: false,
      },
      {
        text: 'Invoice No.',
        align: 'left',
        value: 'invoiceId',
        sortable: true,
        class: 'client-invoice-invoiceId-column-header',
      },
      {
        text: 'Client',
        align: 'left',
        sortable: this.selectedRowId ? false : true,
        value: 'clientName',
        class: 'client-invoice-name-column-header',
      },
      {
        text: '# Of Jobs',
        align: 'left',
        value: 'numberOfJobs',
        sortable: this.selectedRowId ? false : true,
        class: 'client-invoice-number-of-jobs-column-header',
      },
      {
        text: 'Terms / Cycle',
        align: 'left',
        value: 'tradingTermName',
        sortable: this.selectedRowId ? false : true,
        class: 'client-invoice-tradingTermName-column-header',
      },
      {
        text: 'Due Date',
        align: 'left',
        value: 'dueDate',
        sortable: this.selectedRowId ? false : true,
        class: 'client-invoice-dueDate-column-header',
      },
      {
        text: 'Margin (MIN/OVR/MAX)',
        align: 'left',
        value: 'marginAverage',
        sortable: false,
        class: 'client-invoice-margins-column-header',
      },
      {
        text: 'Freight ($)',
        align: 'right',
        value: 'freightSubtotal',
        sortable: false,
        class: 'client-invoice-freight-column-header',
      },
      {
        text: 'Additional ($)',
        align: 'right',
        value: 'additionalCharges',
        sortable: false,
        class: 'client-invoice-additional-column-header',
      },
      {
        text: 'Fuel Levy (% / $)',
        align: 'right',
        value: 'fuelSurcharge',
        sortable: false,
        class: 'client-invoice-fuel-column-header',
      },
      {
        text: 'GST ($)',
        align: 'right',
        value: 'gst',
        sortable: false,
        class: 'client-invoice-gst-column-cell',
      },
      {
        text: 'Invoiced ($)',
        align: 'right',
        value: 'total',
        sortable: this.selectedRowId ? false : true,
        class: 'client-invoice-invoiced-column-header',
      },

      {
        text: 'Document',
        align: 'right',
        value: '',
        sortable: false,
        class: 'client-invoice-document-column-header',
      },
    ];

    return tableHeaders;
  }

  public isAuthorised(): boolean {
    return hasAdminOrHeadOfficeRole();
  }

  // Utilised for the v-if on table checkboxes
  get selectionIsEnabled() {
    if (
      this.invoiceType === InvoiceNavigation.DUMMY ||
      this.invoiceType === InvoiceNavigation.APPROVED
    ) {
      return false;
    }
    if (this.invoiceType === InvoiceNavigation.READY_FOR_INVOICING) {
      if (!this.invoiceRunStatus) {
        return true;
      }
      if (this.invoiceRunStatus && this.selectedRowId) {
        return true;
      }
      return false;
    }
    if (
      this.invoiceType === InvoiceNavigation.DRAFT &&
      this.selectedRowId !== null
    ) {
      return true;
    }
    return false;
  }

  // returns the current stage our invoice run is at.
  get invoiceRunStatus(): InvoiceStatus | null {
    return this.clientInvoiceStore.invoiceRunStatus;
  }

  // returns the indeterminate state for the overall invoice selection checkbox
  get invoicesPartiallySelected(): boolean {
    const selectedInvoices = this.clientInvoices.filter(
      (x: ClientInvoiceRow) => x.isSelected,
    );
    return (
      selectedInvoices.length > 0 &&
      selectedInvoices.length !== this.clientInvoices.length
    );
  }

  // main v-model controller for our "Select all invoices" checkbox.
  get allInvoicesCheckBox(): boolean {
    for (const invoice of this.clientInvoices) {
      for (const job of invoice.jobList) {
        if (!job.isSelected) {
          return false;
        }
      }
    }
    return this.clientInvoices.length > 0;
  }
  set allInvoicesCheckBox(value: boolean) {
    for (const invoice of this.clientInvoices) {
      const invoiceIsSelected =
        invoice.invalidFields.length > 0 ? false : value;
      invoice.isSelected = invoiceIsSelected;
      invoice.partialSelected =
        invoice.invalidJobIds.length > 0 && invoiceIsSelected ? true : false;
      for (const job of invoice.jobList) {
        if (!invoice.invalidJobIds.includes(job.jobId)) {
          job.isSelected = invoiceIsSelected;
        }
      }
    }
  }

  // returns boolean on whether a client has selected jobs. This is for our indeterminate icon on our checkbox.
  public clientHasSelectedJobs(clientId: string): boolean {
    const clientInvoice = this.clientInvoices.find(
      (x: ClientInvoiceRow) => x.clientId === clientId,
    );
    if (!clientInvoice) {
      return false;
    }
    const selectedJobs = clientInvoice.jobList.filter(
      (x: AccountingJobRow) => x.isSelected,
    );
    return (
      selectedJobs.length > 0 &&
      selectedJobs.length !== clientInvoice.jobList.length
    );
  }

  public generatingInvoices: Record<string, boolean> = {};
  /**
   * Dispatches request to generate the invoice using its ledger id.
   * @param ledgerId  The ledger id of the invoice to generate.
   * @param accessMethod  The ReportAccess email/Download of the invoice to generate.
   */
  public viewInvoiceDocument(
    ledgerId: string,
    accessMethod: ReportAccessMethodTypes,
  ) {
    const request: LedgerReportRequest = {
      ledgerId: ledgerId,
      accessType: accessMethod,
    };
    this.$set(this.generatingInvoices, ledgerId, true);

    this.clientInvoiceStore.generateClientInvoice(request).finally(() => {
      this.$set(this.generatingInvoices, ledgerId, false);
    });
  }

  // Emits the selected clientId back to parent component.
  public setSelectedClient(row: ClientInvoiceRow): void {
    this.$emit(
      'update:selectedRowId',
      row.rowId !== this.selectedRowId ? row.rowId : null,
    );

    if (this.invoiceRunStatus) {
      this.$emit('resetClientInvoiceSelections');
    }
  }

  public confirmSendingOfSingleInvoice(ledgerId: string) {
    this.clientInvoiceStore.resendInvoiceForLedgerId(ledgerId);
  }

  // emit from child job list component. Emit event actioned by the global job list checkbox. Updates clientInvoices data with selection.
  public setJobsCheckBoxForClient(jobSelection: TableJobSelection): void {
    if (!jobSelection.clientId) {
      return;
    }
    const clientId: string = jobSelection.clientId;
    const setAllJobs: boolean = jobSelection.setAllJobs;
    const clientInvoice: ClientInvoiceRow | undefined =
      this.clientInvoices.find(
        (x: ClientInvoiceRow) => x.clientId === clientId,
      );
    if (!clientInvoice || clientInvoice.invalidFields.length > 0) {
      return;
    }
    clientInvoice.isSelected = setAllJobs;
    for (const job of clientInvoice.jobList) {
      job.isSelected = setAllJobs;
    }
    this.setPartialSelected();
  }

  // Updates clientInvoices data with selections. Similar to "setJobsCheckBoxForClient". The reason we have this is because emits only allow one arg. Where as the vuetify checkbox requires $event and clientId in this component
  public setAllJobsSelectionOnSingleClientInvoice(
    value: boolean,
    clientId: string,
  ): void {
    const clientInvoice = this.clientInvoices.find(
      (x: ClientInvoiceRow) => x.clientId === clientId,
    );
    if (!clientInvoice) {
      return;
    }

    for (const job of clientInvoice.jobList) {
      if (!clientInvoice.invalidJobIds.includes(job.jobId)) {
        job.isSelected = value;
      }
    }
    this.setPartialSelected();
  }

  // Sets indeterminate on invoice table checkboxes and expanded job list
  public setPartialSelected(): void {
    for (const clientInvoice of this.clientInvoices) {
      const selectedJobs = clientInvoice.jobList.filter(
        (x: AccountingJobRow) => x.isSelected,
      );

      if (selectedJobs.length === clientInvoice.jobList.length) {
        clientInvoice.isSelected = true;
        clientInvoice.partialSelected = false;
      }

      if (selectedJobs.length === 0) {
        clientInvoice.isSelected = false;
        clientInvoice.partialSelected = false;
      }

      if (
        selectedJobs.length > 0 &&
        selectedJobs.length !== clientInvoice.jobList.length
      ) {
        clientInvoice.partialSelected = true;
        clientInvoice.isSelected = true;
      }
    }
  }

  // returns the total number of selected jobs for a single invoice. called from html template.
  public getNumberOfJobsSelected(clientId: string): number {
    const clientInvoice: ClientInvoiceRow | undefined =
      this.clientInvoices.find(
        (x: ClientInvoiceRow) => x.clientId === clientId,
      );
    if (!clientInvoice) {
      return 0;
    }
    const selectedJobs = clientInvoice.jobList.filter(
      (x: AccountingJobRow) => x.isSelected,
    );
    return selectedJobs.length;
  }
}
