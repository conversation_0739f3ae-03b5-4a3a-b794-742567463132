<v-layout class="client-invoice-table">
  <div class="table-header-left-action-icon-container">
    <v-checkbox
      v-if="selectionIsEnabled && invoiceRunStatus !== 'DRAFT'"
      @click.native.stop
      :indeterminate="invoicesPartiallySelected"
      v-model="allInvoicesCheckBox"
      hide-details
      :ripple="false"
      :disabled="selectedRowId !== null || clientInvoices.length === 0 || isLoading"
      color="info"
    ></v-checkbox>
  </div>
  <v-data-table
    ref="tableComponent"
    :headers="tableHeaders"
    item-key="rowId"
    :items="clientInvoices"
    :rows-per-page-items="[15, 20]"
    hide-actions
    :no-data-text="isLoading ? 'Loading...' : 'No Invoices are ready to run for the selected week ending date.'"
    :loading="isLoading"
    class="default-table-dark client-invoice-accounting-table gd-dark-theme"
    :class="[{'client-invoice-accounting-table-client-selected': selectedRowId}]"
  >
    <template v-slot:items="props">
      <tr
        @click="setSelectedClient(props.item); props.expanded = !props.expanded;"
        v-show="selectedRowId === null || selectedRowId === props.item.rowId"
      >
        <td
          class="inner-table__cell checkbox-type client-invoice-checkbox-column-cell"
        >
          <v-checkbox
            v-if="selectionIsEnabled"
            @click.native.stop
            v-model="props.item.isSelected"
            hide-details
            :disabled="props.item.invalidFields.length > 0 || props.item.jobList.length === props.item.invalidJobIds.length || isLoading"
            :indeterminate="props.item.partialSelected"
            @change="setAllJobsSelectionOnSingleClientInvoice($event, props.item.clientId)"
            :ripple="false"
            color="info"
          ></v-checkbox>

          <v-icon size="18" v-if="props.item.status === 'SENT'" color="success">
            fas fa-check-circle</v-icon
          >
          <v-tooltip
            bottom
            v-if="invoiceType === 'APPROVED' && invoiceRunStatus === 'SENT' && props.item.status === 'FAIL'"
          >
            <template v-slot:activator="{ on: tooltip }">
              <div v-on="{ ...tooltip }">
                <ConfirmationDialog
                  buttonText="Email"
                  :message="'Please confirm that you wish to manually email invoice ' + props.item.invoiceId + ' (' + props.item.clientName + ') to ' + props.item.toEmailIds.join(', ')"
                  :title="'Confirm Manual Emailing'"
                  @confirm="confirmSendingOfSingleInvoice(props.item.ledgerId)"
                  :buttonColor="'error'"
                  :isIcon="true"
                  :iconSize="18"
                  :buttonDisabled="props.item.toEmailIds.length < 1 || !isAuthorised()"
                  :faIconName="'far fa-envelope'"
                  :confirmationButtonText="'Confirm'"
                  :dialogIsActive="true"
                  :noButtonMargin="true"
                >
                </ConfirmationDialog>
              </div>
            </template>
            <span v-if="props.item.toEmailIds.length > 0">Resend Email</span>
            <span v-if="props.item.toEmailIds.length < 1"
              >No Email Address Available</span
            >
          </v-tooltip>
        </td>

        <td
          class="inner-table__cell string-type client-invoice-invoiceId-column-cell"
        >
          <span class="pr-2"> {{props.item.invoiceId}}</span>

          <InformationTooltip
            :right="true"
            tooltipType="info"
            v-if="invoiceType !== 'READY_FOR_INVOICING'"
          >
            <v-layout slot="content" row wrap>
              <v-flex md12>
                <p class="mb-1" slot="content">
                  This invoice will be emailed to:
                </p>

                <ul>
                  <li v-for="emailAddress of props.item.toEmailIds">
                    {{emailAddress}}
                  </li>
                </ul>
              </v-flex>
            </v-layout>
          </InformationTooltip>
        </td>
        <td
          class="inner-table__cell string-type client-invoice-name-column-cell"
        >
          <v-layout style="position: relative">
            <div class="invalid-client-exclamation">
              <InvalidFieldsToolTip
                :ledgerType="'CLIENT_INVOICE'"
                :invalidFields="props.item.invalidFields"
                v-if="props.item.invalidFields.length > 0"
              />
            </div>
            {{props.item.clientName}} {{props.item.clientId.substring(0,2) !==
            'CS' ? (' - ' + props.item.clientId) : ""}}
          </v-layout>
        </td>
        <td
          class="inner-table__cell string-type client-invoice-number-of-jobs-column-cell"
        >
          <v-layout>
            <div class="invalid-jobs-exclamation pr-2">
              <InvalidJobsToolTip
                v-if="props.item.invalidJobIds.length > 0"
                :invalidJobIds="props.item.invalidJobIds"
              />
            </div>
            <v-layout
              justify-left
              v-if="invoiceRunStatus !== 'DUMMY' && invoiceRunStatus !== 'APPROVED'"
            >
              <div class="number-of-jobs-selected-container">
                <div class="selected-jobs-selected-container">
                  <span>{{getNumberOfJobsSelected(props.item.clientId)}}</span>
                </div>

                <div class="selected-of-container">
                  <span>of</span>
                </div>

                <div class="total-number-of-jobs-container">
                  <span>{{props.item.numberOfJobs}} </span>
                </div>
              </div>
            </v-layout>

            <span
              v-if="invoiceRunStatus === 'DUMMY' || invoiceRunStatus === 'APPROVED'"
              >{{props.item.numberOfJobs}}
            </span>
          </v-layout>
        </td>
        <td
          class="inner-table__cell text-left client-invoice-tradingTermName-column-cell"
        >
          {{props.item.tradingTermName}} / {{props.item.billingCycleName}}
        </td>
        <td
          class="inner-table__cell text-left client-invoice-dueDate-column-cell"
        >
          {{props.item.dueDate}}
        </td>
        <td class="inner-table__cell client-invoice-margins-column-cell">
          <table class="segmented-cell">
            <tr>
              <td class="px-0">
                <v-layout align-center justify-center>
                  {{props.item.marginMin}}
                  <v-icon size="10" color="red" class="px-1"
                    >fas fa-caret-down</v-icon
                  >
                </v-layout>
              </td>
              <td class="px-1">{{props.item.marginAverage}}</td>
              <td class="px-0">
                <v-layout justify-center align-center>
                  {{props.item.marginMax}}
                  <v-icon size="10" color="green" class="px-1"
                    >fas fa-caret-up</v-icon
                  >
                </v-layout>
              </td>
            </tr>
          </table>
        </td>
        <td
          class="inner-table__cell integer-type client-invoice-freight-column-cell"
        >
          {{props.item.freightSubtotal}}
        </td>
        <td
          class="inner-table__cell integer-type client-invoice-additional-column-cell"
        >
          {{props.item.additionalCharges}}
        </td>
        <td
          class="inner-table__cell integer-type client-invoice-fuel-column-cell"
        >
          {{props.item.fuelSurcharge}}
        </td>
        <td
          class="inner-table__cell integer-type client-invoice-gst-column-header"
        >
          {{props.item.gst}}
        </td>
        <td
          class="inner-table__cell integer-type client-invoice-invoiced-column-cell"
          style="padding-right: 25px !important"
        >
          {{displayCurrencyValue(props.item.total)}}
        </td>

        <td class="text-right client-invoice-document-column-header">
          <div>
            <v-tooltip bottom class="pr-2">
              <template v-slot:activator="{ on: tooltip }">
                <v-btn
                  v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  'DOWNLOAD',
                )
              "
                  flat
                  @click.native.stop="viewInvoiceDocument(props.item.ledgerId, 'DOWNLOAD')"
                  icon
                  color="accent"
                  v-on="{ ...tooltip }"
                  class="ma-0"
                  :disabled="generatingInvoices[props.item.ledgerId] || invoiceType === 'READY_FOR_INVOICING'"
                >
                  <v-icon size="24"> downloading </v-icon>
                </v-btn>
              </template>
              <span>Download Invoice</span>
            </v-tooltip>
            <v-tooltip bottom class="pl-2">
              <template v-slot:activator="{ on: tooltip }">
                <v-btn
                  v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  'EMAIL',
                )
              "
                  flat
                  @click.native.stop="viewInvoiceDocument(props.item.ledgerId, 'EMAIL')"
                  icon
                  color="orange"
                  v-on="{ ...tooltip }"
                  class="ma-0"
                  :disabled="generatingInvoices[props.item.ledgerId] || invoiceType === 'READY_FOR_INVOICING'"
                >
                  <v-icon size="24"> forward_to_inbox </v-icon>
                </v-btn>
              </template>
              <span>Email Preview</span>
            </v-tooltip>
          </div>
        </td>
      </tr>
    </template>
    <template v-slot:expand="props">
      <div class="inner-table__container" id="inner-table__container">
        <v-layout class="inner-table__container--row first-row">
          <v-flex md12>
            <JobListTable
              :jobList="props.item.jobList"
              :isInnerTable="true"
              :isClient="true"
              :clientId="props.item.clientId"
              @setJobsCheckBoxForClient="setJobsCheckBoxForClient"
              @setPartialSelected="setPartialSelected"
              :selectionIsEnabled="selectionIsEnabled && props.item.invalidFields.length < 1"
              :invalidJobIds="props.item.invalidJobIds"
              :invalidClient="props.item.invalidFields.length > 0"
              :isClientNameVisible="false"
            ></JobListTable>
          </v-flex>
        </v-layout>
      </div>
    </template>

    <template v-slot:no-data>
      <v-layout justify-center>
        <span v-if="isLoading">Loading...</span>
        <span v-if="!isLoading">No Invoices Available.</span>
      </v-layout>
    </template>
  </v-data-table>

  <div
    class="hide-non-expanded-rows-container"
    v-if="selectedRowId !== null"
  ></div>
</v-layout>
