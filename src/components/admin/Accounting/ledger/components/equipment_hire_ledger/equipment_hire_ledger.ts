import LedgerTable from '@/components/admin/Accounting/ledger/components/ledger_table/index.vue';
import DateTimeInputs from '@/components/common/date-picker/date_time_inputs.vue';
import { getLedgerDetailsTableData } from '@/helpers/AccountingHelpers/AccountingHelpers';
import { LedgerDetails } from '@/interface-models/Accounting/LedgerDetails';
import { LedgerPaginationRequest } from '@/interface-models/Accounting/LedgerPaginationRequest';
import { LedgerTableRow } from '@/interface-models/Accounting/LedgerTableRow';
import { LedgerType } from '@/interface-models/Accounting/LedgerType';
import LedgerItemsResponse from '@/interface-models/Generic/Accounting/Ledgers/LedgerItemsResponse';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { Component, Vue } from 'vue-property-decorator';
@Component({
  components: { DateTimeInputs, LedgerTable },
})
export default class GeneralLedger extends Vue {
  public totalLedgerCount: number = 0;
  public ledgerDetailsList: LedgerDetails[] = [];
  public isLoading: boolean = false;

  public setRequestedLedgerItems(
    requestedLedgerItems: LedgerItemsResponse | null,
  ) {
    if (!requestedLedgerItems) {
      this.ledgerDetailsList = [];
      this.totalLedgerCount = 0;
      return;
    }
    this.totalLedgerCount = requestedLedgerItems.totalRecords;
    this.ledgerDetailsList = requestedLedgerItems.ledgerDetailsList;
    this.setLoading(false);
  }

  get ledgerTableList(): LedgerTableRow[] {
    return getLedgerDetailsTableData(this.ledgerDetailsList);
  }

  public async getSubcontractorLedgerItems(
    ledgerRequest: LedgerPaginationRequest,
  ) {
    this.setLoading(true);
    ledgerRequest.ledgerType = [
      LedgerType.EQUIPMENT_HIRE,
      LedgerType.EQUIPMENT_HIRE_RC_ADJUSTMENT_NOTE,
    ];
    // Make request and handle response
    this.setRequestedLedgerItems(
      await useCompanyDetailsStore().requestLedgerItems(ledgerRequest),
    );
  }

  public setLoading(value: boolean) {
    this.isLoading = value;
  }

  public mounted() {
    this.getSubcontractorLedgerItems(
      new LedgerPaginationRequest(
        1,
        50,
        [
          LedgerType.EQUIPMENT_HIRE,
          LedgerType.EQUIPMENT_HIRE_RC_ADJUSTMENT_NOTE,
        ],
        false,
      ),
    );
  }
}
