<v-layout wrap>
  <v-flex md12>
    <v-form ref="form">
      <v-layout class="py-2" align-center>
        <div class="search-title-text-container pr-3">
          <span class="actions-bar--header">Search Ledger</span>
        </div>

        <v-layout wrap>
          <v-flex md3 grow>
            <v-select
              solo
              flat
              label="Search By"
              v-model="selectedSearchByMenuId"
              :items="searchByOptions"
              item-value="id"
              background-color="light-blue"
              item-text="longName"
              color="blue"
              class="v-solo-custom"
              :disabled="isEntitySpecificSearch"
              hint="Search by"
              persistent-hint
            ></v-select>
          </v-flex>

          <v-flex md3 px-2>
            <v-text-field
              solo
              flat
              v-if="selectedSearchByMenuId === 1"
              label="Invoice Number"
              hint="Enter an Invoice Number to search for"
              persistent-hint
              v-model="searchLedgerRequest.invoiceRef"
              class="v-solo-custom"
              :rules="[validate.required]"
            >
            </v-text-field>
            <SelectEntity
              v-if="selectedSearchByMenuId === 3"
              :entityTypes="[entityType.CLIENT]"
              :id.sync="searchLedgerRequest.clientId"
              :rules="[validate.required]"
              :cashSaleClient="true"
              :disabled="isEntitySpecificSearch"
            />

            <SelectEntity
              v-if="selectedSearchByMenuId === 4"
              :entityTypes="[entityType.FLEET_ASSET_OWNER]"
              :id.sync="searchLedgerRequest.fleetAssetOwnerId"
              :rules="[validate.required]"
              :disabled="isEntitySpecificSearch"
            />

            <v-text-field
              solo
              flat
              type="number"
              v-if="selectedSearchByMenuId === 8"
              label="Job #"
              hint="Enter a Job Number to search for"
              persistent-hint
              class="v-solo-custom"
              v-model="searchLedgerRequest.jobId"
              :rules="[validate.required]"
            >
            </v-text-field>
          </v-flex>

          <v-flex
            md4
            v-if="selectedSearchByMenuId === 3 || selectedSearchByMenuId === 4"
            style="position: relative"
          >
            <v-layout align-center>
              <v-flex md12>
                <DateTimeInputs
                  :epochTime.sync="searchLedgerRequest.endEpoch"
                  :enableValidation="true"
                  type="DATE_END_OF_DAY"
                  dateLabel="Week Ending Date"
                  :soloInput="true"
                  :boxInput="false"
                  hintTextType="LABEL"
                  datePrefix="Date:"
                  :isRequired="!isEntitySpecificSearch"
                ></DateTimeInputs>
              </v-flex>
              <div class="mb-4 pl-2">
                <v-icon
                  size="22"
                  v-if="searchLedgerRequest.endEpoch !== null"
                  @click="clearDate"
                  class="reset-date"
                  >fal fa-times</v-icon
                >
              </div>
            </v-layout>
          </v-flex>

          <v-flex
            md2
            px-1
            v-if="selectedSearchByMenuId !== 3 && selectedSearchByMenuId !== 4"
          >
            <DateTimeInputs
              :epochTime.sync="searchLedgerRequest.startEpoch"
              :enableValidation="true"
              type="DATE_START_OF_DAY"
              dateLabel="Start Date"
              :soloInput="true"
              :boxInput="false"
              :readOnly="isDisabledDateRange"
              datePrefix="Start:"
              hintTextType="LABEL"
              :isRequired="isDisabledDateRange ? false : true"
            ></DateTimeInputs>
          </v-flex>
          <v-flex
            md2
            px-1
            v-if="selectedSearchByMenuId !== 3 && selectedSearchByMenuId !== 4"
          >
            <DateTimeInputs
              :epochTime.sync="searchLedgerRequest.endEpoch"
              :enableValidation="true"
              type="DATE_END_OF_DAY"
              dateLabel="End Date"
              :soloInput="true"
              :boxInput="false"
              :readOnly="isDisabledDateRange"
              datePrefix="End:"
              hintTextType="LABEL"
              :isRequired="isDisabledDateRange ? false : true"
            ></DateTimeInputs>
          </v-flex>

          <v-flex md2 px-1 pl-3>
            <v-btn
              block
              :disabled="selectedLedgerId !== null"
              @click="searchLedger"
              v-shortkey.once="['enter']"
              @shortkey="searchLedger()"
              class="view-details-button"
              >Run Query</v-btn
            >
          </v-flex>
        </v-layout>
      </v-layout>
    </v-form>
  </v-flex>

  <v-flex md12>
    <LedgerTable
      :ledgerTableList="ledgerTableList"
      :isLoading="isLoading"
      :isSearchLedger="true"
      :totalLedgerCount="totalLedgerCount"
      @getLedgerDetailsList="getGeneralLedgerItems"
      @setSelectedLedger="setSelectedLedger"
      :selectedLedgerId="selectedLedgerId"
      :jobList="jobList"
      :isInDialog="isInDialog"
    />
  </v-flex>
</v-layout>
