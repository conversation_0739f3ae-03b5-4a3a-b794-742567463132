import LedgerTable from '@/components/admin/Accounting/ledger/components/ledger_table/index.vue';
import DateTimeInputs from '@/components/common/date-picker/date_time_inputs.vue';
import SelectEntity from '@/components/common/inputs/select_entity/index.vue';
import {
  getAccountingJobListTableData,
  getLedgerDetailsTableData,
} from '@/helpers/AccountingHelpers/AccountingHelpers';
import { generateAccountingJobSummary } from '@/helpers/JobDataHelpers/JobDataHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { initialiseJobDetails } from '@/helpers/classInitialisers/InitialiseJobDetails';
import { AccountingJobRow } from '@/interface-models/Accounting/AccountingJobRow';
import { LedgerDetails } from '@/interface-models/Accounting/LedgerDetails';
import { LedgerPaginationRequest } from '@/interface-models/Accounting/LedgerPaginationRequest';
import { LedgerTableRow } from '@/interface-models/Accounting/LedgerTableRow';
import { LedgerType } from '@/interface-models/Accounting/LedgerType';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import { LedgerTableRowSelection } from '@/interface-models/Generic/Accounting/Ledgers/LedgerTableRowSelection';
import SearchLedgerRequest from '@/interface-models/Generic/Accounting/Ledgers/SearchLedgerRequest';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { SearchByOption } from '@/interface-models/Generic/SearchByOption';
import { VForm } from '@/interface-models/Generic/VForm/VForm';
import { Validation } from '@/interface-models/Generic/Validation';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { useClientPortalStore } from '@/store/modules/ClientPortalStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useRootStore } from '@/store/modules/RootStore';
import { sessionManager } from '@/store/session/SessionState';
import moment from 'moment-timezone';
import { nextTick } from 'vue';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';

@Component({
  components: {
    DateTimeInputs,
    LedgerTable,
    SelectEntity,
  },
})
export default class SearchLedger extends Vue {
  @Prop({ default: null }) public clientId: string | null;
  @Prop({ default: null }) public fleetAssetOwnerId: string | null;

  public entityType = EntityType;
  public totalLedgerCount: number = 0;
  public ledgerDetailsList: LedgerDetails[] = [];
  public selectedSearchByMenuId: number = 1;
  public ledgerItems: LedgerDetails[] = [];
  public isPdfDownload: boolean = false;
  public requestingCsv: boolean = false;
  public tableSearch: string = '';
  public isLoading: boolean = false;
  public selectedLedgerId: string | null = null;
  public jobList: AccountingJobRow[] = [];
  public searchedJobsLedgerType: LedgerType = LedgerType.CLIENT_INVOICE;

  public searchLedgerRequest: SearchLedgerRequest = new SearchLedgerRequest();

  public searchByOptions: SearchByOption[] = [
    {
      id: 1,
      longName: 'Invoice Number',
      keyName: 'invoiceRef',
    },
    {
      id: 5,
      longName: 'Week Ending',
      keyName: 'isWeekEndingDate',
    },
    {
      id: 7,
      longName: 'Due Date',
      keyName: 'isDueDate',
    },
    // {
    // id: 3 'client' added if not client portal check OnMounted
    // },
    // {
    //   id: 4 'Subcontractor' added if not client portal check OnMounted
    // },
    {
      id: 8,
      longName: 'Job #',
      keyName: 'jobId',
    },

    {
      id: 6,
      longName: 'Created Date',
      keyName: 'isCreatedDate',
    },
  ];

  /**
   * Returns true if the search clientId or fleetAssetOwnerId is not null. This
   * is the case when the SearchLedger components is used within the Client
   * Administration or Fleet Asset Owner Administration pages.
   */
  get isEntitySpecificSearch(): boolean {
    return !!this.clientId || !!this.fleetAssetOwnerId;
  }

  public $refs!: {
    form: VForm;
  };

  get fleetAssetOwnerList(): FleetAssetOwnerSummary[] {
    return useFleetAssetOwnerStore().getOwnerList;
  }

  get ledgerTableList(): LedgerTableRow[] {
    return getLedgerDetailsTableData(this.ledgerDetailsList);
  }
  public setLoading(value: boolean) {
    this.isLoading = value;
  }

  public getGeneralLedgerItems(ledgerRequest: LedgerPaginationRequest) {
    this.searchLedgerRequest.page = ledgerRequest.page;
    this.searchLedgerRequest.size = ledgerRequest.size;
    this.setLoading(true);
    this.searchLedger();
  }

  public mounted() {
    // Conditionally add the "Client" option if not client portal
    if (!sessionManager.isClientPortal()) {
      this.searchByOptions.push(
        {
          id: 3,
          longName: 'Client',
          keyName: 'clientId',
        },
        {
          id: 4,
          longName: 'Subcontractor',
          keyName: 'fleetAssetOwnerId',
        },
      );
    }
    // Arrange by ID
    this.searchByOptions.sort((a, b) => {
      const idA = typeof a.id === 'number' ? a.id : 0;
      const idB = typeof b.id === 'number' ? b.id : 0;
      return idA - idB;
    });

    // If clientId or fleetAssetId is provided as a prop, it means this
    // component instance is only meant for searching that entity's ledgers. Set
    // the default properties in the request and send the initial search request
    if (this.isEntitySpecificSearch) {
      this.selectedSearchByMenuId = this.clientId ? 3 : 4;
      this.searchLedgerRequest.clientId = this.clientId ? this.clientId : '';
      this.searchLedgerRequest.fleetAssetOwnerId = this.fleetAssetOwnerId
        ? this.fleetAssetOwnerId
        : '';

      // Use nextTick to ensure the child components are mounted so validation
      // will pass
      nextTick(() => {
        this.searchLedger();
      });
    }
  }

  public clearDate() {
    this.searchLedgerRequest.endEpoch = null;
  }

  /**
   * Validates the form, sends a request to get ledger details, and then updates
   * the ledger details list and total count based on the response. If the
   * response is null, it resets the ledger details list and total count.
   */
  public async searchLedger() {
    // Validate the form
    if (!this.$refs.form.validate()) {
      return;
    }
    this.setLoading(true);
    this.setRequiredSearch();

    // Request ledger details
    const results = await useRootStore().getLedgerSearchQuery(
      this.searchLedgerRequest,
    );
    // Update ledger details list and total count based on the response
    this.ledgerDetailsList = results ? results.ledgerDetailsList : [];
    this.totalLedgerCount = results ? results.totalRecords : 0;
    this.setLoading(false);
  }

  get isDisabledDateRange() {
    return (
      this.selectedSearchByMenuId === 1 ||
      this.selectedSearchByMenuId === 2 ||
      this.selectedSearchByMenuId === 3 ||
      this.selectedSearchByMenuId === 4 ||
      this.selectedSearchByMenuId === 8 ||
      this.selectedSearchByMenuId === 9
    );
  }

  @Watch('isDisabledDateRange')
  public resetDateRangeEntries(isDisabled: boolean) {
    if (isDisabled) {
      this.searchLedgerRequest.startEpoch = null;
      this.searchLedgerRequest.endEpoch = null;
    } else {
      this.setDefaultDateRange();
    }
  }

  get isInDialog() {
    return this.clientId !== null || this.fleetAssetOwnerId !== null;
  }

  // we reset old search values back to string in this.searchJobRequest
  public setRequiredSearch() {
    const searchByName = this.searchByOptions.find(
      (x: SearchByOption) => x.id === this.selectedSearchByMenuId,
    );
    if (!searchByName) {
      return;
    }
    switch (this.selectedSearchByMenuId) {
      case 1:
        this.setNonDateRangeRequest('invoiceRef');
        break;
      case 2:
        this.searchLedgerRequest.isOverDue = true;
        this.setNonDateRangeRequest('isOverDue');
        break;
      case 3:
        this.setEntityWithDateRequest('clientId');
        break;
      case 4:
        this.setEntityWithDateRequest('fleetAssetOwnerId');
        break;
      case 5:
        this.searchLedgerRequest.isWeekEndingDate = true;
        this.setDateRangeRequest('isWeekEndingDate');
        break;
      case 6:
        this.searchLedgerRequest.isCreatedDate = true;
        this.setDateRangeRequest('isCreatedDate');
        break;
      case 7:
        this.searchLedgerRequest.isDueDate = true;
        this.setDateRangeRequest('isDueDate');
        break;
      case 8:
        this.setNonDateRangeRequest('jobId');
        break;
    }
  }

  public setNonDateRangeRequest(key: string): void {
    Object.keys(this.searchLedgerRequest).forEach((v: string) => {
      if (v !== key && v !== 'page' && v !== 'size') {
        (this.searchLedgerRequest as any)[v] = null;
      }
    });
  }

  public setEntityWithDateRequest(key: string): void {
    Object.keys(this.searchLedgerRequest).forEach((v: string) => {
      if (v !== 'endEpoch' && v !== 'page' && v !== 'size' && v !== key) {
        (this.searchLedgerRequest as any)[v] = null;
      }
    });
  }

  public setDateRangeRequest(key: string): void {
    Object.keys(this.searchLedgerRequest).forEach((v: string) => {
      if (
        v !== 'startEpoch' &&
        v !== 'endEpoch' &&
        v !== 'page' &&
        v !== 'size' &&
        v !== key
      ) {
        (this.searchLedgerRequest as any)[v] = null;
      }
    });
  }

  // sets the date range when it is required. The range is 1 week.
  public setDefaultDateRange() {
    const companyDetailsStore = useCompanyDetailsStore();
    const currentTime = moment.tz(companyDetailsStore.userLocale).valueOf();
    const yearAgo = moment
      .tz(companyDetailsStore.userLocale)
      .subtract(1, 'week')
      .valueOf();
    this.searchLedgerRequest.startEpoch = yearAgo;
    this.searchLedgerRequest.endEpoch = currentTime;
  }

  public setSelectedLedger(ledgerTableRowSelection: LedgerTableRowSelection) {
    if (this.selectedLedgerId === ledgerTableRowSelection.ledgerId) {
      this.selectedLedgerId = null;
      this.jobList = [];
      this.searchedJobsLedgerType = LedgerType.CLIENT_INVOICE;
      return;
    }
    this.selectedLedgerId = ledgerTableRowSelection.ledgerId;
    this.searchedJobsLedgerType = ledgerTableRowSelection.type;
    this.searchForJobs(ledgerTableRowSelection.jobIds);
  }

  get validate(): Validation {
    return validationRules;
  }

  /**
   * Searches for full JobDetails documents using the provided list of jobIds,
   * converts to accounting summaries and sets to jobList
   * @param jobIds jobIds to query for
   */
  public async searchForJobs(jobIds: number[]) {
    this.setLoading(true);
    let results: JobDetails[] | null = null;
    if (sessionManager.isClientPortal()) {
      results = await useClientPortalStore().getJobDetailsForJobIds(jobIds);
    } else {
      results = await useJobStore().getJobDetailsForJobIds(jobIds);
    }
    if (results) {
      const accountingJobSummaryList = results.map((x: JobDetails) => {
        return generateAccountingJobSummary(initialiseJobDetails(x));
      });

      const isClient =
        this.searchedJobsLedgerType === LedgerType.CLIENT_INVOICE
          ? true
          : this.searchedJobsLedgerType === LedgerType.RCTI
            ? false
            : true;
      this.jobList = getAccountingJobListTableData(
        accountingJobSummaryList,
        isClient,
      );
      this.setLoading(false);
    }
  }
}
