import TableHeader from '@/interface-models/Generic/Table/TableHeader';

import JobDetailsDialog from '@/components/operations/OperationDashboard/components/JobDetailsDialog/index.vue';
import { AccountingJobRow } from '@/interface-models/Accounting/AccountingJobRow';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { Component, Prop, Vue } from 'vue-property-decorator';

import { LedgerTableRow } from '@/interface-models/Accounting/LedgerTableRow';

import JobListTable from '@/components/admin/Accounting/components/job_list_table/index.vue';
import Pagination from '@/components/common/pagination/index.vue';
import { LedgerPaginationRequest } from '@/interface-models/Accounting/LedgerPaginationRequest';
import { LedgerTableRowSelection } from '@/interface-models/Generic/Accounting/Ledgers/LedgerTableRowSelection';

import { LedgerType } from '@/interface-models/Accounting/LedgerType';
import { VPagination } from '@/interface-models/Generic/VPagination';

import { DivisionReportSettings } from '@/interface-models/Company/DivisionCustomConfig/DivisionCustomConfig';
import GenerateReportResponse from '@/interface-models/Reporting/GenerateReportResponse';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { useClientInvoiceStore } from '@/store/modules/ClientInvoiceStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useEquipmentHireInvoiceStore } from '@/store/modules/EquipmentHireInvoiceStore';
import { useFleetAssetOwnerInvoiceStore } from '@/store/modules/FleetAssetOwnerInvoiceStore';
import { useInvoiceAdjustmentStore } from '@/store/modules/InvoiceAdjustmentStore';
import { sessionManager } from '@/store/session/SessionState';
@Component({
  components: { Pagination, JobDetailsDialog, JobListTable },
})
export default class LedgerTable extends Vue {
  @Prop() public ledgerTableList: LedgerTableRow[];
  @Prop() public isLoading: boolean;
  @Prop({ default: false }) public isSearchLedger: boolean;
  @Prop() public totalLedgerCount: number;
  @Prop({ default: null }) public selectedLedgerId: string | null;
  @Prop({ default: () => [] }) public jobList: AccountingJobRow[];
  @Prop({ default: false }) public isInDialog: boolean;
  @Prop({ default: true }) public expandableTable: boolean;

  public invoiceAdjustmentStore = useInvoiceAdjustmentStore();
  public operationsStore = useOperationsStore();
  public isClientPortal: boolean = sessionManager?.isClientPortal() || false;
  public generatingInvoices: boolean = false;

  public company = useCompanyDetailsStore().companyDetails;
  public reportsSettings: DivisionReportSettings | null =
    this.company?.divisions?.[0]?.customConfig?.reports ?? null;

  public page: number = 1;
  public rowsPerPage: number = 50;

  get tableHeaders(): TableHeader[] {
    const tableHeaders: TableHeader[] = [
      {
        text: 'Week Ending',
        align: 'left',
        value: 'weekEndingDate',
        sortable: false,
        class: 'ledger-weekEnding-column-header',
      },
      {
        text: 'Sent',
        align: 'left',
        value: 'emailedDate',
        sortable: false,
        class: 'ledger-sent-column-header',
      },
      {
        text: 'Invoice Number',
        align: 'left',
        sortable: false,
        value: 'invoiceNumber',
        class: 'ledger-invoiceNumber-column-header',
      },
      {
        text: 'Type',
        align: 'left',
        sortable: false,
        value: 'type',
        class: 'type-column-header',
      },
      {
        text: 'Due Date',
        align: 'left',
        value: 'dueDate',
        sortable: false,
        class: 'ledger-dueDate-column-header',
      },
      {
        text: this.isClientPortal ? 'Credit' : 'Debit', //CP DEBIT AS CREDITS
        align: 'right',
        value: 'debit',
        sortable: false,
        class: 'job-list-fuel-column-header',
      },
      {
        text: this.isClientPortal ? 'Charges' : 'Credit', //CP CREDITS AS CHARGES
        align: 'right',
        value: 'credit',
        sortable: false,
        class: 'job-list-additional-column-header',
      },

      {
        text: 'Action',
        align: 'right',
        value: '',
        sortable: false,
        class: 'job-list-gst-column-header',
      },
    ];
    // Add the 'Fleet' column for client portal users
    // Add the 'Fleet' column after 'Client' and before 'Service'
    if (!this.isClientPortal) {
      const recipientColumn: TableHeader = {
        text: 'Recipient',
        align: 'left',
        value: 'recipientName',
        sortable: false,
        class: 'ledger-recipient-column-header',
      };
      // Insert at the 4th position
      tableHeaders.splice(4, 0, recipientColumn);
    }

    return tableHeaders;
  }

  get pagination(): VPagination {
    return {
      descending: true,
      page: this.page,
      rowsPerPage: this.rowsPerPage,
      totalItems: this.totalLedgerCount,
    };
  }

  public pageIncrement(value: number) {
    this.page += value;
    this.getLedgerDetailsList();
  }

  public getLedgerDetailsList() {
    this.$emit(
      'getLedgerDetailsList',
      new LedgerPaginationRequest(
        this.page,
        this.pagination.rowsPerPage,
        null,
        false,
      ),
    );
  }

  public setSelectedLedger(
    ledgerDetailsId: string,
    jobIds: number[],
    type: LedgerType,
  ) {
    if (!this.expandableTable) {
      return;
    }
    const ledgerTableRowSelection: LedgerTableRowSelection = {
      ledgerId: ledgerDetailsId,
      jobIds,
      type,
    };

    this.$emit('setSelectedLedger', ledgerTableRowSelection);
  }

  /**
   * Send request to download the documents associates with the ledger item.
   * Sends a different request based on the ledger type
   * @param ledgerId - The ledger id to download the documents for
   * @param type - The type of ledger
   * @param accessMethod  The ReportAccess email/Download of the invoice to generate.
   */
  public viewInvoiceDocument(
    ledgerId: string,
    type: LedgerType,
    accessMethod: ReportAccessMethodTypes,
  ) {
    const request = {
      ledgerId: ledgerId,
      accessType: accessMethod,
    };

    this.generatingInvoices = true;

    let promise: Promise<GenerateReportResponse | null | undefined> | null =
      null;

    if (type === LedgerType.CLIENT_INVOICE) {
      promise = useClientInvoiceStore().generateClientInvoice(request);
    } else if (type === LedgerType.RCTI) {
      promise = useFleetAssetOwnerInvoiceStore().generateOwnerInvoice(request);
    } else if (type === LedgerType.EQUIPMENT_HIRE) {
      promise =
        useEquipmentHireInvoiceStore().generateEquipmentHireInvoice(request);
    } else if (
      type === LedgerType.CLIENT_INVOICE_ADJUSTMENT ||
      type === LedgerType.SUBCONTRACTOR_ADJUSTMENT_NOTE ||
      type === LedgerType.SUBCONTRACTOR_RC_ADJUSTMENT_NOTE ||
      type === LedgerType.EQUIPMENT_HIRE_RC_ADJUSTMENT_NOTE
    ) {
      promise =
        this.invoiceAdjustmentStore.generateInvoiceAdjustmentReport(request);
    }

    if (promise && typeof promise.finally === 'function') {
      promise.finally(() => {
        this.generatingInvoices = false;
      });
    } else {
      this.generatingInvoices = false;
    }
  }

  /**
   * View the selected jobId in the job details dialog
   * @param jobId - The jobId to view the details for
   */
  public viewJobDetails(jobId: number) {
    this.operationsStore.getFullJobDetails(jobId);
    this.operationsStore.setSelectedJobId(jobId);
    this.operationsStore.setViewingJobDetailsDialog(true);
  }

  get showJobDetailsDialog() {
    return this.operationsStore.viewingJobDetailsDialog;
  }

  get selectedJobDetails() {
    if (
      !this.showJobDetailsDialog ||
      this.operationsStore.selectedJobId === -1
    ) {
      return null;
    }
    return this.operationsStore.selectedJobDetails;
  }

  public mounted() {
    if (!this.isSearchLedger) {
      this.getLedgerDetailsList();
    }
  }
}
