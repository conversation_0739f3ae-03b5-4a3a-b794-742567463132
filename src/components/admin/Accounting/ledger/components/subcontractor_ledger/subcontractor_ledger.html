<v-layout wrap>
  <v-flex md12>
    <v-layout class="ledger-table-header" align-center>
      <span class="actions-bar--header">Subcontractor Ledger</span>
    </v-layout>
  </v-flex>

  <v-flex md12>
    <LedgerTable
      :ledgerTableList="ledgerTableList"
      @getLedgerDetailsList="getSubcontractorLedgerItems"
      :isLoading="isLoading"
      :totalLedgerCount="totalLedgerCount"
      @setSelectedLedger="setSelectedLedger"
      :selectedLedgerId="selectedLedgerId"
      :jobList="jobList"
    />
  </v-flex>
</v-layout>
