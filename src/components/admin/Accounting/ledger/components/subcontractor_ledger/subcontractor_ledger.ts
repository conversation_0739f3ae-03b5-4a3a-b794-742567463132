import LedgerTable from '@/components/admin/Accounting/ledger/components/ledger_table/index.vue';
import DateTimeInputs from '@/components/common/date-picker/date_time_inputs.vue';
import {
  getAccountingJobListTableData,
  getLedgerDetailsTableData,
} from '@/helpers/AccountingHelpers/AccountingHelpers';
import { generateAccountingJobSummary } from '@/helpers/JobDataHelpers/JobDataHelpers';
import { initialiseJobDetails } from '@/helpers/classInitialisers/InitialiseJobDetails';
import { AccountingJobRow } from '@/interface-models/Accounting/AccountingJobRow';
import { LedgerDetails } from '@/interface-models/Accounting/LedgerDetails';
import { LedgerPaginationRequest } from '@/interface-models/Accounting/LedgerPaginationRequest';
import { LedgerTableRow } from '@/interface-models/Accounting/LedgerTableRow';
import { LedgerType } from '@/interface-models/Accounting/LedgerType';
import LedgerItemsResponse from '@/interface-models/Generic/Accounting/Ledgers/LedgerItemsResponse';
import { LedgerTableRowSelection } from '@/interface-models/Generic/Accounting/Ledgers/LedgerTableRowSelection';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useJobStore } from '@/store/modules/JobStore';
import { Component, Vue } from 'vue-property-decorator';
@Component({
  components: { DateTimeInputs, LedgerTable },
})
export default class GeneralLedger extends Vue {
  public totalLedgerCount: number = 0;
  public ledgerDetailsList: LedgerDetails[] = [];
  public isLoading: boolean = false;
  public selectedLedgerId: string | null = null;
  public jobList: AccountingJobRow[] = [];

  get ledgerTableList(): LedgerTableRow[] {
    return getLedgerDetailsTableData(this.ledgerDetailsList);
  }

  public setRequestedLedgerItems(
    requestedLedgerItems: LedgerItemsResponse | null,
  ) {
    if (!requestedLedgerItems) {
      this.ledgerDetailsList = [];
      this.totalLedgerCount = 0;
      return;
    }
    this.totalLedgerCount = requestedLedgerItems.totalRecords;
    this.ledgerDetailsList = requestedLedgerItems.ledgerDetailsList;

    this.setLoading(false);
  }

  public async getSubcontractorLedgerItems(
    ledgerRequest: LedgerPaginationRequest,
  ) {
    this.setLoading(true);
    ledgerRequest.ledgerType = [
      LedgerType.RCTI,
      LedgerType.SUBCONTRACTOR_RC_ADJUSTMENT_NOTE,
      LedgerType.SUBCONTRACTOR_ADJUSTMENT_NOTE,
    ];
    // Make request and handle response
    this.setRequestedLedgerItems(
      await useCompanyDetailsStore().requestLedgerItems(ledgerRequest),
    );
  }

  public setLoading(value: boolean) {
    this.isLoading = value;
  }

  public setSelectedLedger(ledgerTableRowSelection: LedgerTableRowSelection) {
    if (this.selectedLedgerId === ledgerTableRowSelection.ledgerId) {
      this.selectedLedgerId = null;
      this.jobList = [];
      return;
    }
    this.selectedLedgerId = ledgerTableRowSelection.ledgerId;
    this.searchForJobs(ledgerTableRowSelection.jobIds);
  }

  /**
   * Searches for full JobDetails documents using the provided list of jobIds,
   * converts to accounting summaries and sets to jobList
   * @param jobIds jobIds to query for
   */
  public async searchForJobs(jobIds: number[]) {
    this.setLoading(true);
    const results = await useJobStore().getJobDetailsForJobIds(jobIds);
    if (results) {
      const accountingJobSummaryList = results.map((x: JobDetails) => {
        return generateAccountingJobSummary(initialiseJobDetails(x));
      });

      this.jobList = getAccountingJobListTableData(
        accountingJobSummaryList,
        true,
      );
      this.setLoading(false);
    }
  }

  public mounted() {
    this.getSubcontractorLedgerItems(
      new LedgerPaginationRequest(
        1,
        50,
        [
          LedgerType.RCTI,
          LedgerType.SUBCONTRACTOR_RC_ADJUSTMENT_NOTE,
          LedgerType.SUBCONTRACTOR_ADJUSTMENT_NOTE,
        ],
        false,
      ),
    );
  }
}
