<v-layout row wrap class="invoice-index">
  <v-flex md12>
    <v-layout class="invoice-index__header-bar">
      <v-sheet
        v-for="(item, index) in navigationOptions"
        class="invoice-index__header-card"
        :class="(item.id === currentView) ? 'active-header__client' : ''"
        :key="item.id"
        elevation="0"
        tile
        @click="setCurrentView(item.id)"
      >
        <v-layout style="height: 100%" justify-center align-center>
          <span class="card-text">{{ item.text }}</span>
        </v-layout>
        <div class="offset-chevron"></div>
      </v-sheet>
    </v-layout>
  </v-flex>

  <v-flex md12 class="invoice-index-table-container">
    <GeneralLedger v-if="currentView === 'GENERAL_LEDGER'" />
    <ClientLedger v-if="currentView === 'CLIENT_LEDGER'" />
    <SubcontractorLedger v-if="currentView === 'SUBCONTRACTOR_LEDGER'" />
    <EquipmentHireLedger v-if="currentView === 'EQUIPMENT_HIRE_LEDGER'" />
    <SearchLedger v-if="currentView === 'SEARCH_LEDGER'" />
  </v-flex>
</v-layout>
