import { Component, Vue } from 'vue-property-decorator';

import { AccountingLedgerNavigation } from '@/interface-models/Accounting/AccountingLedgerNavigation';

import GeneralLedger from '@/components/admin/Accounting/ledger/components/general_ledger/index.vue';
import ClientLedger from '@/components/admin/Accounting/ledger/components/client_ledger/index.vue';
import SubcontractorLedger from '@/components/admin/Accounting/ledger/components/subcontractor_ledger/index.vue';
import EquipmentHireLedger from '@/components/admin/Accounting/ledger/components/equipment_hire_ledger/index.vue';
import SearchLedger from '@/components/admin/Accounting/ledger/components/search_ledger/index.vue';

interface NavigationOption {
  id: AccountingLedgerNavigation;
  text: string;
}

@Component({
  components: {
    GeneralLedger,
    ClientLedger,
    SubcontractorLedger,
    EquipmentHireLedger,
    SearchLedger,
  },
})
export default class AccountingLedger extends Vue {
  public currentView: AccountingLedgerNavigation =
    AccountingLedgerNavigation.GENERAL_LEDGER;

  public navigationOptions: NavigationOption[] = [
    {
      id: AccountingLedgerNavigation.GENERAL_LEDGER,
      text: 'General Ledger',
    },
    {
      id: AccountingLedgerNavigation.CLIENT_LEDGER,
      text: 'Client Ledger',
    },
    {
      id: AccountingLedgerNavigation.SUBCONTRACTOR_LEDGER,
      text: 'Subcontractor Ledger',
    },
    {
      id: AccountingLedgerNavigation.EQUIPMENT_HIRE_LEDGER,
      text: 'Equipment Hire Ledger',
    },
    {
      id: AccountingLedgerNavigation.SEARCH_LEDGER,
      text: 'Search Ledger',
    },
  ];

  public setCurrentView(navigationType: AccountingLedgerNavigation) {
    this.currentView = navigationType;
  }
}
