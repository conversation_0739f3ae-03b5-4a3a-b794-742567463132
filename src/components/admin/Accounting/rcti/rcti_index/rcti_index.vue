<template>
  <v-layout row wrap class="invoice-index">
    <v-flex md12>
      <v-layout class="invoice-index__header-bar">
        <v-sheet
          v-for="item in navigationOptions"
          class="invoice-index__header-card"
          :class="item.id === currentView ? 'active-header__client' : ''"
          :key="item.id"
          elevation="0"
          tile
          @click="setCurrentView(item.id)"
        >
          <v-layout style="height: 100%" justify-center align-center>
            <span class="card-text">{{ item.text }}</span>
          </v-layout>
          <div class="offset-chevron"></div>
        </v-sheet>
      </v-layout>
    </v-flex>
    <v-flex
      md12
      class="invoice-index-table-container"
      v-if="!accountingIndexLoading"
    >
      <template v-if="isViewingStartInvoiceCard">
        <StartInvoiceRunCard
          ref="startInvoiceRunCard"
          :type="AccountingNavigationType.RCTI"
          @confirm="applyRunParameters"
        ></StartInvoiceRunCard>
      </template>
      <template v-else>
        <RctiReadyForInvoicing
          v-if="currentView === 'READY_FOR_INVOICING'"
          :accountingIndexLoading="accountingIndexLoading"
          @setCurrentView="setCurrentView"
          @editRunParameters="editRunParameters"
        />
        <RctiDraft
          v-if="currentView === 'DRAFT'"
          :accountingIndexLoading="accountingIndexLoading"
          @setCurrentView="setCurrentView"
        />
        <RctiDummy
          v-if="currentView === 'DUMMY'"
          :accountingIndexLoading="accountingIndexLoading"
          @setCurrentView="setCurrentView"
        />
        <RctiApproved
          v-if="currentView === 'APPROVED'"
          :accountingIndexLoading="accountingIndexLoading"
          @setCurrentView="setCurrentView"
        />
      </template>
    </v-flex>
    <v-flex md12 v-else>
      <v-layout justify-center pa-4>
        <img
          src="@/static/loader/infinity-loader-light.svg"
          height="80px"
          width="80px"
        />
      </v-layout>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
interface NavigationOption {
  id: InvoiceNavigation;
  text: string;
}

import StartInvoiceRunCard from '@/components/admin/Accounting/client_invoice/components/start_invoice_run_card.vue';
import RctiApproved from '@/components/admin/Accounting/rcti/components/rcti_approved/index.vue';
import RctiDraft from '@/components/admin/Accounting/rcti/components/rcti_draft/index.vue';
import RctiDummy from '@/components/admin/Accounting/rcti/components/rcti_dummy/index.vue';
import RctiReadyForInvoicing from '@/components/admin/Accounting/rcti/components/rcti_ready_for_invoicing/index.vue';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { AccountingNavigationType } from '@/interface-models/Accounting/AccountingNavigationType';
import { InvoiceNavigation } from '@/interface-models/Accounting/InvoiceNavigation';
import { InvoiceRunParameters } from '@/interface-models/Accounting/InvoiceRunParameters';
import { InvoiceStatus } from '@/interface-models/Accounting/InvoiceStatus';
import { useFleetAssetOwnerInvoiceStore } from '@/store/modules/FleetAssetOwnerInvoiceStore';
import {
  computed,
  ComputedRef,
  nextTick,
  onBeforeMount,
  Ref,
  ref,
  toRef,
  watch,
} from 'vue';

const props = withDefaults(
  defineProps<{
    accountingIndexLoading?: boolean;
  }>(),
  {
    accountingIndexLoading: true,
  },
);

const fleetAssetOwnerInvoiceStore = useFleetAssetOwnerInvoiceStore();

const accountingIndexLoading: Ref<boolean> = toRef(
  props,
  'accountingIndexLoading',
);

const startInvoiceRunCard: Ref<any> = ref(null);
const isViewingStartInvoiceCard: Ref<boolean> = ref(false);

const currentView: Ref<InvoiceNavigation> = ref(
  InvoiceNavigation.READY_FOR_INVOICING,
);
const navigationOptions: NavigationOption[] = [
  {
    id: InvoiceNavigation.READY_FOR_INVOICING,
    text: 'Ready for Invoicing',
  },
  {
    id: InvoiceNavigation.DRAFT,
    text: 'Draft',
  },
  {
    id: InvoiceNavigation.DUMMY,
    text: 'Dummy',
  },
  {
    id: InvoiceNavigation.APPROVED,
    text: 'Approved',
  },
];

/**
 * Returns the current stage our invoice run is at. Returns null if no invoice
 * run is in progress (ready for invoicing)
 */
const invoiceRunStatus: ComputedRef<InvoiceStatus | null> = computed(
  () => fleetAssetOwnerInvoiceStore.invoiceRunStatus,
);

/**
 * Watches for the loading state of the accounting index page. Display the start
 * invoice run card if there is no invoice run currently in progress
 */
watch(accountingIndexLoading, (newValue) => {
  if (!newValue) {
    setNavigationViewForInvoiceStatus(invoiceRunStatus.value);
  }
});

/**
 * Sets current view based on the provided InvoiceStatus. Called on mount if the
 * parent is already finished loading, otherwise called by the watch on
 * accountingIndexLoading
 */
function setNavigationViewForInvoiceStatus(status: InvoiceStatus | null) {
  switch (status) {
    case InvoiceStatus.DRAFT:
      setCurrentView(InvoiceNavigation.DRAFT);
      break;
    case InvoiceStatus.DUMMY:
      setCurrentView(InvoiceNavigation.DUMMY);
      break;
    case InvoiceStatus.APPROVED:
      setCurrentView(InvoiceNavigation.APPROVED);
      break;
    default:
      setCurrentView(InvoiceNavigation.READY_FOR_INVOICING);
      break;
  }
}

/**
 * Sets the main view within the client invoice page. Actioned from the top
 * panel navigation
 */
function setCurrentView(navigationType: InvoiceNavigation) {
  if (
    navigationType === InvoiceNavigation.READY_FOR_INVOICING &&
    invoiceRunStatus.value === null
  ) {
    isViewingStartInvoiceCard.value = true;
  }
  currentView.value = navigationType;
}

/**
 * Opens the dialog to update the run parameters. Called by emit from
 * ClientInvoiceReadyForInvoicing component.
 */
function editRunParameters() {
  if (
    currentView.value === InvoiceNavigation.READY_FOR_INVOICING &&
    invoiceRunStatus.value === null
  ) {
    isViewingStartInvoiceCard.value = true;
    nextTick(() => {
      const params = {
        billingCycleIds: fleetAssetOwnerInvoiceStore.billingCycleIds,
        weekEndingDate: fleetAssetOwnerInvoiceStore.weekEndingDate,
        processingDate: fleetAssetOwnerInvoiceStore.processingDate,
        includeAdjustments: fleetAssetOwnerInvoiceStore.includeAdjustments,
      };
      startInvoiceRunCard.value?.initWithParameters(params);
    });
  }
}

/**
 * Applies the invoice run parameters to the ready for invoicing component
 */
function applyRunParameters(invoiceRunParameters: InvoiceRunParameters) {
  if (
    invoiceRunParameters.billingCycleIds === null ||
    invoiceRunParameters.weekEndingDate === null ||
    invoiceRunParameters.processingDate === null
  ) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }
  fleetAssetOwnerInvoiceStore.setWeekEndingDate(
    invoiceRunParameters.weekEndingDate,
  );
  fleetAssetOwnerInvoiceStore.setProcessingDate(
    invoiceRunParameters.processingDate,
  );
  fleetAssetOwnerInvoiceStore.setBillingCycleIds(
    invoiceRunParameters.billingCycleIds,
  );
  fleetAssetOwnerInvoiceStore.setIncludeAdjustments(
    invoiceRunParameters.includeAdjustments ?? false,
  );

  isViewingStartInvoiceCard.value = false;
}

onBeforeMount(() => {
  if (!accountingIndexLoading.value) {
    setNavigationViewForInvoiceStatus(invoiceRunStatus.value);
  }
});
</script>
