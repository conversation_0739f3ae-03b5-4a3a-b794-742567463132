import ClientInvoiceTable from '@/components/admin/Accounting/client_invoice/components/client_invoice_table/index.vue';
import DraftJobRemovalConfirmationDialog from '@/components/admin/Accounting/client_invoice/components/confirmation_dialogs/draft_job_removal_confirmation_dialog.vue';
import CancelRunConfirmationDialog from '@/components/admin/Accounting/components/cancel_run_confirmation_dialog.vue';
import CurrentInvoiceStatus from '@/components/admin/Accounting/components/current_invoice_status/index.vue';
import CustomEmailMessageToolTip from '@/components/admin/Accounting/components/custom_email_message_tooltip.vue';
import InvoiceEventHistoryDialog from '@/components/admin/Accounting/components/invoice_event_history_dialog.vue';
import InvoiceRunParametersSummary from '@/components/admin/Accounting/components/invoice_run_parameters_summary.vue';
import RctiInvoiceSummaryReportDialog from '@/components/admin/Accounting/components/rcti_summary_report_dialog/index.vue';
import RctiTable from '@/components/admin/Accounting/rcti/components/rcti_table/index.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import { getFleetAssetOwnerRctiTableData } from '@/helpers/AccountingHelpers/AccountingHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import {
  FleetAssetOwnerAccountingTable,
  RctiInvoiceRow,
} from '@/interface-models/Accounting/FleetAssetOwnerAccountingTable';
import { InvoiceEvent } from '@/interface-models/Accounting/InvoiceEvent';
import { InvoiceNavigation } from '@/interface-models/Accounting/InvoiceNavigation';
import { InvoiceRunSuccessResponse } from '@/interface-models/Accounting/InvoiceRunSuccessResponse';
import { InvoiceStatus } from '@/interface-models/Accounting/InvoiceStatus';
import { RctiRunAccountingTable } from '@/interface-models/Accounting/RctiRunAccountingTable';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { useAdjustmentChargeStore } from '@/store/modules/AdjustmentChargeStore';
import { useFleetAssetOwnerInvoiceStore } from '@/store/modules/FleetAssetOwnerInvoiceStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  components: {
    RctiTable,
    ClientInvoiceTable,
    DraftJobRemovalConfirmationDialog,
    InvoiceEventHistoryDialog,
    ConfirmationDialog,
    CurrentInvoiceStatus,
    CancelRunConfirmationDialog,
    CustomEmailMessageToolTip,
    RctiInvoiceSummaryReportDialog,
    InvoiceRunParametersSummary,
  },
})
export default class RctiDummy extends Vue implements IUserAuthority {
  @Prop({ default: true }) public accountingIndexLoading: boolean;

  public fleetAssetOwnerInvoiceStore = useFleetAssetOwnerInvoiceStore();

  public weekEndingDate: number | null = null;
  public processingDate: number | null = null;
  public billingCycleIds: number[] | null = null;
  public rctiInvoices: RctiInvoiceRow[] = [];
  public invoiceEventHistory: InvoiceEvent[] = [];
  public invoiceType: InvoiceNavigation = InvoiceNavigation.DUMMY;
  public isLoadingDummy: boolean = true;
  public isLoadingUserActionRequest: boolean = false;
  public columnWidth: string = '';
  public $refs!: {
    optionsMenu: any;
    rctiTable: any;
  };

  get approvalConfirmationCheckBoxList(): string[] {
    const checkboxList = [
      'I have reviewed the Remittance Summary Report',
      'I have reviewed the Fleet Asset Owner Pays to the extent that I am satisfied that they are accurate and complete',
    ];
    if (
      this.weekEndingDate !== null &&
      this.processingDate !== null &&
      this.weekEndingDate > this.processingDate
    ) {
      checkboxList.push(
        'I understand that the processing date is before the week ending date',
      );
    }
    // if there is a custom email message we should add this to our user checkbox list.
    if (this.customEmailMessage) {
      checkboxList.push(
        'I have reviewed the custom email message that will be appended to all outgoing emails',
      );
    }
    checkboxList.push(
      'I understand that by Approving these RCTIs for Live run, they will not be editable in the future',
    );
    return checkboxList;
  }

  // returns the current stage our invoice run is at.
  get invoiceRunStatus(): InvoiceStatus | null {
    return this.fleetAssetOwnerInvoiceStore.invoiceRunStatus;
  }

  get customEmailMessage(): string {
    return this.fleetAssetOwnerInvoiceStore.customEmailMessage;
  }

  get isLoading() {
    return (
      this.accountingIndexLoading ||
      this.isLoadingDummy ||
      this.isLoadingUserActionRequest
    );
  }

  public setTableData(
    fleetAssetOwnerAccountingTableData: FleetAssetOwnerAccountingTable[],
  ): void {
    if (!this.weekEndingDate && fleetAssetOwnerAccountingTableData.length > 0) {
      return;
    }
    this.rctiInvoices = getFleetAssetOwnerRctiTableData(
      fleetAssetOwnerAccountingTableData,
      this.weekEndingDate,
      useAdjustmentChargeStore().adjustmentChargeCategories,
    );
  }
  // Because the select tile in our menu is a component it does not automatically close when selected. We must close the menu manually. Method call Emitted from components that act as a tile in our menu.
  public closeMenuOptions() {
    if (this.$refs.optionsMenu) {
      this.$refs.optionsMenu.isActive = false;
    }
  }

  public approveDummyRun(): void {
    this.isLoadingUserActionRequest = true;
    this.fleetAssetOwnerInvoiceStore.approveDummyRun();
  }

  public async getDummyRun() {
    this.isLoadingDummy = true;
    // Send request and handle response
    const response = await this.fleetAssetOwnerInvoiceStore.getOwnerInvoiceRun(
      InvoiceStatus.DUMMY,
    );
    if (response?.type !== InvoiceStatus.DUMMY) {
      return;
    }
    this.setDummyRunResponse(response);
  }

  public cancelDummyRun() {
    this.isLoadingUserActionRequest = true;
    this.fleetAssetOwnerInvoiceStore.cancelDummyRun();
  }

  public setDummyRunResponse(response: RctiRunAccountingTable): void {
    if (response.type !== InvoiceStatus.DUMMY) {
      return;
    }
    this.weekEndingDate = response.weekEndingDate;
    this.processingDate = response.processingDate;
    this.billingCycleIds = response.billingCycleIds;
    this.invoiceEventHistory = response.eventList;
    this.setTableData(response.fleetAssetOwnerAccountingTable);
    // this.resizeTableColumns();
    this.isLoadingDummy = false;
  }

  public setResponseForUserAction(
    response: InvoiceRunSuccessResponse,
    showDummyApprovedSuccess: boolean,
    showDummyCancelledSuccess: boolean,
    isDraftApproved: boolean,
  ) {
    // if there was an error force users to refresh page.
    if (!response || !response.success) {
      this.isLoadingUserActionRequest = false;
      showNotification(GENERIC_ERROR_MESSAGE);
      this.getDummyRun();
      return;
    }

    // if dummy is canceled by user we should update our invoice status and relevant invoice dates in ClientInvoiceModule
    if (showDummyCancelledSuccess) {
      this.fleetAssetOwnerInvoiceStore.setInvoiceRunStatus(InvoiceStatus.DRAFT);
      this.fleetAssetOwnerInvoiceStore.setProcessingDate(
        response.processingDate,
      );
      this.fleetAssetOwnerInvoiceStore.setBillingCycleIds(
        response.billingCycleIds,
      );
    }

    // set whether the current view is the user who made the request or any other division users.
    const responseIsForDivisionUsers =
      response.userName !== sessionManager.getUserName();

    // handle response for division users.
    if (responseIsForDivisionUsers) {
      // show notification specific to the update type
      if (showDummyApprovedSuccess) {
        showNotification('Dummy run approved.', {
          title: 'Dummy Run',
          type: HealthLevel.INFO,
        });
      }

      if (showDummyCancelledSuccess) {
        showNotification('Dummy Cancelled.', {
          title: 'Dummy Run',
          type: HealthLevel.INFO,
        });
      }

      if (isDraftApproved) {
        showNotification('New Dummy Run Available.', {
          title: 'Dummy Run',
          type: HealthLevel.INFO,
        });
      }
    }
    // handle response for user who made the request.
    if (!responseIsForDivisionUsers) {
      // show notification specific to the update type
      if (showDummyApprovedSuccess) {
        showNotification('Successfully Approved.', {
          title: 'Dummy Run',
          type: HealthLevel.SUCCESS,
        });
        // Push user to the APPROVED tab.
        this.$emit('setCurrentView', InvoiceNavigation.APPROVED);
        return;
      }

      if (showDummyCancelledSuccess) {
        showNotification('Successfully Cancelled.', {
          title: 'Dummy Run',
          type: HealthLevel.SUCCESS,
        });
      }
    }
    this.isLoadingUserActionRequest = false;
    this.getDummyRun();
  }

  public isAuthorised(): boolean {
    return hasAdminOrTeamLeaderOrBranchManagerRole();
  }
  /**
   * Handles response from updating the processing date of an RCTI Draft
   */
  private handleDraftApproval(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, false, false, true);
    }
  }

  /** Mitt callback for cancelling a DUMMY run (transition to DRAFT) */
  private handleDummyCancel(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, false, true, false);
    }
  }

  /** Mitt callback for cancelling a DUMMY run (transition to DRAFT) */
  private handleDummyApproval(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, true, false, false);
    }
  }

  public mounted() {
    this.getDummyRun();
    Mitt.on('approveRctiDraftRunResponse', this.handleDraftApproval);
    Mitt.on('cancelRctiDummyRunResponse', this.handleDummyCancel);
    Mitt.on('approveRctiDummyRunResponse', this.handleDummyApproval);
  }

  public beforeDestroy() {
    Mitt.off('approveRctiDraftRunResponse', this.handleDraftApproval);
    Mitt.off('cancelRctiDummyRunResponse', this.handleDummyCancel);
    Mitt.off('approveRctiDummyRunResponse', this.handleDummyApproval);
  }
}
