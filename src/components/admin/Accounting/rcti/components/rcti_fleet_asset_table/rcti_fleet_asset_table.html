<v-layout class="job-list-table">
  <v-data-table
    :headers="tableHeaders"
    :item-key="'fleetAssetId'"
    :items="fleetAssetRctiTable"
    :rows-per-page-items="[15, 20]"
    hide-actions
    :no-data-text="'No Invoices are ready to run for the selected week ending date.'"
    :loading="isLoading"
    class="default-table-dark fleet-asset-accounting-table gd-dark-theme"
    :class="[{'fleet-asset-accounting-table-fleet-asset-selected': selectedFleetAssetId}]"
  >
    <template v-slot:items="props">
      <tr>
        <td
          class="inner-table__cell checkbox-type checkbox-column-cell"
          @click="setSelectedFleetAsset(props.item.fleetAssetId); props.expanded = !props.expanded;"
        >
          <v-icon
            v-if="props.item.hasDevice"
            color="warning"
            size="16"
            class="mobile-icon-container"
            >fas fa-mobile-alt</v-icon
          >

          <v-checkbox
            v-if="selectionIsEnabled"
            @click.native.stop
            v-model="props.item.isSelected"
            hide-details
            :disabled="invalidFields.length > 0"
            :indeterminate="props.item.partialSelected"
            @change="setAllJobsSelectionOnSingleFleetAsset($event, props.item.fleetAssetId)"
            :ripple="false"
            color="info"
          ></v-checkbox>
        </td>

        <td
          class="inner-table__cell text-left csrAssignedId-column-cell"
          @click="setSelectedFleetAsset(props.item.fleetAssetId); props.expanded = !props.expanded;"
        >
          {{props.item.csrAssignedId}}
        </td>

        <td
          class="inner-table__cell text-right numberOfJobs-column-cell"
          @click="setSelectedFleetAsset(props.item.fleetAssetId); props.expanded = !props.expanded;"
        >
          <v-layout
            justify-left
            v-if="invoiceRunStatus !== 'DUMMY' && invoiceRunStatus !== 'APPROVED'"
          >
            <div class="number-of-jobs-selected-container">
              <div class="invalid-jobs-exclamation">
                <InvalidJobsToolTip
                  v-if="props.item.invalidJobIds.length > 0"
                  :invalidJobIds="props.item.invalidJobIds"
                />
              </div>
              <div class="selected-jobs-selected-container">
                <span
                  >{{getNumberOfJobsSelected(props.item.fleetAssetId)}}</span
                >
              </div>

              <div class="selected-of-container">
                <span>of</span>
              </div>

              <div class="total-number-of-jobs-container">
                <span>{{props.item.numberOfJobs}} </span>
              </div>
            </div>
          </v-layout>

          <v-layout
            justify-left
            v-if="invoiceRunStatus === 'DUMMY' || invoiceRunStatus === 'APPROVED'"
          >
            {{props.item.numberOfJobs}}
          </v-layout>
        </td>
        <td
          class="inner-table__cell text-left tradingTermName-column-cell"
          @click="setSelectedFleetAsset(props.item.fleetAssetId); props.expanded = !props.expanded;"
        >
          -
        </td>
        <td
          class="inner-table__cell text-left dueDate-column-cell"
          @click="setSelectedFleetAsset(props.item.fleetAssetId); props.expanded = !props.expanded;"
        >
          -
        </td>
        <td
          class="inner-table__cell text-right rctiAmount-column-cell"
          :style="{width: columnWidth + '!important', maxWidth: columnWidth + '!important' }"
          @click="setSelectedFleetAsset(props.item.fleetAssetId); props.expanded = !props.expanded;"
          :class="{'highlight-text' : props.item.hasRctiAdjustment}"
        >
          {{props.item.rctiAmount}}
        </td>
        <td
          class="inner-table__cell text-right rctiGst-column-cell"
          :style="{width: columnWidth + '!important', maxWidth: columnWidth + '!important' }"
          @click="setSelectedFleetAsset(props.item.fleetAssetId); props.expanded = !props.expanded;"
          :class="{'highlight-text' : props.item.hasRctiAdjustment}"
        >
          {{props.item.rctiGst}}
        </td>
        <td
          class="inner-table__cell integer-type taxAllowanceExclGst-column-cell"
          :style="{width: columnWidth + '!important', maxWidth: columnWidth + '!important' }"
          @click="setSelectedFleetAsset(props.item.fleetAssetId); props.expanded = !props.expanded;"
        >
          {{props.item.fuelSurchargeExclGst}}
        </td>
        <td
          class="inner-table__cell text-right taxAllowanceGst-column-cell"
          :style="{width: columnWidth + '!important', maxWidth: columnWidth + '!important' }"
          @click="setSelectedFleetAsset(props.item.fleetAssetId); props.expanded = !props.expanded;"
        >
          {{props.item.fuelSurchargeGst}}
        </td>

        <td
          class="text-right taxInvoiceAmountExclGst-column-cell"
          :style="{width: columnWidth + '!important', maxWidth: columnWidth + '!important' }"
          @click="setSelectedFleetAsset(props.item.fleetAssetId); props.expanded = !props.expanded;"
        >
          <div class="divider-container-left">
            {{props.item.deductionAmountExclGst}}
          </div>
        </td>
        <td
          class="inner-table__cell text-right taxInvoiceAmountGst-column-cell"
          :style="{width: columnWidth + '!important', maxWidth: columnWidth + '!important' }"
          @click="setSelectedFleetAsset(props.item.fleetAssetId); props.expanded = !props.expanded;"
        >
          {{props.item.deductionAmountGst}}
        </td>

        <td
          class="text-right total-column-cell"
          :style="{width: columnWidth + '!important', maxWidth: columnWidth + '!important'}"
          @click="setSelectedFleetAsset(props.item.fleetAssetId); props.expanded = !props.expanded;"
          :class="{'highlight-text' : props.item.hasRctiAdjustment}"
        >
          <div class="divider-container-left">{{props.item.total}}</div>
        </td>

        <td class="text-right action-column-cell">
          <v-menu right ref="optionsMenu">
            <template v-slot:activator="{ on: menu }">
              <v-tooltip bottom>
                <template v-slot:activator="{ on: tooltip }">
                  <v-btn flat icon v-on="{ ...tooltip, ...menu }" class="ma-0">
                    <v-icon size="16">fas fa-ellipsis-v </v-icon>
                  </v-btn>
                </template>
                <span>View Options</span>
              </v-tooltip>
            </template>
            <v-list dense class="v-list-custom">
              <RctiAdminAdjustmentDialog
                :readOnly="invoiceType !== 'DRAFT'"
                :ownerName="ownerName"
                :csrAssignedId="props.item.csrAssignedId"
                :numberOfJobs="props.item.numberOfJobs"
                :administrationAdjustments="props.item.administrationAdjustments"
                :rctiTotalExlGst="props.item.jobsTotalExclGst"
                :rctiTotalGst="props.item.jobsTotalGst"
                :fuelTotalExclGst="props.item.fuelSurchargeExclGst"
                :fuelTotalGst="props.item.fuelSurchargeGst"
                :deductionTotalExclGst="props.item.deductionAmountExclGst"
                :deductionTotalGst="props.item.deductionAmountGst"
                :netTotal="props.item.total"
                :registeredForGst="registeredForGst"
                @closeMenuOptions="closeMenuOptions"
                :affiliationType="affiliationType"
                :fleetAssetId="props.item.fleetAssetId"
                :ledgerId="ledgerId"
                :rctiAdjustmentsExclGst="props.item.rctiAdjustmentExclGst"
                :rctiAdjustmentsGst="props.item.rctiAdjustmentGst"
              />
              <v-divider></v-divider>
            </v-list>
          </v-menu>
        </td>
      </tr>
    </template>

    <template v-slot:expand="props">
      <div class="inner-table__container" id="inner-table__container">
        <v-layout class="inner-table__container--row first-row">
          <v-flex md12>
            <jobListTable
              :jobList="props.item.jobList"
              :isInnerTable="true"
              :fleetAssetId="props.item.fleetAssetId"
              @setJobsCheckBoxForClient="setJobsCheckBoxForClient"
              @setPartialSelected="setPartialSelected"
              :selectionIsEnabled="selectionIsEnabled"
              :invalidJobIds="props.item.invalidJobIds"
              :isClientNameVisible="true"
            />
          </v-flex>
        </v-layout>
      </div>
    </template>
  </v-data-table>
</v-layout>
