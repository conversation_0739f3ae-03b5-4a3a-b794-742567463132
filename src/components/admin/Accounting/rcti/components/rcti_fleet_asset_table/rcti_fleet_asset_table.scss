.job-list-table {
  position: relative;
  padding: 0px;
  background-color: $translucent;

  .inner-table__cell {
    cursor: pointer;
    padding: 0 4px !important;

    &.integer-type {
      text-align: right;
    }

    &.string-type {
      text-align: left;
    }

    &.add-small-width {
      width: 233px;
    }

    &.checkbox-type {
      padding-left: 12px !important;
    }
  }
}

.table-header-left-action-icon-container {
  position: absolute;
  top: -3px;
  left: 13px;
  z-index: 5;
}

.inner-table__container {
  background-color: $app-dark-primary-500;
  padding: 10px 12px;

  .dummy-invoice-icon {
    font-size: $font-size-16;
    color: rgb(173, 173, 173);
    transition: 0.15s ease;

    &:hover {
      color: white;
    }
  }
}

.number-of-jobs-selected-container {
  position: relative;
  width: 80px;
  max-width: 80px;
  display: flex;

  .invalid-jobs-exclamation {
    position: absolute;
  }

  .selected-jobs-selected-container {
    width: 30px;
    max-width: 30px;
    text-align: right;
    color: $warning;
  }

  .selected-of-container {
    width: 20px;
    max-width: 20px;
    text-align: center;
  }

  .total-number-of-jobs-container {
    text-align: left;
    width: 30px;
    max-width: 30px;
  }
}

.hide-non-expanded-rows-container {
  width: 100%;
  position: absolute;
  background-color: red;
  height: 20px;
  bottom: 19px;
  background-color: var(--background-color-100);
  border-top: 1px solid $border-color-alternate;
}

.v-table__overflow {
  overflow-y: auto;
  position: relative;
  border: 1px solid red !important;
  border-radius: 3px;
}

.highlight-text {
  color: #ffc400;
}

.divider-container-left {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-left: 1px solid rgba(255, 255, 255, 0.12);
}

.mobile-icon-container {
  position: absolute;
  left: -20px;
  top: 15px;
  z-index: 5 !important;
}
