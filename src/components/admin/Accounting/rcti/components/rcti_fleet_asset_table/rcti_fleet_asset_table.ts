import InvalidJobsToolTip from '@/components/admin/Accounting/components/invalid_jobs_tool_tip.vue';
import JobListTable from '@/components/admin/Accounting/components/job_list_table/index.vue';
import RctiAdminAdjustmentDialog from '@/components/admin/Accounting/components/rcti_admin_adjustment_dialog.vue';
import JobDetailsDialog from '@/components/operations/OperationDashboard/components/JobDetailsDialog/index.vue';
import { AccountingJobRow } from '@/interface-models/Accounting/AccountingJobRow';
import { FleetAssetRctiTableRow } from '@/interface-models/Accounting/FleetAssetAccountingTable';
import { InvoiceNavigation } from '@/interface-models/Accounting/InvoiceNavigation';
import { InvoiceStatus } from '@/interface-models/Accounting/InvoiceStatus';
import { TableJobSelection } from '@/interface-models/Accounting/TableJobSelection';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { JobDetails } from '@/interface-models/Jobs/JobDetails';
import { useFleetAssetOwnerInvoiceStore } from '@/store/modules/FleetAssetOwnerInvoiceStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  components: {
    JobDetailsDialog,
    JobListTable,
    RctiAdminAdjustmentDialog,
    InvalidJobsToolTip,
  },
})
export default class RctiFleetAssetTable extends Vue {
  @Prop() public fleetAssetRctiTable: FleetAssetRctiTableRow[];
  @Prop() public selectedFleetAssetId: string | null;
  @Prop({ default: false }) public isInnerTable: boolean;
  @Prop({ default: false }) public invoiceIdAvailable: boolean;
  @Prop({ default: false }) public isLoading: boolean;
  @Prop({ default: true }) public selectionIsEnabled: boolean;
  @Prop({ default: () => [] }) public invalidJobIds: number[];
  @Prop({ default: '' }) public ownerName: string;
  @Prop() public invoiceType: InvoiceNavigation;
  @Prop({ default: true }) public registeredForGst: boolean;
  @Prop() public affiliationType: string;
  @Prop() public ledgerId: string;
  @Prop() public columnWidth: string;
  @Prop() public invalidFields: string[];

  public operationsStore = useOperationsStore();

  public tableData: AccountingJobRow[] = [];
  public selectAll: boolean = false;

  public $refs!: {
    optionsMenu: any;
  };

  get tableHeaders(): TableHeader[] {
    const tableHeaders: TableHeader[] = [
      {
        text: '',
        align: 'left',
        value: '',
        class: 'checkbox-column-header',
        sortable: false,
      },
      {
        text: 'Fleet #',
        align: 'left',
        value: 'csrAssignedId',
        sortable: false,
        class: 'csrAssignedId-column-header',
      },

      {
        text: '# of Jobs',
        align: 'left',
        value: 'numberOfJobs',
        sortable: true,
        class: 'numberOfJobs-column-header',
      },
      {
        text: 'Trading Terms',
        align: 'left',
        value: 'tradingTermName',
        sortable: true,
        class: 'tradingTermName-column-header',
      },
      {
        text: 'Due Date',
        align: 'left',
        value: 'dueDate',
        sortable: true,
        class: 'dueDate-column-header',
      },
      {
        text: 'RCTI AMT ($)',
        align: 'right',
        value: 'marginAverage',
        sortable: false,
        class: 'rctiAmount-column-header',
      },
      {
        text: 'RCTI GST ($)',
        align: 'right',
        value: 'marginAverage',
        sortable: false,
        class: 'rctiGst-column-header',
      },
      {
        text: 'Tax Allow ($)',
        align: 'right',
        value: 'freightSubtotal',
        sortable: false,
        class: 'taxAllowanceExclGst-column-header',
      },
      {
        text: 'Tax Allow GST ($)',
        align: 'right',
        value: 'freightSubtotal',
        sortable: false,
        class: 'taxAllowanceGst-column-header',
      },
      {
        text: 'Tax INV Amount ($)',
        align: 'right',
        value: 'additionalCharges',
        sortable: false,
        class: 'taxInvoiceAmountExclGst-column-header',
      },
      {
        text: 'Tax INV GST ($)',
        align: 'right',
        value: 'fuelSurcharge',
        sortable: false,
        class: 'taxInvoiceAmountGst-column-header',
      },
      {
        text: 'Net ($)',
        align: 'right',
        value: 'gst',
        sortable: false,
        class: 'total-column-header',
      },
      {
        text: '',
        align: 'right',
        value: '',
        sortable: false,
        class: 'action-column-header',
      },
    ];
    return tableHeaders;
  }

  // emit from child job list component. Emit event actioned by the global job list checkbox. Updates clientInvoices data with selection.
  public setJobsCheckBoxForClient(jobSelection: TableJobSelection) {
    if (!jobSelection.fleetAssetId) {
      return;
    }
    const fleetAssetId: string = jobSelection.fleetAssetId;
    const setAllJobs: boolean = jobSelection.setAllJobs;
    const fleetAssetRow: FleetAssetRctiTableRow | undefined =
      this.fleetAssetRctiTable.find(
        (x: FleetAssetRctiTableRow) => x.fleetAssetId === fleetAssetId,
      );
    if (!fleetAssetRow) {
      return;
    }
    fleetAssetRow.isSelected = setAllJobs;
    for (const job of fleetAssetRow.jobList) {
      job.isSelected = setAllJobs;
    }
    this.setPartialSelected();
  }

  // returns the current stage our invoice run is at.
  get invoiceRunStatus(): InvoiceStatus | null {
    return useFleetAssetOwnerInvoiceStore().invoiceRunStatus;
  }

  // Updates clientInvoices data with selections. Similar to "setJobsCheckBoxForClient". The reason we have this is because emits only allow one arg. Where as the vuetify checkbox requires $event and clientId in this component
  public setAllJobsSelectionOnSingleFleetAsset(
    value: boolean,
    fleetAssetId: string,
  ): void {
    const fleetAsset: FleetAssetRctiTableRow | undefined =
      this.fleetAssetRctiTable.find(
        (x: FleetAssetRctiTableRow) => x.fleetAssetId === fleetAssetId,
      );
    if (!fleetAsset) {
      return;
    }
    for (const job of fleetAsset.jobList) {
      if (!fleetAsset.invalidJobIds.includes(job.jobId)) {
        job.isSelected = value;
      }
    }
    this.setPartialSelected();
  }

  // emites the selected clientId back to parent component.
  public setSelectedFleetAsset(fleetAssetId: string) {
    this.$emit(
      'update:selectedFleetAssetId',
      fleetAssetId !== this.selectedFleetAssetId ? fleetAssetId : null,
    );
  }

  public viewJobDetails(jobId: number) {
    this.operationsStore.getFullJobDetails(jobId);
    this.operationsStore.setSelectedJobId(jobId);
    this.operationsStore.setViewingJobDetailsDialog(true);
  }

  get showJobDetailsDialog() {
    return this.operationsStore.viewingJobDetailsDialog;
  }

  get selectedJobDetails(): JobDetails | null {
    if (
      !this.showJobDetailsDialog ||
      this.operationsStore.selectedJobId === -1
    ) {
      return null;
    }
    return this.operationsStore.selectedJobDetails;
  }

  // returns the indeterminate state for the overall invoice selection checkbox
  get invoicesPartiallySelected() {
    const selectedInvoices = this.fleetAssetRctiTable.filter(
      (x: FleetAssetRctiTableRow) => x.isSelected,
    );
    return (
      selectedInvoices.length > 0 &&
      selectedInvoices.length !== this.fleetAssetRctiTable.length
    );
  }

  // Sets indeterminate on invoice table checkboxes and expanded job list
  public setPartialSelected() {
    for (const fleetAsset of this.fleetAssetRctiTable) {
      const selectedJobs = fleetAsset.jobList.filter(
        (x: AccountingJobRow) => x.isSelected,
      );

      if (selectedJobs.length === fleetAsset.jobList.length) {
        fleetAsset.isSelected = true;
        fleetAsset.partialSelected = false;
      }

      if (selectedJobs.length === 0) {
        fleetAsset.isSelected = false;
        fleetAsset.partialSelected = false;
      }

      if (
        selectedJobs.length > 0 &&
        selectedJobs.length !== fleetAsset.jobList.length
      ) {
        fleetAsset.partialSelected = true;
        fleetAsset.isSelected = true;
      }
    }
    this.$emit('setPartialSelected');
  }

  // main v-model controller for our "Select all invoices" checkbox.
  get allInvoicesCheckBox() {
    for (const invoice of this.fleetAssetRctiTable) {
      for (const job of invoice.jobList) {
        if (!job.isSelected) {
          return false;
        }
      }
    }
    return this.fleetAssetRctiTable.length > 0;
  }
  set allInvoicesCheckBox(value: boolean) {
    for (const invoice of this.fleetAssetRctiTable) {
      invoice.isSelected = value;
      invoice.partialSelected =
        invoice.invalidJobIds.length > 0 && value ? true : false;
      for (const job of invoice.jobList) {
        if (!invoice.invalidJobIds.includes(job.jobId)) {
          job.isSelected = value;
        }
      }
    }
  }

  // returns the total number of selected jobs for a single invoice. called from html template.
  public getNumberOfJobsSelected(fleetAssetId: string) {
    const fleetAssetRow: FleetAssetRctiTableRow | undefined =
      this.fleetAssetRctiTable.find(
        (x: FleetAssetRctiTableRow) => x.fleetAssetId === fleetAssetId,
      );
    if (!fleetAssetRow) {
      return '-';
    }
    const selectedJobs = fleetAssetRow.jobList.filter(
      (x: AccountingJobRow) => x.isSelected,
    );
    return selectedJobs.length;
  }

  public setSelectedSingleJob() {
    this.$emit('setPartialSelected');
  }

  // Because the select tile in our menu is a component it does not automatically close when selected. We must close the menu manually. Method call Emitted from components that act as a tile in our menu.
  public closeMenuOptions() {
    if (this.$refs.optionsMenu) {
      this.$refs.optionsMenu.isActive = false;
    }
  }
}
