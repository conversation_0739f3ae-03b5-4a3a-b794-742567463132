import InvalidFieldsToolTip from '@/components/admin/Accounting/components/invalid_fields_tool_tip.vue';
import JobListTable from '@/components/admin/Accounting/components/job_list_table/index.vue';
import RctiFleetAssetTable from '@/components/admin/Accounting/rcti/components/rcti_fleet_asset_table/index.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import JobDetailsDialog from '@/components/operations/OperationDashboard/components/JobDetailsDialog/index.vue';
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { AccountingJobRow } from '@/interface-models/Accounting/AccountingJobRow';
import { FleetAssetRctiTableRow } from '@/interface-models/Accounting/FleetAssetAccountingTable';
import { RctiInvoiceRow } from '@/interface-models/Accounting/FleetAssetOwnerAccountingTable';
import { InvoiceNavigation } from '@/interface-models/Accounting/InvoiceNavigation';
import { InvoiceStatus } from '@/interface-models/Accounting/InvoiceStatus';
import { TableJobSelection } from '@/interface-models/Accounting/TableJobSelection';
import { DivisionReportSettings } from '@/interface-models/Company/DivisionCustomConfig/DivisionCustomConfig';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useFleetAssetOwnerInvoiceStore } from '@/store/modules/FleetAssetOwnerInvoiceStore';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
@Component({
  components: {
    JobDetailsDialog,
    JobListTable,
    RctiFleetAssetTable,
    ConfirmationDialog,
    InformationTooltip,
    InvalidFieldsToolTip,
  },
})
export default class RctiTable extends Vue implements IUserAuthority {
  @Prop() public rctiInvoices: RctiInvoiceRow[];
  @Prop() public invoiceType: InvoiceNavigation;
  @Prop({ default: false }) public isLoading: boolean;
  @Prop({ default: false }) public isClientComparison: boolean;
  public columnWidth: string = '';

  public fleetAssetOwnerInvoiceStore = useFleetAssetOwnerInvoiceStore();

  public displayCurrencyValue = DisplayCurrencyValue;

  public sortBy: string = '';
  public sortDesc: boolean = false;

  public company = useCompanyDetailsStore().companyDetails;
  public reportsSettings: DivisionReportSettings | null =
    this.company?.divisions?.[0]?.customConfig?.reports ?? null;

  public $refs!: {
    tableComponent: any;
  };

  public isAuthorised(): boolean {
    return hasAdminOrHeadOfficeRole();
  }

  // returns the current stage our invoice run is at.
  get invoiceRunStatus(): InvoiceStatus | null {
    return this.fleetAssetOwnerInvoiceStore.invoiceRunStatus;
  }

  get tableHeaders(): TableHeader[] {
    const tableHeaders: TableHeader[] = [
      {
        text: '',
        align: 'left',
        value: '',
        class: 'checkbox-column-header',
        sortable: false,
      },
      {
        text: 'Invoice No.',
        align: 'left',
        value: 'invoiceId',
        sortable: false,
        class: 'invoiceId-column-header',
      },
      {
        text: 'Owner',
        align: 'left',
        value: 'fleetAssetOwnerName',
        sortable: true,
        class: 'ownerName-column-header',
      },
      {
        text: 'Assets',
        align: 'left',
        value: 'numberOfAssets',
        sortable: true,
        class: 'numberOfFleetAssets-column-header',
      },
      {
        text: 'Terms / Cycle',
        align: 'left',
        value: 'tradingTermName',
        sortable: false,
        class: 'tradingTermName-column-header',
      },
      {
        text: 'Due Date',
        align: 'left',
        value: 'dueDate',
        sortable: false,
        class: 'dueDate-column-header',
      },
      {
        text: 'Charges ($)',
        align: 'right',
        value: 'rctiAmount',
        sortable: false,
        class: 'rctiAmount-column-header',
      },
      {
        text: 'Charges GST ($)',
        align: 'right',
        value: 'rctiGst',
        sortable: false,
        class: 'rctiGst-column-header',
      },
      {
        text: 'Fuel ($)',
        align: 'right',
        value: 'fuelSurchargeExclGst',
        sortable: false,
        class: 'taxAllowanceExclGst-column-header',
      },
      {
        text: 'Fuel GST ($)',
        align: 'right',
        value: 'fuelSurchargeGst',
        sortable: false,
        class: 'taxAllowanceGst-column-header',
      },
      {
        text: 'Tax INV ($)',
        align: 'right',
        value: 'deductionAmountExclGst',
        sortable: false,
        class: 'taxInvoiceAmountExclGst-column-header',
      },
      {
        text: 'Tax INV GST ($)',
        align: 'right',
        value: 'deductionAmountGst',
        sortable: false,
        class: 'taxInvoiceAmountGst-column-header',
      },
      {
        text: 'Net ($)',
        align: 'right',
        value: 'total',
        sortable: true,
        class: 'total-column-header',
      },
      {
        text: 'Document',
        align: 'right',
        value: 'gst',
        sortable: false,
        class: 'action-column-header',
      },
    ];
    return tableHeaders;
  }

  // utilised for the v-if on table checkboxes
  get selectionIsEnabled(): boolean {
    if (
      this.isClientComparison ||
      this.invoiceRunStatus === InvoiceStatus.DUMMY ||
      this.invoiceRunStatus === InvoiceStatus.APPROVED ||
      this.invoiceType === InvoiceNavigation.DUMMY ||
      this.invoiceType === InvoiceNavigation.APPROVED
    ) {
      return false;
    }
    if (
      this.invoiceType === InvoiceNavigation.READY_FOR_INVOICING ||
      this.invoiceType === InvoiceNavigation.DRAFT
    ) {
      return true;
    }
    return false;
  }

  get numberOfSubcontractors(): number {
    return this.rctiInvoices.length;
  }

  @Watch('numberOfSubcontractors')
  public listenForTableUpdate(value: number) {
    this.expandTableRows();

    setTimeout(() => {
      this.resizeTableColumns();
    }, 1);
  }
  // Expands the table items. This is so the fleet assets will open under each subcontractor line item.
  public expandTableRows() {
    for (let i = 0; i < this.rctiInvoices.length; i++) {
      this.$set(
        this.$refs.tableComponent.expanded,
        this.rctiInvoices[i].fleetAssetOwnerId,
        true,
      );
    }
  }

  public mounted() {
    window.addEventListener('resize', this.resizeTableColumns);
    this.expandTableRows();
    this.resizeTableColumns();
  }

  public resizeTableColumns(): void {
    if (document.getElementsByClassName('rctiGst-column-cell').length < 1) {
      return;
    }
    const style = window.getComputedStyle(
      document.getElementsByClassName('rctiGst-column-cell')[0],
    );
    this.columnWidth = style.getPropertyValue('width');
  }

  // returns the indeterminate state for the overall invoice selection checkbox
  get invoicesPartiallySelected(): boolean {
    for (const invoice of this.rctiInvoices) {
      if (invoice.partialSelected) {
        return true;
      }
      for (const asset of invoice.fleetAssets) {
        if (asset.partialSelected) {
          return true;
        }
      }
    }

    return false;
  }

  // main v-model controller for our "Select all invoices" checkbox.
  get allInvoicesCheckBox(): boolean {
    for (const invoice of this.rctiInvoices) {
      for (const fleetAsset of invoice.fleetAssets) {
        for (const job of fleetAsset.jobList) {
          if (!job.isSelected) {
            return false;
          }
        }
      }
    }
    return this.rctiInvoices.length > 0;
  }
  set allInvoicesCheckBox(value: boolean) {
    for (const invoice of this.rctiInvoices) {
      let numberOfInvalidJobs: number = 0;

      const invoiceIsSelected =
        invoice.invalidFields.length > 0 ? false : value;
      invoice.isSelected = invoiceIsSelected;

      for (const fleetAsset of invoice.fleetAssets) {
        numberOfInvalidJobs += fleetAsset.invalidJobIds.length;
        fleetAsset.isSelected = invoiceIsSelected;
        fleetAsset.partialSelected =
          fleetAsset.invalidJobIds.length > 0 ? true : false;
        for (const job of fleetAsset.jobList) {
          job.isSelected = fleetAsset.invalidJobIds.includes(job.jobId)
            ? false
            : invoiceIsSelected;
        }
      }

      invoice.partialSelected =
        numberOfInvalidJobs > 0 && invoiceIsSelected ? true : false;
    }
  }

  // request to generate and download rct
  public viewRctiDocument(
    ledgerId: string,
    accessMethod: ReportAccessMethodTypes,
  ): void {
    const request = {
      ledgerId: ledgerId,
      accessType: accessMethod,
    };
    this.fleetAssetOwnerInvoiceStore.generateOwnerInvoice(request);
  }

  // Updates clientInvoices data with selections. Similar to "setJobsCheckBoxForClient". The reason we have this is because emits only allow one arg. Where as the vuetify checkbox requires $event and clientId in this component
  public setAllFleetAssetsAndJobsSelectionOnSingleOwnerInvoice(
    value: boolean,
    fleetAssetOwnerId: string,
  ): void {
    const rctiInvoiceForOwner = this.rctiInvoices.find(
      (x: RctiInvoiceRow) => x.fleetAssetOwnerId === fleetAssetOwnerId,
    );

    if (!rctiInvoiceForOwner) {
      return;
    }

    let numberOfInvalidJobs: number = 0;
    const invoiceIsSelected =
      rctiInvoiceForOwner.invalidFields.length > 0 ? false : value;

    rctiInvoiceForOwner.isSelected = invoiceIsSelected;

    for (const fleetAsset of rctiInvoiceForOwner.fleetAssets) {
      numberOfInvalidJobs += fleetAsset.invalidJobIds.length;
      fleetAsset.isSelected = invoiceIsSelected;
      fleetAsset.partialSelected =
        fleetAsset.invalidJobIds.length > 0 ? true : false;
      for (const job of fleetAsset.jobList) {
        job.isSelected = fleetAsset.invalidJobIds.includes(job.jobId)
          ? false
          : invoiceIsSelected;
      }
    }

    rctiInvoiceForOwner.partialSelected =
      numberOfInvalidJobs > 0 && invoiceIsSelected ? true : false;

    this.setPartialSelected();
  }

  // returns boolean on whether a fleet asset owner has selected jobs. This is for our indeterminate icon on our checkbox.
  public fleetAssetHasSelectedJobs(fleetAssetId: string) {
    const rctiInvoice: RctiInvoiceRow | undefined = this.rctiInvoices.find(
      (x: RctiInvoiceRow) =>
        x.fleetAssets.find(
          (fa: FleetAssetRctiTableRow) => fa.fleetAssetId === fleetAssetId,
        ),
    );
    if (!rctiInvoice) {
      return false;
    }
    const fleetAssetInRctiInvoice = rctiInvoice.fleetAssets.find(
      (x: FleetAssetRctiTableRow) => x.fleetAssetId === fleetAssetId,
    );
    if (!fleetAssetInRctiInvoice) {
      return false;
    }
    const selectedJobs = fleetAssetInRctiInvoice.jobList.filter(
      (x: AccountingJobRow) => x.isSelected,
    );
    return (
      selectedJobs.length > 0 &&
      selectedJobs.length !== fleetAssetInRctiInvoice.jobList.length
    );
  }

  // emit from child job list component. Emit event actioned by the global job list checkbox. Updates clientInvoices data with selection.
  public setJobsCheckBoxForClient(jobSelection: TableJobSelection) {
    if (!jobSelection.fleetAssetId) {
      return;
    }
    const fleetAssetId: string = jobSelection.fleetAssetId;
    const setAllJobs: boolean = jobSelection.setAllJobs;
    const rctiInvoice: RctiInvoiceRow | undefined = this.rctiInvoices.find(
      (x: RctiInvoiceRow) =>
        x.fleetAssets.find(
          (fa: FleetAssetRctiTableRow) => fa.fleetAssetId === fleetAssetId,
        ),
    );
    if (!rctiInvoice) {
      return;
    }

    const fleetAssetInRctiInvoice = rctiInvoice.fleetAssets.find(
      (x: FleetAssetRctiTableRow) => x.fleetAssetId === fleetAssetId,
    );

    if (!fleetAssetInRctiInvoice) {
      return;
    }

    fleetAssetInRctiInvoice.isSelected = setAllJobs;
    for (const job of fleetAssetInRctiInvoice.jobList) {
      job.isSelected = setAllJobs;
    }
    this.setPartialSelected();
  }

  // Updates clientInvoices data with selections. Similar to "setJobsCheckBoxForClient". The reason we have this is because emits only allow one arg. Where as the vuetify checkbox requires $event and clientId in this component
  public setAllJobsSelectionOnSingleRcti(
    value: boolean,
    fleetAssetOwnerId: string,
  ): void {
    const rctiInvoice: RctiInvoiceRow | undefined = this.rctiInvoices.find(
      (x: RctiInvoiceRow) => x.fleetAssetOwnerId === fleetAssetOwnerId,
    );
    if (!rctiInvoice) {
      return;
    }

    for (const fleetAsset of rctiInvoice.fleetAssets) {
      for (const job of fleetAsset.jobList) {
        job.isSelected = value;
      }
    }

    this.setPartialSelected();
  }

  // Sets indeterminate on invoice table checkboxes and expanded job list
  public setPartialSelected(): void {
    for (const rctiInvoice of this.rctiInvoices) {
      const numberOfFleetAssets = rctiInvoice.fleetAssets.length;

      const numberOfSelectedFleetAssets = rctiInvoice.fleetAssets.filter(
        (x: FleetAssetRctiTableRow) => x.isSelected,
      ).length;

      if (numberOfSelectedFleetAssets === numberOfFleetAssets) {
        rctiInvoice.isSelected = true;
        rctiInvoice.partialSelected = false;
      }
      if (numberOfSelectedFleetAssets === 0) {
        rctiInvoice.isSelected = false;
        rctiInvoice.partialSelected = false;
      }
      if (
        numberOfSelectedFleetAssets > 0 &&
        numberOfSelectedFleetAssets !== numberOfFleetAssets
      ) {
        rctiInvoice.isSelected = true;
        rctiInvoice.partialSelected = true;
      }
      for (const fleetAsset of rctiInvoice.fleetAssets) {
        const selectedJobs = fleetAsset.jobList.filter(
          (x: AccountingJobRow) => x.isSelected,
        );

        if (selectedJobs.length === fleetAsset.jobList.length) {
          fleetAsset.isSelected = true;
          fleetAsset.partialSelected = false;
        }

        if (selectedJobs.length === 0) {
          fleetAsset.isSelected = false;
          fleetAsset.partialSelected = false;
        }
        if (
          selectedJobs.length > 0 &&
          selectedJobs.length !== fleetAsset.jobList.length
        ) {
          fleetAsset.partialSelected = true;
          fleetAsset.isSelected = true;
        }
      }
    }
  }

  // returns the total number of selected fleet assets for a single invoice. called from html template. If any fleet asset has at least one job selected it will satisfy the requirement.
  public getNumberOfFleetAssetsSelected(fleetAssetOwnerId: string): string {
    const rctiInvoice: RctiInvoiceRow | undefined = this.rctiInvoices.find(
      (x: RctiInvoiceRow) => x.fleetAssetOwnerId === fleetAssetOwnerId,
    );
    if (!rctiInvoice) {
      return '-';
    }

    let countOfSelectedFleetAssets: number = 0;

    for (const fleetAsset of rctiInvoice.fleetAssets) {
      const selectedJobs = fleetAsset.jobList.filter(
        (x: AccountingJobRow) => x.isSelected,
      );

      if (selectedJobs.length > 0) {
        countOfSelectedFleetAssets += 1;
        break;
      }
    }
    return countOfSelectedFleetAssets.toString();
  }

  public confirmSendingOfSingleInvoice(ledgerId: string): void {
    this.fleetAssetOwnerInvoiceStore.resendInvoiceForLedgerId(ledgerId);
  }

  public beforeDestroy() {
    window.removeEventListener('resize', this.resizeTableColumns);
  }

  public sortTable(items: any, index: string, isDesc: boolean) {
    if (index !== null && isDesc !== null) {
      items.sort((a: RctiInvoiceRow, b: RctiInvoiceRow) => {
        if (index === 'total') {
          if (!isDesc) {
            return a.total - b.total;
          } else if (isDesc) {
            return b.total - a.total;
          }
        } else if (index === 'fleetAssetOwnerName') {
          if (!isDesc) {
            return a.fleetAssetOwnerName.localeCompare(b.fleetAssetOwnerName);
          } else {
            return b.fleetAssetOwnerName.localeCompare(a.fleetAssetOwnerName);
          }
        }
      });
      this.expandTableRows();
      return items;
    } else {
      this.expandTableRows();
      return this.rctiInvoices;
    }
  }
}
