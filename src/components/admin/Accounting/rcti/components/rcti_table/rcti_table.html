<v-layout class="job-list-table">
  <div class="table-header-left-action-icon-container">
    <v-checkbox
      v-if="selectionIsEnabled"
      @click.native.stop
      :indeterminate="invoicesPartiallySelected"
      v-model="allInvoicesCheckBox"
      hide-details
      :ripple="false"
      :disabled="rctiInvoices.length === 0"
      color="info"
    ></v-checkbox>
  </div>
  <v-data-table
    ref="tableComponent"
    :headers="tableHeaders"
    :item-key="'fleetAssetOwnerId'"
    :items="rctiInvoices"
    :rows-per-page-items="[15, 20]"
    hide-actions
    :loading="isLoading"
    :expand="true"
    :custom-sort="sortTable"
    class="default-table-dark rcti-accounting-table gd-dark-theme"
  >
    <template v-slot:items="props">
      <tr>
        <td class="inner-table__cell checkbox-type checkbox-column-cell">
          <v-checkbox
            v-if="selectionIsEnabled"
            @click.native.stop
            v-model="props.item.isSelected"
            hide-details
            :disabled="props.item.invalidFields.length > 0"
            :indeterminate="props.item.partialSelected"
            @change="setAllFleetAssetsAndJobsSelectionOnSingleOwnerInvoice($event, props.item.fleetAssetOwnerId)"
            :ripple="false"
            color="info"
          ></v-checkbox>

          <v-icon size="18" v-if="props.item.status === 'SENT'" color="success">
            fas fa-check-circle</v-icon
          >
          <v-tooltip
            bottom
            v-if="invoiceType === 'APPROVED' && invoiceRunStatus === 'SENT' && props.item.status === 'FAIL'"
          >
            <template v-slot:activator="{ on: tooltip }">
              <div v-on="{ ...tooltip }">
                <ConfirmationDialog
                  buttonText="Email"
                  :message="'Please confirm that you wish to manually email RCTI ' + props.item.invoiceId + ' (' + props.item.fleetAssetOwnerName + ') to ' + props.item.toEmailIds.join(', ') "
                  :title="'Confirm Manual Emailing'"
                  @confirm="confirmSendingOfSingleInvoice(props.item.ledgerId)"
                  :buttonColor="'error'"
                  :isIcon="true"
                  :iconSize="18"
                  :buttonDisabled="props.item.toEmailIds.length < 1 || !isAuthorised()"
                  :faIconName="'far fa-envelope'"
                  :confirmationButtonText="'Confirm'"
                  :dialogIsActive="true"
                  :noButtonMargin="true"
                >
                </ConfirmationDialog>
              </div>
            </template>
            <span v-if="props.item.toEmailIds.length > 0">Resend Email</span>
            <span v-if="props.item.toEmailIds.length < 1"
              >No Email Address Available</span
            >
          </v-tooltip>
        </td>

        <td class="inner-table__cell string-type invoiceId-column-cell">
          <span class="pr-2"> {{props.item.invoiceId}}</span>
          <InformationTooltip
            :right="true"
            tooltipType="info"
            v-if="invoiceType !== 'READY_FOR_INVOICING'"
          >
            <v-layout slot="content" row wrap>
              <v-flex md12>
                <p class="mb-1" slot="content">This RCTI will be emailed to:</p>

                <ul>
                  <li v-for="emailAddress of props.item.toEmailIds">
                    {{emailAddress}}
                  </li>
                </ul>
              </v-flex>
            </v-layout>
          </InformationTooltip>
        </td>
        <td class="inner-table__cell string-type ownerName-column-cell">
          <v-layout style="position: relative">
            <div class="invalid-client-exclamation">
              <InvalidFieldsToolTip
                :ledgerType="'RCTI'"
                :invalidFields="props.item.invalidFields"
                v-if="props.item.invalidFields.length > 0"
              />
            </div>
            <span
              :class="props.item.affiliationType === '3' ? 'outside-hire-text-color' : props.item.affiliationType === '4' ? 'internal-text-color' : ''"
              >{{props.item.fleetAssetOwnerName}}
            </span>
          </v-layout>
        </td>
        <td
          class="inner-table__cell text-right numberOfFleetAssets-column-cell"
        >
          <v-layout
            justify-left
            v-if="invoiceRunStatus !== 'DUMMY' && invoiceRunStatus !== 'APPROVED'"
          >
            <div class="number-of-jobs-selected-container">
              <div class="selected-jobs-selected-container">
                <span
                  >{{getNumberOfFleetAssetsSelected(props.item.fleetAssetOwnerId)}}</span
                >
              </div>

              <div class="selected-of-container">
                <span>of</span>
              </div>

              <div class="total-number-of-jobs-container">
                <span>{{props.item.numberOfAssets}}</span>
              </div>
            </div>
          </v-layout>
          <v-layout
            v-if="invoiceRunStatus === 'DUMMY' || invoiceRunStatus === 'APPROVED'"
          >
            <span>{{props.item.numberOfAssets}}</span>
          </v-layout>
        </td>
        <td class="inner-table__cell text-left tradingTermName-column-cell">
          {{props.item.tradingTermName}} / {{props.item.billingCycleName}}
        </td>
        <td class="inner-table__cell text-left dueDate-column-cell">
          {{props.item.dueDate}}
        </td>
        <td
          class="inner-table__cell text-right rctiAmount-column-cell"
          :class="{'highlight-text' : props.item.hasRctiAdjustment}"
        >
          {{props.item.rctiAmount}}
        </td>
        <td
          class="inner-table__cell text-right rctiGst-column-cell"
          :class="{'highlight-text' : props.item.hasRctiAdjustment}"
        >
          {{props.item.rctiGst}}
        </td>
        <td
          class="inner-table__cell text-right taxAllowanceExclGst-column-header"
        >
          {{props.item.fuelSurchargeExclGst}}
        </td>
        <td class="inner-table__cell text-right taxAllowanceGst-column-cell">
          {{props.item.fuelSurchargeGst}}
        </td>

        <td class="text-left taxInvoiceAmountExclGst-column-cell">
          <div class="divider-container-left">
            {{props.item.deductionAmountExclGst}}
          </div>
        </td>
        <td
          class="inner-table__cell text-right taxInvoiceAmountGst-column-cell"
        >
          {{props.item.deductionAmountGst}}
        </td>

        <td
          class="text-right total-column-cell"
          :class="{'highlight-text' : props.item.hasRctiAdjustment}"
        >
          <div class="divider-container-left">
            {{displayCurrencyValue(props.item.total)}}
          </div>
        </td>

        <td
          class="inner-table__cell text-right view-job-menu action-column-cell"
        >
          <div class="pr-2">
            <v-tooltip bottom class="pr-3">
              <template v-slot:activator="{ on: tooltip }">
                <v-btn
                  v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  'DOWNLOAD',
                )
              "
                  flat
                  @click.native.stop="viewRctiDocument(props.item.ledgerId, 'DOWNLOAD')"
                  icon
                  color="accent"
                  v-on="{ ...tooltip }"
                  class="ma-0"
                  :disabled="invoiceType === 'READY_FOR_INVOICING' || isClientComparison"
                >
                  <v-icon size="25"> downloading </v-icon>
                </v-btn>
              </template>
              <span>Download Invoice</span>
            </v-tooltip>
            <v-tooltip bottom class="pr-2">
              <template v-slot:activator="{ on: tooltip }">
                <v-btn
                  v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  'EMAIL',
                )
              "
                  flat
                  @click.native.stop="viewRctiDocument(props.item.ledgerId, 'EMAIL')"
                  icon
                  color="orange"
                  v-on="{ ...tooltip }"
                  class="ma-0"
                  :disabled="invoiceType === 'READY_FOR_INVOICING' || isClientComparison"
                >
                  <v-icon size="25"> forward_to_inbox </v-icon>
                </v-btn>
              </template>
              <span>Email Preview</span>
            </v-tooltip>
          </div>
        </td>
      </tr>
    </template>
    <template v-slot:expand="props">
      <div class="inner-table__container" id="inner-table__container">
        <v-layout class="inner-table__container--row">
          <v-flex md12>
            <RctiFleetAssetTable
              :invoiceType="invoiceType"
              :ownerName="props.item.fleetAssetOwnerName"
              :fleetAssetRctiTable="props.item.fleetAssets"
              :isInnerTable="true"
              :registeredForGst="props.item.registeredForGst"
              @setJobsCheckBoxForClient="setJobsCheckBoxForClient"
              @setPartialSelected="setPartialSelected"
              :selectionIsEnabled="selectionIsEnabled && props.item.invalidFields.length < 1"
              :affiliationType="props.item.affiliationType"
              :ledgerId="props.item.ledgerId"
              :columnWidth="columnWidth"
              :invalidFields="props.item.invalidFields"
            />
          </v-flex>
        </v-layout>
      </div>
    </template>
    <template v-slot:no-data>
      <v-layout justify-center>
        <span v-if="isLoading">Loading...</span>
        <span v-if="!isLoading">No Invoices Available.</span>
      </v-layout>
    </template>
  </v-data-table>
</v-layout>
