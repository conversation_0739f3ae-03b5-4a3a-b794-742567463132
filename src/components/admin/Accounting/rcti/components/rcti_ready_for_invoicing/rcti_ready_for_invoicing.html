<v-layout wrap>
  <v-flex md12>
    <v-layout align-center class="accounting-data-table__actions-bar">
      <v-layout align-center>
        <v-menu right ref="optionsMenu">
          <template v-slot:activator="{ on: menu }">
            <v-tooltip bottom>
              <template v-slot:activator="{ on: tooltip }">
                <v-btn
                  flat
                  icon
                  v-on="{ ...tooltip, ...menu }"
                  class="ma-0"
                  :disabled="isLoading"
                >
                  <v-icon size="16">fas fa-ellipsis-v </v-icon>
                </v-btn>
              </template>
              <span>View Options</span>
            </v-tooltip>
          </template>
          <v-list dense class="v-list-custom">
            <RctiInvoiceSummaryReportDialog
              :isDisabled="invoiceRunStatus !== null"
              :rctiInvoices="rctiInvoices"
            />
            <v-divider></v-divider>
            <v-list-tile v-if="!invoiceRunStatus" @click="editRunParameters">
              <v-list-tile-action>
                <v-icon size="18" class="pr-2">fal fa-sliders-h</v-icon>
              </v-list-tile-action>
              <v-list-tile-content>
                <v-list-tile-title class="pr-2 ma-0">
                  <span
                    class="pr-2"
                    style="text-transform: none; font-weight: 400"
                    >Edit Run Options</span
                  >
                </v-list-tile-title>
              </v-list-tile-content>
            </v-list-tile>
            <ReleaseForEditingConfirmationDialog
              :selectedJobIds="selectedJobIds"
              @closeMenuOptions="closeMenuOptions"
              @setLoading="setLoading"
            />
          </v-list>
        </v-menu>

        <v-layout align-center>
          <v-layout align-center>
            <InvoiceRunParametersSummary
              :parameters="{
                billingCycleIds: billingCycleIds,
                weekEndingDate: weekEndingDate,
                processingDate: processingDate,
                includeAdjustments: includeAdjustments,
              }"
            ></InvoiceRunParametersSummary>
            <v-layout v-if="invoiceRunStatus">
              <CurrentInvoiceStatus :invoiceRunStatus="invoiceRunStatus" />
            </v-layout>
          </v-layout>
        </v-layout>
      </v-layout>
      <v-spacer />
      <v-flex md2 v-if="!invoiceRunStatus || invoiceRunStatus === 'DRAFT'">
        <v-btn
          v-if="!invoiceRunStatus"
          block
          color="info"
          :disabled="numberOfSelectedReadyForDraft === 0 || !isAuthorised() && invoiceRunStatus !== null || isLoading"
          @click="setSelectedToDraft"
          :loading="isLoading"
        >
          Create Draft From Selected
          <template v-slot:loader>
            <span>Loading...</span>
          </template>
        </v-btn>

        <v-btn
          v-if="invoiceRunStatus === 'DRAFT'"
          block
          color="info"
          :disabled="!isAuthorised() || isLoading"
          :loading="isLoading"
          @click="addSelectedToDraft"
        >
          Add Selected To Draft
          <template v-slot:loader>
            <span>Loading...</span>
          </template>
        </v-btn>
      </v-flex>
    </v-layout>
  </v-flex>

  <RctiTable
    ref="rctiTable"
    :rctiInvoices="rctiInvoices"
    :invoiceType="invoiceType"
    :isLoading="isLoading"
  />
</v-layout>
