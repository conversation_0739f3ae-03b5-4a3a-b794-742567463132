import ReleaseForEditingConfirmationDialog from '@/components/admin/Accounting/client_invoice/components/confirmation_dialogs/release_for_editing_confirmation_dialog/index.vue';
import CurrentInvoiceStatus from '@/components/admin/Accounting/components/current_invoice_status/index.vue';
import InvoiceRunParametersSummary from '@/components/admin/Accounting/components/invoice_run_parameters_summary.vue';
import JobListTable from '@/components/admin/Accounting/components/job_list_table/index.vue';
import ProcessingDateUpdateDialog from '@/components/admin/Accounting/components/processing_date_update_dialog.vue';
import RctiInvoiceSummaryReportDialog from '@/components/admin/Accounting/components/rcti_summary_report_dialog/index.vue';
import RctiTable from '@/components/admin/Accounting/rcti/components/rcti_table/index.vue';
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import { getFleetAssetOwnerRctiTableData } from '@/helpers/AccountingHelpers/AccountingHelpers';
import { returnEndOfDayFromEpoch } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { AccountingJobRow } from '@/interface-models/Accounting/AccountingJobRow';
import { FleetAssetRctiTableRow } from '@/interface-models/Accounting/FleetAssetAccountingTable';
import {
  FleetAssetOwnerAccountingTable,
  RctiInvoiceRow,
} from '@/interface-models/Accounting/FleetAssetOwnerAccountingTable';
import { GenerateClientDraftRequest } from '@/interface-models/Accounting/GenerateClientDraftRequest';
import { InvoiceNavigation } from '@/interface-models/Accounting/InvoiceNavigation';
import { InvoiceRunSuccessResponse } from '@/interface-models/Accounting/InvoiceRunSuccessResponse';
import { InvoiceStatus } from '@/interface-models/Accounting/InvoiceStatus';
import { InvoiceDatesRequest } from '@/interface-models/Accounting/invoiceDatesRequest';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { useAdjustmentChargeStore } from '@/store/modules/AdjustmentChargeStore';
import { useFleetAssetOwnerInvoiceStore } from '@/store/modules/FleetAssetOwnerInvoiceStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  components: {
    RctiTable,
    DatePickerBasic,
    JobListTable,
    ProcessingDateUpdateDialog,
    CurrentInvoiceStatus,
    RctiInvoiceSummaryReportDialog,
    ReleaseForEditingConfirmationDialog,
    InvoiceRunParametersSummary,
  },
})
export default class RCTIReadyForInvoicing
  extends Vue
  implements IUserAuthority
{
  @Prop({ default: true }) public accountingIndexLoading: boolean;

  public fleetAssetOwnerInvoiceStore = useFleetAssetOwnerInvoiceStore();

  public invoiceType: InvoiceNavigation = InvoiceNavigation.READY_FOR_INVOICING;

  public weekEndingDate: number | null = null;
  public processingDate: number | null = null;
  public billingCycleIds: number[] | null = null;
  public includeAdjustments: boolean = true;

  public rctiInvoices: RctiInvoiceRow[] = [];
  public selectedFleetAssetOwnerId: string | null = null;
  public isLoadingInvoices: boolean = false;
  public isLoadingUserActionRequest: boolean = false;

  public $refs!: {
    optionsMenu: any;
    rctiTable: any;
  };

  // Handles the completed loaded state for this component. The accounting index makes a request to get the current invoice run status (accountingIndexLoading). This component also has the ability to switch to an all jobs view that makes a request (isLoadingAllREadyForInvoiceJobList)
  get isLoading() {
    return (
      this.accountingIndexLoading ||
      this.isLoadingInvoices ||
      this.isLoadingUserActionRequest
    );
  }

  /** Returns the current stage our invoice run is at. */
  get invoiceRunStatus(): InvoiceStatus | null {
    return this.fleetAssetOwnerInvoiceStore.invoiceRunStatus;
  }

  /**
   * Opens the dialog to update the run parameters. Emits to parent to open the
   * StartInvoiceRunCard
   */
  public editRunParameters() {
    this.$emit('editRunParameters');
  }

  // returns all the users selected jobIds.
  get selectedJobIds(): number[] {
    const selectedFleetAssetOwners = this.rctiInvoices.filter(
      (x: RctiInvoiceRow) => x.isSelected,
    );
    let selectedJobIds: number[] = [];
    for (const invoice of selectedFleetAssetOwners) {
      for (const fleetAsset of invoice.fleetAssets) {
        const jobIds = fleetAsset.jobList
          .filter((x: AccountingJobRow) => x.isSelected)
          .map((y: AccountingJobRow) => y.jobId);
        selectedJobIds = selectedJobIds.concat(jobIds);
      }
    }
    return selectedJobIds;
  }

  public setLoading(isLoading: boolean) {
    this.isLoadingInvoices = isLoading;
  }
  // gets the selected job ids when a user has a client selected and wishes to release the selected jobs back to pricing.
  get selectedJobIdsForSelectedFleetAssetOwner(): number[] {
    if (this.selectedFleetAssetOwnerId === null) {
      return [];
    }
    const rctiInvoice: RctiInvoiceRow | undefined = this.rctiInvoices.find(
      (x: RctiInvoiceRow) =>
        x.fleetAssetOwnerId === this.selectedFleetAssetOwnerId,
    );
    if (!rctiInvoice) {
      return [];
    }
    const fleetAssetInRctiInvoice = rctiInvoice.fleetAssets.find(
      (x: FleetAssetRctiTableRow) =>
        x.fleetAssetId === this.selectedFleetAssetOwnerId,
    );
    if (!fleetAssetInRctiInvoice) {
      return [];
    }
    return fleetAssetInRctiInvoice.jobList
      .filter((x: AccountingJobRow) => x.isSelected)
      .map((y: AccountingJobRow) => y.jobId);
  }

  // sets the users selected weekending date and makes a request to get jobs that are ready for invoicing based on the selected date.
  public async setWeekEndingEpoch(
    weekEndingDate: number | null,
    processingDate?: number | null,
  ): Promise<void> {
    if (weekEndingDate === null || !this.billingCycleIds) {
      console.error(
        `Week ending date or billing cycle ids are null. weekEndingDate: ${weekEndingDate}, billingCycleIds: ${this.billingCycleIds}`,
      );
      return;
    }
    this.weekEndingDate = returnEndOfDayFromEpoch(weekEndingDate);
    this.processingDate = processingDate
      ? returnEndOfDayFromEpoch(processingDate)
      : returnEndOfDayFromEpoch(weekEndingDate);

    if (!this.weekEndingDate || !this.processingDate) {
      return;
    }

    const getReadyForInvoiceRequest: InvoiceDatesRequest = {
      weekEndingDate: this.weekEndingDate,
      processingDate: this.processingDate,
      billingCycleIds: this.billingCycleIds,
      includeAdjustments: this.includeAdjustments,
    };
    this.isLoadingInvoices = true;

    // Send request to get ready for invoicing jobs based off of the users selected week ending date
    const response =
      await this.fleetAssetOwnerInvoiceStore.getReadyForInvoicingJobs(
        getReadyForInvoiceRequest,
      );
    this.setReadyForRctiTableData(response ?? []);
  }

  /**
   * Called from parent component to set the billing cycle ids based on user
   * selection, when initiating a new run. After setting this,
   * setWeekEndingEpoch is called to make the initial request.
   * @param billingCycleIds - The billing cycle ids to set.
   */
  public setBillingCycleIds(billingCycleIds: number[]) {
    this.billingCycleIds = billingCycleIds;
  }

  public updateProcessingDate(processingDate: number) {
    this.setWeekEndingEpoch(this.weekEndingDate, processingDate);
  }

  // Because the select tile in our menu is a component it does not
  // automatically close when selected. We must close the menu manually. Method
  // call Emitted from components that act as a tile in our menu.
  public closeMenuOptions() {
    if (this.$refs.optionsMenu) {
      this.$refs.optionsMenu.isActive = false;
    }
  }

  // request to generate a draft run.
  public setSelectedToDraft() {
    if (
      this.weekEndingDate === null ||
      this.processingDate === null ||
      !this.billingCycleIds
    ) {
      console.error(
        `Week ending date or billing cycle ids are null. Week ending date: ${this.weekEndingDate} - Billing cycle ids: ${this.billingCycleIds}`,
      );
      return;
    }
    this.isLoadingUserActionRequest = true;
    const generateClientDraftRequest: GenerateClientDraftRequest = {
      weekEndingDate: this.weekEndingDate,
      processingDate: this.processingDate,
      billingCycleIds: this.billingCycleIds,
      jobIds: this.selectedJobIds,
      includeAdjustments: this.includeAdjustments,
    };
    this.fleetAssetOwnerInvoiceStore.generateRctiDraftRun(
      generateClientDraftRequest,
    );
  }

  // return the total number of selected clients that will be sent to draft. Displayed in "CREATE DRAFT" button
  get numberOfSelectedReadyForDraft() {
    let numberOfSelectedFleetAssets = 0;
    for (const rcti of this.rctiInvoices) {
      for (const fleetAsset of rcti.fleetAssets) {
        const selectedJob = fleetAsset.jobList.find(
          (x: AccountingJobRow) => x.isSelected,
        );
        if (selectedJob) {
          numberOfSelectedFleetAssets++;
        }
      }
    }
    return numberOfSelectedFleetAssets;
  }

  public actionIncludeAdjustmentsFlag() {
    this.setWeekEndingEpoch(this.weekEndingDate, this.processingDate);
  }

  // makes request to add selected jobs to the current existing draft
  public addSelectedToDraft() {
    this.isLoadingUserActionRequest = true;
    this.fleetAssetOwnerInvoiceStore.addJobsToDraftRun(this.selectedJobIds);
  }

  // resets the user edits back to the table datas default state.
  public resetClientInvoiceSelections() {
    for (const rctiInvoice of this.rctiInvoices) {
      rctiInvoice.isSelected = false;
      rctiInvoice.partialSelected = false;
      for (const fleetAsset of rctiInvoice.fleetAssets) {
        for (const job of fleetAsset.jobList) {
          job.isSelected = false;
        }
      }
    }
  }

  public isAuthorised(): boolean {
    return hasAdminOrHeadOfficeRole();
  }

  // converts our ClientAccountingTable[] into ClientInvoiceRow[]
  public setReadyForRctiTableData(
    response: FleetAssetOwnerAccountingTable[],
  ): void {
    if (!this.weekEndingDate) {
      return;
    }
    this.rctiInvoices = getFleetAssetOwnerRctiTableData(
      response,
      this.weekEndingDate,
      useAdjustmentChargeStore().adjustmentChargeCategories,
    );
    this.isLoadingInvoices = false;
  }

  public setResponseForUserAction(
    response: InvoiceRunSuccessResponse,
    isNewDraftRun: boolean,
    isJobAddedToDraft: boolean,
  ) {
    // close any selections the user has made as we will be updating the view.
    this.closeAndResetExpandedTable();

    // if there was an error force users to refresh page.
    if (!response || !response.success) {
      this.isLoadingUserActionRequest = false;
      showNotification(GENERIC_ERROR_MESSAGE);
      this.setInitialWeekEndingDate();
      return;
    }

    // set whether the current view is the user who made the request or any other division users.
    const responseIsForDivisionUsers =
      response.userName !== sessionManager.getUserName();

    // handle response for division users.
    if (responseIsForDivisionUsers) {
      // show notification specific to the update type
      if (isNewDraftRun) {
        showNotification('A new draft is now available.', {
          title: 'Draft Run',
          type: HealthLevel.INFO,
        });
      }
      if (isJobAddedToDraft) {
        showNotification('Draft Updated.', {
          type: HealthLevel.INFO,
        });
      }
      // reload the users view with the correct draft dates
      this.setWeekEndingEpoch(response.weekEndingDate, response.processingDate);
      return;
    }

    // handle response for user who made the request.
    if (!responseIsForDivisionUsers) {
      // show notification specific to the update type
      if (isNewDraftRun) {
        showNotification('Successfully created.', {
          title: 'Draft Run',
          type: HealthLevel.SUCCESS,
        });
      }
      if (isJobAddedToDraft) {
        showNotification('Successfully updated.', {
          title: 'Draft Run',
          type: HealthLevel.SUCCESS,
        });
      }
      // push them to the Draft tab.
      this.$emit('setCurrentView', InvoiceNavigation.DRAFT);
    }
    this.isLoadingUserActionRequest = false;
  }

  get currentRunIncludeAdjustments(): boolean | null {
    return this.fleetAssetOwnerInvoiceStore.includeAdjustments;
  }

  // If the current invoice run status is equal to DRAFT we do not allow the user to edit the week ending date. When this is the case the week ending date is set to the current week ending date defined in the draft.
  public setInitialWeekEndingDate() {
    this.includeAdjustments =
      this.currentRunIncludeAdjustments === null
        ? true
        : this.currentRunIncludeAdjustments;

    this.weekEndingDate = this.fleetAssetOwnerInvoiceStore.weekEndingDate;
    this.processingDate = this.fleetAssetOwnerInvoiceStore.processingDate;
    this.billingCycleIds = this.fleetAssetOwnerInvoiceStore.billingCycleIds;
    this.setWeekEndingEpoch(this.weekEndingDate, this.processingDate);
  }

  // manually update the expanded prop in the child component ClientInvoiceTable
  public closeAndResetExpandedTable() {
    this.selectedFleetAssetOwnerId = null;
  }

  public setReleasedForEditingJobResponse() {
    this.closeAndResetExpandedTable();
    this.setInitialWeekEndingDate();
  }

  public isAuthorisedToCreateAndEditDraft(): boolean {
    return hasAdminOrHeadOfficeRole();
  }

  /** Handles the response from generating an RCTI Draft run. */
  private handleDraftRunResponse(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, true, false);
    }
  }

  /**
   * Handles the response from adding jobs to an existing dummy run
   */
  private handleDraftAddJob(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, false, true);
    }
  }

  // set our components store subscription and initialise the weekEnding date
  public beforeMount() {
    this.setInitialWeekEndingDate();
    this.isLoadingInvoices = true;
    Mitt.on('rctiDraftRunResponse', this.handleDraftRunResponse);
    Mitt.on('cancelRctiDraftRunResponse', this.setInitialWeekEndingDate);
    Mitt.on('rctiDraftAddJobResponse', this.handleDraftAddJob);
    Mitt.on(
      'releasedAccountingJobsToCompleted',
      this.setReleasedForEditingJobResponse,
    );
  }

  public beforeDestroy() {
    Mitt.off('rctiDraftRunResponse', this.handleDraftRunResponse);
    Mitt.off('cancelRctiDraftRunResponse', this.setInitialWeekEndingDate);
    Mitt.off('rctiDraftAddJobResponse', this.handleDraftAddJob);
    Mitt.off(
      'releasedAccountingJobsToCompleted',
      this.setReleasedForEditingJobResponse,
    );
  }
}
