import DraftJobRemovalConfirmationDialog from '@/components/admin/Accounting/client_invoice/components/confirmation_dialogs/draft_job_removal_confirmation_dialog.vue';
import CancelDraftDialog from '@/components/admin/Accounting/components/cancel_draft_dialog.vue';
import CurrentInvoiceStatus from '@/components/admin/Accounting/components/current_invoice_status/index.vue';
import CustomEmailMessageDialog from '@/components/admin/Accounting/components/custom_email_message_dialog/index.vue';
import CustomEmailMessageToolTip from '@/components/admin/Accounting/components/custom_email_message_tooltip.vue';
import InvoiceEventHistoryDialog from '@/components/admin/Accounting/components/invoice_event_history_dialog.vue';
import InvoiceRunParametersSummary from '@/components/admin/Accounting/components/invoice_run_parameters_summary.vue';
import ProcessingDateUpdateDialog from '@/components/admin/Accounting/components/processing_date_update_dialog.vue';
import RctiInvoiceSummaryReportDialog from '@/components/admin/Accounting/components/rcti_summary_report_dialog/index.vue';
import RctiTable from '@/components/admin/Accounting/rcti/components/rcti_table/index.vue';
import { getFleetAssetOwnerRctiTableData } from '@/helpers/AccountingHelpers/AccountingHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { AccountingJobRow } from '@/interface-models/Accounting/AccountingJobRow';
import { FleetAssetRctiTableRow } from '@/interface-models/Accounting/FleetAssetAccountingTable';
import {
  FleetAssetOwnerAccountingTable,
  RctiInvoiceRow,
} from '@/interface-models/Accounting/FleetAssetOwnerAccountingTable';
import { InvoiceEvent } from '@/interface-models/Accounting/InvoiceEvent';
import { InvoiceNavigation } from '@/interface-models/Accounting/InvoiceNavigation';
import { InvoiceRunSuccessResponse } from '@/interface-models/Accounting/InvoiceRunSuccessResponse';
import { InvoiceStatus } from '@/interface-models/Accounting/InvoiceStatus';
import { RctiRunAccountingTable } from '@/interface-models/Accounting/RctiRunAccountingTable';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { useAdjustmentChargeStore } from '@/store/modules/AdjustmentChargeStore';
import { useFleetAssetOwnerInvoiceStore } from '@/store/modules/FleetAssetOwnerInvoiceStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  components: {
    RctiTable,
    DraftJobRemovalConfirmationDialog,
    InvoiceEventHistoryDialog,
    ProcessingDateUpdateDialog,
    CancelDraftDialog,
    CurrentInvoiceStatus,
    RctiInvoiceSummaryReportDialog,
    CustomEmailMessageDialog,
    CustomEmailMessageToolTip,
    InvoiceRunParametersSummary,
  },
})
export default class RctiDraft extends Vue implements IUserAuthority {
  @Prop({ default: true }) public accountingIndexLoading: boolean;

  public fleetAssetOwnerInvoiceStore = useFleetAssetOwnerInvoiceStore();

  public weekEndingDate: number | null = null;
  public processingDate: number | null = null;
  public billingCycleIds: number[] | null = null;
  public rctiInvoices: RctiInvoiceRow[] = [];
  public invoiceEventHistory: InvoiceEvent[] = [];
  public invoiceType: InvoiceNavigation = InvoiceNavigation.DRAFT;
  public isDraftEditMode: boolean = false;
  public isLoadingDraft: boolean = false;
  public isLoadingUserActionRequest: boolean = false;
  public columnWidth: string = '';

  public $refs!: {
    rctiTable: any;
    optionsMenu: any;
  };

  // returns all the users selected jobIds.
  get selectedJobIds(): number[] {
    let selectedJobIds: number[] = [];
    for (const invoice of this.rctiInvoices) {
      for (const fleetAsset of invoice.fleetAssets) {
        const jobIds = fleetAsset.jobList
          .filter((x: AccountingJobRow) => x.isSelected)
          .map((y: AccountingJobRow) => y.jobId);

        selectedJobIds = selectedJobIds.concat(jobIds);
      }
    }
    return selectedJobIds;
  }

  // returns overall loading state for component
  get isLoading() {
    return (
      this.accountingIndexLoading ||
      this.isLoadingDraft ||
      this.isLoadingUserActionRequest
    );
  }

  // returns the current stage our invoice run is at.
  get invoiceRunStatus(): InvoiceStatus | null {
    return this.fleetAssetOwnerInvoiceStore.invoiceRunStatus;
  }

  get customEmailMessage(): string {
    return this.fleetAssetOwnerInvoiceStore.customEmailMessage;
  }

  // returns the total number of jobs currently in the draft run
  get totalNumberOfJobsInDraft(): number {
    let totalNumberOfJobsInDraft = 0;
    for (const rctiInvoice of this.rctiInvoices) {
      for (const fleetAsset of rctiInvoice.fleetAssets) {
        totalNumberOfJobsInDraft += fleetAsset.jobList.length;
      }
    }
    return totalNumberOfJobsInDraft;
  }

  // Because the select tile in our menu is a component it does not automatically close when selected. We must close the menu manually. Method call Emitted from components that act as a tile in our menu.
  public closeMenuOptions() {
    if (this.$refs.optionsMenu) {
      this.$refs.optionsMenu.isActive = false;
    }
  }

  // set local variables from our main draft run response
  public setDraftRunResponse(response: RctiRunAccountingTable): void {
    this.weekEndingDate = response.weekEndingDate;
    this.processingDate = response.processingDate;
    this.billingCycleIds = response.billingCycleIds;
    this.fleetAssetOwnerInvoiceStore.setIncludeAdjustments(
      response.includeAdjustments,
    );
    this.invoiceEventHistory = response.eventList;
    this.setTableData(response.fleetAssetOwnerAccountingTable);
    this.isLoadingDraft = false;
  }

  public setTableData(
    fleetAssetOwnerAccountingTableData: FleetAssetOwnerAccountingTable[],
  ): void {
    if (!this.weekEndingDate && fleetAssetOwnerAccountingTableData.length > 0) {
      return;
    }
    this.rctiInvoices = getFleetAssetOwnerRctiTableData(
      fleetAssetOwnerAccountingTableData,
      this.weekEndingDate,
      useAdjustmentChargeStore().adjustmentChargeCategories,
    );
  }

  // initial request to get the current draft run
  public async getDraftRun() {
    this.isLoadingDraft = true;
    // Send request and handle response
    const response = await this.fleetAssetOwnerInvoiceStore.getOwnerInvoiceRun(
      InvoiceStatus.DRAFT,
    );
    if (response?.type !== InvoiceStatus.DRAFT) {
      return;
    }
    this.setDraftRunResponse(response);
  }

  public cancelDraftRun() {
    this.isLoadingUserActionRequest = true;
    this.fleetAssetOwnerInvoiceStore.cancelDraftRun();
  }

  public removeSelectedJobsFromDraft() {
    this.fleetAssetOwnerInvoiceStore.removeJobsFromDraftRun(
      this.selectedJobIds,
    );
  }

  public setDraftJobRemoval(
    jobRemovalResponse: InvoiceRunSuccessResponse | null,
  ) {
    if (!jobRemovalResponse) {
      // something went wrong
      return;
    }
    this.getDraftRun();
  }

  public approveDraftRun() {
    this.isLoadingUserActionRequest = true;
    this.fleetAssetOwnerInvoiceStore.approveDraftRun();
  }

  public updateProcessingDate(processingDate: number) {
    this.processingDate = processingDate;
    this.isLoadingUserActionRequest = true;
    this.fleetAssetOwnerInvoiceStore.updateDraftProcessingDate(
      this.processingDate,
    );
  }

  // find and replace the row in our table with the updated data. This is utilised when a user is editing/adding a adjustment charge
  public updateRctiInvoice(
    response: {
      success: boolean;
      fleetAssetOwnerAccountingTable: FleetAssetOwnerAccountingTable;
    } | null,
  ) {
    if (!response?.success) {
      return;
    }
    const fleetAssetOwnerAccountingTable =
      response.fleetAssetOwnerAccountingTable;
    const updateAsRctiInvoiceRow: RctiInvoiceRow[] =
      getFleetAssetOwnerRctiTableData(
        [fleetAssetOwnerAccountingTable],
        this.processingDate,
        useAdjustmentChargeStore().adjustmentChargeCategories,
      );
    const updatedRctiInvoiceRow = updateAsRctiInvoiceRow[0];
    const indexToUpdate: number = this.rctiInvoices.findIndex(
      (x: RctiInvoiceRow) => x.ledgerId === updatedRctiInvoiceRow.ledgerId,
    );
    const rctiInvoiceRowToUpdate: RctiInvoiceRow =
      this.rctiInvoices[indexToUpdate];
    updatedRctiInvoiceRow.isSelected = rctiInvoiceRowToUpdate.isSelected;
    updatedRctiInvoiceRow.partialSelected =
      rctiInvoiceRowToUpdate.partialSelected;
    for (const fleetAsset of rctiInvoiceRowToUpdate.fleetAssets) {
      const updatedFleetAsset: FleetAssetRctiTableRow | undefined =
        updatedRctiInvoiceRow.fleetAssets.find(
          (x: FleetAssetRctiTableRow) =>
            x.fleetAssetId === fleetAsset.fleetAssetId,
        );
      if (!updatedFleetAsset) {
        continue;
      }
      updatedFleetAsset.isSelected = fleetAsset.isSelected;
      updatedFleetAsset.partialSelected = fleetAsset.partialSelected;
    }

    this.rctiInvoices.splice(indexToUpdate, 1, updatedRctiInvoiceRow);
  }

  public setResponseForUserAction(
    response: InvoiceRunSuccessResponse,
    options: {
      isDraftCancel?: boolean;
      isProcessingDateUpdate?: boolean;
      isDraftApproved?: boolean;
      isNewDraftRun?: boolean;
      isDummyCancel?: boolean;
      isApprovedCancel?: boolean;
    } = {},
  ): void {
    // if there was an error force users to refresh page.
    if (!response || !response.success) {
      this.isLoadingUserActionRequest = false;
      showNotification(GENERIC_ERROR_MESSAGE);
      this.getDraftRun();
      return;
    }
    const {
      isDraftCancel = false,
      isProcessingDateUpdate = false,
      isDraftApproved = false,
      isNewDraftRun = false,
      isDummyCancel = false,
      isApprovedCancel = false,
    } = options;

    // If draft is canceled by user we should update our invoice status and relevant invoice dates in ClientInvoiceModule
    if (isDraftCancel) {
      this.fleetAssetOwnerInvoiceStore.setInvoiceRunStatus(null);
      this.fleetAssetOwnerInvoiceStore.setProcessingDate(null);
      this.fleetAssetOwnerInvoiceStore.setWeekEndingDate(null);
      this.fleetAssetOwnerInvoiceStore.setBillingCycleIds(null);
    }

    // set whether the current view is the user who made the request or any other division users.
    const responseIsForDivisionUsers =
      response.userName !== sessionManager.getUserName();

    // handle response for division users.
    if (responseIsForDivisionUsers) {
      // show notification specific to the update type

      if (isNewDraftRun) {
        showNotification('New draft available.', {
          title: 'Draft Run',
          type: HealthLevel.INFO,
        });
      }
      if (isDraftApproved) {
        this.isLoadingUserActionRequest = false;
        showNotification('New dummy run available.', {
          title: 'Dummy Run',
          type: HealthLevel.INFO,
        });
      }

      if (isProcessingDateUpdate) {
        showNotification('Processing Date Updated.', {
          title: 'Draft Run',
          type: HealthLevel.INFO,
        });
      }

      if (isDraftCancel) {
        showNotification('Draft cancelled.', {
          title: 'Draft Run',
          type: HealthLevel.INFO,
        });
      }

      if (isDummyCancel) {
        showNotification('Dummy cancelled.', {
          title: 'Dummy Run',
          type: HealthLevel.INFO,
        });
      }

      if (isApprovedCancel) {
        showNotification('Approved cancelled.', {
          title: 'Approved Run',
          type: HealthLevel.INFO,
        });
      }
    }
    // handle response for user who made the request.
    if (!responseIsForDivisionUsers) {
      // show notification specific to the update type
      if (isDraftApproved) {
        showNotification('Successfully Approved.', {
          title: 'Draft Run',
          type: HealthLevel.SUCCESS,
        });
        // Push user to the DUMMY tab.
        this.$emit('setCurrentView', InvoiceNavigation.DUMMY);
        return;
      }

      if (isProcessingDateUpdate) {
        showNotification('Processing Date Updated.', {
          title: 'Draft Run',
          type: HealthLevel.SUCCESS,
        });
      }

      if (isDraftCancel) {
        showNotification('Successfully Cancelled.', {
          title: 'Draft Run',
          type: HealthLevel.SUCCESS,
        });
        this.$emit('setCurrentView', InvoiceNavigation.READY_FOR_INVOICING);
      }
    }
    this.isLoadingUserActionRequest = false;
    this.getDraftRun();
  }

  public isAuthorised(): boolean {
    return hasAdminOrHeadOfficeRole();
  }

  /** Handles the response from generating an RCTI Draft run. */
  private handleDraftRunResponse(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, { isNewDraftRun: true });
    }
  }
  /**
   * Mitt callback for cancelling a Client Invoice Draft run. Used in
   * mitt listener.
   */
  private handleDraftCancel(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, { isDraftCancel: true });
    }
  }
  /**
   * Mitt callback for updating the processing date of a Client Invoice
   * Draft
   */
  private handleDraftProcessingDate(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, { isProcessingDateUpdate: true });
    }
  }
  /** Mitt callback for approving a DRAFT run (transition to DUMMY) */
  private handleDraftApproval(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, { isDraftApproved: true });
    }
  }
  /** Mitt callback for cancelling a DUMMY run (transition to DRAFT) */
  private handleDummyCancel(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, { isDummyCancel: true });
    }
  }
  /** Mitt callback for cancelling am APPROVED run (transition to DRAFT) */
  private handleApprovedCancel(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, { isApprovedCancel: true });
    }
  }

  public mounted() {
    this.getDraftRun();
    Mitt.on('rctiDraftRunResponse', this.handleDraftRunResponse);
    Mitt.on('approveRctiDraftRunResponse', this.handleDraftApproval);
    Mitt.on('cancelRctiDummyRunResponse', this.handleDummyCancel);
    Mitt.on('cancelRctiLiveRunResponse', this.handleApprovedCancel);
    Mitt.on('cancelRctiDraftRunResponse', this.handleDraftCancel);
    Mitt.on('rctiDraftRemoveJobResponse', this.setDraftJobRemoval);
    Mitt.on(
      'updateRctiDraftProcessingDateResponse',
      this.handleDraftProcessingDate,
    );
    Mitt.on('adjustmentChargeHistoryLedgerResponse', this.updateRctiInvoice);
  }

  public beforeDestroy() {
    Mitt.off('rctiDraftRunResponse', this.handleDraftRunResponse);
    Mitt.off('approveRctiDraftRunResponse', this.handleDraftApproval);
    Mitt.off('cancelRctiDummyRunResponse', this.handleDummyCancel);
    Mitt.off('cancelRctiLiveRunResponse', this.handleApprovedCancel);
    Mitt.off('cancelRctiDraftRunResponse', this.handleDraftCancel);
    Mitt.off('rctiDraftRemoveJobResponse', this.setDraftJobRemoval);
    Mitt.off(
      'updateRctiDraftProcessingDateResponse',
      this.handleDraftProcessingDate,
    );
    Mitt.off('adjustmentChargeHistoryLedgerResponse', this.updateRctiInvoice);
  }
}
