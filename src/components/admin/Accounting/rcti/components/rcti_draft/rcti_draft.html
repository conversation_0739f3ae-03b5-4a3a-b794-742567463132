<v-layout wrap>
  <v-flex md12>
    <v-layout align-center class="accounting-data-table__actions-bar">
      <v-menu right ref="optionsMenu">
        <template v-slot:activator="{ on: menu }">
          <v-tooltip bottom>
            <template v-slot:activator="{ on: tooltip }">
              <v-btn
                flat
                icon
                v-on="{ ...tooltip, ...menu }"
                class="ma-0"
                :disabled="invoiceRunStatus !== 'DRAFT' || isLoading"
              >
                <v-icon size="16">fas fa-ellipsis-v </v-icon>
              </v-btn>
            </template>
            <span>View Options</span>
          </v-tooltip>
        </template>
        <v-list dense class="v-list-custom">
          <RctiInvoiceSummaryReportDialog :rctiInvoices="rctiInvoices" />

          <v-divider></v-divider>
          <ProcessingDateUpdateDialog
            v-if="processingDate !== null && invoiceRunStatus === 'DRAFT'"
            :isDraft="invoiceRunStatus === 'DRAFT'"
            @closeMenuOptions="closeMenuOptions"
            :processingDate.sync="processingDate"
            @updateProcessingDate="updateProcessingDate"
            :isLoadingUserActionRequest.sync="isLoadingUserActionRequest"
          />
          <v-divider></v-divider>

          <CustomEmailMessageDialog
            :ledgerType="'RCTI'"
            :customEmailMessage=" customEmailMessage"
          />
          <v-divider></v-divider>
          <DraftJobRemovalConfirmationDialog
            @closeMenuOptions="closeMenuOptions"
            :selectedJobIds="selectedJobIds"
            :totalNumberOfJobsInDraft.sync="totalNumberOfJobsInDraft"
            @removeSelectedJobsFromDraft="removeSelectedJobsFromDraft"
            @cancelDraftRun="cancelDraftRun"
          />

          <v-divider></v-divider>

          <InvoiceEventHistoryDialog
            v-if="invoiceRunStatus === 'DRAFT'"
            @closeMenuOptions="closeMenuOptions"
            :invoiceEventHistory="invoiceEventHistory"
            :invoiceRunStatus="invoiceRunStatus"
          />
          <v-divider></v-divider>
          <CancelDraftDialog
            v-if="invoiceRunStatus === 'DRAFT'"
            @closeMenuOptions="closeMenuOptions"
            @cancelDraftRun="cancelDraftRun"
          />
        </v-list>
      </v-menu>
      <v-layout v-if="invoiceRunStatus === 'DRAFT'">
        <InvoiceRunParametersSummary
          :parameters="{
            billingCycleIds: billingCycleIds,
            weekEndingDate: weekEndingDate,
            processingDate: processingDate,
            includeAdjustments: fleetAssetOwnerInvoiceStore.includeAdjustments,
          }"
        ></InvoiceRunParametersSummary>
      </v-layout>

      <CurrentInvoiceStatus
        v-if="invoiceRunStatus !== 'DRAFT'"
        :invoiceRunStatus="invoiceRunStatus"
      />

      <v-spacer />
      <CustomEmailMessageToolTip
        v-if="customEmailMessage"
        :customEmailMessage="customEmailMessage"
        class="pr-3"
      />
      <v-flex md2>
        <v-btn
          block
          color="info"
          :disabled="isLoading || invoiceRunStatus !== 'DRAFT' || rctiInvoices.length < 1 || !isAuthorised()"
          :loading="isLoading"
          @click="approveDraftRun"
        >
          Generate Dummy
          <template v-slot:loader>
            <span>Loading...</span>
          </template>
        </v-btn>
      </v-flex>
    </v-layout>
  </v-flex>

  <v-flex md12 class="reviewed-jobs-container">
    <RctiTable
      ref="rctiTable"
      :rctiInvoices="rctiInvoices"
      :invoiceType="invoiceType"
      :isLoading="isLoading"
    />
  </v-flex>
</v-layout>
