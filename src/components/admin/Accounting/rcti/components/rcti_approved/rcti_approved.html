<v-layout wrap>
  <v-flex md12>
    <v-layout align-center class="accounting-data-table__actions-bar">
      <v-menu right ref="optionsMenu">
        <template v-slot:activator="{ on: menu }">
          <v-tooltip bottom>
            <template v-slot:activator="{ on: tooltip }">
              <v-btn
                flat
                icon
                v-on="{ ...tooltip, ...menu }"
                class="ma-0"
                :disabled="invoiceRunStatus !== 'APPROVED' || isLoading"
              >
                <v-icon size="16">fas fa-ellipsis-v </v-icon>
              </v-btn>
            </template>
            <span>View Options</span>
          </v-tooltip>
        </template>
        <v-list dense class="v-list-custom">
          <RctiInvoiceSummaryReportDialog :rctiInvoices="rctiInvoices" />
          <v-divider></v-divider>
          <InvoiceEventHistoryDialog
            :invoiceEventHistory="invoiceEventHistory"
            :invoiceRunStatus="invoiceRunStatus"
            @closeMenuOptions="closeMenuOptions"
          />
          <v-divider></v-divider>

          <CancelRunConfirmationDialog
            v-if="invoiceRunStatus === 'APPROVED'"
            :runType="'Approved'"
            @closeMenuOptions="closeMenuOptions"
            @cancelRun="cancelApprovedRun"
          />
        </v-list>
      </v-menu>

      <v-layout v-if="invoiceRunStatus === 'APPROVED'">
        <InvoiceRunParametersSummary
          :parameters="{
            billingCycleIds: billingCycleIds,
            weekEndingDate: weekEndingDate,
            processingDate: processingDate,
            includeAdjustments: fleetAssetOwnerInvoiceStore.includeAdjustments,
          }"
        ></InvoiceRunParametersSummary>
      </v-layout>

      <v-layout v-if="invoiceRunStatus !== 'APPROVED'">
        <CurrentInvoiceStatus :invoiceRunStatus="invoiceRunStatus" />
      </v-layout>
      <v-spacer />
      <CustomEmailMessageToolTip
        v-if="customEmailMessage"
        :customEmailMessage="customEmailMessage"
        class="pr-3"
      />
      <v-flex md2 style="height: 48px">
        <ConfirmationDialog
          buttonText="Approve and Email RCTI's"
          message="I declare that:"
          title="Confirm Approval for Live Invoice"
          @confirm="approveAndEmailRctiList"
          :isSmallButton="false"
          :buttonDisabled=" !isAuthorisedToGenerateLiveInvoice() || invoiceRunStatus !== 'APPROVED'"
          :isOutlineButton="false"
          :isBlockButton="true"
          :buttonColor="'info'"
          :confirmationButtonText="'Approve and Send'"
          :isCheckboxList="true"
          :checkboxLabelList="approveAndEmailConfirmationCheckBoxList"
          :dialogIsActive="true"
        ></ConfirmationDialog>
      </v-flex>
    </v-layout>
  </v-flex>

  <v-flex md12 class="reviewed-jobs-container">
    <RctiTable
      ref="rctiTable"
      :rctiInvoices="rctiInvoices"
      :invoiceType="invoiceType"
      :isLoading="isLoading"
    />
  </v-flex>
</v-layout>
