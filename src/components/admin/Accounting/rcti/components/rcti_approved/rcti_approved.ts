import DraftJobRemovalConfirmationDialog from '@/components/admin/Accounting/client_invoice/components/confirmation_dialogs/draft_job_removal_confirmation_dialog.vue';
import CancelRunConfirmationDialog from '@/components/admin/Accounting/components/cancel_run_confirmation_dialog.vue';
import CurrentInvoiceStatus from '@/components/admin/Accounting/components/current_invoice_status/index.vue';
import CustomEmailMessageToolTip from '@/components/admin/Accounting/components/custom_email_message_tooltip.vue';
import InvoiceEventHistoryDialog from '@/components/admin/Accounting/components/invoice_event_history_dialog.vue';
import InvoiceRunParametersSummary from '@/components/admin/Accounting/components/invoice_run_parameters_summary.vue';
import RctiInvoiceSummaryReportDialog from '@/components/admin/Accounting/components/rcti_summary_report_dialog/index.vue';
import RctiTable from '@/components/admin/Accounting/rcti/components/rcti_table/index.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import { getFleetAssetOwnerRctiTableData } from '@/helpers/AccountingHelpers/AccountingHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  hasAdminOrHeadOfficeRole,
  hasAdminOrTeamLeaderOrBranchManagerRole,
} from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { CurrentInvoiceRunTypeResponse } from '@/interface-models/Accounting/CurrentInvoiceRunTypeResponse';
import {
  FleetAssetOwnerAccountingTable,
  RctiInvoiceRow,
} from '@/interface-models/Accounting/FleetAssetOwnerAccountingTable';
import { InvoiceEmailSentUpdate } from '@/interface-models/Accounting/InvoiceEmailSentUpdate';
import { InvoiceEvent } from '@/interface-models/Accounting/InvoiceEvent';
import { InvoiceNavigation } from '@/interface-models/Accounting/InvoiceNavigation';
import { InvoiceRunSuccessResponse } from '@/interface-models/Accounting/InvoiceRunSuccessResponse';
import { InvoiceStatus } from '@/interface-models/Accounting/InvoiceStatus';
import { RctiRunAccountingTable } from '@/interface-models/Accounting/RctiRunAccountingTable';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { useAdjustmentChargeStore } from '@/store/modules/AdjustmentChargeStore';
import { useFleetAssetOwnerInvoiceStore } from '@/store/modules/FleetAssetOwnerInvoiceStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  components: {
    RctiTable,
    DraftJobRemovalConfirmationDialog,
    InvoiceEventHistoryDialog,
    ConfirmationDialog,
    CurrentInvoiceStatus,
    CancelRunConfirmationDialog,
    CustomEmailMessageToolTip,
    RctiInvoiceSummaryReportDialog,
    InvoiceRunParametersSummary,
  },
})
export default class RctiApproved extends Vue implements IUserAuthority {
  @Prop({ default: true }) public accountingIndexLoading: boolean;

  public fleetAssetOwnerInvoiceStore = useFleetAssetOwnerInvoiceStore();

  public weekEndingDate: number | null = null;
  public processingDate: number | null = null;
  public billingCycleIds: number[] | null = null;

  public rctiInvoices: RctiInvoiceRow[] = [];
  public invoiceEventHistory: InvoiceEvent[] = [];
  public selectedClientId: string | null = null;
  public invoiceType: InvoiceNavigation = InvoiceNavigation.APPROVED;
  public isLoadingApproved: boolean = true;
  public isLoadingUserActionRequest: boolean = false;

  public $refs!: {
    optionsMenu: any;
    rctiTable: any;
  };
  // list of checkboxes the user will need to confirm before approving and sending the invoices
  get approveAndEmailConfirmationCheckBoxList(): string[] {
    const checkboxList = [
      'I understand that an email will be sent to all Subcontractors listed on this page',
    ];
    if (
      this.weekEndingDate !== null &&
      this.processingDate !== null &&
      this.weekEndingDate > this.processingDate
    ) {
      checkboxList.push(
        'I understand that the processing date is before the week ending date.',
      );
    }
    // If there is a custom email message we should add this to our user checkbox list.
    if (this.customEmailMessage) {
      checkboxList.push('I understand that there is a custom email message.');
    }
    checkboxList.push(
      'I understand that by approving the Live Run, they will not be editable in the future',
    );
    return checkboxList;
  }

  get customEmailMessage(): string {
    return this.fleetAssetOwnerInvoiceStore.customEmailMessage;
  }

  public setTableData(
    fleetAssetOwnerAccountingTableData: FleetAssetOwnerAccountingTable[],
  ): void {
    if (!this.weekEndingDate && fleetAssetOwnerAccountingTableData.length > 0) {
      return;
    }
    this.rctiInvoices = getFleetAssetOwnerRctiTableData(
      fleetAssetOwnerAccountingTableData,
      this.weekEndingDate,
      useAdjustmentChargeStore().adjustmentChargeCategories,
    );
  }

  // returns the current stage our invoice run is at.
  get invoiceRunStatus(): InvoiceStatus | null {
    return this.fleetAssetOwnerInvoiceStore.invoiceRunStatus;
  }

  get isLoading(): boolean {
    return (
      this.accountingIndexLoading ||
      this.isLoadingApproved ||
      this.isLoadingUserActionRequest
    );
  }

  /**
   * Request for approving the current APPROVED run to be finalised and sent,
   * which will move it to the SENT status.
   */
  public approveAndEmailRctiList(): void {
    this.fleetAssetOwnerInvoiceStore.approveLiveRun();
  }

  // Because the select tile in our menu is a component it does not automatically close when selected. We must close the menu manually. Method call Emitted from components that act as a tile in our menu.
  public closeMenuOptions() {
    if (this.$refs.optionsMenu) {
      this.$refs.optionsMenu.isActive = false;
    }
  }

  // Sends request to get the current table data for this run status
  public async getApprovedRun() {
    this.isLoadingApproved = true;
    // Send request and handle response
    const response = await this.fleetAssetOwnerInvoiceStore.getOwnerInvoiceRun(
      InvoiceStatus.APPROVED,
    );
    this.setApprovedRunResponse(response);
  }

  public setApprovedRunResponse(response: RctiRunAccountingTable | null) {
    if (!response) {
      return;
    }
    this.weekEndingDate = response.weekEndingDate;
    this.processingDate = response.processingDate;
    this.billingCycleIds = response.billingCycleIds;
    this.invoiceEventHistory = response.eventList;
    this.setTableData(response.fleetAssetOwnerAccountingTable);
    this.isLoadingApproved = false;
  }

  // Request to cancel the approved invoice run
  public cancelApprovedRun() {
    this.isLoadingUserActionRequest = true;
    this.fleetAssetOwnerInvoiceStore.cancelApprovedRun();
  }

  public setResponseForUserAction(
    response: InvoiceRunSuccessResponse,
    isApprovedCancel: boolean,
    isDummyApproved: boolean,
  ) {
    // if there was an error force users to refresh page.
    if (!response || !response.success) {
      this.isLoadingUserActionRequest = false;
      showNotification(GENERIC_ERROR_MESSAGE);
      this.getApprovedRun();
      return;
    }

    // if draft is canceled by user we should update our invoice status and relevant invoice dates in ClientInvoiceModule
    if (isApprovedCancel) {
      this.fleetAssetOwnerInvoiceStore.setInvoiceRunStatus(InvoiceStatus.DRAFT);
      this.fleetAssetOwnerInvoiceStore.setWeekEndingDate(
        response.weekEndingDate,
      );
      this.fleetAssetOwnerInvoiceStore.setProcessingDate(
        response.processingDate,
      );
      this.fleetAssetOwnerInvoiceStore.setBillingCycleIds(
        response.billingCycleIds,
      );
    }

    // set whether the current view is the user who made the request or any other division users.
    const responseIsForDivisionUsers =
      response.userName !== sessionManager.getUserName();

    // handle response for division users.
    if (responseIsForDivisionUsers) {
      // show notification specific to the update type
      if (isApprovedCancel) {
        showNotification('Approved cancelled.', {
          title: 'Approved Run',
          type: HealthLevel.INFO,
        });
      }

      if (isDummyApproved) {
        showNotification('Approved run available.', {
          title: 'Approved Run',
          type: HealthLevel.INFO,
        });
      }
    }
    // handle response for user who made the request.
    if (!responseIsForDivisionUsers) {
      // show notification specific to the update type
      if (isApprovedCancel) {
        showNotification('Approved cancelled.', {
          title: 'Approved Run',
          type: HealthLevel.SUCCESS,
        });
        // Push user to the DUMMY tab.
        this.$emit('setCurrentView', InvoiceNavigation.DRAFT);
        return;
      }
    }
    this.isLoadingUserActionRequest = false;
    this.getApprovedRun();
  }

  public setRctiEmailUpdate(invoiceEmail: InvoiceEmailSentUpdate | null): void {
    if (!invoiceEmail) {
      return;
    }
    const ledgerId: string = invoiceEmail.ledgerId;
    const isSuccessful: boolean = invoiceEmail.isSuccessful;
    const invoice: RctiInvoiceRow | undefined = this.rctiInvoices.find(
      (x: RctiInvoiceRow) => x.ledgerId === ledgerId,
    );

    if (!invoice) {
      return;
    }
    const status: InvoiceStatus = isSuccessful
      ? InvoiceStatus.SENT
      : InvoiceStatus.FAIL;
    invoice.status = status;
  }

  public setLiveRunFinishedResponse(
    runTypeResponse: CurrentInvoiceRunTypeResponse | null,
  ) {
    if (!runTypeResponse) {
      console.error('Current Invoice Run Type Response was null.');
      return;
    }
    // If the client invoice list contains items and the response for client
    // invoice is null (null essentially means ready for invoicing) then the
    // live run is finished. We should update this screen for any users
    // currently active on it.
    if (this.rctiInvoices.length > 0 && !runTypeResponse.rcti) {
      showNotification('Live run complete.', {
        title: 'Live Run',
        type: HealthLevel.SUCCESS,
      });
      this.getApprovedRun();
    }
  }

  public isAuthorised(): boolean {
    return hasAdminOrTeamLeaderOrBranchManagerRole();
  }

  /** Mitt callback for cancelling a DUMMY run (transition to APPROVED) */
  private handleDummyApproval(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, false, true);
    }
  }

  /** Mitt callback for cancelling am APPROVED run (transition to DRAFT) */
  private handleApprovedCancel(payload: InvoiceRunSuccessResponse | null) {
    if (payload) {
      this.setResponseForUserAction(payload, true, false);
    }
  }

  // Set mitt listeners
  public mounted() {
    this.getApprovedRun();
    Mitt.on('currentInvoiceRunType', this.setLiveRunFinishedResponse);
    Mitt.on('approveRctiDummyRunResponse', this.handleDummyApproval);
    Mitt.on('cancelRctiLiveRunResponse', this.handleApprovedCancel);
    Mitt.on('rctiEmailSentUpdate', this.setRctiEmailUpdate);
  }
  /**
   * When the component is destroyed, we remove the mitt listener
   */
  public beforeDestroy() {
    Mitt.off('currentInvoiceRunType', this.setLiveRunFinishedResponse);
    Mitt.off('approveRctiDummyRunResponse', this.handleDummyApproval);
    Mitt.off('cancelRctiLiveRunResponse', this.handleApprovedCancel);
    Mitt.off('rctiEmailSentUpdate', this.setRctiEmailUpdate);
  }

  public isAuthorisedToGenerateLiveInvoice(): boolean {
    return hasAdminOrHeadOfficeRole();
  }
}
