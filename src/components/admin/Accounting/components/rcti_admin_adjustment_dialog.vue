<template>
  <v-layout>
    <v-dialog
      v-model="dialogIsActive"
      width="600px"
      class="ma-0"
      persistent
      no-click-animation
      content-class="v-dialog-custom"
    >
      <template v-slot:activator="{ on }">
        <v-list-tile v-on="on" :disabled="!isAuthorised()" @click="() => null">
          <v-list-tile-title class="pr-2 ma-0">
            <v-icon size="18" class="pr-2" :disabled="!isAuthorised()"
              >far fa-file-invoice-dollar</v-icon
            ><span class="pr-2" style="text-transform: none; font-weight: 400"
              >Tax Invoice Deductions</span
            >
          </v-list-tile-title>
        </v-list-tile>
      </template>
      <div>
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight"
        >
          <span>Tax Invoice Deductions</span>
          <div
            class="app-theme__center-content--closebutton"
            @click="closeDialog"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>
        <v-layout
          row
          wrap
          class="app-theme__center-content--body dialog-content"
        >
          <v-flex md12>
            <v-layout
              row
              wrap
              class="app-theme__center-content--body body-scrollable--65 body-min-height--65"
            >
              <v-flex md12>
                <div class="header-container pa-3 mb-4">
                  <v-layout justify-space-between>
                    <span class="remittance-dialog__header">Owner</span>
                    <span class="remittance-dialog__value">
                      {{ ownerName }}
                    </span>
                  </v-layout>
                  <v-layout justify-space-between>
                    <span class="remittance-dialog__header">Truck</span>
                    <span class="remittance-dialog__value">
                      {{ csrAssignedId }}
                    </span>
                  </v-layout>
                  <v-layout justify-space-between>
                    <span class="remittance-dialog__header"
                      >Number of Jobs</span
                    >
                    <span class="remittance-dialog__value">
                      <span v-if="numberOfJobs != null">{{
                        numberOfJobs
                      }}</span>
                    </span>
                  </v-layout>
                  <v-layout justify-space-between>
                    <span class="remittance-dialog__header"
                      >GST Registered</span
                    >
                    <span class="remittance-dialog__value">
                      <span v-if="numberOfJobs != null">{{
                        registeredForGst ? 'Yes' : 'No'
                      }}</span>
                    </span>
                  </v-layout>
                </div>

                <div class="pa-3">
                  <v-layout
                    column
                    v-for="adjustmentType of adjustmentTypeLineItems"
                    :key="adjustmentType.name"
                    class="mb-2"
                  >
                    <v-layout>
                      <span class="remittance-dialog__section-header">{{
                        adjustmentType.name
                      }}</span>
                      <v-divider class="my-2 ml-3"></v-divider>

                      <v-icon
                        size="16"
                        color="warning"
                        class="pl-2 pr-1"
                        v-if="adjustmentType.balancesOutToBeZero"
                        >fad fa-exclamation-circle</v-icon
                      >
                    </v-layout>
                    <v-layout px-2>
                      <span class="remittance-dialog__header header-type"
                        >Charge</span
                      >
                      <v-spacer></v-spacer>
                      <v-flex md2 style="text-align: right">
                        <span class="remittance-dialog__header header-type">
                          Qty
                        </span>
                      </v-flex>
                      <v-flex md2 style="text-align: right">
                        <span class="remittance-dialog__header header-type">
                          Amt ($)
                        </span>
                      </v-flex>
                      <v-flex md2 style="text-align: right">
                        <span class="remittance-dialog__header header-type">
                          GST ($)
                        </span>
                      </v-flex>
                    </v-layout>

                    <v-layout
                      v-for="adjustmentCharge in adjustmentType.adjustments"
                      :key="adjustmentCharge.adjustmentChargeHistoryId"
                      px-2
                    >
                      <v-flex>
                        <v-layout align-center>
                          <v-icon
                            size="12"
                            class="mr-2"
                            style="padding-bottom: 2px"
                            :disabled="!isAuthorised() || readOnly"
                            @click="
                              editAdminAdjustmentCharge(
                                adjustmentCharge.adjustmentChargeHistoryId,
                              )
                            "
                            >fa fa-edit</v-icon
                          >
                          <span class="remittance-dialog__header pb-0"
                            >{{ adjustmentCharge.chargeDisplayName }}
                          </span>
                        </v-layout>
                        <v-layout
                          v-if="[1, 5, 6].includes(adjustmentCharge.categoryId)"
                          pl-4
                          column
                        >
                          <span
                            class="remittance-dialog__header subheader-type"
                            v-if="adjustmentCharge.deviceName"
                            >{{
                              adjustmentCharge.deviceName
                                ? adjustmentCharge.deviceName
                                : '-'
                            }}</span
                          >
                          <span
                            class="remittance-dialog__header subheader-type"
                            >{{
                              adjustmentCharge.durationSinceLastCharge
                                ? adjustmentCharge.durationSinceLastCharge
                                : '-'
                            }}</span
                          >
                        </v-layout>

                        <v-layout
                          v-if="[3, 4].includes(adjustmentCharge.categoryId)"
                          pl-4
                          column
                        >
                          <span
                            class="remittance-dialog__header subheader-type"
                            style="max-width: 250px"
                            >{{
                              adjustmentCharge.description
                                ? adjustmentCharge.description
                                : '-'
                            }}</span
                          >
                        </v-layout>
                      </v-flex>
                      <v-spacer></v-spacer>
                      <v-flex
                        md2
                        v-if="
                          !isAdministrationDeductionAdjustmentCharge(
                            adjustmentCharge.categoryId,
                          )
                        "
                      >
                        <v-layout
                          justify-end
                          align-start
                          style="text-align: right"
                        >
                          <span class="remittance-dialog__value">
                            {{ adjustmentCharge.quantity }}
                          </span>
                        </v-layout>
                      </v-flex>
                      <v-flex md2 v-else style="text-align: right">
                        <span class="remittance-dialog__value"> - </span>
                      </v-flex>
                      <v-flex md2 style="text-align: right">
                        <span class="remittance-dialog__value">
                          {{
                            adjustmentCharge.isNegativeAmount
                              ? '(' +
                                DisplayCurrencyValue(
                                  adjustmentCharge.total.exclGst,
                                ) +
                                ')'
                              : DisplayCurrencyValue(
                                  adjustmentCharge.total.exclGst,
                                )
                          }}
                        </span>
                      </v-flex>
                      <v-flex md2 style="text-align: right">
                        <span class="remittance-dialog__value">
                          {{
                            adjustmentCharge.isNegativeAmount
                              ? '(' +
                                DisplayCurrencyValue(
                                  adjustmentCharge.total.gst,
                                ) +
                                ')'
                              : DisplayCurrencyValue(adjustmentCharge.total.gst)
                          }}
                        </span>
                      </v-flex>
                    </v-layout>

                    <v-layout
                      v-if="adjustmentType.adjustments.length < 1"
                      justify-center
                    >
                      <span class="remittance-dialog__header subheader-type"
                        >No {{ adjustmentType.name }} Exist.</span
                      >
                    </v-layout>

                    <v-layout>
                      <RctiAddAdminAdjustment
                        v-if="adjustmentType.name === 'RCTI Adjustments'"
                        :adjustmentType="AdjustmentChargeType.RCTI"
                        :readOnly="readOnly"
                        :fleetAssetId="fleetAssetId"
                        :affiliationType="affiliationType"
                        :registeredForGst="registeredForGst"
                        :ledgerId="ledgerId"
                      />

                      <RctiAddAdminAdjustment
                        v-if="adjustmentType.name === 'Tax Invoice Adjustments'"
                        :adjustmentType="AdjustmentChargeType.TAX_INVOICE"
                        :readOnly="readOnly"
                        :fleetAssetId="fleetAssetId"
                        :affiliationType="affiliationType"
                        :registeredForGst="registeredForGst"
                        :ledgerId="ledgerId"
                      />
                    </v-layout>
                  </v-layout>

                  <v-divider class="my-2"></v-divider>
                  <v-layout>
                    <span class="remittance-dialog__section-header"
                      >Charge List</span
                    >
                  </v-layout>
                  <v-layout px-2>
                    <span class="remittance-dialog__header header-type"
                      >Charge</span
                    >
                    <v-spacer></v-spacer>
                    <v-flex md2 style="text-align: right">
                      <span class="remittance-dialog__header header-type">
                        Amt ($)
                      </span>
                    </v-flex>
                    <v-flex md2 style="text-align: right">
                      <span class="remittance-dialog__header header-type">
                        GST ($)
                      </span>
                    </v-flex>
                  </v-layout>
                  <v-layout px-2>
                    <span class="remittance-dialog__header">RCTI Amount</span>
                    <v-spacer></v-spacer>
                    <v-flex md2 style="text-align: right">
                      <span class="remittance-dialog__value">
                        {{ rctiTotalExlGst }}
                      </span>
                    </v-flex>
                    <v-flex md2 style="text-align: right">
                      <span class="remittance-dialog__value">
                        {{ rctiTotalGst }}
                      </span>
                    </v-flex>
                  </v-layout>
                  <v-layout px-2>
                    <span class="remittance-dialog__header"
                      >RCTI Adjustments</span
                    >
                    <v-spacer></v-spacer>
                    <v-flex md2 style="text-align: right">
                      <span class="remittance-dialog__value">
                        {{
                          returnFormattedDollarValueFromString(
                            rctiAdjustmentsExclGst,
                          )
                        }}
                      </span>
                    </v-flex>
                    <v-flex md2 style="text-align: right">
                      <span class="remittance-dialog__value">
                        {{
                          returnFormattedDollarValueFromString(
                            rctiAdjustmentsGst,
                          )
                        }}
                      </span>
                    </v-flex>
                  </v-layout>
                  <v-layout px-2>
                    <span class="remittance-dialog__header"
                      >Fuel Surcharge</span
                    >
                    <v-spacer></v-spacer>
                    <v-flex md2 style="text-align: right">
                      <span class="remittance-dialog__value">
                        {{ fuelTotalExclGst }}
                      </span>
                    </v-flex>
                    <v-flex md2 style="text-align: right">
                      <span class="remittance-dialog__value">
                        {{ fuelTotalGst }}
                      </span>
                    </v-flex>
                  </v-layout>
                  <v-layout px-2>
                    <span class="remittance-dialog__header">Deductions</span>
                    <v-spacer></v-spacer>
                    <v-flex md2 style="text-align: right">
                      <span class="remittance-dialog__value">
                        {{
                          returnFormattedDollarValueFromString(
                            deductionTotalExclGst,
                          )
                        }}
                      </span>
                    </v-flex>
                    <v-flex md2 style="text-align: right">
                      <span class="remittance-dialog__value">
                        {{
                          returnFormattedDollarValueFromString(
                            deductionTotalGst,
                          )
                        }}
                      </span>
                    </v-flex>
                  </v-layout>
                  <v-layout justify-end>
                    <v-flex md4>
                      <v-divider class="my-2"></v-divider>
                    </v-flex>
                  </v-layout>
                  <v-layout px-2 py-1>
                    <v-spacer></v-spacer>
                    <span
                      class="remittance-dialog__header"
                      style="text-align: right"
                      >Net ($):</span
                    >
                    <v-flex md2 style="text-align: right">
                      <span class="remittance-dialog__value highlighted">
                        {{ netTotal }}
                      </span>
                    </v-flex>
                  </v-layout>
                  <v-layout justify-end>
                    <v-flex md6>
                      <v-divider class="my-2"></v-divider>
                    </v-flex>
                  </v-layout>
                </div>
              </v-flex>
            </v-layout>
          </v-flex>

          <v-flex md12>
            <v-divider></v-divider>
            <v-layout align-center>
              <v-btn color="error" outline @click="closeDialog"> Cancel </v-btn>
              <v-spacer></v-spacer>
              <v-btn depressed color="blue" @click="closeDialog"> Okay </v-btn>
            </v-layout>
          </v-flex>
        </v-layout>
      </div>
    </v-dialog>

    <RctiEditAdminAdjustment
      v-if="administrationAdjustment"
      :administrationAdjustment="administrationAdjustment"
      @closeEditAdminAdjustment="closeEditAdminAdjustment"
      :registeredForGst="registeredForGst"
      :csrAssignedId="csrAssignedId"
      :ledgerId="ledgerId"
    />
  </v-layout>
</template>

<script setup lang="ts">
import RctiAddAdminAdjustment from '@/components/admin/Accounting/components/rcti_add_admin_adjustment.vue';
import RctiEditAdminAdjustment from '@/components/admin/Accounting/components/rcti_edit_admin_adjustment.vue';
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { isAdministrationDeductionAdjustmentCharge } from '@/helpers/RateHelpers/AdjustmentChargeHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { RctiAdministrationAdjustments } from '@/interface-models/Accounting/RctiAdministrationAdjustments';
import { AdjustmentChargeType } from '@/interface-models/Generic/Accounting/AdjustmentChargeType';
import { ComputedRef, Ref, computed, ref } from 'vue';

interface AdjustmentLineItem {
  name: string;
  adjustments: RctiAdministrationAdjustments[];
  balancesOutToBeZero: boolean;
}

const props = withDefaults(
  defineProps<{
    ownerName?: string;
    csrAssignedId?: string;
    fleetAssetId: string;
    numberOfJobs?: number | null;
    readOnly?: boolean;
    administrationAdjustments: RctiAdministrationAdjustments[];
    netTotal: string;
    rctiTotalExlGst: string;
    rctiTotalGst: string;
    rctiAdjustmentsExclGst: string;
    rctiAdjustmentsGst: string;
    fuelTotalExclGst: string;
    fuelTotalGst: string;
    deductionTotalExclGst: string;
    deductionTotalGst: string;
    registeredForGst?: boolean;
    affiliationType: string;
    ledgerId: string;
  }>(),
  {
    ownerName: '-',
    csrAssignedId: '-',
    numberOfJobs: null,
    readOnly: false,
    registeredForGst: true,
  },
);

const emit = defineEmits<{
  (event: 'update:isDialogOpen', payload: boolean): void;
  (event: 'closeMenuOptions'): void;
}>();

const dialogIsActive: Ref<boolean> = ref(false);
const administrationAdjustment: Ref<RctiAdministrationAdjustments | null> =
  ref(null);

/**
 * Checks if the user is authorised to perform the action.
 * @returns {boolean} - True if the user has admin or head office role.
 */
function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}

/**
 * Finds and sets the admin adjustment charge that the user selected to edit.
 * @param adjustmentChargeId - The ID of the adjustment charge to edit.
 */
function editAdminAdjustmentCharge(adjustmentChargeId: string): void {
  const adjustmentCharge: RctiAdministrationAdjustments | undefined =
    props.administrationAdjustments.find(
      (x: RctiAdministrationAdjustments) =>
        x.adjustmentChargeHistoryId === adjustmentChargeId,
    );
  if (!adjustmentCharge) {
    return;
  }
  administrationAdjustment.value = adjustmentCharge;
}

/**
 * Closes the edit adjustment charge by setting administrationAdjustment to null.
 */
function closeEditAdminAdjustment(): void {
  administrationAdjustment.value = null;
}

/**
 * Closes the dialog and resets the state.
 */
function closeDialog(): void {
  emit('closeMenuOptions');
  administrationAdjustment.value = null;
  dialogIsActive.value = false;
}

/**
 * Returns the formatted dollar value from a string.
 * @param value - The value to format.
 * @returns {string} - The formatted dollar value.
 */
function returnFormattedDollarValueFromString(value: string): string {
  const asNumber = parseInt(value, 10);
  // Check if not a number
  if (isNaN(asNumber)) {
    return value;
  }
  return asNumber < 0 ? `(${DisplayCurrencyValue(Math.abs(asNumber))})` : value;
}

/**
 * Gets the adjustment type line items.
 * @returns {AdjustmentLineItem[]} - The adjustment type line items.
 */
const adjustmentTypeLineItems: ComputedRef<AdjustmentLineItem[]> = computed(
  () => {
    const rctiAdjustments: RctiAdministrationAdjustments[] =
      props.administrationAdjustments.filter(
        (x: RctiAdministrationAdjustments) => x.categoryId === 4,
      );
    const taxInvoiceAdjustments: RctiAdministrationAdjustments[] =
      props.administrationAdjustments.filter(
        (x: RctiAdministrationAdjustments) => x.categoryId !== 4,
      );
    return [
      {
        name: 'RCTI Adjustments',
        adjustments: rctiAdjustments,
        balancesOutToBeZero:
          parseInt(props.rctiAdjustmentsExclGst, 10) === 0 &&
          rctiAdjustments.length > 0,
      },
      {
        name: 'Tax Invoice Adjustments',
        adjustments: taxInvoiceAdjustments,
        balancesOutToBeZero:
          parseInt(props.deductionTotalExclGst, 10) === 0 &&
          taxInvoiceAdjustments.length > 0,
      },
    ];
  },
);
</script>
<style scoped lang="scss">
.remittance-dialog__section-header {
  color: rgb(188, 188, 190);
  text-transform: uppercase;
  font-size: $font-size-14;
  font-weight: 500;
  padding-bottom: 6px;
}

.remittance-dialog__header {
  color: rgb(188, 188, 190);
  text-transform: uppercase;
  font-size: $font-size-12;
  font-weight: 600;
  padding-bottom: 4px;
  &.header-type {
    font-size: $font-size-13;
    padding-bottom: 8px;
    font-weight: 700;
    color: var(--accent);
  }
  &.subheader-type {
    font-size: $font-size-11;
    font-style: italic;
    font-weight: 500;
    color: rgb(136, 136, 145);
    text-transform: none;
  }
}

.remittance-dialog__value {
  color: white;
  font-size: $font-size-12;
  padding-bottom: 4px;
  &.highlighted {
    color: white;
    background-color: var(--primary);
    padding: 4px 10px;
    font-size: $font-size-14;
    border-radius: 2px;
    font-weight: 700;
  }
}

.header-container {
  background-color: var(--background-color-250);
}
</style>
