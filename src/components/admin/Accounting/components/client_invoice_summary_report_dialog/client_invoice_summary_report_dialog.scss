.accounting-summary-report {
  width: 100%;

  $table-font-size: $font-size-12;

  .summary-report__page-header {
    padding: 0px 16px;
    .summary-report__page-header--title {
      font-size: $font-size-16;
      font-weight: 600;
      color: rgb(255, 255, 255);

      &.inactive-type {
        color: rgb(113, 114, 120);
      }
    }

    .summary-report__page-header--reportname {
      font-size: $font-size-16;
      font-weight: 600;
      // color: rgb(110, 228, 255);
      color: white;
      &:hover {
        cursor: pointer;
      }
    }

    .summary-report__page-header--reportlink {
      font-size: $font-size-13;
      font-weight: 500;
      // color: rgb(110, 228, 255);
      color: rgb(129, 207, 255);
      &:hover {
        cursor: pointer;
      }
    }
    .summary-report__page-header--label {
      font-size: $table-font-size;
      font-weight: 500;
      padding-right: 10px;
    }
    .summary-report__page-header--value {
      font-size: $table-font-size;
      color: rgb(207, 207, 211);
    }
    .summary-report__page-header--dummyindicator {
      font-size: $font-size-13;
      letter-spacing: 1px;
      font-weight: 700;
      text-transform: uppercase;
      background-color: rgb(228, 174, 27);
      padding: 2px 6px;
      border-radius: 2px;
    }
  }

  .summary-report__column {
    .summary-report__column--title {
      font-size: $font-size-14;
      padding-bottom: 6px;
      text-transform: uppercase;
      color: white;
      font-weight: 600;
    }

    .summary-report__column--subtitle {
      font-size: $font-size-13;
      padding-bottom: 6px;
      // text-transform: uppercase;
      color: rgb(208, 211, 231);
      font-weight: 500;
    }
  }

  .report-line__row {
    font-size: $table-font-size;
    // padding-bottom: 6px;
    padding: 1px 4px;
    color: white;

    .report-line__text {
      font-weight: 500;
      color: rgb(221, 221, 226);
    }

    .report-line__value {
      padding: 2px 0px;
      font-weight: 400;
      color: rgb(224, 226, 243);
      text-align: right;

      &.final-total {
        background-color: rgb(53, 172, 247);
        padding: 2px 6px;
        border-radius: 2px;
        color: white;
      }
    }

    &.column-border-right {
      border-right: 1px solid rgb(91, 89, 99);
    }
  }

  .report-line__headers {
    font-size: $table-font-size;
    padding-bottom: 8px;
    .report-line__columnheader {
      font-weight: 600;
      text-transform: uppercase;
    }
  }
}
