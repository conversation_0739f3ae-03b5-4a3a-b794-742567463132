import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import {
  DisplayCurrencyValue,
  RoundCurrencyValue,
} from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { hasAdminOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { AccountingJobRow } from '@/interface-models/Accounting/AccountingJobRow';
import { ClientInvoiceRow } from '@/interface-models/Accounting/ClientInvoiceRow';
import { ReportCurrencyDetails } from '@/interface-models/Accounting/CurrencyDetails';
import { InvoiceSubtotals } from '@/interface-models/Accounting/InvoiceSubtotals';
import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { useClientInvoiceStore } from '@/store/modules/ClientInvoiceStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { Component, Prop, Vue } from 'vue-property-decorator';

interface ClientInvoiceSummaryReport {
  name: string;
  charge: ReportCurrencyDetails;
  isSubCharge: boolean;
}

interface RateCodeBreakdown {
  service: string;
  description: string;
  total: string;
}

@Component({
  components: { DatePickerBasic, InformationTooltip },
})
export default class ClientInvoiceSummaryReportDialog
  extends Vue
  implements IUserAuthority
{
  @Prop() public clientInvoices: ClientInvoiceRow[];
  @Prop({ default: false }) public isDisabled: boolean;

  public clientInvoiceStore = useClientInvoiceStore();
  public companyDetailsStore = useCompanyDetailsStore();

  public dialogIsActive: boolean = false;

  public isAuthorised(): boolean {
    return hasAdminOrTeamLeaderOrBranchManagerRole();
  }

  get companyName() {
    return this.companyDetailsStore.companyDetails?.name?.toUpperCase() ?? '-';
  }

  get serviceCodeHeaderNames() {
    return ['Rate', 'Description', 'Amount'];
  }

  get runDate(): string {
    const runDate = this.clientInvoiceStore.weekEndingDate
      ? this.clientInvoiceStore.processingDate
      : this.clientInvoiceStore.weekEndingDate;

    return runDate ? returnFormattedDate(runDate, 'DD/MM/YY') : '-';
  }

  get jobCount(): number {
    let jobCount: number = 0;
    for (const clientInvoice of this.clientInvoices) {
      jobCount += clientInvoice.numberOfJobs;
    }
    return jobCount;
  }

  get clientInvoiceSummaryReport(): ClientInvoiceSummaryReport[] {
    const clientInvoiceSummaryReport: ClientInvoiceSummaryReport[] = [];

    let freightSubtotalExclGst: number = 0;
    let freightSubtotalGst: number = 0;
    let freightSubtotalTotal: number = 0;

    let baseFreightExclGst: number = 0;
    let baseFreightGst: number = 0;
    let baseFreightTotal: number = 0;

    let standbyFreightExclGst: number = 0;
    let standbyFreightGst: number = 0;
    let standbyFreightTotal: number = 0;

    let demurrageExclGst: number = 0;
    let demurrageGst: number = 0;
    let demurrageTotal: number = 0;

    let outsideMetroExclGst: number = 0;
    let outsideMetroGst: number = 0;
    let outsideMetroTotal: number = 0;

    let freightAdjustmentExclGst: number = 0;
    let freightAdjustmentGst: number = 0;
    let freightAdjustmentTotal: number = 0;

    let fuelExclGst: number = 0;
    let fuelGst: number = 0;
    let fuelTotal: number = 0;

    let tollsExclGst: number = 0;
    let tollsGst: number = 0;
    let tollsTotal: number = 0;

    let overallAdjustmentExclGst: number = 0;
    let overallAdjustmentGst: number = 0;
    let overallAdjustmentTotal: number = 0;

    let totalInvoicedExclGst: number = 0;
    let totalInvoicedGst: number = 0;
    let totalInvoicedTotal: number = 0;

    for (const clientInvoice of this.clientInvoices) {
      if (!clientInvoice.invoiceSubtotals) {
        continue;
      }
      const totals: InvoiceSubtotals = clientInvoice.invoiceSubtotals;
      freightSubtotalExclGst += totals.freightSubtotal.exclGst;
      freightSubtotalGst += totals.freightSubtotal.gst;
      freightSubtotalTotal += totals.freightSubtotal.total;

      // Because standby is included in baseFreight we need to remove standby charge from this sum
      baseFreightExclGst +=
        totals.baseFreight.exclGst - totals.standbyFreight.exclGst;
      baseFreightGst += totals.baseFreight.gst - totals.standbyFreight.gst;
      baseFreightTotal +=
        totals.baseFreight.total - totals.standbyFreight.total;

      standbyFreightExclGst += totals.standbyFreight.exclGst;
      standbyFreightGst += totals.standbyFreight.gst;
      standbyFreightTotal += totals.standbyFreight.total;

      demurrageExclGst += totals.demurrageFreight.exclGst;
      demurrageGst += totals.demurrageFreight.gst;
      demurrageTotal += totals.demurrageFreight.total;

      freightAdjustmentExclGst += totals.freightAdditionalCharges.exclGst;
      freightAdjustmentGst += totals.freightAdditionalCharges.gst;
      freightAdjustmentTotal += totals.freightAdditionalCharges.total;

      fuelExclGst += totals.fuelSurcharge.exclGst;
      fuelGst += totals.fuelSurcharge.gst;
      fuelTotal += totals.fuelSurcharge.total;

      overallAdjustmentExclGst += totals.overallAdditionalCharges.exclGst;
      overallAdjustmentGst += totals.overallAdditionalCharges.gst;
      overallAdjustmentTotal += totals.overallAdditionalCharges.total;

      totalInvoicedExclGst += clientInvoice.invoiceTotal.exclGst;
      totalInvoicedGst += clientInvoice.invoiceTotal.gst;
      totalInvoicedTotal += clientInvoice.invoiceTotal.total;

      // calculate tolls and outside metro sum via client invoice job list.
      for (const job of clientInvoice.jobList) {
        tollsExclGst += job.tollCharges.exclGst;
        tollsGst += job.tollCharges.gst;
        tollsTotal += job.tollCharges.total;

        // Because toll charges are included in overallAdditionalCharges we need to remove toll charges from its sum
        overallAdjustmentExclGst -= job.tollCharges.exclGst;
        overallAdjustmentGst -= job.tollCharges.gst;
        overallAdjustmentTotal -= job.tollCharges.total;

        outsideMetroExclGst += job.outsideMetroCharges.exclGst;
        outsideMetroGst += job.outsideMetroCharges.gst;
        outsideMetroTotal += job.outsideMetroCharges.total;
      }
    }

    const overallFreightTotal: ClientInvoiceSummaryReport = {
      name: 'Freight Subtotal',
      charge: {
        exclGst: DisplayCurrencyValue(
          RoundCurrencyValue(freightSubtotalExclGst),
        ),
        gst: DisplayCurrencyValue(RoundCurrencyValue(freightSubtotalGst)),
        total: DisplayCurrencyValue(RoundCurrencyValue(freightSubtotalTotal)),
      },
      isSubCharge: false,
    };

    const freightServiceCharge: ClientInvoiceSummaryReport = {
      name: 'Base Freight Service Charges',
      charge: {
        exclGst: DisplayCurrencyValue(RoundCurrencyValue(baseFreightExclGst)),
        gst: DisplayCurrencyValue(RoundCurrencyValue(baseFreightGst)),
        total: DisplayCurrencyValue(RoundCurrencyValue(baseFreightTotal)),
      },
      isSubCharge: true,
    };

    const freightStandbyCharge: ClientInvoiceSummaryReport = {
      name: 'Freight Standby Charge',
      charge: {
        exclGst: DisplayCurrencyValue(
          RoundCurrencyValue(standbyFreightExclGst),
        ),
        gst: DisplayCurrencyValue(RoundCurrencyValue(standbyFreightGst)),
        total: DisplayCurrencyValue(RoundCurrencyValue(standbyFreightTotal)),
      },
      isSubCharge: true,
    };

    const demurrageCharge: ClientInvoiceSummaryReport = {
      name: 'Demurrage Charges',
      charge: {
        exclGst: DisplayCurrencyValue(RoundCurrencyValue(demurrageExclGst)),
        gst: DisplayCurrencyValue(RoundCurrencyValue(demurrageGst)),
        total: DisplayCurrencyValue(RoundCurrencyValue(demurrageTotal)),
      },
      isSubCharge: true,
    };

    const outsideMetroSurcharge: ClientInvoiceSummaryReport = {
      name: 'Outside Metro Surcharge',
      charge: {
        exclGst: DisplayCurrencyValue(RoundCurrencyValue(outsideMetroExclGst)),
        gst: DisplayCurrencyValue(RoundCurrencyValue(outsideMetroGst)),
        total: DisplayCurrencyValue(RoundCurrencyValue(outsideMetroTotal)),
      },
      isSubCharge: true,
    };

    const freightAdjustmentCharge: ClientInvoiceSummaryReport = {
      name: 'Freight Adjustment Charges',
      charge: {
        exclGst: DisplayCurrencyValue(
          RoundCurrencyValue(freightAdjustmentExclGst),
        ),
        gst: DisplayCurrencyValue(RoundCurrencyValue(freightAdjustmentGst)),
        total: DisplayCurrencyValue(RoundCurrencyValue(freightAdjustmentTotal)),
      },
      isSubCharge: true,
    };

    const fuelSurcharge: ClientInvoiceSummaryReport = {
      name: 'Fuel Surcharge',
      charge: {
        exclGst: DisplayCurrencyValue(RoundCurrencyValue(fuelGst)),
        gst: DisplayCurrencyValue(RoundCurrencyValue(fuelGst)),
        total: DisplayCurrencyValue(RoundCurrencyValue(fuelGst)),
      },
      isSubCharge: false,
    };

    const tollCharge: ClientInvoiceSummaryReport = {
      name: 'Toll Charges',
      charge: {
        exclGst: DisplayCurrencyValue(RoundCurrencyValue(tollsExclGst)),
        gst: DisplayCurrencyValue(RoundCurrencyValue(tollsGst)),
        total: DisplayCurrencyValue(RoundCurrencyValue(tollsTotal)),
      },
      isSubCharge: false,
    };

    const overallAjustments: ClientInvoiceSummaryReport = {
      name: 'Overall Adjustments',
      charge: {
        exclGst: DisplayCurrencyValue(
          RoundCurrencyValue(overallAdjustmentExclGst),
        ),
        gst: DisplayCurrencyValue(RoundCurrencyValue(overallAdjustmentGst)),
        total: DisplayCurrencyValue(RoundCurrencyValue(overallAdjustmentTotal)),
      },
      isSubCharge: false,
    };

    const totalInvoiced: ClientInvoiceSummaryReport = {
      name: 'Total Invoiced',
      charge: {
        exclGst: DisplayCurrencyValue(RoundCurrencyValue(totalInvoicedExclGst)),
        gst: DisplayCurrencyValue(RoundCurrencyValue(totalInvoicedGst)),
        total: DisplayCurrencyValue(RoundCurrencyValue(totalInvoicedTotal)),
      },
      isSubCharge: false,
    };

    clientInvoiceSummaryReport.push(overallFreightTotal);
    clientInvoiceSummaryReport.push(freightServiceCharge);
    clientInvoiceSummaryReport.push(freightStandbyCharge);
    clientInvoiceSummaryReport.push(demurrageCharge);
    clientInvoiceSummaryReport.push(outsideMetroSurcharge);
    clientInvoiceSummaryReport.push(freightAdjustmentCharge);
    clientInvoiceSummaryReport.push(fuelSurcharge);
    clientInvoiceSummaryReport.push(tollCharge);
    clientInvoiceSummaryReport.push(overallAjustments);
    clientInvoiceSummaryReport.push(totalInvoiced);
    return clientInvoiceSummaryReport;
  }

  get breakDownByRateCodes(): RateCodeBreakdown[] {
    const rateCodeBreakdowns: RateCodeBreakdown[] = [];
    const jobList: AccountingJobRow[] = [];
    for (const clientInvoice of this.clientInvoices) {
      for (const job of clientInvoice.jobList) {
        jobList.push(job);
      }
    }
    this.companyDetailsStore.getServiceTypesList.forEach(
      (serviceType: ServiceTypes) => {
        const jobsByServiceType = jobList.filter(
          (x: AccountingJobRow) =>
            x.serviceTypeId === serviceType.serviceTypeId,
        );
        if (jobsByServiceType.length === 0) {
          return;
        }
        const service: string = serviceType.shortServiceTypeName;
        const description: string = serviceType.longServiceTypeName;
        let total: number = 0;
        for (const job of jobsByServiceType) {
          total += job.totalChargeInclGst;
        }
        const rateBreakdown = {
          service,
          description,
          total: DisplayCurrencyValue(RoundCurrencyValue(total)),
        };
        rateCodeBreakdowns.push(rateBreakdown);
      },
    );

    return rateCodeBreakdowns;
  }
}
