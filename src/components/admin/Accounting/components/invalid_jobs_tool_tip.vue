<template>
  <v-tooltip right>
    <template v-slot:activator="{ on }">
      <v-icon size="13" color="warning" v-on="on"
        >fad fa-exclamation-circle</v-icon
      >
    </template>
    <v-layout>
      <v-flex md12>
        The following jobs are invalid and cannot be added to a draft:
        <ul>
          <li v-for="(jobId, index) of invalidJobIds" :key="index">
            {{ jobId }}
          </li>
        </ul>

        Please contact GoDesta support if assistance is required.
      </v-flex>
    </v-layout>
  </v-tooltip>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    invalidJobIds?: number[];
  }>(),
  {
    invalidJobIds: () => [],
  },
);
</script>
