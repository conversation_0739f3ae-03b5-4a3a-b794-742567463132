import {
  DisplayCurrencyValue,
  RoundCurrencyValue,
} from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { CurrencyDetails } from '@/interface-models/Accounting/CurrencyDetails';
import { SumType } from '@/interface-models/Generic/Accounting/SumType';
import { EquipmentHireCustomAdjustment } from '@/interface-models/Generic/Accounting/hireContractChargeHistory/EquipmentHireCustomAdjustment';
import { EquipmentHireCustomAdjustmentEdit } from '@/interface-models/Generic/Accounting/hireContractChargeHistory/HireContractChargeHistory';
import { Validation } from '@/interface-models/Generic/Validation';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { useEquipmentHireInvoiceStore } from '@/store/modules/EquipmentHireInvoiceStore';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class EquipmentHireAddCustomAdjustmentDialog
  extends Vue
  implements IUserAuthority
{
  @Prop({ default: false }) public readOnly: boolean;
  @Prop() public ownerAffiliation: string;
  @Prop() public fleetAssetId: string;
  @Prop({ default: true }) public registeredForGst: boolean;
  @Prop() public affiliationType: string;
  @Prop() public ledgerId: string;
  @Prop() public contractId: string;
  public dialogIsActive: boolean = false;
  public isLoading: boolean = false;
  public isDeduction: boolean = false;
  public customAdjustmentCharge: EquipmentHireCustomAdjustment | null =
    new EquipmentHireCustomAdjustment();
  public chargeAmount: number = 0.0;
  public displayCurrencyValue = DisplayCurrencyValue;
  public isAuthorised(): boolean {
    return hasAdminOrHeadOfficeRole();
  }

  get rctiAdjustmentEnumType(): number {
    return 4;
  }

  get customAdjustmentEnumType(): number {
    return 3;
  }

  public closeDialog(): void {
    this.customAdjustmentCharge = new EquipmentHireCustomAdjustment();
    this.isDeduction = true;
    this.dialogIsActive = false;
  }

  get validate(): Validation {
    return validationRules;
  }

  // RCTI isDeduction is reducing the amount metro pays the driver
  // custom isDeduction is reducing the amount the driver pays metro;
  public async saveCustomAdjustmentCharge(): Promise<void> {
    if (!this.customAdjustmentCharge) {
      return;
    }

    const total: CurrencyDetails = {
      exclGst: RoundCurrencyValue(
        this.customAdjustmentCharge.quantity * this.customAdjustmentCharge.rate,
      ),
      gst: this.registeredForGst
        ? RoundCurrencyValue(
            this.customAdjustmentCharge.quantity *
              this.customAdjustmentCharge.rate *
              (10 / 100),
          )
        : 0.0,
      total: RoundCurrencyValue(
        this.customAdjustmentCharge.quantity *
          this.customAdjustmentCharge.rate +
          this.customAdjustmentCharge.quantity *
            this.customAdjustmentCharge.rate *
            (10 / 100),
      ),
    };

    this.customAdjustmentCharge.total = total;

    this.customAdjustmentCharge.sumType = this.isDeduction
      ? SumType.DEDUCTION
      : SumType.ADDITION;
    this.isLoading = true;
    const request: EquipmentHireCustomAdjustmentEdit = {
      ledgerId: this.ledgerId,
      contractId: this.contractId,
      fleetAssetId: this.fleetAssetId,
      customAdjustment: this.customAdjustmentCharge,
    };
    // Send request and close dialog on response
    const result =
      await useEquipmentHireInvoiceStore().addOrEditLedgerAdjustment(request);
    if (result && this.isLoading) {
      this.isLoading = false;
      this.closeDialog();
    }
  }

  get deductionExclGst(): string {
    if (!this.customAdjustmentCharge) {
      return '0.00';
    }
    return (
      '$' +
      DisplayCurrencyValue(
        RoundCurrencyValue(
          this.customAdjustmentCharge.quantity *
            this.customAdjustmentCharge.rate,
        ),
      )
    );
  }

  get deductionGst(): string {
    if (!this.registeredForGst || !this.customAdjustmentCharge) {
      return '$0.00';
    }
    return (
      '$' +
      DisplayCurrencyValue(
        RoundCurrencyValue(
          this.customAdjustmentCharge.quantity *
            this.customAdjustmentCharge.rate *
            (10 / 100),
        ),
      )
    );
  }

  get deductionTotal(): string {
    if (!this.customAdjustmentCharge) {
      return '$0.00';
    }
    return (
      '$' +
      DisplayCurrencyValue(
        RoundCurrencyValue(
          this.customAdjustmentCharge.quantity *
            this.customAdjustmentCharge.rate,
        ) +
          RoundCurrencyValue(
            this.customAdjustmentCharge.quantity *
              this.customAdjustmentCharge.rate *
              (10 / 100),
          ),
      )
    );
  }
}
