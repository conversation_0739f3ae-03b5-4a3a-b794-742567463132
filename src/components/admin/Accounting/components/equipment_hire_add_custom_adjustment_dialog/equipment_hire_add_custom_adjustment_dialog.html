<v-layout>
  <v-dialog
    v-model="dialogIsActive"
    width="100%"
    height="100%"
    class="ma-0"
    fullscreen
    persistent
    no-click-animation
  >
    <template v-slot:activator="{ on }">
      <v-btn
        small
        flat
        block
        v-on="on"
        color="#00b1e2"
        :disabled="!isAuthorised() || readOnly"
      >
        <v-icon left size="16" class="mr-2">fal fa-plus</v-icon>Add Custom
        Adjustment
      </v-btn>
    </template>
    <v-layout
      column
      align-center
      justify-center
      style="height:100%; background-color: rgb(10, 10, 18, 0.9);"
    >
      <div>
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight border-container-header"
          style="width:400px"
        >
          <span>Add Custom Adjustment</span>
          <div
            class="app-theme__center-content--closebutton"
            @click="closeDialog"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>

        <v-layout
          wrap
          class="app-theme__center-content--body dialog-content border-container-content"
          style="width:400px !important"
        >
          <v-flex md12 class="pa-3">
            <v-form>
              <v-flex md12>
                <v-layout>
                  <v-flex md12 mb-3>
                    <v-alert type="info" :value="true">
                      You are adding a CUSTOM ADJUSTMENT CHARGE. Please note:
                      <ul>
                        <li>
                          The total remitted amount will INCREASE if this charge
                          is a deduction and DECREASE if the charge is not a
                          deduction.
                        </li>
                      </ul>
                    </v-alert>
                  </v-flex>
                </v-layout>

                <v-layout>
                  <v-text-field
                    v-model="customAdjustmentCharge.description"
                    color="orange"
                    label="Charge Description"
                    box
                    background-color="#20202a"
                    class="form-field-required"
                  ></v-text-field>
                </v-layout>
                <v-layout>
                  <v-flex>
                    <v-text-field
                      v-model.number="customAdjustmentCharge.rate"
                      color="orange"
                      label="Amount"
                      :rules="[validate.currency]"
                      prefix="$"
                      box
                      type="number"
                      class="form-field-required"
                      background-color="#20202a"
                    >
                    </v-text-field>
                  </v-flex>
                </v-layout>
                <v-layout>
                  <v-flex>
                    <v-text-field
                      v-model.number="customAdjustmentCharge.quantity"
                      color="orange"
                      label="Quantity"
                      :rules="[validate.number]"
                      min="1"
                      box
                      type="number"
                      class="form-field-required"
                      background-color="#20202a"
                    >
                    </v-text-field>
                  </v-flex>
                </v-layout>
                <v-layout>
                  <v-flex>
                    <v-checkbox
                      label="Charge is Deduction"
                      v-model="isDeduction"
                    />
                  </v-flex>
                </v-layout>
              </v-flex>
            </v-form>
          </v-flex>
          <v-flex md12 class="px-3 pb-3">
            <v-layout>
              <span class="remittance-dialog__section-header"
                >Applied Deduction</span
              >
            </v-layout>
            <v-divider class="my-2"></v-divider>
            <v-layout justify-space-between>
              <span class="remittance-dialog__header">Rate</span>
              <span class="remittance-dialog__value">
                ${{ displayCurrencyValue(customAdjustmentCharge.rate) }}
              </span>
            </v-layout>
            <v-layout justify-space-between>
              <span class="remittance-dialog__header">Quantity</span>
              <span class="remittance-dialog__value">
                {{ customAdjustmentCharge.quantity }}
              </span>
            </v-layout>
            <v-layout justify-space-between>
              <span class="remittance-dialog__header">Excl Gst</span>
              <span class="remittance-dialog__value">
                {{ deductionExclGst }}
              </span>
            </v-layout>
            <v-layout justify-space-between>
              <span class="remittance-dialog__header">Gst</span>
              <span class="remittance-dialog__value">
                {{ deductionGst }}
              </span>
            </v-layout>
            <v-layout justify-space-between>
              <span class="remittance-dialog__header">Total</span>
              <span class="remittance-dialog__value">
                {{ deductionTotal }}
              </span>
            </v-layout>
          </v-flex>

          <v-flex md12>
            <v-divider></v-divider>
            <v-layout justify-space-between>
              <v-btn color="white" flat @click="closeDialog">Cancel</v-btn>
              <v-btn
                color="light-blue"
                outline
                :loading="isLoading"
                :disabled="!customAdjustmentCharge.description || !customAdjustmentCharge.rate || !customAdjustmentCharge.quantity || !isAuthorised()"
                @click="saveCustomAdjustmentCharge"
                >Add Charge</v-btn
              >
            </v-layout>
          </v-flex>
        </v-layout>
      </div>
    </v-layout>
  </v-dialog>
</v-layout>
