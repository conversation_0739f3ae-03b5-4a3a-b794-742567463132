<template>
  <v-layout>
    <v-dialog
      v-model="dialogIsActive"
      width="450px"
      class="ma-0"
      persistent
      no-click-animation
      content-class="v-dialog-custom"
    >
      <template v-slot:activator="{ on }">
        <v-list-tile
          v-on="on"
          @click="() => null"
          style="width: 100%"
          :disabled="!isAuthorised()"
        >
          <v-list-tile-action>
            <v-icon :disabled="!isAuthorised()" size="18" class="pr-2"
              >fal fa-trash-alt</v-icon
            >
          </v-list-tile-action>
          <v-list-tile-content>
            <v-list-tile-title class="pr-2 ma-0">
              <span class="pr-2" style="text-transform: none; font-weight: 400"
                >Cancel Draft Run</span
              >
            </v-list-tile-title>
          </v-list-tile-content>
        </v-list-tile>
      </template>
      <div>
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight"
        >
          <div>
            <span class="mr-2">Cancel Draft Run Confirmation</span>

            <InformationTooltip
              :right="true"
              :tooltipType="HealthLevel.WARNING"
            >
              <v-layout slot="content" row wrap>
                <v-flex md12>
                  <p class="mb-1" slot="content">
                    The processing date is utilised against the trading terms
                    when calculating the due date for each invoice.
                  </p>
                </v-flex>
              </v-layout>
            </InformationTooltip>
          </div>
          <div
            class="app-theme__center-content--closebutton"
            @click="dialogIsActive = false"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>
        <v-layout
          row
          wrap
          class="app-theme__center-content--body dialog-content"
        >
          <v-flex md12>
            <v-layout row wrap class="body-scrollable--65" pa-3>
              <v-flex md12>
                <v-layout pa-2 wrap>
                  <v-flex md12 class="mb-2">
                    <v-alert type="warning" :value="true">
                      <span
                        >Cancelling a draft run can not be undone, please
                        confirm that you wish to cancel and delete the
                        draft.</span
                      >
                    </v-alert>
                  </v-flex>
                </v-layout>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-divider></v-divider>
            <v-layout align-center>
              <v-spacer></v-spacer>
              <v-btn depressed color="red" @click="confirm">
                <span>confirm & Delete Draft</span>
              </v-btn>
            </v-layout>
          </v-flex>
        </v-layout>
      </div>
    </v-dialog>
  </v-layout>
</template>

<script setup lang="ts">
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { ref, Ref } from 'vue';

const dialogIsActive: Ref<boolean> = ref(false);

const emit = defineEmits(['cancelDraftRun', 'closeMenuOptions']);

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}

function confirm() {
  emit('cancelDraftRun');
  emit('closeMenuOptions');
  dialogIsActive.value = false;
  return;
}
</script>
