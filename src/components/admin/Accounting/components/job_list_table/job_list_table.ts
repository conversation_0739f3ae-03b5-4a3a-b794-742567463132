import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { Component, Prop, Vue } from 'vue-property-decorator';

import ReleaseForEditingConfirmationDialog from '@/components/admin/Accounting/client_invoice/components/confirmation_dialogs/release_for_editing_confirmation_dialog/index.vue';
import JobDetailsDialog from '@/components/operations/OperationDashboard/components/JobDetailsDialog/index.vue';
import { AccountingJobRow } from '@/interface-models/Accounting/AccountingJobRow';
import { PickUpDropOffSuburb } from '@/interface-models/Accounting/PickUpDropOffSuburb';
import { TableJobSelection } from '@/interface-models/Accounting/TableJobSelection';
import { useClientPortalStore } from '@/store/modules/ClientPortalStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { sessionManager } from '@/store/session/SessionState';

@Component({
  components: { JobDetailsDialog, ReleaseForEditingConfirmationDialog },
})
export default class JobListTable extends Vue {
  @Prop() public jobList: AccountingJobRow[];
  @Prop({ default: null }) public clientId: string | null;
  @Prop({ default: null }) public fleetAssetId: string | null;
  @Prop({ default: false }) public isInnerTable: boolean;
  @Prop({ default: false }) public invoiceIdAvailable: boolean;
  @Prop({ default: false }) public isLoading: boolean;
  @Prop({ default: true }) public selectionIsEnabled: boolean;
  @Prop({ default: () => [] }) public invalidJobIds: number[];
  @Prop({ default: () => [] }) public disabledJobIds: number[];
  @Prop({ default: false }) public invalidClient: boolean;
  @Prop({ default: true }) public isClientNameVisible: boolean;

  public operationsStore = useOperationsStore();

  public isClientPortal: boolean = sessionManager?.isClientPortal() || false;

  // Computed getter for selecting distanceColumn
  get distanceColumn() {
    const hasEditedDistance = this.jobList.some(
      (job) => job.serviceTypeId === 3,
    );

    return {
      text: 'Distance Travelled',
      align: 'center',
      value: hasEditedDistance
        ? 'editedTravelDistance'
        : 'plannedDistanceTravelled',
      class: 'job-list-viewJob-column-header',
      sortable: false,
    };
  }

  get tableHeaders(): TableHeader[] {
    const tableHeaders: TableHeader[] = [
      {
        text: '',
        align: 'left',
        value: '',
        sortable: false,
        class: '',
      },
      {
        text: 'Date',
        align: 'left',
        value: 'date',
        sortable: true,
        class: '',
      },
      {
        text: 'Job ID',
        align: 'left',
        sortable: true,
        value: 'displayId',
        class: '',
      },
      {
        text: 'Service',
        align: 'left',
        value: 'serviceRateName',
        sortable: false,
        class: '',
      },
      {
        text: 'Distance Travelled',
        align: 'center',
        value: 'distanceColumn',
        class: '',
        sortable: false,
      },
      {
        text: 'Billed Duration',
        align: 'center',
        value: 'readableBilledDuration',
        class: '',
        sortable: false,
      },
      {
        text: 'Suburbs',
        align: 'center',
        value: 'pickUpDropOffSuburbList',
        class: 'job-list-name-column-header',
        sortable: false,
      },
      {
        text: 'Freight ($)',
        align: 'center',
        value: 'freightSubtotal',
        sortable: false,
        class: '',
      },
      {
        text: 'Additional ($)',
        align: 'center',
        value: 'additionalCharge',
        sortable: false,
        class: '',
      },
      {
        text: 'Fuel (% / $)',
        align: 'center',
        value: 'fuelSurchargeRate',
        sortable: false,
        class: '',
      },
      {
        text: 'GST ($)',
        align: 'center',
        value: 'gstCharge',
        sortable: false,
        class: '',
      },
      {
        text: 'Total ($)',
        align: 'center',
        value: 'totalCharge',
        sortable: false,
        class: '',
      },
      {
        text: 'View',
        align: 'center',
        value: '',
        class: '',
        sortable: false,
      },
    ];

    // Add the 'Fleet' & 'invoiceStatusDescription' column for operations portal only
    // Add the 'Fleet' column after 'Client' and before 'Service'
    if (!this.isClientPortal) {
      const fleetColumn: TableHeader = {
        text: 'Fleet',
        align: 'left',
        value: 'csrAssignedId',
        sortable: true,
        class: '',
      };
      const invoiceStatusDescriptionColumn: TableHeader = {
        text: `Inv'd / Paid`,
        align: 'center',
        value: '',
        sortable: false,
        class: '',
      };

      if (this.isClientNameVisible) {
        const clientColumn: TableHeader = {
          text: 'Client',
          align: 'left',
          value: 'clientName',
          sortable: true,
          class: 'job-list-name-column-header',
        };
        tableHeaders.splice(3, 0, clientColumn);
      }
      tableHeaders.splice(4, 0, fleetColumn);
      tableHeaders.splice(-1, 0, invoiceStatusDescriptionColumn);
    }

    return tableHeaders;
  }

  // main v-model controller for our "Select all jobs" checkbox.
  get allJobsCheckBox(): boolean {
    if (this.jobList.length === 0) {
      return false;
    }
    return (
      this.jobList.filter((x: AccountingJobRow) => !x.isSelected).length === 0
    );
  }
  set allJobsCheckBox(value: boolean) {
    if (this.isInnerTable) {
      const emitPayload: TableJobSelection = {
        clientId: this.clientId,
        fleetAssetId: this.fleetAssetId,
        setAllJobs: value,
      };
      this.$emit('setJobsCheckBoxForClient', emitPayload);
      return;
    }
    for (const job of this.jobList) {
      job.isSelected = value;
    }
  }

  get jobsPartiallySelected(): boolean {
    if (this.isInnerTable) {
      return false;
    }
    const selectedJobs = this.jobList.filter(
      (x: AccountingJobRow) => x.isSelected,
    );
    return (
      selectedJobs.length > 0 && selectedJobs.length !== this.jobList.length
    );
  }

  // format list of pud items to display first and last stop
  public formatSuburbList(suburbList: PickUpDropOffSuburb[]): string {
    if (!suburbList || suburbList.length === 0) {
      return '';
    }
    if (suburbList.length === 1) {
      return suburbList[0].suburb;
    }
    if (suburbList.length === 2) {
      return `${suburbList[0].suburb} → ${suburbList[1].suburb}`;
    }

    return `${suburbList[0].suburb} (+ ${suburbList.length - 2}) ${
      suburbList[suburbList.length - 1].suburb
    }`;
  }

  // Format tooltip text with all pud stops
  public getTooltipText(suburbList: PickUpDropOffSuburb[]): string {
    return suburbList
      .map((s) => `${s.legTypeFlag} - ${s.suburb}`)
      .join('<br/>');
  }

  /**
   * Called from template when you select to view a job in the table. Requests
   * the full JobDetails object and opens the JobDetailsDialog.
   * @param jobId - the jobId of the job to view
   */
  public async viewJobDetails(jobId: number) {
    if (sessionManager.isClientPortal()) {
      useClientPortalStore().setSelectedJobId(jobId);
    } else {
      this.operationsStore.setSelectedJobId(jobId);
      const result = await this.operationsStore.getFullJobDetails(jobId);
      if (result) {
        this.operationsStore.setViewingJobDetailsDialog(true);
      }
    }
  }

  get showJobDetailsDialog() {
    return this.operationsStore.viewingJobDetailsDialog;
  }

  get selectedJobDetails(): JobDetails | null {
    if (
      !this.showJobDetailsDialog ||
      this.operationsStore.selectedJobId === -1
    ) {
      return null;
    }
    return this.operationsStore.selectedJobDetails;
  }

  public setSelectedSingleJob(): void {
    this.$emit('setPartialSelected');
  }
}
