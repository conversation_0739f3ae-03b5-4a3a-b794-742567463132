<v-layout class="job-list-table">
  <div class="select-all-container" v-if="!isInnerTable">
    <v-checkbox
      @click.native.stop
      :indeterminate="jobsPartiallySelected"
      v-model="allJobsCheckBox"
      hide-details
      :ripple="false"
      :disabled="jobList.length === 0"
      color="info"
    ></v-checkbox>
  </div>

  <v-data-table
    :headers="tableHeaders"
    :item-key="'jobId'"
    :items="jobList"
    :no-data-text="isLoading ? 'Loading...' : 'No jobs to display.'"
    :rows-per-page-items="[15, 20]"
    hide-actions
    :loading="isLoading"
    class="default-table-dark accounting-job-list-table gd-dark-theme"
    :class="isInnerTable ? 'accounting-job-table-inner' : ''"
  >
    <template v-slot:items="props">
      <tr>
        <td>
          <v-icon
            size="13"
            style="padding-left: 5px"
            color="warning"
            v-if="invalidJobIds.includes(props.item.jobId)"
            >fad fa-exclamation-circle</v-icon
          >
          <span class="pl-1" v-if="disabledJobIds.includes(props.item.jobId)">
            <InformationTooltip :right="true" tooltipType="info">
              <v-layout slot="content" row wrap>
                <v-flex md12>
                  <p class="mb-1">
                    This job is part of an active <strong>Invoice</strong> or
                    <strong>RCTI run</strong>. If you wish to make changes to
                    this job, please remove it from the active run.
                  </p>
                </v-flex>
              </v-layout>
            </InformationTooltip>
          </span>

          <v-checkbox
            v-if="selectionIsEnabled && !invalidJobIds.includes(props.item.jobId) && !disabledJobIds.includes(props.item.jobId)"
            @click.native.stop
            v-model="props.item.isSelected"
            hide-details
            :ripple="false"
            :disabled="invalidJobIds.includes(props.item.jobId)"
            color="info"
            @change="isInnerTable ? setSelectedSingleJob() : null"
          ></v-checkbox>
        </td>
        <td class="text-left">{{ props.item.date }}</td>
        <td class="text-left">{{ props.item.displayId }}</td>

        <td
          class="text-left job-list-name-column-header"
          v-if="!isClientPortal && isClientNameVisible"
        >
          {{props.item.clientName}} - {{ props.item.clientId }}
        </td>

        <td class="text-left" v-if="!isClientPortal">
          {{props.item.csrAssignedId}}
        </td>

        <td class="text-left">{{ props.item.serviceRateName}}</td>

        <td class="text-center" v-if="props.item.rateTypeId !== 3">
          {{ props.item.plannedDistanceTravelled }} km
        </td>

        <td v-else class="text-center">
          {{ props.item.editedTravelDistance }} km
        </td>

        <td class="text-center">{{ props.item.readableBilledDuration }}</td>

        <td class="text-center job-list-name-column-header">
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <span v-bind="attrs" v-on="on" class="suburb-tooltip">
                {{ formatSuburbList(props.item.pickUpDropOffSuburbList) }}
              </span>
            </template>
            <div
              class="tooltip-content"
              v-html="getTooltipText(props.item.pickUpDropOffSuburbList)"
            ></div>
          </v-tooltip>
        </td>

        <td class="text-center">$ {{ props.item.freightSubtotal}}</td>

        <td class="text-center">$ {{ props.item.additionalCharge}}</td>

        <td class="text-center">
          {{props.item.fuelSurchargeRate}} &mdash; $ {{
          props.item.fuelSurchargeTotal}}
        </td>

        <td class="text-center">$ {{ props.item.gstCharge}}</td>

        <td class="text-center">$ {{ props.item.totalCharge}}</td>
        <td class="text-center" v-if="!isClientPortal">
          {{ props.item.invoiceStatusDescription}}
        </td>

        <td class="text-center view-job-menu">
          <div class="pr-4">
            <v-tooltip
              bottom
              v-if="!invalidJobIds.includes(props.item.jobId) && !invalidClient"
            >
              <template v-slot:activator="{ on: tooltip }">
                <v-btn
                  flat
                  @click="viewJobDetails(props.item.jobId)"
                  icon
                  color="info"
                  v-on="{ ...tooltip }"
                  class="ma-0"
                >
                  <v-icon style="padding-right: 3px" size="14">
                    fad fa-eye
                  </v-icon>
                </v-btn>
              </template>
              <span>View Job Details</span>
            </v-tooltip>
          </div>
          <div
            style="padding-right: 25px"
            v-if="invalidJobIds.includes(props.item.jobId) || invalidClient"
          >
            <v-menu right>
              <template v-slot:activator="{ on: menu }">
                <v-tooltip bottom>
                  <template v-slot:activator="{ on: tooltip }">
                    <v-btn
                      flat
                      icon
                      v-on="{ ...tooltip, ...menu }"
                      class="ma-0"
                    >
                      <v-icon size="16">fas fa-ellipsis-v </v-icon>
                    </v-btn>
                  </template>
                  <span>View Job Options</span>
                </v-tooltip>
              </template>
              <v-list dense class="v-list-custom">
                <ReleaseForEditingConfirmationDialog
                  :isSingleJob="true"
                  :singleJobDisplayId="props.item.displayId"
                  :selectedJobIds="[props.item.jobId]"
                />
                <v-divider></v-divider>
              </v-list>
            </v-menu>
          </div>
        </td>
      </tr>
    </template>
  </v-data-table>

  <JobDetailsDialog
    v-if="showJobDetailsDialog && selectedJobDetails"
    :jobDetails="selectedJobDetails"
    :showJobDetailsDialog="showJobDetailsDialog"
    :isJobSearchScreen="true"
  ></JobDetailsDialog>
</v-layout>
