import { Component, Prop, Vue } from 'vue-property-decorator';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import {
  DisplayCurrencyValue,
  RoundCurrencyValue,
} from '@/helpers/AccountingHelpers/CurrencyHelpers';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import { Validation } from '@/interface-models/Generic/Validation';
import {
  EquipmentHireCustomAdjustmentDelete,
  EquipmentHireCustomAdjustmentEdit,
} from '@/interface-models/Generic/Accounting/hireContractChargeHistory/HireContractChargeHistory';
import { EquipmentHireCustomAdjustment } from '@/interface-models/Generic/Accounting/hireContractChargeHistory/EquipmentHireCustomAdjustment';
import { SumType } from '@/interface-models/Generic/Accounting/SumType';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { useEquipmentHireInvoiceStore } from '@/store/modules/EquipmentHireInvoiceStore';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
@Component({
  components: { ConfirmationDialog },
})
export default class EquipmentHireEditCustomAdjustmentDialog
  extends Vue
  implements IUserAuthority
{
  @Prop({ default: '-' }) public ownerName: string;
  @Prop() public fleetAssetId: string;
  @Prop({ default: '-' }) public csrAssignedId: string;
  @Prop({ default: null }) public numberOfJobs: number;
  @Prop({ default: false }) public readOnly: boolean;
  @Prop({ default: () => [] })
  public customAdjustment: EquipmentHireCustomAdjustment;
  @Prop({ default: true }) public registeredForGst: boolean;
  @Prop() public ledgerId: string;
  @Prop() public contractId: string;
  @Prop() public contractNumber: string;

  public isLoading: boolean = false;
  public displayCurrencyValue = DisplayCurrencyValue;
  public quantity: number = 0;
  public isLoadingDeletionOfAdjustment: boolean = false;
  public rate: number = 0;
  public description: string = '';
  public isDeduction: boolean = false;

  public isAuthorised(): boolean {
    return hasAdminOrHeadOfficeRole();
  }

  get validate(): Validation {
    return validationRules;
  }

  get dialogIsActive(): boolean {
    return this.customAdjustment !== null;
  }

  set dialogIsActive(value: boolean) {
    this.$emit('closeEditAdminAdjustment');
  }

  // close dialog and reset state
  public closeDialog(): void {
    this.isLoading = false;
    this.isLoadingDeletionOfAdjustment = false;
    this.dialogIsActive = false;
    this.rate = 0;
    this.description = '';
    this.isDeduction = false;
  }

  public mounted(): void {
    this.quantity = this.customAdjustment.quantity;
    this.rate = this.customAdjustment.rate;
    this.description = this.customAdjustment.description;
    this.isDeduction = this.customAdjustment.sumType === SumType.DEDUCTION;
  }

  get deductionExclGst(): string {
    return (
      '$' + DisplayCurrencyValue(RoundCurrencyValue(this.quantity * this.rate))
    );
  }

  get deductionGst(): string {
    if (!this.registeredForGst) {
      return '$0.00';
    }
    return (
      '$' +
      DisplayCurrencyValue(
        RoundCurrencyValue(this.quantity * this.rate * (10 / 100)),
      )
    );
  }

  get deductionTotal(): string {
    return (
      '$' +
      DisplayCurrencyValue(
        RoundCurrencyValue(this.quantity * this.customAdjustment.rate) +
          RoundCurrencyValue(this.quantity * this.rate * (10 / 100)),
      )
    );
  }

  public async saveAdminAdjustment(): Promise<void> {
    this.isLoading = true;
    this.customAdjustment.quantity = this.quantity;
    this.customAdjustment.sumType = this.isDeduction
      ? SumType.DEDUCTION
      : SumType.ADDITION;
    const request: EquipmentHireCustomAdjustmentEdit = {
      ledgerId: this.ledgerId,
      contractId: this.contractId,
      fleetAssetId: this.fleetAssetId,
      customAdjustment: this.customAdjustment,
    };
    // Send request and close dialog on response
    const result =
      await useEquipmentHireInvoiceStore().addOrEditLedgerAdjustment(request);
    if (!result) {
      showNotification(GENERIC_ERROR_MESSAGE, {
        title: 'Save Equipment Hire Adjustment',
      });
    }
    this.closeDialog();
  }

  public async deleteAdminAdjustment(): Promise<void> {
    this.isLoadingDeletionOfAdjustment = true;
    const request: EquipmentHireCustomAdjustmentDelete = {
      adjustmentId: this.customAdjustment.adjustmentId,
      ledgerId: this.ledgerId,
      contractId: this.contractId,
      fleetAssetId: this.fleetAssetId,
    };
    // Send request and close dialog on response
    const result =
      await useEquipmentHireInvoiceStore().removeLedgerAdjustment(request);
    if (!result) {
      showNotification(GENERIC_ERROR_MESSAGE, {
        title: 'Remove Equipment Hire Adjustment',
      });
    }
    this.closeDialog();
  }
}
