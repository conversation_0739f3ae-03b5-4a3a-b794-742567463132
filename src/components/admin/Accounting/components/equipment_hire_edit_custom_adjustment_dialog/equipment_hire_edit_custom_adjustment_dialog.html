<v-layout>
  <v-dialog
    v-model="dialogIsActive"
    width="100%"
    height="100%"
    class="ma-0"
    fullscreen
    persistent
    no-click-animation
  >
    <v-layout
      column
      align-center
      justify-center
      style="height:100%; background-color: rgb(10, 10, 18, 0.9);"
    >
      <div>
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight border-container-header"
          style="width:400px"
        >
          <span>Edit Custom Adjustment</span>
          <div
            class="app-theme__center-content--closebutton"
            @click="closeDialog"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>
        <v-layout
          row
          wrap
          class="app-theme__center-content--body dialog-content border-container-content"
          style="width:400px !important"
        >
          <v-flex md12>
            <v-layout
              row
              wrap
              class="app-theme__center-content--body body-scrollable--65 pa-3"
            >
              <v-flex md12>
                <v-layout>
                  <span class="remittance-dialog__section-header"
                    >Edit Quantity</span
                  >
                </v-layout>
                <v-divider class="my-2"></v-divider>
                <v-form>
                  <v-layout wrap>
                    <v-flex md12>
                      <v-text-field
                        v-model="description"
                        color="orange"
                        label="Charge Description"
                        box
                        background-color="#20202a"
                        class="form-field-required"
                      ></v-text-field>
                    </v-flex>

                    <v-flex md12>
                      <v-text-field
                        label="Please Enter A New Rate"
                        min="0"
                        :disabled="isLoading"
                        :rules="[validate.required, validate.number]"
                        v-model.number="rate"
                        type="number"
                        box
                      ></v-text-field>
                    </v-flex>
                    <v-flex md12>
                      <v-text-field
                        label="Please Enter A New Quantity"
                        min="0"
                        :disabled="isLoading"
                        :rules="[validate.required, validate.number]"
                        v-model.number="quantity"
                        type="number"
                        box
                      ></v-text-field>
                    </v-flex>

                    <v-flex md12>
                      <v-checkbox
                        label="Charge is Deduction"
                        v-model="isDeduction"
                      />
                    </v-flex>
                  </v-layout>
                </v-form>
              </v-flex>
              <v-flex md12>
                <v-layout>
                  <span class="remittance-dialog__section-header"
                    >Applied Deduction</span
                  >
                </v-layout>
                <v-divider class="my-2"></v-divider>

                <v-layout justify-space-between>
                  <span class="remittance-dialog__header">Rate</span>
                  <span class="remittance-dialog__value">
                    ${{displayCurrencyValue(customAdjustment.rate)}}
                  </span>
                </v-layout>
                <v-layout justify-space-between>
                  <span class="remittance-dialog__header">Quantity</span>
                  <span class="remittance-dialog__value">
                    {{quantity}}
                  </span>
                </v-layout>
                <v-layout justify-space-between>
                  <span class="remittance-dialog__header">Excl Gst</span>
                  <span class="remittance-dialog__value">
                    {{ deductionExclGst }}
                  </span>
                </v-layout>
                <v-layout justify-space-between>
                  <span class="remittance-dialog__header">Gst</span>
                  <span class="remittance-dialog__value">
                    {{ deductionGst }}
                  </span>
                </v-layout>
                <v-layout justify-space-between>
                  <span class="remittance-dialog__header">Total</span>
                  <span class="remittance-dialog__value">
                    {{ deductionTotal}}
                  </span>
                </v-layout>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-divider></v-divider>
            <v-layout align-center>
              <ConfirmationDialog
                buttonText="Remove Adjustment"
                :message="'Please confirm that you wish to remove this custom adjustment from contract: ' + contractNumber + '.'"
                title="Confirm Adjustment Removal"
                @confirm="deleteAdminAdjustment"
                :isSmallButton="true"
                :buttonDisabled="!isAuthorised()"
                :isOutlineButton="false"
                :isFlatButton="true"
                :buttonColor="'error'"
                :isLoading="isLoadingDeletionOfAdjustment"
                :confirmationButtonText="'Confirm and Remove'"
              ></ConfirmationDialog>

              <v-spacer></v-spacer>
              <v-btn color="white" flat @click="closeDialog">
                Cancel
              </v-btn>
              <v-btn
                depressed
                color="blue"
                :disabled="!isAuthorised()"
                @click="saveAdminAdjustment"
                :loading="isLoading"
              >
                Save
              </v-btn>
            </v-layout>
          </v-flex>
        </v-layout>
      </div>
    </v-layout>
  </v-dialog>
</v-layout>
