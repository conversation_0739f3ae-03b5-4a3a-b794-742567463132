<v-layout>
  <v-dialog
    v-model="dialogIsActive"
    width="60%"
    class="ma-0"
    persistent
    no-click-animation
    content-class="v-dialog-custom"
  >
    <template v-slot:activator="{ on }">
      <v-list-tile
        v-on="on"
        @click="() => null"
        style="width: 100%"
        :disabled="isDisabled"
      >
        <v-list-tile-action>
          <v-icon size="18" :disabled="isDisabled"
            >far fa-file-chart-line</v-icon
          >
        </v-list-tile-action>
        <v-list-tile-content>
          <v-list-tile-title>
            <span style="text-transform: none; font-weight: 400"
              >View RCTI Summary Report</span
            >
          </v-list-tile-title>
        </v-list-tile-content>
      </v-list-tile>
    </template>
    <div>
      <v-layout
        justify-space-between
        align-center
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <div>
          <span class="mr-2">RCTI Summary Report</span>
        </div>
        <div
          class="app-theme__center-content--closebutton"
          @click="dialogIsActive = false"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <v-layout
        row
        wrap
        class="app-theme__center-content--body dialog-content pa-3"
      >
        <v-flex md12>
          <v-layout wrap class="accounting-summary-report">
            <v-flex md12 class="summary-report__page-header">
              <v-layout align-start>
                <span class="summary-report__page-header--title">
                  {{companyName}}
                </span>
                <v-spacer></v-spacer>
                <span class="summary-report__page-header--reportname">
                  RCTI Summary
                </span>
              </v-layout>
              <v-layout pt-2>
                <span class="summary-report__page-header--label">
                  Run Date:
                </span>
                <span class="summary-report__page-header--value">
                  {{runDate}}
                </span>
                <v-spacer></v-spacer>
              </v-layout>
              <v-layout pt-2>
                <span class="summary-report__page-header--label">
                  Number of Jobs:
                </span>
                <span class="summary-report__page-header--value">
                  {{jobCount}}
                </span>
              </v-layout>
            </v-flex>
            <v-flex md12>
              <v-divider class="my-2"></v-divider>
            </v-flex>
            <v-flex md12 pa-2 pb-3>
              <v-layout row wrap>
                <v-flex
                  md12
                  class="summary-report__column pa-3 app-bgcolor--400 app-bordercolor--600 app-borderside--a"
                >
                  <v-layout justify-center>
                    <v-flex md12>
                      <v-layout justify-space-between align-center px-2>
                        <h6 class="subheader--faded">
                          Subcontractor Charge Summary
                        </h6>
                      </v-layout>
                      <v-layout px-2>
                        <v-divider class="mb-3"></v-divider>
                      </v-layout>
                      <v-layout>
                        <v-flex md12 class="report-line__headers">
                          <v-layout align-center>
                            <v-flex
                              ><span class="pl-1 report-line__columnheader"
                                >Description</span
                              ></v-flex
                            >
                            <v-spacer></v-spacer>
                            <v-flex md1
                              ><v-layout justify-end>
                                <span class="report-line__columnheader"
                                  >Value ($)</span
                                >
                              </v-layout></v-flex
                            >
                            <v-flex md1
                              ><v-layout justify-end>
                                <span class="report-line__columnheader"
                                  >GST ($)</span
                                >
                              </v-layout></v-flex
                            >
                            <v-flex md1
                              ><v-layout justify-end>
                                <span class="report-line__columnheader"
                                  >Total ($)</span
                                >
                              </v-layout></v-flex
                            >
                          </v-layout>
                        </v-flex>
                      </v-layout>
                      <v-layout
                        class="report-line__row"
                        align-center
                        wrap
                        v-for="(summary, chargeIndex) of rctiSummaryReport"
                        :key="chargeIndex"
                      >
                        <v-flex :class="summary.isSubCharge ? 'pl-3' :''">
                          <span class="report-line__text"
                            >{{summary.name}}
                          </span>
                        </v-flex>
                        <v-spacer></v-spacer>

                        <v-flex
                          md3
                          :class="chargeIndex === rctiSummaryReport.length - 1 ?'app-borderside--t app-bordercolor--600 pt-1' : ''"
                        >
                          <v-layout>
                            <v-flex md4>
                              <v-layout justify-end align-center>
                                <span class="report-line__value">
                                  {{summary.charge.exclGst}}
                                </span>
                              </v-layout>
                            </v-flex>
                            <v-flex md4>
                              <v-layout justify-end align-center>
                                <span class="report-line__value">
                                  {{summary.charge.gst}}
                                </span>
                              </v-layout>
                            </v-flex>
                            <v-flex md4>
                              <v-layout justify-end align-center>
                                <span
                                  class="report-line__value"
                                  :class="chargeIndex === rctiSummaryReport.length - 1 ? 'final-total' : ''"
                                >
                                  {{summary.charge.total}}
                                </span>
                              </v-layout>
                            </v-flex>
                          </v-layout>
                        </v-flex>

                        <v-flex md12 v-if="summary.name === 'RCTI Adjustments'">
                          <v-divider class="my-1"></v-divider>
                        </v-flex>
                      </v-layout>
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex
                  md12
                  class="summary-report__column mt-2 pa-3 app-bgcolor--400 app-bordercolor--600 app-borderside--a"
                >
                  <v-layout justify-space-between align-center px-2>
                    <h6 class="subheader--faded">Breakdown by Rate Code</h6>
                  </v-layout>
                  <v-layout px-2>
                    <v-divider class="mb-3"></v-divider>
                  </v-layout>
                  <v-layout row wrap>
                    <v-flex
                      md6
                      px-1
                      class="report-line__headers"
                      v-for="(rateCodeBreakDownHeader, headerIndex) of 2"
                      :key="rateCodeBreakDownHeader + '' + headerIndex"
                    >
                      <v-layout px-2 align-center>
                        <v-flex md1
                          ><span class="report-line__columnheader"
                            >Rate</span
                          ></v-flex
                        >
                        <v-flex
                          ><span class="pl-1 report-line__columnheader"
                            >Description</span
                          ></v-flex
                        >
                        <v-spacer></v-spacer>
                        <v-flex md2
                          ><v-layout justify-end>
                            <span class="report-line__columnheader"
                              >Amount ($)</span
                            >
                          </v-layout></v-flex
                        >
                      </v-layout>
                    </v-flex>
                    <v-flex
                      md6
                      v-for="(serviceBreakdown, serviceBreakdownIndex) in breakDownByRateCodes"
                      :key="serviceBreakdown.service"
                      class="report-line__row px-1"
                      :class="(serviceBreakdownIndex % 2) ? '' : 'column-border-right'"
                    >
                      <v-layout px-2>
                        <v-flex md1
                          ><span class="report-line__text"
                            >{{serviceBreakdown.service}}</span
                          ></v-flex
                        >
                        <span class="pl-1 report-line__text"
                          >{{serviceBreakdown.description}}</span
                        >
                        <v-spacer></v-spacer>
                        <v-flex md2
                          ><v-layout justify-end>
                            <span class="report-line__value"
                              >{{serviceBreakdown.total}}</span
                            >
                          </v-layout></v-flex
                        >
                      </v-layout>
                    </v-flex>
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
  </v-dialog>
</v-layout>
