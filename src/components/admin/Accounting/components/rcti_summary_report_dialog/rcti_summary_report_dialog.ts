import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import {
  DisplayCurrencyValue,
  RoundCurrencyValue,
} from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { hasAdminOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { AccountingJobRow } from '@/interface-models/Accounting/AccountingJobRow';
import { ReportCurrencyDetails } from '@/interface-models/Accounting/CurrencyDetails';
import { RctiInvoiceRow } from '@/interface-models/Accounting/FleetAssetOwnerAccountingTable';
import { InvoiceSubtotals } from '@/interface-models/Accounting/InvoiceSubtotals';
import { RctiAdministrationAdjustments } from '@/interface-models/Accounting/RctiAdministrationAdjustments';
import { AdjustmentChargeType } from '@/interface-models/Generic/Accounting/AdjustmentChargeType';
import { SumType } from '@/interface-models/Generic/Accounting/SumType';
import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { useClientInvoiceStore } from '@/store/modules/ClientInvoiceStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { Component, Prop, Vue } from 'vue-property-decorator';

interface ClientInvoiceSummaryReport {
  name: string;
  charge: ReportCurrencyDetails;
  isSubCharge: boolean;
  isDeduction: boolean;
}

interface RateCodeBreakdown {
  service: string;
  description: string;
  total: string;
}

@Component({
  components: { DatePickerBasic, InformationTooltip },
})
export default class RctiSummaryReportDialog
  extends Vue
  implements IUserAuthority
{
  @Prop() public rctiInvoices: RctiInvoiceRow[];
  @Prop({ default: false }) public isDisabled: boolean;

  public clientInvoiceStore = useClientInvoiceStore();
  public companyDetailsStore = useCompanyDetailsStore();

  public dialogIsActive: boolean = false;

  public isAuthorised(): boolean {
    return hasAdminOrTeamLeaderOrBranchManagerRole();
  }

  get companyName() {
    return this.companyDetailsStore.companyDetails?.name?.toUpperCase() ?? '-';
  }

  get runDate(): string {
    const runDate = this.clientInvoiceStore.weekEndingDate
      ? this.clientInvoiceStore.processingDate
      : this.clientInvoiceStore.weekEndingDate;

    return runDate ? returnFormattedDate(runDate, 'DD/MM/YY') : '-';
  }

  get jobCount(): number {
    let jobCount: number = 0;
    for (const rctiInvoice of this.rctiInvoices) {
      for (const fleetAsset of rctiInvoice.fleetAssets) {
        jobCount += fleetAsset.jobList.length;
      }
    }
    return jobCount;
  }

  get rctiSummaryReport(): ClientInvoiceSummaryReport[] {
    const clientInvoiceSummaryReport: ClientInvoiceSummaryReport[] = [];
    const administrationAdjustments: RctiAdministrationAdjustments[] = [];

    let freightSubtotalExclGst: number = 0;
    let freightSubtotalGst: number = 0;
    let freightSubtotalTotal: number = 0;

    let baseFreightExclGst: number = 0;
    let baseFreightGst: number = 0;
    let baseFreightTotal: number = 0;

    let standbyFreightExclGst: number = 0;
    let standbyFreightGst: number = 0;
    let standbyFreightTotal: number = 0;

    let demurrageExclGst: number = 0;
    let demurrageGst: number = 0;
    let demurrageTotal: number = 0;

    let travelDelayExclGst: number = 0;
    let travelDelayGst: number = 0;
    let travelDelayTotal: number = 0;

    let outsideMetroExclGst: number = 0;
    let outsideMetroGst: number = 0;
    let outsideMetroTotal: number = 0;

    let freightAdjustmentExclGst: number = 0;
    let freightAdjustmentGst: number = 0;
    let freightAdjustmentTotal: number = 0;

    let fuelExclGst: number = 0;
    let fuelGst: number = 0;
    let fuelTotal: number = 0;

    let tollsExclGst: number = 0;
    let tollsGst: number = 0;
    let tollsTotal: number = 0;

    let overallAdjustmentExclGst: number = 0;
    let overallAdjustmentGst: number = 0;
    let overallAdjustmentTotal: number = 0;

    let totalInvoicedExclGst: number = 0;
    let totalInvoicedGst: number = 0;
    let totalInvoicedTotal: number = 0;

    for (const rctiInvoice of this.rctiInvoices) {
      if (!rctiInvoice.invoiceSubtotals) {
        continue;
      }
      const totals: InvoiceSubtotals = rctiInvoice.invoiceSubtotals;
      freightSubtotalExclGst += totals.freightSubtotal.exclGst;
      freightSubtotalGst += totals.freightSubtotal.gst;
      freightSubtotalTotal += totals.freightSubtotal.total;

      // Because standby is included in baseFreight we need to remove standby charge from this sum
      baseFreightExclGst +=
        totals.baseFreight.exclGst - totals.standbyFreight.exclGst;
      baseFreightGst += totals.baseFreight.gst - totals.standbyFreight.gst;
      baseFreightTotal +=
        totals.baseFreight.total - totals.standbyFreight.total;

      standbyFreightExclGst += totals.standbyFreight.exclGst;
      standbyFreightGst += totals.standbyFreight.gst;
      standbyFreightTotal += totals.standbyFreight.total;

      demurrageExclGst += totals.demurrageFreight.exclGst;
      demurrageGst += totals.demurrageFreight.gst;
      demurrageTotal += totals.demurrageFreight.total;

      travelDelayExclGst += totals.travelDelayFreight.exclGst;
      travelDelayGst += totals.travelDelayFreight.gst;
      travelDelayTotal += totals.travelDelayFreight.total;

      freightAdjustmentExclGst += totals.freightAdditionalCharges.exclGst;
      freightAdjustmentGst += totals.freightAdditionalCharges.gst;
      freightAdjustmentTotal += totals.freightAdditionalCharges.total;

      fuelExclGst += totals.fuelSurcharge.exclGst;
      fuelGst += totals.fuelSurcharge.gst;
      fuelTotal += totals.fuelSurcharge.total;

      overallAdjustmentExclGst += totals.overallAdditionalCharges.exclGst;
      overallAdjustmentGst += totals.overallAdditionalCharges.gst;
      overallAdjustmentTotal += totals.overallAdditionalCharges.total;

      totalInvoicedExclGst += rctiInvoice.totalRemittedExclGst;
      totalInvoicedGst += rctiInvoice.totalRemittedGst;
      totalInvoicedTotal += rctiInvoice.totalRemitted;

      // calculate tolls and outside metro sum via client invoice job list and also push all adjustment charges to administrationAdjustments array for summing.
      for (const fleetAsset of rctiInvoice.fleetAssets) {
        for (const job of fleetAsset.jobList) {
          tollsExclGst += job.tollCharges.exclGst;
          tollsGst += job.tollCharges.gst;
          tollsTotal += job.tollCharges.total;

          // Because toll charges are included in overallAdditionalCharges we need to remove toll charges from its sum
          overallAdjustmentExclGst -= job.tollCharges.exclGst;
          overallAdjustmentGst -= job.tollCharges.gst;
          overallAdjustmentTotal -= job.tollCharges.total;

          outsideMetroExclGst += job.outsideMetroCharges.exclGst;
          outsideMetroGst += job.outsideMetroCharges.gst;
          outsideMetroTotal += job.outsideMetroCharges.total;
        }
        // push adjustment charges into administrationAdjustments array. We will use this array to calculate adjustment charges further down.
        for (const adjustmentCharge of fleetAsset.administrationAdjustments) {
          administrationAdjustments.push(adjustmentCharge);
        }
      }
    }

    const overallFreightTotal: ClientInvoiceSummaryReport = {
      name: 'Freight Subtotal',
      charge: {
        exclGst: DisplayCurrencyValue(
          RoundCurrencyValue(freightSubtotalExclGst),
        ),
        gst: DisplayCurrencyValue(RoundCurrencyValue(freightSubtotalGst)),
        total: DisplayCurrencyValue(RoundCurrencyValue(freightSubtotalTotal)),
      },
      isSubCharge: false,
      isDeduction: false,
    };

    const freightServiceCharge: ClientInvoiceSummaryReport = {
      name: 'Base Freight Service Charges',
      charge: {
        exclGst: DisplayCurrencyValue(RoundCurrencyValue(baseFreightExclGst)),
        gst: DisplayCurrencyValue(RoundCurrencyValue(baseFreightGst)),
        total: DisplayCurrencyValue(RoundCurrencyValue(baseFreightTotal)),
      },
      isSubCharge: true,
      isDeduction: false,
    };

    const freightStandbyCharge: ClientInvoiceSummaryReport = {
      name: 'Freight Standby Charge',
      charge: {
        exclGst: DisplayCurrencyValue(
          RoundCurrencyValue(standbyFreightExclGst),
        ),
        gst: DisplayCurrencyValue(RoundCurrencyValue(standbyFreightGst)),
        total: DisplayCurrencyValue(RoundCurrencyValue(standbyFreightTotal)),
      },
      isSubCharge: true,
      isDeduction: false,
    };

    const demurrageCharge: ClientInvoiceSummaryReport = {
      name: 'Demurrage Charges',
      charge: {
        exclGst: DisplayCurrencyValue(RoundCurrencyValue(demurrageExclGst)),
        gst: DisplayCurrencyValue(RoundCurrencyValue(demurrageGst)),
        total: DisplayCurrencyValue(RoundCurrencyValue(demurrageTotal)),
      },
      isSubCharge: true,
      isDeduction: false,
    };

    const travelDelayCharge: ClientInvoiceSummaryReport = {
      name: 'Travel Delay Charges',
      charge: {
        exclGst: DisplayCurrencyValue(RoundCurrencyValue(travelDelayExclGst)),
        gst: DisplayCurrencyValue(RoundCurrencyValue(travelDelayGst)),
        total: DisplayCurrencyValue(RoundCurrencyValue(travelDelayTotal)),
      },
      isSubCharge: true,
      isDeduction: false,
    };

    const outsideMetroSurcharge: ClientInvoiceSummaryReport = {
      name: 'Outside Metro Surcharge',
      charge: {
        exclGst: DisplayCurrencyValue(RoundCurrencyValue(outsideMetroExclGst)),
        gst: DisplayCurrencyValue(RoundCurrencyValue(outsideMetroGst)),
        total: DisplayCurrencyValue(RoundCurrencyValue(outsideMetroTotal)),
      },
      isSubCharge: true,
      isDeduction: false,
    };

    const freightAdjustmentCharge: ClientInvoiceSummaryReport = {
      name: 'Freight Adjustment Charges',
      charge: {
        exclGst: DisplayCurrencyValue(
          RoundCurrencyValue(freightAdjustmentExclGst),
        ),
        gst: DisplayCurrencyValue(RoundCurrencyValue(freightAdjustmentGst)),
        total: DisplayCurrencyValue(RoundCurrencyValue(freightAdjustmentTotal)),
      },
      isSubCharge: true,
      isDeduction: false,
    };

    const fuelSurcharge: ClientInvoiceSummaryReport = {
      name: 'Fuel Surcharge',
      charge: {
        exclGst: DisplayCurrencyValue(RoundCurrencyValue(fuelExclGst)),
        gst: DisplayCurrencyValue(RoundCurrencyValue(fuelGst)),
        total: DisplayCurrencyValue(RoundCurrencyValue(fuelTotal)),
      },
      isSubCharge: false,
      isDeduction: false,
    };

    const tollCharge: ClientInvoiceSummaryReport = {
      name: 'Toll Charges',
      charge: {
        exclGst: DisplayCurrencyValue(RoundCurrencyValue(tollsExclGst)),
        gst: DisplayCurrencyValue(RoundCurrencyValue(tollsGst)),
        total: DisplayCurrencyValue(RoundCurrencyValue(tollsTotal)),
      },
      isSubCharge: false,
      isDeduction: false,
    };

    const overallAdjustments: ClientInvoiceSummaryReport = {
      name: 'Job Adjustments',
      charge: {
        exclGst: DisplayCurrencyValue(
          RoundCurrencyValue(overallAdjustmentExclGst),
        ),
        gst: DisplayCurrencyValue(RoundCurrencyValue(overallAdjustmentGst)),
        total: DisplayCurrencyValue(RoundCurrencyValue(overallAdjustmentTotal)),
      },
      isSubCharge: false,
      isDeduction: false,
    };

    const administrationFees: ClientInvoiceSummaryReport = {
      name: 'Administration Fees',
      charge: this.getAdjustmentChargeTotals(
        administrationAdjustments,
        2,
        null,
      ),
      isSubCharge: false,
      isDeduction: true,
    };

    const deviceAdjustments: ClientInvoiceSummaryReport = {
      name: 'Device Deductions',
      charge: this.getAdjustmentChargeTotals(
        administrationAdjustments,
        1,
        null,
      ),
      isSubCharge: false,
      isDeduction: true,
    };

    const phoneAdjustments: ClientInvoiceSummaryReport = {
      name: 'Phone',
      charge: this.getAdjustmentChargeTotals(administrationAdjustments, 1, 1),
      isSubCharge: true,
      isDeduction: true,
    };

    const tabletAdjustments: ClientInvoiceSummaryReport = {
      name: 'Tablet',
      charge: this.getAdjustmentChargeTotals(administrationAdjustments, 1, 2),
      isSubCharge: true,
      isDeduction: true,
    };

    const rctiAdjustments: ClientInvoiceSummaryReport = {
      name: 'RCTI Adjustments',
      charge: this.getAdjustmentChargeTotals(
        administrationAdjustments,
        4,
        null,
      ),
      isSubCharge: false,
      isDeduction: true,
    };

    const mobileAppAdjustments: ClientInvoiceSummaryReport = {
      name: 'Mobile Application',
      charge: this.getAdjustmentChargeTotals(
        administrationAdjustments,
        5,
        null,
      ),
      isSubCharge: false,
      isDeduction: true,
    };

    const trailerParkingAdjustments: ClientInvoiceSummaryReport = {
      name: 'Trailer Parking',
      charge: this.getAdjustmentChargeTotals(
        administrationAdjustments,
        6,
        null,
      ),
      isSubCharge: false,
      isDeduction: true,
    };

    const customAdjustmentCharge = this.getAdjustmentChargeTotals(
      administrationAdjustments,
      3,
      null,
    );

    const customAdjustments: ClientInvoiceSummaryReport = {
      name: 'Custom Adjustments',
      charge: customAdjustmentCharge,
      isSubCharge: false,
      isDeduction:
        parseInt(customAdjustmentCharge.total, 10) >= 0 ? false : true,
    };

    const totalInvoiced: ClientInvoiceSummaryReport = {
      name: 'Total Invoiced',
      charge: {
        exclGst: DisplayCurrencyValue(RoundCurrencyValue(totalInvoicedExclGst)),
        gst: DisplayCurrencyValue(RoundCurrencyValue(totalInvoicedGst)),
        total: DisplayCurrencyValue(RoundCurrencyValue(totalInvoicedTotal)),
      },
      isSubCharge: false,
      isDeduction: false,
    };

    clientInvoiceSummaryReport.push(overallFreightTotal);
    clientInvoiceSummaryReport.push(freightServiceCharge);
    clientInvoiceSummaryReport.push(freightStandbyCharge);
    clientInvoiceSummaryReport.push(demurrageCharge);
    clientInvoiceSummaryReport.push(travelDelayCharge);
    clientInvoiceSummaryReport.push(outsideMetroSurcharge);
    clientInvoiceSummaryReport.push(freightAdjustmentCharge);
    clientInvoiceSummaryReport.push(fuelSurcharge);
    clientInvoiceSummaryReport.push(tollCharge);
    clientInvoiceSummaryReport.push(overallAdjustments);

    clientInvoiceSummaryReport.push(rctiAdjustments);
    clientInvoiceSummaryReport.push(administrationFees);
    clientInvoiceSummaryReport.push(deviceAdjustments);
    clientInvoiceSummaryReport.push(phoneAdjustments);
    clientInvoiceSummaryReport.push(tabletAdjustments);
    clientInvoiceSummaryReport.push(mobileAppAdjustments);
    clientInvoiceSummaryReport.push(trailerParkingAdjustments);

    clientInvoiceSummaryReport.push(customAdjustments);
    clientInvoiceSummaryReport.push(totalInvoiced);
    return clientInvoiceSummaryReport;
  }

  public getAdjustmentChargeTotals(
    administrationAdjustmentList: RctiAdministrationAdjustments[],
    categoryId: number,
    subCategoryId: number | null,
  ): ReportCurrencyDetails {
    let administrationAdjustments = administrationAdjustmentList.filter(
      (x: RctiAdministrationAdjustments) => x.categoryId === categoryId,
    );

    if (subCategoryId !== null) {
      administrationAdjustments = administrationAdjustments.filter(
        (x: RctiAdministrationAdjustments) => x.subCategoryId === subCategoryId,
      );
    }

    let exclGst = 0;
    let gst = 0;
    let total = 0;

    for (const administrationAdjustment of administrationAdjustments) {
      // exclGst += administrationAdjustment.total.exclGst;
      // gst += administrationAdjustment.total.gst;
      // total += administrationAdjustment.total.total;

      const isDeduction: boolean =
        (administrationAdjustment.adjustmentChargeType ===
          AdjustmentChargeType.RCTI &&
          administrationAdjustment.sumType === SumType.DEDUCTION) ||
        (administrationAdjustment.adjustmentChargeType ===
          AdjustmentChargeType.TAX_INVOICE &&
          administrationAdjustment.sumType === SumType.DEDUCTION);

      if (!isDeduction) {
        exclGst += administrationAdjustment.total.exclGst;
        gst += administrationAdjustment.total.gst;
        total +=
          administrationAdjustment.total.exclGst +
          administrationAdjustment.total.gst;
      } else {
        exclGst -= administrationAdjustment.total.exclGst;
        gst -= administrationAdjustment.total.gst;
        total -=
          administrationAdjustment.total.exclGst +
          administrationAdjustment.total.gst;
      }
    }

    // return the charges and make negative values contain parenthesis
    return {
      exclGst:
        exclGst < 0
          ? '(' +
            DisplayCurrencyValue(Math.abs(RoundCurrencyValue(exclGst))) +
            ')'
          : DisplayCurrencyValue(RoundCurrencyValue(exclGst)),
      gst:
        gst < 0
          ? '(' + DisplayCurrencyValue(Math.abs(RoundCurrencyValue(gst))) + ')'
          : DisplayCurrencyValue(RoundCurrencyValue(gst)),
      total:
        total < 0
          ? '(' +
            DisplayCurrencyValue(Math.abs(RoundCurrencyValue(total))) +
            ')'
          : DisplayCurrencyValue(RoundCurrencyValue(total)),
    };
  }

  get breakDownByRateCodes(): RateCodeBreakdown[] {
    const rateCodeBreakdowns: RateCodeBreakdown[] = [];
    const jobList: AccountingJobRow[] = [];
    for (const rctiInvoice of this.rctiInvoices) {
      for (const fleetAsset of rctiInvoice.fleetAssets) {
        for (const job of fleetAsset.jobList) {
          jobList.push(job);
        }
      }
    }
    useCompanyDetailsStore().getServiceTypesList.forEach(
      (serviceType: ServiceTypes, index: number) => {
        const jobsByServiceType = jobList.filter(
          (x: AccountingJobRow) =>
            x.serviceTypeId === serviceType.serviceTypeId,
        );
        if (jobsByServiceType.length === 0) {
          return;
        }
        const service: string = serviceType.shortServiceTypeName;
        const description: string = serviceType.longServiceTypeName;
        let total: number = 0;
        for (const job of jobsByServiceType) {
          total += job.totalChargeInclGst;
        }

        const rateBreakdown = {
          service,
          description,
          total: DisplayCurrencyValue(RoundCurrencyValue(total)),
        };
        rateCodeBreakdowns.push(rateBreakdown);
      },
    );

    return rateCodeBreakdowns;
  }
}
