<v-layout>
  <v-dialog
    v-model="dialogIsActive"
    width="450px"
    class="ma-0"
    persistent
    no-click-animation
    content-class="v-dialog-custom"
  >
    <template v-slot:activator="{ on }">
      <v-list-tile
        v-on="on"
        @click="() => null"
        style="width:100%"
        :disabled="!isDraft || !isAuthorised() || !includeAdjustments"
      >
        <v-list-tile-action>
          <v-icon
            :disabled="!isDraft || !isAuthorised() || !includeAdjustments"
            size="18"
            >fas fa-truck-container</v-icon
          >
        </v-list-tile-action>
        <v-list-tile-content>
          <v-list-tile-title>
            <span style="text-transform: none; font-weight: 400;"
              >Excess Travel</span
            >
          </v-list-tile-title>
        </v-list-tile-content>
      </v-list-tile>
    </template>
    <div>
      <v-layout
        justify-space-between
        align-center
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <div>
          <span class="mr-2">Excess Travel</span>

          <InformationTooltip :right="true" tooltipType="info">
            <v-layout slot="content" row wrap>
              <v-flex md12>
                <p class="mb-1" slot="content">
                  The Excess distance traveled is defined by the hire contracts
                  allowed travel distance.
                </p>
              </v-flex>
            </v-layout>
          </InformationTooltip>
        </div>
        <div
          class="app-theme__center-content--closebutton"
          @click="dialogIsActive = false"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <v-layout row wrap class="app-theme__center-content--body dialog-content">
        <v-flex md12>
          <v-layout row wrap class="body-scrollable--65" pa-3>
            <v-flex md12>
              <v-layout pa-2 wrap>
                <v-flex md12 class="mb-2">
                  <v-alert type="warning" :value="true">
                    <span
                      >You are about to update the excess distance traveled for
                      this contracts charge. Please note:</span
                    >

                    <ul>
                      <li>
                        <span>
                          The amount entered should be the excess distance
                          traveled and not the total distance traveled.
                        </span>
                      </li>
                    </ul>
                  </v-alert>
                </v-flex>
                <v-flex md12>
                  <v-text-field
                    v-model.number="editedExcessTravelDistance"
                    color="orange"
                    label="Excess Distance Traveled"
                    :rules="[validate.number]"
                    suffix="KM"
                    box
                    min="0"
                    type="number"
                    class="form-field-required"
                    background-color="#20202a"
                  >
                  </v-text-field>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-divider></v-divider>
          <v-layout align-center>
            <v-spacer></v-spacer>
            <v-btn
              depressed
              color="blue"
              @click="confirm"
              :loading="isLoading"
              :disabled="!isAuthorised()"
            >
              <span>Save</span>
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
  </v-dialog>
</v-layout>
