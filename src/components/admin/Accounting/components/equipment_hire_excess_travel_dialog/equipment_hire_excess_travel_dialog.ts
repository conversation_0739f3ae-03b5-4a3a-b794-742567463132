import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { EquipmentHireExcessTravelDistanceRequest } from '@/interface-models/Generic/Accounting/hireContractChargeHistory/HireContractChargeHistory';
import { Validation } from '@/interface-models/Generic/Validation';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { useEquipmentHireInvoiceStore } from '@/store/modules/EquipmentHireInvoiceStore';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  components: { DatePickerBasic, InformationTooltip },
})
export default class EquipmentHireExcessTravelDialog
  extends Vue
  implements IUserAuthority
{
  @Prop() public excessTravelDistance: number;
  @Prop() public excessTravelRate: number;
  @Prop() public isDraft: boolean;
  @Prop() public isLoadingUserActionRequest: boolean;
  @Prop() public ledgerId: string;
  @Prop() public contractId: string;
  @Prop() public fleetAssetId: string;

  public editedExcessTravelDistance: number | null = null;

  public isLoading: boolean = false;

  public dialogIsActive: boolean = false;

  public isAuthorised(): boolean {
    return hasAdminOrHeadOfficeRole();
  }

  public async confirm(): Promise<void> {
    const request: EquipmentHireExcessTravelDistanceRequest = {
      ledgerId: this.ledgerId,
      contractId: this.contractId,
      fleetAssetId: this.fleetAssetId,
      excessTravelDistanceInKm: this.editedExcessTravelDistance,
    };
    this.isLoading = true;
    const result =
      await useEquipmentHireInvoiceStore().editLedgerExcessTravelDistance(
        request,
      );
    if (!result) {
      showNotification(GENERIC_ERROR_MESSAGE, {
        title: 'Save Excess Travel Distance',
      });
    }
    this.isLoading = false;
    this.$emit('closeMenuOptions');
    this.dialogIsActive = false;
  }

  get validate(): Validation {
    return validationRules;
  }

  // set the local excess travel variable to equal the parents excessTravel
  public mounted(): void {
    this.editedExcessTravelDistance = this.excessTravelDistance;
  }

  get includeAdjustments(): boolean | null {
    return useEquipmentHireInvoiceStore().includeAdjustments;
  }
}
