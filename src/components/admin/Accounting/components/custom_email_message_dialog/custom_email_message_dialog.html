<v-layout>
  <v-dialog
    v-model="dialogIsActive"
    width="400px"
    class="ma-0"
    persistent
    no-click-animation
    content-class="v-dialog-custom"
  >
    <template v-slot:activator="{ on }">
      <v-list-tile
        v-on="on"
        :disabled="!isAuthorised()"
        @click="() => null"
        style="width: 100%"
      >
        <v-list-tile-action>
          <v-icon size="18" class="pr-2" :disabled="!isAuthorised()"
            >fal fa-envelope-open-text</v-icon
          >
        </v-list-tile-action>
        <v-list-tile-content>
          <v-list-tile-title class="pr-2 ma-0">
            <span class="pr-2" style="text-transform: none; font-weight: 400"
              >Custom Email Message</span
            >
          </v-list-tile-title>
        </v-list-tile-content>
      </v-list-tile>
    </template>
    <div>
      <v-layout
        justify-space-between
        align-center
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>Custom Email Message</span>
        <div
          class="app-theme__center-content--closebutton"
          @click="closeDialog"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <v-layout row wrap class="app-theme__center-content--body dialog-content">
        <v-flex md12>
          <v-layout row wrap class="body-scrollable--65" pa-3>
            <v-flex md12>
              <v-layout pa-2 wrap>
                <v-flex md12 class="mb-2">
                  <v-alert type="warning" :value="true">
                    <span>
                      The custom email message will be appended to ALL outgoing
                      RCTI emails.
                    </span>
                  </v-alert>
                </v-flex>
              </v-layout>
              <v-layout class="mb-2">
                <v-divider></v-divider>
              </v-layout>

              <v-layout>
                <v-textarea
                  v-model="editedCustomEmailMessage"
                  auto-grow
                  box
                  label="Please Enter Your Custom Email Message"
                  rows="2"
                ></v-textarea>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-divider></v-divider>
          <v-layout align-center>
            <v-btn color="white" flat :disabled="isLoading" @click="closeDialog"
              >Cancel</v-btn
            >
            <v-spacer></v-spacer>
            <v-btn
              depressed
              color="blue"
              :loading="isLoading"
              @click="saveCustomEmailMessage"
            >
              Save Custom Email Message
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
  </v-dialog>
</v-layout>
