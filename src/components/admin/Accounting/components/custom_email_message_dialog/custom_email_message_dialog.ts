import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { InvoiceStatus } from '@/interface-models/Accounting/InvoiceStatus';
import { LedgerType } from '@/interface-models/Accounting/LedgerType';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { useFleetAssetOwnerInvoiceStore } from '@/store/modules/FleetAssetOwnerInvoiceStore';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class CustomEmailMessageDialog
  extends Vue
  implements IUserAuthority
{
  @Prop() public invoiceRunStatus: InvoiceStatus | null;
  @Prop() public ledgerType: LedgerType;
  @Prop() public customEmailMessage: string;

  public editedCustomEmailMessage: string = '';
  public dialogIsActive: boolean = false;
  public isLoading: boolean = false;

  @Watch('dialogIsActive')
  public dialogOpened(val: boolean) {
    if (!val) {
      this.editedCustomEmailMessage = '';
      return;
    }
    this.editedCustomEmailMessage = this.customEmailMessage;
  }

  public async saveCustomEmailMessage(): Promise<void> {
    this.isLoading = true;
    const result = await useFleetAssetOwnerInvoiceStore().setCustomEmailMessage(
      this.editedCustomEmailMessage,
    );
    if (!result) {
      showNotification(
        'The custom email message could not be saved. Please try again later.',
        {
          title: 'RCTI Run Email Message',
        },
      );
    }
    this.closeDialog();
  }

  public isAuthorised(): boolean {
    return hasAdminOrHeadOfficeRole();
  }

  public closeDialog(): void {
    this.dialogIsActive = false;
    this.isLoading = false;
    this.editedCustomEmailMessage = '';
    this.$emit('closeMenuOptions');
  }
}
