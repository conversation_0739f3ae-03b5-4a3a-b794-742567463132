<template>
  <v-tooltip right>
    <template v-slot:activator="{ on }">
      <v-icon size="18" color="warning" v-on="on"
        >fad fa-exclamation-circle</v-icon
      >
    </template>
    <v-layout>
      <v-flex md12>
        The following {{ typeName }} is missing information that is required for
        invoicing. The following information is required:
        <ul>
          <li v-for="(invalidField, index) of invalidFields" :key="index">
            {{ getInvalidFieldName(invalidField) }}
          </li>
        </ul>
        Please contact GoDesta support if assistance is required.
      </v-flex>
    </v-layout>
  </v-tooltip>
</template>

<script setup lang="ts">
import { LedgerType } from '@/interface-models/Accounting/LedgerType';
import { ComputedRef, computed } from 'vue';

const props = withDefaults(
  defineProps<{
    invalidFields?: string[];
    ledgerType: LedgerType;
  }>(),
  {
    invalidFields: () => [],
  },
);

function getInvalidFieldName(invalidField: string): string {
  return invalidField !== 'Email ids'
    ? invalidField
    : 'An accounts role email address or an accounts receivable email address.';
}

const typeName: ComputedRef<string> = computed(() => {
  if (props.ledgerType === LedgerType.CLIENT_INVOICE) {
    return 'client';
  } else if (
    props.ledgerType === LedgerType.RCTI ||
    props.ledgerType === LedgerType.EQUIPMENT_HIRE
  ) {
    return 'subcontractor';
  }
  return 'entity';
});
</script>
