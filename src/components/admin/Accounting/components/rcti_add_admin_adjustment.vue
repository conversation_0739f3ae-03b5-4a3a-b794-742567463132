<template>
  <v-layout>
    <v-btn
      small
      flat
      block
      @click="openDialog"
      color="#00b1e2"
      :disabled="!isAuthorised() || readOnly"
    >
      <v-icon left size="16" class="mr-2">fal fa-plus</v-icon>Add
      {{ chargeTypeName }}
    </v-btn>
    <ContentDialog
      :showDialog.sync="dialogIsActive"
      :title="`Add ${chargeTypeName} Charge`"
      width="500px"
      contentPadding="pa-0"
      @cancel="closeDialog"
      @confirm="saveCustomAdjustmentCharge"
      :showActions="true"
      :isConfirmUnsaved="false"
      :isDisabled="
        !customAdjustmentCharge?.description || !chargeAmount || !isAuthorised()
      "
      :isLoading="isLoading"
      confirmBtnText="Confirm"
    >
      <v-layout>
        <v-flex md12 class="body-scrollable--75 body-min-height--75 pa-3">
          <v-form v-if="customAdjustmentCharge">
            <v-flex md12>
              <v-layout>
                <v-flex md12 mb-3>
                  <v-alert
                    type="info"
                    :value="true"
                    v-if="adjustmentType === AdjustmentChargeType.TAX_INVOICE"
                  >
                    You are adding a CUSTOM ADJUSTMENT CHARGE. Please note:
                    <ul>
                      <li>
                        The total remitted amount will INCREASE if this charge
                        is a deduction and DECREASE if the charge is not a
                        deduction.
                      </li>
                    </ul>
                  </v-alert>
                  <v-alert
                    type="info"
                    :value="true"
                    v-if="adjustmentType === AdjustmentChargeType.RCTI"
                  >
                    You are adding a RCTI ADJUSTMENT CHARGE. Please note:
                    <ul>
                      <li>
                        The total remitted amount will DECREASE if this charge
                        is a deduction and INCREASE if the charge is not a
                        deduction.
                      </li>
                    </ul>
                  </v-alert>
                </v-flex>
              </v-layout>
              <v-layout>
                <v-select
                  v-model="customAdjustmentCharge.categoryId"
                  :items="adjustmentChargeCategoriesForType"
                  item-value="categoryId"
                  item-text="longName"
                  color="orange"
                  label="Category"
                  :disabled="adjustmentChargeCategoriesForType.length === 0"
                  box
                  class="form-field-required"
                ></v-select>
              </v-layout>
              <v-layout>
                <v-text-field
                  v-model="customAdjustmentCharge.description"
                  color="orange"
                  label="Charge Description"
                  box
                  class="form-field-required"
                ></v-text-field>
              </v-layout>
              <v-layout>
                <v-flex>
                  <v-text-field
                    v-model.number="chargeAmount"
                    color="orange"
                    label="Amount"
                    :rules="[validate.currency]"
                    prefix="$"
                    box
                    min="0"
                    type="number"
                    class="form-field-required"
                    @focus="$event.target.select()"
                  >
                  </v-text-field>
                </v-flex>
              </v-layout>
              <v-layout>
                <v-flex>
                  <v-checkbox
                    label="Charge is Deduction"
                    v-model="isDeduction"
                  />
                </v-flex>
              </v-layout>
            </v-flex>
          </v-form>
        </v-flex>
      </v-layout>
    </ContentDialog>
  </v-layout>
</template>

<script setup lang="ts">
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { RoundCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  getRctiAdjustmentId,
  isCustomAdjustmentCharge,
} from '@/helpers/RateHelpers/AdjustmentChargeHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { RctiAdministrationAdjustments } from '@/interface-models/Accounting/RctiAdministrationAdjustments';
import { AdjustmentChargeType } from '@/interface-models/Generic/Accounting/AdjustmentChargeType';
import { SumType } from '@/interface-models/Generic/Accounting/SumType';
import { AddAdjustmentChargeHistoryRequest } from '@/interface-models/Generic/AllowanceAndDeductions/AddAdjustmentChargeHistoryRequest';
import { AdjustmentChargeCategory } from '@/interface-models/Generic/AllowanceAndDeductions/AdditionalAllowanceAndDeductionTypes/AdjustmentChargeCategory';
import AdjustmentChargeHistory from '@/interface-models/Generic/AllowanceAndDeductions/AdjustmentChargeHistory';
import { DeviceList } from '@/interface-models/Generic/DeviceList/DeviceList';
import { useAdjustmentChargeStore } from '@/store/modules/AdjustmentChargeStore';
import { useFleetAssetOwnerInvoiceStore } from '@/store/modules/FleetAssetOwnerInvoiceStore';
import { computed, ComputedRef, ref } from 'vue';

const props = defineProps<{
  readOnly?: boolean;
  fleetAssetId: string;
  registeredForGst?: boolean;
  administrationAdjustment?: RctiAdministrationAdjustments | null;
  affiliationType: string;
  adjustmentType: AdjustmentChargeType;
  ledgerId?: string;
}>();

const dialogIsActive = ref(false);
const isLoading = ref(false);
const isDeduction = ref(true);
const customAdjustmentCharge = ref<AdjustmentChargeHistory>(
  new AdjustmentChargeHistory(),
);
const chargeAmount = ref(0.0);

/**
 * Checks if the user is authorised to perform the action.
 * @returns {boolean} - True if the user has admin or head office role.
 */
const isAuthorised = (): boolean => {
  return hasAdminOrHeadOfficeRole();
};

/**
 * Opens the dialog
 */
function openDialog() {
  if (props.adjustmentType === AdjustmentChargeType.RCTI) {
    customAdjustmentCharge.value.categoryId = getRctiAdjustmentId();
  }
  dialogIsActive.value = true;
}

/**
 * Closes the dialog and resets the form fields.
 */
function closeDialog() {
  customAdjustmentCharge.value = new AdjustmentChargeHistory();
  isDeduction.value = true;
  chargeAmount.value = 0.0;
  dialogIsActive.value = false;
}

const validate = validationRules;

/**
 * Computes the charge type name based on the adjustment type. Used in the
 * template in the button an dialog header.
 * @returns {string} - The charge type name.
 */
const chargeTypeName = computed((): string => {
  if (props.adjustmentType === AdjustmentChargeType.RCTI) {
    return 'RCTI Adjustment';
  } else if (props.adjustmentType === AdjustmentChargeType.TAX_INVOICE) {
    return 'Custom Adjustment';
  }
  return '';
});

const adjustmentChargeCategoriesForType: ComputedRef<
  AdjustmentChargeCategory[]
> = computed(() => {
  const categories =
    useAdjustmentChargeStore().adjustmentChargeCategories.filter(
      (c: AdjustmentChargeCategory) =>
        c.allowAdhocCharges && c.adjustmentChargeType === props.adjustmentType,
    );

  return [
    ...categories.filter((c) => !isCustomAdjustmentCharge(c.categoryId)),
    ...categories.filter((c) => isCustomAdjustmentCharge(c.categoryId)),
  ];
});

/**
 * Computes the adjustment charge category based on the adjustment type.
 * @returns {AdjustmentChargeCategory | null} - The adjustment charge category.
 */
const selectedAdjustmentChargeCategory = computed(
  (): AdjustmentChargeCategory | null => {
    if (!customAdjustmentCharge.value.categoryId) {
      return null;
    }
    return (
      adjustmentChargeCategoriesForType.value.find(
        (x: AdjustmentChargeCategory) =>
          x.categoryId === customAdjustmentCharge.value!.categoryId,
      ) ?? null
    );
  },
);

/**
 * Computes the charge subcategory name based on the adjustment charge category.
 * @returns {string} - The charge subcategory name.
 */
const chargeSubCategoryName = computed((): string => {
  if (!selectedAdjustmentChargeCategory.value) {
    return '';
  }

  const subCategoryId = 1;
  const subCategory = selectedAdjustmentChargeCategory.value.subCategories.find(
    (x: DeviceList) => x.subCategoryId === subCategoryId,
  );
  return subCategory ? subCategory.longName : '';
});

/**
 * Dispatches request to save the adjustment charge. Handles response then
 * closes the dialog.
 */
async function saveCustomAdjustmentCharge(): Promise<void> {
  const fleetAssetOwnerInvoiceStore = useFleetAssetOwnerInvoiceStore();
  if (
    !customAdjustmentCharge.value.categoryId ||
    !selectedAdjustmentChargeCategory.value ||
    fleetAssetOwnerInvoiceStore.processingDate === null ||
    !chargeAmount.value ||
    !props.ledgerId
  ) {
    return;
  }
  customAdjustmentCharge.value.chargeCategoryName =
    selectedAdjustmentChargeCategory.value.longName;
  customAdjustmentCharge.value.chargeSubCategoryName =
    chargeSubCategoryName.value;
  const fleetAssetEntityId = 1;
  customAdjustmentCharge.value.entityTypeId = fleetAssetEntityId;
  customAdjustmentCharge.value.entityId = props.fleetAssetId;
  customAdjustmentCharge.value.timestamp =
    fleetAssetOwnerInvoiceStore.processingDate ?? 0;
  customAdjustmentCharge.value.ledgerReferenceId = props.ledgerId;

  customAdjustmentCharge.value.chargeAmount = chargeAmount.value;
  customAdjustmentCharge.value.rate = chargeAmount.value;

  // Always apply GST for TAX_INVOICE type adjustment charges.
  if (selectedAdjustmentChargeCategory.value.alwaysApplyGst) {
    customAdjustmentCharge.value.gstAmount = RoundCurrencyValue(
      customAdjustmentCharge.value.chargeAmount / 10,
    );
  } else {
    // RCTI Adjustment - Whether or not GST applies is dependent on whether the owner
    // is registered for GST
    if (props.registeredForGst) {
      customAdjustmentCharge.value.gstAmount = RoundCurrencyValue(
        customAdjustmentCharge.value.chargeAmount / 10,
      );
    } else {
      customAdjustmentCharge.value.gstAmount = 0;
    }
  }

  customAdjustmentCharge.value.sumType = !isDeduction.value
    ? SumType.ADDITION
    : SumType.DEDUCTION;

  customAdjustmentCharge.value.adjustmentChargeType =
    selectedAdjustmentChargeCategory.value.adjustmentChargeType;

  isLoading.value = true;

  const request: AddAdjustmentChargeHistoryRequest = {
    ledgerId: props.ledgerId,
    adjustmentChargeHistory: customAdjustmentCharge.value,
  };

  const result =
    await fleetAssetOwnerInvoiceStore.addAdjustmentChargeToLedger(request);
  if (!result) {
    showNotification(GENERIC_ERROR_MESSAGE);
  }
  isLoading.value = false;
  closeDialog();
}
</script>

<style scoped lang="scss"></style>
