<template>
  <v-tooltip left>
    <template v-slot:activator="{ on }">
      <v-icon size="18" color="info" v-on="on"
        >fal fa-envelope-open-text</v-icon
      >
    </template>
    <v-layout>
      <v-flex md12>
        <span
          >The follow custom email message will be append to ALL outgoing
          emails:</span
        >
        <v-divider class="my-2"> </v-divider>
        <p>
          <i>{{ customEmailMessage }}</i>
        </p>
      </v-flex>
    </v-layout>
  </v-tooltip>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    customEmailMessage?: string;
  }>(),
  {
    customEmailMessage: '',
  },
);
</script>
