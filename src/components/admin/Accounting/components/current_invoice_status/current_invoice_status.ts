import { InvoiceStatus } from '@/interface-models/Accounting/InvoiceStatus';

import { Component, Prop, Vue, Watch } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class CurrentInvoiceStatus extends Vue {
  @Prop() public invoiceRunStatus: InvoiceStatus | null;
  // returns the current stage our invoice run is at.
  get runStatus(): InvoiceStatus | string {
    return this.invoiceRunStatus
      ? this.invoiceRunStatus
      : 'READY FOR INVOICING';
  }
}
