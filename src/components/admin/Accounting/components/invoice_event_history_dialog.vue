<template>
  <v-layout>
    <v-dialog
      v-model="dialogIsActive"
      width="400px"
      class="ma-0"
      persistent
      no-click-animation
      content-class="v-dialog-custom"
    >
      <template v-slot:activator="{ on }">
        <v-list-tile
          v-on="on"
          :disabled="!isAuthorised()"
          @click="() => null"
          style="width: 100%"
        >
          <v-list-tile-action>
            <v-icon size="18" class="pr-2" :disabled="!isAuthorised()"
              >far fa-history</v-icon
            >
          </v-list-tile-action>
          <v-list-tile-content>
            <v-list-tile-title class="pr-2 ma-0">
              <span class="pr-2" style="text-transform: none; font-weight: 400"
                >View Run Event History</span
              >
            </v-list-tile-title>
          </v-list-tile-content>
        </v-list-tile>
      </template>
      <div>
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight"
        >
          <span>Invoice Run Event History</span>
          <div
            class="app-theme__center-content--closebutton"
            @click="closeDialog"
            :disabled="invoiceEventHistory == null"
          >
            <v-icon
              :disabled="invoiceEventHistory == null"
              class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>
        <v-layout
          row
          wrap
          class="app-theme__center-content--body dialog-content"
        >
          <v-flex md12>
            <v-layout
              row
              wrap
              class="body-scrollable--65 body-min-height--65"
              pa-3
            >
              <v-flex md12>
                <v-layout pb-2 pt-1>
                  <h6 class="subheader--faded pb-0">Current Run Status:</h6>
                  <v-spacer />
                  <span
                    class="side-column__summaryitem--value status-container"
                  >
                    {{ invoiceRunStatus }}
                  </span>
                </v-layout>
                <v-layout class="mb-2">
                  <v-divider></v-divider>
                </v-layout>

                <v-layout
                  v-for="(eventItem, index) in eventListItems"
                  :key="index"
                >
                  <v-flex md2>
                    <v-layout align-center class="event-line-item">
                      <span class="eventlist-item__date">{{
                        eventItem.date
                      }}</span>
                    </v-layout>
                  </v-flex>

                  <v-flex md10>
                    <v-layout
                      align-center
                      class="event-line-item"
                      v-for="(event, eventIndex) in eventItem.events"
                      :key="eventIndex"
                    >
                      <span class="eventlist-item__time JobEvent">{{
                        event.hourOfDay
                      }}</span>

                      <span class="eventlist-item__title">{{
                        event.type
                      }}</span>
                      <span class="eventlist-item__connector-text">by </span
                      ><span class="eventlist-item__username"
                        >{{ event.user }}
                      </span>
                    </v-layout>
                  </v-flex>
                </v-layout>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-divider></v-divider>
            <v-layout align-center>
              <v-spacer></v-spacer>
              <v-btn depressed color="blue" @click="closeDialog"> Okay </v-btn>
            </v-layout>
          </v-flex>
        </v-layout>
      </div>
    </v-dialog>
  </v-layout>
</template>

<script setup lang="ts">
import { computed, ComputedRef, ref, Ref } from 'vue';
import { hasAdminOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { InvoiceEvent } from '@/interface-models/Accounting/InvoiceEvent';
import { InvoiceStatus } from '@/interface-models/Accounting/InvoiceStatus';
import {
  returnFormattedDate,
  returnFormattedTime,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
interface EventListItem {
  hourOfDay: string;
  date: string;
  type: string;
  user: string;
  jobList: number[];
}
interface EventListByDate {
  date: string;
  events: EventListItem[];
}

const props = withDefaults(
  defineProps<{
    invoiceEventHistory?: InvoiceEvent[];
    invoiceRunStatus?: InvoiceStatus | null;
  }>(),
  {
    invoiceRunStatus: null,
    invoiceEventHistory: () => [],
  },
);

const dialogIsActive: Ref<boolean> = ref(false);
const emit = defineEmits(['closeMenuOptions']);

function isAuthorised(): boolean {
  return hasAdminOrTeamLeaderOrBranchManagerRole();
}

function closeDialog() {
  dialogIsActive.value = false;
  emit('closeMenuOptions');
}

const eventListItems: ComputedRef<EventListByDate[]> = computed(() => {
  if (!props.invoiceEventHistory) {
    return [];
  }
  const eventList: EventListItem[] = props.invoiceEventHistory.map(
    (x: InvoiceEvent) => {
      return {
        hourOfDay: returnFormattedTime(x.timestamp),
        date: returnFormattedDate(x.timestamp),
        type: x.status,
        user: x.userName,
        jobList: x.jobIds,
      };
    },
  );
  const uniqueDates = [...new Set(eventList.map((s) => s.date))];
  const eventListByDate: EventListByDate[] = [];
  for (const date of uniqueDates) {
    const eventsOnDate = eventList.filter(
      (x: EventListItem) => x.date === date,
    );
    const datedEvent: EventListByDate = {
      date,
      events: eventsOnDate,
    };
    eventListByDate.push(datedEvent);
  }

  return eventListByDate;
});
</script>

<style scoped lang="scss">
.side-column__summaryitem--key {
  font-size: $font-size-13;
  text-transform: uppercase;
  font-weight: 600;
  color: rgb(186, 188, 209);
  padding-left: 3px;
}
.side-column__summaryitem--value {
  font-size: $font-size-13;
  font-weight: 500;
  padding-right: 3px;

  &.status-container {
    padding: 1px 8px;
    font-weight: 600;
    background-color: $outline-type;
    border-radius: 2px;
    letter-spacing: 0.03em;
  }
}

.eventlist-item {
  font-size: $font-size-12;
  color: rgb(205, 208, 220);
  &.with-hover {
    &:hover {
      filter: brightness(115%);
      background-color: $app-dark-primary-600;
      cursor: pointer;
    }
  }

  &.dense-list {
    font-size: $font-size-11;
    padding: 2px 0px;
  }

  &--selected {
    background-color: #484858;
  }
}

.eventlist-item__time {
  padding: 2px 4px;
  font-size: $font-size-11;
  border-radius: 2px;
  font-weight: 600;
  color: white;
  margin-left: 6px;
  margin-right: 6px;
  position: relative;
  white-space: nowrap;
  border: 1px solid #5c74cc;
}

.eventlist-item__date {
  color: grey;
  min-width: 55px;
  padding: 2px 4px;
  font-size: $font-size-11;
  font-weight: 600;
  text-align: right;
}

.eventlist-item__title {
  text-transform: uppercase;
  font-weight: 600;
  color: white;
}
.eventlist-item__connector-text {
  padding-left: 3px;
  font-size: $font-size-12;
  color: rgb(205, 208, 220);
}
.eventlist-item__change-list-text {
  padding-left: 3px;

  font-size: $font-size-11;
  color: rgb(165, 169, 186);
}
.eventlist-item__username {
  padding-left: 3px;
  font-size: $font-size-12;
  color: rgb(205, 208, 220);
}

.eventlist-item__chat-container {
  border-left: 1px solid grey;
  .eventlist-item__chat-content {
    padding: 2px 6px;
    font-style: italic;
  }
}

.event-line-item {
  height: 30px;
}
</style>
