<template>
  <v-layout v-if="administrationAdjustment">
    <ContentDialog
      :showDialog.sync="dialogIsActive"
      :title="`Edit ${administrationAdjustment.chargeDisplayName} Deduction`"
      width="600px"
      contentPadding="pa-0"
      @cancel="closeDialog"
      @confirm="saveAdminAdjustment"
      :showActions="true"
      :isConfirmUnsaved="false"
      confirmBtnText="Confirm"
      :isDisabled="!isAuthorised()"
      :isLoading="false"
      @action="deleteAdminAdjustment"
      :showActionButton="true"
      actionBtnText="Remove Adjustment"
      :actionRequiresConfirmation="true"
      :actionConfirmationMessage="
        'Please confirm that you wish to remove this ' +
        administrationAdjustment.chargeDisplayName +
        ' from fleet asset ' +
        csrAssignedId +
        '.'
      "
    >
      <v-layout>
        <v-flex md12 class="body-scrollable--75 pa-3">
          <v-layout row wrap>
            <v-flex md12>
              <v-layout
                row
                wrap
                class="app-theme__center-content--body body-scrollable--65 body-min-height--65 pa-3"
              >
                <v-flex md12 v-if="isEditable">
                  <v-layout>
                    <span class="remittance-dialog__section-header"
                      >Edit Quantity</span
                    >
                  </v-layout>
                  <v-divider class="my-2"></v-divider>

                  <v-layout>
                    <v-flex md12>
                      <v-form>
                        <v-text-field
                          label="Please Enter A New Quantity"
                          min="0"
                          :disabled="isLoading"
                          :rules="[validate.required, validate.number]"
                          v-model.number="quantity"
                          type="number"
                          @focus="$event.target.select()"
                          box
                        ></v-text-field>
                      </v-form>
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex md12>
                  <v-layout>
                    <span class="remittance-dialog__section-header"
                      >Applied Deduction</span
                    >
                  </v-layout>
                  <v-divider class="my-2"></v-divider>
                  <v-layout justify-space-between>
                    <span class="remittance-dialog__header"
                      >Deduction Type</span
                    >
                    <span class="remittance-dialog__value">
                      {{ administrationAdjustment.chargeDisplayName }}
                    </span>
                  </v-layout>
                  <v-layout justify-space-between>
                    <span class="remittance-dialog__header">Rate</span>
                    <span class="remittance-dialog__value">
                      ${{ DisplayCurrencyValue(administrationAdjustment.rate) }}
                    </span>
                  </v-layout>
                  <v-layout justify-space-between>
                    <span class="remittance-dialog__header">Quantity</span>
                    <span class="remittance-dialog__value">
                      {{ quantity }}
                    </span>
                  </v-layout>
                  <v-layout justify-space-between>
                    <span class="remittance-dialog__header">Excl Gst</span>
                    <span class="remittance-dialog__value">
                      {{ deductionValues.exclGst }}
                    </span>
                  </v-layout>
                  <v-layout justify-space-between>
                    <span class="remittance-dialog__header">Gst</span>
                    <span class="remittance-dialog__value">
                      {{ deductionValues.gst }}
                    </span>
                  </v-layout>
                  <v-layout justify-space-between>
                    <span class="remittance-dialog__header">Total</span>
                    <span class="remittance-dialog__value">
                      {{ deductionValues.total }}
                    </span>
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </ContentDialog>
  </v-layout>
</template>

<script setup lang="ts">
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import {
  calculateGstInclusiveTotals,
  DisplayCurrencyValue,
} from '@/helpers/AccountingHelpers/CurrencyHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import {
  AdminAdjustmentChargeEdit,
  DeleteAdminAdjustmentCharge,
} from '@/interface-models/Accounting/RctiAdminAjustmentEdit';
import { RctiAdministrationAdjustments } from '@/interface-models/Accounting/RctiAdministrationAdjustments';
import { Validation } from '@/interface-models/Generic/Validation';
import { useFleetAssetOwnerInvoiceStore } from '@/store/modules/FleetAssetOwnerInvoiceStore';
import { computed, ComputedRef, ref, Ref } from 'vue';

const props = defineProps<{
  csrAssignedId?: string;
  administrationAdjustment?: RctiAdministrationAdjustments;
  registeredForGst?: boolean;
  ledgerId: string;
}>();

const emit = defineEmits<{
  (event: 'closeEditAdminAdjustment', payload: boolean): void;
}>();

const fleetAssetOwnerInvoiceStore = useFleetAssetOwnerInvoiceStore();

const isLoading: Ref<boolean> = ref(false);
const quantity: Ref<number> = ref(0);
const isLoadingDeletionOfAdjustment: Ref<boolean> = ref(false);

const isAuthorised = (): boolean => {
  return hasAdminOrHeadOfficeRole();
};

const validate: Validation = validationRules;

const dialogIsActive = computed<boolean>({
  get() {
    return props.administrationAdjustment !== null;
  },
  set(value: boolean) {
    emit('closeEditAdminAdjustment', value);
  },
});

/**
 * Closes the dialog and resets the loading state.
 */
function closeDialog(): void {
  isLoading.value = false;
  isLoadingDeletionOfAdjustment.value = false;
  dialogIsActive.value = false;
}

/**
 * Used in the template to determine if certain buttons after available.
 * Disabled when the category type is ADMINISTRATION DEDUCTION
 */
const isEditable: ComputedRef<boolean> = computed<boolean>(() => {
  return props.administrationAdjustment?.categoryId !== 2;
});

/**
 * Deduction values including GST calculations.
 */
const deductionValues: ComputedRef<{
  exclGst: string;
  gst: string;
  total: string;
}> = computed(() => {
  const exclGstAmount =
    quantity.value * (props.administrationAdjustment?.rate ?? 0);

  const { exclGst, gst, total } = calculateGstInclusiveTotals(
    exclGstAmount,
    props.registeredForGst ?? false,
  );
  return {
    exclGst: `$${DisplayCurrencyValue(exclGst)}`,
    gst: `$${DisplayCurrencyValue(gst)}`,
    total: `$${DisplayCurrencyValue(total)}`,
  };
});

/**
 * Dispatches request to update the adjustment charge. Handles response then
 * closes the dialog.
 */
async function saveAdminAdjustment(): Promise<void> {
  isLoading.value = true;
  const request: AdminAdjustmentChargeEdit = {
    adjustmentChargeHistoryId:
      props.administrationAdjustment?.adjustmentChargeHistoryId ?? '',
    ledgerId: props.ledgerId,
    quantity: quantity.value,
  };
  const result =
    await fleetAssetOwnerInvoiceStore.editLedgerAdjustmentCharge(request);
  if (!result) {
    showNotification(GENERIC_ERROR_MESSAGE, {
      title: 'Adjustment save failed',
    });
  }
  closeDialog();
}

/**
 * Dispatches request to delete the adjustment charge from the ledger item.
 * Handles the response then closes the dialog.
 */
async function deleteAdminAdjustment(): Promise<void> {
  isLoadingDeletionOfAdjustment.value = true;
  const request: DeleteAdminAdjustmentCharge = {
    adjustmentChargeHistoryId:
      props.administrationAdjustment?.adjustmentChargeHistoryId ?? '',
    ledgerId: props.ledgerId,
  };
  const result =
    await fleetAssetOwnerInvoiceStore.removeAdjustmentChargeFromLedger(request);
  if (!result) {
    showNotification(GENERIC_ERROR_MESSAGE, {
      title: 'Adjustment save failed',
    });
  }
  closeDialog();
}
</script>

<style scoped lang="scss">
.remittance-dialog__section-header {
  color: rgb(74, 199, 233);
  text-transform: uppercase;
  font-size: $font-size-14;
  font-weight: 500;
}

.remittance-dialog__header {
  color: rgb(188, 188, 190);
  text-transform: uppercase;
  font-size: $font-size-12;
  font-weight: 600;
  padding-bottom: 4px;

  &.header-type {
    font-size: $font-size-13;
    padding-bottom: 8px;
    font-weight: 700;
    color: rgb(74, 199, 233);
  }

  &.subheader-type {
    font-size: $font-size-11;
    font-style: italic;
    font-weight: 500;
    color: rgb(136, 136, 145);
    text-transform: none;
  }
}

.remittance-dialog__value {
  color: rgb(228, 228, 240);
  font-size: $font-size-12;
  padding-bottom: 4px;

  &.highlighted {
    color: white;
    background-color: #00b1e2;
    padding: 4px 10px;
    font-size: $font-size-14;
    border-radius: 2px;
    font-weight: 700;
  }
}

.border-container-header {
  border-top: 1px solid var(--background-color-650);
  border-right: 1px solid var(--background-color-650);
  border-left: 1px solid var(--background-color-650);
}

.border-container-content {
  outline: 2px solid black;
  border-bottom: 1px solid var(--background-color-650);
  border-right: 1px solid var(--background-color-650);
  border-left: 1px solid var(--background-color-650);
}
</style>
