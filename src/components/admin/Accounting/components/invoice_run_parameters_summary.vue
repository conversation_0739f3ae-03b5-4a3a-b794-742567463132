<template>
  <v-layout>
    <v-layout row v-if="readableBillingCycle" class="pa-2">
      <h2>Billing Cycle:</h2>
      <h3 class="subheader--light pl-2">{{ readableBillingCycle }}</h3>
    </v-layout>
    <v-layout row v-if="readableWeekEndingDate" class="pa-2">
      <h2>Week Ending:</h2>
      <h3 class="subheader--light pl-2">{{ readableWeekEndingDate }}</h3>
    </v-layout>
    <div v-if="!weekEndingDateIsProcessingDate">
      <v-layout align-center>
        <v-layout row v-if="readableProcessingDate" class="pa-2">
          <h2>Processing Date:</h2>
          <h3 class="subheader--light pl-2">{{ readableProcessingDate }}</h3>
        </v-layout>
        <v-tooltip right v-if="showProcessingDateWarning" class="px-2">
          <template v-slot:activator="{ on }">
            <v-icon size="14" color="warning" v-on="on"
              >fad fa-exclamation-circle</v-icon
            >
          </template>
          <v-layout>
            <v-flex md12>
              The current processing date is before the week ending date.
            </v-flex>
          </v-layout>
        </v-tooltip>
      </v-layout>
    </div>
    <v-layout row v-if="readableIncludeAdjustments" class="pa-2">
      <h2>Include Adjustments:</h2>
      <h3 class="subheader--light pl-2">{{ readableIncludeAdjustments }}</h3>
    </v-layout>
  </v-layout>
</template>

<script setup lang="ts">
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { InvoiceRunParameters } from '@/interface-models/Accounting/InvoiceRunParameters';
import { billingCycles } from '@/interface-models/Generic/BillingCycles/BillingCycles';
import { ComputedRef, computed } from 'vue';

const props = defineProps<{
  parameters: InvoiceRunParameters;
}>();

// Returns the printable version of the processing date
const readableProcessingDate: ComputedRef<string> = computed(() => {
  if (props.parameters.processingDate === null) {
    return '';
  }
  return returnFormattedDate(props.parameters.processingDate);
});

// Returns the printable version of the week ending date
const readableWeekEndingDate: ComputedRef<string> = computed(() => {
  if (props.parameters.weekEndingDate === null) {
    return '';
  }
  return returnFormattedDate(props.parameters.weekEndingDate);
});

// Returns the readable billing cycle based on the billing cycle ids.
const readableBillingCycle: ComputedRef<string> = computed(() => {
  return (
    props.parameters.billingCycleIds
      ?.map((b) => billingCycles.find((x) => x.id === b)?.longName ?? '')
      .filter((s) => !!s)
      .join(', ') ?? ''
  );
});

// When the weekending date is not equal to the processing date we should
// display the processing date in the components action bar.
const weekEndingDateIsProcessingDate: ComputedRef<boolean> = computed(() => {
  if (
    props.parameters.weekEndingDate === null ||
    props.parameters.processingDate === null
  ) {
    return false;
  }
  return readableProcessingDate.value === readableWeekEndingDate.value;
});

/**
 * Used in the template to display a warning tooltip when the selected
 * processing date is before the week ending date.
 */
const showProcessingDateWarning: ComputedRef<boolean> = computed(() => {
  if (
    props.parameters.weekEndingDate === null ||
    props.parameters.processingDate === null
  ) {
    return false;
  }
  return props.parameters.weekEndingDate > props.parameters.processingDate;
});

/**
 * Returns the readable value if includeAdjustments
 */
const readableIncludeAdjustments: ComputedRef<string> = computed(() => {
  if (
    props.parameters.includeAdjustments === null ||
    props.parameters.includeAdjustments === undefined
  ) {
    return '';
  }
  return props.parameters.includeAdjustments ? 'Yes' : 'No';
});
</script>
