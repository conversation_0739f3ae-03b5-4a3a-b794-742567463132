<v-layout>
  <v-dialog
    v-model="dialogIsActive"
    width="600px"
    class="ma-0"
    persistent
    no-click-animation
    content-class="v-dialog-custom"
  >
    <template v-slot:activator="{ on }">
      <v-list-tile
        v-on="on"
        :disabled="!isAuthorised()"
        @click="() => null"
        style="width:100%"
      >
        <v-list-tile-action>
          <v-icon size="18" :disabled="!isAuthorised()"
            >far fa-file-invoice-dollar</v-icon
          >
        </v-list-tile-action>
        <v-list-tile-content>
          <v-list-tile-title>
            <span class="pr-2" style="text-transform: none; font-weight: 400;"
              >Custom Adjustments</span
            >
          </v-list-tile-title>
        </v-list-tile-content>
      </v-list-tile>
    </template>
    <div>
      <v-layout
        justify-space-between
        align-center
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>Contract Custom Adjustments</span>
        <div
          class="app-theme__center-content--closebutton"
          @click="closeDialog"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <v-layout row wrap class="app-theme__center-content--body dialog-content">
        <v-flex md12>
          <v-layout
            row
            wrap
            class="app-theme__center-content--body body-scrollable--65 "
          >
            <v-flex md12>
              <div class="header-container pa-3 mb-4">
                <v-layout justify-space-between>
                  <span class="remittance-dialog__header">Owner</span>
                  <span class="remittance-dialog__value">
                    {{ownerName}}
                  </span>
                </v-layout>
                <v-layout justify-space-between>
                  <span class="remittance-dialog__header">Truck</span>
                  <span class="remittance-dialog__value">
                    {{csrAssignedId}}
                  </span>
                </v-layout>
                <v-layout justify-space-between>
                  <span class="remittance-dialog__header">Contract Number</span>
                  <span class="remittance-dialog__value">
                    <span>{{ contractNumber }}</span>
                  </span>
                </v-layout>
              </div>

              <div class="pa-3">
                <v-layout
                  column
                  v-for="adjustmentType of adjustmentTypeLineItems"
                  :key="adjustmentType.name"
                  class="mb-2"
                >
                  <v-layout>
                    <span class="remittance-dialog__section-header"
                      >{{adjustmentType.name}}</span
                    >
                    <v-divider class="my-2 ml-3"></v-divider>

                    <v-icon
                      size="16"
                      color="warning"
                      class="pl-2 pr-1"
                      v-if="adjustmentType.balancesOutToBeZero"
                      >fad fa-exclamation-circle</v-icon
                    >
                  </v-layout>
                  <v-layout px-2>
                    <span class="remittance-dialog__header header-type"
                      >Charge</span
                    >
                    <v-spacer></v-spacer>
                    <v-flex md2 style="text-align: right;">
                      <span class="remittance-dialog__header header-type">
                        Qty
                      </span>
                    </v-flex>
                    <v-flex md2 style="text-align: right;">
                      <span class="remittance-dialog__header header-type">
                        Amt ($)
                      </span>
                    </v-flex>
                    <v-flex md2 style="text-align: right;">
                      <span class="remittance-dialog__header header-type">
                        GST ($)
                      </span>
                    </v-flex>
                  </v-layout>

                  <v-layout
                    v-for="adjustmentCharge in adjustmentType.adjustments"
                    :key="adjustmentCharge.adjustmentChargeHistoryId"
                    px-2
                  >
                    <v-flex>
                      <v-layout align-center>
                        <v-icon
                          size="12"
                          class="mr-2"
                          style="padding-bottom:2px;"
                          :disabled="!isAuthorised() || readOnly"
                          @click="editAdminAdjustmentCharge(adjustmentCharge.adjustmentId)"
                          >fa fa-edit</v-icon
                        >
                        <span class="remittance-dialog__header pb-0"
                          >Custom Adjustment
                        </span>
                      </v-layout>

                      <v-layout pl-4 column>
                        <span
                          class="remittance-dialog__header subheader-type"
                          style="max-width:250px"
                          >{{adjustmentCharge.description ?
                          adjustmentCharge.description : '-'}}</span
                        >
                      </v-layout>
                    </v-flex>
                    <v-spacer></v-spacer>
                    <v-flex md2>
                      <v-layout
                        justify-end
                        align-start
                        style="text-align: right;"
                      >
                        <span class="remittance-dialog__value">
                          {{adjustmentCharge.quantity }}
                        </span>
                      </v-layout>
                    </v-flex>

                    <v-flex md2 style="text-align: right;">
                      <span class="remittance-dialog__value">
                        {{
                        displayCurrencyValue(adjustmentCharge.total.exclGst)}}
                      </span>
                    </v-flex>
                    <v-flex md2 style="text-align: right;">
                      <span class="remittance-dialog__value">
                        {{ displayCurrencyValue(adjustmentCharge.total.gst)}}
                      </span>
                    </v-flex>
                  </v-layout>

                  <v-layout
                    v-if="adjustmentType.adjustments.length < 1"
                    justify-center
                  >
                    <span class="remittance-dialog__header subheader-type"
                      >No {{adjustmentType.name}} Exist.</span
                    >
                  </v-layout>

                  <v-layout>
                    <EquipmentHireAddCustomAdjustmentDialog
                      :readOnly="readOnly"
                      :fleetAssetId="fleetAssetId"
                      :affiliationType="affiliationType"
                      :registeredForGst="registeredForGst"
                      :ledgerId="ledgerId"
                      :contractId="contractId"
                    />
                  </v-layout>
                </v-layout>

                <v-divider class="my-2"></v-divider>
                <v-layout>
                  <span class="remittance-dialog__section-header"
                    >Charge List</span
                  >
                </v-layout>
                <v-layout px-2>
                  <span class="remittance-dialog__header header-type"
                    >Charge</span
                  >
                  <v-spacer></v-spacer>
                  <v-flex md2 style="text-align: right;">
                    <span class="remittance-dialog__header header-type">
                      Amt ($)
                    </span>
                  </v-flex>
                  <v-flex md2 style="text-align: right;">
                    <span class="remittance-dialog__header header-type">
                      GST ($)
                    </span>
                  </v-flex>
                </v-layout>
                <v-layout px-2>
                  <span class="remittance-dialog__header"
                    >Base Equipment Hire</span
                  >
                  <v-spacer></v-spacer>
                  <v-flex md2 style="text-align: right;">
                    <span class="remittance-dialog__value">
                      {{baseEquipmentHire.exclGst}}
                    </span>
                  </v-flex>
                  <v-flex md2 style="text-align: right;">
                    <span class="remittance-dialog__value">
                      {{baseEquipmentHire.gst}}
                    </span>
                  </v-flex>
                </v-layout>
                <v-layout px-2>
                  <span class="remittance-dialog__header">Excess Travel</span>
                  <v-spacer></v-spacer>
                  <v-flex md2 style="text-align: right;">
                    <span class="remittance-dialog__value">
                      {{excessTravel.exclGst}}
                    </span>
                  </v-flex>
                  <v-flex md2 style="text-align: right;">
                    <span class="remittance-dialog__value">
                      {{excessTravel.gst}}
                    </span>
                  </v-flex>
                </v-layout>
                <v-layout px-2>
                  <span class="remittance-dialog__header">Insurance</span>
                  <v-spacer></v-spacer>
                  <v-flex md2 style="text-align: right;">
                    <span class="remittance-dialog__value">
                      {{ insurance.exclGst }}
                    </span>
                  </v-flex>
                  <v-flex md2 style="text-align: right;">
                    <span class="remittance-dialog__value">
                      {{ insurance.gst }}
                    </span>
                  </v-flex>
                </v-layout>
                <v-layout px-2>
                  <span class="remittance-dialog__header"
                    >Administration Fee</span
                  >
                  <v-spacer></v-spacer>
                  <v-flex md2 style="text-align: right;">
                    <span class="remittance-dialog__value">
                      {{administrationFee.exclGst }}
                    </span>
                  </v-flex>
                  <v-flex md2 style="text-align: right;">
                    <span class="remittance-dialog__value">
                      {{administrationFee.gst }}
                    </span>
                  </v-flex>
                </v-layout>
                <v-layout px-2>
                  <span class="remittance-dialog__header"
                    >Custom Adjustments</span
                  >
                  <v-spacer></v-spacer>
                  <v-flex md2 style="text-align: right;">
                    <span class="remittance-dialog__value">
                      {{customAdjustmentCharge.exclGst }}
                    </span>
                  </v-flex>
                  <v-flex md2 style="text-align: right;">
                    <span class="remittance-dialog__value">
                      {{customAdjustmentCharge.gst }}
                    </span>
                  </v-flex>
                </v-layout>
                <v-layout justify-end>
                  <v-flex md4>
                    <v-divider class="my-2"></v-divider>
                  </v-flex>
                </v-layout>
                <v-layout px-2 py-1>
                  <v-spacer></v-spacer>
                  <span
                    class="remittance-dialog__header"
                    style="text-align: right;"
                    >Net ($):</span
                  >
                  <v-flex md2 style="text-align: right;">
                    <span class="remittance-dialog__value highlighted">
                      {{netTotal.total}}
                    </span>
                  </v-flex>
                </v-layout>
                <v-layout justify-end>
                  <v-flex md6>
                    <v-divider class="my-2"></v-divider>
                  </v-flex>
                </v-layout>
              </div>
            </v-flex>
          </v-layout>
        </v-flex>

        <v-flex md12>
          <v-divider></v-divider>
          <v-layout align-center>
            <v-btn color="error" outline @click="closeDialog">
              Cancel
            </v-btn>
            <v-spacer></v-spacer>
            <v-btn depressed color="blue" @click="closeDialog">
              Okay
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
  </v-dialog>

  <EquipmentHireEditCustomAdjustmentDialog
    v-if="customAdjustment"
    :customAdjustment="customAdjustment"
    @closeEditAdminAdjustment="closeEditAdminAdjustment"
    :registeredForGst="registeredForGst"
    :csrAssignedId="csrAssignedId"
    :ledgerId="ledgerId"
    :contractId="contractId"
    :fleetAssetId="fleetAssetId"
    :contractNumber="contractNumber"
  />
</v-layout>
