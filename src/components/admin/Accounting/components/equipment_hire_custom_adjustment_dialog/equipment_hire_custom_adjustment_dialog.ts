import EquipmentHireAddCustomAdjustmentDialog from '@/components/admin/Accounting/components/equipment_hire_add_custom_adjustment_dialog/index.vue';
import EquipmentHireEditCustomAdjustmentDialog from '@/components/admin/Accounting/components/equipment_hire_edit_custom_adjustment_dialog/index.vue';
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { ReportCurrencyDetails } from '@/interface-models/Accounting/CurrencyDetails';
import { InvoiceNavigation } from '@/interface-models/Accounting/InvoiceNavigation';
import { EquipmentHireCustomAdjustment } from '@/interface-models/Generic/Accounting/hireContractChargeHistory/EquipmentHireCustomAdjustment';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { Component, Prop, Vue } from 'vue-property-decorator';

import { useEquipmentHireInvoiceStore } from '@/store/modules/EquipmentHireInvoiceStore';
interface AdjustmentLineItem {
  name: string;
  adjustments: EquipmentHireCustomAdjustment[];
  balancesOutToBeZero: boolean;
}

@Component({
  components: {
    EquipmentHireAddCustomAdjustmentDialog,
    EquipmentHireEditCustomAdjustmentDialog,
  },
})
export default class EquipmentHireCustomAdjustmentDialog
  extends Vue
  implements IUserAuthority
{
  @Prop({ default: '-' }) public ownerName: string;
  @Prop({ default: '-' }) public csrAssignedId: string;
  @Prop() public fleetAssetId: string;
  @Prop() public contractNumber: string;
  @Prop({ default: false }) public readOnly: boolean;
  @Prop({ default: () => [] })
  public customAdjustments: EquipmentHireCustomAdjustment[];
  @Prop() public netTotal: ReportCurrencyDetails;
  @Prop() public baseEquipmentHire: ReportCurrencyDetails;
  @Prop() public excessTravel: ReportCurrencyDetails;
  @Prop() public insurance: ReportCurrencyDetails;
  @Prop() public administrationFee: ReportCurrencyDetails;
  @Prop() public customAdjustmentCharge: ReportCurrencyDetails;
  @Prop({ default: true }) public registeredForGst: boolean;
  @Prop() public affiliationType: string;
  @Prop() public ledgerId: string;
  @Prop() public contractId: string;
  @Prop() public invoiceType: InvoiceNavigation;
  public dialogIsActive: boolean = false;
  public customAdjustment: EquipmentHireCustomAdjustment | null = null;
  public displayCurrencyValue = DisplayCurrencyValue;

  public isAuthorised(): boolean {
    return hasAdminOrHeadOfficeRole();
  }

  // find and set the admin adjustment charge that the user selected to edit
  public editAdminAdjustmentCharge(adjustmentChargeId: string) {
    const adjustmentCharge: EquipmentHireCustomAdjustment | undefined =
      this.customAdjustments.find(
        (x: EquipmentHireCustomAdjustment) =>
          x.adjustmentId === adjustmentChargeId,
      );
    if (!adjustmentCharge) {
      return;
    }
    this.customAdjustment = adjustmentCharge;
  }

  // close edit adjustment charge by settings administrationAdjustment to null
  public closeEditAdminAdjustment() {
    this.customAdjustment = null;
  }

  public closeDialog(): void {
    // manually close the menu
    this.$emit('closeMenuOptions');
    // set the adjustment charge to initial null state
    this.customAdjustment = null;
    // close dialog
    this.dialogIsActive = false;
  }

  get adjustmentTypeLineItems(): AdjustmentLineItem[] {
    return [
      {
        name: 'Custom Adjustments',
        adjustments: this.customAdjustments,
        balancesOutToBeZero: false,
      },
    ];
  }

  get includeAdjustments(): boolean | null {
    return useEquipmentHireInvoiceStore().includeAdjustments;
  }
}
