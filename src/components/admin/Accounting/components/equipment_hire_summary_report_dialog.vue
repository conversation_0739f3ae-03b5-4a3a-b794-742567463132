<template>
  <v-layout>
    <v-dialog
      v-model="dialogIsActive"
      width="60%"
      class="ma-0"
      persistent
      no-click-animation
      content-class="v-dialog-custom"
    >
      <template v-slot:activator="{ on }">
        <v-list-tile
          v-on="on"
          @click="() => null"
          style="width: 100%"
          :disabled="isDisabled"
        >
          <v-list-tile-action>
            <v-icon size="18" :disabled="isDisabled"
              >far fa-file-chart-line</v-icon
            >
          </v-list-tile-action>
          <v-list-tile-content>
            <v-list-tile-title>
              <span style="text-transform: none; font-weight: 400"
                >View Summary Report</span
              >
            </v-list-tile-title>
          </v-list-tile-content>
        </v-list-tile>
      </template>
      <div>
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight"
        >
          <div>
            <span class="mr-2">RCTI Summary Report</span>
          </div>
          <div
            class="app-theme__center-content--closebutton"
            @click="closeDialog"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>
        <v-layout
          row
          wrap
          class="app-theme__center-content--body dialog-content pa-3"
        >
          <v-flex md12>
            <v-layout wrap class="accounting-summary-report">
              <v-flex md12 class="summary-report__page-header">
                <v-layout align-start>
                  <span class="summary-report__page-header--title">
                    {{ companyName }}
                  </span>
                  <v-spacer></v-spacer>
                  <span class="summary-report__page-header--reportname">
                    RCTI Summary
                  </span>
                </v-layout>
                <v-layout pt-2>
                  <span class="summary-report__page-header--label">
                    Run Date:
                  </span>
                  <span class="summary-report__page-header--value">
                    {{ runDate }}
                  </span>
                  <v-spacer></v-spacer>
                </v-layout>
                <v-layout pt-2>
                  <span class="summary-report__page-header--label">
                    Number of Contracts:
                  </span>
                  <span class="summary-report__page-header--value">
                    {{ numberOfContracts }}
                  </span>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-divider class="my-2"></v-divider>
              </v-flex>
              <v-flex md12 pa-2 pb-3>
                <v-layout row wrap>
                  <v-flex
                    md12
                    class="summary-report__column pa-3 app-bgcolor--400 app-bordercolor--600 app-borderside--a"
                  >
                    <v-layout justify-center>
                      <v-flex md12>
                        <v-layout justify-space-between align-center px-2>
                          <h6 class="subheader--faded">
                            Subcontractor Charge Summary
                          </h6>
                        </v-layout>
                        <v-layout px-2>
                          <v-divider class="mb-3"></v-divider>
                        </v-layout>
                        <v-layout>
                          <v-flex md12 class="report-line__headers">
                            <v-layout align-center>
                              <v-flex
                                ><span class="pl-1 report-line__columnheader"
                                  >Description</span
                                ></v-flex
                              >
                              <v-spacer></v-spacer>
                              <v-flex md1
                                ><v-layout justify-end>
                                  <span class="report-line__columnheader"
                                    >Value ($)</span
                                  >
                                </v-layout></v-flex
                              >
                              <v-flex md1
                                ><v-layout justify-end>
                                  <span class="report-line__columnheader"
                                    >GST ($)</span
                                  >
                                </v-layout></v-flex
                              >
                              <v-flex md1
                                ><v-layout justify-end>
                                  <span class="report-line__columnheader"
                                    >Total ($)</span
                                  >
                                </v-layout></v-flex
                              >
                            </v-layout>
                          </v-flex>
                        </v-layout>
                        <v-layout
                          class="report-line__row"
                          align-center
                          wrap
                          v-for="(summary, chargeIndex) of summaryReport"
                          :key="chargeIndex"
                        >
                          <v-flex>
                            <span class="report-line__text"
                              >{{ summary.name }}
                            </span>
                          </v-flex>
                          <v-spacer></v-spacer>

                          <v-flex
                            md3
                            :class="
                              chargeIndex === summaryReport.length - 1
                                ? 'app-borderside--t app-bordercolor--600 pt-1'
                                : ''
                            "
                          >
                            <v-layout>
                              <v-flex md4>
                                <v-layout justify-end align-center>
                                  <span class="report-line__value">
                                    {{ summary.charge.exclGst }}
                                  </span>
                                </v-layout>
                              </v-flex>
                              <v-flex md4>
                                <v-layout justify-end align-center>
                                  <span class="report-line__value">
                                    {{ summary.charge.gst }}
                                  </span>
                                </v-layout>
                              </v-flex>
                              <v-flex md4>
                                <v-layout justify-end align-center>
                                  <span
                                    class="report-line__value"
                                    :class="
                                      chargeIndex === summaryReport.length - 1
                                        ? 'final-total'
                                        : ''
                                    "
                                  >
                                    {{ summary.charge.total }}
                                  </span>
                                </v-layout>
                              </v-flex>
                            </v-layout>
                          </v-flex>

                          <v-flex
                            md12
                            v-if="summary.name === 'Job Adjustments'"
                          >
                            <v-divider class="my-1"></v-divider>
                          </v-flex>
                        </v-layout>
                      </v-flex>
                    </v-layout>
                  </v-flex>
                  <v-flex
                    md12
                    class="summary-report__column mt-2 pa-3 app-bgcolor--400 app-bordercolor--600 app-borderside--a"
                  >
                    <v-layout justify-space-between align-center px-2>
                      <h6 class="subheader--faded">
                        Breakdown by Vehicle Class
                      </h6>
                    </v-layout>
                    <v-layout px-2>
                      <v-divider class="mb-3"></v-divider>
                    </v-layout>
                    <v-layout row wrap>
                      <v-flex
                        md6
                        px-1
                        class="report-line__headers"
                        v-for="(rateCodeBreakDownHeader, headerIndex) of 2"
                        :key="rateCodeBreakDownHeader + '' + headerIndex"
                      >
                        <v-layout px-2 align-center>
                          <v-flex md1
                            ><span class="report-line__columnheader"
                              >Rate</span
                            ></v-flex
                          >
                          <v-flex
                            ><span class="pl-1 report-line__columnheader"
                              >Description</span
                            ></v-flex
                          >
                          <v-spacer></v-spacer>
                          <v-flex md2
                            ><v-layout justify-end>
                              <span class="report-line__columnheader"
                                >Amount ($)</span
                              >
                            </v-layout></v-flex
                          >
                        </v-layout>
                      </v-flex>
                      <v-flex
                        md6
                        v-for="(
                          serviceBreakdown, serviceBreakdownIndex
                        ) in breakDownByTrailerType"
                        :key="serviceBreakdown.service"
                        class="report-line__row px-1"
                        :class="
                          serviceBreakdownIndex % 2 ? '' : 'column-border-right'
                        "
                      >
                        <v-layout px-2>
                          <v-flex md1
                            ><span class="report-line__text">{{
                              serviceBreakdown.service
                            }}</span></v-flex
                          >
                          <span class="pl-1 report-line__text">{{
                            serviceBreakdown.description
                          }}</span>
                          <v-spacer></v-spacer>
                          <v-flex md2
                            ><v-layout justify-end>
                              <span class="report-line__value">{{
                                serviceBreakdown.total
                              }}</span>
                            </v-layout></v-flex
                          >
                        </v-layout>
                      </v-flex>
                    </v-layout>
                  </v-flex>
                </v-layout>
              </v-flex>
            </v-layout>
          </v-flex>
        </v-layout>
      </div>
    </v-dialog>
  </v-layout>
</template>

<script setup lang="ts">
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  CurrencyDetails,
  ReportCurrencyDetails,
} from '@/interface-models/Accounting/CurrencyDetails';
import { EquipmentHireOwnerInvoiceRow } from '@/interface-models/Accounting/EquipmentHireAccountingTable';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import {
  TrailerType,
  trailerTypes,
} from '@/interface-models/Generic/TrailerTypes/TrailerTypes';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useEquipmentHireInvoiceStore } from '@/store/modules/EquipmentHireInvoiceStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { computed, ComputedRef, ref, Ref } from 'vue';

interface InvoiceSummaryReport {
  name: string;
  charge: ReportCurrencyDetails;
}

interface TrailerTypeBreakdown {
  service: string;
  description: string;
  total: string;
}

const emit = defineEmits<{
  (event: 'update:isDialogOpen', payload: boolean): void;
  (event: 'closeMenuOptions'): void;
}>();

const props = withDefaults(
  defineProps<{
    invoices: EquipmentHireOwnerInvoiceRow[];
    isDisabled?: boolean;
    weekEndingDate: number;
    title?: string;
    subtitle?: string;
  }>(),
  {
    isDisabled: false,
    title: '',
    subtitle: '',
  },
);

const fleetAssetStore = useFleetAssetStore();
const dialogIsActive: Ref<boolean> = ref(false);

/**
 * Returns the company name in uppercase, or '-' if not available.
 */
const companyName: ComputedRef<string> = computed(() => {
  const companyDetailsStore = useCompanyDetailsStore();
  return companyDetailsStore.companyDetails?.name?.toUpperCase() ?? '-';
});

/**
 * Closes the dialog and emits the close event.
 */
function closeDialog(): void {
  emit('closeMenuOptions');
  dialogIsActive.value = false;
}

/**
 * Returns the formatted run date for the report.
 */
const runDate: ComputedRef<string> = computed(() => {
  const equipmentHireInvoiceStore = useEquipmentHireInvoiceStore();
  const runDate =
    props.weekEndingDate ??
    (equipmentHireInvoiceStore.weekEndingDate
      ? equipmentHireInvoiceStore.processingDate
      : equipmentHireInvoiceStore.weekEndingDate);

  return runDate
    ? returnFormattedDate(props.weekEndingDate || 0, 'DD/MM/YY')
    : '-';
});

/**
 * Returns the total number of contracts in all invoices.
 */
const numberOfContracts: ComputedRef<number> = computed(() => {
  let numberOfContracts = 0;
  for (const invoice of props.invoices) {
    numberOfContracts += invoice.equipmentContracts.length;
  }
  return numberOfContracts;
});

/**
 * Returns the summary report for the invoice, including all charge types and subtotal.
 */
const summaryReport: ComputedRef<InvoiceSummaryReport[]> = computed(() => {
  const summaryReport: InvoiceSummaryReport[] = [];

  const equipmentHire: CurrencyDetails = { exclGst: 0, gst: 0, total: 0 };
  const excessTravel: CurrencyDetails = { exclGst: 0, gst: 0, total: 0 };
  const insurance: CurrencyDetails = { exclGst: 0, gst: 0, total: 0 };
  const administrationFee: CurrencyDetails = { exclGst: 0, gst: 0, total: 0 };
  const customAdjustment: CurrencyDetails = { exclGst: 0, gst: 0, total: 0 };

  for (const invoice of props.invoices) {
    for (const contract of invoice.equipmentContracts) {
      equipmentHire.exclGst += parseFloat(
        contract.baseEquipmentHireAmount.exclGst.replaceAll(',', ''),
      );
      equipmentHire.gst += parseFloat(
        contract.baseEquipmentHireAmount.gst.replaceAll(',', ''),
      );
      equipmentHire.total += parseFloat(
        contract.baseEquipmentHireAmount.total.replaceAll(',', ''),
      );

      excessTravel.exclGst += parseFloat(
        contract.excessTravelAmount.exclGst.replaceAll(',', ''),
      );
      excessTravel.gst += parseFloat(
        contract.excessTravelAmount.gst.replaceAll(',', ''),
      );
      excessTravel.total += parseFloat(
        contract.excessTravelAmount.total.replaceAll(',', ''),
      );

      insurance.exclGst += parseFloat(
        contract.insuranceAmount.exclGst.replaceAll(',', ''),
      );
      insurance.gst += parseFloat(
        contract.insuranceAmount.gst.replaceAll(',', ''),
      );
      insurance.total += parseFloat(
        contract.insuranceAmount.total.replaceAll(',', ''),
      );

      administrationFee.exclGst += parseFloat(
        contract.administrationFeeAmount.exclGst.replaceAll(',', ''),
      );
      administrationFee.gst += parseFloat(
        contract.administrationFeeAmount.gst.replaceAll(',', ''),
      );
      administrationFee.total += parseFloat(
        contract.administrationFeeAmount.total.replaceAll(',', ''),
      );

      customAdjustment.exclGst +=
        contract.customAdjustmentAmountAsNumber.exclGst;
      customAdjustment.gst += contract.customAdjustmentAmountAsNumber.gst;
      customAdjustment.total += contract.customAdjustmentAmountAsNumber.total;
    }
  }

  const equipmentHireRow: InvoiceSummaryReport = {
    name: 'Base Equipment Hire',
    charge: {
      exclGst: DisplayCurrencyValue(equipmentHire.exclGst),
      gst: DisplayCurrencyValue(equipmentHire.gst),
      total: DisplayCurrencyValue(equipmentHire.total),
    },
  };

  const excessTravelRow: InvoiceSummaryReport = {
    name: 'Excess Travel',
    charge: {
      exclGst: DisplayCurrencyValue(excessTravel.exclGst),
      gst: DisplayCurrencyValue(excessTravel.gst),
      total: DisplayCurrencyValue(excessTravel.total),
    },
  };

  const insuranceRow: InvoiceSummaryReport = {
    name: 'Insurance',
    charge: {
      exclGst: DisplayCurrencyValue(insurance.exclGst),
      gst: DisplayCurrencyValue(insurance.gst),
      total: DisplayCurrencyValue(insurance.total),
    },
  };

  const administrationFeeRow: InvoiceSummaryReport = {
    name: 'Administration Fee',
    charge: {
      exclGst: DisplayCurrencyValue(administrationFee.exclGst),
      gst: DisplayCurrencyValue(administrationFee.gst),
      total: DisplayCurrencyValue(administrationFee.total),
    },
  };

  const customAdjustmentsAreNegative = customAdjustment.total < 0;
  const customAdjustmentRow: InvoiceSummaryReport = {
    name: 'Custom Adjustments',
    charge: {
      exclGst:
        (customAdjustmentsAreNegative ? '(' : '') +
        DisplayCurrencyValue(Math.abs(customAdjustment.exclGst)) +
        (customAdjustmentsAreNegative ? ')' : ''),
      gst:
        (customAdjustmentsAreNegative ? '(' : '') +
        DisplayCurrencyValue(Math.abs(customAdjustment.gst)) +
        (customAdjustmentsAreNegative ? ')' : ''),
      total:
        (customAdjustmentsAreNegative ? '(' : '') +
        DisplayCurrencyValue(Math.abs(customAdjustment.total)) +
        (customAdjustmentsAreNegative ? ')' : ''),
    },
  };

  const subTotalRow: InvoiceSummaryReport = {
    name: 'Subtotal',
    charge: {
      exclGst: DisplayCurrencyValue(
        equipmentHire.exclGst +
          excessTravel.exclGst +
          insurance.exclGst +
          administrationFee.exclGst +
          customAdjustment.exclGst,
      ),
      gst: DisplayCurrencyValue(
        equipmentHire.gst +
          excessTravel.gst +
          insurance.gst +
          administrationFee.gst +
          customAdjustment.gst,
      ),
      total: DisplayCurrencyValue(
        equipmentHire.total +
          excessTravel.total +
          insurance.total +
          administrationFee.total +
          customAdjustment.total,
      ),
    },
  };

  summaryReport.push(equipmentHireRow);
  summaryReport.push(excessTravelRow);
  summaryReport.push(insuranceRow);
  summaryReport.push(administrationFeeRow);
  summaryReport.push(customAdjustmentRow);
  summaryReport.push(subTotalRow);
  return summaryReport;
});

/**
 * Returns the total amount charged to each trailer type.
 */
const breakDownByTrailerType: ComputedRef<TrailerTypeBreakdown[]> = computed(
  () => {
    const rateCodeBreakdowns: TrailerTypeBreakdown[] = [];

    for (const invoice of props.invoices) {
      for (const contract of invoice.equipmentContracts) {
        const fleetAssetId = contract.fleetAssetId;
        const fleetAsset: FleetAssetSummary | undefined =
          fleetAssetStore.getFleetAssetFromFleetAssetId(fleetAssetId);
        if (!fleetAsset) {
          continue;
        }
        const trailerTypeId: number | undefined | null = fleetAsset.trailerType;
        const trailerType: TrailerType | undefined = trailerTypes.find(
          (x: TrailerType) => x.id === trailerTypeId,
        );

        const trailerTypeService = trailerType
          ? trailerType.shortName
          : 'Other';
        const trailerTypeDescription = trailerType
          ? trailerType.longName
          : 'Other';

        const existingTrailerTypeBreakdown = rateCodeBreakdowns.find(
          (rcb: TrailerTypeBreakdown) => rcb.service === trailerTypeService,
        );

        if (existingTrailerTypeBreakdown) {
          existingTrailerTypeBreakdown.total = DisplayCurrencyValue(
            parseFloat(existingTrailerTypeBreakdown.total.replaceAll(',', '')) +
              parseFloat(contract.total.total.replaceAll(',', '')),
          );
        } else {
          const truckClassBreakdown: TrailerTypeBreakdown = {
            service: trailerTypeService,
            description: trailerTypeDescription,
            total: contract.total.total,
          };
          rateCodeBreakdowns.push(truckClassBreakdown);
        }
      }
    }

    return rateCodeBreakdowns;
  },
);
</script>
<style scoped lang="scss">
.accounting-summary-report {
  width: 100%;

  $table-font-size: $font-size-12;

  .summary-report__page-header {
    padding: 0px 16px;
    .summary-report__page-header--title {
      font-size: $font-size-16;
      font-weight: 600;
      color: rgb(255, 255, 255);

      &.inactive-type {
        color: rgb(113, 114, 120);
      }
    }

    .summary-report__page-header--reportname {
      font-size: $font-size-16;
      font-weight: 600;
      // color: rgb(110, 228, 255);
      color: white;
      &:hover {
        cursor: pointer;
      }
    }

    .summary-report__page-header--reportlink {
      font-size: $font-size-13;
      font-weight: 500;
      // color: rgb(110, 228, 255);
      color: rgb(129, 207, 255);
      &:hover {
        cursor: pointer;
      }
    }
    .summary-report__page-header--label {
      font-size: $table-font-size;
      font-weight: 500;
      padding-right: 10px;
    }
    .summary-report__page-header--value {
      font-size: $table-font-size;
      color: rgb(207, 207, 211);
    }
    .summary-report__page-header--dummyindicator {
      font-size: $font-size-13;
      letter-spacing: 1px;
      font-weight: 700;
      text-transform: uppercase;
      background-color: rgb(228, 174, 27);
      padding: 2px 6px;
      border-radius: 2px;
    }
  }

  .summary-report__column {
    .summary-report__column--title {
      font-size: $font-size-14;
      padding-bottom: 6px;
      text-transform: uppercase;
      color: white;
      font-weight: 600;
    }

    .summary-report__column--subtitle {
      font-size: $font-size-13;
      padding-bottom: 6px;
      // text-transform: uppercase;
      color: rgb(208, 211, 231);
      font-weight: 500;
    }
  }

  .report-line__row {
    font-size: $table-font-size;
    // padding-bottom: 6px;
    padding: 1px 4px;
    color: white;

    .report-line__text {
      font-weight: 500;
      color: rgb(221, 221, 226);
    }

    .report-line__value {
      padding: 2px 0px;
      font-weight: 400;
      color: rgb(224, 226, 243);
      text-align: right;

      &.final-total {
        background-color: rgb(53, 172, 247);
        padding: 2px 6px;
        border-radius: 2px;
        color: white;
      }
    }

    &.column-border-right {
      border-right: 1px solid rgb(91, 89, 99);
    }
  }

  .report-line__headers {
    font-size: $table-font-size;
    padding-bottom: 8px;
    .report-line__columnheader {
      font-weight: 600;
      text-transform: uppercase;
    }
  }
}
</style>
