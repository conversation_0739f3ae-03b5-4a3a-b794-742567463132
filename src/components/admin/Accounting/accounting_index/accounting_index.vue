<template>
  <v-layout class="accounting-index-page">
    <v-flex md1 class="left-panel-navigation">
      <v-flex
        md12
        v-for="(item, index) in navigationOptions"
        :key="item.id"
        class="accounting__tab-button"
        @click="setCurrentView(item.id)"
        :class="[
          { selected: currentView === item.id },
          { 'selection-disabled': item.isDisabled },
          { 'include-divider': index === 2 },
        ]"
      >
        <v-layout
          column
          justify-center
          align-center
          class="accounting__tab-button--contents"
        >
          <v-icon class="tab-icon"> {{ item.icon }} </v-icon>
          <span class="tab-label"> {{ item.text }} </span>

          <span
            class="side-column__summaryitem--value status-container mt-1"
            v-if="item.text !== 'Ledger' && item.text !== 'Jobs'"
          >
            {{ item.status ? item.status : 'READY' }}
          </span>
        </v-layout>
      </v-flex>
    </v-flex>
    <v-flex md11>
      <v-layout>
        <AccountingJobsIndex
          v-if="currentView === AccountingNavigationType.JOB_LIST"
          :accountingIndexLoading="isLoading"
        ></AccountingJobsIndex>
        <ClientInvoiceIndex
          v-if="currentView === AccountingNavigationType.CLIENT_INVOICE"
          :accountingIndexLoading="isLoading"
        />
        <RctiIndex
          v-if="currentView === AccountingNavigationType.RCTI"
          :accountingIndexLoading="isLoading"
        />
        <EquipmentHireIndex
          v-if="currentView === AccountingNavigationType.EQUIPMENT_HIRE"
          :accountingIndexLoading="isLoading"
        />
        <LedgerIndex v-if="currentView === AccountingNavigationType.LEDGER" />
      </v-layout>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import AccountingJobsIndex from '@/components/admin/Accounting/accounting_jobs/accounting_jobs_index.vue';
import ClientInvoiceIndex from '@/components/admin/Accounting/client_invoice/client_invoice_index/client_invoice_index.vue';
import EquipmentHireIndex from '@/components/admin/Accounting/equipment_hire/equipment_hire_index/equipment_hire_index.vue';
import LedgerIndex from '@/components/admin/Accounting/ledger/ledger_index/index.vue';
import RctiIndex from '@/components/admin/Accounting/rcti/rcti_index/rcti_index.vue';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { AccountingNavigationType } from '@/interface-models/Accounting/AccountingNavigationType';
import { InvoiceStatus } from '@/interface-models/Accounting/InvoiceStatus';
import { useClientInvoiceStore } from '@/store/modules/ClientInvoiceStore';
import { useEquipmentHireInvoiceStore } from '@/store/modules/EquipmentHireInvoiceStore';
import { useFleetAssetOwnerInvoiceStore } from '@/store/modules/FleetAssetOwnerInvoiceStore';
import { ComputedRef, Ref, computed, onBeforeMount, ref } from 'vue';

interface NavigationOption {
  id: AccountingNavigationType;
  icon: string;
  text: string;
  status: string;
  isDisabled: boolean;
}

const currentView: Ref<AccountingNavigationType> = ref(
  AccountingNavigationType.CLIENT_INVOICE,
);
const isLoading: Ref<boolean> = ref(false);

const navigationOptions: ComputedRef<NavigationOption[]> = computed(() => {
  return [
    {
      id: AccountingNavigationType.CLIENT_INVOICE,
      icon: 'fas fa-user',
      text: 'Client',
      status: clientInvoiceRunStatus.value
        ? clientInvoiceRunStatus.value.toString()
        : 'READY',
      isDisabled: false,
    },
    {
      id: AccountingNavigationType.RCTI,
      icon: 'fas fa-steering-wheel',
      text: 'Fleet Asset',
      status: rctiRunStatus.value ? rctiRunStatus.value.toString() : 'READY',
      isDisabled: false,
    },
    {
      id: AccountingNavigationType.EQUIPMENT_HIRE,
      icon: 'fa fa-trailer',
      text: 'Equipment Hire',
      status: equipmentHireRunStatus.value
        ? equipmentHireRunStatus.value.toString()
        : 'READY',
      isDisabled: false,
    },
    {
      id: AccountingNavigationType.JOB_LIST,
      icon: 'fas fa-list',
      text: 'Jobs',
      status: '',
      isDisabled: false,
    },
    {
      id: AccountingNavigationType.LEDGER,
      icon: 'fas fa-balance-scale',
      text: 'Ledger',
      status: '',
      isDisabled: !hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole(),
    },
  ];
});

// sets the main view in the accounting page. Actioned from the left panel navigation.
function setCurrentView(type: AccountingNavigationType) {
  currentView.value = type;
}

// returns the current stage our client invoice run is at.
const clientInvoiceRunStatus: ComputedRef<InvoiceStatus | null> = computed(
  () => {
    return useClientInvoiceStore().invoiceRunStatus;
  },
);

// returns the current stage our rcti run is at.
const rctiRunStatus: ComputedRef<InvoiceStatus | null> = computed(() => {
  return useFleetAssetOwnerInvoiceStore().invoiceRunStatus;
});

const equipmentHireRunStatus: ComputedRef<InvoiceStatus | null> = computed(
  () => {
    return useEquipmentHireInvoiceStore().invoiceRunStatus;
  },
);

/**
 * Fetches the current invoice run status for the client invoice run. Called
 * before component mount.
 */
async function getCurrentInvoiceRunStatus(): Promise<void> {
  isLoading.value = true;
  const response = await useClientInvoiceStore().getCurrentInvoiceRunStatus();
  if (!response) {
    showNotification(GENERIC_ERROR_MESSAGE);
  }
  isLoading.value = false;
}

onBeforeMount(() => {
  getCurrentInvoiceRunStatus();
});
</script>
<style scoped lang="scss">
.accounting-index-page {
  background-color: var(--background-color-250);
  padding: 0px;

  .left-panel-navigation {
    left: 0;
    bottom: 0px;
    height: calc(100vh - 40px);

    z-index: 2;
    background-color: var(--background-color-300);
    border-right: 4px solid var(--background-color-300);
    padding-top: 4px;

    .accounting__tab-button {
      transition: 0.2s ease-in;
      position: relative;
      width: 100%;
      height: 160px;
      border: 1px solid $translucent;
      padding: 4px;
      border-radius: 2px;
      background-color: var(--background-color-400);

      .side-column__summaryitem--value {
        font-size: $font-size-13;
        font-weight: 500;
        padding-right: 3px;
        color: var(--light-text-color);
        &.status-container {
          padding: 1px 8px;
          font-weight: 600;
          background-color: $enabled-color;
          border-radius: $border-radius-Xlg;
          letter-spacing: 0.05em;
        }
      }

      &.selected {
        background-color: var(--background-color-200);
        .accounting__tab-button--contents {
          .tab-icon {
            font-weight: 600;
            color: var(--primary-light);
          }

          .tab-label {
            color: var(--text-color);
          }

          .side-column__summaryitem--value {
            color: var(--primary-dark);

            &.status-container {
              background-color: var(--primary-light);
            }
          }
        }

        &:hover {
          .accounting__tab-button--contents {
            .tab-icon {
              color: var(--primary);
            }

            .tab-label {
              color: #e2e2e2;
            }
          }
        }
      }

      &:hover {
        background-color: var(--background-color-600);
        cursor: pointer;

        .accounting__tab-button--contents {
          .tab-icon {
            color: var(--light-text-color);
          }

          .tab-label {
            color: var(--light-text-color);
          }
        }
      }

      .accounting__tab-button--contents {
        height: 100%;
        width: 100%;

        .tab-icon {
          font-size: 28px;
          font-weight: 200;
          color: #505050;
        }

        .tab-label {
          font-size: $font-size-large;
          text-transform: uppercase;
          font-weight: 500;
          padding-top: 10px;
          color: #505050;
        }
      }

      &.include-divider {
        position: relative;
        margin-bottom: 30px;
        &::after {
          content: '';
          position: absolute;
          bottom: -16px;
          left: 50%;
          width: 40px;
          height: 3px;
          border-radius: 3px;
          background-color: var(--highlight);
          transform: translateX(-50%);
        }
      }
    }
  }
}

.selection-disabled {
  pointer-events: none;
}
</style>
