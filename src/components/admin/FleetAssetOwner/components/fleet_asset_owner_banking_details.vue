<template>
  <div class="fleet-asset-owner-banking-details-container">
    <v-layout wrap>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">2. Payment Details</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">BSB Number:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              :disabled="!isEdited"
              solo
              flat
              class="v-solo-custom"
              v-model="bankingDetails.bsbNumber"
              label="BSB Number"
              persistent-hint
              :hint="'Financial Institution details will autofill if BSB number is found.'"
              v-mask="'###-###'"
            >
            </v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Account Number:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              :disabled="!isEdited"
              solo
              flat
              class="v-solo-custom"
              name="accountNumber"
              v-model.number="bankingDetails.accountNumber"
              :rules="[validationRules.numbers]"
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Account Name:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              :disabled="!isEdited"
              solo
              flat
              class="v-solo-custom"
              v-model="bankingDetails.accountName"
              label="Account Name"
            >
            </v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Financial Institution:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              solo
              class="v-solo-custom"
              flat
              :disabled="true"
              v-model="bankingDetails.financialInstitutionName"
              label="Bank"
            >
            </v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Address:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              solo
              flat
              class="v-solo-custom"
              :disabled="true"
              v-model="bankingDetails.streetAddress"
              label="Address"
            >
            </v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Suburb:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              solo
              flat
              class="v-solo-custom"
              :disabled="true"
              v-model="bankingDetails.suburb"
              label="Suburb"
            >
            </v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Postcode:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              solo
              class="v-solo-custom"
              flat
              :disabled="true"
              v-model="bankingDetails.postcode"
              label="Postcode"
            >
            </v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import BankingDetailsAU from '@/interface-models/Generic/BankingDetails/BankingDetailsAU';
import bsbJsonData from '@/static/staticData/bsb_list.json';
import { watch } from 'vue';

const props = withDefaults(
  defineProps<{
    bankingDetails: BankingDetailsAU;
    isEdited?: boolean;
  }>(),
  {
    isEdited: false,
  },
);

// We need to watch the BSB Number changing and perform a search based on the input from this field.
const bsbEntered = (value: string) => {
  if (value.length === 7) {
    searchForBSB(value);
  } else {
    resetBankDetails();
  }
};
watch(() => props.bankingDetails.bsbNumber, bsbEntered);

// Perform search against JSON Data structure provided in file bsb_list.json
// The process to generate this is to download the CSV from
// http://bsb.apca.com.au/Public/CS4BSBDir.nsf/0/FF6C089701090485CA2583ED000D157E/$File/BSB%20Directory%20Update%2002Apr19-01May19.csv
// Add the following header to the top of the CSV File
// "BSBNumber", "finacialInstitutionName", "instiutionFullName", "streetAddress", "suburb", "state", "postcode", "paymentSystem"
// Generate online a JSON object from the resulting CSV
const searchForBSB = (bsbNumberToSearch: string) => {
  const branch = bsbJsonData.find(
    (elem) => elem.BSBNumber === bsbNumberToSearch,
  );
  // We have found a matching Bank so we would like to update the details.
  if (branch) {
    props.bankingDetails.financialInstitutionName =
      branch.finacialInstitutionName;
    props.bankingDetails.institutionFullName = branch.institutionFullName;
    props.bankingDetails.streetAddress = branch.streetAddress;
    props.bankingDetails.suburb = branch.suburb;
    props.bankingDetails.postcode = parseInt(branch.postcode as string);
  } else {
    resetBankDetails();
  }
};

// Reset bank details
const resetBankDetails = () => {
  props.bankingDetails.financialInstitutionName = '';
  props.bankingDetails.institutionFullName = '';
  props.bankingDetails.streetAddress = '';
  props.bankingDetails.suburb = '';
  props.bankingDetails.postcode = undefined;
};
</script>

<style scoped>
.fleet-asset-owner-banking-details-container {
  display: flex;
  justify-content: center;
  position: relative;
}

.address-label {
  width: 100%;
  height: 48px;
}

.address-label-container {
  padding-top: 28px;
}

.label-container {
  height: 48px;
}
</style>
