<template>
  <div class="associated-devices-container">
    <div class="add-device-btn">
      <v-btn
        :disabled="formDisabled"
        @click="newDevice"
        color="blue"
        small
        depressed
        v-if="!isEdit && !isNew"
        ><v-icon size="18">add</v-icon> New Device</v-btn
      >
    </div>

    <v-layout>
      <v-flex md12>
        <v-card flat color="#2e2e2e">
          <v-data-table
            class="gd-dark-theme"
            :headers="headers"
            :items="devices"
            hide-actions
            item-key="_id"
          >
            <template v-slot:items="props">
              <tr>
                <td>{{ props.item.deviceId }}</td>
                <td>{{ deviceTypeName(props.item.deviceTypeId) }}</td>
                <td>
                  {{ associationTypeName(props.item.associationTypeId) }}
                </td>
                <td>
                  {{
                    entityName(
                      props.item.associationTypeId,
                      props.item.associationId,
                    )
                  }}
                </td>
                <td>
                  {{
                    props.item.createdEpoch
                      ? returnFormattedDate(props.item.createdEpoch)
                      : '-'
                  }}
                </td>
                <td>
                  {{
                    props.item.retiredEpoch
                      ? returnFormattedDate(props.item.retiredEpoch)
                      : 'Active'
                  }}
                </td>
                <td class="text-xs-center">
                  <v-icon
                    small
                    @click="editDevice(props.item)"
                    :disabled="formDisabled"
                  >
                    fa fa-edit
                  </v-icon>
                </td>
              </tr>
            </template>
          </v-data-table>
        </v-card>
        <v-dialog
          v-model="showMaintenanceDialog"
          width="50%"
          content-class="v-dialog-custom"
        >
          <v-layout
            justify-space-between
            class="task-bar app-theme__center-content--header no-highlight"
          >
            <span>Device Management</span>

            <div
              class="app-theme__center-content--closebutton"
              @click="cancelMaintenance"
            >
              <v-icon class="app-theme__center-content--closebutton--icon"
                >fal fa-times</v-icon
              >
            </div>
          </v-layout>
          <v-layout class="app-theme__center-content--body" row wrap>
            <v-flex md12 pa-3>
              <v-form ref="deviceForm">
                <v-layout v-if="isEdit || isNew" wrap>
                  <v-flex md12 class="pb-2">
                    <v-alert
                      :value="!deviceIdValidation.isValid"
                      icon="warning"
                    >
                      <p
                        class="ma-0"
                        v-for="message of deviceIdValidation.messages"
                        :key="message"
                      >
                        {{ message }}
                      </p>
                    </v-alert>
                  </v-flex>
                  <v-flex md12 pb-3 v-if="isNew">
                    <v-alert :value="deviceIdValidation.isValid" type="info">
                      New devices are created on the current default rate.
                      Custom device rates can be set in the Division section.
                    </v-alert>
                  </v-flex>
                  <v-flex md6 class="pr">
                    <v-text-field
                      solo
                      flat
                      v-model="associatedDevice.deviceId"
                      label="Device ID"
                      :rules="[validate.required]"
                      outline
                      class="v-solo-custom"
                      :disabled="isEdit"
                    />
                  </v-flex>
                  <v-flex md6 class="pl">
                    <v-select
                      box
                      v-model="associatedDevice.deviceTypeId"
                      label="Type"
                      :items="deviceList"
                      item-text="longName"
                      item-value="subCategoryId"
                      outline
                      class="v-solo-custom"
                      :disabled="isEdit"
                    />
                  </v-flex>
                  <v-flex md6 class="pr">
                    <v-select
                      box
                      :items="associationTypeSelectList"
                      item-text="shortName"
                      item-value="id"
                      label="Associated Entity Type"
                      disabled
                      v-model="associatedDevice.associationTypeId"
                      outline
                      class="v-solo-custom"
                    />
                  </v-flex>
                  <v-flex md6 class="pl">
                    <v-select
                      box
                      :disabled="
                        associatedDevice.associationTypeId === 2 || isEdit
                      "
                      :items="associationList"
                      item-text="value"
                      item-value="id"
                      label="Associated Entity"
                      v-model="associatedDevice.associationId"
                      :rules="
                        associatedDevice.associationTypeId !== 2
                          ? [validate.required]
                          : []
                      "
                      outline
                      class="v-solo-custom"
                    />
                  </v-flex>
                </v-layout>
              </v-form>
            </v-flex>
            <v-flex md12>
              <v-divider></v-divider>
            </v-flex>
            <v-flex md12>
              <v-layout
                v-if="
                  associatedDevice.retiredEpoch === null ||
                  associatedDevice.retiredEpoch === undefined
                "
              >
                <v-btn
                  @click="cancelMaintenance"
                  :disabled="formDisabled"
                  color="red"
                  outline
                  v-if="isEdit || isNew"
                  >Cancel</v-btn
                >
                <v-flex md12>
                  <v-layout
                    v-if="
                      !removeDeviceConfirmation &&
                      !isNew &&
                      (associatedDevice.retiredEpoch === null ||
                        associatedDevice.retiredEpoch === undefined)
                    "
                  >
                    <v-btn
                      class="mx-1"
                      color="white"
                      outline
                      @click="removeDeviceConfirmation = true"
                    >
                      Retire Device
                    </v-btn>
                  </v-layout>
                  <v-layout v-if="removeDeviceConfirmation && !isNew">
                    <v-flex>
                      <v-layout>
                        Are you sure you sure to retire this device?
                      </v-layout>
                    </v-flex>
                    <v-flex md12>
                      <v-layout>
                        <v-btn
                          small
                          @click="removeDeviceConfirmation = false"
                          outlined
                        >
                          Cancel</v-btn
                        >
                        <v-btn
                          small
                          depressed
                          @click="removeDevice"
                          color="error"
                          >Yes
                        </v-btn>
                      </v-layout>
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-spacer></v-spacer>
                <v-btn
                  :disabled="!deviceIdValidation.isValid"
                  color="blue"
                  depressed
                  @click="addNewDevice"
                  v-if="isNew"
                  >Add Device</v-btn
                >
                <!-- <v-btn
                  @click="setEditedDevice"
                  color="blue"
                  depressed
                  v-if="isEdit"
                >
                  Save Changes
                </v-btn> -->
              </v-layout>
              <v-layout v-else>
                <v-btn
                  @click="cancelMaintenance"
                  :disabled="formDisabled"
                  depressed
                  color="blue"
                  v-if="isEdit || isNew"
                  >Done</v-btn
                >
              </v-layout>
            </v-flex>
          </v-layout>
        </v-dialog>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import {
  AdjustmentChargeUnits,
  adjustmentChargeUnits,
} from '@/interface-models/Generic/AllowanceAndDeductions/AdditionalAllowanceAndDeductionTypes/AdjustmentChargeUnits';
import AdjustmentCharge from '@/interface-models/Generic/AllowanceAndDeductions/AdjustmentCharge';
import AddDeviceFleetAssetOwnerRequest from '@/interface-models/Generic/AssociatedDevice/AddDeviceFleetAssetOwnerRequest';
import AssociatedDevice from '@/interface-models/Generic/AssociatedDevice/AssociatedDevice';
import RetireDeviceFleetAssetOwnerRequest from '@/interface-models/Generic/AssociatedDevice/RetireDeviceFleetAssetOwnerRequest';
import {
  DeviceList,
  deviceList,
} from '@/interface-models/Generic/DeviceList/DeviceList';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { Validation } from '@/interface-models/Generic/Validation';
import { useAdjustmentChargeStore } from '@/store/modules/AdjustmentChargeStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import {
  ComputedRef,
  Ref,
  WritableComputedRef,
  computed,
  ref,
  watch,
} from 'vue';

const props = withDefaults(
  defineProps<{
    formDisabled: boolean;
    fleetAssetIds: string[];
    driverIds: string[];
    devices: AssociatedDevice[];
    fleetAssetOwnerId: string;
  }>(),
  {
    formDisabled: false,
  },
);

const fleetAssetOwnerStore = useFleetAssetOwnerStore();
const driverDetailsStore = useDriverDetailsStore();
const fleetAssetStore = useFleetAssetStore();

const validate: ComputedRef<Validation> = computed(() => validationRules);
const associatedDevice: Ref<AssociatedDevice> = ref(new AssociatedDevice());
const associationTypes: Ref<AdjustmentChargeUnits[]> = ref(
  adjustmentChargeUnits,
);

const removeDeviceConfirmation: Ref<boolean> = ref(false);
const isNew: Ref<boolean> = ref(false);
const isEdit: Ref<boolean> = ref(false);
const deviceForm: Ref<any> = ref<any>(null);
const isSendingDeviceRetirementRequest: Ref<boolean> = ref(false);

const fleetAssetOwners: ComputedRef<any[]> = computed(
  () => fleetAssetOwnerStore.getOwnerList,
);

const fleetAssetOwner: ComputedRef<string> = computed(
  () => props.fleetAssetOwnerId,
);

const headers: TableHeader[] = [
  {
    text: 'Device Name',
    align: 'left',
    sortable: true,
    value: 'deviceId',
  },
  {
    text: 'Device Type',
    align: 'left',
    sortable: true,
    value: 'deviceTypeId',
  },
  {
    text: 'Associated To',
    align: 'left',
    sortable: true,
    value: 'associationTypeId',
  },
  {
    text: 'Assigned To',
    align: 'left',
    sortable: true,
    value: 'associationId',
  },
  {
    text: 'Date Added',
    align: 'left',
    sortable: true,
    value: 'createdEpoch',
  },
  {
    text: 'Date Retired',
    align: 'left',
    sortable: true,
    value: 'retiredEpoch',
  },
  {
    text: 'Edit',
    align: 'center',
    sortable: false,
    value: 'edit',
  },
];

// if fleet asset owner is selected as associationTypeId we need to remove previously associated drivers and fleet assets
//   const associatedDevice = ref(new AssociatedDevice());
watch(
  associatedDevice,
  (newValue: AssociatedDevice) => {
    if (newValue.associationTypeId === 2) {
      associatedDevice.value.associationId = '';
      resetValidation();
    }
  },
  { deep: true },
);

// Function to reset validation
function resetValidation() {
  if (deviceForm.value) {
    deviceForm.value.resetValidation();
  }
}

// when fleet asset owner changes we need to make sure we reset the components state so its still not in edit or new associated device mode
watch(fleetAssetOwner, () => {
  associatedDevice.value = new AssociatedDevice();
  isNew.value = false;
  isEdit.value = false;
});

// fleet asset owners associated drivers
const drivers: ComputedRef<DriverDetailsSummary[]> = computed(() => {
  return props.driverIds
    .map((fid) => driverDetailsStore.getDriverFromDriverId(fid))
    .filter((d): d is DriverDetailsSummary => d !== undefined);
});
// fleet asset owners associated fleet assets
const fleetAssets = computed<FleetAssetSummary[]>(() => {
  return props.fleetAssetIds
    .map((fid) => fleetAssetStore.getFleetAssetFromFleetAssetId(fid))
    .filter((fa): fa is FleetAssetSummary => fa !== undefined);
});

// We require to change the association selection list if the parent changes eg; fleet asset owner, driver, fleet asset
const associationTypeSelectList: ComputedRef<AdjustmentChargeUnits[]> =
  computed(() => {
    const associationTypeList: AdjustmentChargeUnits[] = JSON.parse(
      JSON.stringify(associationTypes.value),
    );

    if (drivers.value.length === 0) {
      const toBeRemovedIndex = associationTypeList.findIndex(
        (item: AdjustmentChargeUnits) => item.id === 3,
      );

      if (toBeRemovedIndex !== -1) {
        associationTypeList.splice(toBeRemovedIndex, 1);
      }
    }

    if (fleetAssets.value.length === 0) {
      const toBeRemovedIndex = associationTypeList.findIndex(
        (item: AdjustmentChargeUnits) => item.id === 1,
      );

      if (toBeRemovedIndex !== -1) {
        associationTypeList.splice(toBeRemovedIndex, 1);
      }
    }

    return associationTypeList;
  });

// Method to get the device name based on its ID
const deviceTypeName = (id: number) => {
  const deviceDetails = deviceList.find(
    (device: DeviceList) => device.subCategoryId === id,
  );
  return deviceDetails ? deviceDetails.longName : 'Unknown';
};

/**
 * Retire a device from an owner. Sends a request including the device id and
 * ownerId, and handles the response.
 */
async function removeDevice() {
  const deviceToBeRemovedIndex = props.devices.findIndex(
    (device: AssociatedDevice) => device.id === associatedDevice.value.id,
  );
  if (deviceToBeRemovedIndex !== -1) {
    const request: RetireDeviceFleetAssetOwnerRequest = {
      ownerId: props.fleetAssetOwnerId,
      associatedDeviceId: associatedDevice.value.id,
    };
    isSendingDeviceRetirementRequest.value = true;
    const result =
      await fleetAssetOwnerStore.retireDeviceFromFleetAssetOwner(request);
    handleRetireResponse(result);
    isSendingDeviceRetirementRequest.value = false;
    resetComponentState();
  }
}

/**
 * Handles response for the retire operation called from removeDevice
 * @param response - Response from the API, containing ownerId and the retired
 * device. If the device was successfully retired, the device is updated in the devices list
 */
function handleRetireResponse(
  response: AddDeviceFleetAssetOwnerRequest | null,
) {
  const retiredDevice = response?.associatedDevice;
  if (!retiredDevice) {
    showNotification(GENERIC_ERROR_MESSAGE, {
      title: 'Fleet Asset Owner - Retire Device',
    });
    return;
  }
  updateAdjustmentChargeForRemovedDevice(retiredDevice);
  // if (props.fleetAssetOwnerId === response?.ownerId) {
  //   const foundMatch = props.devices.findIndex(
  //     (ad) => ad.id === retiredDevice.id,
  //   );
  //   if (foundMatch !== -1) {
  //     props.devices.splice(foundMatch, 1, retiredDevice);
  //   }
  // }
}

// Method to reset component state
const resetComponentState = (): void => {
  isEdit.value = false;
  isNew.value = false;
  removeDeviceConfirmation.value = false;
  associatedDevice.value = new AssociatedDevice();
};

// Method to update adjustment charge for removed device
const updateAdjustmentChargeForRemovedDevice = async (
  device: AssociatedDevice,
) => {
  const deviceDefaults = adjustmentCharges.value.filter(
    (charge: AdjustmentCharge) =>
      charge.categoryId === 1 &&
      charge.subCategoryId === device.deviceTypeId &&
      charge.unitId === device.associationTypeId &&
      !charge.isDefault,
  );
  const foundCustomCharge = deviceDefaults.find((charge: AdjustmentCharge) =>
    charge.appliesTo.includes(device.associationId),
  );

  if (foundCustomCharge === undefined) {
    resetComponentState();
  } else {
    const foundIndex = foundCustomCharge.appliesTo.findIndex(
      (entityId: string) => entityId === device.associationId,
    );

    if (foundIndex !== -1) {
      foundCustomCharge.appliesTo.splice(foundIndex, 1);
      // Send request to update adjustment charge
      const result =
        await useAdjustmentChargeStore().saveAdjustmentCharge(
          foundCustomCharge,
        );
      if (result !== null) {
        resetComponentState();
      }
    }
  }
};

// Method to get entity name based on type and ID
const entityName = (typeId: number, entityId: string) => {
  switch (typeId) {
    case 1:
      const fleetAsset = fleetAssets.value.find(
        (asset: FleetAssetSummary) => asset.fleetAssetId === entityId,
      );
      return fleetAsset ? fleetAsset.csrAssignedId : 'Unknown';
    case 2:
      return '-';
    case 3:
      const driverDetails = drivers.value.find(
        (driver: DriverDetailsSummary) => driver.driverId === entityId,
      );
      return driverDetails?.displayName ?? 'Unknown';
    default:
      return 'Unknown';
  }
};

// Getter for adjustment charges
const adjustmentCharges: ComputedRef<AdjustmentCharge[]> = computed(
  () => useAdjustmentChargeStore().adjustmentCharges,
);

// Computed property to get the association type name
const associationTypeName: ComputedRef<(unitId: number) => string> = computed(
  () => (unitId: number) => {
    const unit = associationTypes.value.find(
      (item: AdjustmentChargeUnits) => item.id === unitId,
    );
    return unit ? unit.shortName : '';
  },
);

// Computed property to determine whether to show the maintenance dialog
const showMaintenanceDialog: WritableComputedRef<boolean> = computed({
  get: (): boolean => isNew.value || isEdit.value,
  set: (value: boolean) => {
    if (!value) {
      cancelMaintenance();
    }
  },
});

// Method to handle new device creation
const newDevice = (): void => {
  isNew.value = true;
  associatedDevice.value = new AssociatedDevice();
};

/**
 * Add a new device to the fleet asset owner. Sends a request including the
 * ownerId and the associated device, and handles the response.
 */
async function addNewDevice() {
  if (deviceForm.value.validate() && deviceIdValidation.value.isValid) {
    const request: AddDeviceFleetAssetOwnerRequest = {
      ownerId: props.fleetAssetOwnerId,
      associatedDevice: associatedDevice.value,
    };
    isNew.value = false;
    const result =
      await fleetAssetOwnerStore.addDeviceToFleetAssetOwner(request);
    if (!result) {
      showNotification(GENERIC_ERROR_MESSAGE, {
        title: 'Fleet Asset Owner - Add Device',
      });
    }
    // handleSaveResponse(result);
  }
}

// Method to edit a device
const editDevice = (device: AssociatedDevice) => {
  associatedDevice.value = device;
  isEdit.value = true;
};

// Method to set the edited device
// async function setEditedDevice() {
//   const request: AddDeviceFleetAssetOwnerRequest = {
//     ownerId: props.fleetAssetOwnerId,
//     associatedDevice: associatedDevice.value,
//   };
//   isEdit.value = false;
//   const result = await fleetAssetOwnerStore.addDeviceToFleetAssetOwner(request);
//   if (!result) {
//     showNotification(GENERIC_ERROR_MESSAGE, {
//       title: 'Fleet Asset Owner - Edit Device',
//     });
//   }
//   // handleSaveResponse(result);
// }

// /**
//  * Handler for save response on adding device to fleet asset owner
//  * @param response - Response from the API, containing ownerId and the added
//  * device
//  */
// function handleSaveResponse(response: AddDeviceFleetAssetOwnerRequest | null) {
//   const addedDevice = response?.associatedDevice;
//   if (!addedDevice) {
//     return;
//   }
//   const foundMatch = props.devices.findIndex((ad) => ad.id === addedDevice.id);
//   if (foundMatch !== -1) {
//     props.devices.splice(foundMatch, 1, addedDevice);
//   } else {
//     props.devices.push(addedDevice);
//   }
// }

// Method to cancel maintenance
const cancelMaintenance = (): void => {
  resetComponentState();
};

// Computed property for deviceIdValidation
const deviceIdValidation: ComputedRef<any> = computed(() => {
  interface DeviceIdValidation {
    isValid: boolean;
    messages: string[];
    associatedDevice: AssociatedDevice;
  }
  const deviceIdValidation: DeviceIdValidation = {
    isValid: true,
    messages: [],
    associatedDevice: associatedDevice.value,
  };

  for (const owner of fleetAssetOwners.value) {
    if (owner.associatedDevices !== null) {
      const foundDevice = owner.associatedDevices.find(
        (device: AssociatedDevice) =>
          device.deviceId === associatedDevice.value.deviceId &&
          device.id !== associatedDevice.value.id &&
          !device.retiredEpoch,
      );

      if (foundDevice) {
        deviceIdValidation.associatedDevice = foundDevice;
        deviceIdValidation.isValid = false;
        deviceIdValidation.messages.push(
          'This device Id is already associated to a fleet asset owner: ' +
            owner.name,
        );
      }
    }
  }
  const defaultExists: AdjustmentCharge[] = adjustmentCharges.value.filter(
    (charge: AdjustmentCharge) =>
      charge.categoryId === 1 &&
      charge.unitId === associatedDevice.value.associationTypeId &&
      charge.subCategoryId === associatedDevice.value.deviceTypeId &&
      charge.isDefault,
  );
  if (defaultExists.length === 0) {
    deviceIdValidation.isValid = false;
    deviceIdValidation.messages.push(
      'There is currently no default adjustment charge setup for this device type and Entity Type. You will be required to make one before you can create this type of device allocation',
    );
  }
  return deviceIdValidation;
});

// Computed property for associationList
const associationList: ComputedRef<any[]> = computed(() => {
  let list: any[] = [];
  switch (associatedDevice.value.associationTypeId) {
    case 1:
      list = fleetAssets.value.map((asset: FleetAssetSummary) => {
        return {
          id: asset.fleetAssetId,
          value: asset.csrAssignedId + ' ' + asset.registrationNumber,
        };
      });
      break;
    case 2:
      return [];
    case 3:
      list = drivers.value.map((driver: DriverDetailsSummary) => {
        return {
          id: driver.driverId,
          value: driver.displayName,
        };
      });
      break;
  }
  return list;
});
</script>

<style scoped>
.add-device-btn {
  position: absolute;
  top: 1px;
  right: 10px;
  z-index: 10 !important;
}
</style>
