<template>
  <div class="applied-adjustment-charges-container">
    <div slot="content">
      <v-data-table
        class="gd-dark-theme"
        :headers="headers"
        :items="appliedChargesTableData"
        hide-actions
        item-key="_id"
      >
        <template v-slot:items="tableProps">
          <tr>
            <td>{{ tableProps.item.category }}</td>
            <td>{{ tableProps.item.item }}</td>
            <td>
              {{
                tableProps.item.isCustomCharge ? 'Custom Rate' : 'Default Rate'
              }}
            </td>
            <td>{{ DisplayCurrencyValue(tableProps.item.amount) }}</td>
            <td>{{ tableProps.item.billed }}</td>

            <td>{{ tableProps.item.entityType }}</td>
            <td>{{ tableProps.item.entity }}</td>
          </tr>
        </template>
      </v-data-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { isDeviceDeductionAdjustmentCharge } from '@/helpers/RateHelpers/AdjustmentChargeHelpers';
import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import { AdjustmentChargeType } from '@/interface-models/Generic/Accounting/AdjustmentChargeType';
import { AdjustmentChargeAssociationType } from '@/interface-models/Generic/AllowanceAndDeductions/AdditionalAllowanceAndDeductionTypes/AdjustmentChargeAssociationType';
import {
  AdjustmentChargeBillingCycles,
  adjustmentChargeBillingCycles,
} from '@/interface-models/Generic/AllowanceAndDeductions/AdditionalAllowanceAndDeductionTypes/AdjustmentChargeBillingCycles';
import { AdjustmentChargeCategory } from '@/interface-models/Generic/AllowanceAndDeductions/AdditionalAllowanceAndDeductionTypes/AdjustmentChargeCategory';
import { AdjustmentChargeSubCategory } from '@/interface-models/Generic/AllowanceAndDeductions/AdditionalAllowanceAndDeductionTypes/AdjustmentChargeSubCategory';
import AdjustmentCharge from '@/interface-models/Generic/AllowanceAndDeductions/AdjustmentCharge';
import AssociatedDevice from '@/interface-models/Generic/AssociatedDevice/AssociatedDevice';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { useAdjustmentChargeStore } from '@/store/modules/AdjustmentChargeStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { ComputedRef, computed } from 'vue';

interface AppliedCharge {
  amount: number;
  category: string;
  isCustomCharge: boolean;
  item: string;
  billed: string;
  entityType: string;
  entity: string;
}

const props = withDefaults(
  defineProps<{
    devices: AssociatedDevice[];
    fleetAssetOwnerId: string;
    driverIds: string[];
    fleetAssetIds: string[];
  }>(),
  {
    fleetAssetOwnerId: '',
  },
);

const fleetAssetStore = useFleetAssetStore();
const driverDetailsStore = useDriverDetailsStore();
const adjustmentChargeStore = useAdjustmentChargeStore();

const billingCycles: AdjustmentChargeBillingCycles[] =
  adjustmentChargeBillingCycles;

const headers: TableHeader[] = [
  {
    text: 'Category',
    align: 'left',
    sortable: false,
    value: 'category',
  },
  {
    text: 'Subcategory',
    align: 'left',
    sortable: false,
    value: 'item',
  },
  {
    text: 'Custom / Default',
    align: 'left',
    sortable: false,
    value: 'isCustomCharge',
  },
  {
    text: 'Amount ($)',
    align: 'left',
    sortable: false,
    value: 'amount',
  },
  {
    text: 'Billed',
    align: 'left',
    sortable: false,
    value: 'billed',
  },
  {
    text: 'Entity Type',
    align: 'left',
    sortable: false,
    value: 'entityType',
  },
  {
    text: 'Entity',
    align: 'left',
    sortable: false,
    value: 'entity',
  },
];

const adjustmentCharges: AdjustmentCharge[] =
  useAdjustmentChargeStore().adjustmentCharges;

/**
 * Get the drivers referenced by the driverId list from the
 * driverIds prop.
 * @returns DriverDetailsSummary[]
 */
const drivers: ComputedRef<DriverDetailsSummary[]> = computed(() => {
  return props.driverIds
    .map((fid) => driverDetailsStore.getDriverFromDriverId(fid))
    .filter((d) => d !== undefined) as DriverDetailsSummary[];
});

/**
 * Get the fleet assets referenced by the fleetAssetId list from the
 * fleetAssetIds prop.
 * @returns FleetAssetSummary[]
 */
const fleetAssets: ComputedRef<FleetAssetSummary[]> = computed(() => {
  return props.fleetAssetIds
    .map((fid) => fleetAssetStore.getFleetAssetFromFleetAssetId(fid))
    .filter((fa) => fa !== undefined) as FleetAssetSummary[];
});

/**
 * For the given categoryId (device deduction, app charge etc.) and
 * association type  (Fleet Asset, Owner etc.), we return a list of charges
 * that are applied.
 * Entity ID is the fleetAssetId, ownerId or driverId depending
 * @param categoryId - The ID of the category to look up
 * @param associationTypeId - The ID of the association type to look up (1 for Fleet Asset, 2 for Owner, etc.)
 * @returns An array of AppliedCharge objects.
 */
function applyChargePerDevice(
  categoryId: number,
  associationTypeId: AdjustmentChargeAssociationType,
): AppliedCharge[] {
  const tableItems: AppliedCharge[] = [];
  // Filter for devices for this association type (all FLEET_ASSET devices etc.)
  const associatedDevices = props.devices.filter(
    (device: AssociatedDevice) =>
      device.associationTypeId === associationTypeId,
  );

  // Filter for all DEFAULT charges of the CategoryType (DEVICE_DEDUCTION or
  // MOBILE_APP etc.) and for the association type
  const defaultCharges = adjustmentCharges.filter(
    (charge: AdjustmentCharge) =>
      charge.categoryId === categoryId &&
      charge.unitId === associationTypeId &&
      charge.isDefault,
  );

  // Filter for all CUSTOM charges (ie isDefault is false) of the CategoryType
  // (DEVICE_DEDUCTION or MOBILE_APP etc.) and for the association type
  const customCharges = adjustmentCharges.filter(
    (charge: AdjustmentCharge) =>
      charge.categoryId === categoryId &&
      charge.unitId === associationTypeId &&
      !charge.isDefault,
  );

  for (const device of associatedDevices) {
    let foundEntity;
    // Use type is DRIVER then try to find Driver Details
    if (associationTypeId === AdjustmentChargeAssociationType.DRIVER) {
      foundEntity = driverDetailsStore.getDriverFromDriverId(
        device.associationId,
      );
    } else if (
      associationTypeId === AdjustmentChargeAssociationType.FLEET_ASSET
    ) {
      // Use type is FLEET_ASSET then try to find FleetAsset
      foundEntity = fleetAssets.value.find(
        (fleetAsset: FleetAssetSummary) =>
          fleetAsset.fleetAssetId === device.associationId,
      );
    } else if (
      associationTypeId === AdjustmentChargeAssociationType.FLEET_ASSET_OWNER
    ) {
      // Use type is FLEET ASSET OWNER then use ownerId
      foundEntity = props.fleetAssetOwnerId;
    }

    if (foundEntity !== undefined) {
      // Check if there is a CUSTOM CHARGE that applies to the selected entity.
      // We only need to check for specific device type if we're looking for

      // Only perform subCategoryId/deviceTypeId comparison if we're dealing
      // with DEVICE_DEDUCTION type charge
      const requiresDeviceIdComparison =
        isDeviceDeductionAdjustmentCharge(categoryId);
      // Try to find Custom Charge
      let chargeItem: AdjustmentCharge | undefined = customCharges.find(
        (charge: AdjustmentCharge) =>
          (!requiresDeviceIdComparison ||
            charge.subCategoryId === device.deviceTypeId) &&
          charge.appliesTo.includes(device.associationId),
      );
      // Try to find Custom Charge
      if (chargeItem === undefined) {
        chargeItem = defaultCharges.find(
          (charge: AdjustmentCharge) =>
            !requiresDeviceIdComparison ||
            charge.subCategoryId === device.deviceTypeId,
        );
      }
      // If there is no Custom Charge, then find a DEFAULT charge for the
      // device type id
      if (chargeItem !== undefined) {
        const tableItem: AppliedCharge = {
          amount: chargeItem.amount,
          category: categoryName(chargeItem.categoryId),
          isCustomCharge: !chargeItem.isDefault,
          item: subCategoryName(
            chargeItem.categoryId,
            chargeItem.subCategoryId,
          ),
          billed: billedMultiplierName(chargeItem.billedMultiplier),
          entityType: returnEntityNameFromAssociationType(associationTypeId),
          entity: '',
        };

        if (associationTypeId === AdjustmentChargeAssociationType.DRIVER) {
          tableItem.entity = (foundEntity as DriverDetailsSummary).displayName;
        } else if (
          associationTypeId === AdjustmentChargeAssociationType.FLEET_ASSET
        ) {
          tableItem.entity = (foundEntity as FleetAssetSummary).csrAssignedId;
        } else if (
          associationTypeId ===
          AdjustmentChargeAssociationType.FLEET_ASSET_OWNER
        ) {
          tableItem.entity = foundEntity as string;
        }

        tableItems.push(tableItem);
      }
    }
  }
  return tableItems;
}

/**
 * Return a readable name to be displayed in the template
 * @param type - The type of the association
 * @returns The name of the association type
 */
function returnEntityNameFromAssociationType(
  type: AdjustmentChargeAssociationType,
): string {
  switch (type) {
    case AdjustmentChargeAssociationType.FLEET_ASSET:
      return 'Fleet Asset';
    case AdjustmentChargeAssociationType.FLEET_ASSET_OWNER:
      return 'Fleet Asset Owner';
    case AdjustmentChargeAssociationType.DRIVER:
      return 'Driver';
    default:
      return '';
  }
}
// For the given categoryId (ADMIN/TRAILER) and
// association type  (Fleet Asset, Owner etc.), we return a list of charges
// that are applied.
// associationTypeId is the fleetAssetId, ownerId or driverId depending
function applyChargePerEntity(
  categoryId: number,
  associationTypeId: AdjustmentChargeAssociationType,
  entityData: (DriverDetailsSummary | FleetAssetSummary | string)[],
): AppliedCharge[] {
  const tableItems: AppliedCharge[] = [];
  const defaultCharges = adjustmentCharges.filter(
    (charge: AdjustmentCharge) =>
      charge.categoryId === categoryId &&
      charge.unitId === associationTypeId &&
      charge.isDefault,
  );

  for (const entity of entityData) {
    let entityId;
    // Find ID to use
    if (associationTypeId === AdjustmentChargeAssociationType.DRIVER) {
      entityId = (entity as DriverDetailsSummary).driverId;
    } else if (
      associationTypeId === AdjustmentChargeAssociationType.FLEET_ASSET
    ) {
      entityId = (entity as FleetAssetSummary).fleetAssetId;
    } else if (
      associationTypeId === AdjustmentChargeAssociationType.FLEET_ASSET_OWNER
    ) {
      entityId = entity as string;
    }

    if (entityId !== undefined) {
      for (const defaultCharge of defaultCharges) {
        let chargeItem: AdjustmentCharge | undefined;
        // Try to find a Custom Charge for this combination of
        // category/association type
        const foundCustoms = adjustmentCharges.filter(
          (charge: AdjustmentCharge) =>
            charge.categoryId === categoryId &&
            charge.unitId === associationTypeId &&
            charge.subCategoryId === defaultCharge.subCategoryId &&
            !charge.isDefault,
        );

        for (const custom of foundCustoms) {
          if (custom.appliesTo.includes(entityId)) {
            chargeItem = custom;
            break;
          }
        }
        // Set chargeItem to default if chargeItem is still undefined, and defaultCharge is active
        if (chargeItem === undefined && defaultCharge.isActive) {
          chargeItem = defaultCharge;
        }
        // No custom charge was found and no suitable default is active - continue.
        if (chargeItem === undefined) {
          continue;
        }

        const tableItem: AppliedCharge = {
          amount: chargeItem.amount,
          category: categoryName(chargeItem.categoryId), // Assuming categoryName is a helper function defined in your component
          isCustomCharge: !chargeItem.isDefault,
          item: subCategoryName(
            chargeItem.categoryId,
            chargeItem.subCategoryId,
          ), // Assuming subCategoryName is a helper function defined in your component
          billed: billedMultiplierName(chargeItem.billedMultiplier), // Assuming billedMultiplierName is a helper function defined in your component
          entityType: returnEntityNameFromAssociationType(associationTypeId), // Assuming returnEntityNameFromAssociationType is a helper function defined in your component
          entity: '',
        };

        if (associationTypeId === AdjustmentChargeAssociationType.DRIVER) {
          tableItem.entity = (entity as DriverDetailsSummary).displayName;
        } else if (
          associationTypeId === AdjustmentChargeAssociationType.FLEET_ASSET
        ) {
          tableItem.entity = (entity as FleetAssetSummary).csrAssignedId;
        } else if (
          associationTypeId ===
          AdjustmentChargeAssociationType.FLEET_ASSET_OWNER
        ) {
          tableItem.entity = entity as string;
        }

        tableItems.push(tableItem);
      }
    }
  }

  return tableItems;
}

/**
 * Computes and returns the data that is used in the v-data-table in the template.
 */
const appliedChargesTableData: ComputedRef<AppliedCharge[]> = computed(() => {
  // Filter for a list of TAX_INVOICE type charges (not RCTI charges) that are
  // recurring (allowRecurringCharges === true)
  const applicableCategoryIds = adjustmentChargeStore.adjustmentChargeCategories
    .filter(
      (category: AdjustmentChargeCategory) =>
        category.adjustmentChargeType === AdjustmentChargeType.TAX_INVOICE &&
        category.allowRecurringCharges,
    )
    .map((category: AdjustmentChargeCategory) => category.categoryId);

  const associationTypes = [
    { type: AdjustmentChargeAssociationType.DRIVER, data: drivers.value },
    {
      type: AdjustmentChargeAssociationType.FLEET_ASSET,
      data: fleetAssets.value,
    },
    {
      type: AdjustmentChargeAssociationType.FLEET_ASSET_OWNER,
      data: [props.fleetAssetOwnerId],
    },
  ];

  return applicableCategoryIds.flatMap((categoryId) =>
    associationTypes.flatMap(({ type, data }) =>
      isDeviceDeductionAdjustmentCharge(categoryId)
        ? applyChargePerDevice(categoryId, type)
        : applyChargePerEntity(categoryId, type, data),
    ),
  );
});

/**
 * Returns the long name of a category based on its ID.
 *
 * @param categoryId - The ID of the category to look up.
 * @returns The long name of the category, or 'Unknown' if not found.
 */
function categoryName(categoryId: number): string {
  const category: AdjustmentChargeCategory | undefined =
    adjustmentChargeStore.adjustmentChargeCategories.find(
      (categoryItem: AdjustmentChargeCategory) =>
        categoryItem.categoryId === categoryId,
    );

  return category?.longName ?? 'Unknown';
}

/**
 * Returns the long name of a subcategory based on its category ID and subcategory ID.
 *
 * @param categoryId - The ID of the category to look up.
 * @param subCategoryId - The ID of the subcategory to look up.
 * @returns The long name of the subcategory, or 'Unknown' if not found.
 */
function subCategoryName(categoryId: number, subCategoryId: number): string {
  const foundCategory: AdjustmentChargeCategory | undefined =
    adjustmentChargeStore.adjustmentChargeCategories.find(
      (category: AdjustmentChargeCategory) =>
        category.categoryId === categoryId,
    );

  const foundSubCategory: AdjustmentChargeSubCategory | undefined =
    foundCategory?.subCategories.find(
      (item: AdjustmentChargeSubCategory) =>
        item.subCategoryId === subCategoryId,
    );

  return foundSubCategory?.longName ?? 'Unknown';
}

function billedMultiplierName(billingCycleId: number): string {
  const billingCycle = billingCycles.find(
    (item: AdjustmentChargeBillingCycles) => item.id === billingCycleId,
  );
  return billingCycle?.longName ?? 'Unknown';
}
</script>

<style scoped>
.applied-adjustment-charges-container {
  width: 100%;
}
</style>
