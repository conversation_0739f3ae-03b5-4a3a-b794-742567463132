<template>
  <div class="fleet-asset-owner-operation-details-container">
    <v-layout wrap>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">1. Invoicing</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Billing Cycle:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              v-model="operationDetails.billingCycleId"
              :items="billingCycles"
              item-text="longName"
              item-value="id"
              class="v-solo-custom"
              :disabled="!isEdited"
              solo
              flat
            />
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Trading Terms:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              v-model="operationDetails.tradingTermsId"
              :items="tradingTerms"
              :rules="[validate.required]"
              item-text="longName"
              item-value="id"
              class="v-solo-custom"
              :disabled="!isEdited"
              flat
              solo
            />
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Gst Registered:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              class="ma-0 ml-1 mt-2"
              v-model="gstDetails.gstRegistered"
              :disabled="!isEdited"
            ></v-checkbox>
          </v-flex>
        </v-layout>
      </v-flex>
      <FleetAssetOwnerBankingDetails
        :isEdited="isEdited"
        :bankingDetails="bankingDetails"
      />
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import FleetAssetOwnerBankingDetails from '@/components/admin/FleetAssetOwner/components/fleet_asset_owner_banking_details.vue';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import OperationDetails from '@/interface-models/FleetAssetOwner/OperationDetails';
import { BankingDetailsAU } from '@/interface-models/Generic/BankingDetails/BankingDetailsAU';
import { billingCycles } from '@/interface-models/Generic/BillingCycles/BillingCycles';
import GstRegistrationDetails from '@/interface-models/Generic/GstRegistrationDetails/GstRegistrationDetails';
import { tradingTerms } from '@/interface-models/Generic/TradingTerms/TradingTerms';
import { Validation } from '@/interface-models/Generic/Validation';
import { computed, ComputedRef } from 'vue';

const props = withDefaults(
  defineProps<{
    operationDetails: OperationDetails;
    gstDetails: GstRegistrationDetails;
    bankingDetails: BankingDetailsAU;
    isEdited?: boolean;
  }>(),
  {
    isEdited: false,
  },
);

const validate: ComputedRef<Validation> = computed(() => {
  return validationRules;
});
</script>

<style scoped>
.label-container {
  height: 48px;
}
</style>
