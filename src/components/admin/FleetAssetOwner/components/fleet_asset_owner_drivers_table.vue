<template>
  <v-layout column>
    <div class="add-driver-btn">
      <ConfirmationDialog
        v-if="fleetAssetOwner"
        :buttonText="
          !fleetAssetOwner.isOutsideHire
            ? `New Driver`
            : `New Generic
      Driver`
        "
        message="You are about to create a new Generic Driver. Outside Hire Owners should only have one (1) Generic Driver. Please check if another Generic Driver exists for this owner before proceeding."
        title="Check for Existing Generic Drivers"
        :isSmallButton="true"
        @confirm="addNewDriver"
        :isDepressedButton="true"
        buttonColor="blue"
        confirmationButtonText="Proceed to Create Driver"
        :dialogIsActive="fleetAssetOwner.isOutsideHire"
      />
    </div>

    <v-data-table
      class="gd-dark-theme"
      :headers="headers"
      :items="associatedDriverTableList"
      :item-key="'driverId'"
      :rows-per-page-items="[10, 20]"
    >
      <template v-slot:items="props">
        <tr
          style="cursor: pointer"
          @click="viewDriverDetailsDialog(props.item.driverId)"
        >
          <td v-if="showAssignedIdColumn">{{ props.item.assignedId }}</td>
          <td>{{ props.item.name }}</td>
          <td>{{ formatPhoneNumber(props.item.mobileNumber) }}</td>
          <td>{{ props.item.email }}</td>
          <td>
            <span
              class="status-container"
              :class="props.item.status === 'Active' ? 'success' : 'error'"
              >{{ props.item.status }}</span
            >
          </td>
        </tr>
      </template>
    </v-data-table>
    <DriverDetailsDialog
      :isFleetAssetOwner="true"
      :fleetAssetOwner="fleetAssetOwner"
    />
  </v-layout>
</template>

<script setup lang="ts">
import DriverDetailsDialog from '@/components/admin/DriverDetails/driver_details_dialog.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import { formatPhoneNumber } from '@/helpers/StringHelpers/StringHelpers';
import { PrimaryDriverIdentifier } from '@/interface-models/Company/DivisionCustomConfig/Operations/PrimaryDriverIdentifier';
import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import FleetAssetOwner from '@/interface-models/FleetAssetOwner/FleetAssetOwner';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import Mitt from '@/utils/mitt';
import {
  ComputedRef,
  Ref,
  computed,
  onBeforeUnmount,
  onMounted,
  ref,
} from 'vue';

const props = withDefaults(
  defineProps<{
    isEdited?: boolean;
    fleetAssetOwner: FleetAssetOwner;
    // fleetAssetIds: string[];
    fleetAssetOwnerId?: string;
  }>(),
  {
    fleetAssetOwnerId: '',
    isEdited: false,
  },
);

const driverDetailsStore = useDriverDetailsStore();

const refreshKey: Ref<number> = ref(0);

interface DriverTable {
  driverId: string;
  assignedId: string;
  name: string;
  mobileNumber: string;
  email: string;
  status: string;
}

const associatedDriverIds: ComputedRef<string[]> = computed(() => {
  if (
    props.fleetAssetOwner.associatedDrivers &&
    props.fleetAssetOwner.associatedDrivers.length
  ) {
    return props.fleetAssetOwner.associatedDrivers;
  } else {
    return (
      useFleetAssetOwnerStore().getOwnerFromOwnerId(
        props.fleetAssetOwner.ownerId,
      )?.associatedDrivers ?? []
    );
  }
});

// Return the summary of all drivers under the current owner to be used in
// template table
const associatedDriverTableList: ComputedRef<DriverTable[]> = computed(() => {
  if (!props.fleetAssetOwner) {
    return [];
  }
  refreshKey.value;

  return associatedDriverIds.value
    .map((driverId) => driverDetailsStore.driverSummaryMap.get(driverId))
    .filter((driver): driver is DriverDetailsSummary => driver !== undefined)
    .map((driver: DriverDetailsSummary) => {
      const displayName = showAssignedIdColumn.value
        ? driver.name
        : driver.displayName;
      const doNotUse: boolean = driver.statusList.includes(47);
      const active: boolean = driver.statusList.includes(4);
      return {
        driverId: driver.driverId,
        assignedId: driver.assignedId || '-',
        name: displayName ?? '-',
        mobileNumber: driver.mobile ? driver.mobile : '-',
        email: driver.email ? driver.email : '-',
        status: doNotUse ? 'Do Not Use' : active ? 'Active' : 'Requires Action',
      };
    });
});

/**
 * Computed property to determine if the Assigned ID column should be shown. In
 * this case we display the assignedId field on the driver as the first column.
 */
const showAssignedIdColumn: ComputedRef<boolean> = computed(() => {
  const primaryDriverIdentifier =
    useCompanyDetailsStore().divisionCustomConfig?.operations
      ?.primaryDriverIdentifier;

  return (
    !!primaryDriverIdentifier &&
    (primaryDriverIdentifier === PrimaryDriverIdentifier.ASSIGNED_ID ||
      primaryDriverIdentifier === PrimaryDriverIdentifier.ASSIGNED_ID_WITH_NAME)
  );
});

const headers: ComputedRef<TableHeader[]> = computed(() => {
  return [
    showAssignedIdColumn.value
      ? {
          text: 'Driver ID',
          align: 'left',
          value: 'assignedId',
          sortable: false,
        }
      : null,
    {
      text: 'Name',
      align: 'left',
      sortable: true,
      value: 'name',
    },
    {
      text: 'Mobile',
      align: 'left',
      value: 'mobileNumber',
      sortable: false,
    },
    {
      text: 'Email Address',
      align: 'left',
      value: 'email',
      sortable: false,
    },
    {
      text: 'Status',
      align: 'left',
      value: 'status',
      sortable: false,
    },
  ].filter((header) => header !== null) as TableHeader[];
});

// methods
function viewDriverDetailsDialog(driverId: string) {
  driverDetailsStore.setSelectedDriverDetailsId(driverId);
}

function addNewDriver() {
  driverDetailsStore.setSelectedDriverDetailsId('new');
}

/**
 * Because the computed associatedDriverTableList getter in this component is
 * not reactive to nested field changes, it causes some updates to statusList to
 * not be displayed until the component is re-rendered. To get around this there
 * is a refreshKey variable that is updated here and referenced in the getter;
 * forcing the re-computation of the associatedDriverTableList getter.
 *
 * @param {boolean} createListener - Whether the listener should be created or
 * destroyed.
 * @returns {void}
 */
// Function to handle subcontractor entity updates
function listenForSubcontractorEntityUpdates(createListener: boolean): void {
  if (!createListener) {
    Mitt.off('updateSubcontractorEntityAssociationResponse');
  }
  Mitt.on('updateSubcontractorEntityAssociationResponse', () => {
    // Update refreshKey to recompute assets getter
    refreshKey.value++;
  });
}

// Lifecycle hooks
onMounted(() => {
  listenForSubcontractorEntityUpdates(true);
});

onBeforeUnmount(() => {
  listenForSubcontractorEntityUpdates(false);
});
</script>

<style scoped lang="scss">
.status-container {
  padding: 2px 8px;
  letter-spacing: 0.03em;
  font-size: $font-size-13;
  font-weight: 500;
  border-radius: $border-radius-sm;
}

.add-driver-btn {
  position: absolute;
  top: 1px;
  right: 10px;
  z-index: 190 !important;
}
</style>
