<template>
  <v-layout column>
    <div class="add-asset-btn">
      <v-btn @click="addNewAsset" color="blue" small depressed
        >Create New Asset</v-btn
      >
    </div>

    <v-data-table
      class="gd-dark-theme"
      :headers="headers"
      :items="assets"
      :item-key="'driverId'"
      :rows-per-page-items="[10, 20]"
    >
      <template v-slot:items="props">
        <tr
          style="cursor: pointer"
          @click="viewFleetAssetDialog(props.item.fleetAssetId)"
        >
          <td>{{ props.item.csrAssignedId }}</td>
          <td>{{ props.item.registrationNumber }}</td>
          <td v-if="fleetAssetTypeId === 1">{{ props.item.defaultDriver }}</td>
          <td v-if="fleetAssetTypeId === 1">
            {{ props.item.otherDrivers ? props.item.otherDrivers : '-' }}
          </td>
          <td>{{ props.item.class ? props.item.class : '-' }}</td>
          <td>
            <span
              class="status-container"
              :class="props.item.status === 'Active' ? 'success' : 'error'"
              >{{ props.item.status }}</span
            >
          </td>
          <td v-if="fleetAssetTypeId === 2">
            <span
              class="status-container"
              :class="
                hireContractStatus(props.item.fleetAssetId)
                  ? 'success active-content'
                  : 'error inactive-content'
              "
            ></span>
          </td>
        </tr>
      </template>
    </v-data-table>

    <FleetAssetDialog
      :isFleetAssetOwner="true"
      :fleetAssetOwnerId="fleetAssetOwnerId"
      :fleetAssetOwnerAssociatedFleetAssetIds="fleetAssetIds"
      @addFleetAssetToAssociatedFleetAssets="
        addFleetAssetToAssociatedFleetAssets
      "
    />
  </v-layout>
</template>

<script setup lang="ts">
import FleetAssetDialog from '@/components/admin/FleetAsset/fleet_asset_dialog.vue';
import HireContract from '@/interface-models/FleetAsset/EquipmentHire/HireContract';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import {
  TrailerType,
  trailerTypes,
} from '@/interface-models/Generic/TrailerTypes/TrailerTypes';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import Mitt from '@/utils/mitt';
import moment from 'moment-timezone';
import {
  ComputedRef,
  Ref,
  computed,
  onBeforeUnmount,
  onMounted,
  ref,
} from 'vue';

interface AssetsTable {
  fleetAssetId: string;
  csrAssignedId: string;
  registrationNumber: string;
  defaultDriver: string;
  otherDrivers: number;
  class: string;
  status: string;
}

const props = withDefaults(
  defineProps<{
    isEdited?: boolean;
    fleetAssetTypeId?: number;
    fleetAssetIds: string[];
    fleetAssetOwnerId: string;
  }>(),
  {
    isEdited: false,
    fleetAssetTypeId: 1,
    fleetAssetIds: () => [],
  },
);

const fleetAssetStore = useFleetAssetStore();

const refreshKey: Ref<number> = ref(0);

const assets: ComputedRef<AssetsTable[]> = computed(() => {
  if (!props.fleetAssetOwnerId) {
    return [];
  }
  // Track refreshKey to ensure reactivity
  refreshKey.value;

  const fleetAssetSummaries = props.fleetAssetIds
    .map((fid) => fleetAssetStore.getFleetAssetFromFleetAssetId(fid))
    .filter(
      (fa): fa is FleetAssetSummary =>
        fa !== undefined && fa.fleetAssetTypeId === props.fleetAssetTypeId,
    ) as FleetAssetSummary[];

  return fleetAssetSummaries.map((x: FleetAssetSummary) => {
    const doNotUse: boolean = x.isRetired;
    const active: boolean = x.isActive;
    let trailerTypeName = '';
    let driverName: string = '';
    let otherDrivers = 0;

    if (props.fleetAssetTypeId === 1) {
      const driver = useDriverDetailsStore().getDriverFromDriverId(
        x.defaultDriver,
      );
      driverName = driver?.displayName ?? 'No Default Driver';
      otherDrivers = x.defaultDriver
        ? x.associatedDrivers.filter((d) => d !== x.defaultDriver).length
        : x.associatedDrivers.length;
    } else if (props.fleetAssetTypeId === 2) {
      const trailerType = trailerTypes.find(
        (trailer: TrailerType) => trailer.id === x.trailerType,
      );
      trailerTypeName = trailerType ? trailerType.longName : '-';
    }

    return {
      fleetAssetId: x.fleetAssetId,
      csrAssignedId: x.csrAssignedId || '-',
      registrationNumber: x.registrationNumber || '-',
      defaultDriver: driverName,
      otherDrivers,
      class:
        props.fleetAssetTypeId === 1 ? x.truckClass || '-' : trailerTypeName,
      status: doNotUse ? 'Do Not Use' : active ? 'Active' : 'Requires Action',
    };
  });
});

const hireContractStatus = (fleetAssetId: string): boolean => {
  const currentDate = moment().valueOf();
  const hireContract: HireContract | undefined =
    fleetAssetStore.hireContracts.find((x) => x.fleetAssetId === fleetAssetId);

  return hireContract &&
    hireContract.validFromDate &&
    hireContract.validFromDate <= currentDate &&
    hireContract.validToDate &&
    hireContract.validToDate >= currentDate &&
    hireContract.statusList.includes(4)
    ? true
    : false;
};

const headers: ComputedRef<TableHeader[]> = computed(() => {
  const tableHeaders: Array<TableHeader | null> = [
    {
      text: 'Fleet ID',
      align: 'left',
      sortable: true,
      value: 'csrAssignedId',
    },
    {
      text: 'Registration',
      align: 'left',
      sortable: true,
      value: 'registrationNumber',
    },
    props.fleetAssetTypeId === 1
      ? {
          text: 'Default Driver',
          align: 'left',
          sortable: true,
          value: 'defaultDriver',
        }
      : null,
    props.fleetAssetTypeId === 1
      ? {
          text: 'Secondary Drivers',
          align: 'left',
          sortable: true,
          value: 'otherDrivers',
        }
      : null,
    {
      text: 'Class',
      align: 'left',
      sortable: true,
      value: 'truckClass',
    },
    {
      text: 'Status',
      align: 'left',
      value: 'status',
      sortable: false,
    },
  ];

  if (props.fleetAssetTypeId === 2) {
    tableHeaders.push({
      text: 'Contract',
      align: 'left',
      value: '',
      sortable: false,
    });
  }

  return tableHeaders.filter((h) => h !== null) as TableHeader[];
});

const addNewAsset = () => {
  if (props.fleetAssetTypeId === 1) {
    fleetAssetStore.setSelectedFleetAssetId('new');
  } else if (props.fleetAssetTypeId === 2) {
    fleetAssetStore.setSelectedFleetAssetTrailerId('new');
  }
};

function viewFleetAssetDialog(fleetAssetId: string) {
  if (props.fleetAssetTypeId === 1) {
    fleetAssetStore.setSelectedFleetAssetId(fleetAssetId);
  } else if (props.fleetAssetTypeId === 2) {
    fleetAssetStore.setSelectedFleetAssetTrailerId(fleetAssetId);
  }
}

function addFleetAssetToAssociatedFleetAssets(fleetAssetId: string) {
  if (!props.fleetAssetIds.includes(fleetAssetId)) {
    props.fleetAssetIds.push(fleetAssetId);
  }
}

/**
 * Because the computed assets getter in this component is not reactive to nested field changes, it
 * causes some updates to statusList to not be displayed until the component is re-rendered. To get
 * around this there is a refreshKey variable that is updated here and referenced in the getter; forcing
 * the recomputation of the assets getter.
 *
 * @param {boolean} createListener - Whether the listener should be created or destroyed.
 * @returns {void}
 */
const listenForSubcontractorEntityUpdates = (createListener: boolean): void => {
  if (!createListener) {
    Mitt.off('updateSubcontractorEntityAssociationResponse');
  }
  Mitt.on('updateSubcontractorEntityAssociationResponse', () => {
    // update refreshKey to recompute assets getter
    refreshKey.value++;
  });
};

onBeforeUnmount(() => {
  listenForSubcontractorEntityUpdates(false);
});

onMounted(() => {
  listenForSubcontractorEntityUpdates(true);
});
</script>

<style scoped lang="scss">
.status-container {
  padding: 2px 8px;
  letter-spacing: 0.03em;
  font-size: $font-size-13;
  font-weight: 500;
  border-radius: $border-radius-sm;
}

.add-asset-btn {
  position: absolute;
  top: 1px;
  right: 10px;
  z-index: 199 !important;
}
</style>
