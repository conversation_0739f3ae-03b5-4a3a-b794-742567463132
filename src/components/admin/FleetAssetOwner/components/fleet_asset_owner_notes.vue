<template>
  <v-layout class="fleet-asset-owner-notes-container">
    <v-flex md8 offset-md2>
      <v-layout>
        <v-flex md12 pb-3
          ><v-layout align-center>
            <h5 class="subheader--bold pr-3 pt-1">Notes</h5>
            <v-flex>
              <v-divider></v-divider>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
      <v-layout>
        <NotesEditor
          :communications="notes"
          :isEdited="isEdited"
          @saveParentDocument="saveParentDocument"
          :type="0"
          :emitSaveEventBackToParent="true"
          @setIsAddingNote="setNotesEdited"
          :isAddingNote="isEdited"
        />
      </v-layout>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import NotesEditor from '@/components/common/notes_editor/notes_editor.vue';
import Communication from '@/interface-models/Generic/Communication/Communication';

const props = withDefaults(
  defineProps<{
    notes: Communication[];
    isEdited?: boolean;
  }>(),
  {
    isEdited: false,
  },
);

const emit = defineEmits(['setEdited', 'saveParentDocument']);

function setNotesEdited(isEdited: boolean) {
  emit('setEdited', isEdited);
}

const saveParentDocument = () => {
  emit('saveParentDocument');
};
</script>

<style scoped>
.add-communication-btn {
  position: absolute;
  top: 1px;
  left: 0px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  z-index: 230 !important;
}
</style>
