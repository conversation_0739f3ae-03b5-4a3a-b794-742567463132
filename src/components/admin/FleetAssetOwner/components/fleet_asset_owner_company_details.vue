<template>
  <div class="fleet-asset-owner-company-details-container pb-4">
    <v-layout wrap>
      <v-flex offset-md1 md10>
        <RateExpirationSummaryAlert
          :expirationSummaries="fleetAssetOwner.allRateExpirationSummaries"
          :relatedSummaries="fleetAssetOwner.relatedExpirationSummaries"
        ></RateExpirationSummaryAlert>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">1. Company Trading Details</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Company / Business / Trading Name:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              v-model="fleetAssetOwner.name"
              :disabled="!isEdited"
              solo
              flat
              color="light-blue"
              :rules="[validate.required]"
              class="form-field-required v-solo-custom"
              autofocus
            />
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Active Status
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-layout>
              <StatusSelect
                :key="statusListKey"
                label="Secondary Status"
                :statusCategory="8"
                :boxInput="false"
                :soloInput="true"
                :statusList="fleetAssetOwner.statusList"
                :resetSelectedSecondaryStatus="false"
                :formDisabled="true"
              >
              </StatusSelect>
              <SubcontractorUpdateOperationalStatus
                :isEdited="isEdited"
                :entityId="fleetAssetOwner.ownerId"
                :entityType="SubcontractorEntityType.FLEET_ASSET_OWNER"
                :statusList="fleetAssetOwner.statusList"
              />
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0r">
                Australian Business Number:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              solo
              flat
              label="ABN"
              class="v-solo-custom"
              v-model="fleetAssetOwner.abn"
              :rules="[validate.mobile]"
              v-mask="'## ### ### ###'"
              color="light-blue"
              :disabled="!isEdited"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                Australian Company Number:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              v-model="fleetAssetOwner.acn"
              :rules="[validate.numbers]"
              solo
              class="v-solo-custom"
              flat
              v-mask="'### ### ###'"
              :disabled="!isEdited"
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Affiliation:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              solo
              :class="
                !isEdited || fleetAssetOwner.affiliation == ''
                  ? 'solo-input-disable-display'
                  : ''
              "
              flat
              v-model="fleetAssetOwner.affiliation"
              item-text="longName"
              :rules="[validate.required]"
              class="v-solo-custom"
              item-value="id"
              :items="AffiliationTypes"
              persistent-hint
              :disabled="
                !isEdited ||
                (fleetAssetOwner.affiliation !== undefined &&
                  fleetAssetOwner._id !== undefined &&
                  fleetAssetOwner._id !== null &&
                  !isAuthorisedAdmin())
              "
              :hint="
                !isAuthorisedAdmin()
                  ? `Once affiliation is set it can not be changed. Please contact GoDesta support with any issues.`
                  : `Once affiliation is set it can not be changed. ADMIN ONLY: Before changing affiliation, ensure that all Fleet Assets associated with props owner have no active work. If changing to or from Outside Hire, ensure all Fleet Assets are updated accordingly.`
              "
              color="light-blue"
            >
            </v-select>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">2. Proprietor Details</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Proprietor Name:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              solo
              flat
              class="v-solo-custom"
              v-model="fleetAssetOwner.proprietorName"
              :disabled="!isEdited"
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout class="pt-2">
          <v-flex md4 class="address-label-container">
            <v-layout align-center class="address-label">
              <h6 class="subheader--faded pr-3 pb-0">Address of Proprietor:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <AddressSearchAU
              :boxInput="false"
              :soloInput="true"
              :address="fleetAssetOwner.proprietorLocationAddress"
              :label="''"
              :formDisabled="!isEdited"
              :enableNicknamedAddress="false"
              :enableSuburbSelect="false"
              :enableReturnToDefaultDispatchAddress="false"
            >
            </AddressSearchAU>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">3. Contact Details</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Primary Contact Name:</h6>
            </v-layout>
          </v-flex>
          <v-flex md4 pr-1>
            <v-text-field
              solo
              flat
              label="First Name"
              persistent-hint
              hint="The primary contact at props Company"
              v-model="fleetAssetOwner.contactFirstName"
              class="v-solo-custom"
              :disabled="!isEdited"
              color="light-blue"
            ></v-text-field>
          </v-flex>
          <v-flex md4 pl-1>
            <v-text-field
              solo
              flat
              label="Last Name"
              v-model="fleetAssetOwner.contactLastName"
              class="v-solo-custom"
              :disabled="!isEdited"
              color="light-blue"
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Email Address:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              solo
              flat
              v-model="fleetAssetOwner.email"
              :rules="[validate.required, validate.email]"
              class="form-field-required v-solo-custom"
              :disabled="!isEdited"
              color="light-blue"
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Phone Number:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              solo
              flat
              class="v-solo-custom"
              v-model="fleetAssetOwner.phone"
              v-mask="'#### ### ###'"
              :disabled="!isEdited"
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Mobile Number:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              v-model="fleetAssetOwner.mobile"
              :rules="[validate.mobile]"
              solo
              class="v-solo-custom"
              flat
              label="Mobile Phone Number"
              v-mask="'#### ### ###'"
              :disabled="!isEdited"
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">
            4. Address / Billing Address
          </h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout class="pt-2">
          <v-flex md4 class="address-label-container">
            <v-layout align-center class="address-label">
              <h6 class="subheader--faded pr-3 pb-0">Location Address:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <AddressSearchAU
              :boxInput="false"
              :soloInput="true"
              :address="fleetAssetOwner.locationAddress"
              :label="''"
              :formDisabled="!isEdited"
              :enableNicknamedAddress="false"
              :enableSuburbSelect="false"
              :enableReturnToDefaultDispatchAddress="false"
            >
            </AddressSearchAU>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4 class="address-label-container">
            <v-layout align-center class="address-label">
              <h6 class="subheader--faded pr-3 pb-0">Billing Address:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-radio-group
              v-model="locationAddressIsBillingAddress"
              :disabled="!isEdited || disableAddressRadioGroup"
              :mandatory="true"
              hide-details
              class="billing-address-radio-group-container mt-0"
            >
              <v-radio
                label="Same as location address"
                value="true"
                class="ma-0 pa-3"
              ></v-radio>
              <v-divider />
              <v-radio
                label="Use a different billing address"
                value="false"
                class="ma-0 pa-3"
              ></v-radio>
            </v-radio-group>
          </v-flex>
        </v-layout>
        <v-expand-transition v-if="locationAddressIsBillingAddress === 'false'">
          <v-flex md8 offset-md4>
            <v-checkbox
              v-model="isPOBoxAddress"
              :disabled="!isEdited"
              class="pl-1"
            >
              <template v-slot:label>
                <div>Billing address is a PO Box</div>
              </template>
            </v-checkbox>
            <AddressSearchAU
              :boxInput="false"
              :soloInput="true"
              :formDisabled="!isEdited"
              v-if="!isPOBoxAddress"
              :address="fleetAssetOwner.paymentAddress"
              :label="'Location Address'"
              :enableNicknamedAddress="false"
              :enableSuburbSelect="false"
              :enableReturnToDefaultDispatchAddress="false"
            />
          </v-flex>
        </v-expand-transition>

        <v-expand-transition v-if="isPOBoxAddress">
          <v-flex md12>
            <POBoxAddressAUComponent
              :formDisabled="!isEdited"
              :poBoxAddress="fleetAssetOwner.pobAddress"
              :validate="validate"
            />
          </v-flex>
        </v-expand-transition>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">5. Billing</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Billing Cycle:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              v-model="fleetAssetOwner.operationDetails.billingCycleId"
              :items="billingCycles"
              item-text="longName"
              item-value="id"
              class="v-solo-custom"
              :rules="[validate.required]"
              :disabled="!isEdited"
              solo
              flat
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">
            6. Outgoing Job Update Emails
          </h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
          <span class="pl-2">
            <InformationTooltip :left="true" :tooltipType="HealthLevel.INFO">
              <v-layout slot="content" row wrap>
                <v-flex md12>
                  <p class="mb-1">
                    <strong>Job Update Emails</strong> will be sent to the Fleet
                    Asset Owner when certain job updates are triggered.
                  </p>
                  <p class="mb-1">Job Update emails will be sent when:</p>
                  <ul>
                    <li>
                      The Fleet Asset Owner has a valid email address set (see
                      Contact Details section above),
                    </li>
                    <li>
                      Fleet Assets owned by props Owner are allocated to a job,
                    </li>
                    <li>
                      The specified Update type is checked (see below),
                      indicating that emails should be sent
                    </li>
                  </ul>
                </v-flex>
              </v-layout>
            </InformationTooltip>
          </span>
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Allocated to Driver:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              v-model="sendEmailJobAllocated"
              :disabled="!isEdited"
              label="Allocated to Driver"
              color="light-blue"
              class="mt-2"
              persistent-hint
              hint="An email will be sent to the Fleet Asset Owner when a job is Allocated."
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">
            7. Signup / Commencement Dates
          </h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Signup Date:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <DateTimeInputs
              :epochTime.sync="fleetAssetOwner.operationDetails.signupDate"
              :enableValidation="true"
              :type="DateTimeType.DATE_START_OF_DAY"
              dateLabel="Signup Date"
              :readOnly="!isEdited"
              :soloInput="true"
              :boxInput="false"
              :hintTextType="HintTextType.FORMATTED_SELECTION"
            ></DateTimeInputs>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Commencement Date:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <DateTimeInputs
              :epochTime.sync="
                fleetAssetOwner.operationDetails.commencementDate
              "
              :enableValidation="true"
              :type="DateTimeType.DATE_START_OF_DAY"
              :readOnly="!isEdited"
              :soloInput="true"
              :boxInput="false"
              :hintTextType="HintTextType.FORMATTED_SELECTION"
            ></DateTimeInputs>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import AddressSearchAU from '@/components/common/addressing/address-search-au/index.vue';
import POBoxAddressAUComponent from '@/components/common/addressing/po-box-au/index.vue';
import DateTimeInputs, {
  DateTimeType,
  HintTextType,
} from '@/components/common/date-picker/date_time_inputs.vue';
import StatusSelect from '@/components/common/status_select.vue';
import SubcontractorUpdateOperationalStatus from '@/components/common/subcontractor_update_operational_status/index.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import RateExpirationSummaryAlert from '@/components/common/ui-elements/rate_expiration_summary_alert.vue';
import { hasAdminRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { FleetAssetOwner } from '@/interface-models/FleetAssetOwner/FleetAssetOwner';
import { SubcontractorAssociationUpdateResponse } from '@/interface-models/FleetAssetOwner/SubcontractorAssociation/SubcontractorAssociationUpdateResponse';
import AddressAU from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/AddressAU';
import POBoxAddressAU from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/POBoxAddressAU';
import AffiliationTypes from '@/interface-models/Generic/Affiliation/Affiliations';
import { billingCycles } from '@/interface-models/Generic/BillingCycles/BillingCycles';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { Validation } from '@/interface-models/Generic/Validation';
import { OperationStatus } from '@/interface-models/Generic/WebSocketRequest/OperationStatus';
import { SubcontractorEntityType } from '@/interface-models/InvoiceAdjustment/EntityTypes/SubcontractorEntityType';
import { useMittListener } from '@/utils/useMittListener';
import { computed, ComputedRef, WritableComputedRef } from 'vue';

const props = withDefaults(
  defineProps<{
    fleetAssetOwner: FleetAssetOwner;
    isEdited?: boolean;
  }>(),
  {
    isEdited: false,
  },
);

// const affiliationTypes: Ref<AffiliationTypes[]> = ref();

const validate: ComputedRef<Validation> = computed(() => {
  return validationRules;
});

// Check if we are authorised to see selects related to compliance
const isAuthorisedAdmin = () => {
  return hasAdminRole();
};

const sendEmailJobAllocated = computed(() => {
  return (
    props.fleetAssetOwner.sendEmailsConfig &&
    props.fleetAssetOwner.sendEmailsConfig.sendEmailJobAllocated
  );
});

const locationAddressIsBillingAddress: WritableComputedRef<string> = computed({
  get(): string {
    return props.fleetAssetOwner.locationAddress.addressId === '' ||
      props.fleetAssetOwner.locationAddress.addressId ===
        props.fleetAssetOwner.paymentAddress.addressId
      ? 'true'
      : 'false';
  },
  set(value: string): void {
    if (value === 'true') {
      props.fleetAssetOwner.paymentAddress =
        props.fleetAssetOwner.locationAddress;
      props.fleetAssetOwner.pobAddress = null;
    } else {
      props.fleetAssetOwner.paymentAddress = new AddressAU();
      props.fleetAssetOwner.pobAddress = null;
    }
  },
});

const isPOBoxAddress: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.fleetAssetOwner.pobAddress !== null;
  },
  set(value: boolean): void {
    if (value && locationAddressIsBillingAddress.value === 'false') {
      props.fleetAssetOwner.pobAddress = new POBoxAddressAU();
    } else {
      props.fleetAssetOwner.pobAddress = null;
    }
  },
});

const disableAddressRadioGroup: ComputedRef<boolean> = computed(() => {
  return (
    props.fleetAssetOwner.locationAddress.addressLine1 === '' &&
    props.fleetAssetOwner.locationAddress.addressLine2 === '' &&
    props.fleetAssetOwner.locationAddress.suburb === ''
  );
});

/**
 * Used as key for StatusSelect component to force re-render when statusList is
 * updated
 */
const statusListKey: ComputedRef<string> = computed(() => {
  return `${props.fleetAssetOwner.statusList}`;
});

/**
 * Handles response to status update. Used to update the statusList in the
 * fleetAssetOwner prop.
 * @param response - contains properties from updated document, including the
 * updated statusList which we'll set to props.fleetAssetOwner
 */
function handleSubcontractorEntityUpdate(
  response: SubcontractorAssociationUpdateResponse | null,
): void {
  if (
    response?.entityType === SubcontractorEntityType.FLEET_ASSET_OWNER &&
    response.ownerId === props.fleetAssetOwner.ownerId &&
    response.operationStatus === OperationStatus.SUCCESS &&
    !!response.statusList
  ) {
    props.fleetAssetOwner.statusList = response.statusList;
  }
}

useMittListener(
  'updateSubcontractorEntityAssociationResponse',
  handleSubcontractorEntityUpdate,
);
</script>

<style scoped lang="scss">
.address-label {
  width: 100%;
  height: 48px;
}

.address-label-container {
  padding-top: 28px;
}

.label-container {
  height: 48px;
}

.location-billing-address-checkbox-container {
  border: 1px solid $secondary;
  border-radius: 2px;
}

.billing-address-radio-group-container {
  width: 100%;
  display: flex;
  display: block;
  align-items: stretch;
  border: 1px solid $secondary;
  border-radius: 2px;
}
</style>
