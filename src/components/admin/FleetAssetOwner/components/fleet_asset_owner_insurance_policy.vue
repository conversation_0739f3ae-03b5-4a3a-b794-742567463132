<template>
  <div class="insurance-policy-container">
    <div class="add-policy-btn">
      <v-btn depressed color="blue" small @click="newInsurancePolicy">
        Add Policy
      </v-btn>
    </div>

    <v-data-table
      class="gd-dark-theme"
      :headers="headers"
      :items="insurancePolicies"
      :rows-per-page-items="[10, 20]"
    >
      <template v-slot:items="props">
        <tr @click="editInsurancePolicy(props.item)" style="cursor: pointer">
          <td>{{ props.item.insurerName }}</td>
          <td>{{ policyTypeName(props.item.policyType) }}</td>
          <td>{{ props.item.policyNumber }}</td>
          <td>{{ readableDate(props.item.validToDate) }}</td>
          <td>
            {{
              props.item.isRetired == null ||
              props.item.isRetired == undefined ||
              !props.item.isRetired
                ? 'ACTIVE'
                : 'RETIRED'
            }}
          </td>
        </tr>
      </template>
    </v-data-table>

    <v-dialog
      v-if="insurancePolicy"
      v-model="insurancePolicyDialogIsOpen"
      content-class="v-dialog-custom"
      width="600px"
    >
      <v-layout
        justify-space-between
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>Insurance Policy</span>

        <div
          class="app-theme__center-content--closebutton"
          @click="cancelInsurancePolicy"
          :style="isLoading ? 'pointer-events: none;' : ''"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <v-layout
        row
        wrap
        class="edit-insurance-policy-container app-theme__center-content--body dialog-content"
      >
        <v-flex md12>
          <v-form ref="insurancePolicyForm">
            <v-layout
              row
              wrap
              class="body-scrollable--65 body-min-height--65"
              pa-3
            >
              <v-flex md12 pb-3
                ><v-layout align-center>
                  <h5 class="subheader--bold pr-3 pt-1">1. Key Details</h5>
                  <v-flex>
                    <v-divider></v-divider>
                  </v-flex>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-layout>
                  <v-flex md4>
                    <v-layout align-center class="form-field-label-container">
                      <h6
                        class="subheader--faded pr-3 pb-0 form-field-required-marker"
                      >
                        Insurer Name:
                      </h6>
                    </v-layout>
                  </v-flex>
                  <v-flex md8>
                    <v-text-field
                      class="v-solo-custom"
                      solo
                      flat
                      v-model.trim="insurancePolicy.insurerName"
                      color="light-blue"
                      :rules="[validate.required]"
                      autofocus
                    ></v-text-field>
                  </v-flex>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-layout>
                  <v-flex md4>
                    <v-layout align-center class="form-field-label-container">
                      <h6
                        class="subheader--faded pr-3 pb-0 form-field-required-marker"
                      >
                        Policy Type:
                      </h6>
                    </v-layout>
                  </v-flex>
                  <v-flex md8>
                    <v-select
                      class="v-solo-custom"
                      solo
                      flat
                      :items="policiesTypes"
                      item-text="name"
                      color="light-blue"
                      :rules="[validate.required]"
                      item-value="id"
                      v-model="insurancePolicy.policyType"
                    >
                    </v-select>
                  </v-flex>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-layout>
                  <v-flex md4>
                    <v-layout align-center class="form-field-label-container">
                      <h6
                        class="subheader--faded pr-3 pb-0 form-field-required-marker"
                      >
                        Policy Number:
                      </h6>
                    </v-layout>
                  </v-flex>
                  <v-flex md8>
                    <v-text-field
                      class="v-solo-custom"
                      solo
                      flat
                      v-model.trim="insurancePolicy.policyNumber"
                      color="light-blue"
                      :rules="[validate.required]"
                      autofocus
                    ></v-text-field>
                  </v-flex>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-layout>
                  <v-flex md4>
                    <v-layout align-center class="form-field-label-container">
                      <h6
                        class="subheader--faded pr-3 pb-0 form-field-required-marker"
                      >
                        Valid From:
                      </h6>
                    </v-layout>
                  </v-flex>
                  <v-flex md8>
                    <DateTimeInputs
                      :epochTime.sync="insurancePolicy.validFromDate"
                      :enableValidation="true"
                      :type="DateTimeType.DATE_START_OF_DAY"
                      dateLabel="Label"
                      :soloInput="true"
                      :boxInput="false"
                      :isRequired="true"
                      :hintTextType="HintTextType.FORMATTED_SELECTION"
                    ></DateTimeInputs>
                  </v-flex>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-layout>
                  <v-flex md4>
                    <v-layout align-center class="form-field-label-container">
                      <h6
                        class="subheader--faded pr-3 pb-0 form-field-required-marker"
                      >
                        Valid To:
                      </h6>
                    </v-layout>
                  </v-flex>
                  <v-flex md8>
                    <DateTimeInputs
                      :epochTime.sync="insurancePolicy.validToDate"
                      :enableValidation="true"
                      :type="DateTimeType.DATE_END_OF_DAY"
                      dateLabel="Label"
                      :soloInput="true"
                      :boxInput="false"
                      :isRequired="true"
                      :hintTextType="HintTextType.FORMATTED_SELECTION"
                    ></DateTimeInputs>
                  </v-flex>
                </v-layout>
              </v-flex>
              <v-flex md12 class="pb-3">
                <v-layout>
                  <v-flex md4>
                    <v-layout align-center class="form-field-label-container">
                      <h6 class="subheader--faded pr-3 pb-0">Retired:</h6>
                    </v-layout>
                  </v-flex>
                  <v-flex md8>
                    <v-layout align-center fill-height>
                      <v-checkbox
                        class="ma-0 pa-0"
                        hide-details
                        v-model="insurancePolicy.isRetired"
                      >
                      </v-checkbox>
                    </v-layout>
                  </v-flex>
                </v-layout>
              </v-flex>
              <v-flex md12 pb-3
                ><v-layout align-center>
                  <h5 class="subheader--bold pr-3 pt-1">
                    2. Supporting Documents
                  </h5>
                  <v-flex>
                    <v-divider></v-divider>
                  </v-flex>
                </v-layout>
              </v-flex>

              <v-flex md12>
                <v-layout>
                  <v-flex md4>
                    <v-layout align-center class="form-field-label-container">
                      <h6 class="subheader--faded pr-3 pb-0">Document:</h6>
                    </v-layout>
                  </v-flex>
                  <v-flex md8>
                    <file-upload
                      :imageLabel="'CERTIFICATE OF CURRENCY'"
                      :attachmentSingle="true"
                      :documentTypeId="
                        attachmentTypes.INSURANCE_CERTIFICATE_OF_CURRENCY
                      "
                      :attachment="insurancePolicy.certificateOfCurrency"
                      :isLoading.sync="isLoading"
                    >
                    </file-upload>
                  </v-flex>
                </v-layout>
              </v-flex>
            </v-layout>
          </v-form>
        </v-flex>
        <v-flex md12>
          <v-divider></v-divider>
          <v-layout justify-space-between>
            <v-btn
              color="error"
              depressed
              outline
              :disabled="isLoading"
              @click="cancelInsurancePolicy"
            >
              <span>cancel</span>
            </v-btn>
            <v-btn
              color="info"
              depressed
              :disabled="isLoading"
              @click="addInsurancePolicy"
            >
              <span>Save Policy</span>
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import DateTimeInputs, {
  DateTimeType,
  HintTextType,
} from '@/components/common/date-picker/date_time_inputs.vue';
import FileUpload from '@/components/common/file-upload/index.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import FleetAssetOwner from '@/interface-models/FleetAssetOwner/FleetAssetOwner';
import Attachment from '@/interface-models/Generic/Attachment/Attachment';
import AttachmentTypes from '@/interface-models/Generic/Attachment/AttachmentTypes';
import { InsurancePolicy } from '@/interface-models/Generic/InsurancePolicy/InsurancePolicy';
import policyTypes, {
  PolicyType,
} from '@/interface-models/Generic/PolicyTypes/policyType';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { Validation } from '@/interface-models/Generic/Validation';
import { useMittListener } from '@/utils/useMittListener';
import { v4 as uuidv4 } from 'uuid';
import { ComputedRef, Ref, WritableComputedRef, computed, ref } from 'vue';

const props = withDefaults(
  defineProps<{
    formDisabled?: boolean;
    insurancePolicies: InsurancePolicy[];
    fleetAssetOwner: FleetAssetOwner;
  }>(),
  {
    formDisabled: false,
  },
);

const insurancePolicy = ref<InsurancePolicy | null>(null);
const attachmentTypes = AttachmentTypes;
const policiesTypes = ref(policyTypes);
const insurancePolicyForm: Ref<any> = ref(null);
const emit = defineEmits(['saveParentDocument']);

const isLoading: Ref<boolean> = ref(false);

const validate: ComputedRef<Validation> = computed(() => validationRules);

const insurancePolicyDialogIsOpen: WritableComputedRef<boolean> = computed({
  get(): boolean {
    if (!insurancePolicy.value) {
      return false;
    }
    return true;
  },
  set(value: boolean): void {
    if (!value) {
      insurancePolicy.value = null;
    }
  },
});

// Headers for the insurance policy table
const headers = ref<TableHeader[]>([
  {
    text: 'Insurer Name',
    align: 'left',
    value: 'InsurerName',
    sortable: false,
  },
  {
    text: 'Policy Type',
    align: 'left',
    sortable: true,
    value: 'policyType',
  },
  {
    text: 'Policy Number',
    align: 'left',
    sortable: true,
    value: 'policyNumber',
  },
  {
    text: 'Expiry Date',
    align: 'left',
    value: 'expiryDate',
    sortable: false,
  },
  {
    text: 'Status',
    align: 'left',
    value: 'isRetired',
    sortable: false,
  },
]);

// Define the method to create a new insurance policy
const newInsurancePolicy = () => {
  insurancePolicy.value = new InsurancePolicy();
  insurancePolicy.value.insuranceId = uuidv4();
};

const editInsurancePolicy = (policy: InsurancePolicy) => {
  insurancePolicy.value = JSON.parse(JSON.stringify(policy));
  if (!insurancePolicy.value) {
    return;
  }
  if (insurancePolicy.value.certificateOfCurrency === null) {
    insurancePolicy.value.certificateOfCurrency = new Attachment();
  }
};

const policyTypeName = (policyId: number): string | undefined => {
  const foundPolicy = policiesTypes.value.find(
    (policy: PolicyType) => policy.id === policyId,
  );
  if (foundPolicy !== undefined) {
    return foundPolicy.name;
  }
};

const readableDate = (epoch: number): string => {
  return returnFormattedDate(epoch, 'DD/MM/YY');
};

/**
 * Called when confirm the Insurance Policy from the dialog. Emits to parent to
 * save the full FleetAssetOwner document.
 */
const addInsurancePolicy = (): void => {
  if (!insurancePolicy.value || !insurancePolicyForm.value.validate()) {
    return;
  }
  const foundIndex = props.insurancePolicies.findIndex(
    (policy: InsurancePolicy) =>
      policy.insuranceId === insurancePolicy.value?.insuranceId,
  );

  if (foundIndex !== -1) {
    props.insurancePolicies.splice(foundIndex, 1, insurancePolicy.value);
  } else {
    props.insurancePolicies.push(insurancePolicy.value);
  }

  isLoading.value = true;
  emit('saveParentDocument');
};

const cancelInsurancePolicy = (): void => {
  isLoading.value = false;
  insurancePolicy.value = null;
};

// When an insurance policy is saved we save the full fleet asset owner document. When the response is available we can close the insurance policy maintenance dialog.
const setSavedFleetAssetOwner = (assetOwner: FleetAssetOwner | null) => {
  if (
    !assetOwner ||
    !isLoading.value ||
    assetOwner.ownerId !== props.fleetAssetOwner.ownerId
  ) {
    return;
  }
  cancelInsurancePolicy(); // Assuming cancelInsurancePolicy is defined somewhere
};

useMittListener('selectedFleetAssetOwner', setSavedFleetAssetOwner);
</script>

<style scoped>
.add-policy-btn {
  position: absolute;
  top: 1px;
  right: 10px;
  z-index: 200 !important;
}
</style>
