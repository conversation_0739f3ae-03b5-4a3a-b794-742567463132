<template>
  <v-layout wrap v-if="fleetAssetOwner" class="fleet-asset-owner-index">
    <v-flex md12>
      <v-layout
        justify-space-between
        v-if="isDialog"
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span><span>Fleet Asset Owner - </span>{{ fleetAssetOwnerName }}</span>

        <div
          class="app-theme__center-content--closebutton"
          :class="{ 'disable-pointer-events': isEdited }"
          @click="closeFleetAssetOwnerDialog"
        >
          <v-icon
            :disabled="isEdited"
            class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
    </v-flex>

    <v-flex md12>
      <v-layout
        class="app-theme__center-content--body dialog-content"
        row
        wrap
        :class="isDialog ? 'main-content-dialog' : 'main-content-route'"
      >
        <v-flex
          lg3
          md4
          class="side-column app-bgcolor--400 app-bordercolor--600 app-borderside--r-shadow"
        >
          <v-layout row wrap>
            <v-flex
              md12
              class="app-bgcolor--250 app-bordercolor--600 app-borderside--b"
            >
              <v-layout row wrap py-2 px-2>
                <v-flex md12>
                  <v-form>
                    <SelectEntity
                      :key="computedEntityKey"
                      :entityTypes="[
                        EntityType.DRIVER,
                        EntityType.FLEET_ASSET,
                        EntityType.FLEET_ASSET_OWNER,
                      ]"
                      v-if="!isDialog"
                      :isRouteSelect="true"
                      :disabled="isEdited || !fleetAssetOwner._id"
                      class="mb-3"
                    />
                  </v-form>
                </v-flex>

                <v-flex md12 class="side-column__summaryitem">
                  <v-layout justify-space-between align-start>
                    <span class="side-column__summaryitem--key"> Status </span>
                    <span
                      class="side-column__summaryitem--value status-container"
                      :class="currentStatus.color"
                    >
                      {{ currentStatus.statusName }}
                    </span>
                  </v-layout>
                </v-flex>

                <v-flex
                  md12
                  class="side-column__summaryitem"
                  v-if="summaryInfoList"
                >
                  <v-layout
                    v-for="infoItem in summaryInfoList"
                    :key="infoItem.id"
                    justify-space-between
                    align-start
                  >
                    <span class="side-column__summaryitem--key">{{
                      infoItem.title
                    }}</span>
                    <span class="side-column__summaryitem--value">{{
                      infoItem.value
                    }}</span>
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>

            <v-flex md12 v-for="menuOption in menuOptions" :key="menuOption.id">
              <v-layout row wrap>
                <v-flex md12 class="app-bordercolor--600 app-borderside--b">
                  <v-layout>
                    <h5 class="subheader--bold--12 px-3 pt-3 pb-1">
                      {{ menuOption.title }}
                    </h5>
                  </v-layout>
                </v-flex>
                <v-flex
                  md12
                  v-for="menuItem in menuOption.items"
                  :key="menuItem.id"
                  class="app-bgcolor--300 app-bordercolor--600 app-borderside--b side-column__button"
                  :class="[
                    selectedViewType === menuItem.id ? 'active-state' : '',
                    menuItem.isActive
                      ? 'menu-item-selectable'
                      : 'menu-item-disabled',
                  ]"
                  @click="setSelectedView(menuItem.id)"
                >
                  <v-layout align-center>
                    <span class="button-label"
                      ><span class="pr-2">-</span>{{ menuItem.title }}</span
                    >
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>

            <v-flex
              md12
              v-if="isAuthorisedToViewLedger()"
              class="app-bgcolor--300 app-bordercolor--600 app-borderside--b side-column__button"
              :class="[
                !isEdited ? 'menu-item-selectable' : 'menu-item-disabled',
                !isAuthorisedToViewLedger() ? 'disable-pointer-events' : '',
              ]"
              @click="setInvoicingSearchDialog(true)"
            >
              <v-layout justify-space-between align-center
                ><span class="button-label"
                  ><span class="pr-2">-</span>INVOICE HISTORY</span
                >
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex lg9 md8 class="dialog-content__scrollable">
          <v-layout
            class="dialog-toolbar center-section__top-toolbar"
            style="position: relative"
            align-center
          >
            <v-btn
              depressed
              v-if="isEdited"
              @click="findAndSetSelectedFleetAssetOwner"
              outline
              color="error"
              small
            >
              Cancel
            </v-btn>

            <v-btn
              depressed
              v-if="!isEdited && !isDialog"
              @click="goToSubcontractorIndexRoute"
              outline
              color="error"
              small
            >
              exit
            </v-btn>

            <v-spacer />
            <v-btn
              :disabled="!isAuthorised()"
              depressed
              color="blue"
              small
              v-if="!isEdited && selectedView?.isEditable"
              @click="setEdited(true)"
            >
              {{ selectedView.actionBtnText }}
            </v-btn>
            <v-btn
              v-if="isEdited"
              depressed
              color="blue"
              small
              :loading="awaitingFleetAssetOwnerSaveResponse"
              :disabled="awaitingFleetAssetOwnerSaveResponse"
              @click="saveFleetAssetOwner"
            >
              Save
            </v-btn>
          </v-layout>

          <v-layout
            class="scrollable"
            :key="fleetAssetOwner ? fleetAssetOwner.ownerId : 'new'"
          >
            <v-form
              ref="ownerForm"
              style="width: 100%"
              v-if="selectedView?.isForm"
            >
              <v-layout fill-height>
                <v-flex lg8 md10 offset-lg2 offset-md1 class="pt-3">
                  <FleetAssetOwnerCompanyDetails
                    v-if="selectedViewType === 'COM'"
                    :isEdited="isEdited"
                    :fleetAssetOwner="fleetAssetOwner"
                    class="px-3"
                  />
                  <FleetAssetOwnerOperationDetails
                    v-if="selectedViewType === 'OPD'"
                    :isEdited="isEdited"
                    :gstDetails="fleetAssetOwner.gstDetails"
                    :operationDetails="fleetAssetOwner.operationDetails"
                    :bankingDetails="fleetAssetOwner.bankingDetails"
                  />
                </v-flex>
              </v-layout>
            </v-form>
            <v-flex md12 v-if="!selectedView?.isForm" class="pa-3">
              <FleetAssetOwnerDriversTable
                v-if="selectedViewType === 'DRI'"
                :fleetAssetOwner="fleetAssetOwner"
                :fleetAssetOwnerId="fleetAssetOwner.ownerId"
              />
              <FleetAssetOwnerAssetsTable
                :key="selectedViewType"
                v-if="selectedViewType === 'TRU' || selectedViewType === 'TRA'"
                :fleetAssetIds="fleetAssetOwner.associatedFleetAssets"
                :fleetAssetOwnerId="fleetAssetOwner.ownerId"
                @saveFleetAssetOwner="saveFleetAssetOwner"
                :fleetAssetTypeId="selectedViewType === 'TRU' ? 1 : 2"
              />
              <FleetAssetOwnerInsurancePolicy
                v-if="selectedViewType === 'INS'"
                :fleetAssetOwner="fleetAssetOwner"
                :insurancePolicies="fleetAssetOwner.insurances"
                @saveParentDocument="saveFleetAssetOwner"
                :isEdited="isEdited"
              />

              <FleetAssetOwnerAdjustmentCharges
                v-if="selectedViewType === 'ADJC'"
                :isEdited="isEdited"
                :devices="fleetAssetOwner.associatedDevices"
                :driverIds="fleetAssetOwner.associatedDrivers"
                :fleetAssetIds="fleetAssetOwner.associatedFleetAssets"
                :fleetAssetOwnerId="fleetAssetOwner.ownerId"
              />

              <FleetAssetOwnerDevices
                v-if="selectedViewType === 'DEVI'"
                :fleetAssetOwnerId="fleetAssetOwner.ownerId"
                :formDisabled="false"
                :driverIds="fleetAssetOwner.associatedDrivers"
                :fleetAssetIds="fleetAssetOwner.associatedFleetAssets"
                :devices="fleetAssetOwner.associatedDevices"
              />

              <FleetAssetOwnerNotes
                v-if="selectedViewType === 'NOT'"
                :notes="fleetAssetOwner.notes"
                @setEdited="setEdited"
                :isEdited="isEdited"
                @saveParentDocument="saveFleetAssetOwner"
              />

              <SubcontractorActiveAssociations
                v-if="selectedViewType === 'CRW'"
                :entityId="fleetAssetOwner.ownerId"
                :entityType="subcontractorEntityType"
              />
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>

    <v-dialog
      v-model="isViewingInvoiceSearchDialog"
      v-if="isViewingInvoiceSearchDialog"
      width="85vw"
      :transition="false"
      content-class="v-dialog-custom"
      persistent
    >
      <v-layout
        justify-space-between
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>Invoice Search</span>

        <div
          class="app-theme__center-content--closebutton"
          @click="isViewingInvoiceSearchDialog = false"
        >
          <v-icon size="14" class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <v-layout
        class="app-theme__center-content--body pa-3 invoice-search-dialog"
        style="max-height: 90vh"
      >
        <SearchLedger
          :isFleetAssetOwner="true"
          :fleetAssetOwnerId="fleetAssetOwner.ownerId"
        />
      </v-layout>
    </v-dialog>
  </v-layout>
</template>

<script setup lang="ts">
import SearchLedger from '@/components/admin/Accounting/ledger/components/search_ledger/index.vue';
import FleetAssetOwnerAdjustmentCharges from '@/components/admin/FleetAssetOwner/components/fleet_asset_owner_adjustment_charges.vue';
import FleetAssetOwnerAssetsTable from '@/components/admin/FleetAssetOwner/components/fleet_asset_owner_assets_table.vue';
import FleetAssetOwnerCompanyDetails from '@/components/admin/FleetAssetOwner/components/fleet_asset_owner_company_details.vue';
import FleetAssetOwnerDevices from '@/components/admin/FleetAssetOwner/components/fleet_asset_owner_devices.vue';
import FleetAssetOwnerDriversTable from '@/components/admin/FleetAssetOwner/components/fleet_asset_owner_drivers_table.vue';
import FleetAssetOwnerInsurancePolicy from '@/components/admin/FleetAssetOwner/components/fleet_asset_owner_insurance_policy.vue';
import FleetAssetOwnerNotes from '@/components/admin/FleetAssetOwner/components/fleet_asset_owner_notes.vue';
import FleetAssetOwnerOperationDetails from '@/components/admin/FleetAssetOwner/components/fleet_asset_owner_operation_details.vue';
import SelectEntity from '@/components/common/inputs/select_entity/index.vue';
import SubcontractorActiveAssociations from '@/components/common/subcontractor_active_associations/index.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole,
  hasAdminOrHeadOfficeRole,
  hasAdminRole,
} from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { initialiseFleetAssetOwner } from '@/helpers/classInitialisers/InitialiseFleetAssetOwner';
import { SaveDriverDetailsResponse } from '@/interface-models/Driver/DriverDetails/DriverDetails';
import { FleetAsset } from '@/interface-models/FleetAsset/FleetAsset';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import { FleetAssetOwner } from '@/interface-models/FleetAssetOwner/FleetAssetOwner';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import {
  AffiliationTypes,
  affiliationTypes,
} from '@/interface-models/Generic/Affiliation/Affiliations';
import { AddDeviceFleetAssetOwnerRequest } from '@/interface-models/Generic/AssociatedDevice/AddDeviceFleetAssetOwnerRequest';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { KeyValuePair } from '@/interface-models/Generic/KeyValue/KeyValue';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { SubcontractorEntityType } from '@/interface-models/InvoiceAdjustment/EntityTypes/SubcontractorEntityType';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useMittListener } from '@/utils/useMittListener';
import {
  ComputedRef,
  Ref,
  computed,
  onBeforeMount,
  onBeforeUnmount,
  onMounted,
  ref,
  watch,
} from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';

interface StatusConfig {
  statusName: string;
  color: string;
}

interface MenuCategory {
  id: string;
  title: string;
  items: FleetAssetOwnerMenu[];
}

interface FleetAssetOwnerMenu {
  id: string;
  title: string;
  isForm: boolean;
  isActive: boolean;
  isEditable: boolean;
  actionBtnText: string;
}

const props = withDefaults(
  defineProps<{
    isDialog?: boolean;
  }>(),
  {
    isDialog: false,
  },
);

const router = useRouter();
const route = useRoute();
const routeId: ComputedRef<string> = computed(() => route.params.id);

const fleetAssetStore = useFleetAssetStore();
const fleetAssetOwnerStore = useFleetAssetOwnerStore();

const componentTitle: string = 'Fleet Asset Owner Administration';
const subcontractorEntityType: SubcontractorEntityType =
  SubcontractorEntityType.FLEET_ASSET_OWNER;

const isViewingInvoiceSearchDialog: Ref<boolean> = ref(false);
const isEdited: Ref<boolean> = ref(false);
const awaitingFleetAssetOwnerSaveResponse: Ref<boolean> = ref(false);
const selectedViewType: Ref<string> = ref('COM');

const fleetAssetOwner: Ref<FleetAssetOwner | null> = ref(null);

const ownerForm: Ref<any> = ref(null);

// re-render select entity component when change in trucks or drivers
const computedEntityKey = computed(() => {
  return `${fleetAssetOwner.value}-${fleetAssetOwner.value?.associatedDriverList}-${fleetAssetStore.selectedFleetAssetId}`;
});

const isAuthorised = (): boolean => {
  return hasAdminOrHeadOfficeRole();
};

const isAuthorisedToViewLedger = (): boolean => {
  return hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole();
};

const isAdmin = (): boolean => {
  return hasAdminRole();
};

const selectedFleetAssetOwnerId: ComputedRef<string | null> = computed(
  () => fleetAssetOwnerStore.selectedFleetAssetOwnerId,
);

watch(selectedFleetAssetOwnerId, () => {
  findAndSetSelectedFleetAssetOwner();
});

const isNewFleetAssetOwner: ComputedRef<boolean> = computed(() => {
  return routeId.value === 'new' || selectedFleetAssetOwnerId.value === 'new';
});

const goToSubcontractorIndexRoute = (): void => {
  router.push({
    name: 'Subcontractor',
  });
};

function findAndSetSelectedFleetAssetOwner(
  isCancelNewFleetAssetOwner: boolean = false,
): void {
  if (!props.isDialog && selectedFleetAssetOwnerId.value !== routeId.value) {
    // Updated value parameter here
    fleetAssetOwnerStore.setSelectedFleetAssetOwnerId(routeId.value);
  }

  isEdited.value = false;
  if (isCancelNewFleetAssetOwner && isNewFleetAssetOwner.value) {
    if (!props.isDialog) {
      goToSubcontractorIndexRoute();
    } else {
      closeFleetAssetOwnerDialog();
    }
    return;
  }

  // Reset any validation issues on inputs.
  if (ownerForm.value) {
    ownerForm.value.resetValidation();
  }

  if (isNewFleetAssetOwner.value) {
    fleetAssetOwner.value = initialiseFleetAssetOwner(new FleetAssetOwner());
    isEdited.value = true;
    return;
  }

  // Confirm that the selected owner is known locally
  const fleetAssetOwnerSummary: FleetAssetOwnerSummary | undefined =
    fleetAssetOwnerStore.getOwnerFromOwnerId(selectedFleetAssetOwnerId.value); // Updated value parameter here
  if (!fleetAssetOwnerSummary) {
    handleUnknownSubcontractor();
    return;
  }
  // Request full Fleet asset owner object
  if (selectedFleetAssetOwnerId.value) {
    requestFleetAssetOwnerDetails(selectedFleetAssetOwnerId.value);
  }
}

/**
 * Request and response for requesting a FleetAssetOwner object by its owner
 * ID. Sets the response to local variables
 * @param ownerId - The owner ID of the FleetAssetOwner object to be
 * retrieved.
 * @returns FleetAssetOwner if the request was successful, otherwise null.
 */
async function requestFleetAssetOwnerDetails(ownerId: string) {
  const response =
    await fleetAssetOwnerStore.requestOwnerDetailsByOwnerId(ownerId);
  // Show notification if the response is null or not valid, else set to
  // fleetAssetOwner
  if (!response?.ownerId) {
    handleUnknownSubcontractor();
    return;
  }
  fleetAssetOwner.value = response;
}

const handleUnknownSubcontractor = (): void => {
  showAppNotification('Sorry, we could not find that Subcontractor.');
  fleetAssetOwner.value = null;
  fleetAssetOwnerStore.setSelectedFleetAssetOwnerId(null);
  closeFleetAssetOwnerDialog();
  router.push({ name: 'Subcontractor' });
};

const fleetAssetOwnerName: ComputedRef<string> = computed(() => {
  return fleetAssetOwner.value?.name ?? '';
});

const closeFleetAssetOwnerDialog = () => {
  fleetAssetOwnerStore.setSelectedFleetAssetOwnerId(null);
};

// Define computed property for menu options
const menuOptions: ComputedRef<MenuCategory[]> = computed(() => {
  const basicInfo: MenuCategory = {
    id: 'INFO',
    title: 'Owner Info',
    items: [
      {
        id: 'COM',
        title: 'Company Details',
        isForm: true,
        isActive: isEdited.value ? false : true,
        isEditable: true,
        actionBtnText: 'Edit',
      },
      {
        id: 'INS',
        title: 'INSURANCE POLICIES',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isEditable: true,
        actionBtnText: 'Edit',
      },
      {
        id: 'NOT',
        title: 'NOTES',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isEditable: true,
        actionBtnText: 'Add Note',
      },
    ],
  };

  const truckInfo: MenuCategory = {
    id: 'ASSET',
    title: 'Assets and Drivers',
    items: [
      // Show driver for everyone but Equipment Hire
      fleetAssetOwner.value?.affiliation !== '2'
        ? {
            id: 'DRI',
            title: 'DRIVERS',
            isForm: false,
            isActive: isEdited.value ? false : true,
            isEditable: false,
            actionBtnText: 'Edit',
          }
        : null,
      // Show trucks for everyone but Equipment Hire
      fleetAssetOwner.value?.affiliation !== '2'
        ? {
            id: 'TRU',
            title: 'TRUCKS',
            isForm: false,
            isActive: isEdited.value ? false : true,
            isEditable: false,
            actionBtnText: 'Edit',
          }
        : null,
      // Only show trailers for Equipment Hire
      fleetAssetOwner.value?.affiliation === '2'
        ? {
            id: 'TRA',
            title: 'TRAILERS',
            isForm: false,
            isActive: isEdited.value ? false : true,
            isEditable: false,
            actionBtnText: 'Edit',
          }
        : null,
      // fleet asset owners current work
      {
        id: 'CRW',
        title: 'CURRENT WORK',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isEditable: false,
        actionBtnText: '',
      },
    ].filter((i) => i !== null) as FleetAssetOwnerMenu[],
  };

  const otherInfo: MenuCategory = {
    id: 'OTHER',
    title: 'Billing / Charge Details',
    items: [
      {
        id: 'OPD',
        title: 'Invoicing & Payment Details',
        isForm: true,
        isActive: isEdited.value ? false : true,
        isEditable: true,
        actionBtnText: 'Edit',
      },
      {
        id: 'ADJC',
        title: 'ADJUSTMENT CHARGES',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isEditable: false,
        actionBtnText: 'Edit',
      },
      {
        id: 'DEVI',
        title: 'DEVICES',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isEditable: false,
        actionBtnText: 'Edit',
      },
    ],
  };
  return [basicInfo, truckInfo, otherInfo];
});

const setInvoicingSearchDialog = (value: boolean): void => {
  if (!isAuthorisedToViewLedger()) {
    return;
  }
  isViewingInvoiceSearchDialog.value = value;
};

const affiliationTypeName: ComputedRef<string> = computed(() => {
  if (!fleetAssetOwner.value) {
    return '';
  }
  const affiliationId: string = fleetAssetOwner.value.affiliation;
  const affiliation: AffiliationTypes | undefined = affiliationTypes.find(
    (x: AffiliationTypes) => x.id === affiliationId,
  );
  return affiliation ? affiliation.longName : 'No Affiliation Set';
});

// Set the selected view from the left navigation menu
function setSelectedView(id: string): void {
  if (!id) {
    selectedViewType.value = menuOptions.value[0].items[0].id;
    return;
  }
  selectedViewType.value = id;
}

const selectedView: ComputedRef<FleetAssetOwnerMenu | null> = computed(() => {
  const selectedView = menuOptions.value
    .flatMap((x) => x.items)
    .find((x) => x.id === selectedViewType.value);
  return selectedView ? selectedView : null;
});

const summaryInfoList: ComputedRef<KeyValuePair[]> = computed(() => {
  const infoList: KeyValuePair[] = [];

  if (!fleetAssetOwner.value) {
    return infoList;
  }

  infoList.push({
    id: 'affiliation',
    title: 'Affiliation',
    value: affiliationTypeName.value,
  });

  infoList.push({
    id: 'proprietor',
    title: 'Proprietor',
    value: fleetAssetOwner.value.proprietorName
      ? fleetAssetOwner.value.proprietorName
      : '-',
  });

  infoList.push({
    id: 'trucks',
    title: 'Trucks',
    value: trucks.value.length,
  });

  infoList.push({
    id: 'trailers',
    title: 'Trailers',
    value: trailers.value.length,
  });

  infoList.push({
    id: 'signupDate',
    title: 'Signup Date',
    value: fleetAssetOwner.value.operationDetails.signupDate
      ? returnFormattedDate(
          fleetAssetOwner.value.operationDetails.signupDate,
          'DD/MM/YYYY',
        )
      : '-',
  });

  if (isAdmin() && selectedFleetAssetOwnerId.value) {
    infoList.push({
      id: 'ID',
      title: 'Owner ID',
      value: selectedFleetAssetOwnerId.value,
    });
  }

  return infoList;
});

const associatedFleetAssets: ComputedRef<FleetAssetSummary[]> = computed(() => {
  if (!fleetAssetOwner.value) {
    return [];
  }
  const ownerId = fleetAssetOwner.value.ownerId;
  return fleetAssetStore.getAllFleetAssetList.filter(
    (x: FleetAssetSummary) => x.fleetAssetOwnerId === ownerId,
  );
});

const trucks: ComputedRef<FleetAssetSummary[]> = computed(() => {
  return associatedFleetAssets.value.filter(
    (x: FleetAssetSummary) => x.fleetAssetTypeId === 1,
  );
});

const trailers: ComputedRef<FleetAssetSummary[]> = computed(() => {
  return associatedFleetAssets.value.filter(
    (x: FleetAssetSummary) => x.fleetAssetTypeId === 2,
  );
});

const currentStatus: ComputedRef<StatusConfig> = computed(() => {
  const statusConfig = {
    statusName: '',
    color: '',
  };

  if (!fleetAssetOwner.value) {
    return statusConfig;
  }

  const doNotUse: boolean = fleetAssetOwner.value.statusList.includes(47);
  const active: boolean = fleetAssetOwner.value.statusList.includes(4);

  if (doNotUse) {
    statusConfig.statusName = 'DO NOT USE';
    statusConfig.color = 'error';
  } else if (active) {
    statusConfig.statusName = 'Active';
    statusConfig.color = 'success';
  } else {
    statusConfig.statusName = 'INCOMPLETE';
    statusConfig.color = 'error';
  }

  return statusConfig;
});

async function saveFleetAssetOwner(): Promise<void> {
  if (!fleetAssetOwner.value) {
    return;
  }
  if (ownerForm.value && !ownerForm.value.validate()) {
    showAppNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }
  awaitingFleetAssetOwnerSaveResponse.value = true;
  const savedOwner = await fleetAssetOwner.value.save();
  setSavedFleetAssetOwner(savedOwner);
}

function setEdited(Edited: boolean): void {
  isEdited.value = Edited;
}

function setSavedFleetAssetOwner(savedOwner: FleetAssetOwner | null) {
  if (!awaitingFleetAssetOwnerSaveResponse.value) {
    return;
  }
  if (!savedOwner) {
    showAppNotification(GENERIC_ERROR_MESSAGE);
    awaitingFleetAssetOwnerSaveResponse.value = false;
    return;
  }
  showAppNotification(
    savedOwner.name + ' successfully saved.',
    HealthLevel.INFO,
  );
  fleetAssetOwner.value = savedOwner;
  isEdited.value = false;
  if (selectedFleetAssetOwnerId.value !== savedOwner.ownerId) {
    fleetAssetOwnerStore.setSelectedFleetAssetOwnerId(savedOwner.ownerId);
  }

  awaitingFleetAssetOwnerSaveResponse.value = false;

  const name = fleetAssetOwner.value.name.toLowerCase().replace(/ /g, '-');
  const id = fleetAssetOwner.value.ownerId;

  router
    .push({
      name: 'Owner',
      params: { name, id },
    })
    .catch((err) => {
      console.error('Navigation error:', err.message);
    });
}

// Trigger app notification. Defaults to ERROR type message, but type can be
// provided to produce other types. Includes componentTitle as a title for the
// notification.
function showAppNotification(text: string, type?: HealthLevel) {
  showNotification(text, {
    type,
    title: componentTitle,
  });
}

/**
 * Mitt handler for save response on adding or removing device to fleet asset owner
 * @param response - Response from the API, containing ownerId and the updated
 * device
 */
function handleDeviceUpdateResponse(
  response: AddDeviceFleetAssetOwnerRequest | null,
) {
  const addedDevice = response?.associatedDevice;
  if (!addedDevice || !fleetAssetOwner.value?.ownerId) {
    return;
  }
  const foundMatch = fleetAssetOwner.value.associatedDevices.findIndex(
    (ad) => ad.id === addedDevice.id,
  );
  if (foundMatch !== -1) {
    fleetAssetOwner.value.associatedDevices.splice(foundMatch, 1, addedDevice);
  } else {
    fleetAssetOwner.value.associatedDevices.push(addedDevice);
  }
}

/**
 * Adds a saved driver to the fleet asset owner's list of associated drivers, if
 * the fleet asset owner exists, the owner IDs match, and the driver is not
 * already associated.
 * @param {SaveDriverDetailsResponse} savedFleetAsset
 */
function addSavedFleetAssetToOwner(savedFleetAsset: FleetAsset | null): void {
  if (
    !fleetAssetOwner.value ||
    !savedFleetAsset?.fleetAssetOwnerId ||
    fleetAssetOwner.value.ownerId !== savedFleetAsset.fleetAssetOwnerId ||
    fleetAssetOwner.value.associatedFleetAssets.includes(
      savedFleetAsset.fleetAssetId,
    )
  ) {
    return;
  }
  fleetAssetOwner.value.associatedFleetAssets.push(
    savedFleetAsset.fleetAssetId,
  );
}
/**
 * Adds a saved driver to the fleet asset owner's list of associated drivers, if
 * the fleet asset owner exists, the owner IDs match, and the driver is not
 * already associated.
 * @param {SaveDriverDetailsResponse} savedDriver
 */
function addSavedDriverToFleetAssetOwner(
  savedDriver: SaveDriverDetailsResponse | null,
) {
  if (
    !fleetAssetOwner.value ||
    !savedDriver?.fleetAssetOwnerId ||
    fleetAssetOwner.value.ownerId !== savedDriver.fleetAssetOwnerId ||
    fleetAssetOwner.value.associatedDrivers.includes(
      savedDriver.driverDetails.driver.driverId,
    )
  ) {
    return;
  }
  fleetAssetOwner.value.associatedDrivers.push(
    savedDriver.driverDetails.driver.driverId,
  );
}

/**
 * Called by mitt listener when an owner save event is received. Used to reset
 * the component state if the owner has been updated by another user.
 */
function handleExternalSave(incomingOwner: FleetAssetOwner | null): void {
  if (!incomingOwner?.ownerId) {
    return;
  }
  if (
    incomingOwner.ownerId === fleetAssetOwner.value?.ownerId &&
    !awaitingFleetAssetOwnerSaveResponse.value
  ) {
    // Show notification if currently in edit mode
    if (isEdited.value) {
      showAppNotification(
        `${fleetAssetOwnerName.value} has been updated by another user. Your changes have been discarded.`,
        HealthLevel.WARNING,
      );
    }
    isEdited.value = false;
    findAndSetSelectedFleetAssetOwner();
  }
}

useMittListener('addedAssociatedDeviceToOwner', handleDeviceUpdateResponse);
useMittListener('retiredAssociatedDeviceFromOwner', handleDeviceUpdateResponse);
useMittListener('selectedFleetAssetDetails', addSavedFleetAssetToOwner);
useMittListener('savedNewDriver', addSavedDriverToFleetAssetOwner);
useMittListener('selectedFleetAssetOwner', handleExternalSave);

onBeforeMount(() => {
  if (fleetAssetOwnerStore.selectedFleetAssetOwnerView) {
    selectedViewType.value = fleetAssetOwnerStore.selectedFleetAssetOwnerView;
    fleetAssetOwnerStore.setSelectedFleetAssetOwnerView(null);
  } else {
    selectedViewType.value = 'COM';
  }
});

onMounted(() => {
  findAndSetSelectedFleetAssetOwner();
  // Mitt.on('savedNewDriver', addSavedDriverToFleetAssetOwner);
});

onBeforeUnmount(() => {
  fleetAssetOwnerStore.setSelectedFleetAssetOwnerId(null);
  // Mitt.on('savedNewDriver', addSavedDriverToFleetAssetOwner);
});
</script>
