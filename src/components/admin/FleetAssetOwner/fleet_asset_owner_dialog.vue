<template>
  <v-dialog
    v-model="fleetAssetOwnerDialogIsOpen"
    content-class="v-dialog-custom"
    persistent
    width="85%"
  >
    <FleetAssetOwnerIndex :isDialog="true" v-if="fleetAssetOwnerDialogIsOpen" />
  </v-dialog>
</template>

<script setup lang="ts">
import { WritableComputedRef, computed } from 'vue';
import FleetAssetOwnerIndex from '@/components/admin/FleetAssetOwner/fleet_asset_owner_index/fleet_asset_owner_index.vue';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';

const fleetAssetOwnerStore = useFleetAssetOwnerStore();

const selectedFleetAssetOwnerId = computed(() => {
  return useFleetAssetOwnerStore().selectedFleetAssetOwnerId;
});
// Computed property to determine if the fleet asset owner dialog is open
const fleetAssetOwnerDialogIsOpen: WritableComputedRef<boolean> =
  computed<boolean>({
    get(): boolean {
      return fleetAssetOwnerStore.selectedFleetAssetOwnerId ? true : false;
    },
    set(value: boolean): void {
      if (!value) {
        closeFleetAssetOwnerDialog();
      }
    },
  });

// Method to close the fleet asset owner dialog
const closeFleetAssetOwnerDialog = (): void => {
  fleetAssetOwnerStore.setSelectedFleetAssetOwnerId(null);
};
</script>

<style scoped></style>
