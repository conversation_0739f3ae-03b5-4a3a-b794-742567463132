<template>
  <div class="admin-service-rates">
    <!-- Header with Create New button -->
    <TableTitleHeader>
      <!-- Title slot -->
      <template #title>
        <GTitle
          :title="`${clientName} Service Rate`"
          subtitle="Add or edit division default service rates"
          :divider="false"
        >
        </GTitle>
      </template>

      <!-- Buttons slot -->
      <template #buttons>
        <v-btn
          color="error"
          outline
          @click="cancelNewServiceRate"
          v-if="isNewServiceRate"
          class="mr-4"
        >
          Cancel
        </v-btn>
        <v-btn
          outline
          @click="editServiceRate"
          v-if="!isEdited && selectedServiceRateId !== null"
          :disabled="!isAuthorised()"
          class="mr-4"
        >
          <v-icon size="16" class="pr-2">edit</v-icon>
          Edit
        </v-btn>
        <v-btn
          color="error"
          outline
          @click="cancelEditServiceRate"
          v-if="isEdited && !isNewServiceRate"
          block
          class="mr-4"
        >
          Cancel Edit
        </v-btn>
        <v-spacer />
        <v-btn
          v-if="!isNewServiceRate && !isEdited"
          @click="newServiceRate"
          :disabled="!ratesLoaded || !isAuthorised() || disableAddNewButton"
          block
          color="primary"
        >
          <v-icon small class="pa-2">fas fa-plus</v-icon>
          Create New
        </v-btn>
        <v-btn block color="success" v-if="isEdited" @click="saveServiceRate">
          <v-icon size="16" class="pr-2">save</v-icon>
          Save
        </v-btn>
      </template>
    </TableTitleHeader>

    <v-layout v-if="!ratesLoaded" justify-center>
      <img
        src="@/static/loader/infinity-loader-light.svg"
        height="80px"
        width="80px"
      />
    </v-layout>

    <v-form ref="form" v-if="ratesLoaded">
      <div>
        <v-alert
          :value="allDefaultRates.length === 0"
          color="error"
          icon="warning"
        >
          <span v-if="entityId === '0'"
            >There are currently no active service rate tables. Please create
            one</span
          >
          <span v-else
            >There are currently no active service rates for Cash Sales. Please
            create one</span
          >
        </v-alert>
        <v-alert
          :value="disableAddNewButton"
          type="warning"
          :class="{ 'mb-3': disableAddNewButton }"
        >
          Creating and Editing Zone-To-Zone rates is not currently available.
          Your division rate card has Zone To Zone pricing. Contact GoDesta
          Support for assistance creating a new Division Rate Card.
        </v-alert>
        <v-slide-x-transition>
          <form-card class="mx-auto">
            <div slot="content">
              <ServiceRateDateRangeEdit
                :serviceRate="serviceRate"
                :dateRangeItems="dateRangeItems"
                :isViewing="viewingServiceRate"
                :isDefault="true"
                :isEdited="isEdited"
                :isNew="isNewServiceRate"
                :hasCurrentServiceRate="currentActiveServiceRate !== null"
                :newServiceRateSettings="isNewServiceRate"
                :allServiceRates="[...allDefaultRates].reverse()"
                :allClientServiceRates="allDefaultRates"
                :allowEditOnDateRange="allowEditOnDateRange"
                :currentActiveServiceRate="currentActiveServiceRate"
                :isClient="true"
                :clientFleetId="entityId"
                :clientAssetName="clientName"
                @getServiceRateByTableId="getClientServiceRatesByTableId"
              />
            </div>
          </form-card>
        </v-slide-x-transition>
      </div>
      <v-flex class="button-group mb-2">
        <span v-for="rateType in availableRateTypes" :key="rateType.rateTypeId">
          <v-btn
            :class="{
              'v-btn--active': selectedRateTypeId === rateType.rateTypeId,
            }"
            flat
            @click="selectedRateTypeId = rateType.rateTypeId"
          >
            <span class="px-2"
              ><strong>{{ rateType.longName }}</strong> Rates</span
            >
          </v-btn>
        </span>
      </v-flex>

      <ServiceRateTableTime
        :isEdited="isEdited"
        :isNew="isNewServiceRate"
        :serviceRate="serviceRate"
        :adminPage="true"
        v-if="selectedRateTypeId === 1"
      />
      <ServiceRateTableDistance
        :type="type"
        :serviceRate="serviceRate"
        v-if="selectedRateTypeId === 3"
        :isEdited="isEdited"
      />

      <v-layout v-if="selectedRateTypeId === 1" pt-3>
        <v-flex md2 offset-md3>
          <v-layout align-center class="form-field-label-container">
            <h6 class="subheader--faded pr-3 pb-0">Time Rate Applied:</h6>
          </v-layout>
        </v-flex>
        <v-flex md4>
          <v-autocomplete
            class="v-solo-custom"
            ref="timeRateAppliedInput"
            solo
            flat
            multiple
            v-model="timeAppliedController"
            :items="validServiceTypeList"
            :disabled="!isEdited"
            item-text="optionSelectName"
            color="orange"
            label="Service Type"
            item-value="serviceTypeId"
            :hide-selected="!allowRateTableItemRemoval"
          >
            <template v-slot:selection="{ item, index }">
              <v-chip
                v-if="index <= 2"
                :close="allowRateTableItemRemoval"
                @input="removeServiceTypeIdFromList('TIME', item.serviceTypeId)"
              >
                <span>{{ item.optionSelectName }}</span>
              </v-chip>
              <span v-if="index === 3">
                (+{{ timeAppliedController.length - 3 }}) more
              </span>
            </template>
          </v-autocomplete>
        </v-flex></v-layout
      >
      <v-layout v-if="selectedRateTypeId === JobRateType.DISTANCE" pt-3>
        <v-flex md2 offset-md3>
          <v-layout align-center class="form-field-label-container">
            <h6 class="subheader--faded pr-3 pb-0">Distance Rate Applied:</h6>
          </v-layout>
        </v-flex>
        <v-flex md4>
          <v-autocomplete
            class="v-solo-custom"
            solo
            flat
            multiple
            :disabled="!isEdited"
            v-model="distanceAppliedController"
            :items="validServiceTypeList"
            item-text="optionSelectName"
            color="orange"
            label="Service Type"
            item-value="serviceTypeId"
            :hide-selected="!allowRateTableItemRemoval"
          >
            <template v-slot:selection="{ item, index }">
              <v-chip
                v-if="index <= 3"
                :color="isEdited ? 'green' : 'grey darken-3'"
                :close="allowRateTableItemRemoval"
                @input="
                  removeServiceTypeIdFromList('DISTANCE', item.serviceTypeId)
                "
              >
                <span>{{ item.optionSelectName }}</span>
              </v-chip>
              <span v-if="index === 4">
                (+{{ timeAppliedController.length - 4 }}) more
              </span>
            </template>
          </v-autocomplete>
        </v-flex></v-layout
      >
    </v-form>
  </div>
</template>

<script setup lang="ts">
import ServiceRateDateRangeEdit from '@/components/common/service-rate-table/service-rate-date-range-edit/index.vue';
import ServiceRateTableTime from '@/components/common/service-rate-table/service-rate-types/service-rate-table-time/index.vue';
import ServiceRateTableDistance from '@/components/common/service-rate-table/service-rate-types/service_rate_table_distance/service_rate_table_distance.vue';
import FormCard from '@/components/common/ui-elements/form_card.vue';
import TableTitleHeader from '@/components/common/ui-elements/table_title_header.vue';
import {
  initialiseClientServiceRate,
  initialiseFleetAssetServiceRate,
} from '@/helpers/classInitialisers/InitialiseServiceRate';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { deepCopy } from '@/helpers/JsonHelpers/JsonHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  addOrRemoveRateTableItems,
  getServiceTypeIdsForRateTypeId,
} from '@/helpers/RateHelpers/ServiceRateHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { CurrentClientServiceRateResponse } from '@/interface-models/Client/CurrentClientServiceRateResponse';
import serviceTypeRates, {
  JobRateType,
  ServiceTypeRates,
} from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import type { ValidFromRequiredEdit } from '@/interface-models/Generic/ValidRequiredEdit';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import FleetAssetServiceRate from '@/interface-models/ServiceRates/FleetAssetOwner/FleetAssetServiceRate/FleetAssetServiceRate';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import TimeRateType from '@/interface-models/ServiceRates/ServiceTypes/TimeServiceRate/TimeRateType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useServiceRateStore } from '@/store/modules/ServiceRateStore';
import { sessionManager } from '@/store/session/SessionState';
import moment from 'moment-timezone';
import { computed, ComputedRef, onMounted, reactive, Ref, ref } from 'vue';

interface StateLoaded {
  allServiceRates: boolean;
  serviceRate: boolean;
}

const props = defineProps<{
  entityId: string;
  type: RateEntityType;
}>();

const serviceRateStore = useServiceRateStore();
const companyDetailsStore = useCompanyDetailsStore();

const isEdited: Ref<boolean> = ref(false);
const isNewServiceRate: Ref<boolean> = ref(false);
const viewingServiceRate: Ref<boolean> = ref(true);
const allowEditOnDateRange: Ref<boolean> = ref(false);

const currentActiveServiceRate: Ref<
  ClientServiceRate | FleetAssetServiceRate | null
> = ref(null);
const serviceRate: Ref<ClientServiceRate | FleetAssetServiceRate | null> =
  ref(null);
const allDefaultRates: Ref<ClientServiceRate[] | FleetAssetServiceRate[]> = ref(
  [],
);

const selectedServiceRateId: Ref<string | null> = ref(null);

const form = ref<any>(null);
const timeRateAppliedInput = ref<any>(null);

const selectedRateTypeId: Ref<JobRateType> = ref(JobRateType.TIME);

const stateLoad = reactive<StateLoaded>({
  allServiceRates: false,
  serviceRate: false,
});

const rateTypesList = computed<ServiceTypeRates[]>(() => {
  return serviceTypeRates.filter(
    (rateType: ServiceTypeRates) => !rateType.adhoc,
  );
});

const availableRateTypes: ServiceTypeRates[] = [
  JobRateType.TIME,
  JobRateType.DISTANCE,
].map((rti) => rateTypesList.value.find((rt) => rt.rateTypeId === rti)!);

const clientName = computed(() => {
  if (props.type === RateEntityType.CLIENT) {
    if (props.entityId === 'CS') {
      return 'Cash Sale';
    }
    return sessionManager.getCompanyId() + ' ' + sessionManager.getDivisionId();
  } else if (props.type === RateEntityType.FLEET_ASSET) {
    return 'Fleet Asset';
  } else {
    return 'Unknown';
  }
});

/**
 * Returns a list of service types that are flagged as being 'division
 * services', meaning they should be available to be added a default (or cash
 * sale) rates.
 */
const validServiceTypeList = computed(() => {
  return useCompanyDetailsStore().getServiceTypesList.filter(
    (serviceType: ServiceTypes) =>
      serviceType.serviceTypeId !== 4 && serviceType.divisionService,
  );
});

// Handle 'close' click event on chip in v-autocomplete in html. Remove the
// selected serviceTypeId from the list specified in listToSplice
function removeServiceTypeIdFromList(
  listToSplice: string,
  serviceTypeId: number,
) {
  switch (listToSplice) {
    case 'TIME':
      timeAppliedController.value = timeAppliedController.value.filter(
        (i) => i !== serviceTypeId,
      );
      break;
    case 'DISTANCE':
      distanceAppliedController.value = distanceAppliedController.value.filter(
        (i) => i !== serviceTypeId,
      );
      break;
  }
}

// Modelled to the autocomplete controlling applied TIME rates. Calls method
// to update rateTableItems on change
const timeAppliedController = computed({
  get: () => {
    if (!serviceRate.value) {
      return [];
    }
    return getServiceTypeIdsForRateTypeId(
      JobRateType.TIME,
      serviceRate.value.rateTableItems,
    );
  },
  set: (value: number[]) => {
    if (!serviceRate.value) {
      return;
    }
    if (props.type === RateEntityType.FLEET_ASSET) {
      addOrRemoveRateTableItems({
        type: RateEntityType.FLEET_ASSET,
        rateTypeId: JobRateType.TIME,
        updatedAppliedServiceTypes: value,
        serviceRates: serviceRate.value as FleetAssetServiceRate,
        allowRemoval: allowRateTableItemRemoval.value,
      });
    } else {
      addOrRemoveRateTableItems({
        type: RateEntityType.CLIENT,
        rateTypeId: JobRateType.TIME,
        updatedAppliedServiceTypes: value,
        serviceRates: serviceRate.value as ClientServiceRate,
        allowRemoval: allowRateTableItemRemoval.value,
      });
    }
  },
});

// Modelled to the autocomplete controlling applied DISTANCE rates. Calls method
// to update rateTableItems on change
const distanceAppliedController = computed({
  get: () => {
    if (!serviceRate.value) {
      return [];
    }
    return getServiceTypeIdsForRateTypeId(
      JobRateType.DISTANCE,
      serviceRate.value.rateTableItems,
    );
  },
  set: (value: number[]) => {
    if (!serviceRate.value) {
      return;
    }
    if (props.type === RateEntityType.FLEET_ASSET) {
      addOrRemoveRateTableItems({
        type: RateEntityType.FLEET_ASSET,
        rateTypeId: JobRateType.DISTANCE,
        updatedAppliedServiceTypes: value,
        serviceRates: serviceRate.value as FleetAssetServiceRate,
        allowRemoval: allowRateTableItemRemoval.value,
      });
    } else {
      addOrRemoveRateTableItems({
        type: RateEntityType.CLIENT,
        rateTypeId: JobRateType.DISTANCE,
        updatedAppliedServiceTypes: value,
        serviceRates: serviceRate.value as ClientServiceRate,
        allowRemoval: allowRateTableItemRemoval.value,
      });
    }
  },
});

//  checking if all state values are true
const ratesLoaded: ComputedRef<boolean> = computed(() => {
  return Object.keys(stateLoad).every(
    (k: string) => stateLoad[k as keyof typeof stateLoad],
  );
});

const dateRangeItems = computed(() => {
  return allDefaultRates.value.map((rates) => ({
    id: rates.tableId,
    validFromDate:
      rates.validFromDate !== null
        ? returnFormattedDate(rates.validFromDate, 'DD/MM/YYYY')
        : '-',
    validToDate:
      rates.validToDate !== null
        ? returnFormattedDate(rates.validToDate, 'DD/MM/YYYY')
        : '-',
  }));
});

const latestKnownServiceRate = computed(() => {
  if (allDefaultRates.value.length === 0) {
    return null;
  }
  return allDefaultRates.value.reduce((prev, current) =>
    prev.validToDate! > current.validToDate! ? prev : current,
  );
});

function generateStockServiceRate(): void {
  if (props.type === RateEntityType.FLEET_ASSET) {
    if (serviceRate.value === null) {
      serviceRate.value = new FleetAssetServiceRate();
    }
    (serviceRate.value as FleetAssetServiceRate).fleetAssetId = props.entityId;
    serviceRate.value.tableId = undefined;
    serviceRate.value.rateTableItems = [];
    delete serviceRate.value._id;
  } else {
    if (serviceRate.value === null) {
      serviceRate.value = new ClientServiceRate();
    }
    (serviceRate.value as ClientServiceRate).clientId = props.entityId;
    serviceRate.value.tableId = undefined;
    serviceRate.value.rateTableItems = [];
    delete serviceRate.value._id;
  }

  if (
    latestKnownServiceRate.value !== null &&
    latestKnownServiceRate.value.validToDate !== null
  ) {
    serviceRate.value.validFromDate = moment(
      latestKnownServiceRate.value.validToDate,
    )
      .tz(companyDetailsStore.userLocale)
      .add(1, 'day')
      .startOf('day')
      .valueOf();
  } else {
    serviceRate.value.validFromDate = moment()
      .tz(companyDetailsStore.userLocale)
      .startOf('day')
      .valueOf();
    serviceRate.value.validToDate = null;
  }

  for (const serviceType of companyDetailsStore.getServiceTypesList) {
    if (serviceType.divisionService && serviceType.serviceTypeId !== 4) {
      const rateTableItem = new RateTableItems();
      rateTableItem.rateTypeId = 1;
      rateTableItem.serviceTypeId = serviceType.serviceTypeId;
      rateTableItem.rateTypeObject = new TimeRateType();
      serviceRate.value.rateTableItems.push(rateTableItem);
    }
  }
}

/**
 * Retrieves the ClientServiceRate object using the provided clientId and
 * tableId.
 *
 * @param clientId - The clientId of the client.
 * @param tableId - The ID of the table.
 * @returns A promise that resolves to the retrieved ClientServiceRate object,
 * or null if not found.
 */
async function getClientServiceRatesByTableId([clientId, tableId]: [
  string,
  string,
]) {
  let result: ClientServiceRate | FleetAssetServiceRate | null = null;
  // Get full ClientServiceRate object using clientId and tableId
  if (props.type === RateEntityType.FLEET_ASSET) {
    result = await serviceRateStore.getFleetAssetServiceRateByTableId(
      clientId,
      tableId,
    );
  } else {
    result = await serviceRateStore.getClientServiceRatesByTableId(
      clientId,
      tableId,
    );
  }
  // Handle result, or show error message if result is null
  if (result) {
    setSelectedServiceRate(result);
  } else {
    showNotification(
      'Something went wrong. The selected Rate Card could not be found.',
      {
        title: 'Rate Card',
      },
    );
  }
}

function editServiceRate() {
  isEdited.value = true;
  viewingServiceRate.value = false;
}

async function cancelEditServiceRate() {
  isEdited.value = false;
  viewingServiceRate.value = true;

  if (currentActiveServiceRate.value !== null) {
    serviceRate.value = deepCopy(currentActiveServiceRate.value);
  } else if (latestKnownServiceRate.value !== null) {
    await getClientServiceRatesByTableId([
      props.entityId,
      `${latestKnownServiceRate.value.tableId}`,
    ]);
  } else {
    generateStockServiceRate();
  }
}

/**
 * Returns true if we are in EDIT mode and we are adding a NEW service rate.
 * This is used to prevent the removal of rate table items if the user is
 * editing an existing service rate table. This is to make sure that once a
 * rate is added, it can't be removed (in case it's being used in a job)
 */
const allowRateTableItemRemoval = computed(() => {
  return isEdited.value && isNewServiceRate.value;
});

/**
 * Returns true if the current active service rates have ZTZ rates in them.
 * This is used in the template to disabled the NEW button so the user can't
 * accidentally make ZONE TO ZONE rates unavailable.
 *
 * NOTE: This is a temporary measure until we make ZONE TO ZONE rates able to
 * be administered in the app.
 */
const disableAddNewButton = computed(() => {
  return (
    currentActiveServiceRate.value?.rateTableItems.some(
      (rateTableItem: RateTableItems) =>
        rateTableItem.rateTypeId === JobRateType.ZONE_TO_ZONE,
    ) ?? false
  );
});

function newServiceRate() {
  isNewServiceRate.value = true;
  isEdited.value = true;
  viewingServiceRate.value = false;
  generateStockServiceRate();
}

function setSelectedServiceRate(
  rate: ClientServiceRate | FleetAssetServiceRate,
) {
  if (isNewServiceRate.value) {
    delete rate._id;
    rate.name = '';
    rate.validFromDate =
      latestKnownServiceRate.value !== null &&
      latestKnownServiceRate.value.validFromDate !== null
        ? moment(latestKnownServiceRate.value.validToDate)
            .tz(companyDetailsStore.userLocale)
            .add(1, 'day')
            .startOf('day')
            .valueOf()
        : moment().tz(companyDetailsStore.userLocale).startOf('day').valueOf();

    rate.validToDate = null;
  } else if (rate._id !== undefined) {
    selectedServiceRateId.value = rate._id;
  }
  serviceRate.value = rate;
}

// Get company and division using the store
const company = computed(() => sessionManager.getCompanyId());
const division = computed(() => sessionManager.getDivisionId());

function showValidationErrorMessage() {
  showNotification(
    'Please make sure all required rate information is entered.',
  );
}

/**
 * Performs validation and saves the current service rate.
 */
/**
 * Performs validation and saves the current service rate.
 */
async function saveServiceRate() {
  if (!serviceRate.value) {
    return;
  }

  // Ensure the form exists before calling validate()
  if (!form.value) {
    console.error('Form reference is missing.');
    return;
  }

  const validation = await form.value.validate(); // Ensure it's awaited if async
  const dateStartValid = typeof serviceRate.value.validFromDate === 'number';
  const dateEndValid = typeof serviceRate.value.validToDate === 'number';
  const elementError = document.getElementsByClassName('error-text').length > 0;

  if (!validation || !dateStartValid || !dateEndValid || elementError) {
    showValidationErrorMessage();
    return;
  }

  if (isNewServiceRate.value) {
    serviceRate.value.tableId = undefined;
    serviceRate.value._id = undefined;
    serviceRate.value.company = company.value;
    serviceRate.value.division = division.value;
  }

  isNewServiceRate.value = false;
  isEdited.value = false;

  if (props.type === RateEntityType.FLEET_ASSET) {
    // Make fleet save request
    const [result] = await Promise.all([
      serviceRateStore.saveFleetAssetServiceRate(
        serviceRate.value as FleetAssetServiceRate,
      ),
      updateRateSummaries(),
    ]);
    savedServiceRateSuccess(result);
  } else {
    // Make client save request
    const [result] = await Promise.all([
      serviceRateStore.saveClientServiceRates(
        serviceRate.value as ClientServiceRate,
      ),
      updateRateSummaries(),
    ]);

    savedServiceRateSuccess(result);
  }
}

/**
 * Send requests to update any adjacent ServiceRates that may have had their
 * dates changed as part of the current edit.
 */
async function updateRateSummaries() {
  if (validFromEdits.value.edit) {
    const foundRate = allDefaultRates.value.find(
      (x: ClientServiceRate | FleetAssetServiceRate) =>
        x.tableId === validFromEdits.value.tableId,
    );
    if (foundRate !== undefined) {
      foundRate.validToDate = validFromEdits.value.validToValue;
      if (props.type === RateEntityType.FLEET_ASSET) {
        await serviceRateStore.updateFleetAssetServiceRateSummary(
          foundRate as FleetAssetServiceRate,
        );
      } else {
        await serviceRateStore.updateClientServiceRateSummary(
          foundRate as ClientServiceRate,
        );
      }
    }
  }
  if (validToEdits.value.edit) {
    const foundRate = allDefaultRates.value.find(
      (x: ClientServiceRate | FleetAssetServiceRate) =>
        x.tableId === validToEdits.value.tableId,
    );
    if (foundRate !== undefined) {
      foundRate.validFromDate = validToEdits.value.validFromValue;

      if (props.type === RateEntityType.FLEET_ASSET) {
        await serviceRateStore.updateFleetAssetServiceRateSummary(
          foundRate as FleetAssetServiceRate,
        );
      } else {
        await serviceRateStore.updateClientServiceRateSummary(
          foundRate as ClientServiceRate,
        );
      }
    }
  }
}

function cancelNewServiceRate(): void {
  isNewServiceRate.value = false;
  isEdited.value = false;
  viewingServiceRate.value = true;

  if (currentActiveServiceRate.value !== null) {
    serviceRate.value = JSON.parse(
      JSON.stringify(currentActiveServiceRate.value),
    );
  }
}

function setCurrentDefaultServiceRate(
  type: RateEntityType,
  rate: ClientServiceRate | FleetAssetServiceRate,
): void {
  if (rate !== null) {
    if (rate._id) {
      selectedServiceRateId.value = rate._id;
    }
    if (type === RateEntityType.CLIENT) {
      currentActiveServiceRate.value = initialiseClientServiceRate(
        rate as ClientServiceRate,
      );
      serviceRate.value = initialiseClientServiceRate(
        rate as ClientServiceRate,
      );
    } else {
      currentActiveServiceRate.value = initialiseFleetAssetServiceRate(
        rate as FleetAssetServiceRate,
      );
      serviceRate.value = initialiseFleetAssetServiceRate(
        rate as FleetAssetServiceRate,
      );
    }
  } else {
    currentActiveServiceRate.value = null;
  }
}

async function savedServiceRateSuccess(
  serviceRate: ClientServiceRate | FleetAssetServiceRate | null,
): Promise<void> {
  const currentTime = moment().tz(companyDetailsStore.userLocale).valueOf();

  if (!serviceRate || !serviceRate.validFromDate || !serviceRate.validToDate) {
    return;
  }

  if (
    !currentActiveServiceRate.value &&
    serviceRate.validFromDate <= currentTime &&
    serviceRate.validToDate >= currentTime
  ) {
    currentActiveServiceRate.value = serviceRate;
    return;
  }

  if (
    currentActiveServiceRate.value &&
    serviceRate.tableId === currentActiveServiceRate.value.tableId &&
    (serviceRate.validFromDate > currentTime ||
      serviceRate.validToDate < currentTime)
  ) {
    currentActiveServiceRate.value = null;
  }
  // Refresh full service rate list
  const allServiceRates = await serviceRateStore.getAllServiceRatesForClientId(
    props.entityId,
  );
  if (allServiceRates) {
    allDefaultRates.value = allServiceRates.reverse();
  }
}

const nextServiceRate = computed(() => {
  if (serviceRate.value !== null) {
    const foundEditedServiceRate = allDefaultRates.value.find(
      (rate: ClientServiceRate | FleetAssetServiceRate) =>
        rate.tableId === serviceRate.value!.tableId,
    );
    if (foundEditedServiceRate !== undefined) {
      const serviceRate = allDefaultRates.value.find(
        (x: ClientServiceRate | FleetAssetServiceRate) =>
          x.validFromDate! > foundEditedServiceRate.validToDate! &&
          x.validFromDate! < foundEditedServiceRate.validToDate! + 80400000,
      );
      if (serviceRate !== undefined) {
        return serviceRate;
      }
    }
  }
  return null;
});

const previousServiceRate = computed(() => {
  if (!serviceRate.value) {
    return undefined;
  }

  const foundEditedServiceRate = allDefaultRates.value.find(
    (rate: ClientServiceRate | FleetAssetServiceRate) =>
      rate.tableId === serviceRate.value!.tableId,
  );

  if (foundEditedServiceRate) {
    return allDefaultRates.value.find(
      (x: ClientServiceRate | FleetAssetServiceRate) =>
        x.validToDate! < foundEditedServiceRate.validFromDate! &&
        x.validToDate! > foundEditedServiceRate.validFromDate! - 80400000,
    );
  }

  return latestServiceRate.value;
});

const latestServiceRate = computed(() => {
  let date: number = 0;
  for (const rate of allDefaultRates.value) {
    if (rate.validFromDate !== null && rate.validToDate !== null) {
      if (rate.validFromDate > date) {
        date = rate.validFromDate;
      }

      if (rate.validToDate > date) {
        date = rate.validToDate;
      }
    }
  }
  return allDefaultRates.value.find(
    (rate: ClientServiceRate | FleetAssetServiceRate) =>
      rate.validToDate === date,
  );
});

const validToEdits = computed(() => {
  // Default structure for required edits
  const requiredEdits: {
    edit: boolean;
    tableId?: number;
    validFromValue: number;
  } = {
    edit: false,
    validFromValue: 0,
  };

  if (!serviceRate.value) {
    return requiredEdits;
  }

  let foundEditedServiceRate = allDefaultRates.value.find(
    (x: ClientServiceRate | FleetAssetServiceRate) =>
      x.tableId === serviceRate.value!.tableId,
  );

  if (isNewServiceRate.value) {
    foundEditedServiceRate = serviceRate.value;
  }

  if (
    serviceRate.value.validToDate &&
    nextServiceRate.value &&
    nextServiceRate.value.validFromDate &&
    foundEditedServiceRate &&
    foundEditedServiceRate.validToDate
  ) {
    if (
      serviceRate.value.validToDate >= nextServiceRate.value.validFromDate ||
      serviceRate.value.validToDate < foundEditedServiceRate.validToDate
    ) {
      requiredEdits.edit = true;
      requiredEdits.tableId = nextServiceRate.value.tableId;
      requiredEdits.validFromValue = moment(serviceRate.value.validToDate)
        .tz(companyDetailsStore.userLocale)
        .add(1, 'day')
        .startOf('day')
        .valueOf();
    }
  }

  return requiredEdits;
});

const validFromEdits = computed(() => {
  // Default structure for required edits
  const requiredEdits: ValidFromRequiredEdit = {
    edit: false,
    tableId: -1,
    validToValue: 0,
  };

  if (!serviceRate.value) {
    return requiredEdits;
  }

  let foundEditedServiceRate = allDefaultRates.value.find(
    (x: ClientServiceRate | FleetAssetServiceRate) =>
      x.tableId === serviceRate.value!.tableId,
  );

  if (isNewServiceRate.value && serviceRate.value) {
    foundEditedServiceRate = serviceRate.value;
  }

  if (
    serviceRate.value.validFromDate &&
    previousServiceRate.value &&
    previousServiceRate.value.validToDate &&
    foundEditedServiceRate &&
    foundEditedServiceRate.validFromDate
  ) {
    if (
      serviceRate.value.validFromDate <=
        previousServiceRate.value.validToDate ||
      serviceRate.value.validFromDate > foundEditedServiceRate.validFromDate
    ) {
      requiredEdits.edit = true;
      requiredEdits.tableId = previousServiceRate.value.tableId;

      requiredEdits.validToValue = moment(serviceRate.value.validFromDate)
        .tz(companyDetailsStore.userLocale)
        .subtract(1, 'day')
        .endOf('day')
        .valueOf();
    }
  }

  return requiredEdits;
});

/**
 * Fetches all service rates and the current service rate for a specific client.
 * The data is then set to local variables and loading states are updated.
 *
 * @param {string} clientId - The ID of the client for which to fetch the service rates.
 * @returns {Promise<void>} - A Promise that resolves when the data has been fetched and set.
 */
async function getInitialRateData(clientId: string): Promise<void> {
  let allServiceRates: ClientServiceRate[] | FleetAssetServiceRate[] | null =
    [];
  generateStockServiceRate();
  if (props.type === RateEntityType.FLEET_ASSET) {
    let currentServiceRate: FleetAssetServiceRate | null = null;
    [allServiceRates, currentServiceRate] = await Promise.all([
      serviceRateStore.getAllServiceRatesForFleetAssetId(clientId),
      serviceRateStore.getActiveFleetAssetServiceRates(
        clientId,
        moment().valueOf(),
      ),
    ]);
    if (currentServiceRate) {
      setCurrentDefaultServiceRate(props.type, currentServiceRate);
    }
  } else {
    let currentServiceRate: CurrentClientServiceRateResponse | null = null;
    [allServiceRates, currentServiceRate] = await Promise.all([
      serviceRateStore.getAllServiceRatesForClientId(clientId),
      serviceRateStore.getCurrentClientServiceRates(clientId),
    ]);
    if (currentServiceRate?.clientServiceRate) {
      setCurrentDefaultServiceRate(
        props.type,
        currentServiceRate.clientServiceRate,
      );
    }
  }

  if (allServiceRates) {
    allDefaultRates.value = allServiceRates.reverse();
  }

  stateLoad.allServiceRates = true;
  stateLoad.serviceRate = true;
}

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}

onMounted(() => {
  getInitialRateData(props.entityId);
});
</script>

<style scoped lang="scss">
.admin-service-rates {
  background-color: var(--background-color-250);
  padding-bottom: 67px;
}
</style>
