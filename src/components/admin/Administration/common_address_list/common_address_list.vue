<template>
  <div>
    <TableTitleHeader>
      <!-- Title slot -->
      <template #title>
        <GTitle
          title="FIND COMMON ADDRESS"
          subtitle="Search for client common address"
          :divider="false"
        >
        </GTitle>
      </template>

      <!-- Inputs slot -->
      <template #inputs>
        <v-layout md12>
          <v-flex md6 class="pa-2">
            <v-text-field
              appendIcon="search"
              label="Search Address Name/Nickname"
              hint="Search Address Name/Nickname"
              color="orange"
              height="30"
              outline
              solo
              flat
              class="v-solo-custom"
              clearable
              v-model="searchQuery"
            >
            </v-text-field>
          </v-flex>
          <v-flex md6 class="pa-2">
            <AddressSearchAU
              :hideHeadingRow="true"
              :label="'Search Full Address'"
              :setFocus="false"
              :address="searchAddress"
              :enablePinDrop="false"
              :enableNicknamedAddress="false"
              :enableAddNickNameAddress="false"
              :enableSuburbSelect="false"
              :soloInput="true"
              :isClientPortal="sessionManager.isClientPortal()"
              @addressSelected="findAddressById"
              :addressIsRequired="false"
              clearable
            >
            </AddressSearchAU>
          </v-flex>
        </v-layout>
      </template>
    </TableTitleHeader>

    <v-layout md12 class="body-scrollable--80 body-min-height--75">
      <v-data-table
        :headers="headers"
        :items="filteredGlobalCommonAddress"
        :loading="awaitingClientNames"
        class="gd-dark-theme fullWidth"
        item-key="_id"
        :rows-per-page-items="[10, 20]"
      >
        <template v-slot:items="tableProps">
          <tr @click="() => {}">
            <td>
              {{ tableProps.item.clientId }} -
              {{ clientNames[tableProps.item.clientId] }}
            </td>
            <td>
              {{
                tableProps.item.addressNickname
                  ? tableProps.item.addressNickname.toUpperCase()
                  : ''
              }}
            </td>
            <td>
              {{ tableProps.item.address.formattedAddress }}
            </td>
            <td>
              {{ tableProps.item.defaultDispatchAddress ? 'Yes' : 'No' }}
            </td>
          </tr>
        </template>
      </v-data-table>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import AddressSearchAU from '@/components/common/addressing/address-search-au/index.vue';
import TableTitleHeader from '@/components/common/ui-elements/table_title_header.vue';
import { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import AddressAU from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/AddressAU';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { sessionManager } from '@/store/session/SessionState';
import { computed, ComputedRef, reactive, Ref, ref, watch } from 'vue';

const searchQuery = ref('');
const clientDetailsStore = useClientDetailsStore();

const searchAddress: Ref<AddressAU> = ref(new AddressAU());
const awaitingClientNames = ref(false);

// store client name with client ids
const clientNames = reactive<Record<string, string>>({});

const globalCommonAddressListByName: Ref<ClientCommonAddress[] | null> =
  ref(null);
const globalCommonAddressListById: Ref<ClientCommonAddress[] | null> =
  ref(null);

const headers: TableHeader[] = [
  {
    text: 'Client',
    align: 'left',
    sortable: true,
    value: 'client',
    visible: true,
  },
  {
    text: 'Common Address Name',
    align: 'left',
    sortable: true,
    value: 'name',
    visible: true,
  },
  {
    text: 'Address',
    align: 'left',
    sortable: true,
    value: 'address',
    visible: true,
  },
  {
    text: 'Default dispatch address',
    align: 'left',
    sortable: true,
    value: 'defaultDispatchAddress',
    visible: true,
  },
];

// get list of global common addresses
const clientCommonAddressList: ComputedRef<ClientCommonAddress[]> = computed(
  () => {
    const commonAddresses = clientDetailsStore.clientCommonAddresses;
    return commonAddresses;
  },
);

// filters global common address list for select common address table dialog
const filteredGlobalCommonAddress: ComputedRef<ClientCommonAddress[]> =
  computed(() => {
    const sourceList = [
      ...(globalCommonAddressListById.value || []),
      ...(globalCommonAddressListByName.value || []),
    ];

    if (!sourceList || !sourceList.length) {
      return [];
    }

    // Create a Set of client common address IDs for quick lookup
    const clientAddressIds = new Set(
      clientCommonAddressList.value.map((item) => item._id),
    );

    const uniqueAddresses = new Map();
    return sourceList.filter((item) => {
      if (!uniqueAddresses.has(item._id) && !clientAddressIds.has(item._id)) {
        uniqueAddresses.set(item._id, true);
        return true;
      }
      return false;
    });
  });

// Handle site/business name blur event (search global common address from nickname)
async function findAddressByName() {
  if (searchQuery.value && searchQuery.value?.length > 3) {
    // Check if the input text matches any item in the list
    const exists = clientCommonAddressList.value.some(
      (item) =>
        item.addressNickname.trim().toLowerCase() ===
        searchQuery.value.trim().toLowerCase(),
    );

    if (!exists && !sessionManager.isClientPortal()) {
      try {
        // Send API request with the input text
        const result =
          await clientDetailsStore.requestClientCommonAddressByNickname(
            searchQuery.value,
          );

        globalCommonAddressListByName.value = Array.isArray(result)
          ? result
          : [];

        fetchClientNames();
      } catch (error) {
        globalCommonAddressListByName.value = [];
      }
    }
  }
}

// Handle select address blur event (search global common address from address id)
async function findAddressById(id: string) {
  // Find the matching address in clientCommonAddressList
  const existingAddress = clientCommonAddressList.value.find(
    (item) => item.address.addressId === id,
  );
  if (!existingAddress && !sessionManager.isClientPortal()) {
    try {
      // Fetch address data from API since it wasn't found in clientCommonAddressList
      const result =
        await clientDetailsStore.requestClientCommonAddressByAddressId(id);

      globalCommonAddressListById.value = Array.isArray(result) ? result : [];
      fetchClientNames();
    } catch (error) {
      globalCommonAddressListById.value = [];
    }
  }
}

// function to fetch and set client names for common address table form clientDetails using clientID
function fetchClientNames() {
  if (!filteredGlobalCommonAddress.value?.length) {
    return;
  }
  awaitingClientNames.value = true;

  filteredGlobalCommonAddress.value.forEach((address) => {
    const clientId = address.clientId;

    // Find client name from clientSummaryList instead of making a request
    const cachedClient = clientDetailsStore.clientSummaryList.find(
      (client) => client.clientId === clientId,
    );

    if (cachedClient) {
      clientNames[clientId] = cachedClient.clientName;
    }
  });

  awaitingClientNames.value = false;
}

// Watch searchQuery for changes and fetch data for common address table
watch(searchQuery, (newValue) => {
  if (newValue.length > 3) {
    findAddressByName();
  } else {
    globalCommonAddressListByName.value = [];
  }
});

// watch full address input to find common address and add data to table
watch(searchAddress, () => {
  if (searchAddress.value.addressId) {
    findAddressById(searchAddress.value.addressId);
  } else {
    globalCommonAddressListByName.value = [];
  }
});
</script>
