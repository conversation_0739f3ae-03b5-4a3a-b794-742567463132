<template>
  <div>
    <TableTitleHeader>
      <template #title>
        <GTitle
          title="Induction Types"
          subtitle="View Induction Types list"
          :divider="false"
        />
      </template>
      <template #inputs>
        <v-flex md5>
          <v-text-field
            appendIcon="search"
            label="Search Induction Type"
            hint="Search Induction by Name"
            color="orange"
            solo
            flat
            class="v-solo-custom"
            clearable
            v-model="searchQuery"
          >
          </v-text-field>
        </v-flex>
      </template>
    </TableTitleHeader>

    <v-flex md12>
      <v-data-table
        :headers="headers"
        :items="filteredInductionTypes"
        class="gd-dark-theme"
        item-key="id"
        :rows-per-page-items="[10, 15, 20]"
      >
        <template v-slot:items="tableProps">
          <tr class="charge-row-item" @click="() => {}">
            <td>{{ tableProps.item.longName }}</td>
            <td>
              {{
                tableProps.item.shortName
                  ? tableProps.item.shortName.toUpperCase()
                  : ''
              }}
            </td>
            <td>
              {{ tableProps.item.company }}
            </td>
            <td>
              {{ tableProps.item.division }}
            </td>

            <td>{{ tableProps.item.requiredForAllDrivers ? 'Yes' : '-' }}</td>
            <td>
              {{ tableProps.item.complianceAlertIfExpired ? 'Yes' : '-' }}
            </td>
          </tr>
        </template>
      </v-data-table>
    </v-flex>
  </div>
</template>

<script setup lang="ts">
import TableTitleHeader from '@/components/common/ui-elements/table_title_header.vue';
import { InductionType } from '@/interface-models/Generic/InductionType/InductionType';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { computed, ComputedRef, Ref, ref } from 'vue';

// search input text
const searchQuery: Ref<string> = ref('');

const headers: TableHeader[] = [
  {
    text: 'Name',
    align: 'left',
    value: 'longName',
    sortable: true,
  },
  {
    text: 'Short Name',
    align: 'left',
    value: 'shortName',
    sortable: true,
  },
  {
    text: 'Company',
    align: 'left',
    value: 'company',
    sortable: true,
  },
  {
    text: 'Division',
    align: 'left',
    value: 'division',
    sortable: true,
  },
  {
    text: 'Required For All Drivers',
    align: 'left',
    value: 'requiredForAllDrivers',
    sortable: true,
  },
  {
    text: 'Compliance Alert If Expired',
    align: 'left',
    value: 'complianceAlertIfExpired',
    sortable: true,
  },
];

/**
 * Computes the InductionType items data.
 * @returns {InductionType[]} The Induction Type items.
 */
const inductionTypes: ComputedRef<InductionType[]> = computed(() => {
  return useDriverDetailsStore().inductionTypesList;
});

// filter table data based on search input
const filteredInductionTypes = computed(() => {
  if (!searchQuery.value) {
    return inductionTypes.value;
  }
  return inductionTypes.value.filter((item) =>
    Object.values(item).some(
      (val) =>
        val &&
        val.toString().toLowerCase().includes(searchQuery.value.toLowerCase()),
    ),
  );
});
</script>
