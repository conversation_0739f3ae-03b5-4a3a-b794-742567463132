<template>
  <section class="charge-types-management">
    <v-flex md12 style="background: #1e1d22" class="py-2">
      <v-layout align-center>
        <b class="pl-3 text-uppercase">Charge Categories</b>
        <v-spacer></v-spacer>
      </v-layout>
    </v-flex>

    <v-data-table
      :headers="headers"
      :items="chargeTypeTableData"
      :rows-per-page-items="[10, 15, 20]"
      class="gd-dark-theme"
    >
      <template v-slot:items="tableProps">
        <tr>
          <td>{{ tableProps.item.longName }}</td>
          <td>{{ tableProps.item.associatedCharges }}</td>
          <td>{{ tableProps.item.expiry }}</td>
        </tr>
      </template>
    </v-data-table>
  </section>
</template>
<script setup lang="ts">
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { AdditionalChargeItem } from '@/interface-models/AdditionalCharges/AdditionalChargeItem';
import { AdditionalChargeType } from '@/interface-models/AdditionalCharges/AdditionalChargeType';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { ComputedRef, computed } from 'vue';

const props = withDefaults(
  defineProps<{
    additionalChargeRates: AdditionalChargeItem[];
    additionalChargeTypes: AdditionalChargeType[];
  }>(),
  {
    additionalChargeRates: () => [],
    additionalChargeTypes: () => [],
  },
);

const headers: TableHeader[] = [
  {
    text: 'Category',
    align: 'left',
    value: 'longName',
    sortable: true,
  },
  {
    text: 'Available Charges',
    align: 'left',
    value: 'associatedCharges',
    sortable: true,
  },
  {
    text: 'Expiry',
    align: 'left',
    value: 'Expiry',
    sortable: true,
  },
];

/**
 * Computes the charge type table data based on the additional charge types and rates.
 * @returns {Array} The formatted charge type table data.
 */
const chargeTypeTableData: ComputedRef<
  Array<{
    longName: string;
    associatedCharges: number;
    expiry: string;
  }>
> = computed(() => {
  interface TableItem {
    longName: string;
    associatedCharges: number;
    expiry: string;
  }

  const tableData: TableItem[] = [];
  for (const type of props.additionalChargeTypes) {
    const associatedCharges = props.additionalChargeRates.filter(
      (charge) => charge.typeReferenceId === type._id,
    );
    const length = associatedCharges.length;
    let expiry!: number;
    for (const [index, charge] of associatedCharges.entries()) {
      if (index === 0) {
        expiry = charge.validFromDate;
      } else if (charge.validFromDate > expiry) {
        expiry = charge.validFromDate;
      }
    }
    const tableItem: TableItem = {
      longName: type.longName,
      associatedCharges: length,
      expiry: returnCorrectDate(expiry),
    };
    tableData.push(tableItem);
  }
  return tableData;
});

/**
 * Returns the formatted date.
 * @param {number} epoch - The epoch time to format.
 * @returns {string} The formatted date.
 */
function returnCorrectDate(epoch: number): string {
  return returnFormattedDate(epoch, 'DD/MM/YYYY');
}
</script>

<style scoped lang="scss"></style>
