<template>
  <v-flex md12>
    <v-layout wrap>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">
            {{
              entityType === RateEntityType.CLIENT ? 'Client' : 'Fleet Asset'
            }}
          </h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 v-if="!isTollAdminAndHandlingCharge">
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Charge Applies To
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
              disabled
              label="Application Type"
              :items="getAdditionalChargeApplicationTypeList()"
              item-text="longName"
              :rules="[validate.required]"
              v-model="rateConfig.appliesTo"
              item-value="id"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Charge
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md4>
            <v-text-field
              class="v-solo-custom"
              type="number"
              solo
              flat
              :prefix="
                rateConfig.chargeBasis === AdditionalChargeRateBasis.FIXED
                  ? '$'
                  : ''
              "
              :suffix="
                rateConfig.chargeBasis === AdditionalChargeRateBasis.PERCENTAGE
                  ? '%'
                  : ''
              "
              color="light-blue"
              label="Charge"
              @focus="$event.target.select()"
              v-model.number="rateConfig.charge"
              :rules="[validate.required]"
              :disabled="isFormDisabled"
            ></v-text-field>
          </v-flex>
          <v-flex md4 pl-2>
            <v-select
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
              :disabled="isFormDisabled || chargeBasisList.length === 1"
              label="Charge basis"
              :items="chargeBasisList"
              item-text="longName"
              v-model="rateConfig.chargeBasis"
              :rules="[validate.required]"
              item-value="id"
            />
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </v-flex>
</template>

<script setup lang="ts">
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { getAdditionalChargeApplicationTypeList } from '@/interface-models/AdditionalCharges/AdditionalChargeApplicationType';
import {
  AdditionalChargeRateBasis,
  getAdditionalChargeRateBasisList,
} from '@/interface-models/AdditionalCharges/AdditionalChargeRateBasis';
import { AdditionalChargeRateConfig } from '@/interface-models/AdditionalCharges/AdditionalChargeRateConfig';
import { AdditionalChargeType } from '@/interface-models/AdditionalCharges/AdditionalChargeType';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import { computed, ComputedRef } from 'vue';

const props = defineProps<{
  entityType: RateEntityType;
  isFormDisabled: boolean;
  rateConfig: AdditionalChargeRateConfig;
  additionalChargeType: AdditionalChargeType | undefined;
  isTollAdminAndHandlingCharge: boolean;
}>();

const validate = validationRules;

const chargeBasisList: ComputedRef<
  {
    id: AdditionalChargeRateBasis;
    shortName: string;
    longName: string;
  }[]
> = computed(() => {
  return getAdditionalChargeRateBasisList().filter((basis) => {
    if (!props.isTollAdminAndHandlingCharge) {
      return (
        !props.additionalChargeType ||
        props.additionalChargeType.allowedRateBases.includes(basis.id)
      );
    } else {
      return basis.id === AdditionalChargeRateBasis.PERCENTAGE;
    }
  });
});
</script>
<style scoped lang="scss"></style>
