<template>
  <section class="additional-charges-table-view">
    <TableTitleHeader :showDivider="false">
      <template #inputs>
        <v-flex md6>
          <v-text-field
            appendIcon="search"
            label="Search Charge"
            hint="Search Charge by Name"
            color="orange"
            solo
            flat
            class="v-solo-custom mr-4"
            clearable
            v-model="searchQuery"
          >
          </v-text-field>
        </v-flex>
        <v-flex md4>
          <v-select
            class="v-solo-custom mr-4"
            solo
            flat
            color="light-blue"
            label="Select Category"
            prefix="Filter"
            :items="chargeTypeSelectOptions"
            item-text="selectName"
            item-value="id"
            v-model="selectedChargeType"
          ></v-select>
        </v-flex>
      </template>
    </TableTitleHeader>
    <v-data-table
      :headers="headers"
      :items="additionalChargesTableData"
      class="gd-dark-theme"
      :rows-per-page-items="[10, 15, 20]"
    >
      <template v-slot:items="tableProps">
        <tr
          class="charge-row-item"
          :class="{ evenBorder: colorSwitch(tableProps.index) }"
          @click="viewAdditionalChargeRate(tableProps.item.id)"
        >
          <td>{{ tableProps.item.type }}</td>
          <td>{{ tableProps.item.name }}</td>
          <td>{{ tableProps.item.clientRate }}</td>
          <td v-if="tableProps.item.id === tollAdminAndHandlingId">N/A</td>
          <td v-else>{{ tableProps.item.driverRate }}</td>
          <td>{{ tableProps.item.validFromDate }}</td>
          <td>{{ tableProps.item.validToDate }}</td>
        </tr>
      </template>
    </v-data-table>
  </section>
</template>
<script setup lang="ts">
import TableTitleHeader from '@/components/common/ui-elements/table_title_header.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { returnAdditionalChargeRateSummary } from '@/helpers/RateHelpers/AdditionalChargeHelpers';
import { AdditionalChargeItem } from '@/interface-models/AdditionalCharges/AdditionalChargeItem';
import { AdditionalChargeType } from '@/interface-models/AdditionalCharges/AdditionalChargeType';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import { useRootStore } from '@/store/modules/RootStore';
import { ComputedRef, Ref, computed, ref } from 'vue';

interface AdditionalChargeTableRow {
  type: string;
  name: string;
  id: string;
  clientRate: string;
  driverRate: string;
  validFromDate: string;
  validToDate: string;
}

const props = withDefaults(
  defineProps<{
    additionalChargeItems: AdditionalChargeItem[];
    additionalChargeTypes: AdditionalChargeType[];
  }>(),
  {
    additionalChargeRates: () => [],
    additionalChargeTypes: () => [],
  },
);

const emit = defineEmits<{
  (event: 'viewAdditionalChargeRate', payload: string): void;
}>();

const selectedChargeType: Ref<string> = ref('');

const searchQuery: Ref<string> = ref('');

const tollAdminAndHandlingId = useRootStore().tollAdminAndHandlingId;

const headers: TableHeader[] = [
  {
    text: 'Type Name',
    align: 'left',
    value: 'type',
    sortable: true,
  },
  {
    text: 'Charge Name',
    align: 'left',
    value: 'longName',
    sortable: true,
  },
  {
    text: 'Client Rate',
    align: 'left',
    value: 'clientRate',
    sortable: true,
  },
  {
    text: 'Driver Rate',
    align: 'left',
    value: 'driverRate',
    sortable: true,
  },
  {
    text: 'Valid From',
    align: 'left',
    value: 'validFromDate',
    sortable: true,
  },
  {
    text: 'Valid To',
    align: 'left',
    value: 'validToDate',
    sortable: true,
  },
];

const chargeTypeSelectOptions: ComputedRef<
  {
    id: string;
    shortName: string;
    longName: string;
    count: number;
  }[]
> = computed(() => {
  const chargeTypes = props.additionalChargeTypes.map((type) => {
    const count = props.additionalChargeItems.filter(
      (item) => item.typeReferenceId === type._id,
    ).length;
    return {
      id: type._id,
      selectName: `${type.shortName} - ${type.longName} (${count})`,
      shortName: type.shortName,
      longName: type.longName,
      count: count,
    };
  });
  const totalCount = props.additionalChargeItems.length;
  const allChargesOption = {
    id: '',
    selectName: `All Charges (${totalCount})`,
    shortName: 'ALL',
    longName: 'All Charges',
    count: totalCount,
  };

  return [allChargesOption, ...chargeTypes];
});

/**
 * Computes the additional charges table data based on the additional charge rates.
 * @returns {AdditionalChargeTableRow[]} The formatted additional charges table data.
 */
const additionalChargesTableData: ComputedRef<AdditionalChargeTableRow[]> =
  computed(() => {
    const query = searchQuery.value.toLowerCase();

    return props.additionalChargeItems
      .filter((item) => {
        // Filter by selectedChargeType if applicable
        const matchesChargeType =
          selectedChargeType.value === '' ||
          item.typeReferenceId === selectedChargeType.value;

        // Filter by search query
        const matchesSearchQuery =
          !query || item.longName.toLowerCase().includes(query);

        // Return true if both filters match
        return matchesChargeType && matchesSearchQuery;
      })
      .map((rate) => ({
        type: returnTypeName(rate.typeReferenceId),
        name: rate.longName,
        id: rate._id ? rate._id : '',
        clientRate: returnAdditionalChargeRateSummary(
          RateEntityType.CLIENT,
          rate,
        ),
        driverRate: returnAdditionalChargeRateSummary(
          RateEntityType.FLEET_ASSET,
          rate,
        ),
        validFromDate: returnFormattedDate(rate.validFromDate),
        validToDate: rate.validToDate
          ? returnFormattedDate(rate.validToDate)
          : 'N/A',
      }));
  });

/**
 * Returns the type name for the given reference ID.
 * @param {string} referenceId - The reference ID.
 * @returns {string} The type name.
 */
function returnTypeName(referenceId: string): string {
  const foundType: AdditionalChargeType | undefined =
    props.additionalChargeTypes.find(
      (type: AdditionalChargeType) => type._id === referenceId,
    );
  return foundType ? `${foundType.shortName} - ${foundType.longName}` : '-';
}

/**
 * Emits an event to view the additional charge rate by ID.
 * @param {string} id - The ID of the additional charge rate.
 */
function viewAdditionalChargeRate(id: string): void {
  emit('viewAdditionalChargeRate', id);
}

/**
 * Determines the color switch for the given index.
 * @param {number} index - The index.
 * @returns {boolean} True if the color should switch, false otherwise.
 */
function colorSwitch(index: number): boolean {
  if (index === 0) {
    return false;
  }

  if (
    props.additionalChargeItems[index].typeReferenceId !==
      props.additionalChargeItems[index - 1].typeReferenceId &&
    !colorSwitch(index - 1)
  ) {
    return true;
  }

  if (
    props.additionalChargeItems[index].typeReferenceId ===
      props.additionalChargeItems[index - 1].typeReferenceId &&
    colorSwitch(index - 1)
  ) {
    return true;
  }

  return false;
}
</script>

<style scoped lang="scss">
.odd-border {
  border-left: 3px solid #ff9800 !important;
}

.evenBorder {
  border-left: 3px solid green !important;
}

.charge-row-item {
  cursor: pointer;
}
</style>
