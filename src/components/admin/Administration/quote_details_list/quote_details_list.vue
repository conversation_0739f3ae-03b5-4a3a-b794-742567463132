<template>
  <div>
    <TableTitleHeader>
      <template #title>
        <GTitle
          title="Job Quote Details"
          subtitle="Select Client to find job quote details"
          :emitSelected="true"
          :divider="false"
        />
      </template>

      <template #inputs>
        <v-flex md7 class="pa-2">
          <v-autocomplete
            id="client-job-select"
            v-model="clientShort.id"
            :items="clientSelectList"
            item-value="clientId"
            item-text="clientDisplayName"
            label="Client Select"
            @change="getAndSetQuoteDetails"
            class="v-solo-custom form-field-required"
            browser-autocomplete="off"
            :allow-overflow="false"
            hide-selected
            color="orange"
            :search-input.sync="search"
            cache-items
            solo
            flat
            clearable
            hide-details
            @update:search-input="trimInput"
          >
          </v-autocomplete>
        </v-flex>
      </template>
    </TableTitleHeader>

    <v-flex md12 class="body-scrollable--80 body-min-height--75 pa-2">
      <v-data-table
        class="gd-dark-theme"
        :headers="headers"
        :items="tableData"
        :rows-per-page-items="[20, 30, 40]"
      >
        <template v-slot:items="dataProps">
          <tr v-if="clientShort.id">
            <td>{{ dataProps.item.quoteId }}</td>
            <td>{{ dataProps.item.date }}</td>
            <td>{{ dataProps.item.serviceTypeName }}</td>
            <td>{{ dataProps.item.references }}</td>
            <td>{{ dataProps.item.fromAddress }}</td>
            <td>{{ dataProps.item.toAddress }}</td>
            <td>{{ dataProps.item.dispatcherName }}</td>
            <td>{{ dataProps.item.clientCharge }}</td>
            <td>{{ dataProps.item.quoteCreationTime }}</td>
            <td>{{ dataProps.item.quoteExpiryTime }}</td>
            <td>
              <v-layout row>
                <!-- MENU BUTTON -->
                <v-menu
                  v-if="
                    reportsSettings?.allowedAccessMethods?.includes(
                      ReportAccessMethodTypes.EMAIL,
                    )
                  "
                  offset-x
                >
                  <template v-slot:activator="{ on: submenu }">
                    <v-tooltip bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn
                          flat
                          icon
                          v-bind="attrs"
                          v-on="{ ...submenu, ...on }"
                          :disabled="isSendingQuoteEmail"
                          class="action-btn download"
                        >
                          <v-icon size="20">menu</v-icon>
                        </v-btn>
                      </template>
                      <span>Generate Documents</span>
                    </v-tooltip>
                  </template>

                  <v-list dense class="v-list-custom">
                    <!-- Download QuoteDetails -->
                    <v-list-tile
                      v-if="
                        reportsSettings?.allowedAccessMethods?.includes(
                          ReportAccessMethodTypes.DOWNLOAD,
                        )
                      "
                      @click="
                        GenerateReport(
                          dataProps.item.id,
                          ReportAccessMethodTypes.DOWNLOAD,
                          QuoteReportRequestType.PREVIEW,
                        )
                      "
                    >
                      <v-icon class="pr-2" size="20">downloading</v-icon>
                      <v-list-tile-title> Download</v-list-tile-title>
                    </v-list-tile>

                    <!-- Email QuoteDetails -->
                    <v-list-tile
                      v-if="
                        reportsSettings?.allowedAccessMethods?.includes(
                          ReportAccessMethodTypes.EMAIL,
                        )
                      "
                      @click="
                        emailQuoteId = dataProps.item.id;
                        sendToEmailAddressList.push(currentUserEmail);
                        GenerateReport(
                          emailQuoteId,
                          ReportAccessMethodTypes.EMAIL,
                          QuoteReportRequestType.SEND,
                        );
                      "
                    >
                      <v-icon class="pr-2" size="20">forward_to_inbox</v-icon>
                      <v-list-tile-title>Email</v-list-tile-title>
                    </v-list-tile>
                  </v-list>
                </v-menu>
                <!-- DOWNLOAD BUTTON -->
                <v-tooltip v-else bottom>
                  <template v-slot:activator="{ on }">
                    <v-btn
                      flat
                      icon
                      v-on="on"
                      @click="
                        GenerateReport(
                          dataProps.item.id,
                          ReportAccessMethodTypes.DOWNLOAD,
                          QuoteReportRequestType.PREVIEW,
                        )
                      "
                      :disabled="isSendingQuoteEmail"
                      class="action-btn download"
                    >
                      <v-icon size="20">download</v-icon>
                    </v-btn>
                  </template>
                  <span>Download Quote Details</span>
                </v-tooltip>
                <!-- EMAIL BUTTON -->
                <v-tooltip bottom>
                  <template v-slot:activator="{ on }">
                    <v-btn
                      flat
                      icon
                      v-on="on"
                      @click="openEmailQuoteDialog(dataProps.item)"
                      :disabled="
                        isSendingQuoteEmail || dataProps.item.quoteAcceptedTime
                      "
                      class="action-btn email"
                    >
                      <v-icon size="20">forward_to_inbox</v-icon>
                    </v-btn>
                  </template>
                  <span>Email Quote Details</span>
                </v-tooltip>
              </v-layout>
            </td>
          </tr>
          <tr v-else>
            <td>Select Client to view job quotes</td>
          </tr>
        </template>
      </v-data-table>
      <ContentDialog
        v-if="sendEmailDialog"
        :showDialog.sync="sendEmailDialog"
        title="Email Quote Details"
        width="720px"
        contentPadding="pa-0"
        @cancel="cancelEmailDialog"
        @confirm="
          GenerateReport(
            emailQuoteId,
            ReportAccessMethodTypes.EMAIL,
            QuoteReportRequestType.SEND,
          )
        "
        :showActions="true"
        :isConfirmUnsaved="false"
        confirmBtnText="Send Email"
        :isLoading="isSendingQuoteEmail"
        :showActionButton="false"
        :isDisabled="sendToEmailAddressList.length <= 0"
      >
        <v-layout column class="send-email-container">
          <div class="d-flex justify-space-between align-center flex-wrap">
            <div class="client-header-txt">
              {{ search }}
            </div>
          </div>
          <GTitle
            :title="`# ${selectedQuote?.quoteId} - ${selectedQuote?.fromAddress} TO  ${selectedQuote?.toAddress}`"
            :subtitle="`Booking Date: ${selectedQuote?.date}`"
            :divider="true"
          />
          <v-alert type="info" :value="true" class="ma-2 pb-2">
            <ul>
              <li>
                This email may be getting sent to an external party. Ensure that
                you have the correct email address recipients.
              </li>
              <li>
                Resending this quote does not extend the validity of the quote
              </li>
            </ul>
          </v-alert>
          <v-layout row ma-2 pt-2>
            <v-flex md12>
              <span class="subheader--faded"
                >Enter Emails To Send QUOTE DETAILS</span
              >
            </v-flex>
          </v-layout>

          <v-layout row>
            <v-flex xs12>
              <v-combobox
                v-model="sendToEmailAddressList"
                :items="sendToEmailAddressList"
                chips
                class="v-solo-custom"
                solo
                multiple
                flat
                color="light-blue"
                hint="Enter an email address then press enter to add"
                label="Add Email Recipients"
                :rules="[validateEmailAddressList]"
                ref="emailAddressCombobox"
                clearable
                persistent-hint
              >
                <template v-slot:selection="data">
                  <v-chip
                    :key="JSON.stringify(data.item)"
                    :selected="data.selected"
                    :disabled="data.disabled"
                    class="v-chip--select-multi"
                    @click.stop="data.parent.selectedIndex = data.index"
                    @input="data.parent.selectItem(data.item)"
                    :class="
                      validate.email(data.item) === true
                        ? 'green darken-4'
                        : 'red darken-4'
                    "
                    close
                  >
                    <v-avatar
                      class="white--text"
                      :class="
                        validate.email(data.item) === true
                          ? 'green darken-2'
                          : 'red darken-2'
                      "
                    >
                      {{ data.item.slice(0, 2).toUpperCase() }}
                    </v-avatar>
                    {{ data.item }}
                  </v-chip>
                </template>
              </v-combobox>
            </v-flex>
          </v-layout>

          <v-layout row wrap mb-2 ml-2>
            <v-flex md3>
              <v-checkbox
                v-model="sendEmailToMe"
                label="Send to Me"
                :disabled="currentUserEmail === dispatcherEmail"
              ></v-checkbox>
            </v-flex>
            <v-flex md6>
              <v-checkbox
                v-model="sendEmailToDispatcher"
                label="Send to Client Dispatcher"
              ></v-checkbox>
            </v-flex>
          </v-layout>
        </v-layout>
      </ContentDialog>
    </v-flex>
  </div>
</template>

<script setup lang="ts">
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import GTitle from '@/components/common/ui-elements/g_title.vue';
import TableTitleHeader from '@/components/common/ui-elements/table_title_header.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import ClientSearchSummary from '@/interface-models/Client/ClientDetails/ClientSearchSummary';
import ClientShort from '@/interface-models/Client/ClientDetails/ClientShort';
import { DivisionReportSettings } from '@/interface-models/Company/DivisionCustomConfig/DivisionCustomConfig';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { Validation } from '@/interface-models/Generic/Validation';
import {
  GenerateQuoteReportRequest,
  QuoteReportRequestType,
} from '@/interface-models/Jobs/Quote/GenerateQuoteReportRequest';
import { QuoteDetails } from '@/interface-models/Jobs/Quote/QuoteDetails';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useJobBookingStore } from '@/store/modules/JobBookingStore';
import { sessionManager } from '@/store/session/SessionState';
import { computed, ComputedRef, Ref, ref, WritableComputedRef } from 'vue';

interface QuoteSummary {
  id: string | undefined;
  quoteId: string;
  quoteDescription: string;
  date: string;
  serviceTypeName: string;
  references: string;
  fromAddress: string;
  toAddress: string;
  dispatcherName: string;
  clientCharge: string;
  quoteCreationTime: string;
  quoteExpiryTime: string;
  dispatcherEmail: string;
}

const clientDetailsStore = useClientDetailsStore();
const quoteDetailsList: Ref<QuoteDetails[]> = ref([]);
const search = ref('');
const clientShort: Ref<ClientShort> = ref(new ClientShort());

const isSendingQuoteEmail: Ref<boolean> = ref(false);
const sendToEmailAddressList: Ref<string[]> = ref([]);
const sendEmailDialog: Ref<boolean> = ref(false);
const emailQuoteId: Ref<string> = ref('');

const selectedQuote: Ref<QuoteSummary | null> = ref(null);
const dispatcherEmail: Ref<string> = ref('');

const company = useCompanyDetailsStore().companyDetails;
const reportsSettings: DivisionReportSettings | null =
  company?.divisions?.[0]?.customConfig?.reports ?? null;

const currentUserEmail: Ref<string> = ref(sessionManager.getUserName());

/**
 * A computed property that checks whether the current user's email
 * is included in the `sendToEmailAddressList`.
 *
 * - Returns `true` if the current user's email is in the list.
 * - Sets; Adds or removes the current user's email from the list based on the boolean value.
 *
 */
const sendEmailToMe: WritableComputedRef<boolean> = computed({
  get: () => sendToEmailAddressList.value.includes(currentUserEmail.value),
  set: (val: boolean) => {
    const email = currentUserEmail.value;
    if (val && !sendToEmailAddressList.value.includes(email)) {
      sendToEmailAddressList.value.push(email);
    } else if (!val) {
      sendToEmailAddressList.value = sendToEmailAddressList.value.filter(
        (e) => e !== email,
      );
    }
  },
});

/**
 * A computed property that checks whether the job dispatcher email
 * is included in the `sendToEmailAddressList`.
 *
 * - Returns `true` if the dispatcher's email is in the list.
 * - Sets; Adds or removes the dispatcher's email from the list based on the boolean value.
 *
 */
const sendEmailToDispatcher: WritableComputedRef<boolean> = computed({
  get: () => sendToEmailAddressList.value.includes(dispatcherEmail.value),
  set: (val: boolean) => {
    const email = dispatcherEmail.value;
    if (val && !sendToEmailAddressList.value.includes(email)) {
      sendToEmailAddressList.value.push(email);
    } else if (!val) {
      sendToEmailAddressList.value = sendToEmailAddressList.value.filter(
        (e) => e !== email,
      );
    }
  },
});

const headers: TableHeader[] = [
  {
    text: 'Quote #',
    align: 'left',
    sortable: true,
    value: 'quoteId',
    visible: true,
  },
  {
    text: 'Job Date',
    align: 'left',
    value: 'date',
    sortable: true,
    visible: true,
  },
  {
    text: 'Service',
    align: 'left',
    value: 'serviceTypeName',
    sortable: true,
    visible: true,
  },
  {
    text: 'Reference',
    align: 'left',
    sortable: true,
    value: 'reference',
    visible: true,
  },

  {
    text: 'From',
    align: 'left',
    value: 'fromAddress',
    sortable: true,
    visible: true,
  },
  {
    text: 'To',
    align: 'left',
    value: 'toAddress',
    sortable: true,
    visible: true,
  },
  {
    text: 'Dispatcher',
    align: 'left',
    value: 'dispatcherName',
    sortable: true,
    visible: true,
  },

  {
    text: 'Charge',
    align: 'left',
    value: 'clientCharge',
    sortable: true,
    visible: true,
  },
  {
    text: 'Quote Created At',
    align: 'left',
    value: 'quoteCreationTime',
    sortable: true,
    visible: true,
  },
  {
    text: 'Quote Expires At',
    align: 'left',
    value: 'quoteExpiryTime',
    sortable: true,
    visible: true,
  },
  {
    text: 'Action',
    align: 'center',
    value: '',
    sortable: false,
    visible: true,
  },
];

// map quotes details to QuoteSummary to create table data
const tableData: ComputedRef<QuoteSummary[]> = computed(() => {
  if (!quoteDetailsList.value) {
    return [];
  }
  return quoteDetailsList.value
    .filter((quote) => quote.jobDetails)
    .map((quote) => {
      // Description format: Q-5 - BD dd-mm-yyyy - SUBURB to SUBURB
      const description = [
        `Q-${quote.quoteId}`,
        `BD ${quote.jobDetails.jobDate}`,
        [
          quote.jobDetails.pudItems?.[0]?.address?.suburb,
          quote.jobDetails.pudItems?.[1]?.address?.suburb,
        ].join(' to '),
      ]
        .filter((x) => !!x)
        .join(' - ');
      const isTimeRate = quote.jobDetails.rateTypeName === 'Time';
      const jobDate = quote.jobDetails.jobDate || 'Unknown';
      const references = quote.jobDetails.allJobReferences || '-';
      const serviceTypeName =
        [quote.jobDetails.serviceTypeShortName, quote.jobDetails.rateTypeName]
          .filter((x) => !!x)
          .join(' - ') || 'N/A';
      // Set from and to address
      const fromAddress =
        quote.jobDetails.pudItems?.[0]?.address?.suburb || 'Unknown';
      let toAddress =
        quote.jobDetails.pudItems?.[1]?.address?.suburb || 'Unknown';
      if (quote.jobDetails.pudItems.length > 2) {
        const additionalStops = quote.jobDetails.pudItems.length - 2;
        toAddress += ` +${additionalStops}`;
      }
      const dispatcherName =
        [
          quote.jobDetails.clientDispatcher.firstName,
          quote.jobDetails.clientDispatcher.lastName,
        ]
          .filter((x) => !!x)
          .join(' ') || 'Unknown';

      return {
        id: quote.id,
        quoteId: quote.quoteId ? `Q-${quote.quoteId}` : '-',
        quoteDescription: description,
        date: jobDate,
        serviceTypeName: serviceTypeName,
        references: references,
        fromAddress: fromAddress,
        toAddress: toAddress,
        clientCharge: isTimeRate
          ? 'Variable'
          : `${
              quote.jobDetails.accounting?.totals?.finalTotal?.client ?? 'N/A'
            }`,
        dispatcherName: dispatcherName,
        quoteCreationTime: returnFormattedDate(
          quote.quoteCreationTime,
          'DD/MM/YYYY HH:mma',
        ),
        quoteExpiryTime: returnFormattedDate(quote.quoteExpiryTime),
        dispatcherEmail: quote.jobDetails.clientDispatcher.emailAddress[0],
      };
    });
});

/**
 * Returns the client list for the Client Select Autocomplete.
 * Filters out clients where id is CS (cash sales).
 */
const clientSelectList: ComputedRef<ClientSearchSummary[]> = computed(() => {
  return clientDetailsStore.clientListForBooking.filter(
    (client) => client.clientId !== 'CS',
  );
});

// Method to trim the input value
function trimInput(query: string) {
  search.value = query.trim(); // Trim leading/trailing spaces
}

/**
 * Computes the validation rules from the store.
 * @returns {ComputedRef<Validation>} A computed reference to the validation rules.
 */
const validate: ComputedRef<Validation> = computed(() => {
  return validationRules;
});

/**
 * Validates a list of email addresses.
 *
 * @param {string[]} emailAddresses - The list of email addresses to validate.
 * @returns {boolean|string} - Returns `true` if all email addresses are valid,
 * otherwise returns an error message.
 */
function validateEmailAddressList(emailAddresses: string[]): boolean | string {
  return emailAddresses.every((e) => validate.value.email(e) === true)
    ? true
    : 'One or more email addresses is invalid.';
}

/**
 * Sends a quote email if the booking type is a quote.
 * Constructs the email request object and triggers the email sending process.
 */
function GenerateReport(
  id: string,
  accessType: ReportAccessMethodTypes,
  requestType: QuoteReportRequestType,
): void {
  const request: GenerateQuoteReportRequest = {
    quoteMongoId: id,
    requestType: requestType,
    emailTo: sendToEmailAddressList.value,
    accessType: accessType,
  };
  sendGenerateReportRequest(request);
}

/**
 * Sends a quote email by calling the job booking store action.
 * Sets loading and success flags accordingly.
 *
 * @param request - The quote report request object containing email and quote details.
 */
async function sendGenerateReportRequest(
  request: GenerateQuoteReportRequest,
): Promise<void> {
  isSendingQuoteEmail.value = true;
  try {
    await useJobBookingStore().generateQuoteReport(request);
  } finally {
    isSendingQuoteEmail.value = false;
    if (sendEmailDialog.value) {
      sendEmailDialog.value = false;
    }
  }
}

// function to open send quote email dialog and sets variable values
function openEmailQuoteDialog(item: QuoteSummary) {
  selectedQuote.value = item;
  emailQuoteId.value = item.id ?? '';
  dispatcherEmail.value = item.dispatcherEmail;
  sendEmailDialog.value = true;
}

// function to close send quote email dialog and reset variable values
function cancelEmailDialog() {
  selectedQuote.value = null;
  emailQuoteId.value = '';
  dispatcherEmail.value = '';
  sendEmailToMe.value = false;
  sendEmailToDispatcher.value = false;
  sendToEmailAddressList.value = [];
  sendEmailDialog.value = false;
}

// async func to get and set job quotes for table data
async function getAndSetQuoteDetails() {
  try {
    const result = await useJobBookingStore().searchQuotes({
      clientId: clientShort.value.id,
    });
    if (result) {
      quoteDetailsList.value = result;
    }
  } catch (error) {
    quoteDetailsList.value = [];
  }
}
</script>

<style scoped lang="scss">
.action-btn {
  border-radius: 50px;
  background-color: var(--background-color-300);
  width: 35px;
  height: 30px;
  &.download {
    color: var(--accent) !important;
  }
  &.email {
    color: var(--primary) !important;
  }

  &:hover {
    box-shadow: var(--box-shadow);
  }
}

.send-email-container {
  margin: 12px 24px;

  .client-header-txt {
    margin: 8px;
    font-size: $font-size-18;
    font-weight: 600;
    color: var(--primary);
  }
}
</style>
