<template>
  <div>
    <TableTitleHeader>
      <template #title>
        <GTitle
          title="Email Message History"
          subtitle="Search for outgoing emails sent by the system"
          :divider="false"
        />
      </template>

      <template #inputs>
        <v-form ref="emailMessageHistoryForm">
          <v-layout align-center>
            <v-flex md8 class="mr-2">
              <v-text-field
                class="v-solo-custom"
                solo
                flat
                color="light-blue"
                v-model.trim="toAddressFilter"
                prefix="Email"
                hide-details
                clearable
                :disabled="isAwaitingResponse"
                @blur="sendSearchRequest(1)"
                @keyup.enter="sendSearchRequest(1)"
                @click:clear="
                  toAddressFilter = undefined;
                  sendSearchRequest(1);
                "
              >
                <template v-slot:prepend-inner>
                  <span class="pr-2">
                    <v-icon size="16">fal fa-search</v-icon>
                  </span>
                </template>
              </v-text-field>
            </v-flex>
            <v-flex class="mr-2">
              <DateTimeInputs
                :epochTime.sync="startRangeEpoch"
                :enableValidation="true"
                :type="DateTimeType.DATE_START_OF_DAY"
                datePrefix="From: "
                :soloInput="true"
                :boxInput="false"
                :hintTextType="HintTextType.FORMATTED_SELECTION"
                :hide-details="true"
                :clearable="true"
                :isRequired="!!endRangeEpoch"
                :readOnly="isAwaitingResponse"
                @dateTimeUpdated="sendSearchRequest(1)"
                @clear="
                  endRangeEpoch = undefined;
                  sendSearchRequest(1);
                "
              ></DateTimeInputs>
            </v-flex>
            <v-flex>
              <DateTimeInputs
                :epochTime.sync="endRangeEpoch"
                :enableValidation="true"
                :type="DateTimeType.DATE_END_OF_DAY"
                datePrefix="To: "
                :soloInput="true"
                :boxInput="false"
                :hintTextType="HintTextType.FORMATTED_SELECTION"
                :hide-details="true"
                :clearable="true"
                :isRequired="!!startRangeEpoch"
                :readOnly="isAwaitingResponse"
                @dateTimeUpdated="sendSearchRequest(1)"
                @clear="
                  startRangeEpoch = undefined;
                  sendSearchRequest(1);
                "
              ></DateTimeInputs>
            </v-flex>
          </v-layout>
        </v-form>
      </template>
    </TableTitleHeader>

    <v-data-table
      class="gd-dark-theme bordered mt-4"
      :headers="tableHeaders"
      :items="tableItems"
      hide-actions
      :loading="isAwaitingResponse || isRequestingFullSizeAttachment"
      :pagination.sync="paginationTable"
    >
      <template v-slot:items="props">
        <tr>
          <td class="text-xs-left">
            {{
              returnFormattedDate(props.item.timestamp, 'DD/MM/YYYY HH:mm a')
            }}
          </td>
          <td class="text-xs-left">{{ props.item.toAddressString }}</td>
          <td class="text-xs-left">{{ props.item.readableEmailType }}</td>
          <td class="text-xs-left">{{ props.item.subject }}</td>
          <td class="text-xs-left">
            <v-btn
              flat
              color="light-blue"
              @click="viewEmailInDialog(props.item)"
              class="v-btn-confirm-custom"
              >View</v-btn
            >
          </td>
          <td class="text-xs-left">
            {{ props.item.hasAttachment ? 'Yes' : 'No' }}
          </td>
          <!-- <td class="text-xs-right">
            <v-menu
              left
            >
              <template v-slot:activator="{ on: menu }">
                <v-tooltip bottom>
                  <template v-slot:activator="{ on: tooltip }">
                    <v-btn
                      flat
                      icon
                      v-on="{ ...tooltip, ...menu }"
                      class="ma-0"
                    >
                      <v-icon size="16">fas fa-ellipsis-v </v-icon>
                    </v-btn>
                  </template>
                  <span>View Additional Actions</span>
                </v-tooltip>
              </template>
              <v-list dense class="v-list-custom">
                <v-list-tile
                  @click="downloadAttachments(props.item.attachmentsIds)"
                >
                  <v-list-tile-title>
                    Download Attachment(s)
                  </v-list-tile-title>
                </v-list-tile>
              </v-list>
            </v-menu>
          </td> -->
          <td
            class="inner-table__cell text-right view-job-menu action-column-cell"
          >
            <div
              class="pr-2"
              v-if="props.item.isDownloadable && props.item.hasAttachment"
            >
              <v-tooltip bottom class="pr-3">
                <template v-slot:activator="{ on: tooltip }">
                  <v-btn
                    v-if="
                      reportsSettings?.allowedAccessMethods?.includes(
                        ReportAccessMethodTypes.DOWNLOAD,
                      )
                    "
                    flat
                    @click="
                      downloadAttachments(
                        props.item.attachmentsIds,
                        ReportAccessMethodTypes.DOWNLOAD,
                      )
                    "
                    icon
                    color="accent"
                    v-on="{ ...tooltip }"
                    class="ma-0"
                    :disabled="isRequestingFullSizeAttachment"
                  >
                    <v-icon size="25"> downloading </v-icon>
                  </v-btn>
                </template>
                <span>Download Attachment(s)</span>
              </v-tooltip>
              <v-tooltip bottom class="pr-2">
                <template v-slot:activator="{ on: tooltip }">
                  <v-btn
                    v-if="
                      reportsSettings?.allowedAccessMethods?.includes(
                        ReportAccessMethodTypes.EMAIL,
                      )
                    "
                    flat
                    @click="
                      downloadAttachments(
                        props.item.attachmentsIds,
                        ReportAccessMethodTypes.EMAIL,
                      )
                    "
                    icon
                    color="orange"
                    v-on="{ ...tooltip }"
                    class="ma-0"
                    :disabled="isRequestingFullSizeAttachment"
                  >
                    <v-icon size="25"> forward_to_inbox </v-icon>
                  </v-btn>
                </template>
                <span>Email Attachment(s)</span>
              </v-tooltip>
            </div>
          </td>
        </tr>
      </template>
    </v-data-table>
    <v-layout>
      <Pagination
        @pageIncrement="pageIncrement"
        :pagination="pagination"
        @change="sendSearchRequest"
        :rowsPerPage.sync="pagination.rowsPerPage"
        :showTotalCount="false"
        :currentPageCount="tableItems.length"
        :rowsPerPageList="[10]"
    /></v-layout>

    <ContentDialog
      :showDialog.sync="isViewingEmailMessageDialog"
      title="View Email"
      width="60%"
      contentPadding="pa-0"
      @cancel="isViewingEmailMessageDialog = false"
      :showActions="false"
    >
      <v-layout style="background-color: white" class="py-4">
        <v-flex md10 offset-md1>
          <div id="email-message-history-dialog"></div>
        </v-flex>
      </v-layout>
    </ContentDialog>
  </div>
</template>

<script setup lang="ts">
import DateTimeInputs, {
  DateTimeType,
  HintTextType,
} from '@/components/common/date-picker/date_time_inputs.vue';
import Pagination from '@/components/common/pagination/index.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import TableTitleHeader from '@/components/common/ui-elements/table_title_header.vue';
import {
  returnEndOfDayFromEpoch,
  returnFormattedDate,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { DivisionReportSettings } from '@/interface-models/Company/DivisionCustomConfig/DivisionCustomConfig';
import { RetrieveReportByNameRequest } from '@/interface-models/Generic/Accounting/RetrieveReportByNameRequest';
import EmailMessage from '@/interface-models/Generic/EmailMessage/EmailMessage';
import EmailMessageHistoryRequest from '@/interface-models/Generic/EmailMessage/EmailMessageHistoryRequest';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { VPagination } from '@/interface-models/Generic/VPagination';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { useAttachmentStore } from '@/store/modules/AttachmentStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useUserActivityStore } from '@/store/modules/UserActivityStore';
import DOMPurify from 'dompurify';
import moment from 'moment';
import { computed, onMounted, ref, Ref } from 'vue';

const isAwaitingResponse: Ref<boolean> = ref(false);

const page: Ref<number> = ref(1);
const rowsPerPage: Ref<number> = ref(10);

const isRequestingFullSizeAttachment: Ref<boolean> = ref(false);
const viewingEmailMessage: Ref<EmailMessage | null> = ref(null);

// Methods from DateTimeHelpers for formatting dates
const toAddressFilter: Ref<string | undefined> = ref('');
const startRangeEpoch: Ref<number | undefined> = ref(
  returnStartOfDayFromEpoch(moment().subtract(1, 'month').valueOf()),
);
const endRangeEpoch: Ref<number | undefined> = ref(returnEndOfDayFromEpoch());

// Array to store Email message data
const tableItems: Ref<EmailMessage[]> = ref([]);
const emailMessageHistoryForm = ref<any>(null);

const company = useCompanyDetailsStore().companyDetails;
const reportsSettings: DivisionReportSettings | null =
  company?.divisions?.[0]?.customConfig?.reports ?? null;

const paginationTable: VPagination = {
  descending: true,
  page: 1,
  rowsPerPage: 10,
  sortBy: 'timestamp',
};

// Define table headers for Email message history
const tableHeaders: TableHeader[] = [
  { text: 'Send At', value: 'timestamp', align: 'left' },
  { text: 'Receiver', value: 'toAddressString', align: 'left' },
  {
    text: 'Email Type',
    value: 'readableEmailType',
    align: 'left',
  },
  {
    text: 'Subject',
    value: 'subject',
    align: 'left',
  },
  { text: 'Content', value: 'hasAttachment', align: 'left' },
  { text: 'Attachment', value: 'hasAttachment', align: 'left' },
  { text: 'Actions', value: '', align: 'right' },
];

// Define pagination settings
const pagination = computed(() => ({
  ...paginationTable,
  page: page.value,
  rowsPerPage: rowsPerPage.value,
}));

const isViewingEmailMessageDialog = computed({
  get: () => viewingEmailMessage.value !== null,
  set: (value) => {
    if (!value) {
      viewingEmailMessage.value = null;
    }
  },
});

function removeImgSrc(htmlString: string) {
  const placeholderStyle =
    'width: 22px; height: 22px; background-color: #eee; border: 1px solid #ccc;';

  // Replace <img> tags with the styled placeholder box
  return htmlString.replace(
    /<img[^>]*?\ssrc=['"](.*?)['"][^>]*?>/g,
    `<div style="${placeholderStyle}"></div>`,
  );
}

function viewEmailInDialog(email: EmailMessage) {
  viewingEmailMessage.value = email;

  // Sanitize the HTML content using DOMPurify
  let sanitizedHtml = DOMPurify.sanitize(email.text);
  sanitizedHtml = removeImgSrc(sanitizedHtml);

  const el = document.getElementById('email-message-history-dialog');
  if (el) {
    el.innerHTML = sanitizedHtml;
  }
}

// Method to increment or decrement page and trigger search
function pageIncrement(value: number) {
  page.value += value;
  sendSearchRequest();
}

// Check user authorization based on roles
// function isAuthorised(): boolean {
//   return hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole();
// }

// Method to send search request to fetch Email message history
async function sendSearchRequest(pageNumber?: number) {
  if (!!pageNumber) {
    page.value = pageNumber;
  }
  if (!emailMessageHistoryForm.value.validate()) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE, {
      title: 'Email Message History',
    });
    return;
  }
  if (
    (!startRangeEpoch.value && !!endRangeEpoch.value) ||
    (!!startRangeEpoch.value && !endRangeEpoch.value)
  ) {
    showNotification(
      'Please enter both Start and End dates to search by date range.',
      {
        title: 'Email Message History',
        type: HealthLevel.INFO,
      },
    );
    return;
  }
  const request: EmailMessageHistoryRequest = {
    page: page.value,
    size: rowsPerPage.value,
    startEpoch: startRangeEpoch.value,
    endEpoch: endRangeEpoch.value,
    toAddress: toAddressFilter.value,
  };
  isAwaitingResponse.value = true;
  // Send request and handle response
  const results =
    await useUserActivityStore().searchEmailMessageHistory(request);
  // Update table data with fetched Email messages
  tableItems.value = results ?? [];
  if (tableItems.value.length === 0) {
    // Display error notification if no results found
    showNotification('No results found for the selected search criteria.', {
      title: 'Email Message History',
      type: HealthLevel.INFO,
    });
  }
  isAwaitingResponse.value = false;
}

/**
 * Downloads multiple attachments.
 *
 * This function takes a list of attachment names, creates a promise for each
 * attachment to download it, and waits for all promises to resolve. It also
 * sets and unset a flag indicating that attachments are being downloaded.
 *
 * @param nameList - An array of attachment names to download.
 */
async function downloadAttachments(
  nameList: string[],
  accessType: ReportAccessMethodTypes,
) {
  isRequestingFullSizeAttachment.value = true;
  const promises = nameList.map((name) =>
    downloadSingleAttachment(name, accessType),
  );
  await Promise.all(promises);
  isRequestingFullSizeAttachment.value = false;
}

/**
 * Downloads a single attachment.
 *
 * This function takes the name of an attachment, retrieves it from the
 * attachment store, and downloads it if it exists. If the attachment does not
 * exist, it logs an error.
 *
 * @param name - The name of the attachment to download.
 */
async function downloadSingleAttachment(
  name: string,
  accessType: ReportAccessMethodTypes,
) {
  const request: RetrieveReportByNameRequest = {
    fileName: name,
    accessType: accessType,
  };
  const result = await useAttachmentStore().getReportByName(request);
  if (result) {
    return;
  } else {
    console.error('Error downloading attachment: ', name);
  }
}

// Set store subscription and send default request on component mount
onMounted(() => {
  sendSearchRequest();
});
</script>

<style scoped lang="scss">
.top-panel {
  position: fixed;
  transition: 0.2s;
  border-bottom: 1px solid $translucent;
  background-color: var(--background-color-400);
  top: 40px;
  left: calc(25% + 37px);
  width: calc(75% - 37px);
  padding: 0 8px;
}

.email-message-history {
  padding-top: 58px;
}

#email-message-history-dialog {
  color: black;
  // font-size: 1.2em;
}
</style>
