<template>
  <ContentDialog
    :showDialog.sync="showDialog"
    title="Re-order Fleet Asset List"
    width="600px"
    contentPadding="pa-0"
    @cancel="showDialog = false"
    @confirm="confirm"
    :isLoading="isLoading"
    :isConfirmUnsaved="isConfirmUnsaved"
    confirmBtnText="Confirm"
  >
    <v-layout>
      <v-flex
        id="driver-list-order-scrollable"
        md12
        class="app-bgcolor--400 body-scrollable--75 body-min-height--65 pa-3"
        v-if="editingServiceTypeList !== null"
      >
        <draggable
          v-model="editingServiceTypeList"
          class="draggable-container"
          draggable=".dashboard-card"
          handle=".arrow-icon-container"
          v-bind="dragOptions"
        >
          <transition-group>
            <div
              class="app-bgcolor--500 dashboard-card no-hover"
              style="margin: 6px 0px"
              md12
              v-for="serviceType in editingServiceTypeList"
              :key="serviceType.serviceTypeId"
            >
              <v-layout
                justify-space-between
                align-center
                class="dashboard-card__toprow"
              >
                <div
                  class="dashboard-card__bottomrow"
                  style="user-select: none"
                >
                  <div class="icon-container" :class="{ disabled: isLoading }">
                    <span
                      class="header-icon"
                      style="font-size: 12px; font-weight: 700"
                      >{{ serviceType.shortServiceTypeName }}</span
                    >
                  </div>
                </div>
                <v-flex pl-2 pr-4>
                  <v-layout column>
                    <span class="title-text" :class="{ disabled: isLoading }">{{
                      serviceType.longServiceTypeName
                    }}</span>
                  </v-layout>
                </v-flex>

                <v-tooltip bottom>
                  <template v-slot:activator="{ on }">
                    <v-btn
                      flat
                      v-on="on"
                      icon
                      @click="sendToTop(serviceType.serviceTypeId)"
                      class="my-0"
                      :disabled="isLoading"
                    >
                      <v-icon class="arrow-icon">far fa-arrow-to-top</v-icon>
                    </v-btn>
                  </template>
                  Send to Top
                </v-tooltip>
                <v-tooltip bottom>
                  <template v-slot:activator="{ on }">
                    <v-btn
                      flat
                      v-on="on"
                      icon
                      @click="sendToBottom(serviceType.serviceTypeId)"
                      class="my-0"
                      :disabled="isLoading"
                    >
                      <v-icon class="arrow-icon">far fa-arrow-to-bottom</v-icon>
                    </v-btn>
                  </template>
                  Send to Bottom
                </v-tooltip>
                <v-divider vertical></v-divider>
                <v-btn
                  flat
                  icon
                  class="my-0 arrow-icon-container"
                  :disabled="isLoading"
                >
                  <v-icon class="arrow-icon">far fa-grip-lines</v-icon>
                </v-btn>
              </v-layout>
            </div>
          </transition-group>
        </draggable>
        <!-- <v-layout row wrap v-if="editingServiceTypeList !== null" md12>
      </v-layout> -->
      </v-flex>
    </v-layout>
  </ContentDialog>
</template>

<script setup lang="ts">
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { deepCopy } from '@/helpers/JsonHelpers/JsonHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import { ServiceTypeDisplayOrderMapping } from '@/interface-models/Generic/ServiceTypes/ServiceTypesDisplayOrder/ServiceTypeDisplayOrderMapping';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import {
  computed,
  ComputedRef,
  ref,
  Ref,
  watch,
  WritableComputedRef,
} from 'vue';
import draggable from 'vuedraggable';

const props = defineProps<{
  dialogIsOpen: boolean;
  serviceTypesList: ServiceTypes[];
}>();

const emit = defineEmits<{
  (event: 'update:dialogIsOpen', value: boolean): void;
}>();

const originalServiceTypeList: Ref<ServiceTypes[] | null> = ref(null);
const editingServiceTypeList: Ref<ServiceTypes[] | null> = ref(null);

const isLoading: Ref<boolean> = ref(false);

/**
 * Returns the prop dialogIsOpen and syncs the value to the parent when showDialog
 * is updated
 */
const showDialog: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.dialogIsOpen;
  },
  set(value: boolean): void {
    emit('update:dialogIsOpen', value);
  },
});

/**
 * When showDialog changes, set the editingServiceTypeList to the listOrder.
 * editingServiceTypeList is modelled to the draggable list in the HTML and
 * will update as the user drags and drops items
 */
watch(
  () => showDialog.value,
  (newValue: boolean) => {
    if (newValue) {
      editingServiceTypeList.value = props.serviceTypesList
        .filter((s) => s.longServiceTypeName !== 'UNIT RATE')
        .sort((a, b) => a.displayOrder - b.displayOrder);

      // Set originalServiceTypeList to clone of editingServiceTypeList
      originalServiceTypeList.value = deepCopy(editingServiceTypeList.value);
    } else {
      editingServiceTypeList.value = null;
      originalServiceTypeList.value = null;
    }
  },
  { immediate: true },
);

/**
 * Modelled to ContentDialog component. Used to show a confirmation dialog
 * when changes have been made
 */
const isConfirmUnsaved: ComputedRef<boolean> = computed(() => {
  return (
    JSON.stringify(editingServiceTypeList.value) !==
    JSON.stringify(originalServiceTypeList.value)
  );
});

/**
 * Moves the service type with the specified ID to the top of the list.
 *
 * @param {number} serviceTypeId - The ID of the service type to move.
 */
function sendToTop(serviceTypeId: number): void {
  if (!editingServiceTypeList.value) {
    return;
  }
  // Find serviceType in the editing list and move it to the first index in the array
  const serviceTypeIndex = editingServiceTypeList.value.findIndex(
    (s) => s.serviceTypeId === serviceTypeId,
  );
  if (serviceTypeIndex === -1) {
    return;
  }
  const serviceType = editingServiceTypeList.value.splice(serviceTypeIndex, 1);
  editingServiceTypeList.value.unshift(serviceType[0]);
}

/**
 * Moves the service type with the specified ID to the bottom of the list.
 *
 * @param {number} serviceTypeId - The ID of the service type to move.
 */
function sendToBottom(serviceTypeId: number): void {
  if (!editingServiceTypeList.value) {
    return;
  }
  // Find serviceType in the editing list and move it to the last index in the array
  const serviceTypeIndex = editingServiceTypeList.value.findIndex(
    (s) => s.serviceTypeId === serviceTypeId,
  );
  if (serviceTypeIndex === -1) {
    return;
  }
  const serviceType = editingServiceTypeList.value.splice(serviceTypeIndex, 1);
  editingServiceTypeList.value.push(serviceType[0]);
}

/**
 * Called when the confirmation button is pressed in the ContentDialog.
 * Constructs the mapping from the reordered list of service types and emits the
 * updated list to the parent component.
 */
async function confirm(): Promise<void> {
  if (!editingServiceTypeList.value) {
    return;
  }

  /**
   * Constructs the mapping from the reordered list of service types.
   */
  const mappings: ServiceTypeDisplayOrderMapping[] =
    editingServiceTypeList.value.map((s, index) => {
      return {
        _id: s._id ?? '',
        displayOrder: (index + 1) * 10,
      };
    });

  await updateDisplayOrder(mappings);

  showDialog.value = false;
}

/**
 * Handles emit from ServiceTypesOrderDialog. Sends request to backend to update
 * the display order.
 * @param updatedMappings - The updated display order mappings.
 */
async function updateDisplayOrder(
  updatedMappings: ServiceTypeDisplayOrderMapping[],
) {
  isLoading.value = true;
  const result =
    await useCompanyDetailsStore().updateServiceTypesDisplayOrder(
      updatedMappings,
    );
  if (!result || !result?.displayOrderMappings) {
    showNotification(GENERIC_ERROR_MESSAGE, {
      title: 'Update Service Type Order',
    });
  } else {
    showNotification('Display order updated successfully', {
      type: HealthLevel.SUCCESS,
      title: 'Update Service Type Order',
    });
  }
  isLoading.value = false;
}

/**
 * Returns the drag options for the draggable component
 */
const dragOptions = computed(() => {
  return {
    chosenClass: 'app-bgcolor--700',
  };
});
</script>
