<template>
  <section class="service-types-table">
    <v-data-table
      :headers="headers"
      :items="serviceTypesTableData"
      class="gd-dark-theme"
      :rows-per-page-items="[10, 15, 20]"
      disable-initial-sort
    >
      <template v-slot:items="tableProps">
        <tr
          @click="viewServiceTypeItem(tableProps.item._id)"
          style="cursor: pointer"
        >
          <td>{{ tableProps.item.shortServiceTypeName }}</td>
          <td>{{ tableProps.item.longServiceTypeName }}</td>
          <td>{{ tableProps.item.optionSelectName }}</td>
          <td>{{ tableProps.item.divisionService ? 'Yes' : 'No' }}</td>
        </tr>
      </template>
    </v-data-table>
  </section>
</template>
<script setup lang="ts">
import { hasAdminRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { ComputedRef, computed } from 'vue';

const props = withDefaults(
  defineProps<{
    serviceTypes: ServiceTypes[];
    searchQuery: string;
  }>(),
  {
    serviceTypes: () => [],
    searchQuery: '',
  },
);

const emit = defineEmits<{
  (event: 'viewServiceTypes', payload: string): void;
}>();

const isAuthorised: ComputedRef<boolean> = computed(() => {
  return hasAdminRole();
});

/**
 * Emits an event to view the ServiceTypes by ID.
 * @param {string} id - The ID of the ServiceTypes item.
 */
function viewServiceTypeItem(id: string): void {
  if (isAuthorised.value) {
    emit('viewServiceTypes', id);
  }
}

const headers: TableHeader[] = [
  {
    text: 'Service Code',
    align: 'left',
    value: 'shortServiceTypeName',
    sortable: true,
  },
  {
    text: 'Name',
    align: 'left',
    value: 'longServiceTypeName',
    sortable: true,
  },
  {
    text: 'Display Name',
    align: 'left',
    value: 'optionSelectName',
    sortable: true,
  },
  {
    text: 'Available in Default Rates',
    align: 'left',
    value: 'divisionService',
    sortable: true,
  },
];

/**
 * Returns the service type filtered by the search query
 * @returns {ServiceTypes[]} - service types matching the search query
 */
const serviceTypesTableData: ComputedRef<ServiceTypes[]> = computed(() => {
  const query = props.searchQuery.toLowerCase();

  return props.serviceTypes.filter((item) => {
    // Filter by search query
    const matchesSearchQuery =
      !query ||
      item.longServiceTypeName.toLowerCase().includes(query) ||
      item.shortServiceTypeName.toLowerCase().includes(query);

    // Return true if both filters match
    return matchesSearchQuery;
  });
});
</script>

<style scoped lang="scss"></style>
