<template>
  <ContentDialog
    :showDialog.sync="dialogController"
    :title="
      !editedServiceTypesItem?._id ? 'New Service Type' : 'Edit Service Type'
    "
    width="40%"
    contentPadding="pa-0"
    @cancel="cancelChanges"
    @confirm="saveServiceType"
    :showActions="true"
    :isConfirmUnsaved="false"
    :isDisabled="isFormDisabled"
    :isLoading="isAwaitingSaveResponse"
    confirmBtnText="Confirm"
  >
    <v-layout>
      <v-flex
        md12
        class="body-scrollable--75 body-min-height--65 pa-3"
        v-if="editedServiceTypesItem !== null"
      >
        <v-form ref="serviceTypeDialogForm" class="service-type-edit-dialog">
          <v-layout wrap>
            <v-flex md12 pb-3
              ><v-layout align-center>
                <h5 class="subheader--bold pr-3 pt-1">Service Type Details</h5>
                <v-flex>
                  <v-divider></v-divider>
                </v-flex>
              </v-layout>
            </v-flex>

            <v-flex md12>
              <v-layout>
                <v-flex md4> </v-flex>
                <v-flex md8>
                  <v-layout>
                    <span class="display-name pr-3 pb-0">{{
                      serviceTypeDisplayName
                    }}</span>
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>

            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6
                      class="subheader--faded pr-3 pb-0 form-field-required-marker"
                    >
                      Service Code
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-text-field
                    class="v-solo-custom"
                    solo
                    flat
                    color="light-blue"
                    :disabled="isFormDisabled"
                    label="Charge Name"
                    v-model="editedServiceTypesItem.shortServiceTypeName"
                    :rules="[
                      validate.validationRules.required,
                      validate.uniqueShortName,
                    ]"
                    hint="This will appear on the invoice"
                  />
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6
                      class="subheader--faded pr-3 pb-0 form-field-required-marker"
                    >
                      Name
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-text-field
                    class="v-solo-custom"
                    solo
                    flat
                    color="light-blue"
                    :disabled="isFormDisabled"
                    label="Charge Name"
                    v-model="editedServiceTypesItem.longServiceTypeName"
                    :rules="[validate.validationRules.required]"
                    hint="This will appear on the invoice"
                  />
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">
                      Available in Default Rates
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-checkbox
                    v-model="editedServiceTypesItem.divisionService"
                    :disabled="isFormDisabled"
                    :label="
                      editedServiceTypesItem.divisionService ? 'Yes' : 'No'
                    "
                    color="light-blue"
                    class="mt-2"
                  />
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">
                      Display Order Position
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-layout align-center class="form-field-label-container">
                    <span
                      class="display-order-txt pr-3 pb-0"
                      v-if="!serviceTypesItem?._id"
                    >
                      Default (Last)
                    </span>

                    <span v-else class="display-order-txt">{{
                      editedItemIndex
                    }}</span>

                    <v-tooltip right v-if="!serviceTypesItem?._id">
                      <template v-slot:activator="{ on }">
                        <v-btn
                          v-on="on"
                          :disabled="
                            !editedServiceTypesItem.shortServiceTypeName ||
                            !editedServiceTypesItem.longServiceTypeName ||
                            serviceTypesItem?._id
                          "
                          icon
                          small
                          @click="saveAndReorderServiceTypes"
                        >
                          <v-icon>edit</v-icon>
                        </v-btn>
                      </template>
                      <span
                        >Save {{ serviceTypeDisplayName }} ServiceType &
                        Reorder</span
                      >
                    </v-tooltip>
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-form>
      </v-flex>
    </v-layout>
  </ContentDialog>
</template>

<script setup lang="ts">
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { deepCopy } from '@/helpers/JsonHelpers/JsonHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  GENERIC_ERROR_MESSAGE,
  GENERIC_SUCCESS_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';

import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import {
  computed,
  ComputedRef,
  Ref,
  ref,
  watch,
  WritableComputedRef,
} from 'vue';

const props = withDefaults(
  defineProps<{
    isDialogOpen: boolean;
    serviceTypesItem: ServiceTypes | null;
  }>(),
  {
    serviceTypesItem: null,
  },
);

const isAwaitingSaveResponse: Ref<boolean> = ref(false);
const serviceTypeDialogForm: Ref<any> = ref(null);
const editedServiceTypesItem: Ref<ServiceTypes | null> = ref(null);

// list of service types from store
const serviceTypesList = useCompanyDetailsStore().getServiceTypesList;

// emits to control dialogs
const emit = defineEmits<{
  (event: 'update:isDialogOpen', payload: boolean): void;
  (event: 'update:reorderDialogIsOpen', payload: boolean): void;
}>();

const validate = {
  validationRules,
  // extra validation rule to check for unique shortName for new serviceTypes
  uniqueShortName: (value: string) => {
    const existingNames = serviceTypesList.map((item) =>
      item.shortServiceTypeName.toUpperCase(),
    );

    if (!props.serviceTypesItem?._id) {
      if (existingNames.includes(value.toUpperCase())) {
        return 'This short name is already in use';
      }
    }
    return true;
  },
};

// combine short name and long name for display Name
const serviceTypeDisplayName = computed(() => {
  if (editedServiceTypesItem.value) {
    return (
      editedServiceTypesItem.value.shortServiceTypeName +
      ' - ' +
      editedServiceTypesItem.value.longServiceTypeName
    );
  }
  return '';
});

// function to return index of serviceTypes for display order label
function getServiceTypeIndex(item: ServiceTypes | null): number | -1 {
  if (!item) {
    return -1;
  }
  const index = serviceTypesList.findIndex(
    (serviceType) => serviceType.serviceTypeId === item.serviceTypeId,
  );
  return index !== -1 ? index + 1 : 1;
}

// Computed property to get the index in the template
const editedItemIndex = computed(() => {
  return getServiceTypeIndex(editedServiceTypesItem.value);
});

/**
 * Saves the currently edited service type item after validating the form.
 * Displays a success or error notification based on the result.
 * Closes the dialog after completion.
 *
 * @returns {Promise<void>} A promise that resolves when the save operation completes.
 */
async function saveServiceType(): Promise<void> {
  if (!editedServiceTypesItem.value) {
    return;
  }
  if (!serviceTypeDialogForm.value?.validate()) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE, {
      title: 'Service Type Maintenance',
      type: HealthLevel.ERROR,
    });
    return;
  }

  editedServiceTypesItem.value.optionSelectName = serviceTypeDisplayName.value;
  isAwaitingSaveResponse.value = true;

  const result = await useCompanyDetailsStore().saveServiceTypesItem(
    editedServiceTypesItem.value,
  );
  if (result) {
    showNotification(GENERIC_SUCCESS_MESSAGE, {
      title: 'Service Type Maintenance',
      type: HealthLevel.SUCCESS,
    });
  } else {
    showNotification(GENERIC_ERROR_MESSAGE, {
      title: 'Service Type Maintenance',
      type: HealthLevel.ERROR,
    });
  }
  isAwaitingSaveResponse.value = false;
  dialogController.value = false;
}

function cancelChanges() {
  if (props.serviceTypesItem) {
    // Reset to the initial service type item passed from props
    editedServiceTypesItem.value = { ...props.serviceTypesItem };
  }
  dialogController.value = false;
}

// async function to await save ServiceType item and open re-order dialog
async function saveAndReorderServiceTypes() {
  await saveServiceType();
  emit('update:reorderDialogIsOpen', true);
}

/**
 * Gets and sets the isDialogOpen prop passed from parent
 */
const dialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.isDialogOpen;
  },
  set(value: boolean): void {
    emit('update:isDialogOpen', value);
  },
});

const isFormDisabled: ComputedRef<boolean> = computed(() => {
  return isAwaitingSaveResponse.value || !isAuthorised.value;
});

const isAuthorised: ComputedRef<boolean> = computed(() => {
  return hasAdminOrHeadOfficeRole();
});

/**
 * When the visibility of the dialog changes, sets the editedServiceTypesItem to a clone
 * of the serviceTypesItem prop for use in the form
 */
watch(
  () => props.isDialogOpen,
  (newVal) => {
    if (newVal && props.serviceTypesItem) {
      editedServiceTypesItem.value = deepCopy(props.serviceTypesItem);
    } else {
      editedServiceTypesItem.value = null;
    }
  },
);
</script>

<style scoped lang="scss">
.service-type-edit-dialog {
  .subsection__header {
    font-size: $font-size-large;
    font-weight: 600;
    color: grey;
    text-transform: uppercase;
  }
}

.display-name {
  width: 100%;
  font-size: $font-size-22;
  font-weight: 500;
  color: var(--primary-light);
  text-transform: uppercase;
  margin-bottom: 20px;
}

.display-order-txt {
  font-weight: 600;
  font-size: $font-size-16;
  color: var(--text-color);
  letter-spacing: 0.5px;
}
</style>
