<template>
  <div
    id="service-types-administration-index"
    class="service-types-administration-index"
  >
    <TableTitleHeader>
      <template #title>
        <GTitle
          title="Service Types Administration"
          subtitle="View or re-order Service Types"
          :divider="false"
        />
      </template>

      <template #buttons>
        <GButton
          icon="fas fa-sort-alt"
          :iconRight="false"
          :disabled="!isAuthorised()"
          @click="reorderDialogIsOpen = true"
          :color="`var(--primary)`"
          class="mr-4"
        >
          Re-order Service Types</GButton
        >
        <GButton
          icon="fal fa-plus"
          :iconRight="false"
          :disabled="!hasAdminRole()"
          @click="addNewServiceType"
        >
          Add new Service Type</GButton
        >
      </template>

      <template #inputs>
        <v-flex md6>
          <v-text-field
            appendIcon="search"
            label="Search Service Types"
            hint="Search Service Types by Name"
            color="orange"
            height="30"
            outline
            solo
            flat
            class="v-solo-custom"
            clearable
            v-model="searchQuery"
          >
          </v-text-field>
        </v-flex>
      </template>
    </TableTitleHeader>

    <v-flex md12 class="pb-2">
      <ServiceTypesTable
        :serviceTypes="serviceTypeItems"
        :searchQuery="searchQuery"
        @viewServiceTypes="editExistingChargeItem"
      ></ServiceTypesTable>
    </v-flex>

    <ServiceTypesOrderDialog
      :dialogIsOpen.sync="reorderDialogIsOpen"
      :serviceTypesList="serviceTypeItems"
      @cancelEdit="cancelEdit"
    ></ServiceTypesOrderDialog>

    <ServiceTypesEditDialog
      :key="editedServiceTypesItem?._id || 'new'"
      :serviceTypesItem="editedServiceTypesItem"
      @cancelEdit="cancelEdit"
      :isDialogOpen.sync="editDialogController"
      :reorderDialogIsOpen.sync="reorderDialogIsOpen"
    />
  </div>
</template>

<script setup lang="ts">
import ServiceTypesEditDialog from '@/components/admin/Administration/service_types_administration/components/service_types_edit_dialog.vue';
import ServiceTypesOrderDialog from '@/components/admin/Administration/service_types_administration/components/service_types_order_dialog.vue';
import ServiceTypesTable from '@/components/admin/Administration/service_types_administration/components/service_types_table.vue';
import TableTitleHeader from '@/components/common/ui-elements/table_title_header.vue';
import {
  hasAdminOrHeadOfficeOrBranchManagerRole,
  hasAdminRole,
} from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { sessionManager } from '@/store/session/SessionState';
import {
  ComputedRef,
  Ref,
  WritableComputedRef,
  computed,
  nextTick,
  ref,
} from 'vue';

const editedServiceTypesItem: Ref<ServiceTypes | null> = ref(null);

const searchQuery: Ref<string> = ref('');

const reorderDialogIsOpen: Ref<boolean> = ref(false);
const addServiceTypeDialog: Ref<boolean> = ref(false);

// list of service types from store
const serviceTypeItems: ComputedRef<ServiceTypes[]> = computed(() => {
  return useCompanyDetailsStore().getServiceTypesList;
});

/**
 * Checks if the user is authorized.
 * @returns {boolean} True if the user has admin or head office role, false otherwise.
 */
function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeOrBranchManagerRole();
}

/**
 * Creates a new service type with default values and opens it in the edit dialog.
 * The `displayOrder` is determined by taking the last item's `displayOrder` and adding 10.
 * If the list is empty, it defaults to `10`.
 */
function addNewServiceType() {
  const lastItem = serviceTypeItems.value.map((item) => item.displayOrder);

  // calculate new display order
  const newDisplayOrder = lastItem.length > 0 ? Math.max(...lastItem) + 10 : 10;

  // Creating a new service type object with default values
  const serviceTypeItem: ServiceTypes = {
    company: sessionManager.getCompanyId(),
    division: sessionManager.getDivisionId(),
    serviceTypeId: 0,
    shortServiceTypeName: '',
    longServiceTypeName: '',
    availableJobInputScreens: [], // Deprecated but still needs a default value
    optionSelectName: '',
    recurringRequirement: false, // Deprecated
    fuelSurcharge: false, // Deprecated
    divisionService: false,
    allocationTypes: [], // Deprecated
    displayOrder: newDisplayOrder,
  };

  // Open the dialog with the new service type
  openServiceItemInDialog(serviceTypeItem);
}

/**
 * Cancels the edit operation.
 */

function cancelEdit() {
  editedServiceTypesItem.value = null;
}

/**
 * Finds and opens an existing service type item in the edit dialog based on its ID.
 * @param {string} id - The unique identifier of the service type item to be edited.
 */
function editExistingChargeItem(id: string): void {
  // Find the service type item by ID from the list of service type items
  const serviceTypes: ServiceTypes | undefined = serviceTypeItems.value.find(
    (type: ServiceTypes) => type._id === id,
  );
  if (!serviceTypes) {
    return;
  }
  openServiceItemInDialog(serviceTypes);
}

/**
 * Opens the service type item in the edit dialog.
 * Sets the provided service type as the currently edited item
 * and ensures the dialog opens in the next UI tick.
 *
 * @param {ServiceTypes} item - The service type item to be edited.
 */
function openServiceItemInDialog(item: ServiceTypes): void {
  // Set the selected service type item for editing
  editedServiceTypesItem.value = item;
  nextTick(() => {
    editDialogController.value = true;
  });
}

// sets addServiceType Dialog
const editDialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return addServiceTypeDialog.value;
  },
  set(value: boolean): void {
    if (!value) {
      editedServiceTypesItem.value = null;
    }
    addServiceTypeDialog.value = value;
  },
});
</script>

<style scoped lang="scss"></style>
