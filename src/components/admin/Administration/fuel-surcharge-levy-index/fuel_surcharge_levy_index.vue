<template>
  <v-layout class="fuel-surcharge-levy-index">
    <div class="top-panel"></div>
    <v-flex md12>
      <v-layout
        v-if="awaitingAllFuelSurcharges || awaitingCurrentFuelSurcharge"
        justify-center
      >
        <v-progress-circular indeterminate color="orange"></v-progress-circular>
      </v-layout>

      <v-alert :value="isExpiredFuelSurcharge">
        The Current active fuel surcharge has <b>EXPIRED</b> and needs
        attention.
      </v-alert>

      <FuelSurchargeLevy
        v-if="!awaitingAllFuelSurcharges && !awaitingCurrentFuelSurcharge"
        :isExpiredFuelSurcharge="isExpiredFuelSurcharge"
        :activeFuelSurchargeTableId="activeFuelSurchargeTableId"
        :fuelSurchargeRate="fuelSurchargeRate"
        :allFuelSurchargeRates="allFuelSurchargeRates"
        :isDivision="type === 'CLIENT'"
        :isCashSale="entityId === 'CS'"
        :isFleetAsset="type === 'FLEET_ASSET'"
        :activeFuelSurchargeRate="activeFuelSurchargeRate"
        @setSelectedFuelSurchargeRate="setSelectedFuelSurchargeRate"
        @setNewFuelSurchargeRate="setNewFuelSurchargeRate"
        @refreshFuelSurchargeRates="updateLocalData"
      />
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import FuelSurchargeLevy from '@/components/common/fuel-surcharge-levy/index.vue';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import moment from 'moment-timezone';

import { deepCopy } from '@/helpers/JsonHelpers/JsonHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import FleetAssetFuelSurchargeRate from '@/interface-models/ServiceRates/FleetAssetOwner/AdditionalCharges/FuelSurcharge/FleetAssetFuelSurchargeRate';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useFuelLevyStore } from '@/store/modules/FuelLevyStore';
import { sessionManager } from '@/store/session/SessionState';
import { computed, onMounted, ref, Ref } from 'vue';

const props = defineProps<{
  entityId: string;
  type?: RateEntityType;
}>();

const componentTitle: string = 'Client Fuel Levy';

const companyDetailsStore = useCompanyDetailsStore();

const fuelSurchargeRate: Ref<
  ClientFuelSurchargeRate | FleetAssetFuelSurchargeRate | null
> = ref(null);
const awaitingCurrentFuelSurcharge: Ref<boolean> = ref(true);
const awaitingAllFuelSurcharges: Ref<boolean> = ref(true);
const allFuelSurchargeRates: Ref<
  ClientFuelSurchargeRate[] | FleetAssetFuelSurchargeRate[]
> = ref([]);
const activeFuelSurchargeTableId: Ref<number | null | undefined> = ref(null);
const isExpiredFuelSurcharge: Ref<boolean> = ref(false);
const activeFuelSurchargeRate: Ref<
  ClientFuelSurchargeRate | FleetAssetFuelSurchargeRate | null
> = ref(null);

const clientName = computed(() => {
  if (props.type === 'CLIENT') {
    return sessionManager.getCompanyId() + ' ' + sessionManager.getDivisionId();
  } else if (props.type === 'FLEET_ASSET') {
    return 'Fleet Asset';
  } else {
    return 'Cash Sales';
  }
});

function setCurrentFuelSurcharge(
  fuelSurchargeList:
    | ClientFuelSurchargeRate[]
    | FleetAssetFuelSurchargeRate[]
    | null,
) {
  awaitingCurrentFuelSurcharge.value = false;
  if (fuelSurchargeList?.length) {
    // TODO: FEAP-182 - check if this logic is suitable after the components have been updated
    if (fuelSurchargeList[0] instanceof ClientFuelSurchargeRate) {
      activeFuelSurchargeRate.value = new ClientFuelSurchargeRate(
        fuelSurchargeList[0],
      );
      fuelSurchargeRate.value = fuelSurchargeList[0];
      activeFuelSurchargeTableId.value = fuelSurchargeList[0].tableId;
    } else if (fuelSurchargeList[0] instanceof FleetAssetFuelSurchargeRate) {
      activeFuelSurchargeRate.value = new FleetAssetFuelSurchargeRate(
        fuelSurchargeList[0],
      );
      fuelSurchargeRate.value = fuelSurchargeList[0];
      activeFuelSurchargeTableId.value = fuelSurchargeList[0].tableId;
    }
  } else {
    activeFuelSurchargeTableId.value = null;
    isExpiredFuelSurcharge.value = false;
    showAppNotification(
      `${clientName.value} currently has no Active Fuel Surcharge`,
    );
    return;
  }
}

function setAllFuelSurchargeRates(
  fuelSurchargeList: ClientFuelSurchargeRate[] | FleetAssetFuelSurchargeRate[],
) {
  allFuelSurchargeRates.value = fuelSurchargeList;
  awaitingAllFuelSurcharges.value = false;
  const currentTime = moment.tz(companyDetailsStore.userLocale).valueOf();
  for (const fuelSurcharge of allFuelSurchargeRates.value) {
    if (
      fuelSurcharge.validFromDate &&
      fuelSurcharge.validToDate &&
      fuelSurcharge.tableId &&
      currentTime >= fuelSurcharge.validFromDate &&
      currentTime <= fuelSurcharge.validToDate
    ) {
      isExpiredFuelSurcharge.value = false;
      activeFuelSurchargeTableId.value = fuelSurcharge.tableId;
    }
  }
}

function setSelectedFuelSurchargeRate(
  fuelSurcharge: ClientFuelSurchargeRate | FleetAssetFuelSurchargeRate,
) {
  fuelSurchargeRate.value = deepCopy(fuelSurcharge);
}

function setNewFuelSurchargeRate() {
  if (props.type === 'FLEET_ASSET') {
    fuelSurchargeRate.value = new FleetAssetFuelSurchargeRate();
    fuelSurchargeRate.value.company = sessionManager.getCompanyId();
    fuelSurchargeRate.value.division = sessionManager.getDivisionId();
    fuelSurchargeRate.value.fleetAssetIds = [props.entityId];
  } else {
    fuelSurchargeRate.value = new ClientFuelSurchargeRate();
    fuelSurchargeRate.value.company = sessionManager.getCompanyId();
    fuelSurchargeRate.value.division = sessionManager.getDivisionId();
    fuelSurchargeRate.value.clientIds = [props.entityId];
  }
}
// requests the latest fuel surcharges
async function updateLocalData() {
  const fuelLevyStore = useFuelLevyStore();
  if (props.type === 'FLEET_ASSET') {
    const [fuelList, currentFuelSurcharges] = await Promise.all([
      fuelLevyStore.getAllFleetAssetFuelSurchargeRates(props.entityId),
      fuelLevyStore.getCurrentFleetAssetFuelSurchargeRates(
        props.entityId,
        moment().valueOf(),
      ),
    ]);
    setAllFuelSurchargeRates(fuelList ?? []);
    setCurrentFuelSurcharge(currentFuelSurcharges);
  } else {
    const [fuelList, currentFuel] = await Promise.all([
      fuelLevyStore.getClientFuelSurchargeList(props.entityId),
      fuelLevyStore.getCurrentClientFuelSurcharges(
        props.entityId,
        moment().valueOf(),
      ),
    ]);
    setAllFuelSurchargeRates(fuelList ?? []);
    setCurrentFuelSurcharge(currentFuel);
  }
}

// Trigger app notification. Defaults to ERROR type message, but type can be
// provided to produce other types. Includes componentTitle as a title for the
// notification.
function showAppNotification(text: string, type?: HealthLevel): void {
  showNotification(text, {
    type,
    title: componentTitle,
  });
}

onMounted(() => {
  activeFuelSurchargeTableId.value = null;
  updateLocalData();
});
</script>

<style scoped lang="scss">
.top-panel {
  position: fixed;
  transition: 0.2s;
  border-bottom: 1px solid $translucent;
  background-color: var(--background-color-400);
  top: 40px;
  height: 49px;
  left: calc(25% + 37px);
  width: calc(75% - 37px);
  padding: 0 8px;
  display: flex;
  align-items: center;
}

.fuel-surcharge-levy-index {
  padding-top: 67px;
}
</style>
