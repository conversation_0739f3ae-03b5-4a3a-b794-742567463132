<template>
  <div class="account-recovery-maintenance pa-3">
    <GTitle
      title="Account Recovery Request"
      subtitle="Please review the account recovery request below"
    />

    <GTextField
      :value="
        props.accountRecoveryRequest.firstName +
        ' ' +
        props.accountRecoveryRequest.lastName
      "
      :placeholder="'The name associated to the user account'"
      :disabled="true"
    ></GTextField>
    <GTextField
      :value="props.accountRecoveryRequest.username"
      :placeholder="'The user who is asking to recover their account'"
      :disabled="true"
    ></GTextField>

    <GTextField
      :value="props.accountRecoveryRequest.newEmailAddress"
      :placeholder="'The users new email address'"
      :disabled="true"
    ></GTextField>

    <GTextField
      :value="props.accountRecoveryRequest.newContactNumber"
      :placeholder="'The users new mobile number'"
      :disabled="true"
    ></GTextField>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue';

import { PasswordRecoveryRequest } from '@/interface-models/User/PasswordRecovery';
interface IProps {
  accountRecoveryRequest: PasswordRecoveryRequest;
}
const props = withDefaults(defineProps<IProps>(), {});

onMounted(async () => {});

onUnmounted(() => {});
</script>
<style scoped lang="scss">
.account-recovery-management {
  padding-top: 58px;
}
</style>
