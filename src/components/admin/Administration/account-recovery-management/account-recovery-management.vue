<template>
  <div class="account-recovery-management">
    <AccountRecoveryTable
      :isOperationsDashboardDialog="isOperationsDashboardDialog"
      @setSelectedAccountRecoveryRequest="setSelectedAccountRecoveryRequest"
    />
    <GDialog
      v-if="accountRecoveryRequest"
      :width="'65%'"
      :title="'Account Recovery Request'"
      :confirmBtnText="'Submit'"
      :confirmDisabled="false"
      @closeDialog="cancel"
      @confirm="submitAccountRecovery"
      :isLoading="isLoading"
    >
      <AccountRecoveryMaintenance
        v-if="accountRecoveryRequest"
        :accountRecoveryRequest="accountRecoveryRequest"
      />

      <div class="pa-3">
        <GTitle
          title="Account Recovery Verification"
          subtitle="Please answer the questions below"
        />

        <v-layout class="section-container">
          <v-flex md6 class="container">
            <p class="input-txt">
              Is there a clear photo of a driver\'s licence displayed?
            </p>
            <GSelect
              v-model="isDriverLicenseClear"
              :items="[
                {
                  key: 'Yes, the driver\'s licence photo is clear.',
                  value: true,
                },
                {
                  key: 'No, the driver\'s licence photo is illegible',
                  value: false,
                },
              ]"
              :disabled="false"
              :item-text="'key'"
              :item-value="'value'"
              :placeholder="'Is there a clear photo of a driver\'s licence displayed?'"
              :hideDetails="true"
            />
            <p class="input-txt">Is the driver's licence current?</p>
            <GSelect
              v-model="isDriverLicenseCurrent"
              :items="[
                {
                  key: 'Yes, the driver\'s licence photo is current.',
                  value: true,
                },
                {
                  key: 'No, the driver\'s licence photo has expired.',
                  value: false,
                },
              ]"
              :disabled="false"
              :item-text="'key'"
              :item-value="'value'"
              :placeholder="'Is the driver\'s licence current?'"
              :hideDetails="true"
            />

            <p class="input-txt">
              Does the name shown on the driver's licence match
              {{ accountRecoveryRequest.firstName }}

              {{ accountRecoveryRequest.lastName }}
              ?
            </p>
            <GSelect
              v-model="isUsernameAndDriverLicenseNameMatch"
              :items="[
                {
                  key: 'Yes, the name does match',
                  value: true,
                },
                {
                  key: 'No, the names do not match',
                  value: false,
                },
              ]"
              :disabled="false"
              :item-text="'key'"
              :item-value="'value'"
              :placeholder="
                'Does the name shown on the driver\'s licence match ' +
                accountRecoveryRequest.firstName +
                ' ' +
                accountRecoveryRequest.lastName
              "
              :hideDetails="true"
            />

            <FileUpload
              class="image-container"
              :imageLabel="'Front Of Drivers Licence'"
              :attachmentSingle="true"
              :documentTypeId="2"
              :formDisabled="true"
              :attachment="accountRecoveryRequest.licensePhoto"
            />
          </v-flex>

          <v-flex md6 class="container">
            <p class="input-txt">
              Is there a clear photo of a person in the workplace photo?
            </p>
            <GSelect
              v-if="accountRecoveryRequest.workplacePhoto"
              v-model="isPersonVisibleInWorkplacePhoto"
              :items="[
                {
                  key: 'Yes, a person is visible in the photo',
                  value: true,
                },
                {
                  key: 'No, there isn\'t a person in the photo',
                  value: false,
                },
              ]"
              :disabled="false"
              :item-text="'key'"
              :item-value="'value'"
              :placeholder="'Is there a clear photo of a person in the workplace photo?'"
              :hideDetails="true"
            />

            <p class="input-txt">
              Is the person in the workplace photo the same as on the licence?
            </p>
            <GSelect
              v-model="isWorkplaceAndLicenseMatchingPerson"
              v-if="accountRecoveryRequest.workplacePhoto"
              :items="[
                {
                  key: 'Yes, they are the same person',
                  value: true,
                },
                {
                  key: 'No, they are not the same',
                  value: false,
                },
              ]"
              :disabled="false"
              :item-text="'key'"
              :item-value="'value'"
              :placeholder="'Is the person in the workplace photo the same as on the licence?'"
              :hideDetails="true"
            />

            <p class="input-txt">
              Do you personally know {{ accountRecoveryRequest.firstName }}
              {{ accountRecoveryRequest.lastName }} and recognise the photograph
              to be
              {{ accountRecoveryRequest.firstName }}
              {{ accountRecoveryRequest.lastName }} ?
            </p>
            <GSelect
              v-model="isPersonalRecognition"
              :items="[
                {
                  key: 'Yes, I recognize the username as the person in the licence.',
                  value: true,
                },
                {
                  key: 'No, I do not recognize the username as the person in the licence.',
                  value: false,
                },
              ]"
              :disabled="false"
              :item-text="'key'"
              :item-value="'value'"
              :placeholder="
                'Do you personally know ' +
                accountRecoveryRequest.firstName +
                ' ' +
                accountRecoveryRequest.lastName +
                ' and recognise the photograph to be ' +
                accountRecoveryRequest.firstName +
                ' ' +
                accountRecoveryRequest.lastName +
                '?'
              "
              :hideDetails="true"
            />

            <FileUpload
              class="image-container"
              v-if="accountRecoveryRequest.workplacePhoto"
              :imageLabel="'Users Work Place Photo'"
              :attachmentSingle="true"
              :documentTypeId="2"
              :formDisabled="true"
              :attachment="accountRecoveryRequest.workplacePhoto"
            />
          </v-flex>
        </v-layout>
      </div>
    </GDialog>
  </div>
</template>

<script setup lang="ts">
import AccountRecoveryMaintenance from '@/components/admin/Administration/account-recovery-management/account-recovery-maintenance.vue';
import AccountRecoveryTable from '@/components/admin/Administration/account-recovery-management/account-recovery-table.vue';
import FileUpload from '@/components/common/file-upload/index.vue';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import {
  ApprovedRejectAccountRecoveryRequest,
  ApproveRejectRequestResponse,
  PasswordRecoveryRequest,
} from '@/interface-models/User/PasswordRecovery';
import useAccountRecoveryStore from '@/store/modules/AccountRecoveryStore';
import { useWebsocketStore } from '@/store/modules/WebsocketStore';
import Mitt from '@/utils/mitt';
import { onMounted, onUnmounted, ref, Ref } from 'vue';

const accountRecoveryRequest: Ref<PasswordRecoveryRequest | null> = ref(null);
const accountRecoveryStore = useAccountRecoveryStore();
const isDriverLicenseClear: Ref<boolean | null> = ref(null);
const isDriverLicenseCurrent: Ref<boolean | null> = ref(null);
const isUsernameAndDriverLicenseNameMatch: Ref<boolean | null> = ref(null);
const isPersonVisibleInWorkplacePhoto: Ref<boolean | null> = ref(null);
const isWorkplaceAndLicenseMatchingPerson: Ref<boolean | null> = ref(null);
const isPersonalRecognition: Ref<boolean | null> = ref(null);
function setSelectedAccountRecoveryRequest(selected: PasswordRecoveryRequest) {
  accountRecoveryRequest.value = selected;
}
interface IProps {
  isOperationsDashboardDialog?: boolean;
}
const props = withDefaults(defineProps<IProps>(), {
  isOperationsDashboardDialog: false,
});

const isLoading: Ref<boolean> = ref(false);

function submitAccountRecovery(): void {
  if (!accountRecoveryRequest.value) {
    return;
  }
  const approveRequest: ApprovedRejectAccountRecoveryRequest = {
    accountRecoveryId: accountRecoveryRequest.value._id,
    declinedQuestions: [],
  };

  if (!isDriverLicenseClear.value) {
    approveRequest.declinedQuestions.push(1);
  }
  if (!isDriverLicenseCurrent.value) {
    approveRequest.declinedQuestions.push(2);
  }
  if (!isUsernameAndDriverLicenseNameMatch.value) {
    approveRequest.declinedQuestions.push(3);
  }
  if (accountRecoveryRequest.value.workplacePhoto) {
    if (!isPersonVisibleInWorkplacePhoto.value) {
      approveRequest.declinedQuestions.push(4);
    }
    if (!isWorkplaceAndLicenseMatchingPerson.value) {
      approveRequest.declinedQuestions.push(5);
    }
  }
  if (!isPersonalRecognition.value) {
    approveRequest.declinedQuestions.push(6);
  }
  isLoading.value = true;
  useWebsocketStore().sendWebsocketRequest(
    new WebSocketRequest(
      '/accountRecovery/approveRejectRequest',
      approveRequest,
      true,
    ),
  );
  Mitt.on(
    'approveRejectRequestResponse',
    (response: ApproveRejectRequestResponse) => {
      if (response.success) {
        const username: string = accountRecoveryRequest.value
          ? accountRecoveryRequest.value.firstName &&
            accountRecoveryRequest.value.lastName
            ? accountRecoveryRequest.value.firstName +
              ' ' +
              accountRecoveryRequest.value.lastName
            : accountRecoveryRequest.value.username
          : 'this user';
        if (response.message === 'ACCEPTED') {
          showNotification(
            'The account recovery submission for ' +
              username +
              ' was successful, and they will receive an email to set their new password.',
            {
              title: 'Account Recovery Submission',
              type: HealthLevel.INFO,
            },
          );
        } else {
          showNotification(
            'The account recovery submission for ' +
              username +
              ' was rejected due to data inconsistency. Please have the user resubmit an account recovery request.',
            {
              title: 'Account Recovery Submission',
              type: HealthLevel.ERROR,
            },
          );
        }
        accountRecoveryRequest.value = null;
        accountRecoveryStore.removePendingAccountRecoveryRequest(
          response.accountRecoveryId,
        );
      } else {
        showNotification('Something went wrong', {
          title: 'Account Recovery Submission',
          type: HealthLevel.ERROR,
        });
      }
      isLoading.value = false;
      Mitt.off('approveRejectRequestResponse');
    },
  );
}

function cancel(): void {
  accountRecoveryRequest.value = null;
}

onMounted(async () => {});

onUnmounted(() => {});
</script>
<style scoped lang="scss">
.top-padding {
  padding-top: 58px;
}

.radio-switch {
  display: flex;
  flex-direction: column;
}

.option {
  padding: 10px;
  border-radius: 5px;
  display: flex;
}

.option input[type='radio']:checked + label {
  background-color: var(--background-color-200);
}

.option label {
  cursor: pointer;
  display: block;
  margin-left: 10px;
}

.section-container {
  margin: 0 auto;
  padding: 4px;
  display: flex;
  .container {
    .input-txt {
      margin: 12px 12px 6px 12px;
      font-size: $font-size-20;
      color: var(--light-text-color);
    }
  }
  .image-container {
    position: relative;
    margin-top: 14px;
  }
}
</style>
