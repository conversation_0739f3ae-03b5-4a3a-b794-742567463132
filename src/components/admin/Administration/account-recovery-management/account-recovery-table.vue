<template>
  <div class="account-recovery-table">
    <TableTitleHeader>
      <!-- Title slot -->
      <template #title>
        <GTitle
          title="Account Recovery"
          subtitle="Showing all pending account recovery requests."
          :divider="false"
        />
      </template>

      <!-- Inputs slot -->
      <template #inputs>
        <v-flex md5>
          <v-layout align-center>
            <v-flex md12>
              <v-text-field
                v-model="search"
                append-icon="search"
                label="Search"
                hide-details
                flat
                solo
                class="v-solo-custom"
              ></v-text-field>
            </v-flex>
          </v-layout>
        </v-flex>
      </template>
    </TableTitleHeader>

    <v-flex md12 class="mt-4">
      <GTable
        :headers="headers"
        :items="pendingAccountRecoveryRequests"
        :selectable="true"
        :search="search"
        :isLoading="isLoading"
        @selectItem="viewAccountRecoveryRequest"
        :height="
          !props.isOperationsDashboardDialog
            ? 'calc(100vh - 223px)'
            : 'calc(90vh - 233px)'
        "
      >
        <template v-slot:items="items">
          <td>{{ items.item.firstName }} {{ items.item.lastName }}</td>
          <td>{{ items.item.username }}</td>
          <td>{{ items.item.newEmailAddress }}</td>
          <td>{{ items.item.newContactNumber }}</td>
          <td>{{ items.item.status }}</td>
        </template>
      </GTable>
    </v-flex>
  </div>
</template>

<script setup lang="ts">
import TableTitleHeader from '@/components/common/ui-elements/table_title_header.vue';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import { PasswordRecoveryRequest } from '@/interface-models/User/PasswordRecovery';
import useAccountRecoveryStore from '@/store/modules/AccountRecoveryStore';
import { useWebsocketStore } from '@/store/modules/WebsocketStore';
import Mitt from '@/utils/mitt';
import Fuse from 'fuse.js';
import { computed, ComputedRef, onMounted, onUnmounted, ref, Ref } from 'vue';
const search: Ref<string> = ref('');
const accountRecoveryStore = useAccountRecoveryStore();
const emit = defineEmits(['setSelectedAccountRecoveryRequest']);

const isLoading: Ref<boolean> = ref(false);
const headers: TableHeader[] = [
  {
    text: 'Name',
    align: 'left',
    value: 'firstName',
  },
  {
    text: 'Username',
    align: 'left',
    value: 'username',
  },

  { text: 'New Email Address', align: 'left', value: 'newEmailAddress' },
  { text: 'New Contact Number', align: 'left', value: 'newContactNumber' },
  { text: 'Status', align: 'left', value: 'status' },
];

interface IProps {
  isOperationsDashboardDialog?: boolean;
}
const props = withDefaults(defineProps<IProps>(), {
  isOperationsDashboardDialog: false,
});

function viewAccountRecoveryRequest(selected: PasswordRecoveryRequest) {
  emit('setSelectedAccountRecoveryRequest', selected);
}

const pendingAccountRecoveryRequests: ComputedRef<PasswordRecoveryRequest[]> =
  computed(() => {
    // If no search value is present return full list
    if (!search.value) {
      return accountRecoveryStore.accountRecoveryRequestList;
    }

    // filter list via search value
    const fuse = new Fuse(accountRecoveryStore.accountRecoveryRequestList, {
      includeScore: true,
      threshold: 0.3,
      keys: ['username'],
    });

    return fuse.search(search.value).map((x: any) => {
      return { ...x.item };
    });
  });

function getPendingAccountRecoveryDetails() {
  useWebsocketStore().sendWebsocketRequest(
    new WebSocketRequest(
      '/accountRecovery/listAllPendingRequests',
      null,
      false,
    ),
  );
  Mitt.on(
    'pendingAccountRecoveryRequests',
    (pendingAccountRecoveryRequests: PasswordRecoveryRequest[]) => {
      accountRecoveryStore.accountRecoveryRequestList =
        pendingAccountRecoveryRequests;
      isLoading.value = false;
      Mitt.off('pendingAccountRecoveryRequests');
    },
  );
}

onMounted(async () => {
  getPendingAccountRecoveryDetails();
});

onUnmounted(() => {});
</script>
<style scoped lang="scss"></style>
