<template>
  <v-flex class="table-container">
    <v-layout row wrap>
      <v-flex class="pb-2">
        <v-layout>
          <v-flex class="checkbox-container">
            <v-checkbox
              color="orange"
              v-model="showOnlyWithRoles"
              label="Show users No Longer Employed"
              class="v-checkbox-custom"
            >
            </v-checkbox>
          </v-flex>
          <v-flex>
            <v-layout>
              <v-flex>
                <v-layout>
                  <v-flex px-2>
                    <v-select
                      hide-details
                      multiple
                      label="Division"
                      hint="Division"
                      persistent-hint
                      :items="companyDivisionList"
                      small-chips
                      :disabled="!companyDivisionList?.length"
                      item-text="value"
                      v-model="filterSelectionDivision"
                      solo
                      flat
                      class="v-solo-custom"
                      item-value="value"
                      clearable
                    >
                      <template v-slot:selection="{ item, index }">
                        <v-chip small v-if="index === 0">
                          <span>{{ item.value }}</span>
                        </v-chip>
                        <span v-if="index === 1" class="grey--text caption"
                          >(+{{
                            filterSelectionDivision.length - 1
                          }}
                          others)</span
                        >
                      </template>
                    </v-select>
                  </v-flex>
                </v-layout>
              </v-flex>

              <v-flex>
                <v-layout>
                  <v-flex px-2>
                    <v-select
                      multiple
                      hide-details
                      label="Roles"
                      hint="Roles"
                      persistent-hint
                      :items="rootStore.roleList"
                      item-value="roleId"
                      item-text="name"
                      small-chips
                      v-model="filterSelectionRoles"
                      solo
                      flat
                      class="v-solo-custom"
                    >
                      <template v-slot:selection="{ item }">
                        <v-chip small class="chips">
                          <span>{{ item.name }}</span>
                        </v-chip>
                      </template>
                    </v-select>
                  </v-flex>
                </v-layout>
              </v-flex>

              <v-flex>
                <v-layout>
                  <v-flex px-2>
                    <v-text-field
                      class="v-solo-custom textFields"
                      v-model="search"
                      append-icon="search"
                      label="Search"
                      hide-details
                      solo
                      flat
                      hint="Search"
                    ></v-text-field>
                  </v-flex>
                </v-layout>
              </v-flex>
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <GTable
          :headers="headers"
          :items="allUsers"
          :selectable="true"
          :search="search"
          :isLoading="!userManagementStore.companyUserWithAuthDetailsList"
          @selectItem="viewUser"
          :height="'calc(100vh - 223px)'"
        >
          <template v-slot:items="props">
            <td :class="props.item.companyRoles.length === 0 ? 'no-roles' : ''">
              {{ props.item.companyUser.firstName }}
              {{ props.item.companyUser.lastName }}
            </td>
            <td>{{ props.item.companyUser.emailAddress }}</td>
            <td>
              {{ formatPhoneNumber(props.item.companyUser.contactNumber) }}
            </td>
            <td
              v-if="
                props.item.companyRoles && props.item.companyRoles.length > 0
              "
            >
              <b
                >{{ getDivisionTableCell(props.item.companyRoles) }}
                {{
                  getAdditionalDivisionCountTableCell(props.item.companyRoles)
                }}</b
              >
            </td>
            <td v-else>
              <InformationTooltip
                :bottom="true"
                :tooltipType="HealthLevel.WARNING"
              >
                <v-layout slot="content"
                  >User does not have any roles. Please contact GoDesta
                  Support.</v-layout
                >
              </InformationTooltip>
            </td>
            <td
              v-if="
                props.item.companyRoles && props.item.companyRoles.length > 0
              "
            >
              {{ getRolesTableCell(props.item.companyRoles) }}
              {{ getAdditionalDivisionCountTableCell(props.item.companyRoles) }}
            </td>
            <td v-else>
              <v-icon size="22" color="warning">priority_high</v-icon>
            </td>
          </template>
        </GTable>
      </v-flex>
    </v-layout>
  </v-flex>
</template>

<script setup lang="ts">
import { computed, ComputedRef, onMounted, onUnmounted, ref, Ref } from 'vue';

import { formatPhoneNumber } from '@/helpers/StringHelpers/StringHelpers';
import { hasAdminRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { KeyValue } from '@/interface-models/Generic/KeyValue/KeyValue';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import {
  CompanyRoleStatus,
  CompanyUserWithAuthDetails,
} from '@/interface-models/User/CompanyUserDetails';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useRootStore } from '@/store/modules/RootStore';
import { useUserManagementStore } from '@/store/modules/UserManagementStore';
import { sessionManager } from '@/store/session/SessionState';
import Fuse from 'fuse.js';

const search: Ref<string> = ref('');
const filterSelectionDivision: Ref<string[]> = ref([]);
const filterSelectionRoles: Ref<number[]> = ref([]);

const userManagementStore = useUserManagementStore();
const companyDetailsStore = useCompanyDetailsStore();
const rootStore = useRootStore();
const showOnlyWithRoles = ref(false);
const emit = defineEmits(['selectUser']);

const headers: TableHeader[] = [
  {
    text: 'Name',
    align: 'left',
    value: 'firstName',
  },
  { text: 'Email', value: 'emailAddress', align: 'left' },
  { text: 'Contact Number', value: 'contactNumber', align: 'left' },
  { text: 'Division', value: '', align: 'left' },
  { text: 'Role', value: '', align: 'left' },
];

const companyDivisionList: ComputedRef<KeyValue[]> = computed(() => {
  return companyDetailsStore.companyDivisionNamesList ?? [];
});

function viewUser(selectedUser: CompanyUserWithAuthDetails): void {
  emit('selectUser', JSON.parse(JSON.stringify(selectedUser)));
}

// return the active division if it exists in the users roles list, else returns the first division
function getDivisionTableCell(companyRoles: CompanyRoleStatus[]): string {
  if (companyRoles.length < 1) {
    return '';
  }
  const activeDivision: CompanyRoleStatus | undefined = companyRoles.find(
    (x: CompanyRoleStatus) => x.division === sessionManager.getDivisionId(),
  );
  return activeDivision?.division ?? companyRoles[0].division;
}

// return the active divisions roles if it exists, else returns the roles in their first division
function getRolesTableCell(companyRoles: CompanyRoleStatus[]): string {
  const activeDivisions: CompanyRoleStatus[] = companyRoles.filter(
    (x: CompanyRoleStatus) => x.division === sessionManager.getDivisionId(),
  );
  if (activeDivisions.length > 0) {
    return activeDivisions.map((x) => x.roleName).join(', ');
  } else {
    const divisionRoles = companyRoles.filter(
      (x: CompanyRoleStatus) => x.division === companyRoles[0].division,
    );
    return divisionRoles.map((x) => x.roleName).join(', ');
  }
}

// return the number of additional divisions that are associated to the user
function getAdditionalDivisionCountTableCell(
  companyRoles: CompanyRoleStatus[],
): string {
  const divisionIds = [...new Set(companyRoles.map((x) => x.division))];
  return divisionIds.length > 1 ? '+ ' + (divisionIds.length - 1) : '';
}

const allUsers = computed(() => {
  let users: CompanyUserWithAuthDetails[] | null =
    userManagementStore.companyUserWithAuthDetailsList;

  if (!users) {
    return [];
  }

  // If emailAddress contains @godesta, then only display this user to admins
  if (!hasAdminRole()) {
    users = users.filter(
      (u: CompanyUserWithAuthDetails) =>
        !u.companyUser.emailAddress.includes('@godesta'),
    );
  }

  // filter by selected divisions
  if (filterSelectionDivision.value.length > 0) {
    users = users.filter((x: CompanyUserWithAuthDetails) =>
      x.companyRoles.find((y: CompanyRoleStatus) =>
        filterSelectionDivision.value.includes(y.division),
      ),
    );
  }

  // Filter by checkbox state (Show users with no roles)
  if (!showOnlyWithRoles.value) {
    users = users.filter((user) => user.companyRoles.length > 0);
  }

  // filter by selected roles
  if (filterSelectionRoles.value.length > 0) {
    users = users.filter((x: CompanyUserWithAuthDetails) =>
      x.companyRoles.find((y: CompanyRoleStatus) =>
        filterSelectionRoles.value.includes(y.roleId),
      ),
    );
  }

  // If no search value is present return users
  if (!search.value) {
    return users;
  }

  // filter users via search value
  const fuse = new Fuse(users, {
    includeScore: true,
    threshold: 0.3,
    keys: ['companyUser.firstName', 'companyUser.lastName'],
  });

  return fuse.search(search.value).map((x: any) => {
    return { ...x.item };
  });
});

onMounted(async () => {
  userManagementStore.getCompanyUserWithAuthDetailsList();
  companyDetailsStore.getCompanyDivisionNamesList();
});

onUnmounted(() => {});
</script>

<style scoped lang="scss">
.table-container {
  margin: 5px;
}
.no-roles {
  border-left: 3px solid var(--warning);
  font-weight: bold;
}

.chips {
  color: $highlight !important;
  background-color: var(--background-color-600) !important;
}
</style>
