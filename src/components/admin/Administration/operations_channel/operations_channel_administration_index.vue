<template>
  <div class="operations-channel-administration-index">
    <TableTitleHeader>
      <template #title>
        <GTitle
          title="Operations Channels Administration"
          subtitle="Add or edit Operations Channels"
          :divider="false"
        />
      </template>

      <template #buttons>
        <GButton
          icon="fal fa-plus"
          :iconRight="false"
          v-if="allowChannelEdit || isAuthorised"
          @click="addNewOperationsChannel"
        >
          Add new Channel</GButton
        >
      </template>

      <template #inputs>
        <v-flex md6>
          <v-text-field
            appendIcon="search"
            label="Search operations channels"
            hint="Search operations channels by Name"
            color="orange"
            height="30"
            outline
            solo
            flat
            class="v-solo-custom"
            clearable
            v-model="searchQuery"
          >
          </v-text-field>
        </v-flex>
      </template>
    </TableTitleHeader>

    <v-flex md12 class="pb-2">
      <OperationsChannelTable
        :operationsChannels="operationsChannels"
        :searchQuery="searchQuery"
        @viewOperationsChannel="editExistingChannel"
        @deleteOperationsChannel="deleteOperationsChannel"
      ></OperationsChannelTable>
    </v-flex>

    <OperationsChannelEditDialog
      :key="editedOperationsChannel?._id || 'new'"
      :operationsChannelItem="editedOperationsChannel"
      :isDialogOpen.sync="editDialogController"
      :defaultChannelId="defaultChannelId"
    />
  </div>
</template>

<script setup lang="ts">
import OperationsChannelEditDialog from '@/components/admin/Administration/operations_channel/components/operations_channel_edit_dialog.vue';
import OperationsChannelTable from '@/components/admin/Administration/operations_channel/components/operations_channel_table.vue';
import TableTitleHeader from '@/components/common/ui-elements/table_title_header.vue';
import { requestUserConfirmation } from '@/helpers/NotificationHelpers/ConfirmationHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  GENERIC_SUCCESS_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { OperationsChannel } from '@/interface-models/OperationsChannels/OperationsChannel';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useFilterStore } from '@/store/modules/OperationsFilterStore';
import { sessionManager } from '@/store/session/SessionState';
import {
  ComputedRef,
  Ref,
  WritableComputedRef,
  computed,
  nextTick,
  ref,
} from 'vue';

const editedOperationsChannel: Ref<OperationsChannel | null> = ref(null);
const searchQuery: Ref<string> = ref('');
const editDialogIsOpen: Ref<boolean> = ref(false);

// list of operations channels from store
const operationsChannels: ComputedRef<OperationsChannel[]> = computed(() => {
  return useFilterStore().operationsChannels;
});

const isAuthorised: ComputedRef<boolean> = computed(() => {
  return hasAdminOrHeadOfficeRole();
});

// Returns true if at least one operationsChannel is default
const defaultChannelId: ComputedRef<string> = computed(() => {
  return (
    operationsChannels.value.find((channel) => channel.isDefaultChannel)?._id ||
    ''
  );
});

/**
 * Creates a new operations channel with default values and opens it in the edit
 * dialog.
 */
function addNewOperationsChannel() {
  // Open the dialog with the new operations channel
  openChannelInDialog(
    new OperationsChannel({
      company: sessionManager.getCompanyId(),
      division: sessionManager.getDivisionId(),
      companyUserId: isAuthorised.value ? null : sessionManager.getUserId(),
    }),
  );
}

const allowChannelEdit: ComputedRef<boolean> = computed(() => {
  return (
    useCompanyDetailsStore().divisionCustomConfig?.operations
      ?.allowCustomOperationsChannel ?? false
  );
});

/**
 * Sends request to backend to delete an operations channel. Called from the
 * template via the delete button in table row.
 * @param id - the mongo id of the channel to delete
 */
async function deleteOperationsChannel(id: string): Promise<void> {
  const foundChannel = operationsChannels.value.find(
    (channel) => channel._id === id,
  );
  if (!foundChannel) {
    return;
  }
  const ok = await requestUserConfirmation(
    'Are you sure you want to delete this operations channel?',
    'Delete Operations Channel',
  );

  if (!ok) {
    return;
  }

  const result = await useFilterStore().deleteOperationsChannel(foundChannel);
  if (result) {
    showNotification(GENERIC_SUCCESS_MESSAGE, {
      title: 'Operations Channel Maintenance - Delete',
      type: HealthLevel.SUCCESS,
    });
  } else {
    showNotification(GENERIC_ERROR_MESSAGE, {
      title: 'Operations Channel Maintenance - Delete',
      type: HealthLevel.ERROR,
    });
  }
}

/**
 * Finds and opens an existing operations channel item in the edit dialog based
 * on its ID.
 * @param {string} id - The mongo id of the operations channel item to
 * be edited.
 */
function editExistingChannel(id: string): void {
  // Find the operations channel item by ID from the list of operations channel items
  const operationsChannel: OperationsChannel | undefined =
    operationsChannels.value.find((type: OperationsChannel) => type._id === id);
  if (!operationsChannel) {
    return;
  }
  openChannelInDialog(operationsChannel);
}

/**
 * Opens the operations channel item in the edit dialog.
 * Sets the provided operations channel as the currently edited item
 * and ensures the dialog opens in the next UI tick.
 *
 * @param {OperationsChannel} item - The operations channel item to be edited.
 */
function openChannelInDialog(item: OperationsChannel): void {
  // Set the selected operations channel item for editing
  editedOperationsChannel.value = new OperationsChannel(item);
  nextTick(() => {
    editDialogController.value = true;
  });
}

// sets addServiceType Dialog
const editDialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return editDialogIsOpen.value;
  },
  set(value: boolean): void {
    if (!value) {
      editedOperationsChannel.value = null;
    }
    editDialogIsOpen.value = value;
  },
});
</script>

<style scoped lang="scss"></style>
