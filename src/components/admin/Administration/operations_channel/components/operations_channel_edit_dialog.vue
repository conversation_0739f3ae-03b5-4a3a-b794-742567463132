<template>
  <ContentDialog
    :showDialog.sync="dialogController"
    :title="
      !editedOperationsChannelItem?._id
        ? 'New Operations Channel'
        : 'Edit Operations Channel'
    "
    width="50%"
    contentPadding="pa-0"
    @cancel="cancelChanges"
    @confirm="saveOperationsChannel"
    :showActions="true"
    :isConfirmUnsaved="false"
    :isDisabled="isFormDisabled"
    :isLoading="isAwaitingSaveResponse"
    confirmBtnText="Confirm"
  >
    <v-layout>
      <v-flex
        md12
        class="body-scrollable--75 body-min-height--65 pa-3"
        v-if="editedOperationsChannelItem !== null"
      >
        <OperationsChannelForm
          ref="operationsChannelDialogForm"
          :operationsChannel="editedOperationsChannelItem"
          :isFormDisabled="isFormDisabled"
          :defaultChannelId="defaultChannelId"
        ></OperationsChannelForm>
      </v-flex>
    </v-layout>
  </ContentDialog>
</template>

<script setup lang="ts">
import OperationsChannelForm from '@/components/common/operations_channel/operations_channel_form.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  GENERIC_ERROR_MESSAGE,
  GENERIC_SUCCESS_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { OperationsChannel } from '@/interface-models/OperationsChannels/OperationsChannel';
import { useFilterStore } from '@/store/modules/OperationsFilterStore';
import {
  computed,
  ComputedRef,
  Ref,
  ref,
  watch,
  WritableComputedRef,
} from 'vue';

const props = withDefaults(
  defineProps<{
    isDialogOpen: boolean;
    operationsChannelItem: OperationsChannel | null;
    defaultChannelId?: string;
  }>(),
  {
    operationsChannelItem: null,
    defaultChannelId: '',
  },
);

const isAwaitingSaveResponse: Ref<boolean> = ref(false);
const editedOperationsChannelItem: Ref<OperationsChannel | null> = ref(null);

const operationsChannelDialogForm: Ref<any> = ref(null);

// emits to control dialogs
const emit = defineEmits<{
  (event: 'update:isDialogOpen', payload: boolean): void;
  (event: 'update:reorderDialogIsOpen', payload: boolean): void;
}>();

/**
 * Saves the currently edited operations channel after validating the form.
 * Displays a success or error notification based on the result. Closes the
 * dialog after completion.
 *
 * @returns {Promise<void>} A promise that resolves when the save operation
 * completes.
 */
async function saveOperationsChannel(): Promise<void> {
  if (!editedOperationsChannelItem.value) {
    return;
  }
  if (
    !operationsChannelDialogForm.value?.validate() ||
    !operationsChannelDialogForm.value.localOperationsChannel
  ) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE, {
      title: 'Operations Channel Maintenance',
      type: HealthLevel.ERROR,
    });
    return;
  }

  isAwaitingSaveResponse.value = true;

  const formToSave: OperationsChannel =
    operationsChannelDialogForm.value.localOperationsChannel;

  const result = await useFilterStore().saveOperationsChannel(formToSave);
  if (result) {
    showNotification(GENERIC_SUCCESS_MESSAGE, {
      title: 'Operations Channel Maintenance',
      type: HealthLevel.SUCCESS,
    });
  } else {
    showNotification(GENERIC_ERROR_MESSAGE, {
      title: 'Operations Channel Maintenance',
      type: HealthLevel.ERROR,
    });
  }
  isAwaitingSaveResponse.value = false;
  dialogController.value = false;
}

/**
 * Cancels the changes made to the operations channel and closes the dialog
 */
function cancelChanges() {
  if (props.operationsChannelItem) {
    // Reset to the initial operations channel passed from props
    editedOperationsChannelItem.value = new OperationsChannel(
      props.operationsChannelItem,
    );
  }
  dialogController.value = false;
}

/**
 * Gets and sets the isDialogOpen prop passed from parent
 */
const dialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.isDialogOpen;
  },
  set(value: boolean): void {
    emit('update:isDialogOpen', value);
  },
});

/**
 * Used in the template to disable the OperationsChannelForm component. It is
 * disabled if we're waiting for the save response, or if the channel is a
 * division-level and the user is not of the allowed roles.
 */
const isFormDisabled: ComputedRef<boolean> = computed(() => {
  if (!editedOperationsChannelItem.value) {
    return true;
  }
  return (
    isAwaitingSaveResponse.value ||
    (editedOperationsChannelItem.value.isDivisionDefault && !isAuthorised.value)
  );
});

const isAuthorised: ComputedRef<boolean> = computed(() => {
  return hasAdminOrHeadOfficeRole();
});

/**
 * When the visibility of the dialog changes, sets the editedOperationsChannelItem to a clone
 * of the operationsChannelItem prop for use in the form
 */
watch(dialogController, (newVal) => {
  if (newVal && props.operationsChannelItem) {
    editedOperationsChannelItem.value = new OperationsChannel(
      props.operationsChannelItem,
    );
  } else {
    editedOperationsChannelItem.value = null;
  }
});
</script>

<style scoped lang="scss">
.service-type-edit-dialog {
  .subsection__header {
    font-size: $font-size-large;
    font-weight: 600;
    color: grey;
    text-transform: uppercase;
  }
}

.display-name {
  width: 100%;
  font-size: $font-size-22;
  font-weight: 500;
  color: var(--primary-light);
  text-transform: uppercase;
  margin-bottom: 20px;
}

.display-order-txt {
  font-weight: 600;
  font-size: $font-size-16;
  color: var(--text-color);
  letter-spacing: 0.5px;
}
</style>
