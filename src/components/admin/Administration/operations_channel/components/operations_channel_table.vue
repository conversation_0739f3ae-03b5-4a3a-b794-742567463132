<template>
  <section class="operations-channel-table">
    <v-data-table
      :headers="headers"
      :items="operationsChannelTableData"
      class="gd-dark-theme"
      :rows-per-page-items="[10, 15, 20]"
      disable-initial-sort
      item-key="id"
    >
      <template v-slot:items="{ item }">
        <tr>
          <td>{{ item.name }}</td>
          <td>{{ item.type }}</td>
          <td>{{ item.isDefaultChannel ? 'Default' : '-' }}</td>
          <td>{{ item.jobClient }}</td>
          <td>{{ item.jobNationalClient }}</td>
          <td>{{ item.jobServiceTypes }}</td>
          <td>{{ item.rateTypes }}</td>
          <td>{{ item.fleetClient }}</td>
          <td>{{ item.fleetNationalClient }}</td>
          <td>{{ item.fleetVehicleClass }}</td>
          <td>{{ item.fleetOwnerAffiliations }}</td>
          <td>{{ item.hasIncludedFleetAssetIds }}</td>
          <td>{{ item.hasExcludedFleetAssetIds }}</td>
          <td>{{ item.hasIncludedDriverIds }}</td>
          <td>{{ item.hasExcludedDriverIds }}</td>
          <td>
            <v-layout justify-start>
              <v-tooltip left>
                <template v-slot:activator="{ on }">
                  <v-btn
                    flat
                    v-on="on"
                    icon
                    @click="emit('viewOperationsChannel', item.id)"
                    class="mx-0"
                  >
                    <v-icon size="17" color="accent">{{
                      item.allowEdit ? 'far fa-edit' : 'far fa-eye'
                    }}</v-icon>
                  </v-btn>
                </template>
                {{ item.allowEdit ? 'Edit' : 'View' }} Operation Channel
              </v-tooltip>
              <v-tooltip left v-if="isAuthorised">
                <template v-slot:activator="{ on }">
                  <v-btn
                    flat
                    v-on="on"
                    icon
                    @click="emit('deleteOperationsChannel', item.id)"
                    class="mx-0"
                    :disabled="item.isDefaultChannel"
                  >
                    <v-icon size="17" color="error">far fa-trash</v-icon>
                  </v-btn>
                </template>
                Delete Operations Channel
              </v-tooltip>
            </v-layout>
          </td>
        </tr>
      </template>
    </v-data-table>
  </section>
</template>
<script setup lang="ts">
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { FilterMode } from '@/interface-models/OperationsChannels/FilterByValues';
import { OperationsChannel } from '@/interface-models/OperationsChannels/OperationsChannel';
import { ComputedRef, computed } from 'vue';

const props = withDefaults(
  defineProps<{
    operationsChannels: OperationsChannel[];
    searchQuery: string;
  }>(),
  {
    searchQuery: '',
  },
);

const emit = defineEmits<{
  (event: 'viewOperationsChannel', payload: string): void;
  (event: 'deleteOperationsChannel', payload: string): void;
}>();

const isAuthorised: ComputedRef<boolean> = computed(() => {
  return hasAdminOrHeadOfficeRole();
});

const headers: TableHeader[] = [
  {
    text: 'Channel Name',
    value: 'name',
    align: 'left',
    sortable: true,
  },
  {
    text: 'Is Division Default',
    value: 'isDivisionDefault',
    align: 'left',
    sortable: true,
  },
  {
    text: 'Default',
    value: 'isDefaultChannel',
    align: 'left',
    sortable: true,
  },
  {
    text: 'Filter Jobs by Client',
    value: 'jobClient',
    align: 'left',
    sortable: true,
  },
  {
    text: 'Jobs by National Client',
    value: 'jobNationalClient',
    align: 'left',
    sortable: true,
  },
  {
    text: 'Filter Job by Service Type',
    value: 'jobServiceTypes',
    align: 'left',
    sortable: true,
  },
  {
    text: 'Filter Jobs by Rate Type',
    value: 'rateTypes',
    align: 'left',
    sortable: true,
  },
  {
    text: 'Group Fleet by Client',
    value: 'fleetClient',
    align: 'left',
    sortable: true,
  },
  {
    text: 'Group Fleet by National Client',
    value: 'fleetNationalClient',
    align: 'left',
    sortable: true,
  },
  {
    text: 'Group Fleet by Vehicle Class',
    value: 'fleetVehicleClass',
    align: 'left',
    sortable: true,
  },
  {
    text: 'Group Fleet by Affiliations',
    value: 'fleetOwnerAffiliations',
    align: 'left',
    sortable: true,
  },
  {
    text: 'Include Specific Vehicles',
    value: 'hasIncludedFleetAssetIds',
    align: 'left',
    sortable: true,
  },
  {
    text: 'Exclude Specific Vehicles',
    value: 'hasExcludedFleetAssetIds',
    align: 'left',
    sortable: true,
  },
  {
    text: 'Included Specific Drivers',
    value: 'hasIncludedDriverIds',
    align: 'left',
    sortable: true,
  },
  {
    text: 'Exclude Specific Drivers',
    value: 'hasExcludedDriverIds',
    align: 'left',
    sortable: true,
  },
  {
    text: 'Actions',
    value: '',
    align: 'right',
    sortable: false,
  },
];

function returnFilterModeDescription(mode: FilterMode) {
  switch (mode) {
    case FilterMode.ANY:
      return 'All visible';
    case FilterMode.ONLY:
      return 'Show specific';
    case FilterMode.NONE:
      return 'Hidden';
    default:
      return '';
  }
}

/**
 * Returns the service type filtered by the search query
 * @returns {OperationsChannel[]} - service types matching the search query
 */
const operationsChannelTableData: ComputedRef<
  {
    id: string;
    name: string;
    type: string;
    isDefaultChannel: boolean;
    isMine: boolean;
    jobClient: string;
    jobNationalClient: string;
    jobServiceTypes: string;
    rateTypes: string;
    fleetClient: string;
    fleetNationalClient: string;
    fleetVehicleClass: string;
    fleetOwnerAffiliations: string;
    hasIncludedFleetAssetIds: string;
    hasExcludedFleetAssetIds: string;
    hasIncludedDriverIds: string;
    hasExcludedDriverIds: string;
    allowEdit: boolean;
    allowDelete: boolean;
  }[]
> = computed(() => {
  return props.operationsChannels.map((channel) => {
    return {
      id: channel._id!,
      name: channel.name,
      type: channel.isDivisionDefault ? 'Shared' : 'Custom (personal)',
      isDefaultChannel: channel.isDefaultChannel,
      isMine: channel.isMine,
      jobClient: returnFilterModeDescription(
        channel.jobFilterOptions.clientFilter.clientIds.mode,
      ),
      jobNationalClient: returnFilterModeDescription(
        channel.jobFilterOptions.clientFilter.nationalClientIds.mode,
      ),
      jobServiceTypes: returnFilterModeDescription(
        channel.jobFilterOptions.serviceTypeIds.mode,
      ),
      rateTypes: returnFilterModeDescription(
        channel.jobFilterOptions.rateTypeIds.mode,
      ),
      fleetClient: returnFilterModeDescription(
        channel.fleetFilterOptions.clientFilter.clientIds.mode,
      ),
      fleetNationalClient: returnFilterModeDescription(
        channel.fleetFilterOptions.clientFilter.nationalClientIds.mode,
      ),
      fleetVehicleClass: returnFilterModeDescription(
        channel.fleetFilterOptions.vehicleClasses.mode,
      ),
      fleetOwnerAffiliations: returnFilterModeDescription(
        channel.fleetFilterOptions.ownerAffiliations.mode,
      ),
      hasIncludedFleetAssetIds: channel.fleetFilterOptions.includeFleetAssetIds
        ?.length
        ? 'Yes'
        : 'No',
      hasExcludedFleetAssetIds: channel.fleetFilterOptions.excludeFleetAssetIds
        ?.length
        ? 'Yes'
        : 'No',
      hasIncludedDriverIds: channel.fleetFilterOptions.includeDriverIds?.length
        ? 'Yes'
        : 'No',
      hasExcludedDriverIds: channel.fleetFilterOptions.excludeDriverIds?.length
        ? 'Yes'
        : 'No',
      allowEdit: isAuthorised.value || channel.isMine,
      allowDelete: isAuthorised.value || channel.isMine,
    };
  });
});
</script>

<style scoped lang="scss"></style>
