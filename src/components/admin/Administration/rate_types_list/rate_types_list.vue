<template>
  <div>
    <TableTitleHeader>
      <template #title>
        <GTitle
          title="Rate Types"
          subtitle="View Rate Types list"
          :divider="false"
        />
      </template>

      <template #inputs>
        <v-flex md6>
          <v-text-field
            appendIcon="search"
            label="Search Rate Type"
            hint="Search Rate Type"
            color="orange"
            solo
            flat
            class="v-solo-custom"
            clearable
            v-model="searchQuery"
          >
          </v-text-field>
        </v-flex>
      </template>
    </TableTitleHeader>

    <v-flex md12>
      <v-data-table
        :headers="headers"
        :items="filteredServiceTypeRates"
        class="gd-dark-theme"
        item-key="id"
        :rows-per-page-items="[10, 15, 20]"
      >
        <template v-slot:items="tableProps">
          <tr class="charge-row-item" @click="() => {}">
            <td>{{ tableProps.item.longName.toUpperCase() }}</td>
            <td>{{ tableProps.item.shortName.toUpperCase() }}</td>
            <td>
              {{ tableProps.item.adhoc ? 'Yes' : 'No' }}
            </td>
          </tr>
        </template>
      </v-data-table>
    </v-flex>
  </div>
</template>

<script setup lang="ts">
import TableTitleHeader from '@/components/common/ui-elements/table_title_header.vue';
import {
  ServiceTypeRates,
  serviceTypeRates,
} from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { computed, ComputedRef, ref } from 'vue';

const searchQuery = ref('');

const headers: TableHeader[] = [
  {
    text: 'Name',
    align: 'left',
    value: 'longName',
    sortable: true,
  },
  {
    text: 'Short Name',
    align: 'left',
    value: 'shortName',
    sortable: true,
  },
  {
    text: 'ADHOC',
    align: 'left',
    value: 'adhoc',
    sortable: true,
  },
];

/**
 * Computes the serviceTypeRate items data.
 * @returns {ServiceTypeRates[]} The Rate Types items.
 */
const serviceTypeRate: ComputedRef<ServiceTypeRates[]> = computed(() => {
  return serviceTypeRates;
});

// filter table data based on search input
const filteredServiceTypeRates = computed(() => {
  if (!searchQuery.value) {
    return serviceTypeRate.value;
  }

  return serviceTypeRate.value.filter((item) =>
    Object.values(item).some(
      (val) =>
        val &&
        val.toString().toLowerCase().includes(searchQuery.value.toLowerCase()),
    ),
  );
});
</script>
