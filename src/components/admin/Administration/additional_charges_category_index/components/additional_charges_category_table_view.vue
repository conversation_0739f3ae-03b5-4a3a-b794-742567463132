<template>
  <section class="additional-charges-table-view">
    <v-data-table
      :headers="headers"
      :items="additionalChargesCategoryTableData"
      class="gd-dark-theme"
      :rows-per-page-items="[10, 15, 20]"
    >
      <template v-slot:items="tableProps">
        <tr
          class="charge-row-item"
          @click="viewAdditionalChargeRate(tableProps.item.id)"
        >
          <td>{{ tableProps.item.name }}</td>
          <td>
            {{
              tableProps.item.shortName
                ? tableProps.item.shortName.toUpperCase()
                : ''
            }}
          </td>
          <td>
            {{ tableProps.item.rateType }}
          </td>
          <td>
            {{ tableProps.item.rateBasis }}
          </td>

          <td>{{ tableProps.item.allowAdhocCharges ? 'Yes' : '-' }}</td>
          <td>{{ countItemsByTypeId(tableProps.item.id) }}</td>
        </tr>
      </template>
    </v-data-table>
  </section>
</template>
<script setup lang="ts">
import { returnReadableApplicationTypeName } from '@/interface-models/AdditionalCharges/AdditionalChargeApplicationType';
import { AdditionalChargeItem } from '@/interface-models/AdditionalCharges/AdditionalChargeItem';
import { returnReadableRateBasisName } from '@/interface-models/AdditionalCharges/AdditionalChargeRateBasis';
import { AdditionalChargeType } from '@/interface-models/AdditionalCharges/AdditionalChargeType';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { useRootStore } from '@/store/modules/RootStore';
import { ComputedRef, computed } from 'vue';

interface AdditionalChargeTableRow {
  id: string;
  name: string;
  shortName: string;
  rateType: string;
  rateBasis: string;
  allowAdhocCharges: boolean;
}

const props = withDefaults(
  defineProps<{
    additionalChargeTypes: AdditionalChargeType[];
    searchQuery: string;
  }>(),
  {
    additionalChargeTypes: () => [],
    searchQuery: '',
  },
);

const emit = defineEmits<{
  (event: 'viewAdditionalChargeRate', payload: string): void;
}>();

const headers: TableHeader[] = [
  {
    text: 'Category Name',
    align: 'left',
    value: 'name',
    sortable: true,
  },
  {
    text: 'Short Name',
    align: 'left',
    value: 'shortName',
    sortable: true,
  },
  {
    text: 'Rate Type',
    align: 'left',
    value: 'rateType',
    sortable: true,
  },
  {
    text: 'Rate Basis',
    align: 'left',
    value: 'rateBasis',
    sortable: true,
  },
  {
    text: 'Allow Adhoc Charges',
    align: 'left',
    value: 'allowAdhocCharges',
    sortable: true,
  },
  {
    text: 'Charge Count',
    align: 'left',
    value: 'count',
    sortable: true,
  },
];

/**
 * Computes the additional charges  category table data based on the additional charge types.
 * @returns {AdditionalChargeTableRow[]} The formatted additional charges category table data.
 */
const additionalChargesCategoryTableData: ComputedRef<
  AdditionalChargeTableRow[]
> = computed(() => {
  const query = props.searchQuery.toLowerCase();
  return props.additionalChargeTypes
    .map((category) => ({
      id: category._id,
      name: category.longName,
      shortName: category.shortName,
      rateType: returnReadableApplicationTypeName(
        category.chargeApplicationType,
      ),
      rateBasis: category.allowedRateBases
        .map((r) => returnReadableRateBasisName(r))
        .join(', '),
      allowAdhocCharges: category.allowAdhocCharges,
    }))
    .filter((item) => {
      return (
        item.name.toLowerCase().includes(query) ||
        item.shortName.toLowerCase().includes(query)
      );
    });
});

/**
 * Computes the additional charges items data.
 * @returns {AdditionalChargeItem[]} The additional charges items.
 */
const additionalChargeItems: ComputedRef<AdditionalChargeItem[]> = computed(
  () => {
    return useRootStore().additionalChargeItemList;
  },
);

/**
 * Emits an event to view the additional charge rate by ID.
 * @param {string} id - The ID of the additional charge rate.
 */
function viewAdditionalChargeRate(id: string): void {
  emit('viewAdditionalChargeRate', id);
}

/**
 * Counts Number of Charges for each category.
 * @param {string} id - The ID of the Category.
 */
function countItemsByTypeId(typeId: string | null): number {
  if (!typeId) {
    return 0;
  }
  return additionalChargeItems.value.filter(
    (item) => item.typeReferenceId === typeId,
  ).length;
}
</script>

<style scoped lang="scss">
.odd-border {
  border-left: 3px solid #ff9800;
}

.evenBorder {
  border-left: 3px solid green !important;
}

.charge-row-item {
  cursor: pointer;
}
</style>
