<template>
  <ContentDialog
    :showDialog.sync="dialogController"
    :title="
      !editedAdditionalChargeType?._id
        ? 'New Additional Charge Category'
        : 'Edit Additional Charge Category'
    "
    width="60%"
    contentPadding="pa-0"
    @cancel="dialogController = false"
    @confirm="saveAdditionalCharge"
    :showActions="true"
    :isConfirmUnsaved="false"
    :isDisabled="isFormDisabled"
    :isLoading="isAwaitingSaveResponse"
    confirmBtnText="Confirm"
  >
    <v-layout>
      <v-flex md12 class="body-scrollable--75 body-min-height--75 pa-3">
        <v-form
          ref="additionalChargesDialogForm"
          class="additional-charges-edit-dialog"
        >
          <v-layout wrap>
            <v-flex md12 pb-3
              ><v-layout align-center>
                <h5 class="subheader--bold pr-3 pt-1">Key Details</h5>
                <v-flex>
                  <v-divider></v-divider>
                </v-flex>
              </v-layout>
            </v-flex>

            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6
                      class="subheader--faded pr-3 pb-0 form-field-required-marker"
                    >
                      Category Name
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md6>
                  <v-text-field
                    class="v-solo-custom"
                    solo
                    flat
                    color="light-blue"
                    :disabled="isFormDisabled"
                    label="Name"
                    v-model="editedAdditionalChargeType.longName"
                    :rules="[
                      validate.validationRules.required,
                      validate.uniqueLongName,
                    ]"
                    hint=""
                  />
                </v-flex>
              </v-layout>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6
                      class="subheader--faded pr-3 pb-0 form-field-required-marker"
                    >
                      Category Short Name
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md3>
                  <v-text-field
                    class="v-solo-custom"
                    solo
                    flat
                    color="light-blue"
                    :disabled="isFormDisabled"
                    label="Short Name"
                    v-model="editedAdditionalChargeType.shortName"
                    :rules="[
                      validate.validationRules.required,
                      validate.uniqueShortName,
                    ]"
                    hint=""
                  />
                </v-flex>
              </v-layout>
            </v-flex>

            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6
                      class="subheader--faded pr-3 pb-0 form-field-required-marker"
                    >
                      Job Charge Type
                    </h6>
                  </v-layout>
                </v-flex>
                <!-- Radio button for Freight Charge -->
                <v-radio-group
                  v-model="chargeApplicationTypeController"
                  :disabled="isFormDisabled"
                  class="mt-2 d-flex"
                  :rules="[validate.validationRules.required]"
                >
                  <v-radio
                    :label="'Freight Charge'"
                    :value="AdditionalChargeApplicationType.FREIGHT"
                    color="warning"
                  />
                  <!-- Radio button for Overall Job Charge -->
                  <v-radio
                    :label="'Overall Job Charge'"
                    :value="AdditionalChargeApplicationType.NON_FREIGHT"
                    color="warning"
                  />
                </v-radio-group>
              </v-layout>
            </v-flex>

            <v-flex md12 pb-3
              ><v-layout align-center>
                <h5 class="subheader--bold pr-3 pt-1">Rate Details</h5>
                <v-flex>
                  <v-divider></v-divider>
                </v-flex>
              </v-layout>
            </v-flex>

            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6
                      class="subheader--faded pr-3 pb-0 form-field-required-marker"
                    >
                      Rate Basis
                    </h6>
                  </v-layout>
                </v-flex>
                <!-- Fixed Checkbox -->
                <label
                  class="custom-checkbox"
                  :class="{
                    disabled: isFormDisabled || disableCheckboxes.fixed,
                  }"
                >
                  <input
                    id="checkbox"
                    type="checkbox"
                    :disabled="isFormDisabled || disableCheckboxes.fixed"
                    v-model="allowedRateBasesController"
                    :value="AdditionalChargeRateBasis.FIXED"
                  />
                  Fixed ($)
                </label>

                <!-- Percentage Checkbox -->
                <label
                  class="custom-checkbox"
                  :class="{
                    disabled: isFormDisabled || disableCheckboxes.percentage,
                  }"
                >
                  <input
                    id="checkbox"
                    type="checkbox"
                    :disabled="isFormDisabled || disableCheckboxes.percentage"
                    v-model="allowedRateBasesController"
                    :value="AdditionalChargeRateBasis.PERCENTAGE"
                  />
                  Percentage (%)
                </label>
              </v-layout>
            </v-flex>

            <v-flex md12>
              <v-layout class="mt-4">
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">
                      Allow Adhoc Charges
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-switch
                    class="custom-switch"
                    v-model="allowAdhocChargesController"
                    :disabled="isFormDisabled"
                    :label="allowAdhocChargesController ? 'Yes' : 'No'"
                    color="#7decd0"
                    inset
                  ></v-switch>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-form>
      </v-flex>
    </v-layout>
  </ContentDialog>
</template>

<script setup lang="ts">
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { deepCopy } from '@/helpers/JsonHelpers/JsonHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  GENERIC_ERROR_MESSAGE,
  GENERIC_SUCCESS_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { AdditionalChargeApplicationType } from '@/interface-models/AdditionalCharges/AdditionalChargeApplicationType';
import { AdditionalChargeItem } from '@/interface-models/AdditionalCharges/AdditionalChargeItem';
import { AdditionalChargeRateBasis } from '@/interface-models/AdditionalCharges/AdditionalChargeRateBasis';
import { AdditionalChargeType } from '@/interface-models/AdditionalCharges/AdditionalChargeType';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { useRootStore } from '@/store/modules/RootStore';
import {
  computed,
  ComputedRef,
  Ref,
  ref,
  watch,
  WritableComputedRef,
} from 'vue';

const props = withDefaults(
  defineProps<{
    isDialogOpen: boolean;
    additionalChargeType: AdditionalChargeType | null;
  }>(),
  {
    additionalChargeType: null,
  },
);

const emit = defineEmits<{
  (event: 'update:isDialogOpen', payload: boolean): void;
}>();

const isAwaitingSaveResponse: Ref<boolean> = ref(false);

const additionalChargesDialogForm: Ref<any> = ref(null);

const editedAdditionalChargeType: Ref<AdditionalChargeType> = ref({
  _id: '',
  company: '',
  division: '',
  longName: '',
  shortName: '',
  chargeApplicationType: AdditionalChargeApplicationType.FREIGHT,
  allowedRateBases: [AdditionalChargeRateBasis.FIXED],
  allowAdhocCharges: false,
});

// get additionalChargeItemList
const additionalChargeItems: ComputedRef<AdditionalChargeItem[]> = computed(
  () => {
    return useRootStore().additionalChargeItemList;
  },
);

const validate = {
  validationRules,
  // add uniqueName validation to check if the charge category already exists
  uniqueLongName: (value: string) => {
    // Apply unique name validation only when the item is new
    if (
      editedAdditionalChargeType.value?.longName !==
      props.additionalChargeType?.longName
    ) {
      const existingNames = useRootStore().additionalChargeTypeList.map(
        (item) => item.longName.toLowerCase(),
      );
      if (existingNames.includes(value.toLowerCase())) {
        return 'This category name is already in use';
      }
    }
    return true;
  },
  uniqueShortName: (value: string) => {
    // Apply unique name validation only when the item is new
    if (
      editedAdditionalChargeType.value?.shortName !==
      props.additionalChargeType?.shortName
    ) {
      const existingNames = useRootStore().additionalChargeTypeList.map(
        (item) => item.shortName.toUpperCase(),
      );
      if (existingNames.includes(value.toUpperCase())) {
        return 'This short name is already in use';
      }
    }
    return true;
  },
};

/**
 * validation check for AdditionalChargeRateBasis Checkbox
 */
const validateCheckboxes = computed(() => {
  return (
    allowedRateBasesController.value.includes(
      AdditionalChargeRateBasis.FIXED,
    ) ||
    allowedRateBasesController.value.includes(
      AdditionalChargeRateBasis.PERCENTAGE,
    )
  );
});

/**
 * Computed for the "Charge Application Type" checkboxes
 * Return the current chargeApplicationType
 * Update the chargeApplicationType
 */
const chargeApplicationTypeController = computed({
  get() {
    return editedAdditionalChargeType.value.chargeApplicationType
      ? editedAdditionalChargeType.value.chargeApplicationType
      : AdditionalChargeApplicationType.FREIGHT;
  },
  set(value: AdditionalChargeApplicationType) {
    editedAdditionalChargeType.value.chargeApplicationType = value;
  },
});

/**
 * Computed property to manage allowed rate bases (checkboxes)
 * Return the current allowedRateBases array from editedAdditionalChargeType
 * Update the allowedRateBases array in the editedAdditionalChargeType object
 */
const allowedRateBasesController = computed({
  get() {
    return editedAdditionalChargeType.value.allowedRateBases || [];
  },
  set(value: AdditionalChargeRateBasis[]) {
    editedAdditionalChargeType.value.allowedRateBases = value;
  },
});

/**
 * Computed for Allow Adhoc Charges
 */
const allowAdhocChargesController = computed<boolean>({
  get() {
    return editedAdditionalChargeType.value.allowAdhocCharges;
  },
  set(value: boolean) {
    editedAdditionalChargeType.value.allowAdhocCharges = value;
  },
});

/**
 * Computed property to disable checkboxes based on conditions
 */
const disableCheckboxes = computed(() => {
  // Check if the edited type matches any additional charge item
  const typeIdMatches =
    editedAdditionalChargeType.value &&
    additionalChargeItems.value.some(
      (item) => item.typeReferenceId === editedAdditionalChargeType.value!._id,
    );

  // Helper to check if a rate basis is allowed
  const isRateBasisAllowed = (rateBasis: AdditionalChargeRateBasis) => {
    return editedAdditionalChargeType.value?.allowedRateBases?.includes(
      rateBasis,
    );
  };

  return {
    fixed: typeIdMatches && isRateBasisAllowed(AdditionalChargeRateBasis.FIXED),
    percentage:
      typeIdMatches && isRateBasisAllowed(AdditionalChargeRateBasis.PERCENTAGE),
  };
});

const isFormDisabled: ComputedRef<boolean> = computed(() => {
  // disable form edit for default Category
  const isChargeTypeDisabled = [
    'tolls',
    'overall adjustments',
    'freight adjustments',
  ].includes(editedAdditionalChargeType.value.longName.toLowerCase());

  return (
    isAwaitingSaveResponse.value || !isAuthorised.value || isChargeTypeDisabled
  );
});

/**
 * Gets and sets the isDialogOpen prop passed from parent
 */
const dialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.isDialogOpen;
  },
  set(value: boolean): void {
    emit('update:isDialogOpen', value);
  },
});

/**
 * When the visibility of the dialog changes, sets the editedPudItem to a clone
 * of the additionalChargeItem prop for use in the form
 */
watch(dialogController, (newValue) => {
  if (newValue && props.additionalChargeType) {
    const toEdit = Object.assign(
      new AdditionalChargeType(),
      deepCopy(props.additionalChargeType),
    );
    editedAdditionalChargeType.value = toEdit;
  } else {
    editedAdditionalChargeType.value = new AdditionalChargeType();
  }
});

/**
 * watch longName to prefill Short Name
 */
watch(
  () => editedAdditionalChargeType.value.longName,
  (newLongName) => {
    // If newLongName is not empty
    if (newLongName) {
      // Check if shortName is empty or hasn't been manually set
      if (
        !props.additionalChargeType?.shortName ||
        !editedAdditionalChargeType.value.shortName
      ) {
        editedAdditionalChargeType.value.shortName = newLongName
          .slice(0, 3)
          .toUpperCase();
      }
      // If shortName is the default (first 3 letters of longName), update it
      else if (
        props.additionalChargeType?.shortName ===
        props.additionalChargeType?.longName.slice(0, 3).toUpperCase()
      ) {
        // Get the first 3 letters of the longName and update shortName, convert to uppercase
        editedAdditionalChargeType.value.shortName = newLongName
          .slice(0, 3)
          .toUpperCase();
      }
    }
  },
);

/**
 * Saves an additional charge type or item.
 * This function checks if an additional charge type or item is currently being edited.
 * If an additional charge type is being edited, it sends a save request for the type.
 * If an additional charge item is being edited, it validates the item and sends a save request for the item.
 * After a successful save, it shows a success notification and resets the edit state.
 * @returns {Promise<void>} Resolves when the save operation is complete.
 * @throws {Error} If the save request fails, an error will be thrown.
 */
async function saveAdditionalCharge(): Promise<void> {
  if (!editedAdditionalChargeType.value) {
    return;
  }
  if (!editedAdditionalChargeType.value.chargeApplicationType) {
    editedAdditionalChargeType.value.chargeApplicationType =
      AdditionalChargeApplicationType.FREIGHT;
  }
  if (
    !additionalChargesDialogForm.value?.validate() ||
    !validateCheckboxes.value
  ) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE, {
      title: 'Additional Charge Category',
      type: HealthLevel.ERROR,
    });
    return;
  }

  isAwaitingSaveResponse.value = true;

  // TODO: Add some validation for additional charge type
  const result = await useRootStore().saveAdditionalChargeType(
    editedAdditionalChargeType.value,
  );
  if (result) {
    showNotification(GENERIC_SUCCESS_MESSAGE, {
      title: 'Additional Charge Category',
      type: HealthLevel.SUCCESS,
    });
  } else {
    showNotification(GENERIC_ERROR_MESSAGE, {
      title: 'Additional Charge Category',
      type: HealthLevel.ERROR,
    });
  }
  isAwaitingSaveResponse.value = false;
  dialogController.value = false;
}

const isAuthorised: ComputedRef<boolean> = computed(() => {
  return hasAdminOrHeadOfficeRole();
});
</script>

<style scoped lang="scss">
.custom-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin: 6px 10px 2px 2px;
  padding: 4px;
  font-size: $font-size-18;
  color: var(--text-color);
  font-weight: 500;
  &.disabled {
    color: gray;
  }
}
#checkbox {
  accent-color: var(--primary);
}

.custom-checkbox input[type='checkbox']:disabled {
  cursor: not-allowed;
  opacity: 0.6; /* Makes the disabled checkbox look faded */
  color: gray;
}

input[type='checkbox'] {
  margin-right: 8px;
  position: relative;
  transition: all 0.3s ease;
  width: 20px;
  height: 20px;
  border-radius: 10px;
}

.custom-switch {
  margin: 10px 10px 2px 2px;
  padding: 4px;
  font-size: 18px !important;
}
</style>
