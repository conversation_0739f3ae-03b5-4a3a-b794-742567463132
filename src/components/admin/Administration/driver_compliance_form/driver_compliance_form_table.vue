<template>
  <section>
    <v-flex md12>
      <v-data-table
        class="gd-dark-theme bordered"
        :headers="headers"
        :items="complianceFormTableData"
        item-key="_id"
        :rows-per-page-items="[20, 40]"
      >
        <template v-slot:items="tableProps">
          <tr
            @click="viewDriverComplianceForm(tableProps.item._id)"
            style="cursor: pointer"
          >
            <td>{{ tableProps.item.title }}</td>
            <td v-if="tableProps.item.applicableAffiliations.length > 0">
              {{ getAffiliationNames(tableProps.item.applicableAffiliations) }}
            </td>
            <td v-else>ALL</td>
            <td>
              {{ tableProps.item.requiredForAllDrivers ? 'YES' : 'NO' }}
            </td>
            <td>
              {{ checklistTriggerOnLabels[tableProps.item.triggerOn] }}
            </td>
          </tr>
        </template>
      </v-data-table>
    </v-flex>
  </section>
</template>
<script setup lang="ts">
import affiliationTypes from '@/interface-models/Generic/Affiliation/Affiliations';
import { ChecklistTriggerOn } from '@/interface-models/Generic/SafetyChecklist/ChecklistTriggerOn';
import { DriverComplianceForm } from '@/interface-models/Generic/SafetyChecklist/DriverComplianceForm';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { ComputedRef, computed } from 'vue';

const props = withDefaults(
  defineProps<{
    driverComplianceForms: DriverComplianceForm[];
    searchQuery: string;
  }>(),
  {
    driverComplianceForms: () => [],
    searchQuery: '',
  },
);

const emit = defineEmits<{
  (event: 'viewDriverComplianceForm', payload: string): void;
}>();

const headers: TableHeader[] = [
  { text: 'Title', align: 'left', value: 'title', sortable: true },
  {
    text: 'Applicable Affiliations',
    align: 'left',
    value: 'applicableAffiliations',
    sortable: true,
  },
  {
    text: 'Required for All Drivers',
    align: 'left',
    value: 'requiredForAllDrivers',
    sortable: true,
  },
  {
    text: 'Trigger On',
    align: 'left',
    value: 'triggerOn',
    sortable: true,
  },
];

/**
 * Computes the DriverChecklist table data based on the DriverComplianceForm.
 * filters the data based on search query.
 * @returns {DriverComplianceForm[]} The filtered checklist table data.
 */
const complianceFormTableData: ComputedRef<DriverComplianceForm[]> = computed(
  () => {
    const query = props.searchQuery.toLowerCase();

    return props.driverComplianceForms
      .map((item) => ({
        _id: item._id ? item._id : '',
        company: item.company,
        division: item.division,
        fleetAssetTypeId: item.fleetAssetTypeId,
        title: item.title,
        triggerOn: item.triggerOn,
        requiredForAllDrivers: item.requiredForAllDrivers,
        isSignatureRequired: item.isSignatureRequired,
        applicableAffiliations: item.applicableAffiliations,
        questionGroups: item.questionGroups,
        applicableServiceTypeIds: item.applicableServiceTypeIds,
        applicableTruckClasses: item.applicableTruckClasses,
      }))
      .filter((checklist) => {
        return checklist.title.toLowerCase().includes(query);
      });
  },
);

// function to map Affiliation id to names for table data
function getAffiliationNames(ids: string[]): string {
  return ids
    .map((id) => {
      return (
        affiliationTypes.find((type) => type.id === id)?.longName ?? 'Unknown'
      );
    })
    .join(', ');
}

// function to set ChecklistTriggerOn string for table data
const checklistTriggerOnLabels: Record<ChecklistTriggerOn, string> = {
  [ChecklistTriggerOn.START_OF_DAY]: 'Start of Day',
  [ChecklistTriggerOn.START_OF_JOB]: 'Start of Job',
};

/**
 * Emits an event to view or edit the DriverChecklist by ID,
 * @param {string} id - The ID of the DriverChecklist.
 */
function viewDriverComplianceForm(id: string): void {
  emit('viewDriverComplianceForm', id);
}
</script>

<style scoped lang="scss">
.selected {
  background-color: var(--background-color-300);
  font-size: $font-size-24;
}

.question-card {
  padding: 4px;
}

.question-header h2 {
  font-size: 20px;
  font-weight: bold;
}

.ques-group-title {
  font-size: 18px;
  font-weight: bold;
  color: var(--primary-light);
  margin-top: 12px;
  margin-bottom: 8px;

  .ques-type {
    font-size: $font-size-16;
    font-weight: 400;
    color: var(--light-text-color);
    text-transform: capitalize;
    padding-left: 12px;
  }
}

.question-item {
  font-size: 16px;
  margin-top: 6px;
  margin-bottom: 6px;
}

.option-flex {
  display: inline-block;
}

.edit-btn {
  border: 1px solid $accent;
  border-radius: 20px;
}
</style>
