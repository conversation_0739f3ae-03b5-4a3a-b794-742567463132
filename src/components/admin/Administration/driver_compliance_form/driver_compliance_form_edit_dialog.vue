<template>
  <ContentDialog
    :showDialog.sync="dialogController"
    :title="
      !editedDriverChecklist?._id
        ? 'New Driver Compliance Form'
        : 'Edit Driver Compliance Form'
    "
    width="70%"
    contentPadding="pa-0"
    @cancel="dialogController = false"
    @confirm="saveDriverChecklist"
    :showActions="true"
    :isConfirmUnsaved="true"
    :isDisabled="!isAdmin"
    :isLoading="isAwaitingSaveResponse"
    confirmBtnText="Confirm"
  >
    <v-flex class="body-scrollable--75 body-min-height--65 pa-3">
      <v-layout>
        <v-flex md10 offset-md1>
          <v-form
            ref="driverChecklistDialogForm"
            class="additional-charges-edit-dialog"
          >
            <v-flex md12 pb-3
              ><v-layout align-center>
                <h5 class="subheader--bold pr-3 pt-1">Key Details</h5>
                <v-flex>
                  <v-divider></v-divider>
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6
                      class="subheader--faded pr-3 pb-0 form-field-required-marker"
                    >
                      Enter Checklist Title:
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-text-field
                    v-model="editedDriverChecklist.title"
                    label="Enter Title"
                    hint="This will be displayed on the driver app"
                    dense
                    solo
                    flat
                    class="form-field-required v-solo-custom pt-2"
                    persistent-hint
                    :rules="[validationRules.required]"
                    :disabled="!isAdmin"
                  />
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6
                      class="subheader--faded pr-3 pb-0 form-field-required-marker"
                    >
                      Show to Driver at...
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-select
                    v-model="editedDriverChecklist.triggerOn"
                    :items="triggerOnOptions"
                    item-value="value"
                    item-text="text"
                    label="Trigger On"
                    :hint="
                      returnChecklistTriggerOnHintText(
                        editedDriverChecklist.triggerOn,
                      )
                    "
                    dense
                    solo
                    flat
                    class="form-field-required v-solo-custom pt-2"
                    persistent-hint
                    :rules="[validationRules.required]"
                    :disabled="true"
                  />
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">
                      Required for all drivers:
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-checkbox
                    v-model="editedDriverChecklist.requiredForAllDrivers"
                    label="Required for all drivers"
                    :hint="
                      editedDriverChecklist.requiredForAllDrivers
                        ? 'This will be displayed to all drivers'
                        : 'This will be displayed drivers based on the criteria selected below'
                    "
                    :disabled="!isAdmin"
                    persistent-hint
                    class="mt-2 pa-2"
                  />
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12 v-if="!editedDriverChecklist.requiredForAllDrivers">
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6
                      class="subheader--faded pr-3 pb-0"
                      :class="{
                        'form-field-required-marker': requireAdditionalCriteria,
                      }"
                    >
                      Applicable to Owner Types:
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-select
                    v-model="editedDriverChecklist.applicableAffiliations"
                    :items="filteredAffiliationTypes"
                    item-text="longName"
                    item-value="id"
                    label="Select Affiliations"
                    hint="Select Affiliations"
                    dense
                    multiple
                    solo
                    flat
                    class="v-solo-custom pt-2"
                    persistent-hint
                    clearable
                    :rules="
                      requireAdditionalCriteria
                        ? [validationRules.listRequired]
                        : []
                    "
                    :disabled="!isAdmin"
                  />
                </v-flex>
              </v-layout>
            </v-flex>
            <!-- <v-autocomplete
              v-model="editedDriverChecklist.applicableServiceTypeIds"
              :items="serviceTypes"
              label="Select Service Type"
              item-text="optionSelectName"
              item-value="serviceTypeId"
              outline
              class="v-solo-custom"
              hide-selected
              multiple
              small-chips
              clearable
              deletable-chips
              :disabled="!isAdmin"
            /> -->
            <v-flex md12 v-if="!editedDriverChecklist.requiredForAllDrivers">
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6
                      class="subheader--faded pr-3 pb-0"
                      :class="{
                        'form-field-required-marker': requireAdditionalCriteria,
                      }"
                    >
                      Applicable Truck Classes:
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-autocomplete
                    v-model="editedDriverChecklist.applicableTruckClasses"
                    :items="serviceTypes"
                    label="Select Truck Class"
                    hint="Select Truck Class"
                    item-text="optionSelectName"
                    item-value="serviceTypeShortName"
                    class="v-solo-custom pt-2"
                    hide-selected
                    multiple
                    small-chips
                    clearable
                    deletable-chips
                    persistent-hint
                    solo
                    flat
                    dense
                    :rules="
                      requireAdditionalCriteria
                        ? [validationRules.listRequired]
                        : []
                    "
                    :disabled="!isAdmin"
                  />
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">
                      Signature required on submission:
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-checkbox
                    v-model="editedDriverChecklist.isSignatureRequired"
                    label="Driver Signature Required"
                    :hint="
                      editedDriverChecklist.isSignatureRequired
                        ? 'The driver will be required to sign the form as the final step'
                        : 'No signature required for submission.'
                    "
                    :disabled="!isAdmin"
                    persistent-hint
                    class="mt-2 pa-2"
                  />
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12 pb-3 pt-3
              ><v-layout align-center>
                <h5 class="subheader--bold pr-3 pt-2">Form Sections</h5>
                <v-flex>
                  <v-divider></v-divider>
                </v-flex>
                <span>
                  <v-btn
                    @click="addQuestionGroup"
                    color="orange"
                    small
                    outline
                    class="add-group-btn"
                    v-if="isAdmin"
                    >Add New Section</v-btn
                  >
                </span>
              </v-layout>
            </v-flex>
            <v-flex
              class="button-group mb-3"
              v-if="editedDriverChecklist.questionGroups.length > 0"
            >
              <v-tooltip
                v-for="(
                  questionGroup, index
                ) in editedDriverChecklist.questionGroups"
                :key="questionGroup.id"
                bottom
              >
                <template v-slot:activator="{ on }">
                  <span v-on="on">
                    <v-btn
                      :class="{
                        'v-btn--active':
                          questionGroup.id === selectedQuestionGroup,
                      }"
                      flat
                      @click="selectedQuestionGroup = questionGroup.id"
                    >
                      <span class="px-2">
                        <strong>
                          {{
                            `${index + 1}. ${
                              questionGroup.title
                                ? questionGroup.title
                                : 'New Section'
                            }`
                          }}
                        </strong>
                      </span>
                    </v-btn>
                  </span>
                </template>
                {{
                  `${index + 1}. ${
                    questionGroup.title ? questionGroup.title : 'New Section'
                  }`
                }}
              </v-tooltip>
            </v-flex>
            <!-- Loop through question groups -->
            <v-flex
              v-show="questionGroup.id === selectedQuestionGroup"
              v-for="(
                questionGroup, index
              ) in editedDriverChecklist.questionGroups"
              :key="questionGroup.id"
            >
              <v-layout md12 align-center row wrap>
                <v-flex md12>
                  <v-layout>
                    <v-flex md4>
                      <v-layout align-center class="form-field-label-container">
                        <h6
                          class="subheader--faded pr-3 pb-0 form-field-required-marker"
                        >
                          Section Title:
                        </h6>
                      </v-layout>
                    </v-flex>
                    <v-flex md8>
                      <v-text-field
                        v-model="questionGroup.title"
                        hint="This will be displayed on the driver app at the top of each section"
                        label="Enter Group Title"
                        dense
                        solo
                        flat
                        class="v-solo-custom pt-2"
                        persistent-hint
                        :disabled="!isAdmin"
                        :rules="[validationRules.required]"
                      />
                    </v-flex>
                  </v-layout>
                </v-flex>

                <v-flex md12>
                  <v-layout>
                    <v-flex md4>
                      <v-layout align-center class="form-field-label-container">
                        <h6 class="subheader--faded pr-3 pb-0">
                          Response Type (multi-choice / input):
                        </h6>
                      </v-layout>
                    </v-flex>
                    <v-flex md8>
                      <v-select
                        v-model="questionGroup.questionType"
                        :items="questionTypes"
                        label="Select Question Type"
                        hint="Multiple choice questions. Please remove all questions to change type."
                        dense
                        solo
                        flat
                        class="v-solo-custom pl-2 pt-2"
                        persistent-hint
                        @input="updateQuestionTypes(questionGroup)"
                        :disabled="
                          questionGroup.questions.length > 0 || !isAdmin
                        "
                      />
                    </v-flex>
                  </v-layout>
                </v-flex>

                <v-tooltip bottom v-if="questionGroup.questions.length < 1">
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn
                      @click="removeQuestionGroup(index)"
                      color="error"
                      small
                      icon
                      class="delete-group-btn"
                      v-bind="attrs"
                      v-on="on"
                      v-if="isAdmin"
                      ><v-icon size="16">delete</v-icon></v-btn
                    >
                  </template>
                  <span>Delete Group</span>
                </v-tooltip>

                <v-flex
                  md12
                  v-if="
                    questionGroup.questionType === ChecklistQuestionType.CHOICE
                  "
                >
                  <v-layout>
                    <v-flex md4>
                      <v-layout align-center class="form-field-label-container">
                        <span
                          class="subheader--faded pr-3 pb-0 form-field-required-marker"
                        >
                          <span class="pr-2">
                            <InformationTooltip
                              :left="true"
                              :tooltipType="HealthLevel.INFO"
                            >
                              <v-layout slot="content" row wrap>
                                <v-flex md12>
                                  <p class="mb-1">
                                    When the question group has has a 'response
                                    type' of CHOICE, the driver will be
                                    presented with a pre-set list of options to
                                    select from.
                                  </p>
                                  <p class="mb-1">
                                    The last option in the list will be
                                    considered the negative response (ie NO,
                                    FALSE), and will cause the question to be
                                    marked as failed.
                                  </p>
                                  <p class="mb-0">
                                    The number of available choices can be 2 to
                                    4.
                                  </p>
                                </v-flex>
                              </v-layout>
                            </InformationTooltip>
                          </span>
                          Enter selectable options:</span
                        >
                      </v-layout>
                    </v-flex>
                    <v-flex md8>
                      <v-layout md12>
                        <v-flex
                          v-for="(
                            option, optionIndex
                          ) in questionGroup.optionChoices"
                          :key="optionIndex"
                        >
                          <!-- If Question Type is CHOICE, show option choices -->
                          <v-text-field
                            v-model="questionGroup.optionChoices[optionIndex]"
                            label="Option Choice"
                            :hint="
                              optionIndex ===
                              questionGroup.optionChoices.length - 1
                                ? 'Enter a negative response (ie NO, FALSE)'
                                : `Option ${optionIndex + 1}`
                            "
                            dense
                            solo
                            flat
                            class="v-solo-custom pl-2 pt-2"
                            persistent-hint
                            :disabled="!isAdmin"
                            :rules="[validationRules.required]"
                          />
                        </v-flex>
                        <v-flex>
                          <v-tooltip
                            bottom
                            v-if="questionGroup.optionChoices.length > 2"
                          >
                            <template v-slot:activator="{ on, attrs }">
                              <v-btn
                                @click="removeOptionChoice(questionGroup)"
                                color="error"
                                small
                                icon
                                class="delete-ques-btn"
                                v-bind="attrs"
                                v-on="on"
                                v-if="isAdmin"
                              >
                                <v-icon size="20">delete</v-icon>
                              </v-btn>
                            </template>
                            <span>Remove Option</span>
                          </v-tooltip>
                          <v-tooltip
                            bottom
                            v-if="questionGroup.optionChoices.length < 4"
                          >
                            <template v-slot:activator="{ on, attrs }">
                              <v-btn
                                @click="addOptionChoice(questionGroup)"
                                color="primary"
                                small
                                icon
                                class="add-option-btn"
                                v-bind="attrs"
                                v-on="on"
                                v-if="isAdmin"
                              >
                                <v-icon size="16">add</v-icon>
                              </v-btn>
                            </template>
                            <span>Add New Option</span>
                          </v-tooltip>
                        </v-flex>
                      </v-layout>
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex md12>
                  <v-layout row wrap>
                    <v-flex md10 offset-md1 pb-3 pt-3
                      ><v-layout align-center>
                        <h5 class="subheader--bold pr-3 pt-2">Questions</h5>
                        <v-flex>
                          <v-divider></v-divider>
                        </v-flex>
                        <span>
                          <v-btn
                            @click="addQuestion(questionGroup)"
                            color="success"
                            small
                            class="add-ques-btn"
                            v-if="isAdmin"
                            >Add Question</v-btn
                          >
                        </span>
                      </v-layout>
                    </v-flex>
                    <v-flex
                      md12
                      v-for="(question, qIndex) in questionGroup.questions"
                      :key="qIndex"
                    >
                      <v-layout>
                        <v-flex md1></v-flex>
                        <v-flex md3>
                          <v-layout
                            align-center
                            class="form-field-label-container"
                          >
                            <span
                              class="subheader--faded pr-3 pb-0 form-field-required-marker"
                              >Question #{{ qIndex + 1 }}:</span
                            >
                          </v-layout>
                        </v-flex>
                        <v-flex md7>
                          <v-layout md12>
                            <v-flex>
                              <!-- Question Title -->
                              <v-text-field
                                v-model="question.title"
                                :hint="'Question ' + (qIndex + 1)"
                                label="Enter Question"
                                dense
                                solo
                                flat
                                class="v-solo-custom pl-2 pt-2"
                                persistent-hint
                                :disabled="!isAdmin"
                                :rules="[validationRules.required]"
                              />
                            </v-flex>
                            <v-flex
                              md4
                              v-if="
                                questionGroup.questionType !==
                                ChecklistQuestionType.CHOICE
                              "
                            >
                              <!-- Allow user to select inputType only if questionType is INPUT -->
                              <v-select
                                v-model="question.inputType"
                                :items="inputTypes"
                                label="Select Answer Type"
                                :hint="
                                  returnInputTypeHintText(question.inputType)
                                "
                                dense
                                solo
                                flat
                                class="v-solo-custom pl-2 pt-2"
                                persistent-hint
                                :disabled="!isAdmin"
                              />
                            </v-flex>
                            <v-tooltip bottom>
                              <template v-slot:activator="{ on, attrs }">
                                <v-btn
                                  @click="removeQuestion(questionGroup, qIndex)"
                                  color="error"
                                  small
                                  icon
                                  class="delete-ques-btn"
                                  v-bind="attrs"
                                  v-on="on"
                                  v-if="isAdmin"
                                  ><v-icon size="20">delete</v-icon></v-btn
                                >
                              </template>
                              <span>Delete Question</span>
                            </v-tooltip>
                          </v-layout>
                        </v-flex>
                      </v-layout>
                    </v-flex>
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-form>
        </v-flex>
      </v-layout>
    </v-flex>
  </ContentDialog>
</template>

<script setup lang="ts">
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import { deepCopy } from '@/helpers/JsonHelpers/JsonHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  GENERIC_ERROR_MESSAGE,
  GENERIC_SUCCESS_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import affiliationTypes from '@/interface-models/Generic/Affiliation/Affiliations';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { ChecklistQuestionGroup } from '@/interface-models/Generic/SafetyChecklist/ChecklistQuestionGroup';
import {
  ChecklistQuestionInputType,
  returnInputTypeHintText,
} from '@/interface-models/Generic/SafetyChecklist/ChecklistQuestionInputType';
import { ChecklistQuestionType } from '@/interface-models/Generic/SafetyChecklist/ChecklistQuestionType';
import {
  ChecklistTriggerOn,
  returnChecklistTriggerOnHintText,
} from '@/interface-models/Generic/SafetyChecklist/ChecklistTriggerOn';
import { DriverComplianceForm } from '@/interface-models/Generic/SafetyChecklist/DriverComplianceForm';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { sessionManager } from '@/store/session/SessionState';
import { v4 as uuidv4 } from 'uuid';
import {
  computed,
  ComputedRef,
  Ref,
  ref,
  watch,
  WritableComputedRef,
} from 'vue';

const props = withDefaults(
  defineProps<{
    isDialogOpen: boolean;
    driverChecklist: DriverComplianceForm | null;
  }>(),
  {
    driverChecklist: null,
  },
);

const emit = defineEmits<{
  (event: 'update:isDialogOpen', payload: boolean): void;
  (event: 'updateChecklist', payload: DriverComplianceForm): void;
}>();

const isAwaitingSaveResponse: Ref<boolean> = ref(false);
const driverChecklistDialogForm: Ref<any> = ref(null);

const selectedQuestionGroup: Ref<string | null> = ref(null);

// check if logged in user is admin or head office role
const isAdmin = computed(() => {
  return hasAdminOrHeadOfficeRole();
});

const editedDriverChecklist: Ref<DriverComplianceForm> = ref(
  createDefaultChecklist(),
);

// input options
const triggerOnOptions = computed(() =>
  Object.entries(ChecklistTriggerOn).map(([value]) => ({
    value,
    text: value.replace(/_/g, ' '), // Converts START_OF_DAY → "START OF DAY"
  })),
);

/**
 * Computes a unique list of serviceType options based on the company serviceTypesMap.
 * @returns An array of serviceType objects containing 'optionSelectName' and 'serviceTypeId'.
 */
const serviceTypes = computed(() => {
  const serviceTypes = Array.from(
    useCompanyDetailsStore().serviceTypesMap.values(),
  ).map((serviceType) => ({
    optionSelectName: serviceType.optionSelectName,
    serviceTypeId: serviceType.serviceTypeId,
    serviceTypeShortName: serviceType.shortServiceTypeName,
  }));
  return serviceTypes;
});

const questionTypes = Object.values(ChecklistQuestionType);
const filteredAffiliationTypes = affiliationTypes.filter(
  (type) => type.id !== '3',
);
const inputTypes = Object.values(ChecklistQuestionInputType).filter(
  (inputType) => inputType !== ChecklistQuestionInputType.OPTION,
);

/**
 * Gets and sets the isDialogOpen prop passed from parent
 */
const dialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.isDialogOpen;
  },
  set(value: boolean): void {
    emit('update:isDialogOpen', value);
  },
});

/**
 * Computed property to determine if we should make certain fields required in
 * the template.
 *
 * If the checklist is not required for all drivers, we need to enforce that at
 * least one of the following fields is filled:
 * - applicableAffiliations
 * - applicableServiceTypeIds
 * - applicableTruckClasses
 *
 * If all of these fields are null/empty, we return true to indicate that
 * additional criteria are required.
 */
const requireAdditionalCriteria: ComputedRef<boolean> = computed(() => {
  if (
    !editedDriverChecklist.value ||
    editedDriverChecklist.value.requiredForAllDrivers
  ) {
    return false;
  }
  // If the checklist is NOT required for all drivers, additional criteria are
  // needed to determine whether a checklist is suitable. Return true if
  // applicableAffiliations, applicableServiceTypeIds and applicableTruckClasses
  // are ALL null/empty
  const applicableAffiliations =
    editedDriverChecklist.value.applicableAffiliations;
  const applicableTruckClasses =
    editedDriverChecklist.value.applicableTruckClasses;
  if (
    (!applicableAffiliations || applicableAffiliations.length === 0) &&
    // !editedDriverChecklist.value.applicableServiceTypeIds?.length &&
    (!applicableTruckClasses?.length || applicableTruckClasses.length === 0)
  ) {
    return true;
  }
  // If any applicable fields are not empty, return false
  return false;
});

/**
 * Creates a new default Driver Compliance Checklist with preset values.
 * Initializes required fields with default values for a truck checklist.
 *
 * @returns {DriverComplianceForm} A new default driver compliance checklist object.
 */
function createDefaultChecklist(): DriverComplianceForm {
  return {
    _id: undefined,
    company: sessionManager.getCompanyId(),
    division: sessionManager.getDivisionId(),
    fleetAssetTypeId: 1, // 1 for truck
    title: '',
    triggerOn: ChecklistTriggerOn.START_OF_DAY, // Default trigger
    requiredForAllDrivers: false,
    isSignatureRequired: false,
    applicableAffiliations: [],
    questionGroups: [],
    applicableServiceTypeIds: [],
    applicableTruckClasses: [],
  };
}

// add remove functions for question group, questions, Answer options
// function to add question group
function addQuestionGroup() {
  editedDriverChecklist.value?.questionGroups.push({
    id: uuidv4(),
    title: '',
    questionType: ChecklistQuestionType.INPUT,
    questions: [],
    optionChoices: [],
  });
  // If the new question group is the only one, set it as selected
  if (
    editedDriverChecklist.value?.questionGroups.length === 1 &&
    selectedQuestionGroup.value === null
  ) {
    selectedQuestionGroup.value =
      editedDriverChecklist.value.questionGroups[0].id;
  }
}

/**
 * Called in the template to remove a question group. It removes the question
 * group at the specified index from the questionGroups array and selects the
 * first group if the removed group was selected.
 * @param questionIndex - The index of the question group to be removed.
 */
function removeQuestionGroup(questionIndex: number): void {
  const removedGroup =
    editedDriverChecklist.value.questionGroups[questionIndex];
  editedDriverChecklist.value.questionGroups.splice(questionIndex, 1);

  // If the removed group was selected, update selection
  if (selectedQuestionGroup.value === removedGroup.id) {
    if (editedDriverChecklist.value.questionGroups.length === 0) {
      selectedQuestionGroup.value = null;
    } else {
      selectedQuestionGroup.value =
        editedDriverChecklist.value.questionGroups[0].id;
    }
  }
}

// function to add questions to group
function addQuestion(questionGroup: any) {
  const newQuestion = {
    id: uuidv4(),
    title: '',
    inputType:
      questionGroup.questionType === ChecklistQuestionType.CHOICE
        ? ChecklistQuestionInputType.OPTION
        : ChecklistQuestionInputType.TEXT, // Default to OPTION for CHOICE
  };

  questionGroup.questions.push(newQuestion);

  // if answer type option add YES/NO as default option
  if (
    !questionGroup.optionChoices ||
    questionGroup.optionChoices.length === 0
  ) {
    questionGroup.optionChoices = ['YES', 'NO'];
  }
}
function removeQuestion(
  questionGroup: ChecklistQuestionGroup,
  questionIndex: number,
) {
  questionGroup.questions.splice(questionIndex, 1);
}

function addOptionChoice(questionGroup: ChecklistQuestionGroup) {
  questionGroup.optionChoices.push('');
}
function removeOptionChoice(questionGroup: ChecklistQuestionGroup) {
  questionGroup.optionChoices.splice(-1, 1);
}

// function updates on question type selector
function updateQuestionTypes(questionGroup: ChecklistQuestionGroup) {
  if (questionGroup.questionType === ChecklistQuestionType.CHOICE) {
    questionGroup.questions.forEach((q) => {
      q.inputType = ChecklistQuestionInputType.OPTION;
    });

    addQuestion(questionGroup);
  }
}

/**
 * Saves the edited driver compliance checklist.
 * Validates the form before submission, sends the checklist to the backend,
 * and provides feedback via notifications.
 *
 * @emits updateChecklist - Emits the updated checklist after successful save.
 *
 * @returns {Promise<void>} A promise that resolves when the operation is complete.
 */
async function saveDriverChecklist(): Promise<void> {
  if (!editedDriverChecklist.value) {
    return;
  }
  if (!driverChecklistDialogForm.value?.validate()) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE, {
      title: 'Driver Safety Checklist',
      type: HealthLevel.ERROR,
    });
    return;
  }
  isAwaitingSaveResponse.value = true;
  const result = await useDriverDetailsStore().saveDriverChecklist(
    editedDriverChecklist.value,
  );
  if (result) {
    emit('updateChecklist', result);
    showNotification(GENERIC_SUCCESS_MESSAGE, {
      title: 'Driver Safety Checklist',
      type: HealthLevel.SUCCESS,
    });
  } else {
    showNotification(GENERIC_ERROR_MESSAGE, {
      title: 'Driver Safety Checklist',
      type: HealthLevel.ERROR,
    });
  }
  isAwaitingSaveResponse.value = false;
  dialogController.value = false;
}

/**
 * Watches for changes in the `dialogController` state.
 * When the dialog opens (`newValue === true`), it initializes `editedDriverChecklist`:
 * - If `props.driverChecklist` exists, it deep copies and assigns it.
 * - Otherwise, it creates a new default checklist.
 *
 * @param {boolean} newValue - The updated value of `dialogController`.
 */
watch(dialogController, (newValue) => {
  if (newValue && props.driverChecklist) {
    editedDriverChecklist.value = deepCopy(props.driverChecklist);
    if (editedDriverChecklist.value.questionGroups.length > 0) {
      selectedQuestionGroup.value =
        editedDriverChecklist.value.questionGroups[0].id;
    }
  } else {
    editedDriverChecklist.value = createDefaultChecklist();
  }
});
</script>

<style scoped lang="scss">
.add-group-btn {
  margin-bottom: 6px;
  border-radius: 20px;
  padding: 0 12px;
}

.add-ques-btn {
  margin-bottom: 24px;
  margin-left: 12px;
  border-radius: 20px;
  padding: 0 12px;
}

.add-option-btn {
  margin-top: 18px;
  margin-left: 12px;
  border-radius: 20px;
}

.delete-ques-btn {
  margin-top: 18px;
  margin-left: 12px;
  border-radius: 20px;
}

.delete-group-btn {
  border-radius: 20px;
  margin-bottom: 24px;
  margin-left: 12px;
}
</style>
