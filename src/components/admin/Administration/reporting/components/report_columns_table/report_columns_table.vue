<template>
  <div>
    <v-data-table
      :headers="tableHeaders"
      :items="columns"
      class="gd-dark-theme"
      hide-actions
      ><template v-slot:items="props">
        <tr>
          <td style="padding-left: 24px">{{ props.item.name }}</td>
          <td>{{ props.item.description }}</td>
          <td>{{ props.item.calculation }}</td>
        </tr>
      </template></v-data-table
    >
  </div>
</template>

<script setup lang="ts">
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { ReportColumnDescription } from '@/interface-models/Reporting/ReportType';

const props = withDefaults(
  defineProps<{
    columns: ReportColumnDescription[];
  }>(),
  {},
);

const tableHeaders: TableHeader[] = [
  {
    text: 'Column Name',
    align: 'left',
    sortable: false,
    value: 'name',
  },
  {
    text: 'Description',
    align: 'left',
    sortable: false,
    value: 'description',
  },
  {
    text: 'Calculation',
    align: 'left',
    sortable: false,
    value: 'calculation',
  },
];
</script>

<style scoped lang="scss">
.column-header-text {
  font-size: $font-size-12;
  text-transform: uppercase;
  font-weight: 600;
  color: var(--light-text-color);
  letter-spacing: 0.02em;
}
</style>
