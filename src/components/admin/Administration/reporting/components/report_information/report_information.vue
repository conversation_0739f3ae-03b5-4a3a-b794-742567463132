<template>
  <v-layout wrap class="mb-3">
    <v-flex
      md12
      class="app-bgcolor--400 app-borderside--a app-bordercolor--600 mb-3"
    >
      <v-layout wrap>
        <v-flex md12>
          <div class="header-text pa-2">Report Parameters</div>
          <v-divider class="ma-0 pa-0" />
        </v-flex>
        <v-flex md12 class="pa-3">
          <slot name="content"></slot>
        </v-flex>
      </v-layout>
    </v-flex>

    <v-flex
      md12
      class="app-bgcolor--400 app-borderside--a app-bordercolor--600 mb-3"
    >
      <v-layout wrap>
        <v-flex md12 class="header-text">
          <div class="pa-2"><slot name="name"></slot></div>
          <v-divider class="ma-0 pa-0" />
        </v-flex>

        <v-flex md12 class="pa-3">
          <v-layout wrap>
            <v-flex md12 class="mb-2">
              <p class="block header-text mb-1">Description</p>
              <slot name="description"></slot>
            </v-flex>

            <v-flex md12 class="mb-2">
              <p class="block header-text mb-1">Audience</p>
              <slot name="audience"></slot>
            </v-flex>

            <v-flex md12 class="mb-2">
              <p class="block header-text mb-1">Query</p>
              <slot name="query"></slot>
            </v-flex>

            <v-flex md12 class="mb-2">
              <p class="block header-text mb-1">File name</p>
              <slot name="filename"></slot>
            </v-flex>

            <v-flex md12>
              <p class="block header-text mb-1">Permissions</p>
              <slot name="permissions"></slot>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>

    <v-flex
      md12
      class="app-bgcolor--400 app-borderside--a app-bordercolor--600 mb-3"
    >
      <div class="pa-2 header-text">Column Descriptions</div>
      <v-divider class="ma-0 pa-0" />

      <slot name="columns"> </slot>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts"></script>

<style scoped lang="scss">
.report-details-header {
  font-size: $font-size-16;
  text-transform: uppercase;
  font-weight: 600;
  color: #e49e1d;
  letter-spacing: 0.02em;
}

.header-text {
  font-size: $font-size-14;
  text-transform: uppercase;
  font-weight: 600;
  color: #e49e1d;
  letter-spacing: 0.02em;
}
</style>
