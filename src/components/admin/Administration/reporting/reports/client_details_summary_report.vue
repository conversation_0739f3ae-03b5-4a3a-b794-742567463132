<template>
  <section>
    <v-form ref="form">
      <v-layout>
        <v-flex md6 class="pr">
          <v-select
            label="Sort By"
            v-model="selectedSortByOption"
            :items="sortByOptions"
            item-value="id"
            hint="Please select how you would like this report to be sorted."
            persistent-hint
            item-text="longName"
            solo
            flat
            class="v-solo-custom"
          ></v-select>
        </v-flex>

        <v-flex md6 class="pl">
          <v-checkbox
            v-model="includeRetired"
            label="Include Retired Clients"
          ></v-checkbox>
        </v-flex>
      </v-layout>
    </v-form>
    <div class="action-bar">
      <v-layout>
        <v-flex md12>
          <v-layout justify-end align-center>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.DOWNLOAD,
                )
              "
              depressed
              class="download"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.DOWNLOAD)"
            >
              <v-icon class="pr-2" size="20">downloading</v-icon>
              Download Report
            </v-btn>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.EMAIL,
                )
              "
              depressed
              class="email"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.EMAIL)"
            >
              <v-icon class="pr-2" size="20">forward_to_inbox</v-icon>
              Email Report
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
  </section>
</template>

<script setup lang="ts">
import { returnEndOfDayFromEpoch } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { hasAdminOrHeadOfficeOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import GenerateReportRequest from '@/interface-models/Reporting/GenerateReportRequest';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { ReportFormatType } from '@/interface-models/Reporting/ReportFormatType';
import { ReportSortByEnum } from '@/interface-models/Reporting/ReportSortByEnum';
import { ReportType } from '@/interface-models/Reporting/ReportType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import moment from 'moment-timezone';
import { ref, Ref } from 'vue';

interface SortByOption {
  id: ReportSortByEnum;
  longName: string;
}

const props = withDefaults(
  defineProps<{
    isLoading?: boolean;
  }>(),
  {
    isLoading: false,
  },
);

const form = ref<any>(null);
const includeRetired: Ref<boolean> = ref(false);

const company = useCompanyDetailsStore().companyDetails;
const reportsSettings = company?.divisions?.[0]?.customConfig?.reports;

const selectedSortByOption: Ref<ReportSortByEnum> = ref(
  ReportSortByEnum.CLIENT_NAME,
);

const emit = defineEmits<{
  (event: 'update:isLoading', value: boolean): void;
  (event: 'generateReport', payload: GenerateReportRequest): void;
}>();

const sortByOptions: SortByOption[] = [
  {
    id: ReportSortByEnum.CLIENT_NAME,
    longName: 'Client Name',
  },
  {
    id: ReportSortByEnum.CLIENT_ID,
    longName: 'Client Id',
  },
  {
    id: ReportSortByEnum.REVENUE,
    longName: 'Revenue',
  },
  {
    id: ReportSortByEnum.LATEST_TRADING_DATE,
    longName: 'Trading Date',
  },
];

function generateReport(runReportType: ReportAccessMethodTypes) {
  if (!form.value.validate()) {
    return;
  }
  const requestPayload: GenerateReportRequest = new GenerateReportRequest(
    moment()
      .tz(useCompanyDetailsStore().userLocale)
      .startOf('day')
      .subtract(6, 'months')
      .valueOf(),
    returnEndOfDayFromEpoch(),
    useCompanyDetailsStore().userLocale,
    ReportFormatType.CSV,
    [],
    [],
    null,
    null,
    ReportType.CLIENT_DETAILS_SUMMARY,
    null,
    null,
    null,
    selectedSortByOption.value,
    includeRetired.value,
    null, // weightType
    null, // queryFieldName
    null, // calculateDistanceTravelledFromGps
    null, // groupByClient
    null, // invoicedJobs
    null, // groupsOf
    null, // totalNumberOfGroups
    null, // isDispatchAddress
    null, // separateTollCharges
    null, // excludeReturnToFirstLeg
    null, // jobId
    null, // includeImages
    null, // includeDriverPay
    null, // includeClientCharge
    null, // includeEventTimes
    runReportType,
  );
  emit('update:isLoading', true);
  emit('generateReport', requestPayload);
}

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeOrBranchManagerRole();
}
</script>
