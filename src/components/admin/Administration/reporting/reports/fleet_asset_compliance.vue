<template>
  <section class="daily-margin-report">
    <v-form ref="form">
      <v-layout>
        <v-flex md12>
          <v-select
            v-model="selectedFormatType"
            :items="
              Object.keys(ReportFormatType).map((e) => ReportFormatType[e])
            "
            label="Format Type"
            hint="The report file type."
            persistent-hint
            color="primary"
            solo
            flat
            class="v-solo-custom"
          >
          </v-select>
        </v-flex>
      </v-layout>
    </v-form>
    <div class="action-bar">
      <v-layout>
        <v-flex md12>
          <v-layout justify-end align-center>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.DOWNLOAD,
                )
              "
              depressed
              class="download"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.DOWNLOAD)"
            >
              <v-icon class="pr-2" size="20">downloading</v-icon>
              Download Report
            </v-btn>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.EMAIL,
                )
              "
              depressed
              class="email"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.EMAIL)"
            >
              <v-icon class="pr-2" size="20">forward_to_inbox</v-icon>
              Email Report
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
  </section>
</template>

<script setup lang="ts">
import { hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import GenerateReportRequest from '@/interface-models/Reporting/GenerateReportRequest';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { ReportFormatType } from '@/interface-models/Reporting/ReportFormatType';
import { ReportType } from '@/interface-models/Reporting/ReportType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { ref, Ref } from 'vue';

const props = withDefaults(
  defineProps<{
    isLoading?: boolean;
  }>(),
  {
    isLoading: false,
  },
);

const selectedFormatType: Ref<ReportFormatType> = ref(ReportFormatType.PDF);
const form = ref<any>(null);

const company = useCompanyDetailsStore().companyDetails;
const reportsSettings = company?.divisions?.[0]?.customConfig?.reports;

const emit = defineEmits<{
  (event: 'update:isLoading', value: boolean): void;
  (event: 'generateReport', payload: GenerateReportRequest): void;
}>();

// send request for the Fleet Asset Compliance Report
function generateReport(runReportType: ReportAccessMethodTypes) {
  if (!form.value.validate()) {
    return;
  }

  const requestPayload: GenerateReportRequest = new GenerateReportRequest(
    0,
    0,
    useCompanyDetailsStore().userLocale,
    selectedFormatType.value,
    [],
    [],
    null,
    null,
    ReportType.FLEET_ASSET_COMPLIANCE,
    null, // userId
    null, // durationInMillisecond
    null, // groupByDay
    null, // sortBy
    null, // includeRetired
    null, // weightType
    null, // queryFieldName
    null, // calculateDistanceTravelledFromGps
    null, // groupByClient
    null, // invoicedJobs
    null, // groupsOf
    null, // totalNumberOfGroups
    null, // isDispatchAddress
    null, // separateTollCharges
    null, // excludeReturnToFirstLeg
    null, // jobId
    null, // includeImages
    null, // includeDriverPay
    null, // includeClientCharge
    null, // includeEventTimes
    runReportType,
  );

  emit('update:isLoading', true);
  emit('generateReport', requestPayload);
}

// This sets the permissions of who can access this report
function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole();
}
</script>
