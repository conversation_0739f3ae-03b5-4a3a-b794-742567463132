<template>
  <section>
    <v-form ref="form" novalidate="false" autocomplete="off">
      <v-layout wrap>
        <v-flex md12>
          <SelectEntity
            :entityTypes="[EntityType.CLIENT]"
            :id.sync="clientId"
            :rules="[validate.required]"
          />
        </v-flex>
        <v-flex md6 class="pr">
          <DatePickerBasic
            @setEpoch="setStartDate"
            :hideIcon="true"
            :isRequired="true"
            :validate="validate"
            :labelName="'Start Date'"
            :soloInput="true"
            :epochTime="startDate"
          >
          </DatePickerBasic>
        </v-flex>
        <v-flex md6 class="pl">
          <DatePickerBasic
            @setEpoch="setEndDate"
            :hideIcon="true"
            :isRequired="true"
            :validate="validate"
            :labelName="'End Date'"
            :soloInput="true"
            :epochTime="endDate"
          >
          </DatePickerBasic>
        </v-flex>
        <v-flex md6 class="pr"
          ><v-text-field
            class="v-solo-custom"
            flat
            solo
            type="number"
            v-model.number="delayDurationInMinutes"
            label="Delay Duration (mins)"
            persistent-hint
            :rules="[validate.required]"
            hint="Delay Duration (mins). Only show legs where the delay exceeds this duration."
          ></v-text-field
        ></v-flex>

        <v-flex md6 class="pl">
          <v-checkbox
            label="Dispatch Locations Only"
            v-model="isDispatchAddress"
            persistent-hint
            :hint="checkboxHint"
          ></v-checkbox>
        </v-flex>
      </v-layout>
      <div class="action-bar">
        <v-layout>
          <v-flex md12>
            <v-layout justify-end align-center>
              <v-btn
                v-if="
                  reportsSettings?.allowedAccessMethods?.includes(
                    ReportAccessMethodTypes.DOWNLOAD,
                  )
                "
                depressed
                class="download"
                small
                :loading="isLoading"
                :disabled="isLoading || !isAuthorised()"
                @click="generateReport(ReportAccessMethodTypes.DOWNLOAD)"
              >
                <v-icon class="pr-2" size="20">downloading</v-icon>
                Download Report
              </v-btn>
              <v-btn
                v-if="
                  reportsSettings?.allowedAccessMethods?.includes(
                    ReportAccessMethodTypes.EMAIL,
                  )
                "
                depressed
                class="email"
                small
                :loading="isLoading"
                :disabled="isLoading || !isAuthorised()"
                @click="generateReport(ReportAccessMethodTypes.EMAIL)"
              >
                <v-icon class="pr-2" size="20">forward_to_inbox</v-icon>
                Email Report
              </v-btn>
            </v-layout>
          </v-flex>
        </v-layout>
      </div>
    </v-form>
  </section>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import SelectEntity from '@/components/common/inputs/select_entity/index.vue';
import {
  returnEndOfDayFromEpoch,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import GenerateReportRequest from '@/interface-models/Reporting/GenerateReportRequest';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { ReportFormatType } from '@/interface-models/Reporting/ReportFormatType';
import { ReportType } from '@/interface-models/Reporting/ReportType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import moment from 'moment-timezone';
import { computed, ComputedRef, ref, Ref, WritableComputedRef } from 'vue';

const validate = validationRules;

const props = withDefaults(
  defineProps<{
    isLoading?: boolean;
  }>(),
  {
    isLoading: false,
  },
);

const startDate: Ref<number> = ref(returnStartOfDayFromEpoch());
const endDate: Ref<number> = ref(returnEndOfDayFromEpoch());
const form: Ref<any> = ref(null);
const clientId: Ref<string> = ref('');
const isDispatchAddress: Ref<boolean> = ref(true);
const delayDurationInMilli: Ref<number> = ref(45 * 60 * 1000);

const company = useCompanyDetailsStore().companyDetails;
const reportsSettings = company?.divisions?.[0]?.customConfig?.reports;

// Handle response from date picker component
const setStartDate = (epoch: number | null): void => {
  if (epoch !== null) {
    startDate.value = returnStartOfDayFromEpoch(epoch);
  }
};
const setEndDate = (epoch: number | null): void => {
  if (epoch !== null) {
    endDate.value = returnEndOfDayFromEpoch(epoch);
  }
};

const delayDurationInMinutes: WritableComputedRef<number> = computed({
  get(): number {
    return moment.duration(delayDurationInMilli.value).asMinutes();
  },
  set(duration: number): void {
    delayDurationInMilli.value = moment
      .duration(duration, 'minutes')
      .asMilliseconds();
  },
});

const emit = defineEmits<{
  (event: 'update:isLoading', value: boolean): void;
  (event: 'generateReport', payload: GenerateReportRequest): void;
}>();

const checkboxHint: ComputedRef<string> = computed(() => {
  let message = 'Only show legs that are dispatch locations';
  if (isDispatchAddress.value) {
    message +=
      '. Note: If the client has no default dispatch address, this report will be blank.';
  }
  return message;
});

function generateReport(runReportType: ReportAccessMethodTypes) {
  if (!form.value.validate() || !clientId.value) {
    showNotification('Please ensure all required fields are completed.', {
      type: HealthLevel.ERROR,
      title: 'Yard Delay Report',
    });
    return;
  }
  const requestPayload: GenerateReportRequest = {
    clientIds: [clientId.value],
    startDate: startDate.value,
    endDate: endDate.value,
    timezone: useCompanyDetailsStore().userLocale,
    reportFormatType: ReportFormatType.CSV,
    statusList: [],
    includeFuel: null,
    includeEquipmentHire: null,
    reportType: ReportType.YARD_DELAY,
    userId: null,
    durationInMillisecond: delayDurationInMilli.value,
    groupByDay: null,
    sortBy: null,
    includeRetired: null,
    weightType: null,
    queryFieldName: null,
    calculateDistanceTravelledFromGps: null,
    groupByClient: null,
    invoicedJobs: null,
    groupsOf: null,
    totalNumberOfGroups: null,
    isDispatchAddress: isDispatchAddress.value,
    separateTollCharges: null,
    excludeReturnToFirstLeg: null,
    jobId: null,
    includeImages: null,
    includeDriverPay: null,
    includeClientCharge: null,
    includeEventTimes: null,
    accessType: runReportType,
  };
  emit('update:isLoading', true);
  emit('generateReport', requestPayload);
}

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole();
}
</script>
