<template>
  <section>
    <v-form ref="form">
      <v-layout wrap>
        <v-flex md6 class="pr">
          <DatePickerBasic
            @setEpoch="setStartDate"
            :hideIcon="true"
            :isRequired="true"
            :validate="validate"
            :labelName="'Budget Start Date'"
            :soloInput="true"
            :epochTime="startDate"
            :mondaysOnly="true"
          >
          </DatePickerBasic>
        </v-flex>

        <v-flex md6 class="pl">
          <v-autocomplete
            solo
            flat
            class="v-solo-custom"
            v-model="selectedUserId"
            :items="userSelectList"
            item-value="_id"
            item-text="fullName"
            color="orange"
            label="User Select"
            clearable
            :rules="[validate.required]"
          >
            <template v-slot:selection="data">
              <span>{{ data.item.firstName }} {{ data.item.lastName }}</span>
            </template>
            <template v-slot:items="data">
              <span>{{ data.item.firstName }} {{ data.item.lastName }}</span>
            </template>
          </v-autocomplete>
        </v-flex>
      </v-layout>
    </v-form>
    <div class="action-bar">
      <v-layout>
        <v-flex md12>
          <v-layout justify-end align-center>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.DOWNLOAD,
                )
              "
              depressed
              class="download"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.DOWNLOAD)"
            >
              <v-icon class="pr-2" size="20">downloading</v-icon>
              Download Report
            </v-btn>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.EMAIL,
                )
              "
              depressed
              class="email"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.EMAIL)"
            >
              <v-icon class="pr-2" size="20">forward_to_inbox</v-icon>
              Email Report
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
  </section>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import {
  returnEndOfDayFromEpoch,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import GenerateReportRequest from '@/interface-models/Reporting/GenerateReportRequest';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { ReportFormatType } from '@/interface-models/Reporting/ReportFormatType';
import { ReportType } from '@/interface-models/Reporting/ReportType';
import {
  CompanyUserDetails,
  CompanyUserWithAuthDetails,
} from '@/interface-models/User/CompanyUserDetails';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useUserManagementStore } from '@/store/modules/UserManagementStore';
import { computed, ComputedRef, onMounted, ref, Ref } from 'vue';

const validate = validationRules;

const props = withDefaults(
  defineProps<{
    isLoading?: boolean;
  }>(),
  {
    isLoading: false,
  },
);

const startDate: Ref<number> = ref(returnStartOfDayFromEpoch());
const endDate: Ref<number> = ref(returnEndOfDayFromEpoch());
const form: Ref<any> = ref(null);
const userManagementStore = useUserManagementStore();
const selectedUserId: Ref<string> = ref('');

const company = useCompanyDetailsStore().companyDetails;
const reportsSettings = company?.divisions?.[0]?.customConfig?.reports;

// Handle response from date picker component
const setStartDate = (epoch: number | null): void => {
  if (epoch !== null) {
    startDate.value = returnStartOfDayFromEpoch(epoch);
  }
};

const emit = defineEmits<{
  (event: 'update:isLoading', value: boolean): void;
  (event: 'generateReport', payload: GenerateReportRequest): void;
}>();

function generateReport(runReportType: ReportAccessMethodTypes) {
  if (!form.value.validate() || startDate.value > endDate.value) {
    return;
  }
  const requestPayload: GenerateReportRequest = new GenerateReportRequest(
    startDate.value,
    undefined,
    useCompanyDetailsStore().userLocale,
    ReportFormatType.CSV,
    [],
    [],
    null,
    null,
    ReportType.SALES_BY_SALES_PERSON,
    selectedUserId.value,
    null, // durationInMillisecond
    null, // groupByDay
    null, // sortBy
    null, // includeRetired
    null, // weightType
    null, // queryFieldName
    null, // calculateDistanceTravelledFromGps
    null, // groupByClient
    null, // invoicedJobs
    null, // groupsOf
    null, // totalNumberOfGroups
    null, // isDispatchAddress
    null, // separateTollCharges
    null, // excludeReturnToFirstLeg
    null, // jobId
    null, // includeImages
    null, // includeDriverPay
    null, // includeClientCharge
    null, // includeEventTimes
    runReportType,
  );
  emit('update:isLoading', true);
  emit('generateReport', requestPayload);
}

const userSelectList: ComputedRef<CompanyUserDetails[]> = computed(() => {
  const list = userManagementStore.companyUserWithAuthDetailsList;
  if (!list) {
    return [];
  }
  return list
    .map((x: CompanyUserWithAuthDetails) => ({
      ...x.companyUser,
      fullName: `${x.companyUser.firstName} ${x.companyUser.lastName}`,
    }))
    .sort((a: CompanyUserDetails, b: CompanyUserDetails) => {
      if (a.firstName < b.firstName) {
        return -1;
      }
      if (a.firstName > b.firstName) {
        return 1;
      }
      return 0;
    });
});

onMounted(() => {
  userManagementStore.getCompanyUserWithAuthDetailsList();
});

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole();
}
</script>
