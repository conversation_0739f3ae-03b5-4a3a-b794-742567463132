<template>
  <section>
    <v-form ref="form">
      <v-layout wrap>
        <v-select
          v-model="selectedFormatType"
          :items="Object.keys(ReportFormatType).map((e) => ReportFormatType[e])"
          label="Format Type"
          hint="The report file type."
          persistent-hint
          color="primary"
          solo
          flat
          class="v-solo-custom"
        >
        </v-select>
        <v-flex md12>
          <SelectEntity
            :entityTypes="[EntityType.CLIENT]"
            :id.sync="clientId"
            :rules="[validate.required]"
          />
        </v-flex>
        <v-flex md6 class="pr">
          <DatePickerBasic
            @setEpoch="setStartDate"
            :hideIcon="true"
            :isRequired="true"
            :validate="validate"
            :labelName="'Start Date'"
            :boxInput="false"
            :soloInput="true"
            :epochTime="startDate"
          >
          </DatePickerBasic>
        </v-flex>
        <v-flex md6 class="pl">
          <DatePickerBasic
            @setEpoch="setEndDate"
            :hideIcon="true"
            :validate="validate"
            :labelName="'End Date'"
            :boxInput="false"
            :soloInput="true"
            :isRequired="true"
            :epochTime="endDate"
          >
          </DatePickerBasic>
        </v-flex>

        <v-flex md12>
          <v-checkbox
            class="pr"
            v-model="separateTollCharges"
            :label="'Print Toll Charges Individually'"
            hint="Whether the sum of all toll charges will be printed on a single row or on separate individual rows."
            persistent-hint
          >
          </v-checkbox>
        </v-flex>
      </v-layout>
    </v-form>
    <div class="action-bar">
      <v-layout>
        <v-flex md12>
          <v-layout justify-end align-center>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.DOWNLOAD,
                )
              "
              depressed
              class="download"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.DOWNLOAD)"
            >
              <v-icon class="pr-2" size="20">downloading</v-icon>
              Download Report
            </v-btn>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.EMAIL,
                )
              "
              depressed
              class="email"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.EMAIL)"
            >
              <v-icon class="pr-2" size="20">forward_to_inbox</v-icon>
              Email Report
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
  </section>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import SelectEntity from '@/components/common/inputs/select_entity/index.vue';
import {
  returnEndOfDayFromEpoch,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import GenerateReportRequest from '@/interface-models/Reporting/GenerateReportRequest';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { ReportFormatType } from '@/interface-models/Reporting/ReportFormatType';
import { ReportType } from '@/interface-models/Reporting/ReportType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { ref, Ref } from 'vue';

const validate = validationRules;

const props = withDefaults(
  defineProps<{
    isLoading?: boolean;
  }>(),
  {
    isLoading: false,
  },
);

const clientId: Ref<string> = ref('');
const form = ref<any>(null);

const selectedFormatType: Ref<ReportFormatType> = ref(ReportFormatType.PDF);
const separateTollCharges: Ref<boolean> = ref(false);
const startDate: Ref<number> = ref(returnStartOfDayFromEpoch());
const endDate: Ref<number> = ref(returnEndOfDayFromEpoch());

const company = useCompanyDetailsStore().companyDetails;
const reportsSettings = company?.divisions?.[0]?.customConfig?.reports;

// Handle response from date picker component
const setStartDate = (epoch: number | null): void => {
  if (epoch !== null) {
    startDate.value = returnStartOfDayFromEpoch(epoch);
  }
};
const setEndDate = (epoch: number | null): void => {
  if (epoch !== null) {
    endDate.value = returnEndOfDayFromEpoch(epoch);
  }
};

const emit = defineEmits<{
  (event: 'update:isLoading', value: boolean): void;
  (event: 'generateReport', payload: GenerateReportRequest): void;
}>();

function generateReport(runReportType: ReportAccessMethodTypes) {
  if (!form.value.validate()) {
    showNotification('Please ensure all required fields are completed.', {
      type: HealthLevel.ERROR,
      title: 'Daily Jobs Report',
    });
    return;
  }
  const requestPayload: GenerateReportRequest = {
    clientIds: [clientId.value],
    startDate: startDate.value,
    endDate: endDate.value,
    timezone: useCompanyDetailsStore().userLocale,
    reportFormatType: selectedFormatType.value,
    statusList: [],
    includeFuel: null,
    includeEquipmentHire: null,
    reportType: ReportType.DAILY_JOBS,
    userId: null,
    durationInMillisecond: null,
    groupByDay: null,
    sortBy: null,
    includeRetired: false,
    weightType: null,
    queryFieldName: null,
    calculateDistanceTravelledFromGps: null,
    groupByClient: null,
    invoicedJobs: null,
    groupsOf: null,
    totalNumberOfGroups: null,
    isDispatchAddress: null,
    separateTollCharges: separateTollCharges.value,
    excludeReturnToFirstLeg: null,
    jobId: null,
    includeImages: null,
    includeDriverPay: null,
    includeClientCharge: null,
    includeEventTimes: null,
    accessType: runReportType,
  };
  emit('update:isLoading', true);
  emit('generateReport', requestPayload);
}

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole();
}
</script>
