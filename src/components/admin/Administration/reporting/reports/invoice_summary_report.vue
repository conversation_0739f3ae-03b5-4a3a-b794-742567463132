<template>
  <section>
    <v-form ref="form">
      <v-layout wrap>
        <v-flex md12>
          <v-select
            v-model="selectedFormatType"
            :items="
              Object.keys(ReportFormatType).map((e) => ReportFormatType[e])
            "
            label="Format Type"
            hint="The report file type."
            persistent-hint
            color="primary"
            solo
            flat
            class="v-solo-custom"
          >
          </v-select>
        </v-flex>
        <v-flex md4 class="pr">
          <v-select
            :items="reportQueryFieldTypes"
            solo
            flat
            class="v-solo-custom"
            v-model="reportQueryField"
            item-value="id"
            item-text="value"
            label="Search By"
            persistent-hint
            hint="The date type."
          />
        </v-flex>
        <v-flex md4 class="px">
          <DatePickerBasic
            @setEpoch="setStartDate"
            :hideIcon="true"
            :validate="validate"
            :labelName="'Start Date.'"
            :soloInput="true"
            :isRequired="true"
            :epochTime="startRunDate"
          >
          </DatePickerBasic>
        </v-flex>
        <v-flex md4 class="pl">
          <DatePickerBasic
            @setEpoch="setEndDate"
            :hideIcon="true"
            :validate="validate"
            :labelName="'End Date.'"
            :soloInput="true"
            :isRequired="true"
            :epochTime="endRunDate"
          >
          </DatePickerBasic>
        </v-flex>
      </v-layout>
    </v-form>
    <div class="action-bar">
      <v-layout>
        <v-flex md12>
          <v-layout justify-end align-center>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.DOWNLOAD,
                )
              "
              depressed
              class="download"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.DOWNLOAD)"
            >
              <v-icon class="pr-2" size="20">downloading</v-icon>
              Download Report
            </v-btn>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.EMAIL,
                )
              "
              depressed
              class="email"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.EMAIL)"
            >
              <v-icon class="pr-2" size="20">forward_to_inbox</v-icon>
              Email Report
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
  </section>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import {
  returnEndOfDayFromEpoch,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import GenerateReportRequest from '@/interface-models/Reporting/GenerateReportRequest';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { ReportFormatType } from '@/interface-models/Reporting/ReportFormatType';
import { ReportType } from '@/interface-models/Reporting/ReportType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { computed, ref, Ref } from 'vue';

const validate = validationRules;

const props = withDefaults(
  defineProps<{
    isLoading?: boolean;
  }>(),
  {
    isLoading: false,
  },
);

const reportQueryField: Ref<string> = ref('dates.weekEndingDate');
const form = ref<any>(null);
const selectedFormatType: Ref<ReportFormatType> = ref(ReportFormatType.PDF);

const startRunDate: Ref<number> = ref(returnStartOfDayFromEpoch());
const endRunDate: Ref<number> = ref(returnEndOfDayFromEpoch());

const company = useCompanyDetailsStore().companyDetails;
const reportsSettings = company?.divisions?.[0]?.customConfig?.reports;

// Handle response from date picker component
const setStartDate = (epoch: number | null): void => {
  if (epoch !== null) {
    startRunDate.value = returnStartOfDayFromEpoch(epoch);
  }
};
const setEndDate = (epoch: number | null): void => {
  if (epoch !== null) {
    endRunDate.value = returnEndOfDayFromEpoch(epoch);
  }
};

const reportQueryFieldTypes = computed(() => [
  { id: 'dates.weekEndingDate', value: 'Week Ending Date' },
  { id: 'dates.dueDate', value: 'Due Date' },
  { id: 'dates.emailedDate', value: 'Date Sent' },
]);

const emit = defineEmits<{
  (event: 'update:isLoading', value: boolean): void;
  (event: 'generateReport', payload: GenerateReportRequest): void;
}>();

function generateReport(runReportType: ReportAccessMethodTypes) {
  if (!form.value.validate() || startRunDate.value > endRunDate.value) {
    return;
  }
  const requestPayload: GenerateReportRequest = new GenerateReportRequest(
    returnStartOfDayFromEpoch(startRunDate.value),
    returnEndOfDayFromEpoch(endRunDate.value),
    useCompanyDetailsStore().userLocale,
    selectedFormatType.value,
    [],
    [],
    null,
    null,
    ReportType.INVOICE_SUMMARY,
    null,
    null,
    null,
    null,
    null,
    null,
    reportQueryField.value,
    null, // calculateDistanceTravelledFromGps
    null, // groupByClient
    null, // invoicedJobs
    null, // groupsOf
    null, // totalNumberOfGroups
    null, // isDispatchAddress
    null, // separateTollCharges
    null, // excludeReturnToFirstLeg
    null, // jobId
    null, // includeImages
    null, // includeDriverPay
    null, // includeClientCharge
    null, // includeEventTimes
    runReportType,
  );
  emit('update:isLoading', true);
  emit('generateReport', requestPayload);
}

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole();
}
</script>
