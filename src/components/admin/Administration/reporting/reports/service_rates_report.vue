<template>
  <section>
    <v-form ref="form">
      <v-layout wrap>
        <v-flex md6 class="pr">
          <DatePickerBasic
            @setEpoch="setStartDate"
            :hideIcon="true"
            :isRequired="true"
            :validate="validate"
            :labelName="'Report Date'"
            :soloInput="true"
            :epochTime="startDate"
            :showHint="true"
            :hintText="'The date that will be utilised when finding active service rates.'"
          >
          </DatePickerBasic>
        </v-flex>
        <v-flex md6 class="pl">
          <v-select
            label="Sort By"
            v-model="selectedSortByOption"
            :items="sortByOptions"
            item-value="id"
            item-text="longName"
            solo
            flat
            persistent-hint
            hint="Sort By"
            class="v-solo-custom"
          ></v-select>
        </v-flex>

        <v-flex md6 class="pr">
          <v-select
            label="Excluded Trading options"
            v-model="notTradedInDuration"
            :items="notTradedInOptions"
            item-value="value"
            item-text="key"
            solo
            flat
            class="v-solo-custom"
            persistent-hint
            hint="Exclude clients that have a known trading date within the selected number of days."
          ></v-select>
        </v-flex>
        <v-flex md6 class="pl">
          <v-checkbox
            v-model="includeRetired"
            label="Include Retired Clients"
          ></v-checkbox>
        </v-flex>
      </v-layout>
    </v-form>
    <div class="action-bar">
      <v-layout>
        <v-flex md12>
          <v-layout justify-end align-center>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.DOWNLOAD,
                )
              "
              depressed
              class="download"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.DOWNLOAD)"
            >
              <v-icon class="pr-2" size="20">downloading</v-icon>
              Download Report
            </v-btn>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.EMAIL,
                )
              "
              depressed
              class="email"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.EMAIL)"
            >
              <v-icon class="pr-2" size="20">forward_to_inbox</v-icon>
              Email Report
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
  </section>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import {
  returnEndOfDayFromEpoch,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { KeyValue } from '@/interface-models/Generic/KeyValue/KeyValue';
import GenerateReportRequest from '@/interface-models/Reporting/GenerateReportRequest';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { ReportFormatType } from '@/interface-models/Reporting/ReportFormatType';
import { ReportSortByEnum } from '@/interface-models/Reporting/ReportSortByEnum';
import { ReportType } from '@/interface-models/Reporting/ReportType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { ref, Ref } from 'vue';

const validate = validationRules;

interface SortByOption {
  id: ReportSortByEnum;
  longName: string;
}

const props = withDefaults(
  defineProps<{
    isLoading?: boolean;
  }>(),
  {
    isLoading: false,
  },
);

const startDate: Ref<number> = ref(returnStartOfDayFromEpoch());
const endDate: Ref<number> = ref(returnEndOfDayFromEpoch());
const form: Ref<any> = ref(null);

const company = useCompanyDetailsStore().companyDetails;
const reportsSettings = company?.divisions?.[0]?.customConfig?.reports;

const includeRetired: Ref<boolean> = ref(false);
const notTradedInDuration: Ref<number> = ref(0);

const selectedSortByOption: Ref<ReportSortByEnum> = ref(
  ReportSortByEnum.CLIENT_NAME,
);

const sortByOptions: SortByOption[] = [
  {
    id: ReportSortByEnum.CLIENT_NAME,
    longName: 'Client Name',
  },
  {
    id: ReportSortByEnum.CLIENT_ID,
    longName: 'Client Id',
  },
  {
    id: ReportSortByEnum.CREDIT_STATUS,
    longName: 'Credit Status',
  },
  {
    id: ReportSortByEnum.LATEST_TRADING_DATE,
    longName: 'Latest Trading Date',
  },
];

const notTradedInOptions: KeyValue[] = [
  { key: 'No Exclusions', value: 0 },
  { key: '30 Days', value: 86400000 * 30 },
  { key: '60 Days', value: 86400000 * 60 },
  { key: '90 Days', value: 86400000 * 90 },
  { key: '120 Days', value: 86400000 * 120 },
];

// Handle response from date picker component
const setStartDate = (epoch: number | null): void => {
  if (epoch !== null) {
    startDate.value = returnStartOfDayFromEpoch(epoch);
  }
};

const emit = defineEmits<{
  (event: 'update:isLoading', value: boolean): void;
  (event: 'generateReport', payload: GenerateReportRequest): void;
}>();

function generateReport(runReportType: ReportAccessMethodTypes) {
  if (!form.value.validate() || startDate.value > endDate.value) {
    return;
  }

  const requestPayload: GenerateReportRequest = new GenerateReportRequest(
    startDate.value,
    endDate.value,
    useCompanyDetailsStore().userLocale,
    ReportFormatType.CSV,
    [],
    [],
    null,
    null,
    ReportType.SERVICE_RATES,
    null,
    notTradedInDuration.value,
    null,
    selectedSortByOption.value,
    includeRetired.value,
    null, // weightType
    null, // queryFieldName
    null, // calculateDistanceTravelledFromGps
    null, // groupByClient
    null, // invoicedJobs
    null, // groupsOf
    null, // totalNumberOfGroups
    null, // isDispatchAddress
    null, // separateTollCharges
    null, // excludeReturnToFirstLeg
    null, // jobId
    null, // includeImages
    null, // includeDriverPay
    null, // includeClientCharge
    null, // includeEventTimes
    runReportType,
  );

  emit('update:isLoading', true);
  emit('generateReport', requestPayload);
}

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole();
}
</script>
