<template>
  <section class="daily-margin-report">
    <v-form ref="form">
      <v-layout wrap>
        <v-flex md12>
          <SelectEntity
            :entityTypes="[EntityType.CLIENT]"
            :id.sync="clientId"
            :required="true"
            :rules="[validate.required]"
          />
        </v-flex>
        <v-flex md6 class="pr">
          <DatePickerBasic
            @setEpoch="setStartDate"
            :hideIcon="true"
            :isRequired="true"
            :validate="validate"
            :labelName="'Start Date'"
            :soloInput="true"
            :epochTime="startDate"
          >
          </DatePickerBasic>
        </v-flex>
        <v-flex md6 class="pl">
          <DatePickerBasic
            @setEpoch="setEndDate"
            :hideIcon="true"
            :isRequired="true"
            :validate="validate"
            :labelName="'End Date'"
            :soloInput="true"
            :epochTime="endDate"
          >
          </DatePickerBasic>
        </v-flex>
        <v-flex md6 class="pl-2 mb-3">
          <v-checkbox
            label="Calculate Actual Distance Travelled"
            v-model="calculateDistanceTravelledFromGps"
            persistent-hint
            hint="Download duration for this report will increase if calculation is included."
          ></v-checkbox>
        </v-flex>
        <v-flex md6 class="pl-2 mb-3">
          <v-checkbox
            label="Exclude Return To First Leg"
            v-model="excludeReturnToFirstLeg"
            persistent-hint
            hint="The last leg will be excluded from this report if it is equal to the first leg."
          ></v-checkbox>
        </v-flex>
      </v-layout>
    </v-form>
    <div class="action-bar">
      <v-layout>
        <v-flex md12>
          <v-layout justify-end align-center>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.DOWNLOAD,
                )
              "
              depressed
              class="download"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.DOWNLOAD)"
            >
              <v-icon class="pr-2" size="20">downloading</v-icon>
              Download Report
            </v-btn>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.EMAIL,
                )
              "
              depressed
              class="email"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.EMAIL)"
            >
              <v-icon class="pr-2" size="20">forward_to_inbox</v-icon>
              Email Report
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
  </section>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import SelectEntity from '@/components/common/inputs/select_entity/index.vue';
import {
  returnEndOfDayFromEpoch,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import GenerateReportRequest from '@/interface-models/Reporting/GenerateReportRequest';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { ReportFormatType } from '@/interface-models/Reporting/ReportFormatType';
import { ReportType } from '@/interface-models/Reporting/ReportType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { ref, Ref } from 'vue';

const validate = validationRules;

const props = withDefaults(
  defineProps<{
    isLoading?: boolean;
  }>(),
  {
    isLoading: false,
  },
);

const startDate: Ref<number> = ref(returnStartOfDayFromEpoch());
const endDate: Ref<number> = ref(returnEndOfDayFromEpoch());

const form = ref<any>(null);
const clientId: Ref<string> = ref('');
const calculateDistanceTravelledFromGps: Ref<boolean> = ref(true);
const excludeReturnToFirstLeg: Ref<boolean> = ref(true);

const company = useCompanyDetailsStore().companyDetails;
const reportsSettings = company?.divisions?.[0]?.customConfig?.reports;

// Handle response from date picker component
const setStartDate = (epoch: number | null): void => {
  if (epoch !== null) {
    startDate.value = returnStartOfDayFromEpoch(epoch);
  }
};
const setEndDate = (epoch: number | null): void => {
  if (epoch !== null) {
    endDate.value = returnEndOfDayFromEpoch(epoch);
  }
};

const emit = defineEmits<{
  (event: 'update:isLoading', value: boolean): void;
  (event: 'generateReport', payload: GenerateReportRequest): void;
}>();

function generateReport(runReportType: ReportAccessMethodTypes) {
  if (
    !form.value.validate() ||
    !clientId.value ||
    startDate.value > endDate.value
  ) {
    showNotification('Please ensure all required fields are completed.', {
      type: HealthLevel.ERROR,
      title: 'Client KPI Daily Report',
    });
    return;
  }

  const requestPayload: GenerateReportRequest = {
    clientIds: [clientId.value],
    startDate: startDate.value,
    endDate: endDate.value,
    timezone: useCompanyDetailsStore().userLocale,
    reportFormatType: ReportFormatType.CSV,
    statusList: [],
    includeFuel: null,
    includeEquipmentHire: null,
    reportType: ReportType.CLIENT_KPI_DAILY,
    userId: null,
    durationInMillisecond: null,
    groupByDay: null,
    sortBy: null,
    includeRetired: null,
    weightType: null,
    queryFieldName: null,
    calculateDistanceTravelledFromGps: calculateDistanceTravelledFromGps.value,
    groupByClient: null,
    invoicedJobs: null,
    groupsOf: null,
    totalNumberOfGroups: null,
    isDispatchAddress: null,
    separateTollCharges: null,
    excludeReturnToFirstLeg: excludeReturnToFirstLeg.value,
    jobId: null,
    includeImages: null,
    includeDriverPay: null,
    includeClientCharge: null,
    includeEventTimes: null,
    accessType: runReportType,
  };
  emit('update:isLoading', true);
  emit('generateReport', requestPayload);
}

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole();
}
</script>
