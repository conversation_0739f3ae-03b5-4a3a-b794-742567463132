<template>
  <section>
    <v-form ref="form">
      <v-layout wrap>
        <v-flex md12>
          <SelectEntity
            :entityTypes="[EntityType.CLIENT]"
            :id.sync="clientId"
            :rules="[validate.required]"
          />
        </v-flex>
        <v-flex md6 class="pr">
          <DatePickerBasic
            @setEpoch="setStartDate"
            :hideIcon="true"
            :isRequired="true"
            :validate="validate"
            :labelName="'Start Date'"
            :soloInput="true"
            :epochTime="startDate"
          >
          </DatePickerBasic>
        </v-flex>
        <v-flex md6 class="pl">
          <DatePickerBasic
            @setEpoch="setEndDate"
            :hideIcon="true"
            :isRequired="true"
            :validate="validate"
            :labelName="'End Date'"
            :soloInput="true"
            :epochTime="endDate"
          >
          </DatePickerBasic>
        </v-flex>
        <v-flex md12>
          <v-select
            label="Weight Type Calculation"
            v-model="selectedWeightTypeOption"
            :items="weightTypeOptions"
            item-value="id"
            hint="Please select how you would like weights to be calculated"
            persistent-hint
            class="v-solo-custom"
            item-text="longName"
            solo
            flat
          ></v-select>
        </v-flex>
        <v-flex md6 class="pl-2 mb-3">
          <v-checkbox
            label="Exclude Return To First Leg"
            v-model="excludeReturnToFirstLeg"
            persistent-hint
            hint="The last leg will be excluded from this report if it is equal to the first leg."
          ></v-checkbox>
        </v-flex>
        <v-flex md6 class="pl-2 mb-3">
          <v-checkbox
            label="Calculate Actual Distance Travelled"
            v-model="calculateDistanceTravelledFromGps"
            persistent-hint
            hint="Download duration for this report will increase if calculation is included."
          ></v-checkbox>
        </v-flex>
      </v-layout>
    </v-form>

    <div class="action-bar">
      <v-layout>
        <v-flex md12>
          <v-layout justify-end align-center>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.DOWNLOAD,
                )
              "
              depressed
              class="download"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.DOWNLOAD)"
            >
              <v-icon class="pr-2" size="20">downloading</v-icon>
              Download Report
            </v-btn>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.EMAIL,
                )
              "
              depressed
              class="email"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.EMAIL)"
            >
              <v-icon class="pr-2" size="20">forward_to_inbox</v-icon>
              Email Report
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
  </section>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import SelectEntity from '@/components/common/inputs/select_entity/index.vue';
import {
  returnEndOfDayFromEpoch,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import GenerateReportRequest from '@/interface-models/Reporting/GenerateReportRequest';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { ReportFormatType } from '@/interface-models/Reporting/ReportFormatType';
import { ReportType } from '@/interface-models/Reporting/ReportType';
import { WeightTypeEnum } from '@/interface-models/Reporting/WeightTypeEnum';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { Ref, ref } from 'vue';

const validate = validationRules;

interface WeightTypeSelectOption {
  id: WeightTypeEnum;
  longName: string;
}

const props = withDefaults(
  defineProps<{
    isLoading?: boolean;
  }>(),
  {
    isLoading: false,
  },
);

const form = ref<any>(null);
const startDate: Ref<number> = ref(returnStartOfDayFromEpoch());
const endDate: Ref<number> = ref(returnEndOfDayFromEpoch());
const clientId: Ref<string> = ref('');

const company = useCompanyDetailsStore().companyDetails;
const reportsSettings = company?.divisions?.[0]?.customConfig?.reports;

const selectedWeightTypeOption: Ref<WeightTypeEnum> = ref(
  WeightTypeEnum.PICKUP,
);
const excludeReturnToFirstLeg: Ref<boolean> = ref(true);
const calculateDistanceTravelledFromGps: Ref<boolean> = ref(true);

const weightTypeOptions: Ref<WeightTypeSelectOption[]> = ref([
  {
    id: WeightTypeEnum.PICKUP,
    longName: 'Pickup',
  },
  {
    id: WeightTypeEnum.DROPOFF,
    longName: 'Dropoff',
  },
  {
    id: WeightTypeEnum.MANIFEST,
    longName: 'Manifest',
  },
]);

// Handle response from date picker component
const setStartDate = (epoch: number | null): void => {
  if (epoch !== null) {
    startDate.value = returnStartOfDayFromEpoch(epoch);
  }
};
const setEndDate = (epoch: number | null): void => {
  if (epoch !== null) {
    endDate.value = returnEndOfDayFromEpoch(epoch);
  }
};

const emit = defineEmits<{
  (event: 'update:isLoading', value: boolean): void;
  (event: 'generateReport', payload: GenerateReportRequest): void;
}>();

function generateReport(runReportType: ReportAccessMethodTypes) {
  if (
    !form.value.validate() ||
    !clientId.value ||
    startDate.value > endDate.value
  ) {
    showNotification('Please ensure all required fields are completed.', {
      type: HealthLevel.ERROR,
      title: 'Cost Per Tonne Report',
    });
    return;
  }

  const requestPayload: GenerateReportRequest = new GenerateReportRequest(
    startDate.value,
    endDate.value,
    useCompanyDetailsStore().userLocale,
    ReportFormatType.CSV,
    [clientId.value],
    [],
    null,
    null,
    ReportType.COST_PER_TONNE,
    null,
    null,
    null,
    null,
    null,
    selectedWeightTypeOption.value,
    null,
    calculateDistanceTravelledFromGps.value,
    null,
    null,
    null,
    null,
    null,
    null,
    excludeReturnToFirstLeg.value,
    null, // jobId
    null, // includeImages
    null, // includeDriverPay
    null, // includeClientCharge
    null, // includeEventTimes
    runReportType,
  );

  emit('update:isLoading', true);
  emit('generateReport', requestPayload);
}

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole();
}
</script>
