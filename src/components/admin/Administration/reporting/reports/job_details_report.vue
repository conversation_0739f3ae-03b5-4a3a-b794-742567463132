<template>
  <v-form ref="form">
    <v-layout wrap>
      <v-flex md12 class="pr" v-if="!isDialog">
        <v-text-field
          solo
          :class="'v-solo-custom'"
          flat
          hint="Please Enter The Job ID"
          persistent-hint
          label="Please Enter The Job ID"
          :rules="[validate.required]"
          v-model="jobIdInput"
        >
        </v-text-field>
      </v-flex>
      <v-flex md6 class="pr">
        <v-checkbox v-model="includeImages" label="Include Images"></v-checkbox>
      </v-flex>

      <v-flex md6 class="pl">
        <v-checkbox
          v-model="includeDriverPay"
          label="Include Subcontractor Pay"
        ></v-checkbox>
      </v-flex>
      <v-flex md6 class="pr">
        <v-checkbox
          v-model="includeClientCharge"
          label="Include Client Charge"
        ></v-checkbox>
      </v-flex>
      <v-flex md6 class="pl">
        <v-checkbox
          v-model="includeEventTimes"
          label="Include Event Times"
        ></v-checkbox>
      </v-flex>

      <v-flex md12>
        <div :class="!isDialog ? 'action-bar' : 'dialog-action'">
          <v-layout>
            <v-flex md12>
              <v-layout justify-end align-center>
                <v-btn
                  v-if="
                    reportsSettings?.allowedAccessMethods?.includes(
                      ReportAccessMethodTypes.DOWNLOAD,
                    )
                  "
                  depressed
                  class="download"
                  small
                  :loading="isLoading"
                  :disabled="isLoading || !isAuthorised()"
                  @click="generateReport(ReportAccessMethodTypes.DOWNLOAD)"
                >
                  <v-icon class="pr-2" size="20">downloading</v-icon>
                  Download Report
                </v-btn>
                <v-btn
                  v-if="
                    reportsSettings?.allowedAccessMethods?.includes(
                      ReportAccessMethodTypes.EMAIL,
                    )
                  "
                  depressed
                  class="email"
                  small
                  :loading="isLoading"
                  :disabled="isLoading || !isAuthorised()"
                  @click="generateReport(ReportAccessMethodTypes.EMAIL)"
                >
                  <v-icon class="pr-2" size="20">forward_to_inbox</v-icon>
                  Email Report
                </v-btn>
              </v-layout>
            </v-flex>
          </v-layout>
        </div>
      </v-flex>
    </v-layout>
  </v-form>
</template>

<script setup lang="ts">
import { hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import GenerateReportRequest from '@/interface-models/Reporting/GenerateReportRequest';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { ReportFormatType } from '@/interface-models/Reporting/ReportFormatType';
import { ReportType } from '@/interface-models/Reporting/ReportType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { computed, ref, Ref } from 'vue';

const validate = validationRules;

const props = withDefaults(
  defineProps<{
    isLoading?: boolean;
    isDialog?: boolean;
    jobId?: number;
  }>(),
  {
    isLoading: false,
    isDialog: false,
    jobId: 0,
  },
);

const form = ref<any>(null);

const jobIdInput: Ref<string> = ref('');
const includeImages: Ref<boolean> = ref(true);
const includeDriverPay: Ref<boolean> = ref(true);
const includeClientCharge: Ref<boolean> = ref(true);
const includeEventTimes: Ref<boolean> = ref(true);

const company = useCompanyDetailsStore().companyDetails;
const reportsSettings = company?.divisions?.[0]?.customConfig?.reports;

const jobIdForReport = computed<string>(() => {
  return props.isDialog ? JSON.stringify(props.jobId) : jobIdInput.value;
});

const emit = defineEmits<{
  (event: 'update:isLoading', value: boolean): void;
  (event: 'generateReport', payload: GenerateReportRequest): void;
}>();

function generateReport(runReportType: ReportAccessMethodTypes) {
  if (!form.value.validate()) {
    return;
  }
  const requestPayload: GenerateReportRequest = new GenerateReportRequest(
    0,
    0,
    useCompanyDetailsStore().userLocale,
    ReportFormatType.PDF,
    [],
    [],
    null,
    null,
    ReportType.JOB_DETAILS,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    jobIdForReport.value,
    includeImages.value,
    includeDriverPay.value,
    includeClientCharge.value,
    includeEventTimes.value,
    runReportType,
  );

  emit('update:isLoading', true);
  emit('generateReport', requestPayload);
}

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole();
}
</script>

<style scoped lang="scss">
.dialog-action {
  border-top: 1px solid $border-color;
  .v-btn {
    min-width: 182px;
    min-height: 34px;
    margin: 16px;
    border-radius: $border-radius-sm !important;

    &:hover {
      cursor: pointer;
      transition: 0.3s;
      scale: 1.1;
    }

    &.email {
      border: 1px solid var(--primary-light) !important;
      background-color: var(--primary) !important;
    }

    &.download {
      border: 1px solid var(--accent) !important;
      background-color: var(--info) !important;
    }
  }
}
</style>
