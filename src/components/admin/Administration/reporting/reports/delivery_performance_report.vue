<template>
  <section>
    <v-form ref="form">
      <v-layout wrap>
        <v-flex md6 class="pr">
          <DatePickerBasic
            @setEpoch="setStartDate"
            :hideIcon="true"
            :isRequired="true"
            :validate="validate"
            :labelName="'Start Date'"
            :soloInput="true"
            :epochTime="startDate"
          >
          </DatePickerBasic>
        </v-flex>
        <v-flex md6 class="pl">
          <DatePickerBasic
            @setEpoch="setEndDate"
            :hideIcon="true"
            :isRequired="true"
            :validate="validate"
            :labelName="'End Date'"
            :soloInput="true"
            :epochTime="endDate"
          >
          </DatePickerBasic>
        </v-flex>
        <v-flex class="pr">
          <v-select
            solo
            flat
            class="v-solo-custom"
            label="Performance Duration"
            v-model="durationInMilliseconds"
            :items="performanceDurations"
            :hint="'Performance Duration'"
            persistent-hint
            item-value="value"
            item-text="key"
            color="blue"
          ></v-select>
        </v-flex>
        <v-flex md6 class="pl">
          <v-select
            solo
            flat
            :items="deliveryPerformanceReportTypes"
            label="Grouping Type"
            class="form-field-required v-solo-custom"
            item-text="key"
            item-value="value"
            v-model="selectedReportTypeId"
            :rules="[validate.required]"
            persistent-hint
            hint="Whether the report groups jobs or prints jobs individually."
          />
        </v-flex>
      </v-layout>
    </v-form>
    <div class="action-bar">
      <v-layout>
        <v-flex md12>
          <v-layout justify-end align-center>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.DOWNLOAD,
                )
              "
              depressed
              class="download"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.DOWNLOAD)"
            >
              <v-icon class="pr-2" size="20">downloading</v-icon>
              Download Report
            </v-btn>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.EMAIL,
                )
              "
              depressed
              class="email"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.EMAIL)"
            >
              <v-icon class="pr-2" size="20">forward_to_inbox</v-icon>
              Email Report
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
  </section>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import {
  returnEndOfDayFromEpoch,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { KeyValue } from '@/interface-models/Generic/KeyValue/KeyValue';
import GenerateReportRequest from '@/interface-models/Reporting/GenerateReportRequest';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { ReportFormatType } from '@/interface-models/Reporting/ReportFormatType';
import { ReportType } from '@/interface-models/Reporting/ReportType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { computed, ComputedRef, ref, Ref } from 'vue';

const validate = validationRules;

const props = withDefaults(
  defineProps<{
    isLoading?: boolean;
  }>(),
  {
    isLoading: false,
  },
);

const startDate: Ref<number> = ref(returnStartOfDayFromEpoch());
const endDate: Ref<number> = ref(returnEndOfDayFromEpoch());
const form = ref<any>(null);
const durationInMilliseconds: Ref<number> = ref(60 * 60 * 1000);
const selectedReportTypeId: Ref<number> = ref(1);

const company = useCompanyDetailsStore().companyDetails;
const reportsSettings = company?.divisions?.[0]?.customConfig?.reports;

// Handle response from date picker component
const setStartDate = (epoch: number | null): void => {
  if (epoch !== null) {
    startDate.value = returnStartOfDayFromEpoch(epoch);
  }
};
const setEndDate = (epoch: number | null): void => {
  if (epoch !== null) {
    endDate.value = returnEndOfDayFromEpoch(epoch);
  }
};

const deliveryPerformanceReportTypes: ComputedRef<KeyValue[]> = computed(() => [
  { key: 'No Grouping', value: 1 },
  { key: 'Group by Day', value: 2 },
  { key: 'Group by Client', value: 3 },
  { key: 'Group by Client and Day', value: 4 },
]);

const performanceDurations: ComputedRef<KeyValue[]> = computed(() => {
  const oneMinute = 60 * 1000;
  return [
    { key: '30 Minutes', value: 30 * oneMinute },
    { key: '60 Minutes', value: 60 * oneMinute },
    { key: '90 Minutes', value: 90 * oneMinute },
    { key: '120 Minutes', value: 120 * oneMinute },
  ];
});

const emit = defineEmits<{
  (event: 'update:isLoading', value: boolean): void;
  (event: 'generateReport', payload: GenerateReportRequest): void;
}>();

function generateReport(runReportType: ReportAccessMethodTypes) {
  if (!form.value.validate() || startDate.value > endDate.value) {
    return;
  }
  const groupByDay: boolean =
    selectedReportTypeId.value === 2 || selectedReportTypeId.value === 4;
  const groupByClient: boolean =
    selectedReportTypeId.value === 3 || selectedReportTypeId.value === 4;

  const requestPayload: GenerateReportRequest = new GenerateReportRequest(
    startDate.value,
    endDate.value,
    useCompanyDetailsStore().userLocale,
    ReportFormatType.CSV,
    [],
    [],
    null,
    null,
    ReportType.DELIVERY_PERFORMANCE,
    null,
    durationInMilliseconds.value,
    groupByDay,
    null,
    null,
    null,
    null,
    null,
    groupByClient,
    null, // invoicedJobs
    null, // groupsOf
    null, // totalNumberOfGroups
    null, // isDispatchAddress
    null, // separateTollCharges
    null, // excludeReturnToFirstLeg
    null, // jobId
    null, // includeImages
    null, // includeDriverPay
    null, // includeClientCharge
    null, // includeEventTimes
    runReportType,
  );
  emit('update:isLoading', true);
  emit('generateReport', requestPayload);
}

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole();
}
</script>
