<template>
  <section>
    <v-form ref="form">
      <v-layout wrap>
        <v-flex md6 class="pr">
          <DatePickerBasic
            @setEpoch="setStartDate"
            :hideIcon="true"
            :isRequired="true"
            :validate="validate"
            :labelName="'Start Date'"
            :soloInput="true"
            :epochTime="startDate"
          >
          </DatePickerBasic>
        </v-flex>
        <v-flex md6 class="pl">
          <DatePickerBasic
            @setEpoch="setEndDate"
            :hideIcon="true"
            :isRequired="true"
            :validate="validate"
            :labelName="'End Date'"
            :soloInput="true"
            :epochTime="endDate"
          >
          </DatePickerBasic>
        </v-flex>

        <v-flex md12 class="">
          <v-checkbox
            label="Group Days"
            v-model="groupDaysTogether"
            persistent-hint
            hint="Whether the report prints a sum of all days or separates days individually."
          />
        </v-flex>
      </v-layout>
    </v-form>
    <div class="action-bar">
      <v-layout>
        <v-flex md12>
          <v-layout justify-end align-center>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.DOWNLOAD,
                )
              "
              depressed
              class="download"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.DOWNLOAD)"
            >
              <v-icon class="pr-2" size="20">downloading</v-icon>
              Download Report
            </v-btn>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.EMAIL,
                )
              "
              depressed
              class="email"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.EMAIL)"
            >
              <v-icon class="pr-2" size="20">forward_to_inbox</v-icon>
              Email Report
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
  </section>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import {
  returnEndOfDayFromEpoch,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import GenerateReportRequest from '@/interface-models/Reporting/GenerateReportRequest';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { ReportFormatType } from '@/interface-models/Reporting/ReportFormatType';
import { ReportType } from '@/interface-models/Reporting/ReportType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { ref, Ref } from 'vue';

const validate = validationRules;

const props = withDefaults(
  defineProps<{
    isLoading?: boolean;
  }>(),
  {
    isLoading: false,
  },
);

const startDate: Ref<number> = ref(returnStartOfDayFromEpoch());
const endDate: Ref<number> = ref(returnEndOfDayFromEpoch());
const form: Ref<any> = ref(null);

const groupDaysTogether: Ref<boolean> = ref(true);

const company = useCompanyDetailsStore().companyDetails;
const reportsSettings = company?.divisions?.[0]?.customConfig?.reports;

// Handle response from date picker component
const setStartDate = (epoch: number | null): void => {
  if (epoch !== null) {
    startDate.value = returnStartOfDayFromEpoch(epoch);
  }
};
const setEndDate = (epoch: number | null): void => {
  if (epoch !== null) {
    endDate.value = returnEndOfDayFromEpoch(epoch);
  }
};

const emit = defineEmits<{
  (event: 'update:isLoading', value: boolean): void;
  (event: 'generateReport', payload: GenerateReportRequest): void;
}>();

function generateReport(runReportType: ReportAccessMethodTypes) {
  if (!form.value.validate() || startDate.value > endDate.value) {
    return;
  }
  const requestPayload: GenerateReportRequest = new GenerateReportRequest(
    startDate.value,
    endDate.value,
    useCompanyDetailsStore().userLocale,
    ReportFormatType.CSV,
    [],
    [],
    null,
    null,
    ReportType.OPERATOR_PERFORMANCE,
    null,
    null,
    !groupDaysTogether.value,
    null, // sortBy
    null, // includeRetired
    null, // weightType
    null, // queryFieldName
    null, // calculateDistanceTravelledFromGps
    null, // groupByClient
    null, // invoicedJobs
    null, // groupsOf
    null, // totalNumberOfGroups
    null, // isDispatchAddress
    null, // separateTollCharges
    null, // excludeReturnToFirstLeg
    null, // jobId
    null, // includeImages
    null, // includeDriverPay
    null, // includeClientCharge
    null, // includeEventTimes
    runReportType,
  );

  emit('update:isLoading', true);
  emit('generateReport', requestPayload);
}

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole();
}
</script>
