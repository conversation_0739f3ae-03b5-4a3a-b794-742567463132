<template>
  <v-dialog
    v-model="viewingEditDialog"
    width="50%"
    persistent
    content-class="v-dialog-custom"
  >
    <v-layout
      justify-space-between
      class="task-bar app-theme__center-content--header no-highlight"
    >
      <span
        >Edit Permanent Job Allocation -
        {{ currentEditedRecurringJob?.client?.clientName }}</span
      >
      <div
        class="app-theme__center-content--closebutton"
        @click="resetWorkingVariables()"
      >
        <v-icon class="app-theme__center-content--closebutton--icon"
          >fal fa-times</v-icon
        >
      </div>
    </v-layout>

    <v-layout
      class="app-theme__center-content--body"
      :key="currentEditedRecurringJob?._id"
      v-if="currentEditedRecurringJob"
    >
      <v-flex md12>
        <v-progress-linear
          indeterminate
          v-if="isAwaitingResponse"
          :top="true"
          :height="2"
          :background-opacity="0"
        ></v-progress-linear>
        <v-layout row wrap class="body-scrollable--75 body-min-height--75 pa-2">
          <v-flex md12 px-2 pb-3>
            <v-alert type="info" :value="true">
              You are editing a Permanent Job. Any changes you make will be
              applied when the next instance of the Job has been created.
            </v-alert>
          </v-flex>
          <v-flex md12>
            <RecurrenceVisualisation
              :recurringJobDetails="currentEditedRecurringJob.recurrenceDetails"
              :isNewRecurringJob="false"
            >
            </RecurrenceVisualisation>
          </v-flex>
          <v-flex md12 pt-2 px-2>
            <v-layout align-center justify-space-between>
              <h6>Allocation Details</h6>
              <span
                class="pb-2"
                v-if="
                  currentEditedRecurringJob.fleetAssetId ||
                  currentEditedRecurringJob.driverId
                "
              >
                <v-btn
                  small
                  flat
                  outline
                  class="v-btn-rounded"
                  color="accent"
                  @click="deallocateCurrentSelection"
                  ><v-icon left size="16">fal fa-times</v-icon>Deallocate</v-btn
                >
              </span>
            </v-layout>
          </v-flex>
          <v-flex
            md12
            v-if="
              rateToApplyToDriver !== -1 &&
              currentEditedRecurringJob.serviceTypeId &&
              currentEditedRecurringJob.client?.id
            "
            class="px-2"
          >
            <AllocateDriver
              ref="allocateDriverComponent"
              :type="ObjectToAllocate.PERMANENT_JOB"
              :onValidSelection="OnValidAllocationTarget.EMIT"
              :fleetAssetId.sync="fleetAssetIdModel"
              :driverId.sync="driverIdModel"
              :serviceTypeId="currentEditedRecurringJob.serviceTypeId"
              :rateTypeId="rateToApplyToDriver"
              :fleetAssetRates="fleetAssetAccountingRates"
              :clientId="currentEditedRecurringJob.client.id"
              :searchDate="currentEditedRecurringJob.jobRunEpoch"
              :isFormDisabled="!isAllocationEnabled"
              @submitPreallocation="handleAllocationDetails"
            ></AllocateDriver>
          </v-flex>

          <v-flex
            v-if="
              currentEditedRecurringJob.clientDispatcher &&
              currentEditedRecurringJob
            "
            md12
            class="input-container px-2 mt-3 mb-2"
          >
            <h6 class="mb-2">Client Dispatcher</h6>
            <ClientPersonDispatcherBookingSelect
              v-if="clientDetails"
              ref="clientDispatcherSelect"
              v-model="currentEditedRecurringJob.clientDispatcher"
              :client_id="clientDetails._id"
              :job_id="
                currentEditedRecurringJob._id
                  ? currentEditedRecurringJob._id
                  : undefined
              "
              :clientId="clientDetails.clientId"
              :clientName="clientDetails.displayName"
              :defaultDispatcherId="clientDetails.defaultDispatcherId"
              :clientPersonIds="clientDetails.clientPersonDispatchers"
              :required="true"
              :disabled="false"
            />
          </v-flex>

          <v-layout md12 ma-2>
            <v-flex md6>
              <h6 class="mb-2">
                Job References
                <span>
                  ({{
                    currentEditedRecurringJob.jobReference?.length ?? 0
                  }})</span
                >
              </h6>
              <v-flex>
                <BookingReferences
                  :isPud="true"
                  :soloInput="true"
                  :jobReferences="currentEditedRecurringJob.jobReference"
                  :readOnlyView="false"
                />
              </v-flex>
            </v-flex>
            <v-divider class="vertical-divider" inset vertical></v-divider>
            <v-flex md6>
              <h6 class="mb-2">
                Notes ({{ currentEditedRecurringJob.notes?.length ?? 0 }})
              </h6>
              <v-flex>
                <v-flex class="notes-container custom-scrollbar">
                  <NotesList
                    :isBookingScreen="true"
                    :allowDelete="true"
                    :allowEdit="true"
                    :communications="currentEditedRecurringJob.notes || []"
                    :showVisibilityTypeName="true"
                    :showAddToJobType="true"
                    :detailedView="true"
                    @editNote="handleEditNote($event)"
                    @removeNote="handleRemoveNote"
                  >
                  </NotesList>
                </v-flex>
                <v-btn
                  block
                  depressed
                  plain
                  large
                  @click="noteDialogIsOpen = !noteDialogIsOpen"
                  class="notes-btn"
                >
                  <v-icon class="pl-1 pr-2" size="16">note_add</v-icon>
                  Add Notes
                </v-btn>
              </v-flex>
            </v-flex>
          </v-layout>
          <v-flex
            md12
            ma-2
            v-if="
              currentEditedRecurringJob.isClientTripRate ||
              currentEditedRecurringJob.isDriverTripRate
            "
          >
            <v-flex class="subheader--faded"><span>TRIP RATE</span> </v-flex>
            <v-layout row wrap>
              <v-flex md6>
                <TripRateBooking
                  class="client"
                  title="Client"
                  :enableTripRate.sync="tripRateInformation.client"
                  :fuelSurchargeRate.sync="
                    tripRateInformation.clientFuelSurcharge
                  "
                  :rateAmount.sync="tripRateInformation.clientRate"
                  :isDirectToInvoice="
                    currentEditedRecurringJob.directToInvoicing
                  "
                />
              </v-flex>
              <v-flex md6>
                <TripRateBooking
                  class="driver"
                  title="Driver"
                  :enableTripRate.sync="
                    currentEditedRecurringJob.directToInvoicing
                      ? true
                      : tripRateInformation.fleetAsset
                  "
                  :fuelSurchargeRate.sync="
                    tripRateInformation.fleetAssetFuelSurcharge
                  "
                  :rateAmount.sync="tripRateInformation.fleetAssetRate"
                  :isDirectToInvoice="
                    currentEditedRecurringJob.directToInvoicing
                  "
                />
              </v-flex>
            </v-layout>
            <span
              class="direct_invoice_text"
              v-if="currentEditedRecurringJob.directToInvoicing"
              >NOTE: JOB IS BOOKED DIRECT TO INVOICE</span
            >
          </v-flex>

          <ContentDialog
            :showDialog.sync="noteDialogIsOpen"
            title="Add Note to Job"
            width="40%"
            contentPadding="pa-0"
            :showActions="false"
            :isConfirmUnsaved="false"
            :isDisabled="false"
            :isLoading="false"
            @cancel="noteDialogIsOpen = false"
            confirmBtnText="Confirm"
          >
            <v-layout>
              <v-flex md12 class="body-scrollable--75 body-min-height--65">
                <NotesEditor
                  v-if="noteDialogIsOpen"
                  :isBookingScreen="true"
                  :communications="currentEditedRecurringJob.notes ?? []"
                  :isEdited="true"
                  :isAddingNote="noteDialogIsOpen"
                  @setIsAddingNote="noteDialogIsOpen = $event"
                  :type="3"
                  :jobDetails="undefined"
                  :isDispatchNote="false"
                  :enableCommunicationTypeSelect="
                    !sessionManager.isClientPortal()
                  "
                  :enableVisibilitySelect="!sessionManager.isClientPortal()"
                  :isClientVisibilityOnly="sessionManager.isClientPortal()"
                  :jobNoteLevel="JobNoteLevel.JOB"
                >
                </NotesEditor>
              </v-flex>
            </v-layout>
          </ContentDialog>
        </v-layout>
        <v-layout row wrap>
          <v-flex md12>
            <v-divider></v-divider>
          </v-flex>
          <v-flex md12>
            <v-layout justify-space-between>
              <v-btn flat color="red" @click="resetWorkingVariables()"
                >Cancel</v-btn
              >
              <v-btn
                depressed
                color="blue"
                :disabled="
                  (currentEditedRecurringJob.fleetAssetId !== '' &&
                    currentEditedRecurringJob.driverId === '') ||
                  (currentEditedRecurringJob.driverId !== '' &&
                    currentEditedRecurringJob.fleetAssetId === '')
                "
                @click="saveEditedRecurringJobTemplate"
                >Confirm and Save</v-btn
              >
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </v-dialog>
</template>

<script setup lang="ts">
import AllocateDriver from '@/components/common/allocate_driver/allocate_driver.vue';
import NotesEditor from '@/components/common/notes_editor/notes_editor.vue';
import NotesList from '@/components/common/notes_list/notes_list.vue';
import RecurrenceVisualisation from '@/components/common/recurring_job/recurrence_visualisation.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import BookingReferences from '@/components/operations/BookJob/booking_references/index.vue';
import ClientPersonDispatcherBookingSelect from '@/components/operations/BookJob/dispatcher_booking_select/dispatcher_booking_select.vue';
import TripRateBooking from '@/components/operations/BookJob/service_rates_booking/trip_rate_booking/index.vue';
import {
  ObjectToAllocate,
  OnValidAllocationTarget,
} from '@/helpers/AllocationHelpers.ts/AllocationHelpers';
import { initialiseJobAccountingDetails } from '@/helpers/classInitialisers/InitialiseJobDetails';
import { addTripRateDetailsToJob } from '@/helpers/JobBooking/Accounting/JobBookingTripRateHelpers';
import { deepCopy } from '@/helpers/JsonHelpers/JsonHelpers';
import {
  GENERIC_SUCCESS_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { returnInitialTripRateInfo } from '@/helpers/RateHelpers/TripRateHelpers';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import { getClientRelatedContactsByClientId } from '@/interface-models/Client/ClientRelatedContact';
import { JobAccountingDetails } from '@/interface-models/Generic/Accounting/JobAccountingDetails';
import JobPrimaryRate from '@/interface-models/Generic/Accounting/JobPrimaryRate';
import Communication from '@/interface-models/Generic/Communication/Communication';
import { JobNoteLevel } from '@/interface-models/Generic/Communication/JobNoteLevel';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { AllocationSummary } from '@/interface-models/Jobs/Allocation/AllocateJobRequest';
import RecurringJobTemplate from '@/interface-models/Jobs/RecurringJob/RecurringJobTemplate';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import TripRate from '@/interface-models/ServiceRates/ServiceTypes/TripRate/TripRate';
import TripRateInformation from '@/interface-models/ServiceRates/TripRateInformation';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useRecurringJobStore } from '@/store/modules/RecurringJobStore';
import { useUserManagementStore } from '@/store/modules/UserManagementStore';
import { sessionManager } from '@/store/session/SessionState';
import {
  ComputedRef,
  Ref,
  WritableComputedRef,
  computed,
  ref,
  watch,
} from 'vue';

const props = withDefaults(
  defineProps<{
    showEditingRecurringJobDialog: boolean;
    currentRecurringJob: RecurringJobTemplate | null;
    allTemplates: RecurringJobTemplate[] | null;
  }>(),
  {
    showEditingRecurringJobDialog: false,
    currentRecurringJob: null,
    allTemplates: null,
  },
);

const emit = defineEmits<{
  (emit: 'update:showEditingRecurringJobDialog', value: boolean): void;
}>();

const recurringJobStore = useRecurringJobStore();
const clientStore = useClientDetailsStore();
const userStore = useUserManagementStore();

const isAwaitingResponse: Ref<boolean> = ref(false);

const noteDialogIsOpen: Ref<boolean> = ref(false);

const currentEditedRecurringJob = ref<RecurringJobTemplate | null>(null);

const clientDetails: Ref<ClientDetails | null> = ref(null);

const currentlyEditingNote: Ref<Communication> = ref(new Communication());

const allocateDriverComponent = ref<InstanceType<typeof AllocateDriver> | null>(
  null,
);
const fleetAssetAccountingRates: Ref<JobPrimaryRate[]> = ref([]);

const accountingDetails: Ref<JobAccountingDetails> = ref(
  new JobAccountingDetails(),
);

const tripRateInformation: Ref<TripRateInformation> = ref(
  new TripRateInformation(),
);

const isAllocationEnabled: Ref<boolean> = ref(false);

/**
 * Controls visibility of edit dialog. When closing the dialog, also clears the
 * current edited recurring job.
 */
const viewingEditDialog = computed({
  get: () => props.showEditingRecurringJobDialog,
  set: (val: boolean) => {
    emit('update:showEditingRecurringJobDialog', val);
  },
});

/**
 * Watches for changes in the dialogController value. If the dialog is opened,
 * copy the current recurrence details from props to the working copy. If the
 * dialog is closed, reset editedRecurrenceDetails.
 */
watch(viewingEditDialog, (newValue) => {
  if (newValue) {
    // Set the initial values of the form fields
    setWorkingVariables();
  } else {
    // Reset the form fields
    resetWorkingVariables();
  }
});

/**
 * Used in the AllocateDriver component to bind the fleet asset ID and
 * driver ID to the current edited recurring job.
 * This allows the AllocateDriver component to update the IDs directly in the
 * current edited recurring job.
 */
const fleetAssetIdModel: WritableComputedRef<string> = computed({
  get(): string {
    return currentEditedRecurringJob.value?.fleetAssetId || '';
  },
  set(value: string): void {
    if (currentEditedRecurringJob.value) {
      currentEditedRecurringJob.value.fleetAssetId = value;
    }
  },
});

/**
 * Used in the AllocateDriver component to bind the driver ID to the
 * current edited recurring job.
 * This allows the AllocateDriver component to update the driver ID directly in the
 * current edited recurring job.
 */
const driverIdModel: WritableComputedRef<string> = computed({
  get(): string {
    return currentEditedRecurringJob.value?.driverId || '';
  },
  set(value: string): void {
    if (currentEditedRecurringJob.value) {
      currentEditedRecurringJob.value.driverId = value;
    }
  },
});

// set working variable, create local copy of selected RecurringJob
async function setWorkingVariables() {
  const job = props.currentRecurringJob;
  // Initialize local copy of job
  currentEditedRecurringJob.value = job
    ? Object.assign(new RecurringJobTemplate(), deepCopy(job))
    : null;
  if (currentEditedRecurringJob.value) {
    isAllocationEnabled.value =
      currentEditedRecurringJob.value.fleetAssetId === '' &&
      currentEditedRecurringJob.value.driverId === '';
  }

  isAwaitingResponse.value = true;
  try {
    await fetchClientDetails();
    if (job?.accounting) {
      tripRateInformation.value = returnInitialTripRateInfo(job.accounting);
      accountingDetails.value = initialiseJobAccountingDetails(job.accounting);
    }
  } catch (error) {
    console.error(error);
  } finally {
    isAwaitingResponse.value = false;
  }
}

// reset variables and close editing dialog
function resetWorkingVariables() {
  currentEditedRecurringJob.value = null;
  isAllocationEnabled.value = false;
  clientDetails.value = null;
  viewingEditDialog.value = false;
}

/**
 * Returns the driver rate currently applied to the recurring job.. Passed into
 * AllocateDriver component. If the service type is not set, returns -1. If the
 * fleetAssetRates (in accounting) has an element in it and that element is a
 * TRIP/Quoted RATE, then return TRIP/Quoted RATE (as backend will use that to book).
 * Otherwise return the rateTypeId defined in serviceTypeObject. In this case,
 * the backend will find the rates based on the rateTypeId.
 */
const rateToApplyToDriver: ComputedRef<number> = computed(() => {
  if (
    !currentEditedRecurringJob.value ||
    !currentEditedRecurringJob.value?.serviceTypeObject
  ) {
    return -1;
  }
  const rti = currentEditedRecurringJob.value.isDriverTripRate
    ? JobRateType.TRIP
    : currentEditedRecurringJob.value.serviceTypeObject.rateTypeId;
  return rti;
});

// Send recurring job cancellation request
async function saveEditedRecurringJobTemplate() {
  if (currentEditedRecurringJob.value === null) {
    return;
  }
  if (rateToApply.value) {
    updateAccountingDetails(accountingDetails.value, rateToApply.value);
  }
  currentEditedRecurringJob.value.accounting = accountingDetails.value;
  // Send request to update job and show notification
  await sendUpdatedRecurringJobTemplate(currentEditedRecurringJob.value);
  viewingEditDialog.value = false;
}

/**
 * Sends an updated recurring job template to the server to be saved. Shows a notification depending on whether the template was saved successfully or not.
 *
 * @param {RecurringJobTemplate} template - The updated recurring job template to send.
 * @returns {Promise<void>} - A promise that resolves when the template is successfully sent.
 */
async function sendUpdatedRecurringJobTemplate(
  template: RecurringJobTemplate,
): Promise<void> {
  const updatedTemplate =
    await recurringJobStore.saveRecurringJobTemplate(template);
  // Show notification based on response
  if (updatedTemplate === null) {
    showNotification(
      'Error occurred when updating Permanent Job. Please try again',
    );
  } else {
    if (props.allTemplates) {
      // Replace the template in the list with the updated one
      const index = props.allTemplates.findIndex(
        (t) => updatedTemplate._id && t._id === updatedTemplate._id,
      );
      if (index !== -1) {
        props.allTemplates.splice(
          index,
          1,
          Object.assign(new RecurringJobTemplate(), updatedTemplate),
        );
      }
      showNotification(GENERIC_SUCCESS_MESSAGE, { type: HealthLevel.SUCCESS });
    }
  }
}

/**
 * Handles the emit from the AllocateDriver component containing the details of an allocation.
 * If the selection is not valid, it resets the fleet asset and driver IDs in the
 * current edited recurring job.
 * @param isValid - boolean indicating whether the selection is valid or not.
 */
function handleAllocationDetails(allocationDetails: AllocationSummary | null) {
  if (!currentEditedRecurringJob.value) {
    return;
  }
  const isValid = allocationDetails !== null;
  // If the selection is not valid, reset the fleet asset and driver IDs
  if (isValid) {
    currentEditedRecurringJob.value.fleetAssetId =
      allocationDetails.fleetAssetId;
    currentEditedRecurringJob.value.driverId = allocationDetails.driverId;
    // NOTE: Don't need to update the fleetAssetRates here as the scheduler will
    // find the current rates while it generates the next job instance.
  } else {
    currentEditedRecurringJob.value.fleetAssetId = '';
    currentEditedRecurringJob.value.driverId = '';
  }
  isAllocationEnabled.value = !isValid;
}

/**
 * Clears the fleet asset and driver from the currently edited recurring job.
 */
function deallocateCurrentSelection() {
  allocateDriverComponent.value?.clearInputs();
  isAllocationEnabled.value = true;
}

// handle notes editor dialog visibility
function handleEditNote(note: Communication) {
  currentlyEditingNote.value = note;
  noteDialogIsOpen.value = true;
}

// Delete the note from PudDetails
function handleRemoveNote(index: number) {
  if (currentEditedRecurringJob.value) {
    currentEditedRecurringJob.value.notes?.splice(index, 1);
  }
}

// async to fetch client details and related contacts for client dispatcher
async function fetchClientDetails() {
  const clientId = currentEditedRecurringJob.value?.client?.id;
  if (!clientId) {
    return;
  }

  try {
    clientDetails.value =
      await clientStore.requestClientDetailsByClientId(clientId);
    await getClientRelatedContactsByClientId(clientId);

    if (clientDetails.value?._id) {
      await userStore.getClientPersonsWithAuthDetails(
        clientDetails.value._id,
        clientDetails.value.clientPersonDispatchers,
      );
    }
  } catch (error) {
    console.error(error);
  }
}

/**
 * Apples properties from the rate table item to the accounting details object.
 * Called when the confirm button is clicked.
 * @param accounting The accounting details object to update
 * @param rateTableItem The rate table item to apply
 */
function updateAccountingDetails(
  accounting: JobAccountingDetails,
  rateData: {
    clientRateTableItem?: RateTableItems;
    driverRateTableItem?: RateTableItems;
  },
) {
  if (currentEditedRecurringJob.value) {
    const jobDetails = currentEditedRecurringJob.value.toJobDetails();

    if (rateData.clientRateTableItem) {
      addTripRateDetailsToJob(
        jobDetails,
        currentEditedRecurringJob.value.serviceTypeId!,
        accountingDetails.value,
        tripRateInformation.value,
        rateToApply.value?.clientRateTableItem ?? null,
        null,
      );
    }
    if (rateData.driverRateTableItem) {
      addTripRateDetailsToJob(
        jobDetails,
        currentEditedRecurringJob.value.serviceTypeId!,
        accountingDetails.value,
        tripRateInformation.value,
        rateToApply.value?.driverRateTableItem ?? null,
        null,
      );
    }
  }
}

/**
 * Returns the rate table items (client and/or driver) to apply
 *
 * If not a trip rate, returns null.
 * If client trip rate creates client rate item.
 * If fleet asset trip rate creates driver rate item.
 *
 * builds `RateTableItems` objects for both the client and driver trip Rate;
 * returns object containing one or both of clientRateTableItem and driverRateTableItem
 * depending on which rate information is available.
 */
const rateToApply: ComputedRef<{
  clientRateTableItem?: RateTableItems;
  driverRateTableItem?: RateTableItems;
} | null> = computed(() => {
  if (
    !tripRateInformation.value.client &&
    !tripRateInformation.value.fleetAsset
  ) {
    return null;
  }
  const result: {
    clientRateTableItem?: RateTableItems;
    driverRateTableItem?: RateTableItems;
  } = {};

  if (tripRateInformation.value.client) {
    const clientRate = new RateTableItems();
    clientRate.rateTypeId = 6;
    clientRate.serviceTypeId = currentEditedRecurringJob.value?.serviceTypeId;
    clientRate.fuelSurcharge = tripRateInformation.value.clientFuelSurcharge;
    clientRate.rateTypeObject = new TripRate(
      tripRateInformation.value.clientRate || 0,
    );
    result.clientRateTableItem = clientRate;
  }

  if (tripRateInformation.value.fleetAsset) {
    const driverRate = new RateTableItems();
    driverRate.rateTypeId = 6;
    driverRate.serviceTypeId = currentEditedRecurringJob.value?.serviceTypeId;
    driverRate.fuelSurcharge =
      tripRateInformation.value.fleetAssetFuelSurcharge;
    driverRate.rateTypeObject = new TripRate(
      tripRateInformation.value.fleetAssetRate || 0,
    );
    result.driverRateTableItem = driverRate;
  }

  return result;
});
</script>

<style scoped lang="scss">
.notes-btn {
  width: 100%;
  border-radius: 14px;
  color: var(--warning) !important;
  padding: 0px;
  margin: 0px;
  height: 34px;
  background-color: transparent !important;
  border: 1.5px solid var(--warning);
  &:disabled {
    background: none !important;
    border: none !important;
  }
}
.notes-container {
  height: 302px;
  max-height: 100%;
  padding-right: 4px;
  overflow-x: hidden;
  margin-top: 4px;
}

.vertical-divider {
  margin-right: 14px;
  margin-left: 14px;
}

.direct_invoice_text {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  margin: 0 auto;
  color: $warning;
  opacity: 0.8;
  padding-top: 8px;
}
</style>
