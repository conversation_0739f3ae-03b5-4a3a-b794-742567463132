<template>
  <div class="audit-container">
    <TableTitleHeader>
      <!-- Title slot -->
      <template #title>
        <GTitle
          title="Audit History"
          subtitle="Search all audit history"
          :divider="false"
        />
      </template>

      <template #inputs>
        <v-layout wrap v-if="!loading">
          <v-flex md3>
            <v-select
              hide-details
              label="Modules"
              solo
              flat
              color="blue"
              hint="Modules"
              persistent-hint
              :items="auditClassTypeNames"
              item-value="k"
              class="v-solo-custom"
              item-text="v"
              v-model="auditClassTypeName"
            >
            </v-select>
          </v-flex>

          <v-flex md3 class="pl-1">
            <v-text-field
              solo
              flat
              hint="Username/E-mail"
              persistent-hint
              class="v-solo-custom"
              v-model="username"
              label="Username/E-mail"
            />
          </v-flex>

          <v-flex md2 class="pl-1">
            <DatePickerBasic
              @setEpoch="setStartRange"
              :labelName="'Start Date'"
              :soloInput="true"
              :epochTime="startRangeEpoch"
            />
          </v-flex>

          <v-flex md2 class="pl-1">
            <DatePickerBasic
              @setEpoch="setEndRange"
              :labelName="'End Date'"
              :soloInput="true"
              :epochTime="endRangeEpoch"
            />
          </v-flex>

          <v-flex md2 class="pl-3" style="padding-top: 1px">
            <v-btn block @click="searchAuditHistory" class="view-details-button"
              >Search</v-btn
            >
          </v-flex>
        </v-layout>
      </template>
    </TableTitleHeader>
    <v-layout v-if="loading" justify-center>
      <v-progress-circular indeterminate color="orange"></v-progress-circular>
    </v-layout>

    <div class="mt-3">
      <AuditDetailsComponent
        v-if="clientDetailsAuditHistory !== null"
        :auditResponses="clientDetailsAuditHistory"
        :auditTypeName="
          auditClassTypeNames.find((a) => a.k === 'CLIENT_DETAILS')?.v
        "
      />

      <AuditDetailsComponent
        v-if="clientFuelSurchargeRateAuditHistory !== null"
        :auditResponses="clientFuelSurchargeRateAuditHistory"
        :auditTypeName="
          auditClassTypeNames.find((a) => a.k === 'CLIENT_FUEL_SURCHARGE_RATE')
            ?.v
        "
      />

      <AuditDetailsComponent
        v-if="clientServiceRateAuditHistory !== null"
        :auditResponses="clientServiceRateAuditHistory"
        :auditTypeName="
          auditClassTypeNames.find((a) => a.k === 'CLIENT_SERVICE_RATE')?.v
        "
      />

      <AuditDetailsComponent
        v-if="driverDetailsAuditHistory !== null"
        :auditResponses="driverDetailsAuditHistory"
        :auditTypeName="
          auditClassTypeNames.find((a) => a.k === 'DRIVER_DETAILS')?.v
        "
      />

      <AuditDetailsComponent
        v-if="fleetAssetDetailsAuditHistory !== null"
        :auditResponses="fleetAssetDetailsAuditHistory"
        :auditTypeName="
          auditClassTypeNames.find((a) => a.k === 'FLEET_ASSET_DETAILS')?.v
        "
      />

      <AuditDetailsComponent
        v-if="fleetAssetFuelSurchargeRateAuditHistory !== null"
        :auditResponses="fleetAssetFuelSurchargeRateAuditHistory"
        :auditTypeName="
          auditClassTypeNames.find(
            (a) => a.k === 'FLEET_ASSET_FUEL_SURCHARGE_RATE',
          )?.v
        "
      />

      <AuditDetailsComponent
        v-if="fleetAssetOwnerAuditHistory !== null"
        :auditResponses="fleetAssetOwnerAuditHistory"
        :auditTypeName="
          auditClassTypeNames.find((a) => a.k === 'FLEET_ASSET_OWNER')?.v
        "
      />

      <AuditDetailsComponent
        v-if="fleetAssetServiceRateAuditHistory !== null"
        :auditResponses="fleetAssetServiceRateAuditHistory"
        :auditTypeName="
          auditClassTypeNames.find((a) => a.k === 'FLEET_ASSET_SERVICE_RATE')?.v
        "
      />

      <AuditDetailsComponent
        v-if="clientPersonAuditHistory !== null"
        :auditResponses="clientPersonAuditHistory"
        :auditTypeName="
          auditClassTypeNames.find((a) => a.k === 'CLIENT_PERSON')?.v
        "
      />
      <AuditDetailsComponent
        v-if="salesManagementAuditHistory !== null"
        :auditResponses="salesManagementAuditHistory"
        :auditTypeName="
          auditClassTypeNames.find((a) => a.k === 'SALES_MANAGEMENT')?.v
        "
      />
      <AuditDetailsComponent
        v-if="additionalChargeItemAuditHistory !== null"
        :auditResponses="additionalChargeItemAuditHistory"
        :auditTypeName="
          auditClassTypeNames.find((a) => a.k === 'ADDITIONAL_CHARGE_ITEM')?.v
        "
      />
      <AuditDetailsComponent
        v-if="driverComplianceFormAuditHistory !== null"
        :auditResponses="driverComplianceFormAuditHistory"
        :auditTypeName="
          auditClassTypeNames.find((a) => a.k === 'DRIVER_COMPLIANCE_FORM')?.v
        "
      />
      <AuditDetailsComponent
        v-if="serviceTypesAuditHistory !== null"
        :auditResponses="serviceTypesAuditHistory"
        :auditTypeName="
          auditClassTypeNames.find((a) => a.k === 'SERVICE_TYPES')?.v
        "
      />

      <AuditDetailsComponent
        v-if="clientServiceRateVariationAuditHistory !== null"
        :auditResponses="clientServiceRateVariationAuditHistory"
        :auditTypeName="
          auditClassTypeNames.find(
            (a) => a.k === 'CLIENT_SERVICE_RATE_VARIATIONS',
          )?.v
        "
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import AuditDetailsComponent from '@/components/admin/Administration/audit_history/audit_details.vue';
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import TableTitleHeader from '@/components/common/ui-elements/table_title_header.vue';
import {
  returnEndOfDayFromEpoch,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { AuditResponse } from '@/interface-models/Audit/AuditChange';
import { AuditRequest } from '@/interface-models/Audit/AuditRequest';
import { AuditClassType } from '@/interface-models/Audit/AuditType';
import { useRootStore } from '@/store/modules/RootStore';
import moment from 'moment';
import { ComputedRef, Ref, computed, onMounted, ref } from 'vue';

const clientDetailsAuditHistory: Ref<AuditResponse[] | null> = ref(null);
const clientFuelSurchargeRateAuditHistory: Ref<AuditResponse[] | null> =
  ref(null);
const clientServiceRateAuditHistory: Ref<AuditResponse[] | null> = ref(null);
const driverDetailsAuditHistory: Ref<AuditResponse[] | null> = ref(null);
const fleetAssetDetailsAuditHistory: Ref<AuditResponse[] | null> = ref(null);
const fleetAssetFuelSurchargeRateAuditHistory: Ref<AuditResponse[] | null> =
  ref(null);
const fleetAssetOwnerAuditHistory: Ref<AuditResponse[] | null> = ref(null);
const fleetAssetServiceRateAuditHistory: Ref<AuditResponse[] | null> =
  ref(null);
const clientPersonAuditHistory: Ref<AuditResponse[] | null> = ref(null);
const salesManagementAuditHistory: Ref<AuditResponse[] | null> = ref(null);
const additionalChargeItemAuditHistory: Ref<AuditResponse[] | null> = ref(null);
const driverComplianceFormAuditHistory: Ref<AuditResponse[] | null> = ref(null);
const serviceTypesAuditHistory: Ref<AuditResponse[] | null> = ref(null);
const clientServiceRateVariationAuditHistory: Ref<AuditResponse[] | null> =
  ref(null);

const loading: Ref<boolean> = ref(false);

const auditClassTypeName: Ref<AuditClassType> = ref(AuditClassType.ALL);
const username: Ref<string> = ref('');
const startRangeEpoch: Ref<number> = ref(
  returnStartOfDayFromEpoch(moment().subtract(2, 'weeks').valueOf()),
);
const endRangeEpoch: Ref<number> = ref(returnEndOfDayFromEpoch());

/**
 * Sets the start range epoch time.
 * @param {number} value - The epoch time to set as the start range.
 */
function setStartRange(value: number) {
  startRangeEpoch.value = value;
}

/**
 * Sets the end range epoch time.
 * @param {number} value - The epoch time to set as the end range.
 */
function setEndRange(value: number) {
  const m = moment(value);
  m.set({ h: 23, m: 59, s: 59 });
  endRangeEpoch.value = m.valueOf();
}

/**
 * Fetches the audit history when the component is mounted.
 */
onMounted(() => {
  searchAuditHistory();
});

/**
 * Fetches audit history based on the provided parameters and updates the relevant properties.
 * If 'auditClassTypeName' is 'ALL', it fetches the full audit history.
 * Otherwise, it fetches the audit history for a single audit module.
 * @returns {Promise<void>} A promise that resolves when the operation is complete.
 */
async function searchAuditHistory(): Promise<void> {
  loading.value = true;

  // Create an AuditRequest object
  const auditRequest = new AuditRequest(
    startRangeEpoch.value,
    endRangeEpoch.value,
    username.value,
    auditClassTypeName.value !== AuditClassType.ALL
      ? auditClassTypeName.value
      : undefined,
  );

  // Fetch the audit history
  const result =
    auditClassTypeName.value !== AuditClassType.ALL
      ? await useRootStore().getAuditHistoryByAuditClassType(auditRequest)
      : await useRootStore().getFullAuditHistory(auditRequest);

  if (result) {
    // Update the properties with the fetched audit history
    clientDetailsAuditHistory.value = result.CLIENT_DETAILS ?? null;
    clientFuelSurchargeRateAuditHistory.value =
      result.CLIENT_FUEL_SURCHARGE_RATE ?? null;
    clientServiceRateAuditHistory.value = result.CLIENT_SERVICE_RATE ?? null;
    driverDetailsAuditHistory.value = result.DRIVER_DETAILS ?? null;
    fleetAssetDetailsAuditHistory.value = result.FLEET_ASSET_DETAILS ?? null;
    fleetAssetFuelSurchargeRateAuditHistory.value =
      result.FLEET_ASSET_FUEL_SURCHARGE_RATE ?? null;
    fleetAssetOwnerAuditHistory.value = result.FLEET_ASSET_OWNER ?? null;
    fleetAssetServiceRateAuditHistory.value =
      result.FLEET_ASSET_SERVICE_RATE ?? null;
    clientPersonAuditHistory.value = result.CLIENT_PERSON ?? null;
    salesManagementAuditHistory.value = result.SALES_MANAGEMENT ?? null;
    additionalChargeItemAuditHistory.value =
      result.ADDITIONAL_CHARGE_ITEM ?? null;
    driverComplianceFormAuditHistory.value =
      result.DRIVER_COMPLIANCE_FORM ?? null;
    serviceTypesAuditHistory.value = result.SERVICE_TYPES ?? null;
    clientServiceRateVariationAuditHistory.value =
      result.CLIENT_SERVICE_RATE_VARIATIONS ?? null;
  } else {
    showNotification('Failed to fetch audit history');
  }
  loading.value = false;
}

/**
 * Returns an array of audit class type names.
 * @returns {Array<{ k: string; v: AuditClassType }>} An array of objects containing the audit class type names.
 */
const auditClassTypeNames: ComputedRef<
  Array<{ k: string; v: AuditClassType }>
> = computed(() => {
  const enumArray: Array<{ k: string; v: AuditClassType }> = [];
  Object.entries(AuditClassType).forEach(([key, value]) =>
    enumArray.push({ k: key, v: value }),
  );
  return enumArray;
});
</script>
<style scoped lang="scss">
.audit-container {
  background-color: var(--background-color-250);
  padding-bottom: 67px;
}
</style>
