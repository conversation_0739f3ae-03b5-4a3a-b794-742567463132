<template>
  <section class="audit-history-table-view mb-3">
    <v-flex md12>
      <v-layout align-center>
        <b class="table-header-txt">{{ auditTypeName }} Activities</b>
        <v-spacer></v-spacer>
      </v-layout>
    </v-flex>

    <v-data-table
      :headers="headers"
      :items="auditTableData"
      :rows-per-page-items="[10, 15, 20]"
      disable-initial-sort
      class="gd-dark-theme"
    >
      <template v-slot:items="tableProps">
        <tr>
          <td>{{ tableProps.item.formattedTimestamp }}</td>
          <td>{{ tableProps.item.changedBy }}</td>
          <td>{{ tableProps.item.updateType }}</td>
          <td>{{ tableProps.item.referenceDetails }}</td>
          <td>{{ tableProps.item.propertyName }}</td>
          <td>{{ tableProps.item.oldValue }}</td>
          <td>{{ tableProps.item.newValue }}</td>
        </tr>
      </template>
    </v-data-table>
  </section>
</template>

<script setup lang="ts">
interface TableItem {
  commitTimestamp: number;
  formattedTimestamp: string;
  changedBy: string;
  updateType: string;
  propertyName: string;
  referenceDetails: string;
  oldValue: string;
  newValue: string;
}

import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { AuditResponse } from '@/interface-models/Audit/AuditChange';
import { PropertyChangeType } from '@/interface-models/Audit/AuditType';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import moment from 'moment-timezone';
import { ComputedRef, computed } from 'vue';

const props = withDefaults(
  defineProps<{
    auditResponses?: AuditResponse[];
    auditTypeName?: string;
  }>(),
  {
    auditResponses: undefined,
    auditTypeName: undefined,
  },
);

const headers: TableHeader[] = [
  {
    text: 'Transaction Date',
    align: 'left',
    value: 'commitTimestamp',
    sortable: true,
  },
  {
    text: 'Changed by',
    align: 'left',
    value: 'changedBy',
    sortable: true,
  },
  {
    text: 'Update Type',
    align: 'left',
    value: 'updateType',
    sortable: true,
  },
  {
    text: 'Reference',
    align: 'left',
    value: 'referenceDetails',
    sortable: true,
  },
  {
    text: 'Property Name',
    align: 'left',
    value: 'propertyName',
    sortable: true,
  },
  {
    text: 'Old Value',
    align: 'left',
    value: 'oldValue',
    sortable: true,
  },
  {
    text: 'New Value',
    align: 'left',
    value: 'newValue',
    sortable: true,
  },
];

/**
 * Maps enum values of PropertyChangeType to a readable version of their name.
 * @param {PropertyChangeType} type - The enum value to map.
 * @returns {string} The readable version of the enum value.
 */
function mapPropertyChangeTypeToReadableName(type: PropertyChangeType): string {
  const mapping: Record<PropertyChangeType, string> = {
    [PropertyChangeType.PROPERTY_ADDED]: 'Property Added',
    [PropertyChangeType.PROPERTY_REMOVED]: 'Property Removed',
    [PropertyChangeType.PROPERTY_VALUE_CHANGED]: 'Property Value Changed',
    [PropertyChangeType.NEW_OBJECT_ADDED]: 'New Object Added',
  };

  return mapping[type] || 'Unknown Change Type';
}

/**
 * Computes the audit table data based on the audit responses.
 * @returns {Array} The formatted audit table data.
 */
const auditTableData: ComputedRef<TableItem[]> = computed(() => {
  const epochDateFields = ['validToDate', 'validFromDate', 'establishedYear'];

  const tableData: TableItem[] = [];
  if (props.auditResponses) {
    for (const type of props.auditResponses) {
      const tableItem: TableItem = {
        commitTimestamp: type.commitTimestamp,
        formattedTimestamp: moment
          .unix(type.commitTimestamp)
          .format('DD/MM/YYYY HH:mm:ss'),
        changedBy: type.changedBy,
        updateType: mapPropertyChangeTypeToReadableName(
          type.propertyChangeType,
        ),
        propertyName: type.propertyName,
        referenceDetails: type.referenceDetails,
        oldValue: !epochDateFields.includes(type.propertyName)
          ? type.oldValue
          : returnFormattedDate(parseInt(type.oldValue, 10), 'DD/MM/YY HH:mm '),
        newValue: !epochDateFields.includes(type.propertyName)
          ? type.newValue
          : returnFormattedDate(parseInt(type.newValue, 10), 'DD/MM/YY HH:mm'),
      };
      tableData.push(tableItem);
    }
  }
  return tableData.sort((a, b) => {
    return b.commitTimestamp - a.commitTimestamp;
  });
});
</script>
<style scoped lang="scss">
.table-header-txt {
  margin-bottom: 4px;
  padding-left: 10px;
  font-size: $font-size-16;
  color: var(--primary);
}
</style>
