<template>
  <div class="holiday-container">
    <TableTitleHeader>
      <template #title>
        <GTitle
          title="Holidays"
          subtitle="Add or edit public holidays"
          :divider="false"
        />
      </template>
      <template #buttons>
        <v-btn
          color="primary"
          depressed
          :disabled="!isAdmin"
          @click="addNewHoliday"
        >
          <v-icon>add</v-icon>
          Add Holiday
        </v-btn>
      </template>
      <template #inputs>
        <v-flex md5>
          <v-text-field
            appendIcon="search"
            label="Search Holiday Name or dd/mm/yyyy"
            hint="Search Holiday Name or dd/mm/yyyy"
            color="orange"
            solo
            flat
            class="v-solo-custom"
            clearable
            v-model="searchQuery"
          >
          </v-text-field>
        </v-flex>
      </template>
    </TableTitleHeader>

    <v-flex md12>
      <v-data-table
        :headers="headers"
        :items="filteredHolidays"
        class="gd-dark-theme"
        item-key="id"
        :rows-per-page-items="[20, 40]"
      >
        <template v-slot:items="tableProps">
          <tr
            class="charge-row-item"
            @click="
              isAdmin ? editHolidayDetails(tableProps.item._id) : () => {}
            "
            :style="isAdmin ? 'cursor: pointer' : ''"
          >
            <td>{{ tableProps.item.holidayName }}</td>
            <td>
              {{ tableProps.item.readableDate }}
            </td>
          </tr>
        </template>
      </v-data-table>
    </v-flex>

    <ContentDialog
      :showDialog.sync="dialogController"
      title="Holiday Details"
      width="40%"
      contentPadding="pa-0"
      @cancel="dialogController = false"
      @confirm="saveHoliday"
      :showActions="true"
      :isConfirmUnsaved="false"
      :isDisabled="false"
      :isLoading="isAwaitingSaveResponse"
      confirmBtnText="Confirm"
    >
      <v-layout>
        <v-flex md12 class="body-scrollable--75 pa-3">
          <v-form ref="holidayDetailsDialogForm">
            <v-layout wrap>
              <v-flex md12 pb-3
                ><v-layout align-center>
                  <h5 class="subheader--bold pr-3 pt-1">
                    {{ !editedHolidayDetails?._id ? 'New' : 'Edit' }} Holiday
                    Details
                  </h5>
                  <v-flex>
                    <v-divider></v-divider>
                  </v-flex>
                </v-layout>
              </v-flex>

              <v-flex md12>
                <v-layout>
                  <v-flex md4>
                    <v-layout align-center class="form-field-label-container">
                      <h6
                        class="subheader--faded pr-3 pb-0 form-field-required-marker"
                      >
                        Holiday Name
                      </h6>
                    </v-layout>
                  </v-flex>
                  <v-flex md6>
                    <v-text-field
                      class="v-solo-custom"
                      solo
                      flat
                      color="light-blue"
                      :disabled="false"
                      label="Enter Holiday Name"
                      v-model="newHolidayName"
                      :rules="[validate.validationRules.required]"
                      hint=""
                    />
                  </v-flex>
                </v-layout>
              </v-flex>

              <v-flex md12>
                <v-layout>
                  <v-flex md4>
                    <v-layout align-center class="form-field-label-container">
                      <h6
                        class="subheader--faded pr-3 pb-0 form-field-required-marker"
                      >
                        Date
                      </h6>
                    </v-layout>
                  </v-flex>
                  <v-flex md6>
                    <DatePickerBasic
                      @setEpoch="setDate"
                      :soloInput="true"
                      :labelName="'Select Holiday Date'"
                      :formDisabled="false"
                      :epochTime="newHolidayDate"
                      :isRequired="true"
                      :clearable="true"
                      :hideDetails="true"
                      :hideIcon="true"
                    />
                  </v-flex>
                </v-layout>
              </v-flex>
            </v-layout>
          </v-form>
        </v-flex>
      </v-layout>
    </ContentDialog>
  </div>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import TableTitleHeader from '@/components/common/ui-elements/table_title_header.vue';
import {
  returnEndOfDayFromEpoch,
  returnFormattedDate,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { hasAdminRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import HolidayDetails from '@/interface-models/Generic/HolidayDetails/HolidayDetails';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { useRootStore } from '@/store/modules/RootStore';
import { sessionManager } from '@/store/session/SessionState';

import { computed, ComputedRef, nextTick, Ref, ref } from 'vue';

const calendar = ref<InstanceType<typeof calendar> | null>(null);
const searchQuery: Ref<string> = ref('');
const dialogController: Ref<boolean> = ref(false);
const isAwaitingSaveResponse: Ref<boolean> = ref(false);
const newHolidayName: Ref<string> = ref('');
const newHolidayDate: Ref<number> = ref(Date.now());

const editedHolidayDetails: Ref<HolidayDetails | null> = ref(null);

const holidayDetailsDialogForm = ref<any>(null);

const validate = {
  validationRules,
};

const headers: TableHeader[] = [
  {
    text: 'Holiday Name',
    align: 'left',
    value: 'holidayName',
    sortable: true,
  },
  {
    text: 'Date',
    align: 'left',
    value: 'readableDate',
    sortable: true,
  },
];

// get list of holidays from store
const publicHolidays: ComputedRef<HolidayDetails[]> = computed(
  () => useRootStore().holidayDetailsList,
);

// filter table data based on search input
const filteredHolidays = computed(() => {
  if (!searchQuery.value) {
    return publicHolidays.value;
  }
  return publicHolidays.value.filter((item) =>
    Object.values(item).some(
      (val) =>
        val &&
        val.toString().toLowerCase().includes(searchQuery.value.toLowerCase()),
    ),
  );
});

// check if logged in user has ADMIN role
const isAdmin = computed(() => {
  return hasAdminRole();
});

/**
 * Saves a holiday entry.
 * This function validates the holiday details form before proceeding. If valid,
 * it updates the holiday details with the appropriate epoch timestamps, formatted
 * date, holiday name, and company/division information. It then sends the updated
 * holiday details to be saved via the store.
 */
async function saveHoliday() {
  if (!holidayDetailsDialogForm.value.validate()) {
    return;
  }
  isAwaitingSaveResponse.value = true;
  if (editedHolidayDetails.value) {
    editedHolidayDetails.value.epochStartTime = returnStartOfDayFromEpoch(
      newHolidayDate.value,
    );
    editedHolidayDetails.value.epochEndTime = returnEndOfDayFromEpoch(
      newHolidayDate.value,
    );
    editedHolidayDetails.value.readableDate = returnFormattedDate(
      newHolidayDate.value,
    );
    editedHolidayDetails.value.holidayName = newHolidayName.value;

    editedHolidayDetails.value.company = sessionManager.getCompanyId();
    editedHolidayDetails.value.division = sessionManager.getDivisionId();

    await useRootStore().saveHolidayDetails(editedHolidayDetails.value);
  }
  isAwaitingSaveResponse.value = false;
  dialogController.value = false;
}

/**
 * For the provided id, finds the associated HolidayDetailsItem and sets it to
 * the edit variable, which will open the edit dialog.
 * @param {string} id - The ID of the Holiday Details item.
 */
function editHolidayDetails(id: string): void {
  const holidayDetails: HolidayDetails | undefined = publicHolidays.value.find(
    (holiday: HolidayDetails) => holiday._id === id,
  );
  if (!holidayDetails) {
    return;
  }
  openHolidayDetailsDialog(holidayDetails);
}

// sets editedHolidayDetails item when edit details dialog is open
function openHolidayDetailsDialog(item: HolidayDetails): void {
  editedHolidayDetails.value = item;
  newHolidayDate.value = item.epochStartTime;
  newHolidayName.value = item.holidayName;
  nextTick(() => {
    dialogController.value = true;
  });
}

/**
 * Sets the valid date for the Holiday Details item.
 * @param {number} epoch - The epoch time to set as the valid from date.
 */
function setDate(epoch: number): void {
  newHolidayDate.value = epoch;
}

// function to reset editedHolidayDetails when adding new holiday details
function addNewHoliday() {
  editedHolidayDetails.value = new HolidayDetails();
  newHolidayDate.value = 0;
  newHolidayName.value = '';
  dialogController.value = true;
}
</script>

<style lang="scss" scoped>
.holiday-container {
  padding-bottom: 47px;
}
</style>
