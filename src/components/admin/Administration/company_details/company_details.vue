<template>
  <div class="division-details-key-information">
    <div class="top-panel">
      <!-- <v-btn small color="error" outline @click="() => {}" depressed>
        Cancel
      </v-btn> -->
      <v-btn
        small
        color="white"
        outline
        @click="() => {}"
        depressed
        :disabled="!isEdited"
      >
        Edit
      </v-btn>
      <v-spacer />
      <span v-if="companyDetails" class="company-header">
        <div
          v-if="companyDetails.brandIdentity.logo"
          class="logo-img"
          :style="{
            backgroundImage: `url(${companyDetails.brandIdentity.logo})`,
          }"
        ></div>
        <span class="company-name">
          {{ companyDetails.name ?? 'CompanyDetails' }}
        </span>
      </span>

      <v-spacer />
      <v-btn
        small
        color="blue"
        depressed
        :disabled="!isEdited"
        @click="() => {}"
      >
        Save
      </v-btn>
    </div>

    <!-- FORM -->
    <v-layout class="client-details-form" wrap v-if="companyDetails">
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">1. Key Details</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Company Name:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              class="v-solo-custom"
              solo
              flat
              v-model.trim="companyDetails.name"
              :disabled="!isEdited"
              :rules="[validate.required]"
              autofocus
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Trading Name:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              class="v-solo-custom"
              solo
              flat
              v-model.trim="companyDetails.companyNameId"
              :disabled="!isEdited"
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Phone Number:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              solo
              flat
              class="v-solo-custom"
              v-model="companyDetails.phone"
              :rules="[validate.required, validate.numbers]"
              :disabled="!isEdited"
              label="Phone Number"
              v-mask="'## #### ####'"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Fax:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              v-model="companyDetails.fax"
              solo
              class="v-solo-custom"
              flat
              label="Fax Number"
              :disabled="!isEdited"
              :rules="[validate.numbers]"
              v-mask="'## # #### ####'"
            />
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4 class="address-label-container">
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Company Address:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <AddressSearchAU
              :boxInput="false"
              :soloInput="true"
              :address="companyDetails.address"
              :label="''"
              :formDisabled="!isEdited"
              :enableNicknamedAddress="false"
              :enableSuburbSelect="false"
              :enableReturnToDefaultDispatchAddress="false"
              :hideHeadingRow="true"
            >
            </AddressSearchAU>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12 class="pt-2 pb-2">
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-2">Company Websites:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-layout>
              <div class="company-weblinks">
                <span
                  v-for="(url, index) in companyDetails.companyWebsiteUrls"
                  :key="index"
                >
                  <span class="link-text">{{ url }}</span>
                  <span
                    v-if="index < companyDetails.companyWebsiteUrls.length - 1"
                  >
                    •
                  </span>
                </span>
              </div>
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout row wrap>
          <v-flex md12 pb-3
            ><v-layout align-center>
              <h5 class="subheader--bold pr-3 pt-1">2. PO BOX Address</h5>
              <v-flex>
                <v-divider></v-divider>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md12>
                <POBoxAddress
                  :formDisabled="!isEdited"
                  :poBoxAddress="companyDetails.poBoxAddress"
                  :validate="validate"
                >
                </POBoxAddress>
              </v-flex>
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">3. Company Divisions</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex
        md12
        v-for="(division, index) in companyDetails.divisions"
        :key="index"
        class="division-container"
      >
        <div>
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                  >
                    Name:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  v-model.trim="division.name"
                  :disabled="!isEdited"
                  :rules="[validate.required]"
                ></v-text-field>
              </v-flex>
            </v-layout>
          </v-flex>

          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                  >
                    ShortName:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  v-model.trim="division.divisionShortName"
                  :disabled="!isEdited"
                  :rules="[validate.required]"
                ></v-text-field>
              </v-flex>
            </v-layout>
          </v-flex>

          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                  >
                    State:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  v-model.trim="division.state"
                  :disabled="!isEdited"
                  :rules="[validate.required]"
                ></v-text-field>
              </v-flex>
            </v-layout>
          </v-flex>

          <v-flex md12 v-if="division.address">
            <v-layout class="pt-2">
              <v-flex md4 class="address-label-container">
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">Division Address:</h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <AddressSearchAU
                  :boxInput="false"
                  :soloInput="true"
                  :address="division.address"
                  :label="''"
                  :formDisabled="!isEdited"
                  :enableNicknamedAddress="false"
                  :enableSuburbSelect="false"
                  :enableReturnToDefaultDispatchAddress="false"
                  :hideHeadingRow="true"
                >
                </AddressSearchAU>
              </v-flex>
            </v-layout>
          </v-flex>

          <v-flex md12 v-if="division.depotAddress">
            <v-layout class="pt-2">
              <v-flex md4 class="address-label-container">
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">Depot Address:</h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <AddressSearchAU
                  :boxInput="false"
                  :soloInput="true"
                  :address="division.depotAddress"
                  :label="''"
                  :formDisabled="!isEdited"
                  :enableNicknamedAddress="false"
                  :enableSuburbSelect="false"
                  :enableReturnToDefaultDispatchAddress="false"
                  :hideHeadingRow="true"
                >
                </AddressSearchAU>
              </v-flex>
            </v-layout>
          </v-flex>

          <v-flex md6 class="billing-address-switch">
            <v-switch
              v-model="division.billingAddressIsPoBox"
              label="Billing Address Is PoBox"
              inset
              :disabled="!isEdited"
            />
          </v-flex>
          <v-flex md12 v-if="!division.billingAddressIsPoBox">
            <v-layout class="pb-2">
              <v-flex md4 class="address-label-container">
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">Billing Address:</h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <AddressSearchAU
                  :boxInput="false"
                  :soloInput="true"
                  :address="division.billingAddress ?? new AddressAU()"
                  :label="''"
                  :formDisabled="!isEdited"
                  :enableNicknamedAddress="false"
                  :enableSuburbSelect="false"
                  :enableReturnToDefaultDispatchAddress="false"
                  :hideHeadingRow="true"
                >
                </AddressSearchAU>
              </v-flex>
            </v-layout>
          </v-flex>

          <v-flex md12>
            <v-layout class="pt-2">
              <v-flex md4 class="address-label-container">
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">Po Box:</h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <POBoxAddress
                  :formDisabled="!isEdited"
                  :poBoxAddress="division.poBoxAddress"
                  :validate="validate"
                />
              </v-flex>
            </v-layout>
          </v-flex>

          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                  >
                    ABN:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  v-model.trim="division.abn"
                  :disabled="!isEdited"
                  :rules="[validate.required, validate.numbers]"
                  v-mask="'## ### ### ###'"
                ></v-text-field>
              </v-flex>
            </v-layout>
          </v-flex>

          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                  >
                    Phone:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  v-model.trim="division.phone"
                  :disabled="!isEdited"
                  :rules="[validate.required, validate.numbers]"
                  v-mask="'## #### ####'"
                ></v-text-field>
              </v-flex>
            </v-layout>
          </v-flex>

          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                  >
                    Email:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  v-model.trim="division.email"
                  :disabled="!isEdited"
                  :rules="[validate.required]"
                ></v-text-field>
              </v-flex>
            </v-layout>
          </v-flex>

          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                  >
                    Fax:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  v-model.trim="division.fax"
                  :disabled="!isEdited"
                  :rules="[validate.numbers]"
                  v-mask="'## # #### ####'"
                ></v-text-field>
              </v-flex>
            </v-layout>
          </v-flex>

          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                  >
                    Website:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  v-model.trim="division.website"
                  :disabled="!isEdited"
                  :rules="[validate.required]"
                ></v-text-field>
              </v-flex>
            </v-layout>
          </v-flex>

          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                  >
                    Timezone:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  v-model.trim="division.timezone"
                  :disabled="!isEdited"
                  :rules="[validate.required]"
                ></v-text-field>
              </v-flex>
            </v-layout>
          </v-flex>
        </div>

        <div>
          <v-flex md12 pb-3
            ><v-layout align-center>
              <h5 class="subheader--bold pr-3 pt-1">4. Banking Details</h5>
              <v-flex>
                <v-divider></v-divider>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                  >
                    Financial Institution Name:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  v-model.trim="division.bankDetails.institutionFullName"
                  :disabled="!isEdited"
                  :rules="[validate.required]"
                ></v-text-field>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                  >
                    Account Name:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  v-model.trim="division.bankDetails.accountName"
                  :disabled="!isEdited"
                  :rules="[validate.required]"
                ></v-text-field>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                  >
                    Account Number:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  v-model.trim="division.bankDetails.accountNumber"
                  :disabled="!isEdited"
                  :rules="[validate.required]"
                ></v-text-field>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                  >
                    Account BSB:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  v-model.trim="division.bankDetails.bsbNumber"
                  :disabled="!isEdited"
                  :rules="[validate.required]"
                ></v-text-field>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                  >
                    Accounts Email:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  v-model.trim="division.accountsEmailAddress"
                  :disabled="!isEdited"
                  :rules="[validate.required]"
                ></v-text-field>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                  >
                    Accounts Receivable Email:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  v-model.trim="division.accountsReceivableEmail"
                  :disabled="!isEdited"
                  :rules="[validate.required]"
                ></v-text-field>
              </v-flex>
            </v-layout>
          </v-flex>
        </div>

        <!-- Division ADMIN SECTION -->
        <v-flex v-if="isAdmin">
          <v-flex md12 pb-3
            ><v-layout align-center>
              <h5 class="subheader--bold pr-3 pt-1">
                5. Division Custom Config
              </h5>
              <v-flex>
                <v-divider></v-divider>
              </v-flex>
              <span class="admin-txt">ADMIN</span>
            </v-layout>
          </v-flex>

          <v-layout row class="tab-item-container">
            <div
              v-for="tab in tabItems"
              :key="tab.id"
              class="tab-item"
              :class="{
                active:
                  !!selectedTabController &&
                  selectedTabController.id === tab.id,
              }"
              @click="selectedTabController = tab"
            >
              {{ tab.name }}
            </div>
          </v-layout>

          <v-slide-y-transition hide-on-leave>
            <v-flex md12 :key="selectedTabController?.id">
              <!-- Tab section 1 Support Ticket  -->
              <div
                v-if="
                  division.customConfig &&
                  division.customConfig.supportTicket &&
                  division.customConfig.supportTicket.emailAlertRecipients &&
                  tabNavigationId.tabId === 'ST'
                "
              >
                <v-flex
                  md12
                  v-for="(category, key) in division.customConfig.supportTicket
                    .emailAlertRecipients"
                  :key="key"
                >
                  <div v-if="category">
                    <span class="admin-tabs">{{
                      formatCategoryLabel(key)
                    }}</span>
                    <v-select
                      v-model="category.onSubmission.roleIds"
                      :items="availableRoles"
                      hint="Select Roles"
                      item-value="id"
                      item-text="name"
                      label="Select Roles"
                      placeholder="Select Roles"
                      chips
                      multiple
                      outline
                      flat
                      solo
                      class="v-solo-custom pt-2"
                      color="Orange"
                      clearable
                      :disabled="!isEdited"
                      persistent-hint
                    >
                    </v-select>
                    <v-select
                      v-model="category.onSubmission.includeEmailAddresses"
                      :items="category.onSubmission.includeEmailAddresses"
                      label="Include Email Addresses"
                      hint="Include Email Addresses"
                      placeholder="Include Email Addresses"
                      multiple
                      chips
                      outline
                      flat
                      solo
                      class="v-solo-custom"
                      color="Blue"
                      clearable
                      :disabled="!isEdited"
                      persistent-hint
                    >
                    </v-select>
                    <v-select
                      v-model="category.onSubmission.excludeEmailAddresses"
                      :items="category.onSubmission.excludeEmailAddresses"
                      label="Exclude Email Addresses"
                      hint="Exclude Email Addresses"
                      placeholder="Exclude Email Addresses"
                      multiple
                      chips
                      outline
                      flat
                      solo
                      class="v-solo-custom"
                      color="Red"
                      clearable
                      :disabled="!isEdited"
                      persistent-hint
                    >
                    </v-select>
                    <v-divider class="ma-2"></v-divider>
                  </div>
                </v-flex>
              </div>

              <!-- Tab section 2 Invoice Adjustment-->
              <div
                v-if="
                  division.customConfig &&
                  division.customConfig.invoiceAdjustment &&
                  division.customConfig.invoiceAdjustment
                    .emailAlertRecipients &&
                  tabNavigationId.tabId === 'IA'
                "
              >
                <v-flex md12>
                  <span class="admin-tabs pb-1"> Required Approval Count </span>
                  <v-layout>
                    <v-flex md12>
                      <v-text-field
                        v-model="
                          division.customConfig.invoiceAdjustment
                            .requiredApprovalCount
                        "
                        label="Required Approval Count"
                        hint="Enter Required Approval Count"
                        placeholder="Enter Required Approval Count"
                        class="v-solo-custom pt-2"
                        clearable
                        type="number"
                        :disabled="!isEdited"
                        solo
                        flat
                        outline
                        persistent-hint
                      ></v-text-field>
                    </v-flex>
                  </v-layout>
                  <v-switch
                    v-model="
                      division.customConfig.invoiceAdjustment
                        .segregationEnforced
                    "
                    label="Segregation Enforced"
                    color="green"
                    :disabled="!isEdited"
                  />
                </v-flex>
                <v-divider class="ma-2"></v-divider>
                <v-flex
                  md12
                  v-for="(category, key) in division.customConfig
                    .invoiceAdjustment.emailAlertRecipients"
                  :key="key"
                >
                  <div v-if="category">
                    <span class="admin-tabs">{{
                      formatCategoryLabel(key)
                    }}</span>
                    <v-select
                      v-model="category.roleIds"
                      :items="availableRoles"
                      hint="Select Roles"
                      item-value="id"
                      item-text="name"
                      label="Select Roles"
                      placeholder="Select Roles"
                      chips
                      multiple
                      outline
                      flat
                      solo
                      class="v-solo-custom pt-2"
                      clearable
                      :disabled="!isEdited"
                      persistent-hint
                    >
                      <template v-slot:selection="{ item }">
                        <v-chip small class="chips">
                          <span>{{ item.name }}</span>
                        </v-chip>
                      </template>
                    </v-select>
                    <v-select
                      v-model="category.includeEmailAddresses"
                      :items="category.includeEmailAddresses"
                      label="Include Email Addresses"
                      hint="Include Email Addresses"
                      placeholder="Include Email Addresses"
                      multiple
                      chips
                      outline
                      flat
                      solo
                      class="v-solo-custom"
                      clearable
                      :disabled="!isEdited"
                      persistent-hint
                    >
                    </v-select>
                    <v-select
                      v-model="category.excludeEmailAddresses"
                      :items="category.excludeEmailAddresses"
                      label="Exclude Email Addresses"
                      hint="Exclude Email Addresses"
                      placeholder="Exclude Email Addresses"
                      multiple
                      chips
                      outline
                      flat
                      solo
                      class="v-solo-custom"
                      color="Red"
                      clearable
                      :disabled="!isEdited"
                      persistent-hint
                    >
                    </v-select>
                    <v-divider class="ma-2"></v-divider>
                  </div>
                </v-flex>
              </div>

              <!-- Tab section 3 Operations -->
              <div
                v-if="
                  division.customConfig &&
                  division.customConfig.operations &&
                  tabNavigationId.tabId === 'OP'
                "
              >
                <div>
                  <v-flex md12>
                    <span class="admin-tabs pb-1"> Pickup Load Duration </span>
                    <v-layout>
                      <v-flex md12>
                        <v-text-field
                          v-model="
                            division.customConfig.operations.pickupLoadDuration
                          "
                          label="Pickup Load Duration"
                          hint="Enter Pickup Load Duration (ms)"
                          placeholder="Enter Pickup Load Duration"
                          class="v-solo-custom pt-2"
                          clearable
                          type="number"
                          :disabled="!isEdited"
                          solo
                          flat
                          outline
                          persistent-hint
                        ></v-text-field>
                      </v-flex>
                    </v-layout>
                  </v-flex>
                  <v-divider class="ma-2"></v-divider>
                  <v-flex md12>
                    <span class="admin-tabs pb-1"> Dropoff Load Duration </span>
                    <v-layout>
                      <v-flex md12>
                        <v-text-field
                          v-model="
                            division.customConfig.operations.dropoffLoadDuration
                          "
                          label="Dropoff Load Duration"
                          hint="Enter Dropoff Load Duration (ms)"
                          placeholder="Enter Dropoff Load Duration"
                          variant="solo"
                          class="v-solo-custom pt-2"
                          clearable
                          type="number"
                          :disabled="!isEdited"
                          solo
                          flat
                          outline
                          persistent-hint
                        ></v-text-field>
                      </v-flex>
                    </v-layout>
                  </v-flex>
                  <v-divider class="ma-2"></v-divider>
                </div>
              </div>

              <!-- Tab section 4  Subscription -->
              <div
                v-if="
                  division.customConfig &&
                  division.customConfig.subscriptions &&
                  tabNavigationId.tabId === 'SB'
                "
              >
                <div>
                  <v-flex md12>
                    <span class="admin-tabs pb-1"> Select Subscriptions </span>
                    <v-layout>
                      <v-flex md12>
                        <v-switch
                          v-model="division.customConfig.subscriptions.revenue"
                          label="Revenue"
                          inset
                          color="green"
                          :disabled="!isEdited"
                        />
                        <v-flex md12>
                          <v-switch
                            v-model="
                              division.customConfig.subscriptions.expenses
                            "
                            label="Expenses"
                            inset
                            color="green"
                            :disabled="!isEdited"
                          />
                        </v-flex>
                      </v-flex>
                    </v-layout>
                    <v-divider class="ma-2"></v-divider>
                  </v-flex>
                </div>
              </div>

              <!-- Tab section 5 Driver App -->
              <div
                v-if="
                  division.customConfig &&
                  division.customConfig.driverApp &&
                  tabNavigationId.tabId === 'DA'
                "
              >
                <v-flex
                  md12
                  v-for="(category, key) in division.customConfig.driverApp"
                  :key="key"
                >
                  <div v-if="category">
                    <span class="admin-tabs pa-2">{{
                      formatCategoryLabel(key)
                    }}</span>

                    <!-- Attachments Settings -->
                    <template v-if="key === 'attachments'">
                      <v-layout row wrap>
                        <v-flex
                          xs11
                          sm6
                          v-for="(platformConfig, platform) in division
                            .customConfig.driverApp.attachments"
                          :key="platform"
                        >
                          <div class="pr-2">
                            <h6 class="subheader--faded pa-2">
                              {{ platform.toUpperCase() }}
                            </h6>

                            <v-text-field
                              v-model="platformConfig.photoQuality"
                              label="Photo Quality"
                              hint="Photo Quality"
                              type="number"
                              solo
                              flat
                              outline
                              class="v-solo-custom pt-2"
                              color="blue"
                              clearable
                              :disabled="!isEdited"
                              persistent-hint
                            />

                            <v-text-field
                              v-model="platformConfig.documentQuality"
                              label="Document Quality"
                              hint="Document Quality"
                              type="number"
                              solo
                              flat
                              outline
                              class="v-solo-custom"
                              clearable
                              :disabled="!isEdited"
                              persistent-hint
                            />

                            <v-text-field
                              v-model="platformConfig.photoScale"
                              label="Photo Scale (%)"
                              hint="Photo Scale (%)"
                              type="number"
                              solo
                              flat
                              outline
                              class="v-solo-custom"
                              clearable
                              :disabled="!isEdited"
                              persistent-hint
                            />

                            <v-text-field
                              v-model="platformConfig.documentScale"
                              label="Document Scale (%)"
                              hint="Document Scale (%)"
                              type="number"
                              solo
                              flat
                              outline
                              class="v-solo-custom"
                              clearable
                              :disabled="!isEdited"
                              persistent-hint
                            />

                            <v-text-field
                              v-model="platformConfig.daysToPersist"
                              label="Days to Persist"
                              hint="Days to Persist"
                              type="number"
                              solo
                              flat
                              outline
                              class="v-solo-custom"
                              clearable
                              :disabled="!isEdited"
                              persistent-hint
                            />

                            <v-divider class="ma-2"></v-divider>
                          </div>
                        </v-flex>
                      </v-layout>
                    </template>

                    <!-- GPS Settings -->
                    <template v-if="key === 'gps'">
                      <v-layout row wrap>
                        <v-flex
                          xs11
                          sm6
                          v-for="(platformConfig, platform) in division
                            .customConfig.driverApp.gps"
                          :key="platform"
                        >
                          <div class="pr-2">
                            <h6 class="subheader--faded pa-2">
                              {{ platform.toUpperCase() }}
                            </h6>

                            <v-text-field
                              v-model="platformConfig.distanceFilter"
                              label="Distance Filter (m)"
                              hint="Distance Filter (m)"
                              type="number"
                              solo
                              flat
                              outline
                              class="v-solo-custom pt-2"
                              clearable
                              :disabled="!isEdited"
                              persistent-hint
                            />

                            <v-switch
                              v-model="
                                platformConfig.showLocationServicesAlerts
                              "
                              label="Show Location Services Alerts"
                              color="green"
                              :disabled="!isEdited"
                            />

                            <v-divider class="ma-2"></v-divider>
                          </div>
                        </v-flex>
                      </v-layout>
                    </template>

                    <!-- Device Tasks -->
                    <template v-if="key === 'deviceTasks'">
                      <v-layout row wrap>
                        <v-flex
                          xs11
                          sm6
                          v-for="(platformConfig, platform) in division
                            .customConfig.driverApp.deviceTasks"
                          :key="platform"
                        >
                          <div class="pr-2">
                            <h6 class="subheader--faded pa-2">
                              {{ platform.toUpperCase() }}
                            </h6>

                            <v-switch
                              v-model="platformConfig.saveSnapshotOnStartDay"
                              label="Save Snapshot on Start Day"
                              color="green"
                              :disabled="!isEdited"
                            />

                            <v-text-field
                              v-model="platformConfig.attachmentUploadSeconds"
                              label="Attachment Upload Interval (sec)"
                              hint="Attachment Upload Interval (sec)"
                              type="number"
                              solo
                              outline
                              flat
                              class="v-solo-custom"
                              persistent-hint
                              clearable
                              :disabled="!isEdited"
                            />

                            <v-text-field
                              v-model="platformConfig.gpsUploadSeconds"
                              label="GPS Upload Interval (sec)"
                              hint="GPS Upload Interval (sec)"
                              type="number"
                              solo
                              flat
                              outline
                              class="v-solo-custom"
                              clearable
                              :disabled="!isEdited"
                              persistent-hint
                            />

                            <v-text-field
                              v-model="platformConfig.mqttQueueUploadSeconds"
                              label="MQTT Queue Upload Interval (sec)"
                              hint="MQTT Queue Upload Interval (sec)"
                              type="number"
                              solo
                              flat
                              outline
                              class="v-solo-custom"
                              clearable
                              :disabled="!isEdited"
                              persistent-hint
                            />

                            <v-text-field
                              v-model="platformConfig.healthCheckSeconds"
                              label="Health Check Interval (sec)"
                              hint="Health Check Interval (sec)"
                              type="number"
                              solo
                              flat
                              outline
                              class="v-solo-custom"
                              clearable
                              :disabled="!isEdited"
                              persistent-hint
                            />

                            <v-divider class="ma-2"></v-divider>
                          </div>
                        </v-flex>
                      </v-layout>
                    </template>

                    <!-- Pay Visibility -->
                    <template v-if="key === 'payVisibility'">
                      <v-layout row wrap>
                        <v-flex
                          xs11
                          sm6
                          v-for="(visibilityConfig, visibilityKey) in division
                            .customConfig.driverApp.payVisibility"
                          :key="visibilityKey"
                        >
                          <div class="pr-2">
                            <h6 class="subheader--faded pa-2">
                              {{ formatCategoryLabel(visibilityKey) }}
                            </h6>

                            <v-select
                              v-model="visibilityConfig.visibleTo"
                              :items="['ALL', 'NONE', 'OWNER DRIVERS ONLY']"
                              label="Visibility"
                              hint="Visibility"
                              solo
                              flat
                              outline
                              class="v-solo-custom pt-2"
                              clearable
                              :disabled="!isEdited"
                              persistent-hint
                            />

                            <v-select
                              v-model="visibilityConfig.displayAtStatuses"
                              :items="allWorkStatuses"
                              item-value="value"
                              item-text="text"
                              label="Display at Statuses"
                              hint="Select work statuses"
                              solo
                              flat
                              outlined
                              class="v-solo-custom"
                              clearable
                              multiple
                              chips
                              :disabled="!isEdited"
                              persistent-hint
                            />

                            <v-divider class="ma-2"></v-divider>
                          </div>
                        </v-flex>
                      </v-layout>
                    </template>
                  </div>
                </v-flex>
              </div>

              <!-- Tab section 6 truck/crane Compliance -->
              <div
                v-if="
                  division.customConfig &&
                  division.customConfig.compliance &&
                  tabNavigationId.tabId === 'CM'
                "
              >
                <v-flex md12>
                  <v-layout md12>
                    <!-- Truck Compliance Column -->
                    <v-flex md5>
                      <span class="admin-tabs">Truck Compliance</span>

                      <v-switch
                        v-model="
                          division.customConfig.compliance.truck
                            .serviceRateAlerts
                        "
                        label="Service Rate Alerts"
                        color="green"
                        :disabled="!isEdited"
                      />

                      <v-switch
                        v-model="
                          division.customConfig.compliance.truck
                            .fuelSurchargeAlerts
                        "
                        label="Fuel Surcharge Alerts"
                        color="green"
                        :disabled="!isEdited"
                      />

                      <v-switch
                        v-model="
                          division.customConfig.compliance.truck
                            .registrationAlerts
                        "
                        label="Registration Alerts"
                        color="green"
                        :disabled="!isEdited"
                      />

                      <v-switch
                        v-model="
                          division.customConfig.compliance.truck.insuranceAlerts
                        "
                        label="Insurance Alerts"
                        color="green"
                        :disabled="!isEdited"
                      />

                      <v-switch
                        v-model="
                          division.customConfig.compliance.truck
                            .additionalEquipmentAlerts
                        "
                        label="Additional Equipment Alerts"
                        color="green"
                        :disabled="!isEdited"
                      />
                    </v-flex>

                    <v-flex md2>
                      <v-divider vertical></v-divider>
                    </v-flex>

                    <!-- Crane Compliance Column -->
                    <v-flex md5>
                      <span class="admin-tabs">Crane Compliance</span>

                      <v-switch
                        v-model="
                          division.customConfig.compliance.crane
                            .requiredInspections.minor
                        "
                        label="Minor Inspection Required"
                        color="green"
                        :disabled="!isEdited"
                      />

                      <h6 class="subheader--faded">Major Inspection</h6>

                      <v-switch
                        v-model="
                          division.customConfig.compliance.crane
                            .requiredInspections.major.lessThanTenMt
                        "
                        label="Major Inspection (< 10MT)"
                        color="green"
                        :disabled="!isEdited"
                      />

                      <v-switch
                        v-model="
                          division.customConfig.compliance.crane
                            .requiredInspections.major.tenMtPlus
                        "
                        label="Major Inspection (10MT+)"
                        color="green"
                        :disabled="!isEdited"
                      />
                    </v-flex>
                  </v-layout>
                </v-flex>
              </div>

              <!-- Tab section 7 Quote Preference -->
              <div
                v-if="
                  division.customConfig &&
                  division.customConfig.quotePreferences &&
                  tabNavigationId.tabId === 'QP'
                "
              >
                <v-flex md12>
                  <div>
                    <span class="admin-tabs">Quote Preferences</span>

                    <v-text-field
                      v-model="
                        division.customConfig.quotePreferences
                          .quoteValidityPeriod
                      "
                      label="Quote Validity Period (ms)"
                      hint="Quote Validity Period (ms)"
                      type="number"
                      solo
                      flat
                      outline
                      class="v-solo-custom pt-2"
                      clearable
                      :disabled="!isEdited"
                      persistent-hint
                    />

                    <v-text-field
                      v-model="
                        division.customConfig.quotePreferences.quoteGracePeriod
                      "
                      label="Quote Grace Period (ms)"
                      hint="Quote Grace Period (ms)"
                      type="number"
                      solo
                      flat
                      outline
                      persistent-hint
                      class="v-solo-custom"
                      clearable
                      :disabled="!isEdited"
                    />

                    <v-text-field
                      v-model="
                        division.customConfig.quotePreferences.quotePurgePeriod
                      "
                      label="Quote Purge Period (ms)"
                      hint="Quote Purge Period (ms)"
                      type="number"
                      solo
                      flat
                      outline
                      persistent-hint
                      class="v-solo-custom"
                      clearable
                      :disabled="!isEdited"
                    />

                    <v-switch
                      v-model="
                        division.customConfig.quotePreferences
                          .allowQuoteValidityOverride
                      "
                      label="Allow Quote Validity Override"
                      color="green"
                      :disabled="!isEdited"
                    />
                  </div>
                </v-flex>
                <v-divider class="ma-2"></v-divider>
              </div>
              <!-- Tab section 8 Report Preference -->
              <div
                v-if="
                  division.customConfig &&
                  division.customConfig.reports &&
                  tabNavigationId.tabId === 'RP'
                "
              >
                <v-flex md12>
                  <div>
                    <span class="admin-tabs">Report Preferences</span>

                    <v-select
                      v-model="
                        division.customConfig.reports.allowedAccessMethods
                      "
                      :items="reportAccessOptions"
                      item-value="value"
                      item-text="text"
                      label="Report Allowed Access Methods"
                      hint="Select Report Access Methods"
                      solo
                      flat
                      outlined
                      class="v-solo-custom pt-2"
                      multiple
                      chips
                      :disabled="!isEdited"
                      persistent-hint
                    />
                  </div>
                </v-flex>
                <v-divider class="ma-2"></v-divider>
              </div>
            </v-flex>
          </v-slide-y-transition>
        </v-flex>
      </v-flex>

      <!-- Company ADMIN SECTION -->
      <div v-if="isAdmin">
        <v-flex md12 pb-3
          ><v-layout align-center>
            <h5 class="subheader--bold pr-3 pt-1">6.Company Custom Config</h5>
            <v-flex>
              <v-divider></v-divider>
            </v-flex>
            <span class="admin-txt">ADMIN</span>
            <span>
              <v-switch
                v-model="companyDetails.customConfig.isBranded"
                label="Is Branded"
                inset
                :disabled="!isEdited"
              />
            </span>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <h6 class="admin-tabs pt-2">Incrementation</h6>
          <v-layout>
            <v-flex
              md12
              v-for="(value, key) in companyDetails.customConfig
                .idIncrementation"
              :key="key"
              class="pb-1 pt-1"
            >
              <h6>{{ formatCategoryLabel(key) }}</h6>
              <v-select
                flat
                outline
                solo
                class="v-solo-custom pr-2"
                :label="`${formatCategoryLabel(key)}`"
                :hint="`${formatCategoryLabel(key)}`"
                v-model="companyDetails.customConfig.idIncrementation[key]"
                :items="['COMPANY', 'DIVISION']"
                single-line
                :disabled="!isEdited"
              />
            </v-flex>
          </v-layout>
        </v-flex>
        <v-divider class="ma-2"></v-divider>

        <div
          v-if="
            companyDetails.customConfig &&
            companyDetails.customConfig.emailAlertRecipients
          "
        >
          <v-flex
            md12
            v-for="(category, key) in companyDetails.customConfig
              .emailAlertRecipients"
            :key="key"
          >
            <div v-if="category">
              <span class="admin-tabs">{{ formatCategoryLabel(key) }}</span>
              <v-select
                v-model="category.roleIds"
                :items="availableRoles"
                hint="Select Roles"
                item-value="id"
                item-text="name"
                label="Select Roles"
                placeholder="Select Roles"
                chips
                multiple
                outline
                flat
                solo
                class="v-solo-custom pt-2"
                clearable
                :disabled="!isEdited"
                persistent-hint
              >
                <template v-slot:selection="{ item }">
                  <v-chip small class="chips">
                    <span>{{ item.name }}</span>
                  </v-chip>
                </template>
              </v-select>
              <v-select
                v-model="category.includeEmailAddresses"
                :items="category.includeEmailAddresses"
                label="Include Email Addresses"
                hint="Include Email Addresses"
                placeholder="Include Email Addresses"
                multiple
                outline
                flat
                solo
                class="v-solo-custom"
                clearable
                :disabled="!isEdited"
                persistent-hint
              >
              </v-select>
              <v-select
                v-model="category.excludeEmailAddresses"
                :items="category.excludeEmailAddresses"
                label="Exclude Email Addresses"
                hint="Exclude Email Addresses"
                placeholder="Exclude Email Addresses"
                multiple
                outline
                flat
                solo
                class="v-solo-custom"
                clearable
                :disabled="!isEdited"
                persistent-hint
              >
              </v-select>
            </div>
            <v-divider class="ma-2"></v-divider>
          </v-flex>
        </div>
      </div>
    </v-layout>
  </div>
</template>
<script setup lang="ts">
import AddressSearchAU from '@/components/common/addressing/address-search-au/index.vue';
import POBoxAddress from '@/components/common/addressing/po-box-au/index.vue';
import { hasAdminRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import CompanyDetails from '@/interface-models/Company/CompanyDetails';
import AddressAU from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/AddressAU.ts';
import { Validation } from '@/interface-models/Generic/Validation';
import {
  getEventForWorkStatus,
  WorkStatus,
} from '@/interface-models/Jobs/WorkStatus';
import UserRole from '@/interface-models/Roles/UserRoles';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useRootStore } from '@/store/modules/RootStore';
import { computed, ComputedRef, ref, Ref } from 'vue';

interface TabItem {
  id: string;
  name: string;
}

const isEdited: Ref<boolean> = ref(false);
const validate: Validation = validationRules;
const CompanyDetailsStore = useCompanyDetailsStore();
const tabNavigationId = ref({ tabId: 'ST' });

const tabItems: Ref<TabItem[]> = ref([
  {
    id: 'ST',
    name: 'Support Ticket',
  },
  {
    id: 'IA',
    name: 'Invoice Adjustment',
  },
  {
    id: 'OP',
    name: 'Operations',
  },
  {
    id: 'SB',
    name: 'Subscription',
  },
  {
    id: 'DA',
    name: 'Driver App',
  },
  {
    id: 'CM',
    name: 'Compliance',
  },
  {
    id: 'QP',
    name: 'Quote Preference',
  },
  {
    id: 'RP',
    name: 'Report Preference',
  },
]);

/**
 * Modelled to tabs in HTML, which contain a list of entity types. Used to
 * filter the expiration summary type buttons on the left side.
 */
const selectedTabController = computed<TabItem | undefined>({
  get: () => tabItems.value.find((t) => t.id === tabNavigationId.value.tabId),
  set: (value) => {
    if (!value) {
      return;
    }
    tabNavigationId.value.tabId = value.id;
  },
});

// get company details object from store
const companyDetails: ComputedRef<CompanyDetails | null> = computed(
  () => CompanyDetailsStore.companyDetails,
);

// fetch role list from store
const rolesList: UserRole[] = useRootStore().roleList;

// map role list with id and name for v-select
const availableRoles = rolesList.map((role) => ({
  id: role.roleId,
  name: role.name,
}));

// computed to check if user has admin role for admin sections
const isAdmin = computed(() => hasAdminRole());

// Format label for custom config Incrementation
function formatCategoryLabel(key: string) {
  // Convert camelCase to readable labels
  return key
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, (str) => str.toUpperCase());
}

// get list of all work status and names for v-select
const allWorkStatuses = computed(() =>
  Object.values(WorkStatus)
    .filter((value) => typeof value === 'number') // Exclude function names
    .map((value) => ({
      value,
      text: getEventForWorkStatus(value as WorkStatus),
    })),
);

// get list of allowedAccessMethods for v-select
const reportAccessOptions = [
  { value: 'EMAIL', text: 'Email' },
  { value: 'DOWNLOAD', text: 'Download' },
];
</script>

<style scoped lang="scss">
.company-weblinks {
  padding-top: 4px;
  .link-text {
    padding: 2px;
    font-weight: 500;
    font-size: $font-size-18;
    color: var(--light-text-color);
  }
}

.admin-tabs {
  margin-top: 18px !important;
  margin-bottom: 18px !important;
  font-weight: 500;
  font-size: $font-size-14;
  color: var(--primary) !important;
}

.company-header {
  display: flex;
  align-items: center;
  gap: 20px;
}

.logo-img {
  width: 175px;
  height: 125px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.company-name {
  font-weight: 700;
  font-size: $font-size-18;
  color: var(--light-text-color);
}

.admin-txt {
  padding: 0 8px;
  margin: 0 12px;
  color: $warning;
  border: 1px solid $warning;
  border-radius: 10px;
}

.tab-item-container {
  margin-bottom: 34px;
  margin-top: 14px;
  position: relative;
  border: 2px solid $translucent;
  border-radius: 14px;
  max-width: 100%; // Make sure it can scale
  overflow-x: auto; // Allow horizontal scroll on smaller screens
  display: flex;
  flex-wrap: wrap; // Allow wrapping

  .tab-item {
    padding: 12px 8px;
    margin: 2px;
    font-size: $font-size-13;
    font-weight: 500;
    color: var(--light-text-color);
    transition: 0.3s;
    border-radius: $border-radius-base;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
    min-width: fit-content;
    flex: 1 1 auto;
    max-width: 100%;

    &:hover {
      color: $warning;
      cursor: pointer;
      background-color: $detail-item-value-bg;
      box-shadow: var(--box-shadow);
    }

    &.active {
      color: $warning;
      background-color: $detail-item-value-bg;
      text-decoration: underline;
      text-decoration-thickness: 2px;
      text-underline-offset: 12px;
      font-weight: 600;
      box-shadow: $shadow-primary;
    }
  }
}

.billing-address-switch {
  margin-left: 25%;
  padding: 10px;
  position: relative;
}
</style>
