<template>
  <div class="subcontractor_index_container">
    <v-layout wrap class="pl-2 py-2">
      <v-flex>
        <v-form>
          <SelectEntity
            :isRouteSelect="true"
            :entityTypes="[entityType.CLIENT]"
          />
        </v-form>
      </v-flex>
      <v-tooltip left>
        <template v-slot:activator="{ on }">
          <v-btn flat icon v-on="on" @click="addNewClient" class="add-btn">
            <v-icon size="22">far fa-plus</v-icon>
          </v-btn>
        </template>
        <span>Create New Client</span>
      </v-tooltip>
    </v-layout>

    <AdministrationDashboard category="CLIENT"></AdministrationDashboard>
  </div>
</template>

<script setup lang="ts">
import AdministrationDashboard from '@/components/admin/SubcontractorIndex/components/administration_dashboard/index.vue';
import SelectEntity from '@/components/common/inputs/select_entity/index.vue';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { useRouter } from 'vue-router/composables';

const router = useRouter();

const entityType = EntityType;

/**
 * Navigates to the Client Details page and initiates the process of creating
 * a new client.
 */
function addNewClient() {
  router.push({
    name: 'Client Details',
    params: {
      name: 'create',
      id: 'new',
    },
  });
}
</script>

<style scoped lang="scss">
.add-btn {
  border-radius: 50px !important;
  border: 1px solid var(--primary-dark) !important;
  background-color: var(--primary) !important;
}
</style>
