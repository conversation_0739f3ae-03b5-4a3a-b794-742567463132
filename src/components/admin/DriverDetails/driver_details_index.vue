<template>
  <v-layout wrap>
    <v-flex md12 v-if="driverDetails">
      <v-layout row wrap>
        <v-flex md12 v-if="isDialog">
          <v-layout
            justify-space-between
            class="task-bar app-theme__center-content--header no-highlight"
          >
            <span v-if="driverDetails && driverDetails._id">{{
              driverDetails.displayName
            }}</span>
            <span v-if="driverDetails && !driverDetails._id">New Driver</span>
            <div
              class="app-theme__center-content--closebutton"
              :class="{ 'disable-pointer-events': isEdited && !isNewDriver }"
              @click="closeDriverDetailsDialog"
            >
              <v-icon
                :disabled="isEdited && !isNewDriver"
                class="app-theme__center-content--closebutton--icon"
                >fal fa-times</v-icon
              >
            </div>
          </v-layout>
        </v-flex>

        <v-flex md12>
          <v-layout
            class="app-theme__center-content--body dialog-content"
            row
            wrap
            :class="isDialog ? 'main-content-dialog' : 'main-content-route'"
          >
            <v-flex
              lg3
              md4
              class="side-column app-bgcolor--400 app-bordercolor--600 app-borderside--r-shadow"
            >
              <v-layout row wrap>
                <v-flex
                  md12
                  class="app-bgcolor--250 app-bordercolor--600 app-borderside--b"
                >
                  <v-layout row wrap py-2 px-2>
                    <v-flex md12>
                      <v-form>
                        <SelectEntity
                          :key="driverDetails.driverId"
                          :entityTypes="[
                            entityType.FLEET_ASSET_OWNER,
                            entityType.FLEET_ASSET,
                            entityType.DRIVER,
                          ]"
                          v-if="!isDialog"
                          :isRouteSelect="true"
                          :disabled="isEdited || !driverDetails._id"
                          class="mb-3"
                        />
                      </v-form>
                    </v-flex>

                    <v-flex md12 class="side-column__summaryitem">
                      <v-tooltip right v-if="!driverDetails.isGenericDriver">
                        <template v-slot:activator="{ on }">
                          <v-layout
                            v-on="on"
                            justify-space-between
                            align-start
                            @click="roleStatusDialogIsOpen = true"
                            style="cursor: pointer"
                          >
                            <span class="side-column__summaryitem--key">
                              Account Status
                            </span>
                            <GButton
                              xsmall
                              :outlined="true"
                              :color="
                                accountStatus.statusName === 'PENDING'
                                  ? 'info'
                                  : accountStatus.statusName === 'LOCKED'
                                    ? 'error'
                                    : 'success'
                              "
                            >
                              {{ accountStatus.statusName }}
                            </GButton>
                          </v-layout>
                        </template>

                        Update Drivers account status
                      </v-tooltip>

                      <GDialog
                        :width="'500px'"
                        :title="'Role Status Management'"
                        :confirmBtnText="
                          'Confirm and ' +
                          (accountStatus.statusName === 'LOCKED'
                            ? 'unlock'
                            : 'lock')
                        "
                        :isDelete="false"
                        :confirmDisabled="false"
                        :isLoading="false"
                        @closeDialog="closeRoleUpdateDialog"
                        @confirm="sendUpdateRoleRequest"
                        v-if="roleStatusDialogIsOpen"
                      >
                        <div class="pa-3">
                          <p>
                            Please verify the associated user and role update
                            details below:
                          </p>
                          <ul>
                            <li>
                              User:
                              <strong>{{ driverDetails.displayName }}</strong>
                            </li>
                            <li>
                              Role:
                              <strong>Driver</strong>
                            </li>
                            <li>
                              Division:
                              <strong> {{ driverDetails.division }}</strong>
                            </li>

                            <li>
                              Current Status:
                              <strong>{{ accountStatus.statusName }}</strong>
                            </li>

                            <li>
                              Updated Status:
                              <strong>{{
                                accountStatus.statusName === 'LOCKED'
                                  ? 'ACTIVE'
                                  : 'LOCKED'
                              }}</strong>
                            </li>
                          </ul>
                          <p class="mt-3">
                            Should the details be accurate, you may proceed with
                            <strong>{{
                              accountStatus.statusName === 'LOCKED'
                                ? 'unlocking'
                                : 'locking'
                            }}</strong>
                            this users driver role.
                          </p>
                        </div>
                      </GDialog>
                    </v-flex>

                    <v-flex md12 class="side-column__summaryitem">
                      <v-layout justify-space-between align-start>
                        <span class="side-column__summaryitem--key">
                          Operational Status
                        </span>
                        <span
                          class="side-column__summaryitem--value status-container"
                          :class="operationsStatus.color"
                        >
                          {{ operationsStatus.statusName }}
                        </span>
                      </v-layout>
                    </v-flex>

                    <v-flex
                      md12
                      class="side-column__summaryitem"
                      v-if="summaryInfoList"
                    >
                      <v-layout
                        v-for="infoItem in summaryInfoList"
                        :key="infoItem.id"
                        justify-space-between
                        align-start
                      >
                        <span class="side-column__summaryitem--key">
                          {{ infoItem.title }}
                          <span
                            class="pl-1"
                            v-if="infoItem.id === 'subcontractorName'"
                          >
                            <InformationTooltip
                              :bottom="true"
                              :tooltipType="HealthLevel.INFO"
                            >
                              <v-layout slot="content" row wrap>
                                <v-flex md12>
                                  <p class="mb-1">
                                    This is a
                                    <strong>Generic Driver</strong> for the
                                    following Fleet Asset Owner.
                                  </p>
                                  <span class="mb-0">
                                    Please note:
                                    <ul>
                                      <li>
                                        Generic Drivers not subject to
                                        compliance checks (Licence and
                                        Induction)
                                      </li>
                                      <li>
                                        Generic Drivers not use the mobile app.
                                        Jobs performed by these drivers must be
                                        manually Started and Completed.
                                      </li>
                                      <li>
                                        Generic Drivers cannot be associated to
                                        new Owners, and cannot be Dissociated
                                        from their existing Owners.
                                      </li>
                                    </ul>
                                  </span>
                                </v-flex>
                              </v-layout>
                            </InformationTooltip>
                          </span>
                        </span>

                        <span
                          v-if="infoItem.id !== 'subcontractorName'"
                          class="side-column__summaryitem--value"
                        >
                          {{ infoItem.value }}
                        </span>

                        <span
                          v-if="infoItem.id === 'subcontractorName'"
                          class="side-column__summaryitem--value"
                          @click="viewFleetAssetOwner"
                          style="cursor: pointer; color: #2196f3"
                        >
                          <b>{{ infoItem.value }}</b>
                        </span>
                      </v-layout>
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex md12 class="app-bordercolor--600 app-borderside--b">
                  <v-layout>
                    <h5 class="subheader--bold--12 px-3 pt-3 pb-1">
                      Driver Info
                    </h5>
                  </v-layout>
                </v-flex>
                <v-flex
                  md12
                  v-for="menuItem in filteredMenuOptions"
                  :key="menuItem.id"
                  class="app-bgcolor--300 app-bordercolor--600 app-borderside--b side-column__button"
                  :class="[
                    selectedViewType === menuItem.id ? 'active-state' : '',
                    menuItem.isActive
                      ? 'menu-item-selectable'
                      : 'menu-item-disabled',
                  ]"
                  @click="setSelectedView(menuItem.id)"
                >
                  <v-layout justify-space-between align-center>
                    <span class="button-label">
                      <span class="pr-2">-</span>{{ menuItem.title }}
                    </span>
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex lg9 md8 class="dialog-content__scrollable">
              <v-layout
                class="dialog-toolbar center-section__top-toolbar"
                style="position: relative"
                align-center
              >
                <v-btn
                  depressed
                  v-if="isEdited"
                  @click="findAndSetSelectedDriverDetails(true)"
                  outline
                  color="error"
                  small
                >
                  Cancel
                </v-btn>

                <v-btn
                  depressed
                  v-if="!isEdited && !isDialog"
                  @click="exitDriver"
                  outline
                  color="error"
                  small
                >
                  exit
                </v-btn>
                <v-spacer />
                <v-btn
                  :disabled="!isAuthorised()"
                  depressed
                  color="blue"
                  small
                  v-if="!isEdited && selectedView?.isEditable"
                  @click="setEdited(true)"
                  >Edit
                </v-btn>
                <v-btn
                  v-if="isEdited && isAuthorised()"
                  depressed
                  color="blue"
                  :loading="awaitingDriverSaveResponse"
                  small
                  :disabled="awaitingDriverSaveResponse"
                  @click="saveDriverDetails"
                >
                  Save
                </v-btn>
              </v-layout>

              <v-layout
                class="scrollable"
                :key="driverDetails ? driverDetails.driverId : 'new'"
              >
                <v-form
                  ref="driverForm"
                  style="width: 100%"
                  v-if="selectedView?.isForm"
                >
                  <v-layout fill-height>
                    <v-flex lg8 md10 offset-lg2 offset-md1 class="pt-3">
                      <DriverDetailsPersonal
                        v-if="selectedViewType === 'PER'"
                        :isEdited="isEdited"
                        :driverDetails="driverDetails"
                        :canEditUsername="canEditUsername"
                        class="px-3"
                      />
                      <DriverDetailsVisa
                        v-if="selectedViewType === 'VIS'"
                        :isEdited="isEdited"
                        :visaDetails="driverDetails.visaRequirements"
                      />
                    </v-flex>
                  </v-layout>
                </v-form>
                <v-flex md12 v-if="!selectedView?.isForm" class="pa-3">
                  <DriverDetailsLicence
                    :driverDetails="driverDetails"
                    v-if="selectedViewType === 'LIC'"
                    :licenceDetailsList="driverDetails.licenseDetailsList"
                    :isEdited="isEdited"
                  />

                  <DriverDetailsInductions
                    :driverDetails="driverDetails"
                    v-if="selectedViewType === 'IND'"
                    :inductionDetailsList="driverDetails.inductionDetailsList"
                  />

                  <DriverDetailsNextOfKin
                    :driverDetails="driverDetails"
                    v-if="selectedViewType === 'NOK'"
                    :nextOfKins="driverDetails.kin"
                  />

                  <DriverDetailsDrugAndAlcoholTesting
                    :driverDetails="driverDetails"
                    v-if="selectedViewType === 'DAA'"
                    :drugAndAlcoholTests="driverDetails.drugAndAlcoholTests"
                  />

                  <ImmunisationHistoryTable
                    :formDisabled="isEdited"
                    v-if="selectedViewType === 'IMM'"
                    :driverDetails="driverDetails"
                    :immunisationHistory="driverDetails.immunisationHistory"
                  />

                  <DriverDetailsAssociations
                    v-if="selectedViewType === 'ASS'"
                    :driverId="driverDetails.driverId"
                    :statusList="driverDetails.statusList"
                  />

                  <AssociatedClientsTable
                    v-if="selectedViewType === 'AC'"
                    :clientList="relatedClients"
                    :title="`Associated Clients for ${driverDetails.displayName}`"
                  />
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>
    <v-flex v-else class="app-theme__center-content--body" pa-3>
      <v-layout justify-center>
        <img
          src="@/static/loader/infinity-loader-light.svg"
          height="80px"
          width="80px"
        />
      </v-layout>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import DriverDetailsAssociations from '@/components/admin/DriverDetails/components/driver_details_associations.vue';
import DriverDetailsDrugAndAlcoholTesting from '@/components/admin/DriverDetails/components/driver_details_drug_and_alcohol_testing.vue';
import DriverDetailsInductions from '@/components/admin/DriverDetails/components/driver_details_inductions.vue';
import DriverDetailsLicence from '@/components/admin/DriverDetails/components/driver_details_licence.vue';
import DriverDetailsNextOfKin from '@/components/admin/DriverDetails/components/driver_details_next_of_kin.vue';
import DriverDetailsPersonal from '@/components/admin/DriverDetails/components/driver_details_personal.vue';
import DriverDetailsVisa from '@/components/admin/DriverDetails/components/driver_details_visa.vue';
import AssociatedClientsTable from '@/components/common/associated_clients_table.vue';
import ImmunisationHistoryTable from '@/components/common/immunisation_history_table/index.vue';
import SelectEntity from '@/components/common/inputs/select_entity/index.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { formatPhoneNumber } from '@/helpers/StringHelpers/StringHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validate } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { initialiseDriverDetails } from '@/helpers/classInitialisers/InitialiseDriverDetails';
import ClientSearchSummary from '@/interface-models/Client/ClientDetails/ClientSearchSummary';
import LicenseDetails from '@/interface-models/Driver/DriverDetails/AdditionalObjects/LicenseDetails';
import {
  DriverDetails,
  SaveDriverDetailsResponse,
} from '@/interface-models/Driver/DriverDetails/DriverDetails';
import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import {
  EditUsernameForUserRequest,
  EditUsernameForUserResponse,
  RoleUserType,
} from '@/interface-models/Email/EmailAlertRecipients/EditUsernameForUser.ts';
import FleetAssetOwner from '@/interface-models/FleetAssetOwner/FleetAssetOwner';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { KeyValuePair } from '@/interface-models/Generic/KeyValue/KeyValue';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import { LockUnlockRequest } from '@/interface-models/User/UpdateRoleAccess';
import { UserRoleStatus } from '@/interface-models/User/UserRoleStatus';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useUserManagementStore } from '@/store/modules/UserManagementStore';
import { useWebsocketStore } from '@/store/modules/WebsocketStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import { useMittListener } from '@/utils/useMittListener';
import {
  ComputedRef,
  Ref,
  computed,
  onBeforeMount,
  onBeforeUnmount,
  onMounted,
  ref,
  watch,
} from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';

interface StatusConfig {
  statusName: string;
  color: string;
}

interface DriverDetailsMenu {
  id: string;
  title: string;
  isForm: boolean;
  isActive: boolean;
  isEditable: boolean;
  actionBtnText: string;
  isHidden?: boolean;
}

// If fleetAssetOwner is defined, then the driver being created/edited is
// fixed to this owner, and should have restrictedToOwnerId and
// complianceOverrides set
const props = withDefaults(
  defineProps<{
    isDialog?: boolean;
    fleetAssetOwner?: FleetAssetOwner | null;
  }>(),
  {
    isDialog: false,
    fleetAssetOwner: null,
  },
);

const driverDetailsStore = useDriverDetailsStore();
const fleetAssetOwnerStore = useFleetAssetOwnerStore();
const userManagementStore = useUserManagementStore();

const router = useRouter();
const route = useRoute();
const routeId: ComputedRef<string> = computed(() => route.params.id);

const componentTitle: string = 'Driver Details Administration';

const initialEmail = ref<string | null>(null);
const initialMobile = ref<string | null>(null);

const awaitingDriverSaveResponse: Ref<boolean> = ref(false);
const isEdited: Ref<boolean> = ref(false);
const roleStatusDialogIsOpen: Ref<boolean> = ref(false);

const driverDetails: Ref<DriverDetails | null> = ref(null);

const canEditUsername: Ref<boolean> = ref(false);

const entityType: Ref<typeof EntityType> = ref(EntityType);
const driverUserRoleStatus: Ref<UserRoleStatus> = ref(UserRoleStatus.PENDING);
const selectedViewType: Ref<string> = ref('PER');

const driverForm: Ref<any> = ref(null);

const filteredMenuOptions: ComputedRef<DriverDetailsMenu[]> = computed(() => {
  return menuOptions.value.filter((menuItem) => !menuItem.isHidden);
});

const menuOptions: ComputedRef<DriverDetailsMenu[]> = computed(() => {
  if (!driverDetails.value) {
    return [];
  }
  if (!driverDetails.value.isGenericDriver) {
    return [
      {
        id: 'PER',
        title: 'Personal Details',
        isForm: true,
        isActive: isEdited.value ? false : true,
        isEditable: true,
        actionBtnText: 'Edit',
      },
      {
        id: 'ASS',
        title: 'Associations',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isEditable: false,
        actionBtnText: 'Edit',
      },
      driverDetails.value.licenceComplianceRequired
        ? {
            id: 'LIC',
            title: 'Licences',
            isForm: false,
            isActive: isEdited.value ? false : true,
            isEditable: false,
            actionBtnText: 'Edit',
          }
        : null,
      driverDetails.value.inductionComplianceRequired
        ? {
            id: 'IND',
            title: 'Inductions',
            isForm: false,
            isActive: isEdited.value ? false : true,
            isEditable: false,
            actionBtnText: 'Edit',
          }
        : null,
      {
        id: 'NOK',
        title: 'Next Of Kin',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isEditable: false,
        actionBtnText: 'Edit',
      },
      {
        id: 'DAA',
        title: 'Drug And Alcohol Testing',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isEditable: false,
        actionBtnText: 'Edit',
      },
      {
        id: 'VIS',
        title: 'VISA Details',
        isForm: true,
        isActive: isEdited.value ? false : true,
        isEditable: true,
        actionBtnText: 'Edit',
      },
      {
        id: 'IMM',
        title: 'Immunisation History',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isEditable: false,
        actionBtnText: 'Edit',
      },
      {
        id: 'AC',
        title: 'Associated Clients',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isEditable: false,
        actionBtnText: 'Edit',
      },
    ].filter((i) => i !== null) as DriverDetailsMenu[];
  } else {
    return [
      {
        id: 'PER',
        title: 'General Details',
        isForm: true,
        isActive: isEdited.value ? false : true,
        isEditable: true,
        actionBtnText: 'Edit',
      },
      driverDetails.value.licenceComplianceRequired
        ? {
            id: 'LIC',
            title: 'Licences',
            isForm: false,
            isActive: isEdited.value ? false : true,
            isEditable: false,
            actionBtnText: 'Edit',
          }
        : null,
      driverDetails.value.inductionComplianceRequired
        ? {
            id: 'IND',
            title: 'Inductions',
            isForm: false,
            isActive: isEdited.value ? false : true,
            isEditable: false,
            actionBtnText: 'Edit',
          }
        : null,
    ].filter((i) => i !== null) as DriverDetailsMenu[];
  }
});

const selectedDriverDetailsId: ComputedRef<string | null> = computed(
  () => driverDetailsStore.selectedDriverDetailsId,
);

watch(selectedDriverDetailsId, () => {
  findAndSetSelectedDriverDetails();
});

const goToSubcontractorIndexRoute = (): void => {
  router.push({
    name: 'Subcontractor',
  });
};

const isNewDriver: ComputedRef<boolean> = computed(() => {
  return routeId.value === 'new' || selectedDriverDetailsId.value === 'new';
});

// Find clients that have this driver in their preAssignedDriverIds
const relatedClients = computed(() => {
  if (!driverDetails.value?.driverId) {
    return [];
  }

  return useClientDetailsStore().clientSummaryList.filter(
    (client: ClientSearchSummary) =>
      client.preAssignedDriverIds?.includes(driverDetails.value!.driverId),
  );
});

function findAndSetSelectedDriverDetails(isCancelNewDriver: boolean = false) {
  if (!props.isDialog && selectedDriverDetailsId.value !== routeId.value) {
    // Updated value parameter here
    driverDetailsStore.setSelectedDriverDetailsId(routeId.value);
  }

  isEdited.value = false;
  // If a new driver is canceled we should push the user back to the main subcontractor index
  if (isCancelNewDriver && isNewDriver.value) {
    if (!props.isDialog) {
      goToSubcontractorIndexRoute();
    } else {
      closeDriverDetailsDialog();
    }
    return;
  }

  // Reset any validation issues on inputs.
  if (driverForm.value) {
    driverForm.value.resetValidation();
  }
  // if the driver is new we initialise a new driver.
  if (isNewDriver.value) {
    driverDetails.value = initialiseDriverDetails(new DriverDetails());
    setInitialUsernameValues(driverDetails.value);
    driverUserRoleStatus.value = UserRoleStatus.PENDING;
    isEdited.value = true;

    // If fleetAssetOwner is defined and is Outside Hire type then it means we
    // are creating a GENERIC driver.
    // TODO: Implementation for normal generic drivers for non-outside hire
    if (props.fleetAssetOwner && props.fleetAssetOwner?.isOutsideHire) {
      // Lock driver to owner's mongo id
      driverDetails.value.restrictedToOwnerId = props.fleetAssetOwner._id;
      driverDetails.value.complianceOverrides = {
        usesMobileApp: false,
        licenceRequired: false,
        inductionRequired: false,
      };
    }
    return;
  }

  // Confirm that the selected owner is known locally
  const driverDetailsSummary: DriverDetailsSummary | undefined =
    driverDetailsStore.getDriverFromDriverId(selectedDriverDetailsId.value); // Updated value parameter here
  if (!driverDetailsSummary) {
    handleUnknownDriver();
    return;
  }

  // Request full Fleet asset owner object
  if (selectedDriverDetailsId.value) {
    getDriverWithAuthDetails(selectedDriverDetailsId.value);
  }
}

// If the selected driver was not found, we should alert and push the user back to the subcontractor index page.
function handleUnknownDriver(): void {
  showAppNotification('Sorry, we could not find that Driver.');
  driverDetails.value = null;
  driverDetailsStore.setSelectedDriverDetailsId(null);
  closeDriverDetailsDialog();
  goToSubcontractorIndexRoute();
}

/**
 * Initiates a request to retrieve driver details along with authentication details for a given driver ID.
 * This method sends a websocket request for the driver's details and listens for a response event.
 * Once the details are received, it processes and stores the driver details and user role status.
 * If the driver details are not found or the driver ID is invalid, it notifies the user and clears the current driver details.
 *
 * @param {string} driverId - The unique identifier for the driver whose details are to be retrieved.
 */
async function getDriverWithAuthDetails(driverId: string): Promise<void> {
  const driverWithAuthDetail =
    await userManagementStore.requestDriverWithAuthDetails(driverId);
  // If response is invalid, show error notification and clear driver details
  if (!driverWithAuthDetail?.driver?.driverId) {
    handleUnknownDriver();
    return;
  }
  driverDetails.value = initialiseDriverDetails(driverWithAuthDetail.driver);
  setInitialUsernameValues(driverDetails.value);
  canEditUsername.value = driverWithAuthDetail.canEditUsername;
  driverUserRoleStatus.value = driverWithAuthDetail.status;
}

function closeDriverDetailsDialog(): void {
  driverDetailsStore.setSelectedDriverDetailsId(null);
}

// Set the selected view from the left navigation menu
const setSelectedView = (id: string): void => {
  if (!id) {
    selectedViewType.value = menuOptions.value[0].id;
    return;
  }
  selectedViewType.value = id;
};

const selectedView: ComputedRef<DriverDetailsMenu | null> = computed(() => {
  const view = menuOptions.value.find(
    (x: DriverDetailsMenu) => x.id === selectedViewType.value,
  );
  return view ?? null;
});

// Data List to be displayed in the top left of
const summaryInfoList: ComputedRef<KeyValuePair[]> = computed(() => {
  const infoList: KeyValuePair[] = [];

  if (!driverDetails.value) {
    return infoList;
  }

  const driverLicence = driverDetails.value.licenseDetailsList.find(
    (x: LicenseDetails) => x.licenseType === 1,
  );

  const highRiskLicence = driverDetails.value.licenseDetailsList.find(
    (x: LicenseDetails) => x.licenseType === 2,
  );

  const whiteCardLicence = driverDetails.value.licenseDetailsList.find(
    (x: LicenseDetails) => x.licenseType === 3,
  );

  if (!driverDetails.value.isGenericDriver) {
    infoList.push({
      id: 'mobile',
      title: 'Mobile',
      value: driverDetails.value.mobile
        ? formatPhoneNumber(driverDetails.value.mobile)
        : '-',
    });
    infoList.push({
      id: 'licenceId',
      title: 'Licence #',
      value: driverLicence ? driverLicence.licenseId : '-',
    });

    infoList.push({
      id: 'highRiskLicence',
      title: 'High Risk',
      value: highRiskLicence ? highRiskLicence.licenseId : '-',
    });

    infoList.push({
      id: 'whiteCardLicence',
      title: 'White Card',
      value: whiteCardLicence ? whiteCardLicence.licenseId : '-',
    });
    infoList.push({
      id: 'dob',
      title: 'DOB',
      value: driverDetails.value.dateOfBirth
        ? returnFormattedDate(driverDetails.value.dateOfBirth, 'DD/MM/YYYY')
        : '-',
    });

    infoList.push({
      id: 'signupDate',
      title: 'Signup Date',
      value: driverDetails.value.dateStarted
        ? returnFormattedDate(driverDetails.value.dateStarted, 'DD/MM/YYYY')
        : '-',
    });
  } else {
    // If fleetAssetOwner is defined (in case when creating NEW driver), use
    // fleetAssetOwner. When just viewing driver with
    // driverDetails.restrictedToOwnerId defined, find that owner in
    // FleetAssetOwnerModule owner list
    const owner = props.fleetAssetOwner
      ? props.fleetAssetOwner
      : !!driverDetails.value.restrictedToOwnerId
        ? fleetAssetOwnerStore.getOwnerList.find(
            (o) => o._id === driverDetails.value!.restrictedToOwnerId,
          )
        : undefined;
    infoList.push({
      id: props.isDialog ? 'ownerName' : 'subcontractorName',
      title: 'Subcontractor (Owner)',
      value: owner ? owner.name : '-',
    });
    if (owner) {
      infoList.push({
        id: 'driverType',
        title: 'Driver Type',
        value: owner.isOutsideHire ? 'Outside Hire' : 'Generic Driver',
      });
    }
  }

  return infoList;
});

// take user to fleet asset owner page
const viewFleetAssetOwner = (): void => {
  if (!driverDetails.value) {
    return;
  }
  // If fleetAssetOwner is defined (in case when creating NEW driver), use
  // fleetAssetOwner. When just viewing driver with
  // driverDetails.restrictedToOwnerId defined, find that owner in
  // FleetAssetOwnerModule owner list
  const owner = props.fleetAssetOwner
    ? props.fleetAssetOwner
    : !!driverDetails.value.restrictedToOwnerId
      ? fleetAssetOwnerStore.getOwnerList.find(
          (o: any) => o._id === driverDetails.value?.restrictedToOwnerId,
        )
      : undefined;
  if (owner) {
    fleetAssetOwnerStore.setSelectedFleetAssetOwnerId(owner.ownerId);
    router.push({
      name: 'Owner',
      params: {
        name: owner.name.toLowerCase().replace(/ /g, '-'),
        id: owner.ownerId,
      },
    });
  } else {
    showAppNotification(
      'Something went wrong. Could not find the associated Fleet Asset Owner.',
    );
  }
};

/**
 * Gets the oauth account status configuration for the driver, including the status name and associated color.
 * The status name is derived from the `driverUserRoleStatus` property.
 *
 * @returns {StatusConfig} The status configuration object containing `statusName` and `color`.
 */
const accountStatus: ComputedRef<StatusConfig> = computed(() => {
  const statusConfig = {
    statusName: driverUserRoleStatus.value,
    color: '',
  };
  if (!driverDetails.value) {
    return statusConfig;
  }
  switch (driverUserRoleStatus.value) {
    case 'PENDING':
      statusConfig.color = 'warning';
      break;
    case 'ACTIVE':
      statusConfig.color = 'success';
      break;
    case 'LOCKED':
      statusConfig.color = 'error';
      break;
  }
  return statusConfig;
});

/**
 * Gets the operations status configuration for the driver, determining if the driver is marked as
 * 'DO NOT USE', 'Active', or 'INCOMPLETE', based on the presence of specific status codes in the `statusList`.
 *
 * @returns {StatusConfig} The operations status configuration object containing `statusName` and `color`.
 */
const operationsStatus: ComputedRef<StatusConfig> = computed(() => {
  const statusConfig = {
    statusName: '',
    color: '',
  };
  if (!driverDetails.value) {
    return statusConfig;
  }
  const doNotUse: boolean = driverDetails.value.statusList.includes(47);
  const active: boolean = driverDetails.value.statusList.includes(4);
  if (doNotUse) {
    statusConfig.statusName = 'DO NOT USE';
    statusConfig.color = 'error';
  } else if (active) {
    statusConfig.statusName = 'ACTIVE';
    statusConfig.color = 'success';
  } else {
    statusConfig.statusName = 'INCOMPLETE';
    statusConfig.color = 'error';
  }
  return statusConfig;
});

function setEdited(Edited: boolean): void {
  isEdited.value = Edited;
}

const setRouteParams = (): void => {
  if (
    !props.isDialog &&
    driverDetails.value &&
    driverDetails.value.name &&
    driverDetails.value.driverId &&
    route.params.name !==
      driverDetails.value.name.toLowerCase().replace(/ /g, '-') &&
    routeId.value !== driverDetails.value.driverId
  ) {
    router.push({
      name: 'Driver',
      params: {
        name: driverDetails.value.name.toLowerCase().replace(/ /g, '-'),
        id: driverDetails.value.driverId,
      },
    });
  }
};

const exitDriver = (): void => {
  router.push({
    name: 'Subcontractor',
  });
};

const isAuthorised = (): boolean => {
  return hasAdminOrHeadOfficeRole();
};

// Trigger app notification. Defaults to ERROR type message, but type can be
// provided to produce other types. Includes componentTitle as a title for the
// notification.
const showAppNotification = (text: string, type?: HealthLevel): void => {
  showNotification(text, {
    type,
    title: componentTitle,
  });
};

/**
 * Save driver details request. We send the fleet asset owner Id along with this
 * request so both documents can be updated. Response is handled via
 * driverDetailsStore.setIncomingDriverDetails where fleet asst owner local
 * state will be updated.
 *
 * @returns {void}
 */
async function saveDriverDetails(): Promise<void> {
  try {
    if (!driverDetails.value) {
      throw new Error('Attempted to save a Driver when it was undefined.');
    }
    const isValid: boolean = !driverDetails.value.isGenericDriver
      ? validate(driverForm.value)
      : true;
    if (!isValid) {
      showAppNotification(FORM_VALIDATION_FAILED_MESSAGE);
      return;
    }

    // If the email or mobile number has changed, send an edit username request
    // to update the user's email and mobile number.
    const isEmailChanged =
      !!initialEmail.value && initialEmail.value !== driverDetails.value.email;
    const isMobileChanged =
      !!initialMobile.value &&
      initialMobile.value !== driverDetails.value.mobile;

    // If the email or mobile has changed, we need to send an edit username request
    if (isEmailChanged || isMobileChanged) {
      const success = await sendEditUsernameRequest();
      // If the request failed, show an error notification and revert the email
      // and mobile
      if (!success) {
        showAppNotification(
          'An error occurred while updating the Driver email address.',
          HealthLevel.ERROR,
        );
        driverDetails.value.email = initialEmail.value ?? '';
        driverDetails.value.mobile = initialMobile.value ?? '';
        return;
      }
    }

    awaitingDriverSaveResponse.value = true;
    const fleetAssetOwnerId: string | null = props.fleetAssetOwner
      ? props.fleetAssetOwner.ownerId
      : null;
    const driver = await driverDetails.value.save(fleetAssetOwnerId);
    awaitingDriverSaveResponse.value = false;
    // Handle result of save operation
    if (driver?.driverDetails?.driver) {
      setSavedDriverDetails(driver.driverDetails.driver);
      isEdited.value = false;
    } else {
      showAppNotification(
        'An error occurred while saving the Driver.',
        HealthLevel.ERROR,
      );
    }
  } catch (e: unknown) {
    console.error(e);
  }
}

/**
 * Updates the local state with the saved driver details. If the provided `driverDetails` are null,
 * it displays a generic error notification. Otherwise, it initializes the driver details, displays a success notification,
 * updates route parameters, and marks the current state as not edited. This method is called after receiving
 * a successful response from the backend save request.
 * @param {DriverDetails | null} details - The driver details to set, or null to indicate a failure or absence of data.
 * @returns {void} Does not return a value. Side effects include updating local state and displaying notifications.
 */
function setSavedDriverDetails(details: DriverDetails | null): void {
  if (!details) {
    showAppNotification(GENERIC_ERROR_MESSAGE);
    return;
  }
  driverDetails.value = initialiseDriverDetails(details);
  showAppNotification(
    `${driverDetails.value.displayName} successfully saved.`,
    HealthLevel.INFO,
  );
  setRouteParams();
  driverDetailsStore.setSelectedDriverDetailsId(driverDetails.value.driverId);
  isEdited.value = false;
}

// Closes the role status maintenance dialog
function closeRoleUpdateDialog(): void {
  roleStatusDialogIsOpen.value = false;
}

// send update role request. Used for locking and unlocking roles.
function sendUpdateRoleRequest(): void {
  if (!driverDetails.value || !driverDetails.value.authRefId) {
    return;
  }

  const lockUnlockRequest: LockUnlockRequest = {
    company: sessionManager.getCompanyId(),
    authRefId: driverDetails.value.authRefId,
    roleRequests: [],
  };

  const roleRequest = {
    division: sessionManager.getDivisionId(),
    roleId: 10, // driver role
    locked: driverUserRoleStatus.value === UserRoleStatus.LOCKED ? false : true,
  };

  lockUnlockRequest.roleRequests.push(roleRequest);
  useWebsocketStore().sendWebsocketRequest(
    new WebSocketRequest(
      '/authUserDetails/lockUnlockRoles',
      lockUnlockRequest,
      true,
    ),
  );
  Mitt.on('lockUnlockRolesResponse', (response: LockUnlockRequest) => {
    if (!lockUnlockRequest) {
      showNotification('Something went wrong.', {
        title: 'Role Status Management',
        type: HealthLevel.ERROR,
      });
    } else {
      showNotification('The users Role was successfully updated.', {
        title: 'Role Status Management',
        type: HealthLevel.INFO,
      });

      if (
        driverDetails.value &&
        driverDetails.value.authRefId === response.authRefId
      ) {
        for (const roleUpdate of response.roleRequests) {
          driverUserRoleStatus.value = roleUpdate.locked
            ? UserRoleStatus.LOCKED
            : UserRoleStatus.ACTIVE;
        }
      }

      closeRoleUpdateDialog();
    }
    Mitt.off('lockUnlockRolesResponse');
  });
}

/**
 * Called by mitt listener when a driver save event is received. Used to reset
 * the driver details if the driver being saved is the same as the driver being
 * viewed.
 */
function handleExternalSave(
  incomingDriver: SaveDriverDetailsResponse | null,
): void {
  if (!incomingDriver?.driverDetails?.driver?.driverId) {
    return;
  }
  const incomingDriverDetails = incomingDriver.driverDetails.driver;
  if (
    incomingDriverDetails.driverId === driverDetails.value?.driverId &&
    !awaitingDriverSaveResponse.value
  ) {
    // Show notification if currently in edit mode
    if (isEdited.value) {
      showAppNotification(
        `${incomingDriverDetails.displayName} has been updated by another user. Your changes have been discarded.`,
        HealthLevel.WARNING,
      );
    }
    isEdited.value = false;
    findAndSetSelectedDriverDetails();
  }
}

useMittListener('savedNewDriver', handleExternalSave);

/**
 * Send can editUserName request. Used for checking if user email is changed to resend invite.
 */
async function sendEditUsernameRequest(): Promise<boolean> {
  if (!driverDetails.value?.authRefId) {
    return false;
  }
  const payload: EditUsernameForUserRequest = {
    authRefId: driverDetails.value.authRefId,
    newEmailAddress: driverDetails.value.email,
    newContactNumber: driverDetails.value.mobile,
    name: driverDetails.value.name,
    userType: RoleUserType.DRIVER,
    clientId: null,
  };
  const response: EditUsernameForUserResponse | null =
    await userManagementStore.editUsernameAndResendInvite(payload);

  return response ? response.editSuccessful : false;
}

/**
 * Set the initial email and mobile values for the driver, so we can check if
 * the email or mobile has changed when saving the driver. This is called when
 * the driver details are loaded.
 */
function setInitialUsernameValues(driver: DriverDetails | null): void {
  if (!driver) {
    return;
  }
  initialEmail.value = driver.email;
  initialMobile.value = driver.mobile;
}

onMounted(() => {
  findAndSetSelectedDriverDetails();
});

onBeforeUnmount(() => {
  driverDetailsStore.setSelectedDriverDetailsId(null);
});

onBeforeMount(() => {
  if (driverDetailsStore.selectedDriverDetailsView) {
    selectedViewType.value = driverDetailsStore.selectedDriverDetailsView;
    driverDetailsStore.setSelectedDriverDetailsView(null);
  } else {
    selectedViewType.value = 'PER';
  }
});
</script>
