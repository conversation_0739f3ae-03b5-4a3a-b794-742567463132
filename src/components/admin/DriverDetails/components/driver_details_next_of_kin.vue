<template>
  <div class="driver-details-next-of-kin-container">
    <div class="add-next-of-kin-btn">
      <v-btn @click="editNextOfKin(-1)" color="blue" small depressed
        >Add Kin</v-btn
      >
    </div>
    <v-data-table
      class="gd-dark-theme"
      :headers="headers"
      :items="nextOfKinsTableData"
      :rows-per-page-items="[10, 20]"
    >
      <template v-slot:items="props">
        <tr @click="editNextOfKin(props.index)" style="cursor: pointer">
          <td>{{ props.item.name }}</td>
          <td>{{ props.item.relation }}</td>
          <td>{{ formatPhoneNumber(props.item.mobile) }}</td>
          <td>{{ formatPhoneNumber(props.item.phone) }}</td>
          <td>{{ formatPhoneNumber(props.item.workNumber) }}</td>
        </tr>
      </template>
    </v-data-table>
    <v-dialog
      v-if="nextOfKin"
      v-model="nextOfKinDialogIsOpen"
      width="700px"
      class="ma-0"
      persistent
      no-click-animation
      content-class="v-dialog-custom"
    >
      <v-flex md12>
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight"
        >
          <span>Next Of Kin</span>
          <div
            class="app-theme__center-content--closebutton"
            @click="cancelNextOfKin"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>
        <v-layout
          row
          wrap
          class="app-theme__center-content--body dialog-content"
        >
          <v-flex md12>
            <v-form ref="nextOfKinForm">
              <v-layout
                row
                wrap
                class="body-scrollable--65 body-min-height--65"
                pa-3
              >
                <v-flex md12>
                  <v-layout>
                    <v-flex md4>
                      <v-layout align-center class="form-field-label-container">
                        <h6 class="subheader--faded pr-3 pb-0">Contact Name</h6>
                      </v-layout>
                    </v-flex>
                    <v-flex md8>
                      <v-text-field
                        class="v-solo-custom"
                        solo
                        flat
                        label="Contact Name"
                        v-model.trim="nextOfKin.name"
                        color="light-blue"
                        autofocus
                      ></v-text-field>
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex md12>
                  <v-layout>
                    <v-flex md4>
                      <v-layout align-center class="form-field-label-container">
                        <h6 class="subheader--faded pr-3 pb-0">Relation</h6>
                      </v-layout>
                    </v-flex>
                    <v-flex md8>
                      <v-text-field
                        class="v-solo-custom"
                        solo
                        flat
                        label="Relation"
                        v-model.trim="nextOfKin.relation"
                        color="light-blue"
                        autofocus
                      ></v-text-field>
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex md12>
                  <v-layout>
                    <v-flex md4>
                      <v-layout align-center class="form-field-label-container">
                        <h6 class="subheader--faded pr-3 pb-0">
                          Contact Number
                        </h6>
                      </v-layout>
                    </v-flex>
                    <v-flex md8>
                      <v-text-field
                        class="v-solo-custom"
                        solo
                        flat
                        label="Contact Number"
                        v-model.trim="nextOfKin.phone"
                        :rules="[validate.numbers]"
                        v-mask="'#### ### ######'"
                        color="light-blue"
                        autofocus
                      ></v-text-field>
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex md12>
                  <v-layout>
                    <v-flex md4>
                      <v-layout align-center class="form-field-label-container">
                        <h6 class="subheader--faded pr-3 pb-0">
                          Mobile Number
                        </h6>
                      </v-layout>
                    </v-flex>
                    <v-flex md8>
                      <v-text-field
                        class="v-solo-custom"
                        solo
                        flat
                        label="Mobile Number"
                        v-model.trim="nextOfKin.mobile"
                        :rules="[validate.numbers]"
                        v-mask="'04## ### ###'"
                        color="light-blue"
                        autofocus
                      ></v-text-field>
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex md12>
                  <v-layout>
                    <v-flex md4>
                      <v-layout align-center class="form-field-label-container">
                        <h6 class="subheader--faded pr-3 pb-0">Work Number</h6>
                      </v-layout>
                    </v-flex>
                    <v-flex md8>
                      <v-text-field
                        class="v-solo-custom"
                        solo
                        flat
                        label="Work Number"
                        v-model.trim="nextOfKin.workNumber"
                        :rules="[validate.numbers]"
                        v-mask="'### ### #######'"
                        color="light-blue"
                        autofocus
                      ></v-text-field>
                    </v-flex>
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-form>
          </v-flex>
          <v-flex md12>
            <v-divider></v-divider>
            <v-layout align-center>
              <v-btn
                color="red"
                flat
                :class="editingIndex === -1 ? '' : 'mr-3'"
                @click="cancelNextOfKin"
              >
                <span>cancel</span>
              </v-btn>

              <v-spacer></v-spacer>
              <ConfirmationDialog
                v-if="editingIndex !== -1"
                buttonText="Delete"
                message="Please confirm that you wish to delete this Next of Kin."
                title="Confirm Deletion of Next Of Kin"
                @confirm="deleteNextOfKin"
                :isDepressedButton="false"
                :buttonDisabled="!isAuthorised()"
                :isBlockButton="false"
                :isOutlineButton="true"
                buttonColor="white"
                :confirmationButtonText="'Confirm Deletion'"
                :dialogIsActive="true"
                :noButtonMargin="true"
              >
              </ConfirmationDialog>
              <v-btn
                color="blue"
                depressed
                class="v-btn-confirm-custom"
                :disabled="!isAuthorised()"
                @click="saveNextOfKin"
              >
                <span>Save Next Of Kin</span>
              </v-btn>
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { formatPhoneNumber } from '@/helpers/StringHelpers/StringHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import NextOfKin from '@/interface-models/Driver/DriverDetails/AdditionalObjects/NextOfKin';
import {
  DriverDetails,
  SaveDriverDetailsResponse,
} from '@/interface-models/Driver/DriverDetails/DriverDetails';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { Validation } from '@/interface-models/Generic/Validation';
import Mitt from '@/utils/mitt';
import { ComputedRef, Ref, computed, onMounted, ref, watch } from 'vue';

const props = withDefaults(
  defineProps<{
    nextOfKins: NextOfKin[];
    driverDetails: DriverDetails | null;
  }>(),
  {
    driverDetails: null,
  },
);

const nextOfKin = ref<NextOfKin | null>(null);
const nextOfKinDialogIsOpen = ref(false);
const awaitingDriverSaveResponse = ref(false);
const deletionRequestSent = ref(false);
const editingIndex = ref(-1);

const nextOfKinForm: Ref<any> = ref(null);

const validate: Ref<Validation> = computed(() => validationRules);

const headers: TableHeader[] = [
  {
    text: 'Contact Name',
    align: 'left',
    value: 'inductionType',
    sortable: false,
  },
  { text: 'Relation', align: 'left', sortable: false, value: 'certificateId' },
  { text: 'Phone', align: 'left', sortable: false, value: 'inductionDate' },
  { text: 'Mobile', align: 'left', value: 'expiry', sortable: false },
  { text: 'Work Number', align: 'left', value: 'expiry', sortable: false },
];

const nextOfKinsTableData: ComputedRef<NextOfKin[]> = computed(() =>
  props.nextOfKins.map((x: NextOfKin) => ({
    name: x.name ? x.name : '-',
    relation: x.relation ? x.relation : '-',
    mobile: x.mobile ? x.mobile : '-',
    phone: x.phone ? x.phone : '-',
    workNumber: x.workNumber ? x.workNumber : '-',
  })),
);

const isAuthorised = () => {
  return hasAdminOrHeadOfficeRole();
};

const editNextOfKin = (index: number) => {
  editingIndex.value = index;

  if (editingIndex.value !== -1) {
    const nextOfKinData = JSON.parse(
      JSON.stringify(props.nextOfKins[editingIndex.value]),
    );
    nextOfKin.value = nextOfKinData ? nextOfKinData : null;
  } else {
    nextOfKin.value = new NextOfKin();
  }

  nextOfKinDialogIsOpen.value = true;
};

const cancelNextOfKin = () => {
  nextOfKinDialogIsOpen.value = false;
  nextOfKin.value = null;
  editingIndex.value = -1;
  deletionRequestSent.value = false;
};

const saveNextOfKin = () => {
  if (!props.driverDetails) {
    showNotification('Please select a driver before adding Next of Kin');
    return;
  }
  if (!nextOfKin.value || !nextOfKinForm.value.validate()) {
    return;
  }

  if (editingIndex.value !== -1) {
    props.nextOfKins.splice(editingIndex.value, 1, nextOfKin.value);
  } else {
    props.nextOfKins.push(nextOfKin.value);
  }

  awaitingDriverSaveResponse.value = true;
  props.driverDetails.save(null);
};

const setSavedDriverDetails = (driverDetails: DriverDetails | null) => {
  if (
    driverDetails &&
    driverDetails.driverId === props.driverDetails?.driverId &&
    nextOfKinDialogIsOpen.value
  ) {
    const successType = !deletionRequestSent.value ? 'saved.' : 'removed.';

    showNotification('Next of kin successfully ' + successType, {
      type: HealthLevel.SUCCESS,
      title: 'Driver Details - Next of Kin',
    });

    cancelNextOfKin();
  }
};

const deleteNextOfKin = () => {
  props.nextOfKins.splice(editingIndex.value, 1);
  deletionRequestSent.value = true;
  awaitingDriverSaveResponse.value = true;
  props.driverDetails?.save(null);
};

/**
 * Configures a listener to react to saved driver details events based on the provided `listen` flag. When enabled,
 * it listens for 'savedNewDriver' events and updates the local state with the received driver details.
 * Please note that we don't clear the listener in this component. We do so in the main driver_details_index on destroy.
 * Possible better solution here is to emit the save request back so it is all handled in driver_details_index; that way we
 * wouldn't require listeners in every driver sub component.
 *
 * @param {boolean} listen - Determines whether to start or stop listening for driver update events.
 * @returns {void}
 */
const listenForDriverUpdates = (listen: boolean): void => {
  if (!listen) {
    Mitt.off('savedNewDriver');
    return;
  }
  Mitt.on('savedNewDriver', (savedDriver: SaveDriverDetailsResponse) => {
    if (awaitingDriverSaveResponse.value) {
      const driverDetails: DriverDetails | null =
        savedDriver &&
        savedDriver.driverDetails &&
        savedDriver.driverDetails.driver
          ? savedDriver.driverDetails.driver
          : null;
      setSavedDriverDetails(driverDetails);
      awaitingDriverSaveResponse.value = false;
    }
  });
};

onMounted(() => {
  listenForDriverUpdates(true);
});

watch(
  () => props.driverDetails,
  (newVal) => {
    listenForDriverUpdates(!!newVal);
  },
);
</script>

<style scoped>
.add-next-of-kin-btn {
  position: absolute;
  top: 1px;
  right: 10px;
  z-index: 230 !important;
}
</style>
