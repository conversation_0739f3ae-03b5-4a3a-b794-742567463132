<template>
  <v-layout column class="driver-associations-container">
    <div class="add-association-btn">
      <v-btn
        @click="addAssociationDialogIsOpen = true"
        color="blue"
        small
        depressed
        >Add Association</v-btn
      >
    </div>
    <v-data-table
      ref="associationTableRef"
      class="gd-dark-theme"
      :headers="fleetAssetOwnerHeaders"
      :items="associatedFleetAssetOwners"
      item-key="fleetAssetOwnerName"
      :no-data-text="'This Driver currently has no associations.'"
      expand
      :rows-per-page-items="[10, 20]"
    >
      <template v-slot:items="tableProps">
        <tr
          @click="
            viewFleetAssetOwner(
              tableProps.item.fleetAssetOwnerId,
              tableProps.item.fleetAssetOwnerName,
            )
          "
          style="
            cursor: pointer;
            position: relative;
            border-bottom: 2px solid hsla(0, 0%, 100%, 0.12);
          "
        >
          <td class="pl-4">{{ tableProps.item.fleetAssetOwnerName }}</td>

          <td>{{ tableProps.item.fleetAssetOwnerPhone }}</td>
          <td>{{ tableProps.item.fleetAssetOwnerMobile }}</td>
          <td>{{ tableProps.item.associatedFleetAssetCount }}</td>

          <td>
            <v-btn
              class="mx-0"
              small
              flat
              color="info"
              @click.stop="removeAssociation(tableProps.item.fleetAssetOwnerId)"
            >
              Remove Driver
            </v-btn>
          </td>
        </tr>
      </template>

      <template v-slot:expand="tableProps">
        <v-layout>
          <v-flex md2 style="border-right: 1px solid hsla(0, 0%, 100%, 0.12)">
            <v-layout align-center style="height: 56px">
              <h6 class="subheader--faded pr-4">- Assets</h6>
            </v-layout>
          </v-flex>

          <v-flex md10>
            <v-data-table
              class="gd-dark-theme"
              :headers="fleetAssetHeaders"
              :items="tableProps.item.associatedFleetAssets"
              :item-key="tableProps.item.fleetAssetId"
              hide-actions
              :rows-per-page-items="[10, 20]"
            >
              <template v-slot:items="innerTableProps">
                <tr
                  style="cursor: pointer"
                  @click="
                    viewFleetAsset(
                      innerTableProps.item.fleetAssetId,
                      innerTableProps.item.csrAssignedId,
                      innerTableProps.item.assetType === 'Truck',
                    )
                  "
                >
                  <td class="pl-4">{{ innerTableProps.item.assetType }}</td>
                  <td>{{ innerTableProps.item.csrAssignedId }}</td>
                  <td>{{ innerTableProps.item.rego }}</td>
                  <td>{{ innerTableProps.item.isDefaultDriver }}</td>
                </tr>
              </template>

              <template v-slot:no-data>
                <v-layout justify-center>
                  No fleet assets owned by
                  {{ tableProps.item.fleetAssetOwnerName }}
                  are assigned to this driver.
                </v-layout>
              </template>
            </v-data-table>
          </v-flex>
        </v-layout>
      </template>
    </v-data-table>

    <ContentDialog
      v-if="addAssociationDialogIsOpen"
      :showDialog.sync="addAssociationDialogIsOpen"
      :title="'Add New Association'"
      :isConfirmUnsaved="false"
      :width="'600px'"
      :confirmBtnText="'Confirm Association'"
      @confirm="addNewAssociation"
      :isLoading="false"
      @cancel="addAssociationDialogIsOpen = false"
      :isDisabled="false"
    >
      <v-layout class="dialog-content">
        <span class="pa-3">
          <InformationTooltip :left="true" :tooltipType="HealthLevel.INFO">
            <v-layout slot="content" row wrap>
              <v-flex md12>
                <p class="mb-1">
                  Drivers may only be associated with
                  <strong>Subcontractor</strong> and
                  <strong>Internal</strong> type Fleet Asset Owners.
                </p>
                <p class="mb-0">
                  Associating an Owner and Driver allows the Driver to be
                  selected as a driver for that Owner's Fleet Assets.
                </p>
              </v-flex>
            </v-layout>
          </InformationTooltip>
        </span>
        <v-flex>
          <v-form ref="form">
            <SelectEntity
              :entityTypes="[entityType.FLEET_ASSET_OWNER]"
              :id.sync="newFleetAssetOwnerIdToAssociate"
              :rules="[validate.required]"
              :placeholder="'Subcontractor to associate'"
              :hint="'Select a Subcontractor with whom you wish to associate this Driver.'"
              :affiliationTypes="['1', '4']"
            />
          </v-form>
        </v-flex>
      </v-layout>
    </ContentDialog>

    <ContentDialog
      v-if="confirmDissociationDialogIsOpen"
      :showDialog.sync="confirmDissociationDialogIsOpen"
      :title="'Driver Association Management'"
      :isConfirmUnsaved="false"
      :width="'850px'"
      :confirmBtnText="'Confirm Dissociation'"
      @confirm="confirmRemoveAssociation"
      :isLoading="false"
      @cancel="cancelAssociationDialog"
      :isDisabled="!canActionAssociationUpdate"
      :contentPadding="'pa-1'"
    >
      <v-layout>
        <v-flex md12 pa-3>
          <v-alert
            type="warning"
            v-if="!canActionAssociationUpdate"
            :value="true"
          >
            <strong class="pr-1">Warning:</strong>This Driver is linked to
            active Jobs or Fleet Assets, so cannot be dissociated from this
            Fleet Asset Owner. To proceed you must:
            <ul class="pt-1">
              <li>
                Set the driver to DO NOT USE if the driver is not associated to
                any other subcontractors.
              </li>
              <li>
                Remove this driver as an associated Driver for all listed
                vehicles.
              </li>
              <li>De-allocate this driver from all allocated work.</li>
              <li>Cancel all permanent jobs associated with this driver.</li>
              <li>Complete all outstanding accounting processes.</li>
            </ul>
          </v-alert>
          <v-alert
            type="success"
            v-if="canActionAssociationUpdate"
            :value="true"
          >
            This Driver is currently not linked to any active vehicles or jobs
            for this Owner. If you are sure you wish to continue, you may safely
            proceed with dissociation.
          </v-alert>
          <SubcontractorActiveAssociations
            :entityId="driverId"
            :entityType="subcontractorEntityType"
            :isDriverAssociations="true"
            :ownerId="ownerIdAssociation"
            :isSubcontractorOperationalStatusUpdate="true"
            :canActionAssociationUpdate.sync="canActionAssociationUpdate"
            :statusList="statusList"
          />
        </v-flex>
      </v-layout>
    </ContentDialog>
  </v-layout>
</template>

<script setup lang="ts">
import SelectEntity from '@/components/common/inputs/select_entity/index.vue';
import SubcontractorActiveAssociations from '@/components/common/subcontractor_active_associations/index.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import {
  DriverAssociatedFleetAssets,
  DriverAssociationTable,
} from '@/interface-models/Driver/DriverAssociations';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import { SubcontractorAssociationUpdateRequest } from '@/interface-models/FleetAssetOwner/SubcontractorAssociation/SubcontractorAssociationUpdateRequest';
import { SubcontractorAssociationUpdateResponse } from '@/interface-models/FleetAssetOwner/SubcontractorAssociation/SubcontractorAssociationUpdateResponse';
import { SubcontractorAssociationUpdateType } from '@/interface-models/FleetAssetOwner/SubcontractorAssociation/SubcontractorAssociationUpdateType';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { Validation } from '@/interface-models/Generic/Validation';
import { OperationStatus } from '@/interface-models/Generic/WebSocketRequest/OperationStatus';
import { SubcontractorEntityType } from '@/interface-models/InvoiceAdjustment/EntityTypes/SubcontractorEntityType';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { Ref, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router/composables';

const props = withDefaults(
  defineProps<{
    driverId: string;
    statusList: number[];
  }>(),
  {
    driverId: '',
  },
);

const router = useRouter();
const fleetAssetStore = useFleetAssetStore();
const driverDetailsStore = useDriverDetailsStore();
const fleetAssetOwnerStore = useFleetAssetOwnerStore();

const entityType = EntityType;
const componentTitle = 'Driver Details - Associations';
const addAssociationDialogIsOpen = ref<boolean>(false);
const confirmDissociationDialogIsOpen = ref<boolean>(false);
const newFleetAssetOwnerIdToAssociate = ref<string | null>(null);
const awaitingDriverAssociationResponse = ref(false);
const awaitingAssociationUpdateType =
  ref<SubcontractorAssociationUpdateType | null>(null);
const currentAssociationUpdateResponse =
  ref<SubcontractorAssociationUpdateResponse | null>(null);
const ownerIdAssociation = ref<string | null>(null);
const canActionAssociationUpdate = ref<boolean>(false);
const associatedFleetAssetOwners = ref<DriverAssociationTable[]>([]);
const subcontractorEntityType = SubcontractorEntityType.DRIVER;

const formRef: Ref<any> = ref(null);

const associationTableRef = ref<any>(null);

const fleetAssetOwnerHeaders: TableHeader[] = [
  {
    text: 'Fleet Asset Owner Name',
    align: 'left',
    sortable: true,
    value: 'fleetAssetOwnerName',
  },
  {
    text: 'Phone',
    align: 'left',
    sortable: true,
    value: 'fleetAssetOwnerPhone',
  },
  {
    text: 'Mobile',
    align: 'left',
    sortable: true,
    value: 'fleetAssetOwnerMobile',
  },
  {
    text: 'Associated Assets',
    align: 'left',
    sortable: true,
    value: 'associatedFleetAssetCount',
  },
  {
    text: 'Dissociation',
    align: 'left',
    sortable: true,
    value: '',
  },
];

const fleetAssetHeaders: TableHeader[] = [
  {
    text: 'Asset Type',
    align: 'left',
    sortable: false,
    value: 'name',
  },
  {
    text: 'Fleet Asset ID',
    align: 'left',
    sortable: false,
    value: 'name',
  },
  {
    text: 'Rego',
    align: 'left',
    sortable: false,
    value: 'name',
  },
  {
    text: 'Default Driver',
    align: 'left',
    sortable: false,
    value: 'defaultDriver',
  },
];

const validate: Validation = validationRules;

const setAssociatedFleetAssetOwners = (): DriverAssociationTable[] => {
  if (!props.driverId) {
    return [];
  }

  return fleetAssetOwnerStore.getOwnerList
    .filter((fleetAssetOwner: FleetAssetOwnerSummary) =>
      fleetAssetOwner.associatedDrivers.includes(props.driverId),
    )
    .map((owner: FleetAssetOwnerSummary) => {
      const fleetAssetOwnersAssociatedFleetAssets =
        fleetAssetStore.getAllFleetAssetList.filter(
          (x: FleetAssetSummary) =>
            owner.associatedFleetAssets.includes(x.fleetAssetId) &&
            x.associatedDrivers.includes(props.driverId),
        );

      const associatedFleetAssets: DriverAssociatedFleetAssets[] =
        fleetAssetOwnersAssociatedFleetAssets.map((f: FleetAssetSummary) => {
          return {
            fleetAssetId: f.fleetAssetId,
            csrAssignedId: f.csrAssignedId,
            assetType: f.fleetAssetTypeId === 1 ? 'Truck' : 'Trailer',
            rego: f.registrationNumber ? f.registrationNumber : '-',
            isDefaultDriver: f.defaultDriver === props.driverId ? 'YES' : 'NO',
          };
        });
      return {
        fleetAssetOwnerName: owner.name,
        fleetAssetOwnerId: owner.ownerId,
        fleetAssetOwnerPhone: owner.phone ? owner.phone : '-',
        fleetAssetOwnerMobile: owner.mobile ? owner.mobile : '-',
        associatedFleetAssetCount: associatedFleetAssets.length,
        associatedFleetAssets,
      };
    });
};

const viewFleetAsset = (
  fleetAssetId: string,
  csrAssignedId: string,
  isTruck: boolean,
): void => {
  driverDetailsStore.setSelectedDriverDetailsId(null);
  isTruck
    ? fleetAssetStore.setSelectedFleetAssetId(fleetAssetId)
    : fleetAssetStore.setSelectedFleetAssetTrailerId(fleetAssetId);
  router.push({
    name: isTruck ? 'Truck' : 'Trailer',
    params: {
      name: csrAssignedId.toLowerCase().replace(/ /g, '-'),
      id: fleetAssetId,
    },
  });
};

const handleUpdateDriverAssociationResponse = (
  response: SubcontractorAssociationUpdateResponse | null,
) => {
  if (response === null) {
    showAppNotification(GENERIC_ERROR_MESSAGE);
    addAssociationDialogIsOpen.value = false;
    newFleetAssetOwnerIdToAssociate.value = null;
    return;
  }
  const type = response.type;
  if (type === SubcontractorAssociationUpdateType.CHECK) {
    // If response is of CHECK type, we should show the confirmation dialog
    // which will display details
    currentAssociationUpdateResponse.value = response;
    confirmDissociationDialogIsOpen.value = true;
  } else if (type === SubcontractorAssociationUpdateType.DISSOCIATE) {
    if (response.operationStatus === OperationStatus.SUCCESS) {
      showAppNotification(
        'Successfully dissociated Driver from the selected Owner.',
        HealthLevel.SUCCESS,
      );
    } else {
      showAppNotification(
        'Something went wrong. The driver could not be associated with this owner.',
      );
    }
    confirmDissociationDialogIsOpen.value = false;
  } else if (type === SubcontractorAssociationUpdateType.ASSOCIATE) {
    if (response.operationStatus === OperationStatus.SUCCESS) {
      showAppNotification(
        'Successfully associated Driver with the selected Owner.',
        HealthLevel.SUCCESS,
      );
    } else {
      showAppNotification(
        'Something went wrong. Check if the selected Owner is already associated with this driver, or try again later.',
      );
    }
    addAssociationDialogIsOpen.value = false;
    newFleetAssetOwnerIdToAssociate.value = null;
  }
  associatedFleetAssetOwners.value = setAssociatedFleetAssetOwners();
  expandAllTableRows();
  awaitingAssociationUpdateType.value = null;

  // Reset awaiting variables
  ownerIdAssociation.value = null;
  awaitingDriverAssociationResponse.value = false;
};

const addNewAssociation = async (): Promise<void> => {
  if (formRef.value?.validate()) {
    showAppNotification('Please ensure a subcontractor is selected.');
    return;
  }

  if (!newFleetAssetOwnerIdToAssociate.value) {
    return;
  }

  const request: SubcontractorAssociationUpdateRequest = {
    driverId: props.driverId,
    ownerId: newFleetAssetOwnerIdToAssociate.value,
    fleetAssetId: null,
    entityType: SubcontractorEntityType.DRIVER,
    type: SubcontractorAssociationUpdateType.ASSOCIATE,
    statusList: null,
  };
  awaitingDriverAssociationResponse.value = true;
  awaitingAssociationUpdateType.value =
    SubcontractorAssociationUpdateType.CHECK;

  // Send request and handle response
  handleUpdateDriverAssociationResponse(
    await fleetAssetOwnerStore.updateSubcontractorEntityAssociation(request),
  );
};

/**
 * Open confirmation dialog to confirm disassociation of driver from a fleet
 * asset owner
 * @param fleetAssetOwnerId - The ID of the fleet asset owner to disassociate
 */
function removeAssociation(fleetAssetOwnerId: string) {
  if (!props.driverId || !fleetAssetOwnerId) {
    return;
  }
  ownerIdAssociation.value = fleetAssetOwnerId;
  confirmDissociationDialogIsOpen.value = true;
}

/**
 * Called from the confirmation dialog to confirm the disassociation of the
 * driver from the fleet asset owner. Dispatches request to the store to
 * update the association.
 */
async function confirmRemoveAssociation() {
  if (!canActionAssociationUpdate.value || !ownerIdAssociation.value) {
    return;
  }
  const request: SubcontractorAssociationUpdateRequest = {
    driverId: props.driverId,
    ownerId: ownerIdAssociation.value,
    fleetAssetId: null,
    entityType: SubcontractorEntityType.DRIVER,
    type: SubcontractorAssociationUpdateType.DISSOCIATE,
    statusList: null, // Please specify the statusList property if needed
  };
  awaitingDriverAssociationResponse.value = true;
  awaitingAssociationUpdateType.value =
    SubcontractorAssociationUpdateType.DISSOCIATE;
  // Send request and handle
  handleUpdateDriverAssociationResponse(
    await fleetAssetOwnerStore.updateSubcontractorEntityAssociation(request),
  );
}

function expandAllTableRows() {
  if (associationTableRef.value) {
    const expandObject: any = {};
    for (const row of associatedFleetAssetOwners.value) {
      expandObject[row.fleetAssetOwnerName] = true;
    }
    associationTableRef.value.expanded = expandObject;
  }
}

/**
 * Navigate to the fleet asset owner page of the selected owner.
 * @param fleetAssetOwnerId - The ID of the fleet asset owner to go to
 * @param fleetAssetOwnerName - Owner name to put into URL
 */
function viewFleetAssetOwner(
  fleetAssetOwnerId: string,
  fleetAssetOwnerName: string,
) {
  driverDetailsStore.setSelectedDriverDetailsId(null);
  fleetAssetOwnerStore.setSelectedFleetAssetOwnerId(fleetAssetOwnerId);
  router.push({
    name: 'Owner',
    params: {
      name: fleetAssetOwnerName.toLowerCase().replace(/ /g, '-'),
      id: fleetAssetOwnerId,
    },
  });
}

function showAppNotification(text: string, type?: HealthLevel) {
  showNotification(text, {
    type,
    title: componentTitle,
  });
}

const cancelAssociationDialog = () => {
  confirmDissociationDialogIsOpen.value = false;
  canActionAssociationUpdate.value = false;
  ownerIdAssociation.value = null;
};

onMounted(() => {
  associatedFleetAssetOwners.value = setAssociatedFleetAssetOwners();
  expandAllTableRows();
});
</script>

<style scoped lang="scss">
.driver-associations-container {
  .table__menu {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
  }

  .remittance-dialog__header {
    color: rgb(188, 188, 190);
    text-transform: uppercase;
    font-size: $font-size-12;
    font-weight: 600;
    padding-bottom: 4px;
    &.header-type {
      font-size: $font-size-13;
      padding-bottom: 8px;
      font-weight: 700;
      color: rgb(74, 199, 233);
    }
    &.subheader-type {
      font-size: $font-size-11;
      font-style: italic;
      font-weight: 500;
      color: rgb(136, 136, 145);
      text-transform: none;
    }
  }

  .status-container {
    padding: 1px 8px;
    font-weight: 600;
    background-color: $outline-type;
    border-radius: 2px;
    letter-spacing: 0.03em;
  }
}

.dialog-content {
  min-height: 30vh;
}
.add-association-btn {
  position: absolute;
  top: 1px;
  right: 10px;
  z-index: 10 !important;
}
</style>
