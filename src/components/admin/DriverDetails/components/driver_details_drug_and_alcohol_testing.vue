<template>
  <div class="driver-details-drug-and-alcohol-testing-container">
    <div class="add-test-btn">
      <v-btn
        @click="editDrugAndAlcoholTestDetails(null)"
        color="blue"
        small
        depressed
        >Add Test</v-btn
      >
    </div>
    <v-data-table
      class="gd-dark-theme"
      :headers="headers"
      :items="drugAndAlcoholTestTableData"
      :rows-per-page-items="[10, 20]"
    >
      <template v-slot:items="props">
        <tr
          @click="editDrugAndAlcoholTestDetails(props.item.id)"
          style="cursor: pointer"
        >
          <td>{{ props.item.testName }}</td>
          <td>{{ props.item.date }}</td>
          <td>{{ props.item.result }}</td>
          <td>{{ props.item.note }}</td>
        </tr>
      </template>
    </v-data-table>
    <v-dialog
      v-if="drugAndAlcoholTestDetails"
      v-model="drugAndAlcoholTestDialogIsOpen"
      width="700px"
      class="ma-0"
      persistent
      no-click-animation
      content-class="v-dialog-custom"
    >
      <v-flex md12>
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight"
        >
          <span>Drug And Alcohol Test</span>
          <div
            class="app-theme__center-content--closebutton"
            @click="cancelDrugAndAlcoholTestDetails"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>
        <v-layout
          row
          wrap
          class="app-theme__center-content--body dialog-content"
        >
          <v-flex md12>
            <v-form ref="drugAndAlcoholTestForm">
              <v-layout
                row
                wrap
                class="body-scrollable--65 body-min-height--65"
                pa-3
              >
                <v-flex md12>
                  <v-layout>
                    <v-flex md4>
                      <v-layout align-center class="form-field-label-container">
                        <h6
                          class="subheader--faded pr-3 pb-0 form-field-required-marker"
                        >
                          Test Type
                        </h6>
                      </v-layout>
                    </v-flex>
                    <v-flex md8>
                      <v-select
                        label="Test Type"
                        v-model="drugAndAlcoholTestDetails.testType"
                        :items="testTypes"
                        class="v-solo-custom"
                        solo
                        flat
                        :rules="[validate.required]"
                        item-text="name"
                        item-value="id"
                      ></v-select>
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex md12>
                  <v-layout>
                    <v-flex md4>
                      <v-layout align-center class="form-field-label-container">
                        <h6
                          class="subheader--faded pr-3 pb-0 form-field-required-marker"
                        >
                          Test Date
                        </h6>
                      </v-layout>
                    </v-flex>
                    <v-flex md8>
                      <DateTimeInputs
                        :epochTime.sync="drugAndAlcoholTestDetails.testDate"
                        :enableValidation="true"
                        :type="DateTimeType.DATE_START_OF_DAY"
                        dateLabel="Test Date"
                        :soloInput="true"
                        :boxInput="false"
                        :isRequired="true"
                        :hintTextType="HintTextType.FORMATTED_SELECTION"
                      ></DateTimeInputs>
                    </v-flex>
                  </v-layout>
                </v-flex>

                <v-flex md12>
                  <v-layout>
                    <v-flex md4>
                      <v-layout align-center class="form-field-label-container">
                        <h6
                          class="subheader--faded pr-3 pb-0 form-field-required-marker"
                        >
                          Result
                        </h6>
                      </v-layout>
                    </v-flex>
                    <v-flex md8>
                      <v-select
                        label="Result"
                        v-model="drugAndAlcoholTestDetails.result"
                        :items="resultTypes"
                        class="v-solo-custom"
                        solo
                        flat
                        :rules="[validate.required]"
                        item-text="longName"
                        item-value="id"
                      ></v-select>
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex md12>
                  <v-layout>
                    <v-flex md4>
                      <v-layout align-center class="form-field-label-container">
                        <h6 class="subheader--faded pr-3 pb-0">
                          Additional Comments
                        </h6>
                      </v-layout>
                    </v-flex>
                    <v-flex md8>
                      <v-textarea
                        class="v-solo-custom"
                        solo
                        flat
                        label="Additional comments"
                        v-model="drugAndAlcoholTestDetails.notes"
                      ></v-textarea>
                    </v-flex>
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-form>
          </v-flex>
          <v-flex md12>
            <v-divider></v-divider>
            <v-layout align-center>
              <v-btn flat color="red" @click="cancelDrugAndAlcoholTestDetails"
                >Cancel</v-btn
              >
              <v-spacer></v-spacer>
              <ConfirmationDialog
                buttonText="Delete"
                v-if="editingExisting"
                message="Please confirm that you wish to delete this test."
                title="Confirm Deletion of Next Of Kin"
                @confirm="deleteTest"
                :isDepressedButton="false"
                :buttonDisabled="!isAuthorised()"
                :isBlockButton="false"
                :isOutlineButton="true"
                :buttonColor="'white'"
                :confirmationButtonText="'Confirm Deletion'"
                :dialogIsActive="true"
                :noButtonMargin="true"
              >
              </ConfirmationDialog>
              <v-btn
                depressed
                color="blue"
                :disabled="!isAuthorised()"
                @click="saveDrugAndAlcoholTestDetails"
                class="v-btn-confirm-custom"
                >Save</v-btn
              >
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import DateTimeInputs, {
  DateTimeType,
  HintTextType,
} from '@/components/common/date-picker/date_time_inputs.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import DrugAndAlcoholTests from '@/interface-models/Driver/DriverDetails/AdditionalObjects/DrugAndAlcoholTests';
import {
  DriverDetails,
  SaveDriverDetailsResponse,
} from '@/interface-models/Driver/DriverDetails/DriverDetails';
import {
  DrugAlcoholResult,
  drugAlcoholResult,
} from '@/interface-models/Generic/DrugAlcoholResult/DrugAlcoholResult';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { Validation } from '@/interface-models/Generic/Validation';
import Mitt from '@/utils/mitt';
import { computed, defineProps, onMounted, ref, Ref, watch } from 'vue';

// Props
const props = defineProps<{
  drugAndAlcoholTests: DrugAndAlcoholTests[];
  driverDetails: DriverDetails;
}>();

const validate: Ref<Validation> = computed(() => validationRules);
const drugAndAlcoholTestDialogIsOpen: Ref<boolean> = ref(false);
const deletionRequestSent: Ref<boolean> = ref(false);
const awaitingDriverSaveResponse: Ref<boolean> = ref(false);
const drugAndAlcoholTestDetails: Ref<DrugAndAlcoholTests | null> = ref(null);

const drugAndAlcoholTestForm: Ref<any> = ref(null);

const isAuthorised = () => {
  return hasAdminOrHeadOfficeRole();
};

const resultTypes: DrugAlcoholResult[] = drugAlcoholResult;
const testTypes: { id: number; name: string }[] = [
  { id: 1, name: 'Drug' },
  { id: 2, name: 'Alcohol' },
];
const headers: TableHeader[] = [
  { text: 'Test', align: 'left', value: 'inductionType', sortable: false },
  { text: 'Date', align: 'left', sortable: false, value: 'date' },
  { text: 'Result', align: 'left', sortable: false, value: 'result' },
  { text: 'Note', align: 'left', value: 'note', sortable: false },
];

// Methods
const deleteTest = () => {
  if (!drugAndAlcoholTestDetails.value) {
    return;
  }
  const drugAndAlcoholTestIndex = props.drugAndAlcoholTests.findIndex(
    (test) => test.id === drugAndAlcoholTestDetails.value!.id,
  );

  if (drugAndAlcoholTestIndex !== -1) {
    props.drugAndAlcoholTests.splice(drugAndAlcoholTestIndex, 1);
    deletionRequestSent.value = true;
    awaitingDriverSaveResponse.value = true;
    props.driverDetails.save(null);
  }
};

const drugAndAlcoholTestTableData = computed(() =>
  props.drugAndAlcoholTests.map((test) => {
    const testType = testTypes.find((type) => type.id === test.testType);
    const testDate = test.testDate ? returnFormattedDate(test.testDate) : '-';
    const result = resultTypes.find((res) => res.id === test.result);
    return {
      id: test.id,
      testName: testType ? testType.name : '-',
      date: testDate,
      result: result ? result.longName : '-',
      note: test.notes,
    };
  }),
);

const editingExisting = computed(() => {
  const drugAndAlcoholTestDetailsValue = drugAndAlcoholTestDetails.value;
  const foundTest = props.drugAndAlcoholTests.find(
    (test) => test.id === drugAndAlcoholTestDetailsValue!.id,
  );

  return !!foundTest;
});

const editDrugAndAlcoholTestDetails = (id: string | null) => {
  const foundTest = props.drugAndAlcoholTests.find((test) => test.id === id);

  if (foundTest) {
    drugAndAlcoholTestDetails.value = JSON.parse(JSON.stringify(foundTest));
  } else {
    drugAndAlcoholTestDetails.value = new DrugAndAlcoholTests();
  }

  drugAndAlcoholTestDialogIsOpen.value = true;
};

const cancelDrugAndAlcoholTestDetails = () => {
  drugAndAlcoholTestDialogIsOpen.value = false;
  drugAndAlcoholTestDetails.value = null;
  deletionRequestSent.value = false;
};

const saveDrugAndAlcoholTestDetails = () => {
  if (!props.driverDetails) {
    showNotification('Please select a driver before adding a record');
  }
  if (
    !drugAndAlcoholTestDetails.value ||
    !drugAndAlcoholTestForm.value?.validate()
  ) {
    return;
  }

  const existingDrugAndAlcoholTestIndex = props.drugAndAlcoholTests.findIndex(
    (test) => test.id === drugAndAlcoholTestDetails.value!.id,
  );
  if (existingDrugAndAlcoholTestIndex !== -1) {
    props.drugAndAlcoholTests.splice(
      existingDrugAndAlcoholTestIndex,
      1,
      drugAndAlcoholTestDetails.value,
    );
  } else {
    props.drugAndAlcoholTests.push(drugAndAlcoholTestDetails.value);
  }
  awaitingDriverSaveResponse.value = true;
  props.driverDetails.save(null);
};

const setSavedDriverDetails = (driverDetails: DriverDetails | null) => {
  if (
    driverDetails &&
    driverDetails.driverId === props.driverDetails.driverId &&
    drugAndAlcoholTestDialogIsOpen.value
  ) {
    const successType: string = !deletionRequestSent.value
      ? 'saved.'
      : 'removed.';

    showNotification('Drug or alcohol test successfully ' + successType, {
      type: HealthLevel.SUCCESS,
      title: 'Driver Details - Drug and Alcohol Tests',
    });

    cancelDrugAndAlcoholTestDetails();
  }
};

const listenForDriverUpdates = (listen: boolean) => {
  if (!listen) {
    Mitt.off('savedNewDriver');
    return;
  }
  Mitt.on('savedNewDriver', (savedDriver: SaveDriverDetailsResponse) => {
    if (awaitingDriverSaveResponse.value) {
      const driverDetails = savedDriver?.driverDetails?.driver ?? null;
      setSavedDriverDetails(driverDetails);
      awaitingDriverSaveResponse.value = false;
    }
  });
};

// Lifecycle Hooks
onMounted(() => {
  listenForDriverUpdates(true);
});

watch(
  () => props.driverDetails,
  (newVal) => {
    listenForDriverUpdates(!!newVal);
  },
);
</script>

<style scoped>
.add-test-btn {
  position: absolute;
  top: 1px;
  right: 10px;
  z-index: 198 !important;
}
</style>
