<template>
  <div class="">
    <div class="flex-row">
      <v-tooltip bottom>
        <template v-slot:activator="{ on }">
          <v-btn
            icon
            flat
            @click="confirmEmailUpdateLink = true"
            v-on="on"
            :disabled="props.disabled"
          >
            <v-icon size="18" color="orange">far fa-edit</v-icon>
          </v-btn>
        </template>
        <span>Request Update</span>
      </v-tooltip>

      <GDialog
        v-if="confirmEmailUpdateLink"
        :width="'500px'"
        :title="'Request to update details - confirmation'"
        :confirmBtnText="'Confirm'"
        @closeDialog="confirmEmailUpdateLink = false"
        @confirm="sendUpdateDriverDetailsRequest"
      >
        <div class="pa-3">
          <div class="email-message">
            <p class="mb-3">
              Please confirm that you wish to email an 'Update Account Details'
              link to the following recipient:
            </p>

            <div class="mb-3">
              <p class="mb-1">
                <b>Recipient:</b>
                {{ props.firstName }}
                {{ props.lastName }}
              </p>
              <p>
                <b>Email Address:</b>
                {{ props.emailAddress }}
              </p>
            </div>
            <p>
              Please review the information above and ensure that you intend to
              send this email before proceeding. Your confirmation is
              appreciated.
            </p>
          </div>
        </div>
      </GDialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, Ref } from 'vue';

import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { DriverUpdateDetailsResponse } from '@/interface-models/Driver/DriverDetails/DriverDetails';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import { useWebsocketStore } from '@/store/modules/WebsocketStore';
import Mitt from '@/utils/mitt';
// const emit = defineEmits([
//   'setIsEmailVerificationLoading',
//   'setIsMobileVerificationLoading',
// ]);

const confirmEmailUpdateLink: Ref<boolean> = ref(false);
interface IProps {
  _id?: string;
  firstName: string;
  lastName: string;
  emailAddress: string;
  mobileNumber: string;
  disabled?: boolean;
}
const props = withDefaults(defineProps<IProps>(), {
  disabled: false,
  _id: '',
});

/** Sends a WebSocket request to update driver details via email. On receiving a response, it shows a
 * notification based on the success or failure of the update. It listens for a specific response
 * event and removes the listener after handling the response.
 */
function sendUpdateDriverDetailsRequest(): void {
  try {
    if (!props._id) {
      return;
    }
    useWebsocketStore().sendWebsocketRequest(
      new WebSocketRequest('/driverDetails/updateDetails', props._id, false),
    );
    Mitt.on(
      'sentDriverUpdateDetailsRequestResponse',
      (sentUpdateEmail: DriverUpdateDetailsResponse) => {
        if (sentUpdateEmail.isSuccess) {
          showNotification('Update email successfully sent', {
            title: 'Driver - Update Details',
            type: HealthLevel.SUCCESS,
          });
          confirmEmailUpdateLink.value = false;
        } else {
          showNotification(
            'Something went wrong when attempting to send the update details email.',
            {
              title: 'Driver Details - Update Details',
              type: HealthLevel.ERROR,
            },
          );
        }
        Mitt.off('sentDriverUpdateDetailsRequestResponse');
      },
    );
  } catch (e) {
    console.error(e);
  }
}
</script>
<style scoped lang="scss">
p {
  color: var(--text-color);
}
</style>
