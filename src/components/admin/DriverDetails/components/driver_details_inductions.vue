<template>
  <div class="driver-details-inductions-container">
    <v-data-table
      class="gd-dark-theme"
      :headers="headers"
      :items="inductionDetailsTableData"
      :rows-per-page-items="[10, 20]"
    >
      <template v-slot:items="props">
        <tr
          @click="editInductionDetails(props.item.inductionTypeId)"
          style="cursor: pointer"
        >
          <td>{{ props.item.inductionType }}</td>
          <td>{{ props.item.certificateId }}</td>
          <td>{{ props.item.inductionDate }}</td>
          <td>{{ props.item.expiry }}</td>
        </tr>
      </template>
    </v-data-table>

    <ContentDialog
      v-if="inductionDetails"
      :showDialog.sync="inductionsDetailsDialogIsOpen"
      :title="'Induction - ' + inductionName"
      :isConfirmUnsaved="isConfirmUnsaved"
      :width="'600px'"
      :confirmBtnText="'save'"
      @confirm="saveInduction"
      :isLoading="isLoading || awaitingDriverSaveResponse"
      contentPadding="pa-0"
      @cancel="cancelInductionDetails"
    >
      <v-form ref="inductionForm">
        <v-layout row wrap class="body-scrollable--75 body-min-height--65 pa-3">
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                  >
                    Induction Date
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <DateTimeInputs
                  :epochTime.sync="inductionDetails.inductionDate"
                  :enableValidation="true"
                  :type="DateTimeType.DATE_START_OF_DAY"
                  dateLabel="Induction Date"
                  :soloInput="true"
                  :isRequired="true"
                  :boxInput="false"
                  :hintTextType="HintTextType.FORMATTED_SELECTION"
                ></DateTimeInputs>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                  >
                    Expiry Date
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <DateTimeInputs
                  :epochTime.sync="inductionDetails.expiry"
                  :enableValidation="true"
                  :type="DateTimeType.DATE_START_OF_DAY"
                  dateLabel="Expiry Date"
                  :soloInput="true"
                  :isRequired="true"
                  :boxInput="false"
                  :hintTextType="HintTextType.FORMATTED_SELECTION"
                ></DateTimeInputs>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">Certificate ID</h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  label="Certificate ID"
                  v-model.trim="inductionDetails.certificate"
                  color="light-blue"
                  autofocus
                ></v-text-field>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12 pb-2>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                  >
                    Status:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-checkbox
                  v-model="inductionDetails.isActive"
                  label="Induction is Active"
                  persistent-hint
                  :hint="
                    inductionDetails.isActive
                      ? 'This Induction will appear in Compliance requirements'
                      : 'This Induction will not appear in Compliance requirements'
                  "
                  color="light-blue"
                  class="mt-2"
                />
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">Upload Certificate</h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <file-upload
                  :imageLabel="'Certificate Upload'"
                  :attachmentSingle="false"
                  :documentTypeId="attachmentTypes.INDUCTION_ATTACHMENT"
                  :attachmentArray="inductionDetails.attachments"
                  :isLoading.sync="isLoading"
                >
                </file-upload>
              </v-flex>
            </v-layout>
          </v-flex>
        </v-layout>
      </v-form>
    </ContentDialog>
  </div>
</template>

<script setup lang="ts">
import DateTimeInputs, {
  DateTimeType,
  HintTextType,
} from '@/components/common/date-picker/date_time_inputs.vue';
import FileUpload from '@/components/common/file-upload/index.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import InductionDetails from '@/interface-models/Driver/DriverDetails/AdditionalObjects/InductionDetails';
import {
  DriverDetails,
  SaveDriverDetailsResponse,
} from '@/interface-models/Driver/DriverDetails/DriverDetails';
import AttachmentTypes from '@/interface-models/Generic/Attachment/AttachmentTypes';
import { InductionType } from '@/interface-models/Generic/InductionType/InductionType';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import Mitt from '@/utils/mitt';
import { ComputedRef, Ref, computed, onMounted, ref, watch } from 'vue';

const props = defineProps<{
  inductionDetailsList: InductionDetails[];
  isEdited?: boolean;
  driverDetails: DriverDetails;
}>();

// const inductionTypes: InductionType[] = inductionType;
const attachmentTypes = AttachmentTypes;
const inductionDetails = ref<InductionDetails | null>(null);
const inductionsDetailsDialogIsOpen = ref(false);
const awaitingDriverSaveResponse = ref(false);
const isLoading = ref(false);
const unEditedInductionDetails = ref<InductionDetails | null>(null);

const formRef: Ref<any> = ref(null);

const inductionTypes: ComputedRef<InductionType[]> = computed(() => {
  return useDriverDetailsStore().inductionTypesList;
});

const headers: TableHeader[] = [
  { text: 'Induction', align: 'left', value: 'inductionType', sortable: false },
  {
    text: 'Certificate ID',
    align: 'left',
    sortable: false,
    value: 'certificateId',
  },
  {
    text: 'Induction Date',
    align: 'left',
    sortable: false,
    value: 'inductionDate',
  },
  { text: 'Expiry', align: 'left', value: 'expiry', sortable: false },
];

const inductionDetailsTableData = computed(() =>
  inductionTypes.value.map((induction: InductionType) => {
    const existingInduction = props.inductionDetailsList.find(
      (x: InductionDetails) => x.inductionType === induction.id,
    );

    const inductionDate =
      existingInduction && existingInduction.inductionDate
        ? returnFormattedDate(existingInduction.inductionDate)
        : '-';

    const expiry =
      existingInduction && existingInduction.expiry
        ? returnFormattedDate(existingInduction.expiry)
        : '-';

    const certificateId = existingInduction
      ? existingInduction.certificate
      : '-';

    return {
      inductionType: induction.longName,
      inductionTypeId: induction.id,
      inductionDate,
      expiry,
      certificateId,
    };
  }),
);

const isConfirmUnsaved = computed(
  () =>
    JSON.stringify(unEditedInductionDetails.value) !==
    JSON.stringify(inductionDetails.value),
);

const editInductionDetails = (inductionTypeId: string) => {
  const induction = props.inductionDetailsList.find(
    (x: InductionDetails) => x.inductionType === inductionTypeId,
  );

  if (induction) {
    inductionDetails.value = JSON.parse(JSON.stringify(induction));
  } else {
    inductionDetails.value = new InductionDetails();
    inductionDetails.value.inductionType = inductionTypeId;
  }

  unEditedInductionDetails.value = JSON.parse(
    JSON.stringify(inductionDetails.value),
  );

  inductionsDetailsDialogIsOpen.value = true;
};

const inductionName = computed(() => {
  if (!inductionDetails.value) {
    return '';
  }
  const induction = inductionTypes.value.find(
    (x: InductionType) => x.id === inductionDetails.value!.inductionType,
  );
  return induction ? induction.longName : '';
});

const cancelInductionDetails = () => {
  inductionsDetailsDialogIsOpen.value = false;
  inductionDetails.value = null;
  awaitingDriverSaveResponse.value = false;
  inductionDetails.value = null;
};

const saveInduction = () => {
  if (!props.driverDetails) {
    showNotification('Please select a driver before adding an Induction');
  }
  if (!inductionDetails.value || formRef.value?.validate()) {
    return;
  }
  const existingInductionIndex = props.inductionDetailsList.findIndex(
    (x: InductionDetails) =>
      x.inductionType === inductionDetails.value!.inductionType,
  );

  if (existingInductionIndex !== -1) {
    props.inductionDetailsList.splice(
      existingInductionIndex,
      1,
      inductionDetails.value,
    );
  } else {
    props.inductionDetailsList.push(inductionDetails.value);
  }
  awaitingDriverSaveResponse.value = true;
  props.driverDetails.save(null);
};

const setSavedDriverDetails = (driverDetails: DriverDetails | null) => {
  if (
    driverDetails &&
    driverDetails.driverId === props.driverDetails.driverId &&
    inductionsDetailsDialogIsOpen.value
  ) {
    showNotification('Induction successfully saved.', {
      type: HealthLevel.SUCCESS,
      title: 'Driver Details - Inductions',
    });
  } else {
    showNotification(GENERIC_ERROR_MESSAGE);
  }

  cancelInductionDetails();
};

/**
 * Configures a listener to react to saved driver details events based on the provided `listen` flag. When enabled,
 * it listens for 'savedNewDriver' events and updates the local state with the received driver details.
 * Please note that we don't clear the listener in this component. We do so in the main driver_details_index on destroy.
 * Possible better solution here is to emit the save request back so it is all handled in driver_details_index; that way we
 * wouldnt require listeners in every driver sub component.
 *
 * @param {boolean} listen - Determines whether to start or stop listening for driver update events.
 * @returns {void}
 */
const listenForDriverUpdates = (listen: boolean): void => {
  if (!listen) {
    Mitt.off('savedNewDriver');
    return;
  }
  Mitt.on('savedNewDriver', (savedDriver: SaveDriverDetailsResponse) => {
    if (awaitingDriverSaveResponse.value) {
      const driverDetails: DriverDetails | null =
        savedDriver &&
        savedDriver.driverDetails &&
        savedDriver.driverDetails.driver
          ? savedDriver.driverDetails.driver
          : null;
      setSavedDriverDetails(driverDetails);
      awaitingDriverSaveResponse.value = false;
    }
  });
};

// Lifecycle Hooks
onMounted(() => {
  listenForDriverUpdates(true);
});

watch(
  () => props.driverDetails,
  (newVal) => {
    listenForDriverUpdates(!!newVal);
  },
);
</script>
