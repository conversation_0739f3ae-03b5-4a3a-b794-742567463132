<template>
  <v-dialog
    v-model="driverDetailsDialogIsOpen"
    content-class="v-dialog-custom"
    persistent
    width="90%"
  >
    <DriverDetailsIndex
      :isDialog="true"
      v-if="driverDetailsDialogIsOpen"
      :fleetAssetOwner="fleetAssetOwner"
    />
    <img
      v-else
      src="@/static/loader/infinity-loader-light.svg"
      height="80px"
      width="80px"
    />
  </v-dialog>
</template>

<script setup lang="ts">
import DriverDetailsIndex from '@/components/admin/DriverDetails/driver_details_index.vue';
import FleetAssetOwner from '@/interface-models/FleetAssetOwner/FleetAssetOwner';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { ComputedRef, WritableComputedRef, computed } from 'vue';

const props = withDefaults(
  defineProps<{
    isFleetAssetOwner: boolean;
    fleetAssetOwner: FleetAssetOwner;
  }>(),
  {
    isFleetAssetOwner: false,
  },
);

const driverDetailsStore = useDriverDetailsStore();

const selectedDriverDetailsId: ComputedRef<string | null> = computed(() => {
  return driverDetailsStore.selectedDriverDetailsId;
});

const driverDetailsDialogIsOpen: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return selectedDriverDetailsId.value ? true : false;
  },
  set(value: boolean): void {
    if (!value) {
      closeDriverDetailsDialog();
    }
  },
});

const closeDriverDetailsDialog = (): void => {
  driverDetailsStore.setSelectedDriverDetailsId(null);
};
</script>
