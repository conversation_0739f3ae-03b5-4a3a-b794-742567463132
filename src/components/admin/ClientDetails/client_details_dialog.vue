<template>
  <v-dialog
    v-model="fleetAssetOwnerDialogIsOpen"
    content-class="v-dialog-custom"
    persistent
    width="85%"
  >
    <FleetAssetOwnerIndex :isDialog="true" v-if="fleetAssetOwnerDialogIsOpen" />
  </v-dialog>
</template>

<script setup lang="ts">
import { computed, WritableComputedRef } from 'vue';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import FleetAssetOwnerIndex from '@/components/admin/FleetAssetOwner/fleet_asset_owner_index.vue';

const fleetAssetOwnerDialogIsOpen: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return !!useFleetAssetOwnerStore().selectedFleetAssetOwnerId;
  },
  set(value: boolean): void {
    if (!value) {
      closeFleetAssetOwnerDialog();
    }
  },
});

const closeFleetAssetOwnerDialog = (): void => {
  useFleetAssetOwnerStore().setSelectedFleetAssetOwnerId(null);
};
</script>
