import ServiceTypeObject from '@/interface-models/Generic/ServiceTypes/ServiceTypeObject';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import DistanceRateType from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/DistanceRateType';
import RangedRate from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/RangedRate';

// POINT to POINT Rate Items and Headers
export const pointToPointHeaders: TableHeader[] = [
  {
    text: 'Service Type',
    align: 'left',
    value: 'serviceType',
    sortable: false,
  },
  { text: 'From', align: 'left', value: 'from', sortable: false },
  { text: 'To', align: 'left', value: 'to', sortable: false },
  { text: 'Rate', align: 'left', value: 'rate', sortable: false },
  {
    text: 'Percentage',
    align: 'left',
    value: 'percentage',
    sortable: false,
  },
  { text: 'Demurrage', align: 'left', value: 'demurrage', sortable: false },
  {
    text: 'Fuel Surcharge',
    align: 'left',
    value: 'fuelSurcharge',
    sortable: false,
  },
  {
    text: 'Source',
    align: 'left',
    value: 'source',
    sortable: false,
  },
];

// Unit Rate Items and Headers
export const unitHeaders: TableHeader[] = [
  { text: 'Unit Type', align: 'left', value: 'unitType', sortable: false },
  { text: 'Rate', align: 'left', value: 'rate', sortable: false },
  { text: 'Zone', align: 'left', value: 'zone', sortable: false },
  {
    text: 'Per Amount',
    align: 'left',
    value: 'perAmount',
    sortable: false,
  },
  { text: 'Forklift', align: 'left', value: 'forklift', sortable: false },
  { text: 'PU Flagfall', align: 'left', value: 'puFlagFall', sortable: false },
  { text: 'DO Flagfall', align: 'left', value: 'doFlagFall', sortable: false },
  {
    text: 'FL PU Flagfall',
    align: 'left',
    value: 'flPuFlagFall',
    sortable: false,
  },
  {
    text: 'FL DO Flagfall',
    align: 'left',
    value: 'flDoFlagFall',
    sortable: false,
  },
  { text: 'DG Flagfall', align: 'left', value: 'dgFlagFall', sortable: false },
  { text: 'Demurrage', align: 'left', value: 'demurrage', sortable: false },
  {
    text: 'Source',
    align: 'left',
    value: 'source',
    sortable: false,
  },
];

// Zone Rate Items and Headers
export const zoneHeaders: TableHeader[] = [
  {
    text: 'Service Type / Zone',
    align: 'left',
    value: 'serviceType',
    sortable: false,
  },
  {
    text: 'Zone Rate',
    align: 'left',
    value: 'zoneRate',
    sortable: false,
  },
  {
    text: 'Pickup Flag Fall',
    align: 'left',
    value: 'pickupFlagfall',
    sortable: false,
  },
  {
    text: 'Drop-off Flag Fall',
    align: 'left',
    value: 'dropoffFlagfall',
    sortable: false,
  },
  {
    text: 'Percentage',
    align: 'left',
    value: 'percentage',
    sortable: false,
  },
  {
    text: 'Demurrage',
    align: 'left',
    value: 'demurrage',
    sortable: false,
  },
  {
    text: 'Fuel Surcharge',
    align: 'left',
    value: 'fuelSurcharge',
    sortable: false,
  },
  {
    text: 'Source',
    align: 'left',
    value: 'source',
    sortable: false,
  },
];

export const timeHeaders: TableHeader[] = [
  {
    text: 'Service Type',
    align: 'left',
    value: 'serviceType',
    sortable: false,
  },
  { text: 'Rate', align: 'left', value: 'rate', sortable: false },
  { text: 'Min Charge', align: 'left', value: 'minCharge', sortable: false },
  {
    text: 'Charge Increment',
    align: 'left',
    value: 'chargeIncrement',
    sortable: false,
  },
  { text: 'Grace', align: 'left', value: 'grace', sortable: false },
  {
    text: 'Standby Rate',
    align: 'left',
    value: 'standbyRate',
    sortable: false,
  },
  {
    text: 'Fuel Surcharge',
    align: 'left',
    value: 'fuelSurcharge',
    sortable: false,
  },
  {
    text: 'Source',
    align: 'left',
    value: 'source',
    sortable: false,
  },
];

export const distanceHeaders: TableHeader[] = [
  {
    text: 'Service Type',
    align: 'left',
    value: 'serviceType',
    sortable: false,
  },
  {
    text: 'Base Freight',
    align: 'left',
    value: 'baseFreight',
    sortable: false,
  },
  {
    text: 'Calculation',
    align: 'left',
    value: 'calculation',
    sortable: false,
  },
  {
    text: 'Rate Increment',
    align: 'left',
    value: 'increment',
    sortable: false,
  },
  {
    text: 'First Leg / Last Leg',
    align: 'left',
    value: 'legs',
    sortable: false,
  },
  {
    text: 'Minimum Charge',
    align: 'left',
    value: 'minimumCharge',
    sortable: false,
  },
  {
    text: 'Demurrage',
    align: 'left',
    value: 'demurrage',
    sortable: false,
  },
  {
    text: 'Range',
    align: 'left',
    value: 'range',
    sortable: false,
  },
  {
    text: 'Range Rate',
    align: 'left',
    value: 'range',
    sortable: false,
  },
  {
    text: 'Source',
    align: 'left',
    value: 'source',
    sortable: false,
  },
];

// Common base fields
interface GroupedDataBase {
  isServiceHeader: boolean;
  serviceTypeName: string;
  serviceTypeId: number | null;
  rateTableItem: ServiceTypeObject; // replace with your actual service type
}

// Zone Service Header Row
interface GroupedDataHeaderRow extends GroupedDataBase {
  isServiceHeader: true;
  isRangeRow?: false;
}

// Zone Child Range Row
interface GroupedDataZoneRow extends GroupedDataBase {
  isServiceHeader: false;
  isRangeRow: true;
  index: number;
  rate: number | string;
  zoneRate: string;
  additionalPickUpFlagFall: number;
  pickupFlagfall: string;
  additionalDropOffFlagFall: number;
  dropoffFlagfall: string;
  demurrageGrace: string;
  demurrageRate: string;
  demurrage: number;
  appliedFuelSurcharge: string;
  appliedDemurrageCharge: string;
  calculation: string;
  isClient: boolean;
  demurrageFuelSurchargeApplies: 'Apply' | "Don't Apply";
}

// Distance Main Row
interface GroupedDataDistanceRow extends GroupedDataBase {
  isServiceHeader: false;
  isDistanceRow: true;
  isRangeRow?: false;
  baseFreight: string;
  calculation: string;
  increment: string;
  legs: string;
  minimumCharge: string;
  demurrageGrace: string;
  demurrageRate: string;
  demurrage: number;
  rangeText: string;
  range: string;
  rateTypeObject: DistanceRateType;
  rangeRate: RangedRate | null;
}

// Distance Child Range Row
interface GroupedDataDistanceRangeRow {
  isServiceHeader: false;
  isDistanceRow: true;
  isRangeRow: true;
  rangeText: string;
  range: string;
  rateTableItem: ServiceTypeObject;
  rateTypeObject: DistanceRateType;
  rangeRate: RangedRate;
}

// GROUPED TABLE ITEM
export type GroupedDataItem =
  | GroupedDataHeaderRow
  | GroupedDataZoneRow
  | GroupedDataDistanceRow
  | GroupedDataDistanceRangeRow;
