<template>
  <v-layout class="client-details-integrations" wrap>
    <v-flex md8 offset-md2 class="">
      <v-layout mt-3 row wrap>
        <v-flex md12 pb-1
          ><v-layout align-center>
            <h5 class="subheader--bold pr-3 pt-1">
              Electronic Data Interchange
            </h5>
            <v-flex>
              <v-divider></v-divider>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout class="pt-3">
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">FTP Integration</h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-layout>
                    <v-select
                      label="Select an integration type..."
                      v-model="clientDetails.importTransformationType"
                      :items="importUserList"
                      item-text="displayName"
                      item-value="username"
                      class="v-solo-custom"
                      solo
                      :disabled="true"
                      color="light-blue"
                      flat
                    />

                    <ImportUserSelect :clientDetails="clientDetails" />
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout class="pt-3">
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">API Integration</h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-layout>
                    <v-select
                      label="Select an integration type..."
                      v-model="clientsApiUsers"
                      :items="apiUserList"
                      class="v-solo-custom"
                      solo
                      multiple
                      :disabled="true"
                      color="light-blue"
                      flat
                    />

                    <ApiUserSelect
                      :clientDetails="clientDetails"
                      @setClientsApiUsers="setClientsApiUsers"
                    />
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import ApiUserSelect from '@/components/admin/ClientDetails/components/api_user_select.vue';
import ImportUserSelect from '@/components/admin/ClientDetails/components/import_user_select.vue';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import ImportUser from '@/interface-models/Generic/ImportUser';
import { useDataImportStore } from '@/store/modules/DataImportStore';
import { computed, ref } from 'vue';

const props = withDefaults(
  defineProps<{
    clientDetails: ClientDetails;
    isEdited: boolean;
  }>(),
  {
    isEdited: false,
  },
);
const dataImportStore = useDataImportStore();

const clientsApiUsers = ref<string[]>([]);

// A list of all available import (FTP) users
const importUserList = computed<ImportUser[]>(() => {
  return dataImportStore.importUserList;
});

// A list of all available api users
const apiUserList = computed<string[]>(() => {
  return dataImportStore.apiUserList;
});

// $emit from api_user_select. Contains the clients associated api username list
const setClientsApiUsers = (usernames: string[]) => {
  clientsApiUsers.value = usernames;
};
</script>
