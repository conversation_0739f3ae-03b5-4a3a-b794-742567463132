<template>
  <div>
    <v-layout class="pl" align-center style="height: 54px">
      <v-tooltip bottom>
        <template v-slot:activator="{ on }">
          <v-btn
            icon
            flat
            @click="setDialog(true)"
            v-on="on"
            :disabled="!isAuthorised()"
          >
            <v-icon size="18" color="orange">far fa-edit </v-icon>
          </v-btn>
        </template>
        <span>Add API User</span>
      </v-tooltip>
    </v-layout>

    <ContentDialog
      v-if="dialogIsOpen"
      :showDialog.sync="dialogIsOpen"
      :title="'API Provider Selection'"
      :width="'600px'"
      :isDisabled="!isAuthorised()"
      :confirmBtnText="'save'"
      @confirm="saveClientAPIUser"
      :isLoading="awaitingSaveResponse"
      @cancel="setDialog(false)"
    >
      <v-form>
        <v-layout row wrap>
          <v-flex md12>
            <v-select
              v-model="clientsApiUsernames"
              :items="apiUserList"
              class="v-solo-custom"
              solo
              multiple
              clearable
              hint="Please select the API Providers for this client"
              persistent-hint
              color="light-blue"
              flat
            />
          </v-flex>
        </v-layout>
      </v-form>
    </ContentDialog>
  </div>
</template>

<script setup lang="ts">
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import {
  APIListUsernamesForClientRequest,
  APIListUsernamesForClientResponse,
  ClientApiUserUpdate,
} from '@/interface-models/Client/APIListUsernamesForClientResponse';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useDataImportStore } from '@/store/modules/DataImportStore';
import { sessionManager } from '@/store/session/SessionState';
import { ComputedRef, Ref, computed, onMounted, ref } from 'vue';

const props = withDefaults(
  defineProps<{
    clientDetails: ClientDetails;
    isEdited?: boolean;
  }>(),
  {
    isEdited: false,
  },
);

const dataImportStore = useDataImportStore();

const awaitingSaveResponse: Ref<boolean> = ref(false);
const dialogIsOpen: Ref<boolean> = ref(false);

const clientsApiUsernames: Ref<string[]> = ref([]);
const initialApiUsernames: Ref<string[]> = ref([]);

// response handler for get clients api user name list. We emit this value back to the parent as it requires this list in the main disabled select input
const emit = defineEmits(['setClientsApiUsers']);

function isAuthorised(): boolean {
  return hasAdminRole();
}

// A list of all available api users
const apiUserList: ComputedRef<string[]> = computed(
  () => dataImportStore.apiUserList,
);

// handles dialog open and close. On close we set the clients api username list back to the original value. This is because the user might have made changes they don't wish to save.
const setDialog = (isOpen: boolean): void => {
  dialogIsOpen.value = isOpen;
  if (!isOpen) {
    clientsApiUsernames.value = JSON.parse(
      JSON.stringify(initialApiUsernames.value),
    );
  }
};

/**
 * Dispatches request to save the client's api usernames.
 */
async function saveClientAPIUser() {
  // If the client has no ID, exit.
  if (!props.clientDetails.clientId || !isAuthorised()) {
    return;
  }
  const clientApiUserUpdate: ClientApiUserUpdate = {
    clientId: props.clientDetails.clientId,
    usernames: clientsApiUsernames.value,
  };
  awaitingSaveResponse.value = true;
  // Send a WebSocket request to the server to update the client's integrations
  const result =
    await useClientDetailsStore().updateClientApiUser(clientApiUserUpdate);
  setSavedApiClient(result);
}

const setClientAPIUsernames = (
  response: APIListUsernamesForClientResponse | null,
): void => {
  if (!response) {
    return;
  }
  clientsApiUsernames.value = response.usernameList;
  emit('setClientsApiUsers', clientsApiUsernames.value);
  initialApiUsernames.value = response.usernameList;
};

/**
 * Response handler for saving the clients api usernames.
 * @param savedApiClient payload from response
 */
const setSavedApiClient = (
  savedApiClient: ClientApiUserUpdate | null,
): void => {
  if (!savedApiClient) {
    showNotification(GENERIC_ERROR_MESSAGE);
    setDialog(false);
    return;
  }
  clientsApiUsernames.value = savedApiClient.usernames;
  emit('setClientsApiUsers', clientsApiUsernames.value);
  initialApiUsernames.value = savedApiClient.usernames;
  awaitingSaveResponse.value = false;
  showNotification('API Providers Successfully updated.', {
    title: 'API Integration',
    type: HealthLevel.INFO,
  });
  awaitingSaveResponse.value = false;
  setDialog(false);
};

// requests this clients list of associated api users
async function getUsernamesForClient(): Promise<void> {
  if (!props.clientDetails || !props.clientDetails.clientId) {
    return;
  }

  // If the jobs source type is not known we will be required to check if the client is associated with any users in our EDI api service
  const apiRequest: APIListUsernamesForClientRequest = {
    company: sessionManager.getCompanyId(),
    division: sessionManager.getDivisionId(),
    clientId: props.clientDetails.clientId,
  };
  setClientAPIUsernames(
    await dataImportStore.getApiUsernamesForClient(apiRequest),
  );
}

onMounted(() => {
  getUsernamesForClient();
});
</script>
