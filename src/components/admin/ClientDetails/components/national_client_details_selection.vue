<template>
  <div class="national-client-details-selection">
    <v-layout>
      <v-text-field
        :disabled="true"
        :value="nationalClientName"
        label="National Client Identification"
        class="v-solo-custom"
        solo
        flat
        color="light-blue"
      >
      </v-text-field>
      <div>
        <v-layout class="pl" align-center style="height: 54px">
          <v-tooltip bottom>
            <template v-slot:activator="{ on }">
              <v-btn
                icon
                flat
                @click="editNationalClientId"
                v-on="on"
                :disabled="
                  isDisabled || nationalClientIdExists || !isAuthorised()
                "
              >
                <v-icon size="18" color="orange">far fa-edit </v-icon>
              </v-btn>
            </template>
            <span>Edit National Client Identification</span>
          </v-tooltip>
        </v-layout>
      </div>
    </v-layout>
    <v-dialog
      v-model="maintenanceDialogIsOpen"
      content-class="v-dialog-custom"
      width="700px"
      persistent
    >
      <v-layout
        justify-space-between
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>National Client Identification</span>
        <div>
          <div
            class="app-theme__center-content--closebutton"
            @click="cancelMaintenance"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </div>
      </v-layout>
      <div class="app-theme__center-content--body dialog-content">
        <v-form ref="nationalClientIdForm">
          <v-layout class="px-3 pt-3">
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6
                      class="subheader--faded pr-3 pb-0 form-field-required-marker"
                    >
                      National Client Name:
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-autocomplete
                    v-model="selectedNationalClientId"
                    :items="clientDetailsStore.nationalClientsList"
                    item-text="name"
                    item-value="_id"
                    class="v-solo-custom"
                    solo
                    flat
                    color="light-blue"
                    persistent-hint
                    :hint="'Please select a national client identification name.'"
                    :rules="[validate.required]"
                    browser-autocomplete="off"
                    auto-select-first
                  >
                    <template v-slot:selection="data">
                      <span>{{ data.item.name }}</span>
                    </template>
                    <template v-slot:items="data">
                      <span>{{ data.item.name }} </span>
                    </template>
                  </v-autocomplete>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
          <v-divider class="mt-2"></v-divider>
          <v-layout justify-space-between>
            <v-btn color="error" depressed outline @click="cancelMaintenance">
              <span>cancel</span>
            </v-btn>
            <div>
              <ConfirmationDialog
                buttonText="Save"
                :message="message"
                title="Confirm National Client Identification"
                @confirm="saveNationalClientIdDetails"
                :isSmallButton="false"
                :buttonDisabled="!isAuthorised()"
                :isOutlineButton="false"
                :isBlockButton="false"
                :buttonColor="'info'"
                :confirmationButtonText="'Confirm'"
                :isCheckboxList="false"
                :dialogIsActive="true"
              >
              </ConfirmationDialog>
            </div>
          </v-layout>
        </v-form>
      </div>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { AddNationalClientIdToClientRequestResponse } from '@/interface-models/Client/ClientDetails/AddNationalClientIdToClientRequestResponse';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import { NationalClientDetails } from '@/interface-models/Client/NationalClientDetails';
import { Validation } from '@/interface-models/Generic/Validation';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { ComputedRef, Ref, computed, onMounted, ref } from 'vue';

const props = withDefaults(
  defineProps<{
    clientDetails: ClientDetails;
    isDisabled?: boolean;
  }>(),
  {
    isDisabled: false,
  },
);

const clientDetailsStore = useClientDetailsStore();

const message = computed(() => {
  const clientName = props.clientDetails.tradingName
    ? props.clientDetails.tradingName
    : props.clientDetails.clientName;
  const nationalClientName = selectedNationalClientDetails.value
    ? selectedNationalClientDetails.value.name
    : '';
  return `Please confirm that you wish to assign national client identifier ${nationalClientName} to ${clientName}.`;
});

const emit = defineEmits(['setUpdatedNationalClientId']);

const nationalClientIdForm: Ref<any> = ref(null);

const nationalClientName: Ref<string> = ref('');

const maintenanceDialogIsOpen: Ref<boolean> = ref(false);
const selectedNationalClientId: Ref<string | null | undefined> = ref(null);

const nationalClientDetailsList: Ref<NationalClientDetails[]> = ref([]);

const selectedNationalClientDetails: ComputedRef<
  NationalClientDetails | undefined
> = computed(() =>
  nationalClientDetailsList.value.find(
    (x: NationalClientDetails) => x._id === selectedNationalClientId.value,
  ),
);

const validate: ComputedRef<Validation> = computed(() => {
  return validationRules;
});

async function editNationalClientId(): Promise<void> {
  nationalClientIdForm.value.resetValidation();

  // Set the local list to a copy of the store values
  const ncdList = clientDetailsStore.nationalClientsList;
  nationalClientDetailsList.value = [...ncdList.map((item) => ({ ...item }))];

  maintenanceDialogIsOpen.value = true;
}

// When a national client id is set we do not allow them to change it. We need
// to implement logic that can handle deletion and changes of national client
// ids first
const nationalClientIdExists: ComputedRef<boolean> = computed(() =>
  props.clientDetails.nationalClientId ? true : false,
);

// handles response for a saved client national client id
function setSavedClientNationalClientId(
  clientNationalClientId: AddNationalClientIdToClientRequestResponse | null,
): void {
  if (!clientNationalClientId) {
    showNotification(GENERIC_ERROR_MESSAGE);
    return;
  }
  if (clientNationalClientId.clientId === props.clientDetails.clientId) {
    props.clientDetails.nationalClientId =
      clientNationalClientId.nationalClientId;

    const foundNationalClientId = nationalClientDetailsList.value.find(
      (x: NationalClientDetails) =>
        x._id === clientNationalClientId.nationalClientId,
    );
    nationalClientName.value = foundNationalClientId
      ? foundNationalClientId.name
      : '';
  }
  cancelMaintenance();
  // we should emit result back to parent so we can update the primary ClientDetails
  emit('setUpdatedNationalClientId', clientNationalClientId);
}

function cancelMaintenance(): void {
  maintenanceDialogIsOpen.value = false;
}

async function saveNationalClientIdDetails() {
  if (!nationalClientIdForm.value.validate()) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }
  if (!props.clientDetails.clientId || !selectedNationalClientId.value) {
    showNotification(GENERIC_ERROR_MESSAGE);
    return;
  }
  const clientNationalClientId: AddNationalClientIdToClientRequestResponse = {
    clientId: props.clientDetails.clientId,
    nationalClientId: selectedNationalClientId.value,
  };
  // Dispatch action to save national client id to client
  const response = await clientDetailsStore.addNationalClientIdToClient(
    clientNationalClientId,
  );
  setSavedClientNationalClientId(response);
}

const isAuthorised = (): boolean => {
  return hasAdminOrHeadOfficeRole();
};

async function setNationalClientName() {
  nationalClientName.value = props.clientDetails.nationalClientName;
}

onMounted(() => {
  setNationalClientName();
});
</script>
