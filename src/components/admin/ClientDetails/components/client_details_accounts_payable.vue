<template>
  <div class="client-details-accounts-payable">
    <v-layout wrap>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">
            1. Client's Accounts Payable Contact Details
          </h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Account Name:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
              v-model="clientDetails.accountsReceivable.accountsName"
              label="Account Name"
              :disabled="!isEdited"
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">E-mail:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
              v-model="clientDetails.accountsReceivable.accountsEmail"
              label="E-mail"
              :disabled="!isEdited"
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Phone Number:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
              v-mask="'#### ### ###'"
              v-model="clientDetails.accountsReceivable.accountsPhone"
              label="Phone Number"
              :disabled="!isEdited"
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Fax Number:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
              v-model="clientDetails.accountsReceivable.accountsFax"
              label="Fax Number"
              :disabled="!isEdited"
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">2. Credit / Spend</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Credit Status:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-layout>
              <v-select
                label="Credit Status"
                :class="[
                  { 'form-field-required': !clientDetails.isPlaceholder },
                ]"
                :rules="[validate.required]"
                v-model="clientDetails.accountsReceivable.creditStatus"
                :items="creditStatusTypes"
                item-text="longName"
                item-value="id"
                :disabled="true"
                class="v-solo-custom"
                @change="setAccountOverdueStatus"
                solo
                :hint="
                  clientDetails.statusList.includes(13)
                    ? 'Credit status unavailable for editing when client is retired.'
                    : ''
                "
                persistent-hint
                color="light-blue"
                flat
              />

              <ClientDetailsUpdateOperationalStatus
                :clientDetails="clientDetails"
                :isEdited="isEdited"
                :clientOperationalStatus="'CREDIT_STATUS'"
              />
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Approved Credit Limit:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
              v-model.number="approvedCreditLimit"
              :prefix="'$'"
              :rules="[
                validate.required,
                validate.nonNegative,
                validate.number,
              ]"
              label="Approved Credit Limit"
              :disabled="!isEdited"
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                Anticipated Weekly Spend:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
              v-model.number="anticipatedWeeklyEarnings"
              :rules="[validate.nonNegative, validate.number]"
              :prefix="'$'"
              :disabled="!isEdited"
              label="Anticipated Weekly Spend"
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout row wrap>
          <v-flex md12 pb-3
            ><v-layout align-center>
              <h5 class="subheader--bold pr-3 pt-1">3. Invoice Run</h5>
              <v-flex>
                <v-divider></v-divider>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">
                    Invoice Job Options:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-select
                  class="v-solo-custom"
                  solo
                  flat
                  color="light-blue"
                  label="Invoice Job Options"
                  v-model="
                    clientDetails.accountsReceivable.invoicePreferences
                      .jobsPerInvoice
                  "
                  :items="jobsPerInvoiceOptions"
                  item-value="value"
                  item-text="text"
                  :hint="jobsPerInvoiceHintText"
                  persistent-hint
                  :disabled="!isEdited"
                ></v-select>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">
                    <span class="pr-2">
                      <InformationTooltip
                        :right="true"
                        :tooltipType="HealthLevel.INFO"
                      >
                        <span slot="content">
                          This determines whether the outgoing invoice email
                          will include a CSV breakdown of the invoice.
                        </span>
                      </InformationTooltip>
                    </span>
                    Include CSV:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-checkbox
                  v-model="
                    clientDetails.accountsReceivable.invoicePreferences
                      .includeCSV
                  "
                  :disabled="!isEdited"
                  color="light-blue"
                  class="mt-2"
                />
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">
                    <span class="pr-2">
                      <InformationTooltip
                        :right="true"
                        :tooltipType="HealthLevel.INFO"
                      >
                        <span slot="content">
                          This determines whether the outgoing invoice email
                          will include a Trading Terms attachment.
                        </span>
                      </InformationTooltip>
                    </span>
                    Include Trading Terms:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-checkbox
                  v-model="
                    clientDetails.accountsReceivable.invoicePreferences
                      .includeTerms
                  "
                  :disabled="!isEdited"
                  color="light-blue"
                  class="mt-2"
                />
              </v-flex>
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout row wrap>
          <v-flex md12 pb-3
            ><v-layout align-center>
              <h5 class="subheader--bold pr-3 pt-1">4. Bank Details</h5>
              <v-flex>
                <v-divider></v-divider>
              </v-flex>
            </v-layout>
          </v-flex>

          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">Bank Name:</h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  color="light-blue"
                  v-model.trim="clientDetails.accountsReceivable.bankName"
                  label="Bank Name"
                  :disabled="!isEdited"
                ></v-text-field>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">Branch:</h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  color="light-blue"
                  v-model.trim="clientDetails.accountsReceivable.bankBranch"
                  label="Bank Branch"
                  :disabled="!isEdited"
                ></v-text-field>
              </v-flex>
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout row wrap>
          <v-flex md12 pb-3
            ><v-layout align-center>
              <h5 class="subheader--bold pr-3 pt-1">5. Trade References</h5>
              <v-flex>
                <v-divider></v-divider>
              </v-flex>
              <v-btn
                outline
                color="blue"
                small
                @click="addOrEditTradeReference()"
                class="v-btn-confirm-custom"
                :disabled="!isEdited"
                >New Reference</v-btn
              >
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-data-table
              class="gd-dark-theme"
              :headers="headers"
              color="light-blue"
              :items="clientDetails.accountsReceivable.tradeReferences"
              :rows-per-page-items="[10, 20]"
            >
              <template v-slot:items="props">
                <tr
                  :style="isEdited ? 'cursor:pointer' : ''"
                  @click="
                    isEdited ? addOrEditTradeReference(props.index) : null
                  "
                >
                  <td>
                    {{
                      props.item.businessName ? props.item.businessName : '-'
                    }}
                  </td>
                  <td>
                    {{ props.item.contactName ? props.item.contactName : '-' }}
                  </td>
                  <td>
                    {{ formatPhoneNumber(props.item.phoneNumber) }}
                  </td>
                  <td>{{ props.item.fax ? props.item.fax : '-' }}</td>
                  <td>{{ props.item.email ? props.item.email : '-' }}</td>
                </tr>
              </template>
              <template v-slot:no-data>
                <v-layout justify-center>
                  No current Trade References.
                </v-layout>
              </template>
            </v-data-table>
            <v-dialog
              v-model="dialogController"
              width="700px"
              class="ma-0"
              persistent
              no-click-animation
              content-class="v-dialog-custom"
            >
              <v-flex md12>
                <v-layout
                  justify-space-between
                  align-center
                  class="task-bar app-theme__center-content--header no-highlight"
                >
                  <span>{{ dialogViewType }} Trade Reference</span>
                  <div
                    class="app-theme__center-content--closebutton"
                    @click="dialogController = false"
                  >
                    <v-icon class="app-theme__center-content--closebutton--icon"
                      >fal fa-times</v-icon
                    >
                  </div>
                </v-layout>
                <v-layout
                  v-if="editingTradeReference"
                  row
                  wrap
                  class="app-theme__center-content--body dialog-content"
                >
                  <v-flex md12>
                    <v-form ref="tradeReferenceDialogForm">
                      <v-layout
                        row
                        wrap
                        class="body-scrollable--65 body-min-height--65"
                        pa-3
                      >
                        <v-flex md12>
                          <v-layout>
                            <v-flex md4>
                              <v-layout
                                align-center
                                class="form-field-label-container"
                              >
                                <span
                                  class="subheader--faded pr-3 pb-0 form-field-required-marker"
                                  >Business Name:</span
                                >
                              </v-layout>
                            </v-flex>
                            <v-flex md8>
                              <v-text-field
                                class="v-solo-custom"
                                solo
                                flat
                                color="light-blue"
                                label="Business Name"
                                :rules="[validate.required]"
                                v-model.trim="
                                  editingTradeReference.businessName
                                "
                              />
                            </v-flex>
                          </v-layout>
                        </v-flex>
                        <v-flex md12>
                          <v-layout>
                            <v-flex md4>
                              <v-layout
                                align-center
                                class="form-field-label-container"
                              >
                                <h6 class="subheader--faded pr-3 pb-0">
                                  Contact Name:
                                </h6>
                              </v-layout>
                            </v-flex>
                            <v-flex md8>
                              <v-text-field
                                label="Contact Name"
                                class="v-solo-custom"
                                solo
                                flat
                                color="light-blue"
                                v-model.trim="editingTradeReference.contactName"
                              />
                            </v-flex>
                          </v-layout>
                        </v-flex>
                        <v-flex md12>
                          <v-layout>
                            <v-flex md4>
                              <v-layout
                                align-center
                                class="form-field-label-container"
                              >
                                <h6 class="subheader--faded pr-3 pb-0">
                                  Phone Number:
                                </h6>
                              </v-layout>
                            </v-flex>
                            <v-flex md8>
                              <v-text-field
                                label="Phone Number"
                                class="v-solo-custom"
                                solo
                                flat
                                color="light-blue"
                                v-mask="'## #### ####'"
                                v-model="editingTradeReference.phoneNumber"
                                :mask="
                                  masks.landlineNumber(
                                    editingTradeReference.phoneNumber,
                                  )
                                "
                              />
                            </v-flex>
                          </v-layout>
                        </v-flex>
                        <v-flex md12>
                          <v-layout>
                            <v-flex md4>
                              <v-layout
                                align-center
                                class="form-field-label-container"
                              >
                                <h6 class="subheader--faded pr-3 pb-0">
                                  Fax Number:
                                </h6>
                              </v-layout>
                            </v-flex>
                            <v-flex md8>
                              <v-text-field
                                label="Fax Number"
                                class="v-solo-custom"
                                solo
                                flat
                                color="light-blue"
                                v-model="editingTradeReference.fax"
                                v-mask="'## #### ####'"
                              />
                            </v-flex>
                          </v-layout>
                        </v-flex>

                        <v-flex md12>
                          <v-layout>
                            <v-flex md4>
                              <v-layout
                                align-center
                                class="form-field-label-container"
                              >
                                <h6 class="subheader--faded pr-3 pb-0">
                                  Email Address:
                                </h6>
                              </v-layout>
                            </v-flex>
                            <v-flex md8>
                              <v-text-field
                                label="Email"
                                class="v-solo-custom"
                                solo
                                flat
                                color="light-blue"
                                :rules="[validate.email]"
                                v-model="editingTradeReference.email"
                              />
                            </v-flex>
                          </v-layout>
                        </v-flex>
                      </v-layout>
                    </v-form>
                  </v-flex>
                  <v-flex md12>
                    <v-divider></v-divider>
                    <v-layout align-center>
                      <v-btn flat color="red" @click="dialogController = false"
                        >Cancel</v-btn
                      >
                      <v-btn
                        outline
                        v-if="dialogViewType === 'EDIT'"
                        color="white"
                        @click="deleteTradeReference()"
                        >Delete</v-btn
                      >
                      <v-spacer></v-spacer>
                      <v-btn
                        depressed
                        color="blue"
                        @click="saveTradeReference()"
                        class="v-btn-confirm-custom"
                        >Save</v-btn
                      >
                    </v-layout>
                  </v-flex>
                </v-layout>
              </v-flex>
            </v-dialog>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import ClientDetailsUpdateOperationalStatus from '@/components/admin/ClientDetails/components/client_details_update_operational_status.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  formatPhoneNumber,
  masks,
} from '@/helpers/StringHelpers/StringHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import type ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import creditStatusTypes from '@/interface-models/Generic/AccountsReceivable/CreditStatusTypes';
import { JobsPerInvoiceOptionEnum } from '@/interface-models/Generic/AccountsReceivable/JobsPerInvoiceOptionEnum';
import TradeReference from '@/interface-models/Generic/AccountsReceivable/TradeReference';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { computed, ComputedRef, ref, Ref, WritableComputedRef } from 'vue';

const props = withDefaults(
  defineProps<{
    clientDetails: ClientDetails;
    isEdited: boolean;
  }>(),
  {
    isEdited: false,
  },
);

const dialogTypes = {
  TABLE: 'TABLE',
  NEW: 'NEW',
  EDIT: 'EDIT',
};

const isViewingAddEditDialog: Ref<boolean> = ref(false);
const dialogViewType = ref('TABLE');
const editingTradeReference = ref<TradeReference | null>(null);
const editingTradeReferenceIndex: Ref<number> = ref(-1);
const validate = ref(validationRules);
const tradeReferenceDialogForm: Ref<any> = ref(null);

const jobsPerInvoiceOptions: {
  value: JobsPerInvoiceOptionEnum;
  text: string;
}[] = [
  {
    value: JobsPerInvoiceOptionEnum.ALL,
    text: 'Include All Jobs',
  },
  {
    value: JobsPerInvoiceOptionEnum.PER_JOB,
    text: 'One Per Job',
  },
  {
    value: JobsPerInvoiceOptionEnum.PER_REF,
    text: 'Once Per Invoice Reference',
  },
];

/**
 * Used in the template to display the hint text for the jobs per invoice
 * select field
 */
const jobsPerInvoiceHintText: ComputedRef<string> = computed(() => {
  switch (
    props.clientDetails.accountsReceivable.invoicePreferences.jobsPerInvoice
  ) {
    case JobsPerInvoiceOptionEnum.ALL:
      return 'A single invoice will be generated each invoice run, which includes all jobs for the period.';
    case JobsPerInvoiceOptionEnum.PER_JOB:
      return 'An invoice will be generated for each job in the invoice run.';
    case JobsPerInvoiceOptionEnum.PER_REF:
      return 'An invoice will be generated for each unique Invoice Reference on the jobs in the invoice run.';
    default:
      return '';
  }
});

/** Open dialog in ADD or EDIT mode depending on if reference is provided  */
function addOrEditTradeReference(idx: number | null = null) {
  dialogViewType.value = idx === null ? dialogTypes.NEW : dialogTypes.EDIT;
  editingTradeReference.value =
    idx === null
      ? new TradeReference()
      : { ...props.clientDetails.accountsReceivable.tradeReferences[idx] };
  editingTradeReferenceIndex.value = idx === null ? -1 : idx;
  isViewingAddEditDialog.value = true;
}

/** Validate and save the new/edited trade reference  */
function saveTradeReference() {
  if (
    !editingTradeReference.value ||
    !tradeReferenceDialogForm.value?.validate()
  ) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }

  const updatedReference = { ...editingTradeReference.value };

  if (dialogViewType.value === dialogTypes.NEW) {
    props.clientDetails.accountsReceivable.tradeReferences.push(
      updatedReference,
    );
  } else {
    if (editingTradeReferenceIndex.value !== -1) {
      props.clientDetails.accountsReceivable.tradeReferences.splice(
        editingTradeReferenceIndex.value,
        1,
        updatedReference,
      );
    }
  }
  isViewingAddEditDialog.value = false;
}

/** Check if we need to add or remove ACCOUNT OVERDUE (7) to statusList when
credit status is changed  */
function setAccountOverdueStatus(): void {
  if (props.clientDetails.accountsReceivable.creditStatus === 2) {
    if (!props.clientDetails.statusList.includes(7)) {
      props.clientDetails.statusList.push(7);
    }
  } else {
    const foundIndex = props.clientDetails.statusList.findIndex((c) => c === 7);
    if (foundIndex !== -1) {
      props.clientDetails.statusList.splice(foundIndex, 1);
    }
  }
}

/** Delete currently edited trade reference  */
function deleteTradeReference(): void {
  if (editingTradeReferenceIndex.value !== -1) {
    props.clientDetails.accountsReceivable.tradeReferences.splice(
      editingTradeReferenceIndex.value,
      1,
    );
  }
  isViewingAddEditDialog.value = false;
}

/** Controls dialog visibility, and resets local working variables on close  */
const dialogController: WritableComputedRef<boolean> = computed({
  get: (): boolean => isViewingAddEditDialog.value,
  set: (value: boolean) => {
    if (!value) {
      editingTradeReference.value = null;
      editingTradeReferenceIndex.value = -1;
      dialogViewType.value = dialogTypes.TABLE;
    }
    isViewingAddEditDialog.value = value;
  },
});

/** Table headers for use in the v-data-table component in the template */
const headers: TableHeader[] = [
  {
    text: 'Business Name',
    align: 'left',
    value: 'businessName',
    sortable: false,
  },
  {
    text: 'Contact Name',
    align: 'left',
    value: 'contactName',
    sortable: false,
  },
  {
    text: 'Phone Number',
    align: 'left',
    value: 'phoneNumber',
    sortable: false,
  },
  {
    text: 'Fax',
    align: 'left',
    sortable: false,
    value: 'fax',
  },
  {
    text: 'Email',
    align: 'left',
    value: 'email',
    sortable: false,
  },
];

const approvedCreditLimit: WritableComputedRef<number | undefined> = computed({
  get(): number | undefined {
    if (
      props.clientDetails.accountsReceivable &&
      props.clientDetails.accountsReceivable.approvedCreditLimit
    ) {
      return props.clientDetails.accountsReceivable.approvedCreditLimit;
    }
    return 0;
  },
  set(value: number | undefined): void {
    if (value !== undefined) {
      props.clientDetails.accountsReceivable.approvedCreditLimit = value;
    }
  },
});

const anticipatedWeeklyEarnings: WritableComputedRef<number | undefined> =
  computed({
    get(): number | undefined {
      if (
        props.clientDetails.accountsReceivable.anticipatedWeeklyEarnings &&
        props.clientDetails.accountsReceivable.anticipatedWeeklyEarnings
      ) {
        return props.clientDetails.accountsReceivable.anticipatedWeeklyEarnings;
      }
      return undefined;
    },
    set(value: number | undefined): void {
      if (value !== undefined) {
        props.clientDetails.accountsReceivable.anticipatedWeeklyEarnings =
          value;
      }
    },
  });
</script>

<style scoped>
.client-details-key-information {
  padding: 0;
}
</style>
