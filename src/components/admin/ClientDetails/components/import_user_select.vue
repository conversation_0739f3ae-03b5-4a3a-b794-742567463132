<template>
  <div>
    <v-layout class="pl" align-center style="height: 54px">
      <v-tooltip bottom>
        <template v-slot:activator="{ on }">
          <v-btn
            icon
            flat
            @click="setDialog(true)"
            v-on="on"
            :disabled="!isAuthorised()"
          >
            <v-icon size="18" color="orange">far fa-edit </v-icon>
          </v-btn>
        </template>
        <span>Add Import User</span>
      </v-tooltip>
    </v-layout>

    <ContentDialog
      v-if="dialogIsOpen"
      :showDialog.sync="dialogIsOpen"
      :title="'FTP Provider Selection'"
      :width="'600px'"
      :confirmBtnText="'save'"
      @confirm="saveClientImportUser"
      :isLoading="awaitingSaveResponse"
      @cancel="setDialog(false)"
    >
      <v-form>
        <v-layout row wrap>
          <v-flex md12>
            <v-select
              v-model="importTransformationType"
              :items="importUserList"
              item-text="displayName"
              item-value="username"
              class="v-solo-custom"
              solo
              clearable
              hint="Please select the FTP Provider for this client"
              persistent-hint
              color="light-blue"
              flat
            />
          </v-flex>
        </v-layout>
      </v-form>
    </ContentDialog>
  </div>
</template>

<script setup lang="ts">
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import ClientImportUserUpdate from '@/interface-models/Client/ClientImportUserUpdate';
import ImportUser from '@/interface-models/Generic/ImportUser';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useDataImportStore } from '@/store/modules/DataImportStore';
import { ComputedRef, Ref, computed, onMounted, ref } from 'vue';

const props = withDefaults(
  defineProps<{
    clientDetails: ClientDetails;
    isEdited?: boolean;
  }>(),
  {
    isEdited: false,
  },
);

const dataImportStore = useDataImportStore();

const importTransformationType: Ref<string | null | undefined> = ref(null);
const awaitingSaveResponse: Ref<boolean> = ref(false);
const dialogIsOpen: Ref<boolean> = ref(false);

function isAuthorised(): boolean {
  return hasAdminRole();
}

// A list of all available import users (FTP)
const importUserList: ComputedRef<ImportUser[]> = computed(
  () => dataImportStore.importUserList,
);

const setDialog = (isOpen: boolean): void => {
  dialogIsOpen.value = isOpen;
};

// set local variables that are defined in the clientDetails;
const setClientsIntegrations = (): void => {
  importTransformationType.value = props.clientDetails.importTransformationType
    ? props.clientDetails.importTransformationType
    : null;
};

// Saves client integrations
async function saveClientImportUser() {
  // If the client has no ID, exit.
  if (!props.clientDetails.clientId || !isAuthorised()) {
    return;
  }
  const clientImportUserUpdate: ClientImportUserUpdate = {
    clientId: props.clientDetails.clientId, // ID of the client
    importUser: importTransformationType.value, // type of import transformation used by the client
  };
  // Indicate that we're waiting for a response from the server
  awaitingSaveResponse.value = true;
  // Send a WebSocket request to the server to update the client's integrations

  const result = await useClientDetailsStore().updateClientImportUser(
    clientImportUserUpdate,
  );
  setClientIntegrationUpdateResponse(result);
}

// response handler for the save client import user request (saveClientImportUser)
const setClientIntegrationUpdateResponse = (
  response: ClientImportUserUpdate | null,
) => {
  if (!response && awaitingSaveResponse.value) {
    showNotification(GENERIC_ERROR_MESSAGE);
    setDialog(false);
    return;
  }
  if (!response || response.clientId !== props.clientDetails.clientId) {
    return;
  }
  showNotification('FTP Provider Successfully updated.', {
    title: 'FTP Integration',
    type: HealthLevel.INFO,
  });
  importTransformationType.value = response.importUser;
  awaitingSaveResponse.value = false;
  setDialog(false);
};

onMounted(() => {
  setClientsIntegrations();
});
</script>
