<template>
  <div class="client-details-shared-emails">
    <v-layout wrap pb-3>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">
            1. Shared Company Email Addresses
          </h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 class="mb-2">
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                {{ returnRoleName(1) }}:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              v-model.trim="dispatcherEmailAddress"
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
              :rules="[dispatcherEmailAddress ? validate.email : true]"
              :label="`${returnRoleName(1)} email`"
              :disabled="!isEdited"
              clearable
              hide-details
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 class="mb-2">
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
            </v-layout>
          </v-flex>
          <v-flex md8>
            <span class="dispatcher-email-txt">
              <v-icon size="16">info</v-icon>
              The Dispatcher Email Address entered here will receive all email
              notifications that are sent to dispatchers in the list
              below.</span
            >
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                {{ returnRoleName(5) }}:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              v-model.trim="reportsEmailAddress"
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
              :rules="[reportsEmailAddress ? validate.email : true]"
              :label="`${returnRoleName(5)} email`"
              :disabled="!isEdited"
              clearable
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                {{ returnRoleName(3) }}:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              v-model.trim="accountsEmailAddress"
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
              :rules="[accountsEmailAddress ? validate.email : true]"
              :label="`${returnRoleName(3)} email`"
              :disabled="!isEdited"
              clearable
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                {{ returnRoleName(4) }}:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              v-model.trim="accountsMgmtEmailAddress"
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
              :rules="[accountsMgmtEmailAddress ? validate.email : true]"
              :label="`${returnRoleName(4)} email`"
              :disabled="!isEdited"
              clearable
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">
            2. Outgoing Job-Update Emails
          </h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12 v-if="!canSetDispatcherEmailType">
        <v-layout>
          <v-spacer />

          <v-flex md8>
            <v-alert type="warning" :value="true">
              <span
                >Before dispatcher email events become available, a dispatcher
                who receives emails must be set up.</span
              >
            </v-alert>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                Booked by Operations User:
              </h6>
            </v-layout>
          </v-flex>

          <v-flex md8>
            <v-checkbox
              class="mt-2"
              label="Booked by Operations User"
              v-model="clientDetails.sendEmailsConfig.sendEmailJobBooked"
              :disabled="!isEdited || !canSetDispatcherEmailType"
              persistent-hint
              color="light-blue"
              :hint="'An email will be sent to the Dispatcher when a job is booked via the Operations portal.'"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Booking by Dispatcher:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              class="mt-2"
              label="Booking by Dispatcher"
              v-model="
                clientDetails.sendEmailsConfig.sendEmailJobBookedByClient
              "
              :disabled="!isEdited || !canSetDispatcherEmailType"
              persistent-hint
              color="light-blue"
              :hint="'An email will be sent to the Dispatcher when a job is booked via the Client Portal.'"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Imported Job Booking:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              class="mt-2"
              label="Imported Job Booking"
              v-model="clientDetails.sendEmailsConfig.sendEmailJobImported"
              :disabled="
                !isEdited ||
                (!canSetDispatcherEmailType &&
                  !clientDetails.sendEmailsConfig.sendEmailJobImported)
              "
              persistent-hint
              color="light-blue"
              :hint="'An email will be sent to the Dispatcher when a job is booked via an import.'"
            />
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Job Update:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              class="mt-2"
              label="Job Update"
              v-model="clientDetails.sendEmailsConfig.sendEmailJobUpdated"
              :disabled="
                !isEdited ||
                (!canSetDispatcherEmailType &&
                  !clientDetails.sendEmailsConfig.sendEmailJobUpdated)
              "
              persistent-hint
              color="light-blue"
              :hint="'An email will be sent to the Dispatcher when the details of a job is updated.'"
            />
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Accepted By Driver:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              class="mt-2"
              label="Accepted By Driver"
              v-model="clientDetails.sendEmailsConfig.sendEmailJobAccepted"
              :disabled="
                !isEdited ||
                (!canSetDispatcherEmailType &&
                  !clientDetails.sendEmailsConfig.sendEmailJobAccepted)
              "
              color="light-blue"
              persistent-hint
              :hint="'An email will be sent to the Dispatcher when a job is accepted by the driver.'"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Job Progress Update:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              class="mt-2"
              label="Job Progress Update"
              v-model="clientDetails.sendEmailsConfig.sendEmailPODJobFinalised"
              :disabled="
                !isEdited ||
                (!canSetDispatcherEmailType &&
                  !clientDetails.sendEmailsConfig.sendEmailPODJobFinalised)
              "
              persistent-hint
              color="light-blue"
              :hint="'An email will be sent to the Dispatcher with a Proof of Delivery attachment when the job is finalised.'"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                Excessive Load Duration:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              class="mt-2"
              label="Driver Exceeded Expected Load Duration"
              v-model="
                clientDetails.sendEmailsConfig.sendEmailDriverAtSiteLonger
              "
              :disabled="
                !isEdited ||
                (!canSetDispatcherEmailType &&
                  !clientDetails.sendEmailsConfig.sendEmailDriverAtSiteLonger)
              "
              persistent-hint
              color="light-blue"
              :hint="'An email will be sent to the Dispatcher when the load duration exceeds the expected load duration.'"
            />
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                Permanent Job Bookings:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              class="mt-2"
              label="Permanent Job Bookings"
              v-model="clientDetails.sendEmailsConfig.sendPermanentJobBooked"
              :disabled="
                !isEdited ||
                (!canSetDispatcherEmailType &&
                  !clientDetails.sendEmailsConfig.sendPermanentJobBooked)
              "
              persistent-hint
              color="light-blue"
              :hint="'An email will be sent to the Dispatcher when a permanent job is booked.'"
            />
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Daily Jobs Report:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              class="mt-2"
              label="Daily Jobs Report"
              v-model="clientDetails.sendEmailsConfig.dailyJobReport"
              :disabled="
                !isEdited ||
                (!canSetDispatcherEmailType &&
                  !clientDetails.sendEmailsConfig.dailyJobReport)
              "
              persistent-hint
              color="light-blue"
              :hint="'An email will be sent to Dispatchers who are marked to receive emails when the bulk Daily Jobs Report is ran.'"
            />
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Invoice:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              class="mt-2"
              label="Invoice"
              v-model="clientDetails.sendEmailsConfig.emailInvoice"
              :disabled="!isEdited"
              persistent-hint
              color="light-blue"
              :hint="'An email will be sent to the clients accounts team on invoicing live run.'"
            />
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </div>
</template>
<script setup lang="ts">
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import DefaultEmail from '@/interface-models/Generic/DefaultEmail/DefaultEmail';
import { Validation } from '@/interface-models/Generic/Validation';
import UserRole from '@/interface-models/Roles/UserRoles';
import {
  ClientIdAndClientPersonWithAuthDetailsList,
  ClientPersonWithAuthDetails,
  ClientRoleStatus,
} from '@/interface-models/User/ClientPerson';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useRootStore } from '@/store/modules/RootStore';
import { useUserManagementStore } from '@/store/modules/UserManagementStore';
import {
  ComputedRef,
  WritableComputedRef,
  computed,
  onBeforeUnmount,
  onMounted,
} from 'vue';

const props = withDefaults(
  defineProps<{
    clientDetails: ClientDetails;
    isEdited?: boolean;
  }>(),
  {
    isEdited: false,
  },
);

const userManagementStore = useUserManagementStore();

const validate: ComputedRef<Validation> = computed(() => {
  return validationRules;
});

const userRolesList = computed<UserRole[]>(() =>
  useRootStore().roleList.filter(
    (role: UserRole) =>
      role.collection === 'ClientDetails' && role.name !== 'Site Contact',
  ),
);

/* Return a string name for the provided role id. Used for labels in html */
const returnRoleName = (roleId: number): string => {
  const foundRole = userRolesList.value.find(
    (u: UserRole) => u.roleId === roleId,
  );
  return foundRole ? foundRole.name : '-';
};

/** Controller for email address field for ROLE ID of 1. Modelled to textfield
 in html */
const dispatcherEmailAddress: WritableComputedRef<string> = computed({
  get(): string {
    return getDefaultEmailForType(1);
  },
  set(email: string): void {
    setDefaultEmailForType(email, 1);
  },
});

/** Controller for email address field for ROLE ID of 5. Modelled to textfield
in html */
const reportsEmailAddress: WritableComputedRef<string> = computed({
  get(): string {
    return getDefaultEmailForType(5);
  },
  set(email: string): void {
    setDefaultEmailForType(email, 5);
  },
});

/** Controller for email address field for ROLE ID of 3. Modelled to textfield
in html */
const accountsEmailAddress: WritableComputedRef<string> = computed({
  get(): string {
    return getDefaultEmailForType(3);
  },
  set(email: string): void {
    setDefaultEmailForType(email, 3);
  },
});

/** Controller for email address field for ROLE ID of 4. Modelled to textfield
in html */
const accountsMgmtEmailAddress: WritableComputedRef<string> = computed({
  get(): string {
    return getDefaultEmailForType(4);
  },
  set(email: string): void {
    setDefaultEmailForType(email, 4);
  },
});

// Get string value of default email address of specified roleId
const getDefaultEmailForType = (roleId: number): string => {
  if (!props.clientDetails.defaultEmails) {
    return '';
  }
  const defaultEmail = props.clientDetails.defaultEmails.find(
    (e) => e.roleId === roleId,
  );
  return defaultEmail ? defaultEmail.email : '';
};

// Set value of default email address of specified roleId. If the default
// email address does not currently exist then add it to
// clientDetails.defaultEmails
const setDefaultEmailForType = (email: string, roleId: number): void => {
  const foundExistingEmail = props.clientDetails.defaultEmails.find(
    (e) => e.roleId === roleId,
  );
  if (foundExistingEmail) {
    foundExistingEmail.email = email;
  } else {
    const defaultEmail = {
      clientPersonRefId: '',
      roleId,
      email,
    };
    props.clientDetails.defaultEmails.push(defaultEmail);
  }
};

const canSetDispatcherEmailType: ComputedRef<boolean> = computed(() => {
  const clientContacts: ClientIdAndClientPersonWithAuthDetailsList | null =
    userManagementStore.clientIdAndClientPersonWithAuthDetailsList;
  if (!clientContacts || clientContacts.client_id !== props.clientDetails._id) {
    return false;
  }
  const allClientPersons: ClientPersonWithAuthDetails[] =
    clientContacts.clientPersonWithAuthDetailsList;
  const clientPersonsWhoReceiveEmails = allClientPersons.filter(
    (x: ClientPersonWithAuthDetails) =>
      x.clientPerson.receivesEmails &&
      x.clientRoles.find((r: ClientRoleStatus) => r.roleId === 1),
  );
  const defaultDispatcherEmail = props.clientDetails.defaultEmails.find(
    (x: DefaultEmail) => x.roleId === 1,
  );
  return (
    clientPersonsWhoReceiveEmails.length > 0 ||
    (defaultDispatcherEmail !== undefined &&
      defaultDispatcherEmail.email !== null &&
      defaultDispatcherEmail.email.length > 0)
  );
});

const getClientPersons = (): void => {
  if (!props.clientDetails || !props.clientDetails._id) {
    return;
  }
  const clientContacts: ClientIdAndClientPersonWithAuthDetailsList | null =
    userManagementStore.clientIdAndClientPersonWithAuthDetailsList;
  // Check if clients contacts list is already available
  if (clientContacts && clientContacts.client_id === props.clientDetails._id) {
    return;
  }

  // Client contacts list is not let loaded. Fetch client contacts list
  userManagementStore.getClientPersonsWithAuthDetails(
    props.clientDetails._id,
    props.clientDetails.clientPersonDispatchers,
  );
};

onMounted(() => {
  getClientPersons();
});

onBeforeUnmount(() => {
  // clear state on destroy
  useClientDetailsStore().setClientPersons([]);
});
</script>

<style scoped lang="scss">
.dispatcher-email-txt {
  color: var(--accent);
  .v-icon {
    padding-right: 4px;
    color: var(--accent);
  }
}
</style>
