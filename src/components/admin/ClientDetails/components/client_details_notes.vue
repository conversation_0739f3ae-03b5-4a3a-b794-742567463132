<template>
  <v-layout class="client-details-notes">
    <v-flex md8 offset-md2>
      <v-layout>
        <v-flex md12 pb-1>
          <v-layout align-center>
            <h5 class="subheader--bold pr-3 pt-1">Notes</h5>
            <v-flex>
              <v-divider></v-divider>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
      <v-layout>
        <NotesEditor
          :communications="notes"
          :isEdited="isEdited"
          :type="2"
          :emitSaveEventBackToParent="true"
          :isAddingNote="isEdited"
          :allowEdit="true"
          :allowDelete="true"
          @saveParentDocument="saveParentDocument"
          @setIsAddingNote="setNotesEdited"
        />
      </v-layout>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import NotesEditor from '@/components/common/notes_editor/notes_editor.vue';
import { Communication } from '@/interface-models/Generic/Communication/Communication';
import { toRef } from 'vue';

// Define props and emits
const props = withDefaults(
  defineProps<{
    notes: Communication[];
    isEdited: boolean;
    isLoading: boolean;
  }>(),
  {
    isEdited: false,
    isLoading: false,
  },
);

const emit = defineEmits(['setEdited', 'saveParentDocument']);

const isEdited = toRef(props, 'isEdited');

function setNotesEdited(isEdited: boolean) {
  emit('setEdited', isEdited);
}

const saveParentDocument = () => {
  emit('saveParentDocument');
};
</script>

<style scoped>
.add-note-btn {
  position: absolute;
  top: 1px;
  right: 10px;
  z-index: 10 !important;
}
</style>
