<template>
  <v-layout row wrap>
    <v-flex md12 class="pb-1">
      <v-layout>
        <v-flex>
          <v-layout
            align-center
            fill-height
            :style="{ width: 'calc(100% - 300px)' }"
          >
            <GTitle
              :title="textDisplay.tableTitle"
              :subtitle="textDisplay.tableSubtitle"
              :divider="false"
            />
          </v-layout>
        </v-flex>
        <v-flex md4 v-if="sessionManager.isOperationsPortal()">
          <v-layout align-center>
            <v-flex md12>
              <v-text-field
                v-model="search"
                append-icon="search"
                label="Search"
                hide-details
                height="60"
                color="orange"
                outline
                class="v-solo-custom"
              ></v-text-field>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>

    <v-flex md12>
      <GTable
        :headers="headers"
        :items="clientCommonAddressList"
        :selectable="true"
        :search="search"
        :isLoading="!clientCommonAddresses"
        @selectItem="viewCommonAddress"
        :noDataMessage="textDisplay.tableNoDataMessage"
        :height="
          !sessionManager.isClientPortal() ? 'calc(100vh - 223px)' : '100%'
        "
      >
        <template v-slot:items="commonAddress">
          <td>{{ commonAddress.item.addressNickname }}</td>
          <td>
            {{
              commonAddress.item.address.formattedAddress
                ? commonAddress.item.address.formattedAddress
                : '-'
            }}
          </td>
          <td>{{ commonAddress.item.defaultDispatchAddress ? 'Yes' : '-' }}</td>
          <td>
            {{
              getCommonAddressDefaultContactName(
                commonAddress.item.defaultContact,
              )
            }}
          </td>
          <td>
            {{
              getCommonAddressRelatedContactNames(
                commonAddress.item.addressContacts,
                commonAddress.item.defaultContact,
              )
            }}
          </td>
          <td>{{ commonAddress.item.isStandbyRate ? 'Yes' : 'No' }}</td>
          <td>{{ commonAddress.item.isDemurrageRate ? 'Yes' : 'No' }}</td>
        </template>
      </GTable>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import { maskMyString } from '@/helpers/StringHelpers/StringHelpers';
import {
  AddressContactType,
  ClientCommonAddress,
  ClientCommonAddressContact,
} from '@/interface-models/Client/ClientCommonAddress';
import { ClientCommonAddressRelatedContactSave } from '@/interface-models/Client/ClientCommonAddressRelatedContactSave';
import { ClientRelatedContact } from '@/interface-models/Client/ClientRelatedContact';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { ClientPersonWithAuthDetails } from '@/interface-models/User/ClientPerson';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import Fuse, { FuseResult } from 'fuse.js';
import { ComputedRef, Ref, computed, onMounted, onUnmounted, ref } from 'vue';

interface IProps {
  clientId: string;
  clientRelatedContacts: ClientRelatedContact[];
  clientPersonsWithAuthDetailsList: ClientPersonWithAuthDetails[];
}
const emit = defineEmits(['viewCommonAddress']);

const props = withDefaults(defineProps<IProps>(), {
  clientId: '',
});

const clientDetailsStore = useClientDetailsStore();

// search table
const search: Ref<string> = ref('');

const headers: TableHeader[] = [
  {
    text: 'Name',
    align: 'left',
    value: 'addressNickname',
  },
  { text: 'Address', value: 'address.formattedAddress', align: 'left' },
  {
    text: 'Default Dispatch Location',
    value: 'defaultDispatchAddress',
    align: 'left',
  },
  { text: 'Default Contact', value: '-', align: 'left' },
  {
    text: 'Contacts',
    value: '-',
    align: 'left',
  },

  { text: 'Standby Rate', value: 'isStandbyRate', align: 'left' },
  { text: 'Demurrage Rate', value: 'isDemurrageRate', align: 'left' },
];

/**
 * Emits the selected common address from the table. Called via template table row click event.
 *
 * @param {ClientCommonAddress} clientCommonAddress
 * @returns {string}
 */
function viewCommonAddress(clientCommonAddress: ClientCommonAddress): void {
  emit('viewCommonAddress', JSON.parse(JSON.stringify(clientCommonAddress)));
}

/**
 * Returns the name of the default contact - called from table cell in template
 *
 * @param {ClientCommonAddressContact} defaultContact
 * @returns {string}
 */
function getCommonAddressDefaultContactName(
  defaultContact: ClientCommonAddressContact,
): string {
  if (!defaultContact) {
    return '-';
  }
  if (defaultContact.type === AddressContactType.CLIENT_PERSON) {
    const clientPersonWithAuthDetails: ClientPersonWithAuthDetails | undefined =
      props.clientPersonsWithAuthDetailsList.find(
        (x: ClientPersonWithAuthDetails) =>
          x.clientPerson._id === defaultContact.contactId,
      );

    if (clientPersonWithAuthDetails) {
      return (
        clientPersonWithAuthDetails.clientPerson.firstName +
        ' ' +
        clientPersonWithAuthDetails.clientPerson.lastName
      );
    }
  } else if (defaultContact.type === AddressContactType.RELATED_CONTACT) {
    const relatedContact: ClientRelatedContact | undefined =
      props.clientRelatedContacts.find(
        (x: ClientRelatedContact) => x._id === defaultContact.contactId,
      );

    if (relatedContact) {
      return relatedContact.firstName + ' ' + relatedContact.lastName;
    }
  }
  return '-';
}

/**
 * returns a string of common address site contact names
 *
 * @param {ClientCommonAddressContact} addressContacts
 * @param {ClientCommonAddressContact} defaultContact
 * @returns {string}
 */
function getCommonAddressRelatedContactNames(
  addressContacts: ClientCommonAddressContact[],
  defaultContact: ClientCommonAddressContact,
): string {
  const addressRelatedContacts: ClientCommonAddressContact[] =
    addressContacts.filter(
      (x: ClientCommonAddressContact) =>
        x.type === AddressContactType.RELATED_CONTACT,
    );
  const addressClientPersons: ClientCommonAddressContact[] =
    addressContacts.filter(
      (x: ClientCommonAddressContact) =>
        x.type === AddressContactType.CLIENT_PERSON,
    );

  const associatedContactNames: string[] = [];

  for (const contact of addressRelatedContacts) {
    if (defaultContact && defaultContact.contactId === contact.contactId) {
      continue;
    }

    const relatedContact: ClientRelatedContact | undefined =
      props.clientRelatedContacts.find(
        (x: ClientRelatedContact) => x._id === contact.contactId,
      );

    if (relatedContact) {
      associatedContactNames.push(
        relatedContact.firstName + ' ' + relatedContact.lastName,
      );
    }
  }
  for (const commonAddressContact of addressClientPersons) {
    if (
      defaultContact &&
      defaultContact.contactId === commonAddressContact.contactId
    ) {
      continue;
    }
    const clientPersonWithAuthDetails: ClientPersonWithAuthDetails | undefined =
      props.clientPersonsWithAuthDetailsList.find(
        (x: ClientPersonWithAuthDetails) =>
          x.clientPerson._id === commonAddressContact.contactId,
      );

    if (clientPersonWithAuthDetails) {
      associatedContactNames.push(
        clientPersonWithAuthDetails.clientPerson.firstName +
          ' ' +
          clientPersonWithAuthDetails.clientPerson.lastName,
      );
    }
  }
  return associatedContactNames.join(', ');
}

/**
 * Utilise the full list and return a filtered result based on the users searched text. We also include the contact names in this search.
 * @return {ClientCommonAddress[]} List of client common addresses filtered via search text.
 */
const clientCommonAddressList: ComputedRef<ClientCommonAddress[]> = computed(
  () => {
    if (!clientCommonAddresses.value) {
      return [];
    }

    const commonAddresses: ClientCommonAddress[] = JSON.parse(
      JSON.stringify(clientCommonAddresses.value),
    );

    // If no search value is present return users
    if (!search.value) {
      return commonAddresses;
    }

    // because a user might search for  a person when looking for an address we should add the contact names as are searchable item.

    interface ClientCommonAddressAndContactNames extends ClientCommonAddress {
      contactNames?: string;
    }
    const searchList: ClientCommonAddressAndContactNames[] = [];
    for (let i = 0; i < commonAddresses.length; i++) {
      let contactNames: string = '';

      for (const addressContact of commonAddresses[i].addressContacts) {
        if (addressContact.type === AddressContactType.CLIENT_PERSON) {
          const clientPersonWithAuthDetails:
            | ClientPersonWithAuthDetails
            | undefined = props.clientPersonsWithAuthDetailsList.find(
            (x: ClientPersonWithAuthDetails) =>
              x.clientPerson._id === addressContact.contactId,
          );

          if (clientPersonWithAuthDetails) {
            contactNames +=
              clientPersonWithAuthDetails.clientPerson.firstName +
              ' ' +
              clientPersonWithAuthDetails.clientPerson.lastName;
          }
        } else if (addressContact.type === AddressContactType.RELATED_CONTACT) {
          const relatedContact: ClientRelatedContact | undefined =
            props.clientRelatedContacts.find(
              (x: ClientRelatedContact) => x._id === addressContact.contactId,
            );

          if (relatedContact) {
            contactNames +=
              relatedContact.firstName + ' ' + relatedContact.lastName;
          }
        }
      }

      (commonAddresses[i] as ClientCommonAddressAndContactNames).contactNames =
        contactNames;
      searchList.push(commonAddresses[i]);
    }

    // filter users via search value
    const fuse = new Fuse(commonAddresses, {
      includeScore: true,
      threshold: 0.3,
      keys: ['addressNickname', 'address', 'contactNames'],
    });
    const result: ClientCommonAddressAndContactNames[] = fuse
      .search(search.value)
      .map((x: FuseResult<ClientCommonAddressAndContactNames>) => {
        return { ...x.item };
      });

    const filteredClientCommonAddressList: ClientCommonAddress[] = [];

    for (const commonAddress of result) {
      delete commonAddress.contactNames;
      filteredClientCommonAddressList.push(commonAddress);
    }

    return filteredClientCommonAddressList;
  },
);

const clientCommonAddresses: ComputedRef<ClientCommonAddress[]> = computed(
  () => {
    return clientDetailsStore.clientCommonAddresses;
  },
);

/**
 * fetch client site contacts
 * @return {void} void
 */
async function getClientsCommonAddresses(): Promise<void> {
  // fetch client site contacts
  const commonAddresses: ClientCommonAddress[] | null =
    await clientDetailsStore.getClientCommonAddressesByClientId(props.clientId);
  if (!commonAddresses) {
    console.error(
      "Something went wrong when fetching client's common addresses.",
    );
    return;
  }
}

interface TextDisplay {
  tableTitle: string;
  tableSubtitle: string;
  tableNoDataMessage: string;
}

/**
 * Computes text displays based on portal type
 *
 * @returns {TextDisplay} An object containing customized text for different sections within this component.
 */
const textDisplay: ComputedRef<TextDisplay> = computed(() => {
  const isOperations: boolean = sessionManager.isOperationsPortal();
  const tableTitle = isOperations
    ? 'Client Common Addresses'
    : 'Company Common Addresses';

  const tableNoDataMessage: string = isOperations
    ? 'Client has no common addresses'
    : 'No common addresses known.';

  const divisionDetails = useCompanyDetailsStore().divisionDetails;
  const clientPortalAlertMessage = isOperations
    ? ''
    : divisionDetails && divisionDetails.name && divisionDetails.phone
      ? 'Please contact ' +
        divisionDetails.name +
        ' at ' +
        maskMyString(divisionDetails.phone, '## #### ####') +
        ' if you would like to update a common address.'
      : '';

  const tableSubtitle = isOperations
    ? 'Showing all associated common addresses'
    : clientPortalAlertMessage;
  return {
    tableTitle,
    tableSubtitle,
    tableNoDataMessage,
  };
});

/**
 * Toggles a listener for saved common addresses and site contacts.
 * This listener is mostly for division responses from other users
 *
 * @param {boolean} setListener - Flag indicating whether to set or remove the listener.
 */
function setListenerForSavedCommonAddressOrRelatedContact(
  setListener: boolean,
) {
  if (!setListener) {
    Mitt.off('savedCommonAddressesByClientId');
    return;
  }
  Mitt.on(
    'savedCommonAddressesByClientId',
    (response: ClientCommonAddressRelatedContactSave) => {
      clientDetailsStore.setUpdatedClientCommonAddresses(
        response.updatedClientCommonAddresses,
      );
      clientDetailsStore.setUpdatedRelatedContact(
        response.clientRelatedContact,
      );
    },
  );
}

onMounted(() => {
  getClientsCommonAddresses();
  setListenerForSavedCommonAddressOrRelatedContact(true);
});
onUnmounted(() => {
  setListenerForSavedCommonAddressOrRelatedContact(false);
});
</script>
<style scoped lang="scss"></style>
