<template>
  <div
    class="common-address-maintenance-container"
    v-if="props.clientCommonAddress"
  >
    <div class="px-3 py-3">
      <div class="flex-row mt-1 align-center">
        <GTitle
          class="mb-2"
          :title="textDisplay.commonAddressTitle"
          :subtitle="textDisplay.commonAddressSubtitle"
          :divider="false"
        />
        <v-btn
          v-if="!sessionManager.isClientPortal()"
          depressed
          plain
          @click="viewAddressNotes = true"
          class="notes-btn"
        >
          <v-icon class="pl-1 pr-2" size="16">note_add</v-icon>
          Add Notes
        </v-btn>
      </div>
      <hr class="divider" />

      <v-text-field
        v-model="props.clientCommonAddress.addressNickname"
        placeholder="Location Name"
        :rules="[rules.required]"
        :disabled="sessionManager.isClientPortal() && !isNewClientCommonAddress"
        autofocus
        class="v-solo-custom"
        solo
        flat
        hint="Location Name"
        persistent-hint
      ></v-text-field>

      <AddressSearchAU
        :address="props.clientCommonAddress.address"
        :nickNameList="[]"
        :soloInput="true"
        :addressIsRequired="true"
        :label="'Search for an address'"
        :formDisabled="
          sessionManager.isClientPortal() && !isNewClientCommonAddress
        "
        :enablePinDrop="false"
        :enableSuburbSelect="false"
        :enableNicknamedAddress="false"
        :isClientPortal="sessionManager.isClientPortal()"
        :enableReturnToDefaultDispatchAddress="false"
        :attach="true"
      ></AddressSearchAU>

      <v-select
        v-if="sessionManager.isOperationsPortal()"
        v-model="props.clientCommonAddress.defaultDispatchAddress"
        :items="[
          {
            key: 'This is the default dispatch address',
            value: true,
          },
          {
            key: 'This is not the default dispatch address',
            value: false,
          },
        ]"
        :disabled="sessionManager.isClientPortal() && !isNewClientCommonAddress"
        class="v-solo-custom pt-1"
        solo
        flat
        :item-text="'key'"
        :item-value="'value'"
        :label="defaultDispatchLabel"
        :placeholder="defaultDispatchLabel"
        :hint="defaultDispatchLabel"
        persistent-hint
        :clearable="false"
      />

      <v-select
        v-if="sessionManager.isOperationsPortal()"
        v-model="props.clientCommonAddress.isStandbyRate"
        :items="[
          { key: 'Standby rates do not apply at this location', value: false },
          { key: 'Standby rates apply at this location', value: true },
        ]"
        :disabled="sessionManager.isClientPortal() && !isNewClientCommonAddress"
        class="v-solo-custom pt-1"
        solo
        flat
        :item-text="'key'"
        :item-value="'value'"
        label="Standby Rates"
        placeholder="Standby Rates"
        hint="Standby Rates"
        persistent-hint
        :clearable="false"
      />

      <v-select
        v-if="sessionManager.isOperationsPortal()"
        v-model="props.clientCommonAddress.isDemurrageRate"
        :items="[
          {
            key: 'Demurrage rates do not apply at this location',
            value: false,
          },
          { key: 'Demurrage rates apply at this location', value: true },
        ]"
        :disabled="sessionManager.isClientPortal() && !isNewClientCommonAddress"
        class="v-solo-custom pt-1"
        solo
        flat
        :item-text="'key'"
        :item-value="'value'"
        label="Demurrage Rates"
        placeholder="Demurrage Rates"
        hint="Demurrage Rates"
        persistent-hint
        :clearable="false"
      />

      <!--
      <GSelect
        v-model="syncedAssociatedAddressContactIds"
        :items="availableAssociatedContactsList"
        :disabledValues="disabledAssociatedContactIds"
        multiple
        :disabled="
          sessionManager.isClientPortal() && !isNewClientCommonAddress
        "
        :item-text="'key'"
        :item-value="'value'"
        :placeholder="'Contacts at this address'"
        :label="'Contacts at this address'"
        hint="Contacts at this address"

      /> -->
      <div class="contacts-autocomplete-container" style="position: relative">
        <v-autocomplete
          attach=".contacts-autocomplete-container"
          v-model="syncedAssociatedAddressContactIds"
          :items="availableAssociatedContactsList"
          class="v-solo-custom pt-1"
          solo
          flat
          label="Contacts at this address"
          hint="Contacts at this address"
          persistent-hint
          item-value="value"
          item-text="key"
          multiple
          browser-autocomplete="off"
          auto-select-first
          :disabled="
            sessionManager.isClientPortal() && !isNewClientCommonAddress
          "
          :item-disabled="
            (item) => item.value === syncedAddressDefaultContactId
          "
        >
        </v-autocomplete>
      </div>

      <v-select
        v-model="syncedAddressDefaultContactId"
        :items="availableDefaultContactsList"
        :disabled="sessionManager.isClientPortal() && !isNewClientCommonAddress"
        class="v-solo-custom pt-1"
        solo
        flat
        item-text="key"
        item-value="value"
        label="Default Contact at this address"
        placeholder="Default Contact at this address"
        :hint="`Contacts need to be associated with this address before a default can be selected.`"
        persistent-hint
      />

      <v-divider></v-divider>
      <div class="pa-2" v-if="!sessionManager.isClientPortal()">
        <v-flex class="subheader--notes"
          ><span>Notes</span
          ><span> ({{ commonAddressNotes.length ?? 0 }})</span>
        </v-flex>
        <v-flex md12 justify-center class="custom-scrollbar">
          <v-layout row wrap>
            <v-flex md12 mb-2>
              <NotesList
                :isBookingScreen="false"
                :communications="commonAddressNotes"
                :detailedView="false"
                :allowDelete="true"
                @removeNote="handleRemoveNote"
              >
              </NotesList>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-divider></v-divider>
      </div>

      <div
        class="flex-row mt-1 align-center"
        v-if="sessionManager.isOperationsPortal() && !isNewClientCommonAddress"
      >
        <GTitle
          v-if="props.clientRelatedContact"
          class="mb-2"
          :title="textDisplay.relatedContactTitle"
          :subtitle="textDisplay.relatedContactSubtitle"
          :divider="false"
        />
        <GSwitch
          :disabled="false"
          class="pr-2"
          v-model="syncedWithRelatedContact"
          :label="'Create site contact for this location'"
        />
        <div class="ml-2">
          <InformationTooltip :bottom="true" :attach="true">
            <div slot="content" row wrap>
              {{ textDisplay.relatedContactSwitchTooltip }}
            </div>
          </InformationTooltip>
        </div>
      </div>

      <div
        class="site-contact"
        v-if="props.clientRelatedContact && sessionManager.isOperationsPortal()"
      >
        <hr class="divider" />
        <GTextField
          v-model="props.clientRelatedContact.firstName"
          :placeholder="'First Name'"
          :rules="[rules.required]"
          :disabled="false"
        ></GTextField>

        <GTextField
          v-model="props.clientRelatedContact.lastName"
          :placeholder="'Last Name'"
          :rules="[rules.required]"
          :disabled="false"
        ></GTextField>

        <GTextField
          v-model="props.clientRelatedContact.emailAddress[0]"
          :placeholder="'Email Address'"
          :disabled="false"
          :rules="[rules.email]"
        ></GTextField>

        <GTextField
          v-model="props.clientRelatedContact.contactMobileNumber"
          :placeholder="'Mobile Number'"
          :disabled="false"
          :rules="[rules.numbers]"
          v-mask="'0### ### ###'"
        ></GTextField>
        <GTextField
          v-model="props.clientRelatedContact.contactLandlineNumber"
          :placeholder="'Landline Number'"
          :disabled="false"
          :rules="[rules.numbers]"
          v-mask="'## #### ####'"
        ></GTextField>
      </div>
    </div>
    <ContentDialog
      :showDialog.sync="viewAddressNotes"
      title="Add Note to Common Address"
      width="700px"
      contentPadding="pa-0"
      @cancel="viewAddressNotes = false"
      :showActions="false"
      :isConfirmUnsaved="false"
      :isDisabled="false"
      :isLoading="false"
    >
      <NotesEditor
        v-if="viewAddressNotes"
        :communications="commonAddressNotes"
        :isEdited="viewAddressNotes"
        @setIsAddingNote="viewAddressNotes = false"
        @saveParentDocument="handleSaveNote"
        :emitSaveEventBackToParent="true"
        :type="2"
        :isAddingNote="viewAddressNotes"
        :allowEdit="false"
        :allowDelete="false"
      />
    </ContentDialog>
  </div>
</template>

<script setup lang="ts">
import AddressSearchAU from '@/components/common/addressing/address-search-au/index.vue';
import NotesEditor from '@/components/common/notes_editor/notes_editor.vue';
import NotesList from '@/components/common/notes_list/notes_list.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { maskMyString } from '@/helpers/StringHelpers/StringHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import { ClientRelatedContact } from '@/interface-models/Client/ClientRelatedContact';
import Communication from '@/interface-models/Generic/Communication/Communication';
import { KeyValue } from '@/interface-models/Generic/KeyValue/KeyValue';
import { Validation } from '@/interface-models/Generic/Validation';
import { ClientPersonWithAuthDetails } from '@/interface-models/User/ClientPerson';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { sessionManager } from '@/store/session/SessionState';
import {
  computed,
  ComputedRef,
  ref,
  Ref,
  watch,
  WritableComputedRef,
} from 'vue';

interface IProps {
  clientCommonAddress: ClientCommonAddress | null;
  clientRelatedContacts: ClientRelatedContact[] | null;
  clientPersonsWithAuthDetailsList: ClientPersonWithAuthDetails[];
  withRelatedContact: boolean;
  clientRelatedContact: ClientRelatedContact | null;
  addressDefaultContactId?: string | null;
  associatedAddressContactIds: string[];
  defaultDispatchAddressId?: string | null;
  commonAddressNotes: Communication[];
}

/**
 * Defines component props with default values using Vue's Composition API.
 * @property {string | null} addressDefaultContactId - The default ID for the address contact, initially null.
 */
const props = withDefaults(defineProps<IProps>(), {
  addressDefaultContactId: null,
  defaultDispatchAddressId: null,
});

const viewAddressNotes: Ref<boolean> = ref(false);

/**
 * Defines the component's custom events.
 */
const emit = defineEmits([
  'update:withRelatedContact',
  'update:addressDefaultContactId',
  'update:associatedAddressContactIds',
  'update:commonAddressNotes',
]);

/**
 * A computed property to retrieve validation rules from the store.
 * @returns {Validation} The validation rules from the store.
 */
const rules: ComputedRef<Validation> = computed(() => {
  return validationRules;
});

/**
 * A writable computed reference for synchronizing the `withRelatedContact` prop with its parent.
 * @returns {boolean} The current value of `withRelatedContact`.
 */
const syncedWithRelatedContact: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.withRelatedContact;
  },
  set(newValue: boolean): void {
    emit('update:withRelatedContact', newValue);
  },
});

/**
 * A writable computed reference for synchronizing the `associatedAddressContactIds` prop with its parent.
 * Ensures the default contact ID is included in the associated contact IDs; otherwise, sets it to null.
 * @returns {string[]} The current array of associated address contact IDs.
 */
const syncedAssociatedAddressContactIds: WritableComputedRef<string[]> =
  computed({
    get(): string[] {
      return props.associatedAddressContactIds;
    },
    set(newValue: string[]): void {
      if (
        syncedAddressDefaultContactId.value &&
        !newValue.includes(syncedAddressDefaultContactId.value)
      ) {
        syncedAddressDefaultContactId.value = null;
      }

      emit('update:associatedAddressContactIds', newValue);
    },
  });

/**
 * A writable computed reference for synchronizing the `addressDefaultContactId` prop with its parent.
 * @returns {string | null} The current value of the default address contact ID.
 */
const syncedAddressDefaultContactId: WritableComputedRef<string | null> =
  computed({
    get(): string | null {
      return props.addressDefaultContactId;
    },
    set(newValue: string | null): void {
      emit('update:addressDefaultContactId', newValue);
    },
  });

/**
 * A computed property to generate a list of available associated contacts.
 * @returns {KeyValue[]} A list of key-value pairs representing available associated contacts.
 */
const availableAssociatedContactsList: ComputedRef<KeyValue[]> = computed(
  () => {
    const hasSiteContactOrDispatcherRole = (roleIds: number[]): boolean =>
      roleIds.some((r) => r === 1 || r === 2);
    const siteContacts: KeyValue[] =
      props.clientRelatedContacts
        ?.filter((c) => hasSiteContactOrDispatcherRole(c.roleIds))
        .map((x: ClientRelatedContact) => {
          return {
            key: x.firstName + ' ' + x.lastName,
            value: x._id!,
          };
        }) ?? [];

    const clientPersons: KeyValue[] =
      props.clientPersonsWithAuthDetailsList
        ?.filter((c) =>
          hasSiteContactOrDispatcherRole(c.clientRoles.map((r) => r.roleId)),
        )
        .map((x: ClientPersonWithAuthDetails) => {
          return {
            key: x.clientPerson.firstName + ' ' + x.clientPerson.lastName,
            value: x.clientPerson._id!,
          };
        }) ?? [];

    return siteContacts.concat(clientPersons);
  },
);

/**
 * A computed property to generate a list of available default contacts.
 * Filters the associated contacts to only include those that are selected.
 * @returns {KeyValue[]} A list of key-value pairs representing available default contacts.
 */
const availableDefaultContactsList: ComputedRef<KeyValue[]> = computed(() => {
  return availableAssociatedContactsList.value.filter((x: KeyValue) =>
    syncedAssociatedAddressContactIds.value.includes(x.value as string),
  );
});

/**
 * A computed property to return a label for Default Dispatch Location select.
 * @returns {string} The default dispatch label.
 */
const defaultDispatchLabel: ComputedRef<string> = computed(() => {
  let message = 'Default Dispatch Location';
  if (
    // If the current address is the default, show a message saying that it is
    !!props.clientCommonAddress?._id &&
    props.clientCommonAddress?._id === props.defaultDispatchAddressId
  ) {
    message += ' (Current Default)';
  } else if (
    // If the current address is not the default, but there IS a current
    // default, and we're setting this one to be the default, show a message
    // saying that this will replace the current default
    props.clientCommonAddress?.defaultDispatchAddress &&
    !!props.defaultDispatchAddressId &&
    props.defaultDispatchAddressId !== props.clientCommonAddress?._id
  ) {
    message += ' (NOTE: this will replace the current default)';
  }
  return message;
});

/**
 * A computed property to determine if the current client common address is new.
 * @returns {boolean} `true` if the client common address is new, otherwise `false`.
 */
const isNewClientCommonAddress: ComputedRef<boolean> = computed(() => {
  if (!props.clientCommonAddress) {
    return false;
  }
  return !props.clientCommonAddress._id;
});

interface TextDisplay {
  commonAddressTitle: string;
  commonAddressSubtitle: string;
  relatedContactSwitchTooltip: string;
  relatedContactTitle: string;
  relatedContactSubtitle: string;
}
/**
 * Computes text displays based on the user's role and division details.
 * It determines whether the user is part of the operations team and customizes the titles,
 * subtitles, and tooltips accordingly. For operations users, the text emphasizes client-specific
 * information, whereas for other users, it focuses on their company. Division details are used
 * to provide a contact point for adding new company contacts.
 *
 * @returns {TextDisplay} An object containing customized text for common address and site contact
 * sections, including titles, subtitles, and a tooltip for the site contact switch. The tooltip
 * content varies depending on the user's role and the availability of division details such as
 * the division name and phone number, with a fallback to 'Support' if those details are missing.
 */
const textDisplay: ComputedRef<TextDisplay> = computed(() => {
  const isOperations: boolean = sessionManager.isOperationsPortal();
  const commonAddressTitle = isOperations
    ? 'Client Common Address'
    : 'Common Address';
  const commonAddressSubtitle = isOperations
    ? 'Maintenance for this clients common address'
    : 'Maintenance for your companies common address';
  const relatedContactTitle: string = isOperations
    ? 'Client Related Contact'
    : 'Site Contact';
  const relatedContactSubtitle: string = 'The site contact for this address';
  const divisionDetails = useCompanyDetailsStore().divisionDetails;
  const relatedContactSwitchTooltip: string = isOperations
    ? 'Please be aware that this option is for adding related contacts only. To add a new Client contact, proceed through the Client contacts management section.'
    : 'Please note that should you need to add a contact from your company, you are advised to contact ' +
      (divisionDetails && divisionDetails.name && divisionDetails.phone
        ? divisionDetails.name +
          ' at ' +
          maskMyString(divisionDetails.phone, '## #### ####')
        : 'Support.');

  return {
    commonAddressTitle,
    commonAddressSubtitle,
    relatedContactSwitchTooltip,
    relatedContactTitle,
    relatedContactSubtitle,
  };
});

/**
 * Handles saving notes from the NotesEditor and updates the client's common address.
 * Ensures that notes are properly assigned if the clientCommonAddress exists.
 */
function handleSaveNote() {
  if (!props.clientCommonAddress) {
    return;
  }
  props.clientCommonAddress.notes = [...(props.commonAddressNotes || [])];
}

/**
 * Deletes a note from the PudDetails at the specified index.
 * @param {number} index - The index of the note to remove.
 */
function handleRemoveNote(index: number) {
  props.commonAddressNotes.splice(index, 1);
}

/**
 * Watches for changes in `commonAddressNotes` and emits an update event
 * to synchronize the notes with the parent component.
 */
watch(
  () => props.commonAddressNotes,
  (newNotes) => {
    emit('update:commonAddressNotes', newNotes || []);
  },
);
</script>

<style scoped lang="scss">
.notes-btn {
  border-radius: 14px;
  color: var(--warning) !important;
  height: 30px;
  background-color: transparent !important;
  border: 1.5px solid var(--warning);
  &:disabled {
    background: none !important;
    border: none !important;
  }
}
.subheader--notes {
  color: $warning;
  font-weight: 600;
  font-size: $font-size-16;
}
</style>
