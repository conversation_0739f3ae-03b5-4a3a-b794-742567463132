<template>
  <div class="common-address-management-container">
    <div class="create-new-btn-container">
      <v-btn depressed color="primary" @click="addNewClientCommonAddress">
        <v-icon size="20" class="mr-2">add</v-icon>
        Create New Common Address
      </v-btn>
    </div>
    <ClientDetailsCommonAddressTable
      :clientId="props.clientId"
      :clientRelatedContacts="clientRelatedContacts"
      @viewCommonAddress="viewCommonAddress"
      :clientPersonsWithAuthDetailsList="clientPersonsWithAuthDetailsList"
    />

    <ContentDialog
      :showDialog.sync="dialogController"
      v-if="clientCommonAddress"
      :width="'800px'"
      :title="'Common Address Maintenance'"
      contentPadding="pa-0"
      @cancel="cancelMaintenance"
      @confirm="saveClientCommonAddress"
      :showActions="true"
      :isConfirmUnsaved="false"
      :isDisabled="false"
      :isLoading="isLoading"
      confirmBtnText="Save"
    >
      <ClientDetailsCommonAddressMaintenance
        ref="commonAddressMaintenanceRef"
        v-if="clientCommonAddress"
        :clientCommonAddress="clientCommonAddress"
        :commonAddressNotes="commonAddressNotes"
        @update:commonAddressNotes="handleNotesUpdate"
        :clientRelatedContacts="clientRelatedContacts"
        :clientRelatedContact="clientRelatedContact"
        :clientPersonsWithAuthDetailsList="clientPersonsWithAuthDetailsList"
        :associatedAddressContactIds.sync="associatedAddressContactIds"
        @cancelMaintenance="cancelMaintenance"
        :withRelatedContact.sync="withRelatedContact"
        :addressDefaultContactId.sync="addressDefaultContactId"
        :defaultDispatchAddressId="defaultDispatchAddressId"
      />
    </ContentDialog>
  </div>
</template>

<script setup lang="ts">
import ClientDetailsCommonAddressMaintenance from '@/components/admin/ClientDetails/components/client_details_common_address/client_details_common_address_maintenance.vue';
import ClientDetailsCommonAddressTable from '@/components/admin/ClientDetails/components/client_details_common_address/client_details_common_address_table.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { validate } from '@/helpers/ValidationHelpers/ValidationHelpers';
import {
  AddressContactType,
  ClientCommonAddress,
  ClientCommonAddressContact,
} from '@/interface-models/Client/ClientCommonAddress';
import {
  ClientCommonAddressRelatedContactSave,
  saveCommonAddressAndRelatedContact,
} from '@/interface-models/Client/ClientCommonAddressRelatedContactSave';
import {
  ClientRelatedContact,
  getClientRelatedContactsByClientId,
} from '@/interface-models/Client/ClientRelatedContact';
import Communication from '@/interface-models/Generic/Communication/Communication';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { ClientPersonWithAuthDetails } from '@/interface-models/User/ClientPerson';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';

import { useUserManagementStore } from '@/store/modules/UserManagementStore';
import {
  computed,
  ComputedRef,
  onMounted,
  ref,
  Ref,
  watch,
  watchEffect,
  WritableComputedRef,
} from 'vue';

interface IProps {
  clientId: string;
  client_id: string;
  clientPersonIds: string[];
  isDialogOpen: boolean;
}
const props = withDefaults(defineProps<IProps>(), {
  clientId: '',
  client_id: '',
  isDialogOpen: false,
});

const clientCommonAddress: Ref<ClientCommonAddress | null> = ref(null);
// notes from common address
const commonAddressNotes: Ref<Communication[]> = ref([]);
const clientRelatedContacts: Ref<ClientRelatedContact[]> = ref([]);
const isLoading: Ref<boolean> = ref(false);
const userManagementStore = useUserManagementStore();
// withRelatedContact - Whether the user is adding a new related contact with the common address
const withRelatedContact: Ref<boolean> = ref(false);
const clientRelatedContact: Ref<ClientRelatedContact | null> = ref(null);
const addressDefaultContactId: Ref<string | null> = ref(null);
const associatedAddressContactIds: Ref<string[]> = ref([]);
const commonAddressMaintenanceRef = ref(null);

const emit = defineEmits<{
  (event: 'update:isDialogOpen', payload: boolean): void;
}>();

/**
 * Gets and sets the isDialogOpen prop passed from parent
 */
const dialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.isDialogOpen;
  },
  set(value: boolean): void {
    emit('update:isDialogOpen', value);
  },
});

/**
 * Updates the component state to view details of a selected client common address.
 * This function sets the currently viewed client common address and updates associated
 * address contact IDs and the default contact ID based on the selected address.
 *
 * @param {ClientCommonAddress} selectedClientCommonAddress - The client common address selected for viewing.
 */
function viewCommonAddress(selectedClientCommonAddress: ClientCommonAddress) {
  clientCommonAddress.value = selectedClientCommonAddress;
  associatedAddressContactIds.value =
    selectedClientCommonAddress.addressContacts.map(
      (x: ClientCommonAddressContact) => x.contactId,
    );
  addressDefaultContactId.value = selectedClientCommonAddress.defaultContact
    ? selectedClientCommonAddress.defaultContact.contactId
    : null;
}

/**
 * Watch for changes to withRelatedContact. If withRelatedContact is true, it means the user wishes to create a new site contact for this address.
 */
watchEffect(() => {
  if (withRelatedContact.value) {
    clientRelatedContact.value = new ClientRelatedContact();
    clientRelatedContact.value.clientId = props.clientId;
  } else {
    clientRelatedContact.value = null;
  }
});

/**
 * Watch for changes to clientCommonAddress notes. If newNotes, updates commonAddressNotes.
 */
watch(
  () => clientCommonAddress.value?.notes,
  (newNotes) => {
    if (clientCommonAddress.value) {
      commonAddressNotes.value = [...(newNotes || [])];
    }
  },
);

/**
 * Returns a list of client persons with their auth details if the list within the userManagement store aligns with this components clientId prop
 *
 * @async
 * @throws {Error} Throws an error if the saving process fails.
 * @returns {Promise<void>} A promise that resolves when the saving process is complete.
 */
const clientPersonsWithAuthDetailsList = computed(() => {
  if (
    !userManagementStore.clientIdAndClientPersonWithAuthDetailsList ||
    userManagementStore.clientIdAndClientPersonWithAuthDetailsList.client_id !==
      props.client_id
  ) {
    return [];
  }
  return userManagementStore.clientIdAndClientPersonWithAuthDetailsList
    .clientPersonWithAuthDetailsList;
});

/**
 * Resets the state back to table view
 * @return {void} void
 */
function cancelMaintenance(): void {
  clientCommonAddress.value = null;
  clientRelatedContact.value = null;
  addressDefaultContactId.value = '';
}

/**
 * Returns the default contact that matches the current addressDefaultContactId
 * @return {ClientCommonAddressContact | null}
 */
function getDefaultContact(): ClientCommonAddressContact | null {
  if (!addressDefaultContactId.value) {
    return null;
  }
  const relatedContact: ClientRelatedContact | undefined =
    clientRelatedContacts.value.find(
      (x: ClientRelatedContact) => x._id === addressDefaultContactId.value,
    );
  const clientPersonWithAuthDetails: ClientPersonWithAuthDetails | undefined =
    clientPersonsWithAuthDetailsList.value.find(
      (x: ClientPersonWithAuthDetails) =>
        x.clientPerson._id === addressDefaultContactId.value,
    );

  if (relatedContact) {
    return {
      type: AddressContactType.RELATED_CONTACT,
      contactId: addressDefaultContactId.value,
    };
  } else if (clientPersonWithAuthDetails) {
    return {
      type: AddressContactType.CLIENT_PERSON,
      contactId: addressDefaultContactId.value,
    };
  } else {
    return null;
  }
}

/**
 * Constructs an array of client common address contacts by mapping associated address contact IDs to their corresponding site contacts or client persons with auth details.
 *
 * @returns {ClientCommonAddressContact[]} An array of client common address contacts, each with a type indicating whether it's a site contact or a client person, and the contact's ID.
 */
function getAssociatedAddressContacts(): ClientCommonAddressContact[] {
  const addressContacts: ClientCommonAddressContact[] = [];
  for (const contactId of associatedAddressContactIds.value) {
    const relatedContact: ClientRelatedContact | undefined =
      clientRelatedContacts.value.find(
        (x: ClientRelatedContact) => x._id === contactId,
      );

    const clientPersonWithAuthDetails: ClientPersonWithAuthDetails | undefined =
      clientPersonsWithAuthDetailsList.value.find(
        (x: ClientPersonWithAuthDetails) => x.clientPerson._id === contactId,
      );

    if (relatedContact) {
      addressContacts.push({
        type: AddressContactType.RELATED_CONTACT,
        contactId: relatedContact._id!,
      });
    } else if (clientPersonWithAuthDetails) {
      addressContacts.push({
        type: AddressContactType.CLIENT_PERSON,
        contactId: clientPersonWithAuthDetails.clientPerson._id!,
      });
    }
  }
  return addressContacts;
}

/**
 * Check ifs the supplied address already exists
 * @return {boolean}
 */
function isDuplicateClientCommonAddress(
  address_id: string | undefined,
  formattedAddress: string,
): boolean {
  const commonAddressAlreadyExists: ClientCommonAddress | undefined =
    useClientDetailsStore().clientCommonAddresses.find(
      (x: ClientCommonAddress) =>
        x.address.formattedAddress === formattedAddress && x._id !== address_id,
    );
  return commonAddressAlreadyExists ? true : false;
}

/**
 * Saves a new common address for a client after validation checks. It verifies the address isn't a duplicate and updates relevant client details before saving. Displays notifications for any validation or duplication errors and manages loading state during the process.
 *
 * @async
 * @throws {Error} Throws an error if the saving process fails.
 * @returns {Promise<void>} A promise that resolves when the saving process is complete.
 */
async function saveClientCommonAddress(): Promise<void> {
  if (
    !validate(commonAddressMaintenanceRef.value) ||
    !clientCommonAddress.value
  ) {
    return;
  }

  // Validate that the new common address does not already exist.
  const isDuplicate: boolean = isDuplicateClientCommonAddress(
    clientCommonAddress.value._id,
    clientCommonAddress.value.address.formattedAddress,
  );
  if (isDuplicate) {
    showNotification(
      'A common address at ' +
        clientCommonAddress.value.address.formattedAddress +
        ' already exists for this client. Duplicate addresses are not permitted.',
      {
        title: 'Duplicate Common Address',
        type: HealthLevel.ERROR,
      },
    );
    return;
  }

  clientCommonAddress.value.defaultContact = getDefaultContact();
  clientCommonAddress.value.clientId = props.clientId;
  clientCommonAddress.value.addressContacts = getAssociatedAddressContacts();

  isLoading.value = true;
  const request: ClientCommonAddressRelatedContactSave = {
    clientId: props.clientId,
    clientCommonAddress: clientCommonAddress.value,
    clientRelatedContact: clientRelatedContact.value,
    relatedContactsAssociatedCommonAddressIds: [],
    relatedContactsDefaultCommonAddressIds: [],
    updatedClientCommonAddresses: [],
  };
  await saveCommonAddressAndRelatedContact(request);
  isLoading.value = false;
  cancelMaintenance();
}

/**
 * Returns the mongo ID of the default dispatch address for the client (if it exists)
 */
const defaultDispatchAddressId: ComputedRef<string | null> = computed(() => {
  return (
    useClientDetailsStore().clientCommonAddresses.find(
      (x: ClientCommonAddress) => x.defaultDispatchAddress,
    )?._id || null
  );
});

/**
 * Sets state for a new client common address
 * @return {void} void
 */
function addNewClientCommonAddress(): void {
  clientCommonAddress.value = new ClientCommonAddress();
  clientRelatedContact.value = null;
  addressDefaultContactId.value = null;
  associatedAddressContactIds.value = [];
}

/**
 * Fetch client site contacts
 * @return {void} void
 */
async function getClientsSiteContacts(): Promise<void> {
  // fetch client site contacts
  const relatedContacts: ClientRelatedContact[] | null =
    await getClientRelatedContactsByClientId(props.clientId);
  if (!relatedContacts) {
    console.error("Something went wrong when fetching client's site contacts.");
    return;
  }
  clientRelatedContacts.value = relatedContacts;
}

/**
 * Updates `commonAddressNotes` with the latest notes from `clientCommonAddress`
 * when a new note is added.
 */
function handleNotesUpdate() {
  if (clientCommonAddress.value?.notes) {
    commonAddressNotes.value = clientCommonAddress.value?.notes;
  }
}

/**
 * On mount we make calls for our client site contacts and client persons. This is because these documents are referenced in common addresses.
 * @return {void} void
 */
onMounted(() => {
  getClientsSiteContacts();
  userManagementStore.getClientPersonsWithAuthDetails(
    props.client_id,
    props.clientPersonIds,
  );
});
</script>

<style scoped lang="scss">
.create-new-btn-container {
  position: absolute;
  top: 40px;
  right: 0;
  z-index: 12 !important;
  display: flex;
  align-items: center;
  height: 40px;
  margin-right: 16px;
}

.client-portal {
  .common-address-management-container {
    padding-top: 42px;
    position: relative;
  }
  .create-new-btn-container {
    top: 50px;
    right: -10px;
  }
}
</style>
