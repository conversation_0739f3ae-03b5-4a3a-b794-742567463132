<template>
  <div>
    <v-layout>
      <div>
        <v-layout class="pl" align-center style="height: 54px">
          <v-tooltip bottom>
            <template v-slot:activator="{ on }">
              <v-btn
                icon
                flat
                @click="editOperationalStatus"
                v-on="on"
                :disabled="
                  isEdited ||
                  !isAuthorised() ||
                  (clientOperationalStatus === 'CREDIT_STATUS' &&
                    clientIsRetired)
                "
              >
                <v-icon size="18" color="orange">far fa-edit</v-icon>
              </v-btn>
            </template>
            <span v-if="clientOperationalStatus === 'RETIRED'"
              >Edit Clients Active Status</span
            >
            <span v-if="clientOperationalStatus === 'CREDIT_STATUS'">
              <span>Edit Clients Credit Status</span>
            </span>
          </v-tooltip>
        </v-layout>
      </div>
    </v-layout>
    <v-dialog
      v-if="dialogIsActive"
      v-model="dialogIsActive"
      content-class="v-dialog-custom"
      width="800px"
      persistent
    >
      <v-layout
        justify-space-between
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>{{ dialogTitle }}</span>
        <div>
          <div
            class="app-theme__center-content--closebutton"
            @click="closeDialog"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </div>
      </v-layout>
      <div class="app-theme__center-content--body dialog-content pa-1">
        <v-form>
          <div class="pa-2 body-scrollable--75">
            <v-layout class="px-3 pt-3">
              <v-flex md12>
                <v-layout>
                  <v-flex md4>
                    <v-layout align-center class="form-field-label-container">
                      <h6
                        class="subheader--faded pr-3 pb-0 form-field-required-marker"
                      >
                        {{ labelName }}:
                      </h6>
                    </v-layout>
                  </v-flex>
                  <v-flex md8>
                    <v-select
                      v-if="clientOperationalStatus === 'RETIRED'"
                      label="Retired"
                      :class="[
                        { 'form-field-required': !clientDetails.isPlaceholder },
                      ]"
                      v-model="selectedRetiredStatus"
                      :items="retiredSelectOptions"
                      class="v-solo-custom"
                      solo
                      :disabled="!canCloseAccount"
                      color="light-blue"
                      flat
                    />
                    <v-select
                      v-else
                      label="Credit Status"
                      :class="[
                        { 'form-field-required': !clientDetails.isPlaceholder },
                      ]"
                      :rules="
                        clientDetails.isPlaceholder ? [] : [validate.required]
                      "
                      v-model="selectedCreditStatus"
                      :items="creditStatusTypes"
                      item-text="longName"
                      item-value="id"
                      class="v-solo-custom"
                      solo
                      :disabled="clientIsRetired || !isAuthorised"
                      color="light-blue"
                      flat
                    />
                  </v-flex>
                </v-layout>
              </v-flex>
            </v-layout>
            <v-layout wrap>
              <v-flex md12>
                <v-alert
                  type="warning"
                  v-if="!canCloseAccount && selectedCreditStatus === 2"
                  :value="true"
                  class="v-alert-main"
                >
                  <strong class="pr-1">Warning:</strong>This Client is linked to
                  active or permanent jobs. Closing this account:
                  <ul class="pt-1">
                    <li>
                      Jobs that are allocated to drivers will be returned to a
                      pre-allocated state.
                    </li>
                    <li>Jobs in progress can continue as normal.</li>
                    <li>
                      Jobs that are not yet allocated for this customer, cannot
                      be allocated.
                    </li>
                    <li>
                      An email summary of the changes made to jobs will be sent
                      to Operations & Accounts teams.
                    </li>
                  </ul>
                </v-alert>
                <v-alert
                  type="success"
                  v-if="selectedCreditStatus === 2 && canCloseAccount"
                  :value="true"
                >
                  This client is not currently linked to any active or permanent
                  jobs. If you are sure you wish to continue, you may proceed
                  safely.
                </v-alert>
              </v-flex>
            </v-layout>
            <v-flex md12>
              <ClientDetailsActiveAssociations
                :clientDetails="clientDetails"
                @setCanCloseAccount="setCanCloseAccount"
                :isClientOperationalStatusUpdate="true"
              />
              <v-divider class="my-3"></v-divider>
            </v-flex>
          </div>
          <v-divider class="mt-2"></v-divider>
          <v-layout justify-space-between pa-2>
            <v-btn
              color="error"
              class="v-btn-custom"
              depressed
              outline
              @click="closeDialog"
            >
              <span>cancel</span>
            </v-btn>
            <v-spacer />
            <v-btn
              @click="saveOperationalStatusUpdate"
              :disabled="saveIsDisabled"
              color="primary"
              class="v-btn-custom"
              block
              solo
              :loading="isAwaitingSaveResponse"
            >
              save
            </v-btn>
          </v-layout>
        </v-form>
      </div>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import ClientDetailsActiveAssociations from '@/components/admin/ClientDetails/components/client_details_active_associations.vue';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import { ClientOperationalStatus } from '@/interface-models/Client/ClientOperationalStatus';
import { ClientOperationalStatusUpdate } from '@/interface-models/Client/ClientOperationalStatusUpdate';
import { creditStatusTypes } from '@/interface-models/Generic/AccountsReceivable/CreditStatusTypes';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { Validation } from '@/interface-models/Generic/Validation';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { ComputedRef, Ref, computed, onMounted, ref, watch } from 'vue';

const props = withDefaults(
  defineProps<{
    clientDetails: ClientDetails;
    isEdited?: boolean;
    clientOperationalStatus: string;
  }>(),
  {
    isEdited: false,
  },
);

const validate: ComputedRef<Validation> = computed(() => {
  return validationRules;
});

const isLoading: Ref<boolean> = ref(true);
const canCloseAccount: Ref<boolean> = ref(false);
const dialogIsActive: Ref<boolean> = ref(false);
const isAwaitingSaveResponse: Ref<boolean> = ref(false);

const retiredSelectOptions: Ref<string[]> = ref(['ACTIVE', 'RETIRED']);

const selectedRetiredStatus: Ref<string> = ref('ACTIVE');

const selectedCreditStatus: Ref<number | null> = ref(null);

// Watcher for dialog active state
watch(dialogIsActive, (value: boolean) => {
  if (value) {
    setInitialState();
  }
});

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}

function editOperationalStatus(): void {
  dialogIsActive.value = true;
}

function closeDialog(): void {
  dialogIsActive.value = false;
}

function setCanCloseAccount(canClose: boolean): void {
  canCloseAccount.value = canClose;
  isLoading.value = false;
}

// Computed property to check if alert messages are active
const alertMessagesAreActive: ComputedRef<boolean> = computed(() => {
  if (isLoading.value) {
    return false;
  }
  if (props.clientOperationalStatus === ClientOperationalStatus.RETIRED) {
    return !props.clientDetails.statusList.includes(13);
  }
  return false;
});

/**
 * Sends request for updating a client's operational status (statusList and
 * creditStatus).
 */
async function saveOperationalStatusUpdate() {
  if (!props.clientDetails.clientId) {
    return;
  }
  const operationalStatusUpdate: ClientOperationalStatusUpdate = {
    clientId: props.clientDetails.clientId,
    creditStatus:
      props.clientOperationalStatus === ClientOperationalStatus.CREDIT_STATUS
        ? selectedCreditStatus.value
        : null,
    isRetired:
      props.clientOperationalStatus === ClientOperationalStatus.RETIRED
        ? selectedRetiredStatus.value === 'RETIRED'
          ? true
          : false
        : null,
    rateExpirationSummaries: props.clientDetails.rateExpirationSummaries,
    updatedJobsSummary: [],
  };

  // Send request and handle response
  isAwaitingSaveResponse.value = true;
  const result = await useClientDetailsStore().updateClientOperationalStatus(
    operationalStatusUpdate,
  );
  // update client list summary
  useClientDetailsStore().setClientOperationalStatusUpdate(result);
  setUpdatedClientOperationalStatus(result);
}

// set initial selects and set loading to false if no requests are to be made.
function setInitialState() {
  if (props.clientOperationalStatus === 'RETIRED') {
    setClientRetiredStatus();
  } else {
    setClientCreditStatus();
  }
  if (!alertMessagesAreActive.value) {
    isLoading.value = false;
  }
}

// Set client retired status
function setClientRetiredStatus(): void {
  const hasRetiredStatus = props.clientDetails.statusList.includes(13);
  selectedRetiredStatus.value = hasRetiredStatus ? 'RETIRED' : 'ACTIVE';
}

function setClientCreditStatus(): void {
  selectedCreditStatus.value =
    props.clientDetails.accountsReceivable.creditStatus;
}

// When we get a response from the updated operational status response we update
// the current clientDetails in state.
function setUpdatedClientOperationalStatus(
  response: ClientOperationalStatusUpdate | null,
): void {
  if (!isAwaitingSaveResponse.value) {
    return;
  }
  if (!response) {
    showNotification('Something went wrong.', {
      title: 'Client Details',
      type: HealthLevel.ERROR,
    });
    closeDialog();
    return;
  }
  if (response.creditStatus !== null) {
    // update client details
    props.clientDetails.accountsReceivable.creditStatus = response.creditStatus;
    // push status 7 (ACCOUNT_OVERDUE) into status list
    if (response.creditStatus === 2) {
      if (!props.clientDetails.statusList.includes(7)) {
        props.clientDetails.statusList.push(7);
      }
    } else {
      // remove retired status
      const indexOfStatusToRemove = props.clientDetails.statusList.findIndex(
        (status: number) => status === 7,
      );
      if (indexOfStatusToRemove >= 0) {
        props.clientDetails.statusList.splice(indexOfStatusToRemove, 1);
      }
    }
    showNotification('Credit Status Updated.', {
      title: 'Client Details',
      type: HealthLevel.SUCCESS,
    });
  }
  if (response.isRetired !== null) {
    if (response.isRetired) {
      // add retired status
      props.clientDetails.statusList.push(13);
    } else {
      // remove retired status
      const indexOfStatusToRemove = props.clientDetails.statusList.findIndex(
        (status: number) => status === 13,
      );
      if (indexOfStatusToRemove >= 0) {
        props.clientDetails.statusList.splice(indexOfStatusToRemove, 1);
      }
    }
  }
  isAwaitingSaveResponse.value = false;
  closeDialog();
}

const dialogTitle: ComputedRef<string> = computed((): string => {
  return props.clientOperationalStatus === ClientOperationalStatus.RETIRED
    ? 'Client Retirement Management'
    : 'Credit Status Management';
});

const labelName: ComputedRef<string> = computed((): string => {
  return props.clientOperationalStatus === ClientOperationalStatus.RETIRED
    ? 'RETIRED'
    : 'Credit Status';
});

const clientIsRetired: ComputedRef<boolean> = computed((): boolean => {
  return props.clientDetails.statusList.includes(13);
});

/**
 * Determines if the 'Save' option should be disabled. The option is disabled if
 * the user is not authorised, or if the client's operational status is
 * 'CREDIT_STATUS' and the client is retired, or if the client's operational
 * status is 'RETIRED' and the account cannot be closed.
 *
 * @returns {ComputedRef<boolean>} - A computed ref that returns true if the
 * 'Save' option should be disabled, false otherwise.
 */
const saveIsDisabled: ComputedRef<boolean> = computed((): boolean => {
  if (!isAuthorised()) {
    return true;
  }
  if (props.clientOperationalStatus === 'CREDIT_STATUS') {
    if (clientIsRetired.value) {
      return true;
    }
  }
  if (props.clientOperationalStatus === 'RETIRED') {
    if (!canCloseAccount.value) {
      return true;
    }
  }
  return false;
});

onMounted(() => {
  setInitialState();
});
</script>
