<template>
  <v-layout class="client-details-notes">
    <v-flex md8 offset-md2>
      <v-layout>
        <v-flex md12 pb-1>
          <v-layout align-center>
            <h5 class="subheader--bold pr-3 pt-1">Rate Configuration</h5>
            <v-flex>
              <v-divider></v-divider>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
      <v-layout>
        <!-- Configuration Cards Container -->
        <div class="configuration-cards">
          <!-- Fuel Surcharge Configuration Card -->
          <div class="config-card" :class="{ editing: isEdited }">
            <div class="card-header">
              <h6 class="card-title">Fuel Surcharge Configuration</h6>
              <div class="status-badge" :class="getStatusClass('fuel')">
                {{ getStatusText('fuel') }}
              </div>
            </div>

            <div class="card-content">
              <div class="status-description">
                {{ getStatusDescription('fuel') }}
              </div>

              <ul class="feature-list">
                <li
                  v-for="(item, index) in getFeatureList('fuel')"
                  :key="`fuel-${index}`"
                >
                  {{ item }}
                </li>
              </ul>
            </div>

            <!-- Edit Form Section -->
            <div v-if="isEdited" class="edit-section">
              <div class="edit-header">
                <span class="edit-title">Custom Fuel Surcharge</span>
              </div>

              <div class="form-group">
                <v-radio-group
                  v-model="hasNegotiatedFuelLevy"
                  row
                  :disabled="!isEdited"
                  label="Does the customer have a negotiated Fuel Levy?"
                  class="radio-group"
                >
                  <v-radio label="Yes" :value="true" />
                  <v-radio label="No" :value="false" />
                </v-radio-group>
              </div>

              <div v-if="hasNegotiatedFuelLevy === true" class="form-group">
                <v-radio-group
                  v-model="defaultsToDivisionOnExpiry"
                  row
                  :disabled="!isEdited"
                  label="When it expires, should it revert to division fuel levy?"
                  class="radio-group"
                >
                  <v-radio label="Yes" :value="true" />
                  <v-radio label="No" :value="false" />
                </v-radio-group>
              </div>
            </div>
          </div>

          <!-- Service Rate Configuration Card -->
          <div class="config-card" :class="{ editing: isEdited }">
            <div class="card-header">
              <h6 class="card-title">Custom Service Rate Configuration</h6>
              <div class="status-badge" :class="getStatusClass('service')">
                {{ getStatusText('service') }}
              </div>
            </div>

            <div class="card-content">
              <div class="status-description">
                {{ getStatusDescription('service') }}
              </div>

              <ul class="feature-list">
                <li
                  v-for="(item, index) in getFeatureList('service')"
                  :key="`service-${index}`"
                >
                  {{ item }}
                </li>
              </ul>
            </div>

            <!-- Edit Form Section -->
            <div v-if="isEdited" class="edit-section">
              <div class="edit-header">
                <span class="edit-title">Custom Rate Card</span>
              </div>

              <div class="form-group">
                <v-radio-group
                  v-model="hasNegotiatedServiceRates"
                  row
                  :disabled="!isEdited"
                  label="Does the customer have negotiated rates for specific services?"
                  class="radio-group"
                >
                  <v-radio label="Yes" :value="true" />
                  <v-radio label="No" :value="false" />
                </v-radio-group>
              </div>

              <div v-if="hasNegotiatedServiceRates === true" class="form-group">
                <v-radio-group
                  v-model="defaultsToDivisionServiceRatesOnExpiry"
                  row
                  :disabled="!isEdited"
                  label="When the negotiated rates expire, should it revert to division service rates?"
                  class="radio-group"
                >
                  <v-radio label="Yes" :value="true" />
                  <v-radio label="No" :value="false" />
                </v-radio-group>
              </div>
            </div>
          </div>

          <!-- Division Service Rates Card -->
          <div class="config-card" :class="{ editing: isEdited }">
            <div class="card-header">
              <h6 class="card-title">Division Service Rates</h6>
              <div class="status-badge" :class="getStatusClass('division')">
                {{ getStatusText('division') }}
              </div>
            </div>

            <div class="card-content">
              <div class="status-description">
                {{ getStatusDescription('division') }}
              </div>

              <ul class="feature-list">
                <li
                  v-for="(item, index) in getFeatureList('division')"
                  :key="`division-${index}`"
                >
                  {{ item }}
                </li>
              </ul>
            </div>

            <!-- Edit Form Section -->
            <div v-if="isEdited" class="edit-section">
              <div class="edit-header">
                <span class="edit-title"
                  >Division Service Rates Used in Merging</span
                >
              </div>

              <div class="form-group">
                <v-radio-group
                  v-model="usesOlderDivisionServiceRates"
                  row
                  :disabled="!isEdited"
                  label="Does the customer use 'older' division service rates as their rate card?"
                  class="radio-group"
                >
                  <v-radio label="Yes" :value="true" />
                  <v-radio label="No" :value="false" />
                </v-radio-group>
              </div>
            </div>
          </div>
        </div>
      </v-layout>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import { computed, onMounted, Ref, ref, WritableComputedRef } from 'vue';

const props = withDefaults(
  defineProps<{
    clientDetails: ClientDetails;
    isEdited: boolean;
    allClientServiceRates?: ClientServiceRate[];
    allFuelSurchargeRates?: ClientFuelSurchargeRate[];
  }>(),
  {
    isEdited: false,
    allClientServiceRates: undefined,
    allFuelSurchargeRates: undefined,
  },
);

const emit = defineEmits<{
  (event: 'updateFuelSurchargeConfig', value: boolean): void;
  (event: 'updateServiceRateConfig', value: boolean): void;
  (event: 'updateDivisionRatesConfig', value: boolean): void;
}>();

const _hasNegotiatedFuelLevy: Ref<boolean> = ref(false);
const _defaultsToDivisionOnExpiry: Ref<boolean> = ref(false);
const _hasNegotiatedServiceRates: Ref<boolean> = ref(false);
const _defaultsToDivisionServiceRatesOnExpiry: Ref<boolean> = ref(false);
const _usesOlderDivisionServiceRates: Ref<boolean> = ref(false);

// Initialize local state based on props
function initializeLocalState() {
  // Fuel Surcharge
  if (props.clientDetails.expiredFuelSurchargeDefaultsToDivisionRate === true) {
    _hasNegotiatedFuelLevy.value = true;
    _defaultsToDivisionOnExpiry.value = true;
  } else if (
    props.clientDetails.expiredFuelSurchargeDefaultsToDivisionRate === false
  ) {
    _hasNegotiatedFuelLevy.value = true;
    _defaultsToDivisionOnExpiry.value = false;
  } else {
    _hasNegotiatedFuelLevy.value = false;
    _defaultsToDivisionOnExpiry.value = false;
  }

  // Service Rates
  if (props.clientDetails.expiredServiceRateDefaultsToDivisionRate === true) {
    _hasNegotiatedServiceRates.value = true;
    _defaultsToDivisionServiceRatesOnExpiry.value = true;
  } else if (
    props.clientDetails.expiredServiceRateDefaultsToDivisionRate === false
  ) {
    _hasNegotiatedServiceRates.value = true;
    _defaultsToDivisionServiceRatesOnExpiry.value = false;
  } else {
    _hasNegotiatedServiceRates.value = false;
    _defaultsToDivisionServiceRatesOnExpiry.value = false;
  }

  // Division Service Rates
  _usesOlderDivisionServiceRates.value =
    props.clientDetails.usesStandardDivisionRates === false;
}

// WritableComputedRef for fuel surcharge negotiation
const hasNegotiatedFuelLevy: WritableComputedRef<boolean> = computed({
  get: (): boolean => _hasNegotiatedFuelLevy.value,
  set: (value: boolean): void => {
    if (value === _hasNegotiatedFuelLevy.value) {
      return;
    } // No change

    // Update local state
    _hasNegotiatedFuelLevy.value = value;

    // Emit to parent to update clientDetails
    if (value === false) {
      emit('updateFuelSurchargeConfig', true);
    } else if (value === true) {
      // Keep current value or set to false if not set
      const currentValue =
        props.clientDetails.expiredFuelSurchargeDefaultsToDivisionRate;
      emit(
        'updateFuelSurchargeConfig',
        currentValue === null ? false : currentValue,
      );
    }
  },
});

// WritableComputedRef for fuel surcharge expiry behavior
const defaultsToDivisionOnExpiry: WritableComputedRef<boolean> = computed({
  get: (): boolean => _defaultsToDivisionOnExpiry.value,
  set: (value: boolean): void => {
    if (value === _defaultsToDivisionOnExpiry.value) {
      return;
    } // No change

    // Update local state
    _defaultsToDivisionOnExpiry.value = value;

    // Only emit if we have negotiated fuel levy
    if (_hasNegotiatedFuelLevy.value === true) {
      emit('updateFuelSurchargeConfig', value ?? false);
    }
  },
});

// WritableComputedRef for service rates negotiation
const hasNegotiatedServiceRates: WritableComputedRef<boolean> = computed({
  get: (): boolean => _hasNegotiatedServiceRates.value,
  set: (value: boolean): void => {
    if (value === _hasNegotiatedServiceRates.value) {
      return;
    } // No change

    // Update local state
    _hasNegotiatedServiceRates.value = value;

    // Emit to parent to update clientDetails
    if (value === false) {
      emit('updateServiceRateConfig', true);
    } else if (value === true) {
      // Keep current value or set to false if not set
      const currentValue =
        props.clientDetails.expiredServiceRateDefaultsToDivisionRate;
      emit(
        'updateServiceRateConfig',
        currentValue === null ? false : currentValue,
      );
    }
  },
});

// WritableComputedRef for service rates expiry behavior
const defaultsToDivisionServiceRatesOnExpiry: WritableComputedRef<boolean> =
  computed({
    get: (): boolean => _defaultsToDivisionServiceRatesOnExpiry.value,
    set: (value: boolean): void => {
      if (value === _defaultsToDivisionServiceRatesOnExpiry.value) {
        return;
      } // No change

      // Update local state
      _defaultsToDivisionServiceRatesOnExpiry.value = value;

      // Only emit if we have negotiated service rates
      if (_hasNegotiatedServiceRates.value === true) {
        emit('updateServiceRateConfig', value ?? false);
      }
    },
  });

// WritableComputedRef for division rates preference
const usesOlderDivisionServiceRates: WritableComputedRef<boolean> = computed({
  get: (): boolean => _usesOlderDivisionServiceRates.value,
  set: (value: boolean): void => {
    if (value === _usesOlderDivisionServiceRates.value) {
      return;
    } // No change

    // Update local state
    _usesOlderDivisionServiceRates.value = value;

    // Emit to parent to update clientDetails
    emit('updateDivisionRatesConfig', value === false);
  },
});

// Helper func to heck for active custom fuel levy in allFuelSurchargeRates
function hasActiveCustomFuelLevyFromList(): boolean {
  const list = props.allFuelSurchargeRates;
  if (!Array.isArray(list) || list.length === 0) {
    return false;
  }
  const now = Date.now();
  return list.some(
    (rate) =>
      (!rate.validFromDate || rate.validFromDate <= now) &&
      (!rate.validToDate || rate.validToDate > now),
  );
}
// Helper func check for active custom service rate in allClientServiceRates
function hasActiveCustomServiceRateFromList(): boolean {
  const list = props.allClientServiceRates;
  if (!Array.isArray(list) || list.length === 0) {
    return false;
  }
  const now = Date.now();
  return list.some(
    (rate) =>
      (!rate.validFromDate || rate.validFromDate <= now) &&
      (!rate.validToDate || rate.validToDate > now),
  );
}

// Update getStatusText to use only the list-based helpers
function getStatusText(type: 'fuel' | 'service' | 'division'): string {
  switch (type) {
    case 'fuel': {
      const isDivision =
        props.clientDetails.expiredFuelSurchargeDefaultsToDivisionRate;
      const hasActive = hasActiveCustomFuelLevyFromList();
      if (isDivision && hasActive) {
        return 'Custom > Division';
      }
      return isDivision ? 'Division' : 'Custom';
    }
    case 'service': {
      const isDivision =
        props.clientDetails.expiredServiceRateDefaultsToDivisionRate;
      const hasActive = hasActiveCustomServiceRateFromList();
      if (isDivision && hasActive) {
        return 'Custom > Division';
      }
      return isDivision ? 'Division' : 'Custom';
    }
    case 'division':
      return props.clientDetails.usesStandardDivisionRates
        ? 'Default'
        : 'Custom';
    default:
      return 'Custom';
  }
}

// Determine color of status pills
function getStatusClass(type: 'fuel' | 'service' | 'division'): string {
  const isCustomDivision = (() => {
    if (type === 'fuel') {
      const isDivision =
        props.clientDetails.expiredFuelSurchargeDefaultsToDivisionRate;
      const hasActive = hasActiveCustomFuelLevyFromList();
      return isDivision && hasActive;
    }
    if (type === 'service') {
      const isDivision =
        props.clientDetails.expiredServiceRateDefaultsToDivisionRate;
      const hasActive = hasActiveCustomServiceRateFromList();
      return isDivision && hasActive;
    }
    return false;
  })();
  if (isCustomDivision) {
    return 'status-custom-division';
  }
  switch (type) {
    case 'fuel':
      return props.clientDetails.expiredFuelSurchargeDefaultsToDivisionRate
        ? 'status-division'
        : 'status-custom';
    case 'service':
      return props.clientDetails.expiredServiceRateDefaultsToDivisionRate
        ? 'status-division'
        : 'status-custom';
    case 'division':
      return props.clientDetails.usesStandardDivisionRates
        ? 'status-default'
        : 'status-custom';
    default:
      return 'status-custom';
  }
}

function getStatusDescription(type: 'fuel' | 'service' | 'division'): string {
  switch (type) {
    case 'fuel':
      return props.clientDetails.expiredFuelSurchargeDefaultsToDivisionRate
        ? 'Division rate will be applied when no valid Client Fuel Surcharge is found'
        : 'If no Client Fuel Surcharge Rate is found, booking and pricing will be unavailable';
    case 'service':
      return props.clientDetails.expiredServiceRateDefaultsToDivisionRate
        ? 'Division rate will be applied when no valid Client Service Rate is found'
        : 'If no Client Service Rate is found, booking and pricing will be unavailable';
    case 'division':
      return props.clientDetails.usesStandardDivisionRates
        ? 'This Client is using the current default service rates.'
        : 'This client has custom default rates configured (see Default Rates Config tab)';
    default:
      return '';
  }
}

function getFeatureList(type: 'fuel' | 'service' | 'division'): string[] {
  switch (type) {
    case 'fuel':
      if (props.clientDetails.expiredFuelSurchargeDefaultsToDivisionRate) {
        return [
          "This Client is set to use the Division Fuel Levy if they don't have a custom rate available.",
          'This Client will not appear in the Expiring Fuel Levies Dashboard.',
        ];
      } else {
        return [
          "This Client will not default to the Division Fuel Levy if they don't have a custom rate available.",
          'If no custom Fuel Levy is found at time of job pricing, pricing may be unavailable.',
          'Warnings will appear in the Expiring Fuel Levies dashboard if custom Fuel Levy rates are expired/expiring soon, and no follow-on rate has been set.',
        ];
      }
    case 'service':
      if (props.clientDetails.expiredServiceRateDefaultsToDivisionRate) {
        return [
          'This client is set to revert back to the Division Global rate card when no valid Custom Service Rate card is found.',
          'This client will not appear in the Expiring Service Rate Cards Dashboard',
        ];
      } else {
        return [
          'This client will not revert back to utilising the Division Global Rate card for all services if a valid Custom Service Rate card is not available.',
          'If no Custom Service Rate Card is available at time of job pricing, the job may not be able to be reviewed.',
          'Warnings will appear in the Expiring Service Rate Cards dashboard if this clients Custom Service Rate Card is expired / expiring soon and no follow-on rate card has been created.',
        ];
      }
    case 'division':
      if (props.clientDetails.usesStandardDivisionRates) {
        return [
          "This client utilises the current Division Global Service Rate as part of the 'Rate Card Merge' process to generate this customers rate card.",
        ];
      } else {
        return [
          "This client utilises a non-standard Division Global Rate Card as part of the 'Rate Card Merge' process to generate this customers rate card.",
          "This client requires an entry in the 'Division Service Rates' section of the Rate Details.",
          "If there is not a valid entry in 'Division Service Rates' section of the Rate Details, this client will appear in the Expiring Default Rates Dashboard.",
        ];
      }
    default:
      return [];
  }
}

onMounted(() => {
  initializeLocalState();
});
</script>

<style lang="scss" scoped>
.configuration-cards {
  display: flex;
  flex-direction: column;
  gap: 18px;
  padding: 12px;
  max-width: fit-content;
}

.config-card {
  background: var(--background-color-300);
  border: 2px solid transparent;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.2s ease;

  &.editing {
    border-color: var(--border-color);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    gap: 24px;
    align-items: center;
    padding: 8px 24px;
    background: var(--background-color-200);

    .card-title {
      font-weight: 600;
      font-size: 14px;
      color: var(--text-color);
      margin-top: 4px;
    }

    .status-badge {
      font-weight: 700;
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 12px;
      // margin-bottom: 2px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      border: 2px solid;

      &.status-division {
        background-color: #e3f2fd;
        color: #1976d2;
        border-color: #bbdefb;
      }

      &.status-custom {
        background-color: #fff3e0;
        color: #f57c00;
        border-color: #ffcc02;
      }

      &.status-default {
        background-color: #e8f5e8;
        color: #388e3c;
        border-color: #c8e6c9;
      }

      &.status-custom-division {
        background: linear-gradient(90deg, #ffdba1 0%, #e3f2fd 100%);
        color: #1976d2;
        border-color: #bbdefb;
      }
    }
  }

  .card-content {
    padding: 12px 20px 12px 20px;

    .status-description {
      font-weight: 500;
      color: var(--text-color);
      margin-bottom: 12px;
      font-size: 14px;
    }

    .feature-list {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        position: relative;
        padding-left: 20px;
        margin-bottom: 8px;
        color: var(--light-text-color);
        font-size: 14px;
        line-height: 1.5;

        &:before {
          content: '•';
          position: absolute;
          left: 0;
          color: var(--accent);
          font-weight: bold;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .edit-section {
    background: var(--background-color-400);
    padding-left: 20px;
    padding-top: 12px;

    .edit-header {
      .edit-title {
        font-weight: 600;
        color: var(--accent);
        font-size: 16px;
      }
    }

    .form-group {
      .radio-group {
        :deep(.v-input__control) {
          .v-input__details {
            display: none;
          }
        }

        :deep(.v-label) {
          font-weight: 500;
          color: var(--text-color);
          font-size: 16px;
        }

        :deep(.v-radio) {
          margin-right: 24px;

          .v-label {
            font-size: 14px;
          }
        }
      }
    }
  }
}
</style>
