<template>
  <div class="client-details-operational-information">
    <v-layout wrap>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">1. Key Operational Details</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Commencement Date:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <DateTimeInputs
              :epochTime.sync="clientDetails.clientCommencementDate"
              :enableValidation="true"
              :type="DateTimeType.DATE_START_OF_DAY"
              dateLabel="Commencement Date"
              :hintTextType="HintTextType.FORMATTED_SELECTION"
              :isRequired="false"
              :soloInput="true"
              :boxInput="false"
              :readOnly="!isEdited"
            ></DateTimeInputs>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Client Type:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              label="Client Type"
              v-model="clientDetails.clientType"
              :items="clientTypes"
              :rules="[validate.required]"
              item-text="longName"
              item-value="id"
              :disabled="!isEdited"
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Trading Terms:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              label="Trading Terms"
              v-model="clientDetails.clientTradingTerms"
              :items="tradingTerms"
              :rules="[validate.required]"
              item-text="longName"
              item-value="id"
              :disabled="!isEdited"
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                Preferred Vehicle Fleet:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-autocomplete
              v-model="clientDetails.preAssignedVehicleDetails"
              :items="fleetList"
              class="v-solo-custom"
              solo
              flat
              label="Preferred Vehicle Fleet"
              hint="Preferred Vehicle Fleet"
              item-value="fleetId"
              item-text="displayText"
              multiple
              clearable
              browser-autocomplete="off"
              auto-select-first
              :disabled="!isEdited"
            >
            </v-autocomplete>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Preferred Drivers:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-autocomplete
              v-model="clientDetails.preAssignedDriverIds"
              :items="driverList"
              class="v-solo-custom"
              solo
              flat
              label="Preferred Drivers"
              hint="Preferred Drivers"
              item-value="id"
              item-text="displayName"
              multiple
              clearable
              browser-autocomplete="off"
              auto-select-first
              :disabled="!isEdited"
            >
            </v-autocomplete>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Weight Requirement:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              label="Weight Requirement"
              v-model="clientDetails.weightRequirementId"
              :items="weightRequirements"
              item-text="longName"
              item-value="id"
              :disabled="!isEdited"
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Billing Cycle:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              label="Billing Cycle"
              v-model="clientDetails.billingCycleId"
              :items="billingCycles"
              :rules="[validate.required]"
              item-text="longName"
              item-value="id"
              :disabled="!isEdited"
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                Default Pickup Load Duration:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              label="Default Pickup Load Duration"
              v-model.number="defaultPickupLoadDuration"
              :disabled="!isEdited"
              class="v-solo-custom"
              min="0"
              :rules="[validate.nonNegative]"
              number
              type="number"
              solo
              suffix="MINUTES"
              hint="When this is blank the division default pickup load duration will be utilised."
              persistent-hint
              flat
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                Default Dropoff Load Duration:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              label="Default Dropoff Load Duration"
              v-model.number="defaultDropoffLoadDuration"
              :disabled="!isEdited"
              class="v-solo-custom"
              :rules="[validate.nonNegative]"
              solo
              min="0"
              type="number"
              suffix="MINUTES"
              hint="When this is blank the division default dropoff load duration will be utilised."
              persistent-hint
              flat
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">
            2. Proof of Delivery (POD) Requirements
          </h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Photos Required:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              label="Photos Required"
              type="number"
              v-model.number="clientDetails.proofOfDelivery.pudPhotos"
              class="v-solo-custom"
              solo
              flat
              hint="The number of POD photos that Drivers will be required to take on the GoDesta Driver app"
              persistent-hint
              :rules="[validate.required]"
              :disabled="!isEdited"
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">PU/DO Paperwork:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              v-model="clientDetails.proofOfDelivery.pudPaperwork"
              :disabled="!isEdited"
              color="light-blue"
              label="Required"
              class="mt-2"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Job Paperwork:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              v-model="clientDetails.proofOfDelivery.jobPaperwork"
              :disabled="!isEdited"
              color="light-blue"
              label="Required"
              class="mt-2"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Signature:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              v-model="clientDetails.proofOfDelivery.deviceSignature"
              :disabled="!isEdited"
              color="light-blue"
              label="Required"
              class="mt-2"
            />
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import DateTimeInputs, {
  DateTimeType,
  HintTextType,
} from '@/components/common/date-picker/date_time_inputs.vue';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import clientTypes from '@/interface-models/Client/ClientTypes';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import billingCycles from '@/interface-models/Generic/BillingCycles/BillingCycles';
import tradingTerms from '@/interface-models/Generic/TradingTerms/TradingTerms';
import { Validation } from '@/interface-models/Generic/Validation';
import { weightRequirements } from '@/interface-models/Jobs/WeightRequirement/WeightRequirement';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { computed, ComputedRef, WritableComputedRef } from 'vue';

const props = withDefaults(
  defineProps<{
    clientDetails: ClientDetails;
    isEdited: boolean;
  }>(),
  {
    isEdited: false,
  },
);

const fleetAssetStore = useFleetAssetStore();

// List of all Truck type Fleet Assets to use in the pre-assigned vehicles
// select in HTML
const allFleetAssetVehicles: ComputedRef<FleetAssetSummary[]> = computed(() => {
  const vehicles: FleetAssetSummary[] = fleetAssetStore.getAllFleetAssetList;
  return vehicles.sort((a, b) =>
    a.csrAssignedId.localeCompare(b.csrAssignedId),
  );
});
// Convert fleet assets map to an array for dropdown
const fleetList = computed(() => {
  return allFleetAssetVehicles.value
    .filter((fleet) => fleet.isActive) // Only include active fleet assets
    .map((fleet) => ({
      id: fleet.csrAssignedId, // ID for reference
      registrationNumber: fleet.registrationNumber, // Registration number
      fleetId: fleet.fleetAssetId, // Fleet ID (should be item-value)
      displayText: `${fleet.csrAssignedId} - ${fleet.registrationNumber}`, // Formatted text
    }));
});

// List of drivers to use in the pre-assigned drivers select in HTML
const driverList = computed(() => {
  return useDriverDetailsStore()
    .getDriverList.filter((driver) => driver.isActive)
    .map((driver) => ({
      id: driver.driverId, // ID for reference
      displayName: `${driver.displayName} - ${driver.mobile}`, // Display name for the driver
    }));
});

const validate: ComputedRef<Validation> = computed(() => {
  return validationRules;
});

const defaultPickupLoadDuration: WritableComputedRef<number | null> = computed({
  get: () => {
    if (!props.clientDetails.pickupLoadDuration) {
      return null;
    }
    return props.clientDetails.pickupLoadDuration / 60000;
  },
  set: (loadDuration: number | null) => {
    if (loadDuration === null) {
      props.clientDetails.pickupLoadDuration = null;
      return;
    }
    props.clientDetails.pickupLoadDuration = loadDuration * 60000;
  },
});

const defaultDropoffLoadDuration: WritableComputedRef<number | null> = computed(
  {
    get: () => {
      if (!props.clientDetails.dropoffLoadDuration) {
        return null;
      }
      return props.clientDetails.dropoffLoadDuration / 60000;
    },
    set: (loadDuration: number | null) => {
      if (loadDuration === null) {
        props.clientDetails.dropoffLoadDuration = null;
        return;
      }
      props.clientDetails.dropoffLoadDuration = loadDuration * 60000;
    },
  },
);
</script>

<style lang="scss" scoped></style>
