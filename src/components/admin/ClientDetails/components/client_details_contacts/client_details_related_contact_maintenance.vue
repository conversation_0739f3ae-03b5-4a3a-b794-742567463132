<template>
  <div
    v-if="props.clientRelatedContact"
    class="related-contacts-maintenance-container"
  >
    <div class="px-3 py-3">
      <GTitle
        class="mb-2"
        :title="textDisplay.title"
        :subtitle="textDisplay.subtitle"
        :divider="false"
      />
      <hr class="divider" />

      <GTextField
        v-model="props.clientRelatedContact.firstName"
        :placeholder="'First Name'"
        :rules="[rules.required]"
        :disabled="false"
        :autofocus="true"
      ></GTextField>

      <GTextField
        v-model="props.clientRelatedContact.lastName"
        :placeholder="'Last Name'"
        :disabled="false"
        :rules="[rules.required]"
      ></GTextField>

      <GTextField
        v-model="props.clientRelatedContact.emailAddress[0]"
        :placeholder="'Email Address'"
        :disabled="false"
        :rules="[rules.email]"
      ></GTextField>

      <GTextField
        v-model="props.clientRelatedContact.contactMobileNumber"
        :placeholder="'Mobile Number'"
        :disabled="false"
        :rules="[rules.numbers]"
        v-mask="'0### ### ###'"
      ></GTextField>
      <GTextField
        v-model="props.clientRelatedContact.contactLandlineNumber"
        :placeholder="'Landline Number'"
        :disabled="false"
        :rules="[rules.numbers]"
        v-mask="'## #### ####'"
      ></GTextField>

      <GSelect
        v-model="props.clientRelatedContact.receivesEmails"
        :items="selectItems"
        :disabled="!props.clientRelatedContact.emailAddress[0]"
        :item-text="'key'"
        :clearable="false"
        :item-value="'value'"
        :placeholder="'Email Preference'"
        :label="'Email Preference'"
      />
      <GSelect
        v-model="props.clientRelatedContact.roleIds"
        :item-text="'key'"
        :item-value="'value'"
        :placeholder="'Roles'"
        multiple
        :items="clientRelatedContactRoles"
      />

      <GSelect
        v-model="syncedRelatedContactsAssociatedCommonAddressIds"
        :items="clientCommonAddressKeyValues"
        :disabled="false"
        :item-text="'key'"
        :item-value="'value'"
        multiple
        :placeholder="'Address locations for this related contact'"
        :disabledValues="syncedRelatedContactsDefaultCommonAddressIds"
        :label="'Address locations for this related contact'"
        hint="Associating an address with this related contact will enable their selection as a contact during the booking process."
      />

      <GSelect
        v-model="syncedRelatedContactsDefaultCommonAddressIds"
        :items="clientCommonAddressKeyValues"
        :disabledValues="disabledDefaultCommonAddressIds"
        multiple
        :disabled="sessionManager.isClientPortal()"
        :item-text="'key'"
        :item-value="'value'"
        :placeholder="'Default address'"
        :label="'Default address'"
        hint="Associating a default address with this related contact will enable automatic selection as a contact during the booking process."
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import { ClientRelatedContact } from '@/interface-models/Client/ClientRelatedContact';
import { KeyValue } from '@/interface-models/Generic/KeyValue/KeyValue';
import { Validation } from '@/interface-models/Generic/Validation';
import UserRole, { UserType } from '@/interface-models/Roles/UserRoles';
import { useRootStore } from '@/store/modules/RootStore';
import { sessionManager } from '@/store/session/SessionState';
import { ComputedRef, WritableComputedRef, computed, watch } from 'vue';

interface IProps {
  clientRelatedContact: ClientRelatedContact | null;
  clientsCommonAddresses: ClientCommonAddress[] | null;
  relatedContactsAssociatedCommonAddressIds: string[];
  relatedContactsDefaultCommonAddressIds: string[];
}
const props = withDefaults(defineProps<IProps>(), {
  clientRelatedContact: null,
  clientsCommonAddresses: null,
});

const emit = defineEmits([
  'update:relatedContactsAssociatedCommonAddressIds',
  'update:relatedContactsDefaultCommonAddressIds',
  'setRolesFromMultiSelect',
]);

// Watches email for email preference
watch(
  () => props.clientRelatedContact!.emailAddress[0],
  (newEmail) => {
    if (!newEmail) {
      props.clientRelatedContact!.receivesEmails = false;
    }
  },
);

// Computed property for select email preference
const selectItems = computed(() => {
  return [
    { key: 'This User Receives Emails', value: true },
    { key: 'This User Does Not Receive Emails', value: false },
  ];
});

const syncedRelatedContactsAssociatedCommonAddressIds: WritableComputedRef<
  string[]
> = computed({
  get(): string[] {
    return props.relatedContactsAssociatedCommonAddressIds;
  },
  set(newValue: string[]): void {
    emit('update:relatedContactsAssociatedCommonAddressIds', newValue);
  },
});

const syncedRelatedContactsDefaultCommonAddressIds: WritableComputedRef<
  string[]
> = computed({
  get(): string[] {
    return props.relatedContactsDefaultCommonAddressIds;
  },
  set(newValue: string[]): void {
    emit('update:relatedContactsDefaultCommonAddressIds', newValue);
  },
});

const rules: ComputedRef<Validation> = computed(() => {
  return validationRules;
});

const clientCommonAddressKeyValues: ComputedRef<KeyValue[]> = computed(() => {
  if (!props.clientsCommonAddresses) {
    return [];
  }
  const allClientsCommonAddresses: KeyValue[] = props.clientsCommonAddresses
    .filter((x: ClientCommonAddress) => x._id !== undefined)
    .map((x: ClientCommonAddress) => {
      let key: string = '';
      key += x.addressNickname ? x.addressNickname : '';

      return {
        key,
        value: x._id!,
      };
    });
  return allClientsCommonAddresses;
});

/**
 * A default address should only be selectable if the address is already associated with the related contact
 * @returns {ComputedRef<string[]>} A list of common address ids that should not be selectable as default address
 */
const disabledDefaultCommonAddressIds: ComputedRef<string[]> = computed(() => {
  const disabledCommonAddressIds: string[] = [];
  for (const address of clientCommonAddressKeyValues.value) {
    const addressId = address.value as string;
    if (
      !syncedRelatedContactsAssociatedCommonAddressIds.value.includes(addressId)
    ) {
      disabledCommonAddressIds.push(addressId);
    }
  }
  return disabledCommonAddressIds;
});

/**
 * Computes and returns a list of user roles filtered by client user type.
 * We also append a default dispatcher role to this list so that the default dispatcher
 * role can be added via backend logic
 * @returns {ComputedRef<UserRole[]>} A computed reference to an array of user roles.
 */
const clientRelatedContactRoles: ComputedRef<KeyValue[]> = computed(() => {
  const clientPersonRoles: UserRole[] = useRootStore().roleList.filter(
    (x: UserRole) => x.userType === UserType.CLIENT,
  );

  return clientPersonRoles.map((x: UserRole) => {
    return {
      key: x.name,
      value: x.roleId,
    };
  });
});

interface TextDisplay {
  title: string;
  subtitle: string;
}

const textDisplay: ComputedRef<TextDisplay> = computed(() => {
  const isOperations: boolean = sessionManager.isOperationsPortal();
  const title = isOperations
    ? 'Client Related Contact'
    : 'Address Related Contact';
  const subtitle = `Add or update this Related Contact's information. This includes contact details, roles within their organisation, whether they receive emails, and more.`;

  return {
    title,
    subtitle,
  };
});
</script>

<style scoped lang="scss">
.related-contacts-management-container {
  padding-top: 50px;
}
</style>
