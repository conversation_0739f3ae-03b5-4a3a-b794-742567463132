<template>
  <GDialog
    v-if="isActive"
    :width="'720px'"
    ref="dialogRef"
    :title="'Create New Contact'"
    :confirmBtnText="`Create ${computedTitle}`"
    :isActionable="isContactTypeSelected"
    :isDelete="true"
    deleteBtnText="Back"
    @closeDialog="cancelMaintenance"
    @confirm="saveNewContact"
    @deleteItem="isContactTypeSelected = false"
    :cancelDisabled="isFullSaveRequired"
    :isLoading="isLoading"
  >
    <div
      v-if="!isContactTypeSelected"
      class="grid grid-cols-2 gap-4 px-3 py-3"
      md8
    >
      <GTitle
        title="Select Contact Type"
        subtitle="Select contact type to create"
        :divider="true"
      />
      <v-flex
        v-for="item in selectItems"
        :key="item.value"
        @click="selectOption(item.value)"
        :class="[
          'select-contact-type-items',
          {
            disabled:
              item.key === 'Login User' &&
              sessionManager.isClientPortal() &&
              !isAccount,
          },
        ]"
      >
        <div>
          <h3 class="item-value">{{ item.key }}</h3>
          <p class="item-des">{{ item.description }}</p>
        </div>
        <div
          v-if="
            item.key === 'Login User' &&
            sessionManager.isClientPortal() &&
            !isAccount
          "
          class="tooltiptxt"
        >
          <v-icon color="white">priority_high</v-icon>
          ACCOUNT MANAGEMENT ROLE REQUIRED
        </div>
      </v-flex>

      <v-divider></v-divider>
      <div class="dialog-actions-container">
        <GButton
          class="action-btn"
          :color="'error'"
          outlined
          @click="cancelMaintenance"
          >Cancel</GButton
        >
      </div>
    </div>

    <div v-else class="px-3 py-3" md8>
      <h1 class="header--light">{{ computedSubtitle }}</h1>
      <v-divider class="mt-2"></v-divider>
      <v-flex md12 v-if="selectedContactType === ContactType.CompanyContacts">
        <v-alert type="info" value="true">
          <strong class="pr-2"
            >Info: This User will is be created as New Client Contact.</strong
          >
          <ul class="pt-1">
            <li>The Registered Email will receive Account Activation Email.</li>
            <li>User can login to Client Portal after Activation.</li>
          </ul>
        </v-alert>
      </v-flex>
      <div v-if="selectedContactType === ContactType.ClientRelatedPersons">
        <ClientDetailsRelatedContactMaintenance
          :clientRelatedContact="clientRelatedContact"
          :relatedContactsAssociatedCommonAddressIds.sync="
            relatedContactsAssociatedCommonAddressIds
          "
          :relatedContactsDefaultCommonAddressIds.sync="
            relatedContactsDefaultCommonAddressIds
          "
          @cancelMaintenance="cancelMaintenance"
          :clientsCommonAddresses="clientsCommonAddresses"
        />
      </div>

      <div v-if="selectedContactType === ContactType.CompanyContacts">
        <ClientPersonMaintenance
          ref="clientPersonMaintenanceDialogRef"
          :client_id="client_id"
          :clientId="clientId"
          :clientName="clientName"
          :clientPersonWithAuthDetails="clientPersonWithAuthDetails"
          :defaultDispatcherId="defaultDispatcherId"
          :clientPersonIds="clientPersonIds"
          :dispatcherOnly="false"
          @cancelMaintenance="cancelMaintenance"
          @setIsEmailVerificationLoading="setIsEmailVerificationLoading"
          @setIsMobileVerificationLoading="setIsMobileVerificationLoading"
          @setDefaultDispatcher="setDefaultDispatcher"
          @addClientPersonDispatcher="addClientPersonContact"
          @updatedRoleAccess="isFullSaveRequired = true"
        >
        </ClientPersonMaintenance>
      </div>
    </div>
  </GDialog>
</template>

<script setup lang="ts">
import ClientPersonMaintenance from '@/components/admin/ClientDetails/components/client_details_contacts/client_details_client_person_maintenance.vue';
import ClientDetailsRelatedContactMaintenance from '@/components/admin/ClientDetails/components/client_details_contacts/client_details_related_contact_maintenance.vue';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAccountManagementRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validate } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import {
  ClientCommonAddressRelatedContactSave,
  saveCommonAddressAndRelatedContact,
} from '@/interface-models/Client/ClientCommonAddressRelatedContactSave';
import ClientPerson from '@/interface-models/Client/ClientDetails/ClientPerson/ClientPerson';
import { ClientRelatedContact } from '@/interface-models/Client/ClientRelatedContact';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import {
  ClientPersonWithAuthDetails,
  ClientRoleStatus,
  SaveNewClientPersonRequest,
} from '@/interface-models/User/ClientPerson';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useWebsocketStore } from '@/store/modules/WebsocketStore';
import { sessionManager } from '@/store/session/SessionState';
import { computed, ComputedRef, onMounted, Ref, ref, watch } from 'vue';

enum ContactType {
  ClientRelatedPersons = 'ClientRelatedPersons',
  CompanyContacts = 'CompanyContacts',
}
// Define props
const props = withDefaults(
  defineProps<{
    clientId: string;
    client_id: string;
    dialogIsActive: boolean;
    clientPersonIds: string[];
    defaultDispatcherId?: string | null;
    clientName: string;
  }>(),
  {
    defaultDispatcherId: '',
    client_id: '',
    clientId: '',
  },
);

// Local state for dialog editing
const isActive = computed(() => props.dialogIsActive);
const isContactTypeSelected = ref(false);
const dialogRef = ref(null);
const clientPersonMaintenanceDialogRef = ref(null);

const isAccount = computed(() => hasAccountManagementRole());

const clientRelatedContact: Ref<ClientRelatedContact | null> = ref(null);
const clientPersonWithAuthDetails: Ref<ClientPersonWithAuthDetails | null> =
  ref(null);
const clientsCommonAddresses: Ref<ClientCommonAddress[] | null> = ref([]);

const relatedContactsAssociatedCommonAddressIds: Ref<string[]> = ref([]);
const relatedContactsDefaultCommonAddressIds: Ref<string[]> = ref([]);

const isSaveNewClientPersonRequest: Ref<boolean> = ref(false);
const isDefaultDispatcher: Ref<boolean> = ref(false);
const isEmailVerificationLoading: Ref<boolean> = ref(false);
const isMobileVerificationLoading: Ref<boolean> = ref(false);
const isLoading: Ref<boolean> = ref(false);

const isFullSaveRequired: Ref<boolean> = ref(false);

const selectedContactType: Ref<ContactType> = ref(ContactType.CompanyContacts);

const emit = defineEmits(['dialogIsActive']);

// selection Create ContactType options
const selectItems = computed(() => [
  {
    key: 'Non-Login User',
    value: ContactType.ClientRelatedPersons,
    description: `A non-login user should be used if this person does not require login access to the client portal. These users will have their details saved into the platform in order to be able to easily add their details to future deliveries.`,
  },
  {
    key: 'Login User',
    value: ContactType.CompanyContacts,
    description: `A login user should be created if this user requires login access to the client portal. These users will be able to book new jobs and view jobs in progress in real-time. These users will have their details saved into the platform in order to be able to easily add their details to future deliveries.`,
  },
]);

// select Contact type and load selected view
const selectOption = (value: ContactType) => {
  selectedContactType.value = value;
  loadSelectedContent();
};

const loadSelectedContent = () => {
  if (selectedContactType.value) {
    isContactTypeSelected.value = true;
  }
};

// Computed property for the Dialog buttons
const computedTitle = computed(() => {
  switch (selectedContactType.value as ContactType) {
    case ContactType.ClientRelatedPersons:
      return 'Client Related Contact';
    case ContactType.CompanyContacts:
      return 'Client Contact';
    default:
      return '';
  }
});

// Computed property for the Dialog Title
const computedSubtitle = computed(() => {
  switch (selectedContactType.value as ContactType) {
    case ContactType.ClientRelatedPersons:
      return 'Creates New User As Client Related Contact';
    case ContactType.CompanyContacts:
      return 'Create New User as Client Contact';
    default:
      return '';
  }
});
// selected Contact type from selector
function selectContactType(type: ContactType) {
  switch (type) {
    case ContactType.ClientRelatedPersons:
      addNewClientRelatedContact();
      break;
    case ContactType.CompanyContacts:
      addNewClientPerson();
      break;
  }
}

// create new contact based on selection
function saveNewContact() {
  switch (selectedContactType.value as ContactType) {
    case ContactType.ClientRelatedPersons:
      saveClientRelatedContact();
      break;
    case ContactType.CompanyContacts:
      saveClientPerson();
      break;
    default:
      addNewClientPerson();
      break;
  }
}

// Close the dialog emit for parent component
function closeDialog() {
  emit('dialogIsActive', false); // Emit `false` to close the dialog
}

/**
 * Adds a new client related contact.
 * Initializes a new client related contact and sets the client ID and role as Contact.
 */
function addNewClientRelatedContact() {
  // Call getClientsCommonAddresses before creating a new ClientRelatedContact
  getClientsCommonAddresses();
  clientRelatedContact.value = new ClientRelatedContact();
  clientRelatedContact.value.clientId = props.clientId;
}

/**
 * Initializes a new client person with authorization details.
 * Resets the `clientPersonWithAuthDetails` ref to a new instance of `ClientPersonWithAuthDetails` and role as Contact.
 */
function addNewClientPerson(): void {
  clientPersonWithAuthDetails.value = new ClientPersonWithAuthDetails();
}

/**
 * Cancels and closes the ad-hoc contact dialog by setting it to null.
 */
function cancelMaintenance(): void {
  switch (selectedContactType.value as ContactType) {
    /**
     * Cancels the related contact maintenance operation.
     * Clears the current related contact information and associated default common address IDs.
     */
    case ContactType.ClientRelatedPersons:
      clientRelatedContact.value = null;
      relatedContactsAssociatedCommonAddressIds.value = [];
      relatedContactsDefaultCommonAddressIds.value = [];
      break;
    /**
     * Cancels the current client person maintenance.
     * Resets the `clientPersonWithAuthDetails` ref to null.
     */
    case ContactType.CompanyContacts:
      clientPersonWithAuthDetails.value = null;
      break;
    default:
      break;
  }
  isFullSaveRequired.value = false;
  isContactTypeSelected.value = false;
  closeDialog();
}

/**
 * Saves the client related contact details. Validates the form data before saving.
 * Constructs the save request with the related contact and associated address information.
 * If validation fails or the related contact does not match the client ID, it aborts the save operation.
 */
async function saveClientRelatedContact() {
  if (
    !validate(dialogRef.value) ||
    !clientRelatedContact.value ||
    !props.client_id ||
    clientRelatedContact.value.clientId !== props.clientId
  ) {
    return;
  }

  isLoading.value = true;
  const request: ClientCommonAddressRelatedContactSave = {
    clientId: props.clientId,
    clientCommonAddress: null,
    clientRelatedContact: clientRelatedContact.value,
    relatedContactsAssociatedCommonAddressIds:
      relatedContactsAssociatedCommonAddressIds.value,
    relatedContactsDefaultCommonAddressIds:
      relatedContactsDefaultCommonAddressIds.value,
    updatedClientCommonAddresses: [],
  };

  const savedSiteContact = await saveCommonAddressAndRelatedContact(request);

  if (!savedSiteContact) {
    showNotification('Something went wrong.', {
      title: 'Error',
      type: HealthLevel.ERROR,
    });
  }
  isLoading.value = false;
  cancelMaintenance();
}

/**
 * Fetches common addresses associated with the client.
 * Retrieves and sets the client's common addresses.
 * Logs an error if the fetch operation fails.
 */
async function getClientsCommonAddresses(): Promise<void> {
  // fetch client related contacts
  const commonAddresses: ClientCommonAddress[] | null =
    await useClientDetailsStore().getClientCommonAddressesByClientId(
      props.clientId,
    );
  if (!commonAddresses) {
    console.error(
      "Something went wrong when fetching client's common addresses.",
    );
    return;
  }
  // TODO: Commented out because parent component is not listening to this
  // event. Is something missing here?
  // emit('updateClientsCommonAddresses', commonAddresses);
}

/**
 * Event emitted back from maintenance component when adding an existing client person to this client.
 * @param {string} clientPerson_id - The client person id that will be added to this clients contact.
 * array.
 * @returns {void}
 */
function addClientPersonContact(clientPerson: ClientPerson): void {
  clientPersonWithAuthDetails.value = new ClientPersonWithAuthDetails();
  clientPersonWithAuthDetails.value.clientPerson = clientPerson;
  isFullSaveRequired.value = true;
}

/**
 * Computed ref that determines if the current client person is new.
 *
 * @returns {ComputedRef<boolean>} True if the client person is new (without an ID), false otherwise.
 */
const isNewClientPerson: ComputedRef<boolean> = computed(() => {
  if (!clientPersonWithAuthDetails.value) {
    return false;
  }
  return !clientPersonWithAuthDetails.value.clientPerson._id;
});

/**
 * Saves the client person details.
 * Validates the input and sends a WebSocket request to save the new client person.
 * Handles the response by adding the new client person to the user management store.
 */
function saveClientPerson() {
  if (
    !clientPersonWithAuthDetails.value ||
    !validate(clientPersonMaintenanceDialogRef.value)
  ) {
    return;
  }

  isSaveNewClientPersonRequest.value = true;

  const rolesToAdd: number[] = isNewClientPerson.value
    ? [
        ...new Set(
          clientPersonWithAuthDetails.value.clientRoles.map(
            (x: ClientRoleStatus) => x.roleId,
          ),
        ),
      ]
    : [];

  const saveNewClientPersonRequest: SaveNewClientPersonRequest = {
    clientPerson: clientPersonWithAuthDetails.value.clientPerson,
    clientId: props.client_id,
    rolesToAdd,
    defaultDispatcher: isDefaultDispatcher.value,
  };

  useWebsocketStore().sendWebsocketRequest(
    new WebSocketRequest(
      sessionManager.isClientPortal()
        ? `/user/${sessionManager.getUserName()}/clientDetails/clientPersonDispatchers/saveNewClientPerson`
        : '/clientDetails/clientPersonDispatchers/saveNewClientPerson',
      saveNewClientPersonRequest,
      true,
    ),
  );
  // TODO: Commented out because parent component is not listening to this
  // event. Is something missing here?
  // emit('clientPerson', clientPersonWithAuthDetails.value.clientPerson);
  cancelMaintenance();
}

/**
 * Sets the loading state for email verification.
 *
 * @param {boolean} isLoading - True if email verification is in progress, false otherwise.
 */
function setIsEmailVerificationLoading(isLoading: boolean): void {
  isEmailVerificationLoading.value = isLoading;
}

/**
 * Sets the loading state for mobile verification.
 *
 * @param {boolean} isLoading - True if mobile verification is in progress, false otherwise.
 */
function setIsMobileVerificationLoading(isLoading: boolean): void {
  isMobileVerificationLoading.value = isLoading;
}

/**
 * Set the default contact boolean. Emitted from child.
 */
function setDefaultDispatcher(value: boolean): void {
  isDefaultDispatcher.value = value;
}

// Function to be called when isActive changes
const onIsActiveChange = () => {
  selectContactType(selectedContactType.value as ContactType);
};

// Watcher for selected contact type
watch(selectedContactType, (newValue: string) => {
  if (newValue) {
    if (newValue === ContactType.ClientRelatedPersons) {
      addNewClientPerson();
    }
    selectContactType(newValue as ContactType);
  } else {
    selectContactType(selectedContactType.value as ContactType);
  }
});

// Watch the isActive computed property for dialog
watch(isActive, onIsActiveChange);

//  default section when dialog is active
onMounted(() => {
  addNewClientPerson();
});
</script>
