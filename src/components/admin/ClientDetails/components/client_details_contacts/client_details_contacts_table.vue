<template>
  <v-layout row wrap>
    <v-flex md12>
      <v-layout>
        <v-flex pr-2 mr-2 md6>
          <v-layout align-center fill-height>
            <GTitle
              :title="textDisplay.tableTitle"
              :subtitle="textDisplay.tableSubtitle"
              :divider="true"
            />
          </v-layout>
        </v-flex>
        <v-flex class="checkbox-container">
          <v-checkbox
            color="orange"
            v-model="showOnlyWithRoles"
            label="Show users with No Roles"
            class="v-checkbox-custom"
          >
          </v-checkbox>
          <InformationTooltip
            :bottom="true"
            :tooltipType="HealthLevel.INFO"
            class="tooltip-container"
          >
            <span slot="content">Users that are NO LONGER EMPLOYED.</span>
          </InformationTooltip>
        </v-flex>
      </v-layout>

      <v-layout class="top-container">
        <v-flex md5 pr-2>
          <v-text-field
            v-model="search"
            appendIcon="search"
            label="Search"
            hint="Search Contacts by Name"
            persistent-hint
            hide-details
            color="orange"
            height="30"
            outline
            flat
            solo
            class="v-solo-custom"
            clearable
          ></v-text-field>
        </v-flex>
        <v-flex md3 pr-1>
          <v-select
            v-model="filterType"
            :items="selectItems"
            :item-text="'key'"
            label="Filter Contact Type"
            outline
            class="v-solo-custom"
            flat
            solo
          ></v-select>
        </v-flex>
        <v-flex md4>
          <v-select
            v-model="selectedRole"
            :items="availableRoles"
            item-value="id"
            item-text="name"
            label="Filter Role Type"
            placeholder="All Roles"
            small-chips
            multiple
            outline
            flat
            solo
            class="v-solo-custom"
            color="Orange"
            clearable
          >
            <template v-slot:selection="{ item }">
              <v-chip small class="chips">
                <span>{{ item.name }}</span>
              </v-chip>
            </template>
          </v-select></v-flex
        >
      </v-layout>

      <v-flex md12>
        <GTable
          :headers="headers"
          :items="filteredClientContacts"
          :selectable="true"
          :search="search"
          :isLoading="!allClientRelatedContacts || !clientsCommonAddresses"
          :noDataMessage="textDisplay.tableNoDataMessage"
          @selectItem="handleSelectItem"
          :height="
            !sessionManager.isClientPortal() ? 'calc(100vh - 223px)' : '100%'
          "
        >
          <template v-slot:items="prop">
            <td :class="prop.item.roleIds.length <= 0 ? 'no-roles' : ''">
              {{ prop.item.firstName ? prop.item.firstName : '-' }}
              {{ prop.item.lastName ? prop.item.lastName : '-' }}
            </td>
            <td
              v-if="
                prop.item.isRelatedContact &&
                prop.item.roleIds &&
                prop.item.roleIds.length > 0
              "
            >
              {{ getRoleNamesFromRoleIds(prop.item.roleIds) }}
            </td>
            <td
              v-else-if="
                !prop.item.isRelatedContact &&
                prop.item.roleIds &&
                prop.item.roleIds.length > 0
              "
            >
              {{ getClientPersonRoleNames(prop.item._id, prop.item.roleId) }}
            </td>
            <td v-else>
              <InformationTooltip
                :bottom="true"
                :tooltipType="HealthLevel.WARNING"
              >
                <v-layout slot="content"
                  >User does not have any roles.</v-layout
                >
              </InformationTooltip>
            </td>
            <td>
              {{
                prop.item.emailAddress && prop.item.emailAddress.length > 0
                  ? prop.item.emailAddress[0]
                  : '-'
              }}
            </td>
            <td>
              {{ formatPhoneNumber(prop.item.contactMobileNumber) }}
            </td>
            <td>
              {{ formatPhoneNumber(prop.item.contactLandlineNumber) }}
            </td>
            <td class="text-xs-center">
              {{ prop.item.receivesEmails ? 'YES' : 'NO' }}
            </td>
            <td
              class="text-xs-center font--bold"
              v-if="filterType === ContactType.allContacts"
            >
              <v-icon
                v-if="prop.item.roleIds.length === 0"
                size="20"
                color="warning"
              >
                warning</v-icon
              >
              <v-icon
                v-else-if="prop.item.hasActiveRole"
                size="20"
                color="success"
                >check_circle</v-icon
              >
              <v-icon
                v-else-if="prop.item.isRelatedContact"
                size="20"
                color="error"
                >cancel</v-icon
              >

              <v-icon v-else size="20" color="grey"
                >radio_button_unchecked</v-icon
              >
            </td>
          </template>
        </GTable>
      </v-flex>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import {
  formatPhoneNumber,
  maskMyString,
} from '@/helpers/StringHelpers/StringHelpers';
import { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import { ClientCommonAddressRelatedContactSave } from '@/interface-models/Client/ClientCommonAddressRelatedContactSave';
import {
  ClientRelatedContact,
  getClientRelatedContactsByClientId,
} from '@/interface-models/Client/ClientRelatedContact';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import UserRole from '@/interface-models/Roles/UserRoles';
import {
  ClientPersonWithAuthDetails,
  ClientRoleStatus,
} from '@/interface-models/User/ClientPerson';
import { UserRoleStatus } from '@/interface-models/User/UserRoleStatus';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useRootStore } from '@/store/modules/RootStore';
import { useUserManagementStore } from '@/store/modules/UserManagementStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import Fuse from 'fuse.js';
import { ComputedRef, Ref, computed, onMounted, onUnmounted, ref } from 'vue';

enum ContactType {
  allContacts = 'AllContacts',
  ClientRelatedPersons = 'ClientRelatedPersons',
  CompanyContacts = 'CompanyContacts',
}

interface IProps {
  clientId: string;
  clientsCommonAddresses?: ClientCommonAddress[] | null;
  client_id: string;
  clientPersonIds: string[];
  defaultDispatcherId?: string | null;
}
const props = withDefaults(defineProps<IProps>(), {
  clientId: '',
  defaultDispatcherId: '',
  clientsCommonAddresses: null,
});

interface CombinedContact {
  firstName: string;
  lastName: string;
  emailAddress: string[];
  contactMobileNumber?: string;
  contactLandlineNumber?: string;
  receivesEmails?: boolean;
  roleIds: number[];
  roleId: ClientRoleStatus[];
  isRelatedContact?: boolean;
  _id?: string;
  hasActiveRole?: boolean;
}

const clientDetailsStore = useClientDetailsStore();
const userManagementStore = useUserManagementStore();

const search: Ref<string> = ref('');

const isLoading: Ref<boolean> = ref(true);

const emit = defineEmits(['viewSiteContact', 'selectUser']);

const showOnlyWithRoles = ref(false);

const selectedRole = ref<number[]>([]);

const rolesList: UserRole[] = useRootStore().roleList;

const availableRoles = rolesList.map((role) => ({
  id: role.roleId,
  name: role.name,
}));

const filterType: Ref<ContactType> = ref(ContactType.allContacts);
const selectItems = computed(() => [
  {
    key: 'All Contacts',
    value: ContactType.allContacts,
  },
  {
    key: 'Client Related Contacts (non-login)',
    value: ContactType.ClientRelatedPersons,
  },
  { key: 'Client Contacts (login)', value: ContactType.CompanyContacts },
]);

/**
 * Defines the structure and labels for table headers used in displaying site contact information.
 * Each header object includes the text to be displayed, alignment for the content, and the corresponding
 * value that links to the data property in the site contact object.
 *
 * @type {TableHeader[]} headers - An array of objects, each representing a header in the table.
 */
const headers = computed(() => {
  const baseHeaders = [
    {
      text: 'Name',
      align: 'left',
      value: 'firstName',
      sortable: false,
    },
    {
      text: 'Roles',
      align: 'left',
      sortable: true,
      value: '',
    },
    {
      text: 'Email Address',
      value: 'emailAddress',
      align: 'left',
      sortable: false,
    },
    {
      text: 'Mobile Number',
      value: 'mobileNumber',
      align: 'left',
    },
    {
      text: 'Landline Number',
      value: 'landlineNumber',
      align: 'left',
      sortable: false,
    },
    {
      text: 'Receives Emails',
      align: 'center',
      sortable: false,
      value: 'receivesEmails',
    },
  ];

  // Conditionally add the "Login Access" column based on filterType
  if (filterType.value === ContactType.allContacts) {
    baseHeaders.push({
      text: 'Login Access',
      value: 'authUser',
      align: 'left',
    });
  }

  return baseHeaders;
});

function handleSelectItem(item: CombinedContact): void {
  if (item.isRelatedContact) {
    emit('viewSiteContact', JSON.parse(JSON.stringify(item)));
  } else if (!item.isRelatedContact) {
    emit('selectUser', item);
  }
}

/**
 * Returns a string of comma-separated role names associated to the supplied roleIds
 * @param {number[]} roleIds - The list of roleIds we will returns names for.
 * @returns {string} The comma-separated role names
 */
function getRoleNamesFromRoleIds(roleIds: number[]): string {
  const rolesList: UserRole[] = useRootStore().roleList;
  return roleIds
    .map((roleId: number) => {
      const role: UserRole | undefined = rolesList.find(
        (role: UserRole) => role.roleId === roleId,
      );
      return role ? role.name : '';
    })
    .filter((x) => x)
    .join(', ');
}

/**
 * Utilise the full list and return a filtered result based on the users searched text.
 * @return {ClientRelatedContact[]} List of client site contacts filtered via search text.
 */
const clientRelatedContacts: ComputedRef<ClientRelatedContact[]> = computed(
  () => {
    if (!allClientRelatedContacts.value) {
      return [];
    }
    const siteContacts: ClientRelatedContact[] = allClientRelatedContacts.value;
    return siteContacts;
  },
);

/**
 * The list of fetched client person with auth details
 *
 * @returns {ClientPersonWithAuthDetails[]}
 */
const clientPersonsWithAuthDetailsList: ComputedRef<
  ClientPersonWithAuthDetails[]
> = computed(() => {
  if (
    !userManagementStore.clientIdAndClientPersonWithAuthDetailsList ||
    userManagementStore.clientIdAndClientPersonWithAuthDetailsList.client_id !==
      props.client_id ||
    isLoading.value
  ) {
    return [];
  }
  return userManagementStore.clientIdAndClientPersonWithAuthDetailsList
    .clientPersonWithAuthDetailsList;
});

const combinedClientContacts: ComputedRef<CombinedContact[]> = computed(() => {
  const siteContacts: CombinedContact[] = clientRelatedContacts.value.map(
    (contact) => ({
      ...contact,
      firstName: contact.firstName,
      lastName: contact.lastName,
      emailAddress: contact.emailAddress || [''],
      contactMobileNumber: contact.contactMobileNumber,
      contactLandlineNumber: contact.contactLandlineNumber,
      receivesEmails: contact.receivesEmails,
      roleIds: contact.roleIds,
      roleId: [],
      isRelatedContact: true,
      hasActiveRole: false,
      _id: contact._id,
    }),
  );

  const authDetails: CombinedContact[] =
    clientPersonsWithAuthDetailsList.value.map((detail) => ({
      ...detail,
      _id: detail.clientPerson._id,
      firstName: detail.clientPerson.firstName,
      lastName: detail.clientPerson.lastName,
      emailAddress: detail.clientPerson.emailAddress,
      contactMobileNumber: detail.clientPerson.contactMobileNumber,
      contactLandlineNumber: detail.clientPerson.contactLandlineNumber,
      receivesEmails: detail.clientPerson.receivesEmails,
      roleIds: detail.clientRoles.map((role) => role.roleId),
      roleId: detail.clientRoles,
      hasActiveRole: hasActiveRole(detail.clientRoles),
      isRelatedContact: false,
    }));

  return [...siteContacts, ...authDetails].sort((a, b) => {
    const nameA = `${a.firstName} ${a.lastName}`.toLowerCase();
    const nameB = `${b.firstName} ${b.lastName}`.toLowerCase();
    if (nameA < nameB) {
      return -1;
    }
    if (nameA > nameB) {
      return 1;
    }
    return 0;
  });
});

function hasActiveRole(clientRoles: ClientRoleStatus[]): boolean {
  return clientRoles.some((role) => role.status === UserRoleStatus.ACTIVE);
}

const filteredClientContacts = computed(() => {
  let combinedList = combinedClientContacts.value;

  // Apply the filter type
  if (filterType.value === ContactType.ClientRelatedPersons) {
    combinedList = combinedList.filter(
      (item: CombinedContact) => item.isRelatedContact,
    );
  } else if (filterType.value === ContactType.CompanyContacts) {
    combinedList = combinedList.filter(
      (item: CombinedContact) => !item.isRelatedContact,
    );
  }
  // Show/hide contacts with no roles
  if (!showOnlyWithRoles.value) {
    combinedList = combinedList.filter((item) => {
      return item.roleIds.length > 0;
    });
  }

  // Filter by selected roles
  if (selectedRole.value && selectedRole.value.length > 0) {
    combinedList = combinedList.filter((item: CombinedContact) => {
      const itemRoleIds = item.isRelatedContact
        ? item.roleIds
        : item.roleId.map((role) => role.roleId);
      return selectedRole.value.some((roleId: number) =>
        itemRoleIds.includes(roleId),
      );
    });
  }

  // If no search value is present, return the filtered combined list
  if (!search.value) {
    return combinedList;
  }

  // Filter the combined list using Fuse.js
  const fuse = new Fuse(combinedList, {
    includeScore: true,
    threshold: 0.3,
    keys: ['firstName', 'lastName'],
  });

  return fuse.search(search.value).map((result) => {
    return { ...result.item };
  });
});

/**
 * Computes whether the user has client portal access. called from table cell in template.
 * @param {string} clientPerson_id - The client person's mongo id. Used to compare to default dispatcherId
 * @param {clientRoleStatus[]} clientRoles - a list of client role statuses
 * @returns {boolean}
 */
function getClientPersonRoleNames(
  clientPerson_id: string,
  clientRoles: ClientRoleStatus[],
): string {
  let roleNames: string = clientRoles
    .map((x: ClientRoleStatus) => x.roleName)
    .join(', ');
  // If this client person is default dispatcher we will replace 'Dispatcher' role name with 'Default Dispatcher'
  if (clientPerson_id === props.defaultDispatcherId) {
    roleNames = roleNames.replaceAll('Dispatcher', 'Default Dispatcher');
  }
  return roleNames;
}

/**
 * return a computed list of site contacts. sourced from ClientDetailsModule for reactivity
 * @return {void} void
 */
const allClientRelatedContacts: ComputedRef<ClientRelatedContact[]> = computed(
  () => {
    if (isLoading.value) {
      return [];
    }
    return clientDetailsStore.clientRelatedContacts;
  },
);

/**
 * fetch client related contacts
 * @return {void} void
 */
async function getClientsRelatedContacts(): Promise<void> {
  // fetch client related contacts
  isLoading.value = true;
  const siteContacts: ClientRelatedContact[] | null =
    await getClientRelatedContactsByClientId(props.clientId);
  if (!siteContacts) {
    console.error("Something went wrong when fetching client's site contacts.");
    return;
  }
  isLoading.value = false;
}

/**
 * Computes text displays based on portal type
 *
 * @returns {TextDisplay} An object containing customized text for different sections within this component.
 */
const textDisplay = computed(() => {
  const isOperations: boolean = sessionManager.isOperationsPortal();
  const divisionDetails = useCompanyDetailsStore().divisionDetails;

  let tableTitle = 'All Contacts';
  let tableSubtitle = 'Details about all contacts related to client';

  if (isOperations) {
    if (filterType.value === ContactType.ClientRelatedPersons) {
      tableTitle = 'Related Contacts';
      tableSubtitle =
        'Related Contacts can be assigned various roles which describe their relationship with the client.';
    } else if (filterType.value === ContactType.CompanyContacts) {
      tableTitle = 'Client Contacts';
      tableSubtitle =
        'Company Related Contacts are people who are associated with the client, and can have access to the Client Portal.';
    }
  } else {
    const clientPortalAlertMessage =
      divisionDetails && divisionDetails.name && divisionDetails.phone
        ? 'Please contact ' +
          divisionDetails.name +
          ' at ' +
          maskMyString(divisionDetails.phone, '## #### ####') +
          ' if you would like to create or update contacts.'
        : '';
    tableTitle = 'Client Contacts';
    tableSubtitle = clientPortalAlertMessage;
  }

  const tableNoDataMessage: string = 'Client has no matching contacts';

  return {
    tableTitle,
    tableSubtitle,
    tableNoDataMessage,
  };
});

/**
 * Toggles a listener for saved common addresses and site contacts.
 * This listener is mostly for division responses from other users
 *
 * @param {boolean} setListener - Flag indicating whether to set or remove the listener.
 */
function setListenerForSavedCommonAddressOrSiteContact(setListener: boolean) {
  if (!setListener) {
    Mitt.off('savedCommonAddressesByClientId');
    return;
  }
  Mitt.on(
    'savedCommonAddressesByClientId',
    (response: ClientCommonAddressRelatedContactSave) => {
      clientDetailsStore.setUpdatedClientCommonAddresses(
        response.updatedClientCommonAddresses,
      );
      clientDetailsStore.setUpdatedRelatedContact(
        response.clientRelatedContact,
      );
    },
  );
}

onMounted(() => {
  userManagementStore.getClientPersonsWithAuthDetails(
    props.client_id,
    props.clientPersonIds,
  );
  getClientsRelatedContacts();
  setListenerForSavedCommonAddressOrSiteContact(true);
});

onUnmounted(() => {
  setListenerForSavedCommonAddressOrSiteContact(false);
});
</script>

<style scoped lang="scss">
.no-roles {
  border-left: 4px solid var(--yellow);
  font-weight: bold;
}
.checkbox-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  align-self: center;
  align-content: center;
  gap: 1px;
  max-width: 244px;
}

.error--text {
  color: $error !important;
  caret-color: $error !important;
}

.success--text {
  color: $success-type !important;
  caret-color: $success-type !important;
}

.chips {
  color: $highlight !important;
  background-color: var(--background-color-600) !important;
}
</style>
