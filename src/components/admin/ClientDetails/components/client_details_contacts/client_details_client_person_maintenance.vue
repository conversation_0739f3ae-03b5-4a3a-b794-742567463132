<template>
  <section
    class="client-person-maintenance"
    v-if="props.clientPersonWithAuthDetails"
  >
    <div class="px-3 py-3">
      <div class="flex-row justify-space-between">
        <GTitle
          class="mb-2"
          title="Account Details"
          subtitle="Add or update this Client Contact's primary account details. This information determine's a users login access, roles within their organisation and more."
          :divider="false"
        />
        <GDialog
          v-if="confirmEmailUpdateLink"
          :width="'500px'"
          :title="'Request to update details - confirmation'"
          :confirmBtnText="'Confirm'"
          @closeDialog="confirmEmailUpdateLink = false"
          @confirm="sendUpdateClientPersonDetailsRequest"
        >
          <div class="pa-3">
            <div class="email-message">
              <p class="mb-3">
                Please confirm that you wish to email an 'Update Account
                Details' link to the following recipient:
              </p>

              <div class="mb-3">
                <p class="mb-1">
                  <b>Recipient:</b>
                  {{ props.clientPersonWithAuthDetails.clientPerson.firstName }}
                  {{ props.clientPersonWithAuthDetails.clientPerson.lastName }}
                </p>
                <p>
                  <b>Email Address:</b>
                  {{
                    props.clientPersonWithAuthDetails.clientPerson
                      .emailAddress[0]
                  }}
                </p>
              </div>
              <p>
                Please review the information above and ensure that you intend
                to send this email before proceeding. Your confirmation is
                appreciated.
              </p>
            </div>
          </div>
        </GDialog>
      </div>
      <GButton
        class="mb-2"
        v-if="
          sessionManager.getPortalType() === Portal.OPERATIONS &&
          !isNewClientPerson
        "
        rounded
        :disabled="isNewClientPerson || confirmEmailUpdateLink"
        @click="confirmEmailUpdateLink = true"
        :icon="'fas fa-envelope'"
        iconRight
        color="primary"
        depressed
        >Update Account Details</GButton
      >
      <hr class="divider" />

      <div style="position: relative">
        <GEmailValidator
          v-model="
            props.clientPersonWithAuthDetails.clientPerson.emailAddress[0]
          "
          :isClientPerson="true"
          @setIsLoading="setIsEmailLoading"
          :disabled="
            (!props.clientPersonWithAuthDetails.canEditUsername &&
              !isNewClientPerson) ||
            (isClientPortal && !isAccountManagementRole)
          "
          @setExistingClientPerson="setExistingClientPerson"
          :autofocus="true"
        />

        <div
          class="existing-client-person-select-text"
          v-if="existingClientPerson"
          @click="isExistingClientPersonDialog = true"
        >
          Did you mean {{ existingClientPerson.firstName }}
          {{ existingClientPerson.lastName }}?
        </div>
      </div>

      <GTextField
        v-model="props.clientPersonWithAuthDetails.clientPerson.firstName"
        :placeholder="'First Name'"
        :rules="[rules.required]"
        :disabled="isClientPortal && !isAccountManagementRole"
      ></GTextField>

      <GTextField
        v-model="props.clientPersonWithAuthDetails.clientPerson.lastName"
        :placeholder="'Last Name'"
        :disabled="isClientPortal && !isAccountManagementRole"
        :rules="[rules.required]"
      ></GTextField>

      <GMobileValidator
        :portal="Portal.OPERATIONS"
        v-model="
          props.clientPersonWithAuthDetails.clientPerson.contactMobileNumber
        "
        :isClientPerson="true"
        @setIsLoading="setIsMobileLoading"
        :disabled="
          (!props.clientPersonWithAuthDetails.canEditUsername &&
            !isNewClientPerson) ||
          (isClientPortal && !isAccountManagementRole)
        "
      />
      <GTextField
        v-model="
          props.clientPersonWithAuthDetails.clientPerson.contactLandlineNumber
        "
        :placeholder="'Landline Number'"
        :disabled="isClientPortal && !isAccountManagementRole"
        v-mask="'## #### ####'"
        :rules="[rules.numbers]"
      ></GTextField>
      <GSelect
        v-model="props.clientPersonWithAuthDetails.clientPerson.receivesEmails"
        :items="selectItems"
        :disabled="
          (isClientPortal && !isAccountManagementRole) ||
          !props.clientPersonWithAuthDetails.clientPerson.emailAddress[0]
        "
        :item-text="'key'"
        :clearable="false"
        :item-value="'value'"
        :placeholder="'Email Preference'"
        :label="'Email Preference'"
      />
      <GSelect
        v-if="hasDispatcherRole"
        v-model="isDefaultDispatcher"
        :items="[
          {
            key: 'This user is the default dispatcher for this client',
            value: true,
          },
          {
            key: 'This user is not the default dispatcher for this client',
            value: false,
          },
        ]"
        :item-text="'key'"
        :clearable="false"
        :item-value="'value'"
        :placeholder="'Default Dispatcher'"
        :label="'Default Dispatcher'"
        :disabled="sessionManager.getPortalType() !== Portal.OPERATIONS"
      />

      <GTable
        class="mt-4"
        :title="'Associated Roles'"
        subtitle="The user's current roles"
        :headers="headers"
        :items="
          isClientPortal && !isAccountManagementRole
            ? props.clientPersonWithAuthDetails.clientRoles.filter(
                (role) => role.status !== UserRoleStatus.LOCKED,
              )
            : props.clientPersonWithAuthDetails.clientRoles
        "
        :dataRequired="isNewClientPerson"
        :customErrorMessage="tableErrorMessage"
        :customValidator="hasRequiredDispatcherRole"
        :noDataMessage="'This user has no roles.'"
        :selectable="!isClientPortal || isAccountManagementRole"
        @selectItem="viewRole"
      >
        <GButton
          slot="action"
          rounded
          :disabled="isClientPortal && !isAccountManagementRole"
          @click="setRoleDialog(true)"
          :icon="'fal fa-plus'"
          iconRight
          color="primary"
        >
          Add Role</GButton
        >

        <template v-slot:items="row">
          <td>
            <span
              v-if="
                props.clientPersonWithAuthDetails &&
                row.item.roleId === 1 &&
                props.defaultDispatcherId ===
                  props.clientPersonWithAuthDetails.clientPerson._id
              "
              >Default</span
            >
            {{ row.item.roleName }}
          </td>
          <td>
            {{ row.item.status }}
          </td>

          <td v-if="sessionManager.getPortalType() === Portal.OPERATIONS">
            <div
              class="status-el"
              style="display: flex; justify-content: flex-end"
            >
              <v-tooltip left attach=".status-el">
                <template v-slot:activator="{ on }">
                  <v-btn
                    class="mr-0"
                    depressed
                    small
                    icon
                    v-on="on"
                    fab
                    :disabled="row.item.status === UserRoleStatus.PENDING"
                    color="primary"
                    @click.stop="setLockUnlockRequest(row.item)"
                    ><i
                      :class="
                        row.item.status === 'LOCKED'
                          ? 'fas fa-unlock-alt'
                          : 'fas fa-lock-alt'
                      "
                    ></i>
                  </v-btn>
                </template>
                {{
                  row.item.status === 'LOCKED'
                    ? 'Unlock this role'
                    : 'Lock this role'
                }}
              </v-tooltip>
            </div>
          </td>
        </template>
      </GTable>

      <GDialog
        ref="roleDialogRef"
        :width="'500px'"
        :title="'Role Administration'"
        :confirmBtnText="
          isViewingExistingRole && clientRoleStatus.roleId === 1
            ? 'Save'
            : 'Add Role'
        "
        :isDelete="isViewingExistingRole"
        :deleteBtnText="'Remove Role'"
        :confirmDisabled="
          isViewingExistingRole && clientRoleStatus.roleId !== 1
        "
        :isLoading="isLoadingRoleAddOrRemoveRequest"
        @deleteItem="removeRole"
        @closeDialog="setRoleDialog(false)"
        @confirm="addRoleToUser"
        v-if="clientRoleStatus"
      >
        <div class="pa-3">
          <GTitle
            title="Role Administration"
            subtitle="Please select the role you would like to apply to this user."
          />
          <div class="flex-row align-center">
            <GSelect
              v-model.number="clientRoleStatus.roleId"
              :items="clientPersonRoles"
              :item-value="'roleId'"
              :item-text="'name'"
              :placeholder="'Select a client contact role type'"
              :label="'Select a client contact role type'"
              :disabled="isViewingExistingRole"
              :customErrorMessage="roleErrorMessage"
              :disabledValues="disabledRoles()"
              :rules="[rules.required]"
            />
          </div>

          <GSwitch
            v-if="clientRoleStatus.roleId === 1"
            :disabled="false"
            class="pr-2"
            v-model="isDefaultDispatcher"
            :label="'This user is the default dispatcher'"
          />
        </div>
      </GDialog>

      <GDialog
        ref="roleDialogRef"
        :width="'500px'"
        :title="'Confirm Role Removal'"
        :confirmBtnText="'Confirm Removal'"
        :isDelete="false"
        :confirmDisabled="false"
        :isLoading="isLoadingRoleAddOrRemoveRequest"
        @closeDialog="confirmRoleRemovalDialog = false"
        @confirm="removeRoleConfirmed"
        v-if="confirmRoleRemovalDialog && roleRemovalConfirmationMessage"
      >
        <div class="pa-3">
          <p>
            Please verify the role to be removed and the associated user details
            below:
          </p>
          <ul>
            <li>
              Role:
              <strong>{{ roleRemovalConfirmationMessage.roleName }}</strong>
            </li>
            <li>
              User:
              <strong>{{ roleRemovalConfirmationMessage.accountName }}</strong>
            </li>
            <li>
              Email:
              <strong>
                {{ roleRemovalConfirmationMessage.accountEmailAddress }}</strong
              >
            </li>
          </ul>
          <p class="mt-3">
            Should the details be accurate, you may proceed with removing the
            role.
          </p>
        </div>
      </GDialog>

      <GDialog
        :width="'500px'"
        :title="'Role Status Management'"
        :confirmBtnText="
          'Confirm and ' +
          (currentRoleStatus.status === UserRoleStatus.LOCKED
            ? 'unlock'
            : 'lock')
        "
        :isDelete="false"
        :confirmDisabled="false"
        :isLoading="false"
        @closeDialog="closeRoleUpdateDialog"
        @confirm="sendUpdateRoleRequest"
        v-if="isRoleStatusUpdate && currentRoleStatus"
      >
        <div class="pa-3">
          <p>
            Please verify the associated user and role update details below:
          </p>
          <ul v-if="props.clientPersonWithAuthDetails">
            <li>
              User:
              <strong>{{
                props.clientPersonWithAuthDetails.clientPerson.firstName +
                ' ' +
                props.clientPersonWithAuthDetails.clientPerson.lastName
              }}</strong>
            </li>
            <li>
              Role:
              <strong>{{ currentRoleStatus.roleName }}</strong>
            </li>

            <li>
              Current Status:
              <strong>{{ currentRoleStatus.status }}</strong>
            </li>

            <li>
              Updated Status:
              <strong>{{
                currentRoleStatus.status === UserRoleStatus.LOCKED
                  ? 'ACTIVE'
                  : 'LOCKED'
              }}</strong>
            </li>
          </ul>
          <p class="mt-3">
            Should the details be accurate, you may proceed with
            <strong>{{
              currentRoleStatus.status === UserRoleStatus.LOCKED
                ? 'unlocking'
                : 'locking'
            }}</strong>
            this users {{ currentRoleStatus.roleName }} role.
          </p>
        </div>
      </GDialog>

      <GDialog
        :width="'500px'"
        :title="'Client Contact Account Association'"
        :confirmBtnText="'Confirm'"
        :isDelete="false"
        :confirmDisabled="false"
        :isLoading="loadingExistingClientPersonAssociation"
        @closeDialog="isExistingClientPersonDialog = false"
        @confirm="addExistingClientPersonToClient"
        v-if="isExistingClientPersonDialog && existingClientPerson"
      >
        <div class="pa-3">
          <!-- {{ existingClientPerson }} -->
          <p>
            The email address you searched for already exists within
            <strong>{{
              companyDetailsStore.companyDetails?.name ?? 'Unknown'
            }}</strong
            >. The account information related to this email address is below:
          </p>

          <p class="mt-3 mb-1">
            <strong> Account Information:</strong>
          </p>
          <ul>
            <li>
              Name:
              <strong>{{
                existingClientPerson.firstName +
                ' ' +
                existingClientPerson.lastName
              }}</strong>
            </li>

            <li>
              Email:
              <strong>{{ existingClientPerson.emailAddress[0] }}</strong>
            </li>
            <li>
              Mobile:
              <strong>{{
                formatPhoneNumber(existingClientPerson.contactMobileNumber)
              }}</strong>
            </li>
          </ul>

          <div v-if="!sessionManager.isClientPortal">
            <p class="mt-3 mb-1">
              <strong> Associated Clients:</strong>
            </p>

            <ul>
              <li
                v-for="(
                  client, index
                ) of existingClientPersonsAssociatedClients"
                :key="index"
              >
                <strong
                  >{{ client.clientName }} ({{ client.clientId }}) -
                  {{ client.division }}
                </strong>
              </li>
            </ul>
          </div>

          <hr class="divider mt-3" />

          <p class="mt-3">
            Instead of creating a new account, we can associate the existing
            contact highlighted above with this client. If you wish to proceed
            with adding the existing contact to this client you are agreeing
            that:
          </p>
          <ul>
            <li>
              <strong>{{
                existingClientPerson.firstName +
                ' ' +
                existingClientPerson.lastName
              }}</strong>
              with the email address of
              <strong>{{ existingClientPerson.emailAddress[0] }}</strong> will
              be associated with
              <strong>{{ props.clientName }} - {{ props.clientId }}</strong>
            </li>

            <li>
              <strong>{{ props.clientName }} </strong> will see
              <strong>{{
                existingClientPerson.firstName +
                ' ' +
                existingClientPerson.lastName
              }}</strong>
              as a client contact within the client portal.
            </li>
          </ul>

          <p class="mt-3">
            Would you like to proceed with associating this user with
            <strong>{{ props.clientName }} - {{ props.clientId }}</strong
            >?
          </p>
        </div>
      </GDialog>
    </div>
  </section>
</template>

<script setup lang="ts">
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { formatPhoneNumber } from '@/helpers/StringHelpers/StringHelpers';
import { hasAccountManagementRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import {
  validate,
  validationRules,
} from '@/helpers/ValidationHelpers/ValidationHelpers';
import CompanyRole from '@/interface-models/Admin/CompanyRole';
import { AssociatedClients } from '@/interface-models/Authentication/Token';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { Portal } from '@/interface-models/Generic/Portal';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { Validation } from '@/interface-models/Generic/Validation';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import {
  UserRole,
  UserRoleType,
  UserType,
} from '@/interface-models/Roles/UserRoles';
import {
  ClientPerson,
  ClientPersonUpdateDetailsResponse,
  ClientPersonWithAuthDetails,
  ClientRoleStatus,
  VerifyClientPersonWithEmailAddressExistsResponse,
} from '@/interface-models/User/ClientPerson';
import {
  LockUnlockRequest,
  UpdateRoleAccessRequest,
  UpdateRoleAccessResponse,
} from '@/interface-models/User/UpdateRoleAccess';
import { UserRoleStatus } from '@/interface-models/User/UserRoleStatus';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useRootStore } from '@/store/modules/RootStore';
import { useWebsocketStore } from '@/store/modules/WebsocketStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import { ComputedRef, Ref, computed, onMounted, ref, watch } from 'vue';

interface IProps {
  clientPersonWithAuthDetails: ClientPersonWithAuthDetails | null;
  client_id: string;
  clientId: string;
  clientName: string;
  defaultDispatcherId: string | null;
  dispatcherOnly?: boolean;
}
const props = withDefaults(defineProps<IProps>(), {
  clientPersonWithAuthDetails: null,
  dispatcherOnly: false,
});

const emit = defineEmits<{
  (event: 'setIsEmailVerificationLoading', payload: boolean): void;
  (event: 'setIsMobileVerificationLoading', payload: boolean): void;
  (event: 'setDefaultDispatcher', payload: boolean): void;
  (event: 'cancelMaintenance'): void;
  (event: 'addClientPersonDispatcherId', payload: string): void;
  (event: 'addClientPersonDispatcher', payload: ClientPerson): void;
  (event: 'updatedRoleAccess'): void;
}>();

const companyDetailsStore = useCompanyDetailsStore();

const isClientPortal = sessionManager.isClientPortal();
const isAccountManagementRole = computed(() => hasAccountManagementRole());

const roleDialogRef = ref(null);
const isViewingExistingRole: Ref<boolean> = ref(false);
const confirmEmailUpdateLink: Ref<boolean> = ref(false);
const confirmRoleRemovalDialog: Ref<boolean> = ref(false);
const isLoadingRoleAddOrRemoveRequest: Ref<boolean> = ref(false);
const isDefaultDispatcher: Ref<boolean> = ref(false);

const currentRoleStatus: Ref<ClientRoleStatus | null> = ref(null);
const isRoleStatusUpdate: Ref<boolean> = ref(false);
const isLoadingRoleUpdateRequest: Ref<boolean> = ref(false);
const isExistingClientPersonDialog: Ref<boolean> = ref(false);
// If the email validator returns that the email exists, it emits the existing client person and sets the result to existingClientPerson
const existingClientPerson: Ref<ClientPerson | null> = ref(null);
const existingClientPersonsAssociatedClients: Ref<AssociatedClients[]> = ref(
  [],
);
const loadingExistingClientPersonAssociation: Ref<boolean> = ref(false);

// Watches email for email preference
watch(
  () => props.clientPersonWithAuthDetails!.clientPerson.emailAddress[0],
  (newEmail) => {
    if (!newEmail) {
      props.clientPersonWithAuthDetails!.clientPerson.receivesEmails = false;
    }
  },
);

// Computed property for select email preference
const selectItems = computed(() => {
  return [
    { key: 'This User Receives Emails', value: true },
    { key: 'This User Does Not Receive Emails', value: false },
  ];
});

function setIsEmailLoading(isLoading: boolean) {
  emit('setIsEmailVerificationLoading', isLoading);
}

function setIsMobileLoading(isLoading: boolean) {
  emit('setIsMobileVerificationLoading', isLoading);
}

const headers: TableHeader[] = [
  {
    text: 'Role',
    align: 'left',
    value: 'roleName',
  },
  {
    text: 'status',
    align: 'left',
    value: 'status',
  },
  {
    text: 'Lock / Unlock',
    align: 'right',
    value: 'status',
    hidden: sessionManager.isClientPortal(),
  },
];

const clientRoleStatus: Ref<ClientRoleStatus | null> = ref(null);

onMounted(() => {
  setIsDefaultDispatcher();
});

function setIsDefaultDispatcher() {
  isDefaultDispatcher.value = !props.clientPersonWithAuthDetails
    ? false
    : props.defaultDispatcherId ===
      props.clientPersonWithAuthDetails.clientPerson._id;
}

// watch isDefaultDispatcher. on change emit back to parent. If user is currently looking at role management, we will send the defaultDispatcher add/remove request
watch(isDefaultDispatcher, (newVal: boolean | null) => {
  emit('setDefaultDispatcher', newVal ? true : false);
});

/**
 * Sets the dialog for unlocking and locking the role.
 */
function setLockUnlockRequest(clientRoleStatus: ClientRoleStatus) {
  isRoleStatusUpdate.value = true;
  currentRoleStatus.value = clientRoleStatus;
}

/**
 * Closes the role status maintenance dialog
 */
function closeRoleUpdateDialog() {
  isRoleStatusUpdate.value = false;
  currentRoleStatus.value = null;
}

/**
 * send update role request. Used for locking and unlocking roles.
 */
function sendUpdateRoleRequest(): void {
  if (
    !currentRoleStatus.value ||
    !isRoleStatusUpdate.value ||
    !props.clientPersonWithAuthDetails ||
    !props.clientPersonWithAuthDetails.clientPerson.authRefId
  ) {
    return;
  }

  const lockUnlockRequest: LockUnlockRequest = {
    company: sessionManager.getCompanyId(),
    authRefId: props.clientPersonWithAuthDetails.clientPerson.authRefId,
    roleRequests: [],
  };

  const roleRequest = {
    division: sessionManager.getDivisionId(),
    roleId: currentRoleStatus.value.roleId,
    clientId: props.client_id,
    locked:
      currentRoleStatus.value.status === UserRoleStatus.LOCKED ? false : true,
  };

  lockUnlockRequest.roleRequests.push(roleRequest);
  isLoadingRoleUpdateRequest.value = true;
  useWebsocketStore().sendWebsocketRequest(
    new WebSocketRequest(
      '/authUserDetails/lockUnlockRoles',
      lockUnlockRequest,
      true,
    ),
  );

  Mitt.on('lockUnlockRolesResponse', (response: LockUnlockRequest) => {
    if (!lockUnlockRequest) {
      showNotification('Something went wrong.', {
        title: 'Role Status Management',
        type: HealthLevel.ERROR,
      });
    } else {
      showNotification('The users Role was successfully updated.', {
        title: 'Role Status Management',
        type: HealthLevel.INFO,
      });

      if (
        props.clientPersonWithAuthDetails &&
        props.clientPersonWithAuthDetails.clientPerson.authRefId ===
          response.authRefId
      ) {
        for (const roleUpdate of response.roleRequests) {
          const roleToUpdate: ClientRoleStatus | undefined =
            props.clientPersonWithAuthDetails.clientRoles.find(
              (x: ClientRoleStatus) => x.roleId === roleUpdate.roleId,
            );

          if (roleToUpdate) {
            roleToUpdate.status = roleUpdate.locked
              ? UserRoleStatus.LOCKED
              : UserRoleStatus.ACTIVE;
          }
        }
      }

      closeRoleUpdateDialog();
    }
    Mitt.off('lockUnlockRolesResponse');
  });
}

/**
 * Sets or resets the role dialog status based on the provided boolean value.
 * When opened, it initializes the role status with the first role in the client person roles list.
 * When closed, it resets the client role status to null.
 * @param {boolean} isOpen - Determines whether to open or close the role dialog.
 */
function setRoleDialog(isOpen: boolean) {
  if (!isOpen) {
    // reset modeled role information
    clientRoleStatus.value = null;
    return;
  } else {
    // set that the user is not viewing an existing role.
    isViewingExistingRole.value = false;
  }
  // set the default selected role. In this case we want to set a role that the user doesn't currently have.
  let roleId: number = clientPersonRoles.value[0].roleId;
  let roleName: string = clientPersonRoles.value[0].name;
  if (props.clientPersonWithAuthDetails) {
    for (const role of clientPersonRoles.value) {
      const foundRole: ClientRoleStatus | undefined =
        props.clientPersonWithAuthDetails.clientRoles.find(
          (x: ClientRoleStatus) => x.roleId === role.roleId,
        );
      if (!foundRole) {
        roleId = role.roleId;
        roleName = role.name;
      }
    }
  }
  clientRoleStatus.value = {
    roleName,
    roleId,
    status: UserRoleStatus.PENDING,
  };
}

/**
 * Checks whether this client person has a dispatcher role.
 * @returns {boolean}
 */
const hasDispatcherRole: ComputedRef<boolean> = computed(() => {
  if (!props.clientPersonWithAuthDetails) {
    return false;
  }
  const dispatcherRoleFound: ClientRoleStatus | undefined =
    props.clientPersonWithAuthDetails.clientRoles.find(
      (x: ClientRoleStatus) => x.roleId === 1,
    );
  return dispatcherRoleFound ? true : false;
});
/**
 * Removes a role from the client person's roles. Checks if the client person details are available
 * and if a role is currently selected. Finds the role to be removed by its ID and removes it from the roles list.
 */
function removeRole(): void {
  if (!props.clientPersonWithAuthDetails || !clientRoleStatus.value) {
    return;
  }

  if (isNewClientPerson.value) {
    const index = props.clientPersonWithAuthDetails.clientRoles.findIndex(
      (x: ClientRoleStatus) => x.roleId === clientRoleStatus.value?.roleId,
    );
    if (index < 0) {
      return;
    }
    props.clientPersonWithAuthDetails.clientRoles.splice(index, 1);
    setRoleDialog(false);
  } else {
    confirmRoleRemovalDialog.value = true;
  }
}

/** Sends a WebSocket request to update client person details via email. On receiving a response, it shows a
 * notification based on the success or failure of the update. It listens for a specific response
 * event and removes the listener after handling the response.
 */
function sendUpdateClientPersonDetailsRequest(): void {
  try {
    if (!props.clientPersonWithAuthDetails) {
      return;
    }
    useWebsocketStore().sendWebsocketRequest(
      new WebSocketRequest(
        '/clientDetails/clientPersonDispatchers/updateDetails',
        props.clientPersonWithAuthDetails.clientPerson._id,
        false,
      ),
    );
    Mitt.on(
      'sentClientPersonUpdateDetailsRequestResponse',
      (sentUpdateEmail: ClientPersonUpdateDetailsResponse) => {
        if (sentUpdateEmail.isSuccess) {
          showNotification('Update email successfully sent', {
            title: 'Client Contact - Update Details',
            type: HealthLevel.SUCCESS,
          });
          confirmEmailUpdateLink.value = false;
        } else {
          showNotification(
            'Something went wrong when attempting to send the update details email.',
            {
              title: 'Client Contact - Update Details',
              type: HealthLevel.ERROR,
            },
          );
        }
        Mitt.off('sentClientPersonUpdateDetailsRequestResponse');
      },
    );
  } catch (e) {
    console.error(e);
  }
}

/**
 * When the user has confirmed that they wish to remove a role from an existing company user.
 */
function removeRoleConfirmed() {
  if (
    !props.clientPersonWithAuthDetails ||
    !clientRoleStatus.value ||
    !props.clientPersonWithAuthDetails.clientPerson._id ||
    !props.clientPersonWithAuthDetails.clientPerson.authRefId ||
    !sessionManager.getCompanyId() ||
    !sessionManager.getDivisionId()
  ) {
    showNotification('Something went wrong.', {
      title: 'Role Maintenance',
      type: HealthLevel.ERROR,
    });
    return;
  }

  updateRoleAccessRequest({
    targetRole: clientRoleStatus.value,
    clientPerson_id: props.clientPersonWithAuthDetails.clientPerson._id!,
    authRefId: props.clientPersonWithAuthDetails.clientPerson.authRefId!,
    client_id: props.client_id,
    company: sessionManager.getCompanyId(),
    division: sessionManager.getDivisionId(),
    isAddRole: false,
    isDefaultDispatcher: isDefaultDispatcher.value,
    isDefaultDispatcherRequestOnly: false,
  });
}

/**
 * Sets the client role status to view an existing role. Marks the status as viewing an existing role
 * and sets the client role status to the provided role.
 * @param {ClientRoleStatus} role - The role to be viewed.
 */
function viewRole(role: ClientRoleStatus) {
  isViewingExistingRole.value = true;
  clientRoleStatus.value = role;
}

/**
 * Computes and returns a list of user roles filtered by client user type.
 * We also append a default dispatcher role to this list so that the default dispatcher
 * role can be added via backend logic
 * @returns {ComputedRef<UserRole[]>} A computed reference to an array of user roles.
 */
const clientPersonRoles = computed(() => {
  // If dispatcherOnly is true, then only allow dispatcher role to be added
  if (props.dispatcherOnly) {
    return useRootStore().roleList.filter(
      (role: UserRole) =>
        role.userType === UserType.CLIENT &&
        role.roleType === UserRoleType.ROLE_DISPATCHER,
    );
  } else {
    // Filter roles based on userType and exclude ROLE_SITE_CONTACT
    const filteredRoles: UserRole[] = useRootStore().roleList.filter(
      (role: UserRole) =>
        role.userType === UserType.CLIENT &&
        role.roleType !== UserRoleType.ROLE_SITE_CONTACT,
    );
    return filteredRoles;
  }
});

/**
 * Adds a role to the user. Validates the role dialog input and if valid, finds the user role by ID
 * and adds it to the client person's roles. Resets the client role status after adding the role.
 */
function addRoleToUser() {
  if (
    !clientRoleStatus.value ||
    !props.clientPersonWithAuthDetails ||
    !validate(roleDialogRef.value)
  ) {
    return;
  }

  // Because default dispatcher is not actually a role, we handle the request to update the default dispatcher a little different from other roles. When the user is viewing an existing dispatcher role we will make sure that the request has empty roles added and roles removed.
  if (isViewingExistingRole.value && clientRoleStatus.value.roleId === 1) {
    updateRoleAccessRequest({
      targetRole: clientRoleStatus.value,
      clientPerson_id: props.clientPersonWithAuthDetails.clientPerson._id!,
      authRefId: props.clientPersonWithAuthDetails.clientPerson.authRefId!,
      client_id: props.client_id,
      company: sessionManager.getCompanyId(),
      division: sessionManager.getDivisionId(),
      isAddRole: false,
      isDefaultDispatcher: isDefaultDispatcher.value,
      isDefaultDispatcherRequestOnly: true,
    });
    return;
  }

  if (isNewClientPerson.value) {
    const userRole: UserRole | undefined = clientPersonRoles.value.find(
      (x: UserRole) => x.roleId === clientRoleStatus.value?.roleId,
    );
    if (!userRole) {
      return;
    }
    clientRoleStatus.value.roleName = userRole.name;
    props.clientPersonWithAuthDetails.clientRoles.push(clientRoleStatus.value);
    clientRoleStatus.value = null;
  } else {
    if (
      !props.clientPersonWithAuthDetails.clientPerson._id ||
      !props.clientPersonWithAuthDetails.clientPerson.authRefId ||
      !sessionManager.getCompanyId() ||
      !sessionManager.getDivisionId()
    ) {
      showNotification('Something went wrong.', {
        title: 'Role Maintenance',
        type: HealthLevel.ERROR,
      });
      return;
    }
    updateRoleAccessRequest({
      targetRole: clientRoleStatus.value,
      clientPerson_id: props.clientPersonWithAuthDetails.clientPerson._id!,
      authRefId: props.clientPersonWithAuthDetails.clientPerson.authRefId!,
      client_id: props.client_id,
      company: sessionManager.getCompanyId(),
      division: sessionManager.getDivisionId(),
      isAddRole: true,
      isDefaultDispatcher: isDefaultDispatcher.value,
      isDefaultDispatcherRequestOnly: false,
    });
  }
}

/**
 * Update request for adding or removing a role for a specified user.
 * This function constructs the role addition or removal request based on the provided parameters
 * and sends it through a WebSocket request. It listens for a response on the operation and handles
 * loading state accordingly.
 *
 * @param {ClientRoleStatus} targetRole - The target role's status and ID.
 * @param {string} clientPerson_id - The ID of the client person to which the role will be added or removed.
 * @param {string} authRefId - The authentication reference ID.
 * @param {string} client_id - The client's unique identifier.
 * @param {string} company - The name of the company.
 * @param {string} division - The division within the company.
 * @param {boolean} isAddRole - Flag indicating whether the role is being added (true) or removed (false).
 * @param {boolean} isDefaultDispatcher - flag indicating whether the role is default dispatcher or not. Utilised with dispatcher role only.
 * @param {boolean} isDefaultDispatcherRequestOnly - flag indicating whether the update request is for administration of default dispatcher role.
 */
function updateRoleAccessRequest({
  targetRole,
  clientPerson_id,
  authRefId,
  client_id,
  company,
  division,
  isAddRole,
  isDefaultDispatcher,
  isDefaultDispatcherRequestOnly,
}: {
  targetRole: ClientRoleStatus;
  clientPerson_id: string;
  authRefId: string;
  client_id: string;
  company: string;
  division: string;
  isAddRole: boolean;
  isDefaultDispatcher: boolean;
  isDefaultDispatcherRequestOnly: boolean;
}) {
  const roleToBeAddedOrRemoved: CompanyRole[] = [
    {
      roleId: targetRole.roleId,
      userId: clientPerson_id,
      referenceId: client_id,
      status: isAddRole ? UserRoleStatus.ACTIVE : targetRole.status,
    },
  ];
  const updateRoleAccessRequest: UpdateRoleAccessRequest = {
    authRefId: authRefId,
    company,
    division,
    addedRoles:
      !isDefaultDispatcherRequestOnly && isAddRole
        ? roleToBeAddedOrRemoved
        : [],
    removedRoles:
      !isDefaultDispatcherRequestOnly && !isAddRole
        ? roleToBeAddedOrRemoved
        : [],
    clientId: client_id,
    defaultDispatcher: isDefaultDispatcher,
  };
  isLoadingRoleAddOrRemoveRequest.value = true;
  useWebsocketStore().sendWebsocketRequest(
    new WebSocketRequest(
      sessionManager.isClientPortal()
        ? `/user/${sessionManager.getUserName()}/authUserDetails/updateRoleAccess`
        : '/authUserDetails/updateRoleAccess',
      updateRoleAccessRequest,
      true,
    ),
  );
  const responseEvent = sessionManager.isClientPortal()
    ? 'updatedRoleAccessFromClientPortalResponse'
    : 'updatedRoleAccessResponse';
  Mitt.on(responseEvent, (response: UpdateRoleAccessResponse) => {
    // check if division response is for this client person
    if (
      props.clientPersonWithAuthDetails &&
      props.clientPersonWithAuthDetails.clientPerson.authRefId ===
        response.authRefId
    ) {
      if (!response) {
        showNotification('Something went wrong.', {
          title: 'Role Maintenance',
          type: HealthLevel.ERROR,
        });
      } else if (response.updateSuccessful) {
        for (const newRole of response.addedRoles) {
          const foundRole = useRootStore().roleList.find(
            (x: UserRole) => x.roleId === newRole.roleId,
          );
          const companyRoleStatus: ClientRoleStatus = {
            roleName: foundRole?.name ?? '-',
            roleId: newRole.roleId,
            status: newRole.status,
          };
          props.clientPersonWithAuthDetails.clientRoles.push(companyRoleStatus);
        }

        for (const removedRole of response.removedRoles) {
          const indexOfRole: number =
            props.clientPersonWithAuthDetails.clientRoles.findIndex(
              (x: ClientRoleStatus) => x.roleId === removedRole.roleId,
            );
          if (indexOfRole >= 0) {
            props.clientPersonWithAuthDetails.clientRoles.splice(
              indexOfRole,
              1,
            );
          }
        }

        const clientPersonName: string =
          props.clientPersonWithAuthDetails.clientPerson.firstName +
          ' ' +
          props.clientPersonWithAuthDetails.clientPerson.lastName;

        const notificationMessage = !isDefaultDispatcherRequestOnly
          ? 'Role Updated Successfully.'
          : clientPersonName +
            ' successfully ' +
            (response.defaultDispatcher ? 'added' : 'removed') +
            ' as default dispatcher';

        showNotification(notificationMessage, {
          title: 'Role Maintenance',
          type: HealthLevel.INFO,
        });
      }

      isLoadingRoleAddOrRemoveRequest.value = false;
      confirmRoleRemovalDialog.value = false;
      clientRoleStatus.value = null;

      emit('updatedRoleAccess');
    }
    Mitt.off('updatedRoleAccessResponse');
  });
}

/**
 * Returns true if a dispatcher role is NOT required, or if a dispatcher role is
 * required and the user has a dispatcher role.
 */
function hasRequiredDispatcherRole(): boolean {
  return (
    !props.dispatcherOnly || (props.dispatcherOnly && hasDispatcherRole.value)
  );
}

/**
 * Used in the template to display a message in the GTable component when the
 * user has no roles. If a dispatcher role is required, then the message will
 * indicate that a dispatcher role is required. If a dispatcher role is not
 * required, then the message will indicate that the user requires at least one
 * role.
 */
const tableErrorMessage: ComputedRef<string> = computed(() => {
  if (!hasRequiredDispatcherRole()) {
    return 'You must add a role of Dispatcher for use in a job.';
  }
  return 'User requires at least one role.';
});

/**
 * Computes an error message for the role based on the current client role status and existing roles.
 * Returns an error message if the user already has the selected role.
 * @returns {ComputedRef<string>} A computed reference to a string containing the error message.
 */
const roleErrorMessage: ComputedRef<string> = computed(() => {
  if (
    !clientRoleStatus.value ||
    !props.clientPersonWithAuthDetails ||
    props.clientPersonWithAuthDetails.clientRoles.length === 0 ||
    isViewingExistingRole.value
  ) {
    return '';
  }
  return props.clientPersonWithAuthDetails.clientRoles.find(
    (x: ClientRoleStatus) => x.roleId === clientRoleStatus.value?.roleId,
  ) !== undefined
    ? 'User already has this role.'
    : '';
});
/**
 * Computes the validation rules from the store.
 * @returns {ComputedRef<Validation>} A computed reference to the validation rules.
 */
const rules: ComputedRef<Validation> = computed(() => {
  return validationRules;
});

/**
 * Computes a boolean indicating whether the client person is new, based on the existence of an ID.
 * @returns {ComputedRef<boolean>} A computed reference to a boolean indicating if the client person is new.
 */
const isNewClientPerson: ComputedRef<boolean> = computed(() => {
  if (!props.clientPersonWithAuthDetails) {
    return false;
  }
  return !props.clientPersonWithAuthDetails.clientPerson._id;
});

/**
 * Interface representing the confirmation details for a role removal operation.
 * @interface
 * @property {string} accountName - The name of the account associated with the role being removed.
 * @property {string} accountEmailAddress - The email address of the account associated with the role being removed.
 * @property {string} roleName - The name of the role to be removed.
 * @property {string} divisionName - The name of the division from which the role will be removed.
 */
interface RoleRemovalConfirmation {
  accountName: string;
  accountEmailAddress: string;
  roleName: string;
}

/**
 * A computed ref that provides the details required for confirming the removal of a role from a user's account.
 * It dynamically constructs an object containing the user's account name, email address, the role name to be removed.
 * @type {ComputedRef<RoleRemovalConfirmation>} @returns {RoleRemovalConfirmation | null}.
 * The confirmation message details, including account name, account email address, and
 * role name. if the prerequisites are met. Returns null
 * if the necessary details are not available. */
const roleRemovalConfirmationMessage: ComputedRef<RoleRemovalConfirmation | null> =
  computed(() => {
    if (!props.clientPersonWithAuthDetails || !clientRoleStatus.value) {
      return null;
    }
    const roleId: number = clientRoleStatus.value.roleId;
    const userRole: UserRole | undefined = clientPersonRoles.value.find(
      (x: UserRole) => x.roleId === roleId,
    );
    const roleName: string = userRole ? userRole.name : '';
    const accountName: string =
      props.clientPersonWithAuthDetails.clientPerson.firstName +
      ' ' +
      props.clientPersonWithAuthDetails.clientPerson.lastName;
    const accountEmailAddress: string =
      props.clientPersonWithAuthDetails.clientPerson.emailAddress[0];
    return {
      accountName,
      accountEmailAddress,
      roleName,
    };
  });

/**
 * Called from emit event from the GEmailValidator component. Sets found client person, their associated clients and the "did you mean" text.
 * @param {string} verifiedEmailAddressExists - The verified email that exists object
 * @returns {void}
 */
function setExistingClientPerson(
  verifiedEmailAddressExists: VerifyClientPersonWithEmailAddressExistsResponse,
): void {
  existingClientPerson.value = verifiedEmailAddressExists.existingClientPerson;
  existingClientPersonsAssociatedClients.value =
    verifiedEmailAddressExists.associatedClients;
}

/**
 * Sends a request to add an existing client person to a clientDetails.clientPersonDispatchers array. Listens for the response and shows a notification based on the response. Null response here indicates that something went wrong.
 * @returns {void}
 */
function addExistingClientPersonToClient(): void {
  if (!existingClientPerson.value || !existingClientPerson.value._id) {
    return;
  }

  // Utilise CompanyRole to construct required referencing. Because no role is getting added here we will utilize -1
  const companyRole: CompanyRole = {
    roleId: -1,
    referenceId: props.client_id,
    userId: existingClientPerson.value._id,
    status: UserRoleStatus.LOCKED,
  };
  loadingExistingClientPersonAssociation.value = true;

  useWebsocketStore().sendWebsocketRequest(
    new WebSocketRequest(
      sessionManager.isClientPortal()
        ? `client/${sessionManager.getClientId()}/user/${sessionManager.getUserName()}/clientDetails/addClientPersonIdToClientDetails`
        : '/clientDetails/addClientPersonIdToClientDetails',
      companyRole,
      true,
    ),
  );
  Mitt.on(
    'clientPersonIdAddedToClientDetails',
    (companyRole: CompanyRole | null) => {
      if (!companyRole) {
        showNotification('Something went wrong.', {
          title: 'Client Contact Account Association',
          type: HealthLevel.ERROR,
        });
        existingClientPerson.value = null;
        existingClientPersonsAssociatedClients.value = [];
      } else {
        if (!existingClientPerson.value) {
          logConsoleError(
            'Existing client person is null when trying to add to client.',
          );
          return;
        }
        showNotification(
          'Client contact successfully associated to this client.',
          {
            title: 'Client Contact Account Association',
            type: HealthLevel.INFO,
          },
        );
        emit('addClientPersonDispatcher', existingClientPerson.value);
        emit('addClientPersonDispatcherId', companyRole.userId);
        existingClientPerson.value = null;
        existingClientPersonsAssociatedClients.value = [];
        loadingExistingClientPersonAssociation.value = false;
      }
      Mitt.off('clientPersonIdAddedToClientDetails');
    },
  );
}

/**
 * Retrieves a list of disabled role IDs. We require this so the role Ids are disabled for
 * selection if they already exist on the user in role administration.
 *
 * @returns {number[]} An array of role IDs that are disabled for selection.
 */
function disabledRoles(): number[] {
  if (!props.clientPersonWithAuthDetails) {
    return [];
  }
  return props.clientPersonWithAuthDetails.clientRoles.map(
    (x: ClientRoleStatus) => x.roleId,
  );
}
</script>
<style scoped lang="scss">
p {
  color: var(--text-color);
}

.existing-client-person-select-text {
  position: absolute;
  bottom: 0;
  right: 0;
  color: var(--highlight);
  font-size: $font-size-12;
  cursor: pointer;
}
</style>
