<template>
  <div class="related-contacts-management-container">
    <div class="create-new-btn-container">
      <v-btn depressed color="primary" @click="dialogIsActive = true">
        <v-icon size="12" class="mr-2">fa-user-plus</v-icon>
        Create New Contact
      </v-btn>
    </div>
    <ClientDetailsClientContactsTable
      :client_id="props.client_id"
      :clientPersonIds="props.clientPersonIds"
      :defaultDispatcherId="props.defaultDispatcherId"
      :clientId="props.clientId"
      :clientsCommonAddresses="clientsCommonAddresses"
      @selectUser="viewClientPerson"
      @viewSiteContact="viewRelatedContact"
    />
    <CreateClientContactDialog
      :client_id="client_id"
      :clientId="clientId"
      :clientName="clientName"
      :clientPersonIds="clientPersonIds"
      :defaultDispatcherId="defaultDispatcherId"
      :dialogIsActive="dialogIsActive"
      @dialogIsActive="dialogIsActive = $event"
    />
    <GDialog
      v-if="clientPersonWithAuthDetails"
      :width="'720px'"
      :title="'Client Contact Details'"
      :confirmBtnText="'Save'"
      :confirmDisabled="isClientPortal && !isAccount"
      :isLoading="
        isMobileVerificationLoading ||
        isEmailVerificationLoading ||
        isSaveNewClientPersonRequest
      "
      :cancelDisabled="isFullSaveRequired"
      @closeDialog="cancelMaintenance"
      @confirm="saveClientPerson"
    >
      <ClientPersonMaintenance
        ref="clientPersonMaintenanceRef"
        v-if="clientPersonWithAuthDetails"
        :client_id="props.client_id"
        :clientId="props.clientId"
        :clientName="props.clientName"
        :clientPersonWithAuthDetails="clientPersonWithAuthDetails"
        :defaultDispatcherId="defaultDispatcherId"
        :clientPersonIds="clientPersonIds"
        @cancelMaintenance="cancelMaintenance"
        @setIsEmailVerificationLoading="setIsEmailVerificationLoading"
        @setIsMobileVerificationLoading="setIsMobileVerificationLoading"
        @setDefaultDispatcher="setDefaultDispatcher"
        @addClientPersonDispatcherId="addClientPersonDispatcherId"
        @updatedRoleAccess="isFullSaveRequired = true"
      >
      </ClientPersonMaintenance>
    </GDialog>
    <GDialog
      v-if="clientRelatedContact"
      ref="dialogRef"
      :width="'720px'"
      :title="'Site Contact Maintenance'"
      :confirmBtnText="'Save'"
      :confirmDisabled="false"
      @closeDialog="cancelDialog"
      @confirm="saveClientRelatedContact"
      :isLoading="isSaveRelatedContactRequest"
    >
      <ClientDetailsRelatedContactMaintenance
        v-if="clientRelatedContact"
        :clientRelatedContact="clientRelatedContact"
        :relatedContactsAssociatedCommonAddressIds.sync="
          relatedContactsAssociatedCommonAddressIds
        "
        :relatedContactsDefaultCommonAddressIds.sync="
          relatedContactsDefaultCommonAddressIds
        "
        @cancelMaintenance="cancelDialog"
        :clientsCommonAddresses="clientsCommonAddresses"
      />
    </GDialog>
  </div>
</template>

<script setup lang="ts">
import ClientPersonMaintenance from '@/components/admin/ClientDetails/components/client_details_contacts/client_details_client_person_maintenance.vue';
import ClientDetailsClientContactsTable from '@/components/admin/ClientDetails/components/client_details_contacts/client_details_contacts_table.vue';
import ClientDetailsRelatedContactMaintenance from '@/components/admin/ClientDetails/components/client_details_contacts/client_details_related_contact_maintenance.vue';
import CreateClientContactDialog from '@/components/admin/ClientDetails/components/client_details_contacts/create_client_contact_dialog.vue';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAccountManagementRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validate } from '@/helpers/ValidationHelpers/ValidationHelpers';
import {
  AddressContactType,
  type ClientCommonAddress,
  type ClientCommonAddressContact,
} from '@/interface-models/Client/ClientCommonAddress';
import {
  ClientCommonAddressRelatedContactSave,
  saveCommonAddressAndRelatedContact,
} from '@/interface-models/Client/ClientCommonAddressRelatedContactSave';
import { ClientRelatedContact } from '@/interface-models/Client/ClientRelatedContact';
import {
  EditUsernameForUserRequest,
  EditUsernameForUserResponse,
  RoleUserType,
} from '@/interface-models/Email/EmailAlertRecipients/EditUsernameForUser.ts';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import {
  ClientPersonWithAuthDetails,
  ClientRoleStatus,
  SaveNewClientPersonRequest,
  SaveNewClientPersonResponse,
} from '@/interface-models/User/ClientPerson';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useUserManagementStore } from '@/store/modules/UserManagementStore';
import { useWebsocketStore } from '@/store/modules/WebsocketStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import { computed, ComputedRef, onMounted, onUnmounted, ref, Ref } from 'vue';

interface IProps {
  clientId: string;
  clientName: string;
  client_id: string;
  clientPersonIds: string[];
  defaultDispatcherId?: string | null;
}
const isEmailVerificationLoading: Ref<boolean> = ref(false);
const isMobileVerificationLoading: Ref<boolean> = ref(false);
const isSaveNewClientPersonRequest: Ref<boolean> = ref(false);
const isDefaultDispatcher: Ref<boolean> = ref(false);
const initialEmail = ref<string | null>(null);
const initialMobile = ref<string | null>(null);

const props = withDefaults(defineProps<IProps>(), {
  defaultDispatcherId: null,
  clientId: '',
  client_id: '',
});

const dialogIsActive: Ref<boolean> = ref(false);
const emit = defineEmits(['addClientPersonDispatcherId']);

const isClientPortal = sessionManager.isClientPortal();
const isAccount = computed(() => hasAccountManagementRole());

const userManagementStore = useUserManagementStore();
const clientPersonMaintenanceRef = ref(null);
const clientPersonWithAuthDetails: Ref<ClientPersonWithAuthDetails | null> =
  ref(null);
const clientsCommonAddresses: Ref<ClientCommonAddress[] | null> = ref(null);
const clientRelatedContact: Ref<ClientRelatedContact | null> = ref(null);

const relatedContactsAssociatedCommonAddressIds: Ref<string[]> = ref([]);
const relatedContactsDefaultCommonAddressIds: Ref<string[]> = ref([]);

const isFullSaveRequired: Ref<boolean> = ref(false);

const isSaveRelatedContactRequest: Ref<boolean> = ref(false);
const dialogRef = ref(null);

/**
 * Set the default dispatcher boolean. Emitted from child.
 */
function setDefaultDispatcher(value: boolean): void {
  isDefaultDispatcher.value = value;
}

/**
 * Views the details of a selected client person.
 * Sets the `clientPersonWithAuthDetails` ref to a deep copy of the selected user.
 *
 * @param {ClientPersonWithAuthDetails} selectedUser - The client person selected for viewing.
 */
function viewClientPerson(selectedUser: ClientPersonWithAuthDetails): void {
  selectedUser.clientPerson.contactMobileNumber =
    selectedUser.clientPerson.contactMobileNumber.replaceAll(/\s/g, '');
  clientPersonWithAuthDetails.value = JSON.parse(JSON.stringify(selectedUser));
  setInitialUsernameValues(clientPersonWithAuthDetails.value);
}

/**
 * Sets the loading state for email verification.
 *
 * @param {boolean} isLoading - True if email verification is in progress, false otherwise.
 */
function setIsEmailVerificationLoading(isLoading: boolean): void {
  isEmailVerificationLoading.value = isLoading;
}

/**
 * Sets the loading state for mobile verification.
 *
 * @param {boolean} isLoading - True if mobile verification is in progress, false otherwise.
 */
function setIsMobileVerificationLoading(isLoading: boolean): void {
  isMobileVerificationLoading.value = isLoading;
}

/**
 * Computed ref that determines if the current client person is new.
 *
 * @returns {ComputedRef<boolean>} True if the client person is new (without an ID), false otherwise.
 */
const isNewClientPerson: ComputedRef<boolean> = computed(() => {
  if (!clientPersonWithAuthDetails.value) {
    return false;
  }
  return !clientPersonWithAuthDetails.value.clientPerson._id;
});

/**
 * Cancels the current client person maintenance.
 * Resets the `clientPersonWithAuthDetails` ref to null.
 */
function cancelMaintenance(): void {
  clientPersonWithAuthDetails.value = null;
  initialEmail.value = null;
  initialMobile.value = null;
  isSaveNewClientPersonRequest.value = false;
  isFullSaveRequired.value = false;
}

function cancelDialog(): void {
  clientRelatedContact.value = null;
  relatedContactsAssociatedCommonAddressIds.value = [];
  relatedContactsDefaultCommonAddressIds.value = [];
}

/**
 * Saves the client person details.
 * Validates the input and sends a WebSocket request to save the new client person.
 * Handles the response by adding the new client person to the user management store.
 */
async function saveClientPerson() {
  // close dialog of not new client person and clientPersonWithAuthDetails has no roles
  if (
    isNewClientPerson.value &&
    Array.isArray(clientPersonWithAuthDetails.value?.clientRoles) &&
    clientPersonWithAuthDetails.value.clientRoles.length === 0
  ) {
    if (!validate(clientPersonMaintenanceRef.value)) {
      return;
    }
  }
  if (!clientPersonWithAuthDetails.value) {
    return;
  }

  isSaveNewClientPersonRequest.value = true;
  const rolesToAdd: number[] = isNewClientPerson.value
    ? [
        ...new Set(
          clientPersonWithAuthDetails.value.clientRoles.map(
            (x: ClientRoleStatus) => x.roleId,
          ),
        ),
      ]
    : [];

  // If the email or mobile number has changed, send an edit username request
  // to update the user's email and mobile number.
  const isEmailChanged =
    !!initialEmail.value &&
    initialEmail.value !==
      clientPersonWithAuthDetails.value.clientPerson.emailAddress[0];
  const isMobileChanged =
    !!initialMobile.value &&
    initialMobile.value !==
      clientPersonWithAuthDetails.value.clientPerson.contactMobileNumber;

  if (isEmailChanged || isMobileChanged) {
    const success = await sendEditUsernameRequest();
    // If the request failed, show an error notification and revert the email
    // and mobile
    if (!success) {
      showNotification(
        'Something went wrong when attempting to save the new client contact.',
        { title: 'Client Contact Maintenance', type: HealthLevel.ERROR },
      );
      clientPersonWithAuthDetails.value.clientPerson.emailAddress[0] =
        initialEmail.value ?? '';
      clientPersonWithAuthDetails.value.clientPerson.contactMobileNumber =
        initialMobile.value ?? '';
      cancelMaintenance();
      return;
    }
  }
  const saveNewClientPersonRequest: SaveNewClientPersonRequest = {
    clientPerson: clientPersonWithAuthDetails.value.clientPerson,
    clientId: props.client_id,
    rolesToAdd,
    defaultDispatcher: isDefaultDispatcher.value,
  };
  useWebsocketStore().sendWebsocketRequest(
    new WebSocketRequest(
      sessionManager.isClientPortal()
        ? `/user/${sessionManager.getUserName()}/clientDetails/clientPersonDispatchers/saveNewClientPerson`
        : '/clientDetails/clientPersonDispatchers/saveNewClientPerson',
      saveNewClientPersonRequest,
      true,
    ),
  );

  cancelMaintenance();
}
/**
 * Event emitted back from maintenance component when adding an existing client person to this client.
 * @param {string} clientPerson_id - The client person id that will be added to this clients dispatchers
 * array.
 * @returns {void}
 */
function addClientPersonDispatcherId(clientPerson_id: string): void {
  emit('addClientPersonDispatcherId', clientPerson_id);
  Mitt.on(
    'clientPersonWithAuthDetailsList',
    (clientPersonWithAuthDetailsList: ClientPersonWithAuthDetails[]) => {
      const foundPerson = clientPersonWithAuthDetailsList.find(
        (x: ClientPersonWithAuthDetails) =>
          x.clientPerson._id === clientPerson_id,
      );
      if (foundPerson) {
        clientPersonWithAuthDetails.value = null;
        clientPersonWithAuthDetails.value = JSON.parse(
          JSON.stringify(foundPerson),
        );
        setInitialUsernameValues(clientPersonWithAuthDetails.value);
      }
      Mitt.off('clientPersonWithAuthDetailsList');
    },
  );
}

/**
 * Send can editUserName request. Used for checking if user email is changed to resend invite.
 */
async function sendEditUsernameRequest(): Promise<boolean> {
  if (!clientPersonWithAuthDetails.value?.clientPerson.authRefId) {
    return false;
  }
  const payload: EditUsernameForUserRequest = {
    authRefId: clientPersonWithAuthDetails.value.clientPerson.authRefId,
    newEmailAddress:
      clientPersonWithAuthDetails.value.clientPerson.emailAddress[0],
    newContactNumber:
      clientPersonWithAuthDetails.value.clientPerson.contactMobileNumber,
    name: clientPersonWithAuthDetails.value.clientPerson.firstName,
    userType: RoleUserType.CLIENT,
    clientId: props.client_id,
  };
  const response: EditUsernameForUserResponse | null =
    await userManagementStore.editUsernameAndResendInvite(payload);

  return response ? response.editSuccessful : false;
}

/**
 * Listens for saved client person events - currently utilised for this user only but can be extended if needed.
 * @param {string} addListener - Boolean on whether to create or destroy the event listener
 * @returns {void}
 */
function listenForSavedClientPerson(addListener: boolean): void {
  const responseEvent = sessionManager.isClientPortal()
    ? 'savedNewClientPersonFromClientPortal'
    : 'savedNewClientPerson';
  if (!addListener) {
    Mitt.off(responseEvent);
    return;
  }
  Mitt.on(
    responseEvent,
    (saveNewClientPersonResponse: SaveNewClientPersonResponse) => {
      // Because this listener listens for updates from other users, we need to check if the response is for this user.
      const responseIsForCurrentUser: boolean =
        isSaveNewClientPersonRequest.value;
      if (responseIsForCurrentUser) {
        if (!saveNewClientPersonResponse) {
          showNotification(
            'Something went wrong when attempting to save the new client contact.',
            {
              title: 'Client Contact Maintenance',
              type: HealthLevel.ERROR,
            },
          );
        } else {
          if (isNewClientPerson.value) {
            showNotification(
              "An email has been sent to the user's email address to complete the account setup and confirm their credentials.",
              {
                title: 'Client Contact Maintenance',
                type: HealthLevel.INFO,
              },
            );
          } else {
            showNotification(
              'Client contact information successfully updated.',
              {
                title: 'Client Contact Successfully Updated',
                type: HealthLevel.INFO,
              },
            );
          }
          cancelMaintenance();
        }
        isSaveNewClientPersonRequest.value = false;
      }
    },
  );
}

/**
 * Views details of a selected related contact and sets associated and default common address IDs.
 * Filters the common addresses to find those associated with the selected related contact,
 * and updates the associated and default common address IDs accordingly.
 * @param {ClientRelatedContact} selectedClientRelatedContact - The related contact to view.
 */
function viewRelatedContact(
  selectedClientRelatedContact: ClientRelatedContact,
) {
  if (!clientsCommonAddresses.value) {
    return;
  }
  // set this related contacts associated addresses
  const associatedAddressList: ClientCommonAddress[] =
    clientsCommonAddresses.value.filter(
      (x: ClientCommonAddress) =>
        x.addressContacts.find(
          (contact: ClientCommonAddressContact) =>
            contact.contactId === selectedClientRelatedContact!._id,
        ) !== undefined,
    );
  relatedContactsAssociatedCommonAddressIds.value = associatedAddressList.map(
    (x: ClientCommonAddress) => x._id!,
  );
  // set this related contacts default addresses
  const defaultCommonAddressList: ClientCommonAddress[] =
    clientsCommonAddresses.value.filter(
      (x: ClientCommonAddress) =>
        x.defaultContact &&
        x.defaultContact.type === AddressContactType.RELATED_CONTACT &&
        x.defaultContact.contactId === selectedClientRelatedContact!._id,
    );
  relatedContactsDefaultCommonAddressIds.value = defaultCommonAddressList.map(
    (x: ClientCommonAddress) => x._id!,
  );

  clientRelatedContact.value = selectedClientRelatedContact;
}

/**
 * Saves the client related contact details. Validates the form data before saving.
 * Constructs the save request with the related contact and associated address information.
 * If validation fails or the related contact does not match the client ID, it aborts the save operation.
 */
async function saveClientRelatedContact() {
  if (
    !validate(dialogRef.value) ||
    !clientRelatedContact.value ||
    !props.client_id ||
    clientRelatedContact.value.clientId !== props.clientId
  ) {
    return;
  }

  isSaveRelatedContactRequest.value = true;
  const request: ClientCommonAddressRelatedContactSave = {
    clientId: props.clientId,
    clientCommonAddress: null,
    clientRelatedContact: clientRelatedContact.value,
    relatedContactsAssociatedCommonAddressIds:
      relatedContactsAssociatedCommonAddressIds.value,
    relatedContactsDefaultCommonAddressIds:
      relatedContactsDefaultCommonAddressIds.value,
    updatedClientCommonAddresses: [],
  };

  const savedSiteContact = await saveCommonAddressAndRelatedContact(request);

  if (!savedSiteContact) {
    showNotification('Something went wrong.', {
      title: 'Error',
      type: HealthLevel.ERROR,
    });
  }
  isSaveRelatedContactRequest.value = false;
  cancelDialog();
}

/**
 * Fetches common addresses associated with the client.
 * Retrieves and sets the client's common addresses.
 * Logs an error if the fetch operation fails.
 */
async function getClientsCommonAddresses(): Promise<void> {
  // fetch client related contacts
  const commonAddresses: ClientCommonAddress[] | null =
    await useClientDetailsStore().getClientCommonAddressesByClientId(
      props.clientId,
    );
  if (!commonAddresses) {
    console.error(
      "Something went wrong when fetching client's common addresses.",
    );
    return;
  }
  clientsCommonAddresses.value = commonAddresses;
}

/**
 * Set the initial email and mobile values for the client person, so we can check if
 * the email or mobile has changed when saving. This is called when
 * the client person are loaded.
 */
function setInitialUsernameValues(
  user: ClientPersonWithAuthDetails | null,
): void {
  if (!user?.clientPerson?.emailAddress[0]) {
    return;
  }
  initialEmail.value = user.clientPerson.emailAddress[0];
  initialMobile.value = user.clientPerson.contactMobileNumber;
}

/**
 * Invokes the method to fetch and set the client's common addresses.
 */
onMounted(async () => {
  getClientsCommonAddresses();
});

onMounted(() => {
  listenForSavedClientPerson(true);
});

onUnmounted(() => {
  listenForSavedClientPerson(false);
});
</script>

<style scoped lang="scss">
.create-new-btn-container {
  position: absolute;
  top: 110px !important;
  right: 10px;
  z-index: 12 !important;
}

@media (min-width: $user-portal-desktop-breakpoint) {
}

.related-contacts-management-container {
  padding-top: 12px;
  position: relative;
}
.create-new-btn-container {
  top: 24px !important;
}
</style>
