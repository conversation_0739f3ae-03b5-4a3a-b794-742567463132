<template>
  <div class="client-details-company-details">
    <v-layout wrap pb-4>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">1. Name & ABN</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Company Name:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              class="v-solo-custom"
              solo
              flat
              v-model.trim="clientDetails.clientName"
              :disabled="!isEdited"
              color="light-blue"
              :rules="[validate.required]"
              autofocus
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Trading Name:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              class="v-solo-custom"
              solo
              flat
              v-model.trim="clientDetails.tradingName"
              :disabled="!isEdited"
              color="light-blue"
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Australian Business Number:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              class="v-solo-custom"
              solo
              flat
              v-model.trim="clientDetails.abn"
              :disabled="!isEdited"
              :rules="[validate.required]"
              v-mask="'## ### ### ###'"
              color="light-blue"
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                Australia Company Number:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              v-model="clientDetails.acn"
              label="Australian Company Number"
              v-mask="'### ### ###'"
              :disabled="!isEdited"
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">2. Company Type</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Entity Type:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              v-model="clientDetails.entityType"
              :items="entityTypes"
              :rules="[validate.required]"
              item-text="longName"
              item-value="_id"
              :disabled="!isEdited"
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
            >
            </v-select>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Industry Types:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              v-model="clientDetails.industryTypes"
              :items="industryTypes"
              validate-on-blur
              :rules="[validate.required]"
              item-text="longName"
              item-value="id"
              multiple
              :disabled="!isEdited"
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                Is this a transport company?
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              v-model="clientDetails.transportCompany"
              :disabled="!isEdited"
              color="amber"
              class="mt-2"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Established Year:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <DateTimeInputs
              :epochTime.sync="clientDetails.establishedYear"
              :enableValidation="true"
              :type="DateTimeType.YEAR"
              dateLabel="Established Year"
              :hintTextType="HintTextType.FORMATTED_SELECTION"
              :isRequired="false"
              :soloInput="true"
              :boxInput="false"
              :readOnly="!isEdited"
            ></DateTimeInputs>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Country Code:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              v-model="clientDetails.countryCode"
              label="Country Code"
              :items="countries"
              :disabled="!isEdited"
              item-text="iso"
              item-value="iso"
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
            >
            </v-select>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">3. Proprietor Details</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Proprietor Name:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
              v-model.trim="clientDetails.proprietorName"
              :disabled="!isEdited"
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4 pt-4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                Proprietor Location Address:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <AddressSearchAU
              :formDisabled="!isEdited"
              :address="clientDetails.proprietorLocationAddress"
              :boxInput="false"
              :soloInput="true"
              :enableNicknamedAddress="false"
              :enableSuburbSelect="false"
              :enableReturnToDefaultDispatchAddress="false"
            ></AddressSearchAU>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">
            4. Address / Billing Address
          </h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4 pt-4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Company Address:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <AddressSearchAU
              :formDisabled="!isEdited"
              :address="clientDetails.locationAddress"
              :boxInput="false"
              :soloInput="true"
              label=""
              :enableSuburbSelect="false"
              :clientDetails="clientDetails"
              :enableNicknamedAddress="false"
              :enableReturnToDefaultDispatchAddress="false"
            >
            </AddressSearchAU>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Billing Address is...</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              v-model="selectedBillingAddressType"
              :items="billingAddressOptions"
              validate-on-blur
              item-text="longName"
              item-value="id"
              :disabled="!isEdited"
              @change="updatedBillingAddressType"
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex
        md12
        v-if="
          selectedBillingAddressType === 'BILLING_ADDRESS' ||
          selectedBillingAddressType === 'SAME_AS_COMPANY'
        "
      >
        <v-layout>
          <v-flex md4 pt-4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Billing Address:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <AddressSearchAU
              :formDisabled="
                !isEdited || selectedBillingAddressType === 'SAME_AS_COMPANY'
              "
              :address="clientDetails.billingAddress"
              :boxInput="false"
              :soloInput="true"
              :label="
                selectedBillingAddressType === 'BILLING_ADDRESS'
                  ? ''
                  : 'Same as Company Address'
              "
              :enableSuburbSelect="false"
              :clientDetails="clientDetails"
              :enableNicknamedAddress="false"
              :enableReturnToDefaultDispatchAddress="false"
            >
            </AddressSearchAU>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex
        md12
        v-if="
          selectedBillingAddressType === 'PO_BOX' && clientDetails.poBoxAddress
        "
      >
        <v-layout row wrap>
          <v-flex md12 pb-3
            ><v-layout align-center>
              <h5 class="subheader--bold pr-3 pt-1">
                5. Billing Address (PO BOX)
              </h5>
              <v-flex>
                <v-divider></v-divider>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md12>
                <POBoxAddress
                  :formDisabled="!isEdited"
                  :poBoxAddress="clientDetails.poBoxAddress"
                  :validate="validate"
                >
                </POBoxAddress>
              </v-flex>
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">5. Sign-Up Details</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Signup Date:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <DateTimeInputs
              :epochTime.sync="clientDetails.clientSignupDate"
              :enableValidation="true"
              :type="DateTimeType.DATE_START_OF_DAY"
              dateLabel="Signup Date"
              :hintTextType="HintTextType.FORMATTED_SELECTION"
              :isRequired="false"
              :soloInput="true"
              :boxInput="false"
              :readOnly="!isEdited"
            ></DateTimeInputs>
          </v-flex>
        </v-layout>
      </v-flex>
      <ReferralDetails
        :referralDetails="clientDetails.referralSource"
        :isEdited="isEdited"
      />
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">6. National Client Details</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                National Client Identifier:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <NationalClientDetailsSelection
              :key="clientDetails._id"
              :clientDetails="clientDetails"
              :isDisabled="isEdited || !clientDetails._id"
              :nationalClientId="clientDetails.nationalClientId"
              @setUpdatedNationalClientId="setUpdatedNationalClientId"
            ></NationalClientDetailsSelection>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </div>
</template>
<script setup lang="ts">
import NationalClientDetailsSelection from '@/components/admin/ClientDetails/components/national_client_details_selection.vue';
import AddressSearchAU from '@/components/common/addressing/address-search-au/index.vue';
import POBoxAddress from '@/components/common/addressing/po-box-au/index.vue';
import DateTimeInputs, {
  DateTimeType,
  HintTextType,
} from '@/components/common/date-picker/date_time_inputs.vue';
import ReferralDetails from '@/components/common/referral_details.vue';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { AddNationalClientIdToClientRequestResponse } from '@/interface-models/Client/ClientDetails/AddNationalClientIdToClientRequestResponse';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import { AddressApplicationType } from '@/interface-models/Generic/AddressApplicationType';
import countries from '@/interface-models/Generic/Addressing/Country';
import entityTypes from '@/interface-models/Generic/EntityTypes/EntityTypes';
import industryTypes from '@/interface-models/Generic/IndustryTypes/IndustryTypes';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { Validation } from '@/interface-models/Generic/Validation';
import { onMounted, ref } from 'vue';

interface SelectOptions {
  id: AddressApplicationType;
  longName: string;
}

const props = withDefaults(
  defineProps<{
    clientDetails: ClientDetails;
    isEdited: boolean;
  }>(),
  {
    isEdited: false,
  },
);

const billingAddressOptions: SelectOptions[] = [
  {
    id: AddressApplicationType.SAME_AS_COMPANY,
    longName: 'Same as Company Address',
  },
  {
    id: AddressApplicationType.BILLING_ADDRESS,
    longName: 'A Different Address (Search)',
  },
  {
    id: AddressApplicationType.PO_BOX,
    longName: 'A PO Box (Enter)',
  },
];

const validate: Validation = validationRules;

const selectedBillingAddressType = ref<AddressApplicationType>(
  AddressApplicationType.SAME_AS_COMPANY,
);

/** Called when the selectedBillingAddressType is changed. Perform some action
depending on the selected value, such as copy from locationAddress to
billingAddress */
const updatedBillingAddressType = (type: AddressApplicationType) => {
  props.clientDetails.updateBillingAddress(type);
};

/** Emitted from our national client id component. We should update the primary
Client Details data with the saved result. */
const setUpdatedNationalClientId = (
  clientNationalClientId: AddNationalClientIdToClientRequestResponse,
) => {
  if (
    props.clientDetails &&
    clientNationalClientId.clientId === props.clientDetails.clientId
  ) {
    props.clientDetails.nationalClientId =
      clientNationalClientId.nationalClientId;
    showNotification('National Client Identifier successfully saved.', {
      type: HealthLevel.SUCCESS,
    });
  }
};

onMounted(() => {
  selectedBillingAddressType.value = props.clientDetails.setBillingAddress();
});
</script>

<style scoped>
.client-details-key-information {
  padding: 0;
}
</style>
