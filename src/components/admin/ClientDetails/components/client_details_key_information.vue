<template>
  <div class="client-details-key-information">
    <v-layout wrap>
      <v-flex offset-md1 md10>
        <RateExpirationSummaryAlert
          :expirationSummaries="clientDetails.allRateExpirationSummaries"
        ></RateExpirationSummaryAlert>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">1. Name & Status</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Company Name:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              class="v-solo-custom"
              solo
              flat
              v-model.trim="clientDetails.clientName"
              :disabled="!isEdited"
              color="light-blue"
              :rules="[validate.required]"
              autofocus
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Trading Name:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              class="v-solo-custom"
              solo
              flat
              v-model.trim="clientDetails.tradingName"
              :disabled="!isEdited"
              color="light-blue"
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 v-if="!clientDetails._id">
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                <span class="pr-2">
                  <InformationTooltip
                    :right="true"
                    :tooltipType="HealthLevel.INFO"
                  >
                    <v-layout slot="content" row wrap>
                      <v-flex md12>
                        <p class="mb-1">
                          <strong>Placeholder Clients</strong> are clients that
                          are pending Credit Checks or additional information
                          from Head Office. Placeholder Clients are able to be
                          created with only a Company Name.
                        </p>
                        <p class="mb-0">
                          When Placeholder Clients are created, they are placed
                          in a
                          <strong>PENDING</strong> state, indicating that
                          additional details must be filled out. While in a
                          PENDING state, jobs are able to be booked for that
                          Client, however job pricing will not be available
                          until the remaining required details have been
                          completed.
                        </p>
                      </v-flex>
                    </v-layout>
                  </InformationTooltip>
                </span>
                Placeholder Client:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              v-model="clientDetails.isPlaceholder"
              :disabled="!isEdited"
              color="amber"
              label="Placeholder Client"
              persistent-hint
              hint="Placeholder Clients are pending credit checks from Head Office"
              class="mt-2 mb-4"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Active Status:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-layout>
              <v-select
                label="Retired"
                :class="[
                  { 'form-field-required': !clientDetails.isPlaceholder },
                ]"
                v-model="clientIsRetired"
                :items="retiredSelectOptions"
                class="v-solo-custom"
                solo
                :disabled="true"
                color="light-blue"
                flat
              />

              <ClientDetailsUpdateOperationalStatus
                :clientDetails="clientDetails"
                :isEdited="isEdited"
                :clientOperationalStatus="'RETIRED'"
              />
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">2. Company Details:</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6
                class="subheader--faded pr-3 pb-0"
                :class="
                  clientDetails.isPlaceholder
                    ? ''
                    : 'form-field-required-marker'
                "
              >
                Australian Business Number:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              class="v-solo-custom"
              solo
              flat
              v-model.trim="clientDetails.abn"
              :disabled="!isEdited"
              :rules="[clientDetails.isPlaceholder ? true : validate.required]"
              color="light-blue"
              v-mask="'## ### ### ###'"
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6
                class="subheader--faded pr-3 pb-0"
                :class="
                  clientDetails.isPlaceholder
                    ? ''
                    : 'form-field-required-marker'
                "
              >
                Entity Type:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              v-model="clientDetails.entityType"
              :class="[{ 'form-field-required': !clientDetails.isPlaceholder }]"
              :items="entityTypes"
              :rules="[!clientDetails.isPlaceholder ? validate.required : true]"
              item-text="longName"
              item-value="_id"
              :disabled="!isEdited"
              class="v-solo-custom"
              solo
              flat
            >
            </v-select>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6
                class="subheader--faded pr-3 pb-0"
                :class="
                  clientDetails.isPlaceholder
                    ? ''
                    : 'form-field-required-marker'
                "
              >
                Industry Types:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              label="Industry Types"
              v-model="clientDetails.industryTypes"
              :items="industryTypes"
              validate-on-blur
              :rules="clientDetails.isPlaceholder ? [] : [validate.required]"
              item-text="longName"
              item-value="id"
              :class="[{ 'form-field-required': !clientDetails.isPlaceholder }]"
              multiple
              :disabled="!isEdited"
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6
                class="subheader--faded pr-3 pb-0"
                :class="
                  clientDetails.isPlaceholder
                    ? ''
                    : 'form-field-required-marker'
                "
              >
                Client Type:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              v-model="clientDetails.clientType"
              :items="clientTypes"
              :class="[{ 'form-field-required': !clientDetails.isPlaceholder }]"
              :rules="[!clientDetails.isPlaceholder ? validate.required : true]"
              item-text="longName"
              item-value="id"
              :disabled="!isEdited"
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6
                class="subheader--faded pr-3 pb-0"
                :class="
                  clientDetails.isPlaceholder
                    ? ''
                    : 'form-field-required-marker'
                "
              >
                POD Photos Required:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="[{ 'form-field-required': !clientDetails.isPlaceholder }]"
              :rules="[
                !clientDetails.isPlaceholder ? validate.required : true,
                validate.nonNegative,
                validate.number,
              ]"
              v-model.number="clientDetails.proofOfDelivery.pudPhotos"
              class="v-solo-custom"
              mask="##"
              solo
              hint="The number of POD photos that Drivers will be required to take on the GoDesta Driver app"
              persistent-hint
              flat
              :disabled="!isEdited"
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">3. Billing</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6
                class="subheader--faded pr-3 pb-0"
                :class="
                  clientDetails.isPlaceholder
                    ? ''
                    : 'form-field-required-marker'
                "
              >
                Trading Terms:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              v-model="clientDetails.clientTradingTerms"
              :items="tradingTerms"
              :class="[{ 'form-field-required': !clientDetails.isPlaceholder }]"
              :rules="[!clientDetails.isPlaceholder ? validate.required : true]"
              item-text="longName"
              item-value="id"
              :disabled="!isEdited"
              class="v-solo-custom"
              solo
              color="light-blue"
              flat
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6
                class="subheader--faded pr-3 pb-0"
                :class="
                  clientDetails.isPlaceholder
                    ? ''
                    : 'form-field-required-marker'
                "
              >
                Billing Cycle:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              v-model="clientDetails.billingCycleId"
              :items="billingCycles"
              :class="[{ 'form-field-required': !clientDetails.isPlaceholder }]"
              :rules="[!clientDetails.isPlaceholder ? validate.required : true]"
              item-text="longName"
              item-value="id"
              :disabled="!isEdited"
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
              placeholder=""
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">
            4. Accounts Payable Information
          </h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6
                class="subheader--faded pr-3 pb-0"
                :class="
                  clientDetails.isPlaceholder
                    ? ''
                    : 'form-field-required-marker'
                "
              >
                Approved Credit Limit:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              v-model.number="approvedCreditLimit"
              :prefix="'$'"
              :class="[{ 'form-field-required': !clientDetails.isPlaceholder }]"
              :rules="
                !clientDetails.isPlaceholder
                  ? [validate.required, validate.nonNegative, validate.number]
                  : []
              "
              class="v-solo-custom"
              solo
              color="light-blue"
              flat
              :disabled="!isEdited"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6
                class="subheader--faded pr-3 pb-0"
                :class="
                  clientDetails.isPlaceholder
                    ? ''
                    : 'form-field-required-marker'
                "
              >
                Credit Status:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-layout>
              <v-select
                label="Credit Status"
                :class="[
                  { 'form-field-required': !clientDetails.isPlaceholder },
                ]"
                :rules="clientDetails.isPlaceholder ? [] : [validate.required]"
                v-model="clientDetails.accountsReceivable.creditStatus"
                :items="creditStatusTypes"
                item-text="longName"
                item-value="id"
                :disabled="true"
                class="v-solo-custom"
                @change="setAccountOverdueStatus"
                solo
                :hint="
                  clientDetails.statusList.includes(13)
                    ? 'Credit status unavailable for editing when client is retired.'
                    : ''
                "
                persistent-hint
                color="light-blue"
                flat
              />

              <ClientDetailsUpdateOperationalStatus
                :clientDetails="clientDetails"
                :isEdited="isEdited"
                :clientOperationalStatus="'CREDIT_STATUS'"
              />
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import ClientDetailsUpdateOperationalStatus from '@/components/admin/ClientDetails/components/client_details_update_operational_status.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import RateExpirationSummaryAlert from '@/components/common/ui-elements/rate_expiration_summary_alert.vue';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import clientTypes from '@/interface-models/Client/ClientTypes';
import creditStatusTypes from '@/interface-models/Generic/AccountsReceivable/CreditStatusTypes';
import billingCycles from '@/interface-models/Generic/BillingCycles/BillingCycles';
import entityTypes from '@/interface-models/Generic/EntityTypes/EntityTypes';
import industryTypes from '@/interface-models/Generic/IndustryTypes/IndustryTypes';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import tradingTerms from '@/interface-models/Generic/TradingTerms/TradingTerms';
import { Validation } from '@/interface-models/Generic/Validation';
import { WritableComputedRef, computed } from 'vue';

const props = withDefaults(
  defineProps<{
    clientDetails: ClientDetails;
    isEdited: boolean;
  }>(),
  {
    isEdited: false,
  },
);

const retiredSelectOptions: string[] = ['ACTIVE', 'RETIRED'];

const validate: Validation = validationRules;

const approvedCreditLimit: WritableComputedRef<number | undefined> = computed({
  get(): number | undefined {
    if (
      props.clientDetails.accountsReceivable &&
      props.clientDetails.accountsReceivable.approvedCreditLimit
    ) {
      return props.clientDetails.accountsReceivable.approvedCreditLimit;
    }
    return undefined;
  },
  set(value: number | undefined): void {
    if (value !== undefined) {
      props.clientDetails.accountsReceivable.approvedCreditLimit = value;
    }
  },
});

/** Client retirement status  */
const clientIsRetired = computed<string>(() => {
  const hasRetiredStatus = props.clientDetails.statusList.includes(13);
  return hasRetiredStatus ? 'RETIRED' : 'ACTIVE';
});

/** Check if we need to add or remove ACCOUNT OVERDUE (7) to statusList when
credit status is changed */
const setAccountOverdueStatus = (): void => {
  const creditStatus = props.clientDetails.accountsReceivable?.creditStatus;
  if (creditStatus === 2) {
    if (!props.clientDetails.statusList.includes(7)) {
      props.clientDetails.statusList.push(7);
    }
  } else {
    const foundIndex = props.clientDetails.statusList.findIndex((c) => c === 7);
    if (foundIndex !== -1) {
      props.clientDetails.statusList.splice(foundIndex, 1);
    }
  }
};
</script>
