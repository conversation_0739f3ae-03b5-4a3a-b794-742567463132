<div class="client-bread-crumbs-container">
  <v-layout>
    <v-flex v-for="breadCrumb of breadCrumbs" :key="breadCrumb.id">
      <v-layout align-center v-if="breadCrumb.show">
        <span
          @click="goToClientHome(breadCrumb.id)"
          class="subheader--faded pb-0"
          :class="breadCrumb.id === 1 ? 'link' : ''"
          >{{breadCrumb.name}}</span
        >

        <v-icon
          v-if="breadCrumb.id !== 3 && breadCrumbs[1].show"
          class="mx-2"
          style="padding-bottom:1px;   color: rgb(176, 176, 189);"
          size="12"
          >fas fa-long-arrow-right</v-icon
        >
      </v-layout>
    </v-flex>
  </v-layout>
</div>
