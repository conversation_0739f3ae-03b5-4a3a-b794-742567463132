import ClientSearchSummary from '@/interface-models/Client/ClientDetails/ClientSearchSummary';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { Component, Vue } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ClientBreadCrumbs extends Vue {
  get breadCrumbs() {
    const clientDetailsStore = useClientDetailsStore();
    const clientDetails: ClientSearchSummary | undefined =
      clientDetailsStore.clientSummaryList.find(
        (x: ClientSearchSummary) =>
          x.clientId === clientDetailsStore.selectedClientId,
      );
    return [
      {
        id: 1,
        name: 'Client',
        show: true,
      },
      {
        id: 2,
        name: this.$route.name,
        show: this.$route.name !== 'Client',
      },
      {
        id: 3,
        name:
          this.$route.params.id === 'new'
            ? 'New Client'
            : clientDetails
              ? clientDetails.tradingName
                ? clientDetails.tradingName
                : clientDetails.clientName
              : '',
        show: this.$route.name !== 'Client',
      },
    ];
  }

  public goToClientHome(id: number) {
    if (id !== 1 || this.$route.name === 'Client') {
      return;
    }
    this.$router.push('/client');
  }
}
