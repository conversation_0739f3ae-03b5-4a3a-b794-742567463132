.administration-dashboard {
  // icons
  $error-icon: '\f071';
  $warn-icon: '\f06a';
  .tab-item-title {
    padding: 0px 16px 3px;
    letter-spacing: 1px;
  }

  .tab-item-container {
    margin-left: 16px;
    margin-bottom: 14px;
    position: relative;
    max-width: fit-content;
    border: 1px solid $translucent;
    border-radius: $border-radius-base;
    .tab-item {
      padding: 6px 18px;
      margin: 2px 4px;
      font-size: $font-size-14;
      font-weight: 500;
      color: var(--light-text-color);
      transition: 0.3s;
      border-radius: $border-radius-base;
      font-family: $sub-font-family;
      letter-spacing: 1px;
      &:hover {
        color: var(--highlight);
        cursor: pointer;
        background-color: $detail-item-value-bg;
        box-shadow: var(--box-shadow);
      }

      &.active {
        color: var(--highlight);
        background-color: $detail-item-value-bg;
        text-decoration: underline;
        text-decoration-thickness: 2px;
        text-underline-offset: 12px;
        font-weight: 700;
        box-shadow: $shadow-primary;
      }
    }
  }

  .expiration-items--header {
    max-height: calc(100vh - (40px + 94px + 45px + 40px + 30px));
    overflow-y: auto;
    font-size: $font-size-16;
    font-weight: 600;
    color: var(--text-color);

    padding-left: 8px;
    padding: 0px $x-padding;

    .summary-details {
      font-size: $font-size-14;
      font-weight: normal;
      background-color: $summary-bg;
      border: 1px solid $border-light;
      padding: 6px 12px;
      border-radius: 8px;
    }

    .dashboard-card.show-divider {
      position: relative;
      &:first-child {
        margin-bottom: 20px;
        &::before {
          content: '';
          position: absolute;
          bottom: -10px;
          left: 50%;
          width: 40px;
          height: 3px;
          border-radius: 3px;
          background-color: var(--highlight);
          transform: translateX(-50%);
        }
      }
    }
    .dashboard-card {
      &.item-active > .dashboard-card__bottomrow > .item-count {
        &.error-type::before,
        &.warn-type::before {
          color: white;
          font-weight: 600;
        }
      }

      .dashboard-card__bottomrow > .item-count {
        &.error-type,
        &.warn-type {
          padding-left: 8px;
          padding-right: 8px;
          font-size: $font-size-18;
          font-weight: 500;
          &::before {
            font-family: $font-awesome-family;
            text-decoration: none;
            font-weight: 500;
            margin-right: 4px;
            font-size: $font-size-16;
            display: inline-block;
          }
        }
        &.error-type {
          position: relative;
          &.show-divider {
            &::after {
              // add var(--text-color) vertical line to the right
              content: '';
              position: absolute;
              top: 0;
              right: 0;
              bottom: 0;
              width: 1px;
              background-color: $translucent-light;
            }
          }
          &::before {
            content: $error-icon;
            color: $error-type;
          }
          &::after {
            color: $toast-error-border;
          }
        }
        &.warn-type {
          &::before {
            content: $warn-icon;
            color: $warning;
          }
        }
      }
      &:hover {
        background-color: var(--background-color-600) !important;
        box-shadow: var(--box-shadow);
      }
    }
  }

  .right-content-container {
    max-height: calc(100vh - (40px + 94px + 20px));
    height: calc(100vh - (40px + 94px + 20px));
    overflow-y: auto;
  }

  .main-content-container {
    padding: 10px 16px 16px 16px;

    .main-content {
      max-height: calc(100vh - (40px + 94px + 60px + 32px));
      height: calc(100vh - (40px + 94px + 60px + 32px));
      overflow-y: auto;
      @media (max-width: 1263px) {
        font-size: $font-size-12;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: $border-radius-base;
      }
      &::-webkit-scrollbar-track {
        border-radius: $border-radius-base;
      }
      background-color: $summary-bg;
      border: 1px solid var(--border-color);
      padding: 4px 2px 16px 2px;
      border-radius: 4px;

      .group {
        color: var(--text-color);
        position: relative;
        margin-top: 2px;
        border-radius: 4px;
        padding: 4px;
        &:first-child {
          margin-top: 4px;
        }
        &:last-child {
          margin-bottom: 8px;
        }
        .group-chip {
          font-size: $font-size-10;
          font-weight: 800;
          text-transform: uppercase;
          padding: 2px 8px 2px 4px;
          margin-right: 8px;
          border-radius: $border-radius-sm;
          color: var(--group-chip-color);
          border: 1px solid var(--group-chip-color);
          &:hover {
            color: var(--text-color);
            background-color: var(--highlight);
          }
        }

        &.related-group {
          position: relative;
          padding-left: $divider-offset;
          margin-top: 0px;
          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: $divider-offset;
            bottom: 0;
            border-left: 1px solid var(--group-chip-color);
          }
          &:last-child {
            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: $divider-offset;
              bottom: 0;
              border-left: 1px solid var(--group-chip-color);
              height: 19px;
            }
          }

          .related-group-chip {
            font-size: $font-size-10;
            font-weight: 800;
            text-transform: uppercase;
            padding: 2px 8px 2px 4px;
            margin-right: 11px;
            border-radius: $border-radius-sm;
            border: 1px solid var(--related-group-chip-color);
            color: var(--related-group-chip-color);
            &:hover {
              color: var(--text-color);
              background-color: var(--highlight);
            }
          }
        }
        .v-divider {
          border-color: var(--group-chip-color);
          opacity: 0.8;
        }

        &:hover {
          cursor: pointer;
          background-color: $detail-item-value-bg;
          // border: 1px solid $translucent;
          box-shadow: $box-shadow-dark;
        }

        &.has-children {
          .expiring-document-list-item {
            &.top-level {
              &::before {
                content: '';
                position: absolute;
                top: 0;
                left: $divider-offset;
                bottom: 0;
                border-left: 1px solid var(--group-chip-color);
              }
            }
          }
        }

        .expiring-document-list-item {
          position: relative;
          padding-left: 40px;

          &.top-level {
            li:first-child {
              padding-top: 4px;
            }
          }

          li {
            .name {
              position: relative;
              left: 4px;
              color: var(--light-text-color);
              width: 90%;
            }
            .due-date {
              padding-right: 8px;
              white-space: nowrap;
              text-transform: uppercase;
              font-weight: 600;
              &.error-type {
                color: var(--light-text-color);
                &::before {
                  content: $error-icon;
                  font-family: $font-awesome-family;
                  text-decoration: none;
                  font-weight: 400;
                  margin-right: 4px;
                  color: $error-type;
                  font-size: $font-size-13;
                  display: inline-block;
                }
              }
              &.warn-type {
                color: var(--light-text-color);
                &::before {
                  content: $warn-icon;
                  font-family: $font-awesome-family;
                  font-weight: 400;
                  margin-right: 4px;
                  color: $warning;
                  font-size: $font-size-13;
                }
              }
            }
            &:hover {
              .name {
                color: var(--highlight);
                text-underline-offset: 4px;
                text-decoration: underline;
                font-weight: 600;
              }
              cursor: pointer;

              color: var(--light-text-color);
            }
          }
        }
      }

      .simple-data-table {
        width: 100%;
        border-collapse: collapse;

        /* Set header to stick to the top of the container. */
        thead tr th {
          padding-top: 12px;
          background-color: var(--background-color-600);
          position: sticky;
          top: 0;
          z-index: 4;
          &::after {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            border-bottom: 1px solid var(--group-chip-color);
            z-index: -1;
          }
        }

        tbody tr td {
          padding: 8px 8px;
        }
        tbody.select-row tr {
          &:hover {
            cursor: pointer;
            background-color: $detail-item-value-bg;
          }
        }
        tbody.select-cell tr td.selectable-cell {
          &:hover {
            color: var(--highlight);
            text-decoration-thickness: 2px !important;
            text-underline-offset: 4px;
            text-decoration: underline;
            cursor: pointer;
          }
        }
      }
    }
  }

  .dashboard-card {
    background-color: var(--background-color-400);
    &.item-active {
      background-color: var(--primary-gradient);
      background-image: var(--primary-gradient);
      box-shadow: $shadow-primary;
    }
  }
}
