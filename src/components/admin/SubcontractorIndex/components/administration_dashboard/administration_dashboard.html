<v-layout class="administration-dashboard">
  <v-flex md4 class="px-2">
    <v-layout class="tab-item-title" align-center>
      <h3>
        {{category === 'SUBCONTRACTOR' ? 'Subcontractor Compliance' : `Client
        Maintenance`}} Reminders
      </h3>

      <div class="pl-2 pb-1">
        <InformationTooltip :bottom="true" tooltipType="info">
          <v-layout slot="content" row wrap>
            <v-flex md12>
              <v-layout align-center>
                <v-icon color="$error-type" size="14">
                  far fa-exclamation-triangle
                </v-icon>
                <span class="pl-2"> Expired/Not Valid/Missing </span>
              </v-layout>
              <v-layout align-center>
                <v-icon color="$warn-type" size="14">
                  far fa-exclamation-circle
                </v-icon>
                <span class="pl-2"> Expiring within 7 days </span>
              </v-layout>
            </v-flex>
          </v-layout>
        </InformationTooltip>
      </div>
    </v-layout>
    <v-layout row class="tab-item-container" align-center>
      <div
        v-for="tab in tabItems"
        :key="tab.id"
        class="tab-item"
        :class="{'active': !!selectedTabController && (selectedTabController.id === tab.id)}"
        @click="selectedTabController = tab"
      >
        {{tab.name}}
      </div>
      <v-spacer></v-spacer>
    </v-layout>
    <v-layout>
      <v-slide-y-transition hide-on-leave>
        <v-flex
          md12
          class="expiration-items--header"
          :key="selectedTabController.id"
        >
          <SubcontractorSummaryDetails
            v-if="selectedTabController && selectedTabController.type"
            class="mb-2 px-2"
            :entityType="selectedTabController.type"
          ></SubcontractorSummaryDetails>
          <div
            v-for="(expirationType, index) in expirationTypesList"
            class="dashboard-card"
            :class="[(expirationType.id === mainContentController) ? 'item-active' : '',
            (expirationType.id === 'ALL_EXPIRATIONS') ? 'show-divider' : ''
            ]"
            :key="expirationType.id"
            v-if="expirationType.isVisible"
            @click="mainContentController = expirationType.id"
          >
            <v-layout
              justify-space-between
              align-center
              class="dashboard-card__toprow"
            >
              <v-flex pr-4>
                <v-layout>
                  <span class="title-text">{{ expirationType.longName }}</span>
                </v-layout>
                <v-layout>
                  <span class="subtitle-text"
                    >{{ expirationType.description }}</span
                  >
                </v-layout>
              </v-flex>
              <span
                ><v-icon class="arrow-icon">far fa-arrow-right</v-icon></span
              >
            </v-layout>
            <v-layout
              justify-start
              align-center
              class="dashboard-card__bottomrow"
            >
              <div class="icon-container">
                <v-icon class="header-icon">{{expirationType.icon}}</v-icon>
              </div>
              <span class="item-count"> {{expirationType.count.total}} </span>
              <v-spacer></v-spacer>
              <span
                class="item-count error-type"
                :class="{ 'show-divider': expirationType.count.urgent && expirationType.count.soon }"
                v-if="expirationType.count.urgent"
              >
                {{expirationType.count.urgent}}
              </span>
              <span
                class="item-count warn-type"
                v-if="expirationType.count.soon"
              >
                {{expirationType.count.soon}}
              </span>
            </v-layout>
          </div>
        </v-flex>
      </v-slide-y-transition>
    </v-layout>
  </v-flex>

  <v-fade-transition hide-on-leave>
    <v-flex md6 class="main-content-container" :key="mainContentController">
      <v-layout
        align-center
        class="banner-custom px-3 pb-2"
        v-if="viewType === 'EXPIRATIONS'"
      >
        <v-layout column>
          <h3 class="mb-0">
            {{selectedExpirationItem.longName}}
            <span style="font-weight: 400">- {{resultCount}} item(s)</span>
            <span class="pl-2" v-if="selectedExpirationItem.tooltip">
              <InformationTooltip
                :right="true"
                tooltipType="info"
                activatorClass="pb-1"
              >
                <v-layout slot="content" row wrap>
                  <v-flex md12>
                    <p class="mb-1">{{selectedExpirationItem.tooltip}}</p>
                  </v-flex>
                </v-layout>
              </InformationTooltip>
            </span>
          </h3>
          <h4>{{selectedExpirationItem.description}}</h4>
        </v-layout>
        <div class="priority-button-group">
          <div
            class="priority-button"
            v-for="option in priorityOptions"
            :key="option.id"
            :class="[option.class ? option.class : '', selectedPriorityOption === option.id ? 'active' : '']"
            @click="setSelectedPriority(option.id)"
          >
            {{option.name}}
          </div>
        </div>
      </v-layout>
      <v-layout align-center class="banner-custom px-3 pb-2" v-else>
        <v-layout column>
          <h3>
            {{selectedActivityItem.longName}}
            <span style="font-weight: 400">- {{resultCount}} item(s)</span>
          </h3>
          <h4>{{selectedActivityItem.description}}</h4>
        </v-layout>
      </v-layout>
      <v-layout
        id="main-content"
        class="main-content"
        v-if="viewType === 'EXPIRATIONS'"
      >
        <v-flex md12>
          <v-layout row wrap>
            <v-flex
              md12
              v-for="group in expirationListForCurrentSelection"
              :key="group.id"
              class="group"
              :class="{'has-children': group.relatedGroups && group.relatedGroups.length}"
            >
              <v-layout
                class="group-main-body"
                @click="viewEntityPage(group.entityId, group.entityType)"
              >
                <v-flex>
                  <v-layout align-center>
                    <span class="group-chip">
                      <i
                        :class="getIconClass(group.entityType)"
                        class="px-1"
                      ></i>
                      {{returnReadableEntityType(group.entityType)}}
                    </span>
                    <span style="font-weight: 700" class="pr-2"
                      >{{group.name}}</span
                    >
                    <v-flex><v-divider class="my-2"></v-divider></v-flex
                  ></v-layout>
                  <ul class="expiring-document-list-item top-level">
                    <li v-for="item in group.summaries" :key="item.name">
                      <span>
                        <v-layout>
                          <span
                            @click="viewEntityPage(item.entityId, group.entityType, item.expirationType)"
                            class="name"
                            >{{item.name}}</span
                          >
                          <v-spacer></v-spacer>
                          <span
                            class="due-date"
                            :class="[returnValidToDateClass(item.validToDate)]"
                          >
                            <span v-if="item.validToDate">Due:</span>
                            <span>
                              {{item.validToDate ?
                              returnFormattedDate(item.validToDate) :
                              `EXPIRED/NOT VALID`}}
                            </span>
                          </span>
                        </v-layout>
                      </span>
                    </li>
                  </ul>
                </v-flex>
              </v-layout>
              <v-layout
                v-if="group.relatedGroups && group.relatedGroups.length"
                row
                wrap
              >
                <v-flex
                  md12
                  v-for="relatedGroup in group.relatedGroups"
                  :key="`r-${relatedGroup.id}`"
                  class="group related-group"
                  @click="viewEntityPage(relatedGroup.entityId, relatedGroup.entityType)"
                >
                  <v-layout align-center py-1>
                    <div style="width: 20px">
                      <v-divider class="my-2"></v-divider>
                    </div>
                    <span class="related-group-chip">
                      <i
                        :class="getIconClass(relatedGroup.entityType)"
                        class="px-1"
                      ></i>
                      {{returnReadableEntityType(relatedGroup.entityType)}}
                    </span>
                    <span style="font-weight: 700">{{relatedGroup.name}}</span>
                    <!-- <v-flex><v-divider class="my-2"></v-divider></v-flex> -->
                  </v-layout>
                  <ul class="expiring-document-list-item">
                    <li
                      v-for="relatedItem in relatedGroup.summaries"
                      :key="relatedItem.name"
                    >
                      <span>
                        <v-layout>
                          <span
                            @click="viewEntityPage(relatedItem.entityId, relatedGroup.entityType, relatedItem.expirationType)"
                            class="name"
                            >{{relatedItem.name}}</span
                          >
                          <v-spacer></v-spacer>
                          <span
                            class="due-date"
                            :class="[returnValidToDateClass(relatedItem.validToDate)]"
                          >
                            <span v-if="relatedItem.validToDate">Due:</span>
                            <span>
                              {{relatedItem.validToDate ?
                              returnFormattedDate(relatedItem.validToDate) :
                              `EXPIRED/NOT VALID`}}
                            </span>
                          </span>
                        </v-layout>
                      </span>
                    </li>
                  </ul>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
      <v-layout id="main-content" class="main-content pt-0" row wrap v-else>
        <v-flex md12>
          <table class="simple-data-table">
            <thead>
              <tr v-if="mainContentController === 'ACTIVE_JOBS'">
                <th class="text-xs-left">Job#</th>
                <th class="text-xs-left" v-if="category === 'CLIENT'">
                  Client
                </th>

                <th class="text-xs-left">Fleet#</th>
                <th class="text-xs-left" v-if="category === 'SUBCONTRACTOR'">
                  Owner
                </th>
                <th
                  class="text-xs-left"
                  style="white-space: nowrap"
                  v-if="category === 'SUBCONTRACTOR'"
                >
                  Owner Mob.
                </th>
                <th class="text-xs-left">Driver</th>
                <th class="text-xs-right" style="white-space: nowrap">
                  Driver Mob.
                </th>
              </tr>
              <tr v-else>
                <th class="text-xs-left">Category</th>
                <th class="text-xs-left">Name</th>
                <th class="text-xs-left" v-if="category === 'SUBCONTRACTOR'">
                  Owner
                </th>
                <th class="text-xs-left" v-if="category === 'SUBCONTRACTOR'">
                  Contact No.
                </th>
                <th class="text-xs-right">Status</th>
              </tr>
            </thead>
            <tbody
              v-if="mainContentController === 'ACTIVE_JOBS'"
              class="select-cell"
            >
              <tr
                v-if="activityJobTableData"
                v-for="data in activityJobTableData"
                :key="data.jobId"
              >
                <td class="text-xs-left">{{data.displayId}}</td>
                <td
                  v-if="category === 'CLIENT'"
                  class="text-xs-left"
                  :class="{'selectable-cell': !!data.clientId}"
                  @click="data.clientId ? viewEntityPage(data.clientId, 'CLIENT') : null"
                >
                  {{data.clientName ? data.clientName : '-'}}
                </td>
                <td
                  class="text-xs-left"
                  :class="{'selectable-cell': !!data.fleetAssetId}"
                  @click="data.fleetAssetId ? viewEntityPage(data.fleetAssetId, 'FLEET_ASSET') : null"
                >
                  {{data.csrAssignedId ? data.csrAssignedId : '-'}}
                </td>

                <td
                  v-if="category === 'SUBCONTRACTOR'"
                  class="text-xs-left"
                  :class="{'selectable-cell': !!data.ownerId}"
                  @click="data.ownerId ? viewEntityPage(data.ownerId, 'FLEET_ASSET_OWNER') : null"
                >
                  {{data.owner ? data.owner : '-'}}
                </td>
                <td class="text-xs-left" v-if="category === 'SUBCONTRACTOR'">
                  {{data.ownerMob ? data.ownerMob : '-'}}
                </td>
                <td
                  class="text-xs-left"
                  :class="{'selectable-cell': !!data.driverId}"
                  @click="data.driverId ? viewEntityPage(data.driverId, 'DRIVER') : null"
                >
                  {{data.driverName ? data.driverName : '-'}}
                </td>
                <td class="text-xs-right">
                  {{data.driverMob ? data.driverMob : '-'}}
                </td>
              </tr>
            </tbody>
            <tbody v-else class="select-row">
              <tr
                v-if="activityTableData"
                v-for="data in activityTableData"
                :key="data.entityId"
                @click="viewEntityPage(data.entityId, data.entityType)"
              >
                <td class="text-xs-left">
                  {{returnReadableEntityType(data.entityType)}}
                </td>
                <td class="text-xs-left">{{data.name}}</td>
                <td class="text-xs-left" v-if="category === 'SUBCONTRACTOR'">
                  {{data.secondaryName ? data.secondaryName : '-'}}
                </td>
                <td class="text-xs-left" v-if="category === 'SUBCONTRACTOR'">
                  {{data.contactNumber}}
                </td>
                <td class="text-xs-right">{{data.status}}</td>
              </tr>
            </tbody>
          </table>
        </v-flex>
      </v-layout>
    </v-flex>
  </v-fade-transition>
  <v-flex md2 class="pl-2 pr-3">
    <v-layout class="right-content-container" row wrap>
      <v-flex
        v-for="(activityItem, index) in entityActivityList"
        :key="activityItem.id"
        md12
        class="dashboard-card"
        :class="(activityItem.id === mainContentController) ? 'item-active' : ''"
        elevation="0"
        tile
        @click="mainContentController = activityItem.id;
        $refs[`activityItem`][index].emitTableData ? $refs[`activityItem`][index].emitTableData() : null"
      >
        <v-layout
          v-if="activityItem.id === 'LOW_ENGAGEMENT_DRIVERS' || activityItem.id === 'LOW_ENGAGEMENT_TRUCKS' || activityItem.id === 'LOW_ENGAGEMENT_CLIENTS'"
        >
          <LastBookingDateCounter
            :entityType="activityItem.entityType"
            :longName="activityItem.longName"
            :description="activityItem.description"
            :icon="activityItem.icon"
            ref="activityItem"
            @setTableData="setActivityTableData"
          ></LastBookingDateCounter>
        </v-layout>
        <v-layout v-else-if="activityItem.id === 'ACTIVE_JOBS'">
          <DashboardActiveJobCounter
            :category="category"
            :longName="activityItem.longName"
            :description="activityItem.description"
            :icon="activityItem.icon"
            ref="activityItem"
            @setTableData="setActivityJobTableData"
          ></DashboardActiveJobCounter>
        </v-layout>
        <v-layout v-else-if="activityItem.id === 'DISSOCIATED_DRIVERS'">
          <DisassociatedDriverCounter
            :longName="activityItem.longName"
            :description="activityItem.description"
            :icon="activityItem.icon"
            ref="activityItem"
            @setTableData="setActivityTableData"
          ></DisassociatedDriverCounter>
        </v-layout>
        <v-layout v-else-if="activityItem.id === 'TRUCKS_WITH_NO_DRIVERS'">
          <ActiveTrucksWithNoDriverCounter
            :longName="activityItem.longName"
            :description="activityItem.description"
            :icon="activityItem.icon"
            ref="activityItem"
            @setTableData="setActivityTableData"
          ></ActiveTrucksWithNoDriverCounter>
        </v-layout>
        <v-layout v-else-if="activityItem.id === 'CLIENTS_REQUIRING_ATTENTION'">
          <ClientsRequiringAttentionCounter
            :longName="activityItem.longName"
            :description="activityItem.description"
            :icon="activityItem.icon"
            ref="activityItem"
            @setTableData="setActivityTableData"
          ></ClientsRequiringAttentionCounter>
        </v-layout>
      </v-flex>
    </v-layout>
  </v-flex>
</v-layout>
