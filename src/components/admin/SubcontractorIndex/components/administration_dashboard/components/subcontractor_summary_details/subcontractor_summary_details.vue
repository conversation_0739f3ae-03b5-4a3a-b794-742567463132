<template>
  <v-layout class="subcontractor-summary-details tags-chip" justify-end>
    <v-flex md12 v-if="entityType === 'FLEET_ASSET'">
      <v-layout justify-space-between>
        <span
          >Current Allocated Work:
          <span class="tags-chip-value">{{ allocatedWork }}</span></span
        >
        <span
          >Active: <span class="tags-chip-value">{{ activeFleet }}</span></span
        >
        <span
          >Do Not Use:
          <span class="tags-chip-value">{{ doNotUseAssets }}</span></span
        >
      </v-layout>
    </v-flex>
    <v-flex md12 v-else-if="entityType === 'FLEET_ASSET_OWNER'">
      <v-layout justify-space-between>
        <span
          >Current Allocated Work:
          <span class="tags-chip-value">{{ allocatedWork }}</span></span
        >
        <span
          >Active: <span class="tags-chip-value">{{ activeOwners }}</span></span
        >
        <span
          >Do Not Use:
          <span class="tags-chip-value">{{ doNotUseOwners }}</span></span
        >
      </v-layout>
    </v-flex>
    <v-flex md12 v-else-if="entityType === 'DRIVER'">
      <v-layout justify-space-between>
        <span
          >Currently Online:
          <span class="tags-chip-value">{{ onlineDrivers }}</span></span
        >
        <span
          >Active:
          <span class="tags-chip-value">{{ activeDrivers }}</span></span
        >
        <span
          >Do Not Use:
          <span class="tags-chip-value">{{ doNotUseDrivers }}</span></span
        >
      </v-layout>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';

import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import { DriverOnlineStatus } from '@/interface-models/Generic/DriverOnlineStatus/DriverOnlineStatus';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';

import { EntityType } from '@/interface-models/Generic/EntityType';
import { OperationJobSummary } from '@/interface-models/Jobs/OperationJobSummary';
import { useDriverAppStore } from '@/store/modules/DriverAppStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useJobStore } from '@/store/modules/JobStore';
import { computed, ComputedRef } from 'vue';

const props = defineProps<{
  entityType: EntityType;
}>();

// FleetAssetSummary EntityType.FLEET_ASSET
const trucks: ComputedRef<FleetAssetSummary[]> = computed(
  (): FleetAssetSummary[] => {
    if (props.entityType !== EntityType.FLEET_ASSET) {
      return [];
    }
    const fleetAssetStore = useFleetAssetStore();
    return fleetAssetStore.getAllFleetAssetList;
  },
);

const activeFleet: ComputedRef<number> = computed((): number => {
  if (props.entityType !== EntityType.FLEET_ASSET) {
    return 0;
  }
  return trucks.value.filter((x: FleetAssetSummary) => x.statusList.includes(4))
    .length;
});

const doNotUseAssets = computed((): number => {
  if (props.entityType !== EntityType.FLEET_ASSET) {
    return 0;
  }
  return trucks.value.filter((x: FleetAssetSummary) =>
    x.statusList.includes(47),
  ).length;
});

const allocatedWork: ComputedRef<number> = computed((): number => {
  if (props.entityType === EntityType.FLEET_ASSET) {
    const allocatedFleetAssetIds = useJobStore()
      .operationJobsList.filter(
        (x: OperationJobSummary) =>
          x.workStatus >= WorkStatus.BOOKED &&
          x.workStatus <= WorkStatus.IN_PROGRESS,
      )
      .map((allocatedJob: OperationJobSummary) => allocatedJob.fleetAssetId);
    return [...new Set(allocatedFleetAssetIds)].length;
  } else if (props.entityType === EntityType.FLEET_ASSET_OWNER) {
    const allocatedFleetAssetIds: string[] = useJobStore()
      .operationJobsList.filter(
        (x: OperationJobSummary) =>
          x.workStatus >= WorkStatus.BOOKED &&
          x.workStatus <= WorkStatus.IN_PROGRESS,
      )
      .map((allocatedJob: OperationJobSummary) => allocatedJob.fleetAssetId);

    const allocatedFleetAssets: string[] = [...new Set(allocatedFleetAssetIds)];
    const ownerIds: string[] = [];
    for (const fleetAssetId of allocatedFleetAssets) {
      const fleetAssetOwner = fleetAssetOwners.value.find(
        (x: FleetAssetOwnerSummary) =>
          x.associatedFleetAssets.includes(fleetAssetId),
      );
      if (fleetAssetOwner && !ownerIds.includes(fleetAssetOwner.ownerId)) {
        ownerIds.push(fleetAssetOwner.ownerId);
      }
    }
    return ownerIds.length;
  } else {
    return 0;
  }
});

// DriverDetailsSummary EntityType.DRIVER
const drivers: ComputedRef<DriverDetailsSummary[]> = computed(
  (): DriverDetailsSummary[] => {
    if (props.entityType !== EntityType.DRIVER) {
      return [];
    }
    return useDriverDetailsStore().getDriverList;
  },
);

const activeDrivers: ComputedRef<number> = computed((): number => {
  if (props.entityType !== EntityType.DRIVER) {
    return 0;
  }
  return drivers.value.filter(
    (x: DriverDetailsSummary) => x.statusList && x.statusList.includes(4),
  ).length;
});

const doNotUseDrivers: ComputedRef<number> = computed((): number => {
  if (props.entityType !== EntityType.DRIVER) {
    return 0;
  }
  return drivers.value.filter(
    (x: DriverDetailsSummary) => !x.statusList || x.statusList.includes(47),
  ).length;
});

const onlineDrivers: ComputedRef<number> = computed((): number => {
  if (props.entityType !== EntityType.DRIVER) {
    return 0;
  }
  const onlineDrivers: Map<string, DriverOnlineStatus> =
    useDriverAppStore().onlineDriverMap;
  if (!onlineDrivers || onlineDrivers.size === 0) {
    return 0;
  }
  // Return count of entries in map where isOnline is true
  return Array.from(onlineDrivers.values()).filter(
    (x: DriverOnlineStatus) => x.isOnline,
  ).length;
});

// FleetAssetOwnerSummary EntityType.FLEET_ASSET_OWNER
const fleetAssetOwners: ComputedRef<FleetAssetOwnerSummary[]> = computed(
  (): FleetAssetOwnerSummary[] => {
    if (props.entityType !== EntityType.FLEET_ASSET_OWNER) {
      return [];
    }
    return useFleetAssetOwnerStore().getOwnerList;
  },
);

const activeOwners: ComputedRef<number> = computed((): number => {
  if (props.entityType !== EntityType.FLEET_ASSET_OWNER) {
    return 0;
  }
  return fleetAssetOwners.value.filter((x: FleetAssetOwnerSummary) =>
    x.statusList.includes(4),
  ).length;
});

const doNotUseOwners: ComputedRef<number> = computed((): number => {
  if (props.entityType !== EntityType.FLEET_ASSET_OWNER) {
    return 0;
  }
  return fleetAssetOwners.value.filter((x: FleetAssetOwnerSummary) =>
    x.statusList.includes(47),
  ).length;
});
</script>

<style scoped lang="scss">
.subcontractor-summary-details {
  font-size: $font-size-14;
  font-family: $sub-font-family;

  &.tags-chip {
    padding: 8px 40px !important;
    border: 1px solid $border-color;
    background-color: transparent;
    color: var(--light-text-color);
  }
  .tags-chip-value {
    color: var(--text-color);
    padding-left: 4px;
    font-size: $font-size-16;
  }
}
</style>
