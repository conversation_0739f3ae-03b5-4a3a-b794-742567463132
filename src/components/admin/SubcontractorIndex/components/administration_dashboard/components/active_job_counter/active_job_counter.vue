<template>
  <v-layout>
    <v-flex m12>
      <v-layout
        justify-space-between
        align-center
        class="dashboard-card__toprow"
      >
        <v-flex pr-4>
          <v-layout>
            <span class="title-text">{{ longName }}</span>
          </v-layout>
          <v-layout>
            <span class="subtitle-text">{{ description }}</span>
          </v-layout>
        </v-flex>
        <span><v-icon class="arrow-icon">far fa-arrow-left</v-icon></span>
      </v-layout>
      <v-layout justify-start align-center class="dashboard-card__bottomrow">
        <div class="icon-container">
          <v-icon class="header-icon">{{ icon }}</v-icon>
        </div>
        <span class="item-count"> {{ currentJobs.length }} </span>
      </v-layout>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import { DashboardJobTableData } from '@/helpers/ExpirationSummaryHelpers/ExpirationSummaryHelpers';
import { OperationJobSummary } from '@/interface-models/Jobs/OperationJobSummary';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useJobStore } from '@/store/modules/JobStore';
import { computed, defineEmits, defineExpose, defineProps } from 'vue';

const props = defineProps<{
  longName?: string;
  description?: string;
  icon?: string;
}>();

const emit = defineEmits(['setTableData']);

// Expose tableData so we can push changes to it when a websocket update is received
defineExpose({
  emitTableData,
});

const jobStore = useJobStore();
const fleetAssetStore = useFleetAssetStore();
const driverDetailsStore = useDriverDetailsStore();
const fleetAssetOwnerStore = useFleetAssetOwnerStore();

const currentJobs = computed(() =>
  jobStore.operationJobsList.filter(
    (job: OperationJobSummary) => job.workStatus === WorkStatus.IN_PROGRESS,
  ),
);

function emitTableData() {
  const data: DashboardJobTableData[] = currentJobs.value.map((job) => {
    const driver = driverDetailsStore.getDriverFromDriverId(job.driverId);
    const fleetAsset = fleetAssetStore.getFleetAssetFromFleetAssetId(
      job.fleetAssetId,
    );
    const owner = fleetAsset
      ? fleetAssetOwnerStore.getOwnerFromOwnerId(fleetAsset.fleetAssetOwnerId)
      : null;

    return {
      jobId: job.jobId!,
      displayId: job.displayId,
      clientId: job.clientId,
      clientName: job.clientName,
      driverId: job.driverId,
      driverName: driver?.displayName ?? '',
      driverMob: driver ? driver.mobile : '',
      fleetAssetId: job.fleetAssetId,
      csrAssignedId: fleetAsset ? fleetAsset.csrAssignedId : '',
      owner: owner ? owner.name : '',
      ownerId: owner ? owner.ownerId : '',
      ownerMob: owner ? owner.mobile : '',
    };
  });
  emit('setTableData', data);
}
</script>
