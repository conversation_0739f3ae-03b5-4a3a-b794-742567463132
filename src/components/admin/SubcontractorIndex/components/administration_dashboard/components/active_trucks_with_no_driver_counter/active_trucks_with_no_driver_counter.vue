<template>
  <v-layout>
    <v-flex m12>
      <v-layout
        justify-space-between
        align-center
        class="dashboard-card__toprow"
      >
        <v-flex pr-4>
          <v-layout>
            <span class="title-text">{{ longName }}</span>
          </v-layout>
          <v-layout>
            <span class="subtitle-text">{{ description }}</span>
          </v-layout>
        </v-flex>
        <span><v-icon class="arrow-icon">far fa-arrow-left</v-icon></span>
      </v-layout>
      <v-layout justify-start align-center class="dashboard-card__bottomrow">
        <div class="icon-container">
          <v-icon class="header-icon">{{ icon }}</v-icon>
        </div>
        <span class="item-count"> {{ trucksWithNoDrivers.length }} </span>
      </v-layout>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import {
  DashboardTableData,
  mapEntityToTableData,
} from '@/helpers/ExpirationSummaryHelpers/ExpirationSummaryHelpers';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { computed, ComputedRef } from 'vue';

const props = defineProps<{
  longName?: string;
  description?: string;
  icon?: string;
}>();

const emit = defineEmits(['setTableData']);
const fleetAssetStore = useFleetAssetStore();

// Expose tableData so we can push changes to it when a websocket update is received
defineExpose({
  emitTableData,
});

/**
 * Returns a list of trucks rom the store that are ACTIVE (status list
 * includes 4) and have no associated drivers
 */
const trucksWithNoDrivers: ComputedRef<FleetAssetSummary[]> = computed(() => {
  return fleetAssetStore.getAllFleetAssetList.filter(
    (fleetAsset: FleetAssetSummary) =>
      fleetAsset.associatedDrivers.length === 0 &&
      fleetAsset.isActive &&
      fleetAsset.fleetAssetTypeId === 1,
  );
});

/**
 * Using the list trucksWithNoDrivers, maps them to a common table data object
 * DashboardTableData, such that we can emit it to the parent to be displayed
 */
function emitTableData() {
  const tableData: DashboardTableData[] = trucksWithNoDrivers.value
    .map((f) =>
      mapEntityToTableData({ type: EntityType.FLEET_ASSET, entity: f }),
    )
    .filter((c) => !!c) as DashboardTableData[];

  emit('setTableData', {
    entityType: EntityType.FLEET_ASSET,
    tableData,
  });
}
</script>
