<template>
  <v-layout>
    <v-flex m12>
      <v-layout
        justify-space-between
        align-center
        class="dashboard-card__toprow"
      >
        <v-flex pr-4>
          <v-layout>
            <span class="title-text">{{ longName }}</span>
          </v-layout>
          <v-layout>
            <span class="subtitle-text">{{ description }}</span>
          </v-layout>
        </v-flex>
        <span><v-icon class="arrow-icon">far fa-arrow-left</v-icon></span>
      </v-layout>
      <v-layout justify-start align-center class="dashboard-card__bottomrow">
        <div class="icon-container">
          <v-icon class="header-icon">{{ icon }}</v-icon>
        </div>
        <span class="item-count"> {{ disassociatedDrivers.length }} </span>
      </v-layout>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';

import {
  DashboardTableData,
  mapEntityToTableData,
} from '@/helpers/ExpirationSummaryHelpers/ExpirationSummaryHelpers';
import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { computed, ComputedRef } from 'vue';

const props = defineProps<{
  longName?: string;
  description?: string;
  icon?: string;
}>();

const emit = defineEmits(['setTableData']);

// Expose tableData so we can push changes to it when a websocket update is received
defineExpose({
  emitTableData,
});

/**
 * Returns an array of drivers who are not associated with any fleet asset
 * owner.
 * @returns {DriverDetailsSummary[]} The array of disassociated drivers.
 */
const disassociatedDrivers: ComputedRef<DriverDetailsSummary[]> = computed(
  () => {
    const allAssociatedIds = new Set(
      useFleetAssetOwnerStore().getOwnerList.flatMap(
        (o) => o.associatedDrivers,
      ),
    );
    return useDriverDetailsStore().getDriverList.filter(
      (driver: DriverDetailsSummary) => !allAssociatedIds.has(driver.driverId),
    );
  },
);
/**
 * Using the list of associated drivers, maps them to a common table
 * data object DashboardTableData, such that we can emit it to the parent to
 * be displayed
 */
function emitTableData() {
  const tableData: DashboardTableData[] = disassociatedDrivers.value
    .map((d) => mapEntityToTableData({ type: EntityType.DRIVER, entity: d }))
    .filter((c) => !!c) as DashboardTableData[];

  emit('setTableData', { entityType: EntityType.DRIVER, tableData });
}
</script>
