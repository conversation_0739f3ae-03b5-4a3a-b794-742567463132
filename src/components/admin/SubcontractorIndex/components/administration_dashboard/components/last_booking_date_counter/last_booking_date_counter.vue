<template>
  <v-layout>
    <v-flex m12>
      <v-layout
        justify-space-between
        align-center
        class="dashboard-card__toprow"
      >
        <v-flex pr-4>
          <v-layout>
            <span class="title-text">{{ longName }}</span>
          </v-layout>
          <v-layout>
            <span class="subtitle-text">{{ description }}</span>
          </v-layout>
        </v-flex>
        <span><v-icon class="arrow-icon">far fa-arrow-left</v-icon></span>
      </v-layout>
      <v-layout justify-start align-center class="dashboard-card__bottomrow">
        <div class="icon-container">
          <v-icon class="header-icon">{{ icon }}</v-icon>
        </div>
        <span class="item-count"> {{ lastBookingDates.length }} </span>
      </v-layout>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import {
  DashboardTableData,
  mapEntityToTableData,
} from '@/helpers/ExpirationSummaryHelpers/ExpirationSummaryHelpers';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { KeyValue } from '@/interface-models/Generic/KeyValue/KeyValue';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useRootStore } from '@/store/modules/RootStore';
import moment from 'moment-timezone';
import { computed, ComputedRef, onMounted, ref, Ref } from 'vue';

interface LastBookingDateRequest {
  entityType: EntityType;
  startDate: number;
  endDate: number;
}

const props = defineProps<{
  entityType: EntityType;
  longName?: string;
  description?: string;
  icon?: string;
}>();

const emit = defineEmits(['setTableData']);

// Expose tableData so we can push changes to it when a websocket update is received
defineExpose({
  emitTableData,
});

const fleetAssetStore = useFleetAssetStore();
const clientDetailsStore = useClientDetailsStore();
const driverDetailsStore = useDriverDetailsStore();

const isLoading: Ref<boolean> = ref(false);

// return the correct table data based on the components entity type.
const lastBookingDates: ComputedRef<KeyValue[]> = computed(() => {
  if (props.entityType === EntityType.CLIENT) {
    return clientDetailsStore.lastBookingDates;
  } else if (props.entityType === EntityType.DRIVER) {
    return driverDetailsStore.lastBookingDates;
  } else if (props.entityType === EntityType.FLEET_ASSET) {
    return fleetAssetStore.lastBookingDates;
  }
  return [];
});

/**
 * Using the data from the API response, maps the ids contained within to
 * their entity summary objects in the store. Then maps them to a common table
 * data object DashboardTableData, such that we can emit it to the parent to
 * be displayed
 */
function emitTableData() {
  let tableData: DashboardTableData[] = [];
  if (props.entityType === EntityType.CLIENT) {
    tableData = lastBookingDates.value
      .map((e: KeyValue) => {
        return clientDetailsStore.clientSummaryList.find(
          (c) => c.clientId === e.key,
        );
      })
      .map((c) => mapEntityToTableData({ type: EntityType.CLIENT, entity: c }))
      .filter((c) => !!c) as DashboardTableData[];
  } else if (props.entityType === EntityType.DRIVER) {
    tableData = lastBookingDates.value
      .map((e: KeyValue) => driverDetailsStore.getDriverFromDriverId(e.key))
      .map((d) => mapEntityToTableData({ type: EntityType.DRIVER, entity: d }))
      .filter((c) => !!c) as DashboardTableData[];
  } else if (props.entityType === EntityType.FLEET_ASSET) {
    tableData = lastBookingDates.value
      .map((e: KeyValue) =>
        fleetAssetStore.getFleetAssetFromFleetAssetId(e.key),
      )
      .map((f) =>
        mapEntityToTableData({ type: EntityType.FLEET_ASSET, entity: f }),
      )
      .filter((c) => !!c) as DashboardTableData[];
  }
  emit('setTableData', { entityType: props.entityType, tableData });
}

// Sends request to get the entity types last job dates.
async function getLastBookingDate() {
  //  We don't want to make duplicated requests if the data already exists.
  //  check for data and return if condition is met.
  if (
    (props.entityType === EntityType.CLIENT &&
      clientDetailsStore.lastBookingDates.length > 0) ||
    (props.entityType === EntityType.FLEET_ASSET &&
      fleetAssetStore.lastBookingDates.length > 0) ||
    (props.entityType === EntityType.DRIVER &&
      driverDetailsStore.lastBookingDates.length > 0)
  ) {
    return;
  }
  const companyDetailsStore = useCompanyDetailsStore();
  const request: LastBookingDateRequest = {
    entityType: props.entityType,
    startDate: moment()
      .tz(companyDetailsStore.userLocale)
      .startOf('day')
      .subtract(3, 'months')
      .valueOf(),
    endDate: moment().tz(companyDetailsStore.userLocale).endOf('day').valueOf(),
  };
  isLoading.value = true;
  const response = await useRootStore().requestLastBookingDates(request);
  setLastBookingDateResponse(response);
}
// response handler. Sets last booking dates to entity store module.
function setLastBookingDateResponse(lastBookingDates: KeyValue[] | null) {
  if (!lastBookingDates) {
    isLoading.value = false;
    return;
  }
  if (props.entityType === EntityType.CLIENT) {
    clientDetailsStore.setLastBookingDates(
      setClientLastBookingDates(lastBookingDates),
    );
  } else if (props.entityType === EntityType.DRIVER) {
    driverDetailsStore.setLastBookingDates(
      setDriverLastBookingDates(lastBookingDates),
    );
  } else if (props.entityType === EntityType.FLEET_ASSET) {
    fleetAssetStore.setLastBookingDates(
      setFleetAssetLastBookingDates(lastBookingDates),
    );
  }
  isLoading.value = false;
}

// compare and set client list with last booking date data. Return data that will be displayed in the table
function setClientLastBookingDates(data: KeyValue[]): KeyValue[] {
  const lastBookingDates: KeyValue[] = [];
  for (const client of clientDetailsStore.clientSummaryList) {
    if (client.statusList.includes(13) || client.statusList.includes(47)) {
      continue;
    }
    const foundData = data.find((x: KeyValue) => x.key === client.clientId);
    if (foundData) {
      continue;
    }
    lastBookingDates.push({
      key: client.clientId,
      value: client.clientDisplayName,
    });
  }
  return lastBookingDates;
}
// compare and set driver list with last booking date data. Return data that
// will be displayed in the table
function setDriverLastBookingDates(data: KeyValue[]): KeyValue[] {
  const lastBookingDates: KeyValue[] = [];
  for (const driver of driverDetailsStore.getDriverList) {
    if (driver.statusList.includes(13) || driver.statusList.includes(47)) {
      continue;
    }
    const foundData = data.find((x: KeyValue) => x.key === driver.driverId);
    if (foundData) {
      continue;
    }
    lastBookingDates.push({ key: driver.driverId, value: driver.displayName });
  }
  return lastBookingDates;
}

// compare and set fleet asset list with last booking date data. Return data
// that will be displayed in the table
function setFleetAssetLastBookingDates(data: KeyValue[]): KeyValue[] {
  const lastBookingDates: KeyValue[] = [];
  // Iterate over active fleet assets. If an active fleet asset is NOT in the
  // response payload, then we add it to the lastBookingDates array.
  for (const fleetAsset of fleetAssetStore.getAllFleetAssetList) {
    if (
      fleetAsset.statusList.includes(13) ||
      fleetAsset.statusList.includes(47) ||
      fleetAsset.fleetAssetTypeId !== 1
    ) {
      continue;
    }
    // If the fleet asset is in the response payload, then we skip it.
    const foundData = data.find(
      (x: KeyValue) => x.key === fleetAsset.fleetAssetId,
    );
    if (foundData) {
      continue;
    }
    lastBookingDates.push({
      key: fleetAsset.fleetAssetId,
      value: fleetAsset.registrationNumber,
    });
  }
  return lastBookingDates;
}

onMounted(() => {
  getLastBookingDate();
});
</script>
