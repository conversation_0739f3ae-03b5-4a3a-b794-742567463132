import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import StatusConfig from '@/interface-models/Status/StatusConfig';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useRootStore } from '@/store/modules/RootStore';
import { Component, Vue } from 'vue-property-decorator';
interface DriverTableData {
  driverId: string;
  name: string;
  associatedFleetAssets: string;
  status: string;
}

@Component({
  components: { InformationTooltip },
})
export default class DisassociatedDriverTable extends Vue {
  public driverDetailsStore = useDriverDetailsStore();

  get driverTableList(): DriverTableData[] {
    const fleetAssetStore = useFleetAssetStore();
    const allAssociatedIds = new Set(
      useFleetAssetOwnerStore().getOwnerList.flatMap(
        (o) => o.associatedDrivers,
      ),
    );
    const disassociatedDrivers: DriverDetailsSummary[] =
      this.driverDetailsStore.getDriverList.filter(
        (driver: DriverDetailsSummary) =>
          !allAssociatedIds.has(driver.driverId),
      );

    return disassociatedDrivers.map((x: DriverDetailsSummary) => {
      const associatedFleetAssets: string = fleetAssetStore.getAllFleetAssetList
        .filter((fa: FleetAssetSummary) =>
          fa.associatedDrivers.includes(x.driverId),
        )
        .map((fam: FleetAssetSummary) => fam.csrAssignedId)
        .join(', ');

      const status: string = x.statusList
        .map((statusId: number) => {
          const foundStatus: StatusConfig | undefined =
            useRootStore().statusTypeList.find(
              (statusConfig: StatusConfig) => statusConfig.sid === statusId,
            );
          return foundStatus ? foundStatus.text : '-';
        })
        .join(', ');

      return {
        driverId: x.driverId,
        name: x.displayName,
        associatedFleetAssets: associatedFleetAssets
          ? associatedFleetAssets
          : '-',
        status: status ? status : '-',
      };
    });
  }

  public headers: TableHeader[] = [
    {
      text: 'Name',
      align: 'left',
      sortable: true,
      value: 'name',
    },
    {
      text: 'Associated Assets',
      align: 'left',
      sortable: true,
      value: 'associatedFleetAssets',
    },
    {
      text: 'Status',
      align: 'left',
      sortable: true,
      value: 'status',
    },
  ];

  public viewDriverDetails(driver: DriverTableData): void {
    this.driverDetailsStore.setSelectedDriverDetailsId(driver.driverId);
    this.$router.push({
      name: 'Driver',
      params: {
        name: driver.name.toLowerCase().replace(/ /g, '-'),
        id: driver.driverId,
      },
    });
  }
}
