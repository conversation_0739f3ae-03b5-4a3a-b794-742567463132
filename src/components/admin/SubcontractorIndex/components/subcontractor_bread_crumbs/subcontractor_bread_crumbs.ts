import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { Component, Vue } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class SubcontractorBreadCrumbs extends Vue {
  get breadCrumbs() {
    if (!this.$route.name) {
      return [];
    }
    const name =
      this.$route.params.id === 'new'
        ? 'New ' + this.$route.name
        : this.getbreadCrumbEntityName(this.$route.name);

    return [
      {
        id: 1,
        name: 'Subcontractor',
        show: true,
      },
      {
        id: 2,
        name: this.$route.name,
        show: this.$route.name !== 'Subcontractor',
      },
      {
        id: 3,
        name,
        show: this.$route.name !== 'Subcontractor',
      },
    ];
  }

  public getbreadCrumbEntityName(routeName: string): string {
    const fleetAssetStore = useFleetAssetStore();
    try {
      if (routeName === 'Driver') {
        const driverDetailsSummary: DriverDetailsSummary | undefined =
          useDriverDetailsStore().getDriverList.find(
            (x: DriverDetailsSummary) => x.driverId === this.$route.params.id,
          );
        if (driverDetailsSummary) {
          return driverDetailsSummary.displayName;
        }
      } else if (routeName === 'Truck' || routeName === 'Trailer') {
        const fleetAssetSummary: FleetAssetSummary | undefined =
          fleetAssetStore.getAllFleetAssetList.find(
            (x: FleetAssetSummary) => x.fleetAssetId === this.$route.params.id,
          );
        if (fleetAssetSummary) {
          return fleetAssetSummary.csrAssignedId;
        }
      } else if (routeName === 'Owner') {
        const fleetAssetOwnerSummary: FleetAssetOwnerSummary | undefined =
          useFleetAssetOwnerStore().getOwnerList.find(
            (x: FleetAssetOwnerSummary) => x.ownerId === this.$route.params.id,
          );
        if (fleetAssetOwnerSummary) {
          return fleetAssetOwnerSummary.name;
        }
      }
      return '';
    } catch (e) {
      console.error(e);
      return '';
    }
  }

  public goToSubcontractorHome(id: number) {
    if (id !== 1 || this.$route.name === 'Subcontractor') {
      return;
    }
    this.$router.push('/subcontractor');
  }
}
