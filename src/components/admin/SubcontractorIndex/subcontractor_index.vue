<template>
  <div class="subcontractor_index_container">
    <v-layout wrap class="pl-2 py-2">
      <v-flex>
        <v-form>
          <SelectEntity
            :isRouteSelect="true"
            :entityTypes="[
              entityType.FLEET_ASSET_OWNER,
              entityType.FLEET_ASSET,
              entityType.DRIVER,
            ]"
          />
        </v-form>
      </v-flex>

      <v-menu right>
        <template v-slot:activator="{ on }">
          <v-btn flat icon v-on="on" class="add-btn">
            <v-icon size="22">far fa-plus</v-icon>
          </v-btn>
        </template>
        <v-list class="v-list-custom" dense>
          <v-list-tile @click="addNewFleetAssetOwner">
            <v-list-tile-title>Create New Fleet Asset Owner</v-list-tile-title>
          </v-list-tile>

          <v-list-tile @click="addNewDriver">
            <v-list-tile-title>Create New Driver</v-list-tile-title>
          </v-list-tile>
        </v-list>
      </v-menu>
    </v-layout>

    <AdministrationDashboard category="SUBCONTRACTOR"></AdministrationDashboard>
  </div>
</template>

<script setup lang="ts">
import AdministrationDashboard from '@/components/admin/SubcontractorIndex/components/administration_dashboard/index.vue';
import SelectEntity from '@/components/common/inputs/select_entity/index.vue';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { useRouter } from 'vue-router/composables';

const router = useRouter();

const entityType = EntityType;

/**
 * Navigate to the Driver route to create a new Driver
 */
function addNewDriver() {
  router.push({
    name: 'Driver',
    params: {
      name: 'create',
      id: 'new',
    },
  });
}

/**
 * Navigate to the Fleet Asset Owner route to create a new owner
 */
function addNewFleetAssetOwner() {
  router.push({
    name: 'Owner',
    params: {
      name: 'create',
      id: 'new',
    },
  });
}
</script>

<style scoped lang="scss">
.add-btn {
  border-radius: 50px !important;
  border: 1px solid var(--primary-dark) !important;
  background-color: var(--primary) !important;
}
</style>
