<template>
  <div class="fleet-asset-trailer-specifications">
    <v-layout wrap>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">
            1. Weight / Capacity Specifications
          </h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Trailer Type</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
              :disabled="!isEdited"
              :items="trailerTypes"
              item-text="longName"
              item-value="id"
              v-model="fleetAsset.fleetAssetTypeObject.trailerType"
            >
            </v-select>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                Gross Trailer Mass (GTM)
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.gvm"
              :disabled="!isEdited"
              solo
              flat
              hint="The maximum axle load this trailer is designed to carry as specified by the manufacturer"
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Tare Weight (kg)</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.tare"
              hint="The weight of the empty trailer"
              :disabled="!isEdited"
              solo
              flat
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Payload Weight (kg)</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.payload"
              hint="The maximum load this vehicle can carry as specified by the manufacturer"
              :disabled="!isEdited"
              solo
              flat
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Pallet Capacity</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.numberOfPallets"
              hint="Number of pallets this trailer can hold"
              :disabled="!isEdited"
              solo
              flat
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">2. Tray Dimensions</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Tray Height (m)</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.trailerDimensions.height"
              :disabled="!isEdited"
              solo
              flat
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Tray Width (m)</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.trailerDimensions.width"
              :disabled="!isEdited"
              solo
              flat
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Tray Length (m)</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.trailerDimensions.length"
              :disabled="!isEdited"
              solo
              flat
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">3. Other Info</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Number of Axles</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.axels"
              :disabled="!isEdited"
              solo
              flat
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import FleetAsset from '@/interface-models/FleetAsset/FleetAsset';
import TrailerDetails from '@/interface-models/FleetAsset/models/FleetAssetTypes/Trailer/TrailerDetails';
import { trailerTypes } from '@/interface-models/Generic/TrailerTypes/TrailerTypes';

const props = withDefaults(
  defineProps<{
    fleetAsset: FleetAsset & { fleetAssetTypeObject: TrailerDetails };
    isEdited: boolean;
  }>(),
  {
    isEdited: false,
  },
);
</script>

<style scoped lang="scss">
.fleet-asset-trailer-specifications {
  .label-container {
    height: 48px;
  }
}
</style>
