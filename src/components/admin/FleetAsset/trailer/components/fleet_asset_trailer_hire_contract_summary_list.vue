<template>
  <v-layout class="hire_contract_summary_list_container" wrap>
    <v-flex md12 pb-3
      ><v-layout align-center>
        <h5 class="subheader--bold pr-3 pt-1">Existing Contracts</h5>
        <v-flex>
          <v-divider></v-divider>
        </v-flex>
      </v-layout>
    </v-flex>
    <v-flex md12>
      <v-data-table
        :headers="tableHeaders"
        class="accounting-data-table"
        :items="summaryList"
        :loading="isLoadingTableData"
        hide-actions
      >
        <v-progress-linear
          model-value="progress"
          color="#ffa000"
          indeterminate
        ></v-progress-linear>
        <template v-slot:items="props">
          <tr
            class="table-row"
            style="cursor: pointer"
            @click="getContractById(props.item._id)"
            :class="{
              activeSelectedRate:
                currentActiveContractId !== null &&
                props.item._id === currentActiveContractId,
            }"
          >
            <td>{{ props.item.documentNumber }}</td>
            <td>{{ props.item.contractNumber }}</td>
            <td>{{ props.item.readableValidFromDate }}</td>
            <td>{{ props.item.readableValidToDate }}</td>
            <td>
              <span
                class="status-container"
                :class="
                  props.item.isActive
                    ? 'success active-content'
                    : 'error inactive-content'
                "
              ></span>
            </td>
          </tr>
        </template>
      </v-data-table>
      <v-layout justify-center>
        <Pagination
          @pageIncrement="pageIncrement"
          :pagination="pagination"
          @change="getHireContractSummaryPaginationList"
          :rowsPerPage.sync="rowsPerPage"
        />
      </v-layout>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
interface HireContractTable {
  _id: string;
  fleetAssetId: string;
  documentNumber: string;
  contractNumber: string;
  validFromDate: number;
  validToDate: number;
  readableValidFromDate: string;
  readableValidToDate: string;
  isActive: boolean;
}

import Pagination from '@/components/common/pagination/index.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { HireContract } from '@/interface-models/FleetAsset/EquipmentHire/HireContract';
import { HireContractSummary } from '@/interface-models/FleetAsset/EquipmentHire/HireContractSummary';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { VPagination } from '@/interface-models/Generic/VPagination';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import moment from 'moment-timezone';
import { ComputedRef, Ref, computed, onMounted, ref } from 'vue';

interface HireContractTable {
  _id: string;
  fleetAssetId: string;
  documentNumber: string;
  contractNumber: string;
  validFromDate: number;
  validToDate: number;
  readableValidFromDate: string;
  readableValidToDate: string;
  isActive: boolean;
}

const props = withDefaults(
  defineProps<{
    fleetAssetId: string;
  }>(),
  {
    fleetAssetId: '',
  },
);

const emit = defineEmits<{
  (event: 'selectHireContract', payload: HireContract): void;
}>();

const fleetAssetStore = useFleetAssetStore();
const page: Ref<number> = ref(1);
const rowsPerPage: Ref<number> = ref(10);

const isLoadingTableData: Ref<boolean> = ref(false);
const resultCount: Ref<number> = ref(0);
const summaryList: Ref<HireContractTable[]> = ref([]);

// Format date
const formatDate = (date: number) => returnFormattedDate(date);

// Table headers
const tableHeaders: TableHeader[] = [
  {
    text: 'Document Number',
    align: 'left',
    sortable: false,
    value: 'documentNumber',
  },
  {
    text: 'Contract Number',
    align: 'left',
    sortable: false,
    value: 'contractNumber',
  },
  {
    text: 'Valid From',
    align: 'left',
    sortable: false,
    value: 'validFromDate',
  },
  {
    text: 'Valid To',
    align: 'left',
    sortable: false,
    value: 'validToDate',
  },
  {
    text: 'Status',
    align: 'left',
    sortable: false,
    value: 'status',
  },
];

const pagination: ComputedRef<VPagination> = computed(() => ({
  descending: true,
  page: page.value,
  rowsPerPage: rowsPerPage.value,
  totalItems: resultCount.value,
}));

function pageIncrement(value: number) {
  page.value += value;
  getHireContractSummaryPaginationList();
}

/**
 * Fetches a paginated list of HireContractSummary objects by fleetAssetId. Maps
 * the response to HireContractSummary format such that it can be displayed in
 * the data table in the template.
 */
async function getHireContractSummaryPaginationList() {
  isLoadingTableData.value = true;
  const result = await fleetAssetStore.requestHireContractsByFleetAssetId(
    props.fleetAssetId,
    page.value,
    rowsPerPage.value,
  );
  isLoadingTableData.value = false;
  if (result) {
    resultCount.value = result.count;
    summaryList.value = result.hireContractSummaryList
      .sort(
        (a: HireContractSummary, b: HireContractSummary) =>
          b.validToDate - a.validToDate,
      )
      .map((x: HireContractSummary) => ({
        _id: x._id,
        fleetAssetId: x.fleetAssetId,
        documentNumber: x.documentNumber || '-',
        contractNumber: x.contractNumber || '-',
        validFromDate: x.validFromDate,
        validToDate: x.validToDate,
        readableValidFromDate: x.validFromDate
          ? formatDate(x.validFromDate)
          : '-',
        readableValidToDate: x.validToDate ? formatDate(x.validToDate) : '-',
        isActive: x.statusList.includes(4) ? true : false,
      }));
  }
}

/**
 * Computed property that returns the current active contract id. This is used
 * to highlight the active contract in the table.
 */
const currentActiveContractId: ComputedRef<string | null> = computed(() => {
  const currentDate = moment().valueOf();
  const activeContract = fleetAssetStore.hireContractSummaryList.find(
    (x: HireContractSummary) =>
      x.validFromDate <= currentDate && x.validToDate >= currentDate,
  );
  return activeContract ? activeContract._id : null;
});

/**
 * Fetches a HireContract by its mongo id. Called when the user selects a table
 * item in the template. Emits the result to the parent.
 * @param id The HireContract mongo id
 */
async function getContractById(id: string) {
  const contract = await fleetAssetStore.requestHireContractById(id);
  if (!contract) {
    showNotification(
      `${GENERIC_ERROR_MESSAGE} The selected contract could not be found.`,
    );
    return;
  }
  emit('selectHireContract', contract);
}

// Lifecycle hook
onMounted(() => {
  getHireContractSummaryPaginationList();
});
</script>

<style scoped lang="scss">
.status-container {
  padding: 2px 8px;
  border-radius: $border-radius-base;
  letter-spacing: 0.03em;
  font-size: $font-size-13;
  font-weight: 500;
}

.add-asset-btn {
  position: absolute;
  top: 1px;
  right: 10px;
  z-index: 199 !important;
}

.alert-border {
  border-top: 1px solid red;
}
</style>
