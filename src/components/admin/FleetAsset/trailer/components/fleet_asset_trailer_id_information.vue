<template>
  <div class="fleet-asset-trailer-identifying-information">
    <v-layout wrap>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">Identifying Information</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                Vehicle Identification Number (VIN)
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.vinNumber"
              :disabled="!isEdited"
              solo
              flat
              autofocus
            />
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Trailer Make</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              :disabled="!isEdited"
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              solo
              flat
              v-model="fleetAsset.fleetAssetTypeObject.make"
              :items="trailerMakes"
              item-value="make"
              item-text="make"
            >
            </v-select>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Trailer Model</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :disabled="!isEdited"
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              solo
              flat
              v-model="fleetAsset.fleetAssetTypeObject.model"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Date of Purchase</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <DateTimeInputs
              v-model="fleetAsset.fleetAssetTypeObject.dateOfPurchase"
              :epochTime.sync="fleetAsset.fleetAssetTypeObject.dateOfPurchase"
              :enableValidation="true"
              :type="DateTimeType.DATE_START_OF_DAY"
              dateLabel="Date of Purchase"
              :readOnly="!isEdited"
              :soloInput="true"
              :boxInput="false"
              :hintTextType="HintTextType.FORMATTED_SELECTION"
            ></DateTimeInputs>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Trailer Description</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-textarea
              :disabled="!isEdited"
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              solo
              flat
              v-model="fleetAsset.fleetAssetTypeObject.description"
            ></v-textarea>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import DateTimeInputs, {
  DateTimeType,
  HintTextType,
} from '@/components/common/date-picker/date_time_inputs.vue';
import FleetAsset from '@/interface-models/FleetAsset/FleetAsset';
import TrailerDetails from '@/interface-models/FleetAsset/models/FleetAssetTypes/Trailer/TrailerDetails';
import TrailerMakes from '@/static/staticData/trailer_makes.json';
import { ref } from 'vue';

const props = withDefaults(
  defineProps<{
    fleetAsset: FleetAsset & { fleetAssetTypeObject: TrailerDetails };
    isEdited: boolean;
  }>(),
  {
    isEdited: false,
  },
);

const trailerMakes = ref(TrailerMakes);
</script>

<style scoped lang="scss">
.fleet-asset-trailer-identifying-information {
  padding: 0;

  .label-container {
    height: 48px;
  }
}
</style>
