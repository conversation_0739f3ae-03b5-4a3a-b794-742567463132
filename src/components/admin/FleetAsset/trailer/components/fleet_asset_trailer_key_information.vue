<template>
  <div class="fleet-asset-trailer-key-information">
    <v-layout wrap>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">1. Id & Status</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Trailer ID
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              solo
              flat
              class="v-solo-custom"
              v-model="fleetAsset.csrAssignedId"
              :disabled="!isEdited"
              :rules="[validate.required]"
              autofocus
              color="light-blue"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Active Status
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <StatusSelect
              :key="statusListKey"
              :statusCategory="8"
              :boxInput="false"
              :soloInput="true"
              :statusList="fleetAsset.statusList"
              :resetSelectedSecondaryStatus="false"
              :formDisabled="!isEdited"
            >
            </StatusSelect>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Contract Type
              </h6>
            </v-layout>
          </v-flex>

          <v-flex md8>
            <v-select
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              solo
              flat
              class="v-solo-custom"
              :disabled="!isEdited"
              :items="contractTypes"
              item-value="id"
              item-text="text"
              :rules="[validate.required]"
              v-model="fleetAsset.fleetAssetTypeObject.contractType"
              color="light-blue"
            >
            </v-select>
          </v-flex> </v-layout
      ></v-flex>

      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">2. Registration Information</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Vehicle Registration Number
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              solo
              flat
              class="v-solo-custom"
              color="light-blue"
              :disabled="!isEdited"
              :rules="[validate.required]"
              v-model="
                fleetAsset.fleetAssetTypeObject.registrationDetails
                  .registrationNumber
              "
            /> </v-flex></v-layout
      ></v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                State of Registration
              </h6>
            </v-layout>
          </v-flex>

          <v-flex md8>
            <v-select
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              solo
              flat
              class="v-solo-custom"
              :disabled="!isEdited"
              :items="stateOfRegistrationSelector"
              item-value="stateShortName"
              item-text="stateLongName"
              :rules="[validate.required]"
              v-model="
                fleetAsset.fleetAssetTypeObject.registrationDetails
                  .stateOfRegistration
              "
              color="light-blue"
            >
            </v-select>
          </v-flex> </v-layout
      ></v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Registration Expiry
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <DateTimeInputs
              :epochTime.sync="
                fleetAsset.fleetAssetTypeObject.registrationDetails.expiry
              "
              :enableValidation="true"
              :type="DateTimeType.DATE_START_OF_DAY"
              dateLabel="Registration Expiry"
              :readOnly="!isEdited"
              :soloInput="true"
              :boxInput="false"
              :hintTextType="HintTextType.FORMATTED_SELECTION"
              :isRequired="true"
            ></DateTimeInputs>
          </v-flex> </v-layout
      ></v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">3. Insurance</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Insurance Policies</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              :disabled="!isEdited"
              label="Insurance Policies"
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              solo
              flat
              class="v-solo-custom"
              :items="selectablePolicies"
              item-text="key"
              item-value="value"
              multiple
              color="light-blue"
              v-model="fleetAsset.insurances"
            >
            </v-select>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import DateTimeInputs, {
  DateTimeType,
  HintTextType,
} from '@/components/common/date-picker/date_time_inputs.vue';
import StatusSelect from '@/components/common/status_select.vue';
import { returnPolicyNameFromId } from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { HireContractType } from '@/interface-models/FleetAsset/EquipmentHire/HireContractType';
import FleetAsset from '@/interface-models/FleetAsset/FleetAsset';
import TrailerDetails from '@/interface-models/FleetAsset/models/FleetAssetTypes/Trailer/TrailerDetails';
import FleetAssetOwner from '@/interface-models/FleetAssetOwner/FleetAssetOwner';
import { SubcontractorAssociationUpdateResponse } from '@/interface-models/FleetAssetOwner/SubcontractorAssociation/SubcontractorAssociationUpdateResponse';
import { KeyValue } from '@/interface-models/Generic/KeyValue/KeyValue';
import { Validation } from '@/interface-models/Generic/Validation';
import { OperationStatus } from '@/interface-models/Generic/WebSocketRequest/OperationStatus';
import { SubcontractorEntityType } from '@/interface-models/InvoiceAdjustment/EntityTypes/SubcontractorEntityType';
import StateRegistrationSelectorDataAU from '@/static/staticData/state_registrationAU.json';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useMittListener } from '@/utils/useMittListener';
import moment from 'moment-timezone';
import {
  ComputedRef,
  computed,
  defineProps,
  onMounted,
  ref,
  withDefaults,
} from 'vue';

interface HireContractTypeSelect {
  id: HireContractType;
  text: string;
}

const props = withDefaults(
  defineProps<{
    fleetAsset: FleetAsset & { fleetAssetTypeObject: TrailerDetails };
    isEdited: boolean;
  }>(),
  {
    isEdited: false,
  },
);

const contractTypes: HireContractTypeSelect[] = [
  {
    id: HireContractType.ADHOC,
    text: 'Adhoc',
  },
  {
    id: HireContractType.PERMANENT,
    text: 'Permanent',
  },
];

// Changing FleetAssetOwner to Null will need to update FleetAssetOwner Object Type as Truck | Trailer
const fleetAssetOwner = ref<FleetAssetOwner | any>(null);

const stateOfRegistrationSelector = StateRegistrationSelectorDataAU;

const validate: ComputedRef<Validation> = computed(() => validationRules);

/**
 * Request and response for requesting a FleetAssetOwner object by its owner
 * ID.*
 * @param ownerId - The owner ID of the FleetAssetOwner object to be
 * retrieved.
 * @returns FleetAssetOwner if the request was successful, otherwise null.
 */
async function requestFleetAssetOwnerDetails() {
  const result = await useFleetAssetOwnerStore().requestOwnerDetailsByOwnerId(
    props.fleetAsset.fleetAssetOwnerId,
  );
  fleetAssetOwner.value = result;
}

// Computed property for selectable insurance policies
const selectablePolicies: ComputedRef<KeyValue[]> = computed(() => {
  const currentTime = moment().valueOf();
  const policies: KeyValue[] = [];
  if (fleetAssetOwner.value) {
    for (const policy of fleetAssetOwner.value.insurances) {
      const isExpired =
        policy.validToDate !== null && policy.validToDate < currentTime;
      const isRetired = policy.isRetired;
      const value = policy.insuranceId;
      const key =
        policy.insurerName +
        ' - ' +
        returnPolicyNameFromId(policy.policyType) +
        ' - ' +
        policy.policyNumber +
        ' ' +
        (isRetired ? '(Retired)' : isExpired ? '(Expired)' : '');
      policies.push({ value, key });
    }
  }
  return policies;
});

/**
 * Used as key for StatusSelect component to force re-render when statusList is
 * updated
 */
const statusListKey: ComputedRef<string> = computed(() => {
  return `${props.fleetAsset.statusList}`;
});

/**
 * Handles response to status update. Used to update the statusList in the
 * fleetAsset prop.
 * @param response - contains properties from updated document, including the
 * updated statusList which we'll set to props.fleetAsset
 */
function handleSubcontractorEntityUpdate(
  response: SubcontractorAssociationUpdateResponse | null,
): void {
  if (
    response?.fleetAssetId === props.fleetAsset.fleetAssetId &&
    response.entityType === SubcontractorEntityType.EQUIPMENT_HIRE &&
    response.operationStatus === OperationStatus.SUCCESS &&
    !!response.statusList
  ) {
    props.fleetAsset.statusList = response.statusList;
  }
}

useMittListener(
  'updateSubcontractorEntityAssociationResponse',
  handleSubcontractorEntityUpdate,
);

onMounted(() => {
  requestFleetAssetOwnerDetails();
});
</script>

<style scoped lang="scss">
.fleet-asset-trailer-key-information {
  .label-container {
    height: 48px;
  }
}
</style>
