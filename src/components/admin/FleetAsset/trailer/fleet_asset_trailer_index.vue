<template>
  <v-layout
    wrap
    v-if="fleetAssetDetails && (fleetAssetOwner || isNewFleetAsset)"
    class="fleet-asset-trailer-index"
  >
    <v-flex md12 v-if="isDialog">
      <v-layout
        justify-space-between
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span><span v-if="isDialog">Trailer - </span>{{ fleetAssetName }}</span>

        <div
          v-if="isDialog"
          class="app-theme__center-content--closebutton"
          @click="closeFleetAssetDialog"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
    </v-flex>
    <v-flex md12>
      <v-layout
        class="app-theme__center-content--body dialog-content"
        row
        wrap
        :class="isDialog ? 'main-content-dialog' : 'main-content-route'"
      >
        <v-flex
          lg3
          md4
          class="side-column app-bgcolor--400 app-bordercolor--600 app-borderside--r-shadow"
        >
          <v-layout row wrap>
            <v-flex
              md12
              class="app-bgcolor--250 app-bordercolor--600 app-borderside--b"
            >
              <v-layout row wrap py-2 px-2>
                <v-flex md12>
                  <v-form>
                    <SelectEntity
                      :key="fleetAssetDetails.fleetAssetId"
                      :entityTypes="[
                        entityType.FLEET_ASSET_OWNER,
                        entityType.FLEET_ASSET,
                        entityType.DRIVER,
                      ]"
                      v-if="!isDialog"
                      :isRouteSelect="true"
                      :disabled="isEdited || !fleetAssetDetails._id"
                      class="mb-3"
                    />
                  </v-form>
                </v-flex>
                <v-flex md12 class="side-column__summaryitem">
                  <v-layout justify-space-between align-start>
                    <span class="side-column__summaryitem--key"> Status </span>
                    <span
                      class="side-column__summaryitem--value status-container"
                      :class="currentStatus.color"
                    >
                      {{ currentStatus.statusName }}
                    </span>
                  </v-layout>
                </v-flex>
                <v-flex
                  md12
                  class="side-column__summaryitem"
                  v-for="infoItem in summaryInfoList"
                  :key="infoItem.id"
                >
                  <v-layout
                    v-if="summaryInfoList"
                    justify-space-between
                    align-start
                  >
                    <span class="side-column__summaryitem--key">
                      {{ infoItem.title }}
                    </span>
                    <span
                      v-if="infoItem.id !== 'subcontractorName'"
                      class="side-column__summaryitem--value"
                    >
                      {{ infoItem.value }}
                    </span>

                    <span
                      v-if="infoItem.id === 'subcontractorName'"
                      class="side-column__summaryitem--value"
                    >
                      <b
                        v-if="!isDialog"
                        @click="viewFleetAssetOwner"
                        style="cursor: pointer; color: #2196f3"
                        >{{ infoItem.value }}</b
                      >
                      <b v-if="isDialog">{{ infoItem.value }}</b>
                    </span>
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>

            <v-flex md12 v-for="menuOption in menuOptions" :key="menuOption.id">
              <v-layout row wrap>
                <v-flex md12 class="app-bordercolor--600 app-borderside--b">
                  <v-layout>
                    <h5 class="subheader--bold--12 px-3 pt-3 pb-1">
                      {{ menuOption.title }}
                    </h5>
                  </v-layout>
                </v-flex>
                <v-flex
                  md12
                  v-for="menuItem in menuOption.items"
                  :key="menuItem.id"
                  class="app-bgcolor--300 app-bordercolor--600 app-borderside--b side-column__button"
                  :class="[
                    selectedViewType === menuItem.id ? 'active-state' : '',
                    menuItem.isActive
                      ? 'menu-item-selectable'
                      : 'menu-item-disabled',
                  ]"
                  @click="setSelectedView(menuItem.id)"
                >
                  <v-layout align-center>
                    <span class="button-label"
                      ><span class="pr-2">-</span>{{ menuItem.title }}</span
                    >
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex lg9 md8 class="dialog-content__scrollable">
          <v-layout
            class="dialog-toolbar center-section__top-toolbar"
            style="position: relative"
            align-center
          >
            <v-btn
              depressed
              v-if="isEdited"
              @click="cancelEdit"
              outline
              color="error"
              small
            >
              Cancel
            </v-btn>
            <v-btn
              depressed
              v-if="!isEdited && !isDialog"
              @click="goToSubcontractorIndexRoute"
              outline
              color="error"
              small
            >
              exit
            </v-btn>

            <v-btn
              :disabled="!isAuthorised()"
              depressed
              color="blue"
              small
              v-if="selectedViewType === 'CON' && hireContract && !isEdited"
              @click="hireContract = null"
            >
              back
            </v-btn>

            <v-spacer />
            <v-btn
              :disabled="!isAuthorised()"
              depressed
              color="blue"
              small
              v-if="
                (!isEdited && isForm && selectedViewType !== 'CON') ||
                (!isEdited &&
                  isForm &&
                  selectedViewType === 'CON' &&
                  hireContract)
              "
              @click="editFleetAssetDetails"
            >
              Edit
            </v-btn>
            <v-btn
              :disabled="!isAuthorised()"
              depressed
              color="blue"
              small
              v-else-if="
                !isEdited &&
                !isForm &&
                selectedViewType === 'CON' &&
                !hireContract
              "
              @click="initialiseNewHireContract"
            >
              Create New Contract
            </v-btn>
            <v-btn
              v-else-if="isEdited && selectedViewType !== 'CON'"
              depressed
              color="blue"
              small
              :loading="isAwaitingFleetAssetResponse"
              @click="saveFleetAssetDetails"
            >
              Save
            </v-btn>
            <v-btn
              v-else-if="isEdited && selectedViewType === 'CON'"
              depressed
              color="blue"
              small
              :loading="isLoading"
              @click="saveHireContractDetails"
            >
              Save Contract
            </v-btn>
          </v-layout>

          <v-layout
            class="scrollable"
            :key="fleetAssetDetails ? fleetAssetDetails.fleetAssetId : 'new'"
          >
            <v-form
              ref="fleetAssetTrailerForm"
              style="width: 100%"
              v-if="isForm"
            >
              <v-layout fill-height v-if="isTrailerDetails(fleetAssetDetails)">
                <v-flex lg8 md10 offset-lg2 offset-md1 class="pt-3">
                  <FleetAssetTrailerKeyInformation
                    v-if="selectedViewType === 'KEY'"
                    :fleetAsset="fleetAssetDetails"
                    :fleetAssetOwner="fleetAssetOwner"
                    :isEdited="isEdited"
                  ></FleetAssetTrailerKeyInformation>
                  <FleetAssetTrailerSpecifications
                    v-if="selectedViewType === 'SPC'"
                    :fleetAsset="fleetAssetDetails"
                    :isEdited="isEdited"
                  ></FleetAssetTrailerSpecifications>
                  <FleetAssetTrailerIdentifyingInformation
                    v-if="selectedViewType === 'IDI'"
                    :fleetAsset="fleetAssetDetails"
                    :isEdited="isEdited"
                  ></FleetAssetTrailerIdentifyingInformation>
                  <FleetAssetTruckImages
                    v-if="selectedViewType === 'IMG'"
                    :fleetAsset="fleetAssetDetails"
                    :isEdited="isEdited"
                  ></FleetAssetTruckImages>
                  <FleetAssetAdditionalEquipment
                    v-if="selectedViewType === 'AEQ'"
                    :key="forceReloadIncrementer"
                    :fleetAsset="fleetAssetDetails"
                    :isEdited="isEdited"
                  ></FleetAssetAdditionalEquipment>
                  <HireContractMaintenance
                    v-if="selectedViewType === 'CON' && hireContract"
                    :fleetAssetId="fleetAssetDetails.fleetAssetId"
                    :contract="hireContract"
                    :isEdited="isEdited"
                  />
                </v-flex>
              </v-layout>
            </v-form>

            <v-flex md12 v-if="!isForm" class="py-3 pl-3 pr-2">
              <FleetAssetTrailerHireContractSummaryList
                v-if="!hireContract"
                :fleetAssetId="fleetAssetDetails.fleetAssetId"
                @selectHireContract="selectHireContract"
              />
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import FleetAssetTrailerHireContractSummaryList from '@/components/admin/FleetAsset/trailer/components/fleet_asset_trailer_hire_contract_summary_list.vue';
import FleetAssetTrailerIdentifyingInformation from '@/components/admin/FleetAsset/trailer/components/fleet_asset_trailer_id_information.vue';
import FleetAssetTrailerKeyInformation from '@/components/admin/FleetAsset/trailer/components/fleet_asset_trailer_key_information.vue';
import FleetAssetTrailerSpecifications from '@/components/admin/FleetAsset/trailer/components/fleet_asset_trailer_specifications.vue';
import HireContractMaintenance from '@/components/admin/FleetAsset/trailer/components/hire_contract.vue';
import FleetAssetAdditionalEquipment from '@/components/admin/FleetAsset/truck/components/fleet_asset_additional_equipment.vue';
import FleetAssetTruckImages from '@/components/admin/FleetAsset/truck/components/fleet_asset_truck_images.vue';
import SelectEntity from '@/components/common/inputs/select_entity/index.vue';
import { initialiseFleetAsset } from '@/helpers/classInitialisers/InitialiseFleetAsset';
import { initialiseFleetAssetOwnerSummary } from '@/helpers/classInitialisers/InitialiseFleetAssetOwner';
import {
  returnFormattedDate,
  returnTimeNow,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  applyDefaultValuesToFleetAsset,
  isTrailerDetails,
} from '@/helpers/FleetAssetHelpers/FleetAssetHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  hasAdminOrHeadOfficeRole,
  hasAdminRole,
} from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import HireContract from '@/interface-models/FleetAsset/EquipmentHire/HireContract';
import FleetAsset from '@/interface-models/FleetAsset/FleetAsset';
import TrailerDetails from '@/interface-models/FleetAsset/models/FleetAssetTypes/Trailer/TrailerDetails';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { KeyValuePair } from '@/interface-models/Generic/KeyValue/KeyValue';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { sessionManager } from '@/store/session/SessionState';
import { useMittListener } from '@/utils/useMittListener';
import {
  computed,
  ComputedRef,
  onBeforeMount,
  onBeforeUnmount,
  onMounted,
  ref,
  Ref,
  watch,
} from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';

interface StatusConfig {
  statusName: string;
  color: string;
}

interface MenuCategory {
  id: string;
  title: string;
  items: MenuItem[];
}

interface MenuItem {
  id: string;
  title: string;
  isForm: boolean;
  isActive: boolean;
}

const props = withDefaults(
  defineProps<{
    isDialog?: boolean;
    fleetAssetOwnerAssociatedFleetAssetIds?: string[];
    fleetAssetOwnerId?: string;
  }>(),
  {
    isDialog: false,
    fleetAssetOwnerAssociatedFleetAssetIds: () => [],
    fleetAssetOwnerId: '',
  },
);

const fleetAssetStore = useFleetAssetStore();
const fleetAssetOwnerStore = useFleetAssetOwnerStore();

const entityType = ref(EntityType);

const emit = defineEmits<{
  (e: 'addFleetAssetToAssociatedFleetAssets', fleetAssetId: string): void;
}>();

const componentTitle: string = 'Trailer Administration';

const selectedViewType: Ref<string> = ref('KEY');

const fleetAssetOwner: Ref<FleetAssetOwnerSummary | null> = ref(null);

const fleetAssetDetails: Ref<FleetAsset | null> = ref(null);

const isEdited: Ref<boolean> = ref(false);

const isAwaitingFleetAssetResponse: Ref<boolean> = ref(false);
const isLoading: Ref<boolean> = ref(false);

const hireContract: Ref<HireContract | null> = ref(null);

// Used to force re-render of certain components when data is updated
const forceReloadIncrementer: Ref<number> = ref(0);

const fleetAssetTrailerForm: Ref<any> = ref(null);

const router = useRouter();
const route = useRoute();
const routeId: ComputedRef<string> = computed(() => route.params.id);
const routeName: ComputedRef<string> = computed(() => route.params.name);

// The fleetAssetId of the currently selected trailer
const selectedFleetAssetTrailerId: ComputedRef<string | null> = computed(() => {
  return fleetAssetStore.selectedFleetAssetTrailerId;
});

watch(selectedFleetAssetTrailerId, () => {
  findAndSetSelectedFleetAssetDetails();
});

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}

const isAdmin = computed(() => {
  return hasAdminRole();
});

function goToSubcontractorIndexRoute() {
  router.push({
    name: 'Subcontractor',
  });
}

const isNewFleetAsset = computed(() => {
  return routeId.value === 'new' || selectedFleetAssetTrailerId.value === 'new';
});

/**
 * Handles emit from FleetAssetTrailerHireContractSummaryList when a table item
 * is selected. Set to local variable.
 * @param hireContract - The selected HireContract object to view
 */
function selectHireContract(contract: HireContract) {
  hireContract.value = contract;
}

// Cancel the editing view. If we are
function cancelEdit() {
  if (selectedViewType.value === 'CON') {
    isEdited.value = false;
    hireContract.value = null;
  } else {
    findAndSetSelectedFleetAssetDetails(isNewFleetAsset.value);
  }
}

// Find the associated Fleet Asset from the list in the store. Re-initialise it
// to create a deep clone, then save to local working copy
async function findAndSetSelectedFleetAssetDetails(
  isCancelNewFleetAsset: boolean = false,
) {
  if (!props.isDialog && selectedFleetAssetTrailerId.value !== routeId.value) {
    fleetAssetStore.setSelectedFleetAssetTrailerId(routeId.value);
  }

  isEdited.value = false;

  // If a new fleet asset is canceled we should push the user back to the main subcontractor index
  if (isCancelNewFleetAsset && isNewFleetAsset.value) {
    if (!props.isDialog) {
      goToSubcontractorIndexRoute();
    } else {
      closeFleetAssetDialog();
    }
    return;
  }

  // reset any validation issues on inputs.
  if (fleetAssetTrailerForm.value) {
    fleetAssetTrailerForm.value.resetValidation();
  }

  // if the driver is new we initialise a new driver.
  if (isNewFleetAsset.value) {
    fleetAssetDetails.value = initialiseFleetAsset(new FleetAsset());
    // Add any default values from DivisionDetails custom config
    applyDefaultValuesToFleetAsset(
      fleetAssetDetails.value,
      useCompanyDetailsStore().divisionDetails,
    );

    if (fleetAssetDetails.value) {
      fleetAssetDetails.value.fleetAssetOwnerId = props.fleetAssetOwnerId;
      fleetAssetDetails.value.fleetAssetTypeId = 2;
      fleetAssetDetails.value.fleetAssetTypeObject = new TrailerDetails();
    }
    isEdited.value = true;
    findAndSetFleetAssetOwner(fleetAssetDetails.value);
    return;
  }

  // Request full FleetAsset object
  if (selectedFleetAssetTrailerId.value) {
    const fleetAsset: FleetAsset | null =
      await fleetAssetStore.requestFleetAssetByFleetAssetId(
        selectedFleetAssetTrailerId.value,
      );
    setFleetAssetFromResponse(fleetAsset);
  }
}

// Handles response of full FleetAsset after selection of a fleetAssetId
function setFleetAssetFromResponse(fleetAsset: FleetAsset | null) {
  if (!fleetAsset?.fleetAssetId) {
    showAppNotification('Sorry, we could not find that Fleet Asset.');
    fleetAssetDetails.value = null;
    closeFleetAssetDialog();
    return;
  }
  fleetAssetDetails.value = initialiseFleetAsset(fleetAsset);
  // Find fleet asset owner once fleetAssetDetails has been set
  findAndSetFleetAssetOwner(fleetAssetDetails.value);
  forceReloadIncrementer.value++;
}

// Find the associated Fleet Asset Owner from the store
const findAndSetFleetAssetOwner = (fleetAssetDetails: FleetAsset | null) => {
  if (!fleetAssetDetails) {
    return;
  }
  const ownerId = fleetAssetDetails.fleetAssetOwnerId;
  const owner = fleetAssetOwnerStore.getOwnerFromOwnerId(ownerId);
  fleetAssetOwner.value = owner
    ? initialiseFleetAssetOwnerSummary(owner)
    : null;
};

// Return a readable representation of the id/rego to use as a title
const fleetAssetName: ComputedRef<string> = computed(() => {
  if (!fleetAssetDetails.value) {
    return '';
  }
  const regoNumber = fleetAssetDetails.value.isTrailer
    ? (fleetAssetDetails.value.fleetAssetTypeObject as TrailerDetails)
        .registrationDetails.registrationNumber
    : '';
  if (!regoNumber) {
    return `${fleetAssetDetails.value.csrAssignedId}`;
  } else {
    return `${fleetAssetDetails.value.csrAssignedId} (${regoNumber})`;
  }
});

// Set selected store id to null, which will close the dialog through the
// getter in this components parent
function closeFleetAssetDialog(): void {
  fleetAssetStore.setSelectedFleetAssetTrailerId(null);
}

const menuOptions: ComputedRef<MenuCategory[]> = computed(() => {
  const trailerInfo: MenuCategory = {
    id: 'INFO',
    title: 'Trailer Info',
    items: [
      {
        id: 'KEY',
        title: 'Key Details',
        isForm: true,
        isActive: !isEdited.value,
      },
      {
        id: 'IDI',
        title: 'Identifying Information',
        isForm: true,
        isActive: !isEdited.value,
      },
      {
        id: 'SPC',
        title: 'Specifications',
        isForm: true,
        isActive: !isEdited.value,
      },
      {
        id: 'IMG',
        title: 'Photos',
        isForm: true,
        isActive: !isEdited.value,
      },
      {
        id: 'AEQ',
        title: 'Additional Equipment',
        isForm: true,
        isActive: !isEdited.value,
      },
    ],
  };

  const contract: MenuCategory = {
    id: 'CONTR',
    title: 'Contract',
    items: [
      {
        id: 'CON',
        title: 'Manage Hire Contract',
        isForm: hireContract.value ? true : false,
        isActive:
          !isEdited.value &&
          fleetAssetDetails.value !== null &&
          fleetAssetDetails.value._id !== undefined &&
          fleetAssetDetails.value._id !== null,
      },
    ],
  };
  // Only show the contracts tab if the owner is Equipment Hire
  if (fleetAssetOwner.value && fleetAssetOwner.value.affiliation === '2') {
    return [trailerInfo, contract];
  } else {
    return [trailerInfo];
  }
});

// Computed property to determine if the selected view is a form
const isForm = computed(() => {
  const selectedView = menuOptions.value
    .flatMap((x) => x.items)
    .find((x: MenuItem) => x.id === selectedViewType.value);
  return selectedView ? selectedView.isForm : false;
});

// Computed property to get summary info list
const summaryInfoList = computed(() => {
  const infoList: KeyValuePair[] = [];
  if (!fleetAssetDetails.value) {
    return infoList;
  }

  const fleetAsset = fleetAssetDetails.value;
  const trailerDetails: Ref<TrailerDetails> = ref(
    fleetAssetDetails.value.fleetAssetTypeObject as TrailerDetails,
  );

  let contractStatus = '';
  let contractValidTo = '';
  // let contractRate = '';
  if (hireContract.value) {
    const isActive =
      hireContract.value.statusList.includes(4) &&
      hireContract.value.validToDate;
    contractValidTo = hireContract.value.validToDate
      ? returnFormattedDate(hireContract.value.validToDate)
      : '';

    contractStatus = isActive ? 'ACTIVE' : 'INACTIVE';

    if (isActive) {
      const now = returnTimeNow();
      if (
        hireContract.value.validToDate !== null &&
        hireContract.value.validToDate < now
      ) {
        contractStatus = 'EXPIRED';
      }
    }
  } else {
    contractStatus = 'NO CONTRACT';
  }

  infoList.push({
    id: 'csrAssignedId',
    title: 'Trailer ID',
    value: fleetAsset.csrAssignedId ? fleetAsset.csrAssignedId : '-',
  });
  infoList.push({
    id: 'subcontractorName',
    title: 'Subcontractor (owner)',
    value: fleetAssetOwner.value ? fleetAssetOwner.value.name : '-',
  });

  infoList.push({
    id: 'regoNumber',
    title: 'Registration Number',
    value: trailerDetails.value.registrationDetails.registrationNumber
      ? trailerDetails.value.registrationDetails.registrationNumber
      : '-',
  });

  infoList.push({
    id: 'regoExpiry',
    title: 'Registration Expiry',
    value: trailerDetails.value.registrationDetails.expiry
      ? returnFormattedDate(trailerDetails.value.registrationDetails.expiry)
      : '-',
  });
  infoList.push({
    id: 'contractStatus',
    title: 'Contract Status',
    value: contractStatus ? contractStatus : '-',
  });
  infoList.push({
    id: 'contractValidTo',
    title: 'Contract Valid To',
    value: contractValidTo ? contractValidTo : '-',
  });

  if (isAdmin.value) {
    infoList.push({
      id: 'ID',
      title: 'fleetAssetId',
      value: fleetAssetDetails.value.fleetAssetId,
    });
  }

  return infoList;
});

// take user to fleet asset owner page
const viewFleetAssetOwner = (): void => {
  if (!fleetAssetOwner.value) {
    return;
  }
  fleetAssetOwnerStore.setSelectedFleetAssetOwnerId(
    fleetAssetOwner.value.ownerId,
  );
  router.push({
    name: 'Owner',
    params: {
      name: fleetAssetOwner.value.name.toLowerCase().replace(/ /g, '-'),
      id: fleetAssetOwner.value.ownerId,
    },
  });
};

// Set the selected view from the left navigation menu
function setSelectedView(id: string) {
  hireContract.value = null;
  if (!id) {
    selectedViewType.value = menuOptions.value[0].id;
    return;
  }
  selectedViewType.value = id;
}

const currentStatus = computed<StatusConfig>(() => {
  const statusConfig = {
    statusName: '',
    color: '',
  };

  if (!fleetAssetDetails.value) {
    return statusConfig;
  }

  const doNotUse: boolean = fleetAssetDetails.value.statusList.includes(47);
  const active: boolean = fleetAssetDetails.value.statusList.includes(4);

  if (doNotUse) {
    statusConfig.statusName = 'DO NOT USE';
    statusConfig.color = 'error';
  } else if (active) {
    statusConfig.statusName = 'Active';
    statusConfig.color = 'success';
  } else {
    statusConfig.statusName = 'INCOMPLETE';
    statusConfig.color = 'error';
  }
  return statusConfig;
});

// Validate form then save the Fleet Asset if all required fields have been
// completed
async function saveFleetAssetDetails() {
  if (!fleetAssetDetails.value) {
    return;
  }
  if (!fleetAssetTrailerForm.value.validate()) {
    showAppNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }
  isAwaitingFleetAssetResponse.value = true;
  const fleetAsset = await fleetAssetDetails.value.save();
  isAwaitingFleetAssetResponse.value = false;
  // Handle result of save operation
  if (fleetAsset) {
    fleetAssetDetails.value = fleetAsset;
    handleSuccessfullySavedFleetAsset();
    forceReloadIncrementer.value++;
    isEdited.value = false;
  } else {
    showAppNotification(
      'An error occurred while saving the Fleet Asset.',
      HealthLevel.ERROR,
    );
  }
}

// Validate then save the current Hire Contract object
async function saveHireContractDetails() {
  if (!fleetAssetTrailerForm.value.validate()) {
    showAppNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }
  if (hireContract.value) {
    isLoading.value = true;
    // Dispatch the save event for HireContract and await response
    const result = await fleetAssetStore.saveHireContract(hireContract.value);

    // Show notification if the save failed
    if (!result) {
      showAppNotification(
        `An error occurred while saving the Hire Contract. Please ensure all required fields are completed, and that the dates don't overlap with other contracts.`,
        HealthLevel.ERROR,
      );
      isLoading.value = false;
      return;
    }

    // Reset form state
    isLoading.value = false;
    isEdited.value = false;
    hireContract.value = null;
  }
}

// Set the component state to edit mode
function editFleetAssetDetails() {
  isEdited.value = true;
}

// When the save events are finished we should update route information and associations
const handleSuccessfullySavedFleetAsset = () => {
  if (!fleetAssetDetails.value) {
    return;
  }

  showAppNotification('Fleet asset successfully saved.', HealthLevel.SUCCESS);
  // update route and subcontractor search selection
  if (!props.isDialog) {
    if (
      routeName.value !==
        fleetAssetDetails.value.csrAssignedId
          .toLowerCase()
          .replace(/ /g, '-') &&
      routeId.value !== fleetAssetDetails.value.fleetAssetId
    ) {
      router.push({
        name: 'Trailer',
        params: {
          name: fleetAssetDetails.value.csrAssignedId
            .toLowerCase()
            .replace(/ /g, '-'),
          id: fleetAssetDetails.value.fleetAssetId,
        },
      });
    }
    return;
  }
  // if the driver was saved within the fleet asset owner we need to associate the saved driver with the fleet asset owner if it is not already;
  emit(
    'addFleetAssetToAssociatedFleetAssets',
    fleetAssetDetails.value.fleetAssetId,
  );
};

const initialiseNewHireContract = () => {
  if (!fleetAssetDetails.value) {
    return;
  }
  hireContract.value = new HireContract();
  hireContract.value.company = sessionManager.getCompanyId();
  hireContract.value.division = sessionManager.getDivisionId();
  hireContract.value.fleetAssetId = fleetAssetDetails.value.fleetAssetId;
  hireContract.value.statusList = [3];
  isEdited.value = true;
};

// Trigger app notification. Defaults to ERROR type message, but type can be
// provided to produce other types. Includes componentTitle as a title for the
// notification.
function showAppNotification(text: string, type?: HealthLevel): void {
  showNotification(text, {
    type,
    title: componentTitle,
  });
}

/**
 * Called by mitt listener when a Fleet Asset save event is received. Used to
 * reset the component state if the Fleet Asset has been updated by another
 * user.
 */
function handleExternalSave(incomingFleetAsset: FleetAsset | null): void {
  if (!incomingFleetAsset?.fleetAssetId) {
    return;
  }
  if (
    incomingFleetAsset.fleetAssetId === fleetAssetDetails.value?.fleetAssetId &&
    !isAwaitingFleetAssetResponse.value
  ) {
    // Show notification if currently in edit mode
    if (isEdited.value) {
      showAppNotification(
        `${fleetAssetName.value} has been updated by another user. Your changes have been discarded.`,
        HealthLevel.WARNING,
      );
    }
    isEdited.value = false;
    findAndSetSelectedFleetAssetDetails();
  }
}

useMittListener('selectedFleetAssetDetails', handleExternalSave);

// Init the component at a specific tab if the value exists in the FleetAssetOwnerModule
onBeforeMount(() => {
  if (fleetAssetStore.selectedFleetAssetView) {
    selectedViewType.value = fleetAssetStore.selectedFleetAssetView;
    fleetAssetStore.setSelectedFleetAssetView(null);
  } else {
    selectedViewType.value = 'KEY';
  }
});

onMounted(() => {
  findAndSetSelectedFleetAssetDetails();
});

// Cancel store subscription listener
onBeforeUnmount(() => {
  fleetAssetStore.setSelectedFleetAssetTrailerId(null);
});
</script>
