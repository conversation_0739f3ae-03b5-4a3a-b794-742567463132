<template>
  <div class="fleet-asset-truck-compliance-information">
    <v-layout wrap>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">1. Registration Information</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Vehicle Registration Number
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              solo
              flat
              class="v-solo-custom"
              :disabled="!isEdited"
              :rules="[validate.required]"
              v-model="
                fleetAsset.fleetAssetTypeObject.registrationDetails
                  .registrationNumber
              "
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                State of Registration
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              solo
              flat
              class="v-solo-custom"
              :disabled="!isEdited"
              :items="stateOfRegistrationSelector"
              item-value="stateShortName"
              item-text="stateLongName"
              :rules="[validate.required]"
              v-model="
                fleetAsset.fleetAssetTypeObject.registrationDetails
                  .stateOfRegistration
              "
            >
            </v-select>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Registration Expiry
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <DateTimeInputs
              :epochTime.sync="
                fleetAsset.fleetAssetTypeObject.registrationDetails.expiry
              "
              :enableValidation="true"
              :type="DateTimeType.DATE_START_OF_DAY"
              dateLabel="Registration Expiry"
              :readOnly="!isEdited"
              :soloInput="true"
              :boxInput="false"
              :isRequired="true"
              :hintTextType="HintTextType.FORMATTED_SELECTION"
            ></DateTimeInputs>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">2. Insurance</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Insurance Policies</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              :disabled="!isEdited"
              label="Insurance Policies"
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              solo
              flat
              class="v-solo-custom"
              :items="selectablePolicies"
              item-text="key"
              item-value="value"
              multiple
              color="light-blue"
              v-model="fleetAsset.insurances"
            >
            </v-select>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">
            3. Services and Police Checks
          </h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Last Service Date</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <DateTimeInputs
              :epochTime.sync="fleetAsset.fleetAssetTypeObject.serviceLast"
              :enableValidation="true"
              :type="DateTimeType.DATE_START_OF_DAY"
              dateLabel="Last Service Date"
              :readOnly="!isEdited"
              :soloInput="true"
              :boxInput="false"
              :hintTextType="HintTextType.FORMATTED_SELECTION"
            ></DateTimeInputs>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Next Service Date</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <DateTimeInputs
              :epochTime.sync="fleetAsset.fleetAssetTypeObject.serviceNext"
              :enableValidation="true"
              :type="DateTimeType.DATE_START_OF_DAY"
              dateLabel="Next Service Date"
              :readOnly="!isEdited"
              :soloInput="true"
              :boxInput="false"
              :hintTextType="HintTextType.FORMATTED_SELECTION"
            ></DateTimeInputs>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Police Check</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <DateTimeInputs
              :epochTime.sync="fleetAsset.fleetAssetTypeObject.policeCheck"
              :enableValidation="true"
              :type="DateTimeType.DATE_START_OF_DAY"
              dateLabel="Police Check"
              :readOnly="!isEdited"
              :soloInput="true"
              :boxInput="false"
              :hintTextType="HintTextType.FORMATTED_SELECTION"
            ></DateTimeInputs>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import DateTimeInputs, {
  DateTimeType,
  HintTextType,
} from '@/components/common/date-picker/date_time_inputs.vue';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { returnPolicyNameFromId } from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import FleetAsset from '@/interface-models/FleetAsset/FleetAsset';
import TruckDetails from '@/interface-models/FleetAsset/models/FleetAssetTypes/Truck/TruckDetails';
import FleetAssetOwner from '@/interface-models/FleetAssetOwner/FleetAssetOwner';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import { KeyValue } from '@/interface-models/Generic/KeyValue/KeyValue';
import { Validation } from '@/interface-models/Generic/Validation';
import StateRegistrationSelectorDataAU from '@/static/staticData/state_registrationAU.json';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import moment from 'moment-timezone';
import { ComputedRef, Ref, computed, onMounted, ref } from 'vue';

const props = withDefaults(
  defineProps<{
    fleetAsset: FleetAsset & { fleetAssetTypeObject: TruckDetails };
    fleetAssetOwnerSummary: FleetAssetOwnerSummary;
    isEdited?: boolean;
  }>(),
  {
    isEdited: false,
  },
);

const stateOfRegistrationSelector = StateRegistrationSelectorDataAU;
const fleetAssetOwner: Ref<FleetAssetOwner | null> = ref(null);
const validate: ComputedRef<Validation> = computed(() => validationRules);

// Construct a list of insurance policies able to be applied to the Fleet Asset based on policies that exist in the owner
const selectablePolicies: ComputedRef<KeyValue[]> = computed(() => {
  if (!fleetAssetOwner.value) {
    return [];
  }
  const currentTime = moment().valueOf();
  const selectablePolicies: KeyValue[] = [];
  for (const policy of fleetAssetOwner.value.insurances) {
    const isExpired =
      policy.validToDate !== null && policy.validToDate < currentTime;
    const isRetired = policy.isRetired;
    const value = policy.insuranceId;
    const key = `${policy.insurerName} - ${returnPolicyNameFromId(
      policy.policyType,
    )} - ${policy.policyNumber} ${
      isRetired ? '(Retired)' : isExpired ? '(Expired)' : ''
    }`;
    selectablePolicies.push({ value, key });
  }
  return selectablePolicies;
});

/**
 * Request the owner details using the owner id from props. Sets the response to
 * the fleetAssetOwner ref.
 */
async function requestFleetAssetOwnerDetails() {
  const response = await useFleetAssetOwnerStore().requestOwnerDetailsByOwnerId(
    props.fleetAssetOwnerSummary.ownerId,
  );
  if (!response) {
    showNotification(GENERIC_ERROR_MESSAGE, {
      title: 'Fleet Asset - Truck Compliance',
    });
  }
  fleetAssetOwner.value = response;
}

onMounted(() => {
  requestFleetAssetOwnerDetails();
});
</script>
