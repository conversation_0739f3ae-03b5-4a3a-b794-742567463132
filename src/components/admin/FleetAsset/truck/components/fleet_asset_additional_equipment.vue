<template>
  <div class="fleet-asset-additional-equipment">
    <v-layout wrap>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">Additional Equipment</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <AdditionalEquipment
          :soloInput="true"
          :fleetAsset="fleetAsset"
          :isEdited="isEdited"
        >
        </AdditionalEquipment>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import AdditionalEquipment from '@/components/common/AdditionalEquipment/additional_equipment_index.vue';
import FleetAsset from '@/interface-models/FleetAsset/FleetAsset';

const props = withDefaults(
  defineProps<{
    fleetAsset: FleetAsset;
    isEdited: boolean;
  }>(),
  {
    isEdited: false,
  },
);
</script>

<style scoped lang="scss">
.fleet-asset-additional-equipment {
  padding: 0;

  .label-container {
    height: 48px;
  }
}
</style>
