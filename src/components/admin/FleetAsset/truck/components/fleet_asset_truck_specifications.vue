<template>
  <div class="fleet-asset-truck-specifications">
    <v-layout wrap>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">
            1. Weight - Capacity Specifications
          </h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                Gross Vehicle Mass (GVM)
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.gvm"
              :disabled="!isEdited"
              hint="The maximum this truck can weigh when fully loaded as specified by the manufacturer"
              solo
              flat
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Tare Weight (kg)</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.tare"
              hint="The unladen weight of the trailer"
              :disabled="!isEdited"
              solo
              flat
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Payload Weight (kg)</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.payload"
              hint="The maximum load this vehicle can carry as specified by the manufacturer"
              :disabled="!isEdited"
              solo
              flat
            />
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Toll Class
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              solo
              flat
              class="v-solo-custom"
              :disabled="!isEdited"
              v-model="fleetAsset.fleetAssetTypeObject.tollClass"
              :items="vehicleClasses"
              :rules="[validate.required]"
              item-text="longName"
              item-value="classId"
            ></v-select>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Pallet Capacity</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.numberOfPallets"
              hint="Number of pallets this truck can hold"
              :disabled="!isEdited"
              solo
              flat
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">2. Vehicle Dimensions</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Vehicle Height (m)</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.truckDimensions.height"
              :disabled="!isEdited"
              solo
              flat
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Vehicle Width (m)</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.truckDimensions.width"
              :disabled="!isEdited"
              solo
              flat
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Vehicle Length (m)</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.truckDimensions.length"
              :disabled="!isEdited"
              solo
              flat
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">3. Tray Dimensions</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Tray Height (m)</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.trayDimensions.height"
              :disabled="!isEdited"
              solo
              flat
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Tray Width (m)</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.trayDimensions.width"
              :disabled="!isEdited"
              solo
              flat
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Tray Length (m)</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.trayDimensions.length"
              :disabled="!isEdited"
              solo
              flat
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">4. Other Info</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Seating Capacity</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.seatingCapacity"
              :disabled="!isEdited"
              solo
              flat
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Number of Axles</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              class="v-solo-custom"
              v-model="fleetAsset.fleetAssetTypeObject.axels"
              :disabled="!isEdited"
              solo
              flat
            />
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import FleetAsset from '@/interface-models/FleetAsset/FleetAsset';
import { Validation } from '@/interface-models/Generic/Validation';
import { vehicleClasses } from '@/interface-models/ServiceRates/AdditionalCharges/static/vehicleClasses';

import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import TruckDetails from '@/interface-models/FleetAsset/models/FleetAssetTypes/Truck/TruckDetails';

const props = withDefaults(
  defineProps<{
    fleetAsset: FleetAsset & { fleetAssetTypeObject: TruckDetails };
    isEdited: boolean;
  }>(),
  {
    isEdited: false,
  },
);
const validate: Validation = validationRules;
</script>

<style scoped lang="scss">
.fleet-asset-truck-specifications {
  .label-container {
    height: 48px;
  }
}
</style>
