<template>
  <div class="fleet-asset-truck-images">
    <v-layout wrap>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">Images of Vehicle Exterior</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 mb-2>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                Front & Rear of {{ vehicleName ? vehicleName : 'TRUCK' }}
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md4 pr-1>
            <file-upload
              :imageLabel="'FRONT'"
              :attachmentSingle="false"
              :formDisabled="!isEdited"
              :documentTypeId="fleetDocumentTypes.front"
              :attachmentArray="fleetAsset.attachments"
            >
            </file-upload>
          </v-flex>
          <v-flex md4 pl-1 style="height: 100%">
            <file-upload
              :imageLabel="'REAR'"
              :attachmentSingle="false"
              :formDisabled="!isEdited"
              :documentTypeId="fleetDocumentTypes.rear"
              :attachmentArray="fleetAsset.attachments"
            >
            </file-upload>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                Sides of {{ vehicleName ? vehicleName : 'TRUCK' }}
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md4 pr-1>
            <file-upload
              :imageLabel="'LEFT SIDE'"
              :attachmentSingle="false"
              :formDisabled="!isEdited"
              :documentTypeId="fleetDocumentTypes.left"
              :attachmentArray="fleetAsset.attachments"
            >
            </file-upload>
          </v-flex>
          <v-flex md4 pl-1>
            <file-upload
              :imageLabel="'RIGHT SIDE'"
              :attachmentSingle="false"
              :formDisabled="!isEdited"
              :documentTypeId="fleetDocumentTypes.right"
              :attachmentArray="fleetAsset.attachments"
            >
            </file-upload>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import FileUpload from '@/components/common/file-upload/index.vue';
import FleetAsset from '@/interface-models/FleetAsset/FleetAsset';
import AttachmentTypes from '@/interface-models/Generic/Attachment/AttachmentTypes';
import { computed } from 'vue';

interface FleetAssetDocumentType {
  left: AttachmentTypes;
  right: AttachmentTypes;
  rear: AttachmentTypes;
  front: AttachmentTypes;
}

const props = withDefaults(
  defineProps<{
    fleetAsset: FleetAsset;
    isEdited: boolean;
  }>(),
  {
    isEdited: false,
  },
);

// Return the name display in labels based on the fleet asset type id
const vehicleName = computed(() => {
  let name = 'Truck';
  if (props.fleetAsset.fleetAssetTypeId === 2) {
    name = 'Trailer';
  }
  return name;
});

// Return an object containing the correct attachment types to use for each
// angle, for different Fleet Asset type ids
const fleetDocumentTypes = computed(() => {
  let documentTypes: FleetAssetDocumentType = {
    left: AttachmentTypes.FLEET_ASSET_LEFT_SIDE,
    right: AttachmentTypes.FLEET_ASSET_RIGHT_SIDE,
    rear: AttachmentTypes.FLEET_ASSET_REAR,
    front: AttachmentTypes.FLEET_ASSET_FRONT,
  };
  // If the fleet asset is a trailer
  if (props.fleetAsset.fleetAssetTypeId === 2) {
    documentTypes = {
      left: AttachmentTypes.TRAILER_LEFT_SIDE,
      right: AttachmentTypes.TRAILER_RIGHT_SIDE,
      rear: AttachmentTypes.TRAILER_REAR,
      front: AttachmentTypes.TRAILER_FRONT,
    };
  }
  return documentTypes;
});
</script>

<style scoped lang="scss">
.fleet-asset-truck-images {
  .label-container {
    height: 48px;
  }
}
</style>
