<template>
  <section class="fleet-asset-dialog">
    <v-dialog
      v-model="fleetAssetDialogIsOpen"
      content-class="v-dialog-custom"
      no-click-animation
      persistent
      width="90%"
    >
      <FleetAssetIndex
        :isDialog="true"
        v-if="fleetAssetDialogIsOpen"
        :fleetAssetOwnerId="fleetAssetOwnerId"
        @addFleetAssetToAssociatedFleetAssets="
          addFleetAssetToAssociatedFleetAssets
        "
      />
    </v-dialog>

    <v-dialog
      v-model="trailerDialogIsOpen"
      content-class="v-dialog-custom"
      no-click-animation
      persistent
      width="90%"
    >
      <FleetAssetTrailerIndex
        v-if="trailerDialogIsOpen"
        :isDialog="true"
        :fleetAssetOwnerId="fleetAssetOwnerId"
        @addFleetAssetToAssociatedFleetAssets="
          addFleetAssetToAssociatedFleetAssets
        "
      ></FleetAssetTrailerIndex>
    </v-dialog>
  </section>
</template>

<script setup lang="ts">
import FleetAssetTrailerIndex from '@/components/admin/FleetAsset/trailer/fleet_asset_trailer_index.vue';
import FleetAssetIndex from '@/components/admin/FleetAsset/truck/fleet_asset_index.vue';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { computed } from 'vue';

// Define props
const props = withDefaults(
  defineProps<{
    fleetAssetOwnerAssociatedDriverIds?: string[];
    fleetAssetOwnerAssociatedFleetAssetIds: string[];
    fleetAssetOwnerId: string;
  }>(),
  {
    fleetAssetOwnerAssociatedDriverIds: () => [],
  },
);
const emit = defineEmits<{
  (e: 'addFleetAssetToAssociatedFleetAssets', fleetAssetId: string): void;
}>();

const fleetAssetStore = useFleetAssetStore();

const selectedFleetAssetId = computed(
  () => fleetAssetStore.selectedFleetAssetId,
);
const fleetAssetDialogIsOpen = computed({
  get: () => !!selectedFleetAssetId.value,
  set: (value: boolean) => {
    if (!value) {
      fleetAssetStore.setSelectedFleetAssetId(null);
    }
  },
});

const selectedFleetAssetTrailerId = computed(
  () => fleetAssetStore.selectedFleetAssetTrailerId,
);
const trailerDialogIsOpen = computed({
  get: () => !!selectedFleetAssetTrailerId.value,
  set: (value: boolean) => {
    if (!value) {
      fleetAssetStore.setSelectedFleetAssetTrailerId(null);
    }
  },
});

const addFleetAssetToAssociatedFleetAssets = (fleetAssetId: string) => {
  emit('addFleetAssetToAssociatedFleetAssets', fleetAssetId);
};
</script>
