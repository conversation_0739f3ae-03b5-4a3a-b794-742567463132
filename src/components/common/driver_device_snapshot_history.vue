<template>
  <div class="driver-device-snapshot-history">
    <v-layout class="px-2 pt-2 pb-1">
      <v-flex md3>
        <v-layout>
          <v-btn
            outline
            block
            color="white"
            :loading="isRequestingSnapshot"
            @click="requestDeviceSnapshot"
            >Request Current Snapshot
          </v-btn>
        </v-layout>
      </v-flex>
      <v-flex md3 class="px">
        <DatePickerBasic
          :soloInput="true"
          @setEpoch="setStartEpoch"
          v-model="startEpoch"
          :labelName="'From Date'"
          :epochTime="startEpoch"
        >
        </DatePickerBasic>
      </v-flex>

      <v-flex md3 class="px">
        <DatePickerBasic
          @setEpoch="setEndEpoch"
          :soloInput="true"
          v-model="endEpoch"
          :labelName="'To Date'"
          :epochTime="endEpoch"
        >
        </DatePickerBasic>
      </v-flex>
      <v-flex md3>
        <v-layout>
          <v-btn
            class="view-details-button"
            depressed
            block
            @click="searchWithCurrentCriteria"
            >Search for Selected Dates
          </v-btn>
        </v-layout>
      </v-flex>
    </v-layout>

    <v-divider />
    <v-layout row wrap pa-3>
      <v-flex md12>
        <v-data-table
          class="gd-dark-theme"
          :headers="headers"
          v-model:pagination="pagination"
          :items="snapshotList"
          no-data-text="No results."
        >
          <template v-slot:items="slotProps">
            <tr
              @click="
                selectedSnapshotId = slotProps.item._id;
                isViewingDialog = true;
              "
              style="cursor: pointer"
            >
              <td>
                {{
                  returnFormattedDate(
                    slotProps.item.timestamp,
                    'DD/MM/YYYY HH:mm',
                  )
                }}
              </td>
              <td>{{ slotProps.item.driverName }}</td>
              <td>{{ slotProps.item.csrAssignedId }}</td>
              <td>{{ slotProps.item.registrationNumber }}</td>
            </tr>
          </template>
        </v-data-table>
      </v-flex>
    </v-layout>

    <ContentDialog
      v-if="isViewingDialog && selectedSnapshot"
      :showDialog.sync="isViewingDialog"
      title="Snapshot Details"
      :isConfirmUnsaved="false"
      width="40%"
      confirmBtnText="Done"
      @confirm="cancelDriverDeviceInfoView"
      @cancel="cancelDriverDeviceInfoView"
      :showCancelBtn="false"
    >
      <DriverDeviceSnapshotSummary
        :driverDeviceSnapshot="selectedSnapshot"
      ></DriverDeviceSnapshotSummary>
    </ContentDialog>
  </div>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import DriverDeviceSnapshotSummary from '@/components/common/driver_device_snapshot_summary.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import {
  returnEndOfDayFromEpoch,
  returnFormattedDate,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { DeviceQueryType } from '@/interface-models/Driver/Device/DeviceQueryType';
import type { DriverDeviceQuery } from '@/interface-models/Driver/Device/DriverDeviceQuery';
import type { DriverDeviceSnapshot } from '@/interface-models/Driver/Device/DriverDeviceSnapshot';
import type TableHeader from '@/interface-models/Generic/Table/TableHeader';
import type { VPagination } from '@/interface-models/Generic/VPagination';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useRootStore } from '@/store/modules/RootStore';
import { computed, ComputedRef, onMounted, ref, Ref } from 'vue';

interface DeviceSnapshot extends DriverDeviceSnapshot {
  driverName: string;
  csrAssignedId: string;
  registrationNumber: string;
}

const props = withDefaults(
  defineProps<{
    driverId?: string;
  }>(),
  {
    driverId: '',
  },
);

const fleetAssetStore = useFleetAssetStore();
const driverDetailsStore = useDriverDetailsStore();

const startEpoch: Ref<number> = ref(returnStartOfDayFromEpoch());
const endEpoch: Ref<number> = ref(returnEndOfDayFromEpoch());

const snapshotList: Ref<DeviceSnapshot[]> = ref([]);
const selectedSnapshotId: Ref<string> = ref('');
const isViewingDialog: Ref<boolean> = ref(false);

const isRequestingSnapshot: Ref<boolean> = ref(false);
const requestedDeviceSnapshot: Ref<DriverDeviceSnapshot | null> = ref(null);

// Returns the requestedDeviceSnapshot if it exists, otherwise returns the
// selected snapshot from the snapshotList (search results)
const selectedSnapshot: ComputedRef<DeviceSnapshot | null> = computed(() => {
  if (requestedDeviceSnapshot.value) {
    return mapToDeviceSnapshot(requestedDeviceSnapshot.value);
  } else {
    return (
      snapshotList.value.find(
        (s) => !!selectedSnapshotId.value && selectedSnapshotId.value === s._id,
      ) ?? null
    );
  }
});

const headers: TableHeader[] = [
  {
    text: 'Date',
    align: 'left',
    sortable: true,
    value: 'timestamp',
  },
  {
    text: 'Driver',
    align: 'left',
    sortable: true,
    value: 'driverName',
  },
  {
    text: 'Fleet ID',
    align: 'left',
    sortable: true,
    value: 'csrAssignedId',
  },
  {
    text: 'Vehicle Rego.',
    align: 'left',
    sortable: true,
    value: 'registrationNumber',
  },
];

const pagination: Ref<VPagination> = ref({
  rowsPerPage: 10,
  page: 1,
  sortBy: 'name',
  descending: false,
  totalItems: snapshotList.value.length,
});

// Mapped to date picker
function setStartEpoch(epoch: number) {
  startEpoch.value = returnStartOfDayFromEpoch(epoch);
}
function setEndEpoch(epoch: number) {
  endEpoch.value = returnEndOfDayFromEpoch(epoch);
}

// Reset selection and close dialog
function cancelDriverDeviceInfoView() {
  isViewingDialog.value = false;
  selectedSnapshotId.value = '';
  requestedDeviceSnapshot.value = null;
}

// Dispatch search using current selected categories and input values
async function searchWithCurrentCriteria() {
  // Return if dates are somehow unset or if startEpoch is greater or equal to endEpoch
  if (
    !startEpoch.value ||
    !endEpoch.value ||
    startEpoch.value >= endEpoch.value
  ) {
    showNotification('Please select a valid date, then try again.');
    return;
  }
  // Return if dates are somehow unset
  if (!props.driverId) {
    showNotification('Please ensure you have a valid Driver selected.');
    return;
  }
  // Send request and handle response
  const results = await driverDetailsStore.getDriverDeviceSnapshotHistory({
    startEpoch: startEpoch.value,
    endEpoch: endEpoch.value,
    driverId: props.driverId,
  });
  if (results?.length) {
    processSearchResponse(results);
  } else {
    showNotification('No results were found for the supplied date.');
  }
}
/**
 * Init dates to start and end of today, then make initial default call
 */
function requestDefaultSearch() {
  startEpoch.value = returnStartOfDayFromEpoch();
  endEpoch.value = returnEndOfDayFromEpoch();
  searchWithCurrentCriteria();
}

/**
 * Handle response from search API
 * @param snapshots - List of snapshots returned from search. Set to
 * snapshotList for display
 */
function processSearchResponse(snapshots: DriverDeviceSnapshot[]) {
  snapshotList.value = snapshots.map((s) => mapToDeviceSnapshot(s));
}

/**
 * Maps a DriverDeviceSnapshot object to a DeviceSnapshot object.
 *
 * @param {DriverDeviceSnapshot} snapshot - The DriverDeviceSnapshot object to
 * be mapped.
 * @returns {DeviceSnapshot} - The mapped DeviceSnapshot object.
 */
function mapToDeviceSnapshot(snapshot: DriverDeviceSnapshot): DeviceSnapshot {
  const foundDriver = driverDetailsStore.getDriverFromDriverId(
    snapshot.driverId,
  );
  const foundFleetAsset = fleetAssetStore.getFleetAssetFromFleetAssetId(
    snapshot.fleetAssetId,
  );
  return {
    ...snapshot,
    driverName: foundDriver?.displayName ?? '-',
    csrAssignedId: foundFleetAsset?.csrAssignedId ?? '-',
    registrationNumber: foundFleetAsset?.registrationNumber ?? '-',
  };
}

/**
 * Dispatches a device query request based on the specified query type. If the
 * query type is DEVICE_SUMMARY, it waits for the response and updates the UI
 * accordingly. If the query type is not DEVICE_SUMMARY, it sends the event
 * without waiting for a response.
 *
 * @param queryType - The type of device query to be dispatched.
 */
async function requestDeviceSnapshot() {
  const request: DriverDeviceQuery = {
    driverId: props.driverId,
    queryType: DeviceQueryType.DEVICE_SUMMARY,
  };
  // If we're fetching a DEVICE_SUMMARY, we need to wait for the response so
  // we can display
  isRequestingSnapshot.value = true;
  const result = await useRootStore().requestDriverDeviceInfo(request);
  if (result) {
    requestedDeviceSnapshot.value = result;
    isViewingDialog.value = true;
  } else {
    showNotification(
      'Something went wrong. Device Query could not be retrieved.',
    );
    cancelDriverDeviceInfoView();
  }
  isRequestingSnapshot.value = false;
}

onMounted(() => {
  requestDefaultSearch();
});
</script>
<style scoped lang="scss"></style>
