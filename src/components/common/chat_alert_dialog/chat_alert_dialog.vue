<template>
  <section class="chat-alert-dialog">
    <v-layout
      v-if="jobCount"
      class="button app-bgcolor-400"
      @click="isViewingDialog = true"
      align-center
    >
      <span class="button-count">{{ jobCount }}</span>
      <span class="button-text">{{ buttonTitle }}</span>
    </v-layout>

    <ContentDialog
      :showDialog.sync="isViewingDialog"
      :title="dialogTitle"
      width="80%"
      contentPadding="pa-0"
      @cancel="isViewingDialog = false"
      :showActions="false"
    >
      <v-layout align-center class="banner-custom pa-3">
        <v-layout column>
          <h3>{{ dialogTitle }}</h3>
          <h4>
            Viewing {{ jobCount }} jobs flagged as
            {{ returnProgressAlertTypeMessage(jobProgressAlertType) }}
          </h4>
        </v-layout>
        <span class="tags-chip">{{
          jobProgressAlertType.replace(/_/g, ' ')
        }}</span>
      </v-layout>
      <v-divider></v-divider>
      <v-layout>
        <v-flex md12 class="body-scrollable--75 pa-3">
          <table class="simple-data-table">
            <thead>
              <tr>
                <th>Date</th>
                <th>Time</th>
                <th>Job #</th>
                <th>Fleet #</th>
                <th>Driver</th>
                <th>Contact</th>
                <th>Message</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="item in jobProgressAlertList" :key="item._id">
                <td>{{ item.date }}</td>
                <td>{{ item.time }}</td>
                <td>{{ item.displayId }}</td>
                <td>{{ item.csrAssignedId }}</td>
                <td>{{ item.driverName }}</td>
                <td>{{ item.driverMobile }}</td>
                <td>{{ item.alertMessage }}</td>
              </tr>
            </tbody>
          </table>
        </v-flex>
      </v-layout>
    </ContentDialog>
  </section>
</template>

<script setup lang="ts">
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import {
  JobProgressAlertDetails,
  transformJobProgressAlerts,
} from '@/helpers/JobProgressAlertHelpers/JobProgressAlertHelpers';
import JobProgressAlert from '@/interface-models/Jobs/JobProgressAlert/JobProgressAlert';
import {
  JobProgressAlertType,
  returnProgressAlertTypeMessage,
} from '@/interface-models/Jobs/JobProgressAlert/JobProgressAlertType';
import { useDriverMessageStore } from '@/store/modules/DriverMessageStore';
import {
  computed,
  ComputedRef,
  onBeforeMount,
  ref,
  Ref,
  watch,
  WritableComputedRef,
} from 'vue';

const props = defineProps<{
  jobProgressAlertType: JobProgressAlertType;
}>();

const driverMessageStore = useDriverMessageStore();

const dialogIsOpen: Ref<boolean> = ref(false);
const dialogTitle: Ref<string> = ref('');
const buttonTitle: Ref<string> = ref('');

/**
 * Computed property for dialog visibility (two-way binding).
 */
const isViewingDialog: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return dialogIsOpen.value;
  },
  set(value: boolean) {
    dialogIsOpen.value = value;
  },
});

/**
 * Returns the correct job progress alerts map based on the alert type.
 */
const jobProgressAlertsMap: ComputedRef<Map<number, JobProgressAlert> | null> =
  computed(() => {
    switch (props.jobProgressAlertType) {
      case JobProgressAlertType.APPROACHING_PICKUP_TIME:
        return driverMessageStore.pickupProgressAlerts;
      case JobProgressAlertType.AWAITING_ACCEPT:
        return driverMessageStore.acceptProgressAlerts;
      case JobProgressAlertType.LOAD_TIME:
        return driverMessageStore.loadTimeProgressAlerts;
      default:
        return null;
    }
  });

/**
 * Returns a list of job progress alert details for the dialog.
 */
const jobProgressAlertList: ComputedRef<JobProgressAlertDetails[]> = computed(
  () => {
    if (!dialogIsOpen.value || jobProgressAlertsMap.value === null) {
      return [];
    }
    return transformJobProgressAlerts(
      Array.from(jobProgressAlertsMap.value.values()),
    );
  },
);

/**
 * Returns the total job count for the current alert type.
 */
const jobCount: ComputedRef<number> = computed(() => {
  if (!jobProgressAlertsMap.value) {
    return 0;
  }
  return jobProgressAlertsMap.value.size;
});

/**
 * Sets dialog and button titles based on the alert type.
 */
function setTitles(): void {
  const suffix = returnProgressAlertTypeMessage(props.jobProgressAlertType);
  if (suffix) {
    dialogTitle.value = `Jobs ${suffix}`;
  }
  switch (props.jobProgressAlertType) {
    case JobProgressAlertType.APPROACHING_PICKUP_TIME:
      buttonTitle.value = 'Pickup Soon ';
      break;
    case JobProgressAlertType.AWAITING_ACCEPT:
      buttonTitle.value = 'Not Accepted';
      break;
    case JobProgressAlertType.LOAD_TIME:
      buttonTitle.value = 'Long Load Time';
      break;
  }
}

onBeforeMount(() => {
  setTitles();
});

watch(
  () => props.jobProgressAlertType,
  () => {
    setTitles();
  },
);
</script>
<style scoped lang="scss">
.chat-alert-dialog {
  .button {
    position: relative;
    height: 75%;
    padding: 0px 8px;
    margin: 0px 1px;
    border-radius: 12px;
    background-color: rgba(79, 78, 90);
    color: $border-light-client;
    font-weight: 700;
    font-size: $font-size-small;

    .button-count {
      color: yellow;
      font-family: $sub-font-family;
      padding: 4px;
      font-weight: 700;
      font-size: $font-size-13;
    }
    &:hover {
      background-color: rgb(88, 87, 99);
      cursor: pointer;
    }
  }
}
</style>
