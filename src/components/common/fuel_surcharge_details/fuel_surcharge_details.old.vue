<template>
  <section class="fuel-surcharge-details">
    <v-layout justify-end class="mb-3">
      <v-btn
        depressed
        color="blue"
        class="mt-0"
        @click="newFuelSurcharge"
        :disabled="!isAuthorised()"
      >
        Create New
      </v-btn>
    </v-layout>
    <slot name="active-rates-summary"></slot>
    <v-data-table
      :headers="tableHeaders"
      class="gd-dark-theme mt-4"
      :items="allFuelSurchargeRatesReversed"
      no-data-text="No history of custom Fuel Surcharges available."
      hide-actions
    >
      <template v-slot:items="tableProps">
        <tr @click="editFuelSurcharge(tableProps.item)" style="cursor: pointer">
          <td
            :class="{
              activeDate: returnDateActive(
                tableProps.item.validFromDate,
                tableProps.item.validToDate,
                tableProps.item.tableId,
              ),
            }"
            class="pl-4"
          >
            {{ tableProps.item.fuelSurchargeRate }}%
          </td>
          <td
            :class="{
              activeDate: returnDateActive(
                tableProps.item.validFromDate,
                tableProps.item.validToDate,
                tableProps.item.tableId,
              ),
            }"
          >
            {{ returnFormattedDate(tableProps.item.validFromDate) }}
          </td>
          <td
            :class="{
              activeDate: returnDateActive(
                tableProps.item.validFromDate,
                tableProps.item.validToDate,
                tableProps.item.tableId,
              ),
            }"
          >
            {{ returnFormattedDate(tableProps.item.validToDate) }}
          </td>
          <td
            :class="{
              activeDate: returnDateActive(
                tableProps.item.validFromDate,
                tableProps.item.validToDate,
                tableProps.item.tableId,
              ),
            }"
          >
            <span
              v-if="
                returnDateActive(
                  tableProps.item.validFromDate,
                  tableProps.item.validToDate,
                  tableProps.item.tableId,
                )
              "
              >Yes</span
            >
            <span v-else> No </span>
          </td>
        </tr>
      </template>
    </v-data-table>

    <v-dialog
      v-model="dialogController"
      width="520px"
      class="ma-0"
      content-class="v-dialog-custom"
      persistent
      no-click-animation
    >
      <v-flex
        md12
        class="app-theme__center-content--body"
        v-if="dialogController"
      >
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight"
        >
          <span>{{
            viewType === 'NEW'
              ? `Create New Fuel Surcharge`
              : `Edit Fuel
          Surcharge`
          }}</span>
          <div
            class="app-theme__center-content--closebutton"
            @click="dialogController = false"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>
        <v-layout row class="pa-3">
          <v-flex md12>
            <v-form ref="fuelSurchargeForm">
              <v-alert
                :value="validFromEdits.edit || validToEdits.edit"
                type="warning"
                class="mb-4"
              >
                The selected dates overlap with an existing Fuel Surcharge. If
                you proceed, the overlapping Fuel Surcharges will have their
                dates adjusted to remove any crossover.
              </v-alert>
              <v-layout wrap>
                <v-flex md12>
                  <v-layout>
                    <v-flex md5>
                      <v-layout align-center class="form-field-label-container">
                        <span
                          class="subheader--faded pr-3 pb-0 form-field-required-marker"
                          >Fuel Surcharge Rate %</span
                        >
                      </v-layout>
                    </v-flex>
                    <!-- TODO: FEAP-182 -->
                    <!-- <v-flex md7>
                      <v-text-field
                        class="v-solo-custom"
                        solo
                        flat
                        type="number"
                        v-model.number="fuelSurchargeRate.fuelSurchargeRate"
                        :rules="[
                          validationRules.required,
                          validationRules.percentage,
                          validationRules.nonNegative,
                        ]"
                        autofocus
                      >
                      </v-text-field>
                    </v-flex> -->
                  </v-layout>
                </v-flex>
                <v-flex md12>
                  <v-layout>
                    <v-flex md5>
                      <v-layout align-center class="form-field-label-container">
                        <span
                          class="subheader--faded pr-3 pb-0 form-field-required-marker"
                          >Valid From Date</span
                        >
                      </v-layout>
                    </v-flex>
                    <v-flex md7>
                      <!-- TODO: FEAP-182 -->
                      <!-- <DateTimeInputs
                        :epochTime.sync="validFromDate"
                        :enableValidation="true"
                        type="DATE_START_OF_DAY"
                        dateLabel="Valid From"
                        :readOnly="!isEdited"
                        :soloInput="true"
                        :boxInput="false"
                        :isRequired="true"
                        hintTextType="FORMATTED_SELECTION"
                        :minimumEpochTime="allowedDates.from.min"
                        :maximumEpochTime="allowedDates.from.max"
                        maxComparisonType="LESS_OR_EQUAL"
                      ></DateTimeInputs> -->
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex md12>
                  <v-layout>
                    <v-flex md5>
                      <v-layout align-center class="form-field-label-container">
                        <span
                          class="subheader--faded pr-3 pb-0 form-field-required-marker"
                          >Valid To Date</span
                        >
                      </v-layout>
                    </v-flex>
                    <v-flex md7>
                      <!-- TODO: FEAP-182 -->
                      <!-- <DateTimeInputs
                        :epochTime.sync="validToDate"
                        :enableValidation="true"
                        type="DATE_END_OF_DAY"
                        dateLabel="Valid To Date"
                        :readOnly="!isEdited"
                        :soloInput="true"
                        :boxInput="false"
                        :isRequired="true"
                        hintTextType="FORMATTED_SELECTION"
                        :minimumEpochTime="allowedDates.to.min"
                        :maximumEpochTime="
                          allowedDates.to.max ? allowedDates.to.max : null
                        "
                      ></DateTimeInputs> -->
                    </v-flex>
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-form>
          </v-flex>
        </v-layout>
        <v-divider></v-divider>
        <v-layout align-center>
          <v-btn flat color="red" @click="dialogController = false"
            >Cancel</v-btn
          >
          <v-spacer></v-spacer>
          <!-- <v-btn outline color="white" @click="">Edit</v-btn> -->
          <v-btn
            depressed
            color="blue"
            class="v-btn-confirm-custom"
            @click="saveFuelSurcharge"
            :loading="awaitingSaveResponse"
            >Save</v-btn
          >
        </v-layout>
      </v-flex>
    </v-dialog>
  </section>
</template>

<script setup lang="ts">
type FuelDetails =
  | {
      type: RateEntityType.CLIENT;
      clientId: string;
      fuelSurchargeRates: ClientFuelSurchargeRate[];
    }
  | {
      type: RateEntityType.FLEET_ASSET;
      fleetAssetId: string;
      fuelSurchargeRates: FleetAssetFuelSurchargeRate[];
    };

import { RoundCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import FleetAssetFuelSurchargeRate from '@/interface-models/ServiceRates/FleetAssetOwner/AdditionalCharges/FuelSurcharge/FleetAssetFuelSurchargeRate';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useFuelLevyStore } from '@/store/modules/FuelLevyStore';
import moment from 'moment-timezone';
import { computed, ComputedRef, ref, Ref } from 'vue';

enum ViewType {
  EDIT = 'EDIT',
  NEW = 'NEW',
  TABLE = 'TABLE',
}

// Props
const props = defineProps<{
  config: FuelDetails;
  activeFuelSurchargeTableId?: number | null;
  isEdited?: boolean;
}>();

// Emits
const emit = defineEmits<{
  (event: 'refreshServiceRateList'): void;
}>();

// Stores
const companyDetailsStore = useCompanyDetailsStore();

// State
const awaitingSaveResponse: Ref<boolean> = ref(false);
const viewType: Ref<ViewType> = ref(ViewType.TABLE);
const fuelSurchargeRate: Ref<
  ClientFuelSurchargeRate | FleetAssetFuelSurchargeRate | null
> = ref(null);

// Table headers
const tableHeaders: TableHeader[] = [
  { text: 'Rate', align: 'left', value: 'result', sortable: false },
  { text: 'Valid From', align: 'left', value: 'result', sortable: false },
  { text: 'Valid To', align: 'left', value: '', sortable: false },
  { text: 'Active', align: 'left', value: '', sortable: false },
];

const fuelSurchargeForm: Ref<any> = ref(null);

/**
 * Controls dialog visibility, and resets local working variables on close.
 */
const dialogController = computed<boolean>({
  get() {
    return viewType.value === ViewType.NEW || viewType.value === ViewType.EDIT;
  },
  set(value: boolean) {
    if (!value) {
      cancelCreateOrEditFuelSurcharge();
    }
  },
});

/**
 * Returns all fuel surcharge rates in reverse order.
 */
const allFuelSurchargeRatesReversed: ComputedRef<
  ClientFuelSurchargeRate[] | FleetAssetFuelSurchargeRate[]
> = computed(() => {
  if (props.config.type === RateEntityType.CLIENT) {
    return [...props.config.fuelSurchargeRates].reverse();
  } else {
    return [...props.config.fuelSurchargeRates].reverse();
  }
});

/**
 * Returns the FuelSurchargeRate with the lowest validFromDate.
 */
const earliestFuelSurcharge: ComputedRef<
  ClientFuelSurchargeRate | FleetAssetFuelSurchargeRate | undefined
> = computed(() => {
  if (
    !props.config.fuelSurchargeRates.length ||
    !latestFuelSurcharge.value ||
    !latestFuelSurcharge.value.validFromDate
  ) {
    return undefined;
  }
  return props.config.fuelSurchargeRates.reduce((prev, curr) =>
    curr.validFromDate &&
    curr.validToDate &&
    curr.validFromDate < (prev.validFromDate ? prev.validFromDate : 0)
      ? curr
      : prev,
  );
});

/**
 * Returns the FuelSurchargeRate with the highest validToDate.
 */
const latestFuelSurcharge: ComputedRef<
  ClientFuelSurchargeRate | FleetAssetFuelSurchargeRate | undefined
> = computed(() => {
  if (!props.config.fuelSurchargeRates.length) {
    return undefined;
  }
  return props.config.fuelSurchargeRates.reduce((prev, curr) =>
    curr.validFromDate !== null &&
    curr.validToDate !== null &&
    curr.validToDate > (prev.validToDate ? prev.validToDate : 0)
      ? curr
      : prev,
  );
});

/**
 * Returns the previous FuelSurchargeRate before the currently edited one.
 */
const previousFuelSurcharge: ComputedRef<
  ClientFuelSurchargeRate | FleetAssetFuelSurchargeRate | undefined
> = computed(() => {
  if (!fuelSurchargeRate.value) {
    return undefined;
  }
  const id = fuelSurchargeRate.value.id;
  const found = props.config.fuelSurchargeRates.find(
    (charge) => charge.id === id,
  );
  if (found) {
    return props.config.fuelSurchargeRates.find(
      (charge) =>
        charge.validToDate! < found.validFromDate! &&
        charge.validToDate! > found.validFromDate! - 80400000,
    );
  } else {
    return latestFuelSurcharge.value;
  }
});

/**
 * Returns the next FuelSurchargeRate after the currently edited one.
 */
const nextFuelSurcharge: ComputedRef<
  ClientFuelSurchargeRate | FleetAssetFuelSurchargeRate | undefined
> = computed(() => {
  if (!fuelSurchargeRate.value) {
    return undefined;
  }
  const id = fuelSurchargeRate.value.id;
  const found = props.config.fuelSurchargeRates.find(
    (charge) => charge.id === id,
  );
  if (found) {
    return props.config.fuelSurchargeRates.find(
      (charge) =>
        charge.validFromDate! > found.validToDate! &&
        charge.validFromDate! < found.validToDate! + 80400000,
    );
  }
  return undefined;
});

/**
 * Returns allowed date ranges for the DateTimeInputs components.
 */
const allowedDates = computed(() => {
  if (!fuelSurchargeRate.value) {
    return;
  }
  const tz = companyDetailsStore.userLocale;
  const formatDate = (t: number) => moment(t).tz(tz).valueOf();
  const formatDateSub = (t: number) =>
    moment(t).tz(tz).subtract(1, 'day').valueOf();
  const formatDateAdd = (t: number) => moment(t).tz(tz).add(1, 'day').valueOf();
  const min = moment.tz(tz).subtract(5, 'year');
  const fsr = fuelSurchargeRate.value;
  const latest = latestFuelSurcharge.value;
  const earliest = earliestFuelSurcharge.value;
  const prev = previousFuelSurcharge.value;
  const next = nextFuelSurcharge.value;

  let dates: any = {
    from: {
      min: min.valueOf(),
      max: !fsr || !fsr.validToDate ? undefined : formatDate(fsr.validToDate),
    },
    to: {
      min:
        !fsr || fsr.validFromDate === null
          ? undefined
          : formatDate(fsr.validFromDate),
      max: undefined,
    },
  };

  if (props.config.fuelSurchargeRates.length === 0) {
    return dates;
  }
  if (viewType.value !== ViewType.EDIT) {
    dates = {
      from: {
        min:
          latest && latest.validFromDate
            ? formatDateAdd(latest.validFromDate)
            : undefined,
        max: !fsr.validToDate ? undefined : formatDateSub(fsr.validToDate + 1),
      },
      to: {
        min:
          fsr.validFromDate === null
            ? undefined
            : formatDate(fsr.validFromDate),
        max: undefined,
      },
    };
    return dates;
  }
  if (props.config.fuelSurchargeRates.length > 1) {
    if (latest && fsr.id === latest.id) {
      dates = {
        from: {
          min:
            prev && prev.validFromDate
              ? formatDateAdd(prev.validFromDate)
              : undefined,
          max: !fsr.validToDate ? undefined : formatDate(fsr.validToDate),
        },
        to: {
          min:
            fsr.validFromDate === null
              ? undefined
              : formatDate(fsr.validFromDate),
          max: undefined,
        },
      };
    } else if (earliest && fsr.id === earliest.id) {
      dates = {
        from: {
          min: min.valueOf(),
          max: !fsr.validToDate ? undefined : formatDateSub(fsr.validToDate),
        },
        to: {
          min:
            fsr.validFromDate === null
              ? undefined
              : formatDate(fsr.validFromDate),
          max:
            next && next.validToDate
              ? formatDateSub(next.validToDate)
              : undefined,
        },
      };
    } else {
      dates = {
        from: {
          min:
            prev && prev.validFromDate
              ? formatDateAdd(prev.validFromDate)
              : undefined,
          max: !fsr.validToDate
            ? next && next.validFromDate
              ? formatDateSub(next.validFromDate)
              : undefined
            : formatDate(fsr.validToDate),
        },
        to: {
          min: !fsr.validFromDate ? undefined : formatDate(fsr.validFromDate),
          max:
            next && next.validToDate
              ? formatDateSub(next.validToDate)
              : undefined,
        },
      };
    }
  }
  return dates;
});

/**
 * Stores the editing to the FuelSurchargeRate that is BEFORE the currently edited item.
 */
const validToEdits = computed(() => {
  if (!fuelSurchargeRate.value) {
    return;
  }
  const fsr = fuelSurchargeRate.value;
  const next = nextFuelSurcharge.value;
  const id = fsr.id;
  let foundEditedFuelSurcharge = props.config.fuelSurchargeRates.find(
    (charge) => charge.id === id,
  );
  if (viewType.value !== ViewType.EDIT) {
    foundEditedFuelSurcharge = fsr;
  }
  const requiredEdits: any = {
    edit: false,
    surchargeId: '',
    validFromValue: 0,
  };
  if (
    fsr.validToDate &&
    next &&
    next.validFromDate &&
    foundEditedFuelSurcharge &&
    foundEditedFuelSurcharge.validToDate
  ) {
    if (
      fsr.validToDate >= next.validFromDate ||
      fsr.validToDate < foundEditedFuelSurcharge.validToDate
    ) {
      requiredEdits.edit = true;
      requiredEdits.surchargeId = next.id;
      requiredEdits.validFromValue = moment(fsr.validToDate)
        .tz(companyDetailsStore.userLocale)
        .add(1, 'day')
        .startOf('day')
        .valueOf();
    }
  }
  return requiredEdits;
});

/**
 * Stores the editing to the FuelSurchargeRate that is AFTER the currently edited item.
 */
const validFromEdits: Ref<{
  edit: boolean;
  surchargeId: string;
  validToValue: number;
}> = computed(() => {
  if (!fuelSurchargeRate.value) {
    return;
  }
  const id = fuelSurchargeRate.value.id;
  let foundEditedFuelSurcharge = props.config.fuelSurchargeRates.find(
    (charge) => charge.id === id,
  );
  if (viewType.value !== ViewType.EDIT) {
    foundEditedFuelSurcharge = fuelSurchargeRate.value;
  }
  const requiredEdits: any = {
    edit: false,
    surchargeId: '',
    validToValue: 0,
  };
  if (
    fuelSurchargeRate.value.validFromDate &&
    previousFuelSurcharge.value &&
    previousFuelSurcharge.value.validToDate &&
    foundEditedFuelSurcharge &&
    foundEditedFuelSurcharge.validFromDate
  ) {
    if (
      fuelSurchargeRate.value.validFromDate <=
        previousFuelSurcharge.value.validToDate ||
      fuelSurchargeRate.value.validFromDate >
        foundEditedFuelSurcharge.validFromDate
    ) {
      requiredEdits.edit = true;
      requiredEdits.surchargeId = previousFuelSurcharge.value.id;
      requiredEdits.validToValue = moment(fuelSurchargeRate.value.validFromDate)
        .tz(companyDetailsStore.userLocale)
        .subtract(1, 'day')
        .endOf('day')
        .valueOf();
    }
  }
  return requiredEdits;
});

/**
 * Returns current validFromDate from fuelSurchargeRate. Used as v-model for DateTimeInput.
 */
const validFromDate = computed<number | null>({
  get() {
    return fuelSurchargeRate.value
      ? fuelSurchargeRate.value.validFromDate
      : null;
  },
  set(epoch: number | null) {
    if (fuelSurchargeRate.value) {
      fuelSurchargeRate.value.validFromDate = epoch;
    }
  },
});

/**
 * Returns current validToDate from fuelSurchargeRate. Used as v-model for DateTimeInput.
 */
const validToDate = computed<number | null>({
  get() {
    return fuelSurchargeRate.value ? fuelSurchargeRate.value.validToDate : null;
  },
  set(epoch: number | null) {
    if (fuelSurchargeRate.value) {
      fuelSurchargeRate.value.validToDate = epoch;
    }
  },
});

/**
 * Returns true if the given date range is active.
 */
function returnDateActive(
  fromDate: number,
  toDate: number,
  tableId: number,
): boolean {
  if (tableId === props.activeFuelSurchargeTableId) {
    return true;
  }
  const currentTime = moment().valueOf();
  return currentTime >= fromDate && currentTime <= toDate;
}

/**
 * Set fuelSurchargeRate to clean rate, set type and init with id.
 */
function newFuelSurcharge(): void {
  viewType.value = ViewType.NEW;

  if (props.config.type === RateEntityType.CLIENT) {
    fuelSurchargeRate.value = new ClientFuelSurchargeRate({
      clientIds: [props.config.clientId],
    });
  } else {
    fuelSurchargeRate.value = new FleetAssetFuelSurchargeRate({
      fleetAssetIds: [props.config.fleetAssetId],
    });
  }
  if (latestFuelSurcharge.value && latestFuelSurcharge.value.validToDate) {
    fuelSurchargeRate.value.validFromDate = moment(
      latestFuelSurcharge.value.validToDate,
    )
      .tz(companyDetailsStore.userLocale)
      .add(1, 'day')
      .startOf('day')
      .valueOf();
  }
}

/**
 * Clear working variable and set view back to TABLE.
 */
function cancelCreateOrEditFuelSurcharge(): void {
  viewType.value = ViewType.TABLE;
  fuelSurchargeRate.value = null;
}

/**
 * Save the current editing FuelSurchargeRate, update any adjacent fuel surcharges that overlap with the new values.
 */
async function saveFuelSurcharge(): Promise<void> {
  if (!fuelSurchargeForm.value?.validate()) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }
  if (!fuelSurchargeRate.value) {
    return;
  }
  awaitingSaveResponse.value = true;
  // Save edits to previous
  if (validFromEdits.value.edit) {
    if (props.config.type === RateEntityType.FLEET_ASSET) {
      const foundFuelSurcharge = props.config.fuelSurchargeRates.find(
        (charge) => charge.id === validFromEdits.value.surchargeId,
      );
      if (foundFuelSurcharge) {
        foundFuelSurcharge.validToDate = validFromEdits.value.validToValue;
        useFuelLevyStore().saveFleetAssetFuelSurchargeRate(foundFuelSurcharge);
      }
    } else {
      const foundFuelSurcharge = props.config.fuelSurchargeRates.find(
        (charge) => charge.id === validFromEdits.value.surchargeId,
      );
      if (foundFuelSurcharge) {
        foundFuelSurcharge.validToDate = validFromEdits.value.validToValue;
        useFuelLevyStore().saveClientFuelSurchargeRate(foundFuelSurcharge);
      }
    }
  }
  // Save edits to next
  if (validToEdits.value.edit) {
    if (props.config.type === RateEntityType.FLEET_ASSET) {
      const foundFuelSurcharge = props.config.fuelSurchargeRates.find(
        (charge) => charge.id === validToEdits.value.surchargeId,
      );
      if (foundFuelSurcharge) {
        foundFuelSurcharge.validFromDate = validToEdits.value.validFromValue;
        useFuelLevyStore().saveFleetAssetFuelSurchargeRate(foundFuelSurcharge);
      }
    } else {
      const foundFuelSurcharge = props.config.fuelSurchargeRates.find(
        (charge) => charge.id === validToEdits.value.surchargeId,
      );
      if (foundFuelSurcharge) {
        foundFuelSurcharge.validFromDate = validToEdits.value.validFromValue;
        useFuelLevyStore().saveClientFuelSurchargeRate(foundFuelSurcharge);
      }
    }
  }
  let saved = false;
  if (props.config.type === RateEntityType.FLEET_ASSET) {
    const toSave = fuelSurchargeRate.value as FleetAssetFuelSurchargeRate;
    toSave.appliedFuelSurchargeRate = RoundCurrencyValue(
      toSave.appliedFuelSurchargeRate,
    );

    const result =
      await useFuelLevyStore().saveFleetAssetFuelSurchargeRate(toSave);
    saved = !!result;
  } else {
    const toSave = fuelSurchargeRate.value as ClientFuelSurchargeRate;
    const result = await useFuelLevyStore().saveClientFuelSurchargeRate(toSave);
    saved = !!result;
  }
  if (saved) {
    viewType.value = ViewType.TABLE;
    awaitingSaveResponse.value = false;
    emit('refreshServiceRateList');
  }
}

/**
 * Copy the selected FuelSurchargeRate to the local working variable, then change the view to edit mode.
 */
function editFuelSurcharge(
  fuelSurcharge: ClientFuelSurchargeRate | FleetAssetFuelSurchargeRate,
): void {
  if (!isAuthorised()) {
    return;
  }
  viewType.value = ViewType.EDIT;
  if (fuelSurcharge instanceof ClientFuelSurchargeRate) {
    fuelSurchargeRate.value = new ClientFuelSurchargeRate(fuelSurcharge);
  } else if (fuelSurcharge instanceof FleetAssetFuelSurchargeRate) {
    fuelSurchargeRate.value = Object.assign(
      new FleetAssetFuelSurchargeRate(),
      fuelSurcharge,
    );
  }
}

/**
 * Returns true if the user is authorised.
 */
function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}
</script>
<style scoped lang="scss">
.fuel-surcharge-details {
  .activeDate {
    background-color: #d98201 !important;
  }
}
</style>
