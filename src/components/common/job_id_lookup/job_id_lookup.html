<div class="job-id-lookup">
  <!-- SINGLE  -->
  <v-autocomplete
    v-if="!multi"
    label="Job ID Lookup"
    v-model="currentResultSingleController"
    :items="jobIdLookupResults"
    :box="boxInput"
    return-object
    item-text="description"
    :search-input.sync="jobIdInputController"
    auto-select-first
    color="orange"
    :solo="soloInput"
    :flat="soloInput"
    :class="soloInput ? 'v-solo-custom' : ''"
    :loading="!!awaitingResponseId"
    clearable
    placeholder="Type a Job No. to Search..."
    @click:clear="clearResults"
    no-data-text="No results."
    :hint="hintText ? hintText : ''"
    :persistent-hint="!!hintText"
    :disabled="readOnly"
  >
    <template slot="selection" slot-scope="data">
      <strong
        >{{ data.item.recurringJobId ? data.item.recurringJobId :
        data.item.jobId }}</strong
      >
    </template>
  </v-autocomplete>
  <!-- MULTI -->
  <v-autocomplete
    v-if="multi"
    ref="jobIdLookupAutocompleteMulti"
    label="Job ID Lookup"
    v-model="currentResultMultiController"
    :items="jobIdLookupResults"
    :box="boxInput"
    return-object
    item-text="description"
    :search-input.sync="jobIdInputController"
    auto-select-first
    color="orange"
    :solo="soloInput"
    :flat="soloInput"
    multiple
    :class="soloInput ? 'v-solo-custom' : ''"
    :loading="!!awaitingResponseId"
    placeholder="Type a Job No. to Search..."
    no-data-text="No results."
    :hint="hintText ? hintText : ''"
    :persistent-hint="!!hintText"
    :disabled="readOnly"
  >
    <template v-slot:selection="{ item, index }">
      <v-chip
        :color="!readOnly ? 'green' : 'grey darken-3'"
        close
        @input="removeJobFromSelection(item.jobId)"
      >
        <span
          >{{ item.recurringJobId ? item.recurringJobId : item.jobId }}</span
        >
      </v-chip>
    </template>
  </v-autocomplete>
</div>
