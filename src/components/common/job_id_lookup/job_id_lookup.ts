import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { JobIdLookupConfig } from '@/interface-models/Jobs/JobIdLookup/JobIdLookupConfig';
import { JobIdLookupSummary } from '@/interface-models/Jobs/JobIdLookup/JobIdLookupSummary';
import { JobIdQuickLookupRequest } from '@/interface-models/Jobs/JobIdLookup/JobIdQuickLookupRequest';
import { JobIdQuickLookupResponse } from '@/interface-models/Jobs/JobIdLookup/JobIdQuickLookupResponse';
import { useJobStatisticsStore } from '@/store/modules/JobStatisticsStore';
import { sessionManager } from '@/store/session/SessionState';
import { v4 as uuidv4 } from 'uuid';
import { Component, Prop, Vue } from 'vue-property-decorator';

interface JobIdLookupResult extends JobIdLookupSummary {
  description: string;
}
@Component({
  components: {},
})
export default class JobIdLookup extends Vue {
  // Either jobId or jobIdList should be supplied.
  @Prop() public jobId: number | undefined;
  @Prop({ default: () => [] }) public jobIdList: number[];
  // If multi is true, then we will use a multi-select type autocomplete.
  // jobIdList prop should be supplied in this case
  @Prop({ default: false }) public multi: boolean;

  @Prop({ default: false }) public soloInput: boolean;
  @Prop({ default: false }) public boxInput: boolean;
  @Prop() public hintText: string;
  @Prop() public lookupConfig: JobIdLookupConfig | undefined;
  @Prop({ default: false }) public readOnly: boolean;

  public returnFormattedDate: (epoch: number) => string = returnFormattedDate;

  public jobIdSummaryList: JobIdLookupSummary[] = [];

  public awaitingResponseId: string = '';
  public currentRequest: JobIdQuickLookupRequest | undefined;
  public selectedSingleResult: JobIdLookupResult | undefined;
  public selectedMultiResultList: JobIdLookupResult[] = [];

  public jobIdTimeout: ReturnType<typeof setTimeout>;

  public jobIdSearchInput: string | undefined;

  public $refs: {
    jobIdLookupAutocomplete: any;
    jobIdLookupAutocompleteMulti: any;
  };

  get jobIdLookupResults(): JobIdLookupResult[] {
    let searchResults = this.addDescriptionToJobIdLookup(this.jobIdSummaryList);

    if (this.selectedSingleResult) {
      const foundIndex = searchResults.findIndex(
        (r) => r.jobId === this.selectedSingleResult!.jobId,
      );
      if (foundIndex === -1) {
        searchResults.push(this.selectedSingleResult);
      }
    } else if (this.selectedMultiResultList.length) {
      this.selectedMultiResultList.forEach((result) => {
        const foundIndex = searchResults.findIndex(
          (r) => r.jobId === result.jobId,
        );
        if (foundIndex === -1) {
          searchResults.push(result);
        }
      });
      searchResults = searchResults.concat(this.selectedMultiResultList);
    }
    return searchResults;
  }
  // V-modelled to autocomplete when in SINGLE view (multi is false)
  get currentResultSingleController(): JobIdLookupResult | undefined {
    return this.selectedSingleResult;
  }
  set currentResultSingleController(selection: JobIdLookupResult | undefined) {
    this.jobIdSync = selection ? selection.jobId : undefined;
    this.selectedSingleResult = selection;
  }

  // V-modelled to autocomplete when in MULTI view (multi is TRUE)
  get currentResultMultiController(): JobIdLookupResult[] {
    return this.selectedMultiResultList;
  }
  set currentResultMultiController(selection: JobIdLookupResult[]) {
    if (!selection) {
      this.jobIdSync = [];
      this.selectedMultiResultList = [];
      return;
    }
    this.jobIdSync = selection.map((s) => s.jobId);
    this.selectedMultiResultList = selection;
  }

  get jobIdSync(): number | number[] | undefined {
    if (this.multi) {
      return this.jobIdList;
    } else {
      return this.jobId;
    }
  }
  set jobIdSync(value: number | number[] | undefined) {
    if (this.multi && Array.isArray(value)) {
      this.jobIdInputController = '';
      this.setAutocompleteLazySearchValue('');
      this.$emit('update:jobIdList', value);
    } else if (!this.multi && !Array.isArray(value)) {
      this.$emit('update:jobId', value);
    }
  }

  get jobIdInputController(): string | undefined {
    return this.jobIdSearchInput;
  }
  set jobIdInputController(value: string | undefined) {
    if (this.readOnly) {
      return;
    }
    this.searchForJobId(value);
    this.jobIdSearchInput = value;
  }
  // Dispatch the search request after a given timeout, to prevent duplicate
  // queries from being sent out as the user types
  public searchForJobId(jobId: string | undefined | null) {
    if (jobId !== null && jobId !== undefined && jobId.length > 0) {
      if (
        this.currentResultSingleController &&
        (jobId === `${this.currentResultSingleController.jobId}` ||
          jobId === `${this.currentResultSingleController.recurringJobId}`)
      ) {
        return;
      }
      clearTimeout(this.jobIdTimeout);
      this.jobIdTimeout = setTimeout(() => {
        this.sendSearchRequest(jobId);
      }, 180);
    } else {
      this.setCurrentRequestFromConfig();
    }
  }

  /**
   * Handles and response for fetching a list of Job Summaries using a provided
   * search string for jobId
   * @param jobId
   */
  public async sendSearchRequest(jobId: string) {
    const req = this.returnLookupRequest(jobId);
    const response =
      await useJobStatisticsStore().sendJobIdQuickLookupRequest(req);
    this.handleJobLookupResponse(response);
  }

  /**
   * Handles the response from the job lookup API.
   * @param response - The response from the job lookup API.
   */
  public handleJobLookupResponse(response: JobIdQuickLookupResponse | null) {
    if (response) {
      this.jobIdSummaryList = response.summaryList ?? [];
      // If we're in readonly mode for multi, then also set selectedMultiResultList
      if (this.readOnly && this.multi) {
        this.selectedMultiResultList = this.addDescriptionToJobIdLookup(
          response.summaryList,
        );
      }
      this.awaitingResponseId = '';
    } else {
      // Display error
      showNotification(GENERIC_ERROR_MESSAGE);
    }
  }

  public returnLookupRequest(jobId: string): JobIdQuickLookupRequest {
    if (this.currentRequest === undefined) {
      this.setCurrentRequestFromConfig();
    }
    this.awaitingResponseId = uuidv4();
    return {
      resId: this.awaitingResponseId,
      jobId,
      ...this.currentRequest!,
    };
  }
  // Used to update the lazyValue and initialValue in the v-autocomplete
  // component. This is necessary because the component cannot detect external
  // changes to the v-modelled array, and hence the chips will not reflect the
  // data change
  public setInitialValueMultiFromParent(jobSummaryList: JobIdLookupSummary[]) {
    // Add description to summary list
    const resultList = this.addDescriptionToJobIdLookup(jobSummaryList);
    // Find component using ref
    const foundComponent = this.$refs.jobIdLookupAutocompleteMulti;
    if (foundComponent) {
      // Add to local selected list
      resultList.forEach((r) => {
        const foundMatch = this.selectedMultiResultList.find(
          (i) => i.jobId === r.jobId,
        );
        if (!foundMatch) {
          this.selectedMultiResultList.push(r);
        }
      });
      // Update local results list and the lazyValue and initialValue
      this.jobIdSummaryList = [...resultList];
      foundComponent.lazyValue = [...resultList];
      foundComponent.initialValue = [...resultList];
    }
  }
  // Used to update the lazySearch property in the v-autocomplete component.
  // This is necessary because the component cannot detect external changes to
  // the v-modelled array, and so when a selection is made we need to manually
  // clear it
  public setAutocompleteLazySearchValue(value: string) {
    if (this.multi) {
      // Find component using ref
      const foundComponent = this.$refs.jobIdLookupAutocompleteMulti;
      if (foundComponent) {
        foundComponent.lazySearch = value;
      }
    }
  }
  // Removes the selected jobId from the current selection. Used when the delete
  // button on the v-autocomplete chips is used
  public removeJobFromSelection(jobId: number) {
    this.currentResultMultiController =
      this.currentResultMultiController.filter((r) => r.jobId !== jobId);
  }

  public clearResults() {
    this.currentResultSingleController = undefined;
    this.awaitingResponseId = '';
    this.jobIdSummaryList = [];
  }

  public addDescriptionToJobIdLookup(summaryList: JobIdLookupSummary[]) {
    return summaryList.map((job) => {
      return {
        ...job,
        description: `${
          job.recurringJobId ? job.recurringJobId : job.jobId
        } (${this.returnFormattedDate(job.jobDate)})`,
      };
    });
  }

  public async setCurrentRequestFromConfig() {
    this.currentRequest = {
      company: sessionManager.getCompanyId(),
      division: sessionManager.getDivisionId(),
      ...this.lookupConfig,
    };
    if (
      this.multi &&
      this.readOnly &&
      this.jobIdList &&
      this.jobIdList.length
    ) {
      this.awaitingResponseId = uuidv4();
      const req: JobIdQuickLookupRequest = {
        company: sessionManager.getCompanyId(),
        division: sessionManager.getDivisionId(),
        resId: this.awaitingResponseId,
        jobIdList: this.jobIdList,
      };
      const response =
        await useJobStatisticsStore().sendJobIdQuickLookupRequest(req);
      this.handleJobLookupResponse(response);
    }
  }

  public mounted() {
    this.setCurrentRequestFromConfig();
  }
}
