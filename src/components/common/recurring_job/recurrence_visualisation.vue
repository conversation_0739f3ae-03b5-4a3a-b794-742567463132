<template>
  <section column class="recurrence-visualisation">
    <div
      class="no-data-text"
      v-if="!recurringJobDetails.nextScheduledRecurrence"
    >
      <span>Not Available. Please set a start date.</span>
    </div>
    <v-layout v-else class="section-container">
      <v-flex md11 pa-3>
        <v-layout justify-space-between class="detailitem">
          <span class="detailitem__header form-field-required-marker"
            >{{ isNewRecurringJob ? 'First' : 'Next' }} Booking Date:</span
          >
          <span class="detailitem__value large-value">{{
            recurringJobDetails.nextScheduledRecurrence
              ? returnFormattedDate(
                  recurringJobDetails.nextScheduledRecurrence,
                  'ddd DD/MM/YY',
                )
              : 'Not Set'
          }}</span>
        </v-layout>
        <v-layout py-2><v-divider></v-divider></v-layout>
        <v-layout justify-space-between class="detailitem">
          <span class="detailitem__header form-field-required-marker"
            >Active from:</span
          >
          <span class="detailitem__value large-value">{{
            recurringJobDetails.commencementDate
              ? returnFormattedDate(
                  recurringJobDetails.commencementDate,
                  'DD/MM/YY',
                )
              : 'Not Set'
          }}</span>
        </v-layout>
        <v-layout justify-space-between class="detailitem">
          <span class="detailitem__header">Ends:</span>
          <span class="detailitem__value large-value">{{
            returnedEndDate
          }}</span>
        </v-layout>
        <v-layout justify-space-between class="detailitem">
          <span class="detailitem__header">On Public Holidays:</span>
          <span class="detailitem__value large-value">{{
            returnPublicHolidayName(recurringJobDetails.holidayPreference)
          }}</span>
        </v-layout>
      </v-flex>
      <v-flex md5>
        <v-layout
          v-for="day in validDaysOfWeek"
          :key="day.id"
          justify-space-between
          align-center
          class="days-of-week"
          :class="day.isValid ? 'active-day' : 'inactive'"
        >
          <span class="day-label">{{ day.longName }} </span>
          <span v-if="day.isValid"
            ><v-icon color="black" class="pb-1" size="11"
              >fas fa-check</v-icon
            ></span
          >
          <span v-else
            ><v-icon color="grey darken-2" class="pb-1" size="11"
              >fas fa-times</v-icon
            ></span
          >
        </v-layout>
      </v-flex>
    </v-layout>
  </section>
</template>

<script setup lang="ts">
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import daysOfWeek, {
  DaysOfWeek,
} from '@/interface-models/Generic/DaysOfWeek/DaysOfWeek';
import publicHolidayDeliveryOptions from '@/interface-models/Generic/PublicHolidayDeliveryOptions/PublicHolidayDeliveryOptions';
import RecurringJobDetails from '@/interface-models/Jobs/RecurringJobDetails';
import { ComputedRef, Ref, computed, onMounted, ref, toRef } from 'vue';

interface ValidDaysOfWeek extends DaysOfWeek {
  isValid: boolean;
}

interface IProps {
  recurringJobDetails: RecurringJobDetails;
  isNewRecurringJob?: boolean;
}
const props = withDefaults(defineProps<IProps>(), {
  isNewRecurringJob: true,
});

const recurrenceDays: Ref<ValidDaysOfWeek[]> = ref([]);

const recurringJobDetails: Ref<RecurringJobDetails> = toRef(
  props,
  'recurringJobDetails',
);

/**
 * Returns a list of ValidDaysOfWeek based on the repeatModifier and repeatDays.
 * Iterated in the template to display a div for each day of the week, with
 * specific styling based on whether a recurrence will happen on that day
 */
const validDaysOfWeek: ComputedRef<ValidDaysOfWeek[]> = computed(() => {
  if (props.recurringJobDetails.repeatModifier === 1) {
    for (const day of recurrenceDays.value) {
      day.isValid = true;
    }
    return recurrenceDays.value;
  }
  if (props.recurringJobDetails.repeatModifier === 2) {
    for (const day of recurrenceDays.value) {
      if (props.recurringJobDetails.repeatDays.includes(day.id)) {
        day.isValid = true;
      } else {
        day.isValid = false;
      }
    }
    return recurrenceDays.value;
  }

  return recurrenceDays.value;
});

/**
 * Returns the longName of the public holiday delivery option (when a job will occur if it lands on a PH) based on the id
 * @param id the id of the public holiday delivery option
 */
function returnPublicHolidayName(id: number) {
  return (
    publicHolidayDeliveryOptions.find((ph) => ph.id === id)?.longName ?? '-'
  );
}

/**
 * Returns a string describing the 'end date' of the permanent job. This can be a date, a number of bookings, or 'Never'
 */
const returnedEndDate: ComputedRef<string> = computed(() => {
  const endOnType = props.recurringJobDetails.endOnType;
  if (endOnType === 2) {
    return props.recurringJobDetails.endDate
      ? returnFormattedDate(props.recurringJobDetails.endDate)
      : '-';
  } else if (endOnType === 3) {
    return `After ${props.recurringJobDetails.endAfter} Bookings`;
  } else if (endOnType === 1) {
    return 'Never';
  } else {
    return 'N/A';
  }
});

/**
 * On mounted, set the recurrenceDays ref to a list of days of the week, with
 * each day having a boolean 'isValid' property by default
 */
onMounted(() => {
  recurrenceDays.value = daysOfWeek.map((d) => {
    return {
      id: d.id,
      shortName: d.shortName,
      longName: d.longName,
      isWeekday: d.isWeekday,
      isValid: true,
    };
  });
});
</script>

<style scoped lang="scss">
.recurrence-visualisation {
  padding: auto;
  .section-container {
    background-color: var(--background-color-300);
    border: 1px solid $translucent;
    border-radius: 8px;
    margin: 2px;
    .detailitem {
      padding: 6px;

      .detailitem__header {
        justify-items: center;
        justify-content: center;
        color: var(--light-text-color);
        text-transform: uppercase;
        font-weight: 500;
        align-items: right;
        font-size: $font-size-16;
        font-weight: 600;
        text-align: start;
      }
      .detailitem__value {
        &.large-value {
          font-size: $font-size-18;
          font-weight: 600;
          text-align: end;
        }
      }
    }
  }

  .days-of-week {
    padding: 2px 8px;
    margin: 4px;
    border-radius: $border-radius-Xsm !important;
    .day-label {
      text-transform: uppercase;
      font-size: $font-size-13;
      font-family: $font-sans;
      font-weight: 700;
    }

    &.active-day {
      background-color: $success-type;
      .day-label {
        color: black;
      }
    }

    &.inactive {
      background-color: $table-row-bg;
      .day-label {
        text-decoration: line-through;
        color: rgb(139, 139, 139);
      }
    }
  }
}
</style>
