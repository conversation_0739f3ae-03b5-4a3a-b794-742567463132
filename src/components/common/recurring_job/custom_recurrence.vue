<template>
  <v-layout column id="custom-recurrence" class="custom-recurrence">
    <v-layout class="custom-alert">
      <v-alert v-if="runHistoryList.length === 0" type="warning" :value="true">
        <span>NO PLANNED RUNS</span>
      </v-alert>
    </v-layout>
    <v-layout row wrap align-start pb-2>
      <v-flex md12 mb-2>
        <v-switch
          color="orange"
          v-model="isUsingFirstAvailable"
          label="Book for First Available"
          hide-details
          class="row-label"
        ></v-switch>
      </v-flex>
      <v-flex md3 mt-2 v-if="!isUsingFirstAvailable">
        <span class="pr-3 row-label">Active From:</span>
      </v-flex>
      <v-flex md9 mt-2 v-if="!isUsingFirstAvailable">
        <v-layout>
          <DatePickerBasic
            @setEpoch="setStartEpoch"
            :labelName="'Valid From'"
            :epochTime="commencementDayOf"
            :minDate="firstAvailableTime"
            :soloInput="true"
          >
          </DatePickerBasic>
        </v-layout>
      </v-flex>
    </v-layout>
    <v-divider></v-divider>

    <v-layout align-center pt-2>
      <v-flex md3>
        <span class="pr-3 row-label">Repeat: </span>
      </v-flex>
      <v-flex md9>
        <v-select
          class="ma-0 v-solo-custom"
          outline
          hide-details
          type="number"
          placeholder="Placeholder"
          item-text="longName"
          label="Repeat"
          item-value="id"
          :items="recurringJobCycles"
          v-model="customRepeatModifier"
          color="orange"
        ></v-select>
      </v-flex>
    </v-layout>

    <v-layout class="mt-2" v-if="recurringJob.repeatModifier === 2">
      <v-flex md9 offset-md3>
        <v-select
          class="ma-0 v-solo-custom"
          outline
          hide-details
          type="number"
          item-text="shortName"
          item-value="id"
          :items="weekDays"
          label="Repeat On"
          v-model="repeatDays"
          multiple
          color="orange"
          small-chips
        >
        </v-select>
      </v-flex>
    </v-layout>
    <v-layout row wrap pt-2>
      <v-flex md3>
        <span class="pr-3 row-label">Public Holiday: </span>
      </v-flex>
      <v-flex md9>
        <v-select
          label="Holiday Preference"
          v-model="recurringJob.holidayPreference"
          :items="publicHolidayDeliveryOptions"
          item-text="longName"
          item-value="id"
          color="orange"
          class="v-solo-custom"
          outline
        />
      </v-flex>
    </v-layout>
    <v-divider></v-divider>

    <v-layout row wrap align-start>
      <v-flex md12>
        <v-switch
          color="orange"
          v-model="runUntilCancelled"
          label="Run Until Cancelled"
          class="row-label"
        ></v-switch>
      </v-flex>
      <v-flex md3 pt-1 v-if="!runUntilCancelled">
        <span class="pr-3 row-label">Ending:</span>
      </v-flex>
      <v-flex md9 v-if="!runUntilCancelled">
        <v-radio-group v-model="recurringJob.endOnType" class="ma-0">
          <v-layout
            align-center
            class="radio-container"
            v-for="(item, index) in endOnTypes"
            :key="index"
            color="orange"
          >
            <v-flex v-if="item.id !== 1" md4>
              <v-radio :label="item.longName" :value="item.id"> </v-radio>
            </v-flex>
            <v-flex md8>
              <DatePickerBasic
                v-if="index === 1"
                @setEpoch="setEndEpoch"
                :labelName="'End On'"
                :formDisabled="recurringJob.endOnType !== 2"
                :epochTime="endOnDate"
                hideIcon
              >
              </DatePickerBasic>
              <v-layout v-if="index === 2" align-center>
                <v-flex md4>
                  <v-text-field
                    class="ma-0"
                    hide-details
                    type="number"
                    :disabled="recurringJob.endOnType !== 3"
                    v-model.number="recurringJob.endAfter"
                  >
                  </v-text-field>
                </v-flex>
                <v-flex md8>
                  <span class="pr-3 row-label">Recurrences</span>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-radio-group>
      </v-flex>
    </v-layout>
  </v-layout>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import { returnEndOfDayFromEpoch } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import HolidayDetails from '@/interface-models/Generic/HolidayDetails/HolidayDetails';
import publicHolidayDeliveryOptions from '@/interface-models/Generic/PublicHolidayDeliveryOptions/PublicHolidayDeliveryOptions';
import endOnTypes from '@/interface-models/Generic/RecurringJobCycles/EndOnTypes';
import recurringJobCycles from '@/interface-models/Generic/RecurringJobCycles/RecurringJobCycles';
import { StartAndEndDate } from '@/interface-models/Generic/StartAndEndDate';
import weekDays from '@/interface-models/Generic/WeekDays/WeekDays';
import { RecurringJobRunDetails } from '@/interface-models/Jobs/RecurringJob/RecurringJobRunDetails';
import { RecurringJobRunStatus } from '@/interface-models/Jobs/RecurringJob/RecurringJobRunStatus';
import RecurringJobDetails from '@/interface-models/Jobs/RecurringJobDetails';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useRecurringJobStore } from '@/store/modules/RecurringJobStore';
import { useRootStore } from '@/store/modules/RootStore';
import moment from 'moment-timezone';
import {
  ComputedRef,
  Ref,
  WritableComputedRef,
  computed,
  onMounted,
  ref,
  toRef,
  watch,
} from 'vue';

interface IProps {
  recurringJob: RecurringJobDetails;
}
const props = withDefaults(defineProps<IProps>(), {
  recurringJob: () => new RecurringJobDetails(),
});

const recurringJobStore = useRecurringJobStore();
const companyDetailsStore = useCompanyDetailsStore();

const runHistoryList: Ref<RecurringJobRunDetails[]> = ref([]);
const localRepeatModifier: Ref<number> = ref(1);
const useFirstAvailableTime: Ref<boolean> = ref(true);
const hasNoEndDate: Ref<boolean> = ref(true);

function setCommencementDate() {
  if (props.recurringJob.commencementDate) {
    props.recurringJob.nextScheduledRecurrence = getNewScheduledRecurrence(
      props.recurringJob,
      props.recurringJob.commencementDate,
    );
  }
}

/**
 * Calculates the next scheduled recurrence for a recurring job.
 *
 * @param {RecurringJobDetails} recurringJobDetails - The details of the recurring job.
 * @param {number} commencementDate - The start date of the recurring job, in milliseconds since the Unix epoch.
 * @returns {number} The date of the next scheduled recurrence, in milliseconds since the Unix epoch.
 */
function getNewScheduledRecurrence(
  recurringJobDetails: RecurringJobDetails,
  commencementDate: number,
): number {
  const startTime = commencementDate;
  if (recurringJobDetails.repeatModifier === 1) {
    // DAILY - recur every day
    // Use selected recurring job start date as time
    // const nextDay = startTime + moment.duration(1, 'day').asMilliseconds();

    const nextDay = startTime;
    if (isStandardWorkingDay(nextDay)) {
      return nextDay;
    } else {
      return getNewScheduledRecurrence(recurringJobDetails, nextDay);
    }
  }

  if (recurringJobDetails.repeatModifier === 2) {
    // WEEKLY - check what days it recurs on
    let nextTime = startTime;
    let foundTime;

    for (let i = 0; i < 30; i++) {
      const currentDay = moment(nextTime).tz(companyDetailsStore.userLocale);
      const dayIndex = currentDay.isoWeekday();

      if (recurringJobDetails.repeatDays.includes(dayIndex)) {
        if (isStandardWorkingDay(nextTime)) {
          foundTime = nextTime;
          break;
        } else {
          if (recurringJobDetails.holidayPreference !== 1) {
            foundTime = returnNewRecurrenceForPublicHoliday(
              recurringJobDetails,
              nextTime,
            );
            break;
          }
        }
      }
      nextTime += moment.duration(1, 'day').asMilliseconds();
    }
    if (foundTime) {
      return foundTime;
    }
  }
  if (recurringJobDetails.repeatModifier === 3) {
    // MONTHLY
    const nextMonth = startTime + moment.duration(1, 'month').asMilliseconds();
    if (isStandardWorkingDay(nextMonth)) {
      return nextMonth;
    } else {
      return getNewScheduledRecurrence(recurringJobDetails, nextMonth);
    }
  }
  return startTime;
}

// For days when a public holiday has been landed on, we must check the preference
// and use the appropriate adjacent day
function returnNewRecurrenceForPublicHoliday(
  recurringJobDetails: RecurringJobDetails,
  commencementDate: number,
) {
  let newDate: number = commencementDate;

  if (recurringJobDetails.holidayPreference === 1) {
    // 1. if holidayPreference = 1 ('No Delivery') (That day is ignored and the next available day is found instead.)
  }
  if (recurringJobDetails.holidayPreference === 2) {
    // 2. if holidayPreference = 2 ('Work Day Before') (Reschedule for prior business day )
    newDate -= moment.duration(1, 'day').asMilliseconds();
  }
  if (recurringJobDetails.holidayPreference === 3) {
    // 3. if holidayPreference = 3 ('Work Day After') (Reschedule for next business day)
    newDate += moment.duration(1, 'day').asMilliseconds();
  }
  if (recurringJobDetails.holidayPreference === 4) {
    // 4. if holidayPreference = 4 ('On Public Holiday') (Deliver on the public holiday, so just create job as usual) - Continue
    newDate = commencementDate;
  }
  return newDate;
}

/**
 * Requests recurring job run details for the next three weeks. Called when
 * component mounts.
 *
 * This method constructs a request object with a start date of the current
 * date and an end date of three weeks in the future. It then sends a request
 * to get the recurring job run details for this date range and handles the
 * response. If the response is null, an empty array is assigned to
 * `this.runHistoryList`.
 *
 * @returns {Promise<void>} A Promise that resolves when the method has
 * completed.
 */
async function requestRecurringJobRunDetails(): Promise<void> {
  // Construct request object from current date to 3 weeks in the future
  const request: StartAndEndDate = {
    startDate: moment().valueOf(),
    endDate: moment().add(3, 'weeks').valueOf(),
  };
  // Request run history and handle response
  const response = await recurringJobStore.getRecurringJobRunDetails(request);
  runHistoryList.value = response ?? [];
}

// Modelled to v-select that dictates the cycle-type
// Setter is used to set default values for other properties
const customRepeatModifier: WritableComputedRef<number> = computed({
  get(): number {
    return localRepeatModifier.value;
  },
  set(value: number): void {
    localRepeatModifier.value = value;
    if (value === 4) {
      props.recurringJob.repeatModifier = 2;
      repeatDays.value = [1, 2, 3, 4, 5];
    } else {
      props.recurringJob.repeatModifier = value;
    }
    setCommencementDate();
  },
});

// Modelled to UI switch
// Decides whether we use the first available time calculated from History, or use user-selected date
const isUsingFirstAvailable: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return useFirstAvailableTime.value;
  },
  set(value: boolean): void {
    if (value) {
      commencementDayOf.value = moment(firstAvailableTime.value).valueOf();
    }
    useFirstAvailableTime.value = value;
  },
});

const runUntilCancelled: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return hasNoEndDate.value;
  },
  set(value: boolean): void {
    if (value) {
      // Use 'Never' as end on type
      props.recurringJob.endOnType = 1;
      props.recurringJob.endDate = null;
      hasNoEndDate.value = value;
    } else {
      // Set to 'On' and set date to
      props.recurringJob.endOnType = 2;
      props.recurringJob.endDate = commencementDayOf.value + 86400000 * 14;
      hasNoEndDate.value = value;
    }
  },
});

/**
 * Watcher for the repeat modifier.
 */
let repeatModifier = toRef(props.recurringJob, 'repeatModifier');
watch(repeatModifier, (newValue) => {
  if (newValue === 3) {
    props.recurringJob.repeat = 1;
  }
});

/**
 * Watcher for the repeat modifier.
 */
let endOnType = toRef(props.recurringJob, 'endOnType');
watch(endOnType, (val) => {
  if (val !== 2) {
    props.recurringJob.endDate = null;
  }
  if (val !== 1) {
    props.recurringJob.endAfter = 0;
  } else {
    props.recurringJob.endAfter = 1;
  }
});

const repeatDays: WritableComputedRef<number[]> = computed({
  get(): number[] {
    return props.recurringJob.repeatDays;
  },
  set(value: number[]): void {
    props.recurringJob.repeat = value.length;
    props.recurringJob.repeatDays = value;
    setCommencementDate();
  },
});

// Use to return the commencementDate (or default value). Setter is used to
// update nextScheduledRecurrence value through setCommencementDate() function
const commencementDayOf: WritableComputedRef<number> = computed({
  get(): number {
    if (
      props.recurringJob.commencementDate !== 0 &&
      props.recurringJob.commencementDate !== null
    ) {
      return props.recurringJob.commencementDate;
    } else {
      return moment()
        .tz(companyDetailsStore.userLocale)
        .startOf('day')
        .valueOf();
    }
  },
  set(value: number): void {
    props.recurringJob.commencementDate = value;
    setCommencementDate();
  },
});

const endOnDate: WritableComputedRef<number> = computed({
  get(): number {
    if (props.recurringJob.endDate) {
      return props.recurringJob.endDate;
    }
    return moment().tz(companyDetailsStore.userLocale).endOf('day').valueOf();
  },
  set(value: number): void {
    props.recurringJob.endDate = value;
  },
});

function setStartEpoch(epoch: number): void {
  commencementDayOf.value = epoch;
}
function setEndEpoch(epoch: number): void {
  endOnDate.value = returnEndOfDayFromEpoch(epoch);
}

// Checks if the supplied epoch time is a public holiday.
// Pulls in the defined holidayDetails objects from the store and checks for crossover
function isStandardWorkingDay(commencementDate: number): boolean {
  const publicHolidays: HolidayDetails[] = useRootStore().holidayDetailsList;
  let workingDay = true;
  for (const ph of publicHolidays) {
    if (
      ph.epochStartTime < commencementDate &&
      ph.epochEndTime > commencementDate
    ) {
      workingDay = false;
      break;
    }
  }
  return workingDay;
}

// Return the first recurring job history item that has NOT been run yet.
// This will be used as the earliest possible date that this job is able to be booked for.
const firstAvailableTime: ComputedRef<string> = computed(() => {
  const userTimeZone = companyDetailsStore.userLocale;

  let earliestTime: number = moment().tz(userTimeZone).startOf('day').valueOf();
  // eslint-disable-next-line vue/no-side-effects-in-computed-properties
  const runDetails: RecurringJobRunDetails[] = runHistoryList.value.sort(
    (a, b) => a.plannedRunDate - b.plannedRunDate,
  );
  const foundFirst = runDetails.find(
    (r) => r.status === RecurringJobRunStatus.NOT_ACTIONED,
  );

  if (foundFirst) {
    earliestTime = foundFirst.plannedRunDate + 86400000;
  }
  const asString = moment(earliestTime).tz(userTimeZone).toISOString();
  if (isUsingFirstAvailable.value && commencementDayOf.value < earliestTime) {
    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    commencementDayOf.value = earliestTime;
  }
  return asString;
});

watch(firstAvailableTime, (value) => {
  const minTime = moment(value).tz(companyDetailsStore.userLocale).valueOf();
  if (minTime > commencementDayOf.value) {
    commencementDayOf.value = minTime;
  }
});

onMounted(() => {
  localRepeatModifier.value = props.recurringJob.repeatModifier;
  requestRecurringJobRunDetails();
});
</script>

<style scoped lang="scss">
.custom-recurrence {
  .row-label {
    font-size: $font-size-14;
    text-transform: uppercase;
    color: $bg-light;
    font-weight: 600;
  }
}
.custom-alert {
  display: block;
  position: relative;
  max-width: 90%;
  .v-alert {
    padding: 8px;
    margin: 8px;
  }
}
</style>
