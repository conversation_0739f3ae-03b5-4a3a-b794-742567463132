<template>
  <div v-if="validCoordinates">
    <v-icon
      class="street-icon"
      v-if="!isTile"
      size="13"
      @click="openStreetView"
    >
      fas fa-street-view</v-icon
    >
    <v-list-tile dense v-else @click="openStreetView">
      <v-list-tile-avatar class="pa-0">
        <v-icon
          class="street-icon"
          v-if="isTile"
          size="13"
          @click="openStreetView"
        >
          fas fa-street-view</v-icon
        >
      </v-list-tile-avatar>
      <v-list-tile-title class="pr-2"> Street View </v-list-tile-title>
    </v-list-tile>
  </div>
</template>

<script setup lang="ts">
import { ComputedRef, computed } from 'vue';

const props = withDefaults(
  defineProps<{
    coordinates?: number[];
    isTile?: boolean;
  }>(),
  {
    isTile: false,
    coordinates: () => [],
  },
);

// Opens the street view in the new tab for the coordinates prop
function openStreetView(): void {
  const latitude: number = props.coordinates[1];
  const longitude: number = props.coordinates[0];
  const link: string =
    'https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=' +
    latitude +
    ',' +
    longitude;
  window.open(link, '_blank', 'noreferrer');
}

// Validates whether the coordinates prop are correct.
const validCoordinates: ComputedRef<boolean> = computed(() => {
  return (
    props.coordinates &&
    props.coordinates.length === 2 &&
    props.coordinates[0] !== 0.0 &&
    props.coordinates[1] !== 0.0
  );
});
</script>

<style lang="scss" scoped>
.street-icon {
  color: var(--text-color) !important;
}
</style>
