<template>
  <ContentDialog
    :showDialog.sync="dialogController"
    title="Add Additional Charge"
    width="50%"
    contentPadding="pa-0"
    @cancel="dialogController = false"
    @confirm="emitSelectedChargeItem"
    :showActions="true"
    :isConfirmUnsaved="false"
    :isDisabled="!isConfirmButtonEnabled"
    :isLoading="false"
    confirmBtnId="addAdditionalChargeDialogConfirmBtn"
    confirmBtnText="Confirm"
  >
    <v-layout wrap>
      <v-layout justify-centre>
        <div class="button-group">
          <v-tooltip
            v-for="buttonType in buttonGroupOptions"
            :key="buttonType.id"
            bottom
            :disabled="!buttonType.tooltipText"
          >
            <template v-slot:activator="{ on }">
              <span v-on="on">
                <v-btn
                  :class="{
                    'v-btn--active': buttonType.id === selectedViewType,
                  }"
                  :disabled="buttonType.isDisabled"
                  flat
                  @click="viewTypeController = buttonType.id"
                >
                  <span class="px-2"
                    ><strong>{{ buttonType.longName }}</strong></span
                  >
                </v-btn>
              </span>
            </template>
            {{ buttonType.tooltipText }}
          </v-tooltip>
        </div>
      </v-layout>

      <v-flex md12 pt-2>
        <v-divider></v-divider>
      </v-flex>

      <v-flex
        md12
        class="body-scrollable--65 body-min-height--65"
        id="additional-charge-dialog-body"
      >
        <section v-if="selectedViewType === ViewType.LIST" class="dialog-body">
          <v-layout row wrap>
            <v-flex md4>
              <v-select
                v-if="chargeTypeSelectOptions"
                class="v-solo-custom"
                solo
                flat
                persistent-hint
                :items="chargeTypeSelectOptions"
                item-text="selectName"
                v-model="selectedChargeTypeId"
                item-value="id"
                color="orange"
                auto-select
                hint="Select Charge Category"
                label="Select Charge Category"
              >
                <template v-slot:prepend-inner>
                  <span>
                    <v-icon size="18" class="pr-3">far fa-filter</v-icon>
                  </span>
                </template>
              </v-select>
            </v-flex>
            <v-flex md8 pl-2>
              <v-text-field
                class="v-solo-custom"
                solo
                flat
                color="light-blue"
                label="Search"
                ref="searchTextField"
                v-model.trim="searchStr"
                @keypress.enter="selectFirstResult"
                @input="resetSelection"
              >
                <template v-slot:prepend-inner>
                  <span>
                    <v-icon size="18" class="pr-3">far fa-search</v-icon>
                  </span>
                </template>
              </v-text-field>
            </v-flex>
          </v-layout>
          <v-layout
            row
            wrap
            v-if="filteredChargeItems?.length && filteredChargeItems.length > 0"
            pt-2
          >
            <table class="additional-charge-item-table simple-data-table">
              <thead>
                <tr>
                  <th>Category</th>
                  <th class="text-xs-left">Name</th>
                  <th class="show-asterisk-marker-left">Client</th>
                  <th class="show-asterisk-marker-left">Fleet Asset</th>
                  <th>#</th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(item, index) in filteredChargeItems"
                  :key="item._id"
                  :id="item._id"
                  class="additional-charge-item-table-row"
                  :class="{
                    'first-result': !!searchStr && index === 0,
                    'is-selected': selectedChargeIndices.includes(index),
                  }"
                  tabindex="0"
                  @click="(e) => selectChargeItem(item, index, e)"
                  @keypress.enter="(e) => selectChargeItem(item, index, e)"
                  @keydown="handleKeydown($event, index)"
                >
                  <td>{{ item.typeName }}</td>
                  <td class="text-xs-left">{{ item.longName }}</td>
                  <td :class="{ 'highlight-text': !!item.customClientRate }">
                    {{
                      returnRateSummaryForChargeSummary(
                        RateEntityType.CLIENT,
                        item,
                      )
                    }}
                  </td>
                  <td
                    :class="{ 'highlight-text': !!item.customFleetAssetRate }"
                  >
                    {{
                      returnRateSummaryForChargeSummary(
                        RateEntityType.FLEET_ASSET,
                        item,
                      )
                    }}
                  </td>
                  <td>
                    {{ item.appliedCount || '' }}
                  </td>
                  <td>
                    <v-btn
                      flat
                      icon
                      small
                      @click="viewChargeItem(item)"
                      class="ma-0"
                      tabindex="-1"
                    >
                      <v-icon size="17">far fa-eye</v-icon>
                    </v-btn>
                  </td>
                </tr>
              </tbody>
            </table>
            <v-layout pt-2 justify-end>
              <span class="asterisk-message"
                >All prices shown in this table are exclusive of GST.</span
              >
            </v-layout>
          </v-layout>
          <v-layout
            row
            wrap
            v-if="filteredChargeItems?.length === 0"
            class="pa-3"
          >
            No available charges found.
          </v-layout>
        </section>
        <v-form ref="adjustmentChargeForm" v-if="singleChargeItem">
          <AddCustomAdditionalCharge
            v-if="selectedViewType === ViewType.ADHOC"
            :chargeItem="singleChargeItem"
            :showPricingTypeOptions="!singleChargeItem?.typeReferenceId"
            :showFleetAssetFuelApplied="showFleetAssetFuelApplied"
            :showClientFuelApplied="showClientFuelApplied"
            :isTollCharge="isTollCharge"
            :additionalChargeTypes="additionalChargeTypes"
          >
          </AddCustomAdditionalCharge>
          <ViewAdditionalChargeDetails
            v-if="selectedViewType === ViewType.DETAIL"
            :additionalChargeItem="singleChargeItem"
            :additionalChargeTypes="additionalChargeTypes"
            :disableInputs="isSelectedChargeAlreadyApplied"
            @returnToList="viewTypeController = ViewType.LIST"
          >
          </ViewAdditionalChargeDetails>
        </v-form>
      </v-flex>
    </v-layout>
  </ContentDialog>
</template>

<script setup lang="ts">
enum ViewType {
  LIST = 'LIST',
  ADHOC = 'ADHOC',
  DETAIL = 'DETAIL',
}

interface AdditionalChargeSummary extends AdditionalChargeItem {
  typeName: string;
  searchName: string;
  isAlreadyApplied: boolean;
  appliedCount: number;
  customClientRate?: number;
  customFleetAssetRate?: number;
}
import AddCustomAdditionalCharge from '@/components/common/additional_charge/add_custom_additional_charge.vue';
import ViewAdditionalChargeDetails from '@/components/common/additional_charge/view_additional_charge_details.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { initialiseAdditionalChargeItem } from '@/helpers/classInitialisers/InitialiseAdditionalChargeItem';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { AdditionalChargeItem } from '@/interface-models/AdditionalCharges/AdditionalChargeItem';
import { AdditionalChargeRateBasis } from '@/interface-models/AdditionalCharges/AdditionalChargeRateBasis';
import { AdditionalChargeType } from '@/interface-models/AdditionalCharges/AdditionalChargeType';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import { useRootStore } from '@/store/modules/RootStore';
import { useMultiSelectIndices } from '@/utils/useMultiSelectIndices';
import Fuse from 'fuse.js';
import { v4 as uuidv4 } from 'uuid';
import {
  ComputedRef,
  Ref,
  WritableComputedRef,
  computed,
  nextTick,
  ref,
  watch,
} from 'vue';

const props = withDefaults(
  defineProps<{
    isDialogOpen: boolean;
    chargeItems: AdditionalChargeItem[];
    currentAppliedCharges: AdditionalChargeItem[];
    height?: string;
    fleetAssetOwnerGstRegistered?: boolean;
    showFleetAssetFuelApplied?: boolean;
    showClientFuelApplied?: boolean;
  }>(),
  {
    fleetAssetOwnerGstRegistered: false,
    showFleetAssetFuelApplied: false,
    showClientFuelApplied: false,
    height: '',
  },
);

const emit = defineEmits<{
  (event: 'addChargesToJob', payload: AdditionalChargeItem[]): void;
  (event: 'update:isDialogOpen', payload: boolean): void;
}>();

const selectedViewType: Ref<ViewType> = ref(ViewType.LIST);

const searchTextField: Ref<any> = ref(null);
const searchStr: Ref<string> = ref('');

const singleChargeItem: Ref<AdditionalChargeItem | null> = ref(null);

const isSelectedChargeAlreadyApplied: Ref<boolean> = ref(false);

const selectedChargeTypeId: Ref<string> = ref('');
const adjustmentChargeForm: Ref<any> = ref(null);

const {
  selectedIndices: selectedChargeIndices,
  lastSelectedIndex,
  handleSelect,
  resetSelection,
} = useMultiSelectIndices();

const buttonGroupOptions = [
  {
    id: ViewType.LIST,
    longName: 'Available Charges',
    tooltipText: '',
    isDisabled: false,
  },
  {
    id: ViewType.ADHOC,
    longName: 'Adhoc Charge',
    tooltipText: '',
    isDisabled: false,
  },
];

/**
 * Gets and sets the isDialogOpen prop passed from parent
 */
const dialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.isDialogOpen;
  },
  set(value: boolean): void {
    if (!value) {
      resetComponentState();
    }
    emit('update:isDialogOpen', value);
  },
});

/**
 * Watches the dialogController value. When the dialog is opened, scrolls to
 * the top of the dialog body and focuses on the search text field.
 */
watch(dialogController, (value) => {
  if (value) {
    nextTick(() => {
      // Scroll to the top of the additional-charge-dialog-body div
      const dialogBody = document.getElementById(
        'additional-charge-dialog-body',
      );
      if (dialogBody) {
        dialogBody.scrollTop = 0;
      }
      // Focus on the search text field
      searchTextField.value.focus();
    });
  }
});

/**
 * Controls the view type of the dialog. The dialog can be in list view, adhoc
 * view, or detail view. Used in the template to conditionally render sections
 */
const viewTypeController: WritableComputedRef<ViewType> = computed({
  get(): ViewType {
    return selectedViewType.value;
  },
  set(value: ViewType): void {
    if (value === ViewType.LIST) {
      searchStr.value = '';
      isSelectedChargeAlreadyApplied.value = false;
      singleChargeItem.value = null;
      selectedChargeIndices.value = [];
    } else if (value === ViewType.ADHOC) {
      isSelectedChargeAlreadyApplied.value = false;
      singleChargeItem.value = returnAdhocChargeItem();
    }
    selectedViewType.value = value;
  },
});

const allAdditionalChargeItems: ComputedRef<AdditionalChargeItem[]> = computed(
  () =>
    props.chargeItems?.filter(
      (item) => item._id !== useRootStore().tollAdminAndHandlingId,
    ) ?? [],
);
const additionalChargeTypes: ComputedRef<AdditionalChargeType[]> = computed(
  () => {
    return useRootStore().additionalChargeTypeList;
  },
);

/**
 * Returns a list of charge types with the count of charges for each type. Used
 * in the template in a v-select to allow the user to filter the results list.
 */
const chargeTypeSelectOptions: ComputedRef<
  {
    id: string;
    selectName: string;
    shortName: string;
    longName: string;
    count: number;
  }[]
> = computed(() => {
  const chargeTypes = useRootStore().additionalChargeTypeList.map((type) => {
    const count = allAdditionalChargeItems.value.filter(
      (item) => item.typeReferenceId === type._id,
    ).length;
    return {
      id: type._id,
      selectName: `${type.shortName} - ${type.longName} (${count})`,
      shortName: type.shortName,
      longName: type.longName,
      count: count,
    };
  });
  const totalCount = allAdditionalChargeItems.value.length;
  const allChargesOption = {
    id: '',
    selectName: `All Charges (${totalCount})`,
    shortName: 'ALL',
    longName: 'All Charges',
    count: totalCount,
  };

  return [allChargesOption, ...chargeTypes];
});

/**
 * Filters the list of additional charges based on the selected charge type, and
 * the search string. Uses fuse to perform a fuzzy search on the search string.
 */
const filteredChargeItems: ComputedRef<AdditionalChargeSummary[]> = computed(
  () => {
    if (viewTypeController.value === ViewType.ADHOC) {
      return [];
    }
    const filteredItems = allAdditionalChargeItems.value
      .filter(
        (item: AdditionalChargeItem) =>
          !selectedChargeTypeId.value ||
          item.typeReferenceId === selectedChargeTypeId.value,
      )
      .map((item) => {
        const typeName =
          additionalChargeTypes.value.find(
            (type) => type._id === item.typeReferenceId,
          )?.shortName ?? 'Unknown';

        const foundCurrentCharge = props.currentAppliedCharges.find(
          (c) => !!item._id && c._id === item._id,
        );

        const appliedCount = foundCurrentCharge?.quantity ?? 0;

        const customClientRate =
          !!foundCurrentCharge &&
          foundCurrentCharge.client.charge !== item.client.charge
            ? foundCurrentCharge.client.charge
            : undefined;

        const customFleetAssetRate =
          !!foundCurrentCharge &&
          foundCurrentCharge.fleetAsset.charge !== item.fleetAsset.charge
            ? foundCurrentCharge.fleetAsset.charge
            : undefined;

        return {
          ...item,
          typeName: typeName,
          searchName: `${typeName} - ${item.longName}`,
          isAlreadyApplied: !!foundCurrentCharge,
          appliedCount: appliedCount,
          customClientRate: customClientRate,
          customFleetAssetRate: customFleetAssetRate,
        } as AdditionalChargeSummary;
      });

    if (!searchStr.value) {
      return filteredItems;
    }

    // Additional filtering using searchName and searchStr
    const fuse = new Fuse<AdditionalChargeSummary>(filteredItems, {
      keys: ['searchName'],
      threshold: 0.3,
      includeScore: true,
      ignoreLocation: true,
    });

    const additionalResults = fuse.search(searchStr.value).map((x) => x.item);

    return additionalResults;
  },
);

/**
 * Used in template to handle enter keypress event on the search text field.
 * Selects the first result in the filteredChargeItems list.
 */
function selectFirstResult() {
  if (filteredChargeItems.value.length > 0) {
    selectChargeItem(filteredChargeItems.value[0]);
  }
}

/**
 * Determines if the confirm button should be enabled. The button should be
 * enabled if a charge item is selected.
 */
const isConfirmButtonEnabled: ComputedRef<boolean> = computed(() => {
  return (
    singleChargeItem.value !== null ||
    (viewTypeController.value === ViewType.LIST &&
      selectedChargeIndices.value.length > 0)
  );
});

/**
 * Handles keydown events on the table rows. Allows the user to navigate the
 * list of results using the arrow keys.
 */
function handleKeydown(event: KeyboardEvent, index: number) {
  const rows = document.querySelectorAll('.additional-charge-item-table-row');
  const lastIndex = rows.length - 1;

  if (event.key === 'ArrowDown') {
    event.preventDefault();
    const nextIndex = index === lastIndex ? 0 : index + 1;
    (rows[nextIndex] as HTMLElement).focus();
  } else if (event.key === 'ArrowUp') {
    event.preventDefault();
    const prevIndex = index === 0 ? lastIndex : index - 1;
    (rows[prevIndex] as HTMLElement).focus();
  }
}

/**
 * Determines if the selected charge item is a toll charge.
 */
const isTollCharge: ComputedRef<boolean> = computed(() => {
  return (
    singleChargeItem.value?.typeReferenceId === useRootStore().tollChargeTypeId
  );
});

/**
 * Validates the form and emits the selected charge items to the parent
 * component.
 */
function emitSelectedChargeItem() {
  // If in ADHOC or DETAIL, validate the form and emit the single selected item
  if (
    viewTypeController.value === ViewType.ADHOC ||
    viewTypeController.value === ViewType.DETAIL
  ) {
    if (!singleChargeItem.value) {
      showNotification(GENERIC_ERROR_MESSAGE);
      return;
    }
    if (!adjustmentChargeForm.value?.validate()) {
      showNotification(FORM_VALIDATION_FAILED_MESSAGE);
      return;
    }
    emit('addChargesToJob', [singleChargeItem.value]);
    dialogController.value = false;
    return;
  }

  // In LIST view, emit all selected items by selectedChargeIndices
  if (viewTypeController.value === ViewType.LIST) {
    const items = selectedChargeIndices.value
      .map((idx) => filteredChargeItems.value[idx])
      .filter(Boolean)
      .map((item) => initialiseAdditionalChargeItem(item));
    if (!items.length) {
      showNotification(GENERIC_ERROR_MESSAGE);
      return;
    }
    emit('addChargesToJob', items);
    dialogController.value = false;
  }
}

/**
 * Called when the user clicks the icon in the table to view the details of a
 * given charge. Sets the selectedChargeItem and sets the view type to DETAIL.
 * @param {AdditionalChargeSummary} item - The charge item to view.
 */
function viewChargeItem(item: AdditionalChargeSummary) {
  isSelectedChargeAlreadyApplied.value = item.isAlreadyApplied;
  singleChargeItem.value = initialiseAdditionalChargeItem(item);

  // If the charge already exists, set the custom rate to the applied rate so we
  // can display it
  if (isSelectedChargeAlreadyApplied.value) {
    singleChargeItem.value.client.charge =
      item.customClientRate ?? singleChargeItem.value.client.charge;
    singleChargeItem.value.fleetAsset.charge =
      item.customFleetAssetRate ?? singleChargeItem.value.fleetAsset.charge;
  }

  viewTypeController.value = ViewType.DETAIL;
}

/**
 * Handles the selection of an additional charge item from a list.
 *
 * - If an index is provided, it triggers the selection handler and initializes the selected charge item.
 * - Clears the search string after selection.
 * - On the next DOM update, if the selection is not a multi-select (no shift/ctrl/cmd key pressed):
 *   - Scrolls the selected item into view smoothly and centers it.
 *   - Focuses the confirm button in the dialog, if present.
 *
 * @param {AdditionalChargeSummary} item - The additional charge item being selected.
 * @param {number} [index] - The index of the selected item in the list (optional).
 * @param {MouseEvent | KeyboardEvent} [event] - The event that triggered the selection (optional).
 */
function selectChargeItem(
  item: AdditionalChargeSummary,
  index?: number,
  event?: MouseEvent | KeyboardEvent,
): void {
  // Use allAdditionalChargeItems and not filteredChargeItems to find the index, as we want
  // the index we select to be the index in the full list, not the filtered
  // list.
  const foundIndex = allAdditionalChargeItems.value.findIndex(
    (i) => i._id === item._id,
  );
  if (foundIndex !== -1) {
    handleSelect(foundIndex, event);
    // singleChargeItem.value = initialiseAdditionalChargeItem(item);
  } else {
    // If the item is not found in the filtered list, reset the selection
    resetSelection();
    // singleChargeItem.value = null;
    return;
  }
  searchStr.value = '';
  nextTick(() => {
    // Only scroll into view if NOT using shift or ctrl/cmd (i.e. single selection)
    const isMultiSelect =
      event && (event.shiftKey || event.ctrlKey || event.metaKey);
    if (!isMultiSelect) {
      const element = item._id ? document.getElementById(item._id) : null;
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      const confirmBtn = document.getElementById(
        'addAdditionalChargeDialogConfirmBtn',
      );
      if (confirmBtn) {
        confirmBtn.focus();
      }
    }
  });
}

/**
 * Resets the component state to its initial state (list view, no selected
 * charge).
 */
function resetComponentState() {
  selectedViewType.value = ViewType.LIST;
  searchStr.value = '';
  isSelectedChargeAlreadyApplied.value = false;
  selectedChargeTypeId.value = '';
  singleChargeItem.value = null;
  selectedChargeIndices.value = [];
}

/**
 * Returns a new adhoc charge item with a unique id.
 */
function returnAdhocChargeItem() {
  const charge = new AdditionalChargeItem();
  charge._id = uuidv4().split('-').join('');
  return charge;
}

/**
 * Returns the rates summary for the given type and charge.
 * @param {RateEntityType} type - The rate entity type.
 * @param {AdditionalChargeItem} charge - The additional charge item.
 * @returns {string} The rates summary.
 */
function returnRateSummaryForChargeSummary(
  type: RateEntityType,
  charge: AdditionalChargeSummary,
): string {
  if (type === RateEntityType.CLIENT) {
    const chargeBasis = charge.client.chargeBasis;
    const value = charge.customClientRate ?? charge.client.charge;
    if (chargeBasis === AdditionalChargeRateBasis.FIXED) {
      return DisplayCurrencyValue(value);
    } else if (chargeBasis === AdditionalChargeRateBasis.PERCENTAGE) {
      return `${value}%`;
    }
  } else {
    const chargeBasis = charge.fleetAsset.chargeBasis;
    const value = charge.customFleetAssetRate ?? charge.fleetAsset.charge;
    if (chargeBasis === AdditionalChargeRateBasis.FIXED) {
      return DisplayCurrencyValue(value);
    } else if (chargeBasis === AdditionalChargeRateBasis.PERCENTAGE) {
      return `${value}%`;
    }
  }
  return 'N/A';
}
</script>

<style scoped lang="scss">
.additional-charge-item-table {
  // border-collapse: separate;
  border-spacing: 0 3px;
  border-collapse: collapse;

  th {
    position: relative;
    &.show-asterisk-marker-left:after {
      // Additive to the left/top position styles set in vuetify-customised
      left: 4px;
      top: 2px;
    }
  }

  .additional-charge-item-table-row {
    background-color: var(--background-color-300);
    padding: 8px;
    // border: 1.2px solid $translucent;
    box-shadow: 0 0 4px $translucent;
    margin-bottom: 3px;
    margin-top: 1px;

    // Prevent text selection when clicking on the row
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;

    td {
      padding: 12px;
      &.highlight-text {
        color: $warning-type;
        font-style: italic;
      }
    }

    td:first-child {
      font-weight: bold;
    }

    &.first-result {
      box-shadow: 0 0 4px var(--primary);
    }

    &.is-selected {
      background-color: var(--info);
      color: white;
      &.highlight-text {
        color: white;
        font-style: italic;
      }
    }

    &:hover {
      cursor: pointer;
    }

    &:not(.is-selected):hover {
      background-color: var(--background-color-600);
      box-shadow: 0 0 4px var(--border-color);
    }

    &:not(.is-selected):focus {
      background-color: var(--background-color-600);
      box-shadow: 0 0 4px var(--text-color);
      outline: none;
    }
  }
}

.dialog-body {
  padding: $dialog-content-padding;
}
</style>
