<template>
  <v-layout pt-3 pl-3>
    <v-flex md1>
      <v-layout column justify-start align-center py-3>
        <v-tooltip bottom>
          <template v-slot:activator="{ on }">
            <v-btn large flat v-on="on" icon @click="emit('returnToList')">
              <v-icon size="24" color="grey lighten-1"
                >far fa-arrow-left</v-icon
              >
            </v-btn>
          </template>
          Return to List
        </v-tooltip>
      </v-layout>
    </v-flex>
    <v-flex md10 class="view-additional-charge-details py-3">
      <v-layout align-center class="banner-custom">
        <v-layout column>
          <h3>
            {{ additionalChargeItem.longName }}
          </h3>
          <h4 v-if="additionalChargeTypeName">
            {{ additionalChargeTypeName }}
          </h4>
        </v-layout>
      </v-layout>
      <v-divider class="mt-2 mb-1"></v-divider>
      <v-layout row wrap>
        <v-flex md6 class="detail-item--vertical">
          <v-layout class="title-text"> Name </v-layout>
          <v-layout class="value-text">
            {{ additionalChargeItem.longName }}
          </v-layout>
        </v-flex>
        <v-flex md6 class="detail-item--vertical">
          <v-layout class="title-text"> Category </v-layout>
          <v-layout class="value-text">
            {{ additionalChargeTypeName }}
          </v-layout>
        </v-flex>
        <v-flex md6 class="detail-item--vertical">
          <v-layout class="title-text"> Valid From </v-layout>
          <v-layout class="value-text">
            {{
              additionalChargeItem.validFromDate
                ? additionalChargeItem.validFromDate
                : 'N/A'
            }}
          </v-layout>
        </v-flex>
        <v-flex md6 class="detail-item--vertical">
          <v-layout class="title-text"> Valid To </v-layout>
          <v-layout class="value-text">
            {{
              additionalChargeItem.validToDate
                ? additionalChargeItem.validToDate
                : 'N/A'
            }}
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-divider class="my-2 mb-3"></v-divider>
          <!-- <v-layout>
            <span class="title--bold"> Charge Breakdown </span>
          </v-layout> -->
        </v-flex>
        <v-flex md12>
          <v-layout>
            <v-flex md12 py-2>
              <table class="simple-data-table">
                <thead>
                  <tr>
                    <th></th>
                    <th class="text-xs-left">Applies To</th>
                    <th class="text-xs-right">Charge Basis</th>
                    <th class="text-xs-right">Charge</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td><span class="table-subheader">Client</span></td>
                    <td class="text-xs-left">
                      {{
                        returnReadableApplicationTypeName(
                          additionalChargeItem.client.appliesTo,
                        )
                      }}
                    </td>
                    <td class="text-xs-right">
                      {{ additionalChargeItem.client.chargeBasis }}
                    </td>
                    <td class="text-xs-right pl-4">
                      <v-text-field
                        class="v-solo-custom my-1"
                        solo
                        flat
                        label="Client ($)"
                        hint="Client ($)"
                        type="number"
                        hide-details
                        :disabled="disableInputs"
                        :prefix="
                          additionalChargeItem.client.chargeBasis ===
                          AdditionalChargeRateBasis.FIXED
                            ? '$'
                            : ''
                        "
                        :suffix="
                          additionalChargeItem.client.chargeBasis ===
                          AdditionalChargeRateBasis.PERCENTAGE
                            ? '%'
                            : ''
                        "
                        v-model.number="additionalChargeItem.client.charge"
                        :rules="[
                          validationRules.nonNegative,
                          validationRules.required,
                        ]"
                        @focus="$event.target.select()"
                      />
                    </td>
                  </tr>
                  <tr>
                    <td><span class="table-subheader">Fleet Asset</span></td>
                    <td class="text-xs-left">
                      {{
                        returnReadableApplicationTypeName(
                          additionalChargeItem.fleetAsset.appliesTo,
                        )
                      }}
                    </td>
                    <td class="text-xs-right">
                      {{ additionalChargeItem.fleetAsset.chargeBasis }}
                    </td>
                    <td class="text-xs-right pl-4">
                      <v-text-field
                        class="v-solo-custom my-1"
                        solo
                        flat
                        label="Client ($)"
                        hint="Client ($)"
                        type="number"
                        hide-details
                        :disabled="disableInputs"
                        :prefix="
                          additionalChargeItem.fleetAsset.chargeBasis ===
                          AdditionalChargeRateBasis.FIXED
                            ? '$'
                            : ''
                        "
                        :suffix="
                          additionalChargeItem.fleetAsset.chargeBasis ===
                          AdditionalChargeRateBasis.PERCENTAGE
                            ? '%'
                            : ''
                        "
                        v-model.number="additionalChargeItem.fleetAsset.charge"
                        :rules="[
                          validationRules.nonNegative,
                          validationRules.required,
                        ]"
                        @focus="$event.target.select()"
                      />
                    </td>
                  </tr>
                </tbody>
              </table>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { returnReadableApplicationTypeName } from '@/interface-models/AdditionalCharges/AdditionalChargeApplicationType';
import { AdditionalChargeItem } from '@/interface-models/AdditionalCharges/AdditionalChargeItem';
import { AdditionalChargeRateBasis } from '@/interface-models/AdditionalCharges/AdditionalChargeRateBasis';
import { AdditionalChargeType } from '@/interface-models/AdditionalCharges/AdditionalChargeType';
import { ComputedRef, computed } from 'vue';

const props = defineProps<{
  additionalChargeItem: AdditionalChargeItem;
  additionalChargeTypes: AdditionalChargeType[];
  disableInputs: boolean;
}>();

const emit = defineEmits<{
  (event: 'returnToList'): void;
}>();

const additionalChargeTypeName: ComputedRef<string> = computed(() => {
  const foundType = props.additionalChargeTypes.find(
    (type) => type._id === props.additionalChargeItem.typeReferenceId,
  );
  return foundType ? `${foundType.shortName} - ${foundType.longName}` : '';
});
</script>
<style scoped lang="scss">
.table-subheader {
  font-weight: 600;
  // padding: 4px 8px 10px 4px;
  font-size: $font-size-16;
}
</style>
