<template>
  <section class="add-custom-additional-charge">
    <v-layout row wrap>
      <v-flex md12 class="pb-3">
        <v-alert type="info" :value="true">
          <ul>
            <li>Overall Adjustments are applied without fuel surcharge.</li>
            <li>Freight Adjustments are applied with fuel surcharge.</li>
          </ul>
        </v-alert>
      </v-flex>

      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">1. Add Charge Details</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Category
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              v-if="additionalChargeTypes"
              id="addCustomChargeCategorySelect"
              class="v-solo-custom"
              solo
              flat
              persistent-hint
              :items="chargeTypeSelectOptions"
              item-text="selectName"
              v-model="selectedAdditionalChargeTypeId"
              item-value="id"
              color="orange"
              auto-select
              hint="Select Charge Category"
              label="Select Charge Category"
              :rules="[validationRules.required]"
            >
            </v-select>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Charge Name
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :label="chargeLabelName"
              :hint="chargeLabelName"
              class="v-solo-custom"
              solo
              flat
              persistent-hint
              color="orange"
              v-model="chargeItem.longName"
              :rules="[validationRules.required]"
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12 v-if="showPricingTypeOptions">
        <v-layout>
          <v-flex md4 pt-2>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Adjustment Type
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-radio-group v-model="selectedAppliesToOption">
              <v-radio
                v-for="(n, index) in appliesToOptions"
                :key="n.id"
                :class="{ 'pb-2': index === 0 }"
                :label="n.longName"
                :value="n.id"
              >
              </v-radio>
            </v-radio-group>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4 pt-2>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Charge Basis
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-radio-group
              v-model="selectedRateBasisOption"
              :disabled="
                selectedAdditionalChargeType &&
                selectedAdditionalChargeType.allowedRateBases.length < 2
              "
            >
              <v-radio
                v-for="(n, index) in rateBasisOptions"
                :key="n.id"
                :class="{ 'pb-2': index === 0 }"
                :label="n.longName"
                :value="n.id"
              >
              </v-radio>
            </v-radio-group>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">
            2. {{ isTollCharge ? 'Toll Value' : 'Charge/Pay Value' }}
          </h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout wrap v-if="!isTollCharge">
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">
                    Client Charge (ex. GST)
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md5 class="pr">
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  label="Client ($)"
                  hint="Client ($)"
                  type="number"
                  :prefix="
                    selectedRateBasisOption === AdditionalChargeRateBasis.FIXED
                      ? '$'
                      : ''
                  "
                  :suffix="
                    selectedRateBasisOption ===
                    AdditionalChargeRateBasis.PERCENTAGE
                      ? '%'
                      : ''
                  "
                  v-model.number="clientAmount"
                  :rules="[validation.nonNegative, validation.required]"
                  persistent-hint
                  @focus="$event.target.select()"
                />
              </v-flex>
              <v-flex md3 class="pl">
                <v-checkbox
                  label="Deduction"
                  color="orange"
                  v-model="isClientDeduction"
                >
                </v-checkbox>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">
                    Driver Pay (ex. GST)
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md5 class="pr">
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  label="Driver ($)"
                  :prefix="
                    selectedRateBasisOption === AdditionalChargeRateBasis.FIXED
                      ? '$'
                      : ''
                  "
                  :suffix="
                    selectedRateBasisOption ===
                    AdditionalChargeRateBasis.PERCENTAGE
                      ? '%'
                      : ''
                  "
                  type="number"
                  v-model.number="fleetAssetAmount"
                  :rules="[validation.nonNegative, validation.required]"
                  hint="Driver ($)"
                  persistent-hint
                  @focus="$event.target.select()"
                />
              </v-flex>
              <v-flex md3 class="pl">
                <v-checkbox
                  label="Deduction"
                  color="orange"
                  v-model="isFleetAssetDeduction"
                >
                </v-checkbox>
              </v-flex>
            </v-layout>
          </v-flex>
        </v-layout>

        <v-layout wrap v-if="isTollCharge">
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">Charge/Pay Option</h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-select
                  :items="chargeOrPaySelectItems"
                  item-text="longName"
                  item-value="id"
                  label="Charge/Pay"
                  class="v-solo-custom"
                  solo
                  flat
                  persistent-hint
                  color="orange"
                  hint="Charge/Pay"
                  v-model="chargeOrPayId"
                  @change="chargeOrPayOptionChanged"
                />
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h5 class="subheader--faded pr-3 pb-0">
                    {{ tollValueInputLabel }}
                  </h5>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  persistent-hint
                  :hint="tollValueInputLabel"
                  :label="tollValueInputLabel"
                  prefix="$"
                  type="number"
                  color="orange"
                  v-model.number="tollValueInputController"
                  :rules="[validation.nonNegative, validation.required]"
                  @focus="$event.target.select()"
                />
              </v-flex>
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </section>
</template>

<script setup lang="ts">
enum ChargePayOption {
  BOTH = 1,
  CHARGE_ONLY = 2,
  PAY_ONLY = 3,
}
import { RoundCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { setChargePropertiesFromType } from '@/helpers/RateHelpers/AdditionalChargeHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import {
  AdditionalChargeApplicationType,
  returnReadableApplicationTypeName,
} from '@/interface-models/AdditionalCharges/AdditionalChargeApplicationType';
import { AdditionalChargeItem } from '@/interface-models/AdditionalCharges/AdditionalChargeItem';
import {
  AdditionalChargeRateBasis,
  returnReadableRateBasisName,
} from '@/interface-models/AdditionalCharges/AdditionalChargeRateBasis';
import { AdditionalChargeType } from '@/interface-models/AdditionalCharges/AdditionalChargeType';
import ShortLongName from '@/interface-models/Generic/ShortLongName/ShortLongName';
import {
  computed,
  ComputedRef,
  onMounted,
  ref,
  Ref,
  watch,
  WritableComputedRef,
} from 'vue';

const validation = validationRules;

const props = withDefaults(
  defineProps<{
    chargeItem: AdditionalChargeItem;
    additionalChargeTypes: AdditionalChargeType[];
    showPricingTypeOptions?: boolean;
    isTollCharge?: boolean;
    fleetAssetOwnerGstRegistered?: boolean;
    showFleetAssetFuelApplied?: boolean;
    showClientFuelApplied?: boolean;
  }>(),
  {
    additionalChargeTypes: () => [],
    showPricingTypeOptions: false,
    isTollCharge: false,
    fleetAssetOwnerGstRegistered: false,
    showFleetAssetFuelApplied: false,
    showClientFuelApplied: false,
  },
);

const chargeOrPayId: Ref<ChargePayOption> = ref(ChargePayOption.BOTH);
const focusTextfieldTimeout = ref<ReturnType<typeof setTimeout> | null>(null);

// Local state, used in template
const isClientDeduction: Ref<boolean> = ref(false);
const isFleetAssetDeduction: Ref<boolean> = ref(false);
const clientAmount: Ref<number> = ref(0);
const fleetAssetAmount: Ref<number> = ref(0);
const chargeAndPayAmount: Ref<number> = ref(0);

const appliesToOptions: ComputedRef<
  {
    id: AdditionalChargeApplicationType;
    longName: string;
  }[]
> = computed(() => {
  return [
    AdditionalChargeApplicationType.NON_FREIGHT,
    AdditionalChargeApplicationType.FREIGHT,
  ].map((type) => {
    return {
      id: type,
      longName: `${returnReadableApplicationTypeName(type)} (${
        type === AdditionalChargeApplicationType.NON_FREIGHT
          ? 'Fuel Levy Not Applicable'
          : 'Fuel Levy Applicable'
      })`,
    };
  });
});

/**
 * Used in the template for AdditionalChargeType v-select. When the selected
 * type ID changes, we also update the additional charge item to make sure the
 * selections are valid for the new AdditionalChargeType.
 */
const selectedAdditionalChargeTypeId: WritableComputedRef<string | undefined> =
  computed({
    get(): string | undefined {
      return props.chargeItem.typeReferenceId;
    },
    set(value: string | undefined): void {
      if (!props.chargeItem) {
        return;
      }
      if (value) {
        const type = props.additionalChargeTypes.find((t) => t._id === value);
        if (type) {
          setChargePropertiesFromType(props.chargeItem, type);
        }
      }
    },
  });

/**
 * Used in the template to determine if we should enable or disable certain
 * inputs based on the selected AdditionalChargeType option.
 */
const selectedAdditionalChargeType: ComputedRef<
  AdditionalChargeType | undefined
> = computed(() => {
  return props.additionalChargeTypes.find(
    (t) => t._id === selectedAdditionalChargeTypeId.value,
  );
});

const chargeTypeSelectOptions: ComputedRef<
  {
    id: string;
    selectName: string;
    shortName: string;
    longName: string;
  }[]
> = computed(() => {
  return props.additionalChargeTypes
    .filter((c) => c.allowAdhocCharges)
    .map((type) => {
      return {
        id: type._id,
        selectName: `${type.shortName} - ${type.longName}`,
        shortName: type.shortName,
        longName: type.longName,
      };
    });
});

const selectedAppliesToOption: WritableComputedRef<AdditionalChargeApplicationType> =
  computed({
    get(): AdditionalChargeApplicationType {
      return props.chargeItem.client.appliesTo;
    },
    set(value: AdditionalChargeApplicationType): void {
      props.chargeItem.client.appliesTo = value;
      props.chargeItem.fleetAsset.appliesTo = value;
    },
  });

const rateBasisOptions: ComputedRef<
  {
    id: AdditionalChargeRateBasis;
    longName: string;
  }[]
> = computed(() => {
  return [
    AdditionalChargeRateBasis.FIXED,
    AdditionalChargeRateBasis.PERCENTAGE,
  ].map((type) => {
    return {
      id: type,
      longName: returnReadableRateBasisName(type),
    };
  });
});

const selectedRateBasisOption: WritableComputedRef<AdditionalChargeRateBasis> =
  computed({
    get(): AdditionalChargeRateBasis {
      return props.chargeItem.client.chargeBasis;
    },
    set(value: AdditionalChargeRateBasis): void {
      props.chargeItem.client.chargeBasis = value;
      props.chargeItem.fleetAsset.chargeBasis = value;
    },
  });

/**
 * Used in the template to return a label for the toll value inputs. Returns a
 * different label based on the chargeOrPayId value.
 */
const tollValueInputLabel: ComputedRef<string> = computed(() => {
  switch (chargeOrPayId.value) {
    case ChargePayOption.CHARGE_ONLY:
      return 'Client Charge Amount (inc. GST)';
    case ChargePayOption.PAY_ONLY:
      return 'Driver Pay Amount (inc. GST)';
    case ChargePayOption.BOTH:
      return 'Charge And Pay Amount (inc. GST)';
    default:
      return 'Charge And Pay Amount (inc. GST)';
  }
});

/**
 * Used as the v-model of the toll value textfield in the template. Returns the
 * value of the appropriate local property based on the chargeOrPayId value.
 * When setting the value, updates the appropriate local property.
 */
const tollValueInputController: WritableComputedRef<number> = computed({
  get(): number {
    switch (chargeOrPayId.value) {
      case ChargePayOption.CHARGE_ONLY:
        return clientAmount.value;
      case ChargePayOption.PAY_ONLY:
        return fleetAssetAmount.value;
      case ChargePayOption.BOTH:
        return chargeAndPayAmount.value;
      default:
        return chargeAndPayAmount.value;
    }
  },
  set(value: number): void {
    switch (chargeOrPayId.value) {
      case ChargePayOption.CHARGE_ONLY:
        clientAmount.value = value;
        break;
      case ChargePayOption.PAY_ONLY:
        fleetAssetAmount.value = value;
        break;
      case ChargePayOption.BOTH:
        chargeAndPayAmount.value = value;
        break;
    }
  },
});

/**
 * Watches the local property containing the clientAmount value and updates the
 * chargeItem prop values accordingly.
 */
watch(clientAmount, (value: number) => {
  if (!props.isTollCharge) {
    if (isClientDeduction.value) {
      value = value * -1;
    }
    props.chargeItem.client.charge = value;
  } else {
    if (value > 0) {
      props.chargeItem.client.charge = RoundCurrencyValue((value / 11) * 10);
    } else {
      props.chargeItem.client.charge = 0;
    }
  }
});

/**
 * Watches the local property containing the fleetAssetAmount value and updates
 * the chargeItem prop values accordingly.
 */
watch(fleetAssetAmount, (value: number) => {
  if (!props.isTollCharge) {
    if (isFleetAssetDeduction.value) {
      value = value * -1;
    }
    props.chargeItem.fleetAsset.charge = value;
  } else {
    if (value > 0) {
      props.chargeItem.fleetAsset.charge = RoundCurrencyValue(
        (value / 11) * 10,
      );
    } else {
      props.chargeItem.fleetAsset.charge = 0;
    }
  }
});

/**
 * Watches the local property containing the chargeAndPayAmount value and
 * updates the chargeItem prop values accordingly.
 */
watch(chargeAndPayAmount, (value: number) => {
  if (value > 0) {
    props.chargeItem.fleetAsset.charge = RoundCurrencyValue((value / 11) * 10);
    props.chargeItem.client.charge = RoundCurrencyValue((value / 11) * 10);
  } else {
    props.chargeItem.fleetAsset.charge = 0;
    props.chargeItem.client.charge = 0;
  }
});

/**
 * Used in the template to conditionally display a section header
 */
const chargeLabelName = computed(() => {
  return props.isTollCharge ? 'Enter Toll Name' : 'Charge Name';
});

/**
 * Prefill charge name with 'Tolls' if the TOL category is selected. Otherwise
 * set to empty string so user can complete.
 */
watch(
  () => props.isTollCharge,
  (value: boolean) => {
    if (value) {
      props.chargeItem.longName = 'Tolls';
    } else {
      props.chargeItem.longName = '';
    }
  },
);

/**
 * Used in the template for toll type charges
 */
const chargeOrPaySelectItems = computed(() => {
  const selectList: ShortLongName[] = [
    {
      longName: 'Client Charge + Driver Pay',
      id: 1,
      shortName: '',
    },
    {
      longName: 'Client Charge Only',
      id: 2,
      shortName: '',
    },
    {
      longName: 'Driver Pay Only',
      id: 3,
      shortName: '',
    },
  ];

  return selectList;
});

/**
 * Called the the v-select for the toll pay/charge method is updated. Sets the
 * values in the chargeItem according.
 */
function chargeOrPayOptionChanged(id: number) {
  switch (id) {
    case ChargePayOption.BOTH:
      break;
    case ChargePayOption.CHARGE_ONLY:
      fleetAssetAmount.value = 0;
      props.chargeItem.fleetAsset.charge = 0;
      chargeAndPayAmount.value = 0;
      break;
    case ChargePayOption.PAY_ONLY:
      clientAmount.value = 0;
      props.chargeItem.client.charge = 0;
      chargeAndPayAmount.value = 0;
      break;
  }
}

/**
 * Watches the isClientDeduction value and updates the client charge value to be
 * positive or negative accordingly.
 */
watch(isClientDeduction, (value: boolean) => {
  if (value) {
    props.chargeItem.client.charge = clientAmount.value * -1;
  } else {
    props.chargeItem.client.charge = Math.abs(props.chargeItem.client.charge);
  }
});

/**
 * Watches the isFleetAssetDeduction value and updates the driver pay value to
 * be positive or negative accordingly.
 */
watch(isFleetAssetDeduction, (value: boolean) => {
  if (value) {
    props.chargeItem.fleetAsset.charge = fleetAssetAmount.value * -1;
  } else {
    props.chargeItem.fleetAsset.charge = Math.abs(
      props.chargeItem.fleetAsset.charge,
    );
  }
});

// Focus textfield in child after short delay
function setFocusOnTitleTextfield() {
  if (focusTextfieldTimeout.value) {
    clearTimeout(focusTextfieldTimeout.value);
  }
  focusTextfieldTimeout.value = setTimeout(() => {
    // Find textfield element, focus and select the contents
    const el = document.getElementById('addCustomChargeCategorySelect') as
      | HTMLInputElement
      | null
      | undefined;
    if (el) {
      el.focus();
      el.select();
    }
  }, 100);
}
onMounted(() => {
  let clientRate = props.chargeItem.client.charge;
  let fleetAssetRate = props.chargeItem.fleetAsset.charge;
  if (!Math.sign(clientRate) && fleetAssetRate !== 0) {
    isClientDeduction.value = true;
    clientRate = Math.abs(fleetAssetRate);
  }
  if (!Math.sign(fleetAssetRate) && fleetAssetRate !== 0) {
    isFleetAssetDeduction.value = true;
    fleetAssetRate = Math.abs(clientRate);
  }
  if (clientAmount.value !== clientRate) {
    clientAmount.value = clientRate;
  }
  if (fleetAssetAmount.value !== fleetAssetRate) {
    fleetAssetAmount.value = fleetAssetRate;
  }

  setFocusOnTitleTextfield();
});
</script>

<style scoped lang="scss">
.add-custom-additional-charge {
  padding: $dialog-content-padding;
  .subsection__header {
    font-size: $font-size-large;
    font-weight: 600;
    color: var(--primary-light);
    text-transform: uppercase;
  }
}
</style>
