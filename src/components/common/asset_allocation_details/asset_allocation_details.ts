import JobStatusManagement from '@/components/common/job_status_management/job_status_management.vue';
import {
  returnFormattedDate,
  returnFormattedTime,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { initialiseJobDetails } from '@/helpers/classInitialisers/InitialiseJobDetails';
import HireContract from '@/interface-models/FleetAsset/EquipmentHire/HireContract';
import ShortLongName from '@/interface-models/Generic/ShortLongName/ShortLongName';
import PUDSheet, {
  AllocatedJobDetails,
} from '@/interface-models/Jobs/Allocation/AllocatedJobDetails';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { JobEventSummary } from '@/interface-models/Jobs/JobEventSummary';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { useAllocationStore } from '@/store/modules/AllocationStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useRecurringJobStore } from '@/store/modules/RecurringJobStore';
import Mitt from '@/utils/mitt';
import { v4 as uuidv4 } from 'uuid';
import { Component, Prop, Vue } from 'vue-property-decorator';
import draggable from 'vuedraggable';

interface JobSummaryDetails extends JobDetails {
  timeDuration: string;
  statusValue: string;
}

interface PudSheetsDetails extends PUDSheet {
  id?: string;
  pudItem?: PUDItem;
  color?: string;
}

interface MenuData {
  id: number;
  value: string;
  longName: string;
  icon: string;
  hidden: boolean;
}

@Component({
  components: {
    draggable,
    JobStatusManagement,
  },
})
export default class AssetAllocationDetails extends Vue {
  @Prop({ default: false }) public isDialog: boolean;
  @Prop() public dialogTabIndex: string;
  @Prop() public allocatedJobDetails: AllocatedJobDetails;

  public returnFormattedTime = returnFormattedTime;

  public hireContract: HireContract | null = null;
  public hireContractPanel: boolean[] = [false];
  public editingAllocatedJobDetails: boolean = false;

  public pudSheetsClone: PudSheetsDetails[] = [];
  public pudSheetsCloneOriginalCopy: PudSheetsDetails[] = [];

  public associatedJobDetails: JobSummaryDetails[] = [];
  public allocatedPudItemMap: Map<string, PUDItem> = new Map();
  public jobColorMap: Map<number, string> = new Map();
  public jobDetailsList: JobDetails[] = [];
  public menuOptions: ShortLongName[] = [
    {
      id: 1,
      longName: 'Current Work',
      shortName: 'STATUS',
    },
    {
      id: 2,
      longName: 'Re-Order Drops',
      shortName: 'REORDER',
    },
  ];

  public selectedViewType: string = 'STATUS';
  public pudSheetsEdited: boolean = false;

  public statusMenuOptions: MenuData[] = [
    {
      id: 0,
      value: 'search',
      longName: 'Address Search',
      icon: 'far fa-search',
      hidden: false,
    },
    {
      id: 1,
      value: 'nickname',
      longName: 'Nicknamed Addresses',
      icon: 'fas fa-address-card',
      hidden: false,
    },
    {
      id: 2,
      value: 'pindrop',
      longName: 'Map Pin Drop',
      icon: 'far fa-map-pin',
      hidden: false,
    },
    {
      id: 3,
      value: 'manual',
      longName: 'Manual Address',
      icon: 'far fa-list-alt',
      hidden: false,
    },
  ];

  public colorArray: string[] = [
    '#E31059',
    '#313290',
    '#F6AD15',
    '#0B5E49',
    '#0CF5DB',
    '#AB54D7',
    '#0018A0',
    '#28D419',
    '#C16133',
    '#7B8102',
    '#D6FED5',
    '#D59CE7',
    '#86252D',
    '#5236C9',
    '#68466F',
  ];

  get dragOptions() {
    return {
      animation: 100,
      group: 'fleet-description',
      disabled: false,
      ghostClass: 'ghost',
    };
  }

  public currentlyEditingPudId: string = '';
  public currentlyEditingJobId: number = 0;
  public currentlyEditingPudStatus: string = '';
  public originalPudStatus: string = '';
  public pudStatusOptions: string[] = ['INCOMPLETE', 'ARRIVED', 'FINISHED'];

  public returnFormattedDate(epoch: number) {
    return returnFormattedDate(epoch, 'DD/MM/YYYY');
  }

  public setEditMode(value: boolean) {
    this.editingAllocatedJobDetails = value;
  }

  public selectedViewTypeChanged() {
    if (this.selectedViewType === 'REORDER') {
      this.pudSheetsCloneOriginalCopy = JSON.parse(
        JSON.stringify(this.pudSheetsClone),
      );
    } else {
      this.pudSheetsCloneOriginalCopy = [];
      this.pudSheetsEdited = false;
    }
  }

  public reorderedPudSheets() {
    this.pudSheetsEdited = true;
  }

  public async confirmReorderedPudSheets() {
    if (!this.allocatedJobDetails) {
      return;
    }
    const clonedAjd: AllocatedJobDetails = JSON.parse(
      JSON.stringify(this.allocatedJobDetails),
    );
    const cloned = JSON.parse(JSON.stringify(this.pudSheetsClone));
    for (let i = 0; i < cloned.length; i++) {
      const pud: PudSheetsDetails = cloned[i];
      delete pud.id;
      delete pud.pudItem;
      delete pud.color;
    }
    clonedAjd.pudSheets = cloned;

    // Send update request and handle response
    this.receivedAllocatedJobDetails(
      await useAllocationStore().updateAllocatedJobDetails(clonedAjd),
      true,
    );
    this.pudSheetsEdited = false;
  }

  /**
   * Revert the pudSheetsClone to the original copy
   */
  public cancelReorderedPudSheets() {
    this.pudSheetsEdited = false;
    if (this.pudSheetsClone.length === this.pudSheetsCloneOriginalCopy.length) {
      this.pudSheetsClone = JSON.parse(
        JSON.stringify(this.pudSheetsCloneOriginalCopy),
      );
    }
  }

  /**
   * Handler for response to update operation.
   */
  public receivedAllocatedJobDetails(
    allocatedJobDetails: AllocatedJobDetails | null,
    isUpdate: boolean,
  ) {
    if (!allocatedJobDetails) {
      return;
    }
    if (!isUpdate && this.isDialog) {
      this.jobDetailsList = [];
      if (this.allocatedJobDetails.jobIds.length > 0) {
        this.searchForJobs(this.allocatedJobDetails.jobIds);
        return;
      }
    }
    this.receivedJobDetailsFromSearch(allocatedJobDetails, this.jobDetailsList);
  }

  /**
   * Searches for full JobDetails documents using the provided list of jobIds,
   * converts to accounting summaries and sets to jobList
   * @param jobIds jobIds to query for
   */
  public async searchForJobs(jobIds: number[]) {
    const results = await useJobStore().getJobDetailsForJobIds(jobIds);
    if (results) {
      this.receivedJobDetailsFromSearch(this.allocatedJobDetails, results);
    }
  }

  public receivedJobDetailsFromSearch(
    allocatedJobDetails: AllocatedJobDetails,
    jobDetailsList: JobDetails[],
  ) {
    if (!jobDetailsList) {
      return;
    }
    this.jobDetailsList = jobDetailsList;
    this.associatedJobDetails = [];
    const ajd: AllocatedJobDetails = allocatedJobDetails;
    this.constructJobDetailsSummary(ajd.jobIds);

    const ajdClone: PudSheetsDetails[] = JSON.parse(
      JSON.stringify(ajd.pudSheets),
    );
    for (let i = 0; i < ajdClone.length; i++) {
      const pudSheet = ajdClone[i] as PudSheetsDetails;
      pudSheet.id = uuidv4().split('-').join('');
      pudSheet.pudItem = this.allocatedPudItemMap.get(pudSheet.pudId);
      pudSheet.color = this.jobColorMap.get(pudSheet.jobId);
    }
    this.pudSheetsClone = ajdClone;
    this.pudSheetsCloneOriginalCopy = JSON.parse(
      JSON.stringify(this.pudSheetsClone),
    );
  }

  public constructJobDetailsSummary(jobIds: number[]) {
    this.allocatedPudItemMap.clear();
    this.jobColorMap.clear();

    for (let i = 0; i < jobIds.length; i++) {
      const jobId = jobIds[i];
      let foundJob: JobDetails | undefined = this.jobDetailsList.find(
        (j: JobDetails) => j.jobId === jobId,
      );
      if (!foundJob) {
        continue;
      }
      foundJob = initialiseJobDetails(foundJob);
      const massagedJob: JobSummaryDetails = foundJob as JobSummaryDetails;
      const validPudItems: PUDItem[] = foundJob.pudItems.filter(
        (pudItem) => pudItem.legTypeFlag === 'P' || pudItem.legTypeFlag === 'D',
      );

      this.jobColorMap.set(jobId, this.colorArray[i]);

      for (let j = 0; j < validPudItems.length; j++) {
        const vp: PUDItem = validPudItems[j];
        this.allocatedPudItemMap.set(vp.pudId, vp);
      }
      const startTime = validPudItems[0].epochTime;
      const endTime = validPudItems[validPudItems.length - 1].epochTime;

      massagedJob.timeDuration = `${returnFormattedTime(
        startTime,
      )} - ${returnFormattedTime(endTime)}`;

      massagedJob.statusValue = foundJob.currentExactJobStatus;
      this.associatedJobDetails.push(massagedJob);
    }
  }

  get recurringJobIdMap() {
    return useRecurringJobStore().recurringJobIdMap;
  }

  /**
   * Handle mitt event for a job status update. Refreshes the state.
   * @param payload JobEventSummary object
   */
  private handleJobStatusUpdate(payload: JobEventSummary | null) {
    if (this.allocatedJobDetails.jobIds.includes(payload?.jobId ?? 0)) {
      this.receivedAllocatedJobDetails(this.allocatedJobDetails, true);
    }
  }

  public mounted() {
    if (this.allocatedJobDetails) {
      this.receivedAllocatedJobDetails(this.allocatedJobDetails, false);
    }
    Mitt.on('jobStatusUpdate', this.handleJobStatusUpdate);
  }

  public beforeDestroy() {
    Mitt.off('jobStatusUpdate', this.handleJobStatusUpdate);
  }
}
