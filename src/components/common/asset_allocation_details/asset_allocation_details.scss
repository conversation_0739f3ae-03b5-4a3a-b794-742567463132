.dialog-section__header {
  font-size: 1.02em;
  text-transform: uppercase;
  color: rgb(161, 161, 179);
  font-weight: 500;
  padding: 8px 12px;
}

.expansion-panel-container {
  border: 1px solid $translucent;
  border-radius: $border-radius-sm;
  .expansion-item-container {
    border-radius: $border-radius-sm !important;
    background-color: var(--background-color-300) !important;
  }
}

.tab-item-container {
  padding-left: 16px;
  margin-bottom: 14px;
  position: relative;
  .tab-item {
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 500;
    color: rgb(193, 197, 207);
    transition: 0.3s;
    border-radius: 3px;
    font-family: 'Archivo';
    letter-spacing: 1px;

    &:hover {
      cursor: pointer;
      background-color: rgba(255, 255, 255, 0.086);
    }
  }

  .check-button__icon {
    color: grey;
    font-size: $font-size-14;
  }
  &.disabled {
    pointer-events: none;
    opacity: 0.3;
  }
}

.expansion-job-item {
  .expansion-job-item__card-wrapper {
    padding-top: 1px;

    &.dialog-view {
      // padding-top: 6px;
      .expansion-job-item__card {
        padding: 16px;
      }
    }

    &.pud-type {
      padding-top: 1px;

      .expansion-job-item__card {
        // background-color: #1e1d2571;
        padding: 12px;
      }
    }
    .expansion-job-item__card {
      padding: 12px;

      font-size: $font-size-12;
      text-transform: uppercase;

      //
      &--jobid {
        font-weight: 500;
        color: rgb(255, 185, 23);
      }
      &--clientname {
        font-weight: 500;
        color: rgb(224, 224, 241);
        padding-left: 6px;
      }
      &--time {
        color: #9997a7;
        font-weight: 400;
        padding-left: 6px;
      }
      &--status {
        font-size: $font-size-11;
        font-weight: 700;
      }
    }
  }
}

.draggable-wrapper {
  .draggable-container {
    .draggable-item {
      border-radius: 4px;
      margin-bottom: 2px;
      margin-top: 1px;
      &.active-drag {
        cursor: pointer;
        border: 1px solid rgb(122, 118, 141);
      }
      &.disable-drag {
        border-width: 0px;
        border-bottom-width: 1px;
      }

      padding: 8px 12px 8px 6px;

      .draggable-item__contents {
        border-left: 4px solid red;
        padding-left: 8px;
        font-size: $font-size-12;
        .draggable-item__jobid {
          // font-size: $font-size-15;
          font-weight: 400;
          color: white;
          text-transform: uppercase;
        }
        .draggable-item__suburb {
          // font-size: $font-size-12;
          font-weight: 500;
          color: rgb(174, 170, 206);
          text-transform: uppercase;
        }
      }
    }
  }
}
