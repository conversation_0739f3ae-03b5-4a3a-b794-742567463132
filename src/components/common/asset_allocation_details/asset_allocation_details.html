<div class="asset-allocation-details">
  <v-layout
    v-if="!isDialog || (isDialog && dialogTabIndex === 'ALLOCATED_WORK')"
  >
    <v-expansion-panel
      v-if="associatedJobDetails.length > 0 && !isDialog"
      class="expansion-panel-container"
    >
      <v-expansion-panel-content class="expansion-item-container">
        <template v-slot:header>
          <div>Allocated Work 2</div>
        </template>
        <v-layout
          row
          wrap
          v-for="(jobDetails, index) of associatedJobDetails"
          :key="jobDetails.jobId"
          class="expansion-job-item"
        >
          <v-flex md12 class="expansion-job-item__card-wrapper">
            <v-layout
              align-center
              class="expansion-job-item__card app-bgcolor--400 app-borderside--b app-bordercolor--600"
            >
              <span class="expansion-job-item__card--jobid">
                #{{jobDetails.displayId}}
              </span>
              <span class="expansion-job-item__card--clientname">
                {{jobDetails.client.clientName}}
              </span>
              <span class="expansion-job-item__card--time">
                {{jobDetails.timeDuration}}
              </span>
              <v-spacer></v-spacer>
              <span class="expansion-job-item__card--status"
                >{{jobDetails.statusValue}}</span
              >
            </v-layout>
          </v-flex>
        </v-layout>
      </v-expansion-panel-content>
    </v-expansion-panel>
    <v-flex md12 v-if="isDialog">
      <v-layout justify-space-between>
        <span class="dialog-section__header">Allocated Work</span>
        <v-flex md5>
          <v-select
            :items="menuOptions"
            item-text="longName"
            item-value="shortName"
            v-model="selectedViewType"
            style="transform: scale(0.9); transform-origin: right center"
            class="app-borderside--a app-bordercolor--600"
            @change="selectedViewTypeChanged"
            solo
            background-color="#1c1c1f"
            hide-details
            color="orange"
          >
          </v-select>
        </v-flex>
      </v-layout>
      <v-layout>
        <v-divider class="mt-1 mb-1"></v-divider>
      </v-layout>
      <v-layout
        row
        justify-space-between
        v-if="associatedJobDetails.length > 0 && selectedViewType === 'REORDER'"
      >
        <v-btn
          v-if="pudSheetsEdited"
          small
          flat
          color="white"
          @click="cancelReorderedPudSheets"
          >Cancel</v-btn
        >
        <v-spacer></v-spacer>
        <v-btn
          small
          color="teal accent-3"
          outline
          :disabled="!pudSheetsEdited"
          @click="confirmReorderedPudSheets()"
          >Confirm</v-btn
        >
      </v-layout>
      <v-layout
        class="pa-1 mt-2 app-bgcolor--400 app-bordercolor--600 app-borderside--a"
        v-if="associatedJobDetails.length > 0 && selectedViewType === 'STATUS'"
      >
        <v-flex md12>
          <v-layout
            v-for="(jobDetails, index) of associatedJobDetails"
            :key="jobDetails.jobId"
            row
            wrap
          >
            <JobStatusManagement
              :jobDetails="jobDetails"
              :isDialog="isDialog"
              :statusValue="jobDetails.statusValue"
              :hidePudStatuses="true"
              :allowStatusChange="false"
            >
            </JobStatusManagement>
            <v-flex md12 v-if="index !== associatedJobDetails.length - 1">
              <v-divider></v-divider>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>

      <v-layout
        row
        wrap
        class="draggable-wrapper"
        v-if="associatedJobDetails.length > 0 && selectedViewType === 'REORDER' && allocatedJobDetails"
      >
        <v-flex md12>
          <draggable
            v-model="pudSheetsClone"
            @change="reorderedPudSheets"
            class="draggable-container"
            v-bind="dragOptions"
            draggable=".active-drag"
          >
            <transition-group name="list" tag="v-flex">
              <v-layout
                row
                v-for="(pudSheet, index) in pudSheetsClone"
                class="draggable-item"
                v-if="pudSheet.pudItem"
                :class="pudSheet.pudItem.status === null ? 'active-drag app-bgcolor--600 ' : 'disable-drag app-bgcolor--400 app-borderside--b app-bordercolor--600'"
                :key="pudSheet.id"
              >
                <v-flex
                  md12
                  class="draggable-item__contents"
                  :style="`border-left-color: ${pudSheet.color}`"
                >
                  <v-layout justify-space-between align-center>
                    <span class="draggable-item__jobid"
                      >Job #{{ recurringJobIdMap.get(pudSheet.jobId) ?
                      recurringJobIdMap.get(pudSheet.jobId) : pudSheet.jobId ?
                      pudSheet.jobId : '--' }}</span
                    >

                    <span v-if="pudSheet.pudItem.status !== null">
                      <v-tooltip left>
                        <template v-slot:activator="{ on }">
                          <v-icon color="amber" v-on="on" size="16"
                            >fad fa-check-circle</v-icon
                          >
                        </template>
                        <span>This stop has been completed.</span>
                      </v-tooltip>
                    </span>
                  </v-layout>
                  <v-layout>
                    <span class="draggable-item__suburb"
                      >{{pudSheet.pudItem.address.suburb}}</span
                    >
                  </v-layout>
                </v-flex>
                <v-icon size="18">reorder</v-icon>
              </v-layout>
            </transition-group>
          </draggable>
        </v-flex>
      </v-layout>
      <v-layout
        v-if="associatedJobDetails.length === 0"
        class="subheader--faded pt-3"
        justify-center
      >
        No current allocated work found.
      </v-layout>
    </v-flex>
  </v-layout>
</div>
