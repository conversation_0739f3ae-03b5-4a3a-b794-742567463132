import { JobRouteProgress } from '@/interface-models/Jobs/JobRouteProgress';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';

export enum SegmentStatus {
  SUCCESS = 'success',
  PENDING = 'pending',
}

export enum LatenessStatus {
  LATE = 'late',
  EARLY = 'early',
}

export interface PudSummary {
  id: string;
  title: string;
  arrival: string;
  departure: string;
  isFirst: boolean;
  isLast: boolean;
  estimatedTravel?: string;
  actualTravel?: string;
  travelDifferenceTime?: string;
  travelDifferencePercent?: string;
  travelIsError?: boolean;
  estimatedLoad?: string;
  actualLoad?: string;
  loadDifferenceTime?: string;
  loadDifferencePercent?: string;
  loadIsError?: boolean;
}

export interface TimeMark {
  time: number;
  label: string;
  position: number;
}

export interface VarianceData {
  variance: number;
  varianceMinutes: string;
  variancePercentage: number;
  isLate: boolean;
}

export interface BreakSegment {
  id: string;
  start: number;
  end: number;
  top: number;
  height: number;
  location?: string;
  startTime: string;
  endTime: string;
  durationFormatted: string;
  visibleStartTime: string;
  visibleEndTime: string;
}

export interface TimelineRange {
  jobStartTime: number;
  jobEndTime: number;
  totalTimelineDuration: number;
}

export interface TempSegment {
  startTime: number;
  endTime: number;
  progress: JobRouteProgress | undefined;
  pud: PUDItem;
}

interface SegmentData {
  duration: number;
  estimated: number;
  variance: number;
  status: LatenessStatus;
  pudTitle: string;
  start?: number;
  end?: number;
}

interface SegmentDetailsData {
  estimated: number;
  actual: number;
  variance: number;
  varianceMinutes: string;
  variancePercentage: number;
  isLate: boolean;
}

export interface SegmentDetails {
  transit: SegmentDetailsData;
  loading: SegmentDetailsData;
  total: SegmentDetailsData;
}

export interface PudSegment {
  pudId: string;
  title: string;
  address: string;
  startTime: number;
  endTime: number;
  scheduledStartTime?: number | null;
  startPosition: number;
  width: number;
  duration: string;
  estimatedTime: string;
  difference: string;
  differenceClass: 'late' | 'early';
  status: SegmentStatus;
  totalSegmentDuration: number;
  arrivalDifferenceInMins?: string;
  arrivalDifferenceWarning?: boolean | null;
  actualLoadTimeReadable?: string;
  loadTimeWarning?: boolean | null;
  differenceInLoadTimeReadable?: string;
  actualDepartureTime?: number | null;
  actualDepartureTimeReadable?: string;
  driverLocationUnknown?: boolean;
  actualArrivalTimeReadable?: string;
  expectedArrivalTimeReadable?: string;
  isFirstLeg: boolean;
  transit: SegmentData;
  onSite: SegmentData;
  details: SegmentDetails;
}
