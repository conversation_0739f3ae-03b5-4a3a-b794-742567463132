<template>
  <section
    class="job-event-timeline-v2"
    :style="{
      maxHeight: typeof maxHeight === 'number' ? `${maxHeight}px` : maxHeight,
    }"
  >
    <!-- Timeline Color Legend -->
    <div class="timeline-legend">
      <!-- Tooltip ICON -->
      <v-tooltip right>
        <template v-slot:activator="{ on }">
          <v-icon v-on="on" class="legend-info-icon" color="info" small
            >info</v-icon
          >
        </template>
        <span
          style="display: flex; flex-direction: column; align-items: flex-start"
        >
          <span
            style="
              font-weight: 600;
              font-size: 10px;
              margin-bottom: 8px;
              color: var(--light-text-color);
            "
            >Timeline Bar Legend</span
          >
          <svg
            width="90"
            height="70"
            viewBox="0 0 90 70"
            style="background: transparent"
          >
            <!-- Break label -->
            <text
              x="0"
              y="40"
              fill="#ffff"
              font-size="8"
              font-family="sans-serif"
              text-anchor="start"
              alignment-baseline="middle"
              font-weight="bold"
            >
              Break
            </text>
            <!-- Break bar -->
            <rect
              x="28"
              y="8"
              width="6"
              height="54"
              fill="#52504e"
              stroke="#888"
              rx="2"
            />
            <!-- Transit -->
            <rect x="40" y="8" width="16" height="27" fill="#4caf50" rx="2" />
            <text x="60" y="28" fill="#fff" font-size="8">Transit</text>
            <!-- On Site -->
            <rect x="40" y="36" width="16" height="27" fill="#2196f3" rx="2" />
            <text x="60" y="56" fill="#fff" font-size="8">On site</text>
          </svg>
        </span>
      </v-tooltip>
      <!-- Legend for timeline -->
      <div class="legend-item">
        <span class="legend-title">Transit:</span>
        <span class="legend-color transit-early"></span> On Time/Early
        <span class="legend-color transit-late"></span> Late
      </div>
      <div class="legend-item">
        <span class="legend-title">On Site & Loading:</span>
        <span class="legend-color onsite-early"></span> On Time/Early
        <span class="legend-color onsite-late"></span> Late
      </div>
      <div class="legend-item">
        <span class="legend-title">Other:</span>
        <span class="legend-color pending"></span> Pending
        <span class="legend-color break"></span> Break
      </div>
    </div>
    <!-- Job Summary cards -->
    <div class="timeline-summary">
      <div class="summary-stats">
        <div class="stat-item">
          <div class="stat-value">{{ totalEvents }}</div>
          <div class="stat-label">Total Events</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ successEvents }}</div>
          <div class="stat-label">Completed Events</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ pendingEvents }}</div>
          <div class="stat-label">Pending Events</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ totalDuration }}</div>
          <div class="stat-label">Total Job Duration</div>
        </div>
      </div>
    </div>
    <!-- Timeline Container -->
    <div class="timeline-layout-container">
      <!-- Left Side: Timeline Track -->
      <div class="timeline-track-container">
        <!-- Time Scale -->
        <div class="time-scale">
          <div
            v-for="timeMark in timeMarks"
            :key="timeMark.time"
            class="time-mark"
            :style="{ top: timeMark.position + '%' }"
          >
            <span class="time-label">{{ timeMark.label }}</span>
            <div class="time-line"></div>
          </div>
        </div>

        <!-- Main Timeline Track -->
        <div class="timeline-track">
          <!-- Progress Background -->
          <div class="timeline-progress-background">
            <div
              class="progress-fill"
              :style="{ height: currentTimePosition + '%' }"
            ></div>
          </div>

          <!-- Timeline Segments -->
          <div
            v-for="segment in pudSegments"
            :key="segment.pudId"
            class="timeline-segment-container"
            :class="{ selected: selectedEvent === segment }"
            :style="{
              top: segment.startPosition + '%',
              height: segment.width + '%',
            }"
            @click="selectedEvent = segment"
          >
            <div class="timeline-segment">
              <!-- Transit -->
              <v-tooltip
                v-if="!segment.isFirstLeg"
                top
                attach
                :content-class="`timeline-tooltip ${
                  segment.status === SegmentStatus.PENDING
                    ? 'pending-tooltip'
                    : `transit-tooltip ${
                        segment.transit.status === LatenessStatus.LATE
                          ? 'transit-late-tooltip'
                          : 'transit-early-tooltip'
                      }`
                }`"
              >
                <template v-slot:activator="{ on }">
                  <div class="sub-segment-container" v-on="on">
                    <div
                      v-if="segment.transit.status === LatenessStatus.LATE"
                      class="late-segment-wrapper"
                    >
                      <div
                        class="sub-segment transit-late"
                        :style="{
                          height:
                            (segment.transit.estimated /
                              segment.transit.duration) *
                              100 +
                            '%',
                        }"
                      ></div>
                      <div
                        class="sub-segment transit-late"
                        :style="{
                          height:
                            (Math.abs(segment.transit.variance) /
                              segment.transit.duration) *
                              100 +
                            '%',
                        }"
                      ></div>
                    </div>
                    <div
                      v-else
                      class="sub-segment"
                      :class="
                        segment.status === SegmentStatus.PENDING
                          ? 'pending'
                          : isLate(segment.transit.status)
                            ? 'transit-late'
                            : 'transit-early'
                      "
                      style="height: 100%"
                    ></div>
                  </div>
                </template>
                <span
                  >{{ segment.transit.pudTitle }} - Transit:
                  {{
                    segment.status === SegmentStatus.PENDING
                      ? 'Pending'
                      : returnCorrectDuration(segment.transit.variance) +
                        ' variance'
                  }}</span
                >
              </v-tooltip>
              <!-- Transparent transit section for first leg -->
              <div v-else class="sub-segment-container">
                <div
                  class="sub-segment transit-transparent"
                  style="height: 100%"
                ></div>
              </div>

              <!--  On-Site & Loading Block -->
              <v-tooltip
                top
                attach
                :content-class="`timeline-tooltip ${
                  segment.status === SegmentStatus.PENDING
                    ? 'pending-tooltip'
                    : `onsite-tooltip ${
                        segment.onSite.status === LatenessStatus.LATE
                          ? 'onsite-late-tooltip'
                          : 'onsite-early-tooltip'
                      }`
                }`"
              >
                <template v-slot:activator="{ on }">
                  <div class="sub-segment-container" v-on="on">
                    <div
                      class="sub-segment"
                      :class="
                        segment.status === SegmentStatus.PENDING
                          ? 'pending'
                          : isLate(segment.onSite.status)
                            ? 'onsite-late'
                            : 'onsite-early'
                      "
                      style="height: 100%"
                    ></div>
                  </div>
                </template>
                <span>
                  {{ segment.onSite.pudTitle }} - On Site & Loading:
                  {{
                    segment.status === SegmentStatus.PENDING
                      ? 'Pending'
                      : returnCorrectDuration(segment.onSite.duration)
                  }}
                </span>
              </v-tooltip>
            </div>
          </div>
          <!-- BREAK BAR: Render break segments as a separate bar to the right -->
          <div class="break-bar">
            <div
              v-for="breakSeg in breakSegments"
              :key="breakSeg.id"
              class="break-segment"
              :style="{
                top: breakSeg.top + '%',
                height: breakSeg.height + '%',
              }"
            >
              <v-tooltip right content-class="timeline-tooltip">
                <template v-slot:activator="{ on }">
                  <div class="break-segment-inner" v-on="on"></div>
                </template>
                <span>
                  <strong>Break</strong><br />
                  Start: {{ breakSeg.startTime }}<br />
                  End: {{ breakSeg.endTime }}<br />
                  Duration: {{ breakSeg.durationFormatted }}<br />
                  <span v-if="breakSeg.location"
                    >@ {{ breakSeg.location }}</span
                  >
                </span>
              </v-tooltip>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Side: Details Panel -->
      <div
        class="details-panel-container"
        :style="{ height: detailsPanelHeight }"
      >
        <!-- SELECTED SEGMENT DETAILS -->
        <v-card v-if="selectedEvent" class="details-card" flat>
          <v-card-title class="details-header">
            <div class="header-left">
              <v-icon color="#757575" class="ml-2"
                >fas fa-map-marker-alt</v-icon
              >
              <div>
                <div class="details-title">{{ selectedEvent.title }}</div>
                <div class="details-subtitle">
                  {{ selectedEvent.address }}
                </div>
              </div>
            </div>
            <v-btn
              icon
              class="close-details-btn"
              @click="selectedEvent = null"
              small
            >
              <v-icon>cancel</v-icon>
            </v-btn>
          </v-card-title>
          <v-card-text class="details-content">
            <div class="segment-summary-cards">
              <div
                class="summary-card"
                :class="{
                  'status-late':
                    selectedEvent.status !== SegmentStatus.PENDING &&
                    selectedEvent.arrivalDifferenceWarning,
                  'status-early':
                    selectedEvent.status !== SegmentStatus.PENDING &&
                    selectedEvent.arrivalDifferenceInMins &&
                    !selectedEvent.arrivalDifferenceWarning &&
                    selectedEvent.arrivalDifferenceInMins.includes('Early'),
                  'status-on-time':
                    selectedEvent.status !== SegmentStatus.PENDING &&
                    (!selectedEvent.arrivalDifferenceInMins ||
                      (!selectedEvent.arrivalDifferenceWarning &&
                        !selectedEvent.arrivalDifferenceInMins.includes(
                          'Early',
                        ))),
                  'status-pending':
                    selectedEvent.status === SegmentStatus.PENDING,
                }"
              >
                <v-icon class="icon">fa-route</v-icon>
                <div class="card-content">
                  <div class="label">
                    <span
                      v-if="
                        selectedEvent.status !== SegmentStatus.PENDING &&
                        selectedEvent.arrivalDifferenceInMins
                      "
                    >
                      Arrived
                    </span>
                    <span v-else>Pending</span>
                  </div>
                  <div
                    class="value"
                    v-if="selectedEvent.status !== SegmentStatus.PENDING"
                  >
                    <div>
                      {{ selectedEvent.arrivalDifferenceInMins }}
                      <span
                        v-if="selectedEvent.expectedArrivalTimeReadable"
                        class="expected-time-inline"
                      >
                        (Exp. {{ selectedEvent.expectedArrivalTimeReadable }})
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="summary-card"
                :class="{
                  'status-late':
                    selectedEvent.status !== SegmentStatus.PENDING &&
                    selectedEvent.loadTimeWarning,
                  'status-early':
                    selectedEvent.status !== SegmentStatus.PENDING &&
                    selectedEvent.differenceInLoadTimeReadable &&
                    !selectedEvent.loadTimeWarning,
                  'status-on-time':
                    selectedEvent.status !== SegmentStatus.PENDING &&
                    !selectedEvent.differenceInLoadTimeReadable,
                  'status-pending':
                    selectedEvent.status === SegmentStatus.PENDING,
                }"
              >
                <v-icon class="icon">fa-truck-loading</v-icon>
                <div class="card-content">
                  <div class="label">
                    <span
                      v-if="
                        selectedEvent.status !== SegmentStatus.PENDING &&
                        selectedEvent.differenceInLoadTimeReadable
                      "
                    >
                      Loading
                    </span>
                    <span v-else>Pending</span>
                  </div>
                  <div class="value">
                    {{ selectedEvent.differenceInLoadTimeReadable }}
                  </div>
                </div>
              </div>
              <div class="summary-card">
                <v-icon class="icon">fas fa-clock</v-icon>
                <div class="card-content">
                  <div class="label">
                    {{
                      selectedEvent.status === SegmentStatus.PENDING
                        ? 'Estimated Time Total'
                        : 'Actual Time Total'
                    }}
                  </div>
                  <div class="value">
                    {{
                      selectedEvent.isFirstLeg
                        ? returnCorrectDuration(
                            selectedEvent.details.loading.actual ||
                              selectedEvent.details.loading.estimated,
                          )
                        : returnCorrectDuration(
                            selectedEvent.totalSegmentDuration,
                          )
                    }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Main Details Table -->
            <div class="details-table-container">
              <table class="details-table">
                <tbody>
                  <!-- Time Range Row -->
                  <div class="time-range-container">
                    <div class="time-range-item">
                      <span class="time-label">Departed previous stop:</span>
                      <span class="time-value">{{
                        selectedEvent.isFirstLeg
                          ? 'N/A'
                          : returnFormattedTime(selectedEvent.startTime)
                      }}</span>
                    </div>
                    <v-icon class="time-arrow">arrow_forward</v-icon>
                    <div class="time-range-item">
                      <span class="time-label">Arrival at this stop:</span>
                      <span class="time-value">
                        <span
                          v-if="
                            selectedEvent.actualArrivalTimeReadable &&
                            selectedEvent.actualArrivalTimeReadable !==
                              'Invalid date'
                          "
                        >
                          {{ selectedEvent.actualArrivalTimeReadable }}
                        </span>
                        <span v-else>Pending</span>
                      </span>
                    </div>
                    <v-icon class="time-arrow">arrow_forward</v-icon>
                    <div class="time-range-item">
                      <span class="time-label">Departure from this stop:</span>
                      <span class="time-value">{{
                        returnFormattedTime(selectedEvent.endTime)
                      }}</span>
                    </div>
                  </div>
                </tbody>
              </table>
            </div>

            <!-- Details Table -->
            <div class="details-table-container">
              <table class="details-table">
                <thead>
                  <tr>
                    <th></th>
                    <th>Estimated</th>
                    <th>Actual</th>
                    <th>Time Variance</th>
                  </tr>
                </thead>
                <tbody>
                  <!-- Transit Row -->
                  <tr v-if="!selectedEvent.isFirstLeg">
                    <td>
                      <div
                        class="section-header"
                        style="display: flex; align-items: center; gap: 6px"
                      >
                        <v-icon size="16" class="mr-2">fas fa-route</v-icon>
                        Transit
                        <span class="time-brackets"
                          >({{ returnFormattedTime(selectedEvent.startTime) }} -
                          {{ selectedEvent.actualArrivalTimeReadable }})</span
                        >
                      </div>
                    </td>
                    <td>
                      {{
                        returnCorrectDuration(
                          selectedEvent.details.transit.estimated,
                        )
                      }}
                    </td>
                    <td>
                      <span
                        v-if="selectedEvent.status !== SegmentStatus.PENDING"
                      >
                        {{
                          returnCorrectDuration(
                            selectedEvent.details.transit.actual,
                          )
                        }}
                      </span>
                      <span v-else>Pending</span>
                    </td>
                    <td>
                      <span
                        v-if="
                          selectedEvent.status !== SegmentStatus.PENDING &&
                          selectedEvent.details.transit.variance !== 0
                        "
                        class="tag-accent-card"
                        :class="{
                          'red-outline': selectedEvent.details.transit.isLate,
                          'green-outline':
                            !selectedEvent.details.transit.isLate,
                        }"
                      >
                        {{ selectedEvent.details.transit.varianceMinutes }}
                        ({{
                          selectedEvent.details.transit.variancePercentage.toFixed(
                            1,
                          )
                        }}%)
                      </span>
                      <span v-else>-</span>
                    </td>
                  </tr>
                  <!-- Transit Row for First Leg -->
                  <tr v-else>
                    <td>
                      <div
                        class="section-header"
                        style="display: flex; align-items: center; gap: 6px"
                      >
                        <v-icon size="16" class="mr-2">fas fa-route</v-icon>
                        Transit
                        <span class="time-brackets">(N/A - First pickup)</span>
                      </div>
                    </td>
                    <td>N/A</td>
                    <td>N/A</td>
                    <td>N/A</td>
                  </tr>
                  <!-- Loading Row -->
                  <tr>
                    <td>
                      <div
                        class="section-header"
                        style="display: flex; align-items: center; gap: 6px"
                      >
                        <v-icon size="16" class="mr-2"
                          >fas fa-truck-loading</v-icon
                        >
                        Loading
                        <span class="time-brackets"
                          >({{ selectedEvent.actualArrivalTimeReadable }} -
                          {{
                            returnFormattedTime(selectedEvent.endTime)
                          }})</span
                        >
                      </div>
                    </td>
                    <td>
                      {{
                        returnCorrectDuration(
                          selectedEvent.details.loading.estimated,
                        )
                      }}
                    </td>
                    <td>
                      <span
                        v-if="selectedEvent.status !== SegmentStatus.PENDING"
                      >
                        {{
                          returnCorrectDuration(
                            selectedEvent.details.loading.actual,
                          )
                        }}
                      </span>
                      <span v-else>Pending</span>
                    </td>
                    <td>
                      <span
                        v-if="
                          selectedEvent.status !== SegmentStatus.PENDING &&
                          selectedEvent.details.loading.variance !== 0
                        "
                        class="tag-accent-card"
                        :class="{
                          'red-outline': selectedEvent.details.loading.isLate,
                          'green-outline':
                            !selectedEvent.details.loading.isLate,
                        }"
                      >
                        {{ selectedEvent.details.loading.varianceMinutes }}
                        ({{
                          selectedEvent.details.loading.variancePercentage.toFixed(
                            1,
                          )
                        }}%)
                      </span>
                      <span v-else>-</span>
                    </td>
                  </tr>
                  <!-- Total Row -->
                  <tr>
                    <td>
                      <div
                        class="section-header"
                        style="display: flex; align-items: center; gap: 6px"
                      >
                        <v-icon size="16" class="mr-2">fas fa-clock</v-icon>
                        Total
                        <span class="time-brackets"
                          >({{ returnFormattedTime(selectedEvent.startTime) }} -
                          {{
                            returnFormattedTime(selectedEvent.endTime)
                          }})</span
                        >
                      </div>
                    </td>
                    <td>
                      {{
                        selectedEvent.isFirstLeg
                          ? returnCorrectDuration(
                              selectedEvent.details.loading.estimated,
                            )
                          : returnCorrectDuration(
                              selectedEvent.details.total.estimated,
                            )
                      }}
                    </td>
                    <td>
                      <span
                        v-if="selectedEvent.status !== SegmentStatus.PENDING"
                      >
                        {{
                          selectedEvent.isFirstLeg
                            ? returnCorrectDuration(
                                selectedEvent.details.loading.actual,
                              )
                            : returnCorrectDuration(
                                selectedEvent.details.total.actual,
                              )
                        }}
                      </span>
                      <span v-else>Pending</span>
                    </td>
                    <td>
                      <span
                        v-if="
                          selectedEvent.status !== SegmentStatus.PENDING &&
                          (selectedEvent.isFirstLeg
                            ? selectedEvent.details.loading.variance !== 0
                            : selectedEvent.details.total.variance !== 0)
                        "
                        class="tag-accent-card"
                        :class="{
                          'red-outline': selectedEvent.isFirstLeg
                            ? selectedEvent.details.loading.isLate
                            : selectedEvent.details.total.isLate,
                          'green-outline': selectedEvent.isFirstLeg
                            ? !selectedEvent.details.loading.isLate
                            : !selectedEvent.details.total.isLate,
                        }"
                      >
                        {{
                          selectedEvent.isFirstLeg
                            ? selectedEvent.details.loading.varianceMinutes
                            : selectedEvent.details.total.varianceMinutes
                        }}
                        ({{
                          (selectedEvent.isFirstLeg
                            ? selectedEvent.details.loading.variancePercentage
                            : selectedEvent.details.total.variancePercentage
                          ).toFixed(1)
                        }}%)
                      </span>
                      <span v-else>-</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </v-card-text>
        </v-card>
        <!-- Default view -->
        <div v-else class="no-selection-placeholder">
          <span class="header-txt">
            <v-icon class="hand-point-icon">fal fa-hand-pointer</v-icon>
            Select a segment to see details
          </span>

          <v-flex m12 mt-4>
            <div class="table-container">
              <table class="simple-data-table">
                <thead>
                  <tr>
                    <th class="column-header">Stop</th>
                    <th class="column-header">Arrival/Departures</th>
                    <th class="column-header pr-2">Travel</th>
                    <th class="column-header">Load</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="row in summaryTableData"
                    :key="row.id"
                    class="eventtime-card"
                  >
                    <td class="eventtime-card__header eventtime-card__value">
                      {{ row.title }}
                    </td>
                    <td class="eventtime-card__value">
                      <span>
                        <span
                          class="eventtime-card__value"
                          :class="row.isFirst ? 'highlighted' : ''"
                          >{{ row.arrival }}</span
                        >
                        -
                        <span
                          class="eventtime-card__value"
                          :class="row.isLast ? 'highlighted' : ''"
                          >{{ row.departure }}</span
                        >
                      </span>
                    </td>
                    <td class="eventtime-card__value">
                      <span style="position: relative">
                        <span v-if="row.actualTravel">{{
                          row.actualTravel
                        }}</span>
                        <span v-else-if="row.estimatedTravel">{{
                          row.estimatedTravel
                        }}</span>
                      </span>
                      <span
                        v-if="row.travelDifferencePercent"
                        :class="[
                          'eventtime-card__diff',
                          {
                            'eventtime-card__diff--error': row.travelIsError,
                          },
                        ]"
                      >
                        (est. {{ row.estimatedTravel }},
                        {{ row.travelDifferencePercent }})
                      </span>
                    </td>
                    <td class="eventtime-card__value">
                      <span>
                        <span v-if="row.actualLoad">{{ row.actualLoad }}</span>
                        <span v-else-if="row.estimatedLoad">{{
                          row.estimatedLoad
                        }}</span>
                      </span>
                      <span
                        v-if="row.loadDifferenceTime && row.actualLoad"
                        :class="[
                          'eventtime-card__diff',
                          { 'eventtime-card__diff--error': row.loadIsError },
                        ]"
                      >
                        (est. {{ row.estimatedLoad }},
                        {{ row.loadDifferenceTime }})
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </v-flex>
        </div>
        <!-- Job In-Progress Indicator -->
        <div v-if="isJobInProgress" class="in-progress-pulse-line">
          <div class="pulse-content">
            <v-icon size="14" color="orange" class="mr-2"
              >mdi-clock-outline</v-icon
            >
            <span>Job in Progress</span>
          </div>
          <div class="pulse-line"></div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { returnFormattedTime } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { returnCorrectDuration } from '@/helpers/DateTimeHelpers/DurationHelpers';
import { ALLOWED_TRAVEL_VARIANCE_PERCENT } from '@/helpers/RouteHelpers/JobRouteHelpers';
import { WorkDiaryActivityType } from '@/interface-models/Driver/WorkDiary/WorkDiaryActivityType';
import WorkDiaryRecord from '@/interface-models/Driver/WorkDiary/WorkDiaryRecord';
import WorkDiaryRecordHistoryRequest from '@/interface-models/Driver/WorkDiary/WorkDiaryRecordHistoryRequest';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { JobRouteProgress } from '@/interface-models/Jobs/JobRouteProgress';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import moment from 'moment-timezone';
import { computed, onMounted, ref } from 'vue';
import {
  BreakSegment,
  LatenessStatus,
  PudSegment,
  PudSummary,
  SegmentDetails,
  SegmentStatus,
  TempSegment,
  TimelineRange,
  TimeMark,
  VarianceData,
} from './JobEventTimeline';

const props = withDefaults(
  defineProps<{
    job: JobDetails;
    jobRouteProgress: JobRouteProgress[];
    maxHeight?: string | number;
  }>(),
  {
    maxHeight: '70vh',
  },
);

const driverDetailsStore = useDriverDetailsStore();

const selectedEvent = ref<PudSegment | null>(null);

const breakRecords = ref<WorkDiaryRecord[]>([]);

const isJobInProgress = computed(
  () => props.job.workStatus === WorkStatus.IN_PROGRESS,
);

// Summary Statistics
const totalEvents = computed(() => props.job?.pudItems?.length || 0);
const pendingEvents = computed(() => totalEvents.value - successEvents.value);
const successEvents = computed(
  () =>
    pudSegments.value.filter((e) => e.status === SegmentStatus.SUCCESS).length,
);
const totalDuration = computed((): string => {
  if (!pudSegments.value.length) {
    return '0m';
  }
  const first = pudSegments.value[0];
  const last: number = pudSegments.value[pudSegments.value.length - 1].endTime;
  return returnCorrectDuration(last - first.startTime);
});

/**
 * Calculates variance data between actual and estimated times
 * @param actual - The actual time in milliseconds
 * @param estimated - The estimated time in milliseconds
 * @returns Object containing variance calculations including minutes, percentage, and lateness status
 */
function calculateVarianceData(
  actual: number,
  estimated: number,
): VarianceData {
  if (estimated <= 0) {
    return {
      variance: 0,
      varianceMinutes: '0m',
      variancePercentage: 0,
      isLate: false,
    };
  }
  const variance = actual - estimated;
  const varianceMinutes = returnCorrectDuration(Math.abs(variance));
  const variancePercentage = Math.abs((variance / estimated) * 100);
  const isLate = actual > estimated;
  return {
    variance,
    varianceMinutes,
    variancePercentage,
    isLate,
  };
}

/**
 * Creates detailed segment information for transit, loading, and total durations
 * @param hasActualData - Whether actual timing data is available
 * @param travelDuration - Actual travel duration in milliseconds
 * @param loadingDuration - Actual loading duration in milliseconds
 * @param estimatedTravelTime - Estimated travel time in milliseconds
 * @param estimatedLoadTime - Estimated loading time in milliseconds
 * @param totalSegmentDuration - Total actual segment duration
 * @param totalEstimatedDuration - Total estimated segment duration
 * @returns Object containing detailed breakdown of transit, loading, and total metrics
 */
function createSegmentDetails(
  hasActualData: boolean,
  travelDuration: number,
  loadingDuration: number,
  estimatedTravelTime: number,
  estimatedLoadTime: number,
  totalSegmentDuration: number,
  totalEstimatedDuration: number,
): SegmentDetails {
  const totalDifference = totalEstimatedDuration - totalSegmentDuration;

  return {
    transit: {
      estimated: estimatedTravelTime,
      actual: hasActualData ? travelDuration : 0,
      ...calculateVarianceData(travelDuration, estimatedTravelTime),
    },
    loading: {
      estimated: estimatedLoadTime,
      actual: hasActualData ? loadingDuration : 0,
      ...calculateVarianceData(loadingDuration, estimatedLoadTime),
    },
    total: {
      estimated: totalEstimatedDuration,
      actual: hasActualData ? totalSegmentDuration : 0,
      ...calculateVarianceData(totalSegmentDuration, totalEstimatedDuration),
      isLate: hasActualData ? totalDifference < 0 : false,
    },
  };
}

/**
 * Filters PUD items to show based on job progress status
 * For in-progress jobs, only shows completed segments plus the next pending one
 * @param pudItems - Array of PUD items from the job
 * @param jobRouteProgress - Current progress data for the job route
 * @param isJobInProgress - Whether the job is currently in progress
 * @returns Filtered array of PUD items to display
 */
function getPudItemsToShow(
  pudItems: PUDItem[],
  jobRouteProgress: JobRouteProgress[],
  isJobInProgress: boolean,
): PUDItem[] {
  if (!isJobInProgress) {
    return pudItems;
  }
  const progressPudIds = jobRouteProgress.map((p) => p.pudId);
  const firstNullArrivalIndex = jobRouteProgress.findIndex(
    (p) => p.actualArrivalTime === null,
  );
  if (firstNullArrivalIndex !== -1) {
    const pudsToInclude = jobRouteProgress
      .slice(0, firstNullArrivalIndex + 1)
      .map((p) => p.pudId);
    return pudItems.filter((pud) => pudsToInclude.includes(pud.pudId));
  }
  return pudItems.filter((pud) => progressPudIds.includes(pud.pudId));
}

/**
 * Calculates the timeline range based on completed segments and estimated pending segments
 * @param tempSegments - Temporary segments with timing data
 * @returns Object containing job start time, end time, and total timeline duration
 */
function calculateTimelineRange(tempSegments: TempSegment[]): TimelineRange {
  const completedSegments = tempSegments.filter(
    (s) => s?.progress && s.progress.actualArrivalTime !== null,
  );

  if (completedSegments.length === 0) {
    return { jobStartTime: 0, jobEndTime: 0, totalTimelineDuration: 0 };
  }

  const jobStartTime = Math.min(
    ...completedSegments
      .map((s) => s?.startTime)
      .filter((t): t is number => typeof t === 'number'),
  );

  const jobEndTime = Math.max(
    ...completedSegments
      .map((s) => s?.endTime)
      .filter((t): t is number => typeof t === 'number'),
  );

  let totalTimelineDuration = jobEndTime - jobStartTime;

  // Add estimated time for pending segments
  const pendingSegments = tempSegments.filter(
    (s) => s?.progress && s.progress.actualArrivalTime === null,
  );

  if (pendingSegments.length > 0) {
    const estimatedPendingDuration = pendingSegments.reduce((total, s) => {
      const progress = s.progress;
      const pud = s.pud;
      const estimatedTravelTime =
        progress?.estimatedTravelTime || (pud.loadTime || 0) * 0.5;
      const estimatedLoadTime = pud.loadTime || 0;
      return total + estimatedTravelTime + estimatedLoadTime;
    }, 0);
    totalTimelineDuration += estimatedPendingDuration;
  }
  return { jobStartTime, jobEndTime, totalTimelineDuration };
}

/**
 * Creates a single PUD segment with all necessary data and calculations
 * @param pud - PUD item data
 * @param progress - Job route progress data
 * @param index - Index of the PUD in the sequence
 * @param currentTime - Current time for positioning
 * @param jobStartTime - Start time of the job timeline
 * @param totalTimelineDuration - Total duration of the timeline
 * @returns Complete PudSegment object
 */
function createPudSegment(
  pud: PUDItem,
  progress: JobRouteProgress | undefined,
  index: number,
  currentTime: number,
  jobStartTime: number,
  totalTimelineDuration: number,
  isFirstLeg: boolean,
): PudSegment | null {
  if (!progress && !isJobInProgress.value) {
    return null;
  }

  const hasActualData = Boolean(
    progress &&
      progress.actualArrivalTime !== null &&
      (progress.actualDepartureTime !== null ||
        progress.actualLoadTime !== null),
  );

  // Calculate durations - no transit for first leg
  const travelDuration = isFirstLeg ? 0 : progress?.actualTravelTime || 0;
  const loadingDuration = progress?.actualLoadTime || 0;
  const estimatedTravelTime = isFirstLeg
    ? 0
    : progress?.estimatedTravelTime || (pud.loadTime || 0) * 0.5;
  const estimatedLoadTime = pud.loadTime || 0;

  const startTime = currentTime;
  const endTime =
    progress?.actualDepartureTime ||
    (progress?.actualArrivalTime || startTime) +
      (hasActualData ? loadingDuration : estimatedLoadTime);

  const displayTravelDuration = hasActualData
    ? travelDuration
    : estimatedTravelTime;
  const displayLoadingDuration = hasActualData
    ? loadingDuration
    : estimatedLoadTime;
  const totalSegmentDuration = displayTravelDuration + displayLoadingDuration;
  const totalEstimatedDuration = estimatedTravelTime + estimatedLoadTime;

  // Calculate status
  const loadingVariance = progress?.differenceInLoadTime || 0;
  const loadingStatus = getLatenessStatus(loadingVariance, progress, 'load');
  const transitVariance = progress?.differenceInTravelTimeMs || 0;
  const transitStatus = getLatenessStatus(transitVariance, progress, 'travel');

  const totalDifference = totalEstimatedDuration - totalSegmentDuration;

  return {
    pudId: pud.pudId,
    title: `${pud.customerDeliveryName || pud.address.suburb} (${
      pud.legTypeFlag
    })`,
    address: pud.address.formattedAddress,
    startTime,
    endTime,
    scheduledStartTime: progress?.expectedArrivalTime,
    startPosition:
      totalTimelineDuration > 0
        ? ((startTime - jobStartTime) / totalTimelineDuration) * 100
        : 0,
    width:
      totalTimelineDuration > 0
        ? Math.max((totalSegmentDuration / totalTimelineDuration) * 100, 2)
        : 2,
    duration: returnCorrectDuration(totalSegmentDuration),
    estimatedTime: returnCorrectDuration(totalEstimatedDuration),
    difference: returnCorrectDuration(totalDifference),
    differenceClass: totalDifference < 0 ? 'late' : 'early',
    status: hasActualData ? SegmentStatus.SUCCESS : SegmentStatus.PENDING,
    totalSegmentDuration,
    arrivalDifferenceInMins: progress?.arrivalDifferenceInMins,
    arrivalDifferenceWarning: progress?.arrivalDifferenceWarning,
    actualLoadTimeReadable: progress?.actualLoadTimeReadable,
    loadTimeWarning: progress?.loadTimeWarning,
    differenceInLoadTimeReadable: progress?.differenceInLoadTimeReadable,
    actualDepartureTime: progress?.actualDepartureTime,
    actualDepartureTimeReadable: progress?.actualDepartureTimeReadable,
    driverLocationUnknown: progress?.driverLocationUnknown,
    actualArrivalTimeReadable: progress?.actualArrivalTimeReadable,
    expectedArrivalTimeReadable: (() => {
      // Calculate expected arrival time based on booking time
      const bookingTime = pud.epochTime;
      const isAsap = pud.timeDefinition === 9;
      const expectedTime = isAsap ? bookingTime + 3600000 : bookingTime; // +1 hour for ASAP
      return returnFormattedTime(expectedTime);
    })(),
    isFirstLeg: isFirstLeg,

    // Sub-segment data for rendering
    transit: {
      duration: displayTravelDuration,
      estimated: estimatedTravelTime,
      variance: transitVariance,
      status:
        hasActualData && transitVariance > 0
          ? LatenessStatus.LATE
          : LatenessStatus.EARLY,
      pudTitle: `${pud.address.suburb} (Leg ${index + 1})`,
    },
    onSite: {
      duration: displayLoadingDuration,
      start: progress?.actualArrivalTime || startTime,
      end: progress?.actualDepartureTime || endTime,
      estimated: estimatedLoadTime,
      variance: loadingVariance,
      pudTitle: `${pud.address.suburb} (Leg ${index + 1})`,
      status:
        hasActualData && loadingVariance > 0
          ? LatenessStatus.LATE
          : LatenessStatus.EARLY,
    },

    // Combined details using helper function
    details: createSegmentDetails(
      hasActualData,
      travelDuration,
      loadingDuration,
      estimatedTravelTime,
      estimatedLoadTime,
      totalSegmentDuration,
      totalEstimatedDuration,
    ),
  };
}

/**
 * Normalizes segment positions to fit within 0-100% timeline
 * @param segments - Array of segments to normalize
 */
function normalizeSegmentPositions(segments: PudSegment[]): void {
  if (segments.length === 0) {
    return;
  }

  let currentPosition = 0;

  for (let i = 0; i < segments.length; i++) {
    const segment = segments[i];
    const segmentEndPosition = currentPosition + segment.width;

    if (segmentEndPosition > 100) {
      const availableSpace = 100 - currentPosition;
      segment.width = Math.max(availableSpace, 2);
      segment.startPosition = currentPosition;
      currentPosition = 100;
      break;
    } else {
      segment.startPosition = currentPosition;
      currentPosition = segmentEndPosition;
    }
  }
}

/**
 * Computed property that generates timeline segments for PUD items
 * Creates visual segments with positioning, styling, and detailed metrics
 * Handles both completed and pending segments with appropriate status indicators
 */
const pudSegments = computed((): PudSegment[] => {
  if (!props.job?.pudItems?.length) {
    return [];
  }

  const pudItems = props.job.pudItems;
  const pudItemsToShow = getPudItemsToShow(
    pudItems,
    props.jobRouteProgress,
    isJobInProgress.value,
  );

  // Create progress lookup map once to avoid repeated find operations
  const progressMap = new Map<string, JobRouteProgress>();
  props.jobRouteProgress.forEach((progress) => {
    progressMap.set(progress.pudId, progress);
  });

  // Create temporary segments for timeline calculation
  const tempSegments: TempSegment[] = pudItemsToShow
    .map((pud) => {
      const progress = progressMap.get(pud.pudId);

      if (!progress && !isJobInProgress.value) {
        return null;
      }

      const startTime =
        progress?.actualArrivalTime || progress?.expectedArrivalTime || 0;
      const endTime =
        progress?.actualDepartureTime ||
        startTime + (progress?.actualLoadTime || 0);

      return { startTime, endTime, progress, pud };
    })
    .filter((s): s is TempSegment => s !== null);

  if (tempSegments.length === 0) {
    return [];
  }

  const { jobStartTime, totalTimelineDuration } =
    calculateTimelineRange(tempSegments);

  if (totalTimelineDuration <= 0) {
    return [];
  }

  // Build final segments
  let currentTime = jobStartTime;
  const segments: PudSegment[] = [];

  for (let i = 0; i < pudItemsToShow.length; i++) {
    const pud = pudItemsToShow[i];
    const progress = progressMap.get(pud.pudId);

    // Determine if this is the first leg by checking if it's the first PUD in the original job
    const isFirstLeg = pud.pudId === props.job.pudItems[0]?.pudId;

    const segment = createPudSegment(
      pud,
      progress,
      i,
      currentTime,
      jobStartTime,
      totalTimelineDuration,
      isFirstLeg,
    );
    if (segment) {
      segments.push(segment);
      // Update current time for next segment - ensure it doesn't go backwards
      currentTime = Math.max(currentTime, segment.endTime);
    }
  }
  // Normalize positions to fit within timeline
  normalizeSegmentPositions(segments);
  return segments;
});

/**
 * Determines the lateness status based on variance and progress data
 * Uses system-defined variance thresholds and progress warning flags
 * @param differenceMs - Time difference in milliseconds
 * @param progress - Job route progress data
 * @param type - Type of timing check ('arrival', 'travel', or 'load')
 * @returns Status indicating if the segment is on-time, late, early, or default
 */
function getLatenessStatus(
  differenceMs?: number | null,
  progress?: JobRouteProgress,
  type: string = 'travel',
): LatenessStatus {
  if (differenceMs === null || differenceMs === undefined) {
    return LatenessStatus.LATE;
  }

  // Use JobRouteProgress warning flags when available
  if (progress) {
    if (type === 'arrival' && progress.arrivalDifferenceWarning !== null) {
      return progress.arrivalDifferenceWarning
        ? LatenessStatus.LATE
        : LatenessStatus.EARLY;
    }
    if (type === 'load' && progress.loadTimeWarning !== null) {
      return progress.loadTimeWarning
        ? LatenessStatus.LATE
        : LatenessStatus.EARLY;
    }
    if (type === 'travel' && progress.differenceInTravelTimePercent !== null) {
      // Use the same variance threshold as the rest of the system
      return Math.abs(progress.differenceInTravelTimePercent) >
        ALLOWED_TRAVEL_VARIANCE_PERCENT
        ? LatenessStatus.LATE
        : LatenessStatus.EARLY;
    }
  }

  // Fallback to simple threshold-based approach that matches calculateVarianceData
  // Since we don't have estimated time here, we use the same logic: positive = late, negative = early
  return differenceMs > 0 ? LatenessStatus.LATE : LatenessStatus.EARLY;
}

/**
 * Computed property that returns
 * timeline bounds calculations of job start/end times
 */
const timelineBounds = computed(() => {
  if (pudSegments.value.length === 0) {
    return { jobStartTime: 0, jobEndTime: 0, duration: 0 };
  }
  const jobStartTime = pudSegments.value[0].startTime;
  const jobEndTime = pudSegments.value[pudSegments.value.length - 1].endTime;
  const duration = jobEndTime - jobStartTime;
  return { jobStartTime, jobEndTime, duration };
});

/**
 * Computed property that generates time marks for the timeline scale
 * Creates evenly spaced time labels along the timeline track
 */
const timeMarks = computed((): TimeMark[] => {
  const { jobStartTime, duration } = timelineBounds.value;
  if (duration <= 0) {
    return [];
  }
  const marks: TimeMark[] = [];
  const intervalCount = 8;
  const interval = duration / intervalCount;

  for (let i = 0; i <= intervalCount; i++) {
    const time = jobStartTime + i * interval;
    marks.push({
      time,
      label: moment(time).format('HH:mm'),
      position: (i / intervalCount) * 100,
    });
  }
  return marks;
});

/**
 * Computed property that calculates the current time position on the timeline
 * Used for the progress indicator showing how much of the job has been completed
 */
const currentTimePosition = computed((): number => {
  const { jobStartTime, duration } = timelineBounds.value;
  if (duration <= 0) {
    return 0;
  }
  const nowPosition = ((Date.now() - jobStartTime) / duration) * 100;
  return Math.min(100, nowPosition);
});

/**
 * Computed property that calculates dynamic height for the details panel
 * Adjusts based on the maxHeight prop to ensure proper layout
 */
const detailsPanelHeight = computed((): string => {
  if (typeof props.maxHeight === 'number') {
    // maxHeight is a number (pixels), subtract space for legend and summary
    return `${props.maxHeight - 200}px`; // Approximate space for legend and summary
  }

  // maxHeight is a string (like '70vh'), calculate relative height
  if (typeof props.maxHeight === 'string' && props.maxHeight.includes('vh')) {
    const vhValue = parseFloat(props.maxHeight);
    const calculatedHeight = (vhValue * window.innerHeight) / 100;
    return `${calculatedHeight - 200}px`;
  }

  // Default fallback
  return '400px';
});

/**
 * Checks if a status indicates lateness
 * @param status - Status string to check
 * @returns True if the status is 'late'
 */
function isLate(status: LatenessStatus): boolean {
  return status === LatenessStatus.LATE;
}

// --- BREAK BAR LOGIC --- //
/**
 * Computed property that gets the start time of the timeline
 * Uses the first PUD segment's start time or falls back to first PUD item's epoch time
 */
const timelineStart = computed((): number | null => {
  // If we have PUD segments, use the first PUD's start time
  if (pudSegments.value.length > 0) {
    return timelineBounds.value.jobStartTime;
  }
  // Fallback to first PUD item's epoch time
  return props.job.pudItems[0]?.epochTime || null;
});

/**
 * Computed property that gets the end time of the timeline
 * Uses the last PUD segment's end time or falls back to last PUD item's epoch time
 */
const timelineEnd = computed((): number | null => {
  // If we have PUD segments, use the last PUD's end time
  if (pudSegments.value.length > 0) {
    return timelineBounds.value.jobEndTime;
  }
  // Fallback to last PUD item's epoch time
  return props.job.pudItems[props.job.pudItems.length - 1]?.epochTime || null;
});

/**
 * Computed property that processes break records into visual segments
 * Filters breaks that overlap with the PUD timeline and calculates positioning
 * Creates break segments with proper top/height positioning for the timeline
 */
const breakSegments = computed((): BreakSegment[] => {
  if (
    timelineStart.value === null ||
    timelineEnd.value === null ||
    breakRecords.value.length === 0
  ) {
    return [];
  }

  // Use the PUD timeline range for positioning (from first PUD to last PUD)
  const pudStartTime = timelineStart.value;
  const pudEndTime = timelineEnd.value;
  const totalPudDuration = pudEndTime - pudStartTime;

  const filteredBreaks = breakRecords.value
    .filter((rec) => rec.duration > 0) // Filter out breaks with zero duration
    .filter((rec) => {
      // Only show breaks that overlap with the PUD timeline
      const breakStart = rec.activityTimestamp;
      const breakEnd = rec.activityTimestamp + rec.duration;
      const hasOverlap = breakEnd > pudStartTime && breakStart < pudEndTime;

      return hasOverlap;
    });

  return filteredBreaks.map((rec): BreakSegment => {
    const breakStart = rec.activityTimestamp;
    const breakEnd = rec.activityTimestamp + rec.duration;

    // Calculate the visible portion of the break within the timeline
    const visibleStart = Math.max(breakStart, pudStartTime);
    const visibleEnd = Math.min(breakEnd, pudEndTime);
    const visibleDuration = visibleEnd - visibleStart;

    // Calculate position and height
    const top = ((visibleStart - pudStartTime) / totalPudDuration) * 100;
    const height = (visibleDuration / totalPudDuration) * 100;

    return {
      id: rec.recordId,
      start: breakStart,
      end: breakEnd,
      top: Math.max(0, Math.min(100, top)),
      height: Math.max(0.5, height), // Minimum 0.5% for visibility
      location: rec.locationReadableName,
      startTime: returnFormattedTime(breakStart),
      endTime: returnFormattedTime(breakEnd),
      durationFormatted: returnCorrectDuration(rec.duration),
      visibleStartTime: returnFormattedTime(visibleStart),
      visibleEndTime: returnFormattedTime(visibleEnd),
    };
  });
});

/**
 * Computed property that generates summary table data for the overview panel
 * Creates a table showing arrival/departure times, travel times, and load times
 * Includes variance calculations and error indicators for each PUD item
 */
const summaryTableData = computed((): PudSummary[] => {
  if (!pudSegments.value.length) {
    return [];
  }

  return pudSegments.value.map((segment, index) => {
    const isFirst = index === 0;
    const isLast = index === pudSegments.value.length - 1;

    // Use data already calculated in pudSegments
    const arrival =
      segment.actualArrivalTimeReadable ||
      (segment.scheduledStartTime
        ? returnFormattedTime(segment.scheduledStartTime)
        : 'Pending');

    const departure = segment.actualDepartureTimeReadable || 'Pending';

    // Travel data from segment details - N/A for first leg
    const estimatedTravel = segment.isFirstLeg
      ? 'N/A'
      : returnCorrectDuration(segment.details.transit.estimated);
    const actualTravel = segment.isFirstLeg
      ? 'N/A'
      : segment.status === SegmentStatus.SUCCESS
        ? returnCorrectDuration(segment.details.transit.actual)
        : undefined;

    // Load data from segment details
    const estimatedLoad = returnCorrectDuration(
      segment.details.loading.estimated,
    );
    const actualLoad =
      segment.status === SegmentStatus.SUCCESS
        ? returnCorrectDuration(segment.details.loading.actual)
        : undefined;

    // Travel differences from segment details - N/A for first leg
    const travelDifferenceTime = segment.isFirstLeg
      ? undefined
      : segment.details.transit.variance !== 0
        ? segment.details.transit.varianceMinutes
        : undefined;
    const travelDifferencePercent = segment.isFirstLeg
      ? undefined
      : segment.details.transit.variance !== 0
        ? `${
            segment.details.transit.variance > 0 ? '+' : ''
          }${segment.details.transit.variancePercentage.toFixed(1)}%`
        : undefined;
    const travelIsError = segment.isFirstLeg
      ? false
      : segment.details.transit.isLate;

    // Load differences from segment details
    const loadDifferenceTime =
      segment.details.loading.variance !== 0
        ? segment.details.loading.varianceMinutes
        : undefined;
    const loadDifferencePercent =
      segment.details.loading.variance !== 0
        ? `${
            segment.details.loading.variance > 0 ? '+' : ''
          }${segment.details.loading.variancePercentage.toFixed(1)}%`
        : undefined;
    const loadIsError = segment.details.loading.isLate;

    return {
      id: segment.pudId,
      title: segment.title,
      arrival,
      departure,
      isFirst,
      isLast,
      estimatedTravel,
      actualTravel,
      travelDifferenceTime,
      travelDifferencePercent,
      travelIsError,
      estimatedLoad,
      actualLoad,
      loadDifferenceTime,
      loadDifferencePercent,
      loadIsError,
    };
  });
});

/**
 * Fetches break records for the driver within the job timeline
 * Retrieves work diary records of type BREAK for the specified time range
 */
async function fetchBreakRecords(): Promise<void> {
  if (!props.job?.driverId || !props.job?.pudItems?.length) {
    breakRecords.value = [];
    return;
  }

  const startTime = props.job.pudItems[0].epochTime;
  const endTime = Date.now(); // Current time

  const req = new WorkDiaryRecordHistoryRequest(
    props.job.driverId,
    startTime,
    endTime,
    WorkDiaryActivityType.BREAK,
  );
  const result = await driverDetailsStore.searchWorkDiaryRecords(req);
  breakRecords.value = result || [];
}

// fetch driver breaks
onMounted(fetchBreakRecords);
</script>

<style lang="scss" scoped>
// Timeline segment gradients (with color variables)
$transit-early-gradient: repeating-linear-gradient(
  45deg,
  #4caf50,
  $pickup 6px,
  #4caf50 6px,
  $pickup 12px
);

$transit-late-gradient: repeating-linear-gradient(
  45deg,
  #f44336,
  $error 6px,
  #f44336 6px,
  $error 12px
);

$onsite-early-gradient: repeating-linear-gradient(
  45deg,
  #2196f3,
  $info 6px,
  #2196f3 6px,
  $info 12px
);

$onsite-late-gradient: repeating-linear-gradient(
  45deg,
  #ff9800,
  $primary 6px,
  #ff9800 6px,
  $primary 12px
);

$pending-gradient: repeating-linear-gradient(
  45deg,
  #dbd0d0,
  #ededed 6px,
  #dbd0d0 6px,
  #ededed 12px
);

.job-event-timeline-v2 {
  display: flex;
  flex-direction: column;
  color: var(--text-color);
  padding: 12px 4px 18px 8px;
  border-radius: 8px;
  overflow: hidden;

  .timeline-layout-container {
    display: flex;
    flex-direction: row;
    gap: 12px;
    flex-grow: 1;
    min-height: 0;
  }

  .timeline-track-container {
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    width: 110px; /* Width for time-scale + track */
    position: relative;
  }

  .time-scale {
    position: relative;
    width: 60px;
    margin-right: 22px;
    border-right: 1px solid var(--light-text-color);
    flex-shrink: 0;

    .time-mark {
      position: absolute;
      right: 0;
      transform: translateY(-50%);

      .time-label {
        font-size: 0.85rem;
        color: var(--light-text-color);
        white-space: nowrap;
        font-weight: 500;
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
      }

      .time-line {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 8px;
        height: 2px;
        background: var(--light-text-color);
      }
    }
  }

  .timeline-track {
    position: relative;
    flex-grow: 1;
    background: #2a2a2a;
    border-radius: 4px;
    width: 100%;
    overflow: visible;

    .timeline-progress-background {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      overflow: hidden;

      .progress-fill {
        width: 100%;
        border: 1px solid $translucent;
        transition: height 0.3s ease;
        border-radius: 4px;
      }
    }

    .timeline-segment-container {
      position: absolute;
      left: 0;
      right: 0;
      width: 28px;
      margin: 0 auto;
      cursor: pointer;
      transition: all 0.2s ease-in-out;

      &:hover {
        transform: scale(1.1);
        z-index: 10;
      }

      &.selected .timeline-segment {
        outline: 1px solid white;
        box-shadow: 0 0 15px rgba(158, 224, 255, 0.5);
      }

      .timeline-segment {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
        border-radius: 4px;
        overflow: hidden;

        .sub-segment-container {
          height: 100%;
          width: 100%;
        }

        .late-segment-wrapper {
          height: 100%;
          width: 100%;
          display: flex;
          flex-direction: column;
        }

        .sub-segment {
          width: 100%;
          transition: all 0.15s;

          &.transit-early {
            background: $transit-early-gradient;
          }
          &.transit-late {
            background: $transit-late-gradient;
          }
          &.onsite-early {
            background: $onsite-early-gradient;
          }
          &.onsite-late {
            background: $onsite-late-gradient;
          }
          &.pending {
            background: $pending-gradient;
          }
          &.transit-transparent {
            background: transparent;
            border: 1px dashed rgba(255, 255, 255, 0.3);
          }
        }
      }

      // Add a gap/divider between segments
      &::after {
        content: '';
        display: block;
        position: absolute;
        left: 50%;
        bottom: -4px;
        transform: translateX(-50%);
        width: 28px;
        height: 8px;
        border-bottom: 2px dashed var(--text-color);
        // opacity: 0;
        z-index: 2;
        pointer-events: none;
      }

      // Hide divider for last segment
      &:last-child::after {
        display: none;
      }

      // Add extra spacing between segments for clarity
      padding-bottom: 2px;
    }

    .break-bar {
      position: absolute;
      left: -18px; // right of pud segments
      top: 0;
      width: 12px;
      height: 100%;
      z-index: 2;
      display: flex;
      flex-direction: column;
      pointer-events: none;
      // allow tooltips
      .break-segment {
        position: absolute;
        left: 0;
        width: 100%;
        pointer-events: auto;
        transition: all 0.2s ease-in-out;

        &:hover {
          cursor: pointer;
          transform: scale(1.1);
          z-index: 10;
        }

        .break-segment-inner {
          width: 100%;
          height: 100%;
          background: #52504e;
          border-radius: 3px;
          border: 1px solid #888;
        }
      }
    }
  }

  .details-panel-container {
    flex-grow: 1;
    overflow: hidden;
    background-color: var(--background-color-300);
    border-radius: 8px;
    padding: 12px 4px;
    min-height: 0;

    .details-card {
      background-color: transparent !important;
      color: var(--text-color);
      height: 100%;
      display: flex;
      flex-direction: column;

      .details-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 0 1rem 0;
        border-bottom: 1px solid var(--border-color);
        flex-shrink: 0;

        .header-left {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .details-title {
          font-weight: 600;
          font-size: 1.1rem;
        }
        .details-subtitle {
          font-size: 0.8rem;
          color: var(--light-text-color);
        }
      }

      .details-content {
        flex-grow: 1;
        overflow-y: auto;
        overflow-x: hidden;

        .segment-summary-cards {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 18px;
          margin-bottom: 12px;

          .summary-card {
            background-color: var(--background-color-300);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s ease;

            &.status-late {
              border-color: $error;
            }
            &.status-early {
              border-color: $success;
            }
            &.status-on-time {
              border-color: $success;
            }
            &.status-pending {
              border-color: #9e9e9e;
              opacity: 0.7;
            }

            .icon {
              font-size: 1.5rem;
              color: var(--light-text-color);
            }

            .card-content {
              .value {
                font-size: 1.1rem;
                font-weight: 600;
                color: var(--text-color);
                .expected-time-inline {
                  font-size: inherit;
                  font-weight: inherit;
                  color: var(--light-text-color);
                }
              }
              .label {
                font-size: 0.75rem;
                color: var(--text-color);
                text-transform: uppercase;
                letter-spacing: 0.5px;
              }
            }
          }
        }
        // New table styles for details
        .details-table-container {
          .details-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;

            thead {
              th {
                padding: 6px;
                text-align: left;
                font-weight: 600;
                color: var(--light-text-color);
                border-bottom: 2px solid var(--border-color);
                vertical-align: top;
              }
            }

            tbody {
              tr {
                border-bottom: 1px solid var(--border-color);

                &:last-child {
                  border-bottom: none;
                }

                td {
                  padding: 12px 8px;
                  vertical-align: top;
                  word-wrap: break-word;
                }
              }
            }

            // Column widths
            th:nth-child(1),
            td:nth-child(1) {
              width: 40%; // Section header column
            }

            th:nth-child(2),
            td:nth-child(2),
            th:nth-child(3),
            td:nth-child(3) {
              width: 20%; // Estimated and Actual columns
              text-align: center;
            }

            th:nth-child(4),
            td:nth-child(4) {
              width: 20%; // Time variance column
              text-align: center;
            }

            // Section header styling within table
            .section-header {
              font-weight: 500;
              font-size: 0.85rem;
              color: var(--text-color);
              display: flex;
              align-items: center;
              gap: 6px;
              margin: 0;
              padding: 0;

              .v-icon {
                color: var(--light-text-color);
                flex-shrink: 0;
              }

              .time-brackets {
                color: var(--light-text-color);
                font-weight: 400;
              }
            }
          }
        }

        // Time range container styles
        .time-range-container {
          display: flex;
          align-items: center;
          gap: 22px;
          padding: 16px 0;
          border-bottom: 1px solid var(--border-color);
          margin-bottom: 12px;
          margin-right: 24px;

          .time-range-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            min-width: 0;
            flex: 1;

            .time-label {
              font-size: 1rem;
              color: var(--light-text-color);
              font-weight: 500;
              margin-bottom: 4px;
              white-space: nowrap;
            }

            .time-value {
              font-size: 1.2rem;
              font-weight: 600;
              color: var(--text-color);
              white-space: nowrap;
            }
          }

          .time-arrow {
            color: var(--light-text-color);
            font-size: 1.5rem;
            flex-shrink: 0;
          }
        }

        .details-section {
          margin-top: 12px;

          .section-header {
            font-weight: 500;
            font-size: 12px;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            color: var(--text-color);
            padding-bottom: 2px;
            border-bottom: 1px solid var(--border-color);

            .v-icon {
              color: var(--light-text-color);
            }
          }
        }
      }
    }

    .no-selection-placeholder {
      display: flex;
      flex-direction: column;
      height: 100%;
      padding: 12px;

      .header-txt {
        color: var(--text-color);
        font-size: $font-size-22;

        .hand-point-icon {
          transform: rotate(270deg);
          font-size: inherit;
          margin-right: 4px;
        }
      }
    }
  }
}

.timeline-summary {
  margin-bottom: 16px;

  .summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;

    .stat-item {
      background: var(--table-bg-200);
      border: 1px solid var(--border-color);
      border-radius: 6px;
      padding: 12px;
      text-align: center;

      .stat-value {
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--text-color);
        margin-bottom: 4px;
      }
      .stat-label {
        font-size: 0.75rem;
        color: var(--light-text-color);
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }
}
.timeline-tooltip {
  min-width: max-content;
  z-index: 99999 !important;
}

// Timeline legend styles
.timeline-legend {
  display: flex;
  gap: 28px;
  margin-bottom: 8px;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding-bottom: 4px;
  justify-content: flex-start;

  .legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85rem;
    color: var(--text-color);
    white-space: nowrap;
    flex-shrink: 0;
    position: relative;
  }

  .legend-item:not(:first-of-type)::before {
    content: '';
    display: block;
    position: absolute;
    left: -12px;
    top: 20%;
    height: 60%;
    width: 1px;
    background: #bdbdbd;
    opacity: 0.7;
  }

  .legend-title {
    font-weight: 600;
    color: var(--text-color);
    white-space: nowrap;
  }

  .legend-color {
    display: inline-block;
    width: 16px;
    height: 10px;
    border-radius: 2px;
  }

  .transit-early {
    background: $transit-early-gradient;
  }

  .transit-late {
    background: $transit-late-gradient;
  }

  .onsite-early {
    background: $onsite-early-gradient;
  }

  .onsite-late {
    background: $onsite-late-gradient;
  }

  .break {
    background: #52504e;
    border: 1px solid #888;
  }

  .pending {
    background: $pending-gradient;
  }
}

.close-details-btn {
  flex-shrink: 0;
  transition: scale 0.2s;
  &:hover {
    scale: 1.1;
  }
}

// In-Progress Pulsing Line styles
.in-progress-pulse-line {
  position: absolute;
  pointer-events: none;
  transform: translateX(-30%);

  .pulse-line {
    position: relative;
    height: 2px;
    bottom: 12px;
    background: linear-gradient(
      90deg,
      transparent,
      $warning,
      $warning-type,
      transparent
    );
    box-shadow: 0 0 8px rgba(255, 217, 0, 1);
    animation: pulseLine 2s ease-in-out infinite;
    border-radius: 2px;
  }

  .pulse-content {
    position: relative;
    bottom: 11px;
    color: $warning;
    padding: 4px 14px 4px 28px;
    border-radius: 0 20px 20px 0;
    font-size: $font-size-14;
    font-weight: 700;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(28px);
    border: 0.5px solid $translucent;
    animation: pulseContent 3s ease-in-out infinite;
  }
}

.column-header {
  font-size: $font-size-11;
  opacity: 0.7;
}

.table-container {
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 4px;

  .simple-data-table {
    width: 100%;
    border-collapse: collapse;

    thead {
      position: sticky;
      top: 0;
      background-color: var(--background-color-300);
      z-index: 1;

      th {
        padding: 8px;
        text-align: left;
        border-bottom: 1px solid var(--border-color);
      }
    }

    tbody {
      tr {
        border-bottom: 1px solid var(--border-color);

        &:last-child {
          border-bottom: none;
        }

        td {
          padding: 8px;
        }
      }
    }
  }
}

.eventtime-card {
  opacity: 0.8;
  .eventtime-card__value {
    color: var(--light-text-color);
    font-size: $font-size-11;
    &.highlighted {
      color: black;
      background-color: var(--primary-light);
      padding: 2px 6px;
      font-size: $font-size-12;
      border-radius: $border-radius-base;
      font-weight: 700;
    }
  }
  .eventtime-card__diff {
    color: inherit;
    font-weight: normal;
  }
  .eventtime-card__diff--error {
    color: $error;
    font-weight: bold;
  }
}

// Pulse animations
@keyframes pulseLine {
  0%,
  100% {
    opacity: 0.6;
    box-shadow: 0 0 8px rgba(255, 0, 0, 0.2);
    transform: scaleY(1);
  }
  50% {
    opacity: 1;
    box-shadow: 0 0 12px rgb(255, 221, 0);
    transform: scaleY(1.1);
  }
}

@keyframes pulseContent {
  0%,
  100% {
    opacity: 0.9;
    transform: translateY(0);
  }
  50% {
    opacity: 1;
    transform: translateY(-2px);
  }
}

.legend-info-icon {
  cursor: pointer;
  color: var(--text-color);
  vertical-align: middle;
  margin-left: 8px;
}

// Custom tooltip styles to match segment colors
:deep(.transit-tooltip) {
  &.transit-early-tooltip {
    background: #4caf50 !important;
    color: white !important;
    border: 1px solid $success !important;
  }

  &.transit-late-tooltip {
    background: #f44336 !important;
    color: white !important;
    border: 1px solid #d32f2f !important;
  }
}

:deep(.onsite-tooltip) {
  &.onsite-early-tooltip {
    background: $info !important;
    color: white !important;
    border: 1px solid #0a3d8f !important;
  }

  &.onsite-late-tooltip {
    background: #f57c00 !important;
    color: white !important;
    border: 1px solid $primary !important;
  }
}

:deep(.pending-tooltip) {
  background: #dbd0d0 !important;
  color: #333 !important;
  border: 1px solid #c0c0c0 !important;
}

.tag-accent-card {
  border-radius: $border-radius-base;
  font-size: $font-size-12;
  padding: 4px 8px;
  font-weight: 600;
  letter-spacing: 0.2px;
  display: inline-block;
  position: relative;
  margin: 0 auto;

  &.green-outline {
    color: var(--bg-light-green);
    background-color: $toast-success-text;
  }

  &.red-outline {
    color: var(--bg-light-red);
    background-color: $toast-error-text;
  }
}
</style>
