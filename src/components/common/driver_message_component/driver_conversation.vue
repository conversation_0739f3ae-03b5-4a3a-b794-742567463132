<template>
  <section class="driver-conversation">
    <div class="table-content restrict-height">
      <v-layout
        class="table-scrollable"
        v-if="isAwaitingResponse"
        justify-center
        align-center
        fill-height
      >
        <img
          src="@/static/loader/infinity-loader-light.svg"
          height="80px"
          width="80px"
        />
      </v-layout>
      <div
        class="table-scrollable"
        ref="messageContentsDiv"
        v-if="!isAwaitingResponse && messageList.length"
      >
        <div class="message-contents" v-if="messageList.length">
          <v-layout v-for="(item, index) in messageList" :key="item._id" row>
            <v-layout
              column
              class="message-bubble-container"
              :class="[item.isOperationsMessage ? 'operations' : 'driver']"
            >
              <v-layout
                v-if="differentSenderFromPrevious(index)"
                pb-1
                align-center
                class="message-details-row"
              >
                <span class="username"> {{ item.senderName }} </span>
                <span class="datetime">
                  {{ returnFormattedTime(item.timestamp, `DD/MM/YY`) }}
                </span>
              </v-layout>
              <div
                class="message-bubble"
                :class="{
                  pinned: item.actionRequired,
                  'new-message': item.isNewChat,
                }"
              >
                <span> {{ item.content }} </span
                ><span class="message-bubble-time">
                  {{ returnFormattedTime(item.timestamp, `HH:mm`) }}</span
                >
                <div class="message-menu">
                  <v-layout class="message-menu-row" align-center>
                    <div>
                      <v-menu
                        :right="!item.isOperationsMessage"
                        :left="item.isOperationsMessage"
                      >
                        <template v-slot:activator="{ on: menu }">
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on: tooltip }">
                              <v-btn
                                flat
                                icon
                                v-on="{ ...tooltip, ...menu }"
                                class="ma-0"
                              >
                                <v-icon size="18">far fa-ellipsis-v </v-icon>
                              </v-btn>
                            </template>
                            <span>View Additional Actions</span>
                          </v-tooltip>
                        </template>
                        <v-list dense class="v-list-custom">
                          <v-list-tile @click="copyMessage(item.content)">
                            <v-list-tile-avatar>
                              <v-icon size="16">far fa-copy</v-icon>
                            </v-list-tile-avatar>
                            <v-list-tile-content>
                              <v-list-tile-title
                                >Copy Message</v-list-tile-title
                              >
                            </v-list-tile-content>
                          </v-list-tile>
                          <v-list-tile @click="changeActiveState(item)">
                            <v-list-tile-avatar>
                              <v-icon size="16">fal fa-thumbtack</v-icon>
                            </v-list-tile-avatar>
                            <v-list-tile-content>
                              <v-list-tile-title v-if="!item.actionRequired"
                                >Pin Message</v-list-tile-title
                              >
                              <v-list-tile-title v-else
                                >Unpin Message</v-list-tile-title
                              >
                            </v-list-tile-content>
                          </v-list-tile>
                          <v-list-tile @click="linkOrUnlinkMessage(item)">
                            <v-list-tile-avatar>
                              <v-icon size="16">fal fa-link</v-icon>
                            </v-list-tile-avatar>
                            <v-list-tile-content>
                              <v-list-tile-title v-if="!item.jobId"
                                >Link with Job</v-list-tile-title
                              >
                              <v-list-tile-title v-else
                                >Unlink from Job #{{
                                  recurringJobIdMap?.get(item.jobId)
                                    ? recurringJobIdMap.get(item.jobId)
                                    : item.jobId
                                      ? item.jobId
                                      : '--'
                                }}</v-list-tile-title
                              >
                            </v-list-tile-content>
                          </v-list-tile>
                        </v-list>
                      </v-menu>
                    </div>

                    <span v-if="item.jobId" class="linked-jobid"
                      >#{{
                        recurringJobIdMap?.get(item.jobId)
                          ? recurringJobIdMap.get(item.jobId)
                          : item.jobId
                            ? item.jobId
                            : '--'
                      }}</span
                    >
                    <span class="date-time"> {{ item.readableDateTime }} </span>
                  </v-layout>
                </div>
              </div>
            </v-layout>
          </v-layout>
        </div>
      </div>
      <v-layout
        class="table-scrollable"
        v-else
        column
        justify-center
        align-center
        fill-height
      >
        <h5 class="subheader--bold">No recent messages found</h5>
        <span class="no-content-message">
          To view older messages,
          <span
            class="is-link"
            v-if="canJumpToHistory"
            @click="jumpToHistoryComponent"
            >click here</span
          >
          <span v-else>please go to Driver Message History.</span>
        </span>
      </v-layout>
    </div>
    <div>
      <DriverConversationTextfield
        :messageList="messageList"
        :driverId="driverId"
        :jobId="jobId"
      ></DriverConversationTextfield>
    </div>
    <ContentDialog
      v-if="showLinkJobDialog"
      :showDialog.sync="showLinkJobDialog"
      title="Select a job to link this message with"
      width="50%"
      contentPadding="pa-3"
      :isDisabled="!linkChatMessageToJobId"
      :isLoading="awaitingLinkMessageResponse"
      @cancel="closeLinkChatMessageDialog"
      @confirm="linkMessageWithJob"
    >
      <v-layout>
        <v-flex md12>
          <table class="simple-data-table">
            <thead>
              <tr>
                <th>Date</th>
                <th>Job #</th>
                <th>Client</th>
                <th>Fleet #</th>
                <th>Driver</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="job in associatedJobList"
                :key="job.jobId"
                class="row-selectable"
                :class="{
                  'row-selected': linkChatMessageToJobId === job.jobId,
                }"
                @click="linkChatMessageToJobId = job.jobId ?? null"
              >
                <td>{{ job.readableJobDate }}</td>
                <td>{{ job.displayId }}</td>
                <td>{{ job.clientName }}</td>
                <td>{{ job.csrAssignedId }}</td>
                <td>{{ job.driverName }}</td>
                <td>{{ job.status }}</td>
              </tr>
            </tbody>
          </table>
        </v-flex>
      </v-layout>
    </ContentDialog>
  </section>
</template>

<script setup lang="ts">
import DriverConversationTextfield from '@/components/common/driver_conversation/driver_conversation_textfield/index.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { handleChatMessage } from '@/helpers/ChatMessageHelpers/ChatMessageHelpers';
import {
  returnEndOfDayFromEpoch,
  returnFormattedTime,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import ChatMessage from '@/interface-models/Generic/ChatConversation/ChatMessage';
import ChatHistoryRequest from '@/interface-models/Generic/ChatConversation/RequestChatHistory';
import { BulkChatMessageResponse } from '@/interface-models/Generic/ChatConversation/SendBulkChatMessageRequest';
import { OperationJobSummary } from '@/interface-models/Jobs/OperationJobSummary';
import { useDriverMessageStore } from '@/store/modules/DriverMessageStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useRecurringJobStore } from '@/store/modules/RecurringJobStore';
import { useRootStore } from '@/store/modules/RootStore';
import Mitt from '@/utils/mitt';
import { useMittListener } from '@/utils/useMittListener';
import moment from 'moment-timezone';
import {
  computed,
  ComputedRef,
  nextTick,
  onBeforeUnmount,
  onMounted,
  ref,
  Ref,
} from 'vue';

const props = defineProps<{
  driverId: string;
  jobId?: number | null;
}>();

const emit = defineEmits<{
  (event: 'jumpToHistoryComponent'): void;
}>();

const driverMessageStore = useDriverMessageStore();

const messageList: Ref<ChatMessage[]> = ref([]);
const isAwaitingResponse: Ref<boolean> = ref(false);

const showLinkJobDialog: Ref<boolean> = ref(false);
const linkChatMessageId: Ref<string | null> = ref(null);
const linkChatMessageToJobId: Ref<number | null> = ref(null);
const awaitingLinkMessageResponse: Ref<boolean> = ref(false);

const canJumpToHistory: Ref<boolean> = ref(false);

const messageContentsDiv: Ref<any> = ref(null);

let scrollTimeout: ReturnType<typeof setTimeout> | undefined;

/**
 * Returns the recurring job id map from the store.
 */
const recurringJobIdMap: ComputedRef<Map<number, string | null> | undefined> =
  computed(() => {
    if (!useRootStore().operationsPortalLoadedData.JOBS) {
      return undefined;
    }
    return useRecurringJobStore().recurringJobIdMap;
  });

/**
 * Emits to the parent component, which will jump to the Chat Message History component.
 */
function jumpToHistoryComponent(): void {
  emit('jumpToHistoryComponent');
}

/**
 * Handles the chat message API response for a specific driver ID.
 * @param messageListParam - The list of chat messages.
 */
function handleChatMessagesForDriverId(messageListParam: ChatMessage[]): void {
  messageList.value = messageListParam.map((m) =>
    Object.assign(new ChatMessage(), { ...m, isNewChat: false }),
  );
  scrollToBottom();
}

/**
 * Returns an array of OperationJobSummary objects associated with the driver.
 * If the driverId or showLinkJobDialog is falsy, an empty array is returned.
 * @returns {OperationJobSummary[]} The associated job list.
 */
const associatedJobList: ComputedRef<OperationJobSummary[]> = computed(() => {
  if (!props.driverId || !showLinkJobDialog.value) {
    return [];
  }
  return useJobStore().operationJobsList.filter(
    (job: OperationJobSummary) => job.driverId === props.driverId,
  );
});

/**
 * Checks if the sender of the current message is different from the previous message.
 * @param {number} index - The index of the current message in the message list.
 * @returns {boolean} - Returns true if the sender is different or if it's the first message.
 */
function differentSenderFromPrevious(index: number): boolean {
  if (
    index === 0 ||
    messageList.value[index].senderId !== messageList.value[index - 1].senderId
  ) {
    return true;
  }
  return false;
}

/**
 * Toggles the active state of a chat message and sends message request to update in database.
 * @param {ChatMessage} message - The chat message to change the active state of.
 */
function changeActiveState(message: ChatMessage): void {
  if (!message.actionRequired) {
    message.actionRequired = true;
    message.isActioned = false;
  } else {
    message.actionRequired = false;
    message.isActioned = true;
  }
  driverMessageStore.updateChatMessage(message);
}

/**
 * Links or unlinks a chat message to a job. Opens the link dialog if linking.
 * @param {ChatMessage} chatMessage - The chat message to link or unlink.
 */
function linkOrUnlinkMessage(chatMessage: ChatMessage): void {
  if (!!chatMessage.jobId) {
    driverMessageStore.removeChatMessageFromJob(chatMessage);
  } else {
    showLinkChatMessageDialog(chatMessage);
  }
}

/**
 * Shows the link chat message dialog for the selected message.
 * @param {ChatMessage} chatMessage - The chat message to show the dialog for.
 */
function showLinkChatMessageDialog(chatMessage: ChatMessage): void {
  linkChatMessageId.value = chatMessage._id;
  linkChatMessageToJobId.value = null;
  showLinkJobDialog.value = true;
}

/**
 * Closes the link chat message dialog and resets local variables.
 */
function closeLinkChatMessageDialog(): void {
  linkChatMessageId.value = null;
  linkChatMessageToJobId.value = null;
  showLinkJobDialog.value = false;
  awaitingLinkMessageResponse.value = false;
}

/**
 * Links a chat message with a job.
 */
async function linkMessageWithJob(): Promise<void> {
  if (!linkChatMessageId.value || !linkChatMessageToJobId.value) {
    return;
  }
  const foundChatMessage = messageList.value.find(
    (c) => c._id === linkChatMessageId.value,
  );
  if (foundChatMessage) {
    awaitingLinkMessageResponse.value = true;
    const result = await driverMessageStore.addChatMessageToJob(
      foundChatMessage,
      linkChatMessageToJobId.value,
    );
    if (!result) {
      showNotification(
        'Something went wrong. The message could not be linked to the job. Please try again later.',
      );
    }
    closeLinkChatMessageDialog();
  }
}

/**
 * Requests the chat history for the driver.
 */
async function requestChatHistory(): Promise<void> {
  const chatHistoryRequest: ChatHistoryRequest = {
    driverId: props.driverId,
    startEpoch: returnStartOfDayFromEpoch(
      moment().subtract(7, 'days').valueOf(),
    ),
    endEpoch: returnEndOfDayFromEpoch(),
  };
  isAwaitingResponse.value = true;
  const results =
    await useDriverMessageStore().requestChatMessagesForDriverId(
      chatHistoryRequest,
    );

  if (results) {
    handleChatMessagesForDriverId(results);
  } else {
    showNotification(
      'Something went wrong. The messages could not be received. Please try again soon.',
    );
    messageList.value = [];
  }
  isAwaitingResponse.value = false;
}

/**
 * Scrolls the conversation to the bottom. Called on receipt of a new chat
 * message for the current conversation.
 */
function scrollToBottom(): void {
  if (scrollTimeout) {
    clearTimeout(scrollTimeout);
  }
  scrollTimeout = setTimeout(() => {
    nextTick(() => {
      const container = messageContentsDiv.value as HTMLElement;
      if (container) {
        container.scrollTo({
          top: container.scrollHeight,
          behavior: 'smooth',
        });
      }
    });
  }, 50);
}

/**
 * Handles the received chat message. Used for new messages from drivers and
 * operations, email updates, and updates to job associations.
 * @param payload The chat message payload.
 */
function handleReceivedChatMessage(payload: ChatMessage | null): void {
  if (
    payload &&
    (payload.senderId === props.driverId ||
      payload.receiverId === props.driverId)
  ) {
    const chatMessage: ChatMessage = handleChatMessage(payload);
    const foundIndex = messageList.value.findIndex(
      (m) => m._id === chatMessage._id,
    );
    if (foundIndex !== -1) {
      messageList.value.splice(foundIndex, 1, chatMessage);
    } else {
      messageList.value.push(chatMessage);
      scrollToBottom();
      setTimeout(() => {
        chatMessage.isNewChat = false;
      }, 2000);
    }
  }
}

/**
 * Mitt listener for all incoming chat messages and chat message updates.
 */
function handleBulkMessageResponse(
  response: BulkChatMessageResponse | null,
): void {
  if (!response?.chatMessages) {
    return;
  }
  response.chatMessages.forEach((message: ChatMessage) => {
    if (message.receiverId === props.driverId) {
      handleReceivedChatMessage(message);
    }
  });
}

/**
 * Copies text from a message bubble to the clipboard.
 * @param text - The text to copy.
 */
function copyMessage(text: string): void {
  navigator.clipboard.writeText(text).then(() => {
    console.log('Message copied to clipboard!');
  });
}

// Mitt listeners for incoming chat message updates
useMittListener('receivedMessage', handleReceivedChatMessage);
useMittListener('publishedMessage', handleReceivedChatMessage);
useMittListener('updatedMessage', handleReceivedChatMessage);
useMittListener('removedChatMessage', handleReceivedChatMessage);
useMittListener('addedChatMessage', handleReceivedChatMessage);
useMittListener('receivedBulkChatMessageResponse', handleBulkMessageResponse);

onMounted(() => {
  requestChatHistory();
  // If parent is listening for jumpToHistoryComponent, enable the link
  canJumpToHistory.value = true;
  scrollToBottom();
});

onBeforeUnmount(() => {
  if (scrollTimeout) {
    clearTimeout(scrollTimeout);
  }
});
</script>

<style scoped lang="scss">
.driver-conversation {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .table-content {
    overflow: hidden;
    position: relative;

    &.restrict-height {
      height: calc(100% - 48px);
      max-height: calc(100% - 48px);
    }

    .table-scrollable {
      max-height: 100%;
      height: 100%;
      flex-direction: column;
      position: relative;
      overflow-y: auto;
      display: flex;
      padding-bottom: 20px;
      padding-top: 10px;

      .no-content-message {
        color: rgb(178, 178, 178);
        font-size: $font-size-medium;
        .is-link {
          color: $highlight;
          font-weight: 600;
          &:hover {
            cursor: pointer;
            text-decoration: underline;
          }
        }
      }
    }
  }

  .message-contents {
    padding: 8px 12px;

    .message-bubble-container {
      .message-details-row {
        .username {
          padding: 0px 6px;
          font-family: $sub-font-family;
          font-size: $font-size-12;
          text-transform: uppercase;
          font-weight: 600;
        }
        .datetime {
          font-size: $font-size-12;
          color: rgb(195, 195, 195);
        }
      }

      &.operations {
        align-items: flex-end;
        .message-details-row {
          flex-direction: row-reverse;
        }
        .message-bubble {
          background-color: rgb(35, 64, 94);
          // padding: 6px 12px;

          .message-menu {
            right: 100%;
            .message-menu-row {
              flex-direction: row-reverse;
            }
          }
        }
      }
      @keyframes highlight {
        from {
          border-color: rgb(196, 216, 241);
        }
        to {
          border-color: normal;
        }
      }

      &.driver {
        align-items: start;
        .message-bubble {
          background-color: rgb(72, 71, 80);

          &.new-message {
            animation-name: highlight;
            animation-duration: 2s; /* adjust as needed */
          }
          .message-menu {
            left: 100%;
          }
        }
      }

      .message-bubble {
        padding: 8px 10px;
        margin-top: 2px;
        border-radius: 12px 2px 12px 12px;
        display: inline-block;
        max-width: 73%;
        word-wrap: break-word;
        white-space: pre-wrap;

        .message-bubble-time {
          font-size: 10px;
          opacity: 0.5;
          margin-left: 4px;
          vertical-align: bottom;
          white-space: nowrap;
          padding-left: 4px;
        }

        &.pinned {
          border-color: rgb(255, 166, 0);
        }

        .message-menu {
          position: absolute;
          display: none;
          top: 0px;
          .linked-jobid {
            background-color: rgba(72, 71, 80, 0.628);
            border: 1px solid rgb(72, 71, 80);
            // font-size: $font-size-12;
            font-style: italic;
            padding: 1px 8px;
            border-radius: 3px;
            font-family: $sub-font-family;
          }
          .date-time {
            white-space: nowrap;
            color: rgb(109, 106, 123);
            padding: 0px 8px;
            font-size: 0.85em;
          }
        }

        &:hover {
          cursor: pointer;
          filter: brightness(110%);
          .message-menu {
            display: unset;
          }
        }
      }
    }
  }

  .chat-text-field-input-container {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
  }
}
</style>
