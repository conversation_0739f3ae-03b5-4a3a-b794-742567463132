<template>
  <div class="driver-message-table">
    <table cellpadding="0" cellspacing="0" v-if="!isLoadingTableData">
      <thead v-if="$vuetify.breakpoint.lgAndUp">
        <tr>
          <th>Vehicle</th>
          <th>Driver</th>
          <th>Prev. Job</th>
          <th>Duration Clear</th>
          <th>Location</th>
        </tr>
      </thead>
      <tbody v-for="section in tableData" :key="section.type">
        <tr @click="toggleExpansion(section.type)">
          <td colspan="5" class="service-name">
            <v-layout align-center>
              <p>{{ section.name }}</p>
              <v-spacer></v-spacer>
              <v-icon size="10" color="white">{{
                expandedGroups.includes(section.type)
                  ? 'far fa-chevron-up'
                  : 'far fa-chevron-down'
              }}</v-icon>
            </v-layout>
          </td>
        </tr>
        <template v-if="expandedGroups.includes(section.type)">
          <tr
            v-for="(item, index) in section.listItems"
            :key="item.fleetAssetId || `${index}`"
            :tabindex="index + 1"
            class="chat-message"
            :class="[false ? 'selected' : '']"
            @click="handleRowSelection(item)"
          >
            <td class="text-xs-left">
              {{ item.csrAssignedId }}
            </td>
            <td class="text-xs-left">
              {{ item.driverName || '-' }}
            </td>
            <td class="text-xs-left">
              {{ item.clearedJobDisplayId || '-' }}
            </td>
            <td class="text-xs-left">
              {{ item.readableDurationClear || '-' }}
            </td>
            <td class="text-xs-left">
              {{ item.locationClear }}
            </td>
          </tr>
        </template>
      </tbody>
    </table>

    <v-layout justify-center pa-3 v-else>
      <img
        src="@/static/loader/infinity-loader-light.svg"
        height="80px"
        width="80px"
      />
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import {
  isDriverValidForChannel,
  isFleetAssetValidForChannel,
} from '@/helpers/AppLayoutHelpers/OperationsChannelHelpers';
import {
  returnStartOfDayFromEpoch,
  returnTimeNow,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  generateVacantTruckTableData,
  VacantTruckGroup,
  VacantTruckGroupType,
  VacantTruckSummary,
} from '@/helpers/JobProgressAlertHelpers/VacantTruckHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import { DriverNotificationType } from '@/interface-models/Generic/DriverLicenseTypes/DriverNotificationType';
import { DriverAppNotification } from '@/interface-models/Generic/DriverOnlineStatus/DriverAppNotification';
import { JobEventSummary } from '@/interface-models/Jobs/JobEventSummary';
import JobProgressAlert from '@/interface-models/Jobs/JobProgressAlert/JobProgressAlert';
import JobProgressAlertHistoryRequest from '@/interface-models/Jobs/JobProgressAlert/JobProgressAlertHistoryRequest';
import { JobProgressAlertType } from '@/interface-models/Jobs/JobProgressAlert/JobProgressAlertType';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { OperationsChannel } from '@/interface-models/OperationsChannels/OperationsChannel';
import { useDriverAppStore } from '@/store/modules/DriverAppStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useDriverMessageStore } from '@/store/modules/DriverMessageStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useFilterStore } from '@/store/modules/OperationsFilterStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import Mitt from '@/utils/mitt';
import { useMittListener } from '@/utils/useMittListener';
import moment from 'moment-timezone';
import { v4 as uuidv4 } from 'uuid';
import {
  computed,
  ComputedRef,
  onMounted,
  onUnmounted,
  Ref,
  ref,
  watch,
  WritableComputedRef,
} from 'vue';

const driverMessageStore = useDriverMessageStore();
const operationsStore = useOperationsStore();

const isLoadingTableData: Ref<boolean> = ref(false);
const tableData: Ref<VacantTruckGroup[]> = ref([]);

const jobProgressAlertList: Ref<JobProgressAlert[]> = ref([]);
const driverAppNotificationMap: Ref<Map<string, DriverAppNotification>> = ref(
  new Map(),
);

let refreshIntervalId: number | undefined;
let refreshTimeout: ReturnType<typeof setTimeout>;

const expandedGroups: WritableComputedRef<VacantTruckGroupType[]> = computed({
  get(): VacantTruckGroupType[] {
    return driverMessageStore.expandedVacantTruckGroups;
  },
  set(value: VacantTruckGroupType[]): void {
    driverMessageStore.setExpandedVacantTruckGroups(value);
  },
});

/**
 * Toggles the expansion state of a vacant truck group.
 * @param type The type of the vacant truck group to toggle.
 */
function toggleExpansion(type: VacantTruckGroupType) {
  if (expandedGroups.value.includes(type)) {
    expandedGroups.value = expandedGroups.value.filter((t) => t !== type);
  } else {
    expandedGroups.value = [...expandedGroups.value, type];
  }
}

/**
 * Refreshes the table data by generating vacant truck table rows
 * using the latest fleet assets, driver list, job progress alerts,
 * operation jobs, and driver app notifications.
 */
function refreshTableData() {
  const tableRows = generateVacantTruckTableData({
    allFleetAssets: validFleetAssets.value,
    allDrivers: validDrivers.value,
    jobProgressAlerts: jobProgressAlertList.value,
    jobList: useJobStore().operationJobsList,
    onlineEventsMap: driverAppNotificationMap.value,
  });

  tableData.value = tableRows;
}

/**
 * Returns the list of valid drivers for the selected operations channel.
 * Filters drivers by active status and channel validity.
 */
const validDrivers: ComputedRef<DriverDetailsSummary[]> = computed(() => {
  const allDrivers = useDriverDetailsStore().getDriverList;
  return allDrivers.filter(
    (driver) =>
      driver.isActive &&
      isDriverValidForChannel(
        driver,
        useFleetAssetStore().getAllFleetAssetList,
        useFleetAssetOwnerStore().ownerAffiliationMap,
        useFilterStore().selectedOperationsChannel,
      ),
  );
});

/**
 * Returns the list of valid fleet assets (trucks) for the selected operations channel.
 * Filters assets by truck type, allocation status, hire status, channel validity,
 * and association with a valid driver.
 */
const validFleetAssets: ComputedRef<FleetAssetSummary[]> = computed(() => {
  const allAssets = useFleetAssetStore().getAllFleetAssetList;
  return allAssets.filter((asset) => {
    const isTruck = asset.isTruck;
    const isActive = asset.isActiveForAllocation;
    const isNotOutsideHire = !asset.outsideHire;
    const isValidForChannel = isFleetAssetValidForChannel(
      asset,
      useFleetAssetOwnerStore().ownerAffiliationMap,
      useFilterStore().selectedOperationsChannel,
    );
    const hasValidDriver = validDrivers.value.some((driver) =>
      asset.associatedDrivers.includes(driver.driverId),
    );

    return (
      isTruck &&
      isActive &&
      isNotOutsideHire &&
      isValidForChannel &&
      hasValidDriver
    );
  });
});

const selectedOperationsChannel: ComputedRef<OperationsChannel | null> =
  computed(() => {
    return useFilterStore().selectedOperationsChannel;
  });

// Watch selectedOperationsChannel._id and perform some action
watch(
  () => selectedOperationsChannel.value?._id,
  (newId) => {
    if (newId) {
      refreshTableData();
    }
  },
);

/**
 * Initializes the job progress alert list for the current day.
 * Fetches job status update alerts from the driver message store
 * and updates the reactive jobProgressAlertList.
 */
async function initJobProgressAlertList() {
  const timeNow = returnTimeNow();
  const jobProgressRequest: JobProgressAlertHistoryRequest = {
    startEpoch: returnStartOfDayFromEpoch(),
    endEpoch: timeNow,
    alertType: JobProgressAlertType.JOB_STATUS_UPDATE,
  };

  const jobProgressAlerts =
    await useDriverMessageStore().searchJobProgressAlertHistory(
      jobProgressRequest,
    );

  jobProgressAlertList.value = jobProgressAlerts ?? [];
}

/**
 * Initializes the driver app notification map for the current day.
 * Fetches online and offline notification events for drivers,
 * and updates the map with the latest event per driver.
 */
async function initAppNotificationMap() {
  const timeNow = returnTimeNow();
  const notificationMap: Map<string, DriverAppNotification> = new Map();

  const [onlineStatusEvents, offlineStatusEvents] = await Promise.all([
    useDriverAppStore().getDriverAppNotificationHistory({
      startEpoch: returnStartOfDayFromEpoch(),
      endEpoch: timeNow,
      notificationType: DriverNotificationType.ONLINE,
    }),
    useDriverAppStore().getDriverAppNotificationHistory({
      startEpoch: returnStartOfDayFromEpoch(),
      endEpoch: timeNow,
      notificationType: DriverNotificationType.OFFLINE,
    }),
  ]);

  // Combine the online and offline events so we can iterate
  const combinedList = [
    ...(onlineStatusEvents ?? []),
    ...(offlineStatusEvents ?? []),
  ];

  combinedList.forEach((event) => {
    const existing = notificationMap.get(event.driverId);
    if (!existing || event.epochTime > existing.epochTime) {
      notificationMap.set(event.driverId, event);
    }
  });

  driverAppNotificationMap.value = notificationMap;
}

/**
 * Initializes all data required for the vacant trucks table.
 * Loads job progress alerts and driver app notifications in parallel,
 * then refreshes the table data and updates the loading state.
 */
async function initialiseTableData() {
  // Get job progress alerts for today
  isLoadingTableData.value = true;

  // Set base lists then call refreshTableData to populate table data
  await Promise.all([initJobProgressAlertList(), initAppNotificationMap()]);
  refreshTableData();

  isLoadingTableData.value = false;
}

/**
 * Handles click event on a row
 */
function handleRowSelection(listItem: VacantTruckSummary) {
  // Set the selection for the fleet list
  if (listItem.fleetAssetId && listItem.driverId) {
    operationsStore.setSelectedFleetAssetId(listItem.fleetAssetId);
    operationsStore.setSelectedDriverId(listItem.driverId);
  }

  // Set the selection for selected job
  if (listItem.clearedJobId) {
    Mitt.emit('closeDashboardJobRows', 'driver-message');
    operationsStore.setSelectedJobId(listItem.clearedJobId);
    operationsStore.getFullJobDetails(listItem.clearedJobId);
  }
}

/**
 * Handles an update to a job. Does nothing for all update types except
 * DRIVER_COMPLETED events
 * @param payload - The job event summary payload
 */
function handleJobStatusUpdate(payload: JobEventSummary | null) {
  try {
    if (payload?.workStatus === WorkStatus.DRIVER_COMPLETED) {
      const foundJob = useJobStore().operationJobsList.find(
        (job) => job.jobId === payload.jobId,
      );
      if (!foundJob) {
        return;
      }
      // Construct JobProgressAlert object and push into list
      const lastStop = foundJob.pudItems[foundJob.pudItems.length - 1];
      const jobProgressAlert: JobProgressAlert = {
        _id: uuidv4().replace(/-/g, ''),
        company: '',
        division: '',
        epochTime: payload.latestEvent?.changeTime ?? moment().valueOf(),
        alertType: JobProgressAlertType.JOB_STATUS_UPDATE,
        driverId: foundJob.driverId,
        fleetAssetId: foundJob.fleetAssetId,
        jobId: payload.jobId,
        pudId: '',
        updatedStatus: 'CompletedJob',
        alertMessage: '',
        locationName: lastStop.suburb,
      };
      jobProgressAlertList.value.push(jobProgressAlert);
    }

    refreshTableDataDebounced();
  } catch (error) {
    logConsoleError('Something went wrong handling job status update', error);
  }
}

/**
 * Handles a DriverAppNotification received from the backend. Updates the
 * driverAppNotificationMap if it's an online/offline update
 * @param payload - The DriverAppNotification received from the backend
 */
function handleDriverAppNotification(payload: DriverAppNotification | null) {
  if (!payload?.driverId) {
    return;
  }
  // If notification type is ONLINE or OFFLINE type, we should update the onlineDriverMap
  if (
    payload.notificationType === DriverNotificationType.ONLINE ||
    payload.notificationType === DriverNotificationType.OFFLINE
  ) {
    driverAppNotificationMap.value.set(payload.driverId, payload);

    refreshTableDataDebounced();
  }
}

/**
 * Handles division-level updates to job allocation.
 * @param jobIds - The list of allocated job IDs
 */
function handleAllocatedJobIds(jobIds: number[] | null): void {
  if (jobIds) {
    refreshTableDataDebounced();
  }
}

/**
 * Debounced version of refreshTableData to prevent excessive calls.
 */
function refreshTableDataDebounced(): void {
  if (refreshTimeout) {
    // console.log('Clearing previous refresh timeout');
    clearTimeout(refreshTimeout);
  }
  // console.log('Setting new refresh timeout for refreshTableData');
  refreshTimeout = setTimeout(() => {
    // console.log('Debounced refreshTableData called');
    refreshTableData();
  }, 1000);
}

/**
 * Sets up a timer to call refreshTableData at the start of every minute.
 */
function setupMinuteRefresh(): void {
  // Calculate ms until the next minute starts
  const now = new Date();
  const msToNextMinute = (60 - now.getSeconds()) * 1000 - now.getMilliseconds();

  // Set a timeout to trigger at the start of the next minute
  setTimeout(() => {
    refreshTableData();
    // After the first refresh, set an interval to refresh every minute
    refreshIntervalId = window.setInterval(() => {
      refreshTableData();
    }, 60 * 1000);
  }, msToNextMinute);
}

// Mitt listeners - update local lists when updates come through
useMittListener('jobStatusUpdate', handleJobStatusUpdate);
useMittListener('allocatedPreAllocatedJobIds', handleAllocatedJobIds);
useMittListener('updatedStatusJobDetails', handleJobStatusUpdate);
useMittListener('updateJobEventListResponse', handleJobStatusUpdate);
useMittListener('driverStatusChanged', handleDriverAppNotification);

onMounted(() => {
  initialiseTableData();
  setupMinuteRefresh();
});

onUnmounted(() => {
  if (refreshIntervalId !== undefined) {
    clearInterval(refreshIntervalId);
  }
});
</script>
<style scoped lang="scss">
.service-name {
  height: 26px;
  background-color: var(--background-color-300);

  padding: 4px 16px;

  &:hover {
    cursor: pointer;
  }

  p {
    margin: 0;
    font-size: $font-size-11;
    text-transform: uppercase;
    font-weight: 600;
    color: var(--bg-light);
  }

  // border-bottom: solid 2px $border-light-client !important;
  // border-bottom: solid 2px $border-light-client;
}
</style>
