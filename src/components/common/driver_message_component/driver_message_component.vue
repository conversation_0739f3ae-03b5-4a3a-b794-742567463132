<template>
  <section class="driver-message-component app-theme__center-content--body">
    <v-layout
      class="task-bar app-theme__center-content--header no-highlight pa-0"
    >
      <v-tooltip right v-if="currentTab === 'CONVERSATION'">
        <template v-slot:activator="{ on }">
          <v-btn
            flat
            v-on="on"
            icon
            @click="currentTab = MessageTabs.CURRENT"
            class="ma-0 pa-0"
          >
            <v-icon size="14" color="grey lighten-1">fal fa-arrow-left</v-icon>
          </v-btn>
        </template>
        Return to Messages
      </v-tooltip>

      <v-tabs
        v-if="currentTab === 'CURRENT' || !conversationHeader"
        height="26"
        :active-class="'tab-active'"
        hide-slider
        class="tab-select-container"
        v-model="selectedTabIndex"
      >
        <v-tab class="ma-0 tab-selector" :value="MessageTabs.CURRENT"
          ><p class="ma-0 tab-text">Driver Chat</p></v-tab
        >
        <v-tab class="ma-0 pl-2 tab-selector" :value="MessageTabs.VACANT_TRUCKS"
          ><p class="ma-0 tab-text">Vacant Trucks</p></v-tab
        >
      </v-tabs>
      <span v-else style="padding: 6px 8px">{{ conversationHeader }}</span>

      <v-spacer></v-spacer>
      <ChatAlertDialog
        v-if="$vuetify.breakpoint.lgAndUp"
        :jobProgressAlertType="JobProgressAlertType.APPROACHING_PICKUP_TIME"
      ></ChatAlertDialog>
      <ChatAlertDialog
        v-if="$vuetify.breakpoint.lgAndUp"
        :jobProgressAlertType="JobProgressAlertType.AWAITING_ACCEPT"
      ></ChatAlertDialog>
      <ChatAlertDialog
        v-if="$vuetify.breakpoint.lgAndUp"
        :jobProgressAlertType="JobProgressAlertType.LOAD_TIME"
      ></ChatAlertDialog>

      <div>
        <v-menu right>
          <template v-slot:activator="{ on: menu }">
            <v-tooltip bottom>
              <template v-slot:activator="{ on: tooltip }">
                <v-btn flat icon v-on="{ ...tooltip, ...menu }" class="ma-0">
                  <v-icon size="14" color="grey lighten-1"
                    >fas fa-ellipsis-v
                  </v-icon>
                </v-btn>
              </template>
              <span>View Additional Actions</span>
            </v-tooltip>
          </template>
          <v-list class="v-list-custom" dense>
            <v-list-tile @click="setShowDriverChatHistoryDialog(true)">
              <v-list-tile-avatar>
                <v-icon size="16">fas fa-history</v-icon>
              </v-list-tile-avatar>
              <v-list-tile-title class="pr-2">
                Search Driver Message History
              </v-list-tile-title>
            </v-list-tile>
            <v-list-tile
              @click="jobProgressAlertHistoryDialogController = true"
            >
              <v-list-tile-avatar>
                <v-icon size="16">far fa-alarm-exclamation</v-icon>
              </v-list-tile-avatar>
              <v-list-tile-title class="pr-2">
                View Job Alert History
              </v-list-tile-title>
            </v-list-tile>

            <v-list-tile @click="setShowDriverBulkMessageDialog(true)">
              <v-list-tile-avatar>
                <v-icon size="16">fal fa-broadcast-tower</v-icon>
              </v-list-tile-avatar>
              <v-list-tile-title class="pr-2">
                Message All Drivers
              </v-list-tile-title>
            </v-list-tile>
            <OperationsSettingsDialog
              key="message-list-settings-dialog"
              buttonText="Driver Message Settings"
              title="Driver Messages - Settings"
              @confirm="tableSettingsUpdated"
              :buttonDisabled="false"
              buttonColor="teal accent-3"
              faIconName="fal fa-sliders-h"
              :dialogWidth="500"
              :isListTile="true"
              :useLeadingIcon="true"
              :settingsList="messageListSettings"
              :defaultSettingsList="messageListSetting"
              :allowExclusions="true"
            >
            </OperationsSettingsDialog>
          </v-list>
        </v-menu>
      </div>
    </v-layout>

    <div class="driver-message-table" v-if="currentTab === MessageTabs.CURRENT">
      <table cellpadding="0" cellspacing="0">
        <thead v-if="$vuetify.breakpoint.lgAndUp">
          <tr>
            <th v-for="header in headers" :key="header.text">
              {{ header.text }}
            </th>
          </tr>
        </thead>
        <tbody>
          <template v-for="(item, index) in chatMessageList">
            <tr
              v-if="!item.isSystemMessage"
              @click.stop="chatMessageSelected(item)"
              :key="item._id || `${index}`"
              :id="item._id ?? ''"
              class="chat-message"
              tabindex="0"
              :class="[selectedChatMessageId === item._id ? 'selected' : '']"
            >
              <template v-if="$vuetify.breakpoint.lgAndUp">
                <td class="text-xs-left">
                  {{
                    messagesAllSameDay
                      ? returnFormattedTime(item.timestamp, `HH:mm`)
                      : returnFormattedTime(item.timestamp, `HH:mm DD/MM/YY`)
                  }}
                </td>
                <td class="text-xs-left">
                  {{
                    item.jobId && recurringJobIdMap.get(item.jobId)
                      ? recurringJobIdMap.get(item.jobId)
                      : item.jobId
                        ? item.jobId
                        : '--'
                  }}
                </td>
                <td
                  class="text-xs-left"
                  :class="
                    item.isOperationsMessage ? 'accent-text--primary' : ''
                  "
                >
                  <span v-if="item.isOperationsMessage">
                    {{
                      item.senderName
                        ? item.senderName
                        : returnDriverName(item.senderId)
                    }}
                  </span>
                  <span v-else>
                    {{
                      item.fleetAssetId
                        ? fleetCsrAssignedId(item.fleetAssetId)
                        : 'Unknown'
                    }}
                    ({{
                      item.senderName
                        ? item.senderName
                        : returnDriverName(item.senderId)
                    }})
                  </span>
                </td>
                <td class="text-xs-left" :class="{ 'pr-4': item.isPinned }">
                  {{ item.content }}
                </td>
              </template>
              <template v-else>
                <td
                  class="text-xs-left"
                  :class="
                    item.isOperationsMessage ? 'accent-text--primary' : ''
                  "
                >
                  <v-layout column align-center>
                    <span v-if="item.isOperationsMessage">
                      {{
                        item.senderName
                          ? item.senderName
                          : returnDriverName(item.senderId)
                      }}
                    </span>
                    <span v-else>
                      {{
                        item.fleetAssetId
                          ? fleetCsrAssignedId(item.fleetAssetId)
                          : 'Unknown'
                      }}
                    </span>
                    <span style="font-size: 8px; white-space: nowrap">
                      {{ returnTimeFromNow(item.timestamp) }}
                    </span>
                    <span style="font-size: 8px; white-space: nowrap">
                      {{
                        item.jobId && recurringJobIdMap.get(item.jobId)
                          ? recurringJobIdMap.get(item.jobId)
                          : item.jobId
                            ? item.jobId
                            : ''
                      }}
                    </span>
                  </v-layout>
                </td>
                <td
                  class="text-xs-left"
                  :class="{ 'pr-4': item.isPinned }"
                  colspan="3"
                >
                  {{ item.content }}
                </td>
              </template>
              <div
                v-if="item.isPinned"
                class="pr-1"
                style="position: absolute; right: 0px"
              >
                <v-tooltip bottom class="pa-0">
                  <template v-slot:activator="{ on }">
                    <v-btn
                      flat
                      v-on="on"
                      icon
                      small
                      @click="hideChatMessage(item, -1)"
                      class="ma-0 pa-0"
                    >
                      <v-icon color="orange" size="11">fas fa-thumbtack</v-icon>
                    </v-btn>
                  </template>
                  Unpin Message
                </v-tooltip>
              </div>
            </tr>
            <template v-else>
              <tr
                class="alert-message"
                :class="[
                  selectedChatMessageId === item._id ? 'selected' : '',
                  !!associatedAlerts &&
                  associatedAlerts.length &&
                  selectedChatMessageId === item._id
                    ? 'alerts-expanded'
                    : '',
                ]"
                @click.stop="chatMessageSelected(item)"
                tabindex="0"
                :key="item._id || index"
                :id="item._id ?? ''"
              >
                <td colspan="4">
                  <v-layout align-center pl-1 pr-4>
                    <v-flex>
                      <v-layout align-center>
                        <span class="px-2">{{
                          returnFormattedTime(item.timestamp, `HH:mm`)
                        }}</span>
                        <v-divider> </v-divider>
                      </v-layout>
                    </v-flex>
                    <div class="job-progress-alert-chip">
                      {{ item.content }}
                    </div>
                    <v-flex>
                      <v-divider> </v-divider>
                    </v-flex>
                  </v-layout>
                </td>
              </tr>
              <template
                v-if="
                  !!associatedAlerts &&
                  associatedAlerts.length &&
                  selectedChatMessageId === item._id
                "
              >
                <tr
                  v-for="(alert, alertIndex) in associatedAlerts"
                  :key="`${item._id} - ${alert._id}`"
                  tabindex="-1"
                  :class="{
                    'first-row': alertIndex === 0,
                    'last-row': alertIndex === associatedAlerts.length - 1,
                    'selected-child':
                      !!alert.jobId && currentSelectedJobId === alert.jobId,
                  }"
                  class="chat-message"
                  @click="
                    !!alert.jobId && currentSelectedJobId !== alert.jobId
                      ? selectJobProgressAlert(alert)
                      : null
                  "
                >
                  <td class="text-xs-left">
                    {{ returnFormattedTime(alert.epochTime, `HH:mm`) }}
                  </td>
                  <td class="text-xs-left" :class="[]">
                    <span class="job-id">
                      {{
                        recurringJobIdMap.get(alert.jobId)
                          ? recurringJobIdMap.get(alert.jobId)
                          : alert.jobId
                            ? alert.jobId
                            : '--'
                      }}
                    </span>
                  </td>
                  <td class="text-xs-left">
                    <span>
                      {{
                        alert.driverId ? returnDriverName(alert.driverId) : '-'
                      }}
                    </span>
                  </td>
                  <td class="text-xs-left">
                    {{ alert.alertMessage }}
                  </td>
                </tr>
              </template>
            </template>
          </template>
        </tbody>
      </table>
    </div>
    <VacantTrucksTable
      v-if="currentTab === MessageTabs.VACANT_TRUCKS"
    ></VacantTrucksTable>

    <!-- =================================================================== -->
    <!-- BY DRIVER -->
    <!-- =================================================================== -->

    <DriverConversation
      :key="currentConversationSenderId"
      :driverId="currentConversationSenderId"
      @jumpToHistoryComponent="jumpToHistoryComponent"
      v-if="currentTab === MessageTabs.CONVERSATION"
    >
    </DriverConversation>

    <v-dialog
      v-model="showDriverChatHistoryDialog"
      persistent
      width="60%"
      content-class="v-dialog-custom"
    >
      <v-layout
        justify-space-between
        align-center
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>Driver Chat History</span>
        <v-spacer></v-spacer>
        <div
          class="app-theme__center-content--closebutton"
          @click="setShowDriverChatHistoryDialog(false)"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <v-layout class="app-theme__center-content--body dialog-content">
        <DriverChatHistory v-if="showDriverChatHistoryDialog" />
      </v-layout>
    </v-dialog>

    <ContentDialog
      :showDialog.sync="jobProgressAlertHistoryDialogController"
      title="Job Alert History"
      width="80%"
      contentPadding="pa-0"
      @cancel="jobProgressAlertHistoryDialogController = false"
      :showActions="false"
    >
      <v-layout>
        <v-flex md12 class="body-scrollable--75 pa-3">
          <JobProgressAlertHistory
            v-if="jobProgressAlertHistoryDialogController"
          />
        </v-flex>
      </v-layout>
    </ContentDialog>
    <v-dialog
      v-model="showDriverBulkMessageDialog"
      persistent
      width="30%"
      content-class="v-dialog-custom"
    >
      <v-layout
        justify-space-between
        align-center
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>Message All Drivers</span>
        <v-spacer></v-spacer>
        <div
          class="app-theme__center-content--closebutton"
          @click="setShowDriverBulkMessageDialog(false)"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <v-layout class="app-theme__center-content--body dialog-content">
        <DriverBulkMessage
          v-if="showDriverBulkMessageDialog"
          @setShowDriverBulkMessageDialog="setShowDriverBulkMessageDialog"
        />
      </v-layout>
    </v-dialog>
  </section>
</template>

<script setup lang="ts">
import ChatAlertDialog from '@/components/common/chat_alert_dialog/chat_alert_dialog.vue';
import DriverBulkMessage from '@/components/common/driver_bulk_message.vue';
import DriverChatHistory from '@/components/common/driver_chat_history/driver_chat_history.vue';
import DriverConversation from '@/components/common/driver_message_component/driver_conversation.vue';
import VacantTrucksTable from '@/components/common/driver_message_component/vacant_trucks_table.vue';
import JobProgressAlertHistory from '@/components/common/job_progress_alert_history/job_progress_alert_history.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import OperationsSettingsDialog from '@/components/common/ui-elements/operations_settings_dialog.vue';
import {
  returnEndOfDayFromEpoch,
  returnFormattedTime,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import ChatMessage from '@/interface-models/Generic/ChatConversation/ChatMessage';
import OperationsDashboardSetting, {
  driverMessageDefaultSettings,
} from '@/interface-models/Generic/OperationScreenOptions/OperationsDashboardSetting';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import JobProgressAlert from '@/interface-models/Jobs/JobProgressAlert/JobProgressAlert';
import JobProgressAlertHistoryRequest from '@/interface-models/Jobs/JobProgressAlert/JobProgressAlertHistoryRequest';
import { JobProgressAlertType } from '@/interface-models/Jobs/JobProgressAlert/JobProgressAlertType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useDriverMessageStore } from '@/store/modules/DriverMessageStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { useRecurringJobStore } from '@/store/modules/RecurringJobStore';
import { useRootStore } from '@/store/modules/RootStore';
import Mitt from '@/utils/mitt';
import { useMittListener } from '@/utils/useMittListener';
import moment from 'moment-timezone';
import {
  computed,
  ComputedRef,
  onBeforeMount,
  onBeforeUnmount,
  onMounted,
  ref,
  Ref,
  watch,
  WritableComputedRef,
} from 'vue';

enum MessageTabs {
  CURRENT = 'CURRENT',
  CONVERSATION = 'CONVERSATION',
  VACANT_TRUCKS = 'VACANT_TRUCKS',
}

/**
 * Store instances
 */
const fleetAssetStore = useFleetAssetStore();
const companyDetailsStore = useCompanyDetailsStore();
const driverMessageStore = useDriverMessageStore();
const operationsStore = useOperationsStore();
const driverDetailsStore = useDriverDetailsStore();

/**
 * Settings
 */
const messageListSetting: OperationsDashboardSetting[] =
  driverMessageDefaultSettings;

/**
 * Reactive state
 */
const unsentMessage: Ref<string> = ref('');
const showJobProgressAlertHistoryDialog: Ref<boolean> = ref(false);
const showDriverChatHistoryDialog: Ref<boolean> = ref(false);
const showDriverBulkMessageDialog: Ref<boolean> = ref(false);
const selectedChatMessageId: Ref<string | null> = ref(null);
const showingAlertsForChatId: Ref<string | null> = ref(null);
const associatedAlerts: Ref<JobProgressAlert[] | null> = ref(null);

const _currentTab: Ref<MessageTabs> = ref(MessageTabs.CURRENT);
const _currentConversationId: Ref<string> = ref('');

/**
 * Controls visibility of the dialogs for displaying certain JobProgressAlert documents.
 */
const jobProgressAlertHistoryDialogController: WritableComputedRef<boolean> =
  computed({
    get(): boolean {
      return showJobProgressAlertHistoryDialog.value;
    },
    set(value: boolean) {
      showJobProgressAlertHistoryDialog.value = value;
    },
  });

/**
 * Current conversation sender ID. Updates the value in the store when setting
 */
const currentConversationSenderId: WritableComputedRef<string> = computed({
  get(): string {
    return _currentConversationId.value;
  },
  set(value: string) {
    _currentConversationId.value = value;
  },
});

/**
 * Current tab (CURRENT or CONVERSATION)
 */
const currentTab: WritableComputedRef<MessageTabs> = computed({
  get(): MessageTabs {
    return _currentTab.value;
  },
  set(value: MessageTabs) {
    if (value === MessageTabs.CURRENT) {
      currentConversationSenderId.value = '';
    }
    _currentTab.value = value;
  },
});

/**
 * Used as the v-model for the v-tabs which use the index of the tabs as their
 * value. Gets and sets the selected tab index based on the
 * current tab.
 */
const selectedTabIndex: WritableComputedRef<number> = computed({
  get(): number {
    const tabs = [
      MessageTabs.CURRENT,
      MessageTabs.VACANT_TRUCKS,
      MessageTabs.CONVERSATION,
    ];
    return tabs.indexOf(currentTab.value);
  },
  set(value: number): void {
    if (value === 0) {
      currentTab.value = MessageTabs.CURRENT;
    } else if (value === 1) {
      currentTab.value = MessageTabs.VACANT_TRUCKS;
    } else if (value === 2) {
      currentTab.value = MessageTabs.CONVERSATION;
    }
  },
});

/**
 * Sorted chat message list for display
 */
const chatMessageList: ComputedRef<ChatMessage[]> = computed(() => {
  const allMessages: ChatMessage[] = driverMessageStore.chatMessageList;
  const driverMessages: ChatMessage[] = allMessages.filter((message) => {
    const isSystemMessage =
      message.isSystemMessage &&
      allowedSystemMessageTypes.value.includes(message.receiverId);
    return message.isDriverMessage || isSystemMessage || message.actionRequired;
  });
  if (currentTab.value === MessageTabs.CURRENT) {
    return driverMessages.filter((message) => !message.isActioned);
  } else {
    return driverMessages;
  }
});

/**
 * Returns true if all messages are from today (for time formatting)
 */
const messagesAllSameDay: ComputedRef<boolean> = computed(() => {
  if (currentTab.value === MessageTabs.CONVERSATION) {
    return false;
  }
  const sod = returnStartOfDayFromEpoch();
  const eod = returnEndOfDayFromEpoch();
  return chatMessageList.value.every(
    (c) => c.timestamp >= sod && c.timestamp < eod,
  );
});

/**
 * Table headers for chat messages
 */
const headers: TableHeader[] = [
  {
    text: 'Time',
    align: 'left',
    sortable: true,
    value: 'timestamp',
    class: 'chat-table-header',
  },
  {
    text: 'Job#',
    align: 'left',
    sortable: true,
    value: 'jobId',
    class: 'chat-table-header',
  },
  {
    text: 'From',
    align: 'left',
    sortable: true,
    value: 'senderName',
    class: 'chat-table-header',
  },
  {
    text: 'Message',
    align: 'left',
    sortable: true,
    value: 'content',
    class: 'chat-table-header',
  },
];

/**
 * Returns a string representing the time elapsed since the given epoch time in
 * milliseconds.
 * @param epochMs - The epoch time in milliseconds.
 * @returns A string representing the time elapsed since `epochMs`.
 */
function returnTimeFromNow(epochMs: number): string {
  const tz: string = companyDetailsStore.userLocale;
  const time = moment(epochMs).tz(tz);
  return time.fromNow();
}

/**
 * Returns the current settings config for the Message component.
 */
const messageListSettings: ComputedRef<OperationsDashboardSetting[]> = computed(
  () => {
    return JSON.parse(
      JSON.stringify(operationsStore.dashboardSettings.messageList),
    );
  },
);

/**
 * Captures emit from Operations Settings component, and commits it to the
 * store.
 */
function tableSettingsUpdated(
  settingsList: OperationsDashboardSetting[],
): void {
  operationsStore.updateDashboardSettingsDriverMessage(settingsList);
}

/**
 * Returns a list of currently enabled system message types.
 */
const allowedSystemMessageTypes: ComputedRef<string[]> = computed(() => {
  const allowedList: string[] = [];
  if (isSettingActive('showJobAlerts')) {
    allowedList.push(JobProgressAlertType.JOB_STATUS_UPDATE);
  }
  if (isSettingActive('showStopAlerts')) {
    allowedList.push(JobProgressAlertType.PUD_STATUS_UPDATE);
  }
  if (isSettingActive('showAttachmentAlerts')) {
    allowedList.push(JobProgressAlertType.ATTACHMENT_ADDED);
  }
  if (isSettingActive('showApproachingPickupTimeAlerts')) {
    allowedList.push(JobProgressAlertType.APPROACHING_PICKUP_TIME);
  }
  if (isSettingActive('showLateAcceptAlerts')) {
    allowedList.push(JobProgressAlertType.AWAITING_ACCEPT);
  }
  if (isSettingActive('showLoadTimeAlerts')) {
    allowedList.push(JobProgressAlertType.LOAD_TIME);
  }
  if (isSettingActive('showFinalLegAlerts')) {
    allowedList.push(JobProgressAlertType.COMPLETED_FINAL_LEG);
  }
  if (isSettingActive('showAppNotifications')) {
    allowedList.push(JobProgressAlertType.LOG_IN);
    allowedList.push(JobProgressAlertType.LOG_OUT);
  }
  if (isSettingActive('showAppRestartNotifications')) {
    allowedList.push(JobProgressAlertType.APP_RESTART);
  }
  if (isSettingActive('showGpsDisabledNotifications')) {
    allowedList.push(JobProgressAlertType.GPS_DISABLED);
  }
  return allowedList;
});

/**
 * Checks if a given setting is active.
 * @param settingId - The setting ID.
 * @param driverId - Optional driver ID.
 */
function isSettingActive(settingId: string, driverId: string = ''): boolean {
  const settings = messageListSettings.value;
  const foundSetting = settings.find((s) => s.id === settingId);
  if (!foundSetting) {
    return false;
  }
  const isActive =
    foundSetting.active &&
    (!driverId ||
      !foundSetting.excludeIds ||
      !foundSetting.excludeIds.includes(driverId));
  return isActive;
}

/**
 * Handles selection of a chat message from the template.
 * @param message - The chat message.
 * @param showAdditionalDetails - Whether to show additional details.
 */
function chatMessageSelected(
  message: ChatMessage,
  showAdditionalDetails = false,
): void {
  let changedFocus = false;
  if (!showAdditionalDetails) {
    changedFocus = focusChatMessage(message._id);
  }
  if (message.jobId && operationsStore.selectedJobId !== message.jobId) {
    setSelectedJobId(message.jobId);
  }
  if (
    (message.isDriverMessage || message.isSystemMessage) &&
    message.fleetAssetId &&
    message.senderId
  ) {
    operationsStore.setSelectedFleetAssetId(message.fleetAssetId);
    operationsStore.setSelectedDriverId(message.senderId);
  }
  if (showAdditionalDetails) {
    if (message.isOperationsMessage && message.receiverId) {
      currentConversationSenderId.value = message.receiverId;
      currentTab.value = MessageTabs.CONVERSATION;
    } else if (message.isDriverMessage && message.senderId) {
      currentConversationSenderId.value = message.senderId;
      currentTab.value = MessageTabs.CONVERSATION;
    } else if (message.isSystemMessage) {
      setAssociatedAlertsFromChatMessage(message);
    }
  }
}

/**
 * Handles click on a job progress alert item in the html, for system messages
 * that have associated JobProgressAlerts.
 * @param alert - The job progress alert to be selected.
 */
function selectJobProgressAlert(alert: JobProgressAlert): void {
  operationsStore.setSelectedJobId(alert.jobId);
  operationsStore.getFullJobDetails(alert.jobId);
  if (!alert.driverId) {
    return;
  }
  const fleets = returnFleetAssetIdsFromDriverId(alert.driverId);
  if (fleets.length) {
    operationsStore.setSelectedDriverId(alert.driverId);
    operationsStore.setSelectedFleetAssetId(fleets[0]);
  }
}

/**
 * Sets the selected job ID in state and dispatches request for the full job details.
 * @param jobId - The ID of the job to be selected.
 */
function setSelectedJobId(jobId: number): void {
  if (currentSelectedJobId.value === jobId) {
    return;
  }
  Mitt.emit('closeDashboardJobRows', 'driver-message');
  operationsStore.setSelectedJobId(jobId);
  operationsStore.getFullJobDetails(jobId);
}

/**
 * The currently selected job ID.
 */
const currentSelectedJobId: ComputedRef<number> = computed(
  () => operationsStore.selectedJobId,
);

/**
 * Sets the associated alerts for a chat message.
 * @param message - The chat message to set the associated alerts for.
 */
function setAssociatedAlertsFromChatMessage(message: ChatMessage): void {
  if (
    !message.isSystemMessage ||
    !message.associatedIds ||
    !message.associatedIds.length ||
    !message._id
  ) {
    showingAlertsForChatId.value = null;
    associatedAlerts.value = null;
    return;
  }
  if (
    !!showingAlertsForChatId.value &&
    showingAlertsForChatId.value === message._id
  ) {
    showingAlertsForChatId.value = null;
    associatedAlerts.value = null;
    setTimeout(() => {
      focusElement(message._id!);
    }, 10);
    return;
  }
  let mapToCheck: Map<number, JobProgressAlert> | null = null;
  switch (message.receiverId) {
    case JobProgressAlertType.LOAD_TIME:
      mapToCheck = driverMessageStore.loadTimeProgressAlerts;
      break;
    case JobProgressAlertType.AWAITING_ACCEPT:
      mapToCheck = driverMessageStore.acceptProgressAlerts;
      break;
    case JobProgressAlertType.APPROACHING_PICKUP_TIME:
      mapToCheck = driverMessageStore.pickupProgressAlerts;
      break;
  }
  if (mapToCheck === null || mapToCheck.size === 0) {
    return;
  }
  const associated = Array.from(mapToCheck.values()).filter((j) =>
    message.associatedIds!.includes(j._id),
  );
  showingAlertsForChatId.value = message._id;
  associatedAlerts.value = associated;
}

/**
 * Returns the csrAssignedId from fleetAssetId.
 */
function fleetCsrAssignedId(fleetAssetId: string): string {
  const csrAssignedId = fleetAssetStore.csrAssignedIdMap.get(fleetAssetId);
  return csrAssignedId ? csrAssignedId : '-';
}

/**
 * Returns the driver name from supplied driverId.
 */
function returnDriverName(driverId: string): string {
  const foundDriver = driverDetailsStore.getDriverFromDriverId(driverId);
  return foundDriver?.displayName ?? '-';
}

/**
 * Hides a chat message and performs necessary actions.
 * @param message - The chat message to hide.
 * @param index - The index of the chat message in the list.
 */
function hideChatMessage(message: ChatMessage, index: number): void {
  if (index !== -1) {
    const nextMessage = chatMessageList.value[index + 1];
    if (nextMessage) {
      focusChatMessage(nextMessage._id);
    }
  }
  if (message.roleId === -1) {
    driverMessageStore.updateSystemChatMessage(message);
  } else {
    message.actionRequired = false;
    message.isActioned = true;
    driverMessageStore.updateChatMessage(message);
  }
}

/**
 * Removes the focus from the currently active element.
 */
function removeElementFocus(): void {
  if (document.activeElement instanceof HTMLElement) {
    document.activeElement.blur();
  }
  selectedChatMessageId.value = null;
  showingAlertsForChatId.value = null;
  associatedAlerts.value = null;
}

/**
 * Gets the current driver details for the conversation header.
 */
const conversationHeader: ComputedRef<string | undefined> = computed(() => {
  if (!currentConversationSenderId.value) {
    return;
  }
  const driver = driverDetailsStore.getDriverFromDriverId(
    currentConversationSenderId.value,
  );
  if (!driver) {
    return;
  }
  const fleetCsrAssignedIds: string[] = returnFleetAssetIdsFromDriverId(
    currentConversationSenderId.value,
  ).map((f) => fleetCsrAssignedId(f));
  return fleetCsrAssignedIds.length
    ? `${driver.displayName} (${fleetCsrAssignedIds.join(', ')})`
    : driver.displayName;
});

/**
 * Returns an array of fleet asset IDs associated with the given driver ID.
 * @param driverId - The ID of the driver.
 * @returns An array of fleet asset IDs.
 */
function returnFleetAssetIdsFromDriverId(driverId: string): string[] {
  const fleets: string[] = [];
  fleetAssetStore.fleetAssetActiveDriver.forEach((value, key) => {
    if (value === driverId) {
      fleets.push(key);
    }
  });
  return fleets;
}

/**
 * Returns the recurring job ID map.
 */
const recurringJobIdMap: ComputedRef<Map<number, string | null>> = computed(
  () => {
    if (!useRootStore().operationsPortalLoadedData.JOBS) {
      return new Map();
    }
    return useRecurringJobStore().recurringJobIdMap;
  },
);

/**
 * Sets the value of showDriverChatHistoryDialog.
 * @param value - The new value for showDriverChatHistoryDialog.
 */
function setShowDriverChatHistoryDialog(value: boolean): void {
  showDriverChatHistoryDialog.value = value;
}

/**
 * Sets the value of showDriverBulkMessageDialog.
 * @param value - The new value for showDriverBulkMessageDialog.
 */
function setShowDriverBulkMessageDialog(value: boolean): void {
  showDriverBulkMessageDialog.value = value;
}

/**
 * Focuses a chat message by element ID.
 * @param elId - The element ID.
 * @returns True if focus changed, false otherwise.
 */
function focusChatMessage(elId: string | undefined | null): boolean {
  if (!elId) {
    selectedChatMessageId.value = null;
    return false;
  }
  if (elId === selectedChatMessageId.value) {
    return false;
  }
  if (!!associatedAlerts.value) {
    showingAlertsForChatId.value = null;
    associatedAlerts.value = null;
    setTimeout(() => {
      focusElement(elId);
    }, 10);
  } else {
    focusElement(elId);
  }
  return true;
}

/**
 * Focuses an element by ID.
 * @param id - The element ID.
 */
function focusElement(id: string): void {
  selectedChatMessageId.value = id;
  const foundElement = document.getElementById(id);
  if (foundElement) {
    foundElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
  }
}

/**
 * For the selected conversation, open that driver's Fleet Asset Dialog and go to the tab for Message History.
 */
function jumpToHistoryComponent(): void {
  const fleets: string[] = returnFleetAssetIdsFromDriverId(
    currentConversationSenderId.value,
  );
  if (fleets.length) {
    operationsStore.setSelectedDriverId(currentConversationSenderId.value);
    operationsStore.setSelectedFleetAssetId(fleets[0]);
    operationsStore.setSelectedFleetAssetDialogTab('MESSAGE_HISTORY');
    operationsStore.setViewingAssetInformationDialog(true);
  } else {
    setShowDriverChatHistoryDialog(true);
  }
}

/**
 * As JobProgressAlerts are only sent by the backend every 5 minutes, we need
 * fetch the last batch when we load this component.
 */
async function getInitialJobProgressAlerts(): Promise<void> {
  if (
    driverMessageStore.loadTimeProgressAlerts === null ||
    driverMessageStore.acceptProgressAlerts === null ||
    driverMessageStore.pickupProgressAlerts === null
  ) {
    const userTimeZone = companyDetailsStore.userLocale;
    const request: JobProgressAlertHistoryRequest = {
      startEpoch: moment().tz(userTimeZone).subtract(5, 'minutes').valueOf(),
      endEpoch: moment().valueOf(),
    };
    const results =
      await driverMessageStore.searchJobProgressAlertHistory(request);
    driverMessageStore.savedJobProgressAlertHistoryResponse({
      company: '',
      division: '',
      jobProgressAlertList: results ?? [],
    });
  }
}

onBeforeMount(() => {
  getInitialJobProgressAlerts();
});

/**
 * Handles global keydown events for chat message navigation and actions.
 */
function handleComponentKeydown(event: KeyboardEvent): void {
  // Only handle if the message table is visible and focused in the DOM
  if (
    currentTab.value !== MessageTabs.CURRENT ||
    !selectedChatMessageId.value
  ) {
    return;
  }

  // Find the currently selected message index
  const currentIndex = chatMessageList.value.findIndex(
    (msg) => msg._id === selectedChatMessageId.value,
  );

  // Navigation: Up/Down
  if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {
    event.preventDefault();
    if (!chatMessageList.value.length) {
      return;
    }
    let newIndex = currentIndex;
    if (event.key === 'ArrowUp') {
      newIndex =
        currentIndex > 0 ? currentIndex - 1 : chatMessageList.value.length - 1;
    } else if (event.key === 'ArrowDown') {
      newIndex =
        currentIndex < chatMessageList.value.length - 1 ? currentIndex + 1 : 0;
    }
    const newMsg = chatMessageList.value[newIndex];
    if (newMsg) {
      focusChatMessage(newMsg._id);
    }
    return;
  }

  // Enter: Show additional details
  if (event.key === 'Enter' && currentIndex !== -1) {
    chatMessageSelected(chatMessageList.value[currentIndex], true);
    return;
  }

  // Delete: Hide message
  if (
    (event.key === 'Delete' || event.key === 'Backspace') &&
    currentIndex !== -1
  ) {
    hideChatMessage(chatMessageList.value[currentIndex], currentIndex);
    return;
  }

  // Escape: Remove focus
  if (event.key === 'Escape') {
    removeElementFocus();
    return;
  }
}

/**
 * Callback for mitt event to open a conversation with a specific driver, which
 * is called from the OperationsFleetList component when the chat icon button is
 * selected
 * @param params - containing message content and the driverId whose
 * conversation we want to open
 */
function handleConversationChange(
  params: {
    driverId: string;
    messageContent: string;
  } | null,
): void {
  if (!params) {
    currentConversationSenderId.value = '';
    unsentMessage.value = '';
    currentTab.value = MessageTabs.CURRENT;
    return;
  }
  currentConversationSenderId.value = params.driverId;
  unsentMessage.value = params.messageContent;
  currentTab.value = MessageTabs.CONVERSATION;
}

useMittListener('openInDriverMessages', handleConversationChange);

onMounted(() => {
  window.addEventListener('keydown', handleComponentKeydown);
});

onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleComponentKeydown);
});
</script>
<style scoped lang="scss">
.driver-message-component {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .task-bar {
    // border-radius: 10px 10px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .messages-header__tab-container {
    height: 30px;
    max-height: 30px;
    .messages-header__tab-item {
      transition: 0.1s;
      text-align: center;
      padding: 4px;
      border-style: solid;
      border-color: #403f46;
      border-width: 0px 1px 1px 1px;

      &.middle-item {
        border-width: 0px 0px 1px 0px;
      }

      &.selected {
        .messages-header__tab-item--text {
          font-size: $font-size-medium;
          color: var(--text-color);
          text-transform: uppercase;
          font-weight: 600;
        }
      }

      .messages-header__tab-item--text {
        font-size: $font-size-medium;
        color: grey;
        text-transform: uppercase;
        font-weight: 600;
      }

      &:hover {
        cursor: pointer;
        background-color: #39373f;
      }
    }
  }

  .message-contents {
    padding: 8px 12px;

    .message-bubble-container {
      .message-details-row {
        padding-top: 6px;

        .username {
          padding: 0px 6px;
          font-family: $sub-font-family;
          font-size: $font-size-12;
          text-transform: uppercase;
          font-weight: 600;
        }
        .datetime {
          font-size: $font-size-12;
          color: var(--light-text-color);
        }
      }

      &.operations {
        align-items: flex-end;
        .message-details-row {
          flex-direction: row-reverse;
        }
        .message-bubble {
          background-color: rgb(35, 64, 94);

          .message-menu {
            right: 100%;
            .message-menu-row {
              flex-direction: row-reverse;
            }
          }
        }
      }
      &.driver {
        align-items: start;
        .message-bubble {
          background-color: rgb(72, 71, 80);
          .message-menu {
            left: 100%;
          }
        }
      }
      .message-bubble {
        position: relative;
        display: inline-block;
        max-width: 80%;
        padding: 8px 10px;
        width: fit-content;
        border-radius: 16px;
        margin-bottom: 1px;
        font-size: 0.95em;
        border: 2px solid transparent;

        &.pinned {
          border-color: $toast-warning-bg;
        }

        .message-menu {
          position: absolute;
          display: none;
          top: 0px;
          .linked-jobid {
            background-color: rgba(72, 71, 80, 0.628);
            border: 1px solid rgb(72, 71, 80);
            // font-size: $font-size-12;
            font-style: italic;
            padding: 1px 8px;
            border-radius: 3px;
            font-family: $sub-font-family;
          }
        }

        &:hover {
          cursor: pointer;
          filter: brightness(110%);
          .message-menu {
            display: unset;
          }
        }
      }
    }
  }

  .chat-text-field-input-container {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
  }
}
</style>
