<template>
  <section class="attachment-thumbnail-item">
    <v-tooltip
      bottom
      max-width="300px"
      close-delay="0"
      content-class="v-tooltip__small-text"
    >
      <template v-slot:activator="{ on }">
        <div
          v-if="attachment.data && attachment.mimeType !== 'application/pdf'"
          class="image-container__container"
          :class="{
            'full-size': isFullSizeSignature,
          }"
          @click="requestFullSizeImage"
          v-on="on"
        >
          <div
            class="image-container"
            :class="{
              'signature-image': isSignatureType(attachment.documentTypeId),
            }"
            v-if="attachment.data"
          >
            <img :src="attachment.data" />
          </div>
        </div>

        <div
          class="image-container__container"
          v-if="attachment.mimeType === 'application/pdf'"
          @click="requestFullSizeImage"
          v-on="on"
        >
          <div class="image-container">
            <v-icon size="40">fas fa-file-pdf</v-icon>
          </div>
        </div>

        <div
          class="image-container__container"
          v-if="!attachment.mimeType && !attachment.data"
          @click="requestFullSizeImage"
          v-on="on"
        >
          <div class="image-container">
            <v-icon size="40">far fa-image-polaroid</v-icon>
          </div>
        </div>
      </template>
      <span
        ><v-layout row wrap>
          <v-flex md12>
            <v-layout>
              Attachment Type:
              <v-spacer></v-spacer>
              <span style="text-align: right">{{
                returnAttachmentTypeName(attachment.documentTypeId)
              }}</span>
            </v-layout></v-flex
          >
          <v-flex md12 v-if="attachment.signatureName">
            <v-layout>
              Signed By: <v-spacer></v-spacer
              >{{ attachment.signatureName ? attachment.signatureName : '-' }}
            </v-layout></v-flex
          >
          <v-flex md12>
            <v-layout>
              Created: <v-spacer></v-spacer
              >{{
                attachment.timestamp
                  ? returnFormattedTime(attachment.timestamp, `HH:mm DD/MM/YY`)
                  : '-'
              }}
            </v-layout></v-flex
          >
          <v-flex md12>
            <v-layout>
              Longitude: <v-spacer></v-spacer
              >{{
                attachment.gpsLocation && attachment.gpsLocation[0]
                  ? attachment.gpsLocation[0].toFixed(4)
                  : 'Unknown'
              }}
            </v-layout></v-flex
          >
          <v-flex md12>
            <v-layout>
              Latitude: <v-spacer></v-spacer
              >{{
                attachment.gpsLocation && attachment.gpsLocation[1]
                  ? attachment.gpsLocation[1].toFixed(4)
                  : 'Unknown'
              }}
            </v-layout></v-flex
          >
        </v-layout>
      </span>
    </v-tooltip>
  </section>
</template>

<script setup lang="ts">
import { downloadAttachment } from '@/helpers/AttachmentHelpers/AttachmentHelpers';
import { returnFormattedTime } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { returnAttachmentTypeName } from '@/helpers/JobDataHelpers/JobDataHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import Attachment from '@/interface-models/Generic/Attachment/Attachment';
import { useAttachmentStore } from '@/store/modules/AttachmentStore';
import { Ref, ref } from 'vue';

const props = withDefaults(
  defineProps<{
    attachment: Attachment;
    isFullSizeSignature?: boolean;
  }>(),
  {
    isFullSizeSignature: false,
  },
);

const isRequestingFullSizeAttachment: Ref<boolean> = ref(true);

/**
 * Determines if the given document type ID corresponds to a signature type
 * (PUD_ITEM_SIGNATURE or CHECKLIST_SIGNATURE).
 *
 * @param documentTypeId - The ID of the document type to check.
 * @returns `true` if the document type ID is either 34 or 14, indicating a
 * signature type; otherwise, `false`.
 */
function isSignatureType(documentTypeId: number): boolean {
  return documentTypeId === 34 || documentTypeId === 14;
}

/**
 * Requests the full-size image for the given attachment.
 * If the attachment ID is not found, a notification is shown.
 * Otherwise, the full-size image is downloaded.
 */
async function requestFullSizeImage(): Promise<void> {
  if (!props.attachment.id) {
    showNotification('Could not find full size image.');
    return;
  }
  isRequestingFullSizeAttachment.value = true;

  const result = await useAttachmentStore().getAttachmentById(
    props.attachment.id,
  );
  if (result?.data !== null) {
    downloadAttachment(result!);
    isRequestingFullSizeAttachment.value = false;
  }
}
</script>
<style scoped lang="scss">
.attachment-thumbnail-item {
  $thumbnail-res: 65px;
  .image-container__container {
    height: $thumbnail-res;
    width: $thumbnail-res;
    position: relative;
    margin: 0px 2px;

    &.full-size {
      width: auto;
      .image-container {
        width: auto;
      }
    }

    &:hover {
      cursor: pointer;

      .image-container__info {
        visibility: visible;
      }
    }

    .image-container__info {
      position: absolute;
      bottom: 0px;
      right: 0px;
      visibility: hidden;
      padding: 3px;
      transition: 0s;
    }

    .image-container {
      border-radius: $border-radius-sm;
      height: $thumbnail-res;
      width: $thumbnail-res;
      display: flex;
      background-color: #1d1d25;
      border: 1px solid #72727a;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      &.center-content {
        align-items: center;
        justify-content: center;
        flex-direction: column;
      }

      &.signature-image {
        background-color: var(--light-text-color) !important;
      }

      img {
        max-width: 100%;
        // max-height: 100%;
        height: 100%;
        object-fit: contain !important;
      }
    }

    .delete-button {
      visibility: hidden;
      position: absolute;
      padding: 6px;
      top: 0px;
      right: 4px;
      cursor: pointer;
    }

    &:hover {
      .delete-button {
        visibility: visible;
      }
    }
  }
}
</style>
