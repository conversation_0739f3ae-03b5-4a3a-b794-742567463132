<template>
  <div class="driver-app-notification-history">
    <v-layout class="px-2 pt-2 pb-1">
      <v-flex md3 class="px">
        <DatePickerBasic
          :soloInput="true"
          @setEpoch="setStartEpoch"
          v-model="startEpoch"
          :labelName="'From Date'"
          :epochTime="startEpoch"
        >
        </DatePickerBasic>
      </v-flex>

      <v-flex md3 class="px">
        <DatePickerBasic
          @setEpoch="setEndEpoch"
          :soloInput="true"
          v-model="endEpoch"
          :labelName="'To Date'"
          :epochTime="endEpoch"
        >
        </DatePickerBasic>
      </v-flex>
      <v-spacer></v-spacer>
      <v-flex md3>
        <v-layout justify p-2>
          <v-btn
            class="view-details-button"
            depressed
            block
            @click="searchWithCurrentCriteria"
            >Search for Selected Dates
          </v-btn>
        </v-layout>
      </v-flex>
    </v-layout>

    <v-divider />
    <v-layout row wrap v-if="notificationListItems.length > 0" pa-3>
      <v-flex
        md12
        v-for="notificationItem in notificationListItems"
        :mt-2="notificationItem.differentDateFromPrevious"
        :key="notificationItem.id"
      >
        <v-layout class="eventlist-item pb-1">
          <span
            class="eventlist-item__date"
            :class="notificationItem.differentDateFromPrevious"
            ><span v-if="notificationItem.differentDateFromPrevious">{{
              notificationItem.readableDate
            }}</span></span
          >
          <span
            class="eventlist-item__time"
            :class="notificationItem.className"
            >{{ notificationItem.readableTime }}</span
          >
          <v-flex>
            <span class="eventlist-item__title">
              {{ notificationItem.eventTitle }} -
            </span>
            <span class="eventlist-item__username">
              {{ notificationItem.eventSubtitle }}
            </span>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
    <v-layout v-else class="subheader--faded pt-3" justify-center>
      No results found.
    </v-layout>
  </div>
</template>

<script setup lang="ts">
interface NotificationListItem {
  id: string;
  epochTime: number;
  readableTime: string;
  readableDate: string;
  notificationType: DriverNotificationType;
  eventTitle: string;
  eventSubtitle: string;
  className: string;
  differentDateFromPrevious: boolean;
}

import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import {
  returnEndOfDayFromEpoch,
  returnFormattedDate,
  returnFormattedTime,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { DriverNotificationType } from '@/interface-models/Generic/DriverLicenseTypes/DriverNotificationType';
import { DriverAppNotification } from '@/interface-models/Generic/DriverOnlineStatus/DriverAppNotification';
import { useDriverAppStore } from '@/store/modules/DriverAppStore';
import { Ref, onMounted, ref } from 'vue';

const props = withDefaults(
  defineProps<{
    driverId?: string;
  }>(),
  {
    driverId: '',
  },
);

const startEpoch: Ref<number> = ref(returnStartOfDayFromEpoch());
const endEpoch: Ref<number> = ref(returnEndOfDayFromEpoch());

const notificationListItems: Ref<NotificationListItem[]> = ref([]);

/**
 * Handle emit from DatePickerBasic component to set the start date
 */
function setStartEpoch(epoch: number) {
  startEpoch.value = returnStartOfDayFromEpoch(epoch);
}

/**
 * Handle emit from DatePickerBasic component to set the end date
 */
function setEndEpoch(epoch: number) {
  endEpoch.value = returnEndOfDayFromEpoch(epoch);
}

/**
 * Return a description of the notification type to be used in the template
 * @param type - The notification type
 */
function returnNotificationTypeDescription(
  type: DriverNotificationType,
): string {
  let subtitleString = '';
  switch (type) {
    case DriverNotificationType.LOG_IN:
      subtitleString = 'Driver logged into the mobile app';
      break;
    case DriverNotificationType.LOG_OUT:
      subtitleString = 'Driver logged out of the mobile app';
      break;
    case DriverNotificationType.AUTHENTICATE:
      subtitleString =
        'Driver has re-opened and re-authenticated after closing the app';
      break;
    case DriverNotificationType.REFRESH_TOKEN:
      subtitleString = 'Driver current logged-in session has been extended';
      break;
    case DriverNotificationType.START_DAY:
      subtitleString = 'Driver has started work for the day';
      break;
    case DriverNotificationType.END_DAY:
      subtitleString = 'Driver has ended work for the day';
      break;
    case DriverNotificationType.FORCE_CLOSE:
      subtitleString =
        'The connection to the server was lost. This indicates FORCE CLOSING, AIRPLANE MODE or LOSS OF RECEPTION.';
      break;
    case DriverNotificationType.GPS_DISABLED:
      subtitleString = `GPS tracking was disabled on the driver's mobile app`;
      break;
    case DriverNotificationType.ONLINE:
      subtitleString = 'Driver online status changed to ONLINE';
      break;
    case DriverNotificationType.OFFLINE:
      subtitleString = 'Driver online status changed to OFFLINE';
      break;
    case DriverNotificationType.APP_RESTART:
      subtitleString = `The driver's mobile app was re-opened after closing it.`;
      break;
    case DriverNotificationType.PRECISE_LOCATION_DISABLED:
      subtitleString = `The driver has Precise Location disabled in the mobile app settings. Their captured location information may be less accurate.`;
      break;
    case DriverNotificationType.GPS_RESTRICTED:
      subtitleString = `GPS tracking has been restricted on the driver's mobile app. Their captured location information may be inconsistent.`;
      break;
  }
  return subtitleString;
}

/**
 * Dispatch search using current selected categories and input values
 */
async function searchWithCurrentCriteria() {
  // Return if dates are somehow unset or if startEpoch is greater or equal to endEpoch
  if (
    !startEpoch.value ||
    !endEpoch.value ||
    startEpoch.value >= endEpoch.value
  ) {
    showNotification('Please select a valid date, then try again.');
    return;
  }
  // Return if dates are somehow unset
  if (!props.driverId) {
    showNotification('Please ensure you have a valid Driver selected.');
    return;
  }
  // Send request and await results
  const results = await useDriverAppStore().getDriverAppNotificationHistory({
    startEpoch: startEpoch.value,
    endEpoch: endEpoch.value,
    driverId: props.driverId,
  });
  // Handle results, or show notification if no results were found
  if (results && results.length > 0) {
    processDriverAppNotificationResponse(
      results.filter((n) => n.notificationType !== null),
    );
  } else {
    showNotification('No results were found for the supplied date.');
  }
}

/**
 * Init dates to start and end of today, then make initial default call for
 * notification history data
 */
function requestInitialData() {
  startEpoch.value = returnStartOfDayFromEpoch();
  endEpoch.value = returnEndOfDayFromEpoch();
  searchWithCurrentCriteria();
}

/**
 * Accepts list from API response and maps to a list of items to be iterated and
 * displayed in HTML
 * @param items - List of DriverAppNotification objects
 */
function constructNotificationListItems(
  items: DriverAppNotification[],
): NotificationListItem[] {
  const eventList: NotificationListItem[] = [];

  const returnTitle = (type: DriverNotificationType) =>
    type !== DriverNotificationType.FORCE_CLOSE
      ? DriverNotificationType[type].replace(/_/g, ' ')
      : 'CONNECTION LOST';

  for (let i = 0; i < items.length; i++) {
    const jobEvent: DriverAppNotification = items[i];

    const eventListItem: NotificationListItem = {
      id: jobEvent._id,
      epochTime: jobEvent.epochTime,
      readableTime: returnFormattedTime(jobEvent.epochTime),
      readableDate: returnFormattedDate(jobEvent.epochTime),
      notificationType: jobEvent.notificationType,
      eventTitle: returnTitle(jobEvent.notificationType), // Removed underscores from enum values to use as title
      eventSubtitle: returnNotificationTypeDescription(
        jobEvent.notificationType,
      ), // Returns a description of the event to be used as subtitle
      className:
        DriverNotificationType[jobEvent.notificationType].toLowerCase(), // used in CSS
      differentDateFromPrevious: false,
    };
    eventList.push(eventListItem);
  }
  return eventList;
}

// Handle response from search API
function processDriverAppNotificationResponse(
  driverAppNotificationList: DriverAppNotification[],
) {
  const notificationList = constructNotificationListItems(
    driverAppNotificationList,
  );
  let currentDateString: string = '';
  for (let i = 0; i < notificationList.length; i++) {
    const item = notificationList[i];
    if (i === 0) {
      item.differentDateFromPrevious = true;
      currentDateString = item.readableDate;
    } else {
      if (item.readableDate !== currentDateString) {
        item.differentDateFromPrevious = true;
        currentDateString = item.readableDate;
      } else {
        item.differentDateFromPrevious = false;
      }
    }
  }
  notificationListItems.value = notificationList;
}

/**
 * On component mount, request initial data
 */
onMounted(() => {
  requestInitialData();
});
</script>
<style scoped lang="scss">
.driver-app-notification-history {
  .eventlist-item {
    font-size: $font-size-12;
    color: rgb(205, 208, 220);
    &.with-hover {
      &:hover {
        filter: brightness(115%);
        background-color: $app-dark-primary-600;
        cursor: pointer;
      }
    }

    &.dense-list {
      font-size: $font-size-11;
      padding: 2px 0px;
    }

    &--selected {
      background-color: #484858;
    }
    .eventlist-item__time {
      padding: 2px 4px;
      font-size: $font-size-11;
      border-radius: 10px;
      font-weight: 600;
      color: white;
      margin-left: 6px;
      margin-right: 6px;
      position: relative;
      white-space: nowrap;

      &.online {
        background-color: $success;
      }
      &.offline {
        border: 1px dotted $average;
      }
      &.force_close {
        background-color: $error-type;
      }

      &.authenticate,
      &.refresh_token,
      &.log_in,
      &.log_out,
      &.start_day,
      &.end_day {
        // background-color: green;
        border: 1px solid rgb(98, 121, 174);
      }
    }

    .eventlist-item__date {
      color: grey;
      min-width: 55px;
      padding: 2px 4px;
      font-size: $font-size-11;
      font-weight: 600;
      text-align: right;
    }

    .eventlist-item__title {
      // padding-left: 3px;
      text-transform: uppercase;
      font-weight: 600;
      color: white;
    }
    .eventlist-item__username {
      padding-left: 3px;
    }
  }
}
</style>
