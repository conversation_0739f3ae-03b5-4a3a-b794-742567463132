<template>
  <v-layout wrap>
    <v-flex md12 v-if="rateDetails && !isTripRate">
      <v-layout
        column
        v-if="rateDetails.rateTypeId === 1 && rateDetails.timeReturnLegs"
      >
        <div>
          <span class="rate-title pr-1">Rate:</span>
          <span class="rate-value">
            ${{ addPercentageTo(baseRate, rateVariation ?? 0) }}
          </span>
        </div>
        <div>
          <span class="rate-title pr-1">First Leg:</span>
          <span class="rate-value">
            {{ rateDetails.timeReturnLegs.firstLeg }}</span
          >
        </div>
        <div>
          <span class="rate-title pr-1">Last Leg:</span>
          <span class="rate-value">
            {{ rateDetails.timeReturnLegs.lastLeg }}</span
          >
        </div>
      </v-layout>

      <v-layout v-if="rateDetails.rateTypeId === 2">
        <v-flex md12 v-if="rateEntityType === 'CLIENT'">
          <v-layout
            v-for="(zoneRate, zoneRateIndex) of zoneRates"
            :key="zoneRateIndex"
            wrap
          >
            <v-flex>
              <span style="display: block" class="rate-header pr-2"
                >{{ zoneRate.zoneName }} -
              </span>
            </v-flex>
            <v-flex>
              <span style="display: block"
                ><span class="rate-title">Rate:</span> ${{
                  addPercentageTo(zoneRate.rate, rateVariation ?? 0)
                }}
              </span>
              <span style="display: block"
                ><span class="rate-title">Pickup:</span> ${{
                  addPercentageTo(
                    zoneRate.additionalPickUpFlagFall,
                    rateVariation ?? 0,
                  )
                }}
              </span>
              <span style="display: block">
                <span class="rate-title">Dropoff:</span>
                ${{
                  addPercentageTo(
                    zoneRate.additionalDropOffFlagFall,
                    rateVariation ?? 0,
                  )
                }}
              </span>
            </v-flex>
            <v-flex
              md12
              v-if="
                zoneRateIndex !==
                (unitRateInformation.length
                  ? unitRateInformation.length - 1
                  : 0)
              "
            >
              <v-divider class="my-1" />
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12 v-if="rateEntityType === 'FLEET_ASSET'">
          <v-layout
            v-for="(zoneRate, zoneRateIndex) of zoneRates"
            :key="zoneRateIndex"
          >
            <v-flex>
              <span
                ><span class="rate-title">Rate:</span>
                {{ zoneRate.percentage }} %
              </span>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>

      <div v-if="rateDetails.rateTypeId === JobRateType.DISTANCE">
        <table>
          <tr>
            <th colspan="2" style="text-align: center">DISTANCE RATE</th>
          </tr>
          <tr v-for="line in distanceRateInformation" :key="line.title">
            <td
              class="rate-title"
              style="text-align: left; vertical-align: top"
            >
              {{ line.title }}
            </td>
            <td
              class="rate-value"
              style="text-align: right; vertical-align: top"
            >
              {{ line.value }}
            </td>
          </tr>
        </table>
      </div>
      <v-layout
        v-if="rateDetails.rateTypeId === JobRateType.ZONE_TO_ZONE"
        row
        wrap
        style="max-width: 900px"
      >
        <v-flex
          md4
          v-for="(zone, index) in zoneToZoneRateInformation"
          :key="`${zone.zoneName}-${index}`"
          pa-1
        >
          <table>
            <tr>
              <th colspan="2" style="text-align: center">
                {{ zone.zoneName }}
              </th>
            </tr>
            <tr v-for="item in zone.items" :key="item.title">
              <td
                class="rate-title"
                style="text-align: left; vertical-align: top"
              >
                {{ item.title }}
              </td>
              <td
                class="rate-value"
                style="text-align: right; vertical-align: top"
              >
                {{ item.value }}
              </td>
            </tr>
          </table>
        </v-flex>
      </v-layout>

      <v-layout wrap v-if="rateDetails.rateTypeId === JobRateType.UNIT">
        <v-flex md12 v-if="rateEntityType === 'CLIENT'">
          <v-layout wrap>
            <v-flex
              style="max-width: 420px"
              v-for="(unitRate, unitRateIndex) of unitRateInformation"
              :key="unitRateIndex"
              class="pr-2"
            >
              <v-layout>
                <v-flex>
                  <span style="display: block" class="rate-header pr-2"
                    >{{ unitRate.zoneName }} -
                  </span>
                </v-flex>
                <v-flex>
                  <span style="display: block"
                    ><span class="rate-title">Unit:</span>
                    {{ unitRate.unitName }}
                  </span>
                  <span style="display: block"
                    ><span class="rate-title">Rate:</span> ${{ unitRate.rate }}
                  </span>
                  <span style="display: block"
                    ><span class="rate-title">Minimum:</span>
                    {{ unitRate.minimum }}
                  </span>
                  <span style="display: block"
                    ><span class="rate-title">Forklift:</span>
                    {{ unitRate.forklift }}
                  </span>
                  <span style="display: block"
                    ><span class="rate-title">Forklift Pickup:</span> ${{
                      unitRate.pickupFlagfallForklift
                    }}
                  </span>
                  <span style="display: block"
                    ><span class="rate-title">Forklift Dropoff:</span> ${{
                      unitRate.dropoffFlagfallForklift
                    }}
                  </span>
                  <span style="display: block"
                    ><span class="rate-title">Hand Pickup:</span> ${{
                      unitRate.pickupFlagfall
                    }}
                  </span>
                  <span style="display: block"
                    ><span class="rate-title">Hand Dropoff:</span> ${{
                      unitRate.dropoffFlagfall
                    }}
                  </span>
                  <span style="display: block"
                    ><span class="rate-title">DG:</span>
                    {{ unitRate.isDangerousGoods }}
                  </span>
                  <span style="display: block"
                    ><span class="rate-title">DG Flagfall:</span>
                    {{ unitRate.dangerousGoodsFlagfall }}
                  </span>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12 v-if="rateEntityType === 'FLEET_ASSET'">
          <v-layout wrap>
            <v-flex
              style="max-width: 220px"
              v-for="(unitRate, unitRateIndex) of unitRateInformation"
              :key="unitRateIndex"
              class="pr-2"
            >
              <v-layout>
                <v-flex>
                  <span style="display: block"
                    ><span class="rate-title">Rate:</span>
                    {{
                      addPercentageTo(
                        unitRate.fleetAssetPercentage,
                        fleetRateVariation ?? 0,
                      )
                    }}%
                  </span>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>

      <v-layout wrap v-if="rateDetails.rateTypeId === 4">
        <v-layout
          wrap
          v-for="(p2pRate, p2pRateIndex) of filteredPointToPointInformation"
          :key="p2pRateIndex"
          class="pr-2"
        >
          <v-flex>
            <span style="display: block" class="rate-header pr-2">P2p -</span>
          </v-flex>
          <v-flex>
            <span style="display: block"
              ><span class="rate-title">Rate:</span> ${{ p2pRate?.rate }}
            </span>
            <span style="display: block"
              ><span class="rate-title">{{ p2pRate?.fromNickname }}:</span>
              {{ p2pRate?.fromAddress }}
            </span>
            <span style="display: block"
              ><span class="rate-title">{{ p2pRate?.toNickname }}:</span>
              {{ p2pRate?.toAddress }}
            </span>
          </v-flex>

          <v-flex
            md12
            v-if="p2pRateIndex !== pointToPointInformation.length - 1"
          >
            <v-divider class="my-1" />
          </v-flex>
        </v-layout>
      </v-layout>
    </v-flex>

    <v-flex md12 v-if="isTripRate">
      <span> Quoted Rate </span>
    </v-flex>
    <v-flex md12 v-if="serviceRateCard">
      <v-layout row wrap>
        <v-flex md12>
          <v-layout>
            <h5 class="subheader--bold white--text">
              <span style="text-transform: none"
                >Current Division Rate Card:</span
              >
              <span class="px-1">{{ serviceRateCard.name }}</span>
              (valid
              {{ returnFormattedDate(serviceRateCard.validFromDate ?? 0) }} -
              {{ returnFormattedDate(serviceRateCard.validToDate ?? 0) }})
            </h5>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import {
  addPercentageTo,
  DisplayCurrencyValue,
} from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import { returnDemurrageDescription } from '@/helpers/RateHelpers/DemurrageHelpers';
import {
  returnChargeIncrementDescription,
  returnMinimumChargeDescription,
  returnRangeRateSummary,
} from '@/helpers/RateHelpers/DistanceRateHelpers';
import {
  isDistanceRateTypeObject,
  isZoneToZoneRateTypeObject,
} from '@/helpers/RateHelpers/RateTableItemHelpers';
import { returnZoneToZoneName } from '@/helpers/RateHelpers/ZoneToZoneRateHelpers';
import { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { returnStartAndReturnLegsLongNameById } from '@/interface-models/Generic/StartAndReturnLegs/StartAndReturnLegs';
import applicableFuelSurcharges from '@/interface-models/ServiceRates/FuelSurcharges/applicableFuelSurcharges';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import ServiceRateCard from '@/interface-models/ServiceRates/ServiceRateCard';
import { returnReadableChargeBasisName } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/ChargeBasis';
import { returnReadableRateBracketTypeName } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/RateBracketType';
import PointToPointRateType from '@/interface-models/ServiceRates/ServiceTypes/PointToPointServiceRate/PointToPointRateType';
import UnitRate from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRate';
import { ZoneRateType } from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRateType';
import { ZoneToZoneRateType } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneToZoneRateType';
import { computed, ComputedRef } from 'vue';

// Used when we should display all information in provided ServiceRateCard
// (such as when showing division rates)
const props = withDefaults(
  defineProps<{
    rateEntityType?: RateEntityType;
    appliedRate?: RateTableItems | null;
    rateVariation?: number | null;
    fleetRateVariation?: number | null;
    serviceRateTable?: ServiceRateCard | null;
    isTripRate?: boolean;
    clientCommonAddresses?: ClientCommonAddress[];
  }>(),
  {
    rateEntityType: RateEntityType.CLIENT,
    serviceRateTable: null,
    isTripRate: false,
    clientCommonAddresses: () => [],
    appliedRate: null,
    rateVariation: null,
    fleetRateVariation: null,
  },
);

// get bae rate
const baseRate = computed<number>(() => {
  const rateType = props.appliedRate?.rateTypeObject;
  if (rateType && !Array.isArray(rateType) && 'rate' in rateType) {
    return rateType.rate ?? 0;
  }
  return 0;
});

// create rate details with updated rate with variation
const rateDetails: ComputedRef<RateTableItems | null> = computed(() => {
  if (!props.appliedRate) {
    return null;
  }
  return Object.assign(new RateTableItems(), props.appliedRate);
});

const serviceRateCard: ComputedRef<ServiceRateCard | null> = computed(() => {
  if (!props.serviceRateTable) {
    return null;
  }
  const serviceRateCard = Object.assign(
    new ServiceRateCard(),
    props.serviceRateTable,
  );
  serviceRateCard.rateTableItems = serviceRateCard.rateTableItems.map((r) =>
    Object.assign(new RateTableItems(), r),
  );
  return serviceRateCard;
});

const unitRateInformation = computed(() => {
  const rateDetailsValue = rateDetails.value;
  if (!rateDetailsValue || !rateDetailsValue.rateTypeObject) {
    return [];
  }

  if (rateDetailsValue.rateTypeId === 5) {
    return (rateDetailsValue.rateTypeObject as UnitRate[]).map((unitRate) => {
      const rates = unitRate.unitRanges.map((rate) => rate.unitRate);
      const highestRate = Math.max(...rates);
      const rate = addPercentageTo(highestRate, props.rateVariation ?? 0);
      const isRangeRateText = unitRate.isRangeRate
        ? ' (Range rate applies)'
        : '';
      return {
        zoneName: unitRate.zoneName,
        unitName: unitRate.unitTypeId === 1 ? unitRate.unitTypeName : 'KG',
        isDangerousGoods: unitRate.isDangerousGoods ? 'YES' : 'NO',
        dangerousGoodsFlagfall: DisplayCurrencyValue(
          unitRate.dangerousGoodsFlagFall,
        ),
        rate: rate + isRangeRateText,
        minimum: unitRate.minimumUnits,
        forklift: unitRate.forkLiftRequired ? 'Required' : 'N/A',
        pickupFlagfallForklift: addPercentageTo(
          unitRate.pickUpFlagFallForkLift,
          props.rateVariation ?? 0,
        ),
        dropoffFlagfallForklift: addPercentageTo(
          unitRate.dropOffFlagFallForkLift,
          props.rateVariation ?? 0,
        ),
        pickupFlagfall: addPercentageTo(
          unitRate.pickUpFlagFallHand,
          props.rateVariation ?? 0,
        ),
        dropoffFlagfall: addPercentageTo(
          unitRate.dropOffFlagFallHand,
          props.rateVariation ?? 0,
        ),
        fleetAssetPercentage: unitRate.fleetAssetPercentage
          ? unitRate.fleetAssetPercentage
          : 0,
      };
    });
  }
  return [];
});

/**
 * Used in the template to display a list of details about the rate applied to a
 * distance rate job.
 */
const distanceRateInformation: ComputedRef<{ title: string; value: string }[]> =
  computed(() => {
    const rateDetailsValue = rateDetails.value;
    if (!rateDetailsValue || !rateDetailsValue.rateTypeObject) {
      return [];
    }
    if (
      isDistanceRateTypeObject(
        rateDetailsValue.rateTypeId,
        rateDetailsValue.rateTypeObject,
      )
    ) {
      try {
        const distanceRateType = rateDetailsValue.rateTypeObject;
        const calculation = `${returnReadableChargeBasisName(
          distanceRateType.chargeBasis,
        )} (${returnReadableRateBracketTypeName(
          distanceRateType.rateBracketType,
        )})`;

        const baseFreight = `$${addPercentageTo(
          distanceRateType.baseFreightCharge ?? 0,
          props.rateVariation ?? 0,
        )}`;

        const minCharge = returnMinimumChargeDescription(distanceRateType);

        const chargeIncrement =
          returnChargeIncrementDescription(distanceRateType);

        const additionalTimes = `${
          returnStartAndReturnLegsLongNameById(
            distanceRateType.firstLegTypeId,
          ) || 'N/A'
        } / ${
          returnStartAndReturnLegsLongNameById(
            distanceRateType.lastLegTypeId,
          ) || 'N/A'
        }`;

        const fuelLevy =
          applicableFuelSurcharges.find(
            (x) => x.id === distanceRateType.appliedFuelSurchargeId,
          )?.shortName || '-';

        const ranges = distanceRateType.rates.map((rate, index) => {
          return {
            title: `Range ${index + 1} - ${returnRangeRateSummary(
              rate,
              index,
            )}`,
            value: `$${DisplayCurrencyValue(rate.rate)}/km`,
          };
        });

        return [
          {
            title: `Calculation`,
            value: `${calculation}`,
          },
          {
            title: `Base Freight`,
            value: `${baseFreight}`,
          },
          {
            title: `Minimum Charge`,
            value: `${minCharge}`,
          },
          {
            title: `Charge Increment`,
            value: `${chargeIncrement}`,
          },
          {
            title: `Additional Times`,
            value: `${additionalTimes}`,
          },
          { title: `Fuel Levy`, value: `${fuelLevy}` },
          ...ranges,
          ...returnDemurrageDescription(distanceRateType.demurrage),
        ];
      } catch (e) {
        logConsoleError(
          'Error generating tooltip list in AppliedRateDetails',
          rateDetailsValue,
        );
        return [];
      }
    }
    return [];
  });

/**
 * Used in the template to display a list of details about the rate applied to a
 * ZONE TO ZONE rate job.
 */
const zoneToZoneRateInformation: ComputedRef<
  {
    zoneName: string;
    items: { title: string; value: string }[];
  }[]
> = computed(() => {
  const rateDetailsValue = rateDetails.value;
  if (!rateDetailsValue || !rateDetailsValue.rateTypeObject) {
    return [];
  }
  if (
    typeof rateDetailsValue.rateTypeObject !== 'string' &&
    isZoneToZoneRateTypeObject(
      rateDetailsValue.rateTypeId,
      rateDetailsValue.rateTypeObject,
    )
  ) {
    try {
      const rateTypes: ZoneToZoneRateType[] = rateDetailsValue.rateTypeObject;
      return rateTypes.map((rateType) => {
        const zoneName = returnZoneToZoneName(rateType);
        const pickupLocation = `${rateType.pickupLocation.suburb} ${rateType.pickupLocation.postcode}`;
        const deliveryLocation = `${rateType.deliveryLocation.suburb} ${rateType.deliveryLocation.postcode}`;
        const rate = rateType.fleetAssetPercentage
          ? `${addPercentageTo(
              rateType.fleetAssetPercentage,
              props.fleetRateVariation ?? 0,
            )}%`
          : `$${addPercentageTo(rateType.rate, props.rateVariation ?? 0)}`;
        const fuelLevy =
          applicableFuelSurcharges.find(
            (x) => x.id === rateType.appliedFuelSurchargeId,
          )?.shortName || '-';

        return {
          zoneName: zoneName,
          items: [
            {
              title: 'Rate',
              value: rate,
            },
            {
              title: 'Pickup Location',
              value: pickupLocation,
            },
            {
              title: 'Delivery Location',
              value: deliveryLocation,
            },
            {
              title: 'Fuel Levy',
              value: fuelLevy,
            },
            ...returnDemurrageDescription(rateType.demurrage),
          ],
        };
      });
    } catch (e) {
      logConsoleError(
        'Error generating tooltip list in AppliedRateDetails',
        rateDetailsValue,
      );
      return [];
    }
  }
  return [
    {
      zoneName: 'TBD',
      items: [],
    },
  ];
});

const zoneRates = computed(() => {
  const rateTypeObject = rateDetails.value?.rateTypeObject;
  if (!rateTypeObject) {
    return [];
  }
  return rateTypeObject as ZoneRateType[];
});

const filteredPointToPointInformation = computed(() => {
  return pointToPointInformation.value.filter((item) => item !== undefined);
});

const pointToPointInformation = computed(() => {
  const rateDetailsValue = rateDetails.value;
  if (!rateDetailsValue || !rateDetailsValue.rateTypeObject) {
    return [];
  }

  if (rateDetailsValue.rateTypeId === 4) {
    return (rateDetailsValue.rateTypeObject as PointToPointRateType[]).map(
      (p2p) => {
        const fromAddress = props.clientCommonAddresses.find(
          (x) => x._id === p2p.fromAddressReference,
        );
        const toAddress = props.clientCommonAddresses.find(
          (x) => x._id === p2p.toAddressReference,
        );
        if (!fromAddress || !toAddress) {
          return;
        }
        return {
          fromNickname: fromAddress.addressNickname,
          toNickname: toAddress.addressNickname,
          fromAddress: fromAddress.address.formattedAddress,
          toAddress: toAddress.address.formattedAddress,
          rate: DisplayCurrencyValue(p2p.rate),
        };
      },
    );
  }
  return [];
});
</script>

<style scoped lang="scss">
p {
  display: block !important;
}

.rate-title {
  color: #bcbcbe;
  text-transform: uppercase;
  font-size: $font-size-11;
  font-weight: 600;
}

.rate-value {
  color: #e4e4f0;
  font-size: $font-size-11;
}

.rate-header {
  color: #49b9ff;
  text-transform: uppercase;
  font-size: $font-size-11;
  font-weight: 600;
}
</style>
