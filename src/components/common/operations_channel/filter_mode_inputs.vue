<template>
  <v-select
    class="v-solo-custom"
    solo
    flat
    :items="filterModeItems"
    item-value="id"
    item-text="name"
    label="Label Name"
    v-model="valueModel"
    :hint="hintText"
    persistent-hint
    :disabled="disabled"
  ></v-select>
</template>

<script setup lang="ts">
import { SelectOption } from '@/interface-models/Generic/ShortLongName/SelectOption';
import { FilterMode } from '@/interface-models/OperationsChannels/FilterByValues';
import {
  ComputedRef,
  Ref,
  WritableComputedRef,
  computed,
  onBeforeMount,
  ref,
} from 'vue';

const props = defineProps<{
  value: FilterMode;
  disabled: boolean;
  allowNoneOption: boolean;
}>();

const emit = defineEmits<{
  (event: 'input', payload: FilterMode): void;
}>();

// Items for mode select
const filterModeItems: Ref<SelectOption<FilterMode>[]> = ref([
  { id: FilterMode.ANY, name: 'All visible' },
  { id: FilterMode.ONLY, name: 'Show only...' },
]);

const hintText: ComputedRef<string> = computed(() => {
  switch (valueModel.value) {
    case FilterMode.ANY:
      return 'All items visible (default)';
    case FilterMode.ONLY:
      return 'Only display specific items (select below)';
    case FilterMode.NONE:
      return 'No items from this category will be visible';
    default:
      return '';
  }
});

const valueModel: WritableComputedRef<FilterMode> = computed({
  get(): FilterMode {
    return props.value;
  },
  set(value: FilterMode): void {
    emit('input', value);
  },
});

onBeforeMount(() => {
  // If none option is not allowed, and current value is NONE, change to ANY
  if (!props.allowNoneOption && valueModel.value === FilterMode.NONE) {
    valueModel.value = FilterMode.ANY;
  }
  if (props.allowNoneOption) {
    filterModeItems.value.push({
      id: FilterMode.NONE,
      name: 'Hide all in this group',
    });
  }
});
</script>
