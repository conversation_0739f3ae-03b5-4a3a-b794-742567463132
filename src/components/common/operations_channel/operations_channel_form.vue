<template>
  <v-layout>
    <v-flex md12 v-if="localOperationsChannel !== null">
      <v-form ref="operationsChannelForm">
        <!-- =========== -->
        <!-- Job Filters -->
        <!-- =========== -->

        <v-layout column px-2>
          <template v-if="!isDashboardView">
            <v-layout align-center>
              <v-layout column>
                <h3>
                  {{ localOperationsChannel.isNew ? 'New ' : 'Edit ' }}
                  Operations Channel
                </h3>
                <h4 v-if="localOperationsChannel.isNew">
                  Fill in the details below to create a new operations channel.
                </h4>
                <h4 v-else>
                  Modify the details below to update the filter options of
                  Channel
                  {{ localOperationsChannel.name }}.
                </h4>
              </v-layout>
            </v-layout>
            <v-divider class="my-2"></v-divider>
            <template v-if="isAuthorised">
              <v-layout justify-start>
                <h6 class="form-subheader form-field-required-marker">
                  Channel Access:
                </h6>
              </v-layout>
              <v-layout>
                <v-flex m8>
                  <v-select
                    v-model="channelLevelController"
                    :items="channelLevelOptions"
                    item-text="name"
                    item-value="id"
                    class="v-solo-custom"
                    :rules="[validationRules.required]"
                    :disabled="
                      isFormDisabled ||
                      !!localOperationsChannel._id ||
                      !isCustomOperationsChannelAllowed
                    "
                    persistent-hint
                    hint="Select who you want to have access to this channel"
                    solo
                    flat
                  />
                </v-flex>
                <v-flex md4 pl-3>
                  <v-switch
                    class="mt-0"
                    label="Default channel"
                    v-model="localOperationsChannel.isDefaultChannel"
                    :disabled="isDefaultChannelSwitchDisabled"
                    :hint="
                      defaultChannelId &&
                      localOperationsChannel?._id !== defaultChannelId
                        ? 'A default channel already exists for this division. '
                        : 'When enabled, this channel will be the default view for all users in the division.'
                    "
                    persistent-hint
                  >
                  </v-switch>
                </v-flex>
              </v-layout>
            </template>
          </template>

          <template
            v-if="!isDashboardView || (isDashboardView && !isFormDisabled)"
          >
            <v-layout justify-start>
              <h6 class="form-subheader form-field-required-marker">Name:</h6>
            </v-layout>
            <v-text-field
              v-model="localOperationsChannel.name"
              label="Channel Name"
              hint="Channel Name"
              :rules="[validationRules.required]"
              required
              class="v-solo-custom"
              flat
              solo
              :disabled="isFormDisabled"
              persistent-hint
            ></v-text-field>
          </template>
        </v-layout>

        <v-layout align-center>
          <h5 class="subheader--bold form-header">Job Filters</h5>
          <v-divider class="mx-3 mt-1" />
          <span class="pl-2" v-if="!isFormDisabled">
            <InformationTooltip :right="true" tooltipType="info">
              <v-layout slot="content" row wrap>
                <v-flex md12>
                  <p class="mb-1">
                    Choose your preferred filter for jobs that appear on the
                    dashboard. By default,
                    <strong>all jobs are visible</strong>.
                  </p>
                  <p class="mb-0">
                    Customise your view to
                    <strong>focus on specific jobs</strong>, such as jobs for a
                    specific client, national client group, or service type.
                  </p>
                </v-flex>
              </v-layout>
            </InformationTooltip>
          </span>
        </v-layout>
        <v-layout column px-2>
          <v-layout wrap>
            <span
              v-for="filter in jobFilterOptions"
              class="tags-chip mx-1 selectable"
              :class="{
                disabled: isFormDisabled,
                selected: selectedJobFilterOptions.includes(filter.id),
              }"
              :key="filter.id"
              @click="
                toggleFilterOptions({
                  type: ChannelFilterType.JOB,
                  option: filter.id,
                })
              "
            >
              <div class="chip-contents">
                <span>{{ filter.name }}</span>
                <span class="subheader">
                  {{ filter.statusText() }}
                </span>
              </div>
            </span>
          </v-layout>

          <v-layout
            column
            v-for="config in jobFilterConfig"
            :key="config.optionId"
          >
            <template v-if="selectedJobFilterOptions.includes(config.optionId)">
              <h6 class="form-subheader">{{ config.label }}</h6>

              <FilterModeInputs
                v-model="config.model().mode"
                :disabled="isFormDisabled"
                :allowNoneOption="config.allowNoneOption"
              />

              <v-autocomplete
                v-if="config.model().requiresValues"
                v-model="config.model().values"
                :items="config.items"
                item-text="name"
                item-value="id"
                class="v-solo-custom"
                multiple
                chips
                small-chips
                persistent-hint
                :hint="config.hint"
                :rules="[validationRules.listRequired]"
                label="Select one or more items"
                :disabled="isFormDisabled"
                solo
                flat
              />
            </template>
          </v-layout>
        </v-layout>
        <!-- ============= -->
        <!-- Fleet Filters -->
        <!-- ============= -->
        <v-layout align-center>
          <h5 class="subheader--bold form-header">Fleet List Grouping</h5>
          <v-divider class="mx-3 mt-1" />
          <span class="pl-2" v-if="!isFormDisabled">
            <InformationTooltip :right="true" tooltipType="info">
              <v-layout slot="content" row wrap>
                <v-flex md12>
                  <p class="mb-1">
                    Choose your preferred grouping schemes in the dashboard
                    Fleet List. By default,
                    <strong>all groups are visible</strong>.
                  </p>
                  <p class="mb-1">
                    When multiple groups are visible, some Vehicles and Drivers
                    will appear in the list multiple times.
                  </p>
                  <p class="mb-0">
                    Customise your view to
                    <strong> focus on specific Vehicles and Drivers </strong>.
                  </p>
                </v-flex>
              </v-layout>
            </InformationTooltip>
          </span>
        </v-layout>

        <v-layout column px-2>
          <v-layout wrap>
            <span
              v-for="filter in fleetFilterOptions"
              class="tags-chip ma-1 selectable"
              :class="{
                disabled: isFormDisabled,
                selected: selectedFleetFilterOptions.includes(filter.id),
              }"
              :key="filter.id"
              @click="
                toggleFilterOptions({
                  type: ChannelFilterType.FLEET,
                  option: filter.id,
                })
              "
            >
              <div class="chip-contents">
                <span>{{ filter.name }}</span>
                <span class="subheader">
                  {{ filter.statusText() }}
                </span>
              </div>
            </span>
          </v-layout>
          <v-layout
            column
            v-for="config in fleetFilterConfig"
            :key="config.optionId"
          >
            <template
              v-if="selectedFleetFilterOptions.includes(config.optionId)"
            >
              <h6 class="form-subheader">{{ config.label }}</h6>

              <FilterModeInputs
                v-model="config.model().mode"
                :disabled="isFormDisabled"
                :allowNoneOption="config.allowNoneOption"
              />
              <v-autocomplete
                v-if="config.model().requiresValues"
                v-model="config.model().values"
                :items="config.items"
                item-text="name"
                item-value="id"
                multiple
                chips
                class="v-solo-custom"
                small-chips
                :rules="[validationRules.listRequired]"
                persistent-hint
                :hint="config.hint"
                :disabled="isFormDisabled"
                label="Select one or more items"
                solo
                flat
              />
            </template>
          </v-layout>
          <template
            v-if="
              selectedFleetFilterOptions.includes(
                ChannelFleetFilterOptions.INCLUDE_EXCLUDE_FLEET,
              )
            "
          >
            <h6 class="form-subheader">Include/Exclude Vehicles</h6>
            <v-autocomplete
              v-model="
                localOperationsChannel.fleetFilterOptions.includeFleetAssetIds
              "
              :items="fleetAssetOptions"
              item-text="name"
              item-value="id"
              class="v-solo-custom"
              multiple
              chips
              small-chips
              persistent-hint
              prefix="Include:"
              hint="Selections will be visible regardless of other filter criteria"
              :disabled="isFormDisabled"
              solo
              flat
            >
            </v-autocomplete>
            <v-autocomplete
              v-model="
                localOperationsChannel.fleetFilterOptions.excludeFleetAssetIds
              "
              :items="fleetAssetOptions"
              item-text="name"
              item-value="id"
              class="v-solo-custom"
              multiple
              chips
              small-chips
              persistent-hint
              prefix="Exclude:"
              hint="Selections will be hidden regardless of other filter criteria"
              :disabled="isFormDisabled"
              solo
              flat
            >
            </v-autocomplete>
          </template>
          <template
            v-if="
              selectedFleetFilterOptions.includes(
                ChannelFleetFilterOptions.INCLUDE_EXCLUDE_DRIVERS,
              )
            "
          >
            <h6 class="form-subheader">Include/Exclude Drivers</h6>
            <v-autocomplete
              v-model="
                localOperationsChannel.fleetFilterOptions.includeDriverIds
              "
              :items="driverOptions"
              item-text="name"
              item-value="id"
              class="v-solo-custom"
              multiple
              chips
              small-chips
              persistent-hint
              prefix="Include:"
              hint="Selections will be visible regardless of other filter criteria"
              :rules="[validationRules.listRequired]"
              :disabled="isFormDisabled"
              solo
              flat
            >
            </v-autocomplete>
            <v-autocomplete
              v-model="
                localOperationsChannel.fleetFilterOptions.excludeDriverIds
              "
              :items="driverOptions"
              item-text="name"
              item-value="id"
              class="v-solo-custom"
              multiple
              chips
              small-chips
              persistent-hint
              prefix="Exclude:"
              hint="Selections will be hidden regardless of other filter criteria"
              :rules="[validationRules.listRequired]"
              :disabled="isFormDisabled"
              solo
              flat
            >
            </v-autocomplete>
          </template>
        </v-layout>
      </v-form>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import {
  ChannelFilterType,
  ChannelFleetFilterOptions,
  ChannelJobFilterOptions,
  OperationsChannelLevel,
  toggleChannelFilterOption,
} from '@/helpers/AppLayoutHelpers/OperationsChannelHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import fleetAssetTypes from '@/interface-models/FleetAsset/static/FleetAssetTypes/FleetAssetTypes';
import affiliationTypes from '@/interface-models/Generic/Affiliation/Affiliations';
import serviceTypeRates from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { SelectOption } from '@/interface-models/Generic/ShortLongName/SelectOption';
import { FilterByValues } from '@/interface-models/OperationsChannels/FilterByValues';
import { OperationsChannel } from '@/interface-models/OperationsChannels/OperationsChannel';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import {
  computed,
  ComputedRef,
  onBeforeMount,
  ref,
  Ref,
  WritableComputedRef,
} from 'vue';
import FilterModeInputs from './filter_mode_inputs.vue';
import { sessionManager } from '@/store/session/SessionState';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';

const props = withDefaults(
  defineProps<{
    operationsChannel: OperationsChannel;
    isFormDisabled?: boolean;
    isDashboardView?: boolean;
    defaultChannelId?: string;
  }>(),
  {
    isFormDisabled: false,
    isDashboardView: false,
    defaultChannelId: '',
  },
);

const clientDetailsStore = useClientDetailsStore();

const fleetAssetTypeOptions: Ref<SelectOption<number>[]> = ref([]);
const serviceTypeOptions: Ref<SelectOption<number>[]> = ref([]);
const rateTypeOptions: Ref<SelectOption<number>[]> = ref([]);
const vehicleClassOptions: Ref<SelectOption<string>[]> = ref([]);
const ownerAffiliationOptions: Ref<SelectOption<string>[]> = ref([]);

const clientSelectOptions: Ref<SelectOption<string>[]> = ref([]);
const nationalClientSelectOptions: Ref<SelectOption<string>[]> = ref([]);

const fleetAssetOptions: Ref<SelectOption<string>[]> = ref([]);
const driverOptions: Ref<SelectOption<string>[]> = ref([]);

const selectedJobFilterOptions: Ref<ChannelJobFilterOptions[]> = ref([]);
const selectedFleetFilterOptions: Ref<ChannelFleetFilterOptions[]> = ref([]);

const operationsChannelForm: Ref<any> = ref(null);

const localOperationsChannel: Ref<OperationsChannel | null> = ref(null);
const jobFilterOptions: ComputedRef<
  {
    id: ChannelJobFilterOptions;
    name: string;
    statusText: () => string;
  }[]
> = computed(() => [
  {
    id: ChannelJobFilterOptions.CLIENT,
    name: 'Client',
    statusText: () =>
      getFilterStatusText(
        ChannelFilterType.JOB,
        ChannelJobFilterOptions.CLIENT,
      ),
  },
  {
    id: ChannelJobFilterOptions.NATIONAL_CLIENT,
    name: 'National Client',
    statusText: () =>
      getFilterStatusText(
        ChannelFilterType.JOB,
        ChannelJobFilterOptions.NATIONAL_CLIENT,
      ),
  },
  {
    id: ChannelJobFilterOptions.SERVICE_TYPES,
    name: 'Service Types',
    statusText: () =>
      getFilterStatusText(
        ChannelFilterType.JOB,
        ChannelJobFilterOptions.SERVICE_TYPES,
      ),
  },
  {
    id: ChannelJobFilterOptions.RATE_TYPES,
    name: 'Rate Types',
    statusText: () =>
      getFilterStatusText(
        ChannelFilterType.JOB,
        ChannelJobFilterOptions.RATE_TYPES,
      ),
  },
]);

const fleetFilterOptions: ComputedRef<
  {
    id: ChannelFleetFilterOptions;
    name: string;
    statusText: () => string;
  }[]
> = computed(() => [
  // { id: ChannelFleetFilterOptions.FLEET_ASSET_TYPE, name: 'Fleet Asset Type', statusText: () => ... },
  {
    id: ChannelFleetFilterOptions.VEHICLE_CLASS,
    name: 'Vehicle Class',
    statusText: () =>
      getFilterStatusText(
        ChannelFilterType.FLEET,
        ChannelFleetFilterOptions.VEHICLE_CLASS,
      ),
  },
  {
    id: ChannelFleetFilterOptions.OWNER_AFFILIATION,
    name: 'Owner Affiliation',
    statusText: () =>
      getFilterStatusText(
        ChannelFilterType.FLEET,
        ChannelFleetFilterOptions.OWNER_AFFILIATION,
      ),
  },
  {
    id: ChannelFleetFilterOptions.NATIONAL_CLIENT,
    name: 'National Client',
    statusText: () =>
      getFilterStatusText(
        ChannelFilterType.FLEET,
        ChannelFleetFilterOptions.NATIONAL_CLIENT,
      ),
  },
  {
    id: ChannelFleetFilterOptions.CLIENT,
    name: 'Client',
    statusText: () =>
      getFilterStatusText(
        ChannelFilterType.FLEET,
        ChannelFleetFilterOptions.CLIENT,
      ),
  },
  {
    id: ChannelFleetFilterOptions.INCLUDE_EXCLUDE_FLEET,
    name: 'Include/Exclude Fleet',
    statusText: () =>
      getFilterStatusText(
        ChannelFilterType.FLEET,
        ChannelFleetFilterOptions.INCLUDE_EXCLUDE_FLEET,
      ),
  },
  {
    id: ChannelFleetFilterOptions.INCLUDE_EXCLUDE_DRIVERS,
    name: 'Include/Exclude Drivers',
    statusText: () =>
      getFilterStatusText(
        ChannelFilterType.FLEET,
        ChannelFleetFilterOptions.INCLUDE_EXCLUDE_DRIVERS,
      ),
  },
]);

const isAuthorised: ComputedRef<boolean> = computed(() => {
  // return false;
  return hasAdminOrHeadOfficeRole();
});

const isDefaultChannelSwitchDisabled: ComputedRef<boolean> = computed(() => {
  // If not authorized, or no local channel, or not division default
  if (
    !isAuthorised.value ||
    !localOperationsChannel.value ||
    !localOperationsChannel.value.isDivisionDefault
  ) {
    return true;
  }
  // If there is no default channel, allow switching
  if (!props.defaultChannelId) {
    return false;
  }
  // If this is the current default channel, allow switching
  if (localOperationsChannel.value._id === props.defaultChannelId) {
    return false;
  }
  return true;
});

/**
 * Returns a short, human-friendly status for a filter option (e.g. "All
 * visible", "Custom selection", "2 included"). Handles both job and fleet
 * filters, including special cases for include/exclude options.
 * @param
 */
function getFilterStatusText(
  type: ChannelFilterType,
  option: ChannelJobFilterOptions | ChannelFleetFilterOptions,
): string {
  if (!localOperationsChannel.value) {
    return '';
  }
  if (type === ChannelFilterType.JOB) {
    const config = jobFilterConfig.value.find((c) => c.optionId === option);
    if (config) {
      const model = config.model();
      if (model.isAny) {
        return 'All visible';
      } else if (model.isOnly) {
        const count = model.values?.length || 0;
        return count > 0 ? `Show only... (${count} groups)` : 'Show specific';
      } else {
        return 'Hidden';
      }
      // Add more mode handling if needed
    }
  } else if (type === ChannelFilterType.FLEET) {
    const config = fleetFilterConfig.value.find((c) => c.optionId === option);
    if (config) {
      const model = config.model();
      if (model.isAny) {
        return 'Group is Visible';
      } else if (model.isOnly) {
        const count = model.values?.length || 0;
        return count > 0 ? `Show only... (${count} groups)` : 'Show specific';
      } else {
        return 'Group hidden';
      }
      // Add more mode handling if needed
    }
    // Special handling for INCLUDE_EXCLUDE_FLEET and INCLUDE_EXCLUDE_DRIVERS
    const fleetFilterOptions = localOperationsChannel.value.fleetFilterOptions;
    if (option === ChannelFleetFilterOptions.INCLUDE_EXCLUDE_FLEET) {
      const included = fleetFilterOptions.includeFleetAssetIds?.length || 0;
      const excluded = fleetFilterOptions.excludeFleetAssetIds?.length || 0;
      if (included && excluded) {
        return `${included} included, ${excluded} excluded`;
      } else if (included) {
        return `${included} included`;
      } else if (excluded) {
        return `${excluded} excluded`;
      } else {
        return 'No inclusions/exclusions';
      }
    }
    if (option === ChannelFleetFilterOptions.INCLUDE_EXCLUDE_DRIVERS) {
      const included = fleetFilterOptions.includeDriverIds?.length || 0;
      const excluded = fleetFilterOptions.excludeDriverIds?.length || 0;
      if (included && excluded) {
        return `${included} included, ${excluded} excluded`;
      } else if (included) {
        return `${included} included`;
      } else if (excluded) {
        return `${excluded} excluded`;
      } else {
        return 'No inclusions/exclusions';
      }
    }
  }
  return '';
}

/**
 * Used in the template when editing in the administration screens, in a
 * v-select which determines whether the document is division level of
 * user-specific
 */
const channelLevelOptions: SelectOption<OperationsChannelLevel>[] = [
  { id: OperationsChannelLevel.DIVISION, name: 'Division-wide (all users)' },
  { id: OperationsChannelLevel.USER, name: 'Personal (only me)' },
];

const isCustomOperationsChannelAllowed: ComputedRef<boolean> = computed(() => {
  return (
    useCompanyDetailsStore().divisionCustomConfig?.operations
      ?.allowCustomOperationsChannel ?? false
  );
});

/**
 * Used as the v-model for the channel level select. Gets and sets the
 * appropriate value from/to the operations channel by setting the companyUserId
 * property.
 */
const channelLevelController: WritableComputedRef<OperationsChannelLevel> =
  computed({
    get(): OperationsChannelLevel {
      return !!props.operationsChannel.companyUserId
        ? OperationsChannelLevel.USER
        : OperationsChannelLevel.DIVISION;
    },
    set(value: OperationsChannelLevel): void {
      if (value === OperationsChannelLevel.USER) {
        props.operationsChannel.companyUserId = sessionManager.getUserId();
      } else {
        props.operationsChannel.companyUserId = null;
      }
    },
  });

// Job filter configuration (order matches jobFilterOptions)
const jobFilterConfig: ComputedRef<
  {
    optionId: ChannelJobFilterOptions;
    label: string;
    model: () => FilterByValues<string> | FilterByValues<number>;
    items: SelectOption<string | number>[];
    allowNoneOption: boolean;
    hint: string;
  }[]
> = computed(() => {
  if (!localOperationsChannel.value) {
    return [];
  }
  return [
    {
      optionId: ChannelJobFilterOptions.CLIENT,
      label: 'Clients',
      model: () =>
        localOperationsChannel.value!.jobFilterOptions.clientFilter.clientIds,
      items: clientSelectOptions.value,
      allowNoneOption: false,
      hint: 'Only jobs for the selected Client(s) will be visible',
    },
    {
      optionId: ChannelJobFilterOptions.NATIONAL_CLIENT,
      label: 'National Clients',
      model: () =>
        localOperationsChannel.value!.jobFilterOptions.clientFilter
          .nationalClientIds,
      items: nationalClientSelectOptions.value,
      allowNoneOption: true,
      hint: 'Only jobs for the selected National Client(s) will be visible',
    },
    {
      optionId: ChannelJobFilterOptions.SERVICE_TYPES,
      label: 'Service Types',
      model: () =>
        localOperationsChannel.value!.jobFilterOptions.serviceTypeIds,
      items: serviceTypeOptions.value,
      allowNoneOption: false,
      hint: 'Only jobs of the selected Service Type(s) will be visible',
    },
    {
      optionId: ChannelJobFilterOptions.RATE_TYPES,
      label: 'Job Rate Types',
      model: () => localOperationsChannel.value!.jobFilterOptions.rateTypeIds,
      items: rateTypeOptions.value,
      allowNoneOption: false,
      hint: 'Only jobs of the selected Rate Type(s) will be visible',
    },
  ];
});

/**
 * Validates the form. Called from parent components.
 */
function validate(): boolean {
  const result = operationsChannelForm.value?.validate() ?? false;
  return result;
}

// Fleet filter configuration (order matches fleetFilterOptions)
const fleetFilterConfig: ComputedRef<
  {
    optionId: ChannelFleetFilterOptions;
    label: string;
    model: () => FilterByValues<string> | FilterByValues<number>;
    items: SelectOption<string | number>[];
    allowNoneOption: boolean;
    hint: string;
  }[]
> = computed(() => {
  if (!localOperationsChannel.value) {
    return [];
  }
  return [
    // { // Uncomment if FLEET_ASSET_TYPE is enabled in fleetFilterOptions
    //   optionId: ChannelFleetFilterOptions.FLEET_ASSET_TYPE,
    //   label: 'Fleet Asset Types',
    //   model: () =>
    //     localOperationsChannel.value!.fleetFilterOptions.fleetAssetTypeIds,
    //   items: fleetAssetTypeOptions.value,
    //   allowNoneOption: true,
    //   hint: 'Select the items to be displayed',
    // },
    {
      optionId: ChannelFleetFilterOptions.VEHICLE_CLASS,
      label: 'Vehicle Classes',
      model: () =>
        localOperationsChannel.value!.fleetFilterOptions.vehicleClasses,
      items: vehicleClassOptions.value,
      allowNoneOption: true,
      hint: 'Select the items to be displayed',
    },
    {
      optionId: ChannelFleetFilterOptions.OWNER_AFFILIATION,
      label: 'Fleet Owner Affiliations',
      model: () =>
        localOperationsChannel.value!.fleetFilterOptions.ownerAffiliations,
      items: ownerAffiliationOptions.value,
      allowNoneOption: true,
      hint: 'Select the items to be displayed',
    },
    {
      optionId: ChannelFleetFilterOptions.NATIONAL_CLIENT,
      label: 'National Clients',
      model: () =>
        localOperationsChannel.value!.fleetFilterOptions.clientFilter
          .nationalClientIds,
      items: nationalClientSelectOptions.value,
      allowNoneOption: true,
      hint: 'Select the items to be displayed',
    },
    {
      optionId: ChannelFleetFilterOptions.CLIENT,
      label: 'Clients',
      model: () =>
        localOperationsChannel.value!.fleetFilterOptions.clientFilter.clientIds,
      items: clientSelectOptions.value,
      allowNoneOption: true,
      hint: 'Select the items to be displayed',
    },
  ];
});

/**
 * Toggles the selection state of a filter option for either JOB or FLEET filter
 * types.
 *
 * Depending on the filter type, this function will add or remove the specified
 * option from the corresponding selected filter options array
 * (`selectedJobFilterOptions` or `selectedFleetFilterOptions`). It also calls
 * `toggleChannelFilterOption` to update the filter state in the channel.
 *
 * @param params - An object containing:
 *   - type: The filter type, either `ChannelFilterType.JOB` or
 *     `ChannelFilterType.FLEET`.
 *   - option: The filter option to toggle (either `ChannelJobFilterOptions` or
 *     `ChannelFleetFilterOptions`).
 */
function toggleFilterOptions(
  params:
    | { type: ChannelFilterType.JOB; option: ChannelJobFilterOptions }
    | { type: ChannelFilterType.FLEET; option: ChannelFleetFilterOptions },
) {
  if (!localOperationsChannel.value) {
    return;
  }
  const { type, option } = params;
  if (type === ChannelFilterType.JOB) {
    const index = selectedJobFilterOptions.value.indexOf(option);
    if (index !== -1) {
      selectedJobFilterOptions.value.splice(index, 1);
      toggleChannelFilterOption({
        channel: localOperationsChannel.value,
        filterType: ChannelFilterType.JOB,
        option,
        on: false,
      });
    } else {
      selectedJobFilterOptions.value.push(option);
      toggleChannelFilterOption({
        channel: localOperationsChannel.value,
        filterType: ChannelFilterType.JOB,
        option,
        on: true,
      });
    }
  } else if (type === ChannelFilterType.FLEET) {
    const index = selectedFleetFilterOptions.value.indexOf(option);
    if (index !== -1) {
      selectedFleetFilterOptions.value.splice(index, 1);
      toggleChannelFilterOption({
        channel: localOperationsChannel.value,
        filterType: ChannelFilterType.FLEET,
        option,
        on: false,
      });
    } else {
      selectedFleetFilterOptions.value.push(option);
      toggleChannelFilterOption({
        channel: localOperationsChannel.value,
        filterType: ChannelFilterType.FLEET,
        option,
        on: true,
      });
    }
  }
}

/**
 * Sets the default selected filter options for both job and fleet filters
 * based on the current state of the operations channel's filter options.
 */
function setDefaultSelectedFilterOptions() {
  // Set default job filter options
  // If either clientIds or nationalClientIds
  const jobFilter = props.operationsChannel.jobFilterOptions;

  // If the filter is ANY or ONLY, then mark the filter type as being selected
  selectedJobFilterOptions.value = [
    !jobFilter.clientFilter.clientIds.isAny
      ? ChannelJobFilterOptions.CLIENT
      : null,
    !jobFilter.clientFilter.nationalClientIds.isAny
      ? ChannelJobFilterOptions.NATIONAL_CLIENT
      : null,
    !jobFilter.serviceTypeIds.isAny
      ? ChannelJobFilterOptions.SERVICE_TYPES
      : null,
    !jobFilter.rateTypeIds.isAny ? ChannelJobFilterOptions.RATE_TYPES : null,
  ].filter((option): option is ChannelJobFilterOptions => option !== null);

  // Set default fleet filter options
  const fleetFilter = props.operationsChannel.fleetFilterOptions;

  // If the filter is ANY or ONLY, then mark the filter type as being selected
  selectedFleetFilterOptions.value = [
    !fleetFilter.fleetAssetTypeIds.isAny
      ? ChannelFleetFilterOptions.FLEET_ASSET_TYPE
      : null,
    !fleetFilter.vehicleClasses.isAny
      ? ChannelFleetFilterOptions.VEHICLE_CLASS
      : null,
    !fleetFilter.ownerAffiliations.isAny
      ? ChannelFleetFilterOptions.OWNER_AFFILIATION
      : null,
    !fleetFilter.clientFilter.clientIds.isAny
      ? ChannelFleetFilterOptions.CLIENT
      : null,
    !fleetFilter.clientFilter.nationalClientIds.isAny
      ? ChannelFleetFilterOptions.NATIONAL_CLIENT
      : null,
    fleetFilter.includeFleetAssetIds || fleetFilter.excludeFleetAssetIds
      ? ChannelFleetFilterOptions.INCLUDE_EXCLUDE_FLEET
      : null,
    fleetFilter.includeDriverIds || fleetFilter.excludeDriverIds
      ? ChannelFleetFilterOptions.INCLUDE_EXCLUDE_DRIVERS
      : null,
  ].filter((option): option is ChannelFleetFilterOptions => option !== null);
}

/**
 * Sets static lists when the component mounts, for use in the template as
 * select options.
 */
function setStaticLists() {
  const allServiceTypes = Array.from(
    useCompanyDetailsStore().serviceTypesMap.values(),
  ).filter((s) => s.serviceTypeId !== 4); // Filter out Unit Rate;

  fleetAssetTypeOptions.value = fleetAssetTypes.map((fleetAssetType) => ({
    id: fleetAssetType.id,
    name: fleetAssetType.longName,
  }));

  ownerAffiliationOptions.value = affiliationTypes.map((affiliation) => ({
    id: affiliation.id,
    name: affiliation.longName,
  }));

  serviceTypeOptions.value = allServiceTypes.map((serviceType) => ({
    id: serviceType.serviceTypeId,
    name: serviceType.optionSelectName,
  }));
  rateTypeOptions.value = serviceTypeRates.map((rateType) => ({
    id: rateType.rateTypeId,
    name: rateType.longName,
  }));
  vehicleClassOptions.value = allServiceTypes.map((vehicleClass) => ({
    id: vehicleClass.shortServiceTypeName,
    name: vehicleClass.longServiceTypeName,
  }));

  clientSelectOptions.value = clientDetailsStore.clientSummaryList.map(
    (client) => ({
      id: client.clientId,
      name: client.clientDisplayName,
    }),
  );
  nationalClientSelectOptions.value =
    clientDetailsStore.nationalClientsList.map((nc) => ({
      id: nc._id!,
      name: nc.name,
    }));
  fleetAssetOptions.value = useFleetAssetStore()
    .getAllFleetAssetList.filter((f) => f.isTruck)
    .map((fleetAsset) => ({
      id: fleetAsset.fleetAssetId,
      name: [fleetAsset.csrAssignedId, fleetAsset.registrationNumber]
        .filter((n) => !!n)
        .join(' - '),
    }));
  driverOptions.value = useDriverDetailsStore().getDriverList.map((driver) => ({
    id: driver.driverId,
    name: [driver.displayName, driver.mobile].filter((n) => !!n).join(' - '),
  }));
}

onBeforeMount(() => {
  localOperationsChannel.value = new OperationsChannel(props.operationsChannel);
  setStaticLists();
  setDefaultSelectedFilterOptions();
});

defineExpose({
  validate,
  localOperationsChannel,
});
</script>
<style scoped lang="scss">
.input-cell {
  min-width: 120px;
  max-width: 200px;
}

.form-header {
  position: relative;
  margin: 22px 0px 10px;
}
.form-subheader {
  margin-top: 20px;
  margin-bottom: 4px;
}

.chip-contents {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}
</style>
