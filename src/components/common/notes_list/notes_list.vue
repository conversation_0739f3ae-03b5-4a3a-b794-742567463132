<template>
  <section class="notes-list">
    <v-flex md12>
      <v-layout row wrap v-if="communications">
        <v-flex
          md12
          v-for="(item, refIndex) in communications.filter((c) => !!c)"
          :key="refIndex"
          class="tile-item"
          :class="{ selected: selectedNotes.includes(item.id ?? '') }"
        >
          <v-layout>
            <v-checkbox
              v-if="allowSelection"
              color="info"
              class="px-2"
              :value="selectedNotes.includes(item.id ?? '')"
              @change="updateNoteSelection($event, item.id)"
            />
            <div
              class="tile-contents"
              :class="[
                detailedView && !isBookingScreen && !isClientPortal
                  ? 'notes-container'
                  : '',
                isBookingScreen ? 'booking-screen-type' : '',
              ]"
            >
              <v-layout row wrap>
                <v-flex md12>
                  <v-layout align-center>
                    <span class="username-text">{{
                      returnStrippedUsername(item.user)
                    }}</span>

                    <span class="time-text">{{
                      returnNaturalDateTime(item.epoch)
                    }}</span>
                  </v-layout>
                </v-flex>
                <v-flex md12 py-1>
                  <span class="body-text" style="white-space: pre-line">
                    {{ item.body.replace(/^\n+|\n+$/g, '') }}
                  </span>

                  <div v-if="getPudInfo(item)" class="pud-info-text">
                    {{ getPudInfo(item) }}
                  </div>
                </v-flex>
                <v-flex md12>
                  <v-layout align-center justify-end>
                    <span
                      class="time-text"
                      :class="{ 'pr-4': isEditableType(item) }"
                      >{{ getAdditionalInfoRow(item) }}</span
                    >
                    <div v-if="isEditableType(item)" class="menu-btn">
                      <v-btn
                        flat
                        icon
                        @click="editNote(item)"
                        small
                        class="ma-0"
                      >
                        <v-icon size="12" color="#8c8ca7"> fas fa-cog </v-icon>
                      </v-btn>
                    </div>
                  </v-layout>
                </v-flex>
              </v-layout>
            </div>

            <v-tooltip bottom v-if="allowDelete">
              <template v-slot:activator="{ on }">
                <v-btn icon v-on="on" @click="deleteNote(refIndex)">
                  <v-icon size="18"> delete </v-icon>
                </v-btn></template
              >
              <span>Delete Note</span>
            </v-tooltip>
          </v-layout>
        </v-flex>
        <v-flex v-if="communications.length === 0">
          <v-layout md12 align-center justify-center pt-2>
            <span class="ma-0 faded-text add-italics">No notes available</span>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>
  </section>
</template>
<script setup lang="ts">
import { returnNaturalDateTime } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import Communication from '@/interface-models/Generic/Communication/Communication';
import { Portal } from '@/interface-models/Generic/Portal';
import { sessionManager } from '@/store/session/SessionState';
import { ComputedRef, WritableComputedRef, computed } from 'vue';

// Interface for notes with PUD information
interface PUDNoteWithInfo extends Communication {
  pudInfo: {
    legNumber: number;
    legType: string;
    suburb: string;
    customerName: string;
  };
}

const emit = defineEmits<{
  (event: 'removeNote', refIndex: number): void;
  (event: 'editNote', note: Communication): void;
  (event: 'input', payload: string[]): void;
}>();

const props = withDefaults(
  defineProps<{
    value?: string[];
    communications: Communication[];
    isBookingScreen?: boolean;
    detailedView?: boolean;
    allowEdit?: boolean;
    allowDelete?: boolean;
    allowSelection?: boolean;
    showVisibilityTypeName?: boolean;
    showAddToJobType?: boolean;
    showPudInfo?: boolean;
  }>(),
  {
    value: () => [],
    isBookingScreen: false,
    detailedView: false,
    allowEdit: false,
    allowDelete: false,
    showVisibilityTypeName: false,
    showAddToJobType: false,
    allowSelection: false,
    showPudInfo: false,
  },
);

const isClientPortal: ComputedRef<boolean> = computed(() => {
  return sessionManager.getPortalType() === Portal.CLIENT;
});

const selectedNotes: WritableComputedRef<string[]> = computed({
  get(): string[] {
    return props.value ?? [];
  },
  set(value: string[]): void {
    emit('input', value);
  },
});

function getAdditionalInfoRow(note: Communication) {
  const info: string[] = [];
  if (props.showVisibilityTypeName) {
    if (note.noteType) {
      info.push(note.noteType);
    }
    if (note.noteVisibilityString) {
      info.push(note.noteVisibilityString);
    }
  }
  if (props.showAddToJobType && note.addToJobTypeString) {
    info.push(note.addToJobTypeString);
  }
  return info.join(' - ');
}

function getPudInfo(note: Communication): string | null {
  if (!props.showPudInfo) {
    return null;
  }

  // Type guard to check if the note has pudInfo
  const pudNote = note as PUDNoteWithInfo;
  if (!pudNote.pudInfo) {
    return null;
  }

  const { legNumber, legType, suburb } = pudNote.pudInfo;
  return `Leg ${legNumber} - ${legType} - ${suburb}`;
}

/**
 * Updates the selection status of a note. Called when the checkbox value is changed.
 * @param {boolean} value - The new selection status.
 * @param {string | null} id - The ID of the note.
 */
function updateNoteSelection(value: boolean, id: string | null) {
  if (!id) {
    return;
  }
  if (value) {
    if (!selectedNotes.value.includes(id)) {
      selectedNotes.value.push(id);
    }
  } else {
    selectedNotes.value = selectedNotes.value.filter((noteId) => noteId !== id);
  }
}

/**
 * Strips the domain from an email address, returning only the username.
 * @param {string} text - The email address.
 * @returns {string} - The username part of the email address.
 */
function returnStrippedUsername(text: string): string {
  if (!text) {
    return '';
  }
  const result: string[] = text.split('@', 1);
  return result[0];
}

/**
 * Emits an event to edit a note. If the allowEdit prop is false, the function
 * does nothing. If this is a child of the NotesEditor component, it will open
 * it for editing in the parent. On the operations screen, it will open the note
 * in the JobNoteDialog so that it can send off the API request.
 * @param {Communication} note - The note to be edited.
 */
function editNote(note: Communication) {
  if (!props.allowEdit) {
    return;
  }
  emit('editNote', note);
}

/**
 * Checks if a note is editable based on various conditions.
 * @param {Communication} note - The note to check.
 * @returns {boolean} - True if the note is editable, false otherwise.
 */
function isEditableType(note: Communication): boolean {
  if (
    !props.allowEdit ||
    !note.id ||
    props.isBookingScreen ||
    isClientPortal.value
  ) {
    return false;
  }
  return note.allowEdit;
}

/**
 * Emits an event to delete a note.
 * @param {number} refIndex - The index of the note to be deleted.
 */
function deleteNote(refIndex: number) {
  emit('removeNote', refIndex);
}
</script>
<style scoped lang="scss">
.notes-list {
  margin-bottom: 6px;
  border-radius: $border-radius-sm;
  box-shadow: $box-shadow-lg;

  .tile-item {
    width: 100%;
    transition: 0.15s;
    padding-bottom: 3px;
    padding-top: 3px;
    border-bottom: 1px solid var(--border-color);
    &:last-child {
      border-bottom: none;
    }
    .username-text {
      font-size: $font-size-12;
      padding-right: 4px;
      font-weight: 600;
      letter-spacing: 0.05em;
      color: var(--light-text-color);
    }
    .time-text {
      font-size: $font-size-12;
      padding-right: 4px;
      font-weight: 400;
      // margin-top: 5px;
      color: var(--light-text-color);
    }

    .tile-contents {
      width: 100%;
      position: relative;
      padding: 8px;
      border-radius: $border-radius-sm;
      box-shadow: $box-shadow-lg;
      color: var(--text-color);
      background-color: var(--background-color-500) !important;
      .notes-container {
        background-color: var(--background-color-500);
      }
      .tile-contents__hovermenu {
        &:hover {
          cursor: pointer;
        }
      }

      .menu-btn {
        position: absolute;
        bottom: 4px;
        right: 4px;
      }

      .body-text {
        font-size: $font-size-14;
        font-weight: 400;
      }

      .pud-info-text {
        font-size: $font-size-12;
        font-weight: 400;
        color: var(--warning);
        margin-top: 4px;
        font-style: italic;
      }

      &.booking-screen-type {
        border-radius: 0px;
        background-color: transparent;
        margin: 0.5px 1px;
      }
    }

    &.selected {
      .tile-contents {
        background-color: $info;
        .body-text {
          color: white;
        }
        .time-text {
          color: white;
        }
        .username-text {
          color: white;
        }
      }
    }
  }

  .faded-text {
    font-size: $font-size-14;
    color: var(--light-text-color);
  }
  .add-italics {
    font-style: italic;
  }
  .primary-header {
    font-weight: 500;
    font-size: 10px;
  }

  .delete {
    .v-btn {
      height: 20px;
      width: 20px;
      margin-top: 16px !important;
      margin-right: 0px !important;
      color: var(--background-color-300);
      background-color: var(--text-color);
      border-radius: 100px;
    }
  }
}

// Responsive Styles
@media (max-width: 1500px) {
  .tile-item {
    .time-text {
      font-size: 6px !important;
    }
    .note-type-text {
      font-size: 6px !important;
    }
    .pud-info-text {
      font-size: 6px !important;
    }
  }
  .body-text {
    font-size: 11px !important;
  }
}

@media (max-width: 1025px) {
  .tile-item {
    .time-text {
      font-size: 5px !important;
    }
    .note-type-text {
      font-size: 5px !important;
    }
    .pud-info-text {
      font-size: 5px !important;
    }
  }
  .body-text {
    font-size: 10px !important;
  }
}
</style>
