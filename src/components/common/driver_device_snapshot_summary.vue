<template>
  <v-layout class="app-theme__center-content--body">
    <v-layout row wrap v-if="driverDeviceSnapshot !== null">
      <v-flex md12 pb-1>
        <v-layout align-center>
          <v-spacer> </v-spacer>
          <span class="driversummary__lastruntime"
            >Report current at:
            {{
              returnFormattedTime(
                driverDeviceSnapshot.timestamp,
                `DD/MM/YYYY
          HH:mm`,
              )
            }}</span
          >
        </v-layout>
      </v-flex>
      <v-flex md12 pt-2 pb-3>
        <v-divider class="ma-0"></v-divider>
      </v-flex>
      <v-flex
        md12
        v-for="item in driverStatusSummaryList"
        :key="item.id"
        class="driversummary__summaryitem px-2"
      >
        <v-layout align-center>
          <span class="driversummary__summaryitem--key">
            {{ item.title }}
          </span>
          <v-flex px-3>
            <v-divider class="ma-0 pa-0" color="#1c1c1f"></v-divider>
          </v-flex>
          <span class="driversummary__summaryitem--value">
            {{ item.value }}
          </span>
        </v-layout>
      </v-flex>
    </v-layout>
    <v-layout row wrap v-else justify-center style="min-height: 300px">
      <v-flex md12>
        <v-layout justify-center>
          <v-progress-circular size="36" indeterminate></v-progress-circular>
        </v-layout>
        <v-layout justify-center pt-3>This may take a while...</v-layout>
      </v-flex>
    </v-layout>
  </v-layout>
</template>

<script setup lang="ts">
import { returnFormattedTime } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { returnReadableDeviceLocationPermissionStatus } from '@/interface-models/Driver/Device/DeviceLocationPermissionStatus';
import { DriverDeviceSnapshot } from '@/interface-models/Driver/Device/DriverDeviceSnapshot';
import { KeyValuePair } from '@/interface-models/Generic/KeyValue/KeyValue';
import { computed, ComputedRef } from 'vue';

const props = defineProps<{
  driverDeviceSnapshot: DriverDeviceSnapshot | null;
}>();

const driverStatusSummaryList: ComputedRef<KeyValuePair[]> = computed(() => {
  const summaryList: KeyValuePair[] = [];

  if (!props.driverDeviceSnapshot) {
    return [];
  }

  summaryList.push({
    id: 'fleetAssetId',
    title: 'Truck Details',
    value: props.driverDeviceSnapshot.fleetAssetId
      ? props.driverDeviceSnapshot.fleetAssetId
      : '-',
  });
  summaryList.push({
    id: 'driverId',
    title: 'Driver ID',
    value: props.driverDeviceSnapshot.driverId
      ? props.driverDeviceSnapshot.driverId
      : '-',
  });

  summaryList.push({
    id: 'currentTime',
    title: 'Current Device Time',
    value: props.driverDeviceSnapshot.currentTime
      ? props.driverDeviceSnapshot.currentTime
      : '-',
  });
  summaryList.push({
    id: 'currentWorkSummary',
    title: 'Current Allocated Work',
    value: props.driverDeviceSnapshot.currentWorkSummary
      ? props.driverDeviceSnapshot.currentWorkSummary
      : '-',
  });
  summaryList.push({
    id: 'timeSinceChecklist',
    title: 'Checklist Status',
    value: props.driverDeviceSnapshot.timeSinceChecklist
      ? props.driverDeviceSnapshot.timeSinceChecklist
      : '-',
  });
  summaryList.push({
    id: 'appVersion',
    title: 'App Version',
    value: props.driverDeviceSnapshot.appVersion
      ? props.driverDeviceSnapshot.appVersion
      : '-',
  });
  summaryList.push({
    id: 'deviceId',
    title: 'Device ID',
    value: props.driverDeviceSnapshot.deviceId
      ? props.driverDeviceSnapshot.deviceId
      : '-',
  });
  summaryList.push({
    id: 'deviceType',
    title: 'Device Type',
    value: props.driverDeviceSnapshot.deviceType
      ? props.driverDeviceSnapshot.deviceType
      : '-',
  });
  summaryList.push({
    id: 'deviceName',
    title: 'Device Name',
    value: props.driverDeviceSnapshot.deviceName
      ? props.driverDeviceSnapshot.deviceName
      : '-',
  });
  summaryList.push({
    id: 'deviceModel',
    title: 'Device Model',
    value: props.driverDeviceSnapshot.deviceModel
      ? props.driverDeviceSnapshot.deviceModel
      : '-',
  });

  summaryList.push({
    id: 'lastLocationUpdate',
    title: 'Last Location Update',
    value: props.driverDeviceSnapshot.lastLocationUpdate
      ? returnFormattedTime(
          props.driverDeviceSnapshot.lastLocationUpdate,
          'DD/MM/YY HH:mm a',
        )
      : '-',
  });
  summaryList.push({
    id: 'lastLatitude',
    title: 'Last Known Location',
    value: props.driverDeviceSnapshot.lastLatitude
      ? `Latitude: ${props.driverDeviceSnapshot.lastLatitude}, Longitude: ${props.driverDeviceSnapshot.lastLongitude}`
      : 'Unknown',
  });
  if (props.driverDeviceSnapshot.locationServicesStatus) {
    summaryList.push({
      id: 'locationServicesStatus',
      title: 'Location Service Status',
      value: props.driverDeviceSnapshot.locationServicesStatus
        ? props.driverDeviceSnapshot.locationServicesStatus
        : '-',
    });
  } else if (props.driverDeviceSnapshot.locationServices) {
    summaryList.push({
      id: 'locationServices-1',
      title: 'Location Services - Permission Status',
      value: returnReadableDeviceLocationPermissionStatus(
        props.driverDeviceSnapshot.locationServices.permissionStatus,
      ),
    });
    summaryList.push({
      id: 'locationServices - 2',
      title: 'Location Services - Accuracy',
      value: props.driverDeviceSnapshot.locationServices.isPreciseAccuracy
        ? 'Precise'
        : 'Approximate',
    });
    summaryList.push({
      id: 'locationServices - 3',
      title: 'Location Services - Currently Sending Location',
      value: props.driverDeviceSnapshot.locationServices
        .isSendingLocationUpdates
        ? 'Yes'
        : 'No',
    });
  }
  summaryList.push({
    id: 'imageAccessStatus',
    title: 'Image Service Status',
    value: props.driverDeviceSnapshot.imageAccessStatus
      ? props.driverDeviceSnapshot.imageAccessStatus
      : '-',
  });
  summaryList.push({
    id: 'batteryPercentage',
    title: 'Current Charge',
    value: props.driverDeviceSnapshot.batteryPercentage
      ? props.driverDeviceSnapshot.batteryPercentage
      : '-',
  });
  if (props.driverDeviceSnapshot.batteryState) {
    summaryList.push({
      id: 'batteryState',
      title: 'Charging State',
      value: props.driverDeviceSnapshot.batteryState
        ? props.driverDeviceSnapshot.batteryState
        : '-',
    });
  }
  summaryList.push({
    id: 'localStorageSize',
    title: 'Local Storage Size',
    value: props.driverDeviceSnapshot.localStorageSize
      ? props.driverDeviceSnapshot.localStorageSize
      : '-',
  });
  summaryList.push({
    id: 'cachedPhotoCount',
    title: 'Cached Photo Count',
    value: props.driverDeviceSnapshot.cachedPhotoCount
      ? props.driverDeviceSnapshot.cachedPhotoCount
      : '-',
  });
  summaryList.push({
    id: 'pendingPhotoStatus',
    title: 'Pending Photos',
    value: props.driverDeviceSnapshot.pendingPhotoStatus
      ? props.driverDeviceSnapshot.pendingPhotoStatus
      : '-',
  });
  summaryList.push({
    id: 'lastCacheClean',
    title: 'Last Cache Clean',
    value: props.driverDeviceSnapshot.lastCacheClean
      ? returnFormattedTime(
          props.driverDeviceSnapshot.lastCacheClean,
          'DD/MM/YY HH:mm a',
        )
      : '-',
  });
  if (props.driverDeviceSnapshot.additionalNotes) {
    props.driverDeviceSnapshot.additionalNotes.forEach((note, idx) => {
      summaryList.push({
        id: `additionalNotes-${idx}`,
        title: idx === 0 ? 'Additional Notes' : '',
        value: note,
      });
    });
  } else if (props.driverDeviceSnapshot.additionalInfo) {
    summaryList.push({
      id: 'additionalInfo',
      title: 'Misc',
      value: props.driverDeviceSnapshot.additionalInfo
        ? props.driverDeviceSnapshot.additionalInfo
        : '-',
    });
  }
  return summaryList;
});
</script>

<style scoped lang="scss">
.driversummary__summaryitem {
  padding: 2px 0px;
  .driversummary__summaryitem--key {
    font-size: $font-size-12;
    text-transform: uppercase;
    font-weight: 600;
    color: rgb(186, 188, 209);
    padding-left: 3px;
  }
  .driversummary__summaryitem--value {
    font-size: $font-size-12;
    font-weight: 400;
    padding-right: 3px;
  }
}

.driversummary__lastruntime {
  font-size: $font-size-12;
  font-weight: 400;
  padding-right: 3px;
  color: #bfbfda;
  font-style: italic;
}

.driversummary__syncicon {
  font-size: $font-size-14;
  color: #bfbfda;
  &:hover {
    color: white;
    cursor: pointer;
  }
}
</style>
