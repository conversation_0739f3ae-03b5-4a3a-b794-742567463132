.pud-timeline {
  width: 100%;
  $icon-color: var(--accent-secondary);
  $icon-color-accent: var(--accent-secondary);

  .timeline {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    padding: 0px 0px 0px 5px;
    margin: 0;
    list-style: none;
    position: relative;
    width: 100%;

    &::before {
      // this is the timeline vertical line
      content: '';
      position: absolute;
      top: 0;
      margin-top: 4px;
      padding-bottom: 20px;
      padding-left: 5px;
      left: 4px;
      height: 92%;
      width: 2px;
      border-right: 2px dotted rgb(172, 172, 172);
    }
  }

  .timeline__item {
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: stretch;
    align-items: stretch;

    padding: 5px 0;
    min-width: 100%;
    position: relative;
    // border-bottom: 1px solid rgb(107, 107, 107);
    color: grey;

    &.break-type {
      pointer-events: none;
    }

    .detailed-container {
      background-color: orange;
      color: #1f1f1f;
      font-size: 0.95em;
      font-weight: 600;
    }

    &:hover {
      background-color: rgba(78, 78, 78, 0.123);
      transition: 0.15s;
      cursor: pointer;
    }

    &.side-highlight {
      background-color: var(--background-color-600);
      border-right: 4px solid var(--light-text-color);
    }
  }

  .timeline__icon-container {
    // display: flex;
    // justify-content: center;
    // align-items: center;
    margin-right: 5px;

    // position: absolute;
    // right: 8px;
    // top: 8px;
  }

  .timeline__item_dense {
    padding: 0px 0px;
  }

  .timeline__step {
    padding-right: 16px;
  }

  .timeline__step__marker {
    position: relative;
    display: table-cell;
    height: 11px;
    min-height: 11px;
    width: 11px;
    min-width: 11px;
    border: 3px solid rgb(99, 99, 99);
    z-index: 0;
  }

  .timeline__step__marker--start {
    border-color: $icon-color;
    -moz-border-radius: 50%;
    -webkit-border-radius: 50%;
    border-radius: 50%;
    background-color: $icon-color;
  }

  .timeline__step__marker--end {
    border-color: $icon-color;
    -moz-border-radius: 50%;
    -webkit-border-radius: 50%;
    border-radius: 50%;
    background-color: #383838;
  }

  .timeline__step__marker--blue-square-pickup {
    background-color: $icon-color;
    border-color: $icon-color;
  }

  .timeline__step__marker--blue-square-dropoff {
    background-color: #383838;
    border-color: $icon-color;
  }

  .timeline__step__marker--blue-square-greyed-pickup {
    background-color: #cecece;
    border-color: #cecece;
  }

  .timeline__step__marker--blue-square-greyed-dropoff {
    background-color: #686868;
    border-color: #cecece;
  }

  .timeline__step__marker--blue-square-active {
    background-color: rgb(221, 221, 221);
    border-color: $icon-color;
  }

  .timeline__step__marker--purple {
    border-color: #c178fa;
  }

  .timeline__time {
    padding-right: 12px;
    font-size: $font-size-small;
    font-weight: 500;
    .time-txt {
      color: var(--text-color);
    }
  }

  .timeline__title {
    padding-bottom: 0px;
    font-size: $font-size-12;
    font-size: 0.85em;
    color: var(--text-color);
    font-weight: 400;
  }

  .timeline__points {
    padding: 0;
    list-style: none;
    font-size: $font-size-11;
    color: var(--light-text-color);
  }

  .timeline__points > * {
    padding: 0 0 0px 0;
  }

  .timeline_address_light {
    font-size: $font-size-10;
    font-weight: 400;
    color: var(--light-text-color);
  }

  .timeline__POD--container {
    display: flex;
    flex-direction: column;
    padding-left: 8px;
    padding-top: 4px;

    .timeline__POD--item {
      font-size: $font-size-11;
      font-weight: 500;
      color: var(--light-text-color);
      // text-transform: uppercase;
      padding: 1px 0px;

      &.complete-type {
        color: #72f3cc;
        .timeline__POD--bullet {
          color: #72f3cc;
          font-size: 8px;
          padding-bottom: 3px;
        }
      }

      &.active-incomplete-type {
        color: #ffd000;
        .timeline__POD--bullet {
          color: #ffd000;
        }
      }
      &.inactive-type {
        color: grey;
      }

      .timeline__POD--bullet {
        font-size: 5px;
        padding-bottom: 4px;
        padding-right: 10px;
        color: var(--accent-secondary);
      }
      .timeline__POD--label {
        padding-right: 6px;
        color: var(--text-color);
      }
      .timeline__POD--value {
        color: var(--text-color);
      }
    }
  }

  .button-minimize-absolute {
    position: absolute;
    top: 0px;
    right: 0px;
  }

  /* Display stuff
   */
  html {
    background: url(//s3-us-west-2.amazonaws.com/s.cdpn.io/153256/trees.jpg)
      no-repeat center center fixed;
    -moz-background-size: cover;
    -o-background-size: cover;
    -webkit-background-size: cover;
    background-size: cover;
  }

  html,
  body {
    line-height: 1.5rem;
    font-family: 'Muli', sans-serif;
    font-weight: 300;
    margin: 0;
    box-sizing: border-box;
    height: 100%;
  }

  *,
  *:before,
  *:after {
    box-sizing: inherit;
  }

  .card {
    margin: 0;
    overflow: hidden;
    min-width: 100%;
    display: table;
  }
}
