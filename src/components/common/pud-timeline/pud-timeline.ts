import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import Attachment from '@/interface-models/Generic/Attachment/Attachment';
import AttachmentApproval from '@/interface-models/Generic/Attachment/AttachmentApproval';
import AttachmentStatus from '@/interface-models/Generic/Attachment/AttachmentStatus';
import AttachmentTypes from '@/interface-models/Generic/Attachment/AttachmentTypes';
import ProofOfDelivery from '@/interface-models/Generic/ProofOfDelivery/ProofOfDelivery';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { Component, Prop, Vue } from 'vue-property-decorator';

interface ProofOfDeliverySummary {
  id: number;
  label: string;
  numberSupplied: number;
  numberRequired: number;
  requirementsMet: boolean;
  allApproved: boolean;
  activeStatus: boolean;
}

@Component({
  components: {},
})
export default class PudTimeline extends Vue {
  @Prop() public pudItems: PUDItem[];
  @Prop() public dynamic: boolean;
  @Prop() public detailed: boolean;
  @Prop() public locked: boolean;
  @Prop() public selectable: boolean;
  @Prop() public alwaysExpanded: boolean;
  @Prop() public showAttachments: boolean;
  @Prop() public addPadding: number;
  @Prop() public selectedPud: number;
  @Prop({ default: false }) public showStartTime: boolean;
  @Prop() public startTime: number;
  @Prop() public proofOfDeliveryRequirements: ProofOfDelivery;

  public expandedState: boolean = false;

  get expanded() {
    if (!this.detailed && !this.alwaysExpanded) {
      return this.expandedState;
    } else if (this.alwaysExpanded) {
      return true;
    } else {
      return true;
    }
  }
  set expanded(val: boolean) {
    this.expandedState = val;
  }

  public returnCorrectTime(epoch: number) {
    return returnFormattedDate(epoch, 'HH:mm');
  }

  get proofOfDeliveryDetails() {
    if (!this.proofOfDeliveryRequirements) {
      return undefined;
    }
    const podReq: ProofOfDelivery = this.proofOfDeliveryRequirements;
    const podData: ProofOfDeliverySummary[][] = [];
    const validPudItems = this.pudItems.filter(
      (pudItem) => pudItem.legTypeFlag === 'P' || pudItem.legTypeFlag === 'D',
    );
    for (const pud of this.pudItems) {
      const podPudData: ProofOfDeliverySummary[] = [];

      if (pud.legTypeFlag !== 'P' && pud.legTypeFlag !== 'D') {
        podData.push(podPudData);
        continue;
      }

      const imageType = pud.attachments.filter(
        (i) =>
          i.documentTypeId === 7 ||
          i.documentTypeId === AttachmentTypes.PUD_ITEM_IMAGE,
      );

      const documentType = pud.attachments.filter(
        (d) =>
          d.documentTypeId === 8 ||
          d.documentTypeId === AttachmentTypes.PUD_ITEM_DOCUMENT,
      );

      const signatureType = pud.attachments.filter(
        (s) =>
          s.documentTypeId === 6 ||
          s.documentTypeId === AttachmentTypes.PUD_ITEM_SIGNATURE,
      );

      const imagePOD: ProofOfDeliverySummary = {
        id: AttachmentTypes.PUD_ITEM_IMAGE,
        label: 'Photos',
        numberSupplied: imageType.length,
        numberRequired: podReq.pudPhotos,
        requirementsMet: imageType.length >= podReq.pudPhotos,
        allApproved: false,
        activeStatus: pud.status !== null,
      };

      const documentPOD: ProofOfDeliverySummary = {
        id: AttachmentTypes.PUD_ITEM_DOCUMENT,
        label: 'Paperwork',
        numberSupplied: documentType.length,
        numberRequired: podReq.pudPaperwork ? 1 : 0,
        requirementsMet: documentType.length >= (podReq.pudPaperwork ? 1 : 0),
        allApproved: false,
        activeStatus: pud.status !== null,
      };

      const signaturePOD: ProofOfDeliverySummary = {
        id: AttachmentTypes.PUD_ITEM_SIGNATURE,
        label: 'Signature',
        numberSupplied: signatureType.length,
        numberRequired: podReq.deviceSignature ? 1 : 0,
        requirementsMet:
          signatureType.length >= (podReq.deviceSignature ? 1 : 0),
        allApproved: false,
        activeStatus: pud.status !== null,
      };

      podPudData.push(imagePOD);
      if (podReq.pudPaperwork) {
        podPudData.push(documentPOD);
      }
      if (podReq.deviceSignature) {
        podPudData.push(signaturePOD);
      }

      podData.push(podPudData);
    }

    return podData;
  }

  public returnAllApproved(podDetails: ProofOfDeliverySummary[]) {
    return podDetails.every((pod) => pod.requirementsMet);
  }

  public returnIconColor(index: number) {
    const item: PUDItem = this.pudItems[index];
    const inactive = 'grey darken-1';
    const activePending = 'amber';
    const activeApproved = 'green accent-4';
    if (
      item.status === null ||
      item.attachments.length === 0 ||
      item.attachments === null
    ) {
      return inactive;
    } else {
      for (const i of item.attachments) {
        if (
          i.documentTypeId === 7 ||
          i.documentTypeId === 8 ||
          i.documentTypeId === AttachmentTypes.PUD_ITEM_IMAGE ||
          i.documentTypeId === AttachmentTypes.PUD_ITEM_DOCUMENT
        ) {
          const mostRecent: AttachmentApproval | undefined =
            this.currentImageApprovalStatus(i);
          if (mostRecent === undefined) {
            return activePending;
          }
          if (
            mostRecent.status === AttachmentStatus.PENDING ||
            mostRecent.status === AttachmentStatus.REJECTED ||
            mostRecent.status === 'PENDING' ||
            mostRecent.status === 'REJECTED'
          ) {
            return activePending;
          }
        }
      }
      return activeApproved;
    }
  }

  public returnIconColorSignature(index: number) {
    const item: PUDItem = this.pudItems[index];
    const inactive = 'grey darken-1';
    const activePending = 'amber';
    const activeApproved = 'green accent-4';
    if (
      item.status === null ||
      item.attachments.length === 0 ||
      item.attachments === null
    ) {
      return inactive;
    } else {
      const signatureAttachment: Attachment | undefined = item.attachments.find(
        (a: Attachment) =>
          a.documentTypeId === 6 ||
          a.documentTypeId === AttachmentTypes.PUD_ITEM_SIGNATURE,
      );
      if (signatureAttachment === undefined) {
        return activePending;
      } else {
        const mostRecent: AttachmentApproval | undefined =
          this.currentImageApprovalStatus(signatureAttachment);
        if (mostRecent === undefined) {
          return activePending;
        }
        if (
          mostRecent.status === AttachmentStatus.PENDING ||
          mostRecent.status === AttachmentStatus.REJECTED ||
          mostRecent.status === 'PENDING' ||
          mostRecent.status === 'REJECTED'
        ) {
          return activePending;
        }
      }
      return activeApproved;
    }
  }

  public currentImageApprovalStatus(
    attachment: Attachment,
  ): AttachmentApproval | undefined {
    if (attachment !== undefined) {
      const currentImageClone: Attachment = Object.assign(
        new Attachment(),
        attachment,
      );
      const mostRecent: AttachmentApproval | undefined =
        currentImageClone.currentApprovalStatus;
      return mostRecent;
    }
    return;
  }

  public itemSelected(index: number) {
    if (this.selectable) {
      if (
        this.pudItems[index].legTypeFlag === 'P' ||
        this.pudItems[index].legTypeFlag === 'D'
      ) {
        this.$emit('indexSelected', index);
      }
    }
  }
}
