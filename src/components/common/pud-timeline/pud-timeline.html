<section class="pud-timeline" v-if="pudItems !== null && pudItems.length >= 1">
  <v-layout>
    <v-flex md12>
      <div class="card" :class="detailed ? 'pt-2' : ''">
        <ul class="timeline">
          <v-flex>
            <li class="timeline__item" v-if="showStartTime">
              <v-layout row :pb-1="addPadding">
                <div class="timeline__step">
                  <div class="timeline__step__marker"></div>
                </div>
                <div class="timeline__time">
                  <v-sheet
                    class="px-1"
                    :color="detailed ? 'orange' : 'transparent'"
                    :class="detailed ? 'detailed-container' : ''"
                  >
                    <span>{{ returnCorrectTime(startTime) }}</span>
                  </v-sheet>
                </div>
                <div class="timeline__content">
                  <div class="timeline__title">Start Job</div>
                </div>
              </v-layout>
            </li>
            <li
              class="timeline__item"
              v-for="(pud, index) in pudItems"
              :class="[detailed ? 'timeline__item_dense' : '', (selectedPud !== undefined && selectedPud === index) ? 'side-highlight' : '', pud.legTypeFlag === 'B' ? 'break-type' : '']"
              v-if="expanded || (index === 0 || index === (pudItems.length - 1))"
              @click="itemSelected(index)"
            >
              <v-layout
                row
                :pb-1="addPadding"
                v-if="pud.legTypeFlag === 'P' || pud.legTypeFlag === 'D'"
              >
                <div class="timeline__step">
                  <div
                    class="timeline__step__marker timeline__step__marker--start"
                    v-if="index === 0"
                  ></div>
                  <div
                    class="timeline__step__marker timeline__step__marker--end"
                    v-if="index === pudItems.length - 1"
                  ></div>
                  <div
                    class="timeline__step__marker"
                    :class="[
                    pud.legTypeFlag === 'P' ? 'timeline__step__marker--blue-square-pickup' : ' timeline__step__marker--blue-square-dropoff',
                    (pud.status === null && dynamic && pud.legTypeFlag === 'P' ) ? 'timeline__step__marker--blue-square-greyed-pickup' : '',
                    (pud.status === null && dynamic && pud.legTypeFlag === 'D' ) ? 'timeline__step__marker--blue-square-greyed-dropoff' : '',
                    ]"
                    v-if="index !== (pudItems.length - 1) && index !== 0"
                  ></div>
                </div>
                <div class="timeline__time">
                  <v-sheet
                    class="px-1"
                    :color="detailed ? 'orange' : 'transparent'"
                    :class="detailed ? 'detailed-container' : ''"
                  >
                    <span class="time-txt" v-if="pud.epochTime !== -1"
                      >{{ returnCorrectTime(pud.epochTime) }}</span
                    >
                    <span v-else> Unknown</span>
                  </v-sheet>
                </div>
                <div class="timeline__content">
                  <div class="timeline__title">
                    {{ pud.customerDeliveryName ? pud.customerDeliveryName :
                    "-"}}
                    <span
                      class="grey--text text--lighten-1 pl-1"
                      v-if="!detailed && !pud.createdByDriver"
                      >{{ pud.address.suburb }}</span
                    >
                    <span v-if="proofOfDeliveryDetails">
                      <v-icon
                        size="12"
                        color="teal accent-3"
                        class="pl-1"
                        v-if="returnAllApproved(proofOfDeliveryDetails[index])"
                      >
                        far fa-check-double
                      </v-icon>
                    </span>
                  </div>
                  <span
                    class="timeline__points"
                    v-if="!expanded && index === 0 && (pudItems.length > 2)"
                  >
                    <v-layout class="pt-1" justify-start>
                      <v-btn
                        flat
                        small
                        class="ma-0"
                        @click="expanded = !expanded"
                      >
                        <v-icon color="grey" class="pr-1" size="17">
                          far fa-long-arrow-alt-down </v-icon
                        >{{ pudItems.length - 2 }} stops
                      </v-btn>
                    </v-layout>
                  </span>
                  <span
                    class="timeline__points timeline_address_light"
                    v-if="detailed"
                  >
                    {{ pud.address.formattedAddress }}
                  </span>
                  <span
                    class="timeline__points timeline__POD--container"
                    :class="[addPadding ? 'pt-2' : '']"
                    v-if="proofOfDeliveryRequirements && proofOfDeliveryRequirements && proofOfDeliveryDetails[index].length > 0 && (selectedPud !== undefined && selectedPud === index)"
                  >
                    <div
                      class="timeline__POD--item"
                      :class="[pod.requirementsMet ? 'complete-type' : '', !pod.activeStatus ? 'inactive-type' : '', !pod.requirementsMet && pod.activeStatus ? 'active-incomplete-type' : '']"
                      v-for="pod in proofOfDeliveryDetails[index]"
                      :key="pod.id"
                    >
                      <v-icon
                        class="timeline__POD--bullet"
                        v-text="pod.requirementsMet ? 'fas fa-check' :'fas fa-circle'"
                      >
                      </v-icon>
                      <v-icon></v-icon>
                      <span class="timeline__POD--label">{{pod.label}}</span>
                      <span class="timeline__POD--value"
                        >{{pod.numberSupplied}}/{{pod.numberRequired}}</span
                      >
                    </div>
                  </span>
                </div>
              </v-layout>

              <!-- <div
                class="timeline__icon-container"
                v-if="showAttachments && (pud.legTypeFlag === 'P' || pud.legTypeFlag === 'D')"
              >
                <v-layout>
                  <v-icon size="11" :color="returnIconColorSignature(index)">
                    fal fa-signature
                  </v-icon>
                  <v-icon
                    size="11"
                    class="pl-2"
                    :color="returnIconColor(index)"
                  >
                    fal fa-images
                  </v-icon>
                </v-layout>
              </div> -->
              <!-- <div
                class="timeline__icon-container"
                v-if="(pud.legTypeFlag === 'P' || pud.legTypeFlag === 'D')"
              ></div> -->
              <v-layout row :pb-1="addPadding" v-if="pud.legTypeFlag === 'B'">
                <div class="timeline__step">
                  <div class="timeline__step__marker"></div>
                </div>
                <div class="timeline__time">
                  <v-sheet
                    class="px-1"
                    :color="detailed ? 'orange' : 'transparent'"
                    :class="detailed ? 'detailed-container' : ''"
                  >
                    <span>{{ returnCorrectTime(pud.epochTime) }}</span>
                  </v-sheet>
                </div>
                <div class="timeline__content break-type">
                  <div class="timeline__title" style="opacity: 0.5">
                    Driver Break
                  </div>
                </div>
              </v-layout>
            </li>
          </v-flex>
        </ul>
        <div class="button-minimize-absolute" v-if="!locked">
          <v-btn
            flat
            icon
            @click="expanded = !expanded"
            color="orange"
            v-if="expanded"
          >
            <v-icon color="white" size="16">fal fa-minus-square</v-icon>
          </v-btn>
        </div>
      </div>
    </v-flex>
  </v-layout>
</section>
