<template>
  <section class="job-lookup-result-summary">
    <v-layout v-if="activatorType === 'LABEL'">
      <span>{{ buttonText }}</span>
      <span
        v-if="showButton"
        @click="dialogController = true"
        class="accent-text-button px-1 ml-1"
        >View</span
      >
    </v-layout>
    <v-layout v-if="activatorType === 'BUTTON'">
      <span
        class="subheader--faded accent-subtext-button pa-1"
        @click="dialogController = true"
      >
        {{ buttonText }}
      </span>
    </v-layout>

    <v-dialog
      v-model="dialogController"
      width="700px"
      class="ma-0"
      persistent
      no-click-animation
      content-class="v-dialog-custom"
    >
      <v-flex md12>
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight"
        >
          <span
            >Potential Affected
            {{
              entityCategory === 'CLIENT'
                ? `Clients`
                : `Fleet
          Assets`
            }}</span
          >
          <div
            class="app-theme__center-content--closebutton"
            @click="dialogController = false"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>
        <v-layout
          row
          wrap
          class="app-theme__center-content--body dialog-content"
        >
          <v-flex md12>
            <v-layout
              row
              wrap
              class="body-scrollable--65 body-min-height--65"
              pa-3
            >
              <v-flex md12>
                <table class="simple-data-table">
                  <thead>
                    <tr>
                      <th>
                        {{
                          entityCategory === 'CLIENT' ? 'Client' : 'Fleet Asset'
                        }}
                      </th>
                      <th># of Jobs</th>
                      <th>Related Jobs</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="item in summaryList"
                      :key="item.id"
                      :class="{
                        'row-selectable': allowSelection,
                        'row-selected': selectedId === item.id,
                      }"
                      @click="allowSelection ? selectItem(item) : null"
                    >
                      <td>
                        <span>{{ item.name }}</span>
                      </td>
                      <td class="text-xs-right">
                        {{ item.jobList.length }}
                      </td>
                      <td class="text-xs-right">
                        {{ returnJoinedJobIdsString(item.jobList) }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12 v-if="allowSelection">
            <v-divider></v-divider>
            <v-layout align-center>
              <v-btn flat color="red" @click="dialogController = false"
                >Cancel</v-btn
              >
              <v-spacer></v-spacer>
              <v-btn
                depressed
                color="blue"
                @click="applySelectedItem"
                class="v-btn-confirm-custom"
                :disabled="selectedId === null"
                >Apply
              </v-btn>
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-dialog>
  </section>
</template>

<script lang="ts">
export enum ActivatorType {
  LABEL = 'LABEL',
  BUTTON = 'BUTTON',
}
</script>

<script setup lang="ts">
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { InvoiceEntityCategory } from '@/interface-models/InvoiceAdjustment/EntityTypes/InvoiceEntityCategory';
import { ClientFleetSummaryItem } from '@/interface-models/Jobs/JobIdLookup/ClientFleetSummaryItem';
import { JobIdLookupSummary } from '@/interface-models/Jobs/JobIdLookup/JobIdLookupSummary';
import { computed, onMounted, Ref, ref, WritableComputedRef } from 'vue';

const props = withDefaults(
  defineProps<{
    activatorType?: ActivatorType;
    entityCategory: InvoiceEntityCategory;
    summaryList: ClientFleetSummaryItem[];
    allowSelection?: boolean;
  }>(),
  {
    activatorType: ActivatorType.LABEL,
    entityCategory: InvoiceEntityCategory.CLIENT,
    summaryList: () => [],
    allowSelection: false,
  },
);

const isViewingDialog: Ref<boolean> = ref(false);
const selectedId: Ref<string | null> = ref(null);

const emit = defineEmits(['applySelectedItem']);

function returnJoinedJobIdsString(jobList: JobIdLookupSummary[]) {
  return jobList
    .map((j) => (j.recurringJobId ? j.recurringJobId : j.jobId.toString()))
    .join(', ');
}

function selectItem(item: ClientFleetSummaryItem) {
  if (selectedId.value !== null && selectedId.value === item.id) {
    selectedId.value = null;
  } else {
    selectedId.value = item.id;
  }
}

function applySelectedItem() {
  if (selectedId.value) {
    const foundItem = props.summaryList.find((i) => i.id === selectedId.value);
    if (foundItem) {
      emit('applySelectedItem', foundItem);
      dialogController.value = false;
    } else {
      showNotification('Selection could not be found');
    }
  }
}

const showButton: WritableComputedRef<boolean> = computed(() => {
  return props.summaryList.length > 1;
});

const buttonText: WritableComputedRef<string> = computed(() => {
  if (props.activatorType === ActivatorType.LABEL) {
    if (props.summaryList.length === 0) {
      return 'None';
    } else if (props.summaryList.length === 1) {
      return props.summaryList[0].name;
    } else {
      return `${props.summaryList.length} ${
        props.entityCategory === InvoiceEntityCategory.CLIENT
          ? 'Clients'
          : 'Fleet Assets'
      }`;
    }
  } else if (props.activatorType === ActivatorType.BUTTON) {
    if (props.summaryList.length === 0) {
      return '';
    } else if (props.summaryList.length === 1) {
      return 'View Suggested';
    } else {
      return `View ${props.summaryList.length} Suggested`;
    }
  } else {
    return '';
  }
});

// Controls dialog visibility
const dialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return isViewingDialog.value;
  },
  set(value: boolean): void {
    if (!value) {
      selectedId.value = null;
    }
    isViewingDialog.value = value;
  },
});

onMounted(() => {
  if (props.summaryList && props.summaryList.length === 1) {
    selectedId.value = props.summaryList[0].id;
  }
});
</script>

<style scoped lang="scss">
.job-lookup-result-summary {
  padding: auto;
}
</style>
