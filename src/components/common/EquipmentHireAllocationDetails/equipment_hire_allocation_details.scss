.expansion-panel-container {
  border: 1px solid $translucent;
  border-radius: $border-radius-sm;
  .expansion-item-container {
    border-radius: $border-radius-sm !important;
    background-color: var(--background-color-300) !important;
  }
}

.expansion-job-item {
  .expansion-job-item__card-wrapper {
    padding-top: 1px;

    &.dialog-view {
      // padding-top: 6px;
      .expansion-job-item__card {
        padding: 16px;
      }
    }

    &.pud-type {
      padding-top: 1px;

      .expansion-job-item__card {
        // background-color: #1e1d2571;
        padding: 12px;
      }
    }
    .expansion-job-item__card {
      padding: 12px;

      font-size: $font-size-12;
      text-transform: uppercase;

      //
      &--jobid {
        font-weight: 500;
        color: rgb(255, 185, 23);
      }
      &--clientname {
        font-weight: 500;
        color: rgb(224, 224, 241);
        padding-left: 6px;
      }
      &--time {
        color: #9997a7;
        font-weight: 400;
        padding-left: 6px;
      }
      &--status {
        font-size: $font-size-11;
        font-weight: 700;
      }
    }
  }
}
