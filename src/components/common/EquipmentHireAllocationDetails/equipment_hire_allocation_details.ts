import { returnFormattedTime } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import AdditionalAsset from '@/interface-models/Jobs/AdditionalAsset';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { OperationJobSummary } from '@/interface-models/Jobs/OperationJobSummary';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { PUDItemShort } from '@/interface-models/Jobs/PUD/PUDItemShort';
import { useJobStore } from '@/store/modules/JobStore';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class EquipmentHireAllocationDetails extends Vue {
  @Prop() public assetId: string;

  get allocatedJobDetails() {
    return useJobStore()
      .operationJobsList.filter((x: OperationJobSummary) => {
        return (
          x.additionalAssets &&
          x.additionalAssets.find(
            (equipment: AdditionalAsset) => equipment.assetId === this.assetId,
          )
        );
      })
      .map((job: OperationJobSummary) => {
        return {
          displayId: job.displayId,
          clientName: job.clientName,
          status: job.status,
          timeDuration: returnFormattedTime(job.date),
        };
      });
  }
}
