<v-layout v-if="allocatedJobDetails.length > 0">
  <v-expansion-panel class="expansion-panel-container">
    <v-expansion-panel-content class="expansion-item-container">
      <template v-slot:header>
        <div>Allocated Work</div>
      </template>

      <v-layout
        row
        wrap
        v-for="(jobDetails, index) of allocatedJobDetails"
        :key="jobDetails.jobId"
        class="expansion-job-item"
      >
        <v-flex md12 class="expansion-job-item__card-wrapper">
          <v-layout
            align-center
            class="expansion-job-item__card app-bgcolor--400 app-borderside--b app-bordercolor--600"
          >
            <span class="expansion-job-item__card--jobid">
              #{{jobDetails.displayId}}
            </span>
            <span class="expansion-job-item__card--clientname">
              {{jobDetails.clientName}}
            </span>
            <span class="expansion-job-item__card--time">
              {{jobDetails.timeDuration}}
            </span>

            <v-spacer></v-spacer>

            <span class="expansion-job-item__card--status"
              >{{jobDetails.status}}</span
            >
          </v-layout>
        </v-flex>
      </v-layout>
    </v-expansion-panel-content>
  </v-expansion-panel>
</v-layout>
