<v-dialog
  v-model="showDialog"
  :width="allDataLoaded ? '70%' : '30%'"
  persistent
  class="ma-0"
  :style="isListTile ? {display: 'inline'} : {display: 'inline-block'}"
  content-class="v-dialog-custom"
>
  <template v-slot:activator="{ on }">
    <v-icon
      v-if="isIcon"
      size="14"
      :disabled="showDialog || buttonDisabled"
      class="icon-hover--primary"
      v-on="on"
    >
      {{faIconName}}</v-icon
    >
    <v-list-tile
      dense
      v-if="isListTile"
      :disabled="showDialog || buttonDisabled"
      v-on="on"
    >
      <v-list-tile-avatar v-if="useLeadingIcon && faIconName">
        <v-icon size="16">{{faIconName}}</v-icon>
      </v-list-tile-avatar>
      <v-list-tile-title class="pr-2"> {{ buttonText }} </v-list-tile-title>
    </v-list-tile>
  </template>
  <v-card color="#242329">
    <v-layout
      justify-space-between
      align-center
      class="task-bar app-theme__center-content--header no-highlight"
    >
      <span>Add/Edit Points</span>
      <v-sheet class="px-2 ml-1" color="amber">{{operationType}}</v-sheet>
      <!--
        We can uncomment this when the leg creation tool is capable of editing just Unassigned Pud Items
        Until then it is unnecessary
        <v-sheet class="px-2 ml-2" color="teal">{{pudMaintenanceType}}</v-sheet> -->
      <v-spacer></v-spacer>
      <div class="app-theme__center-content--closebutton" @click="closeDialog">
        <v-icon class="app-theme__center-content--closebutton--icon"
          >fal fa-times</v-icon
        >
      </div>
    </v-layout>

    <v-form ref="pudMaintenanceForm">
      <v-layout class="pa-3 content-container">
        <v-flex md12>
          <v-layout row wrap>
            <v-flex :md12="!allDataLoaded" :md6="allDataLoaded" pr-2>
              <v-autocomplete
                id="client-job-select"
                v-model="unassignedPudItem.clientId"
                :items="clientList"
                item-value="clientId"
                item-text="clientSearchCriteria"
                color="light-blue"
                label="Select A Client"
                solo
                @change="getSelectedClientDetails"
                browser-autocomplete="off"
                auto-select-first
                :rules="[validationRules.required]"
                flat
                persistent-hint
                hint="Please Select A Client"
                class="form-field-required"
                :disabled="operationType === 'VIEW'"
              >
                <template slot="selection" slot-scope="data">
                  <span>{{ data.item.clientDisplayName }}</span>
                </template>
                <template slot="item" slot-scope="data">
                  <span>{{ data.item.clientDisplayName }} </span>
                </template>
              </v-autocomplete>
            </v-flex>
            <v-flex :md12="!allDataLoaded" :md6="allDataLoaded" pr-2>
              <v-select
                label="Leg Type"
                :items="legTypes"
                item-text="value"
                item-value="id"
                solo
                color="light-blue"
                flat
                v-model="editingPudItem.legTypeFlag"
                :rules="[validationRules.required]"
                persistent-hint
                hint="Leg Type"
                class="form-field-required"
                :disabled="operationType === 'VIEW'"
              />
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
      <v-layout
        row
        wrap
        v-if="pudMaintenanceType === 'UNASSIGNED' && allDataLoaded"
        px-3
      >
        <v-flex :md12="!allDataLoaded" :md4="allDataLoaded" pr-2>
          <v-text-field
            solo
            flat
            label="Reference #"
            v-model="unassignedPudItem.clientSuppliedId"
            :disabled="operationType === 'VIEW'"
          ></v-text-field>
        </v-flex>
        <v-flex :md12="!allDataLoaded" :md4="allDataLoaded">
          <v-switch
            color="light-blue"
            v-model="showUnitRateInputs"
            label="Include Unit Rate Details"
            :disabled="operationType === 'VIEW'"
          ></v-switch>
        </v-flex>
      </v-layout>
      <v-layout v-if="allDataLoaded" px-3>
        <v-divider></v-divider>
      </v-layout>
    </v-form>
    <!-- <v-layout
      v-if="allDataLoaded"
      px-3
      style="max-height: 60vh; overflow-y: scroll;"
    >
      <v-flex md12 :key="editingPudItem.legTypeFlag">
        <AddPudItem
          :currentPudItem="editingPudItem"
          :validServiceTypes="validServiceTypes"
          :rateTypeId="showUnitRateInputs ? 5  : 1"
          :editingPudIndex="-1"
          :pudItems="[]"
          :editingExistingPUDItem="operationType === 'EDIT'"
          :clientDetails="clientDetails"
          :existingUserAccountList="[]"
          :enableSuburbSelect="true"
          :isRecurringJob="false"
          :hasDynamicRateType="showUnitRateInputs"
          :allowAsapTimeDefinition="false"
          :columnView="true"
          :formDisabled="operationType === 'VIEW'"
          saveButtonLabel="Point"
          @SavedPUDItem="savePudItem"
          @CancelAddPUDItem="closeDialog"
        >
        </AddPudItem>
      </v-flex>
    </v-layout> -->
  </v-card>
</v-dialog>
