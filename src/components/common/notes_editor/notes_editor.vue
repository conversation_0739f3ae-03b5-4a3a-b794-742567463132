<template>
  <section class="notes-editor">
    <v-layout row wrap>
      <v-flex
        md12
        pb-2
        v-if="isEditingExistingNote || editingExistingNoteLocally"
      >
        <v-alert
          type="info"
          :value="isEditingExistingNote || editingExistingNoteLocally"
        >
          You are editing an existing note.
        </v-alert>
      </v-flex>

      <v-flex md12 v-if="visibilityAlertMessages.length > 0">
        <v-alert type="info" :value="visibilityAlertMessages.length > 0">
          <ul>
            <li v-for="note of visibilityAlertMessages" :key="note">
              {{ note }}
            </li>
          </ul>
        </v-alert>
      </v-flex>
      <v-flex md12>
        <v-form ref="noteEditorForm">
          <v-flex md12>
            <v-layout
              row
              wrap
              v-if="isAddingNote && currentlyEditingNote.type !== undefined"
            >
              <v-flex md12 px-3>
                <v-layout
                  py-1
                  pb-2
                  v-if="jobDetails && pudItemList && enableAddNoteToSelect"
                  align-center
                >
                  <h6 class="mr-4">Add Note to:</h6>
                  <v-flex>
                    <v-select
                      label="Adding note to"
                      :disabled="
                        !isEdited ||
                        isEditingExistingNote ||
                        disableSelectsForNoteType
                      "
                      hide-details
                      :items="pudItemList"
                      item-text="longName"
                      item-value="id"
                      outline
                      class="v-solo-custom"
                      v-model="selectedPudItemId"
                      autofocus
                      @change="
                        currentlyEditingNote.visibleTo =
                          returnVisibilityForJobNoteLevel(
                            $event === 'job'
                              ? JobNoteLevel.JOB
                              : JobNoteLevel.PUD_ITEM,
                          )
                      "
                    >
                    </v-select>
                  </v-flex>
                </v-layout>
                <v-layout py-1 v-if="enableVisibilitySelect">
                  <v-flex>
                    <v-select
                      label="Visible To"
                      outline
                      class="v-solo-custom"
                      :class="{
                        required: !isEditingNote,
                      }"
                      hide-details
                      :disabled="!isEdited || disableSelectsForNoteType"
                      multiple
                      :items="filteredVisibilityList"
                      item-text="longName"
                      item-value="id"
                      color="orange"
                      :rules="[
                        !isEditingNote ? validationRules.listRequired : true,
                      ]"
                      v-model="currentlyEditingNote.visibleTo"
                    >
                    </v-select>
                  </v-flex>
                </v-layout>
              </v-flex>
              <v-flex md12 px-3 v-if="currentlyEditingNote.type.id === 2">
                <v-layout py-1>
                  <v-flex>
                    <v-select
                      label="Add this note to jobs?"
                      outline
                      class="v-solo-custom required"
                      hide-details
                      :disabled="!isEdited"
                      :items="addToJobTypeOptions"
                      item-text="longName"
                      item-value="id"
                      :rules="[validationRules.required]"
                      color="orange"
                      v-model="
                        currentlyEditingNote.type.communicationDetails
                          .addToJobType
                      "
                    >
                    </v-select>
                  </v-flex>
                </v-layout>
              </v-flex>
              <v-flex md12 px-3>
                <v-textarea
                  v-model="currentlyEditingNote.body"
                  placeholder="Note contents"
                  hide-details
                  :disabled="isEditingExistingNote || awaitingSaveResponse"
                  color="orange"
                  class="v-solo-custom"
                  outline
                ></v-textarea>
              </v-flex>
              <v-flex md12>
                <v-layout px-2 pt-2>
                  <v-btn @click="cancelChanges()" flat color="red">
                    Cancel
                  </v-btn>
                  <ConfirmationDialog
                    v-if="allowDelete"
                    buttonText="Remove Note"
                    message="Please confirm you would like to remove this note. This action is irreversible."
                    title="Confirm Note Removal"
                    @confirm="deleteNote"
                    :isOutlineButton="true"
                    buttonColor="error"
                    confirmationButtonText="Confirm and Remove"
                    cancelButtonText="Cancel"
                  />
                  <v-spacer></v-spacer>
                  <ConfirmationDialog
                    v-if="!isEditingNote"
                    :buttonText="'Add Note ' + actionButtonText"
                    message="Please confirm you would like to cancel this job. This action is irreversible."
                    title="Confirm Job Cancellation"
                    @confirm="saveCommunication"
                    :buttonDisabled="
                      currentlyEditingNote.body.length === 0 ||
                      currentlyEditingNote.visibleTo.length === 0
                    "
                    :isOutlineButton="false"
                    :buttonColor="'blue'"
                    :confirmationButtonText="'Yes'"
                    :cancelButtonText="'No'"
                    :isCheckbox="false"
                    :isLoading="awaitingSaveResponse"
                    :dialogIsActive="isJobCancellation"
                  />
                  <v-btn
                    @click="saveCommunication()"
                    depressed
                    v-else
                    color="blue"
                  >
                    Save Changes
                  </v-btn>
                </v-layout>
              </v-flex>
            </v-layout>
          </v-flex>
        </v-form>
      </v-flex>
      <v-flex md12>
        <v-layout
          align-center
          px-1
          v-if="!isAddingNote && enableCommunicationTypeSelect"
        >
          <v-flex pb-1>
            <v-select
              hide-details
              :items="customCommunicationTypeList"
              :disabled="!isEdited"
              item-text="longName"
              color="orange"
              item-value="id"
              label="Text"
              outline
              class="v-solo-custom"
              v-model="selectedViewingCommunicationType"
            ></v-select>
          </v-flex>
          <span class="custom-input" v-if="jobDetails">
            <input id="notes-editor-4" type="checkbox" v-model="showPudItems" />
            <label for="notes-editor-4">Show Stops</label>
          </span>
        </v-layout>
        <NotesList
          :style="!isEdited ? 'opacity: 0.7' : ''"
          v-if="showCommunicationList"
          :isBookingScreen="isBookingScreen"
          :communications="notesList"
          :allowEdit="allowEdit"
          :showVisibilityTypeName="true"
          :showAddToJobType="true"
          @editNote="editNoteLocally"
        >
        </NotesList>
      </v-flex>
    </v-layout>
  </section>
</template>
<script setup lang="ts">
import NotesList from '@/components/common/notes_list/notes_list.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import ChatMessage from '@/interface-models/Generic/ChatConversation/ChatMessage';
import AddNoteToJobRequest from '@/interface-models/Generic/Communication/AddNoteToJobRequest';
import Communication from '@/interface-models/Generic/Communication/Communication';
import { CommunicationType } from '@/interface-models/Generic/Communication/CommunicationType';
import {
  AddToJobType,
  ClientInstructions,
} from '@/interface-models/Generic/Communication/CommunicationTypes/ClientInstructions';
import communicationTypeList, {
  CommunicationTypeList,
} from '@/interface-models/Generic/Communication/CommunicationTypes/CommunicationTypeList';
import GenericCommunication from '@/interface-models/Generic/Communication/CommunicationTypes/GenericCommunication';
import JobCommunication from '@/interface-models/Generic/Communication/CommunicationTypes/JobCommunication';
import {
  CommunicationVisibility,
  communicationVisibility,
} from '@/interface-models/Generic/Communication/CommunicationVisibility';
import { JobNoteLevel } from '@/interface-models/Generic/Communication/JobNoteLevel';
import { JobEventType } from '@/interface-models/Jobs/Event/JobEventType';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { useJobStore } from '@/store/modules/JobStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { sessionManager } from '@/store/session/SessionState';
import moment from 'moment-timezone';
import { v4 as uuidv4 } from 'uuid';
import { ComputedRef, Ref, computed, onMounted, ref, watch } from 'vue';

const emit = defineEmits<{
  (event: 'setIsAddingNote', payload: boolean): void;
  (event: 'saveParentDocument'): void;
}>();

const props = withDefaults(
  defineProps<{
    type: number;
    communications: Communication[];
    isEdited?: boolean;
    isAddingNote?: boolean;
    isBookingScreen?: boolean;
    jobDetails?: JobDetails;
    isJobServiceFailure?: boolean;
    isJobCancellation?: boolean;
    isJobReleaseForEditing?: boolean;
    isRestoreCancelledJob?: boolean;
    isDispatchNote?: boolean;
    isStartOfDayCheckNote?: boolean;
    enableVisibilitySelect?: boolean;
    enableAddNoteToSelect?: boolean;
    isClientVisibilityOnly?: boolean;
    enableCommunicationTypeSelect?: boolean;

    // If true, then it means we're being passed a note as a prop (currentlyEditingNoteDetails)
    isEditingExistingNote?: boolean;
    currentlyEditingNoteDetails?: AddNoteToJobRequest;

    // If true, then emit to parent to send request to save the parent document. Used in Client and FleetAssetOwner admin pages
    emitSaveEventBackToParent?: boolean;

    // Controls whether the edit cog is shown in notes list, and whether the
    // delete button is available in edit mode
    allowEdit?: boolean;
    allowDelete?: boolean;

    // Used to distinguish between a job note or a pud note, such that we can
    // set the default visibility.
    // NOTE: This is only used when type = 3 for JobCommunication
    jobNoteLevel?: JobNoteLevel | null;
  }>(),
  {
    isBookingScreen: false,
    isJobServiceFailure: false,
    isJobCancellation: false,
    isJobReleaseForEditing: false,
    isRestoreCancelledJob: false,
    isDispatchNote: false,
    isStartOfDayCheckNote: false,
    enableVisibilitySelect: true,
    enableAddNoteToSelect: true,
    isClientVisibilityOnly: false,
    enableCommunicationTypeSelect: true,
    isEditingExistingNote: false,
    emitSaveEventBackToParent: false,
    allowEdit: false,
    allowDelete: false,
    jobDetails: undefined,
    currentlyEditingNoteDetails: undefined,
    jobNoteLevel: null,
  },
);

const jobStore = useJobStore();

const noteEditorForm: Ref<any> = ref(null);

const currentlyEditingNote: Ref<Communication> = ref(new Communication());
const showCommunicationList: Ref<boolean> = ref(true);
const showPudItems: Ref<boolean> = ref(false);
const selectedViewingCommunicationType: Ref<number> = ref(4);
const selectedPudItemId: Ref<string> = ref('job');
const customCommunicationTypeList: Ref<CommunicationTypeList[]> = ref([]);
const awaitingSaveResponse: Ref<boolean> = ref(false);

const editingExistingNoteLocally: Ref<boolean> = ref(false);

/**
 * Returns true if we're either editing a note from a prop, or editing a note locally
 */
const isEditingNote: ComputedRef<boolean> = computed(() => {
  return props.isEditingExistingNote || editingExistingNoteLocally.value;
});

const actionButtonText: ComputedRef<string> = computed(() => {
  let actionButtonString = '';
  if (props.isJobServiceFailure && !props.isJobCancellation) {
    actionButtonString += jobHasActiveServiceFailure.value
      ? ' and remove service failure'
      : ' and apply service failure';
  }
  if (props.isRestoreCancelledJob) {
    actionButtonString += ' and restore job';
  }
  if (props.isJobReleaseForEditing) {
    actionButtonString += ' and release for editing';
  }
  if (props.isJobCancellation && !props.isJobServiceFailure) {
    actionButtonString += ' and cancel job';
  }
  if (props.isJobCancellation && props.isJobServiceFailure) {
    actionButtonString += ' and cancel job with service failure';
  }
  return actionButtonString;
});

const addToJobTypeOptions = [
  {
    id: AddToJobType.AUTO,
    longName: 'Add this note to all jobs booked for this client',
  },
  {
    id: AddToJobType.PROMPT,
    longName: 'Prompt before adding this note to job',
  },
  {
    id: AddToJobType.NOTIFY,
    longName:
      'Display this note as a notification when booking a job for this client',
  },
  {
    id: AddToJobType.NOTIFY_AT_PRICING,
    longName:
      'Display this note as a notification when pricing a job for this client',
  },
  {
    id: AddToJobType.NEVER,
    longName: `Don't add this note to jobs booked for this client`,
  },
];

const disableSelectsForNoteType: ComputedRef<boolean> = computed(() => {
  return (
    props.isJobServiceFailure ||
    props.isJobCancellation ||
    props.isRestoreCancelledJob ||
    props.isDispatchNote ||
    props.isStartOfDayCheckNote
  );
});

const filteredVisibilityList: ComputedRef<CommunicationVisibility[]> = computed(
  () => {
    const visibilityList = JSON.parse(JSON.stringify(communicationVisibility));

    if (
      !props.isJobServiceFailure &&
      !props.isJobCancellation &&
      !props.isRestoreCancelledJob &&
      !props.isDispatchNote &&
      !props.isStartOfDayCheckNote
    ) {
      return communicationVisibility.filter(
        (x: CommunicationVisibility) => ![3, 4, 7, 8].includes(x.id),
      );
    }

    if (props.isJobServiceFailure && jobHasActiveServiceFailure.value) {
      const foundSelectedServiceFailureItem = visibilityList.find(
        (x: CommunicationVisibility) => x.id === 4,
      );
      if (foundSelectedServiceFailureItem) {
        foundSelectedServiceFailureItem.longName = 'Remove Job Service Failure';
      }
      return visibilityList;
    }

    if (props.isRestoreCancelledJob) {
      const foundSelectedServiceFailureItem = visibilityList.find(
        (x: CommunicationVisibility) => x.id === 3,
      );
      if (foundSelectedServiceFailureItem) {
        foundSelectedServiceFailureItem.longName = 'Restore Cancelled Job';
      }
      return visibilityList;
    }

    return communicationVisibility;
  },
);

const jobHasActiveServiceFailure: ComputedRef<boolean> = computed(() => {
  return props.jobDetails?.serviceFailure ?? false;
});

const isDriverPayQuery: ComputedRef<boolean> = computed(() => {
  return currentlyEditingNote.value.visibleTo?.includes(5) ?? false;
});

const isClientVisibleNote: ComputedRef<boolean> = computed(() => {
  return (
    ((currentlyEditingNote.value.visibleTo?.includes(1) ?? false) ||
      currentlyEditingNote.value.visibleTo?.includes(6)) ??
    false
  );
});

const visibilityAlertMessages: ComputedRef<string[]> = computed(() => {
  const alertMessages: string[] = [];
  if (isDriverPayQuery.value) {
    alertMessages.push(
      'Pay query notes will appear on the subcontractor RCTI.',
    );
  }
  if (isClientVisibleNote.value) {
    alertMessages.push('Client notes will appear on the client invoice.');
  }
  return alertMessages;
});

const notesList: ComputedRef<Communication[]> = computed(() => {
  let notes: Communication[] = [];
  if (props.jobDetails) {
    if (showPudItems.value) {
      notes = (props.jobDetails.notes ?? []).concat(
        props.jobDetails.pudItems.flatMap((pud) => pud.notes ?? []),
      );
    } else {
      notes = props.jobDetails.notes ?? [];
    }
  } else {
    notes = props.communications ?? [];
  }
  return filterNotesByViewingType(notes);
});

const pudItemList: ComputedRef<OptionList[] | undefined> = computed(() => {
  if (!props.jobDetails) {
    return;
  }
  const filteredList = props.jobDetails.pudItems.filter(
    (pud) => pud.legTypeFlag === 'P' || pud.legTypeFlag === 'D',
  );
  const optionList: OptionList[] = [
    {
      id: 'job',
      longName: 'Job Notes',
      index: -1,
    },
  ];
  filteredList.forEach((pud, index) => {
    let longNameValue = `Leg ${index + 1}`;
    longNameValue += pud.customerDeliveryName
      ? ` - ${pud.customerDeliveryName}`
      : '';
    optionList.push({
      id: props.jobDetails?.jobId ? pud.pudId : index,
      longName: longNameValue,
      index,
    });
  });
  return optionList;
});

interface OptionList {
  id: string | number;
  longName: string;
  index: number;
}

function filterNotesByViewingType(noteList: Communication[]): Communication[] {
  if (selectedViewingCommunicationType.value === 4) {
    return noteList;
  }
  return noteList.filter(
    (item) => item.type?.id === selectedViewingCommunicationType.value,
  );
}

/**
 * Handles emit from the NotesList component to edit a note.
 * @param note - The note to edit.
 */
function editNoteLocally(note: Communication) {
  currentlyEditingNote.value = Object.assign(new Communication(), note);
  editingExistingNoteLocally.value = true;
  emit('setIsAddingNote', true);
}

/**
 * Returns a new Communication object with default values.
 */
function returnNewCommunicationObject(): Communication {
  const note: Communication = {
    id: uuidv4().split('-').join(''),
    epoch: moment().valueOf(),
    type: communicationDetailType(props.type ?? 0),
    user: sessionManager.getUserName(),
    body: '',
    hidden: false,
  } as Communication;
  // If we're returning a new ClientInstructions note, set the default value for
  // AddToJobType to AUTO.
  if (note.type!.id === 2) {
    note.type!.communicationDetails.addToJobType = AddToJobType.AUTO;
  }
  // Return initialised object
  return Object.assign(new Communication(), note);
}

watch(() => props.isAddingNote, updateFormView);

/**
 * Called when the value of the isAddingNote prop changes. If the value
 * is true, the newCommunication object is set to a new Communication object (or
 * an existing note if editing).
 * @param value - The value of the isAddingNote prop.
 */
function updateFormView(value: boolean | undefined) {
  // If true, initialise the form view
  if (value) {
    // If we're editing an existing note, set the currentlyEditingNote to the
    // note from the prop
    if (props.isEditingExistingNote && props.currentlyEditingNoteDetails) {
      currentlyEditingNote.value = Object.assign(
        currentlyEditingNote.value,
        props.currentlyEditingNoteDetails.note,
      );
    } else {
      if (!editingExistingNoteLocally.value) {
        currentlyEditingNote.value = returnNewCommunicationObject();
        if (currentlyEditingNote.value.type?.id === 3 && props.jobDetails) {
          currentlyEditingNote.value.type.communicationDetails.currentStatus =
            props.jobDetails.workStatus;
        }
      }
    }
    showCommunicationList.value = false;
  } else {
    showCommunicationList.value = true;
  }
}

/**
 * Returns a CommunicationType object based on the provided type (from props).
 * Conditionally sets the communicationDetails property based on the type, and
 * sets the visibleTo property based on the isClientVisibilityOnly prop.
 * @param type - The type id of communication to create.
 * @returns A CommunicationType object.
 */
function communicationDetailType(type: number): CommunicationType {
  switch (type) {
    case 1:
      return {
        id: 1,
        communicationDetails: new ChatMessage(),
      };
    case 2:
      return {
        id: 2,
        communicationDetails: new ClientInstructions(),
      };
    case 3:
      const type: CommunicationType = {
        id: 3,
        communicationDetails: new JobCommunication(),
      };
      if (props.isClientVisibilityOnly) {
        type.communicationDetails.visibleTo = [6];
      } else {
        type.communicationDetails.visibleTo = returnVisibilityForJobNoteLevel(
          props.jobNoteLevel,
        );
      }
      return type;
    default:
      return {
        id: 0,
        communicationDetails: new GenericCommunication(),
      };
  }
}

/**
 * Used to return the visibility for provided JobNoteLevel. This is used to set
 * the default visibility based on where the note is being added. Only used when
 * type = 3 for JobCommunication.
 * @param jobNoteLevel - The JobNoteLevel to return visibility for.
 */
function returnVisibilityForJobNoteLevel(
  jobNoteLevel: JobNoteLevel | null,
): number[] {
  if (jobNoteLevel === JobNoteLevel.JOB) {
    return [2]; // Operations
  } else if (jobNoteLevel === JobNoteLevel.PUD_ITEM) {
    return [2, 0]; // Operations and Driver
  } else {
    return [2]; // Default case, return Operations visibility
  }
}

/**
 * Cancels the currently editing note and emits a setIsAddingNote event. Used in
 * template on cancel button.
 */
function cancelChanges() {
  currentlyEditingNote.value = new Communication();
  editingExistingNoteLocally.value = false;
  emit('setIsAddingNote', false);
}

function deleteNote() {
  if (!props.allowDelete) {
    return;
  }
  if (editingExistingNoteLocally.value && currentlyEditingNote.value) {
    const foundIndex = props.communications.findIndex(
      (x) => x.id === currentlyEditingNote.value.id,
    );
    if (foundIndex !== -1) {
      props.communications.splice(foundIndex, 1);
    }
    editingExistingNoteLocally.value = false;
    emit('setIsAddingNote', false);
    if (props.emitSaveEventBackToParent) {
      emit('saveParentDocument');
    }
  }
}
/**
 * Sends a request to save the currently editing note to the JobDetails document.
 */
async function saveCommunication() {
  if (!noteEditorForm.value?.validate()) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }
  if (props.jobDetails?.jobId) {
    awaitingSaveResponse.value = true;

    // Send request to add or update note, as well as any other job events
    await saveNoteToJob();
    await saveJobEvents();

    // Reset component state
    awaitingSaveResponse.value = false;
    emit('setIsAddingNote', false);
    if (props.emitSaveEventBackToParent) {
      emit('saveParentDocument');
    }
  } else {
    // If we're editing an existing note within this component, we need to update the
    // note in the parent component.
    if (editingExistingNoteLocally.value) {
      const foundIndex = props.communications.findIndex(
        (x) => x.id === currentlyEditingNote.value.id,
      );
      if (foundIndex !== -1) {
        props.communications.splice(foundIndex, 1, currentlyEditingNote.value);
      }
    } else {
      // Otherwise push into props.communications
      props.communications.push(currentlyEditingNote.value);
    }
    editingExistingNoteLocally.value = false;
    emit('setIsAddingNote', false);
    if (props.emitSaveEventBackToParent) {
      emit('saveParentDocument');
    }
  }
}

/**
 * Sends a request to save the currently editing note to the JobDetails
 * document.
 */
async function saveNoteToJob() {
  if (props.isJobServiceFailure && !props.isJobCancellation) {
    let serviceFailureType = 'Service Failure Add: ';
    if (jobHasActiveServiceFailure.value) {
      serviceFailureType = 'Service Failure Remove: ';
    }
    currentlyEditingNote.value.body = serviceFailureType.concat(
      currentlyEditingNote.value.body,
    );
  }
  let request: AddNoteToJobRequest;
  if (props.isEditingExistingNote && props.currentlyEditingNoteDetails) {
    request = props.currentlyEditingNoteDetails;
  } else {
    if (selectedPudItemId.value !== '' && selectedPudItemId.value !== 'job') {
      request = {
        jobId: props.jobDetails!.jobId!,
        note: currentlyEditingNote.value,
        pudId: selectedPudItemId.value,
      };
    } else {
      request = {
        jobId: props.jobDetails!.jobId!,
        note: currentlyEditingNote.value,
      };
    }
  }
  await jobStore.addNoteToJob(request);
}

/**
 * Sends various requests based on the provided props. We request them
 * sequentially to avoid race conditions.
 */
async function saveJobEvents() {
  if (props.isJobServiceFailure) {
    await jobStore.updateJobStatus(
      props.jobDetails!.jobId!,
      JobEventType.ServiceFailure,
    );
  }
  if (props.isJobCancellation) {
    await jobStore.updateJobStatus(
      props.jobDetails!.jobId!,
      JobEventType.CancelledJob,
    );
  }
  if (props.isJobReleaseForEditing) {
    useOperationsStore().setReleaseForEditingRequestMade(true);
    await jobStore.updateJobStatus(
      props.jobDetails!.jobId!,
      JobEventType.CompletedJob,
    );
  }
}

watch(
  () => props.isJobServiceFailure,
  (value: boolean) => {
    if (!currentlyEditingNote.value.type) {
      return;
    }
    if (value) {
      const containsServiceFailure = (
        currentlyEditingNote.value.type
          .communicationDetails as GenericCommunication
      ).visibleTo.includes(4);
      if (!containsServiceFailure) {
        (
          currentlyEditingNote.value.type
            .communicationDetails as GenericCommunication
        ).visibleTo.push(4);
      }
    } else {
      const foundIndexOfServiceFailure = (
        currentlyEditingNote.value.type
          .communicationDetails as GenericCommunication
      ).visibleTo.findIndex((x) => x === 4);
      if (foundIndexOfServiceFailure !== -1) {
        (
          currentlyEditingNote.value.type
            .communicationDetails as GenericCommunication
        ).visibleTo.splice(foundIndexOfServiceFailure, 1);
      }
    }
  },
);

/**
 * Sets the note visibility based on the props set in the OperationsModule.
 * See CommunicationVisibility.ts for static list.
 */
function setNoteVisibilityFromProps() {
  if (props.isJobServiceFailure) {
    currentlyEditingNote.value.visibleTo = [4];
  }
  if (props.isJobCancellation) {
    currentlyEditingNote.value.visibleTo = [3];
  }
  if (props.isJobReleaseForEditing) {
    currentlyEditingNote.value.visibleTo = [2];
  }
  if (props.isRestoreCancelledJob) {
    currentlyEditingNote.value.visibleTo = [3];
  }
  if (props.isDispatchNote) {
    currentlyEditingNote.value.visibleTo = [7];
  }
  if (props.isStartOfDayCheckNote) {
    currentlyEditingNote.value.visibleTo = [8];
    const username = sessionManager.getActiveUser();
    if (username) {
      currentlyEditingNote.value.body = `This job was checked by ${username}`;
    }
  }
}

onMounted(() => {
  const typeList: CommunicationTypeList[] = communicationTypeList;
  const newType: CommunicationTypeList = {
    id: 4,
    longName: 'All',
  };
  typeList.splice(0, 0, newType);
  customCommunicationTypeList.value = typeList;

  currentlyEditingNote.value = new Communication();
  currentlyEditingNote.value.id = uuidv4().split('-').join('');

  updateFormView(props.isAddingNote ?? false);
  setNoteVisibilityFromProps();
});
</script>
<style scoped lang="scss">
.notes-editor {
  width: 100%;
  height: 100%;
  border-radius: $border-radius-base;
  padding: 20px;
  background-color: var(--background-color-400);
}
</style>
