<v-flex class="md12 pud-item-container px-2 pt-1">
  <v-layout justify-space-between wrap class="suburb-container">
    <v-flex md12>
      <v-layout align-center>
        <span class="pr-1 suburb-title"
          ><span class="accent-text--primary pr-2">{{pudNumber}}.</span
          ><span v-if="pudItem.legTypeFlag !== 'B'" class="pr-1"
            >{{pudItem.address.formattedAddress}}</span
          >
          <span v-if="pudItem.legTypeFlag === 'P'">(Pickup)</span>
          <span v-if="pudItem.legTypeFlag === 'D'">(Drop-off)</span>
          <span v-if="pudItem.legTypeFlag === 'B'">BREAK</span>
        </span>
        <v-flex px-2><v-divider></v-divider></v-flex>
        <!-- <v-btn
          depressed
          color="blue"
          small
          @click="addReference"
          :disabled="readOnly"
        >
          Add Reference
        </v-btn> -->
      </v-layout>
    </v-flex>

    <v-flex md12>
      <BookingReferences
        :isBeforePricing="true"
        :readOnlyView="readOnly"
        :requirementsMet.sync="pudReferenceRequirementsMet"
        :isPud="true"
        :isDropoff="pudItem.legTypeFlag === 'D'"
        :jobReferences="pudItem.legTypeFlag === 'P' ? pudItem.pickupReference : pudItem.dropoffReference"
        :clientReferences="clientDetails ? clientDetails.references.pudScreen : []"
      />
    </v-flex>

    <v-flex md12>
      <v-text-field
        label="Weight (kg)"
        type="number"
        v-model.number="pudItem.weight"
        color="orange darken-2"
        box
        min="0"
        :class="weightIsRequired ? 'form-field-required' : ''"
        :rules="weightIsRequired ? [validationRules.required, validationRules.number] : []"
        @input="$emit('update:addOnStartingPrice', Number($event))"
        :disabled="readOnly"
      >
        <template v-slot:append-outer>
          <v-tooltip bottom>
            <template v-slot:activator="{ on }">
              <v-icon
                size="18"
                class="check-active"
                :disabled="readOnly"
                v-if="(weightIsRequired && pudItem.weight !== null && typeof pudItem.weight !== 'string') || !weightIsRequired"
                >far fa-check
              </v-icon>
              <v-icon
                v-else
                size="15"
                :disabled="readOnly"
                color="rgba(255, 255, 255, 0.561)"
              >
                fas fa-exclamation-triangle
              </v-icon></template
            >
            <span>Add Weight</span>
          </v-tooltip>
        </template>
      </v-text-field>
    </v-flex>

    <v-flex md12 v-if="pudItem.legTypeFlag === 'P'">
      <PudManifest
        :manifestList="pudItem.manifest"
        :isJobDialog="true"
        :isDisabled="readOnly"
      />
    </v-flex>
  </v-layout>
</v-flex>
