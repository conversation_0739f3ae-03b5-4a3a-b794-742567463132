import ClientReferenceDetails from '@/interface-models/Client/ClientDetails/References/ClientReferenceDetails';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { Component, Prop, Vue } from 'vue-property-decorator';
import BookingReferences from '@/components/operations/BookJob/booking_references/index.vue';
import JobReferenceDetails from '@/interface-models/Jobs/JobReferenceDetails';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import { WeightRequirement } from '@/interface-models/Jobs/WeightRequirement/WeightRequirement';
import PudManifest from '@/components/operations/BookJob/pud_manifest/index.vue';
import { Manifest } from '@/interface-models/Jobs/Manifest';
import { Validation } from '@/interface-models/Generic/Validation';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
@Component({
  components: { BookingReferences, PudManifest },
})
export default class PudRequirements extends Vue {
  @Prop() public pudItem: PUDItem;
  @Prop() public clientPudReferences: ClientReferenceDetails[];
  @Prop() public pudNumber: number;
  @Prop() public clientDetails: ClientDetails;
  @Prop({ default: false }) public readOnly: boolean;
  @Prop({ default: null }) public weightRequirement: WeightRequirement | null;
  @Prop({ default: false }) public isFirstPickupInJob: boolean;
  public pudReferenceRequirementsMet: boolean = false;

  public addReference() {
    const references =
      this.pudItem.legTypeFlag === 'P'
        ? this.pudItem.pickupReference
        : this.pudItem.dropoffReference;

    references.push(new JobReferenceDetails(4, ''));
  }

  get validationRules(): Validation {
    return validationRules;
  }

  get weightIsRequired() {
    return this.pudItem.weightIsRequired(
      this.weightRequirement,
      this.isFirstPickupInJob,
      true,
    );
  }

  public addManifest(pudId: string) {
    if (!this.pudItem) {
      return;
    }

    this.pudItem.manifest.push(new Manifest());
  }
}
