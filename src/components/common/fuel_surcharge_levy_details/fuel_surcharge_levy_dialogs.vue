<template>
  <div>
    <!-- Create/Edit Dialog -->
    <ContentDialog
      width="800px"
      max-width="800px"
      :showDialog.sync="showCreateEditDialog"
      :title="
        props.isEditing ? 'Edit Fuel Surcharge' : 'Create New Fuel Surcharge'
      "
      @cancel="closeCreateEditDialog"
      contentPadding="ma-0"
      :showActions="false"
    >
      <v-flex>
        <v-layout
          row
          wrap
          class="app-theme__center-content--body dialog-content"
        >
          <v-flex md12>
            <div v-if="showCreateEditDialog">
              <!-- Tabbed interface for create mode (hide tabs for single item edit) -->
              <v-tabs
                v-if="!props.isEditing"
                v-model="createTab"
                dark
                tabs
                centered
                color="#24232a"
                slider-color="yellow"
              >
                <v-tab>New Group</v-tab>
                <v-tab>Add to Existing Group</v-tab>
              </v-tabs>

              <!-- Form content -->
              <v-layout row wrap class="body-scrollable--65 pa-2 mt-2">
                <v-form ref="createEditForm" v-model="formValid">
                  <v-layout row wrap pa-2>
                    <!-- TITLE -->
                    <v-layout row wrap align-center>
                      <h3 class="subheader--light">
                        {{
                          props.isEditing
                            ? 'Edit Fuel Surcharge'
                            : createTab === 1
                              ? 'Add To Existing Fuel Surcharge'
                              : 'Create New Fuel Surcharge'
                        }}
                      </h3>
                      <v-layout
                        align-center
                        v-if="
                          componentType !== FuelComponentType.CASH_SALE &&
                          !entityId &&
                          createTab !== 1 &&
                          !props.isEditing
                        "
                      >
                        <v-divider class="ma-2" />
                        <v-radio-group
                          v-model="isDivisionLevel"
                          row
                          @change="onLevelChange"
                        >
                          <v-radio
                            label="Division Fuel"
                            :value="true"
                            :disabled="props.isEditing"
                          ></v-radio>
                          <v-radio
                            :label="
                              componentType === FuelComponentType.CLIENT
                                ? 'Client Fuel'
                                : 'Vehicle Fuel'
                            "
                            :value="false"
                          ></v-radio>
                        </v-radio-group>
                      </v-layout>
                    </v-layout>

                    <!-- ADD TO GROUP -->
                    <v-flex
                      md12
                      class="mt-2"
                      v-if="createTab === 1 && !props.isEditing"
                    >
                      <v-select
                        v-model="selectedExistingGroup"
                        :items="existingGroupOptions"
                        item-text="name"
                        item-value="uuid"
                        label="Select Existing Group"
                        hint="Select Existing Group"
                        class="v-solo-custom"
                        flat
                        solo
                        persistent-hint
                        :rules="[validate.required]"
                        @change="onExistingGroupChange"
                      />
                    </v-flex>

                    <!-- NAME -->
                    <v-flex md12 class="mt-2">
                      <v-flex>
                        <v-layout
                          align-center
                          class="form-field-label-container"
                        >
                          <h6 class="pr-3 pb-0 form-field-required-marker">
                            Name:
                          </h6>
                        </v-layout>
                      </v-flex>
                      <v-text-field
                        v-model="formData.name"
                        label="Fuel Surcharge Name"
                        hint="Fuel Surcharge Name"
                        :rules="[validate.required]"
                        required
                        class="v-solo-custom"
                        flat
                        solo
                        persistent-hint
                      ></v-text-field>
                    </v-flex>

                    <!-- SERVICE TYPE -->
                    <v-flex md12>
                      <v-layout align-center justify-space-between>
                        <h6 class="pr-3 pb-0 form-field-required-marker">
                          Select Service Type:
                        </h6>
                        <v-checkbox
                          v-model="applyToAllServiceTypes"
                          label="Apply to all"
                          :disabled="false"
                          color="primary"
                          @change="onApplyToAllServiceTypesChange"
                        ></v-checkbox>
                      </v-layout>
                      <v-layout md12 row wrap>
                        <v-select
                          v-model="formData.serviceTypeId"
                          :items="availableServiceTypes"
                          item-text="longServiceTypeName"
                          item-value="serviceTypeId"
                          label="Service Type"
                          :disabled="applyToAllServiceTypes"
                          clearable
                          solo
                          flat
                          class="v-solo-custom"
                          hide-details
                          multiple
                          chips
                          hint="Service types prefilled from selected group (editable)"
                          persistent-hint
                          :rules="
                            !applyToAllServiceTypes ? [validate.required] : []
                          "
                        ></v-select>
                      </v-layout>
                    </v-flex>

                    <!-- Client Selection (when specific level and CLIENT component type) -->
                    <v-flex
                      v-if="
                        !isDivisionLevel &&
                        componentType === FuelComponentType.CLIENT &&
                        !entityId &&
                        (createTab === 0 ||
                          (createTab === 1 && selectedExistingGroup))
                      "
                      md12
                      class="mt-2"
                    >
                      <v-layout align-center class="form-field-label-container">
                        <h6 class="pr-3 pb-0 form-field-required-marker">
                          {{
                            props.isEditing || createTab === 1
                              ? 'Current Client(s):'
                              : 'Select Client(s):'
                          }}
                        </h6>
                      </v-layout>
                      <v-flex md12 v-if="!props.isEditing && createTab === 0">
                        <v-autocomplete
                          v-model="selectedNationalClients"
                          :items="nationalClients"
                          item-text="name"
                          item-value="_id"
                          label="Add Client From National Client"
                          class="v-solo-custom mt-2 mb-2"
                          flat
                          multiple
                          chips
                          solo
                          clearable
                          @change="onNationalClientsChange"
                          hint="Selecting a national client will add all its related clients"
                          persistent-hint
                        ></v-autocomplete>
                      </v-flex>
                      <v-flex md12>
                        <v-autocomplete
                          v-model="selectedClientIds"
                          :items="availableClients"
                          item-text="clientDisplayName"
                          item-value="clientId"
                          :label="
                            props.isEditing
                              ? 'Current Clients'
                              : 'Select Clients'
                          "
                          multiple
                          chips
                          deletable-chips
                          :rules="[validate.required]"
                          :disabled="!!entityId"
                          solo
                          flat
                          class="v-solo-custom mt-2"
                          :hint="
                            props.isEditing
                              ? 'These are the clients this surcharge currently applies to'
                              : 'Select one or more clients to apply this rate variation to'
                          "
                          persistent-hint
                          @change="onClientSelectionChange"
                        ></v-autocomplete>
                      </v-flex>
                    </v-flex>

                    <!-- Fleet Asset Selection (when specific level and FLEET_ASSET component type) -->
                    <v-flex
                      md12
                      class="mt-2"
                      v-if="
                        !isDivisionLevel &&
                        componentType === FuelComponentType.FLEET_ASSET &&
                        !entityId &&
                        (createTab === 0 ||
                          (createTab === 1 && selectedExistingGroup))
                      "
                    >
                      <v-layout align-center class="form-field-label-container">
                        <h6 class="pr-3 pb-0 form-field-required-marker">
                          {{
                            props.isEditing || createTab === 1
                              ? 'Current Fleet Asset(s):'
                              : 'Select Fleet Asset(s):'
                          }}
                        </h6>
                      </v-layout>

                      <v-autocomplete
                        v-model="formData.fleetAssetIds"
                        :items="availableFleetAssets"
                        :item-text="formatFleetAsset"
                        item-value="fleetAssetId"
                        :label="
                          props.isEditing || createTab === 1
                            ? 'Current Fleet Assets'
                            : 'Apply to Fleet Assets'
                        "
                        :hint="
                          props.isEditing || createTab === 1
                            ? 'Current Fleet Assets'
                            : 'Apply to Fleet Assets'
                        "
                        multiple
                        chips
                        class="v-solo-custom"
                        flat
                        solo
                        persistent-hint
                        deletable-chips
                        :rules="[validate.required]"
                        :disabled="!!entityId"
                      >
                      </v-autocomplete>
                    </v-flex>

                    <!-- Rate Brackets Section -->
                    <v-flex md12 class="mt-4">
                      <v-layout align-center justify-space-between>
                        <h6 class="pr-3 pb-0 form-field-required-marker">
                          Rate Brackets
                        </h6>
                        <v-divider class="ma-2" />
                        <v-btn
                          v-if="
                            formData.fuelSurchargeApplicationType ===
                            FuelSurchargeType.RANGED_RATE
                          "
                          color="primary"
                          @click="addBracket"
                          small
                        >
                          <v-icon small class="pr-2">add</v-icon>
                          Add Bracket
                        </v-btn>
                      </v-layout>
                      <v-layout md12 row wrap pa-2>
                        <v-flex md6>
                          <v-select
                            v-model="formData.fuelSurchargeApplicationType"
                            :items="surchargeTypeOptions"
                            label="Surcharge Type"
                            hint="Surcharge Type"
                            class="v-solo-custom mt-2 mr-2"
                            flat
                            solo
                            persistent-hint
                            :rules="[validate.required]"
                            :disabled="
                              (currentEditingSurcharge && groupCount > 1) ||
                              (createTab !== 0 && !isEditing)
                            "
                          ></v-select>
                        </v-flex>

                        <!-- Range Determinant (for RANGED_RATE types) -->
                        <v-flex
                          md6
                          mb-4
                          v-if="
                            formData.fuelSurchargeApplicationType ===
                            FuelSurchargeType.RANGED_RATE
                          "
                        >
                          <v-select
                            v-model="formData.rangeDeterminant"
                            :items="rangeDeterminantOptions"
                            item-text="text"
                            item-value="value"
                            label="Range Determinant"
                            hint="How ranges should be calculated for this surcharge"
                            :rules="[validate.required]"
                            class="v-solo-custom mt-2 ml-4 form-field-required"
                            flat
                            solo
                            persistent-hint
                          ></v-select>
                        </v-flex>

                        <v-layout
                          row
                          align-center
                          md12
                          pa-2
                          v-for="(bracket, index) in formData.rateBrackets"
                          :key="index"
                        >
                          <v-flex>
                            <v-text-field
                              v-model.number="bracket.rate"
                              label="Rate (%)"
                              hint="Rate (%)"
                              type="number"
                              suffix="%"
                              step="0.1"
                              class="v-solo-custom mr-4"
                              flat
                              solo
                              persistent-hint
                              :rules="[validate.required, validate.nonNegative]"
                            ></v-text-field>
                          </v-flex>
                          <v-flex
                            md3
                            v-if="
                              formData.fuelSurchargeApplicationType ===
                              FuelSurchargeType.RANGED_RATE
                            "
                          >
                            <v-text-field
                              v-model.number="bracket.bracketMin"
                              label="Min Range"
                              hint="Min Range"
                              type="number"
                              suffix="KM"
                              :rules="[validate.nonNegative]"
                              class="v-solo-custom mr-2"
                              flat
                              solo
                              persistent-hint
                              readonly
                            ></v-text-field>
                          </v-flex>
                          <v-flex
                            md3
                            v-if="
                              formData.fuelSurchargeApplicationType ===
                              FuelSurchargeType.RANGED_RATE
                            "
                          >
                            <v-text-field
                              v-if="bracket.bracketMax !== -1"
                              v-model.number="bracket.bracketMax"
                              label="Max Range"
                              hint="Max Range"
                              type="number"
                              class="v-solo-custom mr-2"
                              flat
                              solo
                              suffix="KM"
                              :rules="[
                                validate.required,
                                validate.positiveInteger,
                              ]"
                              persistent-hint
                              @input="updateNextBracketMin(index)"
                              @focus="$event.target.select()"
                            >
                            </v-text-field>
                            <span v-else class="no-limit-text">NO LIMIT</span>
                          </v-flex>
                          <v-flex
                            pa-2
                            mb-4
                            ml-4
                            md3
                            class="range-text"
                            :class="returnRangeRateSummary(bracket, index)"
                            v-if="
                              formData.fuelSurchargeApplicationType ===
                              FuelSurchargeType.RANGED_RATE
                            "
                          >
                            {{ returnRangeRateSummary(bracket, index) }}
                          </v-flex>
                          <v-flex align-center mb-4>
                            <v-btn
                              icon
                              small
                              @click="removeBracket(index)"
                              :disabled="formData.rateBrackets.length <= 1"
                            >
                              <v-icon size="16">fal fa-times</v-icon>
                            </v-btn>
                          </v-flex>
                        </v-layout>
                      </v-layout>
                    </v-flex>

                    <!-- Date fields for adding to existing group -->
                    <v-flex md12>
                      <v-layout align-center justify-space-between class="mt-4">
                        <h6 class="pb-0">Date Range</h6>
                      </v-layout>
                      <v-layout md12 row wrap pa-2>
                        <v-flex md6 class="mt-2 pr-2">
                          <DatePickerBasic
                            :epochTime="formData.validFromDate"
                            labelName="Valid From"
                            :isRequired="true"
                            @setEpoch="handleValidFromDateChange"
                            soloInput
                            showHint
                            :validate="validate"
                            hintText="Valid From"
                          />
                        </v-flex>

                        <v-flex md6 class="mt-2 pl-2">
                          <DatePickerBasic
                            :epochTime="formData.validToDate"
                            labelName="Valid To"
                            :isRequired="true"
                            @setEpoch="handleValidToDateChange"
                            soloInput
                            showHint
                            :validate="validate"
                            hintText="Valid To"
                          />
                        </v-flex>
                      </v-layout>
                    </v-flex>
                  </v-layout>
                </v-form>
              </v-layout>
            </div>
            <v-flex md12 mt-4>
              <v-divider></v-divider>
              <v-layout align-center pa-2>
                <v-btn
                  outline
                  color="error"
                  @click="closeCreateEditDialog"
                  :disabled="awaitingSaveResponse"
                  >Cancel</v-btn
                >
                <v-spacer></v-spacer>
                <v-btn
                  color="primary"
                  @click="saveCurrentSurcharge()"
                  :disabled="!formValid || awaitingSaveResponse"
                  :loading="awaitingSaveResponse"
                  solo
                  block
                >
                  {{ props.isEditing ? 'Update' : 'Create' }}
                </v-btn>
              </v-layout>
            </v-flex>
          </v-flex>
        </v-layout>
      </v-flex>
    </ContentDialog>

    <!-- Bulk Edit Dialog -->
    <ContentDialog
      width="700px"
      :showDialog.sync="showBulkEditDialog"
      :title="`Bulk Edit Fuel Surcharges (${selectedSurchargesForBulkEdit.length}
            items)`"
      @cancel="closeBulkEditDialog"
      contentPadding="ma-0"
      :showActions="false"
    >
      <div>
        <v-layout
          row
          wrap
          class="app-theme__center-content--body dialog-content"
        >
          <v-flex md12>
            <div v-if="showBulkEditDialog">
              <!-- <v-form ref="bulkEditForm" v-model="bulkFormValid"> -->
              <v-layout row wrap class="body-scrollable--65" pa-3>
                <v-flex>
                  <v-layout align-center justify-space-between class="mt-4">
                    <h6 class="pr-3 pb-0 form-field-required-marker">
                      Surcharges Name
                    </h6>
                  </v-layout>
                  <v-flex md12>
                    <v-text-field
                      v-model="bulkEditData.name"
                      label="Fuel Surcharge Name (Optional)"
                      hint="Leave empty to keep existing names, or enter a name to update all selected surcharges"
                      persistent-hint
                      solo
                      flat
                      class="v-solo-custom mt-2"
                    ></v-text-field>
                  </v-flex>
                </v-flex>

                <!-- SERVICE TYPE -->
                <v-flex md12 class="mt-2">
                  <v-flex>
                    <v-layout align-center class="form-field-label-container">
                      <h6 class="pr-3 pb-0 form-field-required-marker">
                        Select Service Type:
                      </h6>
                      <v-checkbox
                        v-model="applyToAllServiceTypes"
                        label="Apply to all"
                        :disabled="false"
                        color="primary"
                        @change="onApplyToAllServiceTypesChange"
                      ></v-checkbox>
                    </v-layout>
                  </v-flex>
                  <v-flex>
                    <v-select
                      v-model="bulkEditData.serviceTypeIds"
                      :items="availableServiceTypes"
                      item-text="longServiceTypeName"
                      item-value="serviceTypeId"
                      label="Service Types"
                      :disabled="applyToAllServiceTypes"
                      clearable
                      solo
                      flat
                      class="v-solo-custom"
                      hide-details
                      multiple
                      chips
                      hint="Select service types to apply to all selected surcharges"
                      persistent-hint
                    ></v-select>
                  </v-flex>
                </v-flex>

                <v-flex v-if="!entityId && bulkEditLevel === 'CLIENT'">
                  <v-layout align-center justify-space-between>
                    <h6 class="pr-3 pb-0 form-field-required-marker mt-4">
                      Select Client(s)
                    </h6>
                  </v-layout>
                  <v-flex md12 class="mt-2">
                    <v-combobox
                      v-model="bulkEditData.clientIds"
                      :items="availableClients"
                      item-text="clientDisplayName"
                      item-value="clientId"
                      label="Select Clients (Optional)"
                      multiple
                      chips
                      deletable-chips
                      :disabled="false"
                      solo
                      flat
                      class="v-solo-custom"
                      hint="Leave empty to keep existing client assignments, or select clients to update all selected surcharges"
                      persistent-hint
                    ></v-combobox>
                  </v-flex>
                </v-flex>

                <v-flex v-if="!entityId && bulkEditLevel === 'FLEET ASSET'">
                  <v-layout align-center>
                    <h6 class="pr-3 pb-0 form-field-required-marker mt-4">
                      Select Fleet Asset(s)
                    </h6>
                  </v-layout>
                  <v-flex md12 class="mt-2">
                    <v-autocomplete
                      v-model="bulkEditData.fleetAssetIds"
                      :items="availableFleetAssets"
                      :item-text="formatFleetAsset"
                      item-value="fleetAssetId"
                      label="Apply to Fleet Assets (Optional)"
                      multiple
                      chips
                      deletable-chips
                      hint="Leave empty to keep existing fleet asset assignments, or select assets to update all selected surcharges"
                      persistent-hint
                      solo
                      flat
                      class="v-solo-custom"
                    ></v-autocomplete>
                  </v-flex>
                </v-flex>

                <v-flex md12>
                  <v-alert type="info" outlined>
                    <strong
                      >Selected Surcharges ({{
                        selectedSurchargesForBulkEdit.length
                      }}):</strong
                    >
                    <p class="mt-2 mb-2">
                      <small
                        >Only these specific surcharges will be updated:</small
                      >
                    </p>
                    <ul class="mt-2">
                      <li
                        v-for="surcharge in selectedSurchargesForBulkEdit"
                        :key="surcharge.tableId"
                      >
                        {{ surcharge.name }} (ID: {{ surcharge.tableId }}) -
                        {{
                          surcharge.validFromDate
                            ? returnFormattedDate(surcharge.validFromDate)
                            : 'N/A'
                        }}
                        to
                        {{
                          surcharge.validToDate
                            ? returnFormattedDate(surcharge.validToDate)
                            : 'N/A'
                        }}
                      </li>
                    </ul>
                  </v-alert>
                </v-flex>
              </v-layout>
              <!-- </v-form> -->
            </div>
            <v-flex md12 mt-4>
              <v-divider></v-divider>
              <v-layout align-center pa-2>
                <v-btn
                  outline
                  color="error"
                  @click="closeBulkEditDialog"
                  :disabled="awaitingSaveResponse"
                  >Cancel</v-btn
                >
                <v-spacer></v-spacer>
                <v-btn
                  color="primary"
                  @click="saveBulkEdit"
                  :disabled="awaitingSaveResponse"
                  :loading="awaitingSaveResponse"
                >
                  Update All ({{ selectedSurchargesForBulkEdit.length }})
                </v-btn>
              </v-layout>
            </v-flex>
          </v-flex>
        </v-layout>
      </div>
    </ContentDialog>

    <!-- Date Adjustment Confirmation Dialog -->
    <ContentDialog
      width="600px"
      :showDialog.sync="showDateAdjustmentDialog"
      :title="dateAdjustmentTitle"
      @cancel="cancelDateAdjustment"
      contentPadding="ma-0"
      :showActions="false"
    >
      <div>
        <v-layout
          row
          wrap
          class="app-theme__center-content--body dialog-content"
        >
          <v-flex md12 class="body-scrollable--65 body-min-height--65">
            <div class="date-adjustment-message pa-4">
              <!-- Date overlap adjustments -->
              <div v-if="dateAdjustmentTitle === 'Date Overlap Detected'">
                <h3 class="subheader--light mb-2">
                  <v-icon size="20" color="orange" class="mr-2">warning</v-icon>
                  Date overlap detected
                </h3>
                <strong class="mb-4">
                  The following adjustments will be made automatically:
                </strong>
                <ul
                  v-if="
                    dateAdjustmentDetails && dateAdjustmentDetails.length > 0
                  "
                >
                  <div
                    v-for="(adjustment, index) in dateAdjustmentDetails"
                    :key="index"
                  >
                    <ol>
                      <li>
                        <h2 class="subheader--light mt-4 mb-1">
                          {{ adjustment.surchargeName }}
                        </h2>

                        <v-layout class="pa-2">
                          FROM:
                          <h4 class="mr-4">
                            <strong class="ml-2">{{
                              adjustment.oldStartDate
                            }}</strong>
                            -
                            <strong>{{ adjustment.oldEndDate }}</strong>
                          </h4>
                          TO:
                          <h4>
                            <strong class="ml-2">{{
                              adjustment.newStartDate ?? adjustment.oldStartDate
                            }}</strong>
                            -
                            <strong>{{
                              adjustment.newEndDate ?? adjustment.oldEndDate
                            }}</strong>
                          </h4>
                        </v-layout>
                      </li>
                    </ol>
                  </div>
                </ul>
                <p class="mt-4 no-limit-text">
                  <em
                    >This ensures continuous date coverage with no gaps or
                    overlaps.</em
                  >
                </p>
              </div>

              <!-- Service type changes -->
              <div
                v-else-if="dateAdjustmentTitle === 'Bulk Service Type Changes'"
              >
                <p>
                  <strong
                    >Service types will be updated for all selected
                    surcharges.</strong
                  >
                </p>
                <p>Do you want to continue?</p>
              </div>

              <!-- Generic confirmation -->
              <div v-else>
                <p>Changes will be applied. Do you want to continue?</p>
              </div>
            </div>
          </v-flex>
          <v-flex md12 mt-4>
            <v-divider></v-divider>
            <v-layout align-center pa-2>
              <v-btn outline color="error" @click="cancelDateAdjustment"
                >Cancel</v-btn
              >
              <v-spacer></v-spacer>
              <v-btn color="primary" @click="confirmDateAdjustment">
                Continue
              </v-btn>
            </v-layout>
          </v-flex>
        </v-layout>
      </div>
    </ContentDialog>
  </div>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import {
  returnEndOfDayFromEpoch,
  returnFormattedDate,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { returnRangeRateSummary } from '@/helpers/RateHelpers/DistanceRateHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import ClientSearchSummary from '@/interface-models/Client/ClientDetails/ClientSearchSummary';
import { NationalClientDetails } from '@/interface-models/Client/NationalClientDetails';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import { Validation } from '@/interface-models/Generic/Validation';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import FleetAssetFuelSurchargeRate from '@/interface-models/ServiceRates/FleetAssetOwner/AdditionalCharges/FuelSurcharge/FleetAssetFuelSurchargeRate';
import { FuelComponentType } from '@/interface-models/ServiceRates/FuelSurcharge/FuelComponentType';
import { FuelLevyChargeBasis } from '@/interface-models/ServiceRates/FuelSurcharge/FuelLevyChargeBasis';
import FuelSurchargeRate from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeRate';
import { FuelSurchargeType } from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeType';
import { RangeDeterminant } from '@/interface-models/ServiceRates/FuelSurcharge/RangeDeterminant';
import { RangedFlexRate } from '@/interface-models/ServiceRates/FuelSurcharge/RangedFlexRate';
import { RateBracketType } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/RateBracketType';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useFuelLevyStore } from '@/store/modules/FuelLevyStore';
import moment from 'moment-timezone';
import { v4 as uuidv4 } from 'uuid';
import { ref, Ref, watch } from 'vue';
/**
 * Interface for form data used in create/edit dialogs
 */
interface FuelSurchargeFormData {
  name: string;
  clientIds: string[];
  fleetAssetIds: (string | FleetAssetSummary)[];
  fuelSurchargeApplicationType: FuelSurchargeType;
  rangeDeterminant: RangeDeterminant | null;
  rateBracketType: RateBracketType | null;
  validFromDate: number | undefined | null;
  validToDate: number | undefined | null;
  serviceTypeId: number[] | null;
  rateBrackets: RangedFlexRate[];
}

/**
 * Interface for bulk edit form data
 */
interface BulkEditFormData {
  name: string;
  clientIds: (string | ClientSearchSummary)[];
  fleetAssetIds: (string | FleetAssetSummary)[];
  serviceTypeIds: number[];
}

const props = withDefaults(
  defineProps<{
    componentType: FuelComponentType;
    entityId?: string;
    allFuelSurchargeRates: FuelSurchargeRate[];
    // Dialog visibility props
    showCreateEditDialog: boolean;
    showBulkEditDialog: boolean;
    // Data props
    availableServiceTypes: ServiceTypes[];
    availableClients: ClientSearchSummary[];
    availableFleetAssets: FleetAssetSummary[];
    existingGroupOptions: { uuid: string; name: string }[];
    selectedSurchargesForBulkEdit: FuelSurchargeRate[];
    // State props
    isEditing: boolean;
    addToGroupUuId: string;
    groupCount: number;
    bulkEditLevel: string;
    currentEditingSurcharge?: FuelSurchargeRate | null;
  }>(),
  {
    entityId: '',
    showCreateEditDialog: false,
    showBulkEditDialog: false,
    availableServiceTypes: () => [],
    availableClients: () => [],
    availableFleetAssets: () => [],
    existingGroupOptions: () => [],
    selectedSurchargesForBulkEdit: () => [],
    isEditing: false,
    groupCount: 0,
    addToGroupUuId: '',
    bulkEditLevel: '',
    currentEditingSurcharge: null,
  },
);

const emit = defineEmits<{
  (event: 'update:showCreateEditDialog', value: boolean): void;
  (event: 'update:showBulkEditDialog', value: boolean): void;
  (event: 'refreshData'): void;
  (event: 'refreshGroupData', payload: string): void;
}>();

const fuelLevyStore = useFuelLevyStore();
const clientDetailsStore = useClientDetailsStore();

/**
 * Date range adjustment interface
 */
interface DateRangeAdjustment {
  previousSurcharge?: FuelSurchargeRate;
  nextSurcharge?: FuelSurchargeRate;
  previousValidTo?: number;
  nextValidFrom?: number;
  allAdjustments?: Array<{
    surcharge: FuelSurchargeRate;
    newValidFrom?: number;
    newValidTo?: number;
  }>;
}

/** Active tab in create dialog (0 = New Group, 1 = Add to Existing) */
const createTab: Ref<number> = ref(0);
const selectedExistingGroup: Ref<string> = ref('');
const selectedClientIds: Ref<(string | ClientSearchSummary)[]> = ref([]);

const validate: Validation = validationRules;

const awaitingSaveResponse: Ref<boolean> = ref(false);
const applyToAllServiceTypes: Ref<boolean> = ref(false);
const formValid: Ref<boolean> = ref(false);
const isDivisionLevel: Ref<boolean> = ref(true);

const createEditForm: Ref<HTMLFormElement | null> = ref(null);
const selectedNationalClients = ref<string[]>([]);

const clientsAddedByNationalSelection: Ref<Map<string, string[]>> = ref(
  new Map(),
);

const nationalClients: Ref<NationalClientDetails[]> = ref([]);

const bulkEditData: Ref<BulkEditFormData> = ref({
  name: '',
  clientIds: [],
  fleetAssetIds: [],
  serviceTypeIds: [],
});

/** Main form data for create/edit operations */
const formData: Ref<FuelSurchargeFormData> = ref({
  name: '',
  clientIds: [],
  fleetAssetIds: [],
  fuelSurchargeApplicationType: FuelSurchargeType.CONSTANT,
  rangeDeterminant: null,
  validFromDate: 0,
  validToDate: 0,
  serviceTypeId: null,
  rateBracketType: RateBracketType.ABSOLUTE,
  rateBrackets: [
    {
      bracketId: uuidv4().split('-').join(''),
      bracketMin: 0,
      bracketMax: -1,
      rate: 0,
      chargeType: FuelLevyChargeBasis.PERCENTAGE,
    },
  ],
});

/**
 * Options for fuel surcharge type selection
 */
const surchargeTypeOptions = [
  { text: 'Fixed Rate', value: FuelSurchargeType.CONSTANT },
  { text: 'Bracketed', value: FuelSurchargeType.RANGED_RATE },
];

/**
 * Options for range determinant selection
 */
const rangeDeterminantOptions = [
  {
    text: 'Distance Travelled (GPS)',
    value: RangeDeterminant.DISTANCE_TRAVELLED,
  },
  { text: 'Anticipated Route', value: RangeDeterminant.ANTICIPATED_ROUTE },
  { text: 'Suburb Centres', value: RangeDeterminant.SUBURB_CENTRES },
];

/**
 * Handles changes to the division/specific level selection
 */
function onLevelChange() {
  // Clear selections when level changes
  formData.value.clientIds = [];
  formData.value.fleetAssetIds = [];
  selectedClientIds.value = [];
  selectedNationalClients.value = [];

  // Clear national client tracking
  clientsAddedByNationalSelection.value.clear();

  // If division level is selected, set clientIds and selectedClientIds appropriately
  if (isDivisionLevel.value) {
    if (props.componentType === FuelComponentType.CLIENT) {
      formData.value.clientIds = ['0'];
    } else if (props.componentType === FuelComponentType.CASH_SALE) {
      formData.value.clientIds = ['CS'];
    }
  } else {
    if (props.componentType === FuelComponentType.CLIENT) {
      nationalClients.value = clientDetailsStore.nationalClientsList;
    } else {
      // Clear national client selection when switching to division level
      selectedNationalClients.value = [];
      clientsAddedByNationalSelection.value.clear();
    }
  }
}

// formats fleet asset display name
function formatFleetAsset(asset: FleetAssetSummary): string {
  const outsideHire = asset.outsideHire;
  return `${asset.csrAssignedId}  (${
    outsideHire ? 'Outside Hire' : asset.registrationNumber
  })`;
}

/**
 * Handles "Apply to all service types" checkbox change
 */
function onApplyToAllServiceTypesChange(): void {
  if (applyToAllServiceTypes.value) {
    formData.value.serviceTypeId = null;
  }
}

/**
 * Handles selection of a national client
 */
function onNationalClientsChange(newSelection: string[]) {
  const currentSelection = [...selectedClientIds.value];

  // Handle added national clients
  newSelection.forEach((nationalClientId) => {
    if (!clientsAddedByNationalSelection.value.has(nationalClientId)) {
      const individualClients = props.availableClients.filter(
        (client) => client.nationalClientId === nationalClientId,
      );

      if (individualClients.length > 0) {
        const addedClientIds: string[] = [];

        individualClients.forEach((client) => {
          const isAlreadySelected = currentSelection.some(
            (item) =>
              (typeof item === 'string' ? item : item.clientId) ===
              client.clientId,
          );
          if (!isAlreadySelected) {
            currentSelection.push(client);
            addedClientIds.push(client.clientId);
          }
        });

        if (addedClientIds.length > 0) {
          clientsAddedByNationalSelection.value.set(
            nationalClientId,
            addedClientIds,
          );
        }
      }
    }
  });

  // Handle removed national clients (those that were deselected)
  Array.from(clientsAddedByNationalSelection.value.keys()).forEach(
    (nationalClientId) => {
      if (!newSelection.includes(nationalClientId)) {
        const clientIdsToRemove =
          clientsAddedByNationalSelection.value.get(nationalClientId) ?? [];
        clientsAddedByNationalSelection.value.delete(nationalClientId);

        clientIdsToRemove.forEach((clientId) => {
          const index = currentSelection.findIndex(
            (item) =>
              (typeof item === 'string' ? item : item.clientId) === clientId,
          );
          if (index !== -1) {
            currentSelection.splice(index, 1);
          }
        });
      }
    },
  );

  selectedClientIds.value = currentSelection;
}

/**
 * Adds a new bracket with proper range adjustment.
 * The new bracket becomes the infinity bracket, and the previous last bracket gets a proper bracketMax.
 */
function addBracket(): void {
  const brackets = formData.value.rateBrackets;

  const lastBracket = brackets[brackets.length - 1];
  if (brackets.length > 0) {
    // Set the previous last bracket's bracketMax to its bracketMin + 1 (or a reasonable default)
    if (lastBracket.bracketMax === -1) {
      lastBracket.bracketMax = lastBracket.bracketMin;
    }
  }

  // Add new bracket that becomes the infinity bracket
  const newBracketMin =
    brackets.length > 0 ? brackets[brackets.length - 1].bracketMax : 0;

  brackets.push({
    bracketId: uuidv4().split('-').join(''),
    bracketMin: newBracketMin,
    bracketMax: -1,
    rate: 0,
    chargeType: FuelLevyChargeBasis.PERCENTAGE,
  });

  // Ensure range continuity
  adjustBracketRangeContinuity();
}

/**
 * Removes a bracket and adjusts remaining brackets to maintain range continuity.
 * Prevents removal if only one bracket remains.
 */
function removeBracket(index: number): void {
  const brackets = formData.value.rateBrackets;

  if (brackets.length <= 1) {
    return; // Cannot remove the last bracket
  }

  // Remove the bracket
  brackets.splice(index, 1);

  // Ensure the last bracket has bracketMax = -1
  if (brackets.length > 0) {
    brackets[brackets.length - 1].bracketMax = -1;
  }

  // Adjust range continuity
  adjustBracketRangeContinuity();
}

/**
 * Updates the bracketMin of the next bracket when a bracket's bracketMax is changed.
 * Maintains range continuity across all brackets.
 */
function updateNextBracketMin(changedIndex: number): void {
  const brackets = formData.value.rateBrackets;
  const nextIndex = changedIndex + 1;

  if (nextIndex < brackets.length) {
    brackets[nextIndex].bracketMin = brackets[changedIndex].bracketMax;

    // Recursively update subsequent brackets if needed
    if (nextIndex < brackets.length - 1) {
      updateNextBracketMin(nextIndex);
    }
  }
}

/**
 * Adjust bracket range continuity - ensures brackets form a continuous range with no gaps
 * This function makes sure that:
 * 1. First bracket starts at 0
 * 2. Each subsequent bracket's min is 1 more than the previous bracket's max
 * 3. Last bracket always has max = -1 (infinity)
 */
function adjustBracketRangeContinuity(): void {
  const brackets = formData.value.rateBrackets;
  if (brackets.length === 0) {
    return;
  }

  // First bracket always starts at 0
  brackets[0].bracketMin = 0;

  // Set bracketMin for subsequent brackets based on previous bracket's bracketMax
  for (let i = 1; i < brackets.length; i++) {
    const previousBracket = brackets[i - 1];
    brackets[i].bracketMin = previousBracket.bracketMax;
  }

  // Last bracket always has bracketMax = -1 (infinity)
  if (brackets.length > 0) {
    brackets[brackets.length - 1].bracketMax = -1;
  }
}

/**
 * Handle valid from date change
 */
function handleValidFromDateChange(epoch: number): void {
  formData.value.validFromDate = returnStartOfDayFromEpoch(epoch);
}

/**
 * Handle valid to date change
 */
function handleValidToDateChange(epoch: number): void {
  formData.value.validToDate = returnEndOfDayFromEpoch(epoch);
}

/**
 * Close create/edit dialog
 */
function closeCreateEditDialog(): void {
  emit('update:showCreateEditDialog', false);
  resetFormData();
  createTab.value = 0;
}

/**
 * Close bulk edit dialog
 */
function closeBulkEditDialog(): void {
  emit('update:showBulkEditDialog', false);
  resetBulkEditData();
}

/**
 * Helper function to get surcharges for a specific group, handling both lazy loading and regular loading
 * @param groupUuid - The UUID of the group to get surcharges for
 * @param excludeTableId - Optional tableId to exclude from results
 * @returns Array of surcharges in the group
 */
function getGroupSurcharges(
  groupUuid: string,
  excludeTableId?: number,
): FuelSurchargeRate[] {
  let surcharges = props.allFuelSurchargeRates.filter(
    (s) => s.uuid === groupUuid,
  );

  if (excludeTableId !== undefined) {
    surcharges = surcharges.filter((s) => s.tableId !== excludeTableId);
  }

  return surcharges;
}

/**
 * Calculates necessary date range adjustments for adjacent surcharges when editing
 */
function calculateDateRangeAdjustments(
  editedSurcharge: FuelSurchargeRate,
): DateRangeAdjustment {
  const adjustments: DateRangeAdjustment = {};

  // Get surcharges in the same group
  const groupSurcharges = getGroupSurcharges(
    editedSurcharge.uuid,
    editedSurcharge.tableId,
  ).sort((a, b) => (a.validFromDate || 0) - (b.validFromDate || 0));

  const editedValidFrom = formData.value.validFromDate || 0;
  const editedValidTo = formData.value.validToDate || Number.MAX_SAFE_INTEGER;

  const allAdjustments: Array<{
    surcharge: FuelSurchargeRate;
    newValidFrom?: number;
    newValidTo?: number;
  }> = [];

  // Find surcharges that need adjustment
  groupSurcharges.forEach((surcharge) => {
    const surchargeValidFrom = surcharge.validFromDate || 0;
    const surchargeValidTo = surcharge.validToDate || Number.MAX_SAFE_INTEGER;

    // Check if this surcharge overlaps with the edited surcharge
    const hasOverlap =
      editedValidFrom <= surchargeValidTo &&
      editedValidTo >= surchargeValidFrom;

    if (hasOverlap) {
      const canAdjustBefore = surchargeValidFrom < editedValidFrom;
      const canAdjustAfter = surchargeValidTo > editedValidTo;

      if (canAdjustBefore && canAdjustAfter) {
        // Choose which end to adjust based on smaller disruption
        const beforeGap = editedValidFrom - surchargeValidFrom;
        const afterGap = surchargeValidTo - editedValidTo;

        if (beforeGap >= afterGap) {
          const newValidTo = moment(editedValidFrom)
            .subtract(1, 'day')
            .endOf('day')
            .valueOf();

          adjustments.previousSurcharge = surcharge as
            | ClientFuelSurchargeRate
            | FleetAssetFuelSurchargeRate;
          adjustments.previousValidTo = newValidTo;
          allAdjustments.push({ surcharge, newValidTo });
        } else {
          const newValidFrom = moment(editedValidTo)
            .add(1, 'day')
            .startOf('day')
            .valueOf();

          adjustments.nextSurcharge = surcharge as
            | ClientFuelSurchargeRate
            | FleetAssetFuelSurchargeRate;
          adjustments.nextValidFrom = newValidFrom;
          allAdjustments.push({ surcharge, newValidFrom });
        }
      } else if (canAdjustBefore) {
        const newValidTo = moment(editedValidFrom)
          .subtract(1, 'day')
          .endOf('day')
          .valueOf();

        if (newValidTo >= surchargeValidFrom) {
          adjustments.previousSurcharge = surcharge as
            | ClientFuelSurchargeRate
            | FleetAssetFuelSurchargeRate;
          adjustments.previousValidTo = newValidTo;
          allAdjustments.push({ surcharge, newValidTo });
        }
      } else if (canAdjustAfter) {
        const newValidFrom = moment(editedValidTo)
          .add(1, 'day')
          .startOf('day')
          .valueOf();

        if (newValidFrom <= surchargeValidTo) {
          adjustments.nextSurcharge = surcharge as
            | ClientFuelSurchargeRate
            | FleetAssetFuelSurchargeRate;
          adjustments.nextValidFrom = newValidFrom;
          allAdjustments.push({ surcharge, newValidFrom });
        }
      }
    }
  });

  adjustments.allAdjustments = allAdjustments;
  return adjustments;
}

const dateAdjustmentDetails: Ref<
  Array<{
    surchargeName: string;
    oldEndDate?: string;
    oldStartDate?: string;
    newEndDate?: string;
    newStartDate?: string;
  }>
> = ref([]);

// Date adjustment dialog state
const dateAdjustmentTitle: Ref<string> = ref('');
const dateAdjustmentResolver: Ref<((value: boolean) => void) | null> =
  ref(null);

const showDateAdjustmentDialog: Ref<boolean> = ref(false);

/**
 * Shows a confirmation dialog for date range adjustments
 * @param adjustments - Array of adjustment details to display
 * @param title - The title for the dialog
 * @returns Promise that resolves when user confirms or cancels
 */
async function showDateAdjustmentAlert(
  adjustments: Array<{
    surchargeName: string;
    oldEndDate?: string;
    oldStartDate?: string;
    newEndDate?: string;
    newStartDate?: string;
  }>,
  title: string = 'Date Adjustment Required',
): Promise<boolean> {
  return new Promise((resolve) => {
    dateAdjustmentDetails.value = adjustments;
    dateAdjustmentTitle.value = title;
    dateAdjustmentResolver.value = resolve;
    showDateAdjustmentDialog.value = true;
  });
}

/**
 * Handles confirmation of date adjustment dialog
 */
function confirmDateAdjustment(): void {
  if (dateAdjustmentResolver.value) {
    dateAdjustmentResolver.value(true);
    dateAdjustmentResolver.value = null;
  }
  showDateAdjustmentDialog.value = false;
}

/**
 * Handles cancellation of date adjustment dialog
 */
function cancelDateAdjustment(): void {
  if (dateAdjustmentResolver.value) {
    dateAdjustmentResolver.value(false);
    dateAdjustmentResolver.value = null;
  }
  showDateAdjustmentDialog.value = false;
}

/**
 * Checks if editing a single surcharge will create date overlaps and shows appropriate alerts
 * @param editedSurcharge - The surcharge being edited
 * @returns Promise<boolean> - true if user confirms to proceed, false to cancel
 */
async function checkAndAlertDateOverlaps(
  editedSurcharge: FuelSurchargeRate,
): Promise<boolean> {
  const adjustments = calculateDateRangeAdjustments(editedSurcharge);

  if (adjustments.allAdjustments && adjustments.allAdjustments.length > 0) {
    const adjustmentDetails = adjustments.allAdjustments.map((adjustment) => {
      const detail: {
        surchargeName: string;
        oldEndDate?: string;
        oldStartDate?: string;
        newEndDate?: string;
        newStartDate?: string;
      } = {
        surchargeName: adjustment.surcharge.name,
      };

      if (adjustment.surcharge.validToDate) {
        detail.oldEndDate = returnFormattedDate(
          adjustment.surcharge.validToDate,
        );
      }

      if (adjustment.surcharge.validFromDate) {
        detail.oldStartDate = returnFormattedDate(
          adjustment.surcharge.validFromDate,
        );
      }

      if (adjustment.newValidTo !== undefined) {
        detail.newEndDate = returnFormattedDate(adjustment.newValidTo);
      }

      if (adjustment.newValidFrom !== undefined) {
        detail.newStartDate = returnFormattedDate(adjustment.newValidFrom);
      }

      return detail;
    });

    return await showDateAdjustmentAlert(
      adjustmentDetails,
      'Date Overlap Detected',
    );
  } else if (adjustments.previousSurcharge || adjustments.nextSurcharge) {
    // Fallback to legacy display logic
    const adjustmentDetails: Array<{
      surchargeName: string;
      oldEndDate?: string;
      oldStartDate?: string;
      newEndDate?: string;
      newStartDate?: string;
    }> = [];

    if (
      adjustments.previousSurcharge &&
      adjustments.previousValidTo !== undefined
    ) {
      adjustmentDetails.push({
        surchargeName: adjustments.previousSurcharge.name,
        newEndDate: returnFormattedDate(adjustments.previousValidTo),
      });
    }

    if (adjustments.nextSurcharge && adjustments.nextValidFrom !== undefined) {
      adjustmentDetails.push({
        surchargeName: adjustments.nextSurcharge.name,
        newStartDate: returnFormattedDate(adjustments.nextValidFrom),
      });
    }

    return await showDateAdjustmentAlert(
      adjustmentDetails,
      'Date Overlap Detected',
    );
  }

  return true; // No overlaps, proceed
}

/**
 * Reset form data to default values
 */
function resetFormData(): void {
  formData.value = {
    name: '',
    clientIds: [],
    fleetAssetIds: [],
    fuelSurchargeApplicationType:
      FuelSurchargeType.CONSTANT || FuelSurchargeType.RANGED_RATE,
    rangeDeterminant: FuelSurchargeType.RANGED_RATE
      ? RangeDeterminant.ACTUAL_TIME
      : null,
    validFromDate: 0,
    validToDate: 0,
    serviceTypeId: null,
    rateBracketType: null,
    rateBrackets: [
      {
        bracketId: uuidv4().split('-').join(''),
        bracketMin: 0,
        bracketMax: -1,
        rate: 0,
        chargeType: FuelLevyChargeBasis.PERCENTAGE,
      },
    ],
  };
  selectedExistingGroup.value = '';
  selectedClientIds.value = [];
  clientsAddedByNationalSelection.value.clear();
  isDivisionLevel.value = props.entityId ? false : true;
}

/**
 * Reset bulk edit data
 */
function resetBulkEditData(): void {
  bulkEditData.value = {
    name: '',
    clientIds: [],
    fleetAssetIds: [],
    serviceTypeIds: [],
  };
}

/**
 * Prefills form from existing group when adding to existing group
 */
async function prefillFormFromExistingGroup(groupUuid: string): Promise<void> {
  if (!groupUuid || createTab.value !== 1) {
    return;
  }

  try {
    // First check if we have the group in our existing group options
    const group = props.existingGroupOptions.find((g) => g.uuid === groupUuid);
    if (!group) {
      return;
    }

    // Get group data from the API
    let allGroupSurcharges: FuelSurchargeRate[] = [];

    // Determine which API to call based on component type
    if (
      props.componentType === FuelComponentType.CLIENT ||
      props.componentType === FuelComponentType.CASH_SALE
    ) {
      const surcharges =
        await fuelLevyStore.getAllClientFuelSurchargeRatesByUUID(groupUuid);
      if (surcharges && surcharges.length > 0) {
        allGroupSurcharges = surcharges;
      }
    } else if (props.componentType === FuelComponentType.FLEET_ASSET) {
      const surcharges =
        await fuelLevyStore.getAllFleetAssetFuelSurchargeRatesByUUID(groupUuid);
      if (surcharges && surcharges.length > 0) {
        allGroupSurcharges = surcharges;
      }
    }

    if (!allGroupSurcharges.length) {
      return;
    }

    // Get the most recent surcharge (last in array when sorted by validFromDate)
    const sortedSurcharges = allGroupSurcharges.sort(
      (a, b) => (a.validFromDate || 0) - (b.validFromDate || 0),
    );
    const mostRecentSurcharge = sortedSurcharges[sortedSurcharges.length - 1];

    // Basic fields
    formData.value.name = mostRecentSurcharge.name;
    formData.value.fuelSurchargeApplicationType =
      mostRecentSurcharge.fuelSurchargeApplicationType;
    formData.value.rateBrackets = mostRecentSurcharge.rateBrackets.map(
      (bracket) => ({
        ...bracket,
        bracketId: uuidv4().split('-').join(''),
        chargeType: FuelLevyChargeBasis.PERCENTAGE,
      }),
    );
    if (mostRecentSurcharge.rangeDeterminant) {
      formData.value.rangeDeterminant = mostRecentSurcharge.rangeDeterminant;
    }

    // Service types
    if (
      mostRecentSurcharge.serviceTypes &&
      mostRecentSurcharge.serviceTypes.length > 0
    ) {
      formData.value.serviceTypeId = [...mostRecentSurcharge.serviceTypes];
      applyToAllServiceTypes.value = false;
    } else {
      formData.value.serviceTypeId = null;
      applyToAllServiceTypes.value = true;
    }

    // Client/fleet/division-level handling
    if (props.entityId) {
      isDivisionLevel.value = false;
      if (props.componentType === FuelComponentType.CLIENT) {
        formData.value.clientIds = [props.entityId];
        const client = props.availableClients.find(
          (c) => c.clientId === props.entityId,
        );
        if (client) {
          selectedClientIds.value = [client];
        }
      } else if (props.componentType === FuelComponentType.FLEET_ASSET) {
        formData.value.fleetAssetIds = [props.entityId];
      }
    } else {
      if (mostRecentSurcharge instanceof ClientFuelSurchargeRate) {
        const surcharge = mostRecentSurcharge;
        const divisionIndicators = ['0', 'CS'];
        const clientIds = surcharge.clientIds || [];
        const hasDivisionId = clientIds.some((id) =>
          divisionIndicators.includes(String(id)),
        );

        if (hasDivisionId || !clientIds.length) {
          isDivisionLevel.value = true;
          selectedClientIds.value = clientIds.includes('CS') ? ['CS'] : ['0'];
        } else {
          isDivisionLevel.value = false;
          selectedClientIds.value = clientIds
            .filter((id) => !divisionIndicators.includes(String(id)))
            .map(
              (id) =>
                props.availableClients.find((c) => c.clientId === String(id)) ||
                String(id),
            );
        }
        formData.value.clientIds = [...clientIds];
      } else if (mostRecentSurcharge instanceof FleetAssetFuelSurchargeRate) {
        const surcharge = mostRecentSurcharge;
        formData.value.fleetAssetIds = [...(surcharge.fleetAssetIds || [])];
        const hasDivisionId = (surcharge.fleetAssetIds || []).some(
          (id) => id === '0',
        );
        isDivisionLevel.value =
          hasDivisionId || !(surcharge.fleetAssetIds || []).length;
      } else {
        isDivisionLevel.value = true;
      }
    }

    // Suggested date setup - start from the end of the most recent surcharge
    if (mostRecentSurcharge.validToDate) {
      const suggestedStartDate = mostRecentSurcharge.validToDate + 86400000; // +1 day
      formData.value.validFromDate = suggestedStartDate;
      formData.value.validToDate =
        suggestedStartDate + 30 * 24 * 60 * 60 * 1000; // +30 days
    } else {
      // If no end date, suggest starting today
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      formData.value.validFromDate = today.getTime();
      formData.value.validToDate = today.getTime() + 30 * 24 * 60 * 60 * 1000; // +30 days
    }

    // Ensure correct continuity of brackets if needed
    adjustBracketRangeContinuity();
  } catch (error) {
    console.error('Error prefilling from existing group:', error);
  }
}

/**
 * Save current surcharge
 */
async function saveCurrentSurcharge(): Promise<void> {
  if (createEditForm.value && !createEditForm.value.validate()) {
    showNotification('Please fix validation errors before saving', {
      type: HealthLevel.ERROR,
    });
    return;
  }
  awaitingSaveResponse.value = true;
  try {
    // check if editing or creating new
    const result = props.isEditing
      ? await saveEditedSurcharge()
      : await saveNewSurcharge();

    // Refresh loaded group data for add to existing group
    if (result) {
      emit('refreshData');
      // Emit event to parent to handle group refresh if needed
      if (props.currentEditingSurcharge) {
        emit('refreshGroupData', props.currentEditingSurcharge.uuid);
      } else if (selectedExistingGroup.value) {
        emit('refreshGroupData', selectedExistingGroup.value);
      }

      showNotification('Fuel Surcharge updated', {
        type: HealthLevel.SUCCESS,
      });
    }
    // Success - close dialog and refresh data
    emit('update:showCreateEditDialog', false);
    resetFormData();
  } catch (error) {
    console.error('Error saving fuel surcharge:', error);
  } finally {
    awaitingSaveResponse.value = false;
  }
}

/**
 * Creates a new surcharge instance from the current form data
 */
function createNewSurchargeFromForm(): FuelSurchargeRate {
  const baseData = {
    uuid:
      createTab.value === 0
        ? uuidv4().split('-').join('')
        : selectedExistingGroup.value,
    name: formData.value.name,
    fuelSurchargeApplicationType: formData.value.fuelSurchargeApplicationType,
    rangeDeterminant: formData.value.rangeDeterminant,
    validFromDate: returnStartOfDayFromEpoch(
      formData.value.validFromDate ?? undefined,
    ),
    validToDate: returnEndOfDayFromEpoch(
      formData.value.validToDate ?? undefined,
    ),
    rateBracketType: formData.value.rateBracketType,
    serviceTypes: applyToAllServiceTypes.value
      ? null
      : formData.value.serviceTypeId,
    rateBrackets: formData.value.rateBrackets.map(
      (bracket: RangedFlexRate) => ({
        ...bracket,
        bracketId: bracket.bracketId
          ? bracket.bracketId
          : uuidv4().split('-').join(''),
        chargeType: FuelLevyChargeBasis.PERCENTAGE,
      }),
    ),
  };

  if (baseData.fuelSurchargeApplicationType === FuelSurchargeType.CONSTANT) {
    baseData.rangeDeterminant = null;
    baseData.rateBracketType = null;
    baseData.rateBrackets = baseData.rateBrackets.map(
      (bracket: RangedFlexRate) => ({
        ...bracket,
        bracketMax: null,
        bracketMin: null,
      }),
    );
  }

  if (props.componentType === FuelComponentType.CLIENT) {
    let clientIds: string[] = [];

    if (isDivisionLevel.value && !props.entityId) {
      clientIds = ['0'];
    } else if (props.entityId) {
      clientIds = [props.entityId];
    } else {
      clientIds = selectedClientIds.value.map((item) =>
        typeof item === 'string' ? item : item.clientId,
      );
    }
    return new ClientFuelSurchargeRate({
      ...baseData,
      clientIds,
    });
  } else if (props.componentType === FuelComponentType.FLEET_ASSET) {
    let fleetAssetIds: string[] = [];
    if (isDivisionLevel.value && !props.entityId) {
      fleetAssetIds = ['0'];
    } else if (props.entityId) {
      fleetAssetIds = [props.entityId];
    } else {
      fleetAssetIds = formData.value.fleetAssetIds.map((item) =>
        typeof item === 'string' ? item : item.fleetAssetId,
      );
    }
    return new FleetAssetFuelSurchargeRate({
      ...baseData,
      fleetAssetIds,
    });
  } else {
    let clientIds: string[] = ['CS'];
    return new ClientFuelSurchargeRate({
      ...baseData,
      clientIds,
    });
  }
}

/**
 * Creates and saves a new fuel surcharge.
 * Cancels if user rejects overlap date adjustments in checkDateOverlapAndSave.
 *
 * @returns Promise<boolean> True if saved successfully, false if cancelled.
 */
async function saveNewSurcharge(): Promise<boolean> {
  const newSurcharge = createNewSurchargeFromForm();
  // If user cancels on date overlap, don't save
  if (!(await checkDateOverlapAndSave(newSurcharge))) {
    return false;
  }
  return true;
}

/**
 * Saves changes to an existing fuel surcharge.
 * Cancels if user rejects overlap date adjustments in checkDateOverlapAndSave.
 *
 * @returns Promise<boolean> True if saved successfully, false if cancelled.
 */
async function saveEditedSurcharge(): Promise<boolean> {
  if (!props.currentEditingSurcharge) {
    return false;
  }

  Object.assign(props.currentEditingSurcharge, {
    name: formData.value.name,
    validFromDate: returnStartOfDayFromEpoch(
      formData.value.validFromDate ?? undefined,
    ),
    validToDate: returnEndOfDayFromEpoch(
      formData.value.validToDate ?? undefined,
    ),
    fuelSurchargeApplicationType: formData.value.fuelSurchargeApplicationType,
    rateBrackets: formData.value.rateBrackets,
    serviceTypes: applyToAllServiceTypes.value
      ? null
      : formData.value.serviceTypeId,
    rangeDeterminant: formData.value.rangeDeterminant,
    rateBracketType:
      formData.value.fuelSurchargeApplicationType !== FuelSurchargeType.CONSTANT
        ? RateBracketType.ABSOLUTE
        : null,
  });

  // Update client IDs or fleet asset IDs based on surcharge type
  if ('clientIds' in props.currentEditingSurcharge) {
    props.currentEditingSurcharge.clientIds = formData.value.clientIds;
  } else if ('fleetAssetIds' in props.currentEditingSurcharge) {
    // Map fleet asset objects to their IDs to ensure we have a string[] type
    props.currentEditingSurcharge.fleetAssetIds =
      formData.value.fleetAssetIds.map((item) =>
        typeof item === 'string' ? item : item.fleetAssetId,
      );
  }

  if (!(await checkDateOverlapAndSave(props.currentEditingSurcharge))) {
    return false;
  }

  return true;
}

/**
 * Processes a surcharge's date ranges for potential overlaps,
 * applies any necessary adjustments, and persists the changes.
 * This function is used by both new and edit save flows.
 *
 * @param (FuelSurchargeRate) surcharge - The surcharge to process and save.
 * @returns Promise<boolean> True if save completed, false if cancelled by user.
 * @throws Error When surcharge type is unknown.
 */
async function checkDateOverlapAndSave(surcharge: FuelSurchargeRate) {
  const adjustments = calculateDateRangeAdjustments(surcharge);
  // DATE OVERLAP
  if (
    (adjustments.allAdjustments && adjustments.allAdjustments.length > 0) ||
    adjustments.previousSurcharge ||
    adjustments.nextSurcharge
  ) {
    const userConfirmed = await checkAndAlertDateOverlaps(surcharge);
    if (!userConfirmed) {
      return false;
    }
  }

  // Apply adjustments
  const surchargesNeedingUpdate = [surcharge];
  if (adjustments.allAdjustments?.length) {
    adjustments.allAdjustments.forEach((adj) => {
      if (adj.newValidFrom !== undefined) {
        adj.surcharge.validFromDate = adj.newValidFrom;
      }
      if (adj.newValidTo !== undefined) {
        adj.surcharge.validToDate = adj.newValidTo;
      }
      surchargesNeedingUpdate.push(adj.surcharge);
    });
  } else {
    if (
      adjustments.previousSurcharge &&
      adjustments.previousValidTo !== undefined
    ) {
      adjustments.previousSurcharge.validToDate = adjustments.previousValidTo;
      surchargesNeedingUpdate.push(adjustments.previousSurcharge);
    }
    if (adjustments.nextSurcharge && adjustments.nextValidFrom !== undefined) {
      adjustments.nextSurcharge.validFromDate = adjustments.nextValidFrom;
      surchargesNeedingUpdate.push(adjustments.nextSurcharge);
    }
  }

  // Save
  if ('clientIds' in surcharge) {
    const clientSurcharges =
      surchargesNeedingUpdate as ClientFuelSurchargeRate[];
    // The clientIds are already set in saveEditedSurcharge, so we don't need to set them again here
    if (clientSurcharges.length === 1) {
      await fuelLevyStore.saveClientFuelSurchargeRate(clientSurcharges[0]);
    } else {
      await fuelLevyStore.saveAllClientFuelSurchargeRate(clientSurcharges);
    }
  } else if ('fleetAssetIds' in surcharge) {
    const fleetSurcharges =
      surchargesNeedingUpdate as FleetAssetFuelSurchargeRate[];
    if (fleetSurcharges.length === 1) {
      await fuelLevyStore.saveFleetAssetFuelSurchargeRate(fleetSurcharges[0]);
    } else {
      await fuelLevyStore.saveAllFleetAssetFuelSurchargeRate(fleetSurcharges);
    }
  } else {
    throw new Error('Unknown surcharge type - cannot determine save method');
  }

  return true;
}

/**
 * Saves the bulk edited surcharges based on selected items and bulk edit data.
 */
async function saveBulkEdit(): Promise<void> {
  awaitingSaveResponse.value = true;

  try {
    const selectedSurcharges = props.selectedSurchargesForBulkEdit;
    if (!selectedSurcharges.length) {
      showNotification('No surcharges selected for bulk edit', {
        type: HealthLevel.WARNING,
      });
      return;
    }

    // Apply bulk changes to selected surcharges
    selectedSurcharges.forEach((surcharge) => {
      // Update name if provided
      if (bulkEditData.value.name && bulkEditData.value.name.trim()) {
        surcharge.name = bulkEditData.value.name.trim();
      }

      // Update service types
      if (applyToAllServiceTypes.value) {
        surcharge.serviceTypes = null;
      } else if (bulkEditData.value.serviceTypeIds.length > 0) {
        surcharge.serviceTypes = [...bulkEditData.value.serviceTypeIds];
      }

      // Update client IDs if applicable and provided
      if (
        surcharge instanceof ClientFuelSurchargeRate &&
        bulkEditData.value.clientIds.length > 0
      ) {
        const clientIds = bulkEditData.value.clientIds.map((item) =>
          typeof item === 'string' ? item : item.clientId,
        );
        surcharge.clientIds = clientIds;
      }

      // Update fleet asset IDs if applicable and provided
      if (
        surcharge instanceof FleetAssetFuelSurchargeRate &&
        bulkEditData.value.fleetAssetIds.length > 0
      ) {
        const fleetAssetIds = bulkEditData.value.fleetAssetIds.map((item) =>
          typeof item === 'string' ? item : item.fleetAssetId,
        );
        surcharge.fleetAssetIds = fleetAssetIds;
      }
    });

    // // Separate client and fleet asset surcharges
    if (selectedSurcharges.length) {
      if (selectedSurcharges[0] instanceof ClientFuelSurchargeRate) {
        const charges = selectedSurcharges as ClientFuelSurchargeRate[];
        await fuelLevyStore.saveAllClientFuelSurchargeRate(charges);
      }
      if (selectedSurcharges[0] instanceof FleetAssetFuelSurchargeRate) {
        const charges = selectedSurcharges as FleetAssetFuelSurchargeRate[];
        await fuelLevyStore.saveAllFleetAssetFuelSurchargeRate(charges);
      }
    }

    emit('update:showBulkEditDialog', false);
    emit('refreshData');
    emit('refreshGroupData', props.selectedSurchargesForBulkEdit[0].uuid);
    showNotification('Fuel Surcharge updated', {
      type: HealthLevel.SUCCESS,
    });
    resetBulkEditData();
  } catch (error) {
    console.error('Error saving bulk edit:', error);
  } finally {
    awaitingSaveResponse.value = false;
  }
}

//  sync selectedClientIds with formData.clientIds
function onClientSelectionChange(
  newSelection: (string | ClientSearchSummary)[],
) {
  formData.value.clientIds = newSelection.map((item) =>
    typeof item === 'string' ? item : item.clientId,
  );
}
// fill selected group data in form
function onExistingGroupChange(groupUuid: string | null) {
  if (groupUuid && createTab.value === 1) {
    prefillFormFromExistingGroup(groupUuid);
  } else if (!groupUuid && createTab.value === 1) {
    resetFormData();
  }
}

// Watch for changes to serviceTypeId to auto-check "Apply to all" when it's null
watch(
  () => formData.value.serviceTypeId,
  (newServiceTypeId) => {
    if (newServiceTypeId === null) {
      applyToAllServiceTypes.value = true;
    } else if (
      Array.isArray(newServiceTypeId) &&
      newServiceTypeId.length === 0
    ) {
      applyToAllServiceTypes.value = true;
    } else {
      applyToAllServiceTypes.value = false;
    }
  },
);

watch(
  () => props.addToGroupUuId,
  (newVal) => {
    createTab.value = 1;
    selectedExistingGroup.value = newVal;
    onExistingGroupChange(newVal);
  },
);

watch(
  () => props.currentEditingSurcharge,
  (surcharge) => {
    if (!surcharge) {
      return;
    }

    // Populate form with surcharge data
    formData.value.name = surcharge.name;
    formData.value.validFromDate = surcharge.validFromDate;
    formData.value.validToDate = surcharge.validToDate;
    formData.value.fuelSurchargeApplicationType =
      surcharge.fuelSurchargeApplicationType;

    if (
      surcharge.fuelSurchargeApplicationType === FuelSurchargeType.RANGED_RATE
    ) {
      formData.value.rangeDeterminant =
        surcharge.rangeDeterminant || RangeDeterminant.DISTANCE_TRAVELLED;
    } else {
      formData.value.rangeDeterminant = surcharge.rangeDeterminant || null;
    }

    if (
      surcharge.fuelSurchargeApplicationType === FuelSurchargeType.RANGED_RATE
    ) {
      adjustBracketRangeContinuity();
    }

    // Deep copy rate brackets
    formData.value.rateBrackets = surcharge.rateBrackets.map((bracket) => ({
      bracketId: bracket.bracketId,
      bracketMin: bracket.bracketMin,
      bracketMax: bracket.bracketMax,
      rate: bracket.rate,
      chargeType: FuelLevyChargeBasis.PERCENTAGE,
    }));

    // Handle client/fleet asset IDs
    if ('clientIds' in surcharge && surcharge.clientIds) {
      let clientIds = (surcharge as ClientFuelSurchargeRate).clientIds;
      const hasDivisionId = clientIds.includes('0') || clientIds.includes('CS');

      if (hasDivisionId || clientIds.length === 0) {
        isDivisionLevel.value = true;
        if (clientIds.includes('CS')) {
          selectedClientIds.value = ['CS'];
          formData.value.clientIds = ['CS'];
        } else {
          selectedClientIds.value = ['0'];
          formData.value.clientIds = ['0'];
        }
      } else {
        isDivisionLevel.value = false;
        const clientObjects = clientIds
          .filter((clientId) => !['0', 'CS'].includes(String(clientId)))
          .map((clientId) => {
            const client = props.availableClients.find(
              (c) => c.clientId === String(clientId),
            );
            return client || String(clientId);
          });

        selectedClientIds.value = clientObjects;
        formData.value.clientIds = clientIds;
      }
      formData.value.fleetAssetIds = [];
    } else if ('fleetAssetIds' in surcharge && surcharge.fleetAssetIds) {
      let fleetAssetIds = (surcharge as FleetAssetFuelSurchargeRate)
        .fleetAssetIds;
      const hasFleetDivisionId = fleetAssetIds.includes('0');

      if (hasFleetDivisionId || fleetAssetIds.length === 0) {
        isDivisionLevel.value = true;
        formData.value.fleetAssetIds = ['0'];
      } else {
        isDivisionLevel.value = false;
        formData.value.fleetAssetIds = fleetAssetIds;
      }
      selectedClientIds.value = [];
    }

    // Handle service types
    if (surcharge.serviceTypes && surcharge.serviceTypes.length > 0) {
      formData.value.serviceTypeId = [...surcharge.serviceTypes];
      applyToAllServiceTypes.value = false;
    } else {
      formData.value.serviceTypeId = null;
      applyToAllServiceTypes.value = true;
    }
  },
);
</script>

<style scoped lang="scss">
.no-limit-text {
  font-size: 14px;
  color: #666;
  padding: 24px;
}
.range-text {
  font-weight: 500;
  color: var(--light-text-color);
  text-transform: capitalize;

  &.Invalid {
    color: $error !important;
  }
}
</style>
