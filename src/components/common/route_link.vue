<template>
  <div class="mapboxgl-ctrl mapboxgl-ctrl-group route-link" @click="openRoute">
    <v-icon size="14" color="black">fas fa-route</v-icon>
  </div>
</template>

<script setup lang="ts">
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { ComputedRef, computed } from 'vue';

interface RouteWayPoint {
  origin: string;
  waypoints: string[];
  destination: string;
}

const props = withDefaults(
  defineProps<{
    isTile?: boolean;
    pudItems?: PUDItem[];
  }>(),
  {
    isTile: false,
    pudItems: () => [],
  },
);

// opens the pudsItems route via google in a new tab.
function openRoute(): void {
  if (!routeWayPoint.value) {
    return;
  }
  const url = getRouteLinkUrl(
    routeWayPoint.value.origin,
    routeWayPoint.value.waypoints,
    routeWayPoint.value.destination,
  );

  window.open(url, '_blank', 'noreferrer');
}

// gets the RouteWayPoint that includes the pudItems origin, waypoints and destination. Origin is the first stop, destination is the last stop and waypoints are the stops inbetween.
const routeWayPoint: ComputedRef<RouteWayPoint | null> = computed(() => {
  const origin = props.pudItems[0].address.formattedAddress;
  const destination =
    props.pudItems[props.pudItems.length - 1].address.formattedAddress;
  const waypoints: string[] = [];
  for (let i = 1; i < props.pudItems.length - 1; i++) {
    if (
      !props.pudItems[i].address ||
      !props.pudItems[i].address.formattedAddress
    ) {
      continue;
    }
    waypoints.push(props.pudItems[i].address.formattedAddress);
  }
  return {
    origin,
    waypoints,
    destination,
  };
});

// converts the origin, waypoints and destination to a google route url. Returns the url
function getRouteLinkUrl(
  origin: string,
  waypoints: string[],
  destination: string,
): string {
  const baseUrl = 'https://www.google.com/maps/dir/?api=1';
  let params = '&origin=' + encodeURIComponent(origin);
  if (waypoints.length > 0) {
    params += '&waypoints=';
  }
  for (let i = 0; i < waypoints.length; i++) {
    params += encodeURIComponent(waypoints[i]);
    if (i !== waypoints.length - 1) {
      params += '|';
    }
  }
  params += '&destination=' + encodeURIComponent(destination);
  return baseUrl + params;
}
</script>

<style scoped lang="scss">
.route-link {
  width: 29px !important;
  height: 29px !important;
  background: white;
  display: flex;
  justify-content: center;
  align-items: center;

  &:hover {
    background: rgb(247, 245, 245);
    cursor: pointer;
  }
}
</style>
