<template>
  <v-flex md12>
    <v-layout
      class="dialog-toolbar center-section__top-toolbar"
      style="position: relative"
      align-center
    >
      <v-layout row wrap align-center>
        <h6 class="ml-2">{{ props.title || 'Associated Clients' }}</h6>
        <v-divider class="ma-2"></v-divider>
        <h6>({{ clientList.length }})</h6>
      </v-layout>
    </v-layout>
    <v-data-table
      :headers="headers"
      :items="props.clientList"
      item-key="_id"
      hide-actions
      class="gd-dark-theme"
    >
      <template v-slot:items="data">
        <tr>
          <td class="text-center">{{ data.item.clientId }}</td>
          <td>{{ data.item.clientName }}</td>
          <td>{{ data.item.tradingName }}</td>
          <td class="text-left">
            <v-icon
              :color="
                isClientActive(data.item.statusList) ? 'success' : 'error'
              "
              small
            >
              {{ isClientActive(data.item.statusList) ? 'check' : 'close' }}
            </v-icon>
          </td>
          <td>
            {{ getClientStatus(data.item.statusList, data.item.creditStatus) }}
          </td>
        </tr>
      </template>
    </v-data-table>
  </v-flex>
</template>

<script setup lang="ts">
import ClientSearchSummary from '@/interface-models/Client/ClientDetails/ClientSearchSummary';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { defineProps } from 'vue';

const props = defineProps<{
  clientList: ClientSearchSummary[];
  title?: string;
}>();

// Table headers
const headers: TableHeader[] = [
  { text: 'Client ID', value: 'clientId', sortable: true, align: 'left' },
  { text: 'Client Name', value: 'clientName', sortable: true, align: 'left' },
  { text: 'Trading Name', value: 'tradingName', sortable: true, align: 'left' },
  { text: 'Active', value: 'active', sortable: true, align: 'left' },
  { text: 'Status', value: 'status', sortable: true, align: 'left' },
];

function getClientStatus(
  statusList: number[],
  creditStatus: number | null,
): string {
  return statusList.includes(13)
    ? 'RETIRED'
    : creditStatus === 2
      ? 'SEE ACCOUNTS'
      : statusList.includes(47)
        ? 'DO NOT USE'
        : 'ACTIVE';
}

function isClientActive(statusList: number[]): boolean {
  return !statusList.includes(13) && !statusList.includes(47);
}
</script>
