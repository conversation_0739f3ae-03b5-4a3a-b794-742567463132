<section class="zone pt-2">
  <v-data-table
    class="default-table-dark gd-dark-theme"
    :headers="headers"
    :items="getGroupedZoneRateTableData()"
    :rows-per-page-items="[10, 20]"
    hide-actions
  >
    <template v-slot:items="props">
      <tr
        :class="{
          'service-header-row': props.item.isServiceHeader,
          'zone-child-row': props.item.isRangeRow,
          'form-container': !props.item.isServiceHeader,
          'item-selected': editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index,
          'empty-rate-item':  isRateItemEmpty(props.item),
        }"
        style="cursor: pointer; position: relative"
        @click="handleRowClick(props.item)"
      >
        <td class="extra-wide">
          <v-layout md12 align-center justify-space-between>
            <v-flex v-if="props.item.isServiceHeader">
              <span class="service-name">
                {{ serviceTypeName(props.item.serviceTypeId) }}
              </span>
            </v-flex>
            <v-flex v-else>
              <v-text-field
                v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index"
                class="small-input"
                box
                hide-details
                v-model="props.item.serviceTypeName"
                :disabled="!isEdited"
                :rules="[validate.required]"
                @focus="$event.target.select()"
              >
              </v-text-field>
              <span v-else class="subheading grey--text zone-child-text">
                {{ props.item.serviceTypeName }}
              </span>
            </v-flex>
          </v-layout>
        </td>

        <td v-if="props.item.isClient" class="fixed-width">
          <v-flex>
            <v-text-field
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index"
              class="small-input"
              box
              type="number"
              hide-details
              v-model.number="props.item.zoneRate"
              :prefix="'$'"
              :disabled="!isEdited"
              :rules="[validate.required, validate.number]"
              @focus="$event.target.select()"
          >
            </v-text-field>
            <v-layout 
                v-else 
                align-center
                :class="{'error--text' : validationError(props.item.zoneRate, [validate.number, validate.required])}">
              <span class="subheading grey--text">
                {{ props.item.zoneRate }}
              </span>
            </v-layout>
          </v-flex>
        </td>

        <td v-else class="fixed-width">
          <v-flex>
            <v-text-field
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index"
              class="small-input"
              box
              v-model.number="props.item.calculation"
              hide-details
              :suffix="'%'"
              :disabled="!isEdited"
              :rules="[validate.required, validate.number]"
            >
            </v-text-field>
            <span v-else class="subheading grey--text">
              {{ props.item.calculation }}
            </span>
          </v-flex>
        </td>

        <td v-if="props.item.isClient" class="fixed-width">
          <v-flex>
            <v-text-field
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index"
              class="small-input"
              box
              v-model.number="props.item.pickupFlagfall"
              hide-details
              :disabled="!isEdited"
              :prefix="'$'"
              :rules="[validate.required, validate.number]"
            >
            </v-text-field>
            <span v-else class="subheading grey--text">
              {{ props.item.pickupFlagfall }}
            </span>
          </v-flex>
        </td>

        <td v-if="props.item.isClient" class="fixed-width">
          <v-flex>
            <v-text-field
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index"
              class="small-input"
              box
              v-model.number="props.item.dropoffFlagfall"
              hide-details
              :prefix="'$'"
              :disabled="!isEdited"
              :rules="[validate.required, validate.number]"
            >
            </v-text-field>
            <span v-else class="subheading grey--text">
              {{ props.item.dropoffFlagfall }}
            </span>
          </v-flex>
        </td>

        <td class="extra-wide">
          <v-flex>
            <v-select
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index"
              :items="applicableFuelSurcharges"
              item-text="shortName"
              item-value="id"
              v-model="props.item.appliedFuelSurcharge"
              :disabled="!isEdited"
              :rules="[validate.required, validate.number]"
              box
              hide-details
              class="small-input"
            >
            </v-select>
            <span v-else class="subheading grey--text">
              {{ props.item.appliedFuelSurcharge }}
            </span>
          </v-flex>
        </td>

        <td class="extra-wide">
          <v-select
            v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index"
            box
            class="small-input"
            :items="applicableDemurrages"
            :disabled="!isEdited"
            item-text="shortName"
            hide-details
            item-value="id"
            v-model="props.item.appliedDemurrageCharge"
            :rules="[validate.required, validate.number]"
          >
          </v-select>
          <span v-else class="subheading grey--text">
            {{ props.item.appliedDemurrageCharge }}
          </span>
        </td>

        <td>
          <v-flex>
            <v-layout
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index"
            >
              <v-text-field
                class="small-input"
                hide-details
                box
                :prefix="'$'"
                :disabled="props.item.demurrage.appliedDemurrageId === 3 || !isEdited"
                v-model.number="props.item.demurrageRate"
                :rules="[validate.required, validate.number]"
              >
              </v-text-field>
            </v-layout>
            <span v-else class="subheading grey--text">
              {{ props.item.demurrageRate }}
            </span>
          </v-flex>
        </td>

        <td v-if="props.item.isClient">
          <v-flex>
            <v-text-field
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index"
              box
              class="small-input"
              hide-details
              :suffix="'mins'"
              v-model.number="graceTimeInMinutes"
              :disabled="zoneRate.demurrage.appliedDemurrageId === 3 || !isEdited"
              :rules="[validate.required, validate.number]"
            >
            </v-text-field>
            <span v-else class="subheading grey--text">
              {{ props.item.demurrageGrace }}
            </span>
          </v-flex>
        </td>

        <td class="extra-wide">
          <v-flex>
            <v-select
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index"
              v-model="props.item.demurrageFuelSurchargeApplies"
              :items="[
                { text: 'Apply', value: true },
                { text: 'Don\'t Apply', value: false }
              ]"
              :disabled="!isEdited"
              item-text="text"
              item-value="value"
              class="small-input"
              hide-details
              box
            />
            <span v-else class="subheading grey--text">
              {{ props.item.demurrageFuelSurchargeApplies }}
            </span>
          </v-flex>
        </td>
        <td class="text-xs-center" v-if="!props.item.isServiceHeader">
          <div
            v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index"
          >
            <v-btn
              small
              flat
              icon
              @click.stop="saveZoneRate()"
              :disabled="!isEdited"
            >
              <v-icon color="success">fas fa-check</v-icon>
            </v-btn>
            <!-- <v-btn
              small
              flat
              icon
              @click.stop="cancelZoneEdit()"
            >
              <v-icon color="grey">close</v-icon>
            </v-btn> -->
            <v-btn
              small
              flat
              icon
              @click.stop="removeRate()"
              :disabled="!isEdited"
            >
              <v-icon color="error">delete</v-icon>
            </v-btn>
          </div>
        </td>

        <td v-if="props.item.isServiceHeader" class="copy-controls">
          <div>
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  v-if="!copyActive"
                  icon
                  flat
                  @click.stop="generateNewZoneRate(props.item.serviceTypeId)"
                  color="warning"
                  class="mr-1"
                  :disabled="!isEdited"
                  v-bind="attrs"
                  v-on="on"
                >
                  <v-icon size="18">fas fa-plus-circle</v-icon>
                </v-btn>
              </template>
              <span>Add new zone rate</span>
            </v-tooltip>

            <v-btn
              v-if="!copyActive"
              icon
              flat
              @click="copyValue(props.item.serviceTypeId)"
              :disabled="!isEdited"
            >
              <v-icon size="18" color="accent"> fad fa-copy </v-icon>
            </v-btn>

            <v-icon
              v-if="copyActive && copiedServiceTypeId === props.item.serviceTypeId"
              fab
              @click="bulkChanges()"
              color="yellow"
            >
              fad fa-paste
            </v-icon>

            <v-checkbox
              v-if="props.item.isServiceHeader && copyActive && copiedServiceTypeId !== props.item.serviceTypeId"
              v-model="selectedBulkChanges"
              :value="props.item.serviceTypeId"
              hide-details
              small
              class="ma-0"
            ></v-checkbox>
          </div>
        </td>
      </tr>
    </template>
  </v-data-table>
</section>
