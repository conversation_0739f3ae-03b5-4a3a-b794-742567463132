import FormCard from '@/components/common/ui-elements/form_card.vue';
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import {
  getServiceTypeById,
  returnServiceTypeShortNameFromId,
} from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import {
  GraceTypes,
  graceTypes,
  returnGraceShortName,
} from '@/interface-models/Generic/ServiceTypes/GraceTypes';
import {
  RateMultipliers,
  rateMultipliers,
} from '@/interface-models/Generic/ServiceTypes/RateMultipliers';
import ShortLongName from '@/interface-models/Generic/ShortLongName/ShortLongName';
import {
  startAndReturnLegs,
  StartAndReturnLegs,
} from '@/interface-models/Generic/StartAndReturnLegs/StartAndReturnLegs';
import { Validation } from '@/interface-models/Generic/Validation';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import FleetAssetServiceRate from '@/interface-models/ServiceRates/FleetAssetOwner/FleetAssetServiceRate/FleetAssetServiceRate';
import applicableFuelSurcharges from '@/interface-models/ServiceRates/FuelSurcharges/applicableFuelSurcharges';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import TimeRateType from '@/interface-models/ServiceRates/ServiceTypes/TimeServiceRate/TimeRateType';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
@Component({
  components: { FormCard },
})
export default class ServiceRateTableTime extends Vue {
  @Prop() public serviceRate: ClientServiceRate | FleetAssetServiceRate;
  @Prop() public defaultsEdited: boolean;
  @Prop() public adminPage: boolean;
  @Prop() public isFleetAsset: boolean;
  @Prop() public isEdited: boolean;
  @Prop() public isNew: boolean;
  @Prop({ default: false }) public isClient: boolean;
  @Prop({ default: true }) public fixedTopPanel: boolean;
  @Prop({ default: '' }) public searchTerm: string;
  public rateId: number = 1;
  public rateMultipliers: RateMultipliers[] = rateMultipliers.filter((m) =>
    [1, 2].includes(m.id),
  );

  public returnGraceType = returnGraceShortName;
  public graceTypes: GraceTypes[] = graceTypes;
  public selectedBulkChanges: number[] = [];
  public bulkChange: number = -1;
  public copyActive: boolean = false;

  public displayCurrencyValue: any = DisplayCurrencyValue;
  public applicableFuelSurcharges: ShortLongName[] =
    applicableFuelSurcharges.filter((x: ShortLongName) =>
      (this.isClient || this.adminPage) && x.id === 2 ? false : true,
    );
  public startAndReturnLegTypes: StartAndReturnLegs[] =
    startAndReturnLegs.filter((type: StartAndReturnLegs) => type.showInRate);
  public pasteIconLocation: number = -1;
  public selectedEdit: number = -1;

  public setSelectedEdit(index: number) {
    this.selectedEdit = index;
  }

  @Watch('isEdited')
  public isEditedChange(val: boolean) {
    if (!this.isEdited) {
      this.selectedEdit = -1;
    }
  }

  // sets last added service rate type as selected edit row
  @Watch('timeRateItems')
  public onTimeRateItemsChange(
    newValue: RateTableItems[],
    oldValue: RateTableItems[],
  ) {
    if (newValue && newValue.length > 0) {
      this.setSelectedEdit(newValue.length - 1);

      // Scroll to bottom when new item is added
      if (oldValue && newValue.length > oldValue.length) {
        this.$nextTick(() => {
          window.scrollTo({
            top: document.body.scrollHeight,
            behavior: 'smooth',
          });
        });
      }
    }
  }

  public returnServiceName(rate: number) {
    return returnServiceTypeShortNameFromId(rate);
  }

  get validate(): Validation {
    return validationRules;
  }

  public validationError(value: any, rules: any) {
    const validatedResults: boolean[] = [];
    for (const rule of rules) {
      validatedResults.push(typeof rule(value) === 'string');
    }
    return validatedResults.includes(true);
  }

  get timeRateItems() {
    let items = this.serviceRate.rateTableItems.filter(
      (item: RateTableItems) => {
        const serviceType = getServiceTypeById(item.serviceTypeId);
        if (serviceType && this.adminPage) {
          return item.rateTypeId === this.rateId && serviceType.divisionService;
        } else if (serviceType) {
          return item.rateTypeId === this.rateId;
        } else {
          return false;
        }
      },
    );
    // Apply search filter if searchTerm is provided
    if (this.searchTerm && this.searchTerm.trim().length > 0) {
      const searchTerm = this.searchTerm.toLowerCase().trim();
      items = items.filter((item) => {
        // Search in service type names (first column)
        const shortName = item.serviceShortName?.toLowerCase() || '';
        const longName = item.serviceLongName?.toLowerCase() || '';
        return shortName.includes(searchTerm) || longName.includes(searchTerm);
      });
    }
    return items;
  }

  public returnMultiplierValue(id: number) {
    const found = this.rateMultipliers.find((item) => item.id === id);
    if (found !== undefined) {
      return found.longName;
    }
  }

  public setBulkChangeConfirm(index: number) {
    if (index === this.bulkChange) {
      return false;
    } else {
      return true;
    }
  }

  public copyValue(index: number) {
    this.copyActive = true;
    this.pasteIconLocation = index;
  }

  public setBulkChangeRadio(index: number) {
    if (this.bulkChange === -1) {
      return false;
    } else if (index !== this.bulkChange) {
      return true;
    } else {
      return false;
    }
  }

  public bulkChanges(index: number) {
    for (const item of this.selectedBulkChanges) {
      for (const [serviceIndex, service] of this.timeRateItems.entries()) {
        const masterChange = JSON.parse(
          JSON.stringify(this.timeRateItems[index]),
        );
        if (item === serviceIndex) {
          this.timeRateItems[serviceIndex].rateTypeObject =
            masterChange.rateTypeObject;
          this.timeRateItems[serviceIndex].fuelSurcharge =
            masterChange.fuelSurcharge;
        }
      }
    }
    this.selectedBulkChanges = [];
    this.bulkChange = -1;
    this.copyActive = false;
  }

  public returnLegTypeName(legTypeId: number) {
    const foundLeg = this.startAndReturnLegTypes.find(
      (legType: StartAndReturnLegs) => legType.id === legTypeId,
    );

    if (foundLeg !== undefined) {
      return foundLeg.type;
    }
  }

  public returnApplicableFuelSurchargeShortName(id: number): string {
    const applicableFuelSurcharge = this.applicableFuelSurcharges.find(
      (x: ShortLongName) => x.id === id,
    );
    return applicableFuelSurcharge ? applicableFuelSurcharge.shortName : '';
  }

  // checks if rate is empty/null or zero to highlights row
  public isRateItemEmpty(rateItem: RateTableItems): boolean {
    if (!rateItem.rateTypeObject || rateItem.rateTypeId !== 1) {
      return false;
    }
    const rateObj = rateItem.rateTypeObject as TimeRateType;
    return !rateObj.rate || rateObj.rate === 0;
  }

  public tabHandler() {
    const el = document.querySelectorAll('.fuel-checkbox');
    if (
      el[this.selectedEdit] &&
      el[this.selectedEdit].classList.contains('v-input--is-focused')
    ) {
      this.selectedEdit = this.selectedEdit + 1;
    }
  }

  public created() {
    window.addEventListener('keydown', this.tabHandler);
  }
  public destroyed() {
    window.removeEventListener('keydown', this.tabHandler);
  }
}
