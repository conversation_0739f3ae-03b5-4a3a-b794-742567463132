.service-rate-table-time {
  position: relative;
  margin-bottom: 10px;
  border-radius: 10px;
  margin-top: 10px;

  .services-table {
    border-radius: 0 0 10px 10px;
  }

  .table-headers {
    display: flex;
    background-color: var(--background-color-300);
    border: 1px solid $translucent;
    height: 80px;
    width: 100%;
    color: var(--light-text-color);
    border-radius: 10px 10px 0 0;

    &.table-headers-fixed {
      position: sticky;
      z-index: 3;
    }
  }

  .form-container {
    background-color: var(--background-color-400);
    border-bottom: 1px solid $translucent;
    flex-shrink: 0;
    width: 100%;
    display: flex;

    &.selected-item {
      background-color: var(--background-color-300);
      border-bottom: 0.5px solid $info;
    }

    &.empty-rate-item {
      background-color: rgba(255, 193, 7, 0.1);
      border-left: 1px solid $warning;
    }

    &:hover {
      background-color: var(--background-color-500);
    }
  }

  &.clientPage {
    .hide-scrolling-data {
      top: 81px;
    }
    .table-headers {
      top: 81px;
    }

    .services-table {
      margin-top: 1px;
    }
  }

  &.fleetAssetPage {
    .hide-scrolling-data {
      top: 116px;
      height: 17px;
    }
    .table-headers {
      top: 133px;
    }
  }

  .service-type {
    width: 74px !important;
    justify-content: center;
    display: flex;
    align-items: center;
  }

  .service-name {
    color: var(--bg-light);
  }

  // .services-table .service-type {
  //   border-bottom: 1px solid $border-color;
  // }

  &.adminPage {
    .table-headers {
      width: 100%;
      z-index: 3;
      top: -28px;
    }
  }

  .hide-scrolling-data {
    background-color: var(--background-color-100);
    height: 16px;
    width: calc(100% - 76px);
    z-index: 10;
    left: calc(25% + 37px);
    position: fixed;
  }

  .border-right {
    border-right: 0.5px solid $translucent;
  }

  .border-left {
    border-left: 0.5px solid $translucent;
  }

  .checkbox-container {
    cursor: pointer;
  }

  .checkbox {
    pointer-events: none;
    margin-bottom: 5px;
  }

  .rate-name {
    padding-left: 9px;
  }

  .rate-sections {
    height: 37px;
  }

  .rate-heading {
    border-bottom: 0.5px solid $translucent;
    height: 37px;
  }

  .radio-group {
    display: flex;
    justify-content: center;
  }

  input .radio-btn {
    width: 50px;
  }

  .v-input--checkbox {
    display: flex;
    justify-content: center;
  }

  .row-height {
    height: 56px;
  }

  .bulk-selections {
    input[type='radio'] {
      transform: scale(2);
    }
  }

  .selected {
    display: none;
  }

  .bulkChangeInactive {
    display: none;
  }

  .thick-border-right {
    border-right: 0.5px solid $translucent !important;
  }

  .thick-border-left {
    border-left: 0.5px solid $translucent !important;
  }

  .thick-border-bottom {
    border-bottom: 0.5px solid $translucent !important;
  }

  &.formDisabled {
    pointer-events: none;
  }

  .big-width {
    width: 14% !important;
  }

  .small-width {
    width: 10% !important;
  }

  .error--text {
    background-color: rgba(207, 96, 96, 0.3);
  }

  .v-icon {
    color: var(--accent);
    opacity: 0.9;
  }
}
