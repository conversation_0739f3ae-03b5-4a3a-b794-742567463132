<section class="point-to-point">
  <v-layout justify-end>
    <v-menu right>
      <template v-slot:activator="{ on }">
        <v-icon size="18" class="pl-3 pr-1 pb-3" v-on.stop="on"
          >fas fa-ellipsis-v
        </v-icon>
      </template>
      <v-list class="v-list-custom" dense>
        <v-list-tile @click="generateNewPointToPointRate">
          <v-list-tile-title class="pr-2 ma-0">
            <span>Create Point to Point</span>
          </v-list-tile-title>
        </v-list-tile>
      </v-list>
    </v-menu>
  </v-layout>
  <v-data-table
    class="gd-dark-theme"
    :headers="headers"
    :items="pointToPointRateTableItems"
    :rows-per-page-items="[10, 20]"
  >
    <template v-slot:items="props">
      <tr
        style="cursor: pointer"
        @click="editPointToPoint(props.item.serviceTypeId, props.item.index)"
      >
        <td>{{ props.item.serviceTypeName }}</td>
        <td v-if="props.item.isClient">{{ props.item.fromAddress }}</td>
        <td v-if="props.item.isClient">{{ props.item.toAddress }}</td>
        <td v-if="props.item.isClient">{{ props.item.rate }}</td>
        <td v-if="!props.item.isClient">{{ props.item.percentage }}</td>
        <td>{{ props.item.appliedFuelSurcharge }}</td>
        <td>{{ props.item.appliedDemurrageCharge }}</td>
        <td>{{ props.item.demurrageRate }}</td>
        <td v-if="props.item.isClient">{{ props.item.demurrageGrace }}</td>
        <td>{{ props.item.demurrageFuelSurchargeApplies }}</td>
      </tr>
    </template>
  </v-data-table>
  <v-dialog
    v-if="pointToPointRate"
    v-model="dialogIsOpen"
    content-class="v-dialog-custom"
    width="600px"
    persistent
  >
    <v-layout
      justify-space-between
      class="task-bar app-theme__center-content--header no-highlight"
    >
      <span>Point To Point Rate</span>
      <div
        class="app-theme__center-content--closebutton"
        @click="cancelPointToPointEdit"
      >
        <v-icon class="app-theme__center-content--closebutton--icon"
          >fal fa-times</v-icon
        >
      </div>
    </v-layout>
    <div class="app-theme__center-content--body dialog-content">
      <v-layout class="pa-2" v-if="!isFleetAsset">
        <v-flex>
          <v-alert value="true" type="info" outline>
            <span class="alert-text"
              >Please ensure addresses for Point to Point rates have been added
              as Common Addresses prior to creating rates.</span
            >
          </v-alert>
        </v-flex>
      </v-layout>

      <v-form ref="p2pForm">
        <div class="px-2">
          <v-layout align-center>
            <h5 class="subheader--bold pr-3 pt-1">Primary rate</h5>
            <v-flex>
              <v-divider></v-divider>
            </v-flex>
          </v-layout>

          <v-layout>
            <v-flex md12>
              <v-select
                label="Service Type"
                :disabled="!isEdited"
                :rules="[validate.required]"
                v-if="isNewRate"
                v-model="selectedServiceTypeId"
                :items="existingServiceTypes"
                item-value="serviceTypeId"
                box
                item-text="optionSelectName"
                color="blue"
                persistent-hint
              >
              </v-select>
            </v-flex>
          </v-layout>
          <v-layout wrap v-if="!isFleetAsset">
            <v-flex md12>
              <v-select
                box
                label="From"
                :disabled="!isEdited"
                item-value="_id"
                class="select__no-overflow"
                :items="commonAddressList"
                v-model="pointToPointRate.fromAddressReference"
                :rules="[validate.required]"
              >
                <template slot="selection" slot-scope="data">
                  <v-flex py-1>
                    <v-layout>
                      {{ data.item.addressNickname }}
                    </v-layout>
                    <v-layout>
                      <small style="font-size: 10px; opacity: 0.6">{{
                        data.item.address.formattedAddress
                      }}</small>
                    </v-layout>
                  </v-flex>
                </template>
                <template slot="item" slot-scope="data">
                  <span class="search-result__text">
                    {{ data.item.addressNickname }} -
                    {{ data.item.address.formattedAddress }}</span
                  >
                </template>
              </v-select>
            </v-flex>
            <v-flex md12>
              <v-select
                box
                label="To "
                :items="commonAddressList"
                :disabled="!isEdited"
                v-model="pointToPointRate.toAddressReference"
                item-value="_id"
                :rules="[validate.required]"
              >
                <template slot="selection" slot-scope="data">
                  <v-flex py-1>
                    <v-layout>
                      {{ data.item.addressNickname }}
                    </v-layout>
                    <v-layout>
                      <small style="font-size: 10px; opacity: 0.6">{{
                        data.item.address.formattedAddress
                      }}</small>
                    </v-layout>
                  </v-flex>
                </template>
                <template slot="item" slot-scope="data">
                  <span class="search-result__text">
                    {{ data.item.addressNickname }} -
                    {{ data.item.address.formattedAddress }}</span
                  >
                </template>
              </v-select>
            </v-flex>
            <v-flex md12>
              <v-text-field
                box
                label="Rate"
                :prefix="'$'"
                type="number"
                :disabled="!isEdited"
                v-model.number="pointToPointRate.rate"
                :rules="[validate.required, validate.number]"
              >
              </v-text-field>
            </v-flex>
          </v-layout>
          <v-layout v-if="isFleetAsset">
            <v-flex md12>
              <v-text-field
                box
                :label="'Percentage'"
                type="number"
                :disabled="!isEdited"
                v-model="pointToPointRate.percentage"
              >
              </v-text-field>
            </v-flex>
          </v-layout>
          <v-layout wrap>
            <v-flex md12>
              <v-select
                label="Fuel Surcharge Application"
                box
                :items="applicableFuelSurcharges"
                :disabled="!isEdited"
                item-text="shortName"
                item-value="id"
                v-model="pointToPointRate.appliedFuelSurchargeId"
                :rules="[validate.required, validate.number]"
              ></v-select>
            </v-flex>

            <v-flex md12>
              <v-layout align-center>
                <h5 class="subheader--bold pr-3">Demurrage</h5>
                <v-flex>
                  <v-divider></v-divider>
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12>
              <v-select
                box
                :items="applicableDemurrages"
                :disabled="!isEdited"
                item-text="shortName"
                label="Demurrage Rate Application"
                item-value="id"
                v-model="pointToPointRate.demurrage.appliedDemurrageId"
                :rules="[validate.required, validate.number]"
              >
              </v-select>
            </v-flex>
            <v-flex md12>
              <v-text-field
                box
                label="Demurrage Rate (per hour)"
                :prefix="'$'"
                type="number"
                :disabled="pointToPointRate.demurrage.appliedDemurrageId === 3 || !isEdited"
                v-model.number="pointToPointRate.demurrage.rate"
                :rules="[validate.required, validate.number]"
              >
              </v-text-field>
            </v-flex>
            <v-flex md12 v-if="!isFleetAsset">
              <v-text-field
                box
                label="Demurrage Grace Time (mins)"
                type="number"
                v-model.number="graceTimeInMinutes"
                :disabled="pointToPointRate.demurrage.appliedDemurrageId === 3 || !isEdited"
                :rules="[validate.required, validate.number]"
              >
              </v-text-field>
            </v-flex>

            <v-flex md12>
              <v-layout class="label-container" justify-end>
                <h6 class="subheader--faded pr-2" style="padding-top: 2px">
                  Apply fuel surcharge to demurrage duration
                </h6>
                <div>
                  <v-switch
                    v-model="pointToPointRate.demurrage.demurrageFuelSurchargeApplies"
                    :disabled="pointToPointRate.demurrage.appliedDemurrageId === 3 || !isEdited"
                    class="ma-0 pa-0"
                    color="#64ffda"
                  >
                  </v-switch>
                </div>
              </v-layout>
            </v-flex>
          </v-layout>
        </div>
        <v-divider class="mt-2"></v-divider>
        <v-layout justify-space-between>
          <ConfirmationDialog
            message="You are about to remove this point to point rate. Do you wish to proceed?"
            buttonText="Remove"
            title="Point to Point Rate Removal Confirmation"
            @confirm="removeRate"
            :buttonDisabled="!isEdited"
            buttonColor="error"
            :isFlatButton="true"
            :isSmallButton="false"
            :isDepressedButton="true"
            confirmationButtonText="Confirm and Remove"
            :dialogIsActive="true"
          ></ConfirmationDialog>
          <v-btn
            color="blue"
            depressed
            :disabled="!isEdited"
            @click="savePointToPointRate"
          >
            <span v-if="isNewRate">Add Point To Point</span>
            <span v-else>Update Point To Point</span>
          </v-btn>
        </v-layout>
      </v-form>
    </div>
  </v-dialog>
</section>
