.table-row {
  background-color: $secondary;
}

.no-unalocated-job-title {
  text-transform: uppercase;
  font-weight: 500;
}

.service-name {
  width: 204px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid black;
  color: white;
  flex-shrink: 0;
}

.form-container {
  display: flex;
  flex-wrap: wrap;
  position: relative;

  .input-container {
    padding: 0 1em;
    flex: 1 0 21%;
  }

  .form-container__row {
    // background-color: red;

    .select__no-overflow {
      .v-select__selection {
        white-space: nowrap;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 90%;
      }
    }
  }
  // background-color: #26263a93;
}

.radio-group {
  width: 100px;
  display: flex;
  justify-content: center;
}

.v-input--checkbox {
  display: flex;
  justify-content: center;
}

.bulk-selection-container {
  position: absolute;
  right: -55px;
  top: -88px;
  width: 204px;
  display: flex;
  justify-content: center;
}

.selected {
  display: none;
}

.bulkChangeInactive {
  display: none;
}

.alert-text {
  text-transform: uppercase;
  font-weight: 500;
}
