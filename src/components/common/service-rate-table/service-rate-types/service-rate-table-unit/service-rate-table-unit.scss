.table-row {
  background-color: #263238;
}

.input-container {
  padding: 0 1em;
}

.ranges-container {
  display: flex;
  flex-wrap: wrap;
  .input-container {
    padding: 0 1em;
    flex: 1 0 21%;
  }
}

.table-header {
  font-size: $font-size-16;
}

i .v-icon .v-icon--link {
  padding-bottom: 10px !important;
}

.alert-text {
  text-transform: uppercase;
  font-weight: 500;
}

.scrollable {
  height: 100%;
  max-height: calc(90vh - 129px);
  overflow-y: scroll;
}
