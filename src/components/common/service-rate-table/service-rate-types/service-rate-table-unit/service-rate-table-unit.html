<section class="unit-rate-component">
  <v-layout justify-end v-if="unitRateItem">
    <v-btn
      small
      outline
      @click="newUnitRate"
      color="warning"
      class="mb-2"
      :disabled="!isEdited || !isClient && unitRates.length >= 1"
    >
      <v-icon small class="mr-2">add</v-icon>Create Unit Rate
    </v-btn>
  </v-layout>

  <ContentDialog
    v-if="unitRate"
    :showDialog.sync="dialogIsOpen"
    title="Unit Rate Maintenance"
    width="800px"
    contentPadding="pa-0"
    @cancel="cancelUnitRate"
    @confirm="addUnitRate"
    :isConfirmUnsaved="false"
    :confirmBtnText="!isEdited ? 'Add Unit Rate' : 'Update Unit Rate'"
    :isDisabled="!isEdited"
    :isLoading="false"
    @action="deleteUnitRate"
    :showActionButton="true"
    actionBtnText="Remove"
    :actionRequiresConfirmation="true"
    :actionConfirmationMessage="
        'Please confirm that you wish to remove this unit rate.'
      "
    :showActions="false"
  >
    <div style="position: relative">
      <div class="app-theme__center-content--body">
        <div class="form-container">
          <v-form ref="unitForm" class="pa-3 scrollable">
            <v-layout v-if="isClient" class="pb-4">
              <v-flex md12>
                <v-alert
                  :value="true"
                  type="info"
                  outline
                  style="background-color: #26263a93"
                >
                  <p class="ma-0 alert-text">
                    Please note that load flagfalls will only be applied at a
                    pickup location.
                  </p>
                </v-alert>
              </v-flex>
            </v-layout>
            <v-layout v-if="!isClient" align-center class="" pb-2>
              <v-flex md12>
                <v-alert
                  :value="true"
                  type="info"
                  outline
                  style="background-color: #26263a93"
                >
                  <p class="ma-0 alert-text">
                    Please note that a clients fuel surcharge rate above 0% for
                    any zone will satisfy a fuel surcharge application of "Apply
                    (Client > 0%)".
                  </p>
                </v-alert>
              </v-flex>
            </v-layout>
            <v-layout wrap row>
              <v-flex md12>
                <v-layout align-center>
                  <h5 class="subheader--bold pb-2 pt-2">Primary rate</h5>
                  <v-flex>
                    <v-divider></v-divider>
                  </v-flex>
                </v-layout>
              </v-flex>

              <v-layout wrap v-if="isClient">
                <v-flex md6 class="pr">
                  <v-layout align-center class="unit-inputs">
                    <v-text-field
                      solo
                      flat
                      class="v-solo-custom"
                      label="Zone Name"
                      :disabled="!isEdited"
                      :rules="[validate.required]"
                      v-model="unitRate.zoneName"
                      hint="Zone name associated with this unit rate."
                      persistent-hint
                    >
                    </v-text-field>
                  </v-layout>
                </v-flex>
                <v-flex md3 class="px">
                  <v-select
                    :items="unitRateTypes"
                    :disabled="!isEdited"
                    item-value="id"
                    item-text="longName"
                    solo
                    flat
                    class="v-solo-custom"
                    label="Unit Type"
                    @change="unitTypeChanged"
                    v-model="unitRate.unitTypeId"
                    :rules="[validate.required, validate.number]"
                    hint="Select from weight or a more dynamic unit type."
                    persistent-hint
                  >
                  </v-select>
                </v-flex>
                <v-flex md3 class="pl">
                  <v-text-field
                    solo
                    flat
                    class="v-solo-custom"
                    label="Unit Name"
                    :disabled="unitRate.unitTypeId === 2 || !isEdited"
                    :rules="[validate.required]"
                    v-model="unitRate.unitTypeName"
                    hint="The name you wish to give this unit."
                    persistent-hint
                  >
                  </v-text-field>
                </v-flex>
                <v-flex md12>
                  <v-layout justify-space-between class="mb-4">
                    <v-checkbox
                      label="Fork Lift Required"
                      :disabled="!isEdited"
                      v-model="unitRate.forkLiftRequired"
                      hint="Forklift is required for this zone by default."
                      persistent-hint
                    />
                    <v-checkbox
                      label="Dangerous Goods"
                      v-model="unitRate.isDangerousGoods"
                      :disabled="!isEdited"
                      hint="Dangerous goods is applied to this zone by default."
                      persistent-hint
                    />
                    <div>
                      <v-checkbox
                        label="Ranged Rates"
                        v-model="unitRate.isRangeRate"
                        :disabled="!isEdited"
                        hint="Rates per unit are based on ranges."
                        persistent-hint
                      />
                    </div>
                  </v-layout>
                </v-flex>

                <v-flex md6 class="pr">
                  <v-text-field
                    solo
                    flat
                    class="v-solo-custom"
                    persistent-hint
                    label="Hand Pickup Flagfall Rate"
                    hint="Hand Pickup Flagfall Rate"
                    :prefix="'$'"
                    type="number"
                    :disabled="!isEdited"
                    v-model.number="unitRate.pickUpFlagFallHand"
                    :rules="[validate.required, validate.number]"
                  >
                  </v-text-field>
                </v-flex>
                <v-flex md6 class="pl">
                  <v-text-field
                    solo
                    flat
                    class="v-solo-custom"
                    persistent-hint
                    hint="Hand Drop-off Flagfall Rate"
                    label="Hand Drop-off Flagfall Rate"
                    :prefix="'$'"
                    type="number"
                    :disabled="!isEdited"
                    v-model.number="unitRate.dropOffFlagFallHand"
                    :rules="[validate.required, validate.number]"
                  >
                  </v-text-field>
                </v-flex>
                <v-flex md6 class="pr">
                  <v-text-field
                    solo
                    flat
                    class="v-solo-custom"
                    persistent-hint
                    hint="Fork Lift Pickup Flagfall Rate"
                    label="Fork Lift Pickup Flagfall Rate"
                    :prefix="'$'"
                    type="number"
                    :disabled="!isEdited"
                    v-model.number="unitRate.pickUpFlagFallForkLift"
                    :rules="[validate.required, validate.number]"
                  >
                  </v-text-field>
                </v-flex>
                <v-flex md6 class="pl">
                  <v-text-field
                    solo
                    flat
                    class="v-solo-custom"
                    persistent-hint
                    hint="Fork Lift Drop-off Flagfall Rate"
                    label="Fork Lift Drop-off Flagfall Rate"
                    :prefix="'$'"
                    type="number"
                    :disabled="!isEdited"
                    :rules="[validate.required, validate.number]"
                    v-model.number="unitRate.dropOffFlagFallForkLift"
                  >
                  </v-text-field>
                </v-flex>
                <v-flex md6 class="pr">
                  <v-text-field
                    solo
                    flat
                    class="v-solo-custom"
                    persistent-hint
                    hint="Dangerous Goods Flag Fall"
                    :rules="[validate.required]"
                    label="Dangerous Goods Flag Fall"
                    :prefix="'$'"
                    :disabled="!isEdited"
                    type="number"
                    v-model.number="unitRate.dangerousGoodsFlagFall"
                  />
                </v-flex>
                <v-flex md6 class="pl">
                  <v-text-field
                    solo
                    flat
                    class="v-solo-custom"
                    persistent-hint
                    hint="Minimum Units"
                    label="Minimum Units"
                    :rules="[validate.required, validate.number]"
                    :disabled="!isEdited"
                    type="number"
                    v-model.number="unitRate.minimumUnits"
                  >
                  </v-text-field>
                </v-flex>
                <v-flex md12>
                  <div
                    class="ranges-container"
                    v-for="(rangeItem, rangeItemIndex) in unitRate.unitRanges"
                    :key="rangeItemIndex"
                  >
                    <v-layout>
                      <v-flex md4 class="pr" v-if="unitRate.isRangeRate">
                        <v-text-field
                          solo
                          flat
                          class="v-solo-custom"
                          persistent-hint
                          hint="Start Range"
                          label="Start Range"
                          :disabled="!isEdited"
                          :rules="[validate.required,validate.number, validRangeAmount(rangeItem.startRange, rangeItemIndex, true)]"
                          type="number"
                          v-model.number="rangeItem.startRange"
                        >
                        </v-text-field>
                      </v-flex>
                      <v-flex md-4 class="px" v-if="unitRate.isRangeRate">
                        <v-text-field
                          solo
                          flat
                          class="v-solo-custom"
                          persistent-hint
                          hint="End Range"
                          label="End Range"
                          :disabled="!isEdited"
                          :rules="[validate.required, validate.number, validRangeAmount(rangeItem.endRange, rangeItemIndex, false)]"
                          type="number"
                          v-model.number="rangeItem.endRange"
                        >
                        </v-text-field>
                      </v-flex>

                      <v-flex :class="unitRate.isRangeRate ? 'md4 ': 'md6 pr'">
                        <v-layout>
                          <v-text-field
                            solo
                            flat
                            class="v-solo-custom"
                            persistent-hint
                            hint="Rate"
                            label="Rate"
                            :prefix="'$'"
                            type="number"
                            :disabled="!isEdited"
                            :rules="[validate.required, validate.unitRateNumber]"
                            v-model.number="rangeItem.unitRate"
                          >
                          </v-text-field>
                          <v-icon
                            @click="deleteRangedRate(rangeItemIndex)"
                            size="18"
                            class="pl-3"
                            style="padding-top: 21px"
                            v-if="rangeItemIndex !== 0 && rangeItemIndex === unitRate.unitRanges.length - 1"
                            color="error"
                          >
                            fal fa-trash-alt</v-icon
                          >

                          <v-icon
                            @click="addRangeRate"
                            class="pl-3"
                            style="padding-top: 19px"
                            v-if="unitRate.isRangeRate && isClient && rangeItemIndex === 0"
                            color="info"
                          >
                            far fa-plus</v-icon
                          >
                        </v-layout>
                      </v-flex>

                      <v-flex
                        class="pl"
                        :class="unitRate.isRangeRate ? 'md4': 'md6'"
                        v-if="!unitRate.isRangeRate && rangeItemIndex === 0"
                      >
                        <v-text-field
                          solo
                          flat
                          class="v-solo-custom"
                          persistent-hint
                          hint="Per Unit Amount"
                          :disabled="true"
                          label="Per Unit Amount"
                          type="number"
                          v-model.number="rangeItem.unitAmountMultiplier"
                          :rules="[validate.required, validate.number]"
                        >
                        </v-text-field>
                      </v-flex>
                    </v-layout>
                  </div>
                </v-flex>
              </v-layout>
              <v-layout wrap v-if="!isClient">
                <v-flex md12>
                  <v-text-field
                    solo
                    flat
                    class="v-solo-custom"
                    persistent-hint
                    hint="Percentage Rate"
                    label="Percentage Rate"
                    type="number"
                    :disabled="!isEdited"
                    :rules="[validate.required, validate.percentage, validate.nonNegative]"
                    v-model.number="unitRate.fleetAssetPercentage"
                  >
                  </v-text-field>
                </v-flex>
              </v-layout>

              <v-flex md12>
                <v-select
                  label="Fuel Surcharge Application"
                  solo
                  flat
                  class="v-solo-custom"
                  persistent-hint
                  hint="Fuel Surcharge Application"
                  :items="applicableFuelSurcharges"
                  item-text="shortName"
                  item-value="id"
                  v-model="unitRate.appliedFuelSurchargeId"
                  :disabled="!isEdited"
                  :rules="[validate.required, validate.number]"
                >
                </v-select>
              </v-flex>
            </v-layout>

            <v-layout wrap>
              <v-flex md12>
                <v-layout align-center>
                  <h5 class="subheader--bold pt-2 pb-2">Demurrage</h5>
                  <v-flex>
                    <v-divider></v-divider>
                  </v-flex>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-select
                  solo
                  flat
                  class="v-solo-custom"
                  persistent-hint
                  hint="Demurrage Rate Application"
                  :items="applicableDemurrages"
                  item-text="shortName"
                  label="Demurrage Rate Application"
                  item-value="id"
                  v-model="unitRate.demurrage.appliedDemurrageId"
                  :disabled="!isEdited"
                  :rules="[validate.required, validate.number]"
                >
                </v-select>
              </v-flex>

              <v-flex md12>
                <v-text-field
                  v-if="isClient"
                  solo
                  flat
                  class="v-solo-custom"
                  persistent-hint
                  hint="Demurrage Grace Time (mins)"
                  label="Demurrage Grace Time (mins)"
                  type="number"
                  v-model.number="graceTimeInMinutes"
                  :disabled="unitRate.demurrage.appliedDemurrageId === 3 || !isEdited"
                  :rules="[validate.required, validate.number]"
                >
                </v-text-field>
              </v-flex>
              <v-layout>
                <v-flex mt-4 mr-4>
                  <v-layout class="label-container" justify-center>
                    <h6 class="subheader--faded pr-2" style="padding-top: 2px">
                      Apply fuel surcharge to demurrage duration
                    </h6>
                    <div>
                      <v-switch
                        v-model="unitRate.demurrage.demurrageFuelSurchargeApplies"
                        :disabled="!isEdited"
                        class="ma-0 pa-0"
                        color="orange"
                      >
                      </v-switch>
                    </div>
                  </v-layout>
                </v-flex>

                <v-flex mt-4 mr-4>
                  <v-layout class="label-container" justify-center>
                    <h6 class="subheader--faded pr-2" style="padding-top: 2px">
                      Apply fuel surcharge to Flag Fall Charges
                    </h6>
                    <div>
                      <v-switch
                        v-model="unitRate.fuelIsAppliedToFlagFalls"
                        :disabled="!isEdited"
                        class="ma-0 pa-0"
                        color="orange"
                      >
                      </v-switch>
                    </div>
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-layout>
          </v-form>
        </div>
        <v-divider class="mt-0"></v-divider>
        <v-layout justify-space-between v-if="unitRate" pa-2>
          <v-layout>
            <ConfirmationDialog
              buttonText="Remove"
              message="Please confirm that you wish to remove this unit rate."
              title="Unit rate removal"
              @confirm="deleteUnitRate"
              :isSmallButton="false"
              :isDepressedButton="true"
              :buttonDisabled="!isEdited"
              :isBlockButton="false"
              :buttonColor="'error'"
              :isFlatButton="false"
              :isOutlineButton="true"
              :confirmationButtonText="'Confirm'"
              :isCheckboxList="false"
              :dialogIsActive="true"
            ></ConfirmationDialog>
          </v-layout>
          <v-spacer />
          <v-layout justify-end align-center>
            <v-btn
              small
              color="info"
              @click="addUnitRate"
              :disabled="!isEdited"
              v-if="!isEdited"
              >Add Unit Rate
            </v-btn>
            <v-btn
              v-if="isEdited"
              color="info"
              :disabled="!isEdited"
              @click="addUnitRate"
              >Update Unit Rate
            </v-btn>
          </v-layout>
        </v-layout>
      </div>
    </div>
  </ContentDialog>

  <div class="client-unit-rate" v-if="!unitRate">
    <v-data-table
      class="mt-1 default-table-dark gd-dark-theme"
      :headers="headers"
      :items="unitRates"
      v-if="isClient"
    >
      <template v-slot:items="props">
        <tr
          @click="editUnitRate(props.item, props.index)"
          style="cursor: pointer"
        >
          <td>{{props.item.unitTypeName}}</td>
          <td>{{props.item.zoneName}}</td>
          <td>{{props.item.unitRanges.map(r => r.unitRate).join(', ')}}</td>
          <td v-if="props.item.isRangeRate">
            <div v-for="(rate, rateIndex) of props.item.unitRanges">
              {{rate.startRange}} - {{rate.endRange}}
            </div>
          </td>
          <td v-if="!props.item.isRangeRate">
            <div v-for="(rate, rateIndex) of props.item.unitRanges">
              {{rate.unitAmountMultiplier}}
            </div>
          </td>
          <td>{{props.item.minimumUnits}}</td>
          <td>{{props.item.forkLiftRequired ? "YES" : "NO"}}</td>
          <td>${{displayCurrencyValue(props.item.pickUpFlagFallHand)}}</td>
          <td>${{displayCurrencyValue(props.item.dropOffFlagFallHand)}}</td>
          <td>${{displayCurrencyValue(props.item.pickUpFlagFallForkLift)}}</td>
          <td>${{displayCurrencyValue(props.item.dropOffFlagFallForkLift)}}</td>
          <td>{{props.item.isDangerousGoods ? "YES" : "NO"}}</td>
          <td>${{displayCurrencyValue(props.item.dangerousGoodsFlagFall)}}</td>
          <td>
            {{appliedFuelSurchargeType(props.item.appliedFuelSurchargeId,
            props.item.fuelIsAppliedToFlagFalls)}}
          </td>
          <td>
            {{
            appliedDemurrageRateTableDisplay(props.item.demurrage.appliedDemurrageId)}}
          </td>
          <td>
            {{
            demurrageGraceTableDisplay(props.item.demurrage.graceTimeInMilliseconds)
            }}
          </td>
          <td>
            {{ props.item.demurrage.demurrageFuelSurchargeApplies ? 'Apply' :
            'Don\'t Apply'}}
          </td>
        </tr>
      </template>
    </v-data-table>
    <v-data-table
      class="pb-3 gd-dark-theme"
      :headers="fleetAssetHeaders"
      :items="unitRates"
      hide-actions
      v-if="!isClient"
    >
      <template v-slot:items="props">
        <tr
          style="cursor: pointer"
          @click="editUnitRate(props.item, props.index)"
        >
          <td class="pl-4">{{props.item.fleetAssetPercentage}}%</td>
          <td>
            {{appliedFuelSurchargeType(props.item.appliedFuelSurchargeId,
            null)}}
          </td>
          <td>
            {{
            appliedDemurrageRateTableDisplay(props.item.demurrage.appliedDemurrageId)}}
          </td>
          <td>
            {{ props.item.demurrage.demurrageFuelSurchargeApplies ? 'Apply' :
            'Don\'t Apply'}}
          </td>
        </tr>
      </template>
    </v-data-table>
  </div>
</section>
