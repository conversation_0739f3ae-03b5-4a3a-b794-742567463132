import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import FormCard from '@/components/common/ui-elements/form_card.vue';
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import ShortLongName from '@/interface-models/Generic/ShortLongName/ShortLongName';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { VForm } from '@/interface-models/Generic/VForm/VForm';
import { Validation } from '@/interface-models/Generic/Validation';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import applicableDemurrages from '@/interface-models/ServiceRates/Demurrage/applicableDemurrages';
import FleetAssetFuelSurchargeRate from '@/interface-models/ServiceRates/FleetAssetOwner/AdditionalCharges/FuelSurcharge/FleetAssetFuelSurchargeRate';
import FleetAssetServiceRate from '@/interface-models/ServiceRates/FleetAssetOwner/FleetAssetServiceRate/FleetAssetServiceRate';
import applicableFuelSurcharges from '@/interface-models/ServiceRates/FuelSurcharges/applicableFuelSurcharges';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import UnitRangeRate from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRangeRate';
import UnitRate from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRate';
import unitRateTypes from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRateTypes';
import moment from 'moment-timezone';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component({
  components: { FormCard, ConfirmationDialog, ContentDialog },
})
export default class ServiceRateTableUnit extends Vue {
  @Prop() public serviceRate: ClientServiceRate | FleetAssetServiceRate;
  @Prop() public fuelSurchargeRate:
    | ClientFuelSurchargeRate
    | FleetAssetFuelSurchargeRate;
  @Prop() public isClient: boolean;
  @Prop() public isEdited: boolean;
  @Prop({ default: '' }) public searchTerm: string;
  public unitRateTypes: ShortLongName[] = unitRateTypes;
  public unitRate: UnitRate | null = null;
  public rateId: number = 5;
  public editingIndex: number | null = null;
  public displayCurrencyValue: any = DisplayCurrencyValue;

  public applicableDemurrages: ShortLongName[] = applicableDemurrages.filter(
    (x: ShortLongName) => {
      if (x.id !== 4 && x.id !== 2) {
        return true;
      }

      if (this.isClient && x.id === 4) {
        return false;
      }

      if (!this.isClient && x.id === 2) {
        return false;
      }

      return true;
    },
  );

  public applicableFuelSurcharges: ShortLongName[] =
    applicableFuelSurcharges.filter((x: ShortLongName) =>
      this.isClient && x.id === 2 ? false : true,
    );

  public $refs!: {
    unitForm: VForm;
  };

  public headers: TableHeader[] = [
    {
      text: 'Unit',
      align: 'left',
      sortable: true,
      value: 'unitTypeName',
    },
    {
      text: 'Zone',
      align: 'left',
      sortable: false,
      value: 'zoneName',
    },
    {
      text: 'Rate ($)',
      align: 'left',
      sortable: false,
      value: 'unitRate',
    },
    {
      text: 'Per Amount',
      align: 'left',
      sortable: false,
      value: 'unitAmountMultiplier',
    },
    {
      text: 'Min Units',
      align: 'left',
      sortable: false,
      value: 'minimumUnits',
    },
    {
      text: 'Fork Lift',
      align: 'left',
      sortable: false,
      value: 'forkLiftRequired',
    },

    {
      text: 'PU Flagfall',
      align: 'left',
      sortable: false,
      value: 'pickUpFlagFallHand',
    },
    {
      text: 'DO Flagfall',
      align: 'left',
      sortable: false,
      value: 'dropOffFlagFallHand',
    },

    {
      text: 'FL PU Flagfall',
      align: 'left',
      sortable: false,
      value: 'pickUpFlagFallForkLift',
    },
    {
      text: 'FL DO Flagfall',
      align: 'left',
      sortable: false,
      value: 'dropOffFlagFallForkLift',
    },

    {
      text: 'DG Charge',
      align: 'left',
      sortable: false,
      value: 'isDangerousGoods',
    },
    {
      text: 'DG FlagFall',
      align: 'left',
      sortable: false,
      value: 'dangerousGoodsFlagFall',
    },
    {
      text: 'Fuel',
      align: 'left',
      value: 'appliedFuelSurcharge',
      sortable: false,
    },
    {
      text: 'Demurrage',
      align: 'left',
      value: 'demurrageType',
      sortable: false,
    },
    {
      text: 'Demurrage Grace',
      align: 'left',
      value: 'demurrageGrace',
      sortable: false,
    },
    {
      text: 'Demurrage Fuel',
      align: 'left',
      value: 'demurrageFuelSurchargeApplies',
      sortable: false,
    },
  ];

  get dialogIsOpen(): boolean {
    return this.unitRate !== null;
  }

  set dialogIsOpen(value: boolean) {
    if (!value) {
      this.unitRate = null;
    }
  }

  get fleetAssetHeaders(): TableHeader[] {
    return [
      {
        text: 'Percent (%)',
        align: 'left',
        sortable: false,
        value: 'fleetAssetPercentage',
      },
      {
        text: 'Fuel Surcharge',
        align: 'left',
        value: 'appliedFuelSurcharge',
        sortable: false,
      },
      {
        text: 'Demurrage',
        align: 'left',
        value: 'demurrageType',
        sortable: false,
      },
      {
        text: 'Demurrage Fuel Surcharge',
        align: 'left',
        value: 'demurrageFuelSurchargeApplies',
        sortable: false,
      },
    ];
  }

  public unitTypeChanged(selectedId: number): void {
    if (!this.unitRate) {
      return;
    }
    switch (selectedId) {
      case 1:
        this.unitRate.unitTypeName = '';
        break;
      case 2:
        this.unitRate.unitTypeName = 'Weight (kg)';
        break;
    }
  }

  get unitRates(): UnitRate[] {
    const unitRateItems = this.serviceRate.rateTableItems.filter(
      (x: RateTableItems) => x.rateTypeId === this.rateId,
    );
    if (unitRateItems.length === 0) {
      return [];
    }
    // Get all unit rates first
    const allUnitRates: UnitRate[] = [];
    unitRateItems.forEach((item) => {
      if (item.rateTypeObject) {
        allUnitRates.push(...(item.rateTypeObject as UnitRate[]));
      }
    });
    // Apply search filter if searchTerm is provided
    if (this.searchTerm && this.searchTerm.trim().length > 0) {
      const searchTerm = this.searchTerm.toLowerCase().trim();
      return allUnitRates.filter((unitRate) => {
        // Search in unit type name (first column)
        const unitTypeName = unitRate.unitTypeName?.toLowerCase() || '';
        // Search in zone name (second column)
        const zoneName = unitRate.zoneName?.toLowerCase() || '';
        return (
          unitTypeName.includes(searchTerm) || zoneName.includes(searchTerm)
        );
      });
    }
    return allUnitRates;
  }

  get unitRateItem(): RateTableItems | undefined {
    return this.serviceRate.rateTableItems.find(
      (item: RateTableItems) => item.rateTypeId === this.rateId,
    );
  }

  get graceTimeInMinutes(): number {
    if (!this.unitRate) {
      return 0;
    }
    return moment
      .duration(this.unitRate.demurrage.graceTimeInMilliseconds)
      .asMinutes();
  }

  set graceTimeInMinutes(value: number) {
    if (!this.unitRate) {
      return;
    }
    this.unitRate.demurrage.graceTimeInMilliseconds = moment
      .duration(value, 'minutes')
      .asMilliseconds();
  }

  public addRangeRate(): void {
    if (!this.unitRate) {
      return;
    }
    const lastRange =
      this.unitRate.unitRanges[this.unitRate.unitRanges.length - 1].endRange +
      1;

    this.unitRate.unitRanges.push(
      new UnitRangeRate(0.0, -1, lastRange, lastRange + 5),
    );
  }

  public deleteRangedRate(index: number): void {
    if (!this.unitRate) {
      return;
    }
    this.unitRate.unitRanges.splice(index, 1);
  }

  get zoneId(): number {
    if (!this.unitRates) {
      return 0;
    }
    const allZoneIds = this.unitRates.map((x: UnitRate) => x.zoneId);
    const maxNumber = Math.max(...allZoneIds);
    return maxNumber !== -Infinity && maxNumber !== Infinity
      ? maxNumber + 1
      : 1;
  }

  public validRangeAmount(value: number, index: number, isStartRange: boolean) {
    if (!this.unitRate) {
      return false;
    }
    const error = 'Value not in range';
    if (index === 0 && isStartRange) {
      if (isStartRange) {
        return (value > 0 && value <= 1) || error;
      } else {
        return (value > 1 && value <= 2) || error;
      }
    } else if (index === this.unitRate.unitRanges.length - 1 && !isStartRange) {
      return false;
    }

    const indexToCheck = isStartRange ? index - 1 : index + 1;

    const valueToCheckAgainst = isStartRange
      ? this.unitRate.unitRanges[indexToCheck].endRange
      : this.unitRate.unitRanges[indexToCheck].startRange;

    if (isStartRange) {
      if (
        value <= valueToCheckAgainst ||
        Math.abs(value - valueToCheckAgainst) > 1
      ) {
        return error;
      }
    } else {
      if (
        value >= valueToCheckAgainst ||
        Math.abs(value - valueToCheckAgainst) > 1
      ) {
        return error;
      }
    }

    return true;
  }

  public newUnitRate() {
    this.unitRate = new UnitRate();
    this.unitRate.zoneId = this.zoneId;
    this.editingIndex = null;
  }
  public cancelUnitRate() {
    this.unitRate = null;
    this.editingIndex = null;
    this.$refs.unitForm.resetValidation();
  }
  public addUnitRate() {
    if (!this.$refs.unitForm.validate()) {
      showNotification(FORM_VALIDATION_FAILED_MESSAGE);
      return;
    }
    if (!this.unitRates || !this.unitRate) {
      return;
    }

    const unitRateTableItems = this.serviceRate.rateTableItems.find(
      (x: RateTableItems) => x.rateTypeId === this.rateId,
    );

    if (!unitRateTableItems) {
      return;
    }
    const unitRates = unitRateTableItems.rateTypeObject as UnitRate[];

    if (this.isEdited && this.editingIndex !== null) {
      unitRates.splice(this.editingIndex, 1, this.unitRate);
    } else {
      unitRates.push(this.unitRate);
    }
    this.unitRate = null;
    this.editingIndex = null;
  }

  public deleteUnitRate() {
    if (this.editingIndex !== null) {
      this.unitRates.splice(this.editingIndex, 1);
    }
    this.cancelUnitRate();
  }

  public editUnitRate(unitRate: UnitRate, index: number) {
    if (!unitRate) {
      return;
    }
    const indexInMainList = this.unitRates.findIndex(
      (x: UnitRate) => x === unitRate,
    );
    this.unitRate = JSON.parse(JSON.stringify(this.unitRates[indexInMainList]));
    this.editingIndex = indexInMainList;
  }

  get validate(): Validation {
    return validationRules;
  }

  public appliedFuelSurchargeType(id: number, appliedToFlagFalls: boolean) {
    const appliedFuelSurcharge: ShortLongName | undefined =
      applicableFuelSurcharges.find((x: ShortLongName) => id === x.id);
    return (
      (appliedFuelSurcharge ? appliedFuelSurcharge.shortName : '-') +
      (appliedToFlagFalls ? ' (Flag Fall)' : '')
    );
  }

  public demurrageGraceTableDisplay(
    graceDurationInMilliseconds: number,
  ): string {
    const graceInMinutes = moment
      .duration(graceDurationInMilliseconds)
      .asMinutes();

    return !graceInMinutes
      ? 'None'
      : moment.duration(graceInMinutes, 'minutes').humanize();
  }

  public appliedDemurrageRateTableDisplay(id: number): string {
    const appliedDemurrage = applicableDemurrages.find(
      (x: ShortLongName) => x.id === id,
    );

    return appliedDemurrage ? appliedDemurrage.shortName : '-';
  }
}
