<v-layout row wrap>
  <v-flex md10>
    <v-alert :value="true" color="info">
      <span class="subheader">
        <v-icon size="24" class="pr-2">fas fa-dollar-sign</v-icon>
        Current Rates for: {{clientAssetName}}
      </span>
      <ul class="pt-1">
        <li>
          <strong>ACTIVE CUSTOM RATES:</strong>
          <span v-if="currentServiceRate">
            <span>
              <span> {{currentServiceRate.name}}</span>

              (valid {{returnCorrectTime(currentServiceRate.validFromDate)}} -
              {{returnCorrectTime(currentServiceRate.validToDate)}})</span
            >
            <span class="pl-2">
              <InformationTooltip :right="true" tooltipType="info">
                <v-layout slot="content" row wrap>
                  <v-flex md12>
                    <p class="mb-1">
                      Custom Rate Card
                      <strong>{{currentServiceRate.name}}</strong> is currently
                      ACTIVE.
                    </p>

                    <p class="mb-0" v-if="serviceRateType === 'CLIENT'">
                      If any services are not set in
                      {{currentServiceRate.name}}, these will revert to the {{
                      appliedDefaultConfig ? `configured Default` : `Division
                      Default` }} Rate Card.
                    </p>
                    <p class="mb-0" v-if="serviceRateType === 'FLEET_ASSET'">
                      If no active Service Rates are available, Job Booking and
                      Pricing will be unavailable.
                    </p>
                  </v-flex>
                </v-layout>
              </InformationTooltip>
            </span>
          </span>
          <span v-else>
            No Active Rates

            <InformationTooltip :bottom="true" tooltipType="info">
              <v-layout slot="content" row wrap>
                <v-flex md12>
                  {{ clientAssetName }} has no active CUSTOM RATE.
                  <span v-if="serviceRateType === 'CLIENT'">
                    The {{ appliedDefaultConfig ? `configured Default` :
                    `Division Default` }} Rate Card will be applied.
                  </span>
                </v-flex>
              </v-layout>
            </InformationTooltip>
          </span>
        </li>
        <li v-if="serviceRateType === 'CLIENT'">
          <strong>DEFAULT RATES:</strong>

          <span v-if="appliedDefaultConfig">
            <span> {{appliedDefaultConfig.name}} </span>
            (valid {{returnCorrectTime(appliedDefaultConfig.validFromDate)}} -
            {{returnCorrectTime(appliedDefaultConfig.validToDate)}})
            <span class="pl-2">
              <!-- If no CUSTOM RATES are available, current selected DEFAULT rates
                will be applied: -->
              <InformationTooltip :bottom="true" tooltipType="info">
                <v-layout slot="content" row wrap>
                  <v-flex md12>
                    If {{clientAssetName}}'s CUSTOM RATES are expired, or not
                    available for a given service, rates from Default Rate Table
                    '{{appliedDefaultConfig.name}}' will be applied.
                  </v-flex>
                </v-layout>
              </InformationTooltip>
            </span>
          </span>
          <span v-else>
            <span> Division Defaults Applied </span>

            <span class="pl-2">
              <!-- If no CUSTOM RATES are available, current selected DEFAULT rates
                will be applied: -->
              <InformationTooltip :bottom="true" tooltipType="info">
                <v-layout slot="content" row wrap>
                  <v-flex md12>
                    <p class="mb-1">
                      If CUSTOM RATES are expired, or not available for a given
                      service, rates from the current active
                      <strong> Division Default Rate Table </strong>
                      will be applied.
                    </p>
                    <br />
                    <p class="mb-0">
                      To apply a different Rate table instead of the Default:
                    </p>
                    <ol>
                      <li>Go to the Rate Configurations tab</li>
                      <li>
                        Follow the steps on-screen and select the desired
                        options
                      </li>
                      <li>
                        Choose which Division Rate Table you wish to use to
                        supplement {{clientAssetName}}'s Custom Rates in
                        Division Service Rates Tab.
                      </li>
                    </ol>
                  </v-flex>
                </v-layout>
              </InformationTooltip>
            </span>
          </span>
        </li>
        <li>
          <strong>FUEL SURCHARGE:</strong>
          <span v-if="currentFuelSurcharge && currentFuelSurcharge.id">
            <span> {{currentFuelSurcharge.rateBrackets[0].rate}}%</span>
            from {{returnCorrectTime(currentFuelSurcharge.validFromDate)}} to
            {{returnCorrectTime(currentFuelSurcharge.validToDate)}}
          </span>
          <span v-else> No Custom Fuel Surcharge </span>
          <span class="pl-2">
            <InformationTooltip :right="true" tooltipType="info">
              <v-layout slot="content" row wrap>
                <v-flex md12>
                  <p class="mb-1" v-if="serviceRateType === 'CLIENT'">
                    If {{clientAssetName}} has no active custom Fuel Surcharge,
                    {{clientDetails.expiredFuelSurchargeDefaultsToDivisionRate ?
                    `the active Division Fuel Surcharge will be applied` : `Job
                    Booking and Pricing will be unavailable`}}.
                  </p>
                  <p class="mb-1" v-if="serviceRateType === 'FLEET_ASSET'">
                    If {{clientAssetName}} has no active Fuel Surcharge, Job
                    Booking and Pricing will be unavailable.
                  </p>
                </v-flex>
              </v-layout>
            </InformationTooltip>
          </span>
        </li>
        <div v-if="serviceRateType === 'FLEET_ASSET'">
          <li v-if="fleetAssetDetails.usesStandardDivisionRates">
            <strong>Fleet Rates Are Set To:</strong>
            <span class="mb-0">
              Merge With Current Division Rate & Will Ignore This Default Rates
              Config
            </span>
          </li>
        </div>
        <div v-if="serviceRateType === 'CLIENT'">
          <li v-if="clientDetails.usesStandardDivisionRates">
            <strong>Client Rates Are Set To:</strong>
            <span class="mb-0">
              Merge With Current Division Rate & Will Ignore This Default Rates
              Config
            </span>
          </li>
        </div>
      </ul>
    </v-alert>
  </v-flex>
</v-layout>
