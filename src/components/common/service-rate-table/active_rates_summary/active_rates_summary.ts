import DateTimeInputs from '@/components/common/date-picker/date_time_inputs.vue';
import QuickSelectDateRange from '@/components/common/date-picker/quick_select_date_range.vue';
import ServiceRateDateRangeEdit from '@/components/common/service-rate-table/service-rate-date-range-edit/index.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import FormCard from '@/components/common/ui-elements/form_card.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import {
  returnFormattedDate,
  returnTimeNow,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import FleetAsset from '@/interface-models/FleetAsset/FleetAsset';
import { Validation } from '@/interface-models/Generic/Validation';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import FuelSurchargeRate from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeRate';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import ServiceRateCard from '@/interface-models/ServiceRates/ServiceRateCard';
import { useServiceRateStore } from '@/store/modules/ServiceRateStore';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  components: {
    FormCard,
    ServiceRateDateRangeEdit,
    QuickSelectDateRange,
    ConfirmationDialog,
    DateTimeInputs,
    InformationTooltip,
  },
})
export default class ActiveRatesSummary extends Vue implements IUserAuthority {
  @Prop({ default: RateEntityType.CLIENT })
  public serviceRateType: RateEntityType;
  @Prop({ default: null })
  public currentServiceRate: ServiceRateCard | null;
  @Prop({ default: null })
  public currentFuelSurcharge: FuelSurchargeRate | null;
  @Prop() public clientDetails: ClientDetails;
  @Prop() public fleetAssetDetails: FleetAsset;

  public allDefaultRates: ClientServiceRate[] = [];

  // Form validation rules
  get validate(): Validation {
    return validationRules;
  }

  // Pass in tableId and return the Service Rate Table name
  public returnTableNameFromTableId(tableId: number) {
    const table = this.allDefaultRates.find((r) => r.tableId === tableId);
    return table ? table.name : `${tableId}`;
  }
  // Pass in tableId and return the Outside Metro rate
  public returnOutsideMetroChargeFromTableId(tableId: number) {
    const table = this.allDefaultRates.find((r) => r.tableId === tableId);
    return table ? table.outsideMetroRate : 0;
  }

  get clientAssetName(): string {
    if (this.serviceRateType === RateEntityType.CLIENT && this.clientDetails) {
      return this.clientDetails.displayName;
    } else if (
      this.serviceRateType === RateEntityType.FLEET_ASSET &&
      this.fleetAssetDetails
    ) {
      return this.fleetAssetDetails.csrAssignedId;
    } else {
      return '';
    }
  }

  // get appliedServiceRate() {}

  get appliedDefaultConfig() {
    if (
      this.serviceRateType !== RateEntityType.CLIENT ||
      !this.clientDetails ||
      !this.allDefaultRates ||
      !this.allDefaultRates.length
    ) {
      return;
    }
    // If clientDetails.usesStandardDivisionRates is TRUE, then we are just
    // using the current division defaults
    if (!!this.clientDetails.usesStandardDivisionRates) {
      return;
    }
    const currentTime = returnTimeNow();
    // Find active config from clientDetails
    const foundConfig = this.clientDetails.defaultsConfiguration.find(
      (item) =>
        currentTime > item.validFromDate! && currentTime < item.validToDate!,
    );
    // If there's no active default config then return nothing
    if (!foundConfig) {
      return;
    }
    // Find the active config from client in the full default rates list
    return this.allDefaultRates.find(
      (rate: any) => rate.tableId === foundConfig.tableId,
    );
  }

  get dateRangeItems() {
    const dateRanges: any = [];
    for (const rates of this.allDefaultRates) {
      const dates = {
        id: rates.tableId,
        validFromDate: rates.validFromDate
          ? returnFormattedDate(rates.validFromDate, 'DD/MM/YYYY')
          : '-',
        validToDate: rates.validToDate
          ? returnFormattedDate(rates.validToDate, 'DD/MM/YYYY')
          : '-',
      };
      dateRanges.push(dates);
    }
    return dateRanges;
  }

  public returnCorrectTime(epoch: number) {
    return returnFormattedDate(epoch, 'DD/MM/YYYY');
  }

  public async mounted() {
    if (this.serviceRateType === RateEntityType.CLIENT) {
      const result =
        await useServiceRateStore().getAllDivisionDefaultServiceRates();
      if (result) {
        this.allDefaultRates = result;
      }
    }
  }

  public isAuthorised(): boolean {
    return hasAdminOrHeadOfficeRole();
  }
}
