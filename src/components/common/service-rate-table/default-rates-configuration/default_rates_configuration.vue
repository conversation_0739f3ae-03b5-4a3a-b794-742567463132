<template>
  <v-layout row wrap>
    <v-flex md8 v-if="formDisabled">
      <v-flex solo flat dense class="pa-2">
        <strong>
          <v-icon class="mr-1" size="16">fas info</v-icon>
          {{ entityDisplayName }} is currently configured to use the Standard
          Division rate card.
        </strong>
        <ol>
          <p>To update this customer's rate configuration:</p>
          <li>Go to the Rate Configurations tab</li>
          <li>Follow the steps on-screen and select the desired options</li>
          <li>
            If a Division Rate Card other than the standard (current) is
            required, return to this screen to select it.
          </li>
        </ol>
      </v-flex>
    </v-flex>
    <v-layout align-end>
      <v-flex md10 class="ml-4">
        <slot name="active-rates-summary"></slot>
      </v-flex>
      <v-flex md2>
        <v-layout justify-end>
          <v-btn
            @click="addOrEditConfig(null)"
            depressed
            color="blue"
            :disabled="!isAuthorised() || dialogController || formDisabled"
          >
            Apply Alternate Division Rates</v-btn
          >
        </v-layout>
      </v-flex>
    </v-layout>
    <v-flex md12>
      <v-data-table
        :headers="headers"
        :pagination.sync="pagination"
        :items="defaultsConfigurationList"
        hide-actions
        class="gd-dark-theme bordered mt-4"
        no-data-text="No history of adjusted Default Rates"
      >
        <template v-slot:items="data">
          <tr :class="{ activeSelectedRate: data.item.isActive }">
            <td>{{ returnTableNameFromTableId(data.item.tableId) }}</td>
            <td>
              {{ returnOutsideMetroChargeFromTableId(data.item.tableId) }}
            </td>
            <td>{{ returnCorrectTime(data.item.validFromDate) }}</td>
            <td>{{ returnCorrectTime(data.item.validToDate) }}</td>
            <td class="text-xs-right">
              <v-menu left>
                <template v-slot:activator="{ on: menu }">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on: tooltip }">
                      <v-btn
                        flat
                        icon
                        v-on="{ ...tooltip, ...menu }"
                        class="ma-0"
                      >
                        <v-icon size="16">fas fa-ellipsis-v </v-icon>
                      </v-btn>
                    </template>
                    <span>View Additional Actions</span>
                  </v-tooltip>
                </template>
                <v-list dense class="v-list-custom">
                  <v-list-tile @click="viewConfig(data.item.id)">
                    <v-list-tile-title> View </v-list-tile-title>
                  </v-list-tile>
                  <v-divider></v-divider>
                  <v-list-tile
                    :disabled="formDisabled"
                    @click="addOrEditConfig(data.item.id)"
                  >
                    <v-list-tile-title> Edit Details </v-list-tile-title>
                  </v-list-tile>
                </v-list>
              </v-menu>
            </td>
          </tr>
        </template>
      </v-data-table>
    </v-flex>

    <v-dialog
      v-model="dialogController"
      width="700px"
      class="ma-0"
      persistent
      no-click-animation
      content-class="v-dialog-custom"
    >
      <v-flex md12 v-if="newOrEditingDefaultConfig">
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight"
        >
          <span>{{ operationType }} Default Rates Configuration</span>
          <div
            class="app-theme__center-content--closebutton"
            @click="cancelNewConfig"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>
        <v-layout
          row
          wrap
          class="app-theme__center-content--body dialog-content"
        >
          <v-flex md12>
            <v-form ref="defaultsConfigForm">
              <v-layout row wrap class="body-scrollable--65" pa-3>
                <v-select
                  :items="allDefaultRatesSelectList"
                  item-value="value"
                  item-text="text"
                  :rules="[validate.required]"
                  v-model="newOrEditingDefaultConfig.tableId"
                  label="Division Default Rates"
                  :disabled="operationType === 'VIEW'"
                  persistent-hint
                  hint="When your selection is made you can apply new date ranges"
                  class="v-solo-custom"
                  outline
                >
                </v-select>
                <v-flex md12 class="mb-1">
                  <v-layout>
                    <v-flex offset-md6 md6>
                      <QuickSelectDateRange
                        :initialValue="
                          newOrEditingDefaultConfig.validFromDate ?? 0
                        "
                        :isDisabled="operationType === 'VIEW'"
                        @setQuickValidToDate="setQuickValidToDate"
                      />
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex md6 pr>
                  <DateTimeInputs
                    :epochTime.sync="newOrEditingDefaultConfig.validFromDate"
                    :enableValidation="true"
                    :type="DateTimeType.DATE_START_OF_DAY"
                    dateLabel="Valid From"
                    :hintTextType="HintTextType.FORMATTED_SELECTION"
                    :readOnly="operationType === 'VIEW'"
                    :isRequired="true"
                  ></DateTimeInputs>
                </v-flex>

                <v-flex md6 pl>
                  <DateTimeInputs
                    :epochTime.sync="newOrEditingDefaultConfig.validToDate"
                    :enableValidation="true"
                    :type="DateTimeType.DATE_END_OF_DAY"
                    dateLabel="Valid To"
                    :hintTextType="HintTextType.FORMATTED_SELECTION"
                    :readOnly="operationType === 'VIEW'"
                    :isRequired="true"
                  ></DateTimeInputs>
                </v-flex>

                <v-flex md12 v-if="datesValid">
                  <v-alert :value="!datesValid.value" type="error">
                    <span>{{ datesValid.message }}</span>
                  </v-alert>
                </v-flex>
              </v-layout>
            </v-form>
          </v-flex>
          <v-flex md12 v-if="operationType !== 'VIEW'">
            <v-divider></v-divider>
            <v-layout align-center>
              <v-btn flat color="red" @click="cancelNewConfig">Cancel</v-btn>
              <v-spacer></v-spacer>

              <v-btn
                depressed
                color="blue"
                class="v-btn-confirm-custom"
                :disabled="!datesValid || !datesValid.value"
                @click="saveDefaultsConfigItem"
                >Save</v-btn
              >
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-dialog>
  </v-layout>
</template>

<script setup lang="ts">
import DateTimeInputs, {
  DateTimeType,
  HintTextType,
} from '@/components/common/date-picker/date_time_inputs.vue';
import QuickSelectDateRange from '@/components/common/date-picker/quick_select_date_range.vue';
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import {
  returnEndOfDayFromEpoch,
  returnFormattedDate,
  returnTimeNow,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import FleetAsset from '@/interface-models/FleetAsset/FleetAsset';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { VPagination } from '@/interface-models/Generic/VPagination';
import { Validation } from '@/interface-models/Generic/Validation';
import { JobOperationType } from '@/interface-models/Jobs/JobOperationType';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import DefaultsConfiguration from '@/interface-models/ServiceRates/DefaultsConfiguration/DefaultsConfiguration';
import FleetAssetServiceRate from '@/interface-models/ServiceRates/FleetAssetOwner/FleetAssetServiceRate/FleetAssetServiceRate';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { computed, ComputedRef, onMounted, Ref, ref } from 'vue';

interface DefaultsConfigurationTableItem extends DefaultsConfiguration {
  id?: string;
  isActive: boolean;
}

const props = defineProps<{
  defaultServiceRate: ClientServiceRate | FleetAssetServiceRate | null;
  entityDetails: ClientDetails | FleetAsset;
  serviceRateType: RateEntityType;
  allDefaultRates: ClientServiceRate[] | FleetAssetServiceRate[] | null;
}>();

const clientDetailsStore = useClientDetailsStore();

const fleetAssetStore = useFleetAssetStore();

const newOrEditingDefaultConfig: Ref<DefaultsConfiguration | null> = ref(null);
const operationType: Ref<JobOperationType | null> = ref(null);

const editingExistingId: Ref<string> = ref('');

const pagination: VPagination = {
  rowsPerPage: 4,
  totalItems: 0,
  page: 1,
  descending: false,
  sortBy: 'name',
};
const headers: TableHeader[] = [
  {
    text: 'Name',
    align: 'left',
    sortable: false,
    value: 'name',
  },
  {
    text: 'Outside Metro Rate (%)',
    align: 'left',
    sortable: false,
    value: 'outsideMetroRate',
  },
  {
    text: 'Valid From',
    align: 'left',
    value: 'result',
    sortable: false,
  },
  {
    text: 'Valid To',
    align: 'left',
    sortable: false,
    value: '',
  },
  {
    text: 'Action',
    align: 'right',
    sortable: false,
    value: '',
  },
];

// computed to display name based on entity type name
const entityDisplayName = computed(() => {
  if (props.serviceRateType === RateEntityType.CLIENT) {
    return (props.entityDetails as ClientDetails).displayName;
  } else {
    return (props.entityDetails as FleetAsset).csrAssignedId;
  }
});

// Ref for the form
const defaultsConfigForm: Ref<any> = ref(null);

// Form validation
const formIsValid = computed(() => {
  if (!defaultsConfigForm.value) {
    return false;
  }
  return defaultsConfigForm.value.validate();
});

// Computed property for formDisabled
const formDisabled = computed(
  () => !!props.entityDetails.usesStandardDivisionRates,
);

const datesValid = computed(() => {
  // Check if the newOrEditingDefaultConfig is null or the operation type is VIEW return,
  if (
    !newOrEditingDefaultConfig.value ||
    operationType.value === JobOperationType.VIEW
  ) {
    return;
  }

  // Flags to track the validity of the "validFromDate" and "validToDate"
  let validFromChecker = true;
  let validToChecker = true;

  // If editing an existing item, exclude it from the list of configurations to check
  const configsToCheck = !editingExistingId.value
    ? defaultsConfigurationList.value
    : defaultsConfigurationList.value.filter(
        (d) => d.id !== editingExistingId.value, // Filter out the currently edited configuration
      );

  // Iterate through all configurations that need to be checked
  for (const config of configsToCheck) {
    // Check if both validFromDate and validToDate exist for both the new/editing config and the current config
    if (
      newOrEditingDefaultConfig.value.validFromDate &&
      newOrEditingDefaultConfig.value.validToDate &&
      config.validFromDate &&
      config.validToDate
    ) {
      // Validate the "validFromDate" does not overlap with an existing range
      if (
        newOrEditingDefaultConfig.value.validFromDate >= config.validFromDate &&
        newOrEditingDefaultConfig.value.validFromDate <= config.validToDate
      ) {
        validFromChecker = false; // Mark as invalid if overlap is found
      }
      // Validate the "validToDate" does not overlap with an existing range
      if (
        newOrEditingDefaultConfig.value.validToDate >= config.validFromDate &&
        newOrEditingDefaultConfig.value.validToDate <= config.validToDate
      ) {
        validToChecker = false; // Mark as invalid if overlap is found
      }
    }
  }

  // If both date ranges are valid (no overlap), return true
  if (validFromChecker && validToChecker) {
    return { value: true };
  } else {
    // If there's an overlap, return false with a message
    return {
      value: false,
      message:
        'One or more selected dates currently overlap previously saved date ranges',
    };
  }
});

// Controls dialog visibility, and resets local working variables on close
const dialogController = computed<boolean>({
  get: () => newOrEditingDefaultConfig.value !== null,
  set: (value: boolean) => {
    if (!value) {
      newOrEditingDefaultConfig.value = null;
    }
  },
});

// If id is provided then we are editing an existing DefaultsConfiguration. If
function addOrEditConfig(id?: string | null) {
  // not, then we are creating a new one
  if (!id) {
    operationType.value = JobOperationType.NEW;
    newOrEditingDefaultConfig.value = new DefaultsConfiguration();
  } else {
    // Find the item we're editing using the id
    const foundConfig = defaultsConfigurationList.value.find(
      (d) => d.id === id,
    );
    if (foundConfig) {
      // Copy to editing version and set editingExistingId to this id so we
      // know this is the one we're making changes to
      editingExistingId.value = foundConfig.id ? foundConfig.id : '';
      operationType.value = JobOperationType.EDIT;
      newOrEditingDefaultConfig.value = Object.assign(
        new DefaultsConfiguration(),
        foundConfig,
      );
    }
  }
}

// View the DefaultsConfiguration associated with the provided id in the
// add/edit dialog
function viewConfig(id: string) {
  if (id) {
    // Find the item we're editing using the id
    const foundConfig = defaultsConfigurationList.value.find(
      (d) => d.id === id,
    );
    if (foundConfig) {
      operationType.value = JobOperationType.VIEW;
      newOrEditingDefaultConfig.value = Object.assign(
        new DefaultsConfiguration(),
        foundConfig,
      );
    }
  }
}

// Form validation rules
const validate: ComputedRef<Validation> = computed(() => {
  return validationRules;
});

function cancelNewConfig() {
  operationType.value = null;
  editingExistingId.value = '';
  newOrEditingDefaultConfig.value = null;
}

const allDefaultRatesSelectList = computed(() => {
  return (
    props.allDefaultRates?.map((rate) => ({
      value: rate.tableId,
      text: `${rate.name} from ${returnFormattedDate(
        rate.validFromDate,
        'DD/MM/YYYY',
      )} to ${returnFormattedDate(rate.validToDate, 'DD/MM/YYYY')}`,
    })) ?? []
  );
});

// Pass in tableId and return the Service Rate Table name
function returnTableNameFromTableId(tableId: number) {
  const table = props.allDefaultRates?.find((r) => r.tableId === tableId);
  return table ? table.name : `${tableId}`;
}
// Pass in tableId and return the Outside Metro rate
function returnOutsideMetroChargeFromTableId(tableId: number) {
  const table = props.allDefaultRates?.find((r) => r.tableId === tableId);
  return table && table.outsideMetroRate
    ? DisplayCurrencyValue(table.outsideMetroRate)
    : '0.00';
}

// function for quick date selection
function setQuickValidToDate(epoch: number): void {
  newOrEditingDefaultConfig.value!.validToDate = returnEndOfDayFromEpoch(epoch);
}

function returnCorrectTime(epoch: number) {
  return returnFormattedDate(epoch, 'DD/MM/YYYY');
}

// function to save default rate config
function saveDefaultsConfigItem() {
  if (!newOrEditingDefaultConfig.value || !formIsValid.value) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }

  // Determine which list to update based on serviceRateType
  const isClient = props.serviceRateType === RateEntityType.CLIENT;
  const configList = isClient
    ? (props.entityDetails as ClientDetails).defaultsConfiguration ??
      ((props.entityDetails as ClientDetails).defaultsConfiguration = [])
    : (props.entityDetails as FleetAsset).divisionRateTableReferences ??
      ((props.entityDetails as FleetAsset).divisionRateTableReferences = []);

  if (!editingExistingId.value) {
    // If creating a new item, push to the appropriate list
    configList.push(newOrEditingDefaultConfig.value);
  } else {
    // If editing an existing item, find and replace it
    const foundIndex = configList.findIndex(
      (d) => returnIdFromConfig(d) === editingExistingId.value,
    );

    if (foundIndex !== -1) {
      configList.splice(foundIndex, 1, newOrEditingDefaultConfig.value);
    }
  }

  newOrEditingDefaultConfig.value = null;

  // Dispatch save request
  if (isClient) {
    clientDetailsStore.saveClientDetails(props.entityDetails as ClientDetails);
  } else {
    fleetAssetStore.saveFleetAsset(props.entityDetails as FleetAsset);
  }
}

// Construct a string id using the values from config
function returnIdFromConfig(config: DefaultsConfiguration): string {
  return `${config.tableId}-${config.validFromDate}-${config.validToDate}`;
}

// Return the defaultsConfiguration list from entityDetails with a new
// property for id such that we have a unique identifier for each element
// (because there is not id property on the model)
const defaultsConfigurationList = computed<DefaultsConfigurationTableItem[]>(
  () => {
    const currentTime = returnTimeNow();

    // Function to check if a configuration is active
    const isActive = (d: any) =>
      !props.entityDetails.usesStandardDivisionRates &&
      d.validFromDate &&
      d.validToDate &&
      currentTime >= d.validFromDate &&
      currentTime <= d.validToDate;

    // Determine the appropriate configuration list
    const configurationList =
      props.serviceRateType === RateEntityType.CLIENT
        ? (props.entityDetails as ClientDetails)?.defaultsConfiguration
        : (props.entityDetails as FleetAsset)?.divisionRateTableReferences;

    return configurationList
      ? configurationList.map((d) => ({
          id: returnIdFromConfig(d),
          isActive: isActive(d),
          ...d,
        }))
      : [];
  },
);

// Set local variables
function setComponentData() {
  // Determine the correct configuration list based on type
  const configurationList =
    props.serviceRateType === RateEntityType.CLIENT
      ? (props.entityDetails as ClientDetails)?.defaultsConfiguration
      : (props.entityDetails as FleetAsset)?.divisionRateTableReferences;

  // Ensure pagination doesn't break if the property is missing
  pagination.totalItems = configurationList?.length ?? 0;
}

// set data on mounted
onMounted(() => {
  setComponentData();
});

// check has admin or head-office role
function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}
</script>

<style scoped lang="scss">
.service-rates-config-view {
  border-right: 3px solid #838383;
  border-left: 3px solid #838383;
  border-bottom: 3px solid #838383;
}

.maintenance-title {
  font-weight: 500;
  font-size: 1.3em;
}

.activeSelectedRate {
  border-left: 2px solid $success-type;
  background-color: $a !important;
}

ol {
  display: block;
  color: var(--text-color);
  margin-left: 30px;

  p {
    margin-top: 12px;
    margin-bottom: 12px;
  }
  li {
    margin-top: 6px;
    margin-bottom: 6px;
    margin-left: 20px;
  }
}
</style>
