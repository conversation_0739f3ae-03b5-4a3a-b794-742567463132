import UnassignedPudItemDialog from '@/components/operations/BookJob/unassigned_pud_item_dialog/index.vue';
import {
  addUnassignedPudJobReference,
  mergePudItemDetailsWithUnassigned,
} from '@/helpers/JobDataHelpers/JobDataHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { initialiseUnassignedPudListFromResponse } from '@/helpers/UnassignedPudItemHelpers/UnassignedPudItemHelpers';
import AddAttachmentToJob from '@/interface-models/Generic/Attachment/AddAttachmentToJob';
import { AddNoteToJobRequest } from '@/interface-models/Generic/Communication/AddNoteToJobRequest';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { JobEventSummary } from '@/interface-models/Jobs/JobEventSummary';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import UnassignedPudItemResponse from '@/interface-models/Jobs/PUD/UnassignedPudItem/SaveUnassignedPudItemResponse';
import UnassignedPudItem from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudItem';
import { UnassignedPudItemRequest } from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudItemRequest';
import { UnassignedPudItemStatus } from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudItemStatus';
import UnassignedPudJobDetailsRequest, {
  UnassignedPudJobDetailsResponse,
} from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudJobDetailsRequest';
import { PudStatusUpdate } from '@/interface-models/Status/PudStatusUpdate';
import { useDataImportStore } from '@/store/modules/DataImportStore';
import Mitt from '@/utils/mitt';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  components: { UnassignedPudItemDialog },
})
export default class UnassignedPudLinkingMaintenance extends Vue {
  @Prop({ default: null }) public staticJobDetails: JobDetails | null;

  public dataImportStore = useDataImportStore();

  public jobDetails: JobDetails | null = null;
  public requestingUnassignedPudListForClient: boolean = false;
  public unassignedPudListForClient: UnassignedPudItem[] | null = null;
  public awaitingSaveResponse: boolean = false;
  public selectedPudIdToLink: string | null = null;
  public showUnassignedPudDialog: boolean = false;
  public currentUnassignedPudIds: string[] = [];

  public $refs!: {
    upiDialogComponent: HTMLElement;
  };

  // Request a list of all Unassigned Pud Items for the selected client
  public async requestUnassignedPudListForClient() {
    if (!this.jobDetails) {
      this.$emit('closeUnassignedPudLinkingMaintenance');
      return;
    }
    this.requestingUnassignedPudListForClient = true;
    const request: UnassignedPudItemRequest = {
      createdStartEpoch: null,
      createdEndEpoch: null,
      pudStartEpoch: null,
      pudEndEpoch: null,
      clientId: this.jobDetails.client.id,
      assignedStatus: [
        UnassignedPudItemStatus.UNASSIGNED,
        UnassignedPudItemStatus.ASSIGNED,
      ],
      groupReferenceId: null,
      clientSuppliedId: null,
      jobId: null,
    };
    // Make request and handle response
    this.handleUnassignedPudItemListResponse(
      await this.dataImportStore.requestUnassignedPudItemList(request),
    );
  }

  /**
   * Handles the response from requestUnassignedPudListForClient. If there are
   * any referenced unassigned puds on the job that are completed we will make a
   * subsequent request for those.
   * @param response - the response from requestUnassignedPudListForClient
   * @returns
   */
  public async handleUnassignedPudItemListResponse(
    response: UnassignedPudItemResponse | null,
  ): Promise<void> {
    if (!response || !response.unassignedPudItemList) {
      return;
    }
    this.unassignedPudListForClient = response.unassignedPudItemList;

    // get a list of all unassigned puds that are required but not yet loaded in the client.
    const missingUnassignedPud_ids: string[] = [];
    for (const unassignedPud_id of this.currentUnassignedPudIds) {
      const unassignedPudItemExistsLocally =
        this.unassignedPudListForClient.find(
          (x: UnassignedPudItem) => x.id === unassignedPud_id,
        );

      if (!unassignedPudItemExistsLocally) {
        missingUnassignedPud_ids.push(unassignedPud_id);
      }
    }

    // If there are no unassigned puds that are missing we can show the dialog
    if (missingUnassignedPud_ids.length === 0) {
      this.unassignedPudListForClient = initialiseUnassignedPudListFromResponse(
        response.unassignedPudItemList,
      );
      this.requestingUnassignedPudListForClient = false;
      this.showUnassignedPudDialog = true;
      return;
    }

    // request the unassigned puds that we do not yet have in local state.
    this.handleRemainingUnassignedPudItemListResponse(
      await this.dataImportStore.getUnassignedPudsBy_idList(
        missingUnassignedPud_ids,
      ),
    );
  }

  /**
   * Response handler for getUnassignedPudsBy_idList(missingUnassignedPud_ids)
   * containing a list of unassigned puds. We will add these unassigned puds to
   * our current list (unassignedPudListForClient)
   * @param unassignedPudItems - the response from getUnassignedPudsBy_idList
   */
  public handleRemainingUnassignedPudItemListResponse(
    unassignedPudItems: UnassignedPudItem[] | null,
  ) {
    unassignedPudItems ??= [];
    if (!this.unassignedPudListForClient) {
      return;
    }
    this.unassignedPudListForClient =
      this.unassignedPudListForClient.concat(unassignedPudItems);
    this.unassignedPudListForClient = initialiseUnassignedPudListFromResponse(
      this.unassignedPudListForClient,
    );
    this.requestingUnassignedPudListForClient = false;
    this.showUnassignedPudDialog = true;
  }

  /**
   * Handles the response for this components save request. One thing to note is
   * that all division responses we be passed into this method so it is
   * important to make sure the response has no conflicts with the users edited
   * state.
   * @param response
   * @returns
   */

  public setIncomingSaveResponse(
    response: UnassignedPudJobDetailsResponse | null,
  ) {
    // If there was an error with the response that is not related to this users request or job details we will ignore it.
    if (
      !this.jobDetails ||
      !response ||
      response?.jobDetails?.jobId !== this.jobDetails.jobId
    ) {
      return;
    }

    // If the user was not waiting for a response and there was an update to this job we will close the leg matching dialog.
    if (
      !this.awaitingSaveResponse &&
      response.jobDetails.jobId === this.jobDetails.jobId
    ) {
      this.showConflictAlertAndClose();
      return;
    }

    // No potential issues where found and the user is not waiting for a response. Safe to return
    if (!this.awaitingSaveResponse) {
      return;
    }

    const notificationMessage: string =
      response !== null
        ? 'Matching/Unmatching Successful.'
        : GENERIC_ERROR_MESSAGE;

    const notificationHealth: HealthLevel =
      response !== null ? HealthLevel.INFO : HealthLevel.ERROR;
    showNotification(notificationMessage, {
      type: notificationHealth,
      title: 'Leg Matching',
    });
    this.selectedPudIdToLink = null;
    this.currentUnassignedPudIds = [];
    this.closeUnassignedPudLinkingMaintenance();
  }

  // shows alert to user about update conflicts and closes this dialog.
  public showConflictAlertAndClose() {
    showNotification(
      'This job was recently updated. No updates are allowed at this time.',
      {
        type: HealthLevel.ERROR,
        title: 'Leg Matching',
      },
    );
    this.closeUnassignedPudLinkingMaintenance();
  }

  public closeUnassignedPudLinkingMaintenance() {
    this.showUnassignedPudDialog = false;
    this.$emit('closeUnassignedPudLinkingMaintenance');
  }

  public async saveJobDetails() {
    if (!this.jobDetails) {
      return;
    }
    // List of all current PudItems with UnassignedPudIds
    // With this we need to discern which are new as of this set of edits, and which
    // may have been removed
    const allUnassignedPudIds: string[] =
      this.returnCurrentAssociatedUnassignedPudIds(this.jobDetails.pudItems);

    const currentUnassignedPudIds = this.currentUnassignedPudIds;

    // Find any newly added ids
    // Check which ids exist right in allUnassignedPudIds that did not exist at time the start of the edit
    const newIds = allUnassignedPudIds.filter(
      (id) => !currentUnassignedPudIds.includes(id),
    );
    // Find any removed ids
    // Check for ids that existed at the start of the edit, but are no longer in allUnassignedPudIds
    const removedIds = currentUnassignedPudIds.filter(
      (id) => !allUnassignedPudIds.includes(id),
    );
    // If there are no new or removed unassignedPudIds, save job as usual
    if (newIds.length === 0 && removedIds.length === 0) {
      showNotification('Save not required as no edits were made.', {
        type: HealthLevel.INFO,
        title: 'Leg Matching',
      });

      this.closeUnassignedPudLinkingMaintenance();

      return;
    } else {
      // If there is AT LEAST 1 new OR removed unassigned pudIds,
      // We should dispatch request which updates both
      this.awaitingSaveResponse = true;
      // Send request and handle response
      const result = await this.dataImportStore.saveUnassignedPudJobDetails(
        this.jobDetails,
        newIds,
        removedIds,
      );
      this.setIncomingSaveResponse(result);
    }
  }

  // Find the PUD Items that currently have the unassignedPudItem reference property set
  // We will use this to compare the list before and after changes have been made to decide
  // what request needs to be dispatched to update the UnassignedPudItems
  public returnCurrentAssociatedUnassignedPudIds(
    pudItems: PUDItem[],
  ): string[] {
    const currentUnassignedPudIds = pudItems
      .filter(
        (p) =>
          p.unassignedPudItemReference !== null &&
          p.unassignedPudItemReference !== undefined &&
          p.unassignedPudItemReference.length > 0,
      )
      .flatMap((p) =>
        p.unassignedPudItemReference ? p.unassignedPudItemReference : [],
      );

    return currentUnassignedPudIds;
  }

  // Capture emit from Unassigned Pud Dialog which contains a list of UPI ids
  public linkMultipleUnassignedPudFromId(payload: {
    pudId: string;
    unassignedPudIds: string[];
  }) {
    if (this.selectedPudIdToLink) {
      this.unassignPud(this.selectedPudIdToLink);
    }
    payload.unassignedPudIds.forEach((id) => {
      this.linkSelectedUnassignedPudItem(id);
    });
    this.selectedPudIdToLink = null;
    (this.$refs.upiDialogComponent as any).setUpiSelectionList();
  }

  // Find the matching UnassignedPudItem from unassignedPudListForClient
  // Convert to pud item, then set the values for Upi reference and reference to currentPudItem
  public linkSelectedUnassignedPudItem(id: string) {
    if (!this.unassignedPudListForClient || !this.jobDetails) {
      return;
    }
    const foundUpi: UnassignedPudItem | undefined =
      this.unassignedPudListForClient.find((p) => p.id === id);

    const foundPudOnJob: PUDItem | undefined = this.jobDetails.pudItems.find(
      (x: PUDItem) => x.pudId === this.selectedPudIdToLink,
    );
    if (!foundUpi || !foundPudOnJob) {
      return;
    }

    const currentPud = foundPudOnJob;
    const unassignedPudItem = foundUpi.asPudItem();

    mergePudItemDetailsWithUnassigned(currentPud, unassignedPudItem);

    // we should now add our groupId (client supplied reference on the unassigned pud) as a job reference.
    addUnassignedPudJobReference(
      foundUpi.groupReferenceId,
      this.jobDetails.jobReference,
    );
  }

  // This method removes an unassignedPud that is currently assigned to a pud.
  // It is actioned via a child emit
  public unassignPud(pudId: string) {
    if (!this.jobDetails) {
      return;
    }
    const foundPud = this.jobDetails.pudItems.find(
      (x: PUDItem) => x.pudId === pudId,
    );
    if (!foundPud) {
      return;
    }
    foundPud.unassignedPudItemReference = [];
  }

  get selectedPudDetails(): PUDItem | null {
    if (!this.jobDetails) {
      return null;
    }
    const foundSelectedPud: PUDItem | undefined = this.jobDetails.pudItems.find(
      (x: PUDItem) => x.pudId === this.selectedPudIdToLink,
    );

    return foundSelectedPud ? foundSelectedPud : null;
  }

  /**
   * Callback for mitt listener for various job related events. If the job details
   * are the same as the job details we are currently working with we will show a
   * conflict alert and close the dialog.
   * @param payload - the payload from the mitt event
   */
  private handleApiUpdate(
    payload:
      | JobEventSummary
      | AddAttachmentToJob
      | AddNoteToJobRequest
      | PudStatusUpdate
      | null,
  ) {
    if (this.jobDetails?.jobId && payload?.jobId === this.jobDetails.jobId) {
      this.showConflictAlertAndClose();
    }
  }
  /** Set the mitt listeners on mount */
  private setMittListeners() {
    Mitt.on('jobStatusUpdate', this.handleApiUpdate);
    Mitt.on('updatedStatusJobDetails', this.handleApiUpdate);
    Mitt.on('updateJobEventListResponse', this.handleApiUpdate);
    Mitt.on('updatedJobWithAttachment', this.handleApiUpdate);
    Mitt.on('addedNoteToJob', this.handleApiUpdate);
    Mitt.on('pudStatusUpdate', this.handleApiUpdate);
  }
  /** Turn off the mitt listeners on unmount */
  private unsetMittListeners() {
    Mitt.off('jobStatusUpdate', this.handleApiUpdate);
    Mitt.off('updatedStatusJobDetails', this.handleApiUpdate);
    Mitt.off('updateJobEventListResponse', this.handleApiUpdate);
    Mitt.off('updatedJobWithAttachment', this.handleApiUpdate);
    Mitt.off('addedNoteToJob', this.handleApiUpdate);
    Mitt.off('pudStatusUpdate', this.handleApiUpdate);
  }

  public mounted() {
    // Set the local jobDetails variable with our static job details prop. This is so we are working with a clean job and don't have to emit events back.
    this.jobDetails = JSON.parse(JSON.stringify(this.staticJobDetails));
    if (!this.jobDetails) {
      return;
    }
    this.currentUnassignedPudIds = this.returnCurrentAssociatedUnassignedPudIds(
      this.jobDetails.pudItems,
    );
    // request our clients unassigned puds
    this.requestUnassignedPudListForClient();
    this.setMittListeners();
  }

  public beforeDestroy() {
    this.unsetMittListeners();
  }
}
