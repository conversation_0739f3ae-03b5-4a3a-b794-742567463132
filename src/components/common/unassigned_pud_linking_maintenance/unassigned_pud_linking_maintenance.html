<v-layout>
  <UnassignedPudItemDialog
    ref="upiDialogComponent"
    v-if="showUnassignedPudDialog && unassignedPudListForClient"
    :showUnassignedPudDialog="showUnassignedPudDialog"
    :clientId="jobDetails.client.id"
    :unassignedPudItemList="unassignedPudListForClient"
    :currentStagedPudItemIds="[]"
    :itemsForQuickAdd="[]"
    @closeDialog="closeUnassignedPudLinkingMaintenance"
    :isMatching="true"
    :pudItems="jobDetails.pudItems"
    @linkSelectedPointList="linkMultipleUnassignedPudFromId"
    :selectedPudIdToLink.sync="selectedPudIdToLink"
    @saveJobDetails="saveJobDetails"
    :pudDetails="selectedPudDetails"
    :jobReferences="!jobDetails ? [] : jobDetails.jobReference"
    :jobDetails="jobDetails"
    @unassignPud="unassignPud"
  >
  </UnassignedPudItemDialog>
</v-layout>
