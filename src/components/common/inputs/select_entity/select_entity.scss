.input-label {
  display: block;
  font-size: $font-size-12;
  transition: 0.2s;
  pointer-events: none;
  color: var(--light-text-color);
  padding: 0 12px;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.autocomplete-input {
  color: var(--text-color);
  height: 48px;
  width: 100%;
  font-size: $font-size-16;
  border: 1px solid $border-color;
  padding: 8px 13px 8px 12px;
  border-radius: $border-radius-sm;
  margin: 0 0 8px 0px;
  background-color: var(--background-color-300);
  &:focus {
    outline: none !important;
    border: 1px solid var(--primary);
  }
  &::placeholder {
    color: var(--light-text-color);
  }
  &:disabled {
    background-color: var(--background-color-550) !important;
    border: none;
  }
  &.hide-details {
    margin-bottom: 0px;
  }
}

.floating-placeholder {
  position: absolute;
  left: 13px;
  bottom: 19px;
  &.hide-details {
    bottom: 11px;
  }
  font-size: $font-size-16;
  color: var(--light-text-color);
}

.suggestions-menu {
  position: absolute;
  top: 48px;
  z-index: 5;
  list-style: none;
  width: fit-content;
  block-size: fit-content;
  min-width: 100%;
  // display: inline-table;
  background: var(--background-color-300);
  overflow: hidden;
  overflow-y: scroll;
  max-height: 294px;
  padding: 8px 0;
  box-shadow:
    0 3px 1px -2px rgba(0, 0, 0, 0.2),
    0 2px 2px 0 rgba(0, 0, 0, 0.14),
    0 1px 5px 0 rgba(0, 0, 0, 0.12);
  li {
    color: var(--text-color);
    height: 42px;
    display: flex;
    align-items: center;
    font-size: $font-size-15;
    padding-left: 12px;
    padding-right: 12px;
    white-space: nowrap;
    span {
      cursor: pointer;
    }
  }
  .select-item {
    cursor: pointer;
    &:hover {
      background-color: $translucent;
    }
  }
}

.checkbox-input {
  margin-left: 12px;
  margin-right: 12px;
  min-height: 22px;
  min-width: 30px;
  accent-color: var(--primary-light);
  pointer-events: none;
}

.action-container {
  position: absolute;
  right: 1px;
  top: 1px;
  height: 46px;
  display: flex;
  align-items: center;
  padding-right: 12px;
  border-radius: $border-radius-sm;
  margin: 0 0 8px 0px;
  background-color: var(--background-color-300);
  &.disabled {
    background: transparent;
    .icon-container {
      display: none;
      pointer-events: none;
    }
  }
  .icon-container {
    cursor: pointer;
    height: 100%;
    width: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    &:hover {
      i {
        color: var(--primary);
      }
    }
    .v-icon {
      color: var(--text-color);
    }
  }
}

.rotate {
  transform: rotate(180deg);
  color: var(--highlight) !important;
}

.highlighted {
  background-color: $translucent-highlight;
}

.chip-item {
  height: 30px;
  border-radius: $border-radius-Xlg;
  border: 1px solid $border-color;
  padding: 0 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
