import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import CashSalesClientSelectItem from '@/interface-models/Client/ClientDetails/CashSalesClientSelectItem';
import ClientSearchSummary from '@/interface-models/Client/ClientDetails/ClientSearchSummary';
import { PrimaryDriverIdentifier } from '@/interface-models/Company/DivisionCustomConfig/Operations/PrimaryDriverIdentifier';
import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useJobStore } from '@/store/modules/JobStore';
import Fuse, { FuseResult } from 'fuse.js';
import moment from 'moment-timezone';
import { v4 as uuidv4 } from 'uuid';
import { Component, Inject, Prop, Vue, Watch } from 'vue-property-decorator';

interface EntitySearch {
  additionalQuery: string;
  id: string;
  name: string;
  nameSuffix?: string;
  metaInfo: string;
  statusList: number[];
  isSelected: boolean;
  color: string;
  type: EntityType;
  icon: string;
  createdDate: number | null;
  score?: number;
}
// COMPONENT NOTE: To utilise vuetify validation within this none vuetify component, we require a partial implementation of the Validatable interface. We also require this component to be registered in the vuetify form component via @inject. The current implementation has this component registered against the 'form' injection within the mounted lifecycle. At the moment this component utilises the minimum requirement: hasError and validate.
// basic Validatable reference below:
// interface Validatable {
//   shouldValidate: boolean;
//   hasError: boolean;
//   validate(force?: boolean, value?: any): boolean;
//   reset(): void;
//   resetValidation(): void;
// }
@Component({
  components: {},
})
export default class SelectEntity extends Vue {
  // This component needs to be registered to injected 'form' for validation to work.
  @Inject('form') public form!: any;
  @Prop() public entityTypes: EntityType[];
  @Prop() public id: string;
  @Prop() public ids: string[];
  @Prop({ default: false }) public isRouteSelect: any[];
  // Whether retired entities should be included in search results
  @Prop({ default: true }) public retired: boolean;
  // Whether do not use entities should be included in search results
  @Prop({ default: true }) public doNotUse: boolean;
  // Whether the input is multiselect
  @Prop({ default: false }) public multiple: boolean;
  @Prop({ default: '' }) public hint: string;
  @Prop({ default: false }) public disabled: boolean;
  @Prop({ default: () => [] }) public rules: any[];
  // Custom placeholder text
  @Prop({ default: '' }) public placeholder: string;
  // The selected id/ids will be emitted back to the parent via 'selected' emit event if emitSelected is true
  @Prop({ default: false }) public emitSelected: boolean;
  // requiredMarker is way to manually not show the red asterisk on the input event if the rule required exists.
  @Prop({ default: true }) public requiredMarker: boolean;
  // affiliationTypes is utilised to filter for specific affiliation types when the entityType is SUBCONTRACTOR
  @Prop({ default: () => [] }) public affiliationTypes: string[];
  // whether cash sale client should be a selectable client when entityType is CLIENT
  @Prop({ default: false }) public cashSaleClient: boolean;
  // Whether the modeled value is the documents mongoId
  @Prop({ default: false }) public isMongoId: boolean;
  // Whether we should hide the hint text that appears beneath the autocomplete
  @Prop({ default: false }) public hideDetails: boolean;
  // If this is true, we will use the current jobs list to provide suggestions
  // when the user has not entered any search text. If false we'll just show the
  // first 15 results from the default list.
  @Prop({ default: true }) public suggestFromCurrentJobs: boolean;
  public containerId: string = '';
  public inputId: string = '';
  public chipId: string = '';
  // The searched text entered by the user. v-modeled to the input.
  public searchedText: string = '';
  // Boolean to handle whether the suggestions list elements are displayed.
  public isOpen: boolean = false;
  // contains the complete search list for the entity type.
  public searchList: EntitySearch[] = [];
  // contains the selected items and the suggested items based on the searchedText
  public selectedAndSuggestions: EntitySearch[] = [];
  // Keeps track of the selected item within the suggestion list. Actioned via the up and down keypress events.
  public currentHighlightedSelectionIndex: number = 0;
  // The text that will be shown in the input chip
  public chipText: string = '';
  // contains a list of entity names that are hidden in the chip tooltip
  public hiddenSelections: string[] = [];
  public hasError: boolean = false;
  public errorMessage: string = '';

  public fleetAssetStore = useFleetAssetStore();
  public clientDetailsStore = useClientDetailsStore();
  public driverDetailsStore = useDriverDetailsStore();
  public fleetAssetOwnerStore = useFleetAssetOwnerStore();

  public mounted() {
    this.setContainerAndInputId();
    this.$nextTick(() => {
      this.searchList = this.getSearchList(this.entityTypes);
      this.setSelectedAndSuggestions(this.selectedIds);
      this.searchedText = this.getSelectedTextToDisplay();
      this.handleInputEventListener(true);
      this.handleValidationRegistration(true);
    });
  }

  // Because we have event listeners on elements, and this component can be rendered multiple times within the user's view, we require dynamic IDs for each instance.
  public setContainerAndInputId(): void {
    this.containerId = uuidv4().replace(/-/g, '');
    this.inputId = uuidv4().replace(/-/g, '');
    this.chipId = uuidv4().replace(/-/g, '');
  }

  @Watch('id')
  public idUpdated() {
    this.searchedText = this.getSelectedTextToDisplay();
  }

  @Watch('ids')
  public idsUpdated() {
    if (!this.isOpen) {
      this.searchedText = this.getSelectedTextToDisplay();
    }
  }

  // The ids that are modeled to the id/ids props. paired with the setter below that emits changes back to parent.
  get selectedIds(): string[] {
    if (!this.multiple) {
      return this.id ? [this.id] : [];
    } else {
      return this.ids ? this.ids : [];
    }
  }

  set selectedIds(selectedIds: string[]) {
    if (!this.multiple) {
      this.$emit('update:id', selectedIds[0]);
      if (this.emitSelected) {
        this.$emit('selected', selectedIds[0]);
      }
      // Because we only have one to select we will close the list elements
      this.$nextTick(() => {
        this.setIsOpen(false);
      });
    } else {
      this.$emit('update:ids', selectedIds);
      if (this.emitSelected) {
        this.$emit('selected', selectedIds);
      }
      // We must set focus back onto the input as the selection from the list element removed the focus. We do this so that when the user clicks away from the input the focusout event will be actioned.
      this.focusInput();
    }
    // set the selected and suggestions based off of the selected ids.
    this.setSelectedAndSuggestions(selectedIds);
  }

  // Returns the label name. If placeholder prop is provided this will be used otherwise the label will be based on the entity type.
  get labelName(): string {
    if (this.placeholder) {
      return this.placeholder;
    }
    let labelName = 'Search';
    for (let i = 0; i < this.entityTypes.length; i++) {
      if (i > 0) {
        labelName += i === this.entityTypes.length - 1 ? ' & ' : ',';
      }
      if (this.entityTypes[i] === EntityType.CLIENT) {
        labelName += ' Clients';
      } else if (this.entityTypes[i] === EntityType.FLEET_ASSET_OWNER) {
        labelName += ' Subcontractors';
      } else if (this.entityTypes[i] === EntityType.DRIVER) {
        labelName += ' Drivers';
      } else if (this.entityTypes[i] === EntityType.FLEET_ASSET) {
        labelName += ' Vehicles';
      }
    }

    return labelName;
  }

  // When up key is pressed while suggestions are open
  public moveUpList() {
    if (
      this.currentHighlightedSelectionIndex - 1 >
      this.selectedAndSuggestions.length - 1
    ) {
      this.currentHighlightedSelectionIndex =
        this.selectedAndSuggestions.length - 1;
    } else if (this.currentHighlightedSelectionIndex > 0) {
      this.currentHighlightedSelectionIndex--;
    }
  }

  // When down key is pressed while suggestions are open
  public moveDownList(): void {
    if (!this.isOpen) {
      this.currentHighlightedSelectionIndex = 0;
      this.setIsOpen(true);
      return;
    }
    if (
      this.currentHighlightedSelectionIndex <
      this.selectedAndSuggestions.length - 1
    ) {
      this.currentHighlightedSelectionIndex++;
    }
  }

  // returns boolean on whether the required rules are contained in the rules prop. Utilised to show the red astrix on the input placeholder
  get isRequired(): boolean {
    return (
      this.requiredMarker &&
      (this.rules.includes(validationRules.required) ||
        this.rules.includes(validationRules.listRequired))
    );
  }

  // When the user changes input
  public inputUpdated(event: any): void {
    if (event.data !== undefined) {
      if (!this.isOpen) {
        // this.isOpen = true;
        this.setIsOpen(true);
      }
      this.hasError = false;
      this.setSelectedAndSuggestions(this.selectedIds);
    }
  }

  // sets the selected item in the suggestions list.
  public setSelectedItem(item: EntitySearch) {
    // If the component has isRouteSelect set to true we will push the user to the selected entities route.
    if (this.isRouteSelect) {
      this.goToRoute(item);
      this.setIsOpen(false);
      return;
    }
    if (!this.multiple) {
      if (!this.isOpen) {
        this.setIsOpen(true);
        return;
      }
      this.selectedIds = [item.id];
    } else {
      const selectedIds: string[] = JSON.parse(
        JSON.stringify(this.selectedIds),
      );
      const selectedItemIndex = this.selectedIds.findIndex(
        (x: string) => x === item.id,
      );
      if (selectedItemIndex >= 0) {
        selectedIds.splice(selectedItemIndex, 1);
      } else {
        selectedIds.push(item.id);
      }
      this.selectedIds = selectedIds;
    }
  }

  // Push the user to the entities route.
  public goToRoute(item: EntitySearch) {
    if (item.type === EntityType.CLIENT) {
      this.clientDetailsStore.setSelectedClientId(item.id);
      this.$router.push({
        name: 'Client Details',
        params: {
          name: item.name.toLowerCase().replace(/ /g, '-'),
          id: item.id,
        },
      });
    } else if (item.type === EntityType.FLEET_ASSET_OWNER) {
      this.fleetAssetOwnerStore.setSelectedFleetAssetOwnerId(item.id);
      this.$router.push({
        name: 'Owner',
        params: {
          name: item.name.toLowerCase().replace(/ /g, '-'),
          id: item.id,
        },
      });
    } else if (item.type === EntityType.DRIVER) {
      this.driverDetailsStore.setSelectedDriverDetailsId(item.id);
      this.$router.push({
        name: 'Driver',
        params: {
          name: item.name.toLowerCase().replace(/ /g, '-'),
          id: item.id,
        },
      });
    } else if (item.type === EntityType.FLEET_ASSET) {
      const fleetAsset: FleetAssetSummary | undefined =
        this.fleetAssetStore.fleetAssetSummaryMap.get(item.id);
      if (!fleetAsset) {
        return;
      }
      const isTruck: boolean = fleetAsset.fleetAssetTypeId === 1;
      if (isTruck) {
        this.fleetAssetStore.setSelectedFleetAssetId(item.id);
        this.$router.push({
          name: 'Truck',
          params: {
            name: item.name.toLowerCase().replace(/ /g, '-'),
            id: item.id,
          },
        });
      } else {
        this.fleetAssetStore.setSelectedFleetAssetTrailerId(item.id);
        this.$router.push({
          name: 'Trailer',
          params: {
            name: item.name.toLowerCase().replace(/ /g, '-'),
            id: item.id,
          },
        });
      }
    }
  }

  // Adds the already selected items that may not be available in the new search query.
  public setSelectedAndSuggestions(selectedIds: string[]): void {
    try {
      const suggestions: EntitySearch[] = JSON.parse(
        JSON.stringify(this.suggestions),
      );
      for (const suggestion of suggestions) {
        suggestion.isSelected = false;
      }
      // add the already selected items to the top of the list.
      for (const selectedId of selectedIds) {
        const selectedItem: EntitySearch | undefined = JSON.parse(
          JSON.stringify(
            this.searchList.find((x: EntitySearch) => x.id === selectedId),
          ),
        );
        if (!selectedItem) {
          this.selectedAndSuggestions = [];
          return;
        }
        selectedItem.isSelected = true;
        const indexOfSelectedItem: number = suggestions.findIndex(
          (x: EntitySearch) => x.id === selectedId,
        );
        if (indexOfSelectedItem >= 0) {
          suggestions[indexOfSelectedItem].isSelected = true;
        } else {
          suggestions.unshift(selectedItem);
        }
      }
      this.selectedAndSuggestions = suggestions;

      // we reset the selected highlighted item in the suggestions list if the current selected index is out of range.
      if (
        this.selectedAndSuggestions.length > 0 &&
        this.currentHighlightedSelectionIndex >
          this.selectedAndSuggestions.length - 1
      ) {
        this.currentHighlightedSelectionIndex =
          this.selectedAndSuggestions.length - 1;
      }
    } catch (e) {
      console.error(e);
    }
  }

  // returns the width of the input component.
  public getInputWidth(): number {
    const inputElement: HTMLElement | null = document.getElementById(
      this.inputId,
    );
    return inputElement ? inputElement.getBoundingClientRect().width : 0;
  }

  // Filtering the suggestion based on the input
  get suggestions(): EntitySearch[] {
    try {
      // Start with a threshold of 0.5. Lower the threshold by 0.02 based on the
      // number of characters in searchedText, to make the search stricter the
      // more you type. The lowest possible value is 0.3.
      let threshold: number = 0.5;
      if (this.searchedText.length > 0) {
        threshold = Math.max(0.3, threshold - this.searchedText.length * 0.02);
      }

      const fuse = new Fuse(this.searchList, {
        keys: [
          { name: 'name', weight: 4 },
          { name: 'additionalQuery', weight: 1 },
        ],
        minMatchCharLength: 1,
        threshold,
        includeScore: true, // Return score so we can count high ranking results
        ignoreLocation: true, // Ignore position of words
      });
      let maxResults: number = 15;
      // Search
      let searchResults: EntitySearch[] = fuse
        .search(this.searchedText)
        .map((x: FuseResult<EntitySearch>) => {
          return { ...x.item, score: x.score };
        });

      // If the number of high-scoring results is less than the max results, we
      // will increase the max results to show more results. We should check
      // check the score of the results, and if there is a large group of highly
      // scoring results followed by the rest of the results, then we should
      // increase maxResults to the number of high scoring results.
      const highScoringResults = searchResults.filter(
        (x: EntitySearch) => !!x.score && x.score <= 0.1,
      );
      if (
        highScoringResults.length < 100 &&
        highScoringResults.length > maxResults
      ) {
        maxResults = highScoringResults.length;
      }

      if (searchResults.length === 0 && !this.searchedText) {
        searchResults = this.getDefaultSuggestions();
        // If no default suggestions were found we will provide the complete search list
        if (searchResults.length === 0) {
          searchResults = this.searchList;
        }
      }

      // Sort result so that active entities are at the top of the list. If a
      // entity was created recently they may have a status of DO NOT USE.
      // Because they are new and administration to these accounts is likely to
      // take place, we don't want to sort these accounts to the bottom of our
      // search result. To handle this we will exclude DO NOT USE accounts from
      // our sorting if they were created within the last week.
      const oneWeekBeforeToday = moment().subtract(1, 'week').valueOf();

      // Split search results into 2 lists,
      // - one for results that are active (!(statusList.includes(13) ||
      //   statusList.includes(47))) OR if their createdDate is within the last
      //   week
      // - a second list for results that are not active
      //   (statusList.includes(13) || statusList.includes(47)) AND their
      //   createdDate is not within the last week
      const activeSearchResults: EntitySearch[] = [];
      const inactiveSearchResults: EntitySearch[] = [];
      for (const searchResult of searchResults.slice(0, maxResults)) {
        const isWithinOneWeek =
          searchResult.createdDate &&
          searchResult.createdDate > oneWeekBeforeToday;
        if (
          (!searchResult.statusList.includes(13) &&
            !searchResult.statusList.includes(47)) ||
          isWithinOneWeek
        ) {
          activeSearchResults.push(searchResult);
        } else {
          inactiveSearchResults.push(searchResult);
        }
      }
      // Combine the lists, with active or recent items first and inactive items afterwards
      return [...activeSearchResults, ...inactiveSearchResults];
    } catch (e) {
      console.error(e);
      return [];
    }
  }

  // When the user has not entered any search text we will provide a list of suggested items. These suggestions come from jobs that are currently in our operationJobsList
  public getDefaultSuggestions(): EntitySearch[] {
    if (!this.suggestFromCurrentJobs) {
      return [];
    }
    interface IdSuggestion {
      id: string;
      entityType: EntityType;
    }
    const idSuggestions: IdSuggestion[] = [];
    // method to find whether the suggestion already exists.
    function suggestionExists(entityType: EntityType, id: string): boolean {
      const exists: IdSuggestion | undefined = idSuggestions.find(
        (x: IdSuggestion) => x.id === id && x.entityType === entityType,
      );
      return exists ? true : false;
    }
    const jobStore = useJobStore();
    // Iterate over jobs and add suggestions based on clientId, fleetAssetId, driverId
    for (let i = 0; i < jobStore.operationJobsList.length; i++) {
      for (const entityType of this.entityTypes) {
        if (entityType === EntityType.CLIENT) {
          if (
            !suggestionExists(
              EntityType.CLIENT,
              jobStore.operationJobsList[i].clientId,
            )
          ) {
            idSuggestions.push({
              id: jobStore.operationJobsList[i].clientId,
              entityType: EntityType.CLIENT,
            });
          }
        } else if (
          entityType === EntityType.DRIVER &&
          jobStore.operationJobsList[i].driverId
        ) {
          if (
            !suggestionExists(
              EntityType.DRIVER,
              jobStore.operationJobsList[i].driverId,
            )
          ) {
            idSuggestions.push({
              id: jobStore.operationJobsList[i].driverId,
              entityType: EntityType.DRIVER,
            });
          }
        } else if (entityType === EntityType.FLEET_ASSET) {
          if (
            !suggestionExists(
              EntityType.FLEET_ASSET,
              jobStore.operationJobsList[i].fleetAssetId,
            )
          ) {
            idSuggestions.push({
              id: jobStore.operationJobsList[i].fleetAssetId,
              entityType: EntityType.FLEET_ASSET,
            });
          }
        }
      }
    }

    // utilising the fleet asset ids on our jobs we push in associated fleet asset owners
    if (this.entityTypes.includes(EntityType.FLEET_ASSET_OWNER)) {
      const fleetAssetIds: string[] = idSuggestions
        .filter((x: IdSuggestion) => x.entityType === EntityType.FLEET_ASSET)
        .map((y: IdSuggestion) => y.id);
      const fleetAssetOwnerIdSuggestions: IdSuggestion[] =
        useFleetAssetOwnerStore()
          .getOwnerList.filter((x: FleetAssetOwnerSummary) =>
            x.associatedFleetAssets.some((el) => fleetAssetIds.includes(el)),
          )
          .map((r: FleetAssetOwnerSummary) => {
            return { id: r.ownerId, entityType: EntityType.FLEET_ASSET_OWNER };
          });
      idSuggestions.push(...fleetAssetOwnerIdSuggestions);
    }

    const suggestions: EntitySearch[] = [];
    // using our idSuggestions we will find the associated EntitySearch
    for (let i = 0; i < idSuggestions.length; i++) {
      const foundSuggestion: EntitySearch | undefined = this.searchList.find(
        (x: EntitySearch) =>
          x.type === idSuggestions[i].entityType &&
          x.id === idSuggestions[i].id,
      );
      if (foundSuggestion) {
        suggestions.push(foundSuggestion);
      }
    }

    // return suggested EntitySearch list
    return suggestions;
  }

  // Returns the text that will be displayed after the selection is made and the suggestions list is closed.
  public getSelectedTextToDisplay(): string {
    try {
      // if this is a route select component we don't have the selected Id passed in as a prop. We will get the id from the route param instead
      const selectedIds = !this.isRouteSelect
        ? this.selectedIds
        : [this.$route.params.id];
      let selected: EntitySearch[] = this.searchList.filter((x: EntitySearch) =>
        selectedIds.includes(x.id),
      );

      // When the component is route select and we wish to show the selected entity we need to filter the list based on what route the user is on. This is because the select entity component in the subcontractor screens has a list of subcontractors, fleet assets, and drivers. If we don't do this the selected text will read all entities with the same id.
      if (this.isRouteSelect) {
        const routeName = this.$route.name;
        if (routeName === 'Driver') {
          selected = selected.filter(
            (x: EntitySearch) => x.type === EntityType.DRIVER,
          );
        } else if (routeName === 'Truck' || routeName === 'Trailer') {
          selected = selected.filter(
            (x: EntitySearch) => x.type === EntityType.FLEET_ASSET,
          );
        } else if (routeName === 'Owner') {
          selected = selected.filter(
            (x: EntitySearch) => x.type === EntityType.FLEET_ASSET_OWNER,
          );
        }
      }

      // we don't wont text overlaping with the input action icons so we should include this width in our calculations.
      const inputActionContainerWidth = 52;
      const inputPaddingSize: number = 24;
      const inputWidthSize =
        this.getInputWidth() - inputActionContainerWidth - inputPaddingSize;
      // If we could not find the width of the input element we will show all selected items as a fallback
      if (!inputWidthSize || selected.length === 1) {
        return JSON.parse(JSON.stringify(selected))
          .map((x: EntitySearch) => x.name)
          .join(', ');
      }

      // If the number of selected items is longer than the single line input, we will add the chip of "plus x more" to the input. The current width of "plus x more" is 85px. Better solution can be implemented here.
      const plusMoreTextWidth: number = 85;
      let isPlusMore: boolean = false;
      let textToDisplayInInput: string = '';

      // iterator for how many selected items to show before the suffix is applied.
      let numberOfSelectedToShow = 0;
      for (let i = 0; i < selected.length; i++) {
        textToDisplayInInput +=
          (textToDisplayInInput !== '' ? ', ' : '') +
          (selected[i] ? selected[i].name : '');
        const textWidth: number = this.getTextWidth(textToDisplayInInput);
        if (inputWidthSize > textWidth) {
          numberOfSelectedToShow++;
          continue;
        } else if (inputWidthSize < textWidth + plusMoreTextWidth) {
          isPlusMore = true;
          break;
        } else {
          isPlusMore = true;
          if (numberOfSelectedToShow > 0) {
            numberOfSelectedToShow--;
          }
          break;
        }
      }
      // selected items that will be displayed in the input chip
      const hiddenSelection = JSON.parse(JSON.stringify(selected)).splice(
        numberOfSelectedToShow,
        selected.length - 1,
      );
      this.hiddenSelections = hiddenSelection.map((x: any) => x.name);
      this.chipText = isPlusMore
        ? ' + ' + (selected.length - numberOfSelectedToShow) + ' more'
        : '';
      const selectedListToDisplay = JSON.parse(JSON.stringify(selected))
        .splice(0, numberOfSelectedToShow)
        .map((x: EntitySearch) => x.name)
        .join(', ');
      return selectedListToDisplay;
    } catch (e) {
      console.error(e);
      return '';
    }
  }

  // resets the the input state
  public clearSelection() {
    this.hasError = false;
    this.selectedIds = [];
    this.searchedText = '';
    this.chipText = '';
    this.hiddenSelections = [];
    this.setIsOpen(false);
    this.$nextTick(() => {
      this.validate();
    });
  }

  // When the user hits the escape key while the input is focused we will close the suggestions and show the number of selected entities in thhe input chip.
  public actionEscapeShortKey() {
    this.setIsOpen(false);
    if (this.selectedIds.length > 0) {
      this.chipText = this.selectedIds.length + ' Selected';
      const selected: EntitySearch[] = this.searchList.filter(
        (x: EntitySearch) => this.selectedIds.includes(x.id),
      );
      this.hiddenSelections = selected.map((x: any) => x.name);
    }
  }

  // Handles the opening and closing of the suggestions list.
  public setIsOpen(isOpen: boolean) {
    try {
      if (this.isOpen === isOpen) {
        if (!isOpen) {
          this.searchedText = this.getSelectedTextToDisplay();
          if (!this.multiple) {
            this.chipText = '';
            this.hiddenSelections = [];
          }
        }
        return;
      }
      this.isOpen = isOpen;
      this.currentHighlightedSelectionIndex = 0;
      if (!this.isOpen) {
        this.searchedText = this.getSelectedTextToDisplay();
      } else {
        this.chipText = '';
        this.hiddenSelections = [];
        this.searchedText = '';
        this.setSelectedAndSuggestions(this.selectedIds);
      }
    } catch (e) {
      console.error(e);
    }
  }

  // utilise a canvas element to find the width of the provided text.
  public getTextWidth(text: string): number {
    try {
      const canvas: HTMLCanvasElement = document.createElement('canvas');
      const context = canvas.getContext('2d');
      if (!context) {
        return 0;
      }
      context.font = 'normal 16px Roboto';
      const metrics = context.measureText(text);
      canvas.remove();
      return metrics.width;
    } catch (e) {
      console.error(e);
      return 0;
    }
  }

  // get the list that will be searched against. This list will be based on the entity types provided via entity types props.
  public getSearchList(entityTypes: EntityType[]): EntitySearch[] {
    const searchList: EntitySearch[] = [];
    for (let i = 0; i < entityTypes.length; i++) {
      if (entityTypes[i] === EntityType.CLIENT) {
        searchList.push(...this.getClientSearchList());
      } else if (entityTypes[i] === EntityType.FLEET_ASSET_OWNER) {
        searchList.push(...this.getSubcontractorSearchList());
      } else if (entityTypes[i] === EntityType.DRIVER) {
        searchList.push(...this.getDriverSearchList());
      } else if (entityTypes[i] === EntityType.FLEET_ASSET) {
        searchList.push(...this.getFleetAssetSearchList());
      }
    }
    return searchList;
  }

  // returns the client list that we will search against.
  public getClientSearchList(): EntitySearch[] {
    try {
      // apply filters to our client search summary list.
      const clientList: ClientSearchSummary[] =
        this.clientDetailsStore.clientSummaryList.filter(
          (x: ClientSearchSummary) => {
            if (
              (!this.retired && x.statusList.includes(13)) ||
              (!this.doNotUse && x.statusList.includes(47))
            ) {
              return false;
            }
            return true;
          },
        );
      if (this.cashSaleClient) {
        clientList.push(...[new CashSalesClientSelectItem()]);
      }

      // return map of ClientSearchSummary to AutoCompleteSearch
      return clientList.map((x: ClientSearchSummary) => {
        const warningColor = '#ff5252';
        const doNotUseColor = '#fb8c00';
        const activeColor = '#FFFFFF';
        const color: string = x.statusList.includes(13)
          ? warningColor
          : x.statusList.includes(47) || x.creditStatus === 2
            ? doNotUseColor
            : activeColor;

        return {
          additionalQuery:
            x.clientId + ' ' + x.tradingName + ' ' + x.clientName,
          id: x.clientId,
          name:
            x.clientId + ' - ' + (x.tradingName ? x.tradingName : x.clientName),
          statusList: x.statusList,
          metaInfo: x.statusList.includes(13)
            ? 'RETIRED'
            : x.creditStatus === 2
              ? 'SEE ACCOUNTS'
              : x.statusList.includes(47)
                ? 'CLOSED'
                : '',
          isSelected: false,
          color,
          type: EntityType.CLIENT,
          icon: '',
          createdDate: null,
        };
      });
    } catch (e) {
      console.error(e);
      return [];
    }
  }

  // returns the subcontractor list that we will search against.
  public getSubcontractorSearchList(): EntitySearch[] {
    try {
      const subcontractorList: FleetAssetOwnerSummary[] =
        this.fleetAssetOwnerStore.getOwnerList.filter((x) => {
          if (
            (!this.retired && x.statusList.includes(13)) ||
            (!this.doNotUse && x.statusList.includes(47))
          ) {
            return false;
          }

          if (this.affiliationTypes && this.affiliationTypes.length > 0) {
            this.affiliationTypes.includes(x.affiliation);
          }
          return true;
        });

      // return map of ClientSearchSummary to AutoCompleteSearch
      return subcontractorList.map((x: FleetAssetOwnerSummary) => {
        const warningColor = '#ff5252';
        const doNotUseColor = '#fb8c00';
        const activeColor = '#FFFFFF';
        const color: string = x.statusList.includes(13)
          ? warningColor
          : x.statusList.includes(47)
            ? doNotUseColor
            : activeColor;

        let additionalQuery: string = '';

        // add additional query options related to the owners associated drivers.
        additionalQuery +=
          x.associatedDriverList
            .map((driver: DriverDetailsSummary) => driver.displayName)
            .join(' ') + ' ';

        // add additional query options related to the owners associated fleet assets.
        additionalQuery += x.associatedFleetAssetList
          .map(
            (fleetAsset: FleetAssetSummary) =>
              fleetAsset.csrAssignedId + ' ' + fleetAsset.registrationNumber,
          )
          .join(' ');

        const id: string = this.isMongoId && x._id ? x._id : x.ownerId;
        return {
          additionalQuery,
          id,
          name: x.name,
          statusList: x.statusList,
          metaInfo: x.statusList.includes(13)
            ? 'RETIRED'
            : x.statusList.includes(47)
              ? 'CLOSED'
              : '',
          isSelected: false,
          color,
          type: EntityType.FLEET_ASSET_OWNER,
          icon: 'fal fa-briefcase',
          createdDate: this.getEpochMillisecondsFromMongoId(x._id),
        };
      });
    } catch (e) {
      console.error(e);
      return [];
    }
  }

  /**
   * Returns the primary driver identifier from custom config, so we know what
   * properties on the driver we should set as additional query.
   */
  get primaryDriverIdentifier(): PrimaryDriverIdentifier {
    return (
      useCompanyDetailsStore().divisionCustomConfig?.operations
        ?.primaryDriverIdentifier ?? PrimaryDriverIdentifier.DRIVER_NAME
    );
  }

  // returns the driver list that we will search against.
  public getDriverSearchList(): EntitySearch[] {
    try {
      const driverList: DriverDetailsSummary[] =
        this.driverDetailsStore.getDriverList.filter((x) => {
          if (
            (!this.retired && x.statusList.includes(13)) ||
            (!this.doNotUse && x.statusList.includes(47))
          ) {
            return false;
          }
          return true;
        });
      // return map of DriverDetailsSummary to EntitySearch
      return driverList.map((x: DriverDetailsSummary) => {
        const warningColor = '#ff5252';
        const doNotUseColor = '#fb8c00';
        const activeColor = '#FFFFFF';
        const color: string = x.statusList.includes(13)
          ? warningColor
          : x.statusList.includes(47)
            ? doNotUseColor
            : activeColor;

        const additionalQueryList = [x.mobile, x.email];
        // Add name or assignedId as an additional field based on the primaryDriverIdentifier
        if (
          this.primaryDriverIdentifier === PrimaryDriverIdentifier.DRIVER_NAME
        ) {
          additionalQueryList.push(x.assignedId ?? '');
        } else if (
          this.primaryDriverIdentifier === PrimaryDriverIdentifier.ASSIGNED_ID
        ) {
          additionalQueryList.push(x.name);
        }

        // add additional query options related to the drivers associated fleet asset owners.
        const associatedSubcontractorList: FleetAssetOwnerSummary[] =
          this.fleetAssetOwnerStore.getOwnerList.filter((owner) =>
            owner.associatedDrivers.includes(x.driverId),
          );
        additionalQueryList.push(
          ...associatedSubcontractorList.map(
            (subcontractor: FleetAssetOwnerSummary) => subcontractor.name,
          ),
        );

        const associatedFleetAssets: FleetAssetSummary[] =
          this.fleetAssetStore.getAllFleetAssetList.filter((fleetAsset) =>
            fleetAsset.associatedDrivers.includes(x.driverId),
          );
        additionalQueryList.push(
          ...associatedFleetAssets.map(
            (fleetAsset: FleetAssetSummary) =>
              fleetAsset.csrAssignedId + ' ' + fleetAsset.registrationNumber,
          ),
        );

        const additionalQuery: string = additionalQueryList
          .filter((s) => !!s)
          .join(' ');

        return {
          additionalQuery,
          id: x.driverId,
          name: x.displayName,
          nameSuffix: x.mobile ? x.mobile : '',
          statusList: x.statusList,
          metaInfo: x.statusList.includes(13)
            ? 'RETIRED'
            : x.statusList.includes(47)
              ? 'CLOSED'
              : '',
          isSelected: false,
          color,
          type: EntityType.DRIVER,
          icon: 'fal fa-steering-wheel',
          createdDate: this.getEpochMillisecondsFromMongoId(x.id),
        };
      });
    } catch (e) {
      console.error(e);
      return [];
    }
  }

  // returns the fleet asset list that we will search against.
  public getFleetAssetSearchList(): EntitySearch[] {
    try {
      const fleetAssetList: FleetAssetSummary[] =
        this.fleetAssetStore.getAllFleetAssetList.filter((x) => {
          if (
            (!this.retired && x.statusList.includes(13)) ||
            (!this.doNotUse && x.statusList.includes(47))
          ) {
            return false;
          }
          return true;
        });
      // return map of ClientSearchSummary to AutoCompleteSearch
      return fleetAssetList.map((x: FleetAssetSummary) => {
        const warningColor = '#ff5252';
        const doNotUseColor = '#fb8c00';
        const activeColor = '#FFFFFF';
        const color: string = x.statusList.includes(13)
          ? warningColor
          : x.statusList.includes(47)
            ? doNotUseColor
            : activeColor;

        let additionalQuery: string = '';
        // add additional query options related to the drivers associated fleet asset owners.
        const associatedSubcontractorList: FleetAssetOwnerSummary[] =
          this.fleetAssetOwnerStore.getOwnerList.filter(
            (owner: FleetAssetOwnerSummary) =>
              owner.associatedFleetAssets.includes(x.fleetAssetId),
          );
        additionalQuery +=
          associatedSubcontractorList
            .map((fleetAsset: FleetAssetOwnerSummary) => fleetAsset.name)
            .join(' ') + ' ';

        // add additional query options related to the fleet assets associated drivers.
        const associatedDriversList: DriverDetailsSummary[] =
          this.driverDetailsStore.getDriverList.filter(
            (driver: DriverDetailsSummary) =>
              x.associatedDrivers.includes(driver.driverId),
          );
        additionalQuery += associatedDriversList
          .map((driver: DriverDetailsSummary) => driver.displayName)
          .join(' ');

        return {
          additionalQuery: '',
          id: x.fleetAssetId,
          name: x.csrAssignedId + ' - ' + x.registrationNumber,
          statusList: x.statusList,
          metaInfo: x.statusList.includes(13)
            ? 'RETIRED'
            : x.statusList.includes(47)
              ? 'CLOSED'
              : '',
          isSelected: false,
          color,
          type: EntityType.FLEET_ASSET,
          icon: x.isTruck ? 'fal fa-truck-moving' : 'fas fa-trailer',
          createdDate: this.getEpochMillisecondsFromMongoId(x._id),
        };
      });
    } catch (e) {
      console.error(e);
      return [];
    }
  }

  // Takes in a mongoId and extracts the created date timestamp as epoch milliseconds
  public getEpochMillisecondsFromMongoId(
    _id: string | undefined,
  ): number | null {
    try {
      if (!_id) {
        return null;
      }
      // The timestamp is contained at the start of the mongoId
      const mongoIdTimestamp: string = _id.substring(0, 8);
      const mongoIdDate: Date = new Date(parseInt(mongoIdTimestamp, 16) * 1000);
      const createdDateInMilliseconds = mongoIdDate.valueOf();
      return createdDateInMilliseconds;
    } catch (e) {
      return null;
    }
  }

  // validate is required by vuetify so that validation can be integrated into vuetify form validation.
  public validate(): boolean {
    let hasError: boolean = false;
    // iterate over rules and validate.
    for (let i = 0; i < this.rules.length; i++) {
      const isValid: boolean | string = !this.multiple
        ? this.rules[i](this.id)
        : this.rules[i](this.ids);
      if (isValid && typeof isValid !== 'string') {
        hasError = false;
      } else {
        this.errorMessage = isValid as string;
        hasError = true;
        break;
      }
    }
    this.hasError = hasError;
    return !hasError;
  }

  // sets focus on the input.
  public focusInput(): void {
    const inputElement: HTMLElement | null = document.getElementById(
      this.inputId,
    );
    if (!inputElement) {
      return;
    }
    inputElement.focus();
  }

  // handles the adding and removing of the input focusout event listener
  public handleInputEventListener(addListener: boolean): void {
    const inputElement: HTMLElement | null = document.getElementById(
      this.inputId,
    );
    if (!inputElement) {
      return;
    }
    if (addListener) {
      inputElement.addEventListener('focusout', this.focusoutHandler);
    } else {
      inputElement.removeEventListener('focusout', this.focusoutHandler);
    }
  }

  // callback for our focus event listener.
  public focusoutHandler(event: FocusEvent): void {
    const eventTarget: EventTarget | null = event.relatedTarget;
    if (eventTarget && (eventTarget as HTMLElement).id === this.containerId) {
      // The selected element inside this component. We don't need to do anything here.
      return;
    }
    // The selected element was outside of this component. We should now close the suggestions list as the user has moved away.
    this.setIsOpen(false);
    // The user has moved away from the input so we should run our validate method.
    this.$nextTick(() => {
      this.validate();
    });
  }

  // Register/Unregister component with the injected v-form component for vuetify validation integration.
  public handleValidationRegistration(isRegister: boolean): void {
    if (!this.form) {
      return;
    }
    if (isRegister) {
      this.form.register(this);
    } else {
      this.form.unregister(this);
    }
  }

  public resetValidation() {
    this.hasError = false;
    this.errorMessage = '';
    this.$nextTick(() => {
      this.validate();
    });
  }

  public beforeDestroy(): void {
    this.handleValidationRegistration(false);
    this.handleInputEventListener(false);
  }

  // There looked to be a custom filter ontop of the old fuse subcontractor search. Leaving this here for now.
  // public customFilter(item: SubcontractorSearch, queryText: string): boolean {
  //   const name = item.name.toLowerCase();
  //   const additionalSearchOptions = item.additionalSearchOptions
  //     ? item.additionalSearchOptions.toLowerCase()
  //     : '';
  //   const searchText = queryText.toLowerCase();
  //   const words = searchText.split(' ');
  //   let containsWord = false;

  //   for (const word of words) {
  //     containsWord =
  //       name.indexOf(word) > -1 || additionalSearchOptions.indexOf(word) > -1;
  //   }

  //   return containsWord;
  // }
}
