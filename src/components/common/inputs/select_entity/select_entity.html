<div :id="containerId" tabindex="-1" style="position: relative">
  <div style="position: relative">
    <input
      :id="inputId"
      class="autocomplete-input"
      :class="{'hide-details': hideDetails}"
      type="search"
      v-model="searchedText"
      @keydown.down="moveDownList"
      @keydown.up="moveUpList"
      @keydown.esc="actionEscapeShortKey"
      @input="inputUpdated"
      :disabled="disabled"
      @focus="setIsOpen(true); (hasError = false)"
      autocomplete="off"
    />
    <!-- Floating placeholder used so we can apply '*' via content on css pseudo element when input is required. -->
    <span
      v-if="!searchedText"
      @click="focusInput"
      class="floating-placeholder"
      :class="{'form-field-required-marker': isRequired, 'hide-details': hideDetails}"
      >{{labelName}}</span
    >
  </div>
  <!-- Action container holds the icons that are displayed on the right side of the input -->
  <div class="action-container" :class="{'disabled': disabled}">
    <v-tooltip bottom v-if="chipText && !isRouteSelect">
      <template v-slot:activator="{ on }">
        <div v-on="on" class="chip-item mr-1" :id="chipId">{{chipText}}</div>
      </template>
      <div v-for="hiddenSelection of hiddenSelections">{{hiddenSelection}}</div>
    </v-tooltip>
    <v-tooltip bottom v-if="selectedIds.length > 0" :disabled="disabled">
      <template v-slot:activator="{ on }">
        <div v-on="on" class="icon-container" @click="clearSelection">
          <v-icon :disabled="disabled" size="18">far fa-times</v-icon>
        </div>
      </template>
      Clear Selection{{(multiple ? "s" : "")}}
    </v-tooltip>
    <div class="icon-container ml-1" @click="setIsOpen(!isOpen)">
      <v-icon
        :color="hasError ? '#ff5252' : ''"
        :class="[{rotate: isOpen}]"
        size="15"
        >fas fa-caret-down</v-icon
      >
    </div>
  </div>
  <!-- input-label contains the secondary label/placeholder, hint message or error message -->
  <label v-if="!hideDetails" class="input-label" for="autocomplete"
    ><span v-if="!hasError">{{hint ? hint : labelName}}</span
    ><span class="error-text" v-else>{{errorMessage}}</span></label
  >
  <!-- Suggestions menu contains the list of selected items and filtered search results based on the searched text -->
  <ul class="suggestions-menu" v-if="isOpen">
    <li
      v-if="!searchedText && selectedIds.length === 0 && selectedAndSuggestions.length === 0"
      style="cursor: default"
      @click="focusInput"
    >
      <span>Enter your query to begin search.</span>
    </li>
    <li
      v-if="selectedAndSuggestions.length > 0"
      v-for="(suggestion, index) in selectedAndSuggestions"
      :key="suggestion.type + '-' + suggestion.id"
      class="select-item"
      :class="{highlighted: index === currentHighlightedSelectionIndex}"
      @mousedown="setSelectedItem(suggestion)"
      v-shortkey="['enter']"
      @shortkey="setSelectedItem(selectedAndSuggestions[currentHighlightedSelectionIndex])"
      @click="focusInput"
    >
      <input
        class="checkbox-input"
        type="checkbox"
        :checked="suggestion.isSelected"
        tabindex="-1"
        v-if="multiple"
      />
      <span @click.stop="setSelectedItem(suggestion)" class="suggestion-item"
        >{{ suggestion.name + (suggestion.nameSuffix ? `
        (${suggestion.nameSuffix})` : '')}}</span
      >
      <span v-if="suggestion.metaInfo" class="px-2"> | </span>
      <span v-if="suggestion.metaInfo" :style="{color: suggestion.color}">
        {{ suggestion.metaInfo }}</span
      >

      <v-icon v-if="suggestion.icon" class="ml-2" color="grey" size="13"
        >{{suggestion.icon}}</v-icon
      >
    </li>
    <li
      v-if="searchedText && selectedAndSuggestions.length === 0"
      style="cursor: default"
      @click="focusInput"
    >
      <span>No results found.</span>
    </li>
  </ul>
</div>
