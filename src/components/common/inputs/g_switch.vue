<template>
  <div
    :id="containerId"
    class="switch-container"
    tabindex="-1"
    style="position: relative; width: 100%"
  >
    <label
      class="switch-input input-label"
      :class="[{ 'animate-wiggle': errorMessage }]"
    >
      <input
        type="checkbox"
        :id="inputId"
        @input="onInput"
        :checked="props.value"
      />
      <span v-if="!errorMessage">{{ label }}</span
      ><span class="error-text" v-else>{{ errorMessage }}</span>
    </label>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  Ref,
  computed,
  getCurrentInstance,
  onMounted,
  ComputedRef,
} from 'vue';
import { v4 as uuidv4 } from 'uuid';

import { useRootStore } from '@/store/modules/RootStore';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
const instance: any = getCurrentInstance();
const emit = defineEmits(['input', 'setLoading', 'actionRequest']);

interface IProps {
  hideDetails?: boolean;
  label?: string;
  rules?: ((x: any[]) => boolean | string)[];
  hint?: string;
  disabled?: boolean;
  // A custom update required error message for when the input is required to change value
  updateRequiredMessage?: string;
  // If you wish to pass in a custom error message.
  customErrorMessage?: string;
  value: any;
}
const props = withDefaults(defineProps<IProps>(), {
  hideDetails: false,
  label: '',
  rules: () => [],
  disabled: false,
  customErrorMessage: '',
});

const errorMessage: Ref<string> = ref('');
const containerId: string = uuidv4().replace(/-/g, '');
const inputId: string = uuidv4().replace(/-/g, '');

function onInput(e: any) {
  emit('input', e.currentTarget.checked);
  errorMessage.value = '';
}

// Returns the error message
const errorMessageText: ComputedRef<string> = computed(() => {
  return props.customErrorMessage || errorMessage.value;
});

// validate is required on input components due to how we handle form validation. This function will be called from a parent.
function validate(): boolean {
  const containerEl: HTMLElement | null = document.getElementById(containerId);
  const labelEl = containerEl
    ? containerEl.getElementsByTagName('label')
    : null;

  // remove animation class
  if (labelEl && labelEl.length === 1) {
    labelEl[0].classList.remove('animate-wiggle');
  }
  // iterate over rules and validate.
  let hasErrors: boolean = false;
  for (let i = 0; i < props.rules.length; i++) {
    const isValid: boolean | string = props.rules[i](
      instance.proxy.$attrs.value,
    );
    if (isValid && typeof isValid !== 'string') {
      errorMessage.value = '';
      hasErrors = false;
    } else {
      errorMessage.value = isValid as string;
      hasErrors = true;
      break;
    }
  }
  hasErrors = hasErrors || props.customErrorMessage ? true : false;

  // add animation class
  if (labelEl && labelEl.length === 1 && hasErrors) {
    setTimeout(() => {
      labelEl[0].classList.add('animate-wiggle');
    }, 1);
  }
  return !hasErrors;
}

// returns boolean on whether the required rules are contained in the rules prop. Utilised to show the red astrix on the input placeholder
const isRequired: ComputedRef<boolean> = computed(() => {
  return (
    props.rules.includes(validationRules.required) ||
    props.rules.includes(validationRules.listRequired)
  );
});

onMounted(() => {});

defineExpose({
  validate,
});
</script>

<style scoped lang="scss">
.switch-container {
  height: 70px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  font-size: $font-size-16;
}

.switch-input {
  z-index: 0;
  position: relative;
  display: inline-block;
  color: var(--light-text-color);
  font-size: $font-size-16;
  line-height: 1.5;
}

/* Input */
.switch-input > input {
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  z-index: -1;
  position: absolute;
  right: 6px;
  top: -8px;
  display: block;
  margin: 0;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  background-color: var(--disabled-text-color);
  outline: none;
  opacity: 0;
  transform: scale(1);
  pointer-events: none;
  transition:
    opacity 0.3s 0.1s,
    transform 0.2s 0.1s;
}

/* Span */
.switch-input > span {
  display: inline-block;
  width: 100%;
  cursor: pointer;
}

/* Track */
.switch-input > span::before {
  content: '';
  float: right;
  display: inline-block;
  margin: 5px 0 5px 10px;
  border-radius: 7px;
  width: 36px;
  height: 14px;
  background-color: var(--disabled-text-color);
  vertical-align: top;
  transition:
    background-color 0.2s,
    opacity 0.2s;
}

/* Thumb */
.switch-input > span::after {
  //   content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='mdc-switch__icon mdc-switch__icon--on' viewBox='0 0 24 24' %3E%3Cpath d='M19.69,5.23L8.96,15.96l-4.23-4.23L2.96,13.5l6,6L21.46,7L19.69,5.23z' /%3E%3C/svg%3E");
  //   content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='mdc-switch__icon mdc-switch__icon--off' viewBox='0 0 24 24' %3E%3Cpath d='M20 13H4v-2h16v2z' /%3E%3C/svg%3E");
  content: '';
  position: absolute;
  top: 2px;
  right: 16px;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  background-color: var(--text-color);
  box-shadow:
    0 3px 1px -2px rgba(0, 0, 0, 0.2),
    0 2px 2px 0 rgba(0, 0, 0, 0.14),
    0 1px 5px 0 rgba(0, 0, 0, 0.12);
  transition:
    background-color 0.2s,
    transform 0.2s;
}

/* Checked */
.switch-input > input:checked {
  right: -10px;
  background-color: rgb(33, 150, 243);
}

.switch-input > input:checked + span::before {
  background-color: rgba(33, 150, 243, 0.6);
}

.switch-input > input:checked + span::after {
  background-color: rgb(33, 150, 243);
  transform: translateX(16px);
}

/* Hover, Focus */
.switch-input:hover > input {
  opacity: 0.04;
}

.switch-input > input:focus {
  opacity: 0.12;
}

.switch-input:hover > input:focus {
  opacity: 0.16;
}

/* Active */
.switch-input > input:active {
  opacity: 1;
  transform: scale(0);
  transition:
    transform 0s,
    opacity 0s;
}

.switch-input > input:active + span::before {
  background-color: rgba(33, 150, 243, 0.6);
}

.switch-input > input:checked:active + span::before {
  background-color: rgba(0, 0, 0, 0.38);
}

/* Disabled */
.switch-input > input:disabled {
  opacity: 0;
}

.switch-input > input:disabled + span {
  color: rgb(0, 0, 0);
  opacity: 0.38;
  cursor: default;
}

.switch-input > input:disabled + span::before {
  background-color: rgba(0, 0, 0, 0.38);
}

.switch-input > input:checked:disabled + span::before {
  background-color: rgba(33, 150, 243, 0.6);
}

.light-theme {
  .input-label {
    color: rgba($color: #000000, $alpha: 0.54);
  }
}
</style>
