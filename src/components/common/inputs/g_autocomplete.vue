<!-- <template>
  <div :class="classes" @mousedown="onMousedown" @mouseup="onMouseup">
    <label
      v-if="label"
      class="g-input__label"
      :class="{
        'g-input__label--active': isMenuActive || isDirty || internalSearch,
        'g-input__label--disabled': isDisabled,
      }"
      >{{ label }}</label
    >
    <div class="g-input__control">
      <div class="g-input__slot" ref="inputSlot" @click="onClick">
        <span v-if="prefix" class="g-input__prefix">{{ prefix }}</span>
        <div v-if="hasChips" class="g-select__selections">
          <span
            v-for="(item, i) in selectedItems"
            :key="getValue(item)"
            class="g-chip g-chip--select-multi"
            :class="{ 'g-chip--active': i === selectedIndex }"
            @click.stop="selectedIndex = i"
          >
            {{ getText(item) }}
            <span
              v-if="deletableChips"
              class="g-chip__close"
              @click.stop="removeItem(item)"
              >×</span
            >
          </span>
        </div>
        <input
          ref="input"
          v-model="internalSearch"
          :placeholder="placeholder"
          :autocomplete="browserAutocomplete"
          :readonly="readonly"
          :disabled="isDisabled"
          class="g-input__input"
          @focus="onFocus"
          @blur="onBlur"
          @keydown="onKeyDown"
          @input="onInput"
          :aria-autocomplete="'list'"
          :aria-expanded="isMenuActive"
          :aria-owns="menuId"
          role="combobox"
        />
        <span
          v-if="clearable && isDirty"
          class="g-input__icon g-input__icon--clear"
          @click.stop="clearableCallback"
        >
          <i class="fas fa-times"></i>
        </span>
        <span
          class="g-input__icon g-input__icon--append"
          @click.stop="toggleMenu"
        >
          <i class="fas fa-chevron-down"></i>
        </span>
      </div>
    </div>
    <div
      v-show="isMenuActive"
      :id="menuId"
      class="g-menu__content g-autocomplete__content"
      ref="menu"
      :style="{ maxHeight: menuProps.maxHeight + 'px', overflowY: 'auto' }"
    >
      <div
        v-if="!filteredItems.length && !hideNoData"
        class="g-list__tile g-list__tile--no-data"
      >
        {{ noDataText }}
      </div>
      <div
        v-for="(item, i) in filteredItems"
        :key="getValue(item)"
        class="g-list__tile"
        :class="{
          'g-list__tile--highlighted': i === menuIndex,
          'g-list__tile--disabled': getDisabled(item),
        }"
        @mousedown.prevent="selectItem(item)"
        @mouseenter="menuIndex = i"
      >
        {{ getText(item) }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, Ref, ComputedRef } from 'vue';
import { CSSProperties } from 'vue/types/jsx.js';

/** Keyboard key codes for navigation and actions */
const keyCodes = {
  tab: 9,
  enter: 13,
  esc: 27,
  up: 38,
  down: 40,
  left: 37,
  right: 39,
  backspace: 8,
  delete: 46,
  space: 32,
};

type ItemType = Record<string, any>;

interface Props {
  items?: ItemType[];
  modelValue?: any;
  multiple?: boolean;
  chips?: boolean;
  deletableChips?: boolean;
  clearable?: boolean;
  hideSelected?: boolean;
  hideNoData?: boolean;
  noDataText?: string;
  itemText?: string | ((item: ItemType) => string);
  itemValue?: string | ((item: ItemType) => any);
  itemDisabled?: string | ((item: ItemType) => boolean);
  filter?: (item: ItemType, queryText: string, itemText: string) => boolean;
  searchInput?: string;
  browserAutocomplete?: string;
  placeholder?: string;
  readonly?: boolean;
  disabled?: boolean;
  menuProps?: { maxHeight?: number };
  prefix?: string;
  label?: string; // <-- add label prop
}

const props = withDefaults(defineProps<Props>(), {
  items: () => [],
  modelValue: undefined,
  multiple: false,
  chips: false,
  deletableChips: false,
  clearable: false,
  hideSelected: false,
  hideNoData: false,
  noDataText: 'No data available',
  itemText: 'text',
  itemValue: 'value',
  itemDisabled: 'disabled',
  filter: (item: ItemType, queryText: string, itemText: string) =>
    itemText.toLocaleLowerCase().indexOf(queryText.toLocaleLowerCase()) > -1,
  searchInput: '',
  browserAutocomplete: 'off',
  placeholder: '',
  readonly: false,
  disabled: false,
  menuProps: () => ({ maxHeight: 300 }),
  prefix: '',
  label: '', // <-- default label
});

const emit = defineEmits<{
  (event: 'update:modelValue', value: any): void;
  (event: 'update:searchInput', value: string): void;
}>();

const input = ref<HTMLInputElement | null>(null);
const inputSlot = ref<HTMLElement | null>(null);
const menu = ref<HTMLElement | null>(null);

const internalSearch: Ref<string> = ref(props.searchInput || '');
const isMenuActive: Ref<boolean> = ref(false);
const selectedItems: Ref<ItemType[]> = ref([]);
const selectedIndex: Ref<number> = ref(-1);
const menuIndex: Ref<number> = ref(-1);
const menuId: string =
  'g-autocomplete-menu-' + Math.random().toString(36).substr(2, 8);

/** Whether the field is disabled */
const isDisabled: ComputedRef<boolean> = computed(() => props.disabled);

/** Whether to show chips for selections */
const hasChips: ComputedRef<boolean> = computed(() => props.chips);

/** Whether the field is dirty (has a value) */
const isDirty: ComputedRef<boolean> = computed(() =>
  props.multiple
    ? selectedItems.value.length > 0
    : !!selectedItems.value.length,
);

/** Filtered items based on search input */
const filteredItems: ComputedRef<ItemType[]> = computed(() => {
  if (!internalSearch.value) {
    return props.items;
  }
  return props.items.filter((item) =>
    props.filter(item, internalSearch.value, getText(item)),
  );
});

/** CSS classes for the root element */
const classes: ComputedRef<Record<string, boolean>> = computed(() => ({
  'g-input': true,
  'g-input--is-focused': isMenuActive.value,
  'g-select': true,
  'g-autocomplete': true,
  'g-select--chips': props.chips,
  'g-select--is-menu-active': isMenuActive.value,
  'g-input--has-prefix': !!props.prefix,
}));

const menuStyles: ComputedRef<CSSProperties> = computed(() => {
  if (!inputSlot.value) {
    return {};
  }
  const rect = inputSlot.value.getBoundingClientRect();
  return {
    position: 'absolute',
    top: `${rect.bottom + window.scrollY}px`,
    left: `${rect.left + window.scrollX}px`,
    width: `${rect.width}px`,
    maxHeight: props.menuProps.maxHeight + 'px',
    // overflowY: ,
    zIndex: 9999,
  };
});

/**
 * Get the display text for an item.
 * @param item - The item object.
 * @returns The display text.
 */
function getText(item: ItemType): string {
  if (typeof props.itemText === 'function') {
    return props.itemText(item);
  }
  return item && item[props.itemText as string];
}

/**
 * Get the value for an item.
 * @param item - The item object.
 * @returns The value.
 */
function getValue(item: ItemType): any {
  if (typeof props.itemValue === 'function') {
    return props.itemValue(item);
  }
  return item && item[props.itemValue as string];
}

/**
 * Get the disabled state for an item.
 * @param item - The item object.
 * @returns True if disabled.
 */
function getDisabled(item: ItemType): boolean {
  if (typeof props.itemDisabled === 'function') {
    return props.itemDisabled(item);
  }
  return !!(item && item[props.itemDisabled as string]);
}

/**
 * Set the selected items based on the model value.
 * @param val - The value to set.
 */
function setSelectedItems(val: any): void {
  if (props.multiple) {
    selectedItems.value = Array.isArray(val)
      ? props.items.filter((i) => val.includes(getValue(i)))
      : [];
  } else {
    const found = props.items.find((i) => getValue(i) === val);
    selectedItems.value = found ? [found] : [];
  }
}

/**
 * Handle click on the input slot to open menu.
 */
function onClick(): void {
  if (isDisabled.value) {
    return;
  }
  isMenuActive.value = true;
  nextTick(() => input.value && input.value.focus());
}

/**
 * Handle focus event on the input.
 */
function onFocus(): void {
  isMenuActive.value = true;
}

/**
 * Handle blur event on the input.
 */
function onBlur(): void {
  setTimeout(() => {
    isMenuActive.value = false;
    menuIndex.value = -1;
  }, 150);
}

/**
 * Handle input event on the input.
 * @param e - Input event.
 */
function onInput(e: Event): void {
  const target = e.target as HTMLInputElement;
  internalSearch.value = target.value;
  emit('update:searchInput', internalSearch.value);
  isMenuActive.value = true;
  menuIndex.value = 0;
}

/**
 * Handle keydown event for navigation and selection.
 * @param e - Keyboard event.
 */
function onKeyDown(e: KeyboardEvent): void {
  if (!isMenuActive.value && [keyCodes.down, keyCodes.up].includes(e.keyCode)) {
    isMenuActive.value = true;
    return;
  }
  if (!filteredItems.value.length) {
    return;
  }
  if (e.keyCode === keyCodes.down) {
    e.preventDefault();
    menuIndex.value = (menuIndex.value + 1) % filteredItems.value.length;
  } else if (e.keyCode === keyCodes.up) {
    e.preventDefault();
    menuIndex.value =
      (menuIndex.value + filteredItems.value.length - 1) %
      filteredItems.value.length;
  } else if (e.keyCode === keyCodes.enter) {
    if (menuIndex.value > -1) {
      selectItem(filteredItems.value[menuIndex.value]);
    }
  } else if (e.keyCode === keyCodes.esc) {
    isMenuActive.value = false;
  }
}

/**
 * Handle mousedown event for click-outside logic.
 */
function onMousedown(): void {
  // Placeholder for click-outside logic if needed
}

/**
 * Handle mouseup event for click-outside logic.
 */
function onMouseup(): void {
  // Placeholder for click-outside logic if needed
}

/**
 * Select an item from the list.
 * @param item - The item to select.
 */
function selectItem(item: ItemType): void {
  if (getDisabled(item)) {
    return;
  }
  if (props.multiple) {
    const exists = selectedItems.value.some(
      (i) => getValue(i) === getValue(item),
    );
    if (exists) {
      selectedItems.value = selectedItems.value.filter(
        (i) => getValue(i) !== getValue(item),
      );
    } else {
      selectedItems.value = [...selectedItems.value, item];
    }
    emit(
      'update:modelValue',
      selectedItems.value.map((i) => getValue(i)),
    );
  } else {
    selectedItems.value = [item];
    emit('update:modelValue', getValue(item));
    isMenuActive.value = false;
  }
  internalSearch.value = '';
  menuIndex.value = -1;
}

/**
 * Remove an item from the selection.
 * @param item - The item to remove.
 */
function removeItem(item: ItemType): void {
  if (props.multiple) {
    selectedItems.value = selectedItems.value.filter(
      (i) => getValue(i) !== getValue(item),
    );
    emit(
      'update:modelValue',
      selectedItems.value.map((i) => getValue(i)),
    );
  } else {
    selectedItems.value = [];
    emit('update:modelValue', null);
  }
}

/**
 * Clear all selections and search input.
 */
function clearableCallback(): void {
  selectedItems.value = [];
  internalSearch.value = '';
  emit('update:modelValue', props.multiple ? [] : null);
  nextTick(() => input.value && input.value.focus());
}

/**
 * Toggle the menu open/close state.
 */
function toggleMenu(): void {
  isMenuActive.value = !isMenuActive.value;
  if (isMenuActive.value) {
    nextTick(() => input.value && input.value.focus());
  }
}

// Watchers
watch(() => props.modelValue, setSelectedItems, { immediate: true });
watch(
  () => props.searchInput,
  (val) => {
    internalSearch.value = val || '';
  },
);
</script>

<style scoped lang="scss">
.g-input {
  position: relative;
  font-family: Roboto, Arial, sans-serif;
  font-size: $font-size-16;
  width: 100%;
  color: var(--primary-text-color, #fff);
}

.g-input__control {
  width: 100%;
}

.g-input__slot {
  display: flex;
  align-items: center;
  border: 1px solid $border-color;
  border-radius: 4px;
  padding: 2px 8px;
  background: var(--background-color-300);
  min-height: 36px;
  position: relative;
  transition:
    border 0.2s,
    background 0.2s;
}

.g-input--is-focused .g-input__slot {
  border: 1px solid var(--primary, #90caf9) !important;
}

.g-input--is-disabled .g-input__slot {
  background: var(--background-color-500, #181a20) !important;
  border: none !important;
  color: var(--disabled-text-color, #666);
}

.g-input--is-readonly .g-input__slot {
  background: var(--app-dark-primary-550, #23272f) !important;
  border: none !important;
}

.g-input__input {
  flex: 1;
  border: none;
  outline: none;
  font-size: inherit;
  background: transparent;
  min-width: 60px;
  padding: 4px 0;
  color: var(--primary-text-color, #fff);
}

.g-input__prefix {
  color: var(--prefix-color, #b0b8c1);
  font-size: $font-size-16;
  margin-right: 4px;
  white-space: nowrap;
  user-select: none;
  /* align with input text */
  display: flex;
  align-items: center;
  height: 100%;
}

.g-input__icon {
  cursor: pointer;
  margin-left: 8px;
  user-select: none;
  font-size: $font-size-11;
  color: var(--icon-color, #b0b8c1);
  transition: color 0.2s;
  display: flex;
  align-items: center;
}

.g-input__icon--clear {
  color: var(--border-color, #333);
}

.g-input__icon--clear i {
  font-size: 1.1em;
}

.g-input__icon--append {
  transition: transform 0.2s;
}

.g-input__icon--append i {
  font-size: 1.1em;
}

.g-select--is-menu-active .g-input__icon--append {
  transform: rotate(180deg);
  color: var(--primary, #90caf9);
}

.g-select__selections {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-right: 8px;
}

.g-chip {
  display: inline-flex;
  align-items: center;
  background: var(--chip-bg, #374151);
  border-radius: 16px;
  padding: 0 8px;
  margin: 2px 4px 2px 0;
  font-size: $font-size-14;
  height: 28px;
  cursor: pointer;
  user-select: none;
  color: var(--primary-text-color, #fff);
  transition:
    background 0.2s,
    color 0.2s;
}

.g-chip--active {
  background: var(--primary, #1976d2);
  color: var(--primary-contrast, #fff);
}

.g-chip__close {
  margin-left: 4px;
  font-weight: bold;
  cursor: pointer;
  color: var(--icon-color, #b0b8c1);
}

.g-menu__content {
  position: absolute;
  left: 0;
  right: 0;
  z-index: 10;
  background: var(--background-color-300,);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.45);
  border-radius: 4px;
  margin-top: 4px;
  min-width: 100%;
  max-width: 100%;
  overflow: auto;
  color: var(--primary-text-color, #fff);
}

.g-autocomplete__content {
  padding: 4px 0;
}

.g-list__tile {
  padding: 8px 16px;
  cursor: pointer;
  font-size: $font-size-16;
  transition:
    background 0.2s,
    color 0.2s;
  color: var(--primary-text-color, #fff);
  background: transparent;
}

.g-list__tile--highlighted {
  background: var(--background-color-500, #181a20);
}

.g-list__tile--disabled {
  color: var(--disabled-text-color, #666);
  cursor: not-allowed;
}

.g-list__tile--no-data {
  color: var(--disabled-text-color, #666);
  text-align: center;
}

.g-input__label {
  position: absolute;
  left: 12px;
  top: 14px;
  font-size: $font-size-16;
  color: var(--label-color, #b0b8c1);
  pointer-events: none;
  transition:
    color 0.2s,
    font-size 0.2s,
    top 0.2s;
  z-index: 2;
  background: transparent;
  padding: 0 4px;
  line-height: 1;
}

.g-input__label--active {
  top: -10px;
  font-size: $font-size-12;
  color: var(--primary, #90caf9);
  background: var(--background-color-300, #23272f);
}

.g-input__label--disabled {
  color: var(--disabled-text-color, #666);
}
</style> -->
