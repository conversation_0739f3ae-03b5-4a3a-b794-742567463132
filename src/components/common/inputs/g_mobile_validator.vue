<!-- g_mobile_validator is a wrapper component around our g_text_field component. The goal of this component is to have a reusable way to validate a users mobile number on an input. Validate in this case means verifying whether the inputed mobile number exists via a api call. -->
<template>
  <GTextField
    v-model="mobileNumber"
    :placeholder="'Contact Number'"
    :hint="inputOptions.hint"
    :rules="disabled ? [] : [validation.required, validation.AUSMobile]"
    :emitRequestOnInput="true"
    @setLoading="setLoadingVerificationRequest"
    @actionRequest="submitMobileNumberValidation"
    :isLoading="isLoading"
    :iconName="inputOptions.iconName"
    :iconColor="inputOptions.iconColor"
    :disabled="disabled"
    :updateRequiredMessage="
      props.updateRequired
        ? 'Unable to update: No changes made to your mobile number.'
        : ''
    "
    :customErrorMessage="inputOptions.errorMessage"
    :hideDetails="props.hideDetails"
  ></GTextField>
</template>

<script setup lang="ts">
import { formatPhoneNumber } from '@/helpers/StringHelpers/StringHelpers';
import {
  validate,
  validationRules,
} from '@/helpers/ValidationHelpers/ValidationHelpers';
import { InputOptions } from '@/interface-models/Generic/InputOptions';
import { Portal } from '@/interface-models/Generic/Portal';
import { Validation } from '@/interface-models/Generic/Validation';
import { sessionManager } from '@/store/session/SessionState';
import { Ref, computed, getCurrentInstance, onMounted, ref } from 'vue';
const emit = defineEmits([
  'input',
  'setIsLoading',
  'setIsAvailable',
  'setExistingDriverMobile',
]);
const instance: any = getCurrentInstance();
// By default this component works around the current sessionManager.getPortalType() type. If this component is utilised in the operations portal the default entity we are working with is the company user, if driver or client person is the working entity we must define this via isClientPerson/isDriver props.
interface IProps {
  disabled?: boolean;
  updateRequired?: boolean;
  isClientPerson?: boolean;
  isDriver?: boolean;
  hideDetails?: boolean;
  value: string;
}
const props = withDefaults(defineProps<IProps>(), {
  disabled: false,
  updateRequired: false,
  isClientPerson: false,
  isDriver: false,
  hideDetails: false,
});

const currentMobileNumber: Ref<string> = ref('');

const mobileNumber = computed({
  get() {
    return formatPhoneNumber(props.value);
  },
  set(value) {
    userHasActionedInput.value = true;
    emit('input', value);
  },
});

const mobileNumberAvailable: Ref<boolean | null> = ref(null);
const userHasActionedInput: Ref<boolean> = ref(false);
const isLoading: Ref<boolean> = ref(false);

/**
 * Initializes the current mobile number state with the value of the `mobileNumber` computed property.
 */
onMounted(() => {
  currentMobileNumber.value = mobileNumber.value;
});

/**
 * Submits the mobile number for validation. Checks if the format is valid and if the number has changed.
 * If invalid or unchanged, no server validation request is made. Otherwise, initiates a verification process
 * based on the current portal context (Operations or User).
 */
function submitMobileNumberValidation(): void {
  setLoadingVerificationRequest(true);
  const isValid: boolean = validate(instance);
  // If the mobile is in an invalid format of the mobile number has not changed, a server validation request is not required.
  if (!isValid || mobileNumberHasNotChanged()) {
    setLoadingVerificationRequest(false);
    setIsAvailable(null);
    return;
  }
  if (sessionManager.getPortalType() === Portal.OPERATIONS) {
    // Three cases currently exist when on the OPERATIONS portal - company users and client persons and drivers
    if (props.isClientPerson) {
      setLoadingVerificationRequest(false);
    } else if (props.isDriver) {
      setLoadingVerificationRequest(false);
    } else {
      setLoadingVerificationRequest(false);
    }
  } else if (sessionManager.getPortalType() === Portal.USER) {
    setLoadingVerificationRequest(false);
  }
}

/**
 * A computed ref that provides validation rules.
 * @returns {Ref<Validation>} A reference to the validation rules.
 */
const validation: Ref<Validation> = computed(() => {
  return validationRules;
});

/**
 * Sets the availability of the mobile number and emits this state to the parent component.
 * @param {boolean | null} isAvailable - The availability state of the mobile number.
 */
function setIsAvailable(isAvailable: boolean | null) {
  mobileNumberAvailable.value = isAvailable;
  emit('setIsAvailable', isAvailable);
}

/**
 * Sets the loading state for the mobile number verification request and emits this state.
 * It checks if the mobile number has changed before setting the loading state.
 * @param {boolean} loading - The loading state to set.
 */

function setLoadingVerificationRequest(loading: boolean): void {
  if (mobileNumberHasNotChanged()) {
    loading = false;
  }
  isLoading.value = loading;
  emit('setIsLoading', loading);
}

/**
 * A computed ref that provides options for the mobile number input field, including icon name, icon color,
 * hint text, and any error message.
 * @returns {Ref<InputOptions>} A reference to the input options, including icon, color, hint, and error message.
 */
const inputOptions: Ref<InputOptions> = computed(() => {
  let iconName = '';
  let iconColor = '';
  let hint =
    sessionManager.getPortalType() === Portal.OPERATIONS
      ? 'Mobile Number'
      : 'The mobile number you will use to access your account.';
  let errorMessage = '';
  if (mobileNumberAvailable.value) {
    iconName = 'fas fa-check';
    iconColor = 'success';
    hint = 'Mobile number available';
  } else if (mobileNumberAvailable.value !== null) {
    iconName = 'fas fa-exclamation-circle';
    iconColor = 'error';
    errorMessage = 'Mobile number already taken';
  }
  return { iconName, iconColor, hint, errorMessage };
});

/**
 * Checks if the current mobile number is different from the initially set mobile number.
 * @returns {boolean} True if the mobile number has not changed, false otherwise.
 */
function mobileNumberHasNotChanged(): boolean {
  return props.value === currentMobileNumber.value;
}

/**
 * Exposes the isLoading ref to the parent component.
 */
defineExpose({
  isLoading,
});
</script>

<style scoped lang="scss">
.number-update-dialog-container {
  h2 {
    line-height: 1rem;
    margin: 0 0 0 12px;
  }

  hr {
    background-color: rgba(255, 255, 255, 0.12);
    color: rgba(255, 255, 255, 0.12);
    border: 1px solid rgba(255, 255, 255, 0.12);
  }

  .input-container {
    width: 100%;
    display: flex;
    align-items: center;

    .tool-tip-container {
      margin: 0 0 27px 8px;
    }
  }
}

.input-container {
  width: 100%;
  display: flex;
  align-items: center;

  .tool-tip-container {
    margin: 0 0 27px 8px;
  }
}

@media (min-width: $user-portal-desktop-breakpoint) {
  .number-update-dialog-container {
    .form-container {
      padding: 12px;
    }
  }
}
</style>
