<template>
  <div
    :id="containerId"
    tabindex="-1"
    style="position: relative; width: 100%; margin-bottom: 8px"
  >
    <input
      :id="inputId"
      class="text-field-input"
      :class="props.outline ? 'outline-input' : ''"
      :type="!showPassword ? props.type : 'text'"
      :value="props.value"
      :disabled="props.disabled"
      :autofocus="props.autofocus"
      @input="onInput"
    />

    <!-- Floating placeholder used so we can apply '*' via content on css pseudo element when input is required. -->
    <span
      v-if="!hideDetails && !props.value"
      @click="focusInput"
      class="floating-placeholder"
      :class="{
        'form-field-required-marker': isRequired,
        'hide-details': hideDetails,
      }"
      >{{ labelName }}</span
    >
    <label class="input-label"
      ><span v-if="!errorMessageText">{{ hint ? hint : labelName }}</span
      ><span class="error-text" v-else>{{ errorMessageText }}</span></label
    >
    <div
      class="append-icon-container"
      v-if="isLoading || iconName || type === 'password'"
    >
      <v-progress-circular
        size="20"
        indeterminate
        color="white"
        v-if="isLoading"
      ></v-progress-circular>
      <v-icon
        class="nav-icon"
        size="18"
        :color="iconColor"
        v-if="!isLoading && iconName"
        >{{ iconName }}</v-icon
      >

      <v-icon
        @click="showPassword = !showPassword"
        class="nav-icon"
        size="18"
        v-if="type === 'password' && !isLoading && !iconName"
        >{{ !showPassword ? 'fas fa-eye' : 'fas fa-eye-slash' }}</v-icon
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { v4 as uuidv4 } from 'uuid';
import { computed, ComputedRef, onMounted, ref, Ref } from 'vue';
const emit = defineEmits(['input', 'onInput', 'setLoading', 'actionRequest']);

interface IProps {
  placeholder: string;
  hideDetails?: boolean;
  label?: string;
  rules?: ((x: any[]) => boolean | string)[];
  hint?: string;
  disabled?: boolean;
  // Sometimes we wish to make an api call on the users input. If this is the case we utilise emitRequestOnInput to handle this via the debounceRequest
  emitRequestOnInput?: boolean;
  // emit onInput event back to parent
  emitOnInput?: boolean;
  iconName?: string;
  iconColor?: string;
  isLoading?: boolean;
  autofocus?: boolean;
  // A custom update required error message for when the input is required to change value
  updateRequiredMessage?: string;
  // If you wish to pass in a custom error message.
  customErrorMessage?: string;
  // if outline is true, the background color within the input will be transparent.
  outline?: boolean;
  type?:
    | 'text'
    | 'password'
    | 'email'
    | 'number'
    | 'tel'
    | 'search'
    | 'url'
    | 'date'
    | 'datetime-local'
    | 'time'
    | 'week'
    | 'month'
    | 'color'
    | 'range'
    | 'file';
  value: any;
}
const props = withDefaults(defineProps<IProps>(), {
  placeholder: '',
  hideDetails: false,
  label: '',
  rules: () => [],
  disabled: false,
  emitRequestOnInput: false,
  emitOnInput: false,
  iconName: '',
  iconColor: '',
  isLoading: false,
  updateRequiredMessage: '',
  customErrorMessage: '',
  outline: false,
  autofocus: false,
  type: 'text',
});

const errorMessage: Ref<string> = ref('');
const containerId: string = uuidv4().replace(/-/g, '');
const inputId: string = uuidv4().replace(/-/g, '');
const debounceTimer: Ref<any> = ref(null);
const originalMountedValue: Ref<string | number | null> = ref(null);

const showPassword: Ref<boolean> = ref(false);

function onInput(e: any) {
  emit('input', e.target.value);
  errorMessage.value = '';
  if (props.emitOnInput) {
    emit('onInput', e.target.value);
  }
  if (props.emitRequestOnInput) {
    emit('setLoading', true);
    debounceRequest();
  }
}

// emit the request back to parent
function debounceRequest() {
  if (debounceTimer.value !== null) {
    clearTimeout(debounceTimer.value);
  }
  debounceTimer.value = setTimeout(() => {
    emit('actionRequest');
  }, 300);
}

// Returns the error message
const errorMessageText: ComputedRef<string> = computed(() => {
  return props.customErrorMessage || errorMessage.value;
});

// validate is required on input components due to how we handle form validation. This function will be called from a parent.
function validate(): boolean {
  const containerEl: HTMLElement | null = document.getElementById(containerId);
  const labelEl = containerEl
    ? containerEl.getElementsByTagName('label')
    : null;

  // remove animation class
  if (labelEl && labelEl.length === 1) {
    labelEl[0].classList.remove('animate-wiggle');
  }
  // iterate over rules and validate.
  let hasErrors: boolean = false;
  for (let i = 0; i < props.rules.length; i++) {
    const isValid: boolean | string = props.rules[i](props.value);
    if (isValid && typeof isValid !== 'string') {
      errorMessage.value = '';
      hasErrors = false;
    } else {
      errorMessage.value = isValid as string;
      hasErrors = true;
      break;
    }
  }
  hasErrors = hasErrors || props.customErrorMessage ? true : false;

  if (
    props.updateRequiredMessage &&
    originalMountedValue.value === props.value
  ) {
    hasErrors = true;
    errorMessage.value = props.updateRequiredMessage;
  }
  // add animation class
  if (labelEl && labelEl.length === 1 && hasErrors) {
    setTimeout(() => {
      labelEl[0].classList.add('animate-wiggle');
    }, 1);
  }
  return !hasErrors;
}

// Returns the label name.
const labelName: ComputedRef<string> = computed(() => {
  return props.placeholder ?? '';
});

// returns boolean on whether the required rules are contained in the rules prop. Utilised to show the red astrix on the input placeholder
const isRequired: ComputedRef<boolean> = computed(() => {
  return (
    props.rules.includes(validationRules.required) ||
    props.rules.includes(validationRules.listRequired)
  );
});

// sets focus on the input.
function focusInput(): void {
  const inputElement: HTMLElement | null = document.getElementById(inputId);
  if (!inputElement) {
    return;
  }
  inputElement.focus();
}

/**
 * If updateRequiredMessage prop is provided we should capture the original value of the input. validation rules will validate against the original value.
 */
function setOriginalMountedValue() {
  if (props.updateRequiredMessage) {
    originalMountedValue.value = JSON.parse(JSON.stringify(props.value));
  }
}

onMounted(() => {
  setOriginalMountedValue();
});

defineExpose({
  validate,
  errorMessage,
});
</script>

<style scoped lang="scss">
.text-field-input {
  height: 48px;
  width: 100%;
  font-size: $font-size-16;
  color: var(--text-color);
  background: var(--background-color-300);
  border: 1px solid var(--border-color);
  padding: 8px 13px 8px 12px;
  border-radius: $border-radius-base;
  margin: 0 0 8px 0px;

  &:focus {
    outline: none !important;
    border: 1.5px solid var(--primary);
  }

  &::placeholder {
    color: var(--light-text-color);
  }

  &:disabled {
    background: var(--background-color-550);
    color: var(--light-text-color);
    border: none;
  }

  &:read-only {
    border: 1px solid transparent;
    background: var(--background-color-550);
    color: var(--light-text-color);
  }
  &.hide-details {
    margin-bottom: 0px;
  }
}

.input-label {
  display: block;
  font-size: $font-size-large;
  transition: 0.2s;
  pointer-events: none;
  color: var(--light-text-color);
  font-size: $font-size-12;
  padding: 0 12px;
  margin: 0 0 0px 0;
  line-height: 1.2;
}

.floating-placeholder {
  position: absolute;
  left: 13px;
  bottom: 33px;
  font-size: $font-size-16;
  color: var(--light-text-color);
  text-wrap: nowrap;
  &.hide-details {
    bottom: 20px;
  }
}

.append-icon-container {
  position: absolute;
  top: 0;
  right: 15px;
  height: 48px;
  display: flex;
  align-items: center;
}

.outline-input {
  background-color: transparent;
}
</style>
