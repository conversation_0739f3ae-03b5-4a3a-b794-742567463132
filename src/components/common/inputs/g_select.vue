<template>
  <div tabindex="-1" :id="containerId" style="width: 100%">
    <!-- {{ inputConfig }} -->
    <input
      :id="inputId"
      class="select-input"
      :value="inputConfig.inputText"
      :disabled="disabled"
      @keydown.down="moveDownList"
      @keydown.up="moveUpList"
      @keydown.esc="setIsOpen(false)"
      @focus="setIsOpen(true)"
      readonly
    />

    <!-- Floating placeholder used so we can apply '*' via content on css pseudo element when input is required. -->
    <div style="position: relative">
      <span
        v-if="!inputConfig.inputText"
        @click="focusInput"
        class="floating-placeholder"
        :class="{
          'form-field-required-marker': isRequired,
          'hide-details': hideDetails,
        }"
        >{{ placeholder }}</span
      >
      <label
        v-if="!props.hideDetails"
        class="input-label"
        :class="[{ 'animate-wiggle': errorMessage }]"
        ><span v-if="!errorMessage">{{ hint ? hint : placeholder }}</span
        ><span class="error-text" v-else>{{ errorMessage }}</span></label
      >
      <!-- Action container holds the icons that are displayed on the right side of the input -->
      <div class="action-container" :class="{ disabled: disabled }">
        <v-tooltip bottom v-if="inputConfig && inputConfig.chipText">
          <template v-slot:activator="{ on }">
            <div v-on="on" class="chip-item mr-1" :id="chipId">
              {{ inputConfig.chipText }}
            </div>
          </template>
          <div
            v-for="(hiddenSelection, index) of inputConfig.hiddenSelections"
            :key="index"
          >
            {{ hiddenSelection }}
          </div>
        </v-tooltip>
        <v-tooltip
          bottom
          v-if="inputConfig && syncedModelValue.length > 0 && props.clearable"
          :disabled="disabled"
        >
          <template v-slot:activator="{ on }">
            <div v-on="on" class="icon-container" @click="clearSelection">
              <v-icon :disabled="disabled" size="18">far fa-times</v-icon>
            </div>
          </template>
          Clear Selection{{ multiple ? 's' : '' }}
        </v-tooltip>
        <div class="icon-container ml-1" @click="setIsOpen(!isOpen)">
          <v-icon
            :color="
              errorMessage
                ? '#ff5252'
                : !sessionManager.isClientPortal()
                  ? '#FFF'
                  : '#000000'
            "
            :class="[{ rotate: isOpen }]"
            size="15"
            >fas fa-caret-down</v-icon
          >
        </div>
      </div>
    </div>
    <!-- Suggestions menu contains the list of selected items and filtered search results based on the searched text -->
    <ul
      class="suggestions-menu"
      :id="suggestionMenuId"
      v-if="isOpen"
      :style="{
        top: menuPosition.y + 'px',
        left: menuPosition.x + 'px',
        width: menuPosition.width + 'px',
      }"
    >
      <li
        v-if="props.items.length === 0"
        style="cursor: default"
        @click="focusInput"
      >
        <span>{{ emptyMessage }}</span>
      </li>
      <li
        v-for="(item, index) in selectItems"
        :key="index"
        class="select-item"
        :class="{
          highlighted: index === currentHighlightedSelectionIndex,
          'disabled-select': props.disabledValues.includes(item.value),
          'selected-item': syncedModelValue.includes(item.value),
        }"
        @click="setSelectedItem(item)"
        v-shortkey="['enter']"
        @shortkey="
          setSelectedItem(selectItems[currentHighlightedSelectionIndex])
        "
      >
        <input
          class="checkbox-input"
          type="checkbox"
          :disabled="props.disabledValues.includes(item.value)"
          :checked="syncedModelValue.includes(item.value)"
          tabindex="-1"
          v-if="multiple"
        />
        <span @click.stop="setSelectedItem(item)" class="suggestion-item">
          {{ item.key }}</span
        >
      </li>
    </ul>
  </div>
</template>
<script setup lang="ts">
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { KeyValue } from '@/interface-models/Generic/KeyValue/KeyValue';
import { sessionManager } from '@/store/session/SessionState';
import { v4 as uuidv4 } from 'uuid';
import {
  computed,
  ComputedRef,
  nextTick,
  onMounted,
  onUnmounted,
  ref,
  Ref,
  WritableComputedRef,
} from 'vue';

const inputId: string = uuidv4().replace(/-/g, '');
const suggestionMenuId: string = uuidv4().replace(/-/g, '');
const containerId: string = uuidv4().replace(/-/g, '');
const chipId: string = uuidv4().replace(/-/g, '');
const validationErrorMessage: Ref<string> = ref('');

const errorMessage: ComputedRef<string> = computed(() => {
  return validationErrorMessage.value || props.customErrorMessage;
});

interface Position {
  x: number;
  y: number;
  width: number;
}

interface InputConfig {
  inputText: string;
  chipText: string;
  hiddenSelections: string[];
}

const isOpen: Ref<boolean> = ref(false);
const currentHighlightedSelectionIndex: Ref<number> = ref(0);
const menuPosition: Ref<Position> = ref({ x: 0, y: 0, width: 0 });

interface IProps {
  items: any[];
  itemText?: string;
  itemValue?: string | number;
  hint?: string;
  hideDetails?: boolean;
  rules?: ((x: any[]) => boolean | string)[];
  placeholder: string;
  customErrorMessage?: string;
  disabled?: boolean;
  clearable?: boolean;
  multiple?: boolean;
  disabledValues?: (string | number | boolean)[];
  customEmptyMessage?: string;
  value: any;
}
const props = withDefaults(defineProps<IProps>(), {
  items: () => [],
  itemText: '',
  itemValue: '',
  hint: '',
  hideDetails: false,
  placeholder: 'Select',
  customErrorMessage: '',
  rules: () => [],
  disabled: false,
  clearable: true,
  multiple: false,
  disabledValues: () => [],
  customEmptyMessage: '',
});
const emit = defineEmits(['input', 'onChange']);

const inputWidth: Ref<number> = ref(0);

onMounted(() => {
  handleInputEventListener(true);
  computePosition();
  window.addEventListener('resize', computePosition);
  window.addEventListener('scroll', computePosition);

  inputWidth.value = getInputWidth();
});

onUnmounted(() => {
  handleInputEventListener(false);
});

/**
 * Resets the input state by clearing the selected values and closing any open input menus.
 */
function clearSelection() {
  syncedModelValue.value = [];
  setIsOpen(false);
}

/**
 * A computed ref that returns a custom empty message if provided, otherwise a default message.
 * @type {ComputedRef<string>}
 */
const emptyMessage: ComputedRef<string> = computed(() => {
  return props.customEmptyMessage
    ? props.customEmptyMessage
    : 'No items available';
});

/**
 * A writable computed ref that synchronizes the model value.
 * It handles getting and setting of selected IDs, and emits changes back to the parent.
 * @type {WritableComputedRef<(string | number | boolean)[]>}
 */
const syncedModelValue: WritableComputedRef<(string | number | boolean)[]> =
  computed({
    get(): string[] {
      if (
        props.value === null ||
        props.value === undefined ||
        props.value === ''
      ) {
        return [];
      }
      return props.multiple ? props.value : [props.value];
    },
    set(selectedIds: string[]): void {
      if (!props.multiple) {
        if (selectedIds.length === 0) {
          emit('input', null);
        } else {
          emit('input', selectedIds[0]);
        }
        // Because we only have one to select we will close the list elements
        setIsOpen(false);
      } else {
        emit('input', selectedIds);
        // We must set focus back onto the input as the selection from the list element removed the focus. We do this so that when the user clicks away from the input the focusout event will be actioned.
        focusInput();
      }
      nextTick(() => {
        validate();
      });
    },
  });

function computePosition() {
  const inputElement: HTMLElement | null = document.getElementById(inputId);
  if (inputElement) {
    const rect = inputElement.getBoundingClientRect();
    menuPosition.value.x = rect.left + window.scrollX; // X-coordinate
    menuPosition.value.y = rect.bottom + window.scrollY - 1; // Y-coordinate, bottom of the input
    // menuPosition.value.x = rect.left - 2;
    // menuPosition.value.y = rect.bottom - 3;
    menuPosition.value.width = rect.width - 2;
  }
}

/**
 * Sets the selected item for the input.
 * If multiple selection is not enabled, it sets the value directly.
 * If multiple selection is enabled, it updates the array of selected values.
 * @param {KeyValue} item - The item to be selected.
 */
function setSelectedItem(item: KeyValue) {
  if (!props.multiple) {
    syncedModelValue.value = [item.value];
  } else {
    const selectedIds: (string | number | boolean)[] = JSON.parse(
      JSON.stringify(syncedModelValue.value),
    );
    const selectedItemIndex = syncedModelValue.value.findIndex(
      (x: string | number | boolean) => x === item.value,
    );
    if (selectedItemIndex >= 0) {
      selectedIds.splice(selectedItemIndex, 1);
    } else {
      selectedIds.push(item.value);
    }
    syncedModelValue.value = selectedIds;
  }
}

/**
 * Handles adding or removing the input focusout event listener.
 * @param {boolean} addListener - Determines whether to add or remove the event listener.
 */
function handleInputEventListener(addListener: boolean): void {
  const inputElement: HTMLElement | null = document.getElementById(inputId);
  if (!inputElement) {
    return;
  }

  if (addListener) {
    // inputElement.addEventListener('focusout', focusoutHandler);
    document.body.addEventListener('focusout', focusoutHandler);
  } else {
    // inputElement.removeEventListener('focusout', focusoutHandler);
    document.body.removeEventListener('focusout', focusoutHandler);
  }
}

/**
 * Callback for the focusout event listener.
 * It handles closing the suggestions list when focus moves outside the component.
 * @param {FocusEvent} event - The focusout event.
 */
function focusoutHandler(event: FocusEvent): void {
  const eventTarget: EventTarget | null = event.relatedTarget;
  if (eventTarget && (eventTarget as HTMLElement).id === containerId) {
    // The selected element inside this component. We don't need to do anything here.
    return;
  }
  // The selected element was outside of this component. We should now close the suggestions list as the user has moved away.
  setIsOpen(false);
}

/**
 * Handles the opening and closing of the suggestions list.
 * It also resets the highlighted selection index and performs validation when closing.
 * @param {boolean} open - Indicates whether to open or close the list.
 */
function setIsOpen(open: boolean) {
  try {
    if (isOpen.value === open) {
      return;
    }
    isOpen.value = open;
    currentHighlightedSelectionIndex.value = 0;
    if (isOpen.value) {
      computePosition();
    }
  } catch (e) {
    console.error(e);
  }
}

/**
 * Moves the highlighted selection up in the suggestions list.
 */
function moveUpList() {
  if (
    currentHighlightedSelectionIndex.value - 1 >
    selectItems.value.length - 1
  ) {
    currentHighlightedSelectionIndex.value = selectItems.value.length - 1;
  } else if (currentHighlightedSelectionIndex.value > 0) {
    currentHighlightedSelectionIndex.value--;
  }
}

/**
 * Moves the highlighted selection down in the suggestions list or opens the list if it's closed.
 */
function moveDownList(): void {
  if (!isOpen.value) {
    currentHighlightedSelectionIndex.value = 0;
    setIsOpen(true);
    return;
  }
  if (currentHighlightedSelectionIndex.value < selectItems.value.length - 1) {
    currentHighlightedSelectionIndex.value++;
  }
}

/**
 * Returns the width of the input component.
 * @return {number} The width of the input element or 0 if the element is not found.
 */
function getInputWidth(): number {
  const inputElement: HTMLElement | null = document.getElementById(inputId);
  return inputElement ? inputElement.getBoundingClientRect().width : 0;
}

/**
 * A computed ref that calculates the configuration for the input display.
 * It determines how selected items are shown and calculates the text for the input chip.
 * @type {ComputedRef<InputConfig>}
 */
const inputConfig: ComputedRef<InputConfig> = computed(() => {
  try {
    // if this is a route select component we don't have the selected Id passed in as a prop. We will get the id from the route param instead
    const selectedIds: (string | number | boolean)[] = props.multiple
      ? props.value
      : [props.value];

    let selected: KeyValue[] = selectItems.value.filter((x: KeyValue) =>
      selectedIds.includes(x.value),
    );
    // we don't wont text overlaping with the input action icons so we should include this width in our calculations.
    const inputActionContainerWidth = 24;
    const inputPaddingSize: number = 12;
    const fullInputWidth = inputWidth.value ? inputWidth.value : 650;
    const inputWidthSize =
      fullInputWidth - inputActionContainerWidth - inputPaddingSize;
    // If we could not find the width of the input element we will show all selected items as a fallback
    if (!inputWidthSize || selected.length === 1) {
      const inputConfig: InputConfig = {
        inputText: JSON.parse(JSON.stringify(selected))
          .map((x: KeyValue) => x.key)
          .join(', '),
        chipText: '',
        hiddenSelections: [],
      };
      return inputConfig;
    }

    // If the number of selected items is longer than the single line input, we will add the chip of "plus x more" to the input. The current width of "plus x more" is 85px. Better solution can be implemented here.
    const plusMoreTextWidth: number = 85;
    let isPlusMore: boolean = false;
    let textToDisplayInInput: string = '';

    // iterator for how many selected items to show before the suffix is applied.
    let numberOfSelectedToShow = 0;
    for (let i = 0; i < selected.length; i++) {
      textToDisplayInInput +=
        (textToDisplayInInput !== '' ? ', ' : '') +
        (selected[i] ? selected[i].key : '');
      const textWidth: number = getTextWidth(textToDisplayInInput);

      if (inputWidthSize > textWidth) {
        numberOfSelectedToShow++;
        continue;
      } else if (inputWidthSize < textWidth + plusMoreTextWidth) {
        isPlusMore = true;
        break;
      } else {
        isPlusMore = true;
        if (numberOfSelectedToShow > 0) {
          numberOfSelectedToShow--;
        }
        break;
      }
    }
    // selected items that will be displayed in the input chip
    const hiddenSelection = JSON.parse(JSON.stringify(selected)).splice(
      numberOfSelectedToShow,
      selected.length - 1,
    );
    const inputConfig: InputConfig = {
      inputText: JSON.parse(JSON.stringify(selected))
        .splice(0, numberOfSelectedToShow)
        .map((x: KeyValue) => x.key)
        .join(', '),
      chipText: isPlusMore
        ? ' + ' + (selected.length - numberOfSelectedToShow) + ' more'
        : '',

      hiddenSelections: hiddenSelection.map((x: KeyValue) => x.key),
    };
    return inputConfig;
  } catch (e) {
    console.error(e);
    return {
      inputText: '',
      chipText: '',
      hiddenSelections: [],
    };
  }
});

/**
 * Calculates the width of the provided text using a canvas element.
 * @param {string} text - The text for which to calculate the width.
 * @return {number} The width of the text in pixels.
 */
function getTextWidth(text: string): number {
  try {
    const canvas: HTMLCanvasElement = document.createElement('canvas');
    const context = canvas.getContext('2d');
    if (!context) {
      return 0;
    }
    context.font = 'normal 16px Roboto';
    const metrics = context.measureText(text);
    canvas.remove();
    return metrics.width;
  } catch (e) {
    console.error(e);
    return 0;
  }
}

/**
 * Transforms the select items (props.items) to a keyValue pair. Utilised props.itemText and props.itemValue to initialise a keyValue
 * @return {KeyValue} KeyValue list that will be the selectable list in our select
 */
const selectItems: ComputedRef<KeyValue[]> = computed(() => {
  const selectItems: KeyValue[] = [];
  for (const item of props.items) {
    // If we are passing in number[] || string[] as items we will set both key and value to item.
    selectItems.push({
      key: props.itemText ? item[props.itemText] : item,
      value: props.itemValue ? item[props.itemValue] : item,
    });
  }

  return selectItems;
});

/**
 * Looks are rules prop and returns boolean on whether the select is required.
 * @return {boolean} boolean on whether the input is required or not.
 */
const isRequired: ComputedRef<boolean> = computed(() => {
  return (
    props.rules.includes(validationRules.required) ||
    props.rules.includes(validationRules.listRequired)
  );
});

/**
 * Validates the input based on prop rules. This method is often called from outside of this components scope.
 * @return {boolean} boolean on Whether validation is valid or not
 */
function validate(): boolean {
  const containerEl: HTMLElement | null = document.getElementById(containerId);
  const labelEl = containerEl
    ? containerEl.getElementsByTagName('label')
    : null;
  // remove animation class
  if (labelEl && labelEl.length === 1) {
    labelEl[0].classList.remove('animate-wiggle');
  }
  // iterate over rules and validate.
  let hasErrors: boolean = false;
  for (let i = 0; i < props.rules.length; i++) {
    const isValid: boolean | string = props.rules[i](props.value);
    if (isValid && typeof isValid !== 'string') {
      validationErrorMessage.value = '';
      hasErrors = false;
    } else {
      validationErrorMessage.value = isValid as string;
      hasErrors = true;

      break;
    }
  }
  hasErrors = hasErrors || props.customErrorMessage ? true : false;
  // add animation class
  if (labelEl && labelEl.length === 1 && hasErrors) {
    setTimeout(() => {
      labelEl[0].classList.add('animate-wiggle');
    }, 1);
  }
  return !hasErrors;
}

/**
 * sets focus on the input.
 * @return {void}
 */
function focusInput(): void {
  const inputElement: HTMLElement | null = document.getElementById(inputId);
  if (!inputElement) {
    return;
  }
  inputElement.focus();
}

/**
 * Expose validate and validationErrorMessage. Utilised when validating forms
 * @return {void}
 */
defineExpose({
  validate,
  validationErrorMessage,
});
</script>
<style scoped lang="scss">
.input-label {
  display: block;
  transition: 0.2s;
  pointer-events: none;
  color: var(--text-color-100);
  font-size: $font-size-12;
  padding: 0 12px;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.select-input {
  height: 48px;
  width: 100%;
  font-size: $font-size-16;
  color: var(--text-color);
  // background: red;
  background-color: var(--background-color-300);
  border: 1px solid var(--border-color);
  padding: 8px 13px 8px 12px;
  border-radius: $border-radius-base;
  margin: 0 0 8px 0px;

  &:focus {
    outline: none !important;
    border: 1px solid var(--primary);
  }

  &::placeholder {
    color: var(--text-color-100);
  }

  &:disabled {
    // background: $app-dark-primary-550 !important;
    border: none;
    background: var(--background-color-500);
    color: var(--text-color-100);
  }

  &.hide-details {
    margin-bottom: 0px;
  }
}

.action-container {
  position: absolute;
  right: 1px;
  top: -55px;
  height: 46px;
  display: flex;
  align-items: center;
  padding-right: 12px;
  background: var(--background-color-300);
  border-radius: $border-radius-base;

  &.disabled {
    background: var(--background-color-500);
    color: var(--text-color-100);

    .icon-container {
      display: none;
      pointer-events: none;
    }
  }
  .icon-container {
    cursor: pointer;
    height: 100%;
    width: 24px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      i {
        color: var(--primary);
      }
    }
  }
}

.floating-placeholder {
  position: absolute;
  left: 13px;
  top: -43px;
  font-size: $font-size-16;
  color: var(--text-color-100);
}

.suggestions-menu {
  position: absolute;
  color: #ffffff;
  z-index: 50000000;
  list-style: none;
  width: fit-content;
  block-size: fit-content;
  background-color: var(--background-color-300);
  color: var(--text-color);
  overflow: hidden;
  overflow-y: scroll;
  max-height: 294px;
  padding: 8px 0;
  box-shadow:
    0 3px 1px -2px rgba(0, 0, 0, 0.2),
    0 2px 2px 0 rgba(0, 0, 0, 0.14),
    0 1px 5px 0 rgba(0, 0, 0, 0.12);

  li {
    height: 42px;
    display: flex;
    align-items: center;
    font-size: $font-size-15;
    padding-left: 12px;
    padding-right: 12px;
    white-space: nowrap;

    span {
      cursor: pointer;
    }
  }

  .select-item {
    cursor: pointer;
    &:hover {
      background-color: $translucent-highlight;
    }
  }
}

.checkbox-input {
  margin-right: 12px;
  accent-color: $highlight;
  pointer-events: none;
}

.rotate {
  transform: rotate(180deg);
  color: $highlight !important;
}

.selected-item {
  background-color: hsla(0, 0%, 33%, 0.3);
  color: var(--primary) !important;
  font-weight: 500;
}

.chip-item {
  height: 30px;
  border-radius: $border-radius-Xlg;
  border: 1px solid $border-color;
  padding: 0 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.disabled-select {
  pointer-events: none !important;
  opacity: 0.4;
}
</style>
