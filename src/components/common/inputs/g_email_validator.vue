<!-- g_email_validator is a wrapper component around our g_text_field component. The goal of this component is to have a reusable way to validate a users email address on an input. Validate in this case means verifying whether the inputed email address exists via a api call. -->
<template>
  <GTextField
    v-model="emailAddress"
    :placeholder="'Email Address'"
    :hint="inputOptions.hint"
    :rules="computedRules"
    :emitRequestOnInput="true"
    @setLoading="setLoadingVerificationRequest"
    @actionRequest="submitEmailValidation"
    :disabled="props.disabled"
    :isLoading="isLoading"
    :iconName="inputOptions.iconName"
    :iconColor="inputOptions.iconColor"
    :updateRequiredMessage="
      props.updateRequired
        ? 'Unable to update: No changes made to your email address.'
        : ''
    "
    :customErrorMessage="!isLoading ? inputOptions.errorMessage : ''"
    :hideDetails="props.hideDetails"
    :autofocus="props.autofocus"
  ></GTextField>
</template>

<script setup lang="ts">
import {
  validate,
  validationRules,
} from '@/helpers/ValidationHelpers/ValidationHelpers';
import CompanyRole from '@/interface-models/Admin/CompanyRole';
import { VerifyDriverWithEmailAddressExistsResponse } from '@/interface-models/Driver/DriverDetails/DriverDetails';
import { InputOptions } from '@/interface-models/Generic/InputOptions';
import { Portal } from '@/interface-models/Generic/Portal';
import { Validation } from '@/interface-models/Generic/Validation';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import { VerifyClientPersonWithEmailAddressExistsResponse } from '@/interface-models/User/ClientPerson';
import { VerifyCompanyUserWithEmailAddressExistsResponse } from '@/interface-models/User/CompanyUserDetails';
import { useWebsocketStore } from '@/store/modules/WebsocketStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import {
  Ref,
  WritableComputedRef,
  computed,
  getCurrentInstance,
  onMounted,
  onUnmounted,
  ref,
} from 'vue';

const instance: any = getCurrentInstance();
const currentEmailAddress: Ref<string> = ref('');
const emailAddressAvailable: Ref<boolean | null> = ref(null);
const emit = defineEmits([
  'input',
  'setIsLoading',
  'setIsAvailable',
  'setExistingClientPerson',
  'setExistingDriver',
]);
const isLoading: Ref<boolean> = ref(false);
interface IProps {
  disabled?: boolean;
  updateRequired?: boolean;
  isClientPerson?: boolean;
  isDriver?: boolean;
  hideDetails?: boolean;
  value: string;
  autofocus?: boolean;
}
const props = withDefaults(defineProps<IProps>(), {
  disabled: false,
  updateRequired: false,
  isClientPerson: false,
  hideDetails: false,
  isDriver: false,
  autofocus: false,
});

/**
 * Initializes the current email address state with the value of the `emailAddress` computed property.
 * This is used to track if the email address value changes over the lifetime of the component.
 */
onMounted(() => {
  currentEmailAddress.value = emailAddress.value;
  listenForClientPersonIdAddedToClientDetails(true);
});

onUnmounted(() => {
  listenForClientPersonIdAddedToClientDetails(false);
});

/**
 * A writable computed property for the email address.
 * It gets the email address from the component's props and emits changes to the parent component.
 *
 * - The getter returns the current value of the email address from props.
 * - The setter updates the email address by emitting an 'input' event to the parent component.
 *
 * @returns {WritableComputedRef<string>} A writable computed reference to the email address.
 */
const emailAddress: WritableComputedRef<string> = computed({
  get(): string {
    return props.value;
  },
  set(value) {
    emit('input', value);
  },
});

/**
 * A computed ref that provides validation rules from the store.
 * @returns {Ref<Validation>} A reference to the validation rules.
 */
const validation: Ref<Validation> = computed(() => {
  return validationRules;
});

/**
 * A computed ref that provides email input validation rules if not disabled.
 * @returns {Ref<[]>} A reference to the email input validation rules.
 */
const computedRules = computed(() => {
  return !props.disabled
    ? [validation.value.required, validation.value.email]
    : [];
});

/**
 * Sets the loading state for the email verification request and emits the state to the parent component.
 * It also checks if the email address has changed before setting the loading state.
 * @param {boolean} loading - The loading state to set.
 */
function setLoadingVerificationRequest(loading: boolean) {
  if (emailAddressHasNotChanged()) {
    loading = false;
  }
  isLoading.value = loading;
  emit('setIsLoading', loading);
}

/**
 * A computed ref that provides options for the input field, including icon name, icon color,
 * hint text, and any error message.
 * @returns {Ref<InputOptions>} A reference to the input options, including icon, color, hint, and error message.
 */
const inputOptions: Ref<InputOptions> = computed(() => {
  let iconName = '';
  let iconColor = '';
  let hint =
    sessionManager.getPortalType() === Portal.OPERATIONS
      ? 'Email Address'
      : 'The email address you will use to access your account.';
  let errorMessage = '';
  if (emailAddressAvailable.value) {
    iconName = 'fas fa-check';
    iconColor = 'success';
    hint = 'Email Address available';
  } else if (emailAddressAvailable.value !== null) {
    iconName = 'fas fa-exclamation-circle';
    iconColor = 'error';
    errorMessage = 'Email address already taken';
  }
  return { iconName, iconColor, hint, errorMessage };
});

/**
 * Sets the availability of the email address and emits the state to the parent component.
 * @param {boolean | null} isAvailable - The availability state of the email address.
 */
function setIsAvailable(isAvailable: boolean | null): void {
  emailAddressAvailable.value = isAvailable;
  emit('setIsAvailable', isAvailable);
}

/**
 * Checks if the current email address is different from the user's existing email address.
 * @returns {boolean} True if the email address has not changed, false otherwise.
 */
function emailAddressHasNotChanged(): boolean {
  return props.value === currentEmailAddress.value;
}

/**
 * Submits the email address for validation. It checks the validity of the email format
 * and whether the email address has changed before making a server request for further validation.
 */
function submitEmailValidation() {
  const isValid: boolean = validate(instance);
  // If the email address is in an invalid format of the email address has not changed, a server validation request is not required.
  if (!isValid || emailAddressHasNotChanged()) {
    setLoadingVerificationRequest(false);
    setIsAvailable(null);
    return;
  }
  setLoadingVerificationRequest(true);
  if (sessionManager.getPortalType() === Portal.OPERATIONS) {
    // Three cases currently exist when on the OPERATIONS portal - company users and client persons and drivers
    if (props.isClientPerson) {
      handleOperationsPortalClientPersonVerification();
    } else if (props.isDriver) {
      handleOperationsPortalDriverVerification();
    } else {
      handleOperationsPortalCompanyUserVerification();
    }
  } else if (sessionManager.getPortalType() === Portal.USER) {
    handleUserPortalVerification();
  } else if (sessionManager.getPortalType() === Portal.CLIENT) {
    handleClientPortalClientPersonVerification();
  }
}

/**
 * Handles the email verification request for a company user on the Operations portal.
 * Sends a WebSocket request and listens for a response event.
 */
function handleOperationsPortalCompanyUserVerification(): void {
  useWebsocketStore().sendWebsocketRequest(
    new WebSocketRequest(
      '/companyUserDetails/verifyEmailExists',
      props.value,
      false,
    ),
  );
  Mitt.on(
    'verifiedCompanyUserWithEmailAddress',
    (
      verifiedEmailAddressExists: VerifyCompanyUserWithEmailAddressExistsResponse,
    ) => {
      setIsAvailable(!verifiedEmailAddressExists.emailExists);
      Mitt.off('verifiedCompanyUserWithEmailAddress');
      setLoadingVerificationRequest(false);
    },
  );
}

/**
 * Handles the email verification request for a client person on the Operations portal.
 * Sends a WebSocket request and listens for a response event.
 */
function handleOperationsPortalClientPersonVerification() {
  useWebsocketStore().sendWebsocketRequest(
    new WebSocketRequest(
      '/clientDetails/clientPersonDispatchers/verifyEmailExists',
      props.value,
      false,
    ),
  );
  Mitt.on(
    'verifiedClientPersonWithEmailAddress',
    (
      verifiedEmailAddressExists: VerifyClientPersonWithEmailAddressExistsResponse,
    ) => {
      setIsAvailable(!verifiedEmailAddressExists.emailExists);
      // If the client person does exist, we will emit this client person back up to parent. We do this so that the parent can kick off a "did you mean" process.
      if (verifiedEmailAddressExists.emailExists) {
        emit('setExistingClientPerson', verifiedEmailAddressExists);
      }

      Mitt.off('verifiedCompanyUserWithEmailAddress');
      setLoadingVerificationRequest(false);
    },
  );
}

/**
 * Handles the email verification request for a client person on the Operations portal.
 * Sends a WebSocket request and listens for a response event.
 */
function handleClientPortalClientPersonVerification() {
  useWebsocketStore().sendWebsocketRequest(
    new WebSocketRequest(
      `/user/${sessionManager.getUserName()}/clientDetails/clientPersonDispatchers/verifyEmailExists`,
      props.value,
      false,
    ),
  );
  Mitt.on(
    'verifiedClientPersonWithEmailAddressFromClientPortal',
    (
      verifiedEmailAddressExists: VerifyClientPersonWithEmailAddressExistsResponse,
    ) => {
      setIsAvailable(!verifiedEmailAddressExists.emailExists);
      // If the client person does exist, we will emit this client person back up to parent. We do this so that the parent can kick off a "did you mean" process.
      if (verifiedEmailAddressExists.emailExists) {
        emit('setExistingClientPerson', verifiedEmailAddressExists);
      }

      Mitt.off('verifiedClientPersonWithEmailAddressFromClientPortal');
      setLoadingVerificationRequest(false);
    },
  );
}

/**
 * Handles the verification request for a driver's email address on the Operations portal.
 * Sends a WebSocket request and listens for the verification response event.
 */
function handleOperationsPortalDriverVerification(): void {
  useWebsocketStore().sendWebsocketRequest(
    new WebSocketRequest(
      '/driverDetails/verifyEmailExists',
      props.value,
      false,
    ),
  );
  Mitt.on(
    'verifiedDriverWithEmailAddress',
    (response: VerifyDriverWithEmailAddressExistsResponse) => {
      setIsAvailable(!response.emailExists);

      emit(
        'setExistingDriver',
        response.emailExists ? response.existingDriverDetails : null,
      );
      Mitt.off('verifiedDriverWithEmailAddress');
      setLoadingVerificationRequest(false);
    },
  );
}

/**
 * Handles the email verification request for a user on the User portal.
 * Sends a WebSocket request and listens for a response event.
 */
async function handleUserPortalVerification() {
  useWebsocketStore().sendWebsocketRequest(
    new WebSocketRequest(
      '/authUserDetails/verifyEmailExists',
      emailAddress.value,
      false,
    ),
  );
  Mitt.on(
    'verifiedEmailAddressExists',
    (verifiedEmailAddressExists: boolean) => {
      setIsAvailable(!verifiedEmailAddressExists);
      Mitt.off('verifiedCompanyUserWithEmailAddress');
      setLoadingVerificationRequest(false);
    },
  );
}

function listenForClientPersonIdAddedToClientDetails(addListener: boolean) {
  if (!addListener) {
    Mitt.off('clientPersonIdAddedToClientDetails');
    return;
  }

  Mitt.on(
    'clientPersonIdAddedToClientDetails',
    (companyRole: CompanyRole | null) => {
      emailAddressAvailable.value = null;
    },
  );
}

/**
 * Exposes the isLoading ref to the parent component.
 */
defineExpose({
  isLoading,
});
</script>

<style scoped lang="scss"></style>
