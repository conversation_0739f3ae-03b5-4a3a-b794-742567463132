<template>
  <section class="connection-status-container">
    <div
      v-if="!isClientPortal"
      :class="[
        { statusDisconnected: !connectionStatus },
        { statusConnected: connectionStatus },
      ]"
    >
      <v-menu left v-if="connectionStatus">
        <template v-slot:activator="{ on: menu }">
          <v-tooltip bottom>
            <template v-slot:activator="{ on: tooltip }">
              <v-btn flat icon v-on="{ ...tooltip, ...menu }" class="ma-0 pa-0">
                <v-icon size="18">far fa-bell </v-icon>
              </v-btn>
            </template>
            <span>View Recent Notifications</span>
          </v-tooltip>
        </template>
        <v-list class="v-list-custom" three-line>
          <v-subheader style="height: 30px">
            <v-layout align-start>
              <span class="pt-1">Recent Notifications</span>
              <v-spacer></v-spacer>
              <v-btn
                flat
                small
                hide-details
                class="ma-0 pa-0"
                @click="clearNotificationList"
                >Clear All</v-btn
              >
            </v-layout>
          </v-subheader>
          <v-divider class="mb-2"></v-divider>
          <v-flex
            v-if="recentNotificationList && recentNotificationList.length"
          >
            <v-list-tile
              style="max-width: 30vw; min-width: 30vw"
              v-for="notification in recentNotificationList"
              :key="notification.id"
            >
              <v-list-tile-avatar>
                <v-icon size="18" :color="notification.iconColor">
                  {{ notification.icon }}
                </v-icon>
              </v-list-tile-avatar>
              <v-list-tile-content>
                <v-list-tile-title>
                  <v-layout
                    ><span>{{ notification.title }}</span>
                    <v-spacer></v-spacer>
                    <span style="font-size: 12px">
                      {{ returnFormattedTime(notification.epochTime) }}
                    </span>
                  </v-layout>
                </v-list-tile-title>
                <v-list-tile-sub-title>
                  <span style="font-size: 14px">{{ notification.text }}</span>
                </v-list-tile-sub-title>
              </v-list-tile-content>
            </v-list-tile>
          </v-flex>
          <v-list-tile
            v-if="!recentNotificationList || !recentNotificationList.length"
            style="max-width: 30vw; min-width: 30vw"
          >
            <v-layout justify-center>
              <h6 class="subheader--faded font-italic">
                No recent notifications
              </h6>
            </v-layout>
          </v-list-tile>
        </v-list>
      </v-menu>
    </div>

    <WsDisconnectionDialog
      v-if="showConnectionErrorDialog && $route.name !== 'login_index'"
      :showConnectionErrorDialog="showConnectionErrorDialog"
    />
  </section>
</template>

<script setup lang="ts">
import WsDisconnectionDialog from '@/components/common/ws_disconnection_dialog/index.vue';
import Environment from '@/configuration/environment';
import { returnFormattedTime } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { useAppNavigationStore } from '@/store/modules/AppNavigationStore';
import { useAuthenticationStore } from '@/store/modules/AuthenticationStore';
import { useNetworkingStore } from '@/store/modules/NetworkingStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { useRootStore } from '@/store/modules/RootStore';
import { useWebsocketStore } from '@/store/modules/WebsocketStore';
import { sessionManager } from '@/store/session/SessionState';
import {
  computed,
  ComputedRef,
  onMounted,
  onUnmounted,
  ref,
  Ref,
  watch,
} from 'vue';
import { useRoute } from 'vue-router/composables';

const appNavigationStore = useAppNavigationStore();
const networkingStore = useNetworkingStore();
const isClientPortal = sessionManager.isClientPortal();

const connectionStatus: Ref<boolean> = ref(false);

const checkIdleUserInterval = ref<ReturnType<typeof setInterval> | null>(null);
const mouseIdleTimeInMinutes: Ref<number> = ref(0);

const route = useRoute();

const stomp: ComputedRef<number | null> = computed(() => {
  return useWebsocketStore().websocket?.ws.readyState ?? null;
});

const showConnectionErrorDialog: ComputedRef<boolean> = computed(() => {
  return networkingStore.showConnectionErrorDialog;
});

// Return the list of recent notifications from appNavigationStore. Add
// properties for icon type and color to be used in HTML to render list tile
// avatar
const recentNotificationList = computed(() =>
  appNavigationStore.recentNotifications.map((n) => ({
    ...n,
    icon: returnIconType(n.type),
    iconColor: returnIconColor(n.type),
  })),
);

// Reset list of notifications in store to be empty list
function clearNotificationList(): void {
  appNavigationStore.setNotificationList([]);
}

// Return a color to be used as list tile icon color in HTML, depending on the
// type of notification
function returnIconColor(notificationType: HealthLevel): string {
  switch (notificationType) {
    case HealthLevel.ERROR:
      return 'red';
    case HealthLevel.WARNING:
      return 'amber';
    case HealthLevel.SUCCESS:
      return 'green accent-3';
    case HealthLevel.INFO:
      return 'blue';
    default:
      return 'white';
  }
}
// Return a string to be used as the type of icon being displayed for each
// notification, depending on the notification type
function returnIconType(notificationType: HealthLevel): string {
  switch (notificationType) {
    case HealthLevel.ERROR:
      return 'fas fa-exclamation-triangle';
    case HealthLevel.WARNING:
      return 'fas fa-exclamation-circle';
    case HealthLevel.SUCCESS:
      return 'fas fa-check-circle';
    case HealthLevel.INFO:
      return 'fas fa-info-circle';
    default:
      return 'white';
  }
}

watch(stomp, (val: any) => {
  if (val === 1) {
    connectionStatus.value = true;
    networkingStore.setWebsocketIsDown(false);
  } else {
    connectionStatus.value = false;
  }

  if (val === 3 && route.name !== 'login_index') {
    networkingStore.setWebsocketIsDown(true);
    useRootStore().setDataLoadedFalse();
    useRootStore().resetDivisionSpecificData();
  }
});

function internetDown() {
  networkingStore.setInternetIsDown(true);
}

function internetUp() {
  networkingStore.setInternetIsDown(false);
}

function resetMouseIdleTime() {
  mouseIdleTimeInMinutes.value = 0;
}

// Gets and sets windowHasFocus property in Networking Module
const windowHasFocus = computed({
  get: () => networkingStore.windowHasFocus,
  set: (value: boolean) => {
    networkingStore.setWindowHasFocus(value);
  },
});

// Callback for event listener on document visibility change
function documentFocusChanged() {
  if (document.hidden) {
    windowHasFocus.value = false;
  } else {
    windowHasFocus.value = true;
  }
}

// Sets a timer to check for user inactivity, with a callback once per minute. On each callback,
// the mouseIdleTimeInMinutes counter is incremented by 1. If the counter reaches the maximum allowed idle time,
// the user is disconnected. This logic is paired with a mousemove event that resets the counter to 0 when there
// is mouse movement.
function setUserIdleInterval() {
  const ENV: string | undefined = Environment.value('environment');
  const allowedIdleTimeDurationInMinutes: number =
    ENV && ENV === 'local' ? 30 : isClientPortal ? 720 : 15;
  // Set the interval for checking user inactivity
  checkIdleUserInterval.value = setInterval(() => {
    // If the user is not authenticated, exit.
    if (sessionManager.getSecurityLevel() === '') {
      return;
    }
    // Increment the mouse idle time by 1 minute
    mouseIdleTimeInMinutes.value++;
    // If the user has been idle for too long, disconnect the user and show a notification
    if (mouseIdleTimeInMinutes.value >= allowedIdleTimeDurationInMinutes) {
      useOperationsStore().closeAllPopoutWindows();
      useAuthenticationStore().disconnectWebsocket();
      showNotification('Disconnected due to inactivity.', {
        title: 'Disconnected',
        duration: -1,
      });
    }
  }, 60000);
}

onMounted(() => {
  setUserIdleInterval();
  window.addEventListener('online', internetUp);
  window.addEventListener('offline', internetDown);
  window.addEventListener('mousemove', resetMouseIdleTime);
  documentFocusChanged();
  document.addEventListener('visibilitychange', documentFocusChanged);

  connectionStatus.value = useWebsocketStore().websocket?.ws.readyState === 1;
});

onUnmounted(() => {
  window.removeEventListener('online', internetUp);
  window.removeEventListener('offline', internetDown);
  window.removeEventListener('mousemove', resetMouseIdleTime);
  document.removeEventListener('visibilitychange', documentFocusChanged);
  // clear the idle user interval when destroyed
  if (checkIdleUserInterval.value) {
    clearInterval(checkIdleUserInterval.value);
  }
});
</script>

<style scoped lang="scss">
.connection-status-container {
  display: flex;
  justify-content: center;
  align-items: center;

  //   position: absolute;
  right: 0px;

  // .status {
  //   height: 14px;
  //   width: 14px;
  //   background-color: green;
  //   border-radius: 11px;
  // }

  // .statusConnected {
  //   position: absolute;
  //   left: 50%;
  //   top: 50%;
  //   transform: translateX(-50%) translateY(-50%);
  //   width: 12px;
  //   height: 12px;

  //   &:before {
  //     content: '';
  //     position: relative;
  //     display: block;
  //     width: 200%;
  //     height: 200%;
  //     box-sizing: border-box;
  //     margin-left: -50%;
  //     margin-top: -50%;
  //     border-radius: 45px;
  //     background-color: #38ef7d;
  //     animation: pulse-ring 1.25s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
  //   }

  //   &:after {
  //     content: '';
  //     position: absolute;
  //     left: 0;
  //     top: 0;
  //     display: block;
  //     width: 100%;
  //     height: 100%;
  //     background: linear-gradient(to right, #18ca5c, #17c926);
  //     border-radius: 15px;
  //     box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
  //     animation: pulse-dot 1.25s cubic-bezier(0.455, 0.03, 0.515, 0.955) -0.4s infinite;
  //   }
  // }

  .statusDisconnected {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    width: 12px;
    height: 12px;

    // &:before {
    //   content: '';
    //   position: relative;
    //   display: block;
    //   width: 200%;
    //   height: 200%;
    //   box-sizing: border-box;
    //   margin-left: -50%;
    //   margin-top: -50%;
    //   border-radius: 45px;
    //   background-color: #38ef7d;
    //   animation: pulse-ring 1.25s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
    // }

    // &:after {
    //   content: '';
    //   position: absolute;
    //   left: 0;
    //   top: 0;
    //   display: block;
    //   width: 100%;
    //   height: 100%;
    //   // background: #11998e;

    //   background: linear-gradient(to right, #d80d0d, #d80d0d);

    //   border-radius: 15px;
    //   box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
    //   animation: pulse-dot 1.25s cubic-bezier(0.455, 0.03, 0.515, 0.955) -0.4s infinite;
    // }
  }

  // @keyframes pulse-ring {
  //   0% {
  //     transform: scale(0.33);
  //   }

  //   80%,
  //   100% {
  //     opacity: 0;
  //   }
  // }

  // @keyframes pulse-dot {
  //   0% {
  //     transform: scale(0.8);
  //   }

  //   50% {
  //     transform: scale(1);
  //   }

  //   100% {
  //     transform: scale(0.8);
  //   }
  // }
}
</style>
