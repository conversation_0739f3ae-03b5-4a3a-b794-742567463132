<template>
  <section class="date-time-display">
    <div class="date-time-container">
      <span class="time">{{ time }}</span>
      <span class="date">{{ date }}</span>
    </div>
  </section>
</template>

<script setup lang="ts">
import { Ref, ref, onMounted, onUnmounted } from 'vue';
import { returnFormattedCurrentDateAndTime } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { useGpsStore } from '@/store/modules/GpsStore';
import Mitt from '@/utils/mitt';

const gpsStore = useGpsStore();

const date: Ref<string> = ref('');
const time: Ref<string> = ref('');

let timeInterval: ReturnType<typeof setInterval>;
let perMinuteTimer: ReturnType<typeof setInterval>;
let per30SecondTimer: ReturnType<typeof setInterval>;

/**
 * Sets the current date and time using the helper function.
 */
function setDateTime(): void {
  const dateTime: [string, string] = returnFormattedCurrentDateAndTime();
  date.value = dateTime[0];
  time.value = dateTime[1];
}

onMounted(() => {
  setDateTime();
  timeInterval = setInterval(setDateTime, 1000);

  perMinuteTimer = setInterval(() => {
    Mitt.emit('perMinuteTrigger', true);
  }, 60000);

  per30SecondTimer = setInterval(() => {
    gpsStore.processGpsQueue();
  }, 30000);
});

onUnmounted(() => {
  clearInterval(timeInterval);
  clearInterval(perMinuteTimer);
  clearInterval(per30SecondTimer);
});
</script>
<style scoped lang="scss">
.date-time-container {
  display: flex;
  flex-direction: column;
  align-items: center;

  $text-color: rgb(255, 255, 255);
  color: #e77700;

  .date {
    font-size: $font-size-10;
    line-height: 1;
    color: $text-color;
  }
  .time {
    font-size: $font-size-13;
    line-height: 1;
    color: $text-color;
    font-family: $sub-font-family;
  }
}
</style>
