<template>
  <v-layout
    justify-space-between
    align-center
    style="height: 28px"
    class="task-bar app-theme__center-content--header no-highlight"
    v-if="selectedTab !== null"
  >
    <div class="navigation-tabs">
      <v-tabs
        height="26"
        v-model="selectedTabController"
        :active-class="'tab-active'"
        hide-slider
        class="tab-select-container"
      >
        <v-tab
          class="tab-selector"
          v-for="(header, headerIndex) of dialogHeaders"
          :key="headerIndex"
        >
          <p class="ma-0 tab-text">{{ header.title }}</p>
        </v-tab>
      </v-tabs>
    </div>

    <v-spacer></v-spacer>

    <div class="app-theme__center-content--closebutton" @click="closeDialog">
      <v-icon class="app-theme__center-content--closebutton--icon"
        >fal fa-times</v-icon
      >
    </div>
  </v-layout>
</template>

<script setup lang="ts">
import DialogHeader from '@/interface-models/Generic/Dialog/DialogHeader';
import { computed, onMounted, ref, Ref, WritableComputedRef } from 'vue';

const props = withDefaults(
  defineProps<{
    dialogHeaders?: DialogHeader[];
  }>(),
  {
    dialogHeaders: () => [],
  },
);
const selectedTab: Ref<string | null> = ref(null);

const emit = defineEmits(['closeDialog', 'tabItemSelected']);

function closeDialog(): void {
  emit('closeDialog');
}

const selectedTabController: WritableComputedRef<string | null> = computed({
  get(): string | null {
    return selectedTab.value;
  },
  set(value: string | null): void {
    selectedTab.value = value;
    emit('tabItemSelected', value);
  },
});

onMounted(() => {
  selectedTab.value = props.dialogHeaders[0].id;
});
</script>

<style scoped lang="scss">
.tab-selector {
  background-color: var(--background-color-400);
}

.navigation-tabs {
  position: absolute;
  left: 0;
  top: 0;
}
</style>
