import StatusSelect from '@/components/common/status_select.vue';
import SubcontractorActiveAssociations from '@/components/common/subcontractor_active_associations/index.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import {
  GENERIC_SUCCESS_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import { SubcontractorAssociationUpdateRequest } from '@/interface-models/FleetAssetOwner/SubcontractorAssociation/SubcontractorAssociationUpdateRequest';
import { SubcontractorAssociationUpdateType } from '@/interface-models/FleetAssetOwner/SubcontractorAssociation/SubcontractorAssociationUpdateType';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { Validation } from '@/interface-models/Generic/Validation';
import { SubcontractorEntityType } from '@/interface-models/InvoiceAdjustment/EntityTypes/SubcontractorEntityType';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
@Component({
  components: {
    StatusSelect,
    SubcontractorActiveAssociations,
    ContentDialog,
  },
})
export default class SubcontractorUpdateOperationalStatus
  extends Vue
  implements IUserAuthority
{
  @Prop() public entityId: string;
  @Prop() public csrAssignedId: string;
  @Prop() public isFleetAssetActive: boolean;
  @Prop() public entityType: SubcontractorEntityType;
  @Prop({ default: false }) public isEdited: boolean;
  @Prop({ default: () => [] }) public statusList: number[];

  public isLoading: boolean = true;
  public canActionAssociationUpdate: boolean = false;
  public dialogIsActive: boolean = false;
  public isAwaitingSaveResponse: boolean = false;
  public editedStatusList: number[] = [];

  public entityIsActive: boolean = false;

  public isAuthorised(): boolean {
    return hasAdminOrHeadOfficeRole();
  }

  get validate(): Validation {
    return validationRules;
  }

  public editOperationalStatus(): void {
    this.dialogIsActive = true;
  }

  public closeDialog(): void {
    this.dialogIsActive = false;
  }

  // Because the dialog is always active we require the resetting of the selects when the dialog is opened.
  @Watch('dialogIsActive')
  public dialogActiveStateUpdate(value: boolean): void {
    if (value) {
      this.setInitialState();
    }
  }

  get alertMessagesAreActive(): boolean {
    if (this.isLoading) {
      return false;
    }
    return !this.statusList.includes(13) && this.entityIsActive;
  }

  get alertMessageList() {
    const alertMessages: string[] = [];

    if (this.entityType !== SubcontractorEntityType.DRIVER) {
      alertMessages.push('Retire all active devices.');
    }
    if (this.entityType === SubcontractorEntityType.DRIVER) {
      alertMessages.push(
        'Remove this driver from all associated fleet assets.',
      );
    }
    if (this.entityType === SubcontractorEntityType.FLEET_ASSET_OWNER) {
      alertMessages.push('Active Fleet Assets must be set to DO NOT USE.');
    }
    alertMessages.push(
      `Progress all active jobs through to RCTI LIVE RUN for this ${this.entityTypeName}.`,
    );
    alertMessages.push(
      `Cancel all pending jobs associated with this ${this.entityTypeName}.`,
    );
    alertMessages.push(
      `Cancel all permanent jobs associated with this ${this.entityTypeName}.`,
    );
    alertMessages.push(
      'Try again when there are no remaining active associations.',
    );
    return alertMessages;
  }

  /**
   * Called when pressing confirm on the dialog. This will send the request to
   * update the operational status of the entity.
   */
  public async saveOperationalStatusUpdate() {
    if (!this.entityId || !this.entityType) {
      return;
    }
    // Construct request
    const request: SubcontractorAssociationUpdateRequest = {
      driverId:
        this.entityType === SubcontractorEntityType.DRIVER
          ? this.entityId
          : null,
      ownerId:
        this.entityType === SubcontractorEntityType.FLEET_ASSET_OWNER
          ? this.entityId
          : null,
      fleetAssetId:
        this.entityType === SubcontractorEntityType.FLEET_ASSET
          ? this.entityId
          : null,
      entityType: this.entityType,
      type: SubcontractorAssociationUpdateType.STATUS_UPDATE,
      statusList: this.editedStatusList,
    };
    this.isAwaitingSaveResponse = true;
    // Send request and show notification if it failed
    const response =
      await useFleetAssetOwnerStore().updateSubcontractorEntityAssociation(
        request,
      );
    const entityTypeFormatted = this.entityType
      .replace(/_/g, ' ')
      .toLowerCase();
    if (!response) {
      showNotification('Something went wrong.', {
        title: `Status Update (${entityTypeFormatted})`,
        type: HealthLevel.ERROR,
      });
      this.closeDialog();
      return;
    }
    showNotification(GENERIC_SUCCESS_MESSAGE, {
      title: `Status Update (${entityTypeFormatted})`,
      type: HealthLevel.SUCCESS,
    });
    this.isAwaitingSaveResponse = false;
    this.closeDialog();
  }

  public mounted(): void {
    this.setInitialState();
  }

  // set initial selects and set loading to false if no requests are to be made.
  public setInitialState() {
    this.editedStatusList = [...this.statusList];
    if (!this.alertMessagesAreActive) {
      this.isLoading = false;
    }
  }

  get dialogTitle(): string {
    return 'Active Status Management';
  }

  get entityTypeName(): string {
    let entityTypeName = 'subcontractor';
    if (this.entityType === SubcontractorEntityType.DRIVER) {
      entityTypeName = 'driver';
    } else if (this.entityType === SubcontractorEntityType.FLEET_ASSET) {
      entityTypeName = 'fleet asset';
    }
    return entityTypeName;
  }

  get saveIsDisabled(): boolean {
    if (!this.isAuthorised()) {
      return true;
    }

    if (!this.canActionAssociationUpdate) {
      return true;
    }

    return false;
  }

  /**
   * Helper function to validate the assigned Fleet CSR ID.
   * Ensures that the entered ID is unique among active fleet assets.
   * @returns {boolean | string} - Returns `true` if valid, otherwise returns an error message.
   */
  get validateCsrId(): boolean {
    if (this.entityType === SubcontractorEntityType.FLEET_ASSET) {
      // Fetch the list of fleet assets
      const fleetAssets = useFleetAssetStore().getAllFleetAssetList;

      // Check for active fleet assets (statusList includes 4 for ACTIVE)
      const activeFleetAssets = fleetAssets.filter(
        (asset: FleetAssetSummary) => asset.isActive,
      );

      // Check if the entered ID already exists among active fleet assets
      const isDuplicate = activeFleetAssets.some(
        (asset: FleetAssetSummary) =>
          asset.csrAssignedId === this.csrAssignedId,
      );

      // Return validation message or true (valid)
      return isDuplicate ? false : true;
    } else {
      return true;
    }
  }
}
