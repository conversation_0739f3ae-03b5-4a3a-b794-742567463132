<div>
  <v-layout>
    <div>
      <v-layout class="pl" align-center style="height: 54px">
        <v-tooltip bottom>
          <template v-slot:activator="{ on }">
            <v-btn
              icon
              flat
              @click="editOperationalStatus"
              v-on="on"
              :disabled="isEdited || !isAuthorised()"
            >
              <v-icon size="18" color="orange">far fa-edit</v-icon>
            </v-btn>
          </template>
          <span> Edit Active Status</span>
        </v-tooltip>
      </v-layout>
    </div>
  </v-layout>
  <ContentDialog
    v-if="dialogIsActive"
    :showDialog.sync="dialogIsActive"
    :title="'Update Active Status'"
    :isConfirmUnsaved="false"
    :width="'850px'"
    :confirmBtnText="'save'"
    @confirm="saveOperationalStatusUpdate"
    :isLoading="false"
    @cancel="closeDialog"
    :isDisabled="saveIsDisabled"
    :contentPadding="'pa-1'"
  >
    <v-form>
      <div class="px-2">
        <v-layout wrap>
          <v-flex md12 v-if="alertMessagesAreActive">
            <v-alert
              type="warning"
              v-if="!canActionAssociationUpdate"
              :value="true"
              class="v-alert-main"
            >
              <strong class="pr-1">Warning:</strong>This {{entityTypeName}} is
              linked to active jobs or assets, so cannot be closed. To proceed
              with closing this account, you must:
              <ul class="pt-1">
                <li v-for="(alertMessage, index) of alertMessageList">
                  {{alertMessage}}
                </li>
              </ul>
            </v-alert>
            <v-alert
              type="success"
              v-if="canActionAssociationUpdate"
              :value="true"
              class="v-alert-main"
            >
              This client is not currently linked to any active or permanent
              jobs. If you are sure you wish to continue, you may proceed
              safely.
            </v-alert>
          </v-flex>

          <v-flex md12 pt-3>
            <SubcontractorActiveAssociations
              :entityId="entityId"
              :entityType="entityType"
              :canActionAssociationUpdate.sync="canActionAssociationUpdate"
              :isSubcontractorOperationalStatusUpdate="true"
              :entityIsActive.sync="entityIsActive"
            />
            <v-divider class="my-3"></v-divider>
          </v-flex>
        </v-layout>

        <v-layout>
          <span
            v-if="!validateCsrId && !isFleetAssetActive"
            class="errorMessage"
            >Fleet ID is already in use. To reactivate this vehicle, please
            update to another identifier.</span
          >
        </v-layout>
        <v-layout class="pt-3">
          <v-flex md12>
            <v-layout>
              <v-flex md3>
                <v-layout align-center class="form-field-label-container">
                  <span
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                    >Active Status</span
                  >
                </v-layout>
              </v-flex>
              <v-flex md9>
                <StatusSelect
                  key="entityId"
                  label="Secondary Status"
                  :statusCategory="8"
                  :boxInput="false"
                  :soloInput="true"
                  :statusList="editedStatusList"
                  :resetSelectedSecondaryStatus="false"
                  :formDisabled="!canActionAssociationUpdate || (!validateCsrId && !isFleetAssetActive)"
                >
                </StatusSelect>
              </v-flex>
            </v-layout>
          </v-flex>
        </v-layout>
      </div>
    </v-form>
  </ContentDialog>

  <!-- <v-dialog
    v-if="dialogIsActive"
    v-model="dialogIsActive"
    content-class="v-dialog-custom"
    width="850px"
    persistent
  >
    <v-layout
      justify-space-between
      class="task-bar app-theme__center-content--header no-highlight"
    >
      <span>{{dialogTitle}}</span>
      <div>
        <div
          class="app-theme__center-content--closebutton"
          @click="closeDialog"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </div>
    </v-layout>
    <v-layout row wrap class="app-theme__center-content--body dialog-content">
      <v-flex md12>
        <v-layout row wrap class="body-scrollable--65" pa-3>
          <v-form>
            <div class="pa-2">
              <v-layout wrap>
                <v-flex md12 v-if="alertMessagesAreActive">
                  <v-alert
                    type="warning"
                    v-if="!canActionAssociationUpdate"
                    :value="true"
                  >
                    <strong class="pr-1">Warning:</strong>This
                    {{entityTypeName}} is linked to active or permanent jobs, so
                    cannot be closed. To proceed with closing this account, you
                    must:
                    <ul class="pt-1">
                      <li v-for="(alertMessage, index) of alertMessageList">
                        {{alertMessage}}
                      </li>
                    </ul>
                  </v-alert>
                  <v-alert
                    type="success"
                    v-if="canActionAssociationUpdate"
                    :value="true"
                  >
                    This client is not currently linked to any active or
                    permanent jobs. If you are sure you wish to continue, you
                    may proceed safely.
                  </v-alert>
                </v-flex>

                <v-flex md12>
                  <SubcontractorActiveAssociations
                    :entityId="entityId"
                    :entityType="entityType"
                    :canActionAssociationUpdate.sync="canActionAssociationUpdate"
                    :isSubcontractorOperationalStatusUpdate="true"
                    :entityIsActive.sync="entityIsActive"
                  />
                  <v-divider class="my-3"></v-divider>
                </v-flex>
              </v-layout>

              <v-layout class="pt-3">
                <v-flex md12>
                  <v-layout>
                    <v-flex md2>
                      <v-layout align-center class="form-field-label-container">
                        <span
                          class="subheader--faded pr-3 pb-0 form-field-required-marker"
                          >Active Status:</span
                        >
                      </v-layout>
                    </v-flex>
                    <v-flex md10>
                      <StatusSelect
                        key="entityId"
                        label="Secondary Status"
                        :statusCategory="8"
                        :boxInput="false"
                        :soloInput="true"
                        :statusList="editedStatusList"
                        :resetSelectedSecondaryStatus="false"
                        :formDisabled="!canActionAssociationUpdate"
                      >
                      </StatusSelect>
                    </v-flex>
                  </v-layout>
                </v-flex>
              </v-layout>
            </div>
            <v-divider class="mt-2"></v-divider>
            <v-layout justify-space-between>
              <v-btn color="error" depressed outline @click="closeDialog">
                <span>cancel</span>
              </v-btn>
              <div>
                <v-btn
                  @click="saveOperationalStatusUpdate"
                  :disabled="saveIsDisabled"
                  color="primary"
                >
                  save
                </v-btn>
              </div>
            </v-layout>
          </v-form>
        </v-layout>
      </v-flex>
    </v-layout>
  </v-dialog> -->
</div>
