import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import ChatMessage from '@/interface-models/Generic/ChatConversation/ChatMessage';
import { ChatMessageType } from '@/interface-models/Generic/ChatConversation/ChatMessageType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useDriverMessageStore } from '@/store/modules/DriverMessageStore';
import { sessionManager } from '@/store/session/SessionState';
import moment from 'moment-timezone';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component({
  components: {
    ContentDialog,
  },
})
export default class DriverConversationTextfield extends Vue {
  @Prop({ default: () => [] }) public messageList: ChatMessage[];
  @Prop() public driverId: string;
  @Prop({ default: null }) public jobId: number | null;

  public companyDetailsStore = useCompanyDetailsStore();

  public messageContent: string = '';

  public $refs!: {
    messageInput: HTMLElement;
  };

  /**
   * Blurs the chat input element element.
   */
  public blurInput(): void {
    this.$refs.messageInput.blur();
    this.messageContent = '';
  }

  /**
   * Sends a message from the textfield component. It first checks if the
   * message content is empty, if so, it returns. Then it iterates over the
   * message list and updates any unactioned messages that don't require action
   * and have no receiver ID. It then creates a new chat message record,
   * populates it with the necessary information, and sends it. Finally, it
   * resets the message content.
   */
  /**
   * This function sends a message if the content is not empty, updates unactioned messages, and resets the message content.
   */
  public sendMessage(): void {
    if (!this.messageContent) {
      return;
    }
    // Iterate over the existing chat messages and mark any unactioned messages
    // as actioned
    this.messageList.forEach((message: ChatMessage) => {
      const noReceiver = !message.receiverId;
      if (!message.isActioned && !message.actionRequired && noReceiver) {
        message.isActioned = true;
        message.jobId = message.jobId === 0 ? null : message.jobId;
        useDriverMessageStore().updateChatMessage(message);
      }
    });
    // Construct ChatMessage object
    const chatRecord: ChatMessage = new ChatMessage();
    chatRecord.jobId = this.jobId || null;
    Object.assign(chatRecord, {
      content: this.messageContent,
      company: sessionManager.getCompanyId(),
      division: sessionManager.getDivisionId(),
      senderName: sessionManager.getActiveUser(),
      timestamp: moment.tz(this.companyDetailsStore.userLocale).valueOf(),
      senderId: sessionManager.getUserId(),
      messageType: ChatMessageType.TEXT,
      receiverId: this.driverId,
      isActioned: true,
    });
    // Send message and reset textfield value
    useDriverMessageStore().sendChatMessage(chatRecord);
    this.messageContent = '';
  }
}
