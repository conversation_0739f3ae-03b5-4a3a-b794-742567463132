<v-dialog
  v-model="dialogIsOpen"
  persistent
  :width="500"
  class="ma-0"
  content-class="v-dialog-custom"
>
  <v-layout
    justify-space-between
    align-center
    class="task-bar app-theme__center-content--header no-highlight"
  >
    <span>Data Import</span>
    <div class="app-theme__center-content--closebutton" @click="closeDialog">
      <v-icon class="app-theme__center-content--closebutton--icon"
        >fal fa-times</v-icon
      >
    </div>
  </v-layout>
  <v-layout class="app-theme__center-content--body dialog-content" row wrap>
    <v-flex md12>
      <v-form ref="form">
        <v-layout
          v-if="isLoading"
          style="height: 283px; width:100%"
          justify-center
          column
          align-center
        >
          <v-progress-circular indeterminate class="mb-2"></v-progress-circular>
        </v-layout>
        <v-layout wrap v-if="!isLoading">
          <v-flex md12 class="pa-3">
            <v-alert type="warning" :value="true" class="mb-3">
              <span
                >Please note that unassigned points that were generated from a
                previous upload will not be processed twice.</span
              >
            </v-alert>
            <v-autocomplete
              v-model="clientId"
              :items="clientSelectList"
              item-value="clientId"
              item-text="clientSearchCriteria"
              color="orange"
              label="Client Select"
              persistent-hint
              solo
              class="v-solo-custom"
              flat
              hint="Please select the client for this import."
              :rules="[validate.required]"
              browser-autocomplete="off"
              auto-select-first
            >
              <template slot="selection" slot-scope="data">
                <span>{{ data.item.clientDisplayName }}</span>
              </template>
              <template slot="item" slot-scope="data">
                <span>{{ data.item.clientDisplayName }} </span>
              </template>
            </v-autocomplete>
          </v-flex>

          <v-flex md12 class="mt-3 mb-2">
            <v-layout column align-center>
              <div
                @click="pickFile"
                class="upload-container"
                style="cursor: pointer;"
              >
                <div class="upload-selection">
                  <span class="py-2 hint-text">ATTACHMENT</span>
                  <v-icon
                    class="pt-1"
                    size="50"
                    :color="csvBase64String === null ? 'blue-grey lighten-3' : 'teal accent-3'"
                  >
                    fas fa-file-spreadsheet
                  </v-icon>
                </div>

                <div class="upload-actions-container">
                  <v-layout align-center v-if="csvBase64String" class="py-1">
                    <v-icon
                      size="13"
                      @click="clearSelectedAttachment"
                      color="red"
                      class="pr-1"
                      >fal fa-times</v-icon
                    >
                    <span class="hint-text">{{fileName}}</span>
                  </v-layout>
                </div>
              </div>
              <input
                type="file"
                ref="csvUpload"
                id="fileInput"
                style="display: none"
                @change="saveAttachments()"
              />
            </v-layout>
          </v-flex>

          <v-flex md12 class="pt-3">
            <v-divider></v-divider>
            <v-layout justify-space-between>
              <v-btn @click="closeDialog" depressed outline color="error"
                >cancel</v-btn
              >
              <v-btn
                @click="sendImportRequest"
                depressed
                :disabled="!csvBase64String || !clientId || !isAuthorised"
                color="info"
                >Import</v-btn
              >
            </v-layout>
          </v-flex>
        </v-layout>
      </v-form>
    </v-flex>
  </v-layout>
</v-dialog>
