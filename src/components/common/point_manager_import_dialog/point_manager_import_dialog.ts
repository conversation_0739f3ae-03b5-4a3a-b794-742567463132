import { hasAdminRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import ClientSearchSummary from '@/interface-models/Client/ClientDetails/ClientSearchSummary';
import { VForm } from '@/interface-models/Generic/VForm/VForm';
import { Validation } from '@/interface-models/Generic/Validation';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useDataImportStore } from '@/store/modules/DataImportStore';
import { sessionManager } from '@/store/session/SessionState';
import { Component, Prop, Vue } from 'vue-property-decorator';

interface DataImportRequest {
  company: string;
  division: string;
  clientId: string;
  transformationType: string | null;
  data: string;
}

@Component({
  components: {},
})
export default class PointManagerImportDialog
  extends Vue
  implements IUserAuthority
{
  @Prop() public pointManagerDialogIsOpen: boolean;

  public dataImportStore = useDataImportStore();

  public csvBase64String: string | null = null;
  public mineType: string | null = null;
  public fileName: string | null = null;
  public isLoading: boolean = false;

  public $refs!: {
    form: VForm;
    csvUpload: any;
  };
  public clientId: string | null = null;
  public isAuthorised(): boolean {
    return hasAdminRole();
  }

  get dialogIsOpen() {
    return this.pointManagerDialogIsOpen;
  }

  set dialogIsOpen(value: boolean) {
    this.dataImportStore.setPointManagerImportDialogIsOpen(value);
  }

  get validate(): Validation {
    return validationRules;
  }

  public closeDialog() {
    this.clearSelectedAttachment();
    this.clientId = null;
    this.dialogIsOpen = false;
  }

  get clientSelectList(): ClientSearchSummary[] {
    const allClients = useClientDetailsStore().clientSummaryList;
    return allClients.filter(
      (x: any) =>
        !x.statusList.includes(13) &&
        x.importTransformationType &&
        x.importTransformationType !== 'HUB_SYSTEMS',
    );
  }

  public pickFile() {
    const fileInput: HTMLElement | null = document.getElementById('fileInput')!;
    fileInput.click();
  }

  public getBase64(file: any) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        resolve(reader.result);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  public saveAttachments() {
    const fileUpload: any = this.$refs.csvUpload;
    const file = fileUpload.files[0];
    const promise = this.getBase64(file);
    promise.then((result) => {
      const base64: any = result;
      this.csvBase64String = base64;
      this.mineType = file.type;
      this.fileName = file.name;
    });
  }

  public clearSelectedAttachment() {
    this.csvBase64String = null;
    this.mineType = null;
    this.fileName = null;
  }

  public async sendImportRequest() {
    if (
      !this.$refs.form.validate() ||
      !this.clientId ||
      !this.csvBase64String
    ) {
      return;
    }
    this.isLoading = true;

    const foundClient: ClientSearchSummary | undefined =
      this.clientSelectList.find((client) => client.clientId === this.clientId);
    const dataImportRequest: DataImportRequest = {
      company: sessionManager.getCompanyId(),
      division: sessionManager.getDivisionId(),
      transformationType:
        foundClient && foundClient.importTransformationType
          ? foundClient.importTransformationType
          : null,
      clientId: this.clientId,
      data: this.csvBase64String,
    };

    await useDataImportStore().sendImportRequest(dataImportRequest);
    this.closeDialog();
  }
}
