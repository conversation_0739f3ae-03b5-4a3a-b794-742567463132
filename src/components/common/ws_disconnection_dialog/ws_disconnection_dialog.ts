import {
  SESSION_STORAGE_MULTI_TOKEN,
  SESSION_STORAGE_TOKEN,
} from '@/helpers/AppLayoutHelpers/LocalStorageHelpers';
import { authenticateUser } from '@/helpers/AuthenticationHelpers/LoginHelpers';
import OperationsPortalLoadedApplication from '@/interface-models/Generic/LoadedApplication/OperationsPortalLoadedApplication';
import { useNetworkingStore } from '@/store/modules/NetworkingStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { useRootStore } from '@/store/modules/RootStore';
import { useWebsocketStore } from '@/store/modules/WebsocketStore';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  components: {},
})
export default class WsDisconnectionDialog extends Vue {
  @Prop() public showConnectionErrorDialog: boolean;

  public networkingStore = useNetworkingStore();

  // ===========================================================================
  // Navigation and View
  // ===========================================================================
  get showDialog() {
    return this.showConnectionErrorDialog;
  }
  set showDialog(value: boolean) {
    this.networkingStore.setShowConnectionErrorDialog(value);
  }

  get reconnectionAttempts() {
    return this.networkingStore.reconnectionAttempts;
  }

  get internetIsDown() {
    return this.networkingStore.internetIsDown;
  }

  get internetWasDown() {
    return this.networkingStore.internetWasDown;
  }

  get percentageLoaded() {
    const data: OperationsPortalLoadedApplication =
      useRootStore().operationsPortalLoadedData;
    const dataLength: number = Object.keys(data).length;

    const hasLoaded: any = Object.values(data).reduce(
      (a: number, item: any) => a + item,
      0,
    );
    return Math.trunc((hasLoaded / dataLength) * 100);
  }

  public forceRefresh() {
    window.location.reload();
    return false;
  }

  public async reconnect() {
    let authenticationRequired = true;
    // check if template route; if true don't log user in.
    if (
      [
        'forgot_password',
        'template_view',
        'job_list',
        'template_view',
        'fleet_tracking',
      ].includes(this.$route.name ? this.$route.name : '')
    ) {
      authenticationRequired = false;
    }
    if (authenticationRequired) {
      const multiToken = sessionStorage.getItem(SESSION_STORAGE_MULTI_TOKEN);
      if (multiToken !== null) {
        useWebsocketStore().setMultiAuthToken(multiToken);
      }
      const token = sessionStorage.getItem(SESSION_STORAGE_TOKEN);
      if (token !== null) {
        const headers = { accessToken: token };
        authenticateUser(headers);
      }
    }
    useOperationsStore().closeAllPopoutWindows();
  }
}
