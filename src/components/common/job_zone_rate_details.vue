<template>
  <section class="job-zone-rate-details">
    <v-layout row wrap class="expansion-job-item pa-2 mb-3">
      <v-flex md12>
        <v-layout justify-space-between align-center px-2>
          <span class="subheader">Zone Rate - Select Pickup/Dropoff Zones</span>
          <v-btn
            depressed
            class="edit-btn"
            small
            @click="dialogController = true"
          >
            <v-icon size="16" class="pr-2">edit</v-icon>
            Edit
          </v-btn>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-divider></v-divider>
      </v-flex>
      <v-flex md12 class="pud-status-list-item">
        <v-layout px-2 pt-3>
          <span class="pud-status-list-item__header header-type">Stop</span>
          <v-spacer></v-spacer>
          <v-flex md2 style="text-align: right">
            <span class="pud-status-list-item__header header-type">
              Selected Zone
            </span>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex
        md12
        class="pud-status-list-item app-borderside--t app-bordercolor--600"
        v-for="(pud, pudIndex) of zoneRateInformation"
        :key="pud.pudId || pud.uniqueId"
      >
        <v-layout px-2 align-center>
          <span class="pr-2">{{ pudIndex + 1 }}. </span>
          <span class="pr-1">{{ pud.suburbName ? pud.suburbName : '-' }}</span>
          <span>({{ pud.legTypeFlag }})</span>
          <v-spacer> </v-spacer>

          <v-flex md2 style="text-align: right">
            <span v-if="returnZoneNameFromId(pud.zoneReference)">
              {{ returnZoneNameFromId(pud.zoneReference) }}
            </span>
            <span v-else class="accent-text--card error-type"> Required </span>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
    <v-dialog
      v-model="dialogController"
      width="40%"
      class="ma-0"
      persistent
      no-click-animation
      content-class="v-dialog-custom"
    >
      <v-flex md12>
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight"
        >
          <span>Edit Job Zone Rate Details</span>
          <div
            class="app-theme__center-content--closebutton"
            @click="dialogController = false"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>
        <v-layout
          row
          wrap
          class="app-theme__center-content--body dialog-content"
        >
          <v-flex md12>
            <v-form>
              <v-layout
                row
                wrap
                class="body-scrollable--70 body-min-height--60"
                pa-3
              >
                <v-flex md12>
                  <v-layout pb-2>
                    <v-flex md4 style="text-align: left" class="px-2"
                      ><h5 class="subheader--bold">Stop</h5></v-flex
                    >
                    <v-flex
                      md3
                      style="text-align: center"
                      class="app-borderside--l app-bordercolor--600"
                      ><h5 class="subheader--bold">Type</h5></v-flex
                    >
                    <v-flex
                      md4
                      style="text-align: center"
                      class="app-borderside--l app-bordercolor--600"
                      ><h5 class="subheader--bold">Selected Zone</h5></v-flex
                    >
                  </v-layout>
                  <v-form ref="jobZoneRateDetailsForm">
                    <v-layout
                      row
                      wrap
                      align-center
                      v-for="(pud, pudIndex) of currentlyEditingZoneInformation"
                      :key="pud.pudId || pud.uniqueId"
                    >
                      <v-flex md12 v-if="pudIndex === 0">
                        <v-divider></v-divider>
                      </v-flex>
                      <v-flex md5>
                        <v-layout>
                          <span class="subheader"
                            ><span class="accent-text--primary"
                              >{{ pudIndex + 1 }}.</span
                            >
                            {{ pud.suburbName ? pud.suburbName : '-' }}</span
                          >
                        </v-layout>
                      </v-flex>
                      <v-layout md3 class="pud-type">
                        <div :class="pud.legTypeFlag"></div>
                        {{ pud.legTypeFlag === 'P' ? 'Pickup' : 'Dropoff' }}
                      </v-layout>
                      <v-flex md4>
                        <ZoneRateBooking
                          :zones="zones"
                          :soloInput="true"
                          :detailedView="true"
                          :selectedZone.sync="pud.zoneReference"
                          :validationRules="validationRules"
                        />
                      </v-flex>
                    </v-layout>
                  </v-form>
                </v-flex>
              </v-layout>
            </v-form>
          </v-flex>
          <v-flex md12>
            <v-divider></v-divider>
            <v-layout align-center>
              <v-btn flat color="red" @click="dialogController = false"
                >Cancel</v-btn
              >
              <v-spacer></v-spacer>
              <v-btn
                outline
                class="v-btn-confirm-custom"
                color="white"
                @click="clearLocalChanges"
                >Clear</v-btn
              >
              <v-btn
                depressed
                color="blue"
                @click="updateZoneRateInformation"
                class="v-btn-confirm-custom"
                >Save</v-btn
              >
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-dialog>
  </section>
</template>

<script setup lang="ts">
import ZoneRateBooking from '@/components/operations/BookJob/service_rates_booking/zone_rate_booking/index.vue';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { ZoneRatePudSummary } from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRatePudSummary';
import { ZoneRateType } from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRateType';
import { computed, ref, Ref, WritableComputedRef } from 'vue';

const props = withDefaults(
  defineProps<{
    zones?: ZoneRateType[];
    zoneRateInformation?: ZoneRatePudSummary[];
  }>(),
  {
    zones: () => [],
    zoneRateInformation: () => [],
  },
);

const emit = defineEmits<{
  (event: 'updateZoneRateInformation', payload: ZoneRatePudSummary[]): void;
}>();

const currentlyEditingZoneInformation: Ref<ZoneRatePudSummary[] | null> =
  ref(null);
const isViewingDialog: Ref<boolean> = ref(false);
const jobZoneRateDetailsForm: Ref<any> = ref(null);

// Controls dialog visibility, and resets local working variables on close
const dialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return isViewingDialog.value;
  },
  set(value: boolean): void {
    if (value) {
      // Copy current unitRateInformation to local copy for editing in dialog view
      currentlyEditingZoneInformation.value = JSON.parse(
        JSON.stringify(props.zoneRateInformation),
      );
    } else {
      // Reset local copy
      currentlyEditingZoneInformation.value = null;
    }
    isViewingDialog.value = value;
  },
});

// Copy current zoneRateInformation to local copy for editing in dialog view
function clearLocalChanges() {
  currentlyEditingZoneInformation.value = JSON.parse(
    JSON.stringify(props.zoneRateInformation),
  );
}

// Emit current
function updateZoneRateInformation() {
  const result = jobZoneRateDetailsForm.value.validate();
  if (!result) {
    return;
  }
  if (currentlyEditingZoneInformation.value) {
    emit('updateZoneRateInformation', currentlyEditingZoneInformation.value);
  }
  dialogController.value = false;
}

function returnZoneNameFromId(zoneId: number | null) {
  if (zoneId === null) {
    return '';
  }
  const foundZone = props.zones.find((z) => z.zone === zoneId);
  return foundZone ? foundZone.zoneName : '';
}
</script>

<style lang="scss" scoped>
.job-zone-rate-details {
  position: relative;
  .subheader {
    color: $bg-light;
    font-size: $font-size-18;
    font-weight: 600;
  }

  .expansion-job-item {
    background-color: var(--background-color-300);
    border-radius: $border-radius-base;
    .edit-btn {
      background-color: $accent !important;
      color: $app-dark-primary-200;
      border-radius: $border-radius-lg;
    }
    .pud-status-list-item {
      padding: 8px;
      font-size: $font-size-14;
      font-weight: 500;
      text-transform: uppercase;

      .pud-status-list-item__header {
        color: var(--light-text-color);
        text-transform: uppercase;
        font-size: $font-size-14;
        font-weight: 600;
        &.header-type {
          font-size: $font-size-14;
          font-weight: 700;
          color: var(--primary);
        }
      }
    }
  }
}

.subheader {
  color: var(--text-color);
  font-size: $font-size-17;
  text-align: center;
  font-weight: 600;
  // margin-top: 12px;
}

.pud-type {
  color: var(--light-text-color);
  font-size: $font-size-16;
  .P {
    background-color: $pickup;
    padding-right: 6px;
    margin-right: 6px;
    border-radius: 100px;
  }
  .D {
    background-color: $drop;
    padding-right: 6px;
    margin-right: 6px;
    border-radius: 100px;
  }
}
</style>
