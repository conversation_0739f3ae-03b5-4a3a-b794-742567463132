import AddressSuburbSearch from '@/components/common/addressing/address-search-au/address_suburb_search.vue';
import {
  returnFormattedAddressFromAddressAU,
  roundToDecimalPlaces,
} from '@/helpers/DistanceHelpers/DistanceHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import {
  countries,
  Country,
} from '@/interface-models/Generic/Addressing/Country';
import AddressAU from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/AddressAU';
import SuburbAU from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/SuburbAU';
import { RouteResponse } from '@/interface-models/Generic/RouteResponse';
import { Validation } from '@/interface-models/Generic/Validation';
import { useAddressingStore } from '@/store/modules/AddressingStore';
import mapboxgl, { Map as MapboxMap } from 'mapbox-gl';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  components: {
    AddressSuburbSearch,
  },
})
export default class PinDropCoordinates extends Vue {
  @Prop() public mapCenter: [number, number]; // init center of the map
  @Prop() public address: AddressAU;
  @Prop({ default: false }) public soloInput: boolean;
  @Prop({ default: false }) public flatInput: boolean;
  @Prop({ default: false }) public isDisabled: boolean;
  public currentBearing: number = 0;
  // The map renderer object
  public mapboxObject: MapboxMap;
  public driverIdMarkerHash: Map<string, any> = new Map();

  public mouseLat: number = 0;
  public mouseLng: number = 0;
  public selectedLat: number = 0;
  public selectedLng: number = 0;
  public pin: any;
  public mouseHandler: any;
  public returnedAddressSearchList: any = [];
  public selectedAddress: any = {};
  public countries: Country[] = countries;
  public enterAddress: boolean = false;

  public $refs!: {
    pinManualAddressRef: any;
  };

  get validate(): Validation {
    return validationRules;
  }

  // Return the selected country so that we can display states list in HTML, and
  // so that we can perform country specific validation
  get selectedCountry(): Country | undefined {
    if (!this.address || !this.address.country) {
      return;
    }
    return countries.find((c) => c.name === this.address.country);
  }

  public async onPinDrop(e: any) {
    this.pin.remove();
    this.pin = new mapboxgl.Marker({
      draggable: false,
    })
      .setLngLat([e.lngLat.lng, e.lngLat.lat])
      .addTo(this.mapboxObject);
    if (isNaN(e.lngLat.lng) || isNaN(e.lngLat.lat)) {
      showNotification('Please select a valid address.');
      return;
    }
    this.selectedLat = roundToDecimalPlaces(e.lngLat.lat, 10);
    this.selectedLng = roundToDecimalPlaces(e.lngLat.lng, 10);

    const result: RouteResponse | null =
      await useAddressingStore().getReverseGeocode({
        latitude: e.lngLat.lat,
        longitude: e.lngLat.lng,
      });
    if (result) {
      // NOTE: Is there a cleaner solution?
      this.returnedAddressSearchList = (result.response as any).features;
    }
  }

  public enterAddressManually() {
    if (this.returnedAddressSearchList.length > 0) {
      const addressInfo = this.returnedAddressSearchList[0].properties;
      this.address.addressId = '';
      this.address.formattedAddress = addressInfo.label;
      this.address.addressLine1 = addressInfo.name;
      this.address.addressLine2 = '';
      this.address.addressLine3 = '';
      this.address.addressLine4 = '';
      this.address.suburb = addressInfo.locality;
      this.address.postcode = addressInfo.postalcode;
      this.address.city = addressInfo.locality;
      this.address.state = addressInfo.region_a;
      this.address.country = addressInfo.country;
      this.address.geoLocation = [this.selectedLng, this.selectedLat];
      this.address.formattedAddress = returnFormattedAddressFromAddressAU(
        this.address,
      );
    }
    this.$emit('enterManualAddress');
  }

  public selectAddress() {
    const addressInfo = this.selectedAddress.properties;
    this.address.addressId = '';
    this.address.formattedAddress = addressInfo.label;
    this.address.addressLine1 = addressInfo.name;
    this.address.addressLine2 = '';
    this.address.addressLine3 = '';
    this.address.addressLine4 = '';
    this.address.suburb = addressInfo.locality;
    this.address.postcode = addressInfo.postalcode;
    this.address.city = addressInfo.locality;
    this.address.state = addressInfo.region_a;
    this.address.country = addressInfo.country;
    this.address.geoLocation = [this.selectedLng, this.selectedLat];
    this.address.formattedAddress = returnFormattedAddressFromAddressAU(
      this.address,
    );
    this.$emit('locationSelected');
  }

  public cancelManualAddress() {
    this.enterAddress = false;
    this.$emit('locationSelected');
  }

  public saveManualAddress() {
    if (this.$refs.pinManualAddressRef.validate()) {
      this.enterAddress = false;
      this.address.formattedAddress = returnFormattedAddressFromAddressAU(
        this.address,
      );

      this.$emit('locationSelected');
    }
  }

  public onSuburbSelected(suburb: SuburbAU): void {
    if (suburb) {
      if (this.enterAddress) {
        this.address.state = suburb.state;
        this.address.postcode = suburb.postcode;
        this.address.geoLocation = suburb.geoLocation;
      }
    }
  }

  public mounted() {
    mapboxgl.baseApiUrl = 'https://maps.gode.st/';
    this.mapboxObject = new mapboxgl.Map({
      container: 'pin-drop-map',
      center:
        this.mapCenter === null ? [153.0070585, -27.5553614] : this.mapCenter,
      zoom: 12,
      style: './maps/goDesta.json',
      pitch: 55,
    });

    this.pin = new mapboxgl.Marker({
      draggable: false,
    })
      .setLngLat([this.mouseLng, this.mouseLat])
      .addTo(this.mapboxObject);

    this.mapboxObject.on('click', this.onPinDrop);
    // this.mapboxObject.on('mousemove', this.mouseMoved);
  }
}
