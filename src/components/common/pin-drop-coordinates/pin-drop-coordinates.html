<section>
  <v-layout v-if="!enterAddress">
    <v-select
      v-model="selectedAddress"
      @change="selectAddress"
      :label="returnedAddressSearchList.length === 0 ? 'Please Drop A Pin On The Map' : 'Select Address'"
      :box="!soloInput"
      :solo="soloInput"
      :flat="soloInput"
      :disabled="isDisabled"
      :items="returnedAddressSearchList"
      item-text="properties.label"
      hint="Once a pin is dropped on the map a list of known address will populate the select above"
      return-object
      persistent-hint
      outline
      class="v-solo-custom"
    >
      <v-list-tile
        slot="prepend-item"
        :disabled="selectedLat === 0 || selectedLng === 0"
        @click="enterAddressManually"
      >
        <v-list-tile-content>
          <v-list-tile-title
            ><strong
              >Use pins location and enter address details manually</strong
            ></v-list-tile-title
          >
        </v-list-tile-content>
      </v-list-tile>
    </v-select>
  </v-layout>
  <v-layout>
    <div v-show="!enterAddress" id="pin-drop-map"></div>
  </v-layout>
</section>
