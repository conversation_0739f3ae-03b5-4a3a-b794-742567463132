<template>
  <div class="job-progress-alert-history">
    <TableTitleHeader>
      <!-- Title slot -->
      <template #title>
        <GTitle
          title="Job Alert History"
          subtitle="View all recent Job Alerts"
          :divider="false"
        />
      </template>

      <template #inputs>
        <v-text-field
          hide-details
          v-model="search"
          class="v-solo-custom"
          solo
          flat
          color="light-blue"
          placeholder="Filter Results..."
          :disabled="isAwaitingResponse"
        />
      </template>
    </TableTitleHeader>

    <v-layout py-3>
      <v-flex md12>
        <table class="simple-data-table" v-if="!isAwaitingResponse">
          <thead>
            <tr>
              <th>Date</th>
              <th>Time</th>
              <th>Job #</th>
              <th>Location</th>
              <th>Fleet #</th>
              <th>Driver</th>
              <th>Contact</th>
              <th>Message</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in filteredResults" :key="item._id">
              <td>{{ item.date }}</td>
              <td>{{ item.time }}</td>
              <td>{{ item.displayId }}</td>
              <td>{{ item.locationName || '-' }}</td>
              <td>{{ item.csrAssignedId }}</td>
              <td>{{ item.driverName }}</td>
              <td>{{ item.driverMobile }}</td>
              <td>{{ item.alertMessage }}</td>
            </tr>
          </tbody>
        </table>
        <v-layout justify-center class="pa-4" v-else>
          <img
            src="@/static/loader/infinity-loader-light.svg"
            height="80px"
            width="80px"
          />
        </v-layout>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import TableTitleHeader from '@/components/common/ui-elements/table_title_header.vue';
import {
  returnEndOfDayFromEpoch,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  JobProgressAlertDetails,
  transformJobProgressAlerts,
} from '@/helpers/JobProgressAlertHelpers/JobProgressAlertHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import JobProgressAlertHistoryRequest from '@/interface-models/Jobs/JobProgressAlert/JobProgressAlertHistoryRequest';
import { useDriverMessageStore } from '@/store/modules/DriverMessageStore';
import { computed, ComputedRef, onMounted, ref, Ref } from 'vue';

/**
 * Props
 */
const props = withDefaults(
  defineProps<{
    driverId?: string;
  }>(),
  {
    driverId: '',
  },
);

const search: Ref<string> = ref('');
const jobProgressAlertList: Ref<JobProgressAlertDetails[]> = ref([]);
const isAwaitingResponse: Ref<boolean> = ref(false);

/**
 * Dispatch request with start and end range as today.
 * Gets job progress alert history and sets to jobProgressAlertList.
 */
async function requestJobProgressAlertHistory(): Promise<void> {
  const request: JobProgressAlertHistoryRequest = {
    startEpoch: returnStartOfDayFromEpoch(),
    endEpoch: returnEndOfDayFromEpoch(),
  };
  isAwaitingResponse.value = true;
  const results =
    await useDriverMessageStore().searchJobProgressAlertHistory(request);
  if (results?.length) {
    jobProgressAlertList.value = transformJobProgressAlerts(results);
  } else {
    showNotification(GENERIC_ERROR_MESSAGE);
  }
  isAwaitingResponse.value = false;
  scrollToBottom();
}

/**
 * Filter results based on search string value.
 * This array is used in the v-for loop in the template.
 */
const filteredResults: ComputedRef<JobProgressAlertDetails[]> = computed(() => {
  const notFiltered: JobProgressAlertDetails[] = JSON.parse(
    JSON.stringify(jobProgressAlertList.value),
  );
  return notFiltered.filter((x: JobProgressAlertDetails) => {
    const content = x.alertMessage.toLowerCase();
    const found = content.includes(search.value.toLowerCase());
    return found;
  });
});

/**
 * Scroll to bottom once data response has been received and rendered.
 */
function scrollToBottom(): void {
  setTimeout(() => {
    const element = document.getElementById(
      'job-progress-alert-scrollable-section',
    );
    if (element) {
      element.scrollTop = element.scrollHeight;
    }
  }, 50);
}

onMounted(() => {
  requestJobProgressAlertHistory();
});
</script>
<style scoped lang="scss">
.job-progress-alert-history {
  width: 100%;
  height: 100%;
  position: relative;

  .message-contents__row {
    position: relative;
    padding: 4px 0px;
    margin: 1px 0px;

    .chat-bubble__container {
      position: relative;
      width: 100%;

      .chat-bubble__time {
        color: #dcdce2;
        padding-left: 14px;
        font-weight: 500;
      }
      .chat-bubble {
        max-width: 90%;
        color: #acacb8;
        font-weight: 400;
        padding-left: 8px;
      }
    }
  }

  .table-content {
    overflow: hidden;
    position: relative;

    &.restrict-height {
      .table-scrollable {
        height: calc(600px);
        max-height: calc(600px);
        overflow-y: scroll;
        padding: 8px;
      }
    }
  }
}
</style>
