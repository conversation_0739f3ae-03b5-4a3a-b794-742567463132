<template>
  <section class="date-time-textfields">
    <v-layout row v-if="boxInput">
      <v-flex :md6="type === 'DATE_AND_TIME'" :md12="type !== 'DATE_AND_TIME'">
        <v-text-field
          v-model="dateInput"
          :disabled="readOnly"
          :hint="dateHintText"
          :mask="dateMask"
          :persistent-hint="!readOnly || persistentHint"
          :rules="
            enableValidation
              ? [
                  isRequired ? validationRules.required : true,
                  testDateFormat,
                  type !== 'DATE_AND_TIME' ? checkMaxEpoch : true,
                  checkMaxEpoch,
                ]
              : []
          "
          :class="isRequired ? 'form-field-required' : ''"
          :placeholder="datePlaceholder"
          :label="dateLabel ? dateLabel : 'Date'"
          @focus="$event.target.select()"
          @blur="checkForSemiCorrectDateFormat"
          :autofocus="autofocusDate"
          :id="uniqueKey ? `${uniqueKey}-date` : ''"
          ref="dateTimeTextfieldDate"
          :prefix="datePrefix ? datePrefix : ''"
          validate-on-blur
          :hide-details="hideDetails"
          class="v-solo-custom"
          outline
        >
        </v-text-field>
      </v-flex>
      <v-flex md6 class="pl-1" v-if="type === 'DATE_AND_TIME'">
        <v-text-field
          v-model="timeInput"
          :disabled="readOnly"
          :rules="
            enableValidation
              ? [
                  validationRules.twentyFourHourTime,
                  checkMinEpoch,
                  checkMaxEpoch,
                ]
              : []
          "
          :hint="timeLabel ? `${timeLabel} (24h Time)` : `24h time`"
          mask="##:##"
          persistent-hint
          color="light-blue"
          box
          @focus="$event.target.select()"
          :label="timeLabel ? timeLabel : 'Time'"
          :autofocus="autofocusTime"
          :id="uniqueKey ? `${uniqueKey}-time` : null"
          ref="dateTimeTextfieldTime"
          validate-on-blur
          :hide-details="hideDetails"
          class="v-solo-custom"
        />
      </v-flex>
    </v-layout>
    <v-layout row wrap v-if="soloInput">
      <v-flex md12>
        <v-text-field
          v-model="dateInput"
          :disabled="readOnly"
          :hint="dateHintText"
          :mask="dateMask"
          :persistent-hint="!readOnly || persistentHint"
          color="light-blue"
          :rules="
            enableValidation
              ? [
                  isRequired ? validationRules.required : true,
                  testDateFormat,
                  type !== 'DATE_AND_TIME' ? checkMaxEpoch : true,
                  checkMaxEpoch,
                  allowedDates ? checkAllowedDate : true,
                ]
              : []
          "
          solo
          flat
          :placeholder="datePlaceholder"
          class="v-solo-custom"
          @focus="$event.target.select()"
          @blur="checkForSemiCorrectDateFormat"
          :autofocus="autofocusDate"
          :id="uniqueKey ? `${uniqueKey}-date` : null"
          validate-on-blur
          :prefix="datePrefix ? datePrefix : null"
          :clearable="clearable"
          @click:clear="clearInputs"
          ref="dateTimeTextfieldDate"
          :hide-details="hideDetails"
        />
      </v-flex>
      <v-flex md12 v-if="type === 'DATE_AND_TIME'">
        <v-text-field
          v-model="timeInput"
          :disabled="readOnly"
          :rules="
            enableValidation
              ? [
                  validationRules.twentyFourHourTime,
                  checkMinEpoch,
                  checkMaxEpoch,
                ]
              : []
          "
          hint="24-hour time"
          mask="##:##"
          persistent-hint
          color="light-blue"
          solo
          flat
          :class="readOnly ? 'solo-input-disable-display' : ''"
          class="v-solo-custom"
          @focus="$event.target.select()"
          :label="timeLabel ? timeLabel : 'Time'"
          :autofocus="autofocusTime"
          validate-on-blur
          :id="uniqueKey ? `${uniqueKey}-time` : null"
          ref="dateTimeTextfieldTime"
          :hide-details="hideDetails"
        />
      </v-flex>
    </v-layout>
  </section>
</template>

<script lang="ts">
export enum ComparisonType {
  GREATER_THAN = 'GREATER_THAN',
  GREATER_OR_EQUAL = 'GREATER_OR_EQUAL',
  LESS_THAN = 'LESS_THAN',
  LESS_OR_EQUAL = 'LESS_OR_EQUAL',
}
export enum DateTimeType {
  DATE_START_OF_DAY = 'DATE_START_OF_DAY',
  DATE_END_OF_DAY = 'DATE_END_OF_DAY',
  YEAR = 'YEAR',
  DATE_AND_TIME = 'DATE_AND_TIME',
}
export enum HintTextType {
  LABEL = 'LABEL', // Show the label text in the hint area
  FORMATTED_SELECTION = 'FORMATTED_SELECTION', // Show the formatted selection in the hint text area
  NONE = 'NONE',
}
</script>

<script setup lang="ts">
import {
  dateAndTimeIsValid,
  returnFormattedTime,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import moment from 'moment-timezone';
import {
  computed,
  ComputedRef,
  onMounted,
  ref,
  Ref,
  WritableComputedRef,
} from 'vue';

const props = withDefaults(
  defineProps<{
    uniqueKey?: string;
    type?: DateTimeType;
    epochTime?: number | null;
    readOnly?: boolean;
    clearable?: boolean;
    // Label to used on textfield when using boxInput
    dateLabel?: string | null;
    timeLabel?: string | null;
    enableValidation?: boolean;
    // Optionally provide an epochTime that the inputted value must be greater
    // than to pass validation
    minimumEpochTime?: number | null;
    // Comparison type to check against minimumEpochTime
    minComparisonType?: ComparisonType;
    // Optionally provide an epochTime that the inputted value must be less than
    maximumEpochTime?: number | null;
    // Comparison type to check against maximumEpochTime
    maxComparisonType?: ComparisonType;
    boxInput?: boolean;
    soloInput?: boolean;
    autofocusDate?: boolean;
    autofocusTime?: boolean;
    showDatePicker?: boolean;
    isRequired?: boolean;
    hintTextType?: HintTextType;
    datePrefix?: string;
    // list of epoch dates that the user can select from. Can be used alongside validation. see checkAllowedDate
    allowedDates?: string[] | null;
    hideDetails?: boolean;
    persistentHint?: boolean;
  }>(),
  {
    uniqueKey: '',
    type: DateTimeType.DATE_AND_TIME,
    readOnly: false,
    clearable: false,
    dateLabel: null,
    timeLabel: null,
    enableValidation: true,
    minimumEpochTime: null,
    maximumEpochTime: null,
    minComparisonType: ComparisonType.GREATER_THAN,
    maxComparisonType: ComparisonType.LESS_THAN,
    boxInput: true,
    soloInput: false,
    autofocusDate: false,
    autofocusTime: false,
    showDatePicker: false,
    isRequired: false,
    hintTextType: HintTextType.LABEL,
    datePrefix: '',
    allowedDates: null,
    hideDetails: false,
    persistentHint: false,
    epochTime: null,
  },
);

const userTimeZone: Ref<string> = ref('');
const emit = defineEmits(['update:epochTime', 'dateTimeUpdated', 'clear']);

// Return the hint text to be displayed on the date input
const dateHintText: ComputedRef<string> = computed(() => {
  const formatDate = (e: number) => returnFormattedTime(e, 'DD/MM/YYYY');
  let hintText = '';
  // If hintTextType prop says we should use label, then set return value to the dateLabel prop
  if (
    props.hintTextType === HintTextType.LABEL ||
    props.type === DateTimeType.YEAR
  ) {
    hintText = props.dateLabel ? props.dateLabel : 'Date';
  } else if (props.hintTextType === HintTextType.FORMATTED_SELECTION) {
    // If hintTextType prop says we should use formatted selection
    if (!dateInput.value) {
      // If we don't have a time selection yet then display the minimum and
      // maximum times as a hint If minimum is defined
      if (props.minimumEpochTime && props.maximumEpochTime) {
        hintText = `${formatDate(props.minimumEpochTime)} to ${formatDate(
          props.maximumEpochTime,
        )}`;
      } else if (props.minimumEpochTime && !props.maximumEpochTime) {
        hintText = `Enter a date after ${formatDate(props.minimumEpochTime)}`;
      } else if (!props.minimumEpochTime && props.maximumEpochTime) {
        hintText = `Enter a date before ${formatDate(props.maximumEpochTime)}`;
      }
    } else {
      if (props.epochTime) {
        // If we do have a time defined, then display a formatted date which
        // includes the day of the week that the selection is
        hintText = returnFormattedTime(props.epochTime, 'dddd, MMMM Do YYYY');
      }
    }
  } else if (props.hintTextType === HintTextType.NONE) {
    return '';
  }
  return hintText;
});

// Returns the correct mask to be used on the dateInput textfield
const dateMask = computed(() => {
  switch (props.type) {
    case DateTimeType.DATE_START_OF_DAY:
    case DateTimeType.DATE_END_OF_DAY:
    case DateTimeType.DATE_AND_TIME:
      return '##/##/####';
    case DateTimeType.YEAR:
      return '####';
  }
  return '##/##/####';
});

const dateInput: WritableComputedRef<string> = computed({
  get(): string {
    if (!props.epochTime) {
      return '';
    }
    const epoch = props.epochTime;
    return props.type !== DateTimeType.YEAR
      ? moment.tz(epoch, userTimeZone.value).format('DDMMYYYY')
      : moment.tz(epoch, userTimeZone.value).format('YYYY');
  },
  set(value: string): void {
    let timeToCheck = '0000';
    // If input type is DATE_AND_TIME then use the time from timeInput field,
    // otherwise just use 0000
    if (props.type === DateTimeType.DATE_AND_TIME) {
      timeToCheck = timeInput.value ? timeInput.value : '0000';
    }
    validateDateTimeSelection(value, timeToCheck);
  },
});

const dateTimeTextfieldDate = ref(dateInput.value);

const datePlaceholder = computed(() => {
  if (!props.epochTime) {
    return props.type !== DateTimeType.YEAR ? 'dd/mm/yyyy' : '';
  }
  const epoch = props.epochTime;
  return props.type !== DateTimeType.YEAR
    ? moment.tz(epoch, userTimeZone.value).format('DD/MM/YYYY')
    : moment.tz(epoch, userTimeZone.value).format('YYYY');
});

const timeInput: WritableComputedRef<string> = computed({
  get(): string {
    if (!props.epochTime) {
      return '';
    }
    const epoch = props.epochTime;
    const timeStr = moment.tz(epoch, userTimeZone.value).format('HHmm');
    return timeStr;
  },
  set(value: string): void {
    validateDateTimeSelection(dateInput.value, value);
  },
});

// Used for date TextField. Check that entered value passes either the DDMMYYYY
// format validation OR the DDMMYY validation.
function testDateFormat(value: string) {
  if (!value) {
    return true;
  }
  if (
    props.type === DateTimeType.DATE_AND_TIME ||
    props.type === DateTimeType.DATE_END_OF_DAY ||
    props.type === DateTimeType.DATE_START_OF_DAY
  ) {
    const yyyy = validationRules.formattedDate(value);
    if (yyyy === true) {
      return true;
    }
    const yy = validationRules.formattedDateYY(value);
    if (yy === true) {
      return yy;
    } else {
      return yyyy;
    }
  } else if (props.type === DateTimeType.YEAR) {
    return validationRules.year(value);
  }
}

// Called on blur of the date component. Checks if the entered date is DDMMYY
// format when the textfield is blurred, and if it is, sets it to DDMMYYYY
// format such that it passes validation and emits update
function checkForSemiCorrectDateFormat() {
  const date = dateInput.value;
  // Find the date textfield so we can compare the current date with the current
  // inputted value
  const foundDateTextfield = dateTimeTextfieldDate.value as any;
  if (!foundDateTextfield) {
    return;
  }
  // lazyValue is the value in the Vuetify TextField component that represents the
  // raw entered string before it has been updated to the v-model. When an entered
  // value fails validation, the modelled value isn't updated. So the v-model and
  // lazyValue can be different.
  const lazyDate = foundDateTextfield.lazyValue;
  // If the date is the same as the lazyDate, then we should return
  if (lazyDate === date) {
    return;
  }
  // If the lazy date is not equal to the date, then we have should check if the
  // lazyDate value is a valid date of format DD/MM/YY
  const yy = validationRules.formattedDateYY(lazyDate);

  // If the lazyDate format is correct then we should insert the year '20'
  // to change from DDMMYY format to DDMMYYYY format
  if (yy === true) {
    // Construct new format and add to setter to validate and update parent
    const newDate = lazyDate.slice(0, 4) + '20' + lazyDate.slice(4);
    dateInput.value = newDate;
  }
}

// Validate the entered date and time strings. Checks that date is of format
// DDMMYYYY and time is of format HHmm. If valid, emit to parent to update with
// epoch time values
function validateDateTimeSelection(date: string, time: string) {
  if (props.readOnly) {
    return;
  }
  // If using a full date DateTimeType then check for date and time validity
  // and return if invalid
  if (
    props.type === DateTimeType.DATE_AND_TIME ||
    props.type === DateTimeType.DATE_END_OF_DAY ||
    props.type === DateTimeType.DATE_START_OF_DAY
  ) {
    if (!dateAndTimeIsValid(date, time)) {
      return;
    }
  } else if (props.type === DateTimeType.YEAR) {
    // If using year type DateTimeType then check for year validity and return if invalid
    if (validationRules.year(date) !== true) {
      return;
    }
  }

  let updatedTime;
  switch (props.type) {
    case DateTimeType.DATE_AND_TIME:
      // Standard case - return epoch of inputted date and time
      updatedTime = moment
        .tz(date + ' ' + time, 'DDMMYYYY HHmm', userTimeZone.value)
        .valueOf();
      break;
    case DateTimeType.DATE_START_OF_DAY:
      // Return start of day
      updatedTime = moment
        .tz(date + ' ' + time, 'DDMMYYYY HHmm', userTimeZone.value)
        .startOf('day')
        .valueOf();
      break;
    case DateTimeType.DATE_END_OF_DAY:
      // Return end of day
      updatedTime = moment
        .tz(date + ' ' + time, 'DDMMYYYY HHmm', userTimeZone.value)
        .endOf('day')
        .valueOf();
      break;
    case DateTimeType.YEAR:
      // Return end of day
      updatedTime = moment.tz(date, 'YYYY', userTimeZone.value).valueOf();
      break;
  }
  if (updatedTime) {
    // Emit epoch time to sync with parent
    emit('update:epochTime', updatedTime);
    emit('dateTimeUpdated', updatedTime);
  }
}

function clearInputs() {
  // Emit the event after a to avoid ts undefined error
  setTimeout(() => {
    emit('update:epochTime', 0);
    emit('clear');
  }, 0);
}

// Validate the entered time against the minimumEpochTime provided. Uses
// ComparisonType to know if we're doing greater than or if it can also be
// equal to, and display appropriate error messages on failed validate
function checkMinEpoch() {
  if (!props.minimumEpochTime) {
    return true;
  }
  const formatTime = (e: number) =>
    returnFormattedTime(
      e,
      props.type === DateTimeType.DATE_AND_TIME ? 'DD/MM/YY HH:mm' : 'DD/MM/YY',
    );

  if (
    props.minComparisonType === ComparisonType.GREATER_THAN &&
    props.epochTime !== null
  ) {
    // Check that time is greater than minimumEpochTime
    return props.epochTime > props.minimumEpochTime
      ? true
      : `Time must be after ${formatTime(props.minimumEpochTime)}`;
  } else if (
    props.minComparisonType === ComparisonType.GREATER_OR_EQUAL &&
    props.epochTime !== null
  ) {
    // Check that time is at least equal to minimumEpochTime
    return props.epochTime >= props.minimumEpochTime
      ? true
      : `Time must be at least ${formatTime(props.minimumEpochTime)}`;
  }
}

// Validate the entered time against the maximumEpochTime provided. Uses
// ComparisonType to know if we're doing LESS THAN comparison or if it can
// also be equal to. Display appropriate error messages on failed validate
function checkMaxEpoch() {
  if (!props.maximumEpochTime) {
    return true;
  }
  const formatTime = (e: number) =>
    returnFormattedTime(
      e,
      props.type === DateTimeType.DATE_AND_TIME ? 'DD/MM/YY HH:mm' : 'DD/MM/YY',
    );
  if (
    props.maxComparisonType === ComparisonType.LESS_THAN &&
    props.epochTime !== null
  ) {
    // Check that time is LESS THAN maximumEpochTime
    return props.epochTime < props.maximumEpochTime
      ? true
      : `Date/Time must be before ${formatTime(props.maximumEpochTime)}`;
  } else if (
    props.maxComparisonType === ComparisonType.LESS_OR_EQUAL &&
    props.epochTime !== null
  ) {
    // Check that time is at most maximumEpochTime (inclusive)
    return props.epochTime <= props.maximumEpochTime
      ? true
      : `Latest allowed time is ${formatTime(props.maximumEpochTime)}`;
  }
}

// Method utilised for validation against the selected date.
function checkAllowedDate(value: string) {
  if (!props.allowedDates) {
    return true;
  }
  // Because the user can enter DMYYYY and DMYY we need to convert the value to be DMYYYY. This is because our allowable dates are string[] of DMYYYY convert value to array.
  // convert entered value to array
  const valueAsArray: string[] = value.split('');
  // check length of entered value to know if DMYY or DMYYY was entered.
  if (value.split('').length !== 8) {
    // splice in missing century
    valueAsArray.splice(4, 0, '20');
  }
  // convert original value to correct DMYYYY
  value = valueAsArray.join('');
  const allowed = props.allowedDates.find((x: string) => x === value);
  return allowed
    ? true
    : 'Invalid or overlapping date. Please verify your entered date.';
}

onMounted(() => {
  userTimeZone.value = useCompanyDetailsStore().userLocale;
});
</script>

<style scoped lang="scss">
.date-time-textfields {
  padding: 0;
}
</style>
