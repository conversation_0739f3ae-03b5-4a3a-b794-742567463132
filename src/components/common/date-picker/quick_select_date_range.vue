<template>
  <div id="quick-select-date-range" :class="{ disabled: isDisabled }">
    <v-layout justify-end>
      <p>Valid To Quick Select:</p>
    </v-layout>
    <v-layout justify-end align-center>
      <div
        v-for="(range, index) of ranges"
        :key="index"
        class="range-select-button-container"
        @click="selectedAmount(range)"
      >
        <div class="range-select-button">
          <p>{{ range.name }}</p>
        </div>
      </div>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import {
  QuickSelectDateRangeInterface,
  quickSelectDateRanges,
} from '@/interface-models/Generic/QuickSelectDateRange/QuickSelectDateRange';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import moment from 'moment-timezone';

const props = withDefaults(
  defineProps<{
    initialValue?: number;
    isDisabled?: boolean;
  }>(),
  {
    initialValue: 0,
    isDisabled: false,
  },
);

const ranges: QuickSelectDateRangeInterface[] = quickSelectDateRanges;

const emit = defineEmits(['update:setQuickValidToDate']);

function selectedAmount(range: any) {
  if (props.initialValue !== null) {
    const initialMoment = moment(props.initialValue).tz(
      useCompanyDetailsStore().userLocale,
    );
    const calculatedDate = moment(initialMoment)
      .tz(useCompanyDetailsStore().userLocale)
      .add(range.multiplier, range.value)
      .endOf('day')
      .valueOf();
    emit('update:setQuickValidToDate', calculatedDate);
  }
}
</script>

<style scoped lang="scss">
.range-select-button-container {
  background-color: var(--primary);
  margin-left: 5px;
  padding: 3px 10px;
  border-radius: 4px;
  cursor: pointer;

  transition: 0.1s linear;

  &:hover {
    background-color: var(--primary-dark);
  }
}

p {
  margin: 0;
  font-size: $font-size-12;
}

.disabled {
  pointer-events: none;

  p {
    color: $bg-light;
  }
}
</style>
