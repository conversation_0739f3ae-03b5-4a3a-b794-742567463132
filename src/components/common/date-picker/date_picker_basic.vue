<template>
  <v-menu
    :close-on-content-click="true"
    v-model="isActive"
    :ref="isActive"
    :nudge-right="40"
    offset-y
    full-width
    max-width="290px"
    min-width="290px"
  >
    <template v-slot:activator="{ on }">
      <v-tooltip bottom v-if="!yearOnly && showNextPrevDayArrows">
        <template v-slot:activator="{ on }">
          <v-btn
            flat
            small
            v-on="on"
            icon
            @click="incrementDecrementDate('DECREMENT')"
            class="ma-0 pa-0"
          >
            <v-icon size="20" color="grey lighten-1"
              >fad fa-long-arrow-alt-left</v-icon
            >
          </v-btn>
        </template>
        <span>Previous Day</span>
      </v-tooltip>

      <v-text-field
        v-if="!yearOnly"
        :disabled="formDisabled"
        v-mask="'##/##/####'"
        v-model="dateFormatted"
        :hide-details="hideDetails"
        :label="labelName"
        :box="boxInput"
        persistent-hint
        :hint="soloInput ? labelName : showHint ? hintText : ''"
        :solo="soloInput"
        :flat="soloInput"
        :prepend-icon="!hideIcon ? 'event' : ''"
        :class="[
          { 'form-field-required': isRequired },
          { 'solo-input-disable-display': soloInput && formDisabled },
          { 'v-solo-custom': soloInput },
        ]"
        :background-color="backgroundColor ? backgroundColor : null"
        v-on="on"
        color="primary"
        :clearable="clearable"
        :rules="validationRule"
        @input="updateDate"
        :readonly="isActive"
      >
      </v-text-field>
      <v-tooltip bottom v-if="!yearOnly && showNextPrevDayArrows">
        <template v-slot:activator="{ on }">
          <v-btn
            flat
            small
            v-on="on"
            icon
            @click="incrementDecrementDate('INCREMENT')"
            class="ma-0 pa-0"
          >
            <v-icon size="20" color="grey lighten-1"
              >fad fa-long-arrow-alt-right</v-icon
            >
          </v-btn>
        </template>
        <span>Next Day</span>
      </v-tooltip>
      <v-text-field
        v-if="yearOnly"
        :disabled="formDisabled"
        v-mask="'##/####'"
        v-model="dateFormatted"
        :label="labelName"
        :box="boxInput"
        persistent-hint
        :prepend-icon="!hideIcon ? 'event' : ''"
        v-on="on"
        :readonly="isActive"
      >
      </v-text-field>
    </template>
    <v-date-picker
      class="v-date-picker-custom"
      elevation="24"
      v-model="date"
      locale="en-AU"
      :min="minDate"
      :max="maxDate"
      :allowed-dates="allowedDates"
      :show-current="yearOnly ? false : true"
      :type="yearOnly ? 'month' : 'date'"
      :first-day-of-week="1"
    ></v-date-picker>
  </v-menu>
</template>

<script setup lang="ts">
import { Validation } from '@/interface-models/Generic/Validation';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import moment from 'moment-timezone';
import { Ref, computed, onMounted, ref, watch } from 'vue';

const props = withDefaults(
  defineProps<{
    epochTime: number | null;
    labelName?: string;
    formDisabled?: boolean;
    boxInput?: boolean;
    soloInput?: boolean;
    hideDetails?: boolean;
    hideIcon?: boolean;
    report?: boolean;
    reportIndex?: number;
    yearOnly?: boolean;
    validate?: Validation;
    isRequired?: boolean;
    minDate?: string;
    maxDate?: string;
    backgroundColor?: string;
    clearable?: boolean;
    isClientPortal?: boolean;
    mondaysOnly?: boolean;
    selectableDates?: string[];
    timeZone?: string | null;
    // Show the arrow buttons on either side of the date picker to go to next or
    // previous day
    showNextPrevDayArrows?: boolean;
    showHint?: boolean;
    hintText?: string;
    allowNegativeEpoch?: boolean;
  }>(),
  {
    epochTime: null,
    labelName: '',
    formDisabled: false,
    boxInput: false,
    soloInput: false,
    hideDetails: false,
    hideIcon: false,
    report: false,
    reportIndex: -1,
    yearOnly: false,
    isRequired: false,
    minDate: undefined,
    maxDate: undefined,
    backgroundColor: undefined,
    clearable: false,
    isClientPortal: false,
    selectableDates: () => [],
    mondaysOnly: false,
    timeZone: null,
    showNextPrevDayArrows: false,
    showHint: false,
    hintText: '',
    validate: undefined,
    allowNegativeEpoch: true,
  },
);

const date: Ref<string> = ref('');
const isActive: Ref<boolean> = ref(false);

const emit = defineEmits(['setEpoch']);

const companyDetailsStore = useCompanyDetailsStore();

const updateDate = () => {
  const epoch = dateFormatted.value;
  emit('setEpoch', epoch); // Convert dateFormatted to epoch
};

const dateFormatted = computed({
  get() {
    let epochTime = props.epochTime;
    if (!props.allowNegativeEpoch && !!epochTime && epochTime < 0) {
      epochTime = 0;
    }
    if (epochTime !== null && epochTime !== undefined && epochTime !== 0) {
      const timeZone = props.timeZone || companyDetailsStore.userLocale;
      if (!props.yearOnly) {
        return moment(epochTime).tz(timeZone).format('DD/MM/YYYY');
      } else {
        return moment(epochTime).tz(timeZone).format('MM/YYYY');
      }
    } else {
      return null;
    }
  },
  set(value: string | null): void {
    if (value === null) {
      date.value = '';
      emit('setEpoch', null);
      return;
    }
    if (value.length === 10) {
      const timeZone = props.timeZone || companyDetailsStore.userLocale;
      const [day, month, year] = value.split('/');
      const vuetifyDate = moment(year + '-' + month + '-' + day)
        .tz(timeZone)
        .valueOf();
      const localEpoch = moment(vuetifyDate).tz(timeZone).valueOf();
      emit('setEpoch', !isNaN(localEpoch) ? localEpoch : null);
    }
  },
});

function allowedDates(value: string) {
  if (props.mondaysOnly) {
    const dayOfWeek = moment(value, 'YYYY-MM-DD').isoWeekday();
    return dayOfWeek === 1;
  }
  return props.selectableDates.length === 0
    ? true
    : props.selectableDates.indexOf(value) !== -1;
}

const validationRule = computed(() => {
  const rules: any[] = [];
  if (!props.validate) {
    return rules;
  }
  if (props.isRequired) {
    rules.push(props.validate.required);
    rules.push(props.validate.validDateLength);
  } else {
    rules.push(props.validate.validDateLength);
  }
  return rules;
});

// sets the circle on the calendar for when the selected date and circle become out of sync. Method is required because sometimes the date picker component mounts before the epochTime props is set by the parent component
function setCircleOnCalendar() {
  const timeZone = props.timeZone || companyDetailsStore.userLocale;
  date.value = moment(props.epochTime).tz(timeZone).format('YYYY-MM-DD');
}

const dateChanged = (val: string) => {
  const localTimeZone = props.timeZone || companyDetailsStore.userLocale;
  const localEpoch = moment.tz(val, localTimeZone).valueOf();
  if (props.report) {
    const reportData = {
      index: props.reportIndex,
      epoch: localEpoch,
    };
    emit('setEpoch', reportData);
  } else {
    emit('setEpoch', !isNaN(localEpoch) ? localEpoch : null); // commits utc epoch to model
  }
};

const updatedEpochTime = (val: number | null, oldVal: number | null) => {
  if (oldVal === null && val !== null) {
    setCircleOnCalendar();
  }
};

watch(() => date.value, dateChanged);
watch(() => props.epochTime, updatedEpochTime);

// Used when prop 'showNextPrevDayArrows' is TRUE. Called by arrow keys.
// Parameter 'type' will be either 'INCREMENT' for the next arrow, or
// 'DECREMENT' for the previous arrow. Add or subtract 1 day depending on
// type.
function incrementDecrementDate(type: string) {
  if (!date.value) {
    return;
  }
  // Use tz from prop if provided
  const timeZone = props.timeZone || companyDetailsStore.userLocale;
  const dates = moment.tz(date.value, 'YYYY-MM-DD', timeZone);
  // Add 1 day for INCREMENT, subtract 1 day for DECREMENT
  if (type === 'INCREMENT') {
    dates.add(1, 'day');
  } else if (type === 'DECREMENT') {
    dates.subtract(1, 'day');
  }
  // Set updated value back to local variable, which will trigger emit from watcher and update parent
  date.value = dates.format('YYYY-MM-DD');
}

// When the component mounts we set the selected date to be equal to the
// epochTime props value. we do this because sometimes the date is changed
// from internal code changes rather then human selection. When the date
// changes from code the vuetify date picker selected date (blue circle) can
// become out of sync. Keying the date component based on what changes the
// epochTime value is required for this to work.
onMounted(() => {
  if (date.value === '' && props.epochTime !== null && props.epochTime !== 0) {
    setCircleOnCalendar();
  }
});
</script>

<style scoped lang="scss">
.v-date-picker-custom {
  border-radius: $border-radius-base;
  background: var(--primary-gradient);
}
</style>
