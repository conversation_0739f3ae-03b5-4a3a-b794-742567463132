import DateTimeInputs from '@/components/common/date-picker/date_time_inputs.vue';
import FileUpload from '@/components/common/file-upload/index.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import FormCard from '@/components/common/ui-elements/form_card.vue';
import { downloadAttachment } from '@/helpers/AttachmentHelpers/AttachmentHelpers';
import {
  returnFormattedDate,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { DriverDetails } from '@/interface-models/Driver/DriverDetails/DriverDetails';
import Attachment from '@/interface-models/Generic/Attachment/Attachment';
import AttachmentTypes from '@/interface-models/Generic/Attachment/AttachmentTypes';
import ImmunisationDose from '@/interface-models/Generic/ImmunisationHistory/ImmunisationDose';
import ImmunisationHistory from '@/interface-models/Generic/ImmunisationHistory/ImmunisationHistory';
import {
  ImmunisationDoseType,
  ImmunisationType,
} from '@/interface-models/Generic/ImmunisationHistory/ImmunisationType';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { VForm } from '@/interface-models/Generic/VForm/VForm';
import { Validation } from '@/interface-models/Generic/Validation';
import { useAttachmentStore } from '@/store/modules/AttachmentStore';
import { sessionManager } from '@/store/session/SessionState';
import moment from 'moment-timezone';
import { v4 as uuidv4 } from 'uuid';
import { Component, Prop, Vue } from 'vue-property-decorator';

interface ImmunisationDoseTableItem {
  id: string;
  dose: ImmunisationDose;
  doseDescription: string;
  historyDocumentId: string;
  declarationDocumentId: string;
}
@Component({
  components: {
    FormCard,
    FileUpload,
    ConfirmationDialog,
    DateTimeInputs,
  },
})
export default class ImmunisationHistoryTable extends Vue {
  @Prop({ default: false }) public formDisabled: boolean;
  @Prop() public driverDetails: DriverDetails;
  @Prop({ default: () => [] })
  public immunisationHistory: ImmunisationHistory[];

  public returnFormattedDate = returnFormattedDate;

  public isViewingDialog: boolean = false;

  public currentlyEditingDose: ImmunisationDose | null = null;
  public immunisationDocumentation: Attachment = new Attachment();
  public immunisationDeclaration: Attachment = new Attachment();

  public $refs!: {
    immunisationHistoryForm: VForm;
  };

  public tableHeaders: TableHeader[] = [
    {
      text: 'Date',
      align: 'left',
      sortable: true,
      value: 'history.dateReceived',
    },
    {
      text: 'Type',
      align: 'left',
      sortable: false,
      value: 'doseType',
    },
    {
      text: 'Notes',
      align: 'left',
      sortable: false,
      value: 'history.additionalNotes',
    },

    {
      text: 'Evidence Supplied',
      align: 'left',
      sortable: false,
      value: 'historyDocumentId',
    },
    {
      text: 'Declaration Supplied',
      align: 'left',
      sortable: false,
      value: 'declarationDocumentId',
    },
    {
      text: 'Verified At',
      align: 'left',
      sortable: false,
      value: 'history.createdTime',
    },
    {
      text: 'Verified By',
      align: 'left',
      sortable: false,
      value: 'history.createdTime',
    },
  ];
  // Menu options for select list
  public doseTypeOptions = [
    {
      id: ImmunisationDoseType.PRIMARY,
      longName: 'Primary Dose (1st or 2nd)',
    },
    {
      id: ImmunisationDoseType.BOOSTER,
      longName: 'Booster Dose',
    },
  ];
  // The model supports the recording of non-COVID type immunisations, but for now we will only display COVID records in the table
  get immunisationHistoryFiltered(): ImmunisationDoseTableItem[] {
    const foundHistory = this.immunisationHistory.find(
      (i) => i.type === ImmunisationType.COVID_19,
    );
    if (foundHistory) {
      const tableItems: ImmunisationDoseTableItem[] = [];
      foundHistory.doseHistory.forEach((dose) => {
        const foundImmunisationHistory = dose.supportingDocumentation.find(
          (d) => d.documentTypeId === AttachmentTypes.IMMUNISATION_HISTORY,
        );
        const foundDeclaration = dose.supportingDocumentation.find(
          (d) =>
            d.documentTypeId ===
            AttachmentTypes.DECLARATION_OF_IMMUNISATION_STATUS,
        );
        const tableItem: ImmunisationDoseTableItem = {
          id: dose.id,
          dose,
          doseDescription: '',
          historyDocumentId: foundImmunisationHistory
            ? foundImmunisationHistory.id
            : '',
          declarationDocumentId: foundDeclaration ? foundDeclaration.id : '',
        };
        tableItems.push(tableItem);
      });
      return tableItems;
    } else {
      return [];
    }
  }
  // Controls dialog visibility, and resets local working variables on close
  get dialogController() {
    return this.isViewingDialog;
  }
  set dialogController(value: boolean) {
    this.isViewingDialog = value;
    if (!value) {
      this.immunisationDocumentation = new Attachment();
      this.immunisationDeclaration = new Attachment();
      this.currentlyEditingDose = null;
    }
  }

  get validate(): Validation {
    return validationRules;
  }

  // Deletes dose history item matching the provided ID from the doseHistory list
  public deleteDoseRecord(id: string) {
    const foundHistory = this.immunisationHistory.find(
      (i) => i.type === ImmunisationType.COVID_19,
    );
    if (foundHistory) {
      const foundDoseIndex = foundHistory.doseHistory.findIndex(
        (d) => d.id === id,
      );
      if (foundDoseIndex !== -1) {
        foundHistory.doseHistory.splice(foundDoseIndex);
      }
    }
  }
  // Fetch full size attachment for id
  public async downloadSelectedAttachment(id: string) {
    const att = await useAttachmentStore().getAttachmentById(id);
    if (att) {
      downloadAttachment(att);
    } else {
      showNotification(GENERIC_ERROR_MESSAGE, {
        title: 'Immunisation History - File Download',
      });
    }
  }
  // Add the ImmunisationDose object to the correct ImmunisationHistory parent
  // If one does not exist, create it
  public addDoseToImmunisationHistory(dose: ImmunisationDose) {
    const foundHistory = this.immunisationHistory.find(
      (i) => i.type === ImmunisationType.COVID_19,
    );
    if (foundHistory) {
      foundHistory.doseHistory.push(dose);
    } else {
      const history = new ImmunisationHistory();
      history.id = uuidv4().replace(/-/g, '');
      history.type = ImmunisationType.COVID_19;
      history.doseHistory = [dose];
      this.immunisationHistory.push(history);
    }

    this.driverDetails.save(null);
  }
  // Save the current ImmunisationDose in the dialog, adding in any uploaded attachments, add it to ImmunisationHistory object and close dialog
  public saveImmunisationDose() {
    if (!this.currentlyEditingDose) {
      showNotification('Something went wrong. Please try again.');
      return;
    }
    if (
      !(this.immunisationDeclaration.id || this.immunisationDocumentation.id)
    ) {
      showNotification(
        'Please ensure at least ONE supporting document is provided to verify vaccination status',
      );
      return;
    }
    if (!this.$refs.immunisationHistoryForm.validate()) {
      return;
    }
    const dose: ImmunisationDose = this.currentlyEditingDose;
    dose.verifiedBy = sessionManager.getActiveUser();
    dose.createdTime = moment().valueOf();

    const attachments: Attachment[] = [];
    if (this.immunisationDocumentation.id) {
      attachments.push(this.immunisationDocumentation);
    }
    if (this.immunisationDeclaration.id) {
      attachments.push(this.immunisationDeclaration);
    }
    dose.supportingDocumentation = attachments;
    this.addDoseToImmunisationHistory(dose);
    this.dialogController = false;
  }
  // Set local variables to default values for editing in dialog
  public recordImmunisationDose() {
    const dose = new ImmunisationDose();

    dose.id = uuidv4().replace(/-/g, '');
    dose.dateReceived = returnStartOfDayFromEpoch();
    this.currentlyEditingDose = dose;
    this.dialogController = true;
  }
}
