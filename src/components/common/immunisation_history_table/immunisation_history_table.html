<div class="immunisation-history-table">
  <div class="add-btn">
    <v-btn
      color="blue"
      depressed
      small
      @click="recordImmunisationDose"
      :disabled="formDisabled"
    >
      Record New Dose
    </v-btn>
  </div>

  <v-layout row wrap>
    <v-flex md12>
      <v-data-table
        :headers="tableHeaders"
        :items="immunisationHistoryFiltered"
        class="gd-dark-theme"
        hide-actions
      >
        <template v-slot:items="props">
          <tr style="position: relative">
            <td>{{returnFormattedDate(props.item.dose.dateReceived)}}</td>
            <td>{{props.item.dose.doseType}}</td>
            <td>
              {{props.item.dose.additionalNotes ?
              props.item.dose.additionalNotes : '-'}}
            </td>
            <td>
              <v-icon
                :color="props.item.historyDocumentId ? 'light-blue' : 'grey'"
                v-html="props.item.historyDocumentId ? 'fas fa-check' : 'fal fa-times'"
                size="14"
              >
              </v-icon>
            </td>
            <td>
              <v-icon
                :color="props.item.declarationDocumentId ? 'light-blue' : 'grey'"
                v-html="props.item.declarationDocumentId ? 'fas fa-check' : 'fal fa-times'"
                size="14"
              >
              </v-icon>
            </td>
            <td>{{returnFormattedDate(props.item.dose.createdTime)}}</td>
            <td>{{ props.item.dose.verifiedBy}}</td>
            <div class="immunisation-history-table__menu" v-if="!formDisabled">
              <v-menu left>
                <template v-slot:activator="{ on }">
                  <v-icon size="20" class="pl-3 pr-1" v-on="on"
                    >far fa-ellipsis-v</v-icon
                  >
                </template>
                <v-list class="v-list-custom" dense>
                  <ConfirmationDialog
                    message="You are about to delete the selected Immunisation record. This action cannot be undone. Do you wish to proceed?"
                    buttonText="Delete Record"
                    title="Confirm Deletion of Immunisation Record"
                    @confirm="deleteDoseRecord(props.item.id)"
                    :buttonDisabled="false"
                    :isOutlineButton="true"
                    confirmationButtonText="Confirm and Delete"
                    :isListTile="true"
                    :dialogIsActive="true"
                    listTileIcon="fas fa-trash"
                  />
                  <v-list-tile
                    v-if="props.item.historyDocumentId"
                    @click="downloadSelectedAttachment(props.item.historyDocumentId)"
                  >
                    <v-list-tile-avatar class="pa-0">
                      <v-icon size="14"> fas fa-download </v-icon>
                    </v-list-tile-avatar>
                    <v-list-tile-title class="pr-2">
                      Download Immunisation Record
                    </v-list-tile-title>
                  </v-list-tile>
                  <v-list-tile
                    v-if="props.item.declarationDocumentId"
                    @click="downloadSelectedAttachment(props.item.declarationDocumentId)"
                  >
                    <v-list-tile-avatar class="pa-0">
                      <v-icon size="14"> fas fa-download </v-icon>
                    </v-list-tile-avatar>
                    <v-list-tile-title class="pr-2">
                      Download Declaration
                    </v-list-tile-title>
                  </v-list-tile>
                </v-list>
              </v-menu>
            </div>
          </tr>
        </template>
      </v-data-table>
    </v-flex>
  </v-layout>
  <v-dialog
    v-model="dialogController"
    width="700px"
    class="ma-0"
    content-class="v-dialog-custom"
  >
    <v-flex md12 class="app-theme__center-content--body dialog-content">
      <v-layout
        justify-space-between
        align-center
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>Add COVID-19 Immunisation Record</span>
        <div
          class="app-theme__center-content--closebutton"
          @click="dialogController = false"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <v-layout row class="pa-3" v-if="currentlyEditingDose">
        <v-flex md12>
          <v-form ref="immunisationHistoryForm">
            <v-layout row wrap>
              <v-flex md12>
                <v-alert class="mb-3" :value="true" type="info">
                  <ul>
                    <li>
                      Please upload all necessary documents to verify the
                      driver's vaccination status.
                    </li>
                    <li>
                      A signed declaration confirming vaccination status with a
                      witness verifying their status may also be uploaded if the
                      driver does not consent to having their vaccination
                      certificate on record.
                    </li>
                  </ul>
                </v-alert>
              </v-flex>
              <v-flex md3
                ><h6
                  class="pt-2 subheader--faded text-md-right form-field-required-marker"
                >
                  Dose Type
                </h6>
              </v-flex>
              <v-flex md9 pl-3>
                <v-select
                  solo
                  flat
                  v-model="currentlyEditingDose.doseType"
                  :items="doseTypeOptions"
                  item-value="id"
                  item-text="longName"
                  color="orange"
                  :rules="[validate.required]"
                  label="Select a Dose Type"
                  class="v-solo-custom"
                >
                </v-select>
              </v-flex>
              <v-flex md3>
                <h6
                  class="pt-2 subheader--faded text-md-right form-field-required-marker"
                >
                  Dose Received On
                </h6>
              </v-flex>
              <v-flex md9 pl-3>
                <DateTimeInputs
                  :epochTime.sync="currentlyEditingDose.dateReceived"
                  :enableValidation="true"
                  type="DATE_START_OF_DAY"
                  dateLabel="Date dose was administered (found on Immunisation Certificate)"
                  :readOnly="formDisabled"
                  :soloInput="true"
                  :boxInput="false"
                  :isRequired="true"
                  hintTextType="LABEL"
                ></DateTimeInputs>
              </v-flex>
              <v-flex md3
                ><h6
                  class="pt-2 subheader--faded text-md-right form-field-required-marker"
                >
                  Upload Supporting Documents
                </h6>
              </v-flex>
              <v-flex md9 mb-2>
                <v-layout>
                  <v-flex md5>
                    <file-upload
                      class="ml-3"
                      imageLabel="Upload Proof of Immunisation"
                      :attachmentSingle="true"
                      :documentTypeId="28"
                      :attachment="immunisationDocumentation"
                    >
                    </file-upload>
                  </v-flex>
                  <v-flex md5>
                    <file-upload
                      class="ml-2 mr-1"
                      imageLabel="Upload Signed Declaration"
                      :attachmentSingle="true"
                      :documentTypeId="29"
                      :attachment="immunisationDeclaration"
                    >
                    </file-upload>
                  </v-flex>
                  <v-flex md2></v-flex>
                </v-layout>
              </v-flex>

              <v-flex md3
                ><h6 class="pt-2 subheader--faded text-md-right">
                  Additional Notes
                </h6>
              </v-flex>
              <v-flex md9 pl-3>
                <v-textarea
                  placeholder="Please add a note..."
                  v-model="currentlyEditingDose.additionalNotes"
                  solo
                  flat
                  rows="3"
                  class="v-solo-custom"
                  color="orange"
                ></v-textarea>
              </v-flex>
            </v-layout>
          </v-form> </v-flex
      ></v-layout>
      <v-divider class="mt-2"></v-divider>
      <v-layout justify-space-between>
        <v-btn color="error" flat @click="dialogController = false">
          Cancel</v-btn
        >
        <v-spacer></v-spacer>
        <v-btn depressed color="blue" @click="saveImmunisationDose"
          >Confirm</v-btn
        >
      </v-layout>
    </v-flex>
  </v-dialog>
</div>
