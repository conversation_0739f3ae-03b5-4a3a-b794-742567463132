<v-layout
  class="px-4"
  :class="!isClientPortal ? 'pagination-container' : 'pagination-container-client-portal'"
  justify-end
  align-center
>
  <span class="rows-per-page-title">Rows per page:</span>
  <div style="width: 40px; margin: 14px">
    <v-select
      :items="rowsPerPageList"
      :disabled="disabled"
      v-model="selectedRowsPerPage"
    />
  </div>
  <span class="caption ml-5">{{pagesOf}}</span>
  <v-icon
    class="ml-5"
    size="16"
    @click="pageIncrement(-1)"
    :disabled="pagePreviousDisabled || disabled"
    >far fa-chevron-left</v-icon
  >
  <v-icon
    class="ml-5"
    size="16"
    @click="pageIncrement(1)"
    :disabled="pageNextDisabled || disabled"
    >far fa-chevron-right</v-icon
  >
</v-layout>
