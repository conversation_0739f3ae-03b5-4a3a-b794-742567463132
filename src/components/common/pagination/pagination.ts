import { hasAdminOrCsrOrHeadOfficeOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { VPagination } from '@/interface-models/Generic/VPagination';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { sessionManager } from '@/store/session/SessionState';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class Pagination extends Vue implements IUserAuthority {
  @Prop() public pagination: VPagination;
  @Prop() public rowsPerPage: number;
  @Prop() public disabled: boolean;
  @Prop({ default: true }) public showTotalCount: boolean;
  @Prop() public currentPageCount: number;
  @Prop({ default: () => [10, 25, 50, 100] }) public rowsPerPageList: number[];

  public isAuthorised(): boolean {
    return hasAdminOrCsrOrHeadOfficeOrTeamLeaderOrBranchManagerRole();
  }

  public pageIncrement(value: number) {
    this.$emit('pageIncrement', value);
  }

  /**
   * Determines if the "previous page" button should be disabled
   */
  get pagePreviousDisabled(): boolean {
    // If the current page is undefined or we're on the first page, disable the
    // button
    if (this.pagination.page === undefined) {
      return true;
    }
    return this.pagination.page <= 1;
  }

  /**
   * Determines if the "next page" button should be disabled
   */
  get pageNextDisabled(): boolean {
    // If the current page or rows per page are undefined, disable the button
    if (
      this.pagination.page === undefined ||
      this.pagination.rowsPerPage === undefined
    ) {
      return true;
    }

    // If total count is shown and there are no total items, disable the button
    if (this.showTotalCount && !this.pagination.totalItems) {
      return true;
    }

    // If we're on the last page of results (i.e. the current page * rows per
    // page), disable the button
    if (this.showTotalCount) {
      return (
        this.pagination.page * this.pagination.rowsPerPage >=
        this.pagination.totalItems!
      );
    } else {
      // If we don't have access to the total count, disable the button if the
      // current page count is less than the rows per page (which indicates
      // we're on the last page of results)
      return this.currentPageCount < this.pagination.rowsPerPage;
    }
  }

  /**
   * Returns the current page range and total number of items in the pagination,
   * used in the HTML on the bottom right of the pagination component. Formats
   * the result into '# of #' format.
   * @returns The current page range and total number of items in the
   * pagination.
   */
  get pagesOf() {
    if (
      this.pagination.page === undefined ||
      this.pagination.rowsPerPage === undefined
    ) {
      return '- of -';
    }
    if (!this.pagination.totalItems) {
      return this.showTotalCount ? '- of ' : `Page ${this.pagination.page}`;
    }

    const currentRange =
      (this.pagination.page - 1) * this.pagination.rowsPerPage + 1;

    const actualCurrentRange =
      this.pagination.totalItems < currentRange ? 1 : currentRange;

    const rangeUpTo =
      (this.pagination.page - 1) * this.pagination.rowsPerPage +
      this.pagination.rowsPerPage;

    const actualRangeUpTo =
      rangeUpTo > this.pagination.totalItems
        ? this.pagination.totalItems
        : rangeUpTo;

    return `${actualCurrentRange}-${actualRangeUpTo} of ${this.pagination.totalItems}`;
  }

  /**
   * Getter and setter modelled to the v-select in the HTML, to allow the user
   * to select the number of items per page. On change it will emit and update
   * the prop in the parent
   */
  get selectedRowsPerPage() {
    return this.rowsPerPage;
  }
  set selectedRowsPerPage(value: number) {
    this.$emit('update:rowsPerPage', value);
    this.$emit('change');
  }

  get isClientPortal() {
    return sessionManager.getSecurityLevel() === 'Client';
  }
}
