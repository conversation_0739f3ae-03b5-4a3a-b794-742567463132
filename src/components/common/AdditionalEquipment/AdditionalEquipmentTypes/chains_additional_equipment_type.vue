<template>
  <section class="chains-additional-equipment-container">
    <v-layout
      row
      wrap
      class="app-bgcolor--400 app-bordercolor--600 app-borderside--a"
    >
      <v-flex md12>
        <v-layout justify-space-between align-center pa-2>
          <h3 class="subheader--light pa-0" style="line-height: 1">
            Chain Details
          </h3>
          <v-btn
            color="info"
            @click="addChain"
            small
            class="pa-0 ma-0"
            :disabled="chainMaintenanceDialogIsOpen || !isEdited"
          >
            Add Chain
          </v-btn>
        </v-layout>
        <v-divider></v-divider>
      </v-flex>
      <v-flex md12 pa-2>
        <div class="list-container mb-3">
          <v-data-table
            class="gd-dark-theme"
            :headers="headers"
            :items="chainsDetails.chains"
            must-sort
            hide-actions
          >
            <template v-slot:items="props">
              <tr
                @click="viewChain(props.item, props.index)"
                style="cursor: pointer"
              >
                <td class="text-xs-left">{{ props.item.serialNumber }}</td>
                <td class="text-xs-left">
                  {{
                    props.item.inspectionDate
                      ? returnFormattedDate(props.item.inspectionDate)
                      : '-'
                  }}
                </td>
                <td class="text-xs-right">
                  {{ props.item.attachmentId ? 'YES' : 'NO' }}
                </td>
              </tr>
            </template>
            <template v-slot:no-results>
              <v-alert :value="true" color="error" icon="warning">
                Your search found no results.
              </v-alert>
            </template>
          </v-data-table>
        </div>
        <v-dialog
          v-if="chain"
          v-model="chainMaintenanceDialogIsOpen"
          content-class="v-dialog-custom"
          width="600px"
          persistent
        >
          <v-layout
            justify-space-between
            class="task-bar app-theme__center-content--header no-highlight"
          >
            <span>Chain Maintenance</span>
            <div
              class="app-theme__center-content--closebutton"
              @click="cancelMaintenance"
              :style="isLoading ? 'pointer-events: none;' : ''"
            >
              <v-icon class="app-theme__center-content--closebutton--icon"
                >fal fa-times</v-icon
              >
            </div>
          </v-layout>

          <v-layout
            class="body-scrollable--65 app-theme__center-content--body dialog-content pa-3 body-min-height--65"
            row
            wrap
          >
            <v-flex md12 pb-3
              ><v-layout align-center>
                <h5 class="subheader--bold pr-3 pt-1">1. Key Details</h5>
                <v-flex>
                  <v-divider></v-divider>
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6
                      class="subheader--faded pr-3 pb-0 form-field-required-marker"
                    >
                      Identification ID
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-text-field
                    :class="!isEdited ? 'solo-input-disable-display' : ''"
                    solo
                    flat
                    autofocus
                    color="light-blue"
                    :disabled="!isEdited"
                    class="v-solo-custom"
                    label="Identification ID"
                    v-model="chain.serialNumber"
                  ></v-text-field>
                </v-flex>
              </v-layout>
            </v-flex>

            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6
                      class="subheader--faded pr-3 pb-0 form-field-required-marker"
                    >
                      Latest Inspection Date
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <DateTimeInputs
                    :epochTime.sync="chain.inspectionDate"
                    :enableValidation="true"
                    :type="DateTimeType.DATE_START_OF_DAY"
                    dateLabel="Latest Inspection Date"
                    :soloInput="true"
                    :boxInput="false"
                    :readOnly="!isEdited"
                    :hintTextType="HintTextType.FORMATTED_SELECTION"
                  ></DateTimeInputs>
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12 pb-3
              ><v-layout align-center>
                <h5 class="subheader--bold pr-3 pt-1">
                  2. Compliance Certificate
                </h5>
                <v-flex>
                  <v-divider></v-divider>
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex
              md12
              v-if="attachment?.id && chainsWithExistingCertificates.length > 0"
            >
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">
                      Apply Existing Certificate
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-select
                    label="Select Existing Certificate From Chain"
                    v-model="selectedCertificate"
                    :items="chainsWithExistingCertificates"
                    item-text="serialNumber"
                    class="v-solo-custom"
                    solo
                    flat
                    item-value="attachmentId"
                    @input="addExistingCertificate"
                  ></v-select>
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">Certificate</h6>
                  </v-layout>
                </v-flex>
                <v-flex md6>
                  <file-upload
                    :isLoading="isLoading"
                    @update:isLoading="(value) => (isLoading = value)"
                    :imageLabel="'COMPLIANCE CERTIFICATE'"
                    :attachmentSingle="true"
                    :documentTypeId="attachmentTypes.COMPLIANCE_CERTIFICATE"
                    :attachment="attachment"
                    :formDisabled="!isEdited"
                  >
                  </file-upload>
                </v-flex>
              </v-layout>
            </v-flex>

            <v-flex md12>
              <v-divider class="mt-2"></v-divider>
              <v-layout justify-end align-end style="height: 100%">
                <ConfirmationDialog
                  v-if="editingChain"
                  buttonText="Delete Chain"
                  message="Please confirm that you wish to remove this chain."
                  title="Removal Confirmation"
                  @confirm="deleteChain"
                  :cancelButtonText="'cancel'"
                  :isSmallButton="false"
                  :buttonDisabled="!isEdited"
                  :flat="true"
                  :buttonColor="'error'"
                  :confirmationButtonText="'Confirm and remove'"
                  :dialogIsActive="true"
                  :confirmationButtonColor="'error'"
                  :cancelButtonColor="'blue'"
                ></ConfirmationDialog>
                <v-spacer></v-spacer>
                <v-btn
                  @click="cancelMaintenance"
                  color="error"
                  outline
                  :disabled="!isEdited"
                  >cancel</v-btn
                >

                <v-btn
                  @click="saveEdit"
                  color="info"
                  depressed
                  v-if="editingChain"
                  :disabled="!isEdited || isLoading"
                >
                  Update Chain
                </v-btn>
                <v-btn
                  @click="saveChain"
                  color="info"
                  depressed
                  v-if="!editingChain"
                  :disabled="!isEdited || isLoading"
                  >Add Chain</v-btn
                >
              </v-layout>
            </v-flex>
          </v-layout>
        </v-dialog>
      </v-flex>
    </v-layout>
  </section>
</template>

<script setup lang="ts">
import DateTimeInputs, {
  DateTimeType,
  HintTextType,
} from '@/components/common/date-picker/date_time_inputs.vue';
import FileUpload from '@/components/common/file-upload/index.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import Chains from '@/interface-models/FleetAsset/models/AdditionalEquipmentItem/AdditionalEquipmentObjects/ChainsDetails/AdditionalChainObjects/Chains';
import ChainsDetails from '@/interface-models/FleetAsset/models/AdditionalEquipmentItem/AdditionalEquipmentObjects/ChainsDetails/ChainsDetails';
import Attachment from '@/interface-models/Generic/Attachment/Attachment';
import AttachmentTypes from '@/interface-models/Generic/Attachment/AttachmentTypes';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { computed, ComputedRef, ref, Ref } from 'vue';

// PLEASE NOTE THAT THERE IS A ONE TO MANY RELATIONSHIP FOR ATTACHMENT TO CHAINS WITHIN THIS COMPONENT.
const props = withDefaults(
  defineProps<{
    chainsDetails: ChainsDetails | any;
    isEdited: boolean;
  }>(),
  {
    isEdited: false,
  },
);

const chain: Ref<Chains | null> = ref(null);
const attachment: Ref<Attachment | null> = ref(null);
const attachmentTypes = AttachmentTypes;
const editingChain: Ref<boolean> = ref(false);
const indexOfEditedChain: Ref<number> = ref(-1);
const isLoading: Ref<boolean> = ref(false);
const selectedCertificate: Ref<string | null> = ref(null);
const chainMaintenanceDialogIsOpen: Ref<boolean> = ref(false);

const headers: TableHeader[] = [
  {
    text: 'Serial Number',
    align: 'left',
    sortable: false,
    value: 'serialNumber',
  },
  {
    text: 'Inspection Date',
    align: 'left',
    sortable: false,
    value: 'inspectionDate',
  },
  {
    text: 'Certificate',
    align: 'right',
    sortable: false,
    value: '',
  },
];

function addChain(): void {
  chain.value = new Chains();
  setNewAttachment();
  chainMaintenanceDialogIsOpen.value = true;
}

function saveChain(): void {
  if (!chain.value) {
    return;
  }
  if (attachment.value && attachment.value.id) {
    // check if attachment already exists
    chain.value.attachmentId = attachment.value.id;
    const existingAttachment = props.chainsDetails.attachments.find(
      (x: Attachment) => x.id === attachment.value!.id,
    );
    if (!existingAttachment) {
      props.chainsDetails.attachments.push(attachment.value);
    }
  }
  props.chainsDetails.chains.push(chain.value);
  chain.value = null;
  chainMaintenanceDialogIsOpen.value = false;
  attachment.value = null;
  selectedCertificate.value = null;
}

const chainsWithExistingCertificates: ComputedRef<Chains[]> = computed(() => {
  return props.chainsDetails.chains.filter((x: Chains) => x.attachmentId);
});

function addExistingCertificate(attachmentId: any) {
  if (!chain.value) {
    return;
  }
  const foundAttachment = props.chainsDetails.attachments.find(
    (x: Attachment) => x.id === attachmentId,
  );

  if (foundAttachment) {
    chain.value.attachmentId = attachmentId;
    attachment.value = JSON.parse(JSON.stringify(foundAttachment));
  }
}

function saveEdit(): void {
  if (indexOfEditedChain.value === -1 || !chain.value) {
    return;
  }
  if (attachment.value && attachment.value.id) {
    // check if chains attachment was removed and replaced with another attachment
    if (
      props.chainsDetails.chains[indexOfEditedChain.value].attachmentId !==
      attachment.value.id
    ) {
      // Find whether the attachment that was removed is assigned to any other chain.
      // set and remove the attachment from the chain that is to be updated
      const attachmentIdToCheck =
        props.chainsDetails.chains[indexOfEditedChain.value].attachmentId;
      props.chainsDetails.chains[indexOfEditedChain.value].attachmentId = '';
      // find chains that have the attachment that was removed from this chain
      const chainsWithAttachment: Chains[] = props.chainsDetails.chains.filter(
        (x: Chains) => x.attachmentId === attachmentIdToCheck,
      );
      if (chainsWithAttachment.length === 0) {
        const attachmentIndexToCheck =
          props.chainsDetails.attachments.findIndex(
            (item) => item.id === attachmentIdToCheck,
          );

        if (attachmentIndexToCheck !== -1) {
          props.chainsDetails.attachments.splice(attachmentIndexToCheck, 1);
        }
      }
    }
    chain.value.attachmentId = attachment.value.id;
    const attachmentIndex = props.chainsDetails.attachments.findIndex(
      (item) => item.id === attachment.value!.id,
    );

    if (attachmentIndex !== -1) {
      props.chainsDetails.attachments.splice(
        attachmentIndex,
        1,
        attachment.value,
      );
    } else {
      props.chainsDetails.attachments.push(attachment.value);
    }
  } else {
    if (chain.value.attachmentId) {
      const attachmentIndex = props.chainsDetails.attachments.findIndex(
        (item) => item.id === chain.value!.attachmentId,
      );
      const chainsWithAttachment: Chains[] = props.chainsDetails.chains.filter(
        (x: Chains) => x.attachmentId === chain.value!.attachmentId,
      );
      if (chainsWithAttachment.length === 1) {
        props.chainsDetails.attachments.splice(attachmentIndex, 1);
      }
    }
    chain.value.attachmentId = '';
  }

  props.chainsDetails.chains.splice(indexOfEditedChain.value, 1, chain.value);
  chain.value = null;
  chainMaintenanceDialogIsOpen.value = false;
  editingChain.value = false;
  indexOfEditedChain.value = -1;
  selectedCertificate.value = null;
}

function deleteChain() {
  if (!chain.value) {
    return;
  }
  props.chainsDetails.chains.splice(indexOfEditedChain.value, 1);
  const attachmentIndex = props.chainsDetails.attachments.findIndex(
    (item) => item.id === chain.value!.attachmentId,
  );

  if (attachmentIndex !== -1) {
    // check that this attachment is applied to another chain
    const chainsWithAttachment: Chains[] = props.chainsDetails.chains.filter(
      (x: Chains) => x.attachmentId === chain.value!.attachmentId,
    );
    if (chainsWithAttachment.length === 0) {
      props.chainsDetails.attachments.splice(attachmentIndex, 1);
    }
  }

  chainMaintenanceDialogIsOpen.value = false;
  chain.value = null;
  attachment.value = null;
  editingChain.value = false;
  indexOfEditedChain.value = -1;
  selectedCertificate.value = null;
}

function viewChain(chains: Chains, indexOfEditedChains: number) {
  chain.value = JSON.parse(JSON.stringify(chains));
  if (!chain.value) {
    return;
  }
  indexOfEditedChain.value = indexOfEditedChains;
  chainMaintenanceDialogIsOpen.value = true;

  // check if chain has an attachment
  if (chain.value.attachmentId) {
    const attachmentIndex = props.chainsDetails.attachments.findIndex(
      (item) => item.id === chain.value!.attachmentId,
    );
    if (attachmentIndex !== -1) {
      attachment.value = JSON.parse(
        JSON.stringify(props.chainsDetails.attachments[attachmentIndex]),
      );
    } else {
      setNewAttachment();
    }
  } else {
    setNewAttachment();
  }
  editingChain.value = true;
}

function setNewAttachment() {
  attachment.value = new Attachment();
  attachment.value.documentTypeId = attachmentTypes.COMPLIANCE_CERTIFICATE;
}

function cancelMaintenance() {
  chainMaintenanceDialogIsOpen.value = false;
  chain.value = null;
  attachment.value = null;
  editingChain.value = false;
  indexOfEditedChain.value = -1;
  selectedCertificate.value = null;
}
</script>

<style scoped lang="scss">
.chains-additional-equipment-container {
  position: relative;
}

.add-new-chain-container {
  position: absolute;
  z-index: 5;
  top: -54px;
  right: -23px;
}

.gd-dark-theme {
  border: none !important;
}
</style>
