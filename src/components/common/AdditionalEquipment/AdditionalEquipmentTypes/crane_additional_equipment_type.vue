<template>
  <section class="crane-additional-equipment-type">
    <v-layout row wrap>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">1. Key Details</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Make
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
              label="Crane Make"
              v-model.trim="craneDetails.make"
              :disabled="!isEdited"
              :rules="[validate.required]"
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
                Model
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
              label="Crane Model"
              v-model.trim="craneDetails.model"
              :disabled="!isEdited"
              :rules="[validate.required]"
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Serial Number</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
              label="Serial Number"
              v-model.trim="craneDetails.serialNumber"
              :disabled="!isEdited"
            ></v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 :mb-4="!craneDetails.installationDate">
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Date of Manufacture</h6>
            </v-layout>
          </v-flex>
          <v-flex md8 v-if="!!craneDetails.installationDate">
            <v-layout>
              <v-flex>
                <DateTimeInputs
                  :epochTime.sync="craneDetails.installationDate"
                  :enableValidation="true"
                  :type="DateTimeType.DATE_START_OF_DAY"
                  dateLabel="Date of Manufacture"
                  :readOnly="true"
                  :soloInput="true"
                  :boxInput="false"
                  :hintTextType="HintTextType.FORMATTED_SELECTION"
                ></DateTimeInputs>
              </v-flex>
              <span>
                <v-menu left>
                  <template v-slot:activator="{ on: menu }">
                    <v-tooltip bottom>
                      <template v-slot:activator="{ on: tooltip }">
                        <v-btn flat icon v-on="{ ...tooltip, ...menu }">
                          <v-icon size="20">fas fa-ellipsis-v </v-icon>
                        </v-btn>
                      </template>
                      <span>View Additional Actions</span>
                    </v-tooltip>
                  </template>
                  <v-list dense class="v-list-custom">
                    <v-list-tile
                      @click="editManufactureDetails(JobOperationType.VIEW)"
                    >
                      <v-list-tile-title> View Details </v-list-tile-title>
                    </v-list-tile>
                    <v-list-tile
                      v-if="isEdited"
                      @click="editManufactureDetails(JobOperationType.EDIT)"
                      :disabled="!isEdited || !isAuthorised()"
                    >
                      <v-list-tile-title> Edit Details </v-list-tile-title>
                    </v-list-tile>
                  </v-list>
                </v-menu>
              </span>
            </v-layout>
          </v-flex>
          <v-flex md4 v-else>
            <v-btn
              depressed
              color="light-blue"
              outline
              @click="editManufactureDetails(JobOperationType.EDIT)"
              class="mx-0 v-btn-confirm-custom"
              :disabled="!isEdited"
              >Add Manufacture Details</v-btn
            >
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12 pb-1
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">2. Service History</h5>
          <span class="pr-2">
            <v-checkbox
              v-model="showRetiredMaintenanceRecords"
              color="light-blue"
              label="Show Inactive"
            ></v-checkbox>
          </span>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>

          <v-tooltip left>
            <template v-slot:activator="{ on }">
              <v-btn
                v-on="on"
                icon
                @click="newMaintenanceRecord()"
                class="mx-0"
                :disabled="!isEdited"
              >
                <v-icon size="20" color="light-blue">fal fa-plus</v-icon>
              </v-btn>
            </template>
            Add new Maintenance Record
          </v-tooltip>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Next Minor Service</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <DateTimeInputs
              :epochTime.sync="craneDetailsReadOnly.nextMinorServiceDate"
              :enableValidation="true"
              :type="DateTimeType.DATE_START_OF_DAY"
              dateLabel="Based on 1 year after date of manufacture or next minor service entered below"
              :readOnly="true"
              :soloInput="true"
              :boxInput="false"
              :persistentHint="true"
              :hintTextType="HintTextType.LABEL"
            ></DateTimeInputs>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 mb-3>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                <span class="pr-2">
                  <InformationTooltip
                    :left="true"
                    :tooltipType="HealthLevel.INFO"
                  >
                    <v-layout slot="content" row wrap>
                      <v-flex md12>
                        <p class="mb-1"></p>
                        <p class="mb-0" style="font-style: italic">
                          AS2550-11-2016 Vehicle Loading Cranes
                        </p>
                      </v-flex>
                    </v-layout>
                  </InformationTooltip>
                </span>
                Next Major Service
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <DateTimeInputs
              :epochTime.sync="craneDetailsReadOnly.nextMajorServiceDate"
              :enableValidation="true"
              :type="DateTimeType.DATE_START_OF_DAY"
              dateLabel="Based on 10 years after date of manufacture or next major service entered below"
              :readOnly="true"
              :soloInput="true"
              :boxInput="false"
              :persistentHint="true"
              :hintTextType="HintTextType.LABEL"
            ></DateTimeInputs>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3>
        <v-data-table
          class="gd-dark-theme"
          :items="maintenanceRecords"
          :headers="maintenanceTableHeaders"
          :rows-per-page-items="[5]"
        >
          <template v-slot:items="props">
            <td class="text-xs-left">{{ props.item.serviceType }}</td>
            <td class="text-xs-left">{{ props.item.readableServiceDate }}</td>
            <td class="text-xs-left">
              {{ props.item.readableNextServiceDate }}
            </td>
            <td class="text-xs-right">
              <v-menu left>
                <template v-slot:activator="{ on: menu }">
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on: tooltip }">
                      <v-btn
                        flat
                        icon
                        v-on="{ ...tooltip, ...menu }"
                        class="ma-0"
                      >
                        <v-icon size="16">fas fa-ellipsis-v </v-icon>
                      </v-btn>
                    </template>
                    <span>View Additional Actions</span>
                  </v-tooltip>
                </template>
                <v-list dense class="v-list-custom">
                  <v-list-tile
                    @click="
                      viewOrEditMaintenanceRecord(
                        props.item,
                        JobOperationType.VIEW,
                      )
                    "
                  >
                    <v-list-tile-title> View Record </v-list-tile-title>
                  </v-list-tile>
                  <v-list-tile
                    v-if="isEdited"
                    @click="
                      viewOrEditMaintenanceRecord(
                        props.item,
                        JobOperationType.EDIT,
                      )
                    "
                    :disabled="!isEdited || !isAuthorised()"
                  >
                    <v-list-tile-title> Edit Record </v-list-tile-title>
                  </v-list-tile>
                </v-list>
              </v-menu>
            </td>
          </template>
        </v-data-table>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">3. Lifting Capacity</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
          <span class="tags-chip mx-1" v-if="requiredLicenceType"
            >{{ requiredLicenceType }}
          </span>
          <span>
            <v-tooltip left>
              <template v-slot:activator="{ on }">
                <v-btn
                  v-on="on"
                  icon
                  @click="editRatedCapacities"
                  class="mx-0"
                  :disabled="!isEdited"
                >
                  <v-icon size="20" color="light-blue">{{
                    ratedCapacities.length ? 'fal fa-edit' : 'fal fa-plus'
                  }}</v-icon>
                </v-btn>
              </template>
              {{ ratedCapacities.length ? 'Edit' : 'Add' }} Crane Capacity
              Details
            </v-tooltip>
          </span>
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3>
        <v-data-table
          class="gd-dark-theme"
          :items="ratedCapacities"
          :headers="capacityTableHeaders"
          :rows-per-page-items="[5]"
          item-key="id"
        >
          <template v-slot:items="props">
            <td class="text-xs-left">{{ props.item.position }}</td>
            <td class="text-xs-right">{{ props.item.workingRadius }}</td>
            <td class="text-xs-right">{{ props.item.liftingCapacity }}</td>
            <td class="text-xs-right">{{ props.item.metreTonnesString }}</td>
          </template>
        </v-data-table>
      </v-flex>
    </v-layout>
    <v-layout wrap>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">4. Crane Images</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md6 class="pr-2 mb-4">
        <file-upload
          :imageLabel="'FRONT'"
          :attachmentSingle="false"
          :documentTypeId="attachmentTypes.FLEET_ASSET_FRONT"
          :attachmentArray="craneDetails.attachments"
          :formDisabled="!isEdited"
        >
        </file-upload>
      </v-flex>
      <v-flex md6 class="pl-2">
        <file-upload
          :imageLabel="'LEFT SIDE'"
          :attachmentSingle="false"
          :documentTypeId="attachmentTypes.FLEET_ASSET_LEFT_SIDE"
          :attachmentArray="craneDetails.attachments"
          :formDisabled="!isEdited"
        >
        </file-upload>
      </v-flex>
      <v-flex md6 class="pr-2 mb-4">
        <file-upload
          :imageLabel="'RIGHT SIDE'"
          :attachmentSingle="false"
          :documentTypeId="attachmentTypes.FLEET_ASSET_RIGHT_SIDE"
          :attachmentArray="craneDetails.attachments"
          :formDisabled="!isEdited"
        >
        </file-upload>
      </v-flex>
      <v-flex md6 class="pl-2">
        <file-upload
          :imageLabel="'REAR'"
          :attachmentSingle="false"
          :documentTypeId="attachmentTypes.FLEET_ASSET_REAR"
          :attachmentArray="craneDetails.attachments"
          :formDisabled="!isEdited"
        >
        </file-upload>
      </v-flex>
    </v-layout>

    <v-layout class="mt-4" align-center v-if="isAuthorisedAdmin()">
      <ConfirmationDialog
        v-if="isEdited"
        buttonText="Delete Crane Details"
        title="Delete Confirmation"
        message="Are you sure you wish to remove this Crane and all associated information?"
        @confirm="remove"
        :isOutlineButton="true"
        buttonColor="red"
        confirmationButtonText="Confirm Deletion"
        :dialogIsActive="true"
      ></ConfirmationDialog>
    </v-layout>
    <ContentDialog
      v-if="isViewingMaintenanceDialog"
      :showDialog.sync="isViewingMaintenanceDialog"
      title="Edit Maintenance Record"
      width="900px"
      contentPadding="pa-0"
      @cancel="isViewingMaintenanceDialog = false"
      @confirm="saveEditedMaintenanceRecord"
      :showActions="operationType !== 'VIEW'"
      confirmBtnText="Confirm"
      @action="retireMaintenanceRecord"
      :showActionButton="true"
      actionBtnText="Mark as Inactive"
      :actionRequiresConfirmation="true"
      actionConfirmationMessage="You are about to make this Maintenance Record INACTIVE. All service date information will be lost. If this is the only valid Maintenance Record, you will need to add a new one to satisfy any compliance requirements. Are you sure you wish to remove this Maintenance Record?"
    >
      <v-form ref="craneMaintenanceForm">
        <v-flex md12 class="body-scrollable--75 body-min-height--65">
          <v-layout>
            <v-flex md10 offset-md1>
              <v-layout align-center class="banner-custom px-3 py-2">
                <v-layout column>
                  <h3 v-if="operationType === JobOperationType.NEW">
                    New Service Record
                  </h3>
                  <h3 v-else>
                    Edit Service Record -
                    {{ editingMaintenanceRecord?.readableServiceDate }}
                  </h3>
                  <h4>
                    Add or edit details of a recent major or minor crane service
                  </h4>
                </v-layout>
                <span
                  class="tags-chip"
                  v-if="editingMaintenanceRecord?.serviceType"
                  >{{ editingMaintenanceRecord.serviceType }} Service</span
                >
              </v-layout>
              <v-divider class="mb-2 mt-1"></v-divider>
              <v-layout row wrap pa-3>
                <v-flex md12>
                  <v-layout>
                    <v-flex md3>
                      <v-layout align-center class="form-field-label-container">
                        <span
                          class="subheader--faded pr-3 pb-0 form-field-required-marker"
                        >
                          <span
                            class="pr-2"
                            v-if="
                              editingMaintenanceRecord?.serviceType === 'MAJOR'
                            "
                          >
                            <InformationTooltip
                              :left="true"
                              :tooltipType="HealthLevel.INFO"
                            >
                              <v-layout slot="content" row wrap>
                                <v-flex md12>
                                  <p class="mb-1"></p>
                                  <p class="mb-0" style="font-style: italic">
                                    The Australian Standards state that Vehicle
                                    Loading Cranes shall be subjected to a major
                                    inspection when they have reached an
                                    operational life of
                                    <strong>10 years</strong>, and
                                    <strong> every 5 years thereafter </strong>
                                    or when they have reached the due date for a
                                    major inspection as determined by an
                                    assessment in accordance with AS 2550.1
                                  </p>
                                </v-flex>
                              </v-layout>
                            </InformationTooltip>
                          </span>
                          Service Type</span
                        >
                      </v-layout>
                    </v-flex>
                    <v-flex md9>
                      <v-select
                        class="v-solo-custom"
                        solo
                        flat
                        :items="assetMaintenanceRecordTypes"
                        item-value="key"
                        item-text="value"
                        color="light-blue"
                        label="Service Type"
                        v-if="editingMaintenanceRecord"
                        v-model="editingMaintenanceRecord.serviceType"
                        :disabled="!isEdited || operationType === 'VIEW'"
                      ></v-select>
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex md12>
                  <v-layout>
                    <v-flex md3>
                      <v-layout align-center class="form-field-label-container">
                        <span
                          class="subheader--faded pr-3 pb-0 form-field-required-marker"
                          >Date of Service</span
                        >
                      </v-layout>
                    </v-flex>
                    <v-flex md9>
                      <DateTimeInputs
                        v-if="editingMaintenanceRecord"
                        :epochTime.sync="editingMaintenanceRecord.serviceDate"
                        :enableValidation="true"
                        :type="DateTimeType.DATE_START_OF_DAY"
                        dateLabel="Date of Service"
                        :readOnly="!isEdited"
                        :soloInput="true"
                        :boxInput="false"
                        :isRequired="true"
                        :hintTextType="HintTextType.FORMATTED_SELECTION"
                      ></DateTimeInputs>
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex md12>
                  <v-layout>
                    <v-flex md3>
                      <v-layout align-center class="form-field-label-container">
                        <span
                          class="subheader--faded pr-3 pb-0 form-field-required-marker"
                          >Date of Next Service</span
                        >
                      </v-layout>
                    </v-flex>
                    <v-flex md9>
                      <DateTimeInputs
                        v-if="editingMaintenanceRecord"
                        :epochTime.sync="
                          editingMaintenanceRecord.nextServiceDate
                        "
                        :type="DateTimeType.DATE_START_OF_DAY"
                        dateLabel="Date of Next Service"
                        :readOnly="!isEdited"
                        :soloInput="true"
                        :boxInput="false"
                        :hintTextType="HintTextType.FORMATTED_SELECTION"
                      ></DateTimeInputs>
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex md12>
                  <v-layout>
                    <v-flex md3>
                      <v-layout align-center class="form-field-label-container">
                        <h6 class="subheader--faded pr-3 pb-0">
                          Supporting Document
                        </h6>
                      </v-layout>
                    </v-flex>
                    <v-flex md6>
                      <file-upload
                        :imageLabel="'COMPLIANCE CERTIFICATE'"
                        :attachmentSingle="true"
                        v-if="editingMaintenanceRecord"
                        :attachment="
                          editingMaintenanceRecord.supportingDocumentation
                        "
                        :documentTypeId="
                          editingMaintenanceRecord.serviceType === 'MINOR'
                            ? attachmentTypes.CRANE_MINOR_COMPLIANCE_CERTIFICATE
                            : attachmentTypes.CRANE_MAJOR_COMPLIANCE_CERTIFICATE
                        "
                        :formDisabled="!isEdited"
                      >
                      </file-upload>
                    </v-flex>
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-form>
    </ContentDialog>
    <ContentDialog
      v-if="isViewingManufactureDetailsDialog"
      :showDialog.sync="isViewingManufactureDetailsDialog"
      title="Edit Manufacture Details"
      width="900px"
      contentPadding="pa-0"
      @cancel="isViewingManufactureDetailsDialog = false"
      @confirm="saveEditedManufactureDetails"
      :showActions="operationType !== 'VIEW'"
      confirmBtnText="Confirm"
    >
      <v-form ref="craneManufactureDetailsForm">
        <v-flex md12 class="body-scrollable--75 body-min-height--65">
          <v-layout>
            <v-flex md10 offset-md1>
              <v-layout align-center class="banner-custom px-3 py-2">
                <v-layout column>
                  <h3>Manufacture Details</h3>

                  <h4>
                    The Date of Manufacture is recorded on the Crane's
                    'Compliance Plate'. This may be in the form of Month/Year or
                    simply just Year
                  </h4>
                </v-layout>
              </v-layout>
              <v-divider class="mb-2 mt-1"></v-divider>
              <v-layout row wrap pa-3>
                <v-flex md12>
                  <v-layout>
                    <v-flex md4>
                      <v-layout align-center class="form-field-label-container">
                        <span
                          class="subheader--faded pr-3 pb-0 form-field-required-marker"
                          >Date of Manufacture</span
                        >
                      </v-layout>
                    </v-flex>
                    <v-flex md8>
                      <DateTimeInputs
                        v-if="editingManufactureDetails"
                        :epochTime.sync="
                          editingManufactureDetails.installationDate
                        "
                        :enableValidation="true"
                        :type="DateTimeType.DATE_START_OF_DAY"
                        dateLabel="Date of Manufacture"
                        :readOnly="!isEdited || operationType === 'VIEW'"
                        :soloInput="true"
                        :boxInput="false"
                        :isRequired="true"
                        :hintTextType="HintTextType.FORMATTED_SELECTION"
                      ></DateTimeInputs>
                    </v-flex>
                    <span class="pa-3">
                      <InformationTooltip
                        :left="true"
                        :tooltipType="HealthLevel.INFO"
                      >
                        <v-layout slot="content" row wrap>
                          <v-flex md12>
                            <p class="mb-1">
                              The first
                              <strong>major service due date</strong> is
                              calculated as 10 years from the Date of
                              Manufacture.
                            </p>
                          </v-flex>
                        </v-layout>
                      </InformationTooltip>
                    </span>
                  </v-layout>
                </v-flex>

                <v-flex md12>
                  <v-layout>
                    <v-flex md4>
                      <v-layout align-center class="form-field-label-container">
                        <h6 class="subheader--faded pr-3 pb-0">
                          Compliance Plate
                        </h6>
                      </v-layout>
                    </v-flex>
                    <v-flex md6>
                      <file-upload
                        v-if="editingManufactureDetails?.compliancePlate"
                        :imageLabel="'COMPLIANCE PLATE'"
                        :attachmentSingle="true"
                        :attachment="editingManufactureDetails.compliancePlate"
                        :documentTypeId="attachmentTypes.CRANE_COMPLIANCE_PLATE"
                        :formDisabled="!isEdited || operationType === 'VIEW'"
                      >
                      </file-upload>
                    </v-flex>
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-form>
    </ContentDialog>
    <ContentDialog
      v-if="isViewingCapacitiesDialog"
      :showDialog.sync="isViewingCapacitiesDialog"
      title="Edit Crane Lifting Capacities"
      width="900px"
      contentPadding="pa-0"
      @cancel="isViewingCapacitiesDialog = false"
      @confirm="saveEditedCraneCapacities"
      :showActions="true"
      confirmBtnText="Confirm"
    >
      <v-form ref="craneCapacityForm" v-if="editingRatedCapacities">
        <v-flex md12 class="body-scrollable--75 body-min-height--65">
          <v-layout>
            <v-flex md10 offset-md1>
              <v-layout align-center class="banner-custom px-3 py-2">
                <v-flex md6>
                  <v-layout column>
                    <h3>Add/Edit Lifting Capacity</h3>
                    <h4>
                      Using the crane's lifting chart, please enter the load
                      capacity at each point along the boom.
                    </h4>
                  </v-layout>
                </v-flex>
                <v-spacer></v-spacer>
                <span class="tags-chip mr-1" v-if="metreTonnage">{{
                  `${DisplayCurrencyValue(metreTonnage)} m/t`
                }}</span>
                <span class="tags-chip" v-if="requiredLicenceType"
                  >{{ requiredLicenceType }}
                </span>
              </v-layout>
              <v-divider class="mb-2 mt-1"></v-divider>
              <v-layout row wrap pa-3>
                <v-flex md12>
                  <table class="simple-data-table">
                    <thead>
                      <tr>
                        <th>Position</th>
                        <th>Distance (m)</th>
                        <th>Rated Load (kg)</th>
                        <th>Metre Tonnes (m/t)</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        v-for="item in editingRatedCapacities"
                        :key="item.id"
                        style="position: relative"
                      >
                        <td>
                          <span class="tags-chip">{{ item.position }}</span>
                        </td>
                        <td class="text-xs-right">
                          <v-text-field
                            class="v-solo-custom"
                            :id="item.id"
                            solo
                            flat
                            color="light-blue"
                            label="Distance"
                            type="number"
                            :rules="[validate.required]"
                            hide-details
                            suffix="m"
                            v-model.number="item.workingRadius"
                          ></v-text-field>
                        </td>
                        <td class="text-xs-right">
                          <v-text-field
                            class="v-solo-custom"
                            solo
                            flat
                            color="light-blue"
                            label="Lifting Capacity"
                            type="number"
                            hide-details
                            suffix="kg"
                            v-model.number="item.liftingCapacity"
                          ></v-text-field>
                        </td>
                        <td class="text-xs-right">
                          <v-tooltip left max-width="300px">
                            <template v-slot:activator="{ on }">
                              <span
                                v-on="item.metreTonnes >= 10 ? on : null"
                                class="metre-tonne-value"
                                :class="{
                                  'with-accent': item.metreTonnes >= 10,
                                }"
                                >{{ item.metreTonnesString }}</span
                              >
                            </template>
                            <span
                              >A High Risk licence is needed to operate a
                              Vehicle Loading Crane over 10 metre tonnes.</span
                            >
                          </v-tooltip>
                        </td>
                        <span style="position: absolute; left: 100%; top: 3px">
                          <v-tooltip left>
                            <template v-slot:activator="{ on }">
                              <v-btn
                                flat
                                v-on="on"
                                icon
                                @click="removeRatedCapacity(item)"
                                class="mx-0"
                                tabindex="-1"
                              >
                                <v-icon size="20" color="grey lighten-1"
                                  >fal fa-times</v-icon
                                >
                              </v-btn>
                            </template>
                            Remove Row
                          </v-tooltip>
                        </span>
                      </tr>
                      <tr class="last-row">
                        <td colspan="4">
                          <v-layout justify-center align-center pt-1>
                            <button
                              type="button"
                              class="add-item-button"
                              @click="addRatedCapacitiesRow"
                            >
                              <v-layout align-center>
                                <span class="pr-2">ADD NEW</span>
                                <v-icon class="add-item-icon"
                                  >fal fa-plus</v-icon
                                >
                              </v-layout>
                            </button>
                          </v-layout>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </v-flex>
                <v-flex md12 pb-3
                  ><v-layout align-center>
                    <h5 class="subheader--bold pr-3 pt-1">
                      Attach Lifting Chart
                    </h5>
                    <v-flex>
                      <v-divider></v-divider>
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex md10 offset-md1>
                  <file-upload
                    :imageLabel="'Lifting Chart'"
                    :attachmentSingle="false"
                    :documentTypeId="attachmentTypes.CRANE_RATING"
                    :attachmentArray="craneDetails.attachments"
                    :formDisabled="!isEdited"
                  >
                  </file-upload>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-form>
    </ContentDialog>
  </section>
</template>

<script setup lang="ts">
import DateTimeInputs, {
  DateTimeType,
  HintTextType,
} from '@/components/common/date-picker/date_time_inputs.vue';
import FileUpload from '@/components/common/file-upload/index.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  hasAdminOrHeadOfficeRole,
  hasAdminRole,
} from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import AssetMaintenanceRecord from '@/interface-models/FleetAsset/AssetMaintenance/AssetMaintenanceRecord';
import { AssetMaintenanceRecordType } from '@/interface-models/FleetAsset/AssetMaintenance/AssetMaintenanceRecordType';
import CraneDetails from '@/interface-models/FleetAsset/models/AdditionalEquipmentItem/AdditionalEquipmentObjects/CraneDetails';
import CraneRatedCapacity from '@/interface-models/FleetAsset/models/AdditionalEquipmentItem/AdditionalEquipmentObjects/CraneRatedCapacity';
import Attachment from '@/interface-models/Generic/Attachment/Attachment';
import AttachmentTypes from '@/interface-models/Generic/Attachment/AttachmentTypes';
import { KeyValue } from '@/interface-models/Generic/KeyValue/KeyValue';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { JobOperationType } from '@/interface-models/Jobs/JobOperationType';
import { v4 as uuidv4 } from 'uuid';
import {
  ComputedRef,
  Ref,
  WritableComputedRef,
  computed,
  nextTick,
  ref,
  set,
} from 'vue';

interface ManufacturerDetails {
  installationDate?: number | undefined;
  compliancePlate: Attachment;
}

const props = withDefaults(
  defineProps<{
    craneDetails: CraneDetails | any;
    isEdited: boolean;
  }>(),
  {
    isEdited: false,
  },
);

const emit = defineEmits(['removeEquipmentItem']);

const validate = validationRules;
const attachmentTypes = AttachmentTypes;
const showRetiredMaintenanceRecords: Ref<boolean> = ref(false);
const editingMaintenanceRecord: Ref<AssetMaintenanceRecord | null> = ref(null);
const editingRatedCapacities: Ref<CraneRatedCapacity[] | null> = ref(null);
const editingManufactureDetails: Ref<ManufacturerDetails | null> = ref(null);
const operationType: Ref<JobOperationType | null> = ref(null);

const craneMaintenanceForm: Ref<any> = ref(null);
const craneCapacityForm: Ref<any> = ref(null);
const craneManufactureDetailsForm: Ref<any> = ref(null);

const assetMaintenanceRecordTypes: KeyValue[] = [
  { key: AssetMaintenanceRecordType.MINOR, value: 'Minor' },
  { key: AssetMaintenanceRecordType.MAJOR, value: 'Major' },
];

const maintenanceTableHeaders: TableHeader[] = [
  {
    text: 'Type',
    align: 'left',
    sortable: true,
    value: 'serviceType',
  },
  {
    text: 'Service Date',
    align: 'left',
    value: 'serviceDate',
    sortable: true,
  },
  {
    text: 'Next Service Date',
    align: 'left',
    value: 'nextServiceDate',
    sortable: true,
  },
  {
    text: '',
    align: 'right',
    value: 'nextServiceDate',
  },
];

const capacityTableHeaders: TableHeader[] = [
  {
    text: 'Position',
    align: 'left',
    sortable: true,
    value: 'position',
  },
  {
    text: 'Working Radius',
    align: 'right',
    value: 'workingRadius',
    sortable: true,
  },
  {
    text: 'Lifting Capacity (kg)',
    align: 'right',
    value: 'liftingCapacity',
    sortable: true,
  },
  {
    text: 'Metre Tonnes (m/t)',
    align: 'right',
    value: 'metreTonnes',
  },
];

/**
 * Returns the craneDetails prop, initialised such that we can read from getters
 * on the instance and display them in the template.
 */
const craneDetailsReadOnly: ComputedRef<CraneDetails> = computed(() => {
  return Object.assign(new CraneDetails(), props.craneDetails);
});

/**
 * From craneDetails, returns the list of maintenance history records, sorted
 * in descending order by serviceData
 */
const maintenanceRecords: ComputedRef<AssetMaintenanceRecord[]> = computed(
  () => {
    const craneDetails = props.craneDetails as CraneDetails;
    if (!craneDetails.maintenanceHistory) {
      return [];
    }
    return craneDetails.maintenanceHistory
      .filter(
        (r: AssetMaintenanceRecord) =>
          showRetiredMaintenanceRecords.value || r.nextServiceDate !== 0,
      )
      .map((record: AssetMaintenanceRecord) =>
        Object.assign(new AssetMaintenanceRecord(), record),
      )
      .sort((a: AssetMaintenanceRecord, b: AssetMaintenanceRecord) => {
        return (
          (b.serviceDate ? b.serviceDate : 0) -
          (a.serviceDate ? a.serviceDate : 0)
        );
      });
  },
);

/**
 * From craneDetails, returns the initialised list of CraneRatedCapacity
 * records, sorted in ascending order by position. Used in template to display
 * in the Rated Capacity table, where we need access to instance properties
 */
const ratedCapacities: ComputedRef<CraneRatedCapacity[]> = computed(() => {
  if (!props.craneDetails.ratedCapacities) {
    return [];
  }
  return props.craneDetails.ratedCapacities
    .map((capacity: CraneRatedCapacity) =>
      Object.assign(new CraneRatedCapacity(), capacity),
    )
    .sort((a: CraneRatedCapacity, b: CraneRatedCapacity) => {
      return a.position - b.position;
    });
});

/**
 * Returns the highest value of CraneRatedCapacity.metreTonnes from the
 * currently editing list (if we're editing) or the
 * craneDetails.ratedCapacities list
 */
const metreTonnage: ComputedRef<number> = computed(() => {
  const capacitiesToCheck =
    editingRatedCapacities.value !== null
      ? editingRatedCapacities.value
      : ratedCapacities.value
        ? ratedCapacities.value
        : [];
  // return the highest value of CraneRatedCapacity.metreTonnes
  return capacitiesToCheck
    .map((r) => r.metreTonnes)
    .reduce((a, b) => {
      return Math.max(a, b);
    }, 0);
});

/**
 * Gets the required licence type based on the metre tonnage of the crane. If
 * the metre tonnage is greater than or equal to 10, 'HR Licence' is
 * required. Otherwise, 'VOC' is require.
 * @returns The required licence type.
 */
const requiredLicenceType: ComputedRef<string> = computed(() => {
  if (metreTonnage.value === 0) {
    return '';
  }
  return metreTonnage.value >= 10 ? 'HR Licence Required' : 'VOC Required';
});

/**
 * Controls visibility of the maintenance record dialog. Clears editing list
 * of records when closed.
 */
const isViewingMaintenanceDialog: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return editingMaintenanceRecord.value !== null;
  },
  set(value: boolean): void {
    if (!value) {
      editingMaintenanceRecord.value = null;
    }
  },
});

/**
 * Controls visibility of the rated capacities dialog. Clears editing list of
 * rated capacities when closed.
 */
const isViewingCapacitiesDialog: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return editingRatedCapacities.value !== null;
  },
  set(value: boolean): void {
    if (!value) {
      editingRatedCapacities.value = null;
    }
  },
});

/**
 * Controls visibility of the manufacture details dialog. Clears
 * manufacture details when closed.
 */
const isViewingManufactureDetailsDialog: WritableComputedRef<boolean> =
  computed({
    get(): boolean {
      return editingManufactureDetails.value !== null;
    },
    set(value: boolean): void {
      if (!value) {
        operationType.value = null;
        editingManufactureDetails.value = null;
      }
    },
  });

/**
 * For the provide AssetMaintenanceRecord, copy it to the local variable and
 * open the dialog for editing or viewing
 * @param record The maintenance record to be edited or viewed.
 */
function viewOrEditMaintenanceRecord(
  record: AssetMaintenanceRecord,
  operationTypes: JobOperationType,
) {
  operationType.value = operationTypes;
  const maintenanceRecord = Object.assign(new AssetMaintenanceRecord(), record);
  if (!maintenanceRecord.supportingDocumentation) {
    maintenanceRecord.supportingDocumentation = new Attachment();
  }
  editingMaintenanceRecord.value = maintenanceRecord;
}

/**
 * Set editingMaintenanceRecord to a new AssetMaintenanceRecord, which will
 * open the dialog
 */
function newMaintenanceRecord() {
  operationType.value = JobOperationType.NEW;
  const newRecord = new AssetMaintenanceRecord();
  newRecord.supportingDocumentation = new Attachment();
  editingMaintenanceRecord.value = newRecord;
}
/**
 * Validates the form and saves the edited maintenance record to the
 * craneDetails. If we were editing a record, it will replace the old record
 * with the new one. If we were creating a new record, it will push it into
 * the list of service records
 */
function saveEditedMaintenanceRecord() {
  const editingMaintenanceRecords = editingMaintenanceRecord.value;
  if (!editingMaintenanceRecords) {
    return;
  }
  if (craneMaintenanceForm.value && !craneMaintenanceForm.value.validate()) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }
  // If there is no id in the attachment, then remove the property
  if (
    editingMaintenanceRecords.supportingDocumentation &&
    !editingMaintenanceRecords.supportingDocumentation.id
  ) {
    delete editingMaintenanceRecords.supportingDocumentation;
  }

  // If the maintenanceHistory property doesn't exist, create it
  if (!props.craneDetails.maintenanceHistory) {
    set(props.craneDetails, 'maintenanceHistory', []);
  }

  // If we're editing a record, replace the old record with the new one
  const index = props.craneDetails.maintenanceHistory!.findIndex(
    (record: AssetMaintenanceRecord) =>
      record._id === editingMaintenanceRecords!._id,
  );
  if (index >= 0) {
    props.craneDetails.maintenanceHistory!.splice(
      index,
      1,
      editingMaintenanceRecords,
    );
  } else {
    // Otherwise, push the new record into the list
    props.craneDetails.maintenanceHistory!.push(editingMaintenanceRecords);
  }
  isViewingMaintenanceDialog.value = false;
}

/**
 * Retires the maintenance record for the crane by setting the serviceDate and
 * nextServiceDate of the editingMaintenanceRecord are set to 0.
 */
function retireMaintenanceRecord() {
  if (!editingMaintenanceRecord.value) {
    showNotification('Error saving maintenance record');
    return;
  }
  editingMaintenanceRecord.value.serviceDate = 0;
  editingMaintenanceRecord.value.nextServiceDate = 0;

  const index = props.craneDetails.maintenanceHistory!.findIndex(
    (record: AssetMaintenanceRecord) =>
      record._id === editingMaintenanceRecord.value!._id,
  );
  if (index >= 0) {
    props.craneDetails.maintenanceHistory!.splice(
      index,
      1,
      editingMaintenanceRecord.value,
    );
  } else {
    showNotification('Error saving maintenance record');
  }
  isViewingMaintenanceDialog.value = false;
}

/**
 * Remove the provided CraneRatedCapacity from editingRatedCapacities list
 */
function removeRatedCapacity(capacity: CraneRatedCapacity) {
  if (!editingRatedCapacities.value) {
    return;
  }
  const index = editingRatedCapacities.value.findIndex(
    (c) => c.id === capacity.id,
  );
  if (index >= 0) {
    editingRatedCapacities.value.splice(index, 1);
  }
  // Update the value for position for each remaining rated capacity, such
  // that they're in ascending order with no gaps
  editingRatedCapacities.value.forEach((r, i) => {
    r.position = i + 1;
  });
}

/**
 * Opens the dialog for editing the crane's list of rated capacities
 */
function editRatedCapacities() {
  // Clone the list of rated capacities to the local variable. If there are no
  // entries, or the property is undefined, then set it to a list with a
  // single entry with default values
  if (props.craneDetails.ratedCapacities) {
    editingRatedCapacities.value = props.craneDetails.ratedCapacities.map(
      (capacity: CraneRatedCapacity) => {
        return Object.assign(new CraneRatedCapacity(), {
          id: uuidv4(),
          ...capacity,
        });
      },
    );
  } else {
    const uuid = uuidv4();
    editingRatedCapacities.value = [
      Object.assign(new CraneRatedCapacity(), {
        id: uuid,
        position: 1,
        workingRadius: 0,
        liftingCapacity: 0,
      }),
    ];
    nextTick().then(() => {
      const textField = document.getElementById(uuid);
      if (textField instanceof HTMLInputElement) {
        textField.focus();
      }
    });
  }
}

/**
 * Adds a new row to the editingRatedCapacities array and sets the position,
 * workingRadius, and liftingCapacity properties of the new row to default
 * values. Focuses on the textfield in the new row.
 */
function addRatedCapacitiesRow() {
  if (!editingRatedCapacities.value) {
    return;
  }
  const newId = uuidv4();
  editingRatedCapacities.value.push(
    Object.assign(new CraneRatedCapacity(), {
      id: newId,
      position: editingRatedCapacities.value.length + 1,
      workingRadius: 0,
      liftingCapacity: 0,
    }),
  );
  // Focus textfield in new row
  nextTick().then(() => {
    const textField = document.getElementById(newId);
    if (textField instanceof HTMLInputElement) {
      textField.focus();
    }
  });
}

/**
 * Validates and saves the edited crane capacities. Filters out the edits that
 * have empty working radius or lifting capacity, then sets the updated values
 * to craneDetails and closes the dialog.
 */
function saveEditedCraneCapacities() {
  if (!editingRatedCapacities.value) {
    return;
  }
  const filteredEdits = editingRatedCapacities.value.filter(
    (e) => !!e.workingRadius && !!e.liftingCapacity,
  );
  if (filteredEdits.length === 0) {
    showNotification('Please enter at least one rated capacity');
    return;
  }
  if (!craneCapacityForm.value.validate()) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }
  if (!props.craneDetails.ratedCapacities) {
    set(props.craneDetails, 'ratedCapacities', []);
  }
  props.craneDetails.ratedCapacities = filteredEdits;
  isViewingCapacitiesDialog.value = false;
}

/**
 * Edits the manufacture details of the crane additional equipment type.
 */
function editManufactureDetails(operationTypes: JobOperationType) {
  const compliancePlate = props.craneDetails.attachments.find(
    (a: Attachment) =>
      a.documentTypeId === AttachmentTypes.CRANE_COMPLIANCE_PLATE,
  );
  operationType.value = operationTypes;
  editingManufactureDetails.value = {
    installationDate: props.craneDetails.installationDate,
    compliancePlate: compliancePlate
      ? Object.assign(new Attachment(), compliancePlate)
      : new Attachment(),
  };
}

/**
 * Validates and saves the edited manufacture details. Sets the updated values
 * to craneDetails and closes the dialog.
 */
function saveEditedManufactureDetails() {
  if (!craneManufactureDetailsForm.value.validate()) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }
  if (editingManufactureDetails.value) {
    props.craneDetails.installationDate =
      editingManufactureDetails.value.installationDate;
    updateCompliancePlate(
      props.craneDetails.attachments,
      editingManufactureDetails.value.compliancePlate,
    );
  }
  isViewingManufactureDetailsDialog.value = false;
}

/**
 * Updates the compliance plate attachment in the attachment list.
 * If the editing attachment already exists in the list, it replaces the existing one.
 * If the editing attachment is new, it is added to the list.
 * If the editing attachment is null, the existing compliance plate attachment is removed from the list.
 *
 * @param attachmentList - The list of attachments.
 * @param editingAttachment - The attachment being edited.
 */
function updateCompliancePlate(
  attachmentList: Attachment[],
  editingAttachment: Attachment,
) {
  const foundExistingCompliancePlate = attachmentList.find(
    (a) => a.documentTypeId === AttachmentTypes.CRANE_COMPLIANCE_PLATE,
  );
  if (foundExistingCompliancePlate && editingAttachment.id) {
    const index = props.craneDetails.attachments.findIndex(
      (a: Attachment) =>
        a.documentTypeId === AttachmentTypes.CRANE_COMPLIANCE_PLATE,
    );
    props.craneDetails.attachments.splice(index, 1, editingAttachment);
  } else if (!foundExistingCompliancePlate && editingAttachment.id) {
    props.craneDetails.attachments.push(editingAttachment);
  } else if (foundExistingCompliancePlate && !editingAttachment.id) {
    const index = props.craneDetails.attachments.findIndex(
      (a: Attachment) =>
        a.documentTypeId === AttachmentTypes.CRANE_COMPLIANCE_PLATE,
    );
    props.craneDetails.attachments.splice(index, 1);
  }
}

function remove(): void {
  emit('removeEquipmentItem', 1);
}

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}
function isAuthorisedAdmin(): boolean {
  return hasAdminRole();
}
</script>

<style scoped lang="scss">
.last-row {
  .add-item-button {
    transition: 0.3s;
    padding: 6px 18px;
    border-radius: 12px;
    background-color: rgb(235, 235, 235);
    color: $table-bg;
    font-weight: 700;
    opacity: 0.8;
    transition: opacity 0.1s ease-in-out;
    font-family: $sub-font-family;

    .add-item-icon {
      color: $table-bg;
    }
    &:focus {
      opacity: 1;
      scale: 1.05;
    }
  }
  &:hover {
    background-color: red;
    .add-item-button {
      opacity: 0.8;
      &:hover {
        opacity: 1;
        cursor: pointer;
      }
    }
  }
}

.metre-tonne-value {
  &.with-accent {
    font-weight: 700;

    color: rgb(255, 230, 0);
  }
}

.simple-data-table {
  tr {
    position: relative;
  }
  tr:last-child {
    background-color: transparent;
  }
}

.gd-dark-theme {
  border: none !important;
}
</style>
