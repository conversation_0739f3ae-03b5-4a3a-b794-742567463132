<template>
  <v-layout row wrap class="additional-equipment" pb-3>
    <v-flex md12>
      <v-select
        :disabled="!isEdited"
        :items="clonedEquipmentTypes"
        label="Please Select Additional Equipment To Add To Fleet Asset"
        item-text="longName"
        item-value="id"
        v-model="selectedEquipmentTypes"
        :class="!isEdited ? 'solo-input-disable-display' : ''"
        solo
        :item-disabled="disableSelectOfEquipmentType"
        flat
        color="light-blue"
        class="v-solo-custom"
        multiple
        persistent-hint
        hint="Some equipment can not be deselected until the associated equipment is removed below."
      >
      </v-select>
    </v-flex>

    <v-flex
      md12
      mb-3
      v-if="selectedEquipmentTypes && selectedEquipmentTypes.length"
    >
      <v-divider></v-divider>
    </v-flex>
    <v-flex md12 mb-2 v-if="selectedEquipmentTypes.includes(1)">
      <v-layout row wrap class="pa-2 app-bgcolor--400 app-bordercolor--600">
        <v-flex md12 pt-1>
          <v-layout justify-space-between align-center px-2>
            <h3 class="subheader--light">Crane Details</h3>
          </v-layout>
        </v-flex>
        <v-flex md12 pa-3>
          <v-layout align-center>
            <CraneAdditionalEquipmentType
              :craneDetails="dynamicEquipmentProp(1)"
              @removeEquipmentItem="removeEquipmentItem"
              :isEdited="isEdited"
            >
            </CraneAdditionalEquipmentType>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>
    <!-- TAIL GATE-->
    <v-flex md12 mb-2 v-if="selectedEquipmentTypes.includes(2)">
      <v-layout row wrap class="pa-2 app-bgcolor--400 app-bordercolor--600">
        <v-flex md12 pt-1>
          <v-layout justify-space-between align-center px-2>
            <h3 class="subheader--light">Tail Gate Details</h3>
          </v-layout>
        </v-flex>
        <v-flex md12 pa-3>
          <v-layout align-center>
            <TailGateAdditionalEquipmentType
              :tailGateDetails="dynamicEquipmentProp(2)"
              @removeEquipmentItem="removeEquipmentItem"
              :isEdited="isEdited"
            >
            </TailGateAdditionalEquipmentType>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>
    <v-flex md12 mb-2 v-if="selectedEquipmentTypes.includes(3)">
      <GenericAdditionalEquipment
        :equipmentTypeId="3"
        :equipmentDetails="dynamicEquipmentProp(3)"
        @removeEquipmentItem="removeEquipmentItem"
        :isEdited="isEdited"
      >
      </GenericAdditionalEquipment>
    </v-flex>
    <v-flex md12 mb-2 v-if="selectedEquipmentTypes.includes(4)">
      <ChainsAdditionalEquipmentType
        :isEdited="isEdited"
        :chainsDetails="dynamicEquipmentProp(4)"
      >
      </ChainsAdditionalEquipmentType>
    </v-flex>

    <v-flex md12 mb-2 v-if="selectedEquipmentTypes.includes(17)">
      <GenericAdditionalEquipment
        :equipmentTypeId="17"
        :equipmentDetails="dynamicEquipmentProp(17)"
        :isEdited="isEdited"
      >
      </GenericAdditionalEquipment>
    </v-flex>
    <v-flex md12 mb-2 v-if="selectedEquipmentTypes.includes(18)">
      <GenericAdditionalEquipment
        :equipmentTypeId="18"
        :equipmentDetails="dynamicEquipmentProp(18)"
        :isEdited="isEdited"
      >
      </GenericAdditionalEquipment>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import type FleetAsset from '@/interface-models/FleetAsset/FleetAsset';
import AdditionalEquipmentItem from '@/interface-models/FleetAsset/models/AdditionalEquipmentItem/AdditionalEquipmentItem';
import ChainsDetails from '@/interface-models/FleetAsset/models/AdditionalEquipmentItem/AdditionalEquipmentObjects/ChainsDetails/ChainsDetails';
import CraneDetails from '@/interface-models/FleetAsset/models/AdditionalEquipmentItem/AdditionalEquipmentObjects/CraneDetails';
import GateDetails from '@/interface-models/FleetAsset/models/AdditionalEquipmentItem/AdditionalEquipmentObjects/GatesDetails';
import { GenericEquipmentDetails } from '@/interface-models/FleetAsset/models/AdditionalEquipmentItem/AdditionalEquipmentObjects/GenericEquipmentDetails';
import StrapDetails from '@/interface-models/FleetAsset/models/AdditionalEquipmentItem/AdditionalEquipmentObjects/StrapDetails/StrapDetails';
import TailGateDetails from '@/interface-models/FleetAsset/models/AdditionalEquipmentItem/AdditionalEquipmentObjects/TailGateDetails';
import TimberDetails from '@/interface-models/FleetAsset/models/AdditionalEquipmentItem/AdditionalEquipmentObjects/TimbersDetails';
import {
  type EquipmentTypes,
  equipmentTypes,
} from '@/interface-models/Generic/EquipmentTypes/EquipmentTypes';
import { onMounted, ref, Ref, watch } from 'vue';
import ChainsAdditionalEquipmentType from './AdditionalEquipmentTypes/chains_additional_equipment_type.vue';
import CraneAdditionalEquipmentType from './AdditionalEquipmentTypes/crane_additional_equipment_type.vue';
import GenericAdditionalEquipment from './AdditionalEquipmentTypes/generic_additional_equipment.vue';
import TailGateAdditionalEquipmentType from './AdditionalEquipmentTypes/tail_gate_additional_equipment_type.vue';

const props = withDefaults(
  defineProps<{
    fleetAsset: FleetAsset;
    boxInput?: boolean;
    soloInput: boolean;
    isEdited: boolean;
  }>(),
  {
    boxInput: false,
    soloInput: false,
    isEdited: false,
  },
);

const selectedEquipmentTypes: Ref<number[]> = ref([]);
// const equipmentType: EquipmentTypes[] = equipmentTypes;
const clonedEquipmentTypes: EquipmentTypes[] = JSON.parse(
  JSON.stringify(equipmentTypes),
);

// find new id and push correct model to equipment item from array
watch(selectedEquipmentTypes, (val, oldVal) => {
  for (const id of val) {
    if (!oldVal.includes(id)) {
      switch (id) {
        case 1: // Crane
          equipmentListHandler(id, new CraneDetails());
          break;
        case 2: // Tail Gate
          equipmentListHandler(id, new TailGateDetails());
          break;
        case 3: // Straps
        case 17: // Lifting chains
        case 18: // Lifting slings
          equipmentListHandler(id, new StrapDetails());
          break;
        case 4: // Chains
          equipmentListHandler(id, new ChainsDetails());
          break;
        case 5: // Timbers
          equipmentListHandler(id, new TimberDetails());
          break;
        case 6: // Gates
          equipmentListHandler(id, new GateDetails());
          break;
        default: // All other equipment types
          equipmentListHandler(id, new GenericEquipmentDetails());
          break;
      }
    }
  }

  for (const id of oldVal) {
    if (!val.includes(id)) {
      const index = props.fleetAsset.additionalEquipments.findIndex(
        (item) => item.id === id,
      );
      props.fleetAsset.additionalEquipments.splice(index, 1);
    }
  }
});

// handler for creating correct model and pushing to parent array on equipment select
function equipmentListHandler(id: number, type: any) {
  const model = new AdditionalEquipmentItem();
  model.equipmentInformation = type;
  model.id = id;

  const itemExists = props.fleetAsset.additionalEquipments.find(
    (item) => item.id === id,
  );

  if (!itemExists) {
    props.fleetAsset.additionalEquipments.push(model);
  }
}

// handler for passing correct equipment type into components
function dynamicEquipmentProp(id: number) {
  const equipmentItem = props.fleetAsset.additionalEquipments.find(
    (item) => item.id === id,
  );
  if (equipmentItem) {
    return equipmentItem.equipmentInformation;
  }
}

// hanlder to set data back to default when an equipment item is deleted
function removeEquipmentItem(id: number) {
  // remove correct item from selected equipment list
  const selectedEquipmentIndex = selectedEquipmentTypes.value.findIndex(
    (item) => item === id,
  );
  selectedEquipmentTypes.value.splice(selectedEquipmentIndex, 1);
  // remove correct item from parent equipment list
  const listIndex = props.fleetAsset.additionalEquipments.findIndex(
    (item) => item.id === id,
  );
  props.fleetAsset.additionalEquipments.splice(listIndex, 1);
  // find deleted equipment type and push it back into multi select list
  // const type = equipmentTypes.find(item => item.id === id);
  // const typeIndex = equipmentTypes.findIndex(item => item.id === id);
  // clonedEquipmentTypes.splice(typeIndex, 0, type as EquipmentTypes);
}

// handles disabled flag on each item in the v-select of equipment types. User should not be able to deselect
// equipment type if the asset current has it defined.
function disableSelectOfEquipmentType(
  equipmentSelectItem: EquipmentTypes,
): boolean {
  const equipmentTypeId: number = equipmentSelectItem.id;
  let isDisabled = false;

  switch (equipmentTypeId) {
    case 1:
      const craneDetails = dynamicEquipmentProp(
        equipmentTypeId,
      ) as CraneDetails;
      if (craneDetails) {
        isDisabled = true;
      }
      break;
    case 3:
      const strapDetails = dynamicEquipmentProp(
        equipmentTypeId,
      ) as StrapDetails;
      if (strapDetails) {
        isDisabled = strapDetails.straps.length > 0 ? true : false;
      }
      break;
    case 4:
      const chainDetails = dynamicEquipmentProp(
        equipmentTypeId,
      ) as ChainsDetails;
      if (chainDetails) {
        isDisabled = chainDetails.chains.length > 0 ? true : false;
      }
      break;
    case 17:
      const liftingChainDetails = dynamicEquipmentProp(
        equipmentTypeId,
      ) as StrapDetails;
      if (liftingChainDetails) {
        isDisabled = liftingChainDetails.straps.length > 0 ? true : false;
      }
      break;
    case 18:
      const liftingSlingDetails = dynamicEquipmentProp(
        equipmentTypeId,
      ) as StrapDetails;
      if (liftingSlingDetails) {
        isDisabled = liftingSlingDetails.straps.length > 0 ? true : false;
      }
      break;
  }

  return isDisabled;
}

onMounted(() => {
  selectedEquipmentTypes.value = props.fleetAsset.additionalEquipments.map(
    (e) => e.id,
  );
});
</script>
