<form-card color="orange" class="mx-auto">
  <span slot="heading">Fuel Surcharge Details</span>
  <span slot="subheading">Existing Fuel Surcharge Details and Maintenance</span>
  <div slot="content">
    <v-layout justify-end>
      <GButton
        small
        icon="fal fa-plus"
        :iconRight="true"
        class="mt-0"
        @click="newFuelSurcharge"
        :disabled="!isAuthorised() || isNewFuelSurcharge || isEdited"
        >Create New</GButton
      >
    </v-layout>
    <v-alert
      :value="true"
      dense
      color="success"
      class="mb-2"
      icon="fas fa-gas-pump"
    >
      {{currentFuelSurchargeStatusMessage}}
    </v-alert>

    <v-data-table
      :headers="tableHeaders"
      class="gd-dark-theme"
      :items="allFuelSurchargeRatesReversed"
      no-data-text="No history of custom Fuel Surcharges available."
      hide-actions
    >
      <template v-slot:items="props">
        <tr
          @click="editFuelSurcharge(props.item)"
          :style="isAuthorised() ? 'cursor:pointer' : ''"
        >
          <td
            :class="{activeDate: returnDateActive(props.item.validFromDate, props.item.validToDate, props.item.tableId)}"
            class="pl-4"
          >
            {{props.item.fuelSurchargeRate}}%
          </td>
          <td
            :class="{activeDate: returnDateActive(props.item.validFromDate, props.item.validToDate, props.item.tableId)}"
          >
            {{returnFormattedDate(props.item.validFromDate)}}
          </td>
          <td
            :class="{activeDate: returnDateActive(props.item.validFromDate, props.item.validToDate, props.item.tableId)}"
          >
            {{returnFormattedDate(props.item.validToDate)}}
          </td>
          <td
            :class="{activeDate: returnDateActive(props.item.validFromDate, props.item.validToDate, props.item.tableId)}"
          >
            <span
              v-if="returnDateActive(props.item.validFromDate, props.item.validToDate, props.item.tableId)"
              >Yes</span
            >
            <span v-else> No </span>
          </td>
        </tr>
      </template>
    </v-data-table>
    <ContentDialog
      v-if="dialogController"
      :showDialog.sync="dialogController"
      title="Add/Edit Fuel Surcharge"
      width="800px"
      contentPadding="pa-0"
      @cancel="cancelCreateOrEditFuelSurcharge"
      @confirm="saveFuelSurcharge"
      :showActions="true"
      :isConfirmUnsaved="false"
      :isDisabled="formIsValid && !datesValid.value || fuelSurchargeRate.validFromDate === null ||
                fuelSurchargeRate.validToDate === null || fuelSurchargeRate.fuelSurchargeRate === undefined"
      :isLoading="isAwaitingSaveResponse"
      confirmBtnText="Save"
    >
      <v-layout>
        <v-flex md12 class="body-scrollable--75 pa-3">
          <v-form ref="fuelSurchargeForm">
            <v-layout wrap class="body-scrollable--65" pa-3>
              <v-flex>
                <v-alert
                  type="warning"
                  :value="validFromEdits.edit || validToEdits.edit"
                  :class="validFromEdits.edit || validToEdits.edit ? 'mb-4' : ''"
                >
                  The date range you have entered overlaps with another fuel
                  surcharge.
                </v-alert>
              </v-flex>
              <v-flex md12>
                <v-layout>
                  <v-flex md4>
                    <v-layout align-center class="form-field-label-container">
                      <h6 class="subheader--faded pr-3 pb-0">
                        Fuel Surcharge Rate (%):
                      </h6>
                    </v-layout>
                  </v-flex>
                  <v-flex md8>
                    <v-text-field
                      class="v-solo-custom"
                      solo
                      flat
                      color="light-blue"
                      label="Fuel Surcharge Rate (%)"
                      type="number"
                      v-model.number="fuelSurchargeRate.fuelSurchargeRate"
                      :disabled="!isEdited && !isNewFuelSurcharge"
                      :rules="[validate.required, validate.percentage, validate.nonNegative]"
                      suffix="%"
                    ></v-text-field>
                  </v-flex>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-layout>
                  <v-flex md4>
                    <v-layout align-center class="form-field-label-container">
                      <h6 class="subheader--faded pr-3 pb-0">Valid From:</h6>
                    </v-layout>
                  </v-flex>
                  <v-flex md8>
                    <DateTimeInputs
                      :epochTime.sync="validFromDate"
                      :enableValidation="true"
                      type="DATE_START_OF_DAY"
                      dateLabel="Valid From"
                      hintTextType="FORMATTED_SELECTION"
                      :isRequired="true"
                      :minimumEpochTime="allowedDates.from.min"
                      :maximumEpochTime="allowedDates.from.max"
                      maxComparisonType="LESS_OR_EQUAL"
                      :boxInput="false"
                      :soloInput="true"
                    ></DateTimeInputs>
                  </v-flex>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-layout>
                  <v-flex md4>
                    <v-layout align-center class="form-field-label-container">
                      <h6 class="subheader--faded pr-3 pb-0">Valid To:</h6>
                    </v-layout>
                  </v-flex>
                  <v-flex md8>
                    <DateTimeInputs
                      :epochTime.sync="validToDate"
                      :enableValidation="true"
                      type="DATE_END_OF_DAY"
                      dateLabel="Valid To"
                      hintTextType="FORMATTED_SELECTION"
                      :isRequired="true"
                      :readOnly="!isEdited && !isNewFuelSurcharge"
                      :minimumEpochTime="allowedDates.to.min"
                      :maximumEpochTime="allowedDates.to.max"
                      :boxInput="false"
                      :soloInput="true"
                    ></DateTimeInputs>
                  </v-flex>
                </v-layout>
              </v-flex>
            </v-layout>
          </v-form>
        </v-flex>
      </v-layout>
    </ContentDialog>
  </div>
</form-card>
