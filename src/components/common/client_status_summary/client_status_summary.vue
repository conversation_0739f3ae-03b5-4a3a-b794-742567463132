<template>
  <div>
    <div class="item-container">
      <span class="summary-header pr-1">Active:</span
      ><span class="summary-value">{{ statusTypes.active }}</span>
    </div>
    <div class="item-container">
      <span class="summary-header pr-1">Credit Requires Action:</span
      ><span class="summary-value">{{ statusTypes.badCredit }}</span>
    </div>

    <div class="item-container">
      <span class="summary-header pr-1">Pending:</span
      ><span class="summary-value">{{ statusTypes.pending }}</span>
    </div>

    <div class="item-container">
      <span class="summary-header pr-1">Pending Requires Action:</span
      ><span class="summary-value">{{
        statusTypes.pendingRequiresAction
      }}</span>
    </div>

    <div class="item-container">
      <span class="summary-header pr-1">Retired:</span
      ><span class="summary-value">{{ statusTypes.retired }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { OperationJobSummary } from '@/interface-models/Jobs/OperationJobSummary';

import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useJobStore } from '@/store/modules/JobStore';
import { computed, ComputedRef } from 'vue';

const statusTypes = computed(() => {
  const clientList = useClientDetailsStore().clientSummaryList;

  const statusTypes = {
    pending: 0,
    retired: 0,
    badCredit: 0,
    active: 0,
    pendingRequiresAction: 0,
  };

  for (const client of clientList) {
    if (client.statusList.includes(13)) {
      statusTypes.retired++;
    } else {
      if (client.statusList.includes(3)) {
        statusTypes.pending++;
        const jobIsActive = jobsToCheckAgainst.value.find(
          (x: OperationJobSummary) => x.clientId === client.clientId,
        );

        if (jobIsActive) {
          statusTypes.pendingRequiresAction++;
        }
      } else {
        statusTypes.active++;
      }
      if (client.statusList.includes(7)) {
        statusTypes.badCredit++;
      }
    }
  }

  return statusTypes;
});

const jobsToCheckAgainst: ComputedRef<OperationJobSummary[]> = computed(
  (): OperationJobSummary[] => {
    return useJobStore().operationJobsList.filter(
      (job: OperationJobSummary) =>
        (job.workStatus >= WorkStatus.BOOKED &&
          job.workStatus <= WorkStatus.REVIEWED) ||
        job.statusList.includes(45),
    );
  },
);
</script>

<style scoped lang="scss">
.summary-header {
  margin: 18px;
  font-size: $font-size-20;
}

.summary-value {
  margin: 18px;
  font-weight: 500;
  font-size: $font-size-20;
  color: var(--text-color);
}

.item-container {
  border-top: 0.5px solid $border-color;
  width: 100%;
  display: flex;
  height: 55px;
  justify-content: space-between;
  align-items: center;
}
</style>
