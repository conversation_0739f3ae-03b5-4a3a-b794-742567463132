<template>
  <div class="division-selection-container">
    <v-layout align-center>
      <v-menu left>
        <template v-slot:activator="{ on: menu }">
          <v-tooltip bottom>
            <template v-slot:activator="{ on: tooltip }">
              <v-btn flat icon v-on="{ ...tooltip, ...menu }" class="ma-0">
                <v-icon size="18">far fa-chevron-left</v-icon>
              </v-btn>
            </template>
            <span>View Available Divisions</span>
          </v-tooltip>
        </template>
        <v-list dense class="v-list-custom">
          <v-subheader v-if="allDivisions" class="header-item"
            >Active Session</v-subheader
          >
          <v-list-tile readonly>
            <v-list-tile-avatar>
              <v-icon size="18">fal fa-user</v-icon>
            </v-list-tile-avatar>

            <v-list-tile-content>
              <v-list-tile-title
                >{{ activeUser }} ({{ activeDivision?.company }} -
                {{ activeDivision?.division }})</v-list-tile-title
              >
              <v-list-tile-sub-title>{{ userName }}</v-list-tile-sub-title>
            </v-list-tile-content>
          </v-list-tile>

          <v-subheader v-if="allDivisions" class="header-item"
            >Change Division</v-subheader
          >
          <v-list-tile
            @click="submitChangeDivision(division)"
            v-for="(division, index) of allDivisions"
            :key="index"
          >
            <v-list-tile-title> {{ division }} </v-list-tile-title>
          </v-list-tile>
        </v-list>
      </v-menu>

      <div
        class="make-selection-container"
        v-if="makingSelection && !selectionMade"
      >
        <div
          class="division fill-height"
          @click="submitChangeDivision(division)"
          v-for="(division, index) of allDivisions"
          :key="index"
        >
          {{ division }}
        </div>
      </div>
    </v-layout>

    <div class="division-container">{{ activeDivision?.division }}</div>

    <ContentDialog
      v-if="showDialog"
      :showDialog.sync="showDialog"
      :title="'Re-enter Password'"
      :width="'600px'"
      :confirmBtnText="'Submit'"
      @confirm="submitCredentials"
      :isDisabled="loginDetails.password.length <= 0"
      @cancel="cancelDivisionChange"
    >
      <v-form>
        <v-layout row wrap>
          <v-flex md12>
            <v-layout>
              <v-flex md3>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">Username</h6>
                </v-layout>
              </v-flex>
              <v-flex md9>
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  color="light-blue"
                  label="Username"
                  :value="userName"
                  readonly
                >
                  <template v-slot:prepend-inner>
                    <span class="pr-2">
                      <v-icon size="18">fal fa-user</v-icon>
                    </span>
                  </template>
                </v-text-field>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md3>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">Password</h6>
                </v-layout>
              </v-flex>
              <v-flex md9>
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  color="light-blue"
                  label="Enter Password"
                  autofocus
                  type="password"
                  v-model="loginDetails.password"
                  placeholder="Enter Password"
                  @keydown="keyPressHandler"
                >
                  <template v-slot:prepend-inner>
                    <span class="pr-2">
                      <v-icon size="18">fal fa-lock-alt</v-icon>
                    </span>
                  </template>
                </v-text-field>
              </v-flex>
            </v-layout>
          </v-flex>
        </v-layout>
      </v-form>
    </ContentDialog>
  </div>
</template>

<script setup lang="ts">
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import {
  SESSION_STORAGE_MULTI_TOKEN,
  SESSION_STORAGE_TOKEN,
} from '@/helpers/AppLayoutHelpers/LocalStorageHelpers';
import { authenticateUser } from '@/helpers/AuthenticationHelpers/LoginHelpers';
import { Token } from '@/interface-models/Authentication/Token';
import LoginDetails from '@/interface-models/Login/Login';
import { useAppNavigationStore } from '@/store/modules/AppNavigationStore';
import { useAuthenticationStore } from '@/store/modules/AuthenticationStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { useRootStore } from '@/store/modules/RootStore';
import { useWebsocketStore } from '@/store/modules/WebsocketStore';
import { sessionManager } from '@/store/session/SessionState';
import { decodeJwt } from 'jose';
import { computed, ref, Ref, watch } from 'vue';
import { useRouter } from 'vue-router/composables';

const loginDetails: Ref<LoginDetails> = ref(new LoginDetails());
const makingSelection: Ref<boolean> = ref(false);
const selectionMade: Ref<boolean> = ref(false);

const token = useAuthenticationStore().authToken.accessToken;

const emit = defineEmits<{
  (event: 'setLoginState'): void;
}>();

// Get router instance
const router = useRouter();

const decodedToken: Token | null = decodeJwt(token);

const allDivisions = computed((): string[] => {
  if (useWebsocketStore().multiAuthToken) {
    const decodedMulti: Token | null = decodeJwt(
      useWebsocketStore().multiAuthToken,
    );
    if (!decodedMulti) {
      return [];
    }
    return decodedMulti.companies
      .map((item: any) => item.division)
      .filter((x: string) => x !== decodedToken?.division); // Ensure decodedToken exists before accessing properties
  }

  return [];
});

const loginState = computed((): string => {
  return useAuthenticationStore().authToken.accessToken;
});

const activeUser = computed((): string => {
  const token = useAuthenticationStore().authToken.accessToken;
  if (token !== '') {
    const decodedDivision = decodeJwt(token);
    return decodedDivision.firstName + ' ' + decodedDivision.lastName;
  } else {
    return '';
  }
});

// Get the user's email address to display in the HTML
const userName = computed((): string => {
  return sessionManager.getUserName();
});

const activeDivision = computed(() => {
  const token = useAuthenticationStore().authToken.accessToken;
  if (token !== '') {
    const decodedDivision = decodeJwt(token);
    const divisionDetails = {
      company: decodedDivision.company,
      division: decodedDivision.division,
    };
    return divisionDetails;
  } else {
    return null;
  }
});

// Handles the keydown event on the password input. If the event is ENTER
// event, then call submitCredentials
function keyPressHandler(e: KeyboardEvent): void {
  if (!e.key) {
    return;
  }
  const key = e.key.toLowerCase(); // Convert to lowercase for case-insensitive comparison
  if (key === 'enter') {
    e.preventDefault();
    submitCredentials();
  }
}

const showDialog = computed((): boolean => {
  return selectionMade.value;
});

function submitChangeDivision(division: string) {
  const multiToken = useWebsocketStore().multiAuthToken;
  const decodedMulti: Token = decodeJwt(multiToken);
  const foundCompanyIndex = decodedMulti.companies.findIndex(
    (item: any) => item.division === division,
  );
  loginDetails.value.company =
    decodedMulti.companies[foundCompanyIndex].company;
  loginDetails.value.division = division;
  loginDetails.value.username = decodedMulti.user_name;
  selectionMade.value = true;
}

function cancelDivisionChange() {
  selectionMade.value = false;
  makingSelection.value = false;
  loginDetails.value = new LoginDetails();
}

function submitCredentials() {
  useOperationsStore().closeAllPopoutWindows();
  useRootStore().setDataLoadedFalse();
  useRootStore().resetDivisionSpecificData();
  useAppNavigationStore().setPageTitle('');
  useWebsocketStore().removeAllSubscriptions();
  authenticateUser(loginDetails.value);
  selectionMade.value = false;
  makingSelection.value = false;
  loginDetails.value = new LoginDetails();
  router.push('/');
}

watch(loginState, (token: string) => {
  if (token.length > 0) {
    const decoded = decodeJwt(token) as { division: string }; // Assuming a Token structure
    if (decoded.division === 'MULTI') {
      useWebsocketStore().setMultiAuthToken(token);
      sessionStorage.setItem(SESSION_STORAGE_MULTI_TOKEN, token);
      emit('setLoginState'); // Emit the 'setLoginState' event
    } else {
      sessionStorage.setItem(SESSION_STORAGE_TOKEN, token);
      router.push('/'); // Navigate to the home route
    }
  }
});
</script>

<style scoped lang="scss">
.division-selection-container {
  display: flex;
  height: 39px;

  align-items: center;

  .make-selection-container {
    display: flex;
    height: 100%;
    padding-right: 18px;
    .division {
      padding: 0 1em;
      cursor: pointer;
      height: 100%;
      display: flex;
      align-items: center;

      &:hover {
        background-color: #303030;
      }
    }
  }

  .division {
    color: green;
  }

  .selected-division {
    height: 100%;
    display: flex;
    align-items: center;

    font-weight: 400;
  }

  .division-container {
    font-family: $sub-font-family;
    font-size: $font-size-18;
    font-weight: 800;
    padding-right: 10px;
    // color: rgb(29, 29, 32);
  }

  .password-container {
    height: 48px;
    overflow-y: hidden;
    display: flex;
    align-items: center;

    #division-select-password {
      width: 100px;
    }
  }
}

.header-item {
  color: var(--accent);
}
</style>
