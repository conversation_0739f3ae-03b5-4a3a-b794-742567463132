<template>
  <div class="jobs-summary-container">
    <div
      fill-height
      :class="props.isStatusBar ? 'statistics-bar' : 'home-card'"
      v-if="jobSummary"
    >
      <div
        style="display: flex"
        v-for="(stat, statIndex) of jobStatistics"
        :key="statIndex"
        :class="[{ 'header-container': stat.isHeader }]"
      >
        <div
          class="item-container"
          @click="stat.isHeader ? null : setFilteredSelection(stat)"
        >
          <span class="summary-header pr-1">{{ stat.key }}:</span
          ><span v-if="!stat.isHeader" class="summary-value">{{
            stat.value
          }}</span>
        </div>
        <v-divider v-if="props.isStatusBar" class="ma-0" vertical></v-divider>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  returnEndOfDayFromEpoch,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { DivisionJobsSummary } from '@/interface-models/Generic/DivisionJobsSummary/DivisionJobsSummary';
import { JobStatistics } from '@/interface-models/Generic/DivisionJobsSummary/JobsStatistics';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useJobStatisticsStore } from '@/store/modules/JobStatisticsStore';
import { useRootStore } from '@/store/modules/RootStore';
import { computed, ComputedRef } from 'vue';

const props = defineProps<{
  isStatusBar: boolean;
}>();

const jobSummary: ComputedRef<DivisionJobsSummary | null> = computed(
  (): DivisionJobsSummary | null => {
    return useJobStatisticsStore().divisionJobsSummary;
  },
);

function setFilteredSelection(selection: JobStatistics) {
  useJobStatisticsStore().setFilteredSelection(selection);
}

const jobStatistics: ComputedRef<JobStatistics[]> = computed(
  (): JobStatistics[] => {
    if (!jobSummary.value) {
      return [];
    }

    const startOfDay = returnStartOfDayFromEpoch();
    const endOfDay = returnEndOfDayFromEpoch();
    const startOfDayTomorrow = useRootStore().startOfDayTomorrow;
    const endOfDayTomorrow = useRootStore().endOfDayTomorrow;
    return [
      {
        key: 'Old Jobs',
        value: jobSummary.value.oldJobs,
        workStatus: null,
        startDate: 0,
        endDate: startOfDay - 1,
        isHeader: false,
      },
      {
        key: 'In progress',
        value: jobSummary.value.inProgress,
        workStatus: WorkStatus.IN_PROGRESS,
        startDate: 0,
        endDate: 0,
        isHeader: false,
      },
      {
        key: 'Ready for Pricing',
        value: jobSummary.value.driverCompleted,
        workStatus: WorkStatus.DRIVER_COMPLETED,
        startDate: 0,
        endDate: 0,
        isHeader: false,
      },
      {
        key: 'Reviewed',
        value: jobSummary.value.reviewed,
        workStatus: WorkStatus.REVIEWED,
        startDate: 0,
        endDate: 0,
        isHeader: false,
      },
      {
        key: 'Today',
        value: 0,
        workStatus: null,
        startDate: 0,
        endDate: 0,
        isHeader: true,
      },
      {
        key: 'Total',
        value: jobSummary.value.jobsToday,
        workStatus: null,
        startDate: startOfDay,
        endDate: endOfDay,
        isHeader: false,
      },
      {
        key: 'Booked Today By Me',
        value: jobSummary.value.bookedTodayByUser,
        workStatus: null,
        startDate: startOfDay,
        endDate: endOfDay,
        isHeader: false,
      },
      {
        key: 'Unallocated',
        value: jobSummary.value.unallocatedToday,
        workStatus: WorkStatus.BOOKED,
        startDate: startOfDay,
        endDate: endOfDay,
        isHeader: false,
      },
      {
        key: 'Pre-allocated',
        value: jobSummary.value.preAllocatedToday,
        workStatus: WorkStatus.PREALLOCATED,
        startDate: startOfDay,
        endDate: endOfDay,
        isHeader: false,
      },
      {
        key: 'Allocated',
        value: jobSummary.value.allocatedToday,
        workStatus: WorkStatus.ALLOCATED,
        startDate: startOfDay,
        endDate: endOfDay,
        isHeader: false,
      },
      {
        key: 'Accepted',
        value: jobSummary.value.acceptedToday,
        workStatus: WorkStatus.ACCEPTED,
        startDate: startOfDay,
        endDate: endOfDay,
        isHeader: false,
      },

      {
        key: 'Tomorrow',
        value: 0,
        workStatus: null,
        startDate: 0,
        endDate: 0,
        isHeader: true,
      },

      {
        key: 'Total',
        value: jobSummary.value.tomorrowsJobs,
        workStatus: null,
        startDate: startOfDayTomorrow,
        endDate: endOfDayTomorrow,
        isHeader: false,
      },
      {
        key: 'Unallocated',
        value: jobSummary.value.unallocatedTomorrow,
        workStatus: WorkStatus.BOOKED,
        startDate: startOfDayTomorrow,
        endDate: endOfDayTomorrow,
        isHeader: false,
      },
      {
        key: 'Pre-allocated',
        value: jobSummary.value.preAllocatedTomorrow,
        workStatus: WorkStatus.PREALLOCATED,
        startDate: startOfDayTomorrow,
        endDate: endOfDayTomorrow,
        isHeader: false,
      },
      {
        key: 'Allocated',
        value: jobSummary.value.allocatedTomorrow,
        workStatus: WorkStatus.ALLOCATED,
        startDate: startOfDayTomorrow,
        endDate: endOfDayTomorrow,
        isHeader: false,
      },
      {
        key: 'Accepted',
        value: jobSummary.value.acceptedTomorrow,
        workStatus: WorkStatus.ACCEPTED,
        startDate: startOfDayTomorrow,
        endDate: endOfDayTomorrow,
        isHeader: false,
      },
    ];
  },
);
</script>

<style scoped lang="scss">
.statistics-bar {
  display: flex;
  color: var(--light-text-color);
  .item-container {
    height: 20px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    &:hover {
      cursor: pointer;
      color: white;
    }
  }
  .summary-header {
    font-size: $font-size-medium;
  }

  .summary-value {
    font-size: $font-size-medium;
  }
}

.home-card {
  .summary-header {
    width: 100%;
    margin: 18px;
    font-size: $font-size-20;
  }

  .summary-value {
    margin: 18px;
    font-weight: 500;
    font-size: $font-size-20;
    color: var(--text-color);
  }

  .item-container {
    border-top: 0.5px solid var(--border-color);
    width: 100%;
    display: flex;
    height: 55px;
    justify-content: space-between;
    align-items: center;
  }
  .header-container {
    background-color: var(--background-color-500);
    .item-container {
      &:hover {
        cursor: default;
      }
    }
    span {
      font-size: $font-size-20;
      font-weight: bold;
      color: var(--text-color);
    }
  }
}
</style>
