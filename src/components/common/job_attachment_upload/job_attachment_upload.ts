import {
  getIconByMimeType,
  validMimeTypes,
} from '@/helpers/AttachmentHelpers/AttachmentHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import AddAttachmentToJob from '@/interface-models/Generic/Attachment/AddAttachmentToJob';
import { Attachment } from '@/interface-models/Generic/Attachment/Attachment';
import AttachmentTypes from '@/interface-models/Generic/Attachment/AttachmentTypes';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { useJobStore } from '@/store/modules/JobStore';
import { v4 as uuidv4 } from 'uuid';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component({})
export default class JobAttachmentUpload extends Vue {
  @Prop() public jobId: number;
  @Prop() public pudId: string;
  @Prop() public jobDetails: JobDetails;
  @Prop({ default: false }) public isOutsideHire: boolean;
  public getIconByMimeType = getIconByMimeType;

  public documentTypeId: number = 15;

  public attachmentArray: Attachment[] = [];
  public attachment: Attachment;
  public attachmentSingle: boolean = true;

  public addedAttachmentsList: Attachment[] = [];
  public waitingForAttachmentResponse: boolean = false;

  public fileType: string = '';

  public uploadAttachmentToJob: boolean = false;

  public localPudId: string = '';
  public $refs!: {
    fileInput: HTMLInputElement;
  };

  public validAttachmentTypes: any[] = [
    {
      id: AttachmentTypes.PUD_ITEM_IMAGE,
      longName: 'POD Image',
    },
    {
      id: AttachmentTypes.PUD_ITEM_DOCUMENT,
      longName: 'Paperwork',
    },
    {
      id: AttachmentTypes.PUD_ITEM_SIGNATURE,
      longName: 'Signature',
    },
  ];

  public selectedAttachmentType: number = AttachmentTypes.PUD_ITEM_IMAGE;

  private awaitingForThumbnail: Attachment[] = [];

  get selectedPudId() {
    if (this.pudId !== undefined) {
      return this.pudId;
    } else {
      return this.localPudId;
    }
  }
  set selectedPudId(value: string) {
    if (!value) {
      this.uploadAttachmentToJob = true;
    } else {
      this.uploadAttachmentToJob = false;
    }
    if (this.pudId !== undefined) {
      this.$emit('selectedPudChanged', value);
    } else {
      this.localPudId = value;
    }
  }

  public pickFile() {
    const fileInput: HTMLElement | null = document.getElementById(
      'fileInput' + this.documentTypeId,
    )!;
    fileInput.click();
  }

  public removeUploadedFile() {
    this.addedAttachmentsList = [];
    this.fileType = '';
  }

  public deleteAttachment(): void {
    if (this.attachmentSingle !== true) {
      const index = this.attachmentArray.findIndex(
        (item: any) =>
          parseInt(item.documentTypeId, 10) === this.documentTypeId,
      );
      this.attachmentArray.splice(index, 1);
    }
    if (this.attachmentSingle) {
      this.attachment.id = '';
      this.attachment.documentTypeId = 0;
      this.attachment.mimeType = '';
      this.attachment.data = '';
      this.attachment.name = '';
    }
    this.fileType = '';
  }

  public saveAttachments() {
    const fileUpload: any = this.$refs.fileInput;

    const attachment: Attachment = new Attachment();

    const file: File = fileUpload.files[0];
    // If mime type is not supported, show error notification to user
    if (!validMimeTypes.includes(file.type)) {
      showNotification('File format not supported.', {
        type: HealthLevel.ERROR,
      });
      return;
    }
    this.fileType = file.type;
    attachment.documentTypeId = this.documentTypeId;
    const promise = this.getBase64(file);
    promise
      .then((result) => {
        const base64: any = result;
        attachment.data = base64;
        attachment.mimeType = file.type;
        attachment.name = uuidv4() + '-' + file.name;
        attachment.documentTypeId =
          this.selectedAttachmentType !== 0
            ? this.selectedAttachmentType
            : AttachmentTypes.PUD_ITEM_IMAGE;

        this.awaitingForThumbnail.push(attachment);
        this.addedAttachmentsList.push(attachment);
      })
      .finally(() => {
        // clear the image that was selected. If we don't do this the @input event on the file input element will fail to do anything if a user uploads the same image.
        if (this.$refs.fileInput) {
          this.$refs.fileInput.value = '';
        }
      });
  }

  public async finishedEditingDetails() {
    if (this.addedAttachmentsList.length < 1) {
      return;
    }
    const attachment: Attachment = this.addedAttachmentsList[0];
    let pudId = this.selectedPudId !== '' ? this.selectedPudId : null;
    if (this.uploadAttachmentToJob) {
      pudId = null;
    }
    if (attachment.documentTypeId === AttachmentTypes.JOB_INVOICE_PDF) {
      pudId = null;
    }
    const request: AddAttachmentToJob = {
      jobId: this.jobId,
      pudId,
      attachment,
    };

    // Send request to add attachment to job, and handle response
    this.waitingForAttachmentResponse = true;
    const result = await useJobStore().addAttachmentToJob(request);
    this.waitingForAttachmentResponse = false;
    // Show notification on failure
    if (!result) {
      showNotification('Error uploading attachment to job.', {
        title: 'Job Attachment Upload',
      });
    }
    // Close dialog
    this.$emit('finishedUploadingAttachments');
  }

  get pudLegNumber() {
    if (this.pudId && this.jobDetails) {
      const validPuds = this.jobDetails.pudItems.filter(
        (pud) => pud.legTypeFlag === 'P' || pud.legTypeFlag === 'D',
      );
      const foundIndex = validPuds.findIndex((pud) => pud.pudId === this.pudId);
      if (foundIndex !== -1) {
        return foundIndex + 1;
      }
    }
  }

  get validPudItems() {
    interface PudItemExtended extends PUDItem {
      legNumber?: number;
    }
    if (this.jobDetails) {
      const validPuds = this.jobDetails.pudItems.filter(
        (pud) => pud.legTypeFlag === 'P' || pud.legTypeFlag === 'D',
      );
      const puds: PudItemExtended[] = [];

      validPuds.forEach((pud) => {
        const extended: PudItemExtended = pud;
        const foundIndex = validPuds.findIndex(
          (item) => item.pudId === extended.pudId,
        );

        extended.legNumber = foundIndex !== -1 ? foundIndex + 1 : 0;
        puds.push(extended);
      });

      return puds;
    } else {
      return [];
    }
  }

  get pudCustomerDeliveryName() {
    if (this.pudId && this.jobDetails) {
      const foundPud = this.jobDetails.pudItems.find(
        (pud) => pud.pudId === this.pudId,
      );
      if (foundPud && foundPud.customerDeliveryName) {
        return foundPud.customerDeliveryName;
      }
    }
  }

  public getBase64(file: any) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        resolve(reader.result);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  public mounted() {
    if (this.isOutsideHire && this.validAttachmentTypes.length === 3) {
      this.validAttachmentTypes.push({
        id: AttachmentTypes.JOB_INVOICE_PDF,
        longName: 'Job Invoice',
      });
    }
  }
}
