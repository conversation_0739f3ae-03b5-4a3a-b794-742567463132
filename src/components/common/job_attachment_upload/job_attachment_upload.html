<section class="job-attachment-upload">
  <v-layout class="loading-bar">
    <v-progress-linear
      :indeterminate="true"
      :active="waitingForAttachmentResponse"
      height="3"
      class="ma-0"
      color="orange"
    ></v-progress-linear>
  </v-layout>
  <v-layout justify-start align-center style="width: 100%;" row wrap>
    <v-flex md4 class="line-label">
      POD Type
    </v-flex>
    <v-flex md8>
      <v-select
        v-model="selectedAttachmentType"
        solo
        flat
        hide-details
        :items="validAttachmentTypes"
        item-text="longName"
        item-value="id"
        color="orange"
      >
      </v-select>
    </v-flex>
    <v-flex md4 class="line-label">
      Job Number
    </v-flex>
    <v-flex md8>
      <v-text-field
        :value="jobDetails.displayId"
        solo
        flat
        hide-details
        color="orange"
        disabled
      ></v-text-field>
    </v-flex>
    <v-flex md4 class="line-label">
      Leg Number
    </v-flex>
    <v-flex md8>
      <v-select
        v-model="selectedPudId"
        solo
        flat
        hide-details
        :items="validPudItems"
        item-text="customerDeliveryName"
        item-value="pudId"
        color="orange"
        clearable
        placeholder="Adding to Job"
      >
        <template slot="item" slot-scope="data">
          <!-- HTML that describe how select should render items when the select is open -->
          <strong class="pr-1">{{ 'Leg ' + data.item.legNumber }}</strong> - {{
          data.item.customerDeliveryName ? data.item.customerDeliveryName :
          'Unknown' }}
        </template>
        <template slot="selection" slot-scope="data">
          <!-- HTML that describe how select should render items when the select is open -->
          <strong class="pr-1">{{ 'Leg ' + data.item.legNumber }}</strong> - {{
          data.item.customerDeliveryName ? data.item.customerDeliveryName :
          'Unknown' }}
        </template>
      </v-select>
    </v-flex>
    <v-flex md12>
      <v-divider class="mx-0 mt-3 mb-2"></v-divider>
    </v-flex>
    <v-flex md12>
      <v-layout>
        <v-flex md4 class="line-label" pt-3>
          Select Image
        </v-flex>
        <v-flex md8 pt-3>
          <div
            v-if="addedAttachmentsList.length > 0"
            class="image-container__container"
          >
            <v-img
              :src="addedAttachmentsList[0].data && fileType.includes('image') && !fileType.includes('heic') && !fileType.includes('heif') ? addedAttachmentsList[0].data : ''"
              aspect-ratio="1"
              class="grey lighten-2"
              style="cursor:pointer"
            >
              <template v-slot:placeholder>
                <v-layout justify-center align-center fill-height>
                  <v-icon class="pb-2" size="50"
                    >{{ getIconByMimeType(fileType) }}
                  </v-icon>
                </v-layout>
              </template>
            </v-img>

            <div class="delete-button" @click="removeUploadedFile">
              <v-icon size="14">
                fas fa-times
              </v-icon>
            </div>
          </div>
          <div v-else>
            <div
              @click="pickFile"
              class="upload-container"
              style="cursor: pointer;"
            >
              <span>Upload</span>
              <v-icon size="18" color="teal accent-3" class="pl-2">
                fal fa-image
              </v-icon>
            </div>
          </div>
        </v-flex>
      </v-layout>
    </v-flex>
    <v-flex md12 pt-2>
      <v-layout justify-end>
        <v-btn
          :disabled="addedAttachmentsList.length === 0 || this.waitingForAttachmentResponse"
          depressed
          color="blue"
          @click="finishedEditingDetails"
        >
          Save Attachment
        </v-btn>
      </v-layout>
    </v-flex>
  </v-layout>

  <v-layout> </v-layout>
  <v-layout>
    <input
      type="file"
      ref="fileInput"
      :id="'fileInput' + documentTypeId"
      style="display: none"
      @change="saveAttachments()"
    />
  </v-layout>
</section>
