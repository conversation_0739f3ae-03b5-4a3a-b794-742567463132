.job-attachment-upload {
  height: 100%;
  width: 100%;
  // background-color: red;
  // display: flex;
  flex-direction: column;
  // justify-content: start;
  // align-items: center;
  padding: 12px;
  position: relative;

  .loading-bar {
    width: 100%;
    position: absolute;
    top: 0px;
    left: 0px;
  }

  .image-container__container {
    height: 100px;
    width: 100px;
    position: relative;
    .image-container {
      height: 100px;
      width: 100px;
      display: flex;
      background-color: #1f1f24;
      border: 1px solid #494953;

      img {
        max-width: 100%;
        // max-height: 100%;
        height: 100%;
        object-fit: contain !important;
      }
    }
    .delete-button {
      visibility: hidden;
      position: absolute;
      padding: 6px;
      top: 0px;
      right: 4px;
      cursor: pointer;
    }

    &:hover {
      .delete-button {
        visibility: visible;
      }
    }
  }

  .line-label {
    // text-transform: uppercase;
    font-size: 1em;
    color: rgb(209, 209, 209);
    font-weight: 600;
  }

  .upload-container {
    text-transform: uppercase;
    font-size: 1em;
    color: rgb(255, 255, 255);
    font-weight: 600;
  }
}
