<template>
  <v-flex
    md12
    class="job-status-management"
    :class="!hidePudStatuses ? 'pa-3' : 'disable-scroll'"
  >
    <v-layout
      row
      wrap
      class="expansion-job-item"
      :class="
        !hidePudStatuses
          ? 'pa-2 app-bgcolor--400 app-bordercolor--600 app-borderside--a'
          : ''
      "
    >
      <v-flex md12 pt-1 v-if="!hidePudStatuses">
        <v-layout justify-space-between align-center px-2>
          <h6>Job Status</h6>
        </v-layout>
      </v-flex>
      <v-flex md12 v-if="!hidePudStatuses">
        <v-divider></v-divider>
      </v-flex>
      <v-flex
        md12
        class="expansion-job-item__card-wrapper"
        :class="isDialog ? 'dialog-view' : ''"
      >
        <v-layout align-center class="expansion-job-item__card">
          <span class="expansion-job-item__card--jobid">
            #{{ jobDetails.displayId }}
          </span>
          <span class="expansion-job-item__card--clientname">
            {{ jobDetails.client.clientName }}
          </span>
          <!-- <span class="expansion-job-item__card--time">
            {{ jobDetails.legDurations }}
          </span> -->
          <!-- =============================================================== -->
          <!-- COMMENT OUT BELOW WHEN NOT IN USE -->
          <!-- =============================================================== -->

          <v-spacer></v-spacer>
          <span class="expansion-job-item__card--status pr-2">{{
            jobDetails.currentExactJobStatus
          }}</span>
          <v-menu
            left
            v-if="
              allowStatusChange &&
              jobDetails.workStatus !== WorkStatus.BOOKED &&
              !jobDetails.isOutsideHire
            "
          >
            <template v-slot:activator="{ on: menu }">
              <v-tooltip bottom>
                <template v-slot:activator="{ on: tooltip }">
                  <v-btn
                    small
                    flat
                    icon
                    v-on="{ ...tooltip, ...menu }"
                    class="ma-0"
                  >
                    <v-icon size="16">fas fa-ellipsis-v</v-icon>
                  </v-btn>
                </template>
                <span>Update Job Status</span>
              </v-tooltip>
            </template>
            <v-list class="v-list-custom">
              <v-list-tile
                @click="finaliseAllocation(jobDetails)"
                v-if="jobDetails.workStatus === WorkStatus.PREALLOCATED"
              >
                <v-list-tile-title class="pr-2"
                  >Allocate (Send to Driver)</v-list-tile-title
                >
              </v-list-tile>
              <v-list-tile
                @click="updateJobStatusAccepted(jobDetails)"
                v-if="jobDetails.workStatus === WorkStatus.ALLOCATED"
              >
                <v-list-tile-title class="pr-2"
                  >Mark Job as Accepted</v-list-tile-title
                >
              </v-list-tile>
              <v-list-tile
                @click="updateJobStatusStarted(jobDetails)"
                v-if="
                  jobDetails.workStatus === WorkStatus.ACCEPTED ||
                  jobDetails.workStatus === WorkStatus.DRIVER_COMPLETED
                "
              >
                <v-list-tile-title
                  class="pr-2"
                  v-if="jobDetails.workStatus === WorkStatus.ACCEPTED"
                  >Mark Job as Started</v-list-tile-title
                >
                <v-list-tile-title
                  class="pr-2"
                  v-if="jobDetails.workStatus === WorkStatus.DRIVER_COMPLETED"
                  >Uncomplete Job</v-list-tile-title
                >
              </v-list-tile>
              <v-list-tile
                @click="updateJobStatusComplete(jobDetails)"
                v-if="jobDetails.workStatus === WorkStatus.IN_PROGRESS"
              >
                <v-list-tile-title class="pr-2"
                  >Mark Job as Complete</v-list-tile-title
                >
              </v-list-tile>

              <v-list-tile
                @click="setReviewedJobToCompleted(jobDetails)"
                v-if="jobDetails.workStatus === WorkStatus.REVIEWED"
              >
                <v-list-tile-title class="pr-2"
                  >Release for Editing</v-list-tile-title
                >
              </v-list-tile>
            </v-list>
          </v-menu>
        </v-layout>
      </v-flex>
    </v-layout>

    <v-flex v-if="!jobDetails.isProgressInOrder()" md12 class="mb-2">
      <v-alert type="warning" :value="true">
        <v-layout align-center>
          The progress of this job is currently out of order and does not align
          with the job's legs.
          <v-spacer></v-spacer>
          <v-btn color="warning" solo @click="setReorderPudDialog">
            <v-icon color="white" size="20" class="mr-2">toc</v-icon>
            Reorder Legs
          </v-btn>
          <ContentDialog
            v-if="reorderPudDialogIsActive"
            :showDialog.sync="reorderPudDialogIsActive"
            :title="'Leg Reordering'"
            :width="'900px'"
            :confirmBtnText="'Confirm Reorder'"
            :isDisabled="awaitingPudReorderingResponse"
            :isLoading="awaitingPudReorderingResponse"
            @confirm="confirmReorderPuds"
            @cancel="reorderPudDialogIsActive = false"
            :contentPadding="''"
          >
            <v-layout wrap>
              <v-flex md12>
                <v-layout wrap>
                  <v-flex md12>
                    <v-layout>
                      <v-flex md6>
                        <v-layout>
                          <span
                            class="pud-status-list-item__header header-type pa-2"
                            >Current Order</span
                          >
                        </v-layout>
                      </v-flex>
                      <v-flex md6>
                        <v-layout>
                          <v-divider vertical> </v-divider>
                          <span
                            class="pud-status-list-item__header header-type pa-2"
                            >Updated Order</span
                          >
                        </v-layout>
                      </v-flex>
                    </v-layout>
                  </v-flex>

                  <v-flex md12>
                    <v-divider></v-divider>
                  </v-flex>
                  <v-flex md6>
                    <v-layout wrap class="py-2">
                      <v-flex
                        md12
                        v-for="(pudItem, pudIndex) of jobDetails.pudItems"
                        :key="pudItem.pudId"
                        class="pl-2 reorder-text"
                      >
                        <span class="pr-1"
                          ><b>{{ pudIndex + 1 }}.</b></span
                        >
                        <span class="pr-1">{{
                          pudItem.address.formattedAddress
                            ? pudItem.address.formattedAddress
                            : '-'
                        }}</span>
                        <span>({{ pudItem.legTypeFlag }})</span></v-flex
                      >
                    </v-layout>
                  </v-flex>
                  <!-- <v-flex style="width:24px">
                  <v-layout fill-height align-center justify-center>
                    <v-icon class="reorder-text"
                      >fad fa-long-arrow-right</v-icon
                    >
                  </v-layout>
                </v-flex> -->
                  <v-flex md6>
                    <v-layout>
                      <v-divider vertical> </v-divider>

                      <v-layout wrap class="py-2">
                        <h6 class="subheader--faded"></h6>
                        <v-flex
                          class="pl-2 reorder-text"
                          md12
                          v-for="(pudItem, pudIndex) of pudListByEventTimes"
                          :key="pudItem.pudId"
                        >
                          <span class="pr-1"
                            ><b>{{ pudIndex + 1 }}.</b></span
                          >
                          <span class="pr-1">{{
                            pudItem.address.formattedAddress
                              ? pudItem.address.formattedAddress
                              : '-'
                          }}</span>
                          <span>({{ pudItem.legTypeFlag }})</span>
                        </v-flex>
                      </v-layout>
                    </v-layout>
                  </v-flex>
                </v-layout>
              </v-flex>

              <v-flex md12>
                <v-divider class="mb-3"></v-divider>
              </v-flex>
              <v-flex md12 class="mb-3 mx-2">
                <v-layout>
                  Do you wish to proceed reordering the legs so they align with
                  the event times?
                </v-layout>
              </v-flex>
            </v-layout>
          </ContentDialog>
        </v-layout>
      </v-alert>
    </v-flex>
    <v-layout
      row
      wrap
      class="expansion-job-item pa-2 mt-2 app-bgcolor--400 app-bordercolor--600 app-borderside--a"
      v-if="!hidePudStatuses"
    >
      <v-flex md12>
        <v-layout justify-space-between align-center px-2>
          <h6>Pickup/Dropoff Statuses</h6>
          <v-btn
            depressed
            color="blue"
            small
            @click="dialogController = true"
            :disabled="
              jobDetails.workStatus !== WorkStatus.DRIVER_COMPLETED &&
              jobDetails.workStatus !== WorkStatus.IN_PROGRESS
            "
            class="v-btn-custom"
          >
            Edit
          </v-btn>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-divider></v-divider>
      </v-flex>
      <v-flex md12 class="pud-status-list-item">
        <v-layout px-2 pt-3>
          <span class="pud-status-list-item__header header-type">Stop</span>
          <v-spacer></v-spacer>
          <v-flex md2 style="text-align: right">
            <span class="pud-status-list-item__header header-type">
              Arrived
            </span>
          </v-flex>
          <v-flex md2 style="text-align: right">
            <span class="pud-status-list-item__header header-type">
              Departed
            </span>
          </v-flex>
          <v-flex md2 style="text-align: right">
            <span class="pud-status-list-item__header header-type">
              Status
            </span>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex
        md12
        class="pud-status-list-item app-borderside--t app-bordercolor--600"
        :class="isDialog ? 'dialog-view' : ''"
        v-for="(pud, pudIndex) of pudStatusList"
        :key="pud.pudId"
      >
        <v-layout px-2 align-center>
          <span class="pr-2">{{ pudIndex + 1 }}. </span>
          <span class="pr-1">{{ pud.suburb ? pud.suburb : '-' }}</span>
          <span>({{ pud.legTypeFlag }})</span>
          <v-spacer> </v-spacer>
          <v-flex md2 style="text-align: right">
            <span>
              {{
                pud.arrivedEvent.correctEventTime
                  ? returnFormattedTime(
                      pud.arrivedEvent.correctEventTime,
                      `DD/MM/YY
            HH:mm`,
                    )
                  : '-'
              }}
            </span>
          </v-flex>
          <v-flex md2 style="text-align: right">
            <span>
              {{
                pud.finishedEvent.correctEventTime
                  ? returnFormattedTime(
                      pud.finishedEvent.correctEventTime,
                      `DD/MM/YY
            HH:mm`,
                    )
                  : '-'
              }}
            </span>
          </v-flex>
          <v-flex md2 style="text-align: right">
            <span> {{ pud.status }} </span>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
    <v-dialog
      v-model="dialogController"
      v-if="dialogController"
      width="70%"
      persistent
      class="ma-0"
      content-class="v-dialog-custom"
    >
      <v-flex md12 class="app-theme__center-content--body">
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight"
        >
          <span>Edit Stop Times</span>
          <div
            class="app-theme__center-content--closebutton"
            @click="dialogController = false"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>
        <v-layout
          row
          wrap
          class="app-theme__center-content--body dialog-content pa-3"
        >
          <v-flex md12>
            <v-layout pb-2>
              <v-flex md2 style="text-align: left" class="px-2"
                ><h3 class="subheader--light">Stop</h3></v-flex
              >
              <v-flex
                md5
                style="text-align: center"
                class="app-borderside--l app-bordercolor--600"
                ><h3 class="subheader--light">Arrived On Site</h3></v-flex
              >
              <v-flex
                md5
                style="text-align: center"
                class="app-borderside--l app-bordercolor--600"
                ><h3 class="subheader--light">Departed</h3></v-flex
              >
            </v-layout>
            <v-form
              ref="pudStatusManagementForm"
              :key="'pudStatusManagementForm' + formIncrement"
            >
              <v-layout
                row
                wrap
                v-for="(pud, pudIndex) of editingPudStatusList"
                :key="pud.pudId"
              >
                <v-flex md12 v-if="pudIndex === 0">
                  <v-divider></v-divider>
                </v-flex>
                <PudStatusManagement
                  :key="pud.pudId"
                  :pudId="pud.pudId"
                  :pudIndex="pudIndex"
                  :suburbName="pud.suburb"
                  :firstArrivedEvent="editingPudStatusList[0].arrivedEvent"
                  :arrivedEvent="pud.arrivedEvent"
                  :finishedEvent="pud.finishedEvent"
                  :nextArrivedEvent="
                    editingPudStatusList[pudIndex + 1]
                      ? editingPudStatusList[pudIndex + 1].arrivedEvent
                      : null
                  "
                  :nextFinishedEvent="
                    editingPudStatusList[pudIndex + 1]
                      ? editingPudStatusList[pudIndex + 1].finishedEvent
                      : null
                  "
                  :prevArrivedEvent="
                    pudIndex !== 0
                      ? editingPudStatusList[pudIndex - 1].arrivedEvent
                      : null
                  "
                  :prevFinishedEvent="
                    pudIndex !== 0
                      ? editingPudStatusList[pudIndex - 1].finishedEvent
                      : null
                  "
                  :disableSwitches="
                    jobDetails.workStatus !== WorkStatus.IN_PROGRESS
                  "
                  @updatedArrivedEvent="updatedArrivedEvent"
                  @updatedFinishedEvent="updatedFinishedEvent"
                ></PudStatusManagement>
                <v-flex md12>
                  <v-divider></v-divider>
                </v-flex>
              </v-layout>
            </v-form>
          </v-flex>

          <v-flex md12 pt-3>
            <v-layout>
              <v-flex md6></v-flex>
              <v-flex md3
                ><h2 class="pa-3 text-md-right">Upload Supporting Document</h2>
              </v-flex>
              <v-flex md3>
                <file-upload
                  class="ml-3"
                  imageLabel="Upload Supporting Document"
                  :attachmentSingle="true"
                  :documentTypeId="16"
                  :attachment="supportingDocumentation"
                >
                </file-upload>
              </v-flex>
            </v-layout>
          </v-flex>
        </v-layout>
        <v-divider class="mt-2"></v-divider>
        <v-layout align-center>
          <v-btn flat color="red" @click="dialogController = false"
            >Cancel</v-btn
          >
          <v-spacer></v-spacer>

          <v-btn outline class="v-btn-custom" @click="restoreOriginalEventTimes"
            >Restore Original Times
          </v-btn>
          <v-btn
            depressed
            color="blue"
            @click="saveEventListAdjustments"
            class="v-btn-confirm-custom"
            :loading="isAwaitingResponse"
            >Save Changes
          </v-btn>
        </v-layout>
      </v-flex>
    </v-dialog>
  </v-flex>
</template>

<script setup lang="ts">
import FileUpload from '@/components/common/file-upload/index.vue';
import PudStatusManagement from '@/components/common/job_status_management/pud_status_management/pud_status_management.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { returnFormattedTime } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { getPudOrderBasedOnEventTimes } from '@/helpers/JobDataHelpers/JobDataHelpers';
import { deepCopy } from '@/helpers/JsonHelpers/JsonHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { initialiseJobDetails } from '@/helpers/classInitialisers/InitialiseJobDetails';
import AddAttachmentToJob from '@/interface-models/Generic/Attachment/AddAttachmentToJob';
import Attachment from '@/interface-models/Generic/Attachment/Attachment';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import JobEventHistoryAdjustment from '@/interface-models/Jobs/Event/JobEventHistoryAdjustment';
import { JobEventType } from '@/interface-models/Jobs/Event/JobEventType';
import JobStatusUpdate from '@/interface-models/Jobs/Event/JobStatusUpdate';
import {
  JobEventListUpdateRequest,
  PudItemStatusShort,
  PudStatusEventSummary,
} from '@/interface-models/Jobs/Event/PudStatusEventSummary';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { JobEventSummary } from '@/interface-models/Jobs/JobEventSummary';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useAllocationStore } from '@/store/modules/AllocationStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { sessionManager } from '@/store/session/SessionState';
import moment from 'moment-timezone';
import { v4 as uuidv4 } from 'uuid';
import { computed, ref, Ref, WritableComputedRef } from 'vue';

const props = withDefaults(
  defineProps<{
    isDialog: boolean;
    jobDetails: JobDetails;
    hidePudStatuses?: boolean;
    allowStatusChange?: boolean;
  }>(),
  {
    isDialog: false,
    hidePudStatuses: false,
    allowStatusChange: true,
  },
);

const jobStore = useJobStore();

const componentTitle: string = 'Job Status Management';
const formIncrement: Ref<number> = ref(0);

const pudStatusManagementForm: Ref<any> = ref(null);
const editingPudStatusList: Ref<PudStatusEventSummary[]> = ref([]);
const supportingDocumentation: Ref<Attachment> = ref(new Attachment());

const isViewingEditDialog: Ref<boolean> = ref(false);
const isAwaitingResponse: Ref<boolean> = ref(false);
const awaitingPudReorderingResponse: Ref<boolean> = ref(false);

// utilised for updating this job. Make clone so we are not mutating jobDetails prop.
const updatedJobDetails: Ref<JobDetails | null> = ref(null);
const pudListByEventTimes: Ref<PUDItem[]> = ref([]);
const reorderPudDialogIsActive: Ref<boolean> = ref(false);

/**
 * Controls dialog visibility, and resets local working variables on close
 */
const dialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return isViewingEditDialog.value;
  },
  set(value: boolean): void {
    if (value) {
      editingPudStatusList.value = deepCopy(pudStatusList.value);
    } else {
      // Reset local variables
      editingPudStatusList.value = [];
      supportingDocumentation.value = new Attachment();
    }
    isViewingEditDialog.value = value;
  },
});

// Construct and return a summary version of the current PUD Item events
// When we're editing the Pud Event Times in the dialog we will take a deep clone of this list to use as the working copy
const pudStatusList = computed<PudStatusEventSummary[]>(() => {
  return !props.jobDetails || !props.jobDetails.pudItems
    ? []
    : props.jobDetails.pudItems.map((pud) => {
        // Find arrived event item
        const foundArrived: JobStatusUpdate | undefined =
          props.jobDetails.returnSpecifiedEvent('ARRIVED', pud.pudId, false);
        let arrivedEvent: JobStatusUpdate;
        // If we find the latest arrived event, then we use that
        if (foundArrived) {
          // Take a copy of the original event, and set the changeTime to whatever the new adjusted value should be
          const arrCopy = Object.assign(new JobStatusUpdate(), foundArrived);
          arrCopy.changeTime = foundArrived.correctEventTime;
          arrivedEvent = arrCopy;
        } else {
          // If we don't find an arrived event, then create a new JobStatusUpdate item
          arrivedEvent = new JobStatusUpdate();
          arrivedEvent.id = uuidv4().replace(/-/g, '');
          if (props.jobDetails.jobId) {
            arrivedEvent.jobId = props.jobDetails.jobId;
            arrivedEvent.pudId = pud.pudId;
            arrivedEvent.updatedStatus = 'ARRIVED';
          }
        }

        // Find finished event item
        const foundFinished: JobStatusUpdate | undefined =
          props.jobDetails.returnSpecifiedEvent('FINISHED', pud.pudId, false);

        let finishedEvent: JobStatusUpdate;
        // If we find the latest arrived event, then we use that
        if (foundFinished) {
          // Take a copy of the original event, and set the changeTime to whatever the new adjusted value should be
          const finCopy = Object.assign(new JobStatusUpdate(), foundFinished);
          finCopy.changeTime = foundFinished.correctEventTime;
          finishedEvent = finCopy;
        } else {
          // If we don't find an finished event, then create a new JobStatusUpdate item with uuid
          finishedEvent = new JobStatusUpdate();
          finishedEvent.id = uuidv4().replace(/-/g, '');
          if (props.jobDetails.jobId) {
            finishedEvent.jobId = props.jobDetails.jobId;
            finishedEvent.pudId = pud.pudId;
            finishedEvent.updatedStatus = 'FINISHED';
          }
        }

        const result: PudStatusEventSummary = {
          pudId: pud.pudId,
          status: pud.status ? pud.status : 'INCOMPLETE',
          legTypeFlag: pud.legTypeFlag,
          suburb: pud.address.suburb,
          arrivedEvent,
          finishedEvent,
        };

        return result;
      });
});

function setReviewedJobToCompleted(job: JobDetails) {
  const operationsStore = useOperationsStore();
  if (job.jobId) {
    operationsStore.setViewingJobNotesDialog(true);
    operationsStore.setJobReleaseForEditing(true);
    operationsStore.setJobServiceFailure(false);
    operationsStore.setJobCancellation(false);
  }
}

function updateJobStatusAccepted(job: JobDetails) {
  showAppNotification(`Job #${job.jobId} Marked as Accepted`, HealthLevel.INFO);
  jobStore.updateJobStatus(job.jobId, JobEventType.AcceptJob);
}

function finaliseAllocation(job: JobDetails): void {
  useAllocationStore().allocatePreAllocatedJobIds([job.jobId!]);
}

function updateJobStatusComplete(job: JobDetails) {
  const valid = allPudsFinished(job);
  if (valid) {
    showAppNotification(
      `Job #${job.jobId} Marked as Complete`,
      HealthLevel.INFO,
    );
    jobStore.updateJobStatus(job.jobId, JobEventType.CompletedJob);
  } else {
    showAppNotification(`All Drops must be FINISHED to Complete`);
  }
}
// Dispatch construct
function updateJobStatusStarted(job: JobDetails) {
  showAppNotification(`Job #${job.jobId} Marked as Started`, HealthLevel.INFO);
  jobStore.updateJobStatus(job.jobId, JobEventType.StartedJob);
}

function allPudsFinished(job: JobDetails) {
  const validPudItems = job.pudItems.filter(
    (p) => p.legTypeFlag === 'P' || p.legTypeFlag === 'D',
  );
  return validPudItems.every((pud) => pud.status === 'FINISHED');
}

// Called from the PudStatusManagement component to update the associated editingPudStatusList item with the incoming value
function updatedArrivedEvent(event: JobStatusUpdate) {
  if (editingPudStatusList.value) {
    const foundEvent = editingPudStatusList.value?.find(
      (e) => e.arrivedEvent.id === event.id,
    );
    if (foundEvent) {
      foundEvent.arrivedEvent.changeTime = event.changeTime;
    }
  }
}
// Called from the PudStatusManagement component to update the associated editingPudStatusList item with the incoming value
function updatedFinishedEvent(event: JobStatusUpdate) {
  if (editingPudStatusList.value) {
    const foundEvent = editingPudStatusList.value?.find(
      (e) => e.finishedEvent.id === event.id,
    );
    if (foundEvent) {
      foundEvent.finishedEvent.changeTime = event.changeTime;
    }
  }
}

// Use the editingPudStatusList to update the existing eventList
// If the event exists already, we need to add an item to the event's adjustmentList property
// If the event does not already exist, we should add an event for the current time then add the new entry to the adjustmentList
function saveEventListAdjustments() {
  if (!editingPudStatusList.value) {
    return;
  }
  if (!pudStatusManagementForm.value.validate()) {
    return;
  }
  const eventList = editingPudStatusList.value;
  const jobCopy = initialiseJobDetails(props.jobDetails);
  const jobEventList: JobStatusUpdate[] = jobCopy.eventList;
  const epochNow = moment().valueOf();

  eventList?.forEach((event) => {
    const arr = event.arrivedEvent;
    const fin = event.finishedEvent;

    // Find and update the arrived event
    const foundArrived = jobEventList.find((je) => je.id === arr.id);
    if (foundArrived) {
      // If the new time and the original time are different, then push a new value into the adjustment list
      // If the value is set to 0 is means we are setting UN-ARRIVING
      if (foundArrived.correctEventTime !== arr.changeTime) {
        if (!foundArrived.adjustmentList) {
          foundArrived.adjustmentList = [];
        }
        // Construct JobEventHistoryAdjustment with details of new value
        const arrivedAdjustment = new JobEventHistoryAdjustment();
        arrivedAdjustment.adjustmentTime = epochNow;
        arrivedAdjustment.adjustedBy = sessionManager.getActiveUser();
        arrivedAdjustment.updatedValue = arr.changeTime;

        foundArrived.adjustmentList.push(arrivedAdjustment);
      }
    } else {
      // If we DIDN'T find the event already in the list, and the changeTime is NOT 0, then it means we need to add it
      if (arr.changeTime !== 0) {
        const newArrEvent = Object.assign(new JobStatusUpdate(), arr);
        // Keep the original event time as NOW to enforce chronologic order of event list
        // If the time we're adding is in the past we will add that as an adjustment
        newArrEvent.changeTime = epochNow;
        newArrEvent.editedBy = sessionManager.getActiveUser();
        newArrEvent.adjustmentList = [];

        // Construct JobEventHistoryAdjustment with details of new value
        const arrivedAdjustment = new JobEventHistoryAdjustment();
        arrivedAdjustment.adjustmentTime = epochNow;
        arrivedAdjustment.adjustedBy = sessionManager.getActiveUser();
        arrivedAdjustment.updatedValue = arr.changeTime;

        newArrEvent.adjustmentList.push(arrivedAdjustment);

        jobEventList.push(newArrEvent);
      }
    }
    // Find and update the finished event
    const foundFinished = jobEventList.find((je) => je.id === fin.id);
    if (foundFinished) {
      // If the new time and the original time are different, then push a new value into the adjustment list
      // If the value is set to 0 is means we are setting UN-ARRIVING
      if (foundFinished.correctEventTime !== fin.changeTime) {
        if (!foundFinished.adjustmentList) {
          foundFinished.adjustmentList = [];
        }
        // Construct JobEventHistoryAdjustment with details of new value
        const finishedAdjustment = new JobEventHistoryAdjustment();
        finishedAdjustment.adjustmentTime = epochNow;
        finishedAdjustment.adjustedBy = sessionManager.getActiveUser();
        finishedAdjustment.updatedValue = fin.changeTime;

        foundFinished.adjustmentList.push(finishedAdjustment);

        // UPDATE PUD ITEM STATUS
      }
    } else {
      // If we DIDN'T find the event already in the list, and the changeTime is NOT 0, then it means we need to add it
      if (fin.changeTime !== 0) {
        const newFinEvent = Object.assign(new JobStatusUpdate(), fin);
        newFinEvent.changeTime = epochNow;
        newFinEvent.editedBy = sessionManager.getActiveUser();
        newFinEvent.adjustmentList = [];

        // Construct JobEventHistoryAdjustment with details of new value
        const finishedAdjustment = new JobEventHistoryAdjustment();
        finishedAdjustment.adjustmentTime = epochNow;
        finishedAdjustment.adjustedBy = sessionManager.getActiveUser();
        finishedAdjustment.updatedValue = fin.changeTime;

        newFinEvent.adjustmentList.push(finishedAdjustment);

        jobEventList.push(newFinEvent);
      }
    }
  });

  compareEventListWithPudStatuses(jobCopy);
}
/**
 * Find what type of PUD event was the most recent for each PUD Item. If the
 * most recent PUD event does not match the current status of that PUD, then
 * we should send a request to update it to match
 * @param jobDetails
 */
async function compareEventListWithPudStatuses(jobDetails: JobDetails) {
  // This map will store what each PUDItem.status value should be based on the most recent events
  const latestEventsMap: Map<string, string | null> = new Map();

  jobDetails.pudItems.forEach((pud) => {
    const pudId = pud.pudId;
    const foundArrived: JobStatusUpdate | undefined =
      jobDetails.returnSpecifiedEvent('ARRIVED', pud.pudId, false);
    const foundFinished: JobStatusUpdate | undefined =
      jobDetails.returnSpecifiedEvent('FINISHED', pud.pudId, false);

    if (foundArrived && foundFinished) {
      // If we found the ARRIVED and FINISHED events, it means they have both been completed in the past
      const arrTime = foundArrived.correctEventTime;
      const finTime = foundFinished.correctEventTime;

      if (arrTime === 0 && finTime === 0) {
        // If the most recent of the start AND finish times is 0, then it means both have been reset.
        // This means we should set the pud status to NULL
        latestEventsMap.set(pudId, null);
      } else if (finTime === 0 && arrTime !== 0) {
        // We are undoing the FINISHED event, so set to ARRIVED
        latestEventsMap.set(pudId, 'ARRIVED');
      } else if (finTime !== 0 && arrTime === 0) {
        // This should not be possible. Set to FINISHED such that there's no incomplete status
        latestEventsMap.set(pudId, 'FINISHED');
      } else {
        // Both times are NOT ZERO. We can presume this means they are FINISHED.
        // In the case that the FINISHED event somehow occurred before the ARRIVED event, we can assume this means it should still be finished as the user should have unchecked the finished switch to unset that status
        latestEventsMap.set(pudId, 'FINISHED');
      }
    } else if (foundArrived && !foundFinished) {
      // We have found only the arrived event.
      if (foundArrived.correctEventTime === 0) {
        // If the latest ARRIVED time has a value of 0, then it means we were unsetting it
        latestEventsMap.set(pudId, null);
      } else {
        // If the latest time was not zero, then it means it was a time update and we should keep at ARRIVED
        latestEventsMap.set(pudId, 'ARRIVED');
      }
    } else if (!foundArrived && foundFinished) {
      // This should not be possible. Set to FINISHED such that there's no incomplete status
      latestEventsMap.set(pudId, 'FINISHED');
    } else {
      // We did not find any arrived or finished events.
      latestEventsMap.set(pudId, null);
    }

    // Find finished event item
  });
  // Check if the status from the eventList is the same as the existing pud status

  const updatedPudItems: PudItemStatusShort[] = [];

  latestEventsMap.forEach((value: string | null, key: string) => {
    const foundPud = jobDetails.pudItems.find((p) => p.pudId === key);
    if (foundPud) {
      if (
        (foundPud.status === 'ARRIVED' && value === 'ARRIVED') ||
        (foundPud.status === 'FINISHED' && value === 'FINISHED') ||
        (!foundPud.status && !value)
      ) {
        // Status is already matched, so we do nothing
      } else {
        // If it's not a match then we will use in the request array
        updatedPudItems.push({ pudId: key, status: value });
      }
    }
  });
  if (jobDetails.jobId) {
    const request: JobEventListUpdateRequest = {
      jobId: jobDetails.jobId,
      updatedEventList: jobDetails.eventList,
      updatedPudItems,
    };
    isAwaitingResponse.value = true;
    handleJobStatusUpdate(await jobStore.updateJobEventList(request));
  }
}
/**
 * Set the epoch time and textfield values back to the original base event
 * time
 */
function restoreOriginalEventTimes() {
  if (editingPudStatusList.value) {
    const eventList = editingPudStatusList.value;
    eventList?.forEach((event) => {
      const arr = event.arrivedEvent;
      const fin = event.finishedEvent;

      const jobEventList: JobStatusUpdate[] = props.jobDetails.eventList;
      const foundArrived = jobEventList.find((je) => je.id === arr.id);
      if (foundArrived) {
        const oldTime = foundArrived.changeTime;
        event.arrivedEvent.changeTime = oldTime ? oldTime : 0;
      } else {
        event.arrivedEvent.changeTime = 0;
      }
      const foundFinished = jobEventList.find((je) => je.id === fin.id);
      if (foundFinished) {
        const oldTime = foundFinished.changeTime;
        event.finishedEvent.changeTime = oldTime ? oldTime : 0;
      } else {
        event.finishedEvent.changeTime = 0;
      }
    });
    formIncrement.value++;
  }
}

/**
 * Trigger app notification. Defaults to ERROR type message, but type can be
 * provided to produce other types. Includes componentTitle as a title for the
 * notification.
 */
function showAppNotification(text: string, type?: HealthLevel): void {
  showNotification(text, {
    type,
    title: componentTitle,
  });
}

function setReorderPudDialog() {
  pudListByEventTimes.value = getPudOrderBasedOnEventTimes(
    props.jobDetails.pudItems,
    props.jobDetails.eventList,
  );
  reorderPudDialogIsActive.value = true;
}

/**
 * Confirm the reordering of the PUDs based on the event times, dispatch the
 * save request and handle the response.
 */
async function confirmReorderPuds() {
  try {
    updatedJobDetails.value = initialiseJobDetails(props.jobDetails);
    awaitingPudReorderingResponse.value = true;
    // Call class method to reorder the puds and return the new route
    const updatedRoute =
      await updatedJobDetails.value.reorderPudsBasedOnEventTimes();
    if (updatedRoute) {
      // Set the route to the job, and update the pud times with the new travel times
      updatedJobDetails.value.plannedRoute = updatedRoute;
      updatedJobDetails.value.setPudTimesFromPlannedRoute(
        updatedJobDetails.value.plannedRoute,
      );
      // Send request to update the full job document
      isAwaitingResponse.value = true;
      handleJobStatusUpdate(
        await jobStore.updateJobDetails(updatedJobDetails.value),
      );
    } else {
      throw new Error('Something went wrong when re-ordering pud items.');
    }
  } catch (error) {
    updatedJobDetails.value = null;
    awaitingPudReorderingResponse.value = false;
    showNotification(GENERIC_ERROR_MESSAGE);
  }
}

/**
 * Handle mitt event for a job status update. Refreshes the state.
 * @param payload JobEventSummary object
 */
function handleJobStatusUpdate(payload: JobEventSummary | null) {
  if (
    !payload?.jobId ||
    payload.jobId !== props.jobDetails.jobId ||
    !isAwaitingResponse.value
  ) {
    return;
  }
  if (payload.event === 'EVENT_TIMES_UPDATED') {
    showAppNotification(
      `Job ${props.jobDetails.displayId} - Times Updated!`,
      HealthLevel.SUCCESS,
    );
    if (
      supportingDocumentation.value.id &&
      supportingDocumentation.value.name
    ) {
      const request: AddAttachmentToJob = {
        jobId: props.jobDetails.jobId,
        pudId: null,
        attachment: supportingDocumentation.value,
      };
      jobStore.addAttachmentToJob(request);
    }
    dialogController.value = false;
  } else if (payload.event === 'UpdateJobDetails') {
    showNotification('Leg order successfully updated.', {
      type: HealthLevel.SUCCESS,
      title: 'Leg Reordering',
    });
    awaitingPudReorderingResponse.value = false;
    reorderPudDialogIsActive.value = false;
  }
  isAwaitingResponse.value = false;
}
</script>

<style scoped lang="scss">
.job-status-management {
  position: relative;
  overflow-y: scroll;
  &.disable-scroll {
    overflow-y: auto;
  }

  .dialog-content {
    $body-height: 65vh;
    height: $body-height;
    position: relative;
    overflow: hidden;
  }
  // background-color: red;
  .check-button {
    pointer-events: all;
    padding: 4px 6px;
    border-radius: 2px;

    &:hover {
      cursor: pointer;
      .check-button__icon {
        color: rgb(255, 255, 255);
      }
    }

    .check-button__icon {
      color: grey;
      font-size: $font-size-14;
    }
    &.disabled {
      pointer-events: none;
      opacity: 0.3;
    }
  }

  .expansion-job-item {
    .pud-status-list-item {
      padding: 8px;
      font-size: $font-size-16;
      text-transform: uppercase;
      color: var(--text-color);
    }

    .expansion-job-item__card-wrapper {
      padding-top: 10px;

      &.dialog-view {
        // padding-top: 6px;
        .expansion-job-item__card {
          padding: 16px;
        }
      }

      .expansion-job-item__card {
        padding: 12px;

        font-size: $font-size-18;
        font-weight: 600;
        text-transform: uppercase;

        &--jobid {
          font-weight: 500;
          color: var(--primary-light);
        }
        &--clientname {
          font-weight: 500;
          color: var(--text-color);
          padding-left: 6px;
        }
        &--time {
          color: var(--light-text-color);
          font-weight: 400;
          padding-left: 6px;
        }
        &--status {
          font-size: $font-size-15;
          font-weight: 600;
          background-color: var(--hover-bg);
          border: 1px solid var(--border-color);
          padding: 4px 14px;
          border-radius: 30px;
          color: var(--text-color);
        }
      }
    }
  }
}
.v-btn-custom {
  color: var(--text-color) !important;
}

.pud-status-list-item__header {
  color: rgb(188, 188, 190);
  text-transform: uppercase;
  font-size: $font-size-16;
  font-weight: 600;
  &.header-type {
    font-size: $font-size-16;
    font-weight: 600;
    color: var(--primary-light);
  }
}

.reorder-text {
  color: #bcbcbe;
  text-transform: uppercase;
  font-size: $font-size-14;
  font-weight: 600;
  padding-bottom: 4px;
}
</style>
