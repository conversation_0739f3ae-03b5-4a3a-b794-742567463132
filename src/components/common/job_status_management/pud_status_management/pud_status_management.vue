<template>
  <v-flex class="pud-status-management" md12>
    <v-layout>
      <v-flex md2 pa-2>
        <h2>
          <span class="index">{{ pudIndex + 1 }}.</span>
          {{ suburbName ? suburbName : '-' }}
        </h2>
      </v-flex>
      <v-flex md5 px-3 py-2 class="app-borderside--l app-bordercolor--600">
        <v-layout justify-space-between>
          <v-flex md2>
            <span>
              <v-switch
                v-model="arrivedSwitchController"
                :disabled="arrivedSwitchDisabled"
                color="light-blue"
              ></v-switch>
            </span>
          </v-flex>
          <v-flex md10>
            <DateTimeInputs
              v-if="arrivedSwitchController"
              :key="pudId + '-arr1'"
              :epochTime.sync="arrivedTime"
              :minimumEpochTime="
                prevFinishedEvent ? prevFinishedEvent.changeTime : undefined
              "
              :maximumEpochTime="maximumAllowedEpochTime"
              :enableValidation="true"
              dateLabel="Arrived Date"
              timeLabel="Arrived Time"
            ></DateTimeInputs>
            <DateTimeInputs
              v-else
              :key="pudId + '-arr2'"
              :enableValidation="false"
              dateLabel="Arrived Date"
              timeLabel="Arrived Time"
              :readOnly="true"
            ></DateTimeInputs>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md5 px-3 py-2 class="app-borderside--l app-bordercolor--600">
        <v-layout justify-space-between>
          <v-flex md2>
            <span>
              <v-switch
                v-model="finishedSwitchController"
                :disabled="finishedSwitchDisabled"
                color="light-blue"
              ></v-switch>
            </span>
          </v-flex>
          <v-flex md10>
            <DateTimeInputs
              v-if="finishedSwitchController"
              :key="pudId + '-fin1'"
              :epochTime.sync="finishedTime"
              :minimumEpochTime="arrivedTime"
              :maximumEpochTime="maximumAllowedEpochTime"
              :enableValidation="true"
              dateLabel="Finished Date"
              timeLabel="Finished Time"
            ></DateTimeInputs>
            <DateTimeInputs
              v-else
              :key="pudId + '-fin2'"
              :enableValidation="false"
              dateLabel="Finished Date"
              timeLabel="Finished Time"
              :readOnly="true"
            ></DateTimeInputs>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </v-flex>
</template>

<script setup lang="ts">
import DateTimeInputs from '@/components/common/date-picker/date_time_inputs.vue';
import { returnStartOfDayFromEpoch } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import JobStatusUpdate from '@/interface-models/Jobs/Event/JobStatusUpdate';
import { computed, onMounted, Ref, ref } from 'vue';

const props = withDefaults(
  defineProps<{
    pudId: string;
    pudIndex: number;
    suburbName: string;
    firstArrivedEvent: JobStatusUpdate | null;
    arrivedEvent: JobStatusUpdate | null;
    finishedEvent: JobStatusUpdate | null;
    nextArrivedEvent: JobStatusUpdate | null;
    nextFinishedEvent: JobStatusUpdate | null;
    prevArrivedEvent: JobStatusUpdate | null;
    prevFinishedEvent: JobStatusUpdate | null;
    disableSwitches: boolean;
  }>(),
  {
    disableSwitches: false,
  },
);

const isArrived: Ref<boolean> = ref(false);
const isFinished: Ref<boolean> = ref(false);

const maximumAllowedEpochTime: Ref<number | null> = ref(null);

const emit = defineEmits<{
  (event: 'updatedFinishedEvent', payload: JobStatusUpdate): void;
  (event: 'updatedArrivedEvent', payload: JobStatusUpdate): void;
}>();

// Control whether textfields are active, and set/unset the changeTime
const arrivedSwitchController = computed({
  get: () => isArrived.value,
  set: (value: boolean) => {
    if (value) {
      if (
        props.prevFinishedEvent &&
        props.prevArrivedEvent &&
        props.prevArrivedEvent.changeTime > 0
      ) {
        arrivedTime.value = returnStartOfDayFromEpoch(
          props.prevFinishedEvent.changeTime,
        );
      }
    } else {
      arrivedTime.value = 0;
    }
    isArrived.value = value;
  },
});
// Control whether textfields are active, and set/unset the changeTime
const finishedSwitchController = computed({
  get: () => isFinished.value,
  set: (value: boolean) => {
    if (value) {
      if (arrivedTime.value) {
        finishedTime.value = returnStartOfDayFromEpoch(arrivedTime.value);
      }
    } else {
      finishedTime.value = 0;
    }
    isFinished.value = value;
  },
});
// If the FINISHED switch value is true, then we should disable the ability to turn this one off to preserve the sequence
const arrivedSwitchDisabled = computed(() => {
  return (
    props.disableSwitches ||
    (props.pudIndex !== 0 && props.firstArrivedEvent?.changeTime === 0)
  );
});
// If the next ARRIVED event is true, then we should disable the ability to turn this one off to preserve the sequence
const finishedSwitchDisabled = computed(() => {
  return (
    props.disableSwitches ||
    (props.pudIndex !== 0 && props.firstArrivedEvent?.changeTime === 0) ||
    !props.arrivedEvent?.changeTime
  );
});
// Return the ARRIVED time from the prop, and emit to parent on set to update
const arrivedTime = computed({
  get: () => (props.arrivedEvent ? props.arrivedEvent.changeTime : 0),
  set: (value: number) => {
    const e = Object.assign(new JobStatusUpdate(), props.arrivedEvent);
    e.changeTime = value;
    emit('updatedArrivedEvent', e);
  },
});
// Return the FINISHED time from the prop, and emit to parent on set to update
const finishedTime = computed({
  get: () => (props.finishedEvent ? props.finishedEvent.changeTime : 0),
  set: (value: number) => {
    const e = Object.assign(new JobStatusUpdate(), props.finishedEvent);
    e.changeTime = value;
    emit('updatedFinishedEvent', e);
  },
});

// Return true if the user is an ADMIN or HEAD OFFICE user. Used to determine
// whether maximum epochTime values should be enforced on the
// DateTimeTextFields component
function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}

onMounted(() => {
  isArrived.value =
    props.arrivedEvent !== null &&
    props.arrivedEvent !== undefined &&
    props.arrivedEvent.changeTime !== 0;

  isFinished.value =
    props.finishedEvent !== null &&
    props.finishedEvent !== undefined &&
    props.finishedEvent.changeTime !== 0;

  if (!isAuthorised()) {
    // Set the maximum epochTime for the inputs to be the start of tomorrow
    maximumAllowedEpochTime.value = returnStartOfDayFromEpoch() + 86400000; // millisecondsInOneDay is 86400000
  }
});
</script>

<style scoped lang="scss">
.index {
  color: $light-text-color !important;
  padding-right: 12px;
}
</style>
