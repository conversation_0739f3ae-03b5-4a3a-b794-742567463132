import {
  downloadAttachment,
  getIconByMimeType,
} from '@/helpers/AttachmentHelpers/AttachmentHelpers';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import Attachment from '@/interface-models/Generic/Attachment/Attachment';
import AttachmentApproval from '@/interface-models/Generic/Attachment/AttachmentApproval';
import AttachmentStatus from '@/interface-models/Generic/Attachment/AttachmentStatus';
import AttachmentTypes from '@/interface-models/Generic/Attachment/AttachmentTypes';
import { useAttachmentStore } from '@/store/modules/AttachmentStore';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';

@Component({})
export default class ImageReviewSlider extends Vue {
  @Prop() public images: Attachment[];
  @Prop() public imageSliderExpanded: boolean;
  @Prop() public currentImageIndex: number;
  @Prop() public disableApprovals: boolean;
  @Prop() public isLastLeg: boolean;

  public attachmentStore = useAttachmentStore();

  public showEditApproval: boolean = false;
  public getIconByMimeType = getIconByMimeType;
  public downloadAttachment = downloadAttachment;

  public attachmentMap: Map<string, Attachment | null> = new Map();

  get viewingImageIndex() {
    return this.currentImageIndex;
  }
  set viewingImageIndex(value: number) {
    this.showEditApproval = false;
    this.$emit('update:currentImageIndex', value);
  }

  get imageData(): Attachment[] {
    if (this.images) {
      return this.images;
    } else {
      return [];
    }
  }

  get imageAttachmentIds(): string[] {
    return this.imageData.map((img) => img.id);
  }

  get currentImage(): Attachment | null {
    if (
      this.currentImageIndex === -1 ||
      this.imageData.length <= this.viewingImageIndex
    ) {
      return null;
    }

    const thumbnail: Attachment = this.imageData[this.viewingImageIndex];
    if (this.attachmentMap.has(thumbnail.id)) {
      const fullSize: Attachment | undefined =
        this.attachmentMap.get(thumbnail.id) || undefined;
      if (fullSize) {
        fullSize.approvalStatus = thumbnail.approvalStatus;
        return fullSize;
      }
    } else {
      this.getAndAddAttachmentById(thumbnail.id);
    }
    return null;
  }

  public async getAndAddAttachmentById(id: string) {
    this.attachmentMap.set(id, null);
    const att = await this.attachmentStore.getAttachmentById(id);

    if (att) {
      this.attachmentMap = new Map(this.attachmentMap.set(id, att));
    }
  }

  get documentLoading(): boolean {
    if (
      this.currentImageIndex === -1 ||
      this.imageData.length <= this.viewingImageIndex
    ) {
      return false;
    }

    const thumbnail: Attachment = this.imageData[this.viewingImageIndex];
    const fullSize: Attachment | undefined | null = this.attachmentMap.get(
      thumbnail.id,
    );

    return fullSize ? false : true;
  }
  get currentImageApprovalStatus(): AttachmentApproval | undefined {
    if (this.currentImage !== undefined) {
      const currentImageClone: Attachment = Object.assign(
        new Attachment(),
        this.currentImage,
      );
      const mostRecent: AttachmentApproval | undefined =
        currentImageClone.currentApprovalStatus;
      return mostRecent;
    }
    return;
  }

  get showApproveButtons(): boolean {
    if (this.imageData.length === 0) {
      return false;
    }
    if (this.showEditApproval) {
      return true;
    }
    if (this.currentImageApprovalStatus === undefined) {
      return true;
    } else {
      if (
        this.currentImageApprovalStatus.status === 'REJECTED' ||
        this.currentImageApprovalStatus.status === 2 ||
        this.currentImageApprovalStatus.status === 'APPROVED' ||
        this.currentImageApprovalStatus.status === 0
      ) {
        return false;
      } else {
        return true;
      }
    }
  }

  public updateAttachmentStatus(reject: boolean = false) {
    this.$emit('updateAttachmentStatus', reject);
  }

  public returnApprovalClassString(approval: AttachmentApproval | undefined) {
    const classList: string[] = [];
    if (approval === undefined) {
      classList.push('pending');
    } else {
      switch (approval.status) {
        case 'APPROVED':
        case AttachmentStatus.APPROVED:
          classList.push('approved');
          break;
        case 'PENDING':
        case AttachmentStatus.PENDING:
          classList.push('pending');
          break;
        case 'REJECTED':
        case AttachmentStatus.REJECTED:
          classList.push('rejected');
          break;
      }
    }
    return classList.join(' ');
  }

  public returnIconString() {
    if (this.currentImage) {
      switch (this.currentImage.documentTypeId) {
        case 6:
        case AttachmentTypes.PUD_ITEM_SIGNATURE:
          return 'fal fa-signature';
        case 7:
        case AttachmentTypes.PUD_ITEM_IMAGE:
          return 'fal fa-image';
        case 8:
        case AttachmentTypes.PUD_ITEM_DOCUMENT:
          return 'fal fa-file-alt';
      }
    }
  }

  public isSignatureType(): boolean {
    if (this.currentImage) {
      if (
        this.currentImage.documentTypeId === 6 ||
        this.currentImage.documentTypeId === AttachmentTypes.PUD_ITEM_SIGNATURE
      ) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  public updateViewingImageIndex(value: number, changeDrop: string = '') {
    if (changeDrop !== '') {
      this.$emit('viewAttachmentsForOtherStop', changeDrop);
      return;
    }
    if (value >= 0 && value <= this.imageData.length - 1) {
      this.viewingImageIndex = value;
    }
  }

  public returnReadableTime(epoch: number): string {
    return returnFormattedDate(epoch, 'H:mm DD/MM/YY');
  }

  @Watch('imageSliderExpanded')
  public expandChange(value: boolean) {
    if (value) {
      if (this.imageData.length > this.viewingImageIndex) {
        const thumbnail: Attachment = this.imageData[this.viewingImageIndex];
        this.attachmentStore.getAttachmentById(thumbnail.id);
      }
    }
  }

  public async initAttachmentsMap() {
    const requests = this.imageAttachmentIds.map((id) =>
      this.getAndAddAttachmentById(id),
    );
    await Promise.all(requests);
  }

  public async mounted() {
    await this.initAttachmentsMap();
  }

  public convertBase64ToPdfFile(data: string) {
    const bin = atob(data.split(',')[1]);
    const byteNumbers = new Array(bin.length);
    for (let i = 0; i < bin.length; i++) {
      byteNumbers[i] = bin.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);

    const file = new Blob([byteArray], {
      type: 'application/pdf',
    });
    return window.URL.createObjectURL(file);
  }
}
