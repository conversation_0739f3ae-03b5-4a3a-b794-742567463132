<div id="image-slider">
  <div class="pa-0 body-container">
    <div
      class="button-container left"
      v-if="viewingImageIndex !== 0 && images.length > 0"
      @click="updateViewingImageIndex(viewingImageIndex - 1)"
    >
      <v-icon> far fa-chevron-left </v-icon>
    </div>
    <div
      class="button-container left"
      v-if="viewingImageIndex === 0 || images.length === 0"
      @click="updateViewingImageIndex(-1, 'prevStop')"
    >
      <v-icon> far fa-chevron-double-left </v-icon>
    </div>
    <div
      class="button-container right"
      v-if="viewingImageIndex !== (imageData.length - 1) && images.length !== 0"
      @click="updateViewingImageIndex(viewingImageIndex + 1)"
    >
      <v-icon> far fa-chevron-right </v-icon>
    </div>
    <div
      class="button-container right"
      v-if="viewingImageIndex === (imageData.length - 1) || viewingImageIndex === 0 && images.length === 0"
      @click="updateViewingImageIndex(-1, 'nextStop')"
    >
      <v-icon> far fa-chevron-double-right </v-icon>
    </div>

    <v-fade-transition hide-on-leave>
      <div
        :key="currentImage ? currentImage.id : null"
        class="image-container"
        :class="isSignatureType() ? 'signature-type' : ''"
      >
        <v-img
          v-if="currentImage"
          :src="currentImage.data && currentImage.mimeType.includes('image') && !currentImage.mimeType.includes('heic') && !currentImage.mimeType.includes('heif') ? currentImage.data : ''"
          aspect-ratio="1"
          class="current-image__img"
        >
          <template v-slot:placeholder>
            <v-layout
              justify-center
              align-center
              fill-height
              column
              class="app-bgcolor--300"
              v-if="currentImage.mimeType !== 'application/pdf'"
            >
              <v-icon size="50"
                >{{ getIconByMimeType(currentImage.mimeType) }}
              </v-icon>
            </v-layout>
            <v-layout
              justify-center
              align-center
              fill-height
              column
              v-if="currentImage.mimeType === 'application/pdf'"
            >
              <object
                class="pdf-view-container"
                :data="convertBase64ToPdfFile(currentImage.data)"
                type="application/pdf"
                width="500"
                height="678"
              ></object>
            </v-layout>
          </template>
        </v-img>
        <div v-if="documentLoading || !currentImage && images.length > 0">
          <v-progress-circular
            indeterminate
            color="white"
          ></v-progress-circular>
        </div>
        <div v-if="images.length === 0" class="no-images-available-container">
          <v-layout column justify-center align-center>
            <v-icon size="40" color="grey" class="mb-2"
              >far fa-image-polaroid</v-icon
            >
            No Images Available For This Leg.
          </v-layout>
        </div>
      </div>
    </v-fade-transition>
  </div>

  <div
    class="approval-message-container"
    v-if="currentImageApprovalStatus !== undefined"
  >
    <div
      class="approval-message"
      :class="returnApprovalClassString(currentImageApprovalStatus)"
      v-if="currentImageApprovalStatus.status === 'APPROVED' || currentImageApprovalStatus.status === 0 || currentImageApprovalStatus.status === 'REJECTED' || currentImageApprovalStatus.status === 2"
    >
      <v-layout row wrap>
        <v-flex md12>
          <v-layout class="approval-message__status" align-center>
            <v-icon
              size="13"
              class="pr-2"
              v-if="currentImageApprovalStatus.status === 'APPROVED' || currentImageApprovalStatus.status === 0"
            >
              fal fa-check-circle
            </v-icon>
            <v-icon
              size="13"
              class="pr-2"
              v-if="currentImageApprovalStatus.status === 'REJECTED' || currentImageApprovalStatus.status === 2"
            >
              fal fa-times-circle
            </v-icon>
            {{ currentImageApprovalStatus.status }}
          </v-layout>
          <v-layout class="approval-message__subtitle first">
            by {{ currentImageApprovalStatus.user }}
          </v-layout>
          <v-layout class="approval-message__subtitle">
            {{ returnReadableTime(currentImageApprovalStatus.epochTime) }}
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
  </div>
  <div class="attachment-type__container">
    <div class="attachment-type__icon">
      <v-icon class="attachment-icon" size="24">
        {{returnIconString()}}
      </v-icon>
    </div>
  </div>
  <div
    class="approve-buttons__container"
    v-if="!disableApprovals && (imageData !== null && imageData.length > 0)"
  >
    <div class="approve-buttons" v-if="showApproveButtons">
      <span>
        <v-btn
          color="green accent-4"
          outline
          :small="!imageSliderExpanded"
          class="ma-0 mr-1"
          @click="updateAttachmentStatus()"
        >
          Approve
        </v-btn>

        <v-btn
          icon
          outline
          color="red"
          :small="!imageSliderExpanded"
          class="ma-0 ml-1"
          @click="updateAttachmentStatus(true)"
        >
          <v-icon size="18">far fa-times</v-icon>
        </v-btn>
      </span>
    </div>
    <div
      class="approve-buttons__alt"
      v-if="!showApproveButtons"
      @click="showEditApproval = true"
    >
      <span>
        <v-icon size="16"> fal fa-ellipsis-h </v-icon>
      </span>
    </div>
    <div
      v-if="currentImage && currentImage.documentTypeId === 14"
      class="signature-text__container"
    >
      <span>Signed by: </span>
      <span class="pl-1 signature-text"
        >{{currentImage.signatureName ? currentImage.signatureName : '-'}}</span
      >
    </div>
  </div>
  <div
    class="page-indicator__container"
    v-if="imageData !== null && imageData.length > 0"
  >
    <div class="page-indicator">
      {{currentImageIndex + 1}} of {{ imageData.length }}
    </div>
  </div>
  <div class="expand-slider">
    <v-btn icon @click="downloadAttachment(currentImage)">
      <v-icon color="primary" size="15">fad fa-download</v-icon>
    </v-btn>
  </div>
</div>
