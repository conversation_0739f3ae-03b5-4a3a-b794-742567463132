#image-slider {
  height: 100%;
  position: relative;

  .expand-slider {
    z-index: 2;
    position: absolute;
    bottom: 0px;
    right: 0px;
  }

  .image-container {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    &.signature-type {
      background-color: rgb(202, 202, 202);
      padding: 30px;
    }

    .current-image__img {
      max-height: 100%;
      max-width: 100%;
      object-fit: contain !important;
    }
  }
  .approval-container {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 2;
  }

  .body-container {
    position: relative;
    height: 100%;
    width: 100%;

    max-height: 100%;
    max-width: 100%;

    .button-container {
      &:hover {
        background-color: rgba(79, 71, 71, 0.377);
      }
      z-index: 3;
      cursor: pointer;
      transition: 0.1s;
      background-color: rgba(98, 97, 108, 0.1);
      padding: 16px;
      border-radius: 4px;

      top: 50%;
      position: absolute;
      &.left {
        left: 10px;
      }
      &.right {
        right: 10px;
      }
    }
  }
  .approval-message-container {
    position: absolute;
    bottom: 0px;
    left: 0px;
    width: 100%;
    .approval-message {
      position: relative;
      padding: 6px 10px;
      background-color: rgba(36, 35, 42, 0.95);
      border: 1px solid $app-dark-primary-550;

      &.approved {
        border-top: 3px solid #1bd659;
      }
      &.pending {
        border-top: 3px solid yellow;
      }
      &.rejected {
        border-top: 3px solid rgb(207, 54, 54);
      }

      .approval-message__status {
        font-size: $font-size-medium;
        font-weight: 600;
        color: #dddddd;
      }

      .approval-message__subtitle {
        &.first {
          padding-top: 3px;
        }
        font-size: $font-size-small;
        font-weight: 500;
        color: rgb(153, 153, 153);
      }
    }
  }

  .page-indicator__container {
    position: absolute;
    top: 0px;
    right: 0px;
    padding: 6px 10px;
    .page-indicator {
      background-color: #202020e8;
      border-radius: 10px;
      padding: 3px 8px;
      font-size: 0.75em;
      text-transform: uppercase;
      font-weight: 600;
      color: #dddddd;
    }
  }

  .attachment-type__container {
    position: absolute;
    top: 0px;
    left: 0px;
    padding: 6px 10px;
    .attachment-type__icon {
      .attachment-icon {
        font-weight: 700;
        color: rgb(26, 26, 26);
      }
    }
  }

  .approve-buttons__container {
    position: absolute;
    top: 0px;
    left: 50%;
    transform: translateX(-50%);
    padding: 6px 10px;
    .approve-buttons {
      background-color: #202020e8;
      border-radius: 10px;
      padding: 6px 8px;
      // width: auto;
      border: 1px solid $table-overflow;
    }
    .approve-buttons__alt {
      background-color: #202020e8;
      border-radius: 4px;
      padding: 0px 10px;
      width: auto;
      // border: 1px solid $table-overflow;
      transition: 0.05s;
      &:hover {
        cursor: pointer;
        background-color: #202020d5;
      }
    }

    .signature-text__container {
      padding-top: 5px;
      color: black;
      text-align: center;
      .signature-text {
        font-weight: 700;
        text-transform: uppercase;
      }
    }
  }
}

.no-images-available-container {
  color: grey;
  font-weight: bold;
  background-color: rgb(202, 202, 202);
  padding: 30px;
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}
