import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import ImageReviewSlider from '@/components/common/image-slider/index.vue';
import Attachment from '@/interface-models/Generic/Attachment/Attachment';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import AttachmentStatus from '@/interface-models/Generic/Attachment/AttachmentStatus';
import PudTimeline from '@/components/common/pud-timeline/index.vue';

import { useRootStore } from '@/store/modules/RootStore';
import JobStatusUpdate from '@/interface-models/Jobs/Event/JobStatusUpdate';
import AttachmentTypes from '@/interface-models/Generic/Attachment/AttachmentTypes';
import JobAttachmentUpload from '@/components/common/job_attachment_upload/index.vue';

import { UpdateAttachmentApprovalStatusRequest } from '@/interface-models/Generic/Attachment/UpdateAttachmentApprovalStatusRequest';
import { useAttachmentStore } from '@/store/modules/AttachmentStore';
import { useJobStore } from '@/store/modules/JobStore';
@Component({
  components: {
    ImageReviewSlider,
    PudTimeline,
    JobAttachmentUpload,
  },
})
export default class JobAttachmentApproval extends Vue {
  @Prop() public jobDetails: JobDetails;
  @Prop({ default: false }) public imageSliderExpanded: boolean;
  @Prop({ default: false }) public isOutsideHire: boolean;

  public currentImageIndex: number = 0;
  public selectedPUDIndex: number = 0;

  public selectingAttachmentsForUpload: boolean = false;

  get selectedPUD(): PUDItem | undefined {
    if (this.jobDetails.pudItems.length > this.selectedPUDIndex) {
      return this.jobDetails.pudItems[this.selectedPUDIndex];
    }
    return undefined;
  }
  get selectedPudAttachments() {
    if (this.selectedPUD) {
      if (
        this.selectedPUD.attachments !== null &&
        this.selectedPUD.attachments.length > 0
      ) {
        return this.selectedPUD.attachments;
      } else {
        return [];
      }
    } else {
      return [];
    }
  }
  get selectedJobValidPuds(): PUDItem[] {
    return this.jobDetails.pudItems.filter(
      (p) => p.legTypeFlag === 'P' || p.legTypeFlag === 'D',
    );
  }
  get invoiceRequirementsMet(): boolean {
    if (!this.isOutsideHire) {
      return true;
    }
    const foundInvoice = this.jobDetails.attachments.find(
      (a) => a.documentTypeId === AttachmentTypes.JOB_INVOICE_PDF,
    );
    if (foundInvoice) {
      return true;
    }
    return false;
  }
  public returnStartTime(eventList: JobStatusUpdate[]) {
    const foundStartTime = eventList.find(
      (event: JobStatusUpdate) => event.updatedStatus === 'StartedJob',
    );
    if (foundStartTime !== undefined) {
      return foundStartTime.correctEventTime;
    } else {
      return;
    }
  }

  // returns true if the last leg is selected. This value is passed into our image slider to indicate that there is no more images to go next on.
  get isLastLeg() {
    return this.selectedPUDIndex === this.jobDetails.pudItems.length - 1;
  }

  public setActivePudIndex(index: number): void {
    this.currentImageIndex = 0;
    this.setSelectedPudIndex(index);
  }

  public setActivePudIndexFromPudid(value: string): void {
    if (!value) {
      return;
    }
    const index = this.jobDetails.pudItems.findIndex(
      (pud) => pud.pudId === value,
    );
    if (index === -1) {
      return;
    }
    this.currentImageIndex = 0;
    this.setSelectedPudIndex(index);
  }

  public setSelectedPudIndex(index: number) {
    this.selectedPUDIndex = index;
  }

  public finishedUploadingAttachments(): void {
    this.selectingAttachmentsForUpload = false;
  }

  public findNextStop(index: number): number {
    let currentIndex = index;
    if (currentIndex === this.jobDetails.pudItems.length - 1) {
      currentIndex = 0;
    } else {
      currentIndex += 1;
    }
    const newPud = this.jobDetails.pudItems[currentIndex];

    if (newPud.legTypeFlag === 'P' || newPud.legTypeFlag === 'D') {
      return currentIndex;
    } else {
      return this.findNextStop(currentIndex);
    }
  }

  public findPreviousStop(index: number): number {
    let currentIndex = index;
    const pudLen = this.jobDetails.pudItems.length;
    if (currentIndex === 0) {
      currentIndex = pudLen - 1;
      this.currentImageIndex =
        this.jobDetails.pudItems[pudLen - 1].attachments.length - 1;
    } else {
      currentIndex -= 1;
    }
    const newPud = this.jobDetails.pudItems[currentIndex];

    if (newPud.legTypeFlag === 'P' || newPud.legTypeFlag === 'D') {
      return currentIndex;
    } else {
      return this.findPreviousStop(currentIndex);
    }
  }

  public changeImageSliderPudItem(type: string) {
    if (type === 'nextStop') {
      this.setSelectedPudIndex(this.findNextStop(this.selectedPUDIndex));
      this.currentImageIndex = 0;
    }
    if (type === 'prevStop') {
      this.setSelectedPudIndex(this.findPreviousStop(this.selectedPUDIndex));
      this.currentImageIndex =
        this.jobDetails.pudItems[this.selectedPUDIndex].attachments.length - 1;
    }
  }

  public updateCarouselPosition(value: number) {
    this.currentImageIndex = value;
  }

  public updateAttachmentStatus(reject: boolean = false) {
    if (
      this.selectedPudAttachments !== undefined &&
      this.selectedPUD !== undefined &&
      !!this.jobDetails.jobId
    ) {
      const selectedAttachment: Attachment =
        this.selectedPudAttachments[this.currentImageIndex];
      const requestObj: UpdateAttachmentApprovalStatusRequest = {
        attachmentId: selectedAttachment.id,
        newStatus: reject
          ? AttachmentStatus.REJECTED
          : AttachmentStatus.APPROVED,
        jobId: this.jobDetails.jobId,
        pudId: this.selectedPUD.pudId,
      };
      useJobStore().updateAttachmentApprovalStatus(requestObj);
      if (this.currentImageIndex !== this.selectedPudAttachments.length - 1) {
        this.currentImageIndex += 1;
      } else {
        this.currentImageIndex = 0;
      }
    }
  }
}
