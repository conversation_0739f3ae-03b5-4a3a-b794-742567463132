<div class="job-attachment-approval">
  <v-flex md4 v-if="!imageSliderExpanded" class="side-menu">
    <v-layout pr-2>
      <v-btn
        block
        small
        outline
        class="v-btn-custom"
        @click="selectingAttachmentsForUpload = true"
        :disabled="selectingAttachmentsForUpload"
        >Add Attachment</v-btn
      >
    </v-layout>
    <pud-timeline
      :pudItems="jobDetails.pudItems"
      :showStartTime="false"
      :startTime="returnStartTime(jobDetails.eventList)"
      :dynamic="true"
      :detailed="false"
      :locked="true"
      :selectable="true"
      :alwaysExpanded="true"
      :showAttachments="true"
      :addPadding="true"
      :selectedPud="selectedPUDIndex"
      @indexSelected="setActivePudIndex"
      :proofOfDeliveryRequirements="jobDetails.proofOfDelivery"
    >
    </pud-timeline>
  </v-flex>
  <v-flex
    :md8="!imageSliderExpanded"
    :md12="imageSliderExpanded"
    class="image-slider-container"
  >
    <ImageReviewSlider
      v-if="!selectingAttachmentsForUpload"
      :images="selectedPudAttachments"
      :imageSliderExpanded="imageSliderExpanded"
      :currentImageIndex.sync="currentImageIndex"
      @viewingImageIndexChanged="updateCarouselPosition"
      @updateAttachmentStatus="updateAttachmentStatus"
      @viewAttachmentsForOtherStop="changeImageSliderPudItem"
      :isLastLeg="isLastLeg"
    />
  </v-flex>
  <v-dialog v-model="selectingAttachmentsForUpload" persistent width="500px">
    <v-layout
      justify-space-between
      class="task-bar app-theme__center-content--header no-highlight"
    >
      <span>Upload Attachment</span>
      <div
        class="app-theme__center-content--closebutton"
        @click="selectingAttachmentsForUpload = false"
      >
        <v-icon class="app-theme__center-content--closebutton--icon"
          >fal fa-times</v-icon
        >
      </div>
    </v-layout>

    <v-layout
      class="app-theme__center-content--body px-3 pb-1"
      v-if="selectingAttachmentsForUpload"
    >
      <job-attachment-upload
        :jobId="jobDetails.jobId"
        :pudId="selectedPUD.pudId"
        :jobDetails="jobDetails"
        :isOutsideHire="isOutsideHire"
        @selectedPudChanged="setActivePudIndexFromPudid"
        @finishedUploadingAttachments="finishedUploadingAttachments"
      ></job-attachment-upload>
    </v-layout>
  </v-dialog>
</div>
