<div class="driver-work-diary-history">
  <v-layout
    justify-space-between
    class="driver-search-top-section px-2 pt-2 pb-1"
  >
    <v-flex md4 v-if="!singleDriverType">
      <v-form>
        <SelectEntity
          :entityTypes="[entityType.DRIVER]"
          :id.sync="historyRequest.driverId"
          :placeholder="'Select Driver'"
          :hint="'Select Driver'"
        />
      </v-form>
    </v-flex>

    <v-flex md3 class="px">
      <DatePickerBasic
        :soloInput="true"
        @setEpoch="setStartEpoch"
        v-model="historyRequest.startEpoch"
        :labelName="'From Date'"
        :epochTime="historyRequest.startEpoch"
      >
      </DatePickerBasic>
    </v-flex>
    <v-flex md3 class="px">
      <DatePickerBasic
        @setEpoch="setEndEpoch"
        :soloInput="true"
        v-model="historyRequest.endEpoch"
        :labelName="'To Date'"
        :epochTime="historyRequest.endEpoch"
      >
      </DatePickerBasic>
    </v-flex>
    <v-spacer v-if="singleDriverType"></v-spacer>
    <v-flex :class="singleDriverType ? 'md3' : 'md2'">
      <v-btn
        class="view-details-button"
        block
        depressed
        :disabled="historyRequest.driverId === ''"
        @click="searchWorkDiaryHistory"
        >Search for Dates</v-btn
      >
    </v-flex>
  </v-layout>
  <v-layout class="filter-result-container">
    <v-flex md12>
      <v-text-field
        class="v-solo-custom pr-2 pl-2"
        hide-details
        flat
        solo
        v-model="search"
        color="orange"
        placeholder="Filter Results..."
      />
    </v-flex>
  </v-layout>

  <v-layout row wrap v-if="workDiaryListItems.length > 0" pa-3>
    <v-flex
      md12
      v-for="(workDiaryItem, index) in workDiaryListItems"
      :mt-2="workDiaryItem.differentDateFromPrevious"
      :key="workDiaryItem.id"
    >
      <v-layout class="eventlist-item pb-1">
        <span
          class="eventlist-item__date"
          :class="workDiaryItem.differentDateFromPrevious"
          ><span v-if="workDiaryItem.differentDateFromPrevious"
            >{{workDiaryItem.readableDate}}</span
          ></span
        >
        <span class="eventlist-item__time" :class="workDiaryItem.className"
          >{{workDiaryItem.readableTime}}</span
        >
        <v-flex>
          <span class="eventlist-item__title">
            {{workDiaryItem.eventTitle}} -
          </span>
          <span class="eventlist-item__username">
            {{workDiaryItem.eventSubtitle}}
          </span>
        </v-flex>
      </v-layout>
    </v-flex>
  </v-layout>
</div>
