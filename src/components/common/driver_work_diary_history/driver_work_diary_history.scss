.driver-work-diary-history {
  .eventlist-item {
    font-size: $font-size-12;
    color: rgb(205, 208, 220);
    &.with-hover {
      &:hover {
        filter: brightness(115%);
        background-color: $app-dark-primary-600;
        cursor: pointer;
      }
    }

    &.dense-list {
      font-size: $font-size-11;
      padding: 2px 0px;
    }

    &--selected {
      background-color: #484858;
    }
    .eventlist-item__time {
      padding: 2px 4px;
      font-size: $font-size-11;
      border-radius: 2px;
      font-weight: 600;
      color: black;
      margin-left: 6px;
      margin-right: 6px;
      position: relative;
      white-space: nowrap;
      border-radius: $border-radius-sm;
      border: 0.5px solid $translucent;

      $primary-swatch: rgb(84, 179, 246);
      $red-swatch: rgb(238, 44, 44);
      &.break {
        // background-color: green;
        background-color: $success;
      }
      &.work {
        // background-color: green;
        background-color: $warning;
      }
      &.rest {
        // background-color: green;
        background-color: var(--accent);
      }
    }

    .eventlist-item__date {
      color: grey;
      min-width: 55px;
      padding: 2px 4px;
      font-size: $font-size-11;
      font-weight: 600;
      text-align: right;
    }

    .eventlist-item__title {
      // padding-left: 3px;
      text-transform: uppercase;
      font-weight: 600;
      color: white;
    }
    .eventlist-item__username {
      padding-left: 3px;
    }
  }
}
