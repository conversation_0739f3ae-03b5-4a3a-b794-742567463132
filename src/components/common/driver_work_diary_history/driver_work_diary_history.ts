import { Component, Prop, Vue } from 'vue-property-decorator';
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import {
  returnEndOfDayFromEpoch,
  returnFormattedDate,
  returnFormattedTime,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';

import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import WorkDiaryRecordHistoryRequest from '@/interface-models/Driver/WorkDiary/WorkDiaryRecordHistoryRequest';
import WorkDiaryRecord from '@/interface-models/Driver/WorkDiary/WorkDiaryRecord';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { WorkDiaryActivityType } from '@/interface-models/Driver/WorkDiary/WorkDiaryActivityType';

import SelectEntity from '@/components/common/inputs/select_entity/index.vue';
import { EntityType } from '@/interface-models/Generic/EntityType';
interface WorkDiaryListItem {
  id: string;
  epochTime: number;
  readableTime: string;
  readableDate: string;
  activityType: WorkDiaryActivityType;
  eventTitle: string;
  eventSubtitle: string;
  className: string;
  differentDateFromPrevious: boolean;
}

@Component({
  components: { DatePickerBasic, SelectEntity },
})
export default class DriverWorkDiaryHistory extends Vue {
  @Prop({ default: false }) public singleDriverType: boolean;
  @Prop({ default: '' }) public driverId: string;

  public driverDetailsStore = useDriverDetailsStore();

  public entityType = EntityType;
  public returnFormattedDate = returnFormattedDate;
  public returnFormattedTime = returnFormattedTime;
  public historyRequest: WorkDiaryRecordHistoryRequest =
    new WorkDiaryRecordHistoryRequest();
  public search: string = '';

  public workDiaryListItems: WorkDiaryListItem[] = [];

  // Handle selections from start date picker
  public setStartEpoch(startEpoch: number) {
    this.historyRequest.startEpoch = returnStartOfDayFromEpoch(startEpoch);
  }
  // Handle selections from end date picker
  public setEndEpoch(endEpoch: number) {
    this.historyRequest.endEpoch = returnEndOfDayFromEpoch(endEpoch);
  }

  /**
   * Send request, await response and process the list of work diary records
   */
  public async searchWorkDiaryHistory() {
    const results = await this.driverDetailsStore.searchWorkDiaryRecords(
      this.historyRequest,
    );
    if (!!results?.length) {
      this.processWorkDiaryListResponse(results);
    } else {
      showNotification('No results were found for the supplied date.', {
        type: HealthLevel.INFO,
      });
    }
  }

  // Return a description for the provided activity type
  public returnActivityTypeDescription(type: WorkDiaryActivityType): string {
    let subtitleString = '';
    switch (type) {
      case WorkDiaryActivityType.WORK:
        subtitleString = 'The driver started or resumed WORK.';
        break;
      case WorkDiaryActivityType.BREAK:
        subtitleString = 'The driver started a BREAK.';
        break;
      case WorkDiaryActivityType.REST:
        subtitleString = 'The driver started RESTING (work ended).';
        break;
    }
    return subtitleString;
  }
  // Accept list from API response and return a list of corresponding items to be iterated and displayed in HTML
  public constructWorkDiaryListItems(
    items: WorkDiaryRecord[],
  ): WorkDiaryListItem[] {
    const eventList: WorkDiaryListItem[] = [];

    const returnTitle = (type: WorkDiaryActivityType) =>
      WorkDiaryActivityType[type].replace(/_/g, ' ');

    for (let i = 0; i < items.length; i++) {
      const record: WorkDiaryRecord = items[i];

      const eventListItem: WorkDiaryListItem = {
        id: record._id ? record._id : '',
        epochTime: record.activityTimestamp,
        readableTime: returnFormattedTime(record.activityTimestamp),
        readableDate: returnFormattedDate(record.activityTimestamp),
        activityType: record.activityType,
        eventTitle: returnTitle(record.activityType), // Removed underscores from enum values to use as title
        eventSubtitle: this.returnActivityTypeDescription(record.activityType), // Returns a description of the event to be used as subtitle
        className: WorkDiaryActivityType[record.activityType].toLowerCase(), // used in CSS
        differentDateFromPrevious: false,
      };
      eventList.push(eventListItem);
    }
    return eventList;
  }

  // Handle response from search API
  public processWorkDiaryListResponse(workDiaryList: WorkDiaryRecord[]) {
    const diaryList = this.constructWorkDiaryListItems(workDiaryList);
    let currentDateString: string = '';
    for (let i = 0; i < diaryList.length; i++) {
      const item = diaryList[i];
      if (i === 0) {
        item.differentDateFromPrevious = true;
        currentDateString = item.readableDate;
      } else {
        if (item.readableDate !== currentDateString) {
          item.differentDateFromPrevious = true;
          currentDateString = item.readableDate;
        } else {
          item.differentDateFromPrevious = false;
        }
      }
    }
    this.workDiaryListItems = diaryList;
  }

  public mounted() {
    this.historyRequest.driverId = this.driverId;
    this.searchWorkDiaryHistory();
  }
}
