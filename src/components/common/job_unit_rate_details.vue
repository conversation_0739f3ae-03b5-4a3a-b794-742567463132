<template>
  <section class="job-unit-rate-details">
    <v-layout row wrap class="expansion-job-item pa-2 mb-3">
      <v-flex md12>
        <v-layout justify-space-between align-center px-2>
          <span class="subheader">Unit Rate - Select Pickup/Dropoff Zones</span>
          <v-btn
            depressed
            class="edit-btn"
            small
            @click="dialogController = true"
          >
            <v-icon size="16" class="pr-2">edit</v-icon>
            Edit
          </v-btn>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-divider></v-divider>
      </v-flex>
      <v-flex md12 class="pud-status-list-item">
        <v-layout px-2 pt-3 align-end>
          <span class="pud-status-list-item__header header-type">Stop</span>
          <v-spacer></v-spacer>
          <v-flex md1 style="text-align: right">
            <span class="pud-status-list-item__header header-type">
              Selected Zone
            </span>
          </v-flex>
          <v-flex md1 style="text-align: right">
            <span class="pud-status-list-item__header header-type">
              Unit Type
            </span>
          </v-flex>
          <v-flex md1 style="text-align: right">
            <span class="pud-status-list-item__header header-type">
              Units P/U
            </span>
          </v-flex>
          <v-flex md1 style="text-align: right">
            <span class="pud-status-list-item__header header-type">
              Units D/O
            </span>
          </v-flex>
          <v-flex md1 style="text-align: right">
            <span class="pud-status-list-item__header header-type">
              Req. Forklift
            </span>
          </v-flex>
          <v-flex md1 style="text-align: right">
            <span class="pud-status-list-item__header header-type">
              Dangerous Goods
            </span>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex
        md12
        class="pud-status-list-item app-borderside--t app-bordercolor--600"
        v-for="(pud, pudIndex) of unitRateInformation"
        :key="pud.pudId || pud.uniqueId"
      >
        <v-layout px-2 align-center>
          <span class="pr-2">{{ pudIndex + 1 }}. </span>
          <span class="pr-1">{{ pud.suburbName ? pud.suburbName : '-' }}</span>
          <span>({{ pud.legTypeFlag }})</span>
          <v-spacer> </v-spacer>

          <v-flex md1 style="text-align: right">
            <span v-if="returnUnitRateZoneNameFromId(pud.zoneReference)">
              {{ returnUnitRateZoneNameFromId(pud.zoneReference) }}
            </span>
            <span v-else class="accent-text--card error-type"> Required </span>
          </v-flex>

          <v-flex md1 style="text-align: right">
            {{ returnUnitRatesNameForZoneId(pud.zoneReference) }}
          </v-flex>
          <v-flex md1 style="text-align: right">
            {{ pud.unitPickUps }}
          </v-flex>
          <v-flex md1 style="text-align: right">
            {{ pud.unitDropOffs }}
          </v-flex>
          <v-flex md1 style="text-align: right">
            {{ pud.isDangerousGoods ? 'YES' : 'NO' }}
          </v-flex>
          <v-flex md1 style="text-align: right">
            {{ pud.forkLiftRequired ? 'YES' : 'NO' }}
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
    <v-dialog
      v-model="dialogController"
      width="70%"
      class="ma-0"
      persistent
      no-click-animation
      content-class="v-dialog-custom"
    >
      <v-flex md12>
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight"
        >
          <span>Edit Job Unit Rate Details</span>
          <div
            class="app-theme__center-content--closebutton"
            @click="dialogController = false"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>
        <v-layout
          row
          wrap
          class="app-theme__center-content--body dialog-content"
        >
          <v-flex md12>
            <v-form>
              <v-layout
                row
                wrap
                class="body-scrollable--80 body-min-height--60"
                pa-3
              >
                <v-flex md12>
                  <v-layout pb-2>
                    <v-flex md2 style="text-align: left" class="px-2"
                      ><h5 class="subheader--bold">Stop</h5></v-flex
                    >
                    <v-flex md12>
                      <v-layout>
                        <v-flex
                          md6
                          style="text-align: center"
                          class="app-borderside--l app-bordercolor--600"
                          ><h5 class="subheader--bold">
                            Selected Zone
                          </h5></v-flex
                        >
                        <v-flex
                          md2
                          style="text-align: center"
                          class="app-borderside--l app-bordercolor--600"
                          ><h5 class="subheader--bold">Units PU/DO</h5></v-flex
                        >
                        <v-flex
                          md2
                          style="text-align: center"
                          class="app-borderside--l app-bordercolor--600"
                          ><h5 class="subheader--bold">Forklift</h5></v-flex
                        >
                        <v-flex
                          md2
                          style="text-align: center"
                          class="app-borderside--l app-bordercolor--600"
                          ><h5 class="subheader--bold">
                            Dangerous Goods
                          </h5></v-flex
                        >
                      </v-layout>
                    </v-flex>
                  </v-layout>
                  <v-form ref="jobUnitRateDetailsForm">
                    <v-layout
                      row
                      wrap
                      v-for="(pud, pudIndex) of currentlyEditingUnitInformation"
                      :key="pud.pudId || pud.uniqueId"
                      style="position: relative"
                    >
                      <v-flex md12>
                        <v-divider class="my-3"></v-divider>
                      </v-flex>
                      <v-flex md2>
                        <v-layout>
                          <span class="subheader"
                            ><span class="accent-text--primary"
                              >{{ pudIndex + 1 }}.</span
                            >
                            {{ pud.suburbName ? pud.suburbName : '-' }}</span
                          >
                        </v-layout>
                        <v-layout class="pud-type">
                          <div :class="pud.legTypeFlag"></div>
                          {{ pud.legTypeFlag === 'P' ? 'Pickup' : 'Dropoff' }}
                        </v-layout>
                      </v-flex>

                      <v-flex md10>
                        <UnitRateBooking
                          :unitRates="unitRates"
                          :soloInput="true"
                          :detailedView="true"
                          :isPickup="pud.legTypeFlag === 'P'"
                          :selectedZone.sync="pud.zoneReference"
                          :selectedUnitTypeId.sync="pud.unitTypeId"
                          :isPudItem="true"
                          :pudWeight.sync="pud.weight"
                          :unitAmountPickUp.sync="pud.unitPickUps"
                          :unitAmountDropOff.sync="pud.unitDropOffs"
                          :isDangerousGoods.sync="pud.isDangerousGoods"
                          :forkliftRequired.sync="pud.forkLiftRequired"
                          :hasDynamicRateType="false"
                          :formDisabled="false"
                          :horizontalView="true"
                        />
                      </v-flex>
                    </v-layout>
                  </v-form>
                </v-flex>
              </v-layout>
            </v-form>
          </v-flex>
          <v-flex md12>
            <v-divider></v-divider>
            <v-layout align-center>
              <v-btn flat color="red" @click="dialogController = false"
                >Cancel</v-btn
              >
              <v-spacer></v-spacer>
              <v-btn
                outline
                class="v-btn-confirm-custom"
                color="white"
                @click="clearLocalChanges"
                >Clear</v-btn
              >
              <v-btn
                depressed
                color="success"
                @click="updateUnitRateInformation"
                class="v-btn-confirm-custom"
                >Save</v-btn
              >
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-dialog>
  </section>
</template>

<script setup lang="ts">
import UnitRateBooking from '@/components/operations/BookJob/service_rates_booking/unit_rate_booking/index.vue';
import { UnitRate } from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRate';
import { UnitRatePudSummary } from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRatePudSummary';
import { computed, ref, Ref, WritableComputedRef } from 'vue';

const props = withDefaults(
  defineProps<{
    unitRates?: UnitRate[];
    unitRateInformation?: UnitRatePudSummary[];
  }>(),
  {
    unitRates: () => [],
    unitRateInformation: () => [],
  },
);

const emit = defineEmits<{
  (event: 'updateUnitRateInformation', payload: UnitRatePudSummary[]): void;
}>();

const jobUnitRateDetailsForm: Ref<any> = ref(null);
const currentlyEditingUnitInformation: Ref<UnitRatePudSummary[] | null> =
  ref(null);
const isViewingDialog: Ref<boolean> = ref(false);

// Controls dialog visibility, and resets local working variables on close
const dialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return isViewingDialog.value;
  },
  set(value: boolean): void {
    if (value) {
      // Copy current unitRateInformation to local copy for editing in dialog view
      currentlyEditingUnitInformation.value = JSON.parse(
        JSON.stringify(props.unitRateInformation),
      );
    } else {
      // Reset local copy
      currentlyEditingUnitInformation.value = null;
    }
    isViewingDialog.value = value;
  },
});

function returnUnitRatesNameForZoneId(zoneId: number | null): string {
  if (!props.unitRates) {
    return '-';
  }
  const foundRate = props.unitRates.find((x: UnitRate) => x.zoneId === zoneId);
  return foundRate ? foundRate.unitTypeName : '';
}

// Copy current unitRateInformation to local copy for editing in dialog view
function clearLocalChanges() {
  currentlyEditingUnitInformation.value = JSON.parse(
    JSON.stringify(props.unitRateInformation),
  );
}

// Emit current currentlyEditingUnitInformation, which will replace the
// unitRateInformation prop
function updateUnitRateInformation() {
  const result = jobUnitRateDetailsForm.value.validate();
  if (!result) {
    return;
  }
  if (currentlyEditingUnitInformation.value) {
    emit('updateUnitRateInformation', currentlyEditingUnitInformation.value);
  }
  dialogController.value = false;
}

// Return the zoneName associated with the zoneId
function returnUnitRateZoneNameFromId(zoneId: number | null) {
  if (zoneId === null) {
    return '';
  }
  const foundZone = props.unitRates.find((z) => z.zoneId === zoneId);
  return foundZone ? foundZone.zoneName : '';
}
</script>

<style scoped lang="scss">
.job-unit-rate-details {
  position: relative;
  .subheader {
    color: $bg-light;
    font-size: $font-size-18;
    font-weight: 600;
  }
  .expansion-job-item {
    background-color: var(--background-color-300);
    border-radius: $border-radius-base;
    .edit-btn {
      background-color: $accent !important;
      color: $app-dark-primary-200;
      border-radius: $border-radius-lg;
    }
    .pud-status-list-item {
      padding: 8px;
      font-size: $font-size-14;
      font-weight: 600;
      text-transform: uppercase;

      .pud-status-list-item__header {
        text-transform: uppercase;
        &.header-type {
          font-size: $font-size-14;
          color: $bg-light-blue;
        }
      }
    }
  }
}

.subheader {
  color: var(--text-color);
  font-size: $font-size-16;
  text-align: center;
  font-weight: 600;
  margin-top: 12px;
}

.pud-type {
  color: var(--light-text-color);
  font-size: $font-size-16;
  .P {
    background-color: $pickup;
    padding-right: 6px;
    padding-top: 20px;
    margin-right: 6px;
    border-radius: 10px;
  }
  .D {
    background-color: $drop;
    padding-right: 6px;
    padding-top: 20px;
    margin-right: 6px;
    border-radius: 10px;
  }
}
</style>
