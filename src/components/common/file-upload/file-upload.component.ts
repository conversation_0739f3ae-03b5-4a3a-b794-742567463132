import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import {
  downloadAttachment,
  getIconByMimeType,
  validMimeTypes,
} from '@/helpers/AttachmentHelpers/AttachmentHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { Attachment } from '@/interface-models/Generic/Attachment/Attachment';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { useAttachmentStore } from '@/store/modules/AttachmentStore';
import moment from 'moment-timezone';
import { v4 as uuidv4 } from 'uuid';
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({ components: { ConfirmationDialog } })
export default class FileUpload extends Vue {
  @Prop() public documentTypeId: number;
  @Prop() public attachmentArray: Attachment[];
  @Prop() public attachment: Attachment;
  @Prop() public attachmentSingle: boolean;
  @Prop() public imageLabel: string;
  @Prop() public formDisabled: boolean;
  @Prop({ default: false }) public isPDF: boolean;
  @Prop({ default: false }) public hideIcon: boolean;
  @Prop({ default: false }) public isLoading: boolean;
  // isAccountRecovery will emit the attachment back to parent instead of sending a save request.
  @Prop({ default: false }) public isAccountRecovery: boolean;

  public getIconByMimeType = getIconByMimeType;
  public awaitingImageSaveFileName: string | null = null;
  public awaitingImageDownload: boolean = false;
  public imageSrc: string | null = null;

  public $refs!: {
    fileInput: HTMLInputElement;
  };

  get showLoadingIndicator(): boolean {
    return (
      this.isLoading &&
      this.awaitingImageSaveFileName !== null &&
      this.awaitingImageSaveFileName !== ''
    );
  }

  get attachmentList(): Attachment[] {
    if (this.attachmentSingle) {
      if (this.attachment && (this.attachment.id || this.isAccountRecovery)) {
        return [this.attachment];
      } else {
        return [];
      }
    } else {
      return this.attachmentArray.filter(
        (x: Attachment) => x.documentTypeId === this.documentTypeId,
      );
    }
  }

  public async getFull(documentId: string, download: boolean) {
    // Early return if the function is called during account recovery or without
    // a document ID or if the user is already waiting for the download response
    if (this.isAccountRecovery || !documentId || this.awaitingImageDownload) {
      if (!documentId) {
        showNotification(GENERIC_ERROR_MESSAGE, { title: 'File Upload' });
      }
      return;
    }

    this.awaitingImageDownload = true;
    try {
      const fullAttachment =
        await useAttachmentStore().getAttachmentById(documentId);

      // If the attachment is found, attempt to download it
      if (fullAttachment) {
        if (download) {
          const downloadSuccess = downloadAttachment(fullAttachment);
          // If the download fails, show an error notification to the user
          if (!downloadSuccess) {
            showNotification(GENERIC_ERROR_MESSAGE, { title: 'File Download' });
          }
        }
        return fullAttachment.data;
      } else if (!fullAttachment) {
        showNotification(
          'Something went wrong. The attachment could not be found.',
          { title: 'File Upload' },
        );
      }
    } finally {
      this.awaitingImageDownload = false;
    }
  }

  // Helper function to get the full image URL
  public async loadImageSrc(attachment: Attachment) {
    try {
      if (
        attachment &&
        attachment.mimeType &&
        attachment.mimeType.includes('image') &&
        !attachment.mimeType.includes('heic') &&
        !attachment.mimeType.includes('heif')
      ) {
        const imageUrl = await this.displayAttachmentImage(attachment.id);
        this.imageSrc = imageUrl ? imageUrl : null;
      } else {
        this.imageSrc = null;
      }
    } catch (error) {
      console.error('Error loading image source: ', error);
      this.imageSrc = null;
    }
  }
  public async displayAttachmentImage(documentId: string) {
    try {
      const fullAttachment = await this.getFull(documentId, false);
      if (fullAttachment) {
        return fullAttachment;
      }
      return null;
    } catch (error) {
      console.error('Error fetching attachment: ', error);
      return null;
    }
  }

  public deleteAttachment(documentId: string): void {
    if (this.$refs.fileInput) {
      (this.$refs.fileInput as any).value = '';
    }

    if (!this.attachmentSingle) {
      const index = this.attachmentArray.findIndex(
        (item: any) => item.id === documentId,
      );
      this.attachmentArray.splice(index, 1);
    }
    if (this.attachmentSingle) {
      this.attachment.id = '';
      this.attachment.documentTypeId = this.documentTypeId;
      this.attachment.mimeType = '';
      this.attachment.data = '';
      this.attachment.name = '';
      this.attachment.timestamp = moment().valueOf();
      this.attachment.approvalStatus = [];
    }
  }

  public pickFile() {
    const fileInput: HTMLElement | null = document.getElementById(
      'fileInput' + this.documentTypeId,
    );
    if (fileInput) {
      fileInput.click();
    }
  }

  public getBase64(file: any): Promise<any> | null {
    try {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          resolve(reader.result);
        };
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  public saveAttachments() {
    try {
      this.$emit('update:isLoading', true);
      const fileUpload: HTMLInputElement = this.$refs.fileInput;
      if (this.attachmentSingle && this.attachment === null) {
        if (this.attachment === null) {
          this.$emit('resetAttachmentData');
        }
      }
      if (!fileUpload.files) {
        this.resetComponent(true);
        return;
      }

      const attachment: Attachment = new Attachment();
      const file: File = fileUpload.files[0];

      // If mime type is not supported, show error notification to user
      if (!validMimeTypes.includes(file.type)) {
        showNotification('File format not supported.', {
          type: HealthLevel.ERROR,
        });
        this.$emit('update:isLoading', false);
        return;
      }
      attachment.documentTypeId = this.documentTypeId;
      const promise = this.getBase64(file);
      if (!promise) {
        this.resetComponent(true);
        return;
      }
      promise
        .then((result) => {
          const base64: any = result;
          const guid = uuidv4();
          const fileName: string = guid + '-' + file.name;
          attachment.data = base64;
          attachment.mimeType = file.type;
          attachment.name = fileName;
          this.awaitingImageSaveFileName = guid.toLowerCase();
          if (this.isAccountRecovery) {
            this.$emit('setAttachment', attachment);
          } else {
            this.saveAttachment(attachment);
          }
        })
        .finally(() => {
          // clear the image that was selected. If we don't do this the @input event on the file input element will fail to do anything if a user uploads the same image.
          if (this.$refs.fileInput) {
            this.$refs.fileInput.value = '';
          }
        });
    } catch (error) {
      console.error(error);
      this.resetComponent(true);
    }
  }

  /**
   * Dispatches the save operation for the new Attachment document, and handles
   * the response
   * @param attachment Attachment object to be saved
   */
  public async saveAttachment(attachment: Attachment) {
    const savedAttachment =
      await useAttachmentStore().saveAttachment(attachment);
    this.setSavedAttachment(savedAttachment);
  }

  /**
   * Handles response to a newly saved Attachment document
   * @param attachment the response to the save operation
   */
  public setSavedAttachment(attachment: Attachment | null) {
    if (!this.awaitingImageSaveFileName) {
      return;
    }
    if (
      !attachment ||
      !attachment.name ||
      !attachment.name.toLowerCase().includes(this.awaitingImageSaveFileName)
    ) {
      showNotification(GENERIC_ERROR_MESSAGE, { title: 'File Upload' });
      this.$emit('update:isLoading', false);
      this.awaitingImageSaveFileName = null;
      return;
    }
    if (this.attachmentSingle) {
      this.attachment.id = attachment.id;
      this.attachment.documentTypeId = attachment.documentTypeId;
      this.attachment.mimeType = attachment.mimeType;
      this.attachment.data = attachment.data;
      this.attachment.name = attachment.name;
      this.attachment.timestamp = attachment.timestamp;
    } else {
      this.attachmentArray.push(attachment);
    }
    this.$emit('update:isLoading', false);
    this.awaitingImageSaveFileName = null;
  }

  public resetComponent(error: boolean) {
    if (error) {
      showNotification(GENERIC_ERROR_MESSAGE, { title: 'File Upload' });
    }
    this.awaitingImageSaveFileName = null;
    this.$emit('update:isLoading', false);
  }

  public downloadAttachment(attachment: Attachment) {
    this.awaitingImageDownload = false;
  }

  mounted() {
    // Load the image source when the component is mounted
    if (this.attachment) {
      this.loadImageSrc(this.attachment);
    }
  }
}
