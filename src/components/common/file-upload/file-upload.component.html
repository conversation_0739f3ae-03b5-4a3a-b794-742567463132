<v-layout column id="fileupload">
  <v-progress-linear
    :active="showLoadingIndicator || awaitingImageDownload"
    :indeterminate="true"
    class="image-loading-bar ma-0"
  ></v-progress-linear>
  <v-flex class="pa-3 app-bgcolor--400 app-borderside--a app-bordercolor--600">
    <div>
      <div
        @click="pickFile"
        v-if="attachmentList.length === 0 && attachmentSingle"
        class="upload-container"
        :class="{fileUploadDisabled: formDisabled}"
      ></div>
      <span class="image-label" style="text-align: center">
        {{ imageLabel }}
      </span>
    </div>
    <v-text-field
      v-if="attachmentList.length > 0 && !attachmentSingle && !formDisabled"
      label="Select File"
      :disabled="formDisabled"
      color="orange"
      @click="pickFile"
      prepend-icon="attach_file"
    >
    </v-text-field>
    <v-layout
      v-if="attachmentList.length === 0 && attachmentSingle || !attachmentSingle"
    >
      <input
        type="file"
        ref="fileInput"
        :id="'fileInput' + documentTypeId"
        style="display: none"
        :disabled="formDisabled"
        @input="saveAttachments()"
      />
    </v-layout>
  </v-flex>
  <v-flex
    class="pa-3 app-bgcolor--400 app-borderside--a app-bordercolor--600 mt-1"
  >
    <v-layout wrap>
      <v-flex md12 v-if="attachmentList.length === 0">
        <v-img
          :src="''"
          :aspect-ratio="2/1"
          contain
          @click="pickFile"
          :style="formDisabled ? '' : 'cursor:pointer'"
        >
          <template v-slot:placeholder>
            <v-layout
              fill-height
              align-center
              justify-center
              ma-0
              class="app-bgcolor--300"
              :class="formDisabled ? 'grey--text' : 'white--text'"
            >
              <v-icon
                size="30"
                :color="formDisabled ? 'grey' : 'white'"
                class="pr-2"
                color="accent"
                >attach_file</v-icon
              >
              <p class="subheading pa-0 ma-0" style="line-height: 1">
                Select File
              </p>
            </v-layout>
          </template>
        </v-img>
      </v-flex>
      <v-flex
        md12
        v-for="(attachment, index) in attachmentList"
        :key="index"
        :class="index === attachmentList.length - 1 ? '' : 'mb-3'"
      >
        <v-img
          @click="getFull(attachment.id, true)"
          :src="imageSrc || attachment.data"
          :aspect-ratio="2/1"
          contain
          style="cursor: pointer"
        >
          <template v-slot:placeholder>
            <v-layout justify-center align-center fill-height>
              <v-icon @click="getFull(attachment.id)" size="50"
                >{{ getIconByMimeType(attachment.mimeType) }}
              </v-icon>
            </v-layout>
          </template>
        </v-img>
        <v-layout
          v-if="attachment.id || isAccountRecovery"
          :justify-space-between="!isAccountRecovery"
          :justify-end="isAccountRecovery"
          align-center
          class="action-btn"
        >
          <v-btn
            solo
            small
            class="image-action-btn"
            v-if="attachment.id"
            :color="awaitingImageDownload ? 'grey' : 'info'"
            @click="getFull(attachment.id, true)"
            :disabled="isAccountRecovery"
          >
            <v-icon class="ma-2" size="20">download</v-icon>
            Download
          </v-btn>

          <v-btn
            solo
            small
            class="image-action-btn"
            v-if="isAccountRecovery"
            color="error"
            @click="$emit('setAttachment', null)"
            :disabled="formDisabled"
          >
            <v-icon class="ma-2" size="20">delete</v-icon>
            remove
          </v-btn>

          <ConfirmationDialog
            v-if="!isAccountRecovery"
            :faIconName="'fas fa-trash-alt'"
            :isIconOnly="true"
            message="Please confirm that you wish to remove this attachment. Saving of the main document may still be required after removal."
            title="Removal Confirmation"
            @confirm="deleteAttachment(attachment.id)"
            :cancelButtonText="'cancel'"
            :isSmallButton="false"
            :buttonDisabled="formDisabled"
            :flat="true"
            :buttonColor="'error'"
            :confirmationButtonText="'Confirm and remove'"
            :dialogIsActive="true"
            :confirmationButtonColor="'error'"
            :cancelButtonColor="'blue'"
          ></ConfirmationDialog>
        </v-layout>
      </v-flex>
    </v-layout>
  </v-flex>
</v-layout>
