// for testing purposes only

const list = [
  {
    sid: 0,
    enumValue: 'ON_HOLD',
    text: 'On Hold',
    master: true,
    category: [0, 1],
    group: [7, 8],
    roles: []
  },
  {
    sid: 1,
    enumValue: 'INACTIVE',
    text: 'Inactive',
    master: true,
    category: [0, 1, 2, 3, 4],
    group: [5, 13, 17],
    roles: []
  },
  {
    sid: 2,
    enumValue: 'REJECTED',
    text: 'Rejected',
    master: true,
    category: [0, 1, 2, 3],
    group: [],
    roles: []
  },
  {
    sid: 3,
    enumValue: 'PENDING',
    text: 'Pending',
    master: true,
    category: [0, 1, 2, 3],
    group: [9, 10, 15, 16, 18, 23, 24],
    roles: []
  },
  {
    sid: 4,
    enumValue: 'ACTIVE',
    text: 'Active',
    master: true,
    category: [0, 1, 2, 3, 4],
    group: [],
    roles: []
  },
  {
    sid: 5,
    enumValue: 'BANNED',
    text: 'Banned',
    master: true,
    category: [],
    group: [],
    roles: []
  },
  {
    sid: 6,
    enumValue: 'CREDIT_LIMIT_EXCEEDED',
    text: 'Credit Limit Exceeded',
    master: false,
    category: [],
    group: [],
    roles: []
  },
  {
    sid: 7,
    enumValue: 'ACCOUNT_OVERDUE',
    text: 'Account Overdue',
    master: false,
    category: [],
    group: [],
    roles: []
  },
  {
    sid: 8,
    enumValue: 'MISSING_PAPERWORK',
    text: 'Missing Paperwork',
    master: false,
    category: [],
    group: [],
    roles: []
  },
  {
    sid: 9,
    enumValue: 'INADEQUATE_INSURANCES',
    text: 'Inadequate Insurances',
    master: false,
    category: [],
    group: [],
    roles: []
  },
  {
    sid: 10,
    enumValue: 'FAILED_DRUG_TEST',
    text: 'Failed Drug Test',
    master: false,
    category: [],
    group: [],
    roles: []
  },
  {
    sid: 11,
    enumValue: 'FAILED_ALCOHOL_TEST',
    text: 'Failed Alcohol Test',
    master: false,
    category: [],
    group: [],
    roles: []
  },
  {
    sid: 12,
    enumValue: 'VEHICLE_WRITTEN_OFF',
    text: 'Vehicle Written Off',
    master: false,
    category: [],
    group: [],
    roles: []
  },
  {
    sid: 13,
    enumValue: 'RETIRED',
    text: 'Retired',
    master: false,
    category: [],
    group: [],
    roles: []
  },
  {
    sid: 14,
    enumValue: 'APPROVED',
    text: 'Approved',
    master: false,
    category: [],
    group: [],
    roles: []
  },
  {
    sid: 15,
    enumValue: 'BRANCH_MANAGER_APPROVAL',
    text: 'Branch Manager Approval',
    master: false,
    category: [],
    group: [],
    roles: []
  },
  {
    sid: 16,
    enumValue: 'ACCOUNTS_APPROVAL',
    text: 'Accounts Approval',
    master: false,
    category: [],
    group: [],
    roles: []
  },
  {
    sid: 17,
    enumValue: 'LOST_LICENSE',
    text: 'Lost License',
    master: false,
    category: [],
    group: [],
    roles: []
  },
  {
    sid: 18,
    enumValue: 'CREDIT_CHECKS',
    text: 'Credit Checks',
    master: false,
    category: [],
    group: [],
    roles: []
  },
  {
    sid: 19,
    enumValue: 'BOOKED',
    text: 'Booked',
    master: false,
    category: [],
    group: [],
    roles: []
  },
  {
    sid: 20,
    enumValue: 'QUOTED',
    text: 'Quoted',
    master: false,
    category: [],
    group: [],
    roles: []
  },
  {
    sid: 21,
    enumValue: 'HOLIDAYS',
    text: 'Holidays',
    master: false,
    category: [],
    group: [],
    roles: []
  },
  {
    sid: 22,
    enumValue: 'SIGNED_PAPER_WORK',
    text: 'Signed Paper Work',
    master: false,
    category: [0],
    group: [],
    roles: []
  },
  {
    sid: 23,
    enumValue: 'FORM_COMPLETION',
    text: 'Form Completion',
    master: false,
    category: [0],
    group: [],
    roles: []
  },
  {
    sid: 24,
    enumValue: 'RATES_COMPLETION',
    text: 'RATES COMPLETION',
    master: false,
    category: [0],
    group: [],
    roles: []
  },
  {
    sid: 25,
    enumValue: 'UNALLOCATED',
    text: 'UNALLOCATED',
    master: true,
    category: [5],
    group: [],
    roles: []
  },
  {
    sid: 26,
    enumValue: 'PREALLOCATED',
    text: 'PREALLOCATED',
    master: false,
    category: [5],
    group: [],
    roles: []
  },
  {
    sid: 27,
    enumValue: 'ALLOCATED',
    text: 'ALLOCATED',
    master: true,
    category: [5, 3],
    group: [],
    roles: []
  },
  {
    sid: 28,
    enumValue: 'DEALLOCATED',
    text: 'DEALLOCATED',
    master: true,
    category: [5, 3],
    group: [],
    roles: []
  },
  {
    sid: 29,
    enumValue: 'ASSIGNED',
    text: 'ASSIGNED',
    master: true,
    category: [5, 3],
    group: [],
    roles: []
  },
  {
    sid: 30,
    enumValue: 'ACCEPTED',
    text: 'ACCEPTED',
    master: false,
    category: [5, 3],
    group: [],
    roles: []
  },
  {
    sid: 31,
    enumValue: 'STARTED',
    text: 'STARTED',
    master: false,
    category: [5, 3],
    group: [],
    roles: []
  },
  {
    sid: 32,
    enumValue: 'FINISHED',
    text: 'FINISHED',
    master: true,
    category: [5, 3],
    group: [],
    roles: []
  },
  {
    sid: 33,
    enumValue: 'COMPLETED',
    text: 'COMPLETED',
    master: true,
    category: [5, 3],
    group: [],
    roles: []
  },
  {
    sid: 34,
    enumValue: 'REVIEWED',
    text: 'REVIEWED',
    master: true,
    category: [5],
    group: [],
    roles: []
  },
  {
    sid: 35,
    enumValue: 'FINALISED',
    text: 'FINALISED',
    master: true,
    category: [5],
    group: [],
    roles: []
  },
  {
    sid: 36,
    enumValue: 'CANCELLED',
    text: 'CANCELLED',
    master: true,
    category: [5],
    group: [],
    roles: []
  },
  {
    sid: 37,
    enumValue: 'READY_FOR_INVOICING',
    text: 'READY_FOR_INVOICING',
    master: true,
    category: [5],
    group: [],
    roles: []
  },
  {
    sid: 38,
    enumValue: 'CLIENT_DUMMY_INVOICE',
    text: 'CLIENT_DUMMY_INVOICE',
    master: true,
    category: [5],
    group: [],
    roles: []
  },
  {
    sid: 39,
    enumValue: 'CLIENT_DUMMY_APPROVED',
    text: 'CLIENT_DUMMY_APPROVED',
    master: true,
    category: [5],
    group: [],
    roles: []
  },
  {
    sid: 40,
    enumValue: 'CLIENT_LIVE_INVOICE',
    text: 'CLIENT_LIVE_INVOICE',
    master: true,
    category: [5],
    group: [],
    roles: []
  },
  {
    sid: 41,
    enumValue: 'READY_FOR_DRIVER_PAY',
    text: 'READY_FOR_DRIVER_PAY',
    master: true,
    category: [7],
    group: [],
    roles: []
  },
  {
    sid: 42,
    enumValue: 'DRIVER_DUMMY_PAY',
    text: 'DRIVER_DUMMY_PAY',
    master: true,
    category: [7],
    group: [],
    roles: []
  },
  {
    sid: 43,
    enumValue: 'DRIVER_DUMMY_APPROVED',
    text: 'DRIVER_DUMMY_APPROVED',
    master: true,
    category: [7],
    group: [],
    roles: []
  },
  {
    sid: 44,
    enumValue: 'DRIVER_LIVE_PAY',
    text: 'DRIVER_LIVE_PAY',
    master: true,
    category: [7],
    group: [],
    roles: []
  },
  {
    sid: 45,
    enumValue: 'COMPLETED_ACTION_REQUIRED',
    text: 'COMPLETED_ACTION_REQUIRED',
    master: true,
    category: [5, 3],
    group: [],
    roles: []
  },
  {
    sid: 46,
    enumValue: 'DRIVER_BREAK',
    text: 'DRIVER_BREAK',
    master: true,
    category: [6],
    group: [],
    roles: []
  },
  {
    sid: 47,
    enumValue: 'DO_NOT_USE',
    text: 'Do Not Use',
    master: true,
    category: [8],
    group: [8, 9, 10, 11, 12, 21],
    roles: []
  },
  {
    sid: 48,
    enumValue: 'DEACTIVATED',
    text: 'Deactivated',
    master: true,
    category: [9],
    group: [5, 13, 17],
    roles: []
  }
];
