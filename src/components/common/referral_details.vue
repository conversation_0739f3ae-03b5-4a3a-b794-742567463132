<template>
  <v-layout wrap>
    <v-flex md12>
      <v-layout>
        <v-flex md4>
          <v-layout align-center class="form-field-label-container">
            <h6 class="subheader--faded pr-3 pb-0">
              How did the Client hear about us?
            </h6>
          </v-layout>
        </v-flex>
        <v-flex md8>
          <v-select
            :items="referralSources"
            v-model="referralDetails.selectedId"
            class="v-solo-custom"
            solo
            flat
            color="light-blue"
            item-text="longName"
            label="How did the Client hear about us?"
            item-value="_id"
            :disabled="!isEdited"
          >
          </v-select>
        </v-flex>
      </v-layout>
    </v-flex>
    <v-flex md12 v-if="otherReferralSelected">
      <v-layout>
        <v-flex md4>
          <v-layout align-center class="form-field-label-container">
            <h6 class="subheader--faded pr-3 pb-0">Additional Comments:</h6>
          </v-layout>
        </v-flex>
        <v-flex md8>
          <v-text-field
            class="v-solo-custom"
            solo
            flat
            color="light-blue"
            label="Additional Comments"
            v-model.trim="referralDetails.additionalInformation"
            :disabled="!isEdited"
          ></v-text-field>
        </v-flex>
      </v-layout>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import ReferralSource from '@/interface-models/Generic/ReferralSource/ReferalSource';
import { referralSources } from '@/interface-models/Generic/ReferralSource/ReferalSourceTypes';

const props = withDefaults(
  defineProps<{
    referralDetails?: ReferralSource;
    isEdited?: boolean;
  }>(),
  {
    isEdited: false,
    referralDetails: undefined,
  },
);

const otherReferralSelected = computed(() => {
  if (props.referralDetails) {
    const selectedReferralId = props.referralDetails.selectedId;
    const other = referralSources.find((item) => item.longName === 'Other');
    if (other !== undefined && selectedReferralId === other._id) {
      return true;
    } else {
      return false;
    }
  }
  return false;
});
</script>
