<div class="asset-information-container" v-if="assetDetails">
  <v-layout pb-2 v-if="assetDetails && fleetAssetOwnerDetails !== undefined">
    <v-flex md12>
      <v-layout v-if="isDialog && dialogTabIndex === 'ASSET_DETAILS'">
        <span class="dialog-section__header">Truck Details</span>
      </v-layout>
      <div
        v-if="!isDialog || (isDialog && dialogTabIndex === 'ASSET_DETAILS')"
        class="card-section pa-2"
      >
        <v-layout justify-space-between wrap>
          <v-flex class="pb-2" md12>
            <v-layout justify-space-between align-start pl-1>
              <span class="section-subheading">MAKE</span>
              <span class="section-heading"
                >{{ assetDetails.fleetAssetTypeObject.make }}</span
              >
            </v-layout>
          </v-flex>

          <v-flex class="pb-2" md12>
            <v-layout justify-space-between align-start pl-1>
              <span class="section-subheading">Body Type</span>
              <span class="section-heading">{{ bodyType }}</span>
            </v-layout>
          </v-flex>
          <v-flex md12 class="pb-2">
            <v-layout justify-space-between align-start pl-1>
              <span class="section-subheading">Weight Class</span>
              <span class="section-heading"
                >{{ assetDetails.fleetAssetTypeObject.truckClass }}</span
              >
            </v-layout>
          </v-flex>

          <v-flex md12 class="pb-2">
            <v-layout justify-space-between align-start pl-1>
              <span class="section-subheading"># DRIVERS</span>
              <span class="section-heading"
                >{{assetDetails.associatedDrivers ?
                assetDetails.associatedDrivers.length : 0}}</span
              >
            </v-layout>
          </v-flex>
          <v-flex md12 class="pb-2">
            <v-layout justify-space-between align-start pl-1>
              <span class="section-subheading">Colour</span>
              <span class="section-heading" v-if="isTruck"
                >{{ assetDetails.fleetAssetTypeObject.colour }}</span
              >
              <span class="section-heading" v-else> Unknown</span>
            </v-layout>
          </v-flex>
          <v-flex md12 class="pb-2">
            <v-layout justify-space-between align-start pl-1>
              <span class="section-subheading">GVM</span>
              <span class="section-heading"
                >{{ assetDetails.fleetAssetTypeObject.gvm }}kg</span
              >
            </v-layout>
          </v-flex>
          <v-flex md12 class="pb-2">
            <v-layout justify-space-between align-start pl-1>
              <span class="section-subheading">Tare</span>
              <span class="section-heading"
                >{{ assetDetails.fleetAssetTypeObject.tare }}kg</span
              >
            </v-layout>
          </v-flex>
          <v-flex md12 class="pb-2">
            <v-layout justify-space-between align-start pl-1>
              <span class="section-subheading">Payload</span>
              <span class="section-heading"
                >{{ assetDetails.fleetAssetTypeObject.payload }}kg</span
              >
            </v-layout>
          </v-flex>
          <v-flex md12 class="pb-2">
            <v-layout justify-space-between align-start pl-1>
              <span class="section-subheading">VIN</span>
              <span class="section-heading"
                >{{ assetDetails.fleetAssetTypeObject.vinNumber ?
                assetDetails.fleetAssetTypeObject.vinNumber : '-' }}</span
              >
            </v-layout>
          </v-flex>
          <v-flex md12 class="pb-2">
            <v-layout justify-space-between align-start pl-1>
              <span class="section-subheading">Axles</span>
              <span class="section-heading"
                >{{ assetDetails.fleetAssetTypeObject.axels ?
                assetDetails.fleetAssetTypeObject.axels : '-' }}</span
              >
            </v-layout>
          </v-flex>
          <v-flex md12 class="pb-2">
            <v-layout justify-space-between align-start pl-1>
              <span class="section-subheading">Registration</span>
              <span class="section-heading"
                >{{
                assetDetails.fleetAssetTypeObject.registrationDetails.registrationNumber
                }}</span
              >
            </v-layout>
          </v-flex>
          <v-flex md12 class="pb-2">
            <v-layout justify-space-between align-start pl-1>
              <span class="section-subheading"> State</span>
              <span class="section-heading"
                >{{
                assetDetails.fleetAssetTypeObject.registrationDetails.stateOfRegistration
                }}</span
              >
            </v-layout>
          </v-flex>
          <v-flex md12 class="pb-2">
            <v-layout justify-space-between align-start pl-1>
              <span class="section-subheading">Registration Expiry</span>
              <span class="section-heading"
                >{{
                returnFormattedDate(assetDetails.fleetAssetTypeObject.registrationDetails.expiry)
                }}</span
              >
            </v-layout>
          </v-flex>
          <v-flex md12 class="pb-2">
            <v-layout justify-space-between align-start pl-1>
              <span class="section-subheading">Body length</span>
              <span class="section-heading">{{ bodyLength }}m</span>
            </v-layout>
          </v-flex>
          <v-flex md12 class="pb-2">
            <v-layout justify-space-between align-start pl-1>
              <span class="section-subheading">Body Height</span>
              <span class="section-heading">{{ bodyHeight }}m</span>
            </v-layout>
          </v-flex>
          <v-flex md12 class="pb-2">
            <v-layout justify-space-between align-start pl-1>
              <span class="section-subheading">Body Width</span>
              <span class="section-heading">{{ bodyWidth }}m</span>
            </v-layout>
          </v-flex>
          <v-flex
            md12
            v-if="assetDetails.locationAddress !== null"
            class="pb-2"
          >
            <v-layout justify-space-between align-start pl-1>
              <span class="section-subheading">Owner</span>
              <span class="section-heading"
                >{{ fleetAssetOwnerDetails.name}}</span
              >
            </v-layout>
          </v-flex>
          <v-flex
            md12
            v-if="assetDetails.locationAddress !== null"
            class="pb-2"
          >
            <v-layout justify-space-between align-start pl-1>
              <span class="section-subheading">Home Location</span>
              <span class="section-heading"
                >{{ assetDetails.locationAddress.formattedAddress }}</span
              >
            </v-layout>
          </v-flex>
        </v-layout>
      </div>
      <div
        v-if="!isDialog || (isDialog && dialogTabIndex === 'ASSET_DETAILS')"
        class="card-section pa-2"
        :class="[isDialog ? 'dialog-text' : '', isFleetAssetList ? 'mt-2' : '']"
      >
        <v-flex md12>
          <v-layout wrap align-start pl-1>
            <v-flex md12>
              <span class="section-subheading">Equipment</span>
            </v-flex>
            <v-flex md12 v-if="availableGenericEquipment.length > 0">
              <span
                class="section-heading"
                v-for="(equipmentItem, index) of availableGenericEquipment"
                >{{equipmentItem}}<span
                  v-if="index !== availableGenericEquipment.length - 1"
                  >,
                </span></span
              >
            </v-flex>
            <v-flex md12 v-else>
              <span class="section-heading">
                This asset has no known equipment.</span
              >
            </v-flex>
            <v-flex md6 v-if="craneInformation !== null" class="mt-2">
              <v-layout column align-start>
                <span class="section-subheading">Crane</span>
                <span class="section-heading">{{craneMakeAndModel}} </span>
              </v-layout>
            </v-flex>
            <v-flex md6 v-if="tailGateInformation !== null" class="mt-2">
              <v-layout justify-space-between align-start>
                <span class="section-subheading">Tail Gate</span>
                <span class="section-heading">{{tailGateDimensions}} </span>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
      </div>
      <v-expansion-panel
        expand
        id="hire-contract-expansion-panel"
        v-model="hireContractPanel"
        v-if="hireContract && (!isDialog || (isDialog && dialogTabIndex === 'ASSET_DETAILS'))"
        color="#24232a"
        class="expansion-panel-container"
        :class=" isFleetAssetList ? 'mt-2' : ''"
      >
        <v-expansion-panel-content class="expansion-item-container">
          <template v-slot:header>
            <span class="section-subheading">Hire Contract Information</span>
          </template>
          <v-card color="#24232a">
            <HireContractMaintenance
              :isAssetInformation="true"
              :fleetAssetId="assetDetails.fleetAssetId"
              :contract="hireContract"
            />
          </v-card>
        </v-expansion-panel-content>
      </v-expansion-panel>
    </v-flex>
  </v-layout>
  <v-layout v-if="isFleetAssetList">
    <v-flex md12>
      <AssetAllocationDetails
        v-if="allocatedJobDetails !== null"
        :isDialog="isDialog"
        :dialogTabIndex="dialogTabIndex"
        :allocatedJobDetails="allocatedJobDetails"
      >
      </AssetAllocationDetails>
      <EquipmentHireAllocationDetails
        v-if="isEquipmentHire"
        :assetId="assetDetails.fleetAssetId"
      />
    </v-flex>
  </v-layout>
</div>
