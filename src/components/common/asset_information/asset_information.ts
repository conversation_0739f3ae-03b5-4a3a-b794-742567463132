import HireContractMaintenance from '@/components/admin/FleetAsset/trailer/components/hire_contract.vue';
import EquipmentHireAllocationDetails from '@/components/common/EquipmentHireAllocationDetails/index.vue';
import AssetAllocationDetails from '@/components/common/asset_allocation_details/index.vue';
import {
  returnFormattedDate,
  returnFormattedTime,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  returnTrailerTypeFromId,
  returnTruckTypeFromId,
} from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import HireContract from '@/interface-models/FleetAsset/EquipmentHire/HireContract';
import FleetAsset from '@/interface-models/FleetAsset/FleetAsset';
import AdditionalEquipmentItem from '@/interface-models/FleetAsset/models/AdditionalEquipmentItem/AdditionalEquipmentItem';
import ChainsDetails from '@/interface-models/FleetAsset/models/AdditionalEquipmentItem/AdditionalEquipmentObjects/ChainsDetails/ChainsDetails';
import CraneDetails from '@/interface-models/FleetAsset/models/AdditionalEquipmentItem/AdditionalEquipmentObjects/CraneDetails';
import GatesDetails from '@/interface-models/FleetAsset/models/AdditionalEquipmentItem/AdditionalEquipmentObjects/GatesDetails';
import GenericEquipmentDetails from '@/interface-models/FleetAsset/models/AdditionalEquipmentItem/AdditionalEquipmentObjects/GenericEquipmentDetails';
import StrapDetails from '@/interface-models/FleetAsset/models/AdditionalEquipmentItem/AdditionalEquipmentObjects/StrapDetails/StrapDetails';
import TailGateDetails from '@/interface-models/FleetAsset/models/AdditionalEquipmentItem/AdditionalEquipmentObjects/TailGateDetails';
import TimbersDetails from '@/interface-models/FleetAsset/models/AdditionalEquipmentItem/AdditionalEquipmentObjects/TimbersDetails';
import TrailerDetails from '@/interface-models/FleetAsset/models/FleetAssetTypes/Trailer/TrailerDetails';
import TruckDetails from '@/interface-models/FleetAsset/models/FleetAssetTypes/Truck/TruckDetails';
import {
  TailGateSizeTypes,
  tailGateSizeTypes,
} from '@/interface-models/FleetAsset/static/AdditionalEquipment/TailGateSizeTypes';
import {
  TailGateTypes,
  tailGateTypes,
} from '@/interface-models/FleetAsset/static/AdditionalEquipment/TailGateTypes';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import {
  EquipmentTypes,
  equipmentTypes,
} from '@/interface-models/Generic/EquipmentTypes/EquipmentTypes';
import { AllocatedJobDetails } from '@/interface-models/Jobs/Allocation/AllocatedJobDetails';
import { useAllocationStore } from '@/store/modules/AllocationStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component({
  components: {
    HireContractMaintenance,
    AssetAllocationDetails,
    EquipmentHireAllocationDetails,
  },
})
export default class AssetInformation extends Vue {
  @Prop() public fleetAssetId: string;
  @Prop() public driverId: string;
  @Prop({ default: false }) public isDialog: boolean;
  @Prop() public dialogTabIndex: number;
  @Prop({ default: false }) public hideViewMoreButton: boolean;
  @Prop({ default: true }) public isFleetAssetList: boolean;

  public fleetAssetStore = useFleetAssetStore();
  public fleetAssetOwnerStore = useFleetAssetOwnerStore();

  public allocatedJobDetails: AllocatedJobDetails | null = null;

  public returnFormattedTime = returnFormattedTime;

  public hireContract: HireContract | null = null;
  public hireContractPanel: boolean[] = [false];

  public assetDetails: FleetAsset | null = null;
  public fleetAssetOwnerDetails: FleetAssetOwnerSummary | null = null;
  public isEquipmentHire: boolean = false;

  public isTruck: boolean = true;
  public bodyLength: string = '';
  public bodyWidth: string = '';
  public bodyHeight: string = '';
  public bodyType: string = '';

  public availableGenericEquipment: string[] = [];

  public craneInformation:
    | CraneDetails
    | TailGateDetails
    | StrapDetails
    | ChainsDetails
    | TimbersDetails
    | GatesDetails
    | GenericEquipmentDetails
    | null = null;

  public craneMakeAndModel: string = '';
  public tailGateInformation: TailGateDetails | null = null;
  public tailGateDimensions: string = '';

  public returnFormattedDate(epoch: number) {
    return returnFormattedDate(epoch, 'DD/MM/YYYY');
  }

  public prepareData() {
    this.hireContract = null;

    const asset = this.fleetAssetStore.getFleetAssetFromFleetAssetId(
      this.fleetAssetId,
    );
    let driverId = this.driverId || '';

    if (asset) {
      const owner = this.fleetAssetOwnerStore.getOwnerFromFleetAssetId(
        this.fleetAssetId,
      );

      if (owner?.affiliation === '2') {
        const singleContract = this.fleetAssetStore.hireContracts.find(
          (x: HireContract) => x.fleetAssetId === this.fleetAssetId,
        );
        this.hireContract = singleContract
          ? JSON.parse(JSON.stringify(singleContract))
          : null;
      }

      if (!driverId && asset.isTruck) {
        driverId = asset.defaultDriver || asset.associatedDrivers[0] || '';
      }
    }

    if (driverId) {
      this.getAllocatedJobDetails(this.fleetAssetId, driverId);
    }
  }
  public saveContract() {
    if (this.hireContract) {
      this.fleetAssetStore.saveHireContract(this.hireContract);
      this.hireContractPanel = [false];
    }
  }

  /**
   * Using the provided parameters, dispatches a request to fetch
   * AllocatedJobDetails documents. On successful response, sets the first
   * result to the local variable
   * @param fleetAssetId fleetAssetId to search for
   * @param driverId driverId to search for
   * @returns
   */
  public async getAllocatedJobDetails(
    fleetAssetId: string,
    driverId: string = '',
  ) {
    if (!fleetAssetId) {
      return;
    }
    const request = {
      fleetAssetId: this.fleetAssetId,
      driverId,
    };
    // Make request and handle response
    const result =
      await useAllocationStore().allocatedJobDetailsByFleetAssetId(request);
    if (
      result?.fleetAssetId === this.fleetAssetId &&
      result.allocatedJobDetails.length > 0
    ) {
      this.allocatedJobDetails = result.allocatedJobDetails[0];
    }
  }

  public updateHireContract(hireContract: HireContract | null) {
    if (
      hireContract &&
      this.hireContract &&
      hireContract._id === this.hireContract._id
    ) {
      this.hireContract = hireContract;
    }

    return;
  }

  public setAvailableEquipment(assetDetails: FleetAsset): string[] {
    const equipmentItems: string[] = [];
    const isGenericEquipmentItem = (id: number) => id !== 1 && id !== 2;

    for (const equipment of assetDetails.additionalEquipments) {
      if (isGenericEquipmentItem(equipment.id)) {
        const foundEquipment = equipmentTypes.find(
          (x: EquipmentTypes) => x.id === equipment.id,
        );
        if (foundEquipment) {
          if (foundEquipment.id === 3) {
            equipmentItems.push(
              foundEquipment.longName +
                ' (' +
                (equipment.equipmentInformation as StrapDetails).straps.length +
                ')',
            );
          } else if (foundEquipment.id === 4) {
            equipmentItems.push(
              foundEquipment.longName +
                ' (' +
                (equipment.equipmentInformation as ChainsDetails).chains
                  .length +
                ')',
            );
          } else {
            equipmentItems.push(foundEquipment.longName);
          }
        }
      }
    }
    return equipmentItems;
  }

  public setLocalVariables() {
    if (this.assetDetails) {
      const owner = this.fleetAssetOwnerStore.getOwnerFromFleetAssetId(
        this.assetDetails.fleetAssetId,
      );
      this.fleetAssetOwnerDetails = owner ? owner : null;
      this.isEquipmentHire = owner ? owner.affiliation === '2' : false;

      const fao = this.assetDetails.fleetAssetTypeObject;
      const isTruck =
        this.assetDetails && this.assetDetails.fleetAssetTypeId === 1;

      this.isTruck = isTruck;

      // Set dimensions
      this.bodyLength = isTruck
        ? (fao as TruckDetails).truckDimensions.length
        : (fao as TrailerDetails).trailerDimensions.length;
      this.bodyWidth = isTruck
        ? (fao as TruckDetails).truckDimensions.width
        : (fao as TrailerDetails).trailerDimensions.width;
      this.bodyHeight = isTruck
        ? (fao as TruckDetails).truckDimensions.height
        : (fao as TrailerDetails).trailerDimensions.height;

      // Set body type
      if (isTruck) {
        this.bodyType = returnTruckTypeFromId((fao as TruckDetails).bodyType);
      } else {
        this.bodyType = returnTrailerTypeFromId(
          (fao as TrailerDetails).trailerType,
        );
      }

      // Set available equipment
      this.availableGenericEquipment = this.setAvailableEquipment(
        this.assetDetails,
      );

      // Set crane
      const foundCrane = this.assetDetails.additionalEquipments.find(
        (x: AdditionalEquipmentItem) => x.id === 1,
      );
      if (foundCrane) {
        this.craneInformation = foundCrane.equipmentInformation as CraneDetails;
        this.craneMakeAndModel = [
          this.craneInformation.make
            ? this.craneInformation.make
            : 'Unknown Make',
          this.craneInformation.model
            ? this.craneInformation.model
            : 'Unknown Model',
        ].join(' - ');
      }

      // Set tailgate information
      const foundTailGate = this.assetDetails.additionalEquipments.find(
        (x: AdditionalEquipmentItem) => x.id === 2,
      );
      this.tailGateInformation = foundTailGate
        ? (foundTailGate.equipmentInformation as TailGateDetails)
        : null;

      if (this.tailGateInformation) {
        const type = tailGateTypes.find(
          (x: TailGateTypes) => x.id === this.tailGateInformation!.type,
        );

        const sizeDetails = tailGateSizeTypes.find(
          (size: TailGateSizeTypes) =>
            size.id === this.tailGateInformation!.size,
        );

        const sizeString = sizeDetails ? sizeDetails.longName : null;

        const weightString =
          this.tailGateInformation.tonne !== ''
            ? this.tailGateInformation.tonne + 'T'
            : null;

        const height =
          this.tailGateInformation.height !== ''
            ? this.tailGateInformation.height + 'm'
            : null;

        this.tailGateDimensions = `${type ? type.longName + ' ' : ''}  ${
          sizeString ? sizeString + ' ' : ''
        } ${height ? height + ' ' : ''} ${
          weightString ? weightString + ' ' : ''
        }`;
      } else {
        this.tailGateDimensions = 'Information not known.';
      }
    }
  }

  // Handles response of full FleetAsset after request after mount
  public setFleetAssetFromResponse(fleetAsset: FleetAsset | null) {
    if (!fleetAsset || !fleetAsset.fleetAssetId) {
      showNotification(
        'Sorry, the details for this Fleet Asset could not be found.',
      );
      return;
    }
    this.assetDetails = fleetAsset;
    this.prepareData();
    this.setLocalVariables();
  }
  // Request the full FleetAsset from the fleetAssetId
  public async requestFleetAssetDetailsByFleetAssetId(fleetAssetId: string) {
    const fleetAsset: FleetAsset | null =
      await this.fleetAssetStore.requestFleetAssetByFleetAssetId(fleetAssetId);
    this.setFleetAssetFromResponse(fleetAsset);
  }

  public mounted() {
    if (!this.fleetAssetId) {
      return;
    }

    this.requestFleetAssetDetailsByFleetAssetId(this.fleetAssetId);
  }
}
