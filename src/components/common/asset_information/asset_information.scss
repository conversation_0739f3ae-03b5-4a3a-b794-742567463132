.card-section {
  background-color: var(--background-color-300);
  border: 1px solid $translucent;
  border-radius: $border-radius-base;
  .section-subheading {
    font-size: 0.85em;
    text-transform: uppercase;
    font-weight: 500;
    color: var(--light-text-color);
    padding-left: 10px;
    text-transform: uppercase;
  }
  .section-heading {
    font-size: $font-size-medium;
    color: var(--text-color);
    padding-right: 10px;
    font-weight: 500;
  }

  &.dialog-text {
    .section-subheading {
      padding: 1px 8px;
      font-size: 1em;
    }
    .section-heading {
      padding: 1px 8px;
      font-size: 1em;
    }
  }
}

.dialog-section__header {
  font-size: $font-size-large;
  text-transform: uppercase;
  color: var(--accent-secondary);
  font-weight: 700;
  padding: 8px 12px;
  letter-spacing: 0.8px;
}

.expansion-panel-container {
  border: 1px solid $translucent;
  border-radius: $border-radius-sm;
  .expansion-item-container {
    border-radius: $border-radius-sm !important;
    background-color: var(--background-color-300) !important;
  }
}

.expansion-job-item {
  .expansion-job-item__card-wrapper {
    padding-top: 1px;

    &.dialog-view {
      // padding-top: 6px;
      .expansion-job-item__card {
        padding: 16px;
      }
    }

    &.pud-type {
      padding-top: 1px;

      .expansion-job-item__card {
        // background-color: #1e1d2571;
        padding: 12px;
      }
    }
    .expansion-job-item__card {
      padding: 12px;

      font-size: $font-size-12;
      text-transform: uppercase;

      &:hover {
        cursor: pointer;
      }
      &--jobid {
        font-weight: 500;
        color: var(--warning);
      }
      &--clientname {
        font-weight: 500;
        color: var(--text-color);
        padding-left: 6px;
      }
      &--time {
        color: #9997a7;
        font-weight: 400;
        padding-left: 6px;
      }
      &--status {
        font-size: $font-size-11;
        font-weight: 700;
      }
    }
  }
}
