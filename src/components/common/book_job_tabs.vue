<template>
  <section class="book-job-tabs">
    <v-layout @click="editDetails" class="jobdetails-info__header" row wrap>
      <v-flex
        md12
        lg4
        class="pr-3 pb-1"
        :class="{
          'app-bordercolor--600 app-borderside--r': $vuetify.breakpoint.lgAndUp,
        }"
        :pl-1="$vuetify.breakpoint.lgAndUp"
      >
        <v-layout><span class="column-header">Client Details</span></v-layout>
        <v-layout class="eventtime-card">
          <span class="eventtime-card__header">Name</span>
          <v-spacer></v-spacer>
          <span class="eventtime-card__value">{{
            jobDetails.client.clientName ? jobDetails.client.clientName : '-'
          }}</span>
        </v-layout>
        <v-layout class="eventtime-card" v-if="dispatcherName">
          <span class="eventtime-card__header">Dispatcher</span>
          <v-spacer></v-spacer>
          <span class="eventtime-card__value">{{
            dispatcherName ? dispatcherName : ' - '
          }}</span>
        </v-layout>

        <v-layout
          class="eventtime-card"
          v-if="
            jobDetails.clientDispatcher.contactLandlineNumber ||
            jobDetails.clientDispatcher.contactMobileNumber
          "
        >
          <span class="eventtime-card__header">Contact</span>
          <v-spacer></v-spacer>
          <span class="eventtime-card__value">
            <span v-if="jobDetails.clientDispatcher.contactLandlineNumber">{{
              jobDetails.clientDispatcher.contactLandlineNumber
            }}</span>
            <span
              v-if="
                jobDetails.clientDispatcher.contactLandlineNumber &&
                jobDetails.clientDispatcher.contactMobileNumber
              "
              >/</span
            >
            <span v-if="jobDetails.clientDispatcher.contactMobileNumber">
              {{ jobDetails.clientDispatcher.contactMobileNumber }}</span
            >
          </span>
        </v-layout>
      </v-flex>
      <v-flex
        md12
        lg4
        class="pb-1"
        :class="{
          'app-bordercolor--600 app-borderside--r': $vuetify.breakpoint.lgAndUp,
        }"
        :px-3="$vuetify.breakpoint.lgAndUp"
      >
        <v-layout><span class="column-header">Service Details</span></v-layout>
        <v-layout class="eventtime-card">
          <span class="eventtime-card__header">Rate/Service</span>
          <v-spacer></v-spacer>
          <span class="eventtime-card__value"
            >{{ jobDetails.rateTypeName ? jobDetails.rateTypeName : '-' }} /
            {{
              jobDetails.serviceTypeShortName
                ? jobDetails.serviceTypeShortName
                : '-'
            }}</span
          >
        </v-layout>
        <v-layout class="eventtime-card" v-if="jobDetails.jobReference">
          <v-layout v-if="jobDetails.jobReference.length > 0">
            <span class="eventtime-card__header mr-1">Reference</span>
            <v-spacer></v-spacer>
            <span class="eventtime-card__value">
              <span class="client-name">
                {{ jobDetails.allJobReferences }}
              </span>
            </span>
          </v-layout>
        </v-layout>
        <v-layout
          class="eventtime-card"
          v-if="additionalEquipmentNames.length > 0"
        >
          <span class="eventtime-card__header">Equipment</span>
          <v-spacer></v-spacer>
          <span class="eventtime-card__value pl-5" style="text-align: right"
            ><span
              class="client-name"
              v-for="(item, index) in additionalEquipmentNames"
              :key="index"
            >
              {{ item
              }}<span v-if="index !== additionalEquipmentNames.length - 1"
                >,</span
              ></span
            ></span
          >
        </v-layout>
      </v-flex>
      <v-flex md12 lg4 class="pb-1" :px-3="$vuetify.breakpoint.lgAndUp">
        <v-layout><span class="column-header">Booking Details</span></v-layout>
        <v-layout class="eventtime-card">
          <span class="eventtime-card__header">Time</span>
          <v-spacer></v-spacer>

          <span class="eventtime-card__value pr-1" v-if="!isRecurringJob">{{
            jobStartDate
          }}</span>
          <span
            class="eventtime-card__value light-blue--text text--accent-2"
            style="font-weight: 600; text-transform: uppercase"
            v-if="!isAsap"
            >{{ jobStartTime }}</span
          >
          <span
            v-if="isAsap"
            class="eventtime-card__value amber--text text--accent-3"
            style="font-weight: 600; text-transform: uppercase"
          >
            ASAP
          </span>
        </v-layout>
        <v-layout class="eventtime-card">
          <span class="eventtime-card__header">Driver/Truck</span>
          <v-spacer></v-spacer>
          <span class="eventtime-card__value"> {{ allocationString }} </span>
        </v-layout>
        <v-layout class="eventtime-card" v-if="driverContact">
          <span class="eventtime-card__header">Driver Contact</span>
          <v-spacer></v-spacer>
          <span class="eventtime-card__value"> {{ driverContact }} </span>
        </v-layout>
      </v-flex>
    </v-layout>
  </section>
</template>

<script setup lang="ts">
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { equipmentTypes } from '@/interface-models/Generic/EquipmentTypes/EquipmentTypes';
import { JobDetails } from '@/interface-models/Jobs/JobDetails';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { computed, ComputedRef } from 'vue';

const props = withDefaults(
  defineProps<{
    jobDetails: JobDetails;
    editingCompletedJob?: boolean;
    isRecurringJob?: boolean;
  }>(),
  {
    editingCompletedJob: false,
    isRecurringJob: false,
  },
);

const emit = defineEmits(['editDetails']);

const fleetAssetStore = useFleetAssetStore();
const driverStore = useDriverDetailsStore();

const additionalEquipmentNames: ComputedRef<string[]> = computed(() => {
  const names: string[] = [];
  for (const item of equipmentTypes) {
    for (const id of props.jobDetails.additionalEquipments) {
      if (id === item.id) {
        names.push(item.longName);
      }
    }
  }
  return names;
});

const dispatcherName = computed(() => {
  const firstName: string = props.jobDetails.clientDispatcher.firstName
    ? props.jobDetails.clientDispatcher.firstName
    : '';
  const lastName: string = props.jobDetails.clientDispatcher.lastName
    ? props.jobDetails.clientDispatcher.lastName
    : '';
  const fullName: string = firstName + ' ' + lastName;
  if (fullName) {
    return fullName;
  }
  return '';
});

const jobStartTime: ComputedRef<string> = computed(() => {
  if (props.jobDetails.pudItems.length < 1) {
    return '-';
  }
  if (!props.jobDetails.pudItems[0].epochTime) {
    return '-';
  }
  const date = returnFormattedDate(
    props.jobDetails.pudItems[0].epochTime,
    'HH:mm',
  );
  return date;
});

const jobStartDate: ComputedRef<string> = computed(() => {
  if (props.jobDetails.pudItems.length < 1) {
    return '-';
  }
  if (!props.jobDetails.pudItems[0].epochTime) {
    return '-';
  }
  const date = returnFormattedDate(
    props.jobDetails.pudItems[0].epochTime,
    'DD/MM/YY',
  );
  return date;
});

const isAsap: ComputedRef<boolean> = computed(() => {
  if (props.jobDetails.pudItems.length < 1) {
    return false;
  }
  if (props.jobDetails.pudItems[0].timeDefinition === 9) {
    return true;
  } else {
    return false;
  }
});

const driverName: ComputedRef<string> = computed(() => {
  if (!props.jobDetails.driverId) {
    return '-';
  }
  if (
    props.jobDetails.additionalJobData &&
    props.jobDetails.additionalJobData.driverName
  ) {
    return props.jobDetails.additionalJobData.driverName;
  }
  const foundDriver = driverStore.getDriverFromDriverId(
    props.jobDetails.driverId,
  );
  return foundDriver?.displayName ?? '-';
});

const driverContact: ComputedRef<string> = computed(() => {
  if (!props.jobDetails.driverId) {
    return '';
  }
  const foundDriver = driverStore.getDriverFromDriverId(
    props.jobDetails.driverId,
  );
  if (foundDriver) {
    if (foundDriver.mobile) {
      return foundDriver.mobile ? foundDriver.mobile : '';
    } else {
      return 'Unknown';
    }
  } else {
    return '';
  }
});

const truckDetails: ComputedRef<string> = computed(() => {
  if (!props.jobDetails.fleetAssetId) {
    return '-';
  }
  if (
    props.jobDetails.additionalJobData &&
    props.jobDetails.additionalJobData.csrAssignedId
  ) {
    return props.jobDetails.additionalJobData.csrAssignedId;
  }
  const foundTruck = fleetAssetStore.getFleetAssetFromFleetAssetId(
    props.jobDetails.fleetAssetId,
  );
  if (foundTruck) {
    return foundTruck.csrAssignedId;
  } else {
    return '-';
  }
});

const allocationString: ComputedRef<string> = computed(() => {
  return driverName.value + ' / ' + truckDetails.value;
});

function editDetails(): void {
  emit('editDetails');
}
</script>

<style scoped lang="scss">
.book-job-tabs {
  position: relative;
  width: 100%;
  border-bottom: 2px solid $translucent;
  box-shadow: var(--box-shadow);

  .column-header {
    text-transform: uppercase;
    font-size: $font-size-12;
    font-weight: 600;
    padding-bottom: 2px;
    color: var(--accent-secondary);
  }

  .eventtime-card {
    .eventtime-card__header {
      color: var(--light-text-color);
      text-transform: uppercase;
      font-size: $font-size-11;
      font-weight: 600;
      padding-bottom: 1px;
    }
    .eventtime-card__value {
      color: var(--text-color);
      font-size: $font-size-11;
      padding-bottom: 1px;
      &.highlighted {
        color: white;
        background-color: var(--primary);
        padding: 2px 6px;
        font-size: $font-size-12;
        border-radius: 2px;
        font-weight: 700;
      }
    }
  }

  .jobdetails-info__header {
    // border-radius: $border-radius-Xlg;
    // background-color: var(--background-color-300);
    // border-bottom: 1px solid $app-dark-primary-600;
    padding: 4px 8px 4px 8px;
    margin: 8px 0px;
    min-height: 82px;

    &:hover {
      background-color: var(--background-color-250);
      // border: 1px solid #7c7c7c;
      cursor: pointer;
    }
  }

  .main-header__row {
    position: relative;
    margin: 8px 0px;
    height: 52px;

    .main-header-tab__container:nth-of-type(1) {
      padding-left: 0px !important;
    }

    .main-header-tab__container:last-child {
      padding-right: 0px !important;
    }

    .main-header-tab__container {
      height: 100%;
      padding: 0px 3px;
      .main-header-tab__sheet {
        height: 100%;
        background-color: var(--background-color-300);
        // margin: 0 4px 0 0 !important;
        border: 1px solid #474747;
        border-radius: 3px;
        padding: 4px 12px 4px 8px;
        transition: 0.15s;

        &:hover {
          background-color: #2c2c32;
          border: 1px solid #7c7c7c;
          cursor: pointer;
        }

        .tab-title {
          display: block;
          text-transform: uppercase;
          line-height: 1.4;
          color: #7a7a7a;
          font-size: $font-size-small;
          font-weight: 400;
        }

        .client-name {
          padding-bottom: 4px;
          font-size: 1em;
          font-weight: 400;
          // color: rgb(255, 173, 22);
          color: rgb(255, 255, 255);
          line-height: 1;
          display: block;

          &.left-padding {
            .contact-icon {
              padding-left: 10px;
            }
          }

          .contact-icon {
            color: rgb(102, 102, 102);
            padding-bottom: 3px;
            padding-right: 3px;
          }
        }

        &.accent-type {
          background-color: $app-dark-primary-550;
          border: 1px solid #979797;
          .client-name {
            color: white;
            font-weight: 600;
          }

          .tab-title {
            color: #c2c2c2;
          }
        }
      }
    }
  }

  .start-end {
    font-size: $font-size-small;
    // color: $table-overflow;
    padding-right: 2px;
  }

  .start-end-time {
    // color: rgb(245, 150, 27);
    font-size: 0.85em;
  }

  .divider {
    height: 100%;
    width: 4px;
    border-radius: 2px;
    background-color: #393939;
    margin: 0 4px;
  }

  .reviewJobDivider {
    margin: 0;
  }

  .reviewJobTab {
    margin: 0 2px !important;
  }

  .reviewJobTabLeft {
    margin: 0 2px 0 0 !important;
  }

  .reviewJobTabRight {
    margin: 0 0 0 2px !important;
  }

  .reviewMainHeaderTab {
    width: 160px;
  }

  .noWrap {
    white-space: nowrap;
  }

  .marginLeft {
    margin-left: 4px !important;
  }
}
</style>
