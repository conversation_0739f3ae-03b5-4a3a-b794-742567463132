<template>
  <div>
    <v-layout class="px-1" align-center>
      <v-tooltip bottom v-if="filterStore.isOperationsChannelFilterApplied">
        <template v-slot:activator="{ on }">
          <v-btn flat icon v-on="on" @click="clearFilters" class="ma-0">
            <v-icon size="24" class="clear-icon">clear</v-icon>
          </v-btn>
        </template>
        <span>Clear All Filters</span>
      </v-tooltip>
      <v-tooltip bottom>
        <template v-slot:activator="{ on }">
          <v-btn
            flat
            icon
            v-on="on"
            @click="isViewingDashboardFiltersDialog = true"
            class="ma-0"
          >
            <v-icon size="20">filter_alt</v-icon>
          </v-btn>
        </template>
        <span>Dashboard Filters</span>
      </v-tooltip>
      <v-tooltip bottom>
        <template v-slot:activator="{ on }">
          <v-btn
            flat
            icon
            v-on="on"
            @click="openUnassignedPudDialog"
            class="ma-0"
          >
            <v-icon size="15">fas fa-route</v-icon>
          </v-btn>
        </template>
        <span>Open Point Manager</span>
      </v-tooltip>
      <v-tooltip bottom>
        <template v-slot:activator="{ on }">
          <v-btn flat icon v-on="on" @click="openJobListWindow" class="ma-0">
            <v-icon
              size="15"
              :color="jobListWindowOpen ? 'light-blue' : 'white'"
              >fas fa-list</v-icon
            >
          </v-btn>
        </template>
        <span v-if="!jobListWindowOpen">Open Job List</span>
        <span v-if="jobListWindowOpen">Close Job List Window</span>
      </v-tooltip>
      <v-tooltip bottom>
        <template v-slot:activator="{ on }">
          <v-btn
            flat
            icon
            v-on="on"
            :color="fleetTrackingMapIsOpen ? 'light-blue' : 'white'"
            @click="openMapWindow"
            class="ma-0"
          >
            <v-icon size="15">fas fa-map-marked-alt</v-icon>
          </v-btn>
        </template>
        <span v-if="!fleetTrackingMapIsOpen">Open Map</span>
        <span v-else>Close Map Window</span>
      </v-tooltip>
    </v-layout>

    <OperationDashboardFilters
      v-model="isViewingDashboardFiltersDialog"
      :dialogActive="isViewingDashboardFiltersDialog"
      :isMap="false"
    ></OperationDashboardFilters>
  </div>
</template>

<script setup lang="ts">
import OperationDashboardFilters from '@/components/operations/OperationDashboard/operation_dashboard_filters.vue';
import { BroadcastChannelType } from '@/helpers/NotificationHelpers/BroadcastChannelHelpers';
import { BroadcastMessage } from '@/interface-models/Generic/OperationScreenOptions/BroadcastMessage';
import { useBroadcastChannelStore } from '@/store/modules/BroadcastChannelStore';
import { useFleetMapStore } from '@/store/modules/FleetMapStore';
import { useFilterStore } from '@/store/modules/OperationsFilterStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { computed, ref, Ref } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';

const fleetMapStore = useFleetMapStore();
const operationsStore = useOperationsStore();
const broadcastChannelStore = useBroadcastChannelStore();
const filterStore = useFilterStore();
const router = useRouter();
const route = useRoute();

const isViewingDashboardFiltersDialog: Ref<boolean> = ref(false);

function openMapWindow() {
  if (!fleetTrackingMapIsOpen.value) {
    const routeUrl = router.resolve({
      path: '/fleet-tracking',
    });

    window.open(routeUrl.href, 'Fleet Tracking', 'menubar=no');
    fleetMapStore.createBroadcastChannel();
    fleetMapStore.setFleetTrackingWindow(true);
  } else {
    fleetMapStore.setFleetTrackingWindow(false);

    // Send closed message then close channel
    broadcastChannelStore.postMessageToChannel(
      BroadcastChannelType.FLEET_TRACKING,
      new BroadcastMessage('closeWindow'),
    );
    broadcastChannelStore.postMessageToChannel(
      BroadcastChannelType.FLEET_TRACKING,
      new BroadcastMessage('windowClosed'),
    );
    broadcastChannelStore.closeChannel(BroadcastChannelType.FLEET_TRACKING);
  }
}
// Open the job list in a new window
function openJobListWindow() {
  if (!jobListWindowOpen.value) {
    operationsStore.createBroadcastChannel(route.name);
    const routeUrl = router.resolve({ path: '/job-list' }).href;
    window.open(routeUrl, 'Jobs List', 'menubar=no');
  } else {
    operationsStore.setJobListWindow(false);

    // Send closed message then close channel
    broadcastChannelStore.postMessageToChannel(
      BroadcastChannelType.JOB_LIST,
      new BroadcastMessage('closeWindow'),
    );
    broadcastChannelStore.closeChannel(BroadcastChannelType.JOB_LIST);
  }
}

const jobListWindowOpen = computed(
  () => operationsStore.operationOptions.jobListWindowOpen,
);
const fleetTrackingMapIsOpen = computed(
  () => fleetMapStore.isFleetTrackingWindowOpen,
);

function openUnassignedPudDialog() {
  operationsStore.setViewingPudSearchDialog(true);
}

/**
 * Clears all dashboard filters.
 */
function clearFilters(): void {
  filterStore.setSelectedOperationsChannelIds(null);
}
</script>

<style scoped lang="scss">
.clear-icon {
  color: $warning !important;
}
</style>
