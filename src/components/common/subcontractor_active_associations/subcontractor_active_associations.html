<div class="client-details-key-information">
  <v-layout row wrap v-if="!isAwaitingResponse">
    <v-flex md12 pb-3 v-if="activeDriverForDissociationName">
      <v-layout wrap
        ><v-flex md12
          ><v-layout align-center>
            <h5 class="subheader--bold px-3 pt-1">Active Driver</h5>
            <v-flex>
              <v-divider></v-divider>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex
          md12
          mb-2
          class="px-3 py-2 app-bgcolor--400 app-bordercolor--600 app-borderside--a"
        >
          <v-layout justify-space-between class="table-row-custom">
            <v-flex md4>
              <span class="table-row-custom__header header-type"> Driver </span>
            </v-flex>
            <v-flex md4 class="table-row-cell right-align">
              <span class="table-row-custom__header header-type"> Status </span>
            </v-flex>
          </v-layout>
          <v-layout justify-space-between class="table-row-custom">
            <v-flex md4>
              <span> {{activeDriverForDissociationName}} </span>
            </v-flex>
            <v-flex md4 class="table-row-cell right-align">
              <span> ACTIVE </span>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>

    <v-flex md12 pb-1 v-if="entityType !== 'FLEET_ASSET'">
      <v-layout wrap
        ><v-flex md12
          ><v-layout align-center>
            <h5 class="subheader--bold px-3 pt-1">
              {{entityType === 'DRIVER' ? 'Associated' : 'Active'}} Vehicles
            </h5>
            <v-flex>
              <v-divider></v-divider>
            </v-flex>
            <h5 class="subheader--bold--14 pt-1 px-3">
              {{fleetAssetIds.length}} Vehicles
            </h5>
          </v-layout>
        </v-flex>
        <v-flex
          md12
          mb-2
          class="px-3 py-2 app-bgcolor--400 app-bordercolor--600 app-borderside--a"
          v-if="!!fleetAssetIds.length"
        >
          <v-layout justify-space-between class="table-row-custom">
            <v-flex md4>
              <span class="table-row-custom__header header-type">
                Asset ID
              </span>
            </v-flex>
            <v-flex md4 class="table-row-cell right-align">
              <span class="table-row-custom__header header-type">
                Registration
              </span>
            </v-flex>
            <v-flex
              md4
              class="table-row-cell right-align"
              v-if="entityType === 'DRIVER'"
            >
              <span class="table-row-custom__header header-type">
                Default Driver
              </span>
            </v-flex>
          </v-layout>
          <v-layout
            justify-space-between
            class="table-row-custom"
            v-for="(associatedAsset, index) in associatedFleetAssets"
            :key="associatedAsset.fleetAssetId + '-' + index"
          >
            <v-flex md4>
              <span> {{associatedAsset.csrAssignedId}} </span>
            </v-flex>
            <v-flex md4 class="table-row-cell right-align">
              <span> {{ associatedAsset.rego }} </span>
            </v-flex>
            <v-flex
              md4
              class="table-row-cell right-align"
              v-if="entityType === 'DRIVER'"
            >
              <span> {{ associatedAsset.isDefaultDriver}} </span>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>

    <v-flex pb-1 md12 v-if="entityType !== 'DRIVER'">
      <v-layout wrap>
        <v-flex md12
          ><v-layout align-center>
            <h5 class="subheader--bold px-3 pt-1">Active Drivers</h5>
            <v-flex>
              <v-divider></v-divider>
            </v-flex>
            <h5 class="subheader--bold--14 pt-1 px-3">
              {{driverIds.length}} Drivers
            </h5>
          </v-layout>
        </v-flex>
        <v-flex
          md12
          mb-2
          class="px-3 py-2 app-bgcolor--400 app-bordercolor--600 app-borderside--a"
          v-if="!!driverIds.length"
        >
          <v-layout justify-space-between class="table-row-custom">
            <v-flex md4>
              <span class="table-row-custom__header header-type">
                Driver Id
              </span>
            </v-flex>
            <v-flex md4 class="table-row-cell right-align">
              <span class="table-row-custom__header header-type"> Name </span>
            </v-flex>
          </v-layout>
          <v-layout
            justify-space-between
            class="table-row-custom"
            v-for="(associatedAsset, index) in associatedDrivers"
            :key="associatedAsset.fleetAssetId + '-' + index"
          >
            <v-flex md4>
              <span> {{associatedAsset.driverId}} </span>
            </v-flex>
            <v-flex md4 class="table-row-cell right-align">
              <span> {{ associatedAsset.name }} </span>
            </v-flex>
            <v-flex
              md4
              class="table-row-cell right-align"
              v-if="entityType === 'DRIVER'"
            >
              <span> {{ associatedAsset.isDefaultDriver}} </span>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>

    <v-flex pb-1 v-if="entityType !== 'DRIVER'">
      <v-layout wrap>
        <v-flex md12
          ><v-layout align-center>
            <h5 class="subheader--bold px-3 pt-1">Active Devices</h5>
            <v-flex>
              <v-divider></v-divider>
            </v-flex>
            <h5 class="subheader--bold--14 pt-1 px-3">
              {{deviceIds.length}} Devices
            </h5>
          </v-layout>
        </v-flex>
        <v-flex
          md12
          mb-2
          class="px-3 py-2 app-bgcolor--400 app-bordercolor--600 app-borderside--a"
          v-if="!!deviceIds.length"
        >
          <v-layout justify-space-between class="table-row-custom">
            <v-flex md4>
              <span class="table-row-custom__header header-type">
                Device Id
              </span>
            </v-flex>
            <v-flex
              md4
              class="table-row-cell right-align"
              v-if="entityType === 'FLEET_ASSET_OWNER'"
            >
              <span class="table-row-custom__header header-type">
                Assigned Asset Id
              </span>
            </v-flex>
            <v-flex md4 class="table-row-cell right-align">
              <span class="table-row-custom__header header-type">
                Assigned Date
              </span>
            </v-flex>
          </v-layout>
          <v-layout
            justify-space-between
            class="table-row-custom"
            v-for="(associatedDevice, index) in associatedDevices"
            :key="associatedDevice.deviceId + '-' + index"
          >
            <v-flex md4>
              <span> {{associatedDevice.deviceId}} </span>
            </v-flex>

            <v-flex
              md4
              class="table-row-cell right-align"
              v-if="entityType === 'FLEET_ASSET_OWNER'"
            >
              <span> {{ associatedDevice.assignedAssetCsrAssignedId }} </span>
            </v-flex>

            <v-flex md4 class="table-row-cell right-align">
              <span> {{ associatedDevice.assignedDate }} </span>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>

    <v-flex md12 pb-1
      ><v-layout align-center>
        <h5 class="subheader--bold px-3 pt-1">Current Work</h5>
        <v-flex>
          <v-divider></v-divider>
        </v-flex>
        <h5 class="subheader--bold--14 pt-1 px-3">
          {{activeWorkJobDetailsList.length}} Jobs
        </h5>
      </v-layout>
    </v-flex>
    <v-flex
      md12
      mb-2
      class="px-3 py-2 app-bgcolor--400 app-bordercolor--600 app-borderside--a"
      v-if="!!activeWorkJobDetailsList.length"
    >
      <v-layout justify-space-between class="table-row-custom">
        <v-flex md4>
          <span class="table-row-custom__header header-type"> Date </span>
        </v-flex>
        <v-flex md4 class="table-row-cell right-align">
          <span class="table-row-custom__header header-type"> Job ID </span>
        </v-flex>
        <v-flex md4 class="table-row-cell right-align">
          <span class="table-row-custom__header header-type"> Progress </span>
        </v-flex>
      </v-layout>
      <v-layout
        justify-space-between
        class="table-row-custom"
        v-for="job in activeWorkJobDetailsList"
        :key="job.jobId"
      >
        <v-flex md4>
          <span> {{job.readableJobDate}} </span>
        </v-flex>
        <v-flex md4 class="table-row-cell right-align">
          <span> {{job.recurringJobId ? job.recurringJobId : job.jobId}} </span>
        </v-flex>
        <v-flex md4 class="table-row-cell right-align">
          <span> {{job.status}} </span>
        </v-flex>
      </v-layout>
    </v-flex>

    <v-flex md12 pb-1
      ><v-layout align-center>
        <h5 class="subheader--bold px-3 pt-1">Pending Work</h5>
        <v-flex>
          <v-divider></v-divider>
        </v-flex>
        <h5 class="subheader--bold--14 pt-1 px-3">
          {{pendingWorkJobDetailsList.length}} Jobs
        </h5>
      </v-layout>
    </v-flex>
    <v-flex
      md12
      mb-2
      class="px-3 py-2 app-bgcolor--400 app-bordercolor--600 app-borderside--a"
      v-if="!!pendingWorkJobDetailsList.length"
    >
      <v-layout justify-space-between class="table-row-custom">
        <v-flex md4>
          <span class="table-row-custom__header header-type"> Date </span>
        </v-flex>
        <v-flex md4 class="table-row-cell right-align">
          <span class="table-row-custom__header header-type"> Job ID </span>
        </v-flex>
        <v-flex md4 class="table-row-cell right-align">
          <span class="table-row-custom__header header-type"> Progress </span>
        </v-flex>
      </v-layout>
      <v-layout
        justify-space-between
        class="table-row-custom"
        v-for="job in pendingWorkJobDetailsList"
        :key="job.jobId"
      >
        <v-flex md4>
          <span> {{job.readableJobDate}} </span>
        </v-flex>
        <v-flex md4 class="table-row-cell right-align">
          <span> {{job.recurringJobId ? job.recurringJobId : job.jobId}} </span>
        </v-flex>
        <v-flex md4 class="table-row-cell right-align">
          <span> {{job.status}} </span>
        </v-flex>
      </v-layout>
    </v-flex>

    <v-flex md12 pb-1
      ><v-layout align-center>
        <h5 class="subheader--bold px-3 pt-1">Active Permanent Jobs</h5>
        <v-flex>
          <v-divider></v-divider>
        </v-flex>
        <h5 class="subheader--bold--14 pt-1 px-3">
          {{recurringJobList.length}} Jobs
        </h5>
      </v-layout>
    </v-flex>
    <v-flex
      md12
      class="px-3 py-2 app-bgcolor--400 app-bordercolor--600 app-borderside--a"
      v-if="!!recurringJobList.length"
    >
      <v-layout justify-space-between class="table-row-custom">
        <v-flex md4>
          <span class="table-row-custom__header header-type"> Client </span>
        </v-flex>
        <v-flex md4 class="table-row-cell right-align">
          <span class="table-row-custom__header header-type"> Job ID </span>
        </v-flex>
        <v-flex md4 class="table-row-cell right-align">
          <span class="table-row-custom__header header-type">
            Next Scheduled
          </span>
        </v-flex>
      </v-layout>
      <v-layout
        justify-space-between
        class="table-row-custom"
        v-for="job in recurringJobList"
        :key="job.jobId"
      >
        <v-flex md4>
          <span
            >{{job.client ? `${job.client.clientName} - ${job.client.id}` :
            'Unknown'}}</span
          >
        </v-flex>
        <v-flex md4 class="table-row-cell right-align">
          <span> {{job.initialJobId ? job.initialJobId : 'Unknown'}} </span>
        </v-flex>
        <v-flex md4 class="table-row-cell right-align">
          <span>
            {{job.recurrenceDetails &&
            job.recurrenceDetails.nextScheduledRecurrence ?
            returnFormattedDate(job.recurrenceDetails.nextScheduledRecurrence) :
            '-'}}
          </span>
        </v-flex>
      </v-layout>
    </v-flex>
  </v-layout>
  <v-layout justify-center pa-3 v-else>
    <img
      src="@/static/loader/infinity-loader-light.svg"
      height="80px"
      width="80px"
    />
  </v-layout>
</div>
