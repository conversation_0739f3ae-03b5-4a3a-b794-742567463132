import DateTimeInputs from '@/components/common/date-picker/date_time_inputs.vue';
import StatusSelect from '@/components/common/status_select.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { DriverAssociatedFleetAssets } from '@/interface-models/Driver/DriverAssociations';
import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import { SubcontractorAssociationUpdateRequest } from '@/interface-models/FleetAssetOwner/SubcontractorAssociation/SubcontractorAssociationUpdateRequest';
import { SubcontractorAssociationUpdateResponse } from '@/interface-models/FleetAssetOwner/SubcontractorAssociation/SubcontractorAssociationUpdateResponse';
import { SubcontractorAssociationUpdateType } from '@/interface-models/FleetAssetOwner/SubcontractorAssociation/SubcontractorAssociationUpdateType';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import AssociatedDevice from '@/interface-models/Generic/AssociatedDevice/AssociatedDevice';
import { Validation } from '@/interface-models/Generic/Validation';
import { SubcontractorEntityType } from '@/interface-models/InvoiceAdjustment/EntityTypes/SubcontractorEntityType';
import { OperationJobSummary } from '@/interface-models/Jobs/OperationJobSummary';
import RecurringJobTemplate from '@/interface-models/Jobs/RecurringJob/RecurringJobTemplate';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useJobStore } from '@/store/modules/JobStore';
import { Component, Prop, Vue } from 'vue-property-decorator';

interface AssociatedDrivers {
  driverId: string;
  name: string;
}

interface AssociatedDeviceTable {
  deviceId: string;
  assignedDate: string;
  assignedAssetCsrAssignedId: string;
}

@Component({
  components: {
    StatusSelect,
    DateTimeInputs,
  },
})
export default class SubcontractorActiveAssociations
  extends Vue
  implements IUserAuthority
{
  @Prop() public entityId: string;
  @Prop() public entityType: SubcontractorEntityType;
  @Prop({ default: false }) public isEdited: boolean;
  @Prop({ default: false })
  public isSubcontractorOperationalStatusUpdate: boolean;
  @Prop({ default: false })
  public isDriverAssociations: boolean;
  // ownerId will be defined when checking against driver dissociation/association
  @Prop({ default: () => null }) public ownerId: string | null;
  @Prop({ default: true }) public entityIsActive: boolean;
  @Prop({ default: null }) public statusList: number[];

  public fleetAssetStore = useFleetAssetStore();
  public driverDetailsStore = useDriverDetailsStore();
  public fleetAssetOwnerStore = useFleetAssetOwnerStore();

  public pendingWorkJobIdList: number[] = [];
  public activeWorkJobIdList: number[] = [];
  public recurringJobList: RecurringJobTemplate[] = [];
  public fleetAssetIds: string[] = [];
  public driverIds: string[] = [];
  public deviceIds: string[] = [];
  public ownerIds: string[] = [];
  public returnFormattedDate: (epoch: number) => string = returnFormattedDate;
  public activeDriverForDissociationName: string | null = null;

  public isAwaitingResponse: boolean = false;

  public isAuthorised(): boolean {
    return hasAdminOrHeadOfficeRole();
  }

  get validate(): Validation {
    return validationRules;
  }

  public setActiveAssociations(
    response: SubcontractorAssociationUpdateResponse,
  ): void {
    if (!response) {
      return;
    }
    this.pendingWorkJobIdList = response.pendingWorkJobIdList
      ? response.pendingWorkJobIdList
      : [];
    this.activeWorkJobIdList = response.activeWorkJobIdList
      ? response.activeWorkJobIdList
      : [];
    this.recurringJobList = response.recurringJobList
      ? response.recurringJobList
      : [];
    this.fleetAssetIds = response.fleetAssetIds ? response.fleetAssetIds : [];
    this.driverIds = response.driverIds ? response.driverIds : [];
    this.deviceIds = response.deviceIds ? response.deviceIds : [];
    this.ownerIds = response.ownerIds ? response.ownerIds : [];

    if (this.isSubcontractorOperationalStatusUpdate) {
      let canUpdateAssociation = false;
      if (this.entityType === SubcontractorEntityType.FLEET_ASSET_OWNER) {
        canUpdateAssociation = this.canUpdateFleetAssetOwner();
      } else if (this.entityType === SubcontractorEntityType.FLEET_ASSET) {
        canUpdateAssociation = this.canUpdateFleetAsset();
      } else if (this.entityType === SubcontractorEntityType.DRIVER) {
        canUpdateAssociation = this.canUpdateDriver();
      }
      // Asset is active if statusList includes 4 (ACTIVE)
      const isCurrentlyActive =
        response.statusList != null && response.statusList.includes(4);

      // If the ASSET is currently inactive then allow the association to be
      // updated. If it is current ACTIVE, then depend on the result of the
      // type-dependant checks above
      this.$emit(
        'update:canActionAssociationUpdate',
        !isCurrentlyActive || canUpdateAssociation,
      );
      this.$emit('update:entityIsActive', isCurrentlyActive);
    }
  }

  public canUpdateFleetAssetOwner(): boolean {
    return !(
      this.pendingWorkJobIdList.length ||
      this.activeWorkJobIdList.length ||
      this.recurringJobList.length ||
      this.driverIds.length ||
      this.fleetAssetIds.length
    );
  }

  public canUpdateFleetAsset(): boolean {
    return !(
      this.pendingWorkJobIdList.length ||
      this.activeWorkJobIdList.length ||
      this.recurringJobList.length ||
      this.driverIds.length
    );
  }

  public canUpdateDriver(): boolean {
    if (this.ownerId) {
      this.setActiveDriverForDissociationName();
      return (
        !this.activeDriverForDissociationName &&
        !(
          this.pendingWorkJobIdList.length ||
          this.activeWorkJobIdList.length ||
          this.recurringJobList.length ||
          this.fleetAssetIds.length
        )
      );
    } else {
      const result =
        !!this.pendingWorkJobIdList.length ||
        !!this.activeWorkJobIdList.length ||
        !!this.recurringJobList.length ||
        !!this.fleetAssetIds.length;
      return !result;
    }
  }

  get pendingWorkJobDetailsList(): OperationJobSummary[] {
    return useJobStore().operationJobsList.filter(
      (x: OperationJobSummary) =>
        x.jobId && this.pendingWorkJobIdList.includes(x.jobId),
    );
  }

  get activeWorkJobDetailsList(): OperationJobSummary[] {
    return useJobStore().operationJobsList.filter(
      (x: OperationJobSummary) =>
        x.jobId && this.activeWorkJobIdList.includes(x.jobId),
    );
  }

  get associatedFleetAssets(): DriverAssociatedFleetAssets[] {
    return this.fleetAssetIds.map((fleetAssetId: string) => {
      const fleetAsset: FleetAssetSummary | undefined =
        this.fleetAssetStore.fleetAssetSummaryMap.get(fleetAssetId);
      return {
        fleetAssetId: fleetAsset ? fleetAsset.fleetAssetId : '-',
        csrAssignedId: fleetAsset ? fleetAsset.csrAssignedId : '-',
        assetType: fleetAsset
          ? fleetAsset.fleetAssetTypeId === 1
            ? 'Truck'
            : 'Trailer'
          : '-',
        rego: fleetAsset ? fleetAsset.registrationNumber : '-',
        isDefaultDriver: fleetAsset
          ? fleetAsset.defaultDriver === this.entityId
            ? 'YES'
            : 'NO'
          : '-',
      };
    });
  }

  get associatedDrivers(): AssociatedDrivers[] {
    if (this.entityType === SubcontractorEntityType.DRIVER || !this.driverIds) {
      return [];
    }
    return this.driverIds.map((driverId: string) => {
      const driverDetails: DriverDetailsSummary | undefined =
        this.driverDetailsStore.driverSummaryMap.get(driverId);

      return {
        driverId: driverDetails ? driverDetails.driverId : '-',
        name: driverDetails?.displayName ?? '-',
      };
    });
  }

  get associatedDevices(): AssociatedDeviceTable[] {
    if (this.entityType === SubcontractorEntityType.DRIVER || !this.deviceIds) {
      return [];
    }

    return this.deviceIds.map((deviceId: string) => {
      const fleetAssetOwner: FleetAssetOwnerSummary | undefined =
        this.entityType === SubcontractorEntityType.FLEET_ASSET
          ? this.fleetAssetOwnerStore.getOwnerFromFleetAssetId(this.entityId)
          : this.fleetAssetOwnerStore.getOwnerFromOwnerId(this.entityId);

      const foundDevice = fleetAssetOwner
        ? fleetAssetOwner.associatedDevices.find(
            (device: AssociatedDevice) => device.id === deviceId,
          )
        : undefined;

      const assignedAsset = foundDevice
        ? this.fleetAssetStore.getFleetAssetFromFleetAssetId(
            foundDevice.associationId,
          )
        : undefined;

      return {
        deviceId: foundDevice ? foundDevice.deviceId : '-',
        assignedDate:
          foundDevice && foundDevice.createdEpoch
            ? returnFormattedDate(foundDevice.createdEpoch)
            : '-',
        assignedAssetCsrAssignedId: assignedAsset
          ? assignedAsset.csrAssignedId
          : '-',
      };
    });
  }

  // When a driver is to be dissociated from their last known fleet asset owner association we should make sure that they are inactive. If they are not inactive the method returns the drivers name. This value dictates where to show the active driver of not
  public setActiveDriverForDissociationName(): void {
    if (this.entityType !== SubcontractorEntityType.DRIVER || !this.ownerId) {
      this.activeDriverForDissociationName = null;
    }
    const showActiveDriverForDissociation =
      this.statusList.includes(4) && this.ownerIds.length === 1;

    if (!showActiveDriverForDissociation) {
      this.activeDriverForDissociationName = null;
      return;
    }
    const driverDetails = this.driverDetailsStore.getDriverFromDriverId(
      this.entityId,
    );
    this.activeDriverForDissociationName = driverDetails?.displayName ?? null;
  }

  public mounted(): void {
    this.getSubcontractorEntityAssociations();
  }

  public async getSubcontractorEntityAssociations(): Promise<void> {
    const request: SubcontractorAssociationUpdateRequest = {
      driverId:
        this.entityType === SubcontractorEntityType.DRIVER
          ? this.entityId
          : null,
      ownerId:
        this.entityType === SubcontractorEntityType.FLEET_ASSET_OWNER
          ? this.entityId
          : SubcontractorEntityType.DRIVER && this.ownerId
            ? this.ownerId
            : null,
      fleetAssetId:
        this.entityType === SubcontractorEntityType.FLEET_ASSET
          ? this.entityId
          : null,
      entityType: this.entityType,
      type: SubcontractorAssociationUpdateType.CHECK,
      statusList: this.statusList,
    };
    // Set isAwaitingResponse to true and dispatch request
    this.isAwaitingResponse = true;
    // Dispatch request
    const result =
      await this.fleetAssetOwnerStore.updateSubcontractorEntityAssociation(
        request,
      );
    if (result) {
      this.setActiveAssociations(result);
    } else {
      showNotification(GENERIC_ERROR_MESSAGE);
    }
    this.isAwaitingResponse = false;
  }
}
