<template>
  <section class="driver-bulk-message">
    <v-layout row wrap>
      <v-flex md12 pt-3 px-3 v-if="!isReviewingMessageDetails">
        <v-layout py-1 pb-2 align-center>
          <v-flex
            md3
            style="
              text-transform: uppercase;
              color: rgb(177, 177, 177);
              font-weight: 600;
            "
            >Send to:
          </v-flex>
          <v-flex md9 v-if="!isReviewingMessageDetails">
            <v-select
              label="Adding note to"
              solo
              flat
              hide-details
              background-color="#22222e"
              :items="menuOptions"
              item-text="title"
              item-value="id"
              v-model="selectedMessageType"
              color="orange"
            >
            </v-select>
          </v-flex>
        </v-layout>
        <v-layout
          py-1
          pb-2
          align-center
          v-if="selectedMessageType === 'SELECTED'"
        >
          <v-flex md3> </v-flex>
          <v-flex md9>
            <v-select
              label="Please select drivers..."
              solo
              flat
              hide-details
              background-color="#22222e"
              :items="allActiveDriversList"
              item-text="displayName"
              item-value="driverId"
              v-model="selectedDrivers"
              multiple
              clearable
              color="orange"
              return-object
            >
              <template v-slot:selection="{ item, index }">
                <v-chip v-if="index === 0">
                  <span>{{ item.name }}</span>
                </v-chip>
                <span v-if="index === 1" class="grey--text caption"
                  >(+{{ selectedDrivers.length - 1 }} others)</span
                >
              </template>
            </v-select>
          </v-flex>
        </v-layout>
        <v-layout>
          <v-flex md12>
            <v-textarea
              placeholder="Please enter a message"
              label="Message contents"
              background-color="#22222e"
              hide-details
              box
              flat
              rows="7"
              color="orange"
              v-model="chatMessageContent"
            ></v-textarea>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12 pt-3 px-3 v-if="isReviewingMessageDetails">
        <v-layout py-1 pb-3 align-start>
          <v-flex
            md3
            style="
              text-transform: uppercase;
              color: rgb(177, 177, 177);
              font-weight: 600;
            "
            >Send to:
          </v-flex>

          <v-flex md9>
            <span>
              {{ messageRecipients.map((d) => d.displayName).join(', ') }}
            </span>
          </v-flex>
        </v-layout>

        <v-layout>
          <v-flex
            md3
            style="
              text-transform: uppercase;
              color: rgb(177, 177, 177);
              font-weight: 600;
            "
            >Message:
          </v-flex>
          <v-flex md9>
            <span style="font-style: italic"> {{ chatMessageContent }} </span>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12 pt-3 pb-1 px-2>
        <v-divider></v-divider>
      </v-flex>
      <v-flex md12 pa-1>
        <v-layout>
          <v-btn @click="showDialog = false" flat color="white"> Cancel </v-btn>
          <v-spacer></v-spacer>

          <v-btn
            @click="isReviewingMessageDetails = false"
            outline
            color="white"
            v-if="isReviewingMessageDetails"
          >
            Edit
          </v-btn>
          <v-btn
            @click="
              isReviewingMessageDetails
                ? dispatchBulkMessageRequest()
                : (isReviewingMessageDetails = true)
            "
            depressed
            color="blue"
            :loading="isAwaitingResponse"
            :disabled="!chatMessageContent"
          >
            {{ isReviewingMessageDetails ? 'Send Message' : 'Confirm' }}
          </v-btn>
        </v-layout>
      </v-flex>
    </v-layout>
  </section>
</template>

<script setup lang="ts">
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import { BulkMessageType } from '@/interface-models/Generic/ChatConversation/BulkChatMessageType';
import ChatMessage from '@/interface-models/Generic/ChatConversation/ChatMessage';
import { ChatMessageType } from '@/interface-models/Generic/ChatConversation/ChatMessageType';
import SendBulkChatMessageRequest from '@/interface-models/Generic/ChatConversation/SendBulkChatMessageRequest';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { useDriverAppStore } from '@/store/modules/DriverAppStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useDriverMessageStore } from '@/store/modules/DriverMessageStore';
import { useGpsStore } from '@/store/modules/GpsStore';
import { sessionManager } from '@/store/session/SessionState';
import moment from 'moment';
import {
  computed,
  ComputedRef,
  onMounted,
  Ref,
  ref,
  WritableComputedRef,
} from 'vue';

/**
 * Menu option interface for the select menu.
 */
interface MenuOption {
  id: string;
  title: string;
}

/**
 * Controls the visibility of the driver bulk message dialog.
 */
const props = withDefaults(
  defineProps<{
    showDriverBulkMessageDialog?: boolean;
  }>(),
  {
    showDriverBulkMessageDialog: false,
  },
);

const emit = defineEmits<{
  (event: 'setShowDriverBulkMessageDialog', value: boolean): void;
}>();

const selectedMenuOption: Ref<string> = ref('TODAY');
const selectedDrivers: Ref<DriverDetailsSummary[]> = ref([]);
const allActiveDriversList: Ref<DriverDetailsSummary[]> = ref([]);
const recentlyActiveDriverList: Ref<DriverDetailsSummary[]> = ref([]);
const isAwaitingResponse: Ref<boolean> = ref(false);
const isReviewingMessageDetails: Ref<boolean> = ref(false);
const chatMessageContent: Ref<string> = ref('');
const menuOptions: MenuOption[] = [
  {
    id: 'TODAY',
    title: `Today's Active Drivers`,
  },
  {
    id: 'SELECTED',
    title: 'Selected Drivers',
  },
];

/**
 * The selected message type (getter/setter for selectedMenuOption).
 */
const selectedMessageType: WritableComputedRef<string> = computed({
  get(): string {
    return selectedMenuOption.value;
  },
  set(value: string): void {
    if (value !== 'SELECTED') {
      selectedDrivers.value = [];
    }
    selectedMenuOption.value = value;
  },
});

/**
 * Controls the dialog visibility (getter/setter for prop).
 */
const showDialog: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.showDriverBulkMessageDialog;
  },
  set(value: boolean): void {
    emit('setShowDriverBulkMessageDialog', value);
  },
});

/**
 * Returns the list of message recipients based on the selected option.
 */
const messageRecipients: ComputedRef<DriverDetailsSummary[]> = computed(() => {
  if (selectedMessageType.value === 'SELECTED') {
    return selectedDrivers.value ? selectedDrivers.value : [];
  } else {
    return recentlyActiveDriverList.value;
  }
});

/**
 * Constructs a ChatMessage object with the given content.
 * @param content - The message content.
 * @returns The constructed ChatMessage.
 */
function constructChatMessage(content: string): ChatMessage {
  const chatRecord: ChatMessage = new ChatMessage();
  chatRecord.content = content;
  chatRecord.company = sessionManager.getCompanyId();
  chatRecord.division = sessionManager.getDivisionId();
  chatRecord.senderName = sessionManager.getActiveUser();
  chatRecord.timestamp = moment().valueOf();
  chatRecord.senderId = sessionManager.getUserId();
  chatRecord.messageType = ChatMessageType.TEXT;
  return chatRecord;
}

/**
 * Dispatches the bulk message request to the selected drivers.
 */
async function dispatchBulkMessageRequest(): Promise<void> {
  if (!chatMessageContent.value) {
    showNotification('Please enter a message.');
    return;
  }
  const recipientIdList: string[] = messageRecipients.value.map(
    (d) => d.driverId,
  );
  if (!recipientIdList.length) {
    showNotification('No valid drivers selected.');
    return;
  }
  const chatMessage: ChatMessage = constructChatMessage(
    chatMessageContent.value,
  );
  const request: SendBulkChatMessageRequest = {
    recipientIdList,
    messageType: BulkMessageType.SPECIFIED_DRIVERS,
    chatMessage,
  };
  isAwaitingResponse.value = true;
  const result =
    await useDriverMessageStore().dispatchBulkChatMessageRequest(request);
  isAwaitingResponse.value = false;
  const message = result
    ? 'Bulk driver message sent successfully.'
    : 'Failed to send bulk message.';
  const type = result ? HealthLevel.SUCCESS : HealthLevel.ERROR;
  showNotification(message, {
    type,
    title: 'Driver Bulk Message',
  });
  showDialog.value = false;
}

/**
 * Returns a list of recently active drivers based on online status and GPS.
 * @param driverList - The list of all drivers.
 * @returns The filtered list of recently active drivers.
 */
function setRecentlyActiveDrivers(
  driverList: DriverDetailsSummary[],
): DriverDetailsSummary[] {
  const recentlyOnline: string[] = [
    ...useDriverAppStore().onlineDriverMap.keys(),
  ];
  const recentGps: string[] = [...useGpsStore().allGpsPositions.values()].map(
    (gps) => gps.driverId,
  );
  const recentlyActive: string[] = [
    ...new Set([...recentlyOnline, ...recentGps]),
  ];
  return driverList.filter((d) => recentlyActive.includes(d.driverId));
}

/**
 * Sets the lists of all active and recently active drivers.
 */
function setDriverLists(): void {
  const allDrivers: DriverDetailsSummary[] =
    useDriverDetailsStore().getDriverList;
  allActiveDriversList.value = allDrivers.filter((d) =>
    (d.statusList ? d.statusList : []).includes(4),
  );
  recentlyActiveDriverList.value = setRecentlyActiveDrivers(allDrivers);
}

/**
 * Initialize driver lists on mount.
 */
onMounted(() => {
  setDriverLists();
});
</script>
<style scoped lang="scss"></style>
