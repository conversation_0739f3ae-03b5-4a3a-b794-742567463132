<template>
  <div v-if="jobSummary">
    <div class="item-container">
      <span class="summary-header pr-1">Ready For Invoicing:</span
      ><span class="summary-value">{{ jobSummary.completed }}</span>
    </div>
    <div class="item-container">
      <span class="summary-header pr-1 pl-1">- In Progress:</span
      ><span class="summary-value">{{ jobSummary.revenueInProgress }}</span>
    </div>
    <div class="item-container">
      <span class="summary-header pr-1">Subcontractor RCTI:</span
      ><span class="summary-value">{{
        jobSummary.revenueCompleteExpenseNotActionedJobCount
      }}</span>
    </div>

    <div class="item-container">
      <span class="summary-header pr-1 pl-1">- In Progress:</span
      ><span class="summary-value">{{ jobSummary.expenseInProgress }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DivisionJobsSummary } from '@/interface-models/Generic/DivisionJobsSummary/DivisionJobsSummary';
import { useJobStatisticsStore } from '@/store/modules/JobStatisticsStore';
import { computed, ComputedRef } from 'vue';

const jobSummary: ComputedRef<DivisionJobsSummary | null> = computed(() => {
  return useJobStatisticsStore().divisionJobsSummary;
});
</script>

<style lang="scss" scoped>
.summary-header {
  width: 100%;
  margin: 18px;
  font-size: $font-size-20;
}

.summary-value {
  margin: 18px;
  font-weight: 500;
  font-size: $font-size-20;
  color: var(--text-color);
}

.item-container {
  border-top: 0.5px solid $border-color;
  width: 100%;
  display: flex;
  height: 55px;
  justify-content: space-between;
  align-items: center;
}
</style>
