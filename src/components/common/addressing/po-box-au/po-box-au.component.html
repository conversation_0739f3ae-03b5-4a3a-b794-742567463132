<section class="address-search-au">
  <v-layout wrap>
    <v-flex md12>
      <v-layout>
        <v-flex md4>
          <v-layout align-center class="label-container">
            <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
              Country / Region:
            </h6>
          </v-layout>
        </v-flex>
        <v-flex md8>
          <v-select
            :items="countries"
            v-model="poBoxAddress.country"
            item-text="name"
            item-value="name"
            solo
            flat
            :rules="[validate.required]"
            class="v-solo-custom"
            color="light-blue"
            :disabled="formDisabled"
          ></v-select>
        </v-flex>
      </v-layout>
    </v-flex>

    <v-flex md12>
      <v-layout>
        <v-flex md4>
          <v-layout align-center class="label-container">
            <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
              PO Box Type:
            </h6>
          </v-layout>
        </v-flex>
        <v-flex md8>
          <v-select
            :disabled="formDisabled"
            :items="poBoxTypes"
            item-value="id"
            :rules="[validate.required]"
            item-text="longName"
            v-model="poBoxAddress.poBoxType"
            class="v-solo-custom"
            solo
            flat
            color="light-blue"
          ></v-select>
        </v-flex>
      </v-layout>
    </v-flex>

    <v-flex md12>
      <v-layout>
        <v-flex md4>
          <v-layout align-center class="label-container">
            <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
              PO Box Number:
            </h6>
          </v-layout>
        </v-flex>
        <v-flex md8>
          <v-text-field
            :disabled="formDisabled"
            v-model="poBoxAddress.poBoxNumber"
            class="v-solo-custom"
            solo
            flat
            color="light-blue"
            :rules="[validate.required]"
          ></v-text-field>
        </v-flex>
      </v-layout>
    </v-flex>

    <v-flex md12>
      <v-layout>
        <v-flex md4>
          <v-layout align-center class="label-container">
            <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
              Suburb:
            </h6>
          </v-layout>
        </v-flex>
        <v-flex md8>
          <v-text-field
            v-model="poBoxAddress.suburb"
            :disabled="formDisabled"
            class="v-solo-custom"
            solo
            flat
            color="light-blue"
            :rules="[validate.required]"
          ></v-text-field>
        </v-flex>
      </v-layout>
    </v-flex>

    <v-flex md12>
      <v-layout>
        <v-flex md4>
          <v-layout align-center class="label-container">
            <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
              Postcode:
            </h6>
          </v-layout>
        </v-flex>
        <v-flex md8>
          <v-text-field
            :disabled="formDisabled"
            v-model="poBoxAddress.postcode"
            :rules="[validate.required]"
            class="v-solo-custom"
            solo
            flat
            color="light-blue"
          ></v-text-field>
        </v-flex>
      </v-layout>
    </v-flex>

    <v-flex md12>
      <v-layout>
        <v-flex md4>
          <v-layout align-center class="label-container">
            <h6 class="subheader--faded pr-3 pb-0 form-field-required-marker">
              State:
            </h6>
          </v-layout>
        </v-flex>
        <v-flex md8>
          <v-select
            :disabled="formDisabled || !poBoxAddress.country"
            :items="selectedCountry ? selectedCountry.states : []"
            v-model="poBoxAddress.state"
            :rules="[validate.required]"
            item-text="name"
            item-value="iso"
            class="v-solo-custom"
            solo
            flat
            color="light-blue"
          >
          </v-select>
        </v-flex>
      </v-layout>
    </v-flex>
  </v-layout>
</section>
