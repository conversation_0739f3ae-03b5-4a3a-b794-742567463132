import { POBoxAddressAU } from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/POBoxAddressAU';
import { Component, Prop, Vue } from 'vue-property-decorator';

import {
  countries,
  Country,
} from '@/interface-models/Generic/Addressing/Country';
import {
  PoBoxType,
  poBoxTypes,
} from '@/interface-models/Generic/POBoxType/POBoxType';
import { Validation } from '@/interface-models/Generic/Validation';

@Component
export default class POBoxAddressAUComponent extends Vue {
  // props
  @Prop() public poBoxAddress: POBoxAddressAU; // pass address into this
  @Prop() public formDisabled: boolean;
  @Prop() public validate: Validation;

  public poBoxTypes: PoBoxType[] = poBoxTypes;

  public countries: Country[] = countries;

  // Return the selected country so that we can display states list in HTML, and
  // so that we can perform country specific validation
  get selectedCountry(): Country | undefined {
    if (!this.poBoxAddress || !this.poBoxAddress.country) {
      return;
    }
    return countries.find((c) => c.name === this.poBoxAddress.country);
  }
}
