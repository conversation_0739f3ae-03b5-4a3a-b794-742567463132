.address-search-au {
  .manual-address__container {
    padding: 4px 16px;
    background-color: var(--background-color-300);
  }
  .address-component__heading {
    font-size: $font-size-16;
    color: var(--heading-text-color);
    text-transform: uppercase;
    font-weight: 600;
  }
  .no-address-txt {
    position: absolute;
    max-width: 375px;
    transform: translate(150px, -50px);
    background-color: var(--background-color-100);
    color: var(--accent);
    transition: all 0.3s ease;
    padding: 12px;
    padding-left: 16px;
    z-index: 1;
    box-shadow: var(--box-shadow);
    border-radius: 40px 50px 50px 5px;
    font-size: $font-size-12;
    font-weight: 500;
    border: 1px solid $border-color;
  }
}

.greyOutOutsideStateAddress {
  color: var(--text-color-100);
}
