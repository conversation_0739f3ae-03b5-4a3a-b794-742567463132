<template>
  <v-autocomplete
    class="v-solo-custom"
    label="Suburb Search"
    v-model="suburbSelection"
    :disabled="formDisabled"
    item-text="formattedAddress"
    return-object
    :items="suburbSearchResults"
    :search-input.sync="suburbInput"
    :placeholder="address.suburb"
    auto-select-first
    type="search"
    autocomplete="off"
    browser-autocomplete="off"
    :suffix="isManual ? ' ' : address.postcode"
    :autofocus="setFocus && searchAddress && enableSuburbSelect"
    color="orange"
    outline
    hide-details
    flat
  >
    <template #item="{ item }">
      <span
        :class="
          !addressIsInDepotState(item.formattedAddress)
            ? 'greyOutOutsideStateAddress'
            : ''
        "
        >{{ item.formattedAddress }}</span
      >
    </template>
    <template #selection="{ item }">
      <span>{{ item.name }}</span>
    </template>
  </v-autocomplete>
</template>

<script setup lang="ts">
import { AddressAU } from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/AddressAU';
import SuburbAU from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/SuburbAU';
import { useAddressingStore } from '@/store/modules/AddressingStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { computed, onUnmounted, ref, watch } from 'vue';

const props = withDefaults(
  defineProps<{
    address: AddressAU;
    formDisabled?: boolean;
    setFocus?: boolean;
    searchAddress?: boolean;
    enableSuburbSelect?: boolean;
    isManual?: boolean;
    selectedSuburbProp?: SuburbAU | null;
  }>(),
  {
    formDisabled: false,
    setFocus: false,
    searchAddress: false,
    enableSuburbSelect: false,
    isManual: false,
    selectedSuburbProp: null,
  },
);

const emit = defineEmits<{
  (event: 'suburbSelected', suburb: SuburbAU): void;
}>();

const companyDetailsStore = useCompanyDetailsStore();
const addressingStore = useAddressingStore();

const suburbSearchInput = ref<string>('');
const selectedSuburb = ref<SuburbAU | null>(null);
const suburbRequestTimeout = ref<ReturnType<typeof setTimeout>>();

// sync suburb inputs with suburbSelection
const suburbInput = computed({
  get: () => suburbSearchInput.value,
  set: (val: string) => {
    if (val !== null && val !== props.address.formattedAddress) {
      searchForSuburb(val);
    }
    suburbSearchInput.value = val;
  },
});

// suburbs list
const suburbSearchResults = computed(() => {
  return addressingStore.suburbsByDepotLocation;
});

// set and emit selected suburb
const suburbSelection = computed({
  get: () => selectedSuburb.value,
  set: (value: SuburbAU | null) => {
    selectedSuburb.value = value;
    if (value) {
      addressingStore.updateAddressSearchResults([]);
      emit('suburbSelected', value);
    }
  },
});

// Watch for changes to selectedSuburbProp
watch(
  () => props.selectedSuburbProp,
  (newVal) => {
    if (newVal && newVal.name && newVal.name.trim() !== '') {
      const results = addressingStore.suburbsByDepotLocation;
      const existing = results.find(
        (r) => r.formattedAddress === newVal.formattedAddress,
      );
      selectedSuburb.value = existing || newVal;
    } else {
      selectedSuburb.value = null;
    }
  },
);

// check address
function addressIsInDepotState(formattedAddress: string): boolean {
  if (companyDetailsStore.activeDepotState) {
    return formattedAddress.includes(companyDetailsStore.activeDepotState);
  }
  return false;
}

// search for input suburbs
function searchForSuburb(value: string): void {
  if (value.length > 0 && value !== undefined) {
    if (suburbRequestTimeout.value) {
      clearTimeout(suburbRequestTimeout.value);
    }
    suburbRequestTimeout.value = setTimeout(() => {
      addressingStore.searchForSuburb(value);
    }, 180);
  } else {
    addressingStore.clearSuburbSearchResults();
  }
}

// Cleanup on unmount
onUnmounted(() => {
  if (suburbRequestTimeout.value) {
    clearTimeout(suburbRequestTimeout.value);
  }
});
</script>

<style lang="scss" scoped>
.greyOutOutsideStateAddress {
  color: var(--text-color-100);
}
</style>
