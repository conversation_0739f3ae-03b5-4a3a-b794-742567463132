import AddressSuburbSearch from '@/components/common/addressing/address-search-au/address_suburb_search.vue';
import PinDropCoordinates from '@/components/common/pin-drop-coordinates/index.vue';
import StreetViewLink from '@/components/common/street_view_link.vue';
import {
  returnFormattedAddressFromAddressAU,
  validateBoundingBoxLat,
  validateBoundingBoxLong,
} from '@/helpers/DistanceHelpers/DistanceHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  validate,
  validationRules,
} from '@/helpers/ValidationHelpers/ValidationHelpers';
import { AuthUserDetails } from '@/interface-models/Admin/AuthUserDetails';
import { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import {
  ClientCommonAddressRelatedContactSave,
  saveCommonAddressAndRelatedContact,
} from '@/interface-models/Client/ClientCommonAddressRelatedContactSave';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import ClientPerson from '@/interface-models/Client/ClientDetails/ClientPerson/ClientPerson';
import {
  countries,
  Country,
} from '@/interface-models/Generic/Addressing/Country';
import { AddressAU } from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/AddressAU';
import SuburbAU from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/SuburbAU';
import { ElasticSearchAddress } from '@/interface-models/Generic/Addressing/ElasticSearchAddress';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { Validation } from '@/interface-models/Generic/Validation';
import { useAddressingStore } from '@/store/modules/AddressingStore';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useRootStore } from '@/store/modules/RootStore';
import { sessionManager } from '@/store/session/SessionState';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';

interface MenuData {
  id: number;
  value: string;
  longName: string;
  icon: string;
  hidden: boolean;
}

@Component({
  components: {
    PinDropCoordinates,
    StreetViewLink,
    AddressSuburbSearch,
  },
})
export default class AddressSearchAU extends Vue {
  // props
  @Prop() public label: string;
  @Prop() public address: AddressAU; // pass address into this
  @Prop() public nickNameList: ClientCommonAddress[]; // pass nickname array into this
  @Prop() public formDisabled: boolean;
  @Prop({ default: false }) public switchToSearch: boolean;
  @Prop({ default: false }) public setFocus: boolean;
  @Prop({ default: false }) public boxInput: boolean;
  @Prop({ default: false }) public clearAddress: boolean;
  @Prop({ default: false }) public enableAddNickNameAddress: boolean;
  @Prop() public clientDetails: ClientDetails;
  @Prop() public existingUserAccountList: AuthUserDetails[];
  @Prop({ default: 0 }) public numberOfExistingStops: number;
  @Prop({ default: false }) public hideFullAddress: boolean;
  @Prop({ default: true }) public enableNicknamedAddress: boolean;
  @Prop({ default: false }) public soloInput: boolean;
  @Prop({ default: true }) public enableSuburbSelect: boolean;
  @Prop({ default: true }) public enablePinDrop: boolean;
  @Prop({ default: true }) public detailedView: boolean;
  @Prop({ default: true }) public enableManualAddress: boolean;
  @Prop({ default: true }) public enableReturnToDefaultDispatchAddress: boolean;
  @Prop({ default: false }) public addressIsRequired: boolean;
  @Prop({ default: false }) public isClientPortal: boolean;
  @Prop({ default: false }) public hideHeadingRow: boolean;
  @Prop({ default: false }) public enableAddPreload: boolean;
  // Attach results to address element. Prop should be defined when address component is placed within the native html dialog element
  @Prop({ default: false }) public attach: boolean;
  @Prop({ default: null }) public commonAddressNickname: string;

  public companyDetailsStore = useCompanyDetailsStore();
  public addressingStore = useAddressingStore();
  public rootStore = useRootStore();

  public countries: Country[] = countries;
  // component data
  public selectedAddress: AddressAU = new AddressAU();
  public searchAddress: boolean = false; // search address input
  public nickNameInput: boolean = false;
  public manualAddress: boolean = false;
  public pinDropAddress: boolean = false;
  public nickNameAddress: ClientCommonAddress = new ClientCommonAddress();
  public nickNameSelect: boolean = false;
  public manualAddressObj: AddressAU = new AddressAU();
  public selectedAddressId: string = '';
  public searchInput: string = '';
  public addressId: AddressAU | string = new AddressAU();
  public selectNicknamePerson: boolean = false;
  public search: string = '';
  // Client Person values
  public clientPerson?: ClientPerson;
  public isClientPersonMaintenance: boolean = false;
  public isEditedClientPerson: boolean = false;

  public viewTypeString: string = '';

  public selectedSuburb: SuburbAU = new SuburbAU();
  public suburbRequestTimeout: ReturnType<typeof setTimeout>;
  public addressRequestTimeout: ReturnType<typeof setTimeout>;

  public clientCommonAddress: ClientCommonAddress | null = null;
  public isLoadingClientCommonAddressSave: boolean = false;

  public errorMessages: string[] = [];

  public $refs!: {
    manualAddressRef: any;
    commonAddressMaintenanceRef: any;
  };

  get menuOptions() {
    const menu = [
      {
        id: 0,
        value: 'search',
        longName: 'Address Search',
        icon: 'far fa-search',
        hidden: false,
      },
      {
        id: 1,
        value: 'nickname',
        longName: 'Common Addresses',
        icon: 'fas fa-address-card',
        hidden: !this.enableNicknamedAddress,
      },
      {
        id: 2,
        value: 'pindrop',
        longName: 'Map Pin Drop',
        icon: 'far fa-map-pin',
        hidden: !this.enablePinDrop,
      },
      {
        id: 3,
        value: 'manual',
        longName: 'Manually Enter Address',
        icon: 'far fa-list-alt',
        hidden: !this.enableManualAddress,
      },
    ];

    return menu.filter((x: any) => !x.hidden);
  }

  public get filteredNickNameList(): ClientCommonAddress[] {
    const filteredList = this.nickNameList.filter(
      (item) =>
        item.address.formattedAddress
          .toLowerCase()
          .includes(this.search.toLowerCase()) ||
        item.addressNickname.toLowerCase().includes(this.search.toLowerCase()),
    );

    // Sort the filtered list alphabetically by addressNickname
    filteredList.sort((a, b) =>
      a.addressNickname.localeCompare(b.addressNickname),
    );

    return filteredList;
  }

  public updateSearch(search: string): void {
    this.search = search;
    // No need to manually call a method to filter the items since filteredNickNameList is already reactive
  }

  // Return the selected country so that we can display states list in HTML, and
  // so that we can perform country specific validation
  get selectedCountry(): Country | undefined {
    if (!this.manualAddressObj || !this.manualAddressObj.country) {
      return;
    }
    return countries.find((c) => c.name === this.manualAddressObj.country);
  }

  get hasDefaultDispatchAddress() {
    if (!this.enableReturnToDefaultDispatchAddress) {
      return false;
    }
    return (
      this.nickNameList.findIndex((nn) => nn.defaultDispatchAddress) !== -1
    );
  }

  get addressSearchPlaceholder() {
    return this.address.formattedAddress;
  }
  // if the nickname address already exists we should block them from adding a duplicate
  get nicknameAddressAlreadyExists() {
    if (!this.clientDetails) {
      return;
    }
    const alreadyExists = useClientDetailsStore().clientCommonAddresses.find(
      (x: ClientCommonAddress) =>
        x.address.geoLocation[0] === this.address.geoLocation[0] &&
        x.address.geoLocation[1] === this.address.geoLocation[1],
    );
    return alreadyExists ? true : false;
  }

  // If no options are available for selection we disable the menu options icon
  get disableMenuOptions(): boolean {
    if (
      !this.enableManualAddress &&
      !this.enableNicknamedAddress &&
      !this.enableReturnToDefaultDispatchAddress &&
      !this.enablePinDrop
    ) {
      return true;
    }
    return false;
  }

  // The display label that is set on the address search. If The selected address is
  // the default dispatch address we let the user known through the label
  get displayLabel() {
    if (!this.nickNameList || this.nickNameList.length <= 0) {
      return this.label ? this.label : '';
    }
    const foundDefault = this.nickNameList.find(
      (item) => item.defaultDispatchAddress,
    );
    if (!foundDefault) {
      return this.label ? this.label : '';
    }
    if (
      this.address.addressId !== '' &&
      this.address.addressId === foundDefault.address.addressId
    ) {
      return 'Default Dispatch Address';
    }
    return this.label ? this.label : '';
  }

  // get selected address information; returned from backend
  get returnedAddress() {
    return this.addressingStore.addressByIdResult;
  }
  // watch returnedAddress for changes and sync changes with clonedAddress
  @Watch('returnedAddress')
  public addresschange(val: AddressAU) {
    if (this.selectedAddressId === val.addressId) {
      this.clonedAddress = val;
    }
  }
  get addressAddJob() {
    if (this.searchInput !== '') {
      // we remove duplicate addresses here so there is no duplicate key errors in html
      return Array.from(
        new Set(this.addressingStore.addressAddJob.map((a) => a.id)),
      ).map((id) => {
        return this.addressingStore.addressAddJob.find((a) => a.id === id);
      });
    }

    return [];
  }

  @Watch('clientDetails')
  public clientDetailsChanged() {
    this.addressId = new AddressAU();
  }

  @Watch('formDisabled')
  public resetBackToAddressSearch(formDisabled: boolean) {
    if (formDisabled) {
      this.menuItemSelected('search');
    }
  }

  @Watch('switchToSearch')
  public resetToAddressSearch(switchToSearch: boolean) {
    if (switchToSearch) {
      this.menuItemSelected('search');
    }
  }

  @Watch('searchInput')
  public searchInputChange(val: string) {
    if (val !== null) {
      if (val !== this.address.formattedAddress && val !== '') {
        this.searchForAddress(val);
      } else if (val === '') {
        this.addressingStore.updateAddressSearchResults([]);
      }
    }
  }

  public addressIsInDepotState(formattedAddress: string) {
    if (this.companyDetailsStore.activeDepotState) {
      return formattedAddress.includes(
        this.companyDetailsStore.activeDepotState,
      );
    } else {
      return false;
    }
  }
  // Checks that the value for lat is within the latitude values that enclose
  // the provided Country
  public validateLatitude(lat: number): string | boolean {
    return validateBoundingBoxLat(lat, this.selectedCountry);
  }
  // Checks that the value for long is within the longitude values that enclose
  // the provided Country
  public validateLongitude(long: number): string | boolean {
    return validateBoundingBoxLong(long, this.selectedCountry);
  }

  @Watch('clearAddress')
  public setNewAddress(value: boolean) {
    if (value) {
      this.addressId = new AddressAU();
    }
  }
  @Watch('address')
  public addressChanged(val: AddressAU) {
    this.searchInput = val.formattedAddress;
  }
  public searchItemSelected(val: ElasticSearchAddress) {
    this.addressId = val.id;
    this.selectAddress(val);
    this.searchInput = '';
    this.$emit('addressSelected', this.addressId);
  }
  public searchForAddress(value: string, enableSuburbSelect: boolean = false) {
    // Full address search
    if (!enableSuburbSelect) {
      if (value.length > 0 && value !== undefined) {
        if (this.addressRequestTimeout) {
          clearTimeout(this.addressRequestTimeout);
        }
        this.addressRequestTimeout = setTimeout(() => {
          this.addressingStore.searchForAddress(value);
        }, 180);
      } else {
        this.addressingStore.clearAddressSearchResults();
      }
    } else {
      // Suburb only search
      if (value.length > 0 && value !== undefined) {
        clearTimeout(this.suburbRequestTimeout);
        this.suburbRequestTimeout = setTimeout(() => {
          this.addressingStore.searchForSuburb(value);
        }, 180);
      } else {
        this.addressingStore.clearSuburbSearchResults();
      }
    }
  }

  get selectedViewData(): MenuData {
    const foundMatch = this.menuOptions.find(
      (item) => item.value === this.viewTypeString,
    );
    if (foundMatch) {
      return foundMatch;
    } else {
      return this.menuOptions[0];
    }
  }

  public menuItemSelected(value: string) {
    this.viewTypeString = value;
    if (value === 'nickname') {
      this.showNickNameSelect();
    }
    if (value === 'pindrop') {
      this.showPinDropInput();
    }
    if (value === 'manual') {
      this.showManualAddress();
    }
  }
  public addReturnToDefaultDispatchLocation() {
    this.$emit('addReturnToDefaultDispatchLocation');
  }

  public addPreloadLeg() {
    this.$emit('addPreloadLeg');
  }
  public addReturnToFirstPud() {
    this.$emit('addReturnToFirstPud');
  }

  // once address is selected, query backend for full address object
  public selectAddress(selectedAddress: ElasticSearchAddress): void {
    this.selectedAddressId = selectedAddress.id;
    this.addressingStore.getFullAddressFromAddressId(selectedAddress);
    if (this.selectedAddress) {
      this.selectedAddress.formattedAddress = selectedAddress.formattedAddress;
      this.selectedAddress.addressId = selectedAddress.id;
    }
  }

  // Watcher to handle changes to the selected address
  public onAddressChange(value: AddressAU): void {
    this.selectedAddress = value;
    this.selectNickName();
  }

  get selectedViewTypeHeading(): string {
    let selectedViewTypeHeading = '';
    switch (this.viewTypeString) {
      case 'search':
        selectedViewTypeHeading = 'Address Search';
        break;
      case 'nickname':
        selectedViewTypeHeading = 'Common Addresses';
        break;
      case 'manual':
        selectedViewTypeHeading = 'Manually Enter Address';
        break;
      case 'pindrop':
        selectedViewTypeHeading = 'Select on map';
        break;
    }
    return selectedViewTypeHeading;
  }

  get validate(): Validation {
    return validationRules;
  }

  get clonedAddress() {
    return this.address;
  }
  set clonedAddress(val: AddressAU) {
    this.address.addressId = val.addressId;
    this.address.formattedAddress = val.formattedAddress;
    this.address.addressLine1 = val.addressLine1;
    this.address.addressLine2 = val.addressLine2;
    this.address.addressLine3 = val.addressLine3;
    this.address.addressLine4 = val.addressLine4;
    this.address.suburb = val.suburb;
    this.address.postcode = val.postcode;
    this.address.city = val.city;
    this.address.state = val.state;
    this.address.country = val.country;
    this.address.geoLocation = val.geoLocation;
    if (this.enableSuburbSelect) {
      this.selectedSuburb.name = val.suburb;
      this.selectedSuburb.postcode = val.postcode;
      this.selectedSuburb.state = val.state;
    }
    if (this.addressIsRequired) {
      this.errorMessages = [];
    }
  }

  // ===========================================================================
  // NICKNAME SELECT
  // ===========================================================================

  // show nickname input for address when add nickname button is clicked
  public showNickNameInput(isShowing: boolean): void {
    this.nickNameInput = isShowing;
    if (isShowing && this.clientDetails.clientId) {
      this.clientCommonAddress = new ClientCommonAddress();
      this.clientCommonAddress.clientId = this.clientDetails.clientId;
      this.clientCommonAddress.address = this.address;
      this.clientCommonAddress.addressNickname = this.commonAddressNickname;
    } else {
      this.clientCommonAddress = null;
    }
  }

  get addressProp() {
    return this.address;
  }
  set addressProp(val) {
    const newVal = JSON.parse(JSON.stringify(val)) as ClientCommonAddress;
    this.clonedAddress = newVal.address;
    this.selectNickName();
  }

  public deleteNickName(): void {
    for (let i = 0; i < this.nickNameList.length; i++) {
      if (
        this.nickNameList[i].address.formattedAddress ===
        this.address.formattedAddress
      ) {
        this.nickNameList.splice(i, 1);
        this.clonedAddress = new AddressAU();
        if (this.nickNameList.length === 0) {
          this.searchAddress = !this.searchAddress;
        }
        return;
      }
    }
  }

  // savedNew is true if we cancelled after saving a Common Address
  public cancelJobNickname(savedNew: boolean = false) {
    this.nickNameInput = false;
    if (savedNew) {
      // If we saved a new Common Address then we should emit index to parent so
      // that we can populate the customer delivery name
      this.$emit('nicknameAddressSelected', this.returnIndexOfCommonAddress());
    }
  }

  // nickname selected. get index of selection and display persons for that location
  // Handles selecting a nickname
  public selectNickName(): void {
    const addressIndex = this.returnIndexOfCommonAddress();
    this.selectNicknamePerson = true;
    this.$emit('nicknameAddressSelected', addressIndex);
  }

  /**
   * Used to find the index of a common address in the nickNameList.
   * - First tries to match the addressId of the selected address with the
   *   addressIds in the nickNameList.
   * - If there is only one match, it returns the index of the matched address.
   * - If there is more than one result, it tries to match the formattedAddress
   *   property.
   * - If there is still no match, it shows a notification and returns -1.
   *
   * @returns {number} - The index of the matched address in the nickNameList,
   * or -1 if no match is found.
   */
  public returnIndexOfCommonAddress(): number {
    // Use addressId of selected address to find the index in the nickNameList
    const matchAddressId = this.nickNameList.filter((item) => {
      return item.address.addressId === this.address.addressId;
    });
    // If there is only one match, return the index of the matched address
    if (matchAddressId.length === 1) {
      return this.nickNameList.indexOf(matchAddressId[0]);
    }
    // If there is more than one result, we will try to use the formattedAddress property
    const matchFormattedAddress = this.nickNameList.findIndex((item) => {
      return item.address.formattedAddress === this.address.formattedAddress;
    });
    // Show notification if we still have no match
    if (matchFormattedAddress === -1) {
      showNotification('Something went wrong. Please try a different address');
    }
    return matchFormattedAddress;
  }

  // Cancel nickname
  public cancelNickName(): void {
    this.nickNameInput = false;
    this.searchAddress = true;
    this.pinDropAddress = false;
  }

  public showNickNameSelect(): void {
    this.viewTypeString = 'nickname';
  }

  // ===========================================================================
  // MANUAL ADDRESS
  // ===========================================================================
  /**
   * Show the manual address input form based on the current address data.
   * If a current address exists, it populates the manual address fields with the current address data.
   * Otherwise, it initializes the manual address fields with empty values.
   */
  public showManualAddress(): void {
    // Check if the clonedAddress has geoLocation data
    const hasCurrentAddress =
      this.clonedAddress.geoLocation &&
      this.clonedAddress.geoLocation.length &&
      this.clonedAddress.geoLocation[0] &&
      this.clonedAddress.geoLocation[1];

    if (hasCurrentAddress) {
      // Populate manual address with data from the existing address
      this.setManualAddressFromAddressAu(this.address);
      this.clonedAddress = Object.assign(new AddressAU(), this.address);
    } else {
      // Initialize manual address with empty values
      this.setManualAddressFromAddressAu(new AddressAU());
      (this.manualAddressObj.geoLocation as any) = [null, null];
      this.clonedAddress = new AddressAU();
    }

    // Set manual address mode and update other UI flags
    this.manualAddress = true;
    this.searchAddress = false;
    this.nickNameSelect = false;
  }

  /**
   * Cancel the manual address input and switch back to the search view.
   */
  public cancelManualAddress(): void {
    this.manualAddress = false;
    this.setManualAddressFromAddressAu(new AddressAU());
    this.viewTypeString = 'search';
  }

  /**
   * Clear the manual address fields by resetting addressId, formattedAddress,
   * and any other relevant form data.
   */
  public clearManualAddress(): void {
    this.manualAddressObj.addressId = '';
    this.manualAddressObj.formattedAddress = '';
    this.$refs.manualAddressRef.reset();
  }

  /**
   * Set the manual address fields based on the provided AddressAU object.
   * If the country is not defined, attempt to set it from the company details module.
   * @param address - The AddressAU object to populate the manual address fields with.
   */
  public setManualAddressFromAddressAu(address: AddressAU) {
    this.manualAddressObj = Object.assign(new AddressAU(), address);

    // Try to set the country from company details if it's not defined
    if (!this.manualAddressObj.country) {
      this.manualAddressObj.country = this.companyDetailsStore.companyDetails
        ? this.companyDetailsStore.companyDetails.address.country
        : '';
    }
  }

  /**
   * Create formatted address from individual input values
   */
  public saveManualAddress(): void {
    if (this.$refs.manualAddressRef.validate()) {
      // add formatted to manually entered address object
      this.manualAddressObj.formattedAddress =
        returnFormattedAddressFromAddressAU(this.manualAddressObj);
      // sync clonedAddress with manually entered address
      this.clonedAddress = this.manualAddressObj;
      this.manualAddress = false;
      this.searchAddress = true;
      this.nickNameSelect = true;
      this.viewTypeString = 'search';
    }
  }

  /**
   * Check if the supplied address already exists for the client
   * @return {boolean}
   */
  public isDuplicateClientCommonAddress(
    address_id: string | undefined,
    formattedAddress: string,
  ): boolean {
    const commonAddressAlreadyExists: ClientCommonAddress | undefined =
      useClientDetailsStore().clientCommonAddresses.find(
        (x: ClientCommonAddress) =>
          x.address.formattedAddress === formattedAddress &&
          x._id !== address_id,
      );
    return commonAddressAlreadyExists ? true : false;
  }

  /**
   * Saves a new common address for a client after validation checks. It verifies the address isn't a duplicate, displays notifications for any validation or duplication errors and manages loading state.
   *
   * @async
   * @throws {Error} Throws an error if the saving process fails.
   * @returns {Promise<void>} A promise that resolves when the saving process is complete.
   */
  public async saveClientCommonAddress(): Promise<void> {
    try {
      if (
        !validate(this.$refs.commonAddressMaintenanceRef) ||
        !this.clientCommonAddress ||
        !this.clientDetails.clientId
      ) {
        return;
      }
      // Validate that the new common address does not already exist.
      const isDuplicate: boolean = this.isDuplicateClientCommonAddress(
        this.clientCommonAddress._id,
        this.clientCommonAddress.address.formattedAddress,
      );
      if (isDuplicate) {
        showNotification(
          'A common address at ' +
            this.clientCommonAddress.address.formattedAddress +
            ' already exists for this client. Duplicate addresses are not permitted.',
          {
            title: 'Duplicate Common Address',
            type: HealthLevel.ERROR,
          },
        );
        return;
      }

      // No default contact is added from the booking screen.
      this.clientCommonAddress.defaultContact = null;
      this.clientCommonAddress.clientId = this.clientDetails.clientId;

      this.isLoadingClientCommonAddressSave = true;
      const request: ClientCommonAddressRelatedContactSave = {
        clientId: this.clientDetails.clientId,
        clientCommonAddress: this.clientCommonAddress,
        clientRelatedContact: null,
        relatedContactsAssociatedCommonAddressIds: [],
        relatedContactsDefaultCommonAddressIds: [],
        updatedClientCommonAddresses: [],
      };
      await saveCommonAddressAndRelatedContact(request);
      this.showNickNameInput(false);

      showNotification('Address Successfully added to client.', {
        title: 'Client Common Address',
        type: HealthLevel.INFO,
      });
      this.isLoadingClientCommonAddressSave = false;
    } catch (e: unknown) {
      console.error(e);
      showNotification('Something went wrong.', {
        title: 'Client Common Address',
        type: HealthLevel.ERROR,
      });
      this.showNickNameInput(false);
      this.isLoadingClientCommonAddressSave = false;
    }
  }

  // ===========================================================================
  // PIN DROP
  // ===========================================================================

  public showPinDropInput(): void {
    this.pinDropAddress = true;
    this.searchAddress = false;
    this.nickNameSelect = false;
  }

  public cancelPinDropInput(): void {
    this.pinDropAddress = false;
    this.searchAddress = false;
    this.nickNameSelect = false;
    this.viewTypeString = 'search';
  }

  public openManualAddress(): void {
    this.pinDropAddress = false;
    this.searchAddress = false;
    this.nickNameSelect = false;
    this.manualAddress = true;
    this.setManualAddressFromAddressAu(this.address);
    this.viewTypeString = 'manual';
  }

  get depotCoordinates() {
    const companyDetails = this.companyDetailsStore.companyDetails;
    const division = sessionManager.getDivisionId();
    if (!companyDetails || !companyDetails.divisions) {
      return null;
    }
    const divisionDetails = companyDetails.divisions.find(
      (div: any) => div.divisionId === division,
    );
    if (divisionDetails !== undefined) {
      return divisionDetails.depotAddress.geoLocation;
    } else {
      return null;
    }
  }

  // Handle suburb selection from the AddressSuburbSearch component
  public onSuburbSelected(suburb: SuburbAU): void {
    this.selectedSuburb = suburb;
    if (suburb) {
      this.addressingStore.updateAddressSearchResults([]);
      const addressFromSuburb = new AddressAU();
      addressFromSuburb.fromSuburb(suburb);
      this.clonedAddress = addressFromSuburb;

      if (this.viewTypeString === 'manual') {
        this.manualAddressObj.suburb = suburb.name;
        this.manualAddressObj.state = suburb.state;
        this.manualAddressObj.postcode = suburb.postcode;
        this.manualAddressObj.geoLocation = suburb.geoLocation;
      }
    }
  }

  public created() {
    if (!this.addressProp.addressId) {
      this.searchAddress = true;
    } else {
      this.addressId = this.addressProp.addressId;
    }
    this.viewTypeString = 'search';
    this.addressingStore.clearSuburbSearchResults();
    this.addressingStore.clearAddressSearchResults();
  }
}
