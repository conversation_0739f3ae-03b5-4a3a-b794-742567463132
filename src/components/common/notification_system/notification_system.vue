<template>
  <section class="notification-card">
    <notifications
      group="alertsGeneric"
      :position="genericNotificationPosition"
      style="margin: 10px"
      classes="toast-notification-custom generic-type"
      :width="genericNotificationWidth"
    />
    <notifications
      group="jobBooking"
      position="bottom center"
      style="cursor: pointer"
      classes="toast-notification-custom job-type"
      :width="jobNotificationWidth"
    />

    <v-dialog
      v-model="showDialogNotificationDialog"
      v-if="showDialogNotificationDialog"
      persistent
      :width="400"
      class="ma-0 v-dialog-custom"
    >
      <v-card color="#242329">
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight"
        >
          <div>
            <span>Attention</span>
            <span v-if="isCompanyWideNotification">
              - Company Notification
            </span>
          </div>
        </v-layout>
        <v-layout row wrap class="px-4 pt-4 pb-2">
          <v-flex md12>
            <ul v-if="!isCompanyWideNotification">
              <li v-for="(message, index) of dialogNotification" :key="index">
                {{ message }}
              </li>
            </ul>
            <p v-if="isCompanyWideNotification">{{ dialogNotification }}</p>
          </v-flex>
        </v-layout>
        <v-divider class="mt-2"></v-divider>
        <v-layout justify-end>
          <v-btn color="info" depressed small @click="clearDialogNotification"
            >Okay</v-btn
          >
        </v-layout>
      </v-card>
    </v-dialog>
  </section>
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  onMounted,
  onBeforeUnmount,
  watch,
  getCurrentInstance,
} from 'vue';
import { useRootStore } from '@/store/modules/RootStore';
import Mitt from '@/utils/mitt';
import NotificationMessage from '@/interface-models/Generic/NotificationMessage/NotificationMessage';
import { useAppNavigationStore } from '@/store/modules/AppNavigationStore';
import { useMittListener } from '@/utils/useMittListener';

const showDialogNotificationDialog = ref(false);

const genericNotificationWidth = ref('500');
const genericNotificationPosition = ref('top right');
const jobNotificationWidth = ref('450');

const rootStore = useRootStore();

const { proxy } = getCurrentInstance()!;

/**
 * Sets a notification alert using the $notify method.
 * @param {NotificationMessage} notification - The notification message object.
 */
function setNotificationAlert(notification: NotificationMessage | null) {
  if (!notification) {
    return;
  }
  useAppNavigationStore().setNotification(notification);
  proxy.$notify(notification);
}

/**
 * Computed property to determine if the notification is company-wide.
 * @returns {boolean} - True if the notification is company-wide, false
 * otherwise.
 */
const isCompanyWideNotification = computed(
  () => rootStore.isCompanyWideNotification,
);

/**
 * Sets the styles for the notification based on the window width.
 */
function setNotificationStyles() {
  genericNotificationWidth.value =
    window && window.innerWidth && window.innerWidth > 600 ? '500' : '350';
  genericNotificationPosition.value =
    window && window.innerWidth && window.innerWidth > 600
      ? 'top right'
      : 'bottom center';
  jobNotificationWidth.value =
    window && window.innerWidth && window.innerWidth > 600 ? '450' : '350';
}

/**
 * Computed property to get the dialog notifications.
 * @returns {Array} - An array of dialog notifications.
 */
const dialogNotification = computed(() => {
  const messages = rootStore.dialogNotification;
  if (messages.length <= 0) {
    return [];
  }
  return isCompanyWideNotification.value ? messages[0] : messages;
});

/**
 * Watches the dialogNotification computed property and shows the dialog notification if there are messages.
 * @param {Array} messages - The array of dialog notification messages.
 */
watch(dialogNotification, (messages) => {
  showDialogNotificationDialog.value = messages.length > 0;
});

/**
 * Clears the dialog notifications in the root store.
 */
function clearDialogNotification() {
  rootStore.setDialogNotification([]);
}

useMittListener('setNotification', setNotificationAlert);

onMounted(() => {
  setNotificationStyles();
  window.addEventListener('resize', setNotificationStyles);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', setNotificationStyles);
});
</script>
<style scoped lang="scss">
.notification-card {
  .notification-comp {
    z-index: 100000000000 !important;
    position: fixed;
  }
}
</style>
