<template>
  <div class="header-container">
    <!-- Title & Buttons Row -->
    <v-layout justify-space-between align-center class="">
      <!-- Title Slot -->
      <div v-if="showTitle">
        <slot name="title" />
      </div>

      <!-- Buttons Slot -->
      <div class="d-flex mr-4" v-if="showButtons">
        <slot name="buttons" />
      </div>
    </v-layout>

    <v-divider md12 v-if="showDivider" />

    <!-- Inputs Row -->
    <v-layout d-flex v-if="showInputs" class="mt-2" align-center>
      <slot name="inputs" />
    </v-layout>
  </div>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    showInputs?: boolean;
    showButtons?: boolean;
    showTitle?: boolean;
    showDivider?: boolean;
  }>(),
  {
    showInputs: true,
    showButtons: true,
    showTitle: true,
    showDivider: true,
  },
);
</script>
