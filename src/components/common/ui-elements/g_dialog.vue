<template>
  <dialog class="dialog-container" ref="dialog" @cancel.prevent>
    <NotificationSystem class="notification-system" />
    <div
      class="dialog-content-container"
      :style="{ width: props.width + 'px' }"
      :id="dialogContentId"
    >
      <div class="dialog-header-container">
        {{ title }}
        <div
          class="header-close-container"
          @click="closeDialog"
          :class="{ 'disable-pointer': isLoading || cancelDisabled }"
        >
          <v-icon
            :disabled="isLoading || cancelDisabled"
            class="app-theme__center-content--closebutton--icon"
            size="16"
            >fal fa-times</v-icon
          >
        </div>
      </div>
      <div class="dialog-content">
        <slot></slot>
      </div>
      <div class="dialog-actions-container" v-if="props.isActionable">
        <GButton
          class="action-btn"
          :color="'error'"
          v-if="isDelete"
          outlined
          :disabled="props.deleteDisabled || isLoading"
          @click="emitDeleteItem"
          >{{ deleteBtnText }}</GButton
        >

        <GButton
          class="action-btn"
          :color="'error'"
          v-if="!isDelete"
          outlined
          :disabled="props.cancelDisabled || isLoading"
          @click="emitCloseDialog"
          >{{ props.cancelBtnText }}</GButton
        >
        <GButton
          :color="'info'"
          @click="emitConfirm"
          :disabled="props.confirmDisabled || isLoading"
          class="action-btn"
          :isLoading="isLoading"
          >{{ props.confirmBtnText }}</GButton
        >
      </div>
    </div>
  </dialog>
</template>

<script setup lang="ts">
import { ref, Ref, onMounted, onUnmounted } from 'vue';
import { v4 as uuidv4 } from 'uuid';
import NotificationSystem from '@/components/common/notification_system/notification_system.vue';
interface IProps {
  title: string;
  width?: string;
  confirmBtnText?: string;
  cancelBtnText?: string;
  isLoading?: boolean;
  cancelDisabled?: boolean;
  deleteDisabled?: boolean;
  isDelete?: boolean;
  confirmDisabled?: boolean;
  deleteBtnText?: string;
  isActionable?: boolean;
}
const props = withDefaults(defineProps<IProps>(), {
  title: '',
  width: '500px',
  isLoading: false,
  deleteBtnText: 'Delete',
  cancelBtnText: 'Cancel',
  isDelete: false,
  confirmDisabled: false,
  confirmBtnText: 'Confirm',
  cancelDisabled: false,
  isActionable: true,
});

const dialogContentId: Ref<string> = ref(uuidv4().replace(/-/g, ''));
const emit = defineEmits(['closeDialog', 'confirm', 'deleteItem']);
const dialog: Ref<HTMLDialogElement | null> = ref(null);
function closeDialog() {
  if (dialog.value) {
    dialog.value.close();
  }
  emit('closeDialog');
}

function emitDeleteItem() {
  emit('deleteItem');
}

function emitCloseDialog() {
  emit('closeDialog');
}

function emitConfirm() {
  emit('confirm');
}

onMounted(() => {
  if (dialog.value) {
    dialog.value.showModal();
  }
  handleResize();
});

function handleResize(): void {
  const width = window.innerWidth;
  const dialogContentElement: HTMLElement | null = document.getElementById(
    dialogContentId.value,
  ) as HTMLElement;
  if (!dialogContentElement) {
    return;
  }
  if (width >= 950) {
    dialogContentElement.style.width = props.width;
  } else {
    dialogContentElement.style.width = '95vw';
  }
}

onMounted(() => {
  handleResize();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped lang="scss">
.dialog-container {
  background-color: rgba(0, 1, 18, 0.8);
  width: 100%;
  height: 100%;
  max-height: 100%;
  max-width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: $border-radius-base;

  .dialog-header-container {
    border-radius: 10px 10px 0 0 !important;
    grid-area: header;
    display: flex;
    align-items: center;
    height: 38px;
    background-color: var(--background-color-200);
    padding: 6px 8px;
    font-size: 1em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    justify-content: space-between;
    color: var(--text-color);
    border-bottom: 1px solid $translucent;
    .header-close-container {
      border-radius: 10px !important;
      background-color: var(--background-color-200);
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 0px 26px;
      position: absolute;
      top: 0px;
      right: 0px;
      &:hover {
        background-color: var(--background-color-300);
        cursor: pointer;
      }
    }
  }

  .dialog-content-container {
    border: 2px solid $translucent !important;
    border-radius: 10px !important;
    max-height: 95vh;
    display: grid;
    color: var(--text-color);
    background-color: var(--background-color-400);
    border: 1px solid var(--border-color);
    grid-template-rows: $app-bar-height 1fr 108px;
    grid-template-areas:
      'header'
      'content'
      'actions';
    .dialog-actions-container {
      border-radius: 0 0 10px 10px !important;
      grid-area: actions;
      padding: 0 12px;
      background-color: var(--background-color-300);

      .action-btn {
        margin: 12px 2px 14px 2px;
        width: 100%;
      }
    }
  }

  .dialog-content {
    grid-area: content;
    overflow-y: auto;
    border-top: 1px solid $translucent;
    border-bottom: 1px solid $translucent;
  }
}

@media (min-width: $user-portal-desktop-breakpoint) {
  .dialog-container {
    .dialog-content-container {
      grid-template-rows: 48px 1fr;
      .dialog-actions-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .action-btn {
          width: initial;
        }
      }
    }
  }
}
</style>
