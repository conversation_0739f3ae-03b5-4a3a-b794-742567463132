<template>
  <v-flex md12>
    <v-layout>
      <v-flex md4>
        <v-layout align-center class="form-field-label-container">
          <template v-if="slots.label">
            <slot name="label" />
          </template>
          <template v-else>
            <h6
              class="subheader--faded pr-3 pb-0"
              :class="[
                labelClass,
                { 'form-field-required-marker': showRequiredMarker },
              ]"
            >
              {{ label }}
            </h6>
          </template>
        </v-layout>
      </v-flex>
      <v-flex md8>
        <slot />
      </v-flex>
    </v-layout>
  </v-flex>
</template>

<script setup lang="ts">
import { defineProps, withDefaults, computed, useSlots } from 'vue';

/**
 * Props for FieldRow:
 * - label: The label text to display.
 * - required: Whether to show the required marker.
 * - labelClass: Additional classes for the label span.
 */
const props = withDefaults(
  defineProps<{
    label?: string;
    required?: boolean;
    labelClass?: string;
  }>(),
  {
    label: '',
    required: false,
    labelClass: '',
  },
);

const showRequiredMarker = computed(() => props.required);
const slots = useSlots();
</script>
