<template>
  <v-dialog
    v-model="showDialog"
    :width="dialogWidth"
    class="ma-0"
    :style="isListTile ? { display: 'inline' } : { display: 'inline-block' }"
    content-class="v-dialog-custom"
  >
    <template v-slot:activator="{ on }">
      <v-icon
        v-if="isIcon"
        size="14"
        :disabled="showDialog || buttonDisabled"
        class="icon-hover--primary"
        v-on="on"
      >
        {{ faIconName }}</v-icon
      >
      <v-list-tile
        dense
        v-if="isListTile"
        :disabled="showDialog || buttonDisabled"
        v-on="on"
      >
        <v-list-tile-avatar v-if="useLeadingIcon && faIconName">
          <v-icon size="16">{{ faIconName }}</v-icon>
        </v-list-tile-avatar>
        <v-list-tile-title class="pr-2"> {{ buttonText }} </v-list-tile-title>
      </v-list-tile>
    </template>
    <v-flex md12>
      <v-layout
        justify-space-between
        align-center
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>{{ title }}</span>
        <div class="app-theme__center-content--closebutton" @click="cancel">
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <v-layout row wrap class="app-theme__center-content--body dialog-content">
        <v-flex md12 v-if="message">
          <p>{{ message }}</p>
        </v-flex>
        <v-flex md12>
          <v-form ref="formRef">
            <v-layout
              row
              wrap
              class="body-scrollable--85 body-min-height--75"
              px-3
            >
              <v-flex md12>
                <v-layout
                  justify-start
                  v-for="settingGroup in groupedSettingsList"
                  :key="settingGroup.id"
                >
                  <v-flex md12 v-if="settingGroup.isRadio">
                    <p class="filterTitle pt-2">View</p>
                    <v-layout>
                      <v-radio-group v-model="settingGroup.selectedRadioId">
                        <v-radio
                          v-for="settingItem in settingGroup.settings"
                          :key="settingItem.id"
                          :label="settingItem.longName"
                          :value="settingItem.id"
                          color="blue lighten-1"
                        ></v-radio>
                      </v-radio-group>
                    </v-layout>
                    <v-layout>
                      <v-divider
                        v-if="settingGroup.isRadio"
                        class="py-1"
                      ></v-divider>
                    </v-layout>
                  </v-flex>

                  <v-flex
                    md12
                    v-if="!settingGroup.isRadio && settingGroup.settings[0]"
                  >
                    <div
                      class="pt-2"
                      v-if="
                        settingGroup.settings[0].id === 'showInternalDrivers'
                      "
                    >
                      <p class="filterTitle pt-2">View</p>
                    </div>
                    <div
                      class="pt-2"
                      v-if="settingGroup.settings[0].id === 'showOnlineDrivers'"
                    >
                      <v-divider></v-divider>
                      <p class="filterTitle pt-2">
                        {{ filterTitle }}
                      </p>
                    </div>
                    <v-checkbox
                      v-model="settingGroup.settings[0].active"
                      hide-details
                      :label="settingGroup.settings[0].longName"
                    ></v-checkbox>
                    <SelectEntity
                      v-if="
                        settingGroup.settings[0].active &&
                        allowExclusions &&
                        allowedSettingsIdsForExclusions.includes(
                          settingGroup.settings[0].id,
                        )
                      "
                      class="mt-2"
                      placeholder="Exclude Drivers"
                      hint="Select any drivers you wish to not show notifications for"
                      :entityTypes="[entityType.DRIVER]"
                      :multiple="true"
                      :ids.sync="settingGroup.settings[0].excludeIds"
                      :suggestFromCurrentJobs="false"
                    />
                  </v-flex>
                </v-layout>
              </v-flex>
            </v-layout>
          </v-form>
        </v-flex>

        <v-flex md12>
          <v-divider class="mt-2"></v-divider>
          <v-layout justify-space-between>
            <v-btn depressed color="error" outline @click="cancel"
              >Cancel</v-btn
            >
            <v-btn depressed plain flat @click="restoreDefaults"
              >Restore Default</v-btn
            >
            <v-btn depressed color="blue" @click="confirm">Save Changes</v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>
  </v-dialog>
</template>

<script setup lang="ts">
import SelectEntity from '@/components/common/inputs/select_entity/index.vue';
import { EntityType } from '@/interface-models/Generic/EntityType';
import OperationsDashboardSetting from '@/interface-models/Generic/OperationScreenOptions/OperationsDashboardSetting';
import { computed, onMounted, ref, Ref, WritableComputedRef } from 'vue';

interface SettingGroup {
  id: number;
  isRadio: boolean;
  selectedRadioId: string;
  settings: OperationsDashboardSetting[];
}

const props = withDefaults(
  defineProps<{
    title?: string;
    message?: string;
    buttonText?: string;
    buttonColor?: string;
    faIconName?: string;
    isIcon?: boolean;
    buttonDisabled?: boolean;
    isListTile?: boolean;
    useLeadingIcon?: boolean;
    allowExclusions?: boolean;
    dialogWidth?: number;
    settingsList?: OperationsDashboardSetting[];
    defaultSettingsList?: OperationsDashboardSetting[];
    filterTitle?: string;
  }>(),
  {
    title: '',
    message: '',
    buttonText: '',
    buttonColor: 'info',
    buttonDisabled: false,
    dialogWidth: 500,
    faIconName: '',
    isIcon: false,
    isListTile: false,
    useLeadingIcon: false,
    allowExclusions: false,
    settingsList: undefined,
    defaultSettingsList: undefined,
    filterTitle: '',
  },
);

const entityType = EntityType;
const isViewingDialog: Ref<boolean> = ref(false);
const groupedSettingsList: Ref<SettingGroup[]> = ref([]);
const emit = defineEmits(['confirm']);

const allowedSettingsIdsForExclusions = [
  'showAppNotifications',
  'showAppRestartNotifications',
  'showGpsDisabledNotifications',
];

const showDialog: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return isViewingDialog.value;
  },
  set(value: boolean): void {
    if (value) {
      setGroupedSettingsList();
    } else {
      groupedSettingsList.value = [];
    }
    isViewingDialog.value = value;
  },
});

function cancel(): void {
  showDialog.value = false;
}

function confirm(): void {
  groupedSettingsList.value.forEach((gsl) => {
    if (gsl.isRadio) {
      const selectedId = gsl.selectedRadioId;
      gsl.settings.forEach((setting) => {
        setting.active = setting.id === selectedId;
      });
    }
  });
  const settingsList = groupedSettingsList.value.flatMap((gsl) => gsl.settings);
  settingsList.forEach((s) => {
    if (!s.active) {
      delete s.excludeIds;
    }
  });
  emit('confirm', settingsList);
  showDialog.value = false;
}

function restoreDefaults(): void {
  // Clear the current settings in groupedSettingsList and replace them with defaults
  const defaultSettingsList = props.defaultSettingsList;
  groupedSettingsList.value.forEach((gsl) => {
    gsl.settings.forEach((setting) => {
      const defaultSetting = defaultSettingsList?.find(
        (ds) => ds.id === setting.id,
      );
      if (defaultSetting) {
        setting.active = defaultSetting.active;
        setting.excludeIds = defaultSetting.excludeIds;
      }
    });
  });
  // Emit the default settings list
  emit('confirm', defaultSettingsList);
}

function setGroupedSettingsList(): void {
  if (props.settingsList) {
    const settingsList = [
      ...props.settingsList.map((s) => {
        return { ...s };
      }),
    ];
    const uniqueGroups = [...new Set(settingsList.map((s) => s.groupId))];
    const settingGroups: SettingGroup[] = [];
    for (let i = 0; i < uniqueGroups.length; i++) {
      const groupId = uniqueGroups[i];
      const group = settingsList.filter(
        (setting) => setting.groupId === groupId,
      );
      if (group && group.length > 0) {
        const isRadio = group.length > 1;
        let selectedRadioId = '';
        if (isRadio) {
          const foundActive = group.find((j) => j.active);
          if (foundActive) {
            selectedRadioId = foundActive.id;
          }
        }
        const settingGroup: SettingGroup = {
          id: groupId,
          isRadio,
          selectedRadioId,
          settings: group,
        };
        settingGroups.push(settingGroup);
      }
    }
    groupedSettingsList.value = settingGroups;
  }
}

onMounted(() => {
  if (!props.settingsList) {
    return;
  }
});
</script>

<style scoped lang="scss">
.operations-settings-dialog {
  padding: 0px;
}

.filterTitle {
  font-size: $font-size-18;
  margin: 1px !important;
  color: $bg-light;
  position: relative;
  font-family: $font-awesome-family;
}
</style>
