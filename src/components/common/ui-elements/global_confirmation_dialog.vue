<template>
  <v-dialog
    v-model="showDialog"
    :width="dialogWidth"
    class="ma-0"
    :content-class="
      isClientPortal ? 'v-dialog-custom client-portal' : 'v-dialog-custom'
    "
  >
    <v-card class="v-card-custom">
      <!-- Header -->
      <v-layout
        justify-space-between
        align-center
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span :class="{ 'client-portal-confirmation-dialog': isClientPortal }">
          {{ title }}
        </span>
        <div class="app-theme__center-content--closebutton" @click="cancel">
          <v-icon
            class="app-theme__center-content--closebutton--icon"
            :color="isClientPortal ? buttonColor : ''"
          >
            fal fa-times
          </v-icon>
        </div>
      </v-layout>

      <!-- Content -->
      <v-layout row wrap class="px-4 pt-4">
        <v-flex md12>
          <p :class="{ 'client-portal-confirmation-dialog': isClientPortal }">
            {{ message }}
          </p>
        </v-flex>
      </v-layout>

      <v-divider class="mt-2"></v-divider>

      <!-- Actions -->
      <v-layout justify-space-between>
        <v-btn depressed :color="cancelButtonColor" flat @click="cancel">
          {{ cancelButtonText }}
        </v-btn>
        <v-btn
          depressed
          :color="confirmationButtonColor"
          class="v-btn-confirm-custom"
          @click="confirm"
        >
          {{ confirmationButtonText }}
        </v-btn>
      </v-layout>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { useMittListener } from '@/utils/useMittListener';
import { ref } from 'vue';

const showDialog = ref(false);
const title = ref('');
const message = ref('');
let resolver: ((result: boolean) => void) | null = null;

const dialogWidth = 400;
const confirmationButtonText = 'Confirm';
const cancelButtonText = 'Cancel';
const confirmationButtonColor = 'blue';
const cancelButtonColor = 'error';
const buttonColor = 'info';
const isClientPortal = false;

function confirm() {
  if (resolver) {
    resolver(true);
  }
  reset();
}

function cancel() {
  if (resolver) {
    resolver(false);
  }
  reset();
}

function reset() {
  showDialog.value = false;
  title.value = '';
  message.value = '';
  resolver = null;
}

function handleEvent(
  payload: {
    message: string;
    title?: string;
    resolve: (result: boolean) => void;
  } | null,
) {
  if (!payload) {
    return;
  }
  title.value = payload.title ?? 'Confirm';
  message.value = payload.message;
  resolver = payload.resolve;
  showDialog.value = true;
}

useMittListener('showInConfirmationDialog', handleEvent);
</script>

<style scoped lang="scss">
.client-portal-confirmation-dialog {
  color: white;
}

.v-card-custom {
  border-radius: 0px !important;
  background-color: var(--background-color-300) !important;
}
</style>
