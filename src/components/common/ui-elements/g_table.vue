<template>
  <div>
    <div class="table-header-container pb-1">
      <GTitle :title="title" :subtitle="subtitle" :divider="false" />
      <slot name="action"> </slot>
    </div>
    <div class="table-container" :style="{ height: height ? height : '100%' }">
      <GProgressBar v-if="isLoading" class="progress-component" />
      <table class="data-table">
        <thead>
          <tr>
            <th
              v-for="header in filteredHeaders"
              :style="{ textAlign: header.align }"
              :key="header.text"
            >
              {{ header.text.toUpperCase() }}
            </th>
          </tr>
        </thead>
        <tbody>
          <template v-if="isSlot">
            <tr
              @click="selectItem(item)"
              v-for="(item, itemIndex) of items"
              :key="itemIndex"
              :class="selectable ? 'row-hover' : ''"
            >
              <slot name="items" :item="item"></slot>
            </tr>
          </template>
          <template v-if="!isSlot">
            <tr
              v-for="(row, rowIndex) of rows"
              :key="rowIndex"
              @click="selectItemAtIndex(rowIndex)"
              :class="selectable ? 'row-hover' : ''"
            >
              <td
                v-for="(column, columnIndex) of row"
                :key="columnIndex"
                :style="{
                  textAlign: headers[columnIndex].align,
                }"
              >
                {{ column }}
              </td>
            </tr>
          </template>
        </tbody>
      </table>
      <div class="no-data" :id="noDataContainerId" v-if="rows.length === 0">
        <span :class="[{ 'error-text animate-wiggle': noDataErrorMessage }]">{{
          noDataMessage
        }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { v4 as uuidv4 } from 'uuid';
import { computed, ComputedRef, getCurrentInstance, ref, Ref } from 'vue';

const instance: any = getCurrentInstance();
const emit = defineEmits(['selectItem']);
interface IProps {
  title?: string;
  subtitle?: string;
  selectable?: boolean;
  noDataMessage?: string;
  headers: TableHeader[];
  isLoading?: boolean;
  height?: string;
  items: any[];
  dataRequired?: boolean;
  customErrorMessage?: string;
  customValidator?: () => boolean;
  search?: string;
}
const props = withDefaults(defineProps<IProps>(), {
  title: '',
  subtitle: '',
  noDataMessage: 'No Data available.',
  selectable: false,
  height: '100%',
  isLoading: false,
  dataRequired: false,
  customErrorMessage: '',
  customValidator: () => true,
  headers: () => [],
  items: () => [],
  search: '',
});

const noDataErrorMessage: Ref<string> = ref('');

const noDataContainerId: string = uuidv4().replace(/-/g, '');

const noDataMessage: ComputedRef<string> = computed(
  () => noDataErrorMessage.value || props.noDataMessage,
);

/**
 * Emits an event to select an item if selection is enabled and no text is currently selected.
 * This function checks if item selection is enabled through `props.selectable`. If so, it
 * verifies that no text is currently selected on the page before emitting a 'selectItem' event
 * with the specified item.
 *
 * @param {any} item - The item to be selected. type any because the table row can hold any type.
 */
function selectItem(item: any) {
  if (!props.selectable) {
    return;
  }
  const selection: Selection | null = window.getSelection();
  if (selection && selection.toString()) {
    return;
  }
  emit('selectItem', item);
}

/**
 * Emits an event to select an item at a specified index if selection is enabled and no text
 * is currently selected. Similar to `selectItem`, but selects the item based on its index in
 * `props.items`. It checks if item selection is enabled and no text selection exists before
 * emitting the 'selectItem' event.
 *
 * @param {number} index - The index of the item in `props.items` to be selected.
 */
function selectItemAtIndex(index: number) {
  if (!props.selectable) {
    return;
  }
  const selection: Selection | null = window.getSelection();
  if (selection && selection.toString()) {
    return;
  }
  emit('selectItem', props.items[index]);
}

/**
 * A computed property that determines whether the table's `td` elements are provided through a slot.
 * It checks if the `items` slot is defined in the current component instance. This is useful for
 * conditionally rendering default content or slot content in a table component.
 *
 * @type {ComputedRef<boolean>} isSlot - Returns `true` if the `items` slot is defined, otherwise `false`.
 */
const isSlot: ComputedRef<boolean> = computed(() => {
  if (!instance) {
    return false;
  }
  return instance.proxy.$scopedSlots.items ? true : false;
});

const rows: ComputedRef<string | number[][]> = computed(() => {
  const rows: any[] = [];
  for (const item of props.items) {
    const columns: (string | number)[] = [];
    for (const header of filteredHeaders.value) {
      if (item[header.value]) {
        columns.push(item[header.value]);
      } else {
        columns.push('-');
      }
    }
    rows.push(columns);
  }
  return rows;
});

/**
 * A computed property that filters `headers` based on their visibility. Headers are included
 * in the result if they are explicitly marked as visible (`visible: true` or `visible` is undefined)
 * and not marked as hidden (`hidden: true`).
 *
 * @type {ComputedRef<TableHeader[]>} filteredHeaders
 */
const filteredHeaders: ComputedRef<TableHeader[]> = computed(() => {
  return props.headers.filter((x: TableHeader) => {
    const isVisible = x.visible === undefined || x.visible === true;
    const isHidden = x.hidden !== undefined && x.hidden === true;
    return isVisible && !isHidden;
  });
});

// validate is required on input components due to how we handle form validation. This function will be called from a parent.
function validate(): boolean {
  if (!instance || !props.dataRequired) {
    return false;
  }
  const containerEl: HTMLElement | null =
    document.getElementById(noDataContainerId);
  const spanEl = containerEl ? containerEl.getElementsByTagName('span') : null;

  // remove animation class
  if (spanEl && spanEl.length === 1) {
    spanEl[0].classList.remove('animate-wiggle');
  }

  let hasErrors: boolean = false;
  const isValid: boolean | string =
    props.items.length > 0 && props.customValidator();

  if (isValid) {
    noDataErrorMessage.value = '';
    hasErrors = false;
  } else {
    noDataErrorMessage.value =
      props.customErrorMessage || 'At least one entry required.';
    hasErrors = true;
  }

  // add animation class
  if (spanEl && spanEl.length === 1 && hasErrors) {
    setTimeout(() => {
      spanEl[0].classList.add('animate-wiggle');
    }, 1);
  }

  return !hasErrors;
}

defineExpose({
  validate,
  noDataErrorMessage,
});
</script>
<style scoped lang="scss">
.table-container {
  background-color: var(--background-color-300);
  border-radius: $border-radius-base;
  border: 2px solid var(--border-color);
  overflow: auto;
  position: relative;
  color: var(--text-color);
  min-height: 300px;
}

.table-header-container {
  display: flex;
  justify-content: space-between;
}

.row-hover {
  cursor: pointer;
  &:hover {
    background-color: var(--background-color-600);
  }
}
.data-table {
  border-collapse: collapse;
  border-radius: $border-radius-base;
  width: 100%;

  thead {
    border-bottom: 1px solid var(--border-color);
    background-color: var(--background-color-300);
    height: 55px;
    th {
      font-weight: 500;
      padding: 8px 4px;
      font-size: $font-size-12;
      height: 100%;
      color: var(--text-color);
      background-color: var(--background-color-300);
      box-shadow: 0px 0px 0 2px $shadow-color;
      position: sticky;
      top: 0;
      z-index: 1;
    }
    th:first-child {
      padding-left: 12px;
    }
    th:last-child {
      padding-right: 12px;
    }
  }
  tbody {
    tr {
      background-color: var(--table-bg-100);
      outline: 1px solid var(--border-color-100);
      border-top: 2px solid var(--border-color-100);
    }
    tr {
      height: 48px;
      td {
        padding: 8px 4px;
        font-size: $font-size-11;
      }
    }
    td:first-child {
      padding-left: 12px;
    }
    td:last-child {
      padding-right: 12px;
    }
  }
}

.no-data {
  width: 100%;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 0px 8px 4px;
  text-align: center;
  background-color: $translucent-bg;
  font-size: $font-size-11;
}

.progress-component {
  position: absolute;
  left: 0;
  top: 56px;
  bottom: 150px;
  width: 100%;
  z-index: 200;
}

@media (min-width: $user-portal-desktop-breakpoint) {
}
</style>
