<template>
  <div class="progress-container">
    <div class="progress-bar">
      <div class="indeterminate"></div>
    </div>
  </div>
</template>

<script setup lang="ts"></script>
<style scoped lang="scss">
@media (min-width: $user-portal-desktop-breakpoint) {
}

$colors: (
  primary: $primary,
  secondary: $secondary,
  accent: $accent,
  error: $error,
  info: $info,
  success: $success,
  warning: $warning,
  text-color: $light-text-color,
  text-color-dark: $text-color-dark,
);

.progress-bar {
  // margin: 5px 0;
  position: relative;
  height: 4px;
  display: block;
  width: 100%;
  background-color: lighten(map-get($colors, primary), 35%);
  // margin: 0.5rem 0 1rem 0;
  overflow: hidden;
  .indeterminate {
    background-color: map-get($colors, primary);
    &:before {
      content: '';
      position: absolute;
      background-color: inherit;
      top: 0;
      left: 0;
      bottom: 0;
      will-change: left, right;
      animation: indeterminate 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395)
        infinite;
    }
    &:after {
      content: '';
      position: absolute;
      background-color: inherit;
      top: 0;
      left: 0;
      bottom: 0;
      will-change: left, right;
      animation: indeterminate-short 2.1s cubic-bezier(0.165, 0.84, 0.44, 1)
        infinite;
      animation-delay: 1.15s;
    }
  }
}

@keyframes indeterminate {
  0% {
    left: -35%;
    right: 100%;
  }
  60% {
    left: 100%;
    right: -90%;
  }
  100% {
    left: 100%;
    right: -90%;
  }
}

@keyframes indeterminate-short {
  0% {
    left: -200%;
    right: 100%;
  }
  60% {
    left: 107%;
    right: -8%;
  }
  100% {
    left: 107%;
    right: -8%;
  }
}
</style>
