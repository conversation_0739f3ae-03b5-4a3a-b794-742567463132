<template>
  <div class="loading-spinner">
    <div class="spinner" :class="props.darkSpinner ? 'dark-spinner' : ''"></div>
  </div>
</template>

<script setup lang="ts">
interface IProps {
  darkSpinner?: boolean;
}
const props = withDefaults(defineProps<IProps>(), {
  darkSpinner: false,
});
</script>

<style scoped lang="scss">
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.spinner {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid var(--text-color);
  border-top: 2px solid transparent;
  border-right: 2px solid transparent;
  animation: spin 1s linear infinite;
}

.dark-spinner {
  border-radius: 50%;
  border: 2px solid var(--text-color-dark);
  border-top: 2px solid transparent;
  border-right: 2px solid transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
