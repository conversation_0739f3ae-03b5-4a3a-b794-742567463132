<template>
  <v-tooltip
    :bottom="bottom"
    :right="right"
    :left="left"
    :top="top"
    :attach="attach"
  >
    <template v-slot:activator="{ on }">
      <v-icon
        :color="tooltipType"
        v-on="on"
        :size="iconSize"
        style="cursor: pointer"
        :class="[activatorClass ? activatorClass : '']"
        >fas fa-info-circle</v-icon
      >
    </template>
    <v-layout :style="{ maxWidth }">
      <v-flex md12>
        <slot name="content"></slot>
      </v-flex>
    </v-layout>
  </v-tooltip>
</template>

<script setup lang="ts">
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';

const props = withDefaults(
  defineProps<{
    bottom?: boolean;
    right?: boolean;
    left?: boolean;
    top?: boolean;
    attach?: boolean;
    tooltipType?: HealthLevel;
    activatorClass?: string;
    maxWidth?: string;
    iconSize?: string;
  }>(),
  {
    bottom: false,
    right: false,
    left: false,
    top: false,
    attach: false,
    tooltipType: HealthLevel.INFO,
    activatorClass: '',
    maxWidth: '300px',
    iconSize: '16',
  },
);
</script>
