<template>
  <div class="title-custom-container">
    <div class="title-custom" px-2>
      <div class="text-container">
        <h3>{{ props.title.toUpperCase() }}</h3>
        <h4>{{ props.subtitle }}</h4>
      </div>
      <div class="extra-content">
        <slot name="right-aligned-content"></slot>
      </div>
    </div>
    <hr class="divider" v-if="divider" />
  </div>
</template>

<script setup lang="ts">
interface IProps {
  title?: string;
  subtitle?: string;
  divider?: boolean;
}
const props = withDefaults(defineProps<IProps>(), {
  title: '',
  subtitle: '',
  divider: true,
});
</script>

<style scoped lang="scss">
.title-custom-container {
  width: 100%;
  color: var(--text-color);
  .title-custom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      display: block;
      padding-left: 8px;
    }
    .text-container {
      display: flex;
      flex-direction: column;
    }
    .extra-content {
      margin-left: auto;
    }
  }
}

@media (min-width: $user-portal-desktop-breakpoint) {
}
</style>
