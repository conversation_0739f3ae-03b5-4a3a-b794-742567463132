<template>
  <button
    ref="buttonRef"
    :id="buttonId"
    :disabled="props.disabled"
    :type="type"
    @click="handleClick"
    :class="[
      {
        'btn-small': props.small,
        'btn-large': props.large,
        'btn-xsmall': props.xsmall,
        'btn-flat': props.flat,
        'btn-link': props.link,
        'btn-rounded': props.rounded,
        'btn-outlined': props.outlined,
        'btn-block': props.block,
      },
    ]"
    :style="styles"
    class="btn"
  >
    <GSpinner v-if="props.isLoading" :darkSpinner="props.color === 'white'" />
    <i
      :style="{ opacity: props.isLoading ? 0 : 1 }"
      :class="[{ right: props.iconRight }, props.icon ? props.icon : '']"
    ></i>
    <slot :style="{ opacity: props.isLoading ? 0 : 1 }"></slot>
  </button>
</template>

<script setup lang="ts">
import { ref, Ref, ComputedRef, computed, onMounted, onUnmounted } from 'vue';
import { v4 as uuidv4 } from 'uuid';
const buttonRef = ref<HTMLElement | null>(null);
const buttonId: string = uuidv4().replace(/-/g, '');

const emit = defineEmits(['click']);
interface IProps {
  darkText?: boolean;
  color?: string;
  small?: boolean;
  large?: boolean;
  xsmall?: boolean;
  disabled?: boolean;
  flat?: boolean;
  icon?: string;
  iconRight?: boolean;
  link?: boolean;
  rounded?: boolean;
  outlined?: boolean;
  isLoading?: boolean;
  type?: 'button' | 'submit' | 'reset';
  block?: boolean;
}
const props = withDefaults(defineProps<IProps>(), {
  darkText: false,
  color: 'var(--info)',
  small: false,
  large: false,
  xsmall: false,
  disabled: false,
  flat: false,
  icon: '',
  iconRight: false,
  link: false,
  rounded: false,
  outlined: false,
  isLoading: false,
  type: 'button',
  block: false,
});

// emit click back
function handleClick() {
  emit('click');
}

const styles: ComputedRef<{ [key: string]: string }[]> = computed(() => {
  const isSassVariable = [
    'primary',
    'secondary',
    'error',
    'info',
    'success',
    'warning',
  ].includes(props.color);

  const colorValue: string = isSassVariable
    ? 'var(--' + props.color + ')'
    : props.color;

  const backgroundColor = !props.outlined ? colorValue : 'transparent';
  const border = props.outlined ? '1px solid ' + colorValue : '';
  const color = props.outlined
    ? colorValue
    : props.darkText
      ? 'var(--text-color-dark)'
      : 'var(--text-color-light)';
  return [
    {
      backgroundColor,
      border,
      color,
    },
  ];
});

// Applies the ripple effect to the button on the mousedown event
function handleRippleEffect(e: MouseEvent) {
  const buttonEl = document.getElementById(buttonId);
  if (!buttonEl) {
    console.error('Button element not found');
    return;
  }
  const self = buttonRef.value;

  if (!self) {
    return;
  }
  // Disable right click
  if (e.button === 2) {
    return false;
  }
  let ripple: HTMLElement | null;
  if (!self.querySelector('.ripple')) {
    const span = document.createElement('span');
    span.className = 'ripple';
    self.insertBefore(span, self.firstChild);
    ripple = span;
  } else {
    ripple = self.querySelector('.ripple');
  }
  if (!ripple) {
    return;
  }
  const eWidth = self.offsetWidth;
  const eHeight = self.offsetHeight;
  const size = Math.max(eWidth, eHeight);
  ripple.style.width = size + 'px';
  ripple.style.height = size + 'px';

  const rippleX = e.pageX - self.offsetLeft - size / 2;
  const rippleY = e.pageY - self.offsetTop - size / 2;
  ripple.style.top = rippleY + 'px';
  ripple.style.left = rippleX + 'px';
  ripple.style.display = 'block';
  ripple.style.position = 'absolute';
  ripple.style.borderRadius = '100%';
  ripple.style.transform = 'scale(0)';
  ripple.style.background = 'rgba(255, 255, 255, 0.5)';

  const animationKeyFrames = [
    { transform: 'scale(0)' },
    { transform: 'scale(2.5)', opacity: '0' },
  ];

  const animationDuration: number = 400;
  const animationTiming: KeyframeAnimationOptions = {
    duration: animationDuration,
  };
  ripple.animate(animationKeyFrames, animationTiming);
  setTimeout(() => {
    if (ripple && ripple.parentNode === self) {
      ripple.parentNode.removeChild(ripple);
    }
  }, animationDuration);
}

onMounted(() => {
  if (buttonRef.value != null) {
    buttonRef.value.addEventListener('mousedown', handleRippleEffect);
  }
});

onUnmounted(() => {
  if (buttonRef.value != null) {
    buttonRef.value.removeEventListener('mousedown', handleRippleEffect);
  }
});
</script>
<style scoped lang="scss">
@media (min-width: $user-portal-desktop-breakpoint) {
}

.dark-font-color {
  color: #fff;
}

.light-font-color {
  color: $secondary;
}

.btn {
  display: inline-block;
  flex-shrink: 0;
  position: relative;
  cursor: pointer;
  height: 35px;
  line-height: 35px;
  padding: 0 1.5rem;
  font-size: $font-size-15;
  font-weight: 600;
  font-family: $app-font-family;
  letter-spacing: 0.8px;
  text-align: center;
  text-decoration: none;
  text-transform: uppercase;
  vertical-align: middle;
  white-space: nowrap;
  outline: none;
  border: none;
  user-select: none;
  border-radius: 2px;
  transition: all 0.3s ease-out;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.225);
  overflow: hidden;

  // background: $primary;

  &:hover {
    text-decoration: none;
    box-shadow: 0 4px 10px 0px rgba(0, 0, 0, 0.225);
  }
}

.btn {
  border-radius: $border-radius-Xsm;
  position: relative;
  &.btn-fab,
  &.btn-fab-mini {
    overflow: hidden;
    position: relative;
    margin: auto;
    padding: 0;
    line-height: normal;
    border-radius: 50%;
    box-shadow: 0 2px 5px 1px rgba(0, 0, 0, 0.3);
    &:hover {
      box-shadow: 0 4px 11px 0px rgba(0, 0, 0, 0.375);
    }
    i {
      display: inline-block;
      float: none;
      width: inherit;
      margin: 0;
      font-size: inherit;
      text-align: center;
      line-height: none;
      vertical-align: middle;
    }
  }
  &.btn-fab {
    width: 56px;
    height: 56px;
    font-size: 28px;
  }
  &.btn-fab-mini {
    width: 40px;
    height: 40px;
    font-size: $font-size-24;
  }
}

.btn {
  &.btn-rounded {
    border-radius: 18px;
  }

  &.btn-outlined {
    background-color: transparent;
  }
  &.btn-large {
    height: 48px;
    line-height: 48px;
  }

  &.btn-small {
    height: 30px;
    padding: 0 1rem;
    line-height: 30px;
    font-size: $font-size-12;

    &.btn-rounded {
      border-radius: 10px;
    }
  }

  &.btn-xsmall {
    height: 24px;
    padding: 0 0.4rem;
    line-height: 24px;
    font-size: $font-size-11;
    font-weight: 600;
    letter-spacing: 0.2px;
    &.btn-rounded {
      border-radius: 12px;
    }

    i {
      font-size: $font-size-13;
    }
  }
}

.btn-block {
  width: 100%;
}

.btn {
  &.disabled,
  &[disabled] {
    cursor: default !important;
    color: var(--text-color) !important;
    border: 1px solid hsla(0, 0%, 100%, 0.12) !important;
    box-shadow: none !important;
  }

  &.disabled:not(.btn-flat),
  &[disabled]:not(.btn-flat) {
    background-color: var(--background-color-400) !important;
    border: 1px solid hsla(0, 0%, 100%, 0.12) !important;
    &:hover {
      background-color: var(--background-color-400) !important;
      border: 1px solid hsla(0, 0%, 100%, 0.12) !important;
    }
  }

  &.btn-flat {
    box-shadow: none !important;
    background-color: transparent !important;
    &:hover {
      background-color: #cecece !important;
      box-shadow: none !important;
    }

    &.disabled:hover,
    &[disabled]:hover {
      background-color: transparent !important;
    }
  }

  &.btn-link {
    color: #3949ab !important;
    box-shadow: none !important;
    background-color: transparent !important;

    &:hover {
      text-decoration: underline !important;
      background-color: transparent !important;
      box-shadow: none !important;
    }

    &.disabled,
    &[disabled] {
      color: hsla(0, 0%, 100%, 0.3) !important;
      text-decoration: underline !important;
      background-color: transparent !important;
    }

    &.disabled:hover,
    &[disabled]:hover {
      background-color: transparent !important;
    }
  }

  i {
    float: left;
    width: auto;
    height: auto;
    margin-right: 10px;
    font-size: 1.3rem;
    line-height: inherit;

    &.right {
      float: right !important;
      margin: 0;
      margin-left: 10px;
    }
  }
}

.ripple-effect {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  vertical-align: middle;
  user-select: none;
  z-index: 200;
}
</style>
