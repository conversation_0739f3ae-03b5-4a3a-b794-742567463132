<template>
  <section class="form-card" :class="disableMarginBottom ? '' : 'mb-3'">
    <v-flex
      class="form-card-container"
      :class="{ blueBackground: isAssetInformation }"
    >
      <v-layout
        class="app-bgcolor--400 app-borderside--a app-bordercolor--600 form-card__header"
        v-if="!isAssetInformation"
        :class="isClientPortal ? 'blue-grey lighten-3' : ''"
      >
        <v-flex md8>
          <v-layout row justify-space-between style="width: 100%">
            <v-layout column justify-center align-start fill-height>
              <span
                class="form-card__header--title"
                :class="isClientPortal ? 'client-portal-text-color' : ''"
              >
                <slot name="heading"></slot>
              </span>

              <span
                class="form-card__header--subtitle"
                :class="isClientPortal ? 'client-portal-text-color' : ''"
              >
                <slot name="subheading"></slot>
              </span>
            </v-layout>
          </v-layout>
        </v-flex>
        <v-flex md4>
          <v-layout row justify-end align-center class="fill-height">
            <slot name="top-right"></slot>
          </v-layout>
        </v-flex>
      </v-layout>
      <v-layout
        :class="[
          { 'pa-0': newTableTitle },
          { 'pa-2': isAssetInformation },
          { 'pa-4': !newTableTitle && !isAssetInformation },
          isClientPortal ? 'blue-grey lighten-4' : '',
        ]"
        class="app-theme__center-content--body"
      >
        <v-flex md12>
          <slot name="content"></slot>
        </v-flex>
      </v-layout>
    </v-flex>
  </section>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    color?: string;
    tableTitle?: boolean;
    newTableTitle?: boolean;
    isAssetInformation?: boolean;
    disableMarginBottom?: boolean;
    isClientPortal?: boolean;
  }>(),
  {
    color: '',
    tableTitle: false,
    newTableTitle: false,
    isAssetInformation: false,
    disableMarginBottom: false,
    isClientPortal: false,
  },
);
</script>

<style lang="scss" scoped>
.form-card {
  .form-card-container {
    // background-color: #2e2e2e;

    .form-card__header {
      padding: 8px 4px 6px 14px;

      .form-card__header--title {
        font-weight: 600;
        font-size: $font-size-large;
        color: $warning-type;
        text-transform: uppercase;
        letter-spacing: 0.02em;
      }

      .form-card__header--subtitle {
        font-weight: 400;
        font-size: 0.85em;
        color: var(--light-text-color);
        text-transform: none;
      }
    }
  }
}

.hide {
  display: none;
}

.removePadding {
  padding: 0px !important;
}

.blueBackground {
  background-color: var(--background-color-300) !important;
}

.client-portal-text-color {
  color: $client-portal-text-color !important;
}
</style>
