<template>
  <div>
    <v-dialog
      v-model="showDialog"
      :width="dialogWidth"
      class="ma-0"
      :content-class="
        isClientPortal ? 'v-dialog-custom client-portal' : 'v-dialog-custom'
      "
      :style="
        !requiresButtonToOpen
          ? {}
          : isListTile
            ? { display: 'inline' }
            : { display: 'inline-block' }
      "
    >
      <template v-slot:activator="{ on }" v-if="requiresButtonToOpen">
        <v-btn
          v-if="
            !isIcon && !isListTile && !isIconOnly && !isDialogCloseConfirmation
          "
          :color="buttonColor"
          :outline="isOutlineButton"
          :small="isSmallButton"
          :block="isBlockButton"
          :flat="isFlatButton"
          :class="noButtonMargin ? 'ma-0' : ''"
          :depressed="isDepressedButton"
          :disabled="showDialog || buttonDisabled"
          :light="isClientPortal"
          v-on="dialogIsActive ? on : null"
          class="v-btn-confirm-custom"
          :loading="isLoading"
          @click="!dialogIsActive ? confirm() : null"
        >
          {{ buttonText }}
          <template v-slot:loader>
            <span>Loading...</span>
          </template>
        </v-btn>
        <v-list-tile
          dense
          v-if="isListTile"
          :disabled="showDialog || buttonDisabled"
          v-on="dialogIsActive ? on : null"
          @click="!dialogIsActive ? confirm() : null"
        >
          <v-list-tile-avatar class="pa-0" v-if="listTileIcon">
            <v-icon size="14">
              {{ listTileIcon }}
            </v-icon>
          </v-list-tile-avatar>
          <v-list-tile-title
            class="pr-2"
            :class="listTileAccent ? 'amber--text font-italic' : ''"
          >
            {{ buttonText }}
          </v-list-tile-title>
        </v-list-tile>

        <v-btn
          v-if="isIcon"
          flat
          icon
          class="ma-0"
          :color="buttonColor"
          :disabled="showDialog || buttonDisabled"
          v-on="dialogIsActive ? on : null"
        >
          <v-icon
            :class="{ 'client-portal-confirmation-dialog': isClientPortal }"
            :size="iconSize"
            >{{ faIconName }}</v-icon
          >
        </v-btn>

        <v-icon
          v-if="isIconOnly"
          v-on="dialogIsActive ? on : null"
          :color="buttonColor"
          :disabled="showDialog || buttonDisabled"
          :class="{ 'client-portal-confirmation-dialog': isClientPortal }"
          :size="iconSize"
          >{{ faIconName }}</v-icon
        >
        <div
          v-if="isDialogCloseConfirmation"
          :id="confirmButtonId"
          class="app-theme__center-content--closebutton"
          v-on="dialogIsActive ? on : null"
          :style="isLoading ? 'pointer-events: none;' : ''"
          @click="!dialogIsActive ? confirm() : null"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </template>
      <v-card class="v-card-custom">
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight"
        >
          <span
            :class="{ 'client-portal-confirmation-dialog': isClientPortal }"
            >{{ title }}</span
          >
          <div class="app-theme__center-content--closebutton" @click="cancel">
            <v-icon
              class="app-theme__center-content--closebutton--icon"
              :color="isClientPortal ? buttonColor : ''"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>
        <v-layout row wrap class="px-4 pt-4">
          <v-flex md12>
            <p :class="{ 'client-portal-confirmation-dialog': isClientPortal }">
              {{ message }}
            </p>
          </v-flex>
          <v-flex md12>
            <slot name="confirmation-dialog-content"></slot>
          </v-flex>
          <v-flex md12>
            <v-checkbox
              class="ma-0"
              v-if="isCheckbox"
              v-model="checkboxValue"
              :label="checkboxLabel"
            />
          </v-flex>
          <v-flex md12 v-if="isCheckboxList" class="pb-2">
            <v-layout v-for="(item, index) in checkboxListItems" :key="item.id">
              <v-flex md1>
                <v-checkbox
                  class="ma-0"
                  v-model="item.value"
                  :id="`confirmation-dialog-checkbox-${index}`"
                />
              </v-flex>
              <v-flex md11 pl-2>
                <label :for="`confirmation-dialog-checkbox-${index}`">
                  <v-layout>
                    <span class="pt-1 confirmation-dialog-label">
                      {{ item.label }}
                    </span>
                  </v-layout>
                </label>
              </v-flex>
            </v-layout>
          </v-flex>
        </v-layout>
        <v-divider class="mt-2"></v-divider>
        <v-layout justify-space-between>
          <v-btn depressed :color="cancelButtonColor" flat @click="cancel">{{
            cancelButtonText
          }}</v-btn>
          <v-btn
            :disabled="!allCheckboxesApproved"
            depressed
            :color="confirmationButtonColor"
            class="v-btn-confirm-custom"
            @click="confirm"
            >{{ confirmationButtonText }}
          </v-btn>
        </v-layout>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, Ref } from 'vue';

interface CheckboxListItem {
  id: string;
  label: string;
  value: boolean;
}

const props = withDefaults(
  defineProps<{
    title?: string;
    message?: string;
    buttonText?: string;
    buttonColor?: string;
    confirmButtonText?: string;
    confirmationButtonColor?: string;
    cancelButtonText?: string;
    cancelButtonColor?: string;
    isSmallButton?: boolean;
    isOutlineButton?: boolean;
    isBlockButton?: boolean;
    confirmationButtonText?: string;
    dialogIsActive?: boolean;
    buttonDisabled?: boolean;
    isCheckbox?: boolean;
    checkboxLabel?: string;
    dialogWidth?: number;
    isDepressedButton?: boolean;
    isFlatButton?: boolean;
    isClientPortal?: boolean;
    // Use the following two props when we want a LIST of checkboxes to be
    // approved by the user.
    isCheckboxList?: boolean;
    checkboxLabelList?: string[];
    faIconName?: string;
    isIcon?: boolean;
    iconSize?: number;
    listTileIcon?: string;
    isListTile?: boolean;
    noButtonMargin?: boolean;
    requiresButtonToOpen?: boolean;
    isIconOnly?: boolean;
    isLoading?: boolean;
    isDialogCloseConfirmation?: boolean;
    listTileAccent?: false;
    // Provided when isDialogCloseConfirmation is provided, and we want to
    // programmatically click the button to close the dialog.
    confirmButtonId?: string;
  }>(),
  {
    title: '',
    message: '',
    string: '',
    buttonText: '',
    buttonColor: 'info',
    confirmButtonText: 'Confirm',
    confirmationButtonColor: 'blue',
    cancelButtonText: 'Cancel',
    cancelButtonColor: 'error',
    isSmallButton: false,
    isOutlineButton: false,
    isBlockButton: false,
    confirmationButtonText: 'Confirm',
    dialogIsActive: true,
    buttonDisabled: false,
    listTileAccent: false,
    isCheckbox: false,
    checkboxLabel: '',
    dialogWidth: 400,
    isDepressedButton: false,
    isFlatButton: false,
    isClientPortal: false,
    listTileIcon: '',
    // Use the following two props when we want a LIST of checkboxes to be
    // approved by the user.
    isCheckboxList: false,
    checkboxLabelList: () => [],
    faIconName: '',
    isIcon: false,
    iconSize: 12,
    isListTile: false,
    noButtonMargin: false,
    requiresButtonToOpen: true,
    isIconOnly: false,
    isLoading: false,
    isDialogCloseConfirmation: false,
    confirmButtonId: '',
  },
);

const checkboxValue: Ref<boolean> = ref(false);
const checkboxListItems: Ref<CheckboxListItem[]> = ref([]);
const showDialog: Ref<boolean> = ref(false);

const emit = defineEmits<{
  (event: 'closeDialog'): void;
  (event: 'confirm', value: boolean): void;
}>();

function cancel() {
  showDialog.value = false;
  checkboxValue.value = false;
  if (props.isCheckboxList) {
    // Reset checkbox values to on cancel
    checkboxListItems.value.forEach((item) => {
      item.value = false;
    });
  }

  if (!props.requiresButtonToOpen) {
    emit('closeDialog');
  }
}

// Controls whether the 'confirm' button is disabled. This is used when a checkbox list
// is required. In this case, all checkboxes must be approved before the confirmation may
// be pressed.
const allCheckboxesApproved = computed(() => {
  if (props.isCheckboxList) {
    return checkboxListItems.value.map((i) => i.value).every((b) => b);
  } else if (props.isCheckbox) {
    return checkboxValue.value;
  } else {
    return true;
  }
});

function confirm() {
  showDialog.value = false;
  emit('confirm', checkboxValue.value);
}

onMounted(() => {
  // If isCheckboxList is true, CheckboxListItem for each label in supplied list
  // This list will be used to create the checkbox list that must be approved before the dialog can be confirmed.
  if (
    props.isCheckboxList &&
    props.checkboxLabelList &&
    props.checkboxLabelList.length > 0
  ) {
    props.checkboxLabelList.forEach((label) => {
      checkboxListItems.value.push({
        id: `${label}`,
        label,
        value: false,
      });
    });
  }

  if (!props.requiresButtonToOpen) {
    showDialog.value = true;
  }
});
</script>

<style scoped lang="scss">
.confirmation-dialog {
  padding: 0px;
  // display: flex;
}

.client-portal-confirmation-dialog {
  color: white;
}

.v-card-custom {
  border-radius: 0px !important;
  background-color: var(--background-color-300) !important;
}
</style>
