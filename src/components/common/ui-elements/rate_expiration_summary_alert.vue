<template>
  <section class="rate-expiration-summary-alert">
    <v-alert
      type="warning"
      :value="showAlert"
      :class="{ 'mb-3': !!expirationSummaries.length }"
      icon="fas fa-exclamation-circle"
    >
      <v-flex md12>
        <v-layout justify-space-between align-center :mb-2="showTable">
          <span class="title"> {{ primaryTitle }} </span>
          <span>
            <v-tooltip left>
              <template v-slot:activator="{ on }">
                <v-btn
                  flat
                  v-on="on"
                  icon
                  @click="showTable = !showTable"
                  class="ma-0"
                >
                  <v-icon
                    size="22"
                    :class="{
                      'fa fa-chevron-up': showTable,
                      'fa fa-chevron-down': !showTable,
                    }"
                    color="orange"
                  ></v-icon>
                </v-btn>
              </template>
              Show/Hide details
            </v-tooltip>
          </span>
        </v-layout>
      </v-flex>

      <div class="table-container" :class="{ expand: showTable }">
        <table
          class="alert-data-table"
          v-if="showTable && expirationSummaries.length"
        >
          <thead>
            <tr>
              <th>Name</th>
              <th style="white-space: nowrap">Due Date/Expiry</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in expirationSummaries" :key="item.name">
              <td class="text-xs-left">{{ item.name }}</td>
              <td class="text-xs-right" style="white-space: nowrap">
                <span v-if="item.validToDate">Due:</span>
                <span>
                  {{
                    item.validToDate
                      ? returnFormattedDate(item.validToDate)
                      : 'EXPIRED/NOT VALID'
                  }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
        <v-layout
          v-if="showTable && relatedSummaries && relatedSummaries.length"
          row
          wrap
        >
          <v-flex md12 pt-3>
            <span
              class="title"
              style="font-size: 0.95em"
              v-if="!!expirationSummaries.length"
            >
              Related Issues
            </span>
          </v-flex>
          <v-flex md12>
            <table
              class="alert-data-table mt-2"
              v-for="group in relatedSummaries"
              :key="group.id"
            >
              <template>
                <tr>
                  <td colspan="2" class="text-xs-left">
                    <v-layout align-center pb-2 pt-1
                      ><span style="font-weight: 700" class="px-2">{{
                        group.name
                      }}</span>
                      <v-flex><v-divider class="my-2"></v-divider></v-flex
                    ></v-layout>
                  </td>
                </tr>
              </template>
              <tbody>
                <tr v-for="item in group.summaries" :key="item.name">
                  <td class="text-xs-left">{{ item.name }}</td>
                  <td class="text-xs-right">
                    <span v-if="item.validToDate">Due:</span>
                    <span>
                      {{
                        item.validToDate
                          ? returnFormattedDate(item.validToDate)
                          : '-'
                      }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </v-flex>
        </v-layout>
      </div>
    </v-alert>
  </section>
</template>

<script setup lang="ts">
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  RateExpirationSummary,
  RateExpirationSummaryGroup,
} from '@/interface-models/ServiceRates/RateExpirationSummary';
import { ComputedRef, Ref, computed, onBeforeMount, ref } from 'vue';

const props = withDefaults(
  defineProps<{
    expirationSummaries?: RateExpirationSummary[];
    relatedSummaries?: RateExpirationSummaryGroup[];
  }>(),
  {
    expirationSummaries: undefined,
    relatedSummaries: undefined,
  },
);

const showTable: Ref<boolean> = ref(true);

const primaryTitle: ComputedRef<string> = computed(() => {
  if (props.expirationSummaries && props.expirationSummaries.length) {
    return `${props.expirationSummaries.length} Issue(s) Require Action`;
  }
  if (props.relatedSummaries && props.relatedSummaries.length) {
    return `${props.relatedSummaries.length} Related Issue(s)`;
  }
  return '';
});

const showAlert: ComputedRef<boolean> = computed(() => {
  if (props.expirationSummaries && props.expirationSummaries.length) {
    return true;
  }
  if (props.relatedSummaries && props.relatedSummaries.length) {
    return true;
  }
  return false;
});

onBeforeMount(() => {
  if (
    (!props.expirationSummaries || !props.expirationSummaries.length) &&
    !!props.relatedSummaries &&
    !!props.relatedSummaries.length
  ) {
    showTable.value = false;
  }
});
</script>

<style scoped lang="scss">
.rate-expiration-summary-alert {
  .title {
    color: var(--primary) !important;
  }

  .table-container {
    &.expand {
      height: fit-content;
      max-height: 400px;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 10px;
      }
      &::-webkit-scrollbar-track {
        background-color: wheat !important;
        opacity: 0;
      }
      &::-webkit-scrollbar-thumb {
        background-color: $warning !important;
        border-radius: $border-radius-Xlg;
      }
    }
  }
}

.v-icon {
  border: none !important;
}
</style>
