<template>
  <v-layout>
    <v-flex
      :md12="!(filteredStatus.length > 0) && !showSecondaryStatusList"
      :md6="filteredStatus.length > 0 && showSecondaryStatusList"
      :class="filteredStatus.length > 0 && showSecondaryStatusList ? 'pr' : ''"
    >
      <v-select
        :items="filteredMasterStatus"
        :class="[
          formDisabled && soloInput ? 'solo-input-disable-display' : '',
          soloInput ? 'v-solo-custom' : '',
        ]"
        :disabled="formDisabled"
        item-text="text"
        item-value="sid"
        :box="boxInput"
        :solo="soloInput"
        :flat="soloInput"
        color="light blue"
        v-model="selectedMasterStatus"
        label="Status"
        :rules="isRequired ? [validate.listRequired] : []"
        :hint="hintText"
        :persistent-hint="!!hintText"
      >
      </v-select>
    </v-flex>
    <v-flex
      md6
      class="pl"
      v-if="filteredStatus.length > 0 && showSecondaryStatusList"
    >
      <v-select
        :class="[
          formDisabled && soloInput ? 'solo-input-disable-display' : '',
          soloInput ? 'v-solo-custom' : '',
        ]"
        :items="filteredStatus"
        :disabled="formDisabled"
        multiple
        label="Reason"
        item-text="enumValue"
        :box="boxInput"
        :solo="soloInput"
        :flat="soloInput"
        color="light blue"
        item-value="sid"
        v-model="selectedSecondaryStatus"
      >
      </v-select>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import { returnStatusConfigFromId } from '@/helpers/StatusHelpers/StatusHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import StatusConfig from '@/interface-models/Status/StatusConfig';
import { useRootStore } from '@/store/modules/RootStore';
import { ComputedRef, Ref, computed, onMounted, ref, watch } from 'vue';

const validate = validationRules;

const props = withDefaults(
  defineProps<{
    statusList: number[];
    statusCategory: number;
    formDisabled?: boolean;
    showSecondaryStatusList?: boolean;
    resetSelectedSecondaryStatus?: boolean;
    doNotUseType?: boolean;
    isRequired?: boolean;
    boxInput?: boolean;
    soloInput?: boolean;
    hintText?: string;
  }>(),
  {
    hintText: '',
    boxInput: true,
    showSecondaryStatusList: true,
    formDisabled: false,
    resetSelectedSecondaryStatus: false,
    doNotUseType: false,
    isRequired: false,
    soloInput: false,
  },
);

// component Data
const selectedMasterStatus: Ref<number> = ref(-1);
const selectedSecondaryStatus: Ref<number[]> = ref([]);

const statusTypes: ComputedRef<StatusConfig[]> = computed(() => {
  return useRootStore().statusTypeList;
});

// filter out master status only with correct permissions
const filteredMasterStatus = computed(() => {
  const masterItems = statusTypes.value.filter(
    (item: StatusConfig) => item.master === true,
  );
  return masterItems.filter((item) =>
    item.category.includes(props.statusCategory),
  );
});

function setComponent() {
  if (props.statusList.length === 0) {
    selectedMasterStatus.value = -1;
    selectedSecondaryStatus.value = [];
    return;
  }
  for (const master of filteredMasterStatus.value) {
    for (const statusId of props.statusList) {
      if (master.sid === statusId) {
        selectedMasterStatus.value = statusId;
      }
    }
  }

  for (const secondary of filteredStatus.value) {
    for (const activeSecondary of props.statusList) {
      if (secondary.sid === activeSecondary) {
        if (!selectedSecondaryStatus.value.includes(activeSecondary)) {
          selectedSecondaryStatus.value.push(activeSecondary);
        }
      }
    }
  }
}

onMounted(() => {
  setComponent();
});

watch(props.statusList, () => {
  setComponent();
});

// watch for selected Master status -> empty statusList -> push master status
watch(selectedMasterStatus, (val, oldVal) => {
  if (val === -1) {
    if (props.statusCategory !== 8) {
      return;
    }

    const matchedOldStatus = returnStatusConfigFromId(
      statusTypes.value,
      oldVal,
    );
    if (matchedOldStatus) {
      const foundIndex = props.statusList.findIndex(
        (item) => item === matchedOldStatus.sid,
      );
      if (foundIndex !== -1) {
        props.statusList.splice(foundIndex, 1);
      }
    }
    selectedSecondaryStatus.value = [];

    return;
  }

  const matchedNewStatus = returnStatusConfigFromId(statusTypes.value, val);

  for (let i = props.statusList.length - 1; i >= 0; i--) {
    const foundMatch = returnStatusConfigFromId(
      statusTypes.value,
      props.statusList[i],
    );
    if (foundMatch && matchedNewStatus) {
      if (foundMatch.category.includes(props.statusCategory)) {
        props.statusList.splice(i, 1, val);
      }
    }
  }
  if (!props.statusList.includes(val)) {
    props.statusList.push(val);
  }

  if (oldVal === -1) {
    for (const secondary of selectedSecondaryStatus.value) {
      if (!props.statusList.includes(secondary)) {
        props.statusList.push(secondary);
      }
    }
  } else {
    selectedSecondaryStatus.value = [];
  }
});

// watch selected secondary option to either push or remove from status list array
watch(selectedSecondaryStatus, (val, oldVal) => {
  // if selected item doesnt exist in statusList push to array
  for (const id of val) {
    if (!oldVal.includes(id)) {
      props.statusList.push(id);
    }
  }
  // if selected item esits in statusList remove it from array
  for (const id of oldVal) {
    if (!val.includes(id)) {
      for (const [i, v] of props.statusList.entries()) {
        if (v === id) {
          props.statusList.splice(i, 1);
        }
      }
    }
  }
});

// filter status data based off selected master status
const filteredStatus: ComputedRef<StatusConfig[]> = computed(() => {
  const returnedResult: StatusConfig[] = [];
  // const filterValue = props.statusList[0];
  if (selectedMasterStatus.value === -1) {
    return [];
  }
  const foundMasterStatus = returnStatusConfigFromId(
    statusTypes.value,
    selectedMasterStatus.value,
  );

  if (!foundMasterStatus) {
    return [];
  }

  for (const groupId of foundMasterStatus.group) {
    const resultItem: StatusConfig | undefined = statusTypes.value.find(
      (item) => item.sid === groupId,
    );

    if (!resultItem) {
      continue;
    }
    returnedResult.push(resultItem);
  }

  return returnedResult;
});
</script>
