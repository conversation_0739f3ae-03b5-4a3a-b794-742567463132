<template>
  <div class="job-reference-container">
    <v-layout align-center>
      <v-text-field
        v-if="referenceMask !== null"
        :label="referenceName"
        v-model="jobReference.reference"
        :rules="referenceRequired"
        v-mask="referenceMask"
        color="orange"
        box
        hide-details
        :disabled="readOnly"
      ></v-text-field>
      <v-text-field
        v-else
        :rules="referenceRequired"
        :label="referenceName"
        v-model="jobReference.reference"
        box
        color="orange"
        hide-details
        :disabled="readOnly"
      ></v-text-field>
      <v-icon
        class="pl-3"
        v-if="!readOnly"
        @click="removeJobReference"
        size=" 16"
        color="error"
        >fa fa-trash-alt
      </v-icon>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import ClientReferenceDetails from '@/interface-models/Client/ClientDetails/References/ClientReferenceDetails';
import JobReferenceDetails from '@/interface-models/Jobs/JobReferenceDetails';
import {
  referenceTypes,
  ReferenceTypes,
} from '@/interface-models/Generic/ReferenceTypes/ReferenceTypes';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';

const validate = validationRules;

const props = withDefaults(
  defineProps<{
    jobReference: JobReferenceDetails;
    clientJobReferences: ClientReferenceDetails[];
    jobReferenceIndex: number;
    jobId: number;
    readOnly?: boolean;
  }>(),
  {
    readOnly: false,
  },
);

const emit = defineEmits(['removeJobReference']);

const referenceMask = computed(() => {
  if (props.readOnly) {
    return null;
  }
  const foundReference = props.clientJobReferences.findIndex(
    (item: ClientReferenceDetails) =>
      item.referenceTypeId === props.jobReference.referenceTypeId,
  );
  if (foundReference !== -1) {
    return props.clientJobReferences[foundReference].referenceMask === ''
      ? null
      : props.clientJobReferences[foundReference].referenceMask;
  } else {
    return null;
  }
});

const referenceName = computed(() => {
  const foundReference = referenceTypes.find(
    (item: ReferenceTypes) => item.id === props.jobReference.referenceTypeId,
  );
  if (foundReference !== undefined) {
    return `${foundReference.longName} ${
      referenceMask.value ? ' - ' + referenceMask.value : ''
    }`;
  } else {
    return 'Generic Reference';
  }
});

function removeJobReference() {
  emit('removeJobReference', props.jobReferenceIndex);
}

const referenceRequired = computed(() => {
  if (props.readOnly) {
    return [];
  }
  const clientReference = props.clientJobReferences.find(
    (reference: ClientReferenceDetails) =>
      reference.referenceTypeId === props.jobReference.referenceTypeId,
  );

  let required: boolean = false;
  if (clientReference) {
    switch (clientReference.requiredTypeId) {
      case 2: // every stop
        required = true;
        break;
    }
  }
  if (required) {
    return [validate.required];
  } else {
    return [];
  }
});
</script>
