<template>
  <div class="summary-container" :class="{ scroll: scrollY > 240 }">
    <!-- First item: Client Name -->
    <div v-if="scrollY < 240" class="summary-item full">
      <div class="summary-content">
        <span class="title-txt">Client Name</span>
        <span class="value-txt">
          {{ jobDetails.client.clientName }}
        </span>
      </div>
    </div>

    <!-- Second item: Job Date -->
    <div class="summary-item half">
      <div class="summary-content">
        <span class="title-txt">Job Date</span>
        <span class="value-txt">
          {{ expectedJobDate }}
        </span>
      </div>
    </div>

    <!-- Third item: Dispatcher -->
    <div class="summary-item dispatcher">
      <div class="summary-content">
        <span class="title-txt">Dispatcher</span>
        <v-layout column justify-start align-start>
          <span class="value-txt"> {{ dispatcherName || '-' }}</span>
          <span v-if="dispatcherName !== '-'" class="site-contact">
            <v-tooltip bottom>
              <template v-slot:activator="{ on }">
                <a
                  v-if="
                    jobDetails.clientDispatcher.contactMobileNumber ||
                    jobDetails.clientDispatcher.contactLandlineNumber
                  "
                  class="site-contact-link"
                  v-on="on"
                  :href="`tel:${
                    jobDetails.clientDispatcher.contactMobileNumber ||
                    jobDetails.clientDispatcher.contactLandlineNumber
                  }`"
                >
                  <v-icon size="14">phone</v-icon>
                  {{
                    formatPhoneNumber(
                      jobDetails.clientDispatcher.contactMobileNumber ||
                        jobDetails.clientDispatcher.contactLandlineNumber,
                    )
                  }}
                </a>
              </template>
              Call {{ dispatcherName }}
            </v-tooltip>
          </span>
        </v-layout>
      </div>
    </div>

    <!-- Fourth item: References -->
    <div class="summary-item">
      <div class="summary-content">
        <span class="title-txt">References</span>
        <span class="value-txt">
          <span v-if="jobDetails.jobReference.length === 0">-</span>
          <span v-else>
            <span>
              <v-tooltip bottom>
                <template v-slot:activator="{ on }">
                  <span class="tooltip" v-on="on">
                    {{
                      truncateReferenceIfLong(
                        jobDetails.jobReference[0].reference,
                      ) || '-'
                    }}
                    <span v-if="jobDetails.jobReference.length > 1"
                      >( +{{ jobDetails.jobReference.length - 1 }} )</span
                    >
                  </span>
                </template>
                <span>
                  <ul>
                    <li
                      v-for="(ref, index) in jobDetails.jobReference"
                      :key="`${(ref.reference, index)}`"
                    >
                      {{ ref.reference || '' }}
                    </li>
                  </ul>
                </span>
              </v-tooltip>
            </span>
          </span>
        </span>
      </div>
    </div>

    <!-- Fifth item: Additional Equipment -->
    <div class="summary-item">
      <div class="summary-content">
        <span class="title-txt">Additional Equipment</span>
        <span class="value-txt">
          <!-- Display dash if no equipment -->
          <span v-if="additionalEquipmentNames.length === 0">-</span>
          <span v-else>
            <!-- Display the first two equipment names -->
            <span
              v-for="(item, index) in additionalEquipmentNames.slice(0, 2)"
              :key="index"
            >
              <span>{{ item }}</span>
              <span
                v-if="index < additionalEquipmentNames.slice(0, 2).length - 1"
                >,{{ '' }}
              </span>
            </span>

            <v-tooltip v-if="additionalEquipmentNames.length > 2" bottom>
              <template v-slot:activator="{ on }">
                <span class="tooltip" v-on="on">
                  ( +{{ additionalEquipmentNames.length - 2 }} )
                </span>
              </template>
              {{ additionalEquipmentNames.slice(2).join(', ') }}
            </v-tooltip>
          </span>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  returnFormattedDate,
  returnTimeNow,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { formatPhoneNumber } from '@/helpers/StringHelpers/StringHelpers';
import equipmentTypes from '@/interface-models/Generic/EquipmentTypes/EquipmentTypes';
import type JobDetails from '@/interface-models/Jobs/JobDetails';
import moment from 'moment-timezone';
import { computed, ComputedRef, Ref, toRef } from 'vue';

const props = withDefaults(
  defineProps<{
    jobDetails: JobDetails;
    jobDate?: number;
    scrollY: number;
  }>(),
  {
    scrollY: 0,
    jobDate: moment().valueOf(),
  },
);

const jobDetails: Ref<JobDetails> = toRef(props, 'jobDetails');
/**
 * Returns the client dispatcher name, if available. Displayed in the
 * template.
 */
const dispatcherName: ComputedRef<string> = computed(() => {
  return [
    jobDetails.value.clientDispatcher.firstName,
    jobDetails.value.clientDispatcher.lastName,
  ]
    .filter((s) => !!s)
    .join(' ');
});

/**
 * Returns the date of the job as a string to be displayed in the template. Uses
 * the first pud time if available, otherwise uses the jobDate prop, otherwise
 * uses the current time.
 */
const expectedJobDate: ComputedRef<string> = computed(() => {
  if (jobDetails.value.pudItems.length > 0) {
    return jobDetails.value.jobDate;
  } else if (props.jobDate) {
    return returnFormattedDate(props.jobDate);
  } else {
    return returnFormattedDate(returnTimeNow());
  }
});

/**
 * Returns the additional equipment names, if any. Displayed in the template.
 */
const additionalEquipmentNames: ComputedRef<string[]> = computed(() => {
  const names: string[] = [];
  for (const item of equipmentTypes) {
    for (const id of props.jobDetails.additionalEquipments) {
      if (id === item.id) {
        names.push(item.longName);
      }
    }
  }
  // If there are more than 2 items, show first two and append the remaining count
  return names;
});

/**
 * Truncates the reference if it is too long. Displayed in the template.
 * @param reference - The reference to truncate.
 */
function truncateReferenceIfLong(reference: string): string {
  return reference.length > 14 ? `${reference.slice(0, 14)}...` : reference;
}
</script>
<style scoped lang="scss">
.summary-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  .summary-item {
    width: 33%; /* Approximately 1/3 width for each item */
    box-sizing: border-box;
    padding-top: 4px;
    &.full {
      width: 66%;
    }
  }

  .summary-content {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    .title-txt {
      margin-right: 24px;
      font-weight: 400;
      color: var(--light-text-color);
      margin-bottom: 2px;
      text-align: left;
    }

    .value-txt {
      display: flex;
      flex-direction: row;
      font-weight: 400;
      color: var(--text-color);
      text-align: left;
      letter-spacing: 0.25px;
      margin-bottom: 12px;
      white-space: nowrap; /* Ensures the text doesn't wrap to the next line */
    }
    .site-contact {
      // position: absolute;
      // transform: translate(0px, 24px);
      position: relative;
      top: -8px;
      .site-contact-link {
        color: var(--accent);

        &:hover {
          scale: 1.1;
          color: var(--accent-secondary);
          font-size: $font-size-14;
          .v-icon {
            color: var(--accent-secondary);
          }
        }
      }
      .v-icon {
        color: var(--accent);
      }
    }
  }

  &.scroll {
    margin-top: 30px;
    display: flex;
    width: 85%;
    flex-wrap: wrap;
    .summary-item {
      width: 20%;
      &.half {
        width: 10%;
      }
      &.dispatcher {
        width: 30%;
      }
      .site-contact {
        position: relative;
        transform: translate(4px, 0px);
        .site-contact-link {
          display: none;
        }
      }
    }
  }
}

.tooltip {
  cursor: pointer !important;
}
</style>
