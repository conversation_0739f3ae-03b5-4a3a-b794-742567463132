<template>
  <ContentDialog
    :showDialog.sync="dialogController"
    :title="dialogTitle"
    width="35%"
    contentPadding="pa-0"
    @cancel="dialogController = false"
    @confirm="confirmAndCloseDialog"
    @action="selectedNoteIds = []"
    :showActionButton="selectedNoteIds.length > 0"
    actionBtnText="Clear Selection"
    :showActions="true"
    :isConfirmUnsaved="false"
    :isDisabled="false"
    :isLoading="false"
    :showCancelBtn="false"
    :confirmBtnText="dialogConfirmButtonText"
  >
    <v-layout>
      <v-flex md12 class="body-scrollable--75 pa-3">
        <v-layout row wrap px-2>
          <v-flex md12 pb-3 v-if="notifications.length > 0">
            <v-alert
              type="info"
              :value="notifications.length > 0"
              icon="fas fa-exclamation-circle"
            >
              <span>Please note the following:</span>
              <ul>
                <li v-for="note in notifications" :key="note.id ?? ''">
                  <span class="body-text" style="white-space: pre-line">
                    {{ note.body.replace(/^\n+|\n+$/g, '') }}
                  </span>
                </li>
              </ul>
            </v-alert>
          </v-flex>
          <v-flex md12 v-if="automaticallyAdd.length > 0">
            <v-layout align-center>
              <h5 class="subheader--bold pr-3 pt-1 pb-3">
                Notes added to job ({{ automaticallyAdd.length }})
              </h5>
              <v-flex>
                <v-divider></v-divider>
              </v-flex>
            </v-layout>
            <NotesList :communications="automaticallyAdd"></NotesList>
          </v-flex>
          <v-flex md12 v-if="promptForAdd.length > 0">
            <v-layout align-center>
              <h5 class="subheader--bold pr-3 pt-1 pb-3">
                Suggested Notes ({{ promptForAdd.length }})
              </h5>
              <v-flex>
                <v-divider></v-divider>
              </v-flex>
            </v-layout>
            <NotesList
              :communications="promptForAdd"
              :allowSelection="true"
              v-model="selectedNoteIds"
            ></NotesList>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </ContentDialog>
</template>

<script setup lang="ts">
import NotesList from '@/components/common/notes_list/notes_list.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { ClientDetails } from '@/interface-models/Client/ClientDetails/ClientDetails';
import Communication from '@/interface-models/Generic/Communication/Communication';
import { AddToJobType } from '@/interface-models/Generic/Communication/CommunicationTypes/ClientInstructions';
import { sessionManager } from '@/store/session/SessionState';
import { ComputedRef, Ref, computed, onMounted, ref } from 'vue';

const emit = defineEmits<{
  (event: 'addNotesFromSelection', payload: Communication[]): void;
}>();

const props = defineProps<{
  clientDetails: ClientDetails;
}>();

const automaticallyAdd: Ref<Communication[]> = ref([]);
const promptForAdd: Ref<Communication[]> = ref([]);
const notifications: Ref<Communication[]> = ref([]);

const selectedNoteIds: Ref<string[]> = ref([]);

const dialogController: Ref<boolean> = ref(false);

/**
 * The title for the dialog, including the clients name.
 */
const dialogTitle: ComputedRef<string> = computed(() => {
  return `${props.clientDetails.displayName} - Booking Notes and Instructions`;
});

/**
 * The text for the confirm button on the dialog. If there are no notes selected,
 * the button will dismiss the dialog. If there are notes selected, the button will
 * confirm the selection and add the notes to the booking.
 */
const dialogConfirmButtonText: ComputedRef<string> = computed(() => {
  if (promptForAdd.value.length === 0) {
    return 'Dismiss';
  }
  return selectedNoteIds.value.length > 0
    ? `Confirm & Add ${selectedNoteIds.value.length} Notes`
    : 'Dismiss';
});

/**
 * Confirm and close the dialog. If there are selected notes, emit the event to
 * add them to the booking.
 */
function confirmAndCloseDialog() {
  if (selectedNoteIds.value.length === 0) {
    dialogController.value = false;
    return;
  }
  const selectedNotes = promptForAdd.value.filter((note) =>
    selectedNoteIds.value.includes(note.id ?? ''),
  );
  emit('addNotesFromSelection', selectedNotes);
  dialogController.value = false;
}

/**
 * On mounted, set the promptForAdd and notifications values based on the
 * client's special instructions. Only display the dialog if there are notes to
 * display.
 */
onMounted(() => {
  if (props.clientDetails) {
    if (sessionManager.isClientPortal()) {
      return;
    }
    const toAutoAdd = props.clientDetails.specialInstructions.filter(
      (note) =>
        note.type?.id === 2 &&
        note.type.communicationDetails.addToJobType === AddToJobType.AUTO,
    );
    automaticallyAdd.value = toAutoAdd;
    const toPrompt = props.clientDetails.specialInstructions.filter(
      (note) =>
        note.type?.id === 2 &&
        note.type.communicationDetails.addToJobType === AddToJobType.PROMPT,
    );
    promptForAdd.value = toPrompt;
    notifications.value = props.clientDetails.specialInstructions.filter(
      (note) =>
        note.type?.id === 2 &&
        note.type.communicationDetails.addToJobType === AddToJobType.NOTIFY,
    );

    if (
      promptForAdd.value.length > 0 ||
      notifications.value.length > 0 ||
      automaticallyAdd.value.length > 0
    ) {
      dialogController.value = true;
    }
  }
});
</script>
<style scoped lang="scss"></style>
