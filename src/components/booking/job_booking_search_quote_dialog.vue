<template>
  <v-layout>
    <v-flex class="quotes-container d-flex justify-end">
      <v-btn class="quoteSearch-btn" solo flat @click="isViewingDialog = true">
        <v-icon class="mr-2">search</v-icon>
        Search Quote
      </v-btn>
    </v-flex>
    <ContentDialog
      :showDialog.sync="isViewingDialog"
      title="Search Job Quote"
      width="45%"
      @cancel="clearQuote"
      contentPadding="pa-2"
      :showActions="false"
    >
      <v-flex md12 class="body-scrollable--65 body-min-height--50">
        <v-flex md12 class="top-container">
          <span class="quote-text">{{ quoteIdPrefix }}</span>
          <v-text-field
            v-if="isViewingDialog"
            v-model="search"
            append-icon="search"
            label="Search Quote ID"
            hint="Press Enter to Search"
            persistent-hint
            dense
            color="orange"
            solo
            autofocus
            flat
            class="v-solo-custom"
            @keyup.enter="searchQuote"
          />
          <v-btn
            solo
            class="search-btn"
            :loading="isLoading"
            block
            @click="searchQuote"
          >
            Search
          </v-btn>
        </v-flex>

        <span
          v-if="!quoteResult && searchedQuotedId.length > 2"
          class="no-data-text"
          >No Quotes Found for {{ quoteIdPrefix }}{{ searchedQuotedId }}</span
        >

        <v-flex md12 class="summary-container">
          <table class="summary-table" :class="!quoteResult ? 'faded' : ''">
            <tbody>
              <tr v-for="(item, index) in quoteSummaryItems" :key="index">
                <td class="title-txt">{{ item.title }}</td>
                <td
                  class="value-txt"
                  :class="item.title === 'Status' ? item.value : ''"
                >
                  {{ item.value }}
                </td>
              </tr>
            </tbody>
          </table>
        </v-flex>
        <ConfirmationDialog
          v-if="confirmDialogActive"
          message="Would you like to book a new job with these details?"
          title="Quote Confirmation"
          @confirm="applyQuoteId"
          :dialogIsActive="true"
          :requiresButtonToOpen="false"
          buttonText="Confirm"
          confirmationButtonText="Confirm"
          @closeDialog="confirmDialogActive = false"
        >
        </ConfirmationDialog>
      </v-flex>
      <v-divider></v-divider>
      <v-layout wrap mt-2 mb-2 pl-2 pr-2>
        <v-btn outline flat color="error" class="action-btn" @click="clearQuote"
          >Cancel</v-btn
        >
        <v-spacer></v-spacer>
        <v-btn
          solo
          depressed
          color="info"
          class="action-btn save"
          @click="applyQuoteId"
          :disabled="!quoteResult"
          :loading="isLoading"
          >Apply Quote</v-btn
        >
      </v-layout>
    </ContentDialog>
  </v-layout>
</template>

<script setup lang="ts">
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import {
  returnEndOfDayFromEpoch,
  returnFormattedDate,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { quoteIdPrefix } from '@/helpers/JobBooking/JobBookingQuoteHelpers.ts';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { QuoteDetails } from '@/interface-models/Jobs/Quote/QuoteDetails';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useJobBookingStore } from '@/store/modules/JobBookingStore';
import { computed, ComputedRef, Ref, ref } from 'vue';

interface QuoteSummary {
  id: string | undefined;
  client: string | undefined;
  quoteId: string;
  date: string;
  serviceTypeName: string;
  references: string;
  pickUpDropOffSuburbList: PUDItem[];
  dispatcherName: string;
  clientCharge: string;
  quoteCreationTime: string;
  quoteExpiryTime: string;
  status: string;
}

enum QuoteStatus {
  VALID = 'VALID',
  INVALID_EXPIRED = 'INVALID_EXPIRED',
  INVALID_PREVIOUSLY_USED = 'INVALID_PREVIOUSLY_USED',
}

interface AppliedQuoteDetails {
  quoteDetails: QuoteDetails | null;
  status: string;
}

const emit = defineEmits<{
  (event: 'applyQuoteId', payload: AppliedQuoteDetails | null): void;
}>();

const jobStore = useJobBookingStore();
const companyDetailsStore = useCompanyDetailsStore();

const isViewingDialog: Ref<boolean> = ref(false);
const confirmDialogActive: Ref<boolean> = ref(false);

const search: Ref<string> = ref('');
const searchedQuotedId: Ref<string> = ref('');

const quoteResult: Ref<QuoteSummary | null> = ref(null);
const quoteFound: Ref<QuoteDetails | null> = ref(null);

const isLoading: Ref<boolean> = ref(false);

/**
 * Returns the status of a quote based on the following conditions:
 *
 * - `VALID`: The quote has not been used to book a job (`bookedJobId` is `null`)
 *   and the quote's expiry time, including the division's grace period,
 *   is greater than or equal to the end of the current day.
 *
 * - `INVALID_PREVIOUSLY_USED`: The quote has already been used to book a job
 *   (`bookedJobId` is not `null`).
 *
 * - `INVALID_EXPIRED`: The quote has not been used to book a job (`bookedJobId` is `null`)
 *   and the quote's expiry time plus the division's grace period
 *   is less than the end of the current day.
 */
const quoteStatus: ComputedRef<QuoteStatus> = computed(() => {
  const graceTime =
    companyDetailsStore.divisionDetails?.customConfig?.quotePreferences
      ?.quoteGracePeriod ?? 0;

  const quote = quoteFound.value;
  const end: number = returnEndOfDayFromEpoch();
  const quoteExpiryTime = quote?.quoteExpiryTime ?? 0;
  const compare: number = quoteExpiryTime + graceTime;

  if (quote?.bookedJobId) {
    if (compare < end) {
      return QuoteStatus.INVALID_EXPIRED;
    }
    return QuoteStatus.INVALID_PREVIOUSLY_USED;
  } else {
    return compare >= end ? QuoteStatus.VALID : QuoteStatus.INVALID_EXPIRED;
  }
});

/**
 * Computed property that returns a summary of quote details formatted for display in a table.
 *
 * - If `quoteResult` is not available (i.e. `null` or `undefined`), returns a default placeholder
 *   table with titles and `'-'` as values.
 * - If `quoteResult` is available, constructs a list of key-value pairs from the quote data,
 *   formatted for display in the `QuoteDetailsTable`.
 *
 * @returns An array of QuoteSummary containing `title` and `value` pairs for the quote summary table.
 */
const quoteSummaryItems = computed(() => {
  if (!quoteResult.value) {
    return [
      { title: 'Quote ID', value: '-' },
      { title: 'Status', value: '-' },
      { title: 'Client', value: '-' },
      { title: 'Created', value: '-' },
      { title: 'Job Date', value: '-' },
      { title: 'Dispatcher', value: '-' },
      { title: 'Service Type', value: '-' },
      { title: 'Client Charge', value: '-' },
      { title: 'Reference', value: '-' },
      { title: 'Stops', value: '-' },
      { title: 'Expires', value: '-' },
    ];
  }

  return [
    { title: 'Quote ID', value: quoteResult.value.quoteId },
    { title: 'Status', value: quoteResult.value.status },
    { title: 'Client', value: quoteResult.value.client },
    { title: 'Created', value: quoteResult.value.quoteCreationTime },
    { title: 'Job Date', value: quoteResult.value.date },
    { title: 'Dispatcher', value: quoteResult.value.dispatcherName },
    { title: 'Service Type', value: quoteResult.value.serviceTypeName },
    { title: 'Client Charge', value: quoteResult.value.clientCharge },
    { title: 'Reference', value: quoteResult.value.references },
    {
      title: 'Stops',
      value: formatSuburbList(quoteResult.value.pickUpDropOffSuburbList),
    },
    { title: 'Expires', value: quoteResult.value.quoteExpiryTime },
  ];
});

/**
 * Searches for a quote using the quote ID entered in the search input.
 * Triggered by pressing the Enter key or clicking the Search button.
 *
 * - Trims the search input and aborts if empty.
 * - Calls the `jobStore.getQuoteById` method to fetch quote details.
 */
const searchQuote = async () => {
  const trimmed = search.value?.replace(/\D/g, '');
  searchedQuotedId.value = trimmed;
  if (!trimmed) {
    quoteResult.value = null;
    return;
  }

  try {
    isLoading.value = true;
    // convert id to number for req
    const id = Number(trimmed);
    const result = await jobStore.getQuoteById(id);
    if (!result || !result.jobDetails) {
      quoteResult.value = null;
      isLoading.value = false;
      return;
    }

    quoteFound.value = result;
    const job = result.jobDetails;

    const isTimeRate = job.rateTypeName === 'Time';
    const dispatcherName =
      job.clientDispatcher?.firstName || job.clientDispatcher?.lastName
        ? `${job.clientDispatcher?.firstName ?? ''} ${
            job.clientDispatcher?.lastName ?? ''
          }`.trim()
        : 'Unknown';
    const cashSaleDispatcher = job.cashSaleClientDetails?.contactName ?? '';

    quoteResult.value = {
      id: result.id,
      quoteId: result.quoteId ? `${quoteIdPrefix} ${result.quoteId}` : '-',
      date: `${job.jobDate} ${job.pudItems[0].pickupTime}`,
      serviceTypeName:
        [job.serviceTypeShortName, job.rateTypeName]
          .filter(Boolean)
          .join(' - ') || 'N/A',
      references: job.allJobReferences || '-',
      pickUpDropOffSuburbList: job.pudItems || [],
      dispatcherName:
        job.client.id === 'CS' ? cashSaleDispatcher : dispatcherName,
      clientCharge: isTimeRate
        ? 'Variable'
        : job.accounting?.totals?.finalTotal?.client !== null
          ? `$${Number(job.accounting.totals.finalTotal.client).toFixed(2)}`
          : 'N/A',

      quoteCreationTime: returnFormattedDate(
        result.quoteCreationTime,
        'DD/MM/YYYY HH:mm',
      ),
      quoteExpiryTime: returnFormattedDate(result.quoteExpiryTime),
      status:
        quoteStatus.value === QuoteStatus.INVALID_PREVIOUSLY_USED
          ? `${formatStatus(quoteStatus.value)} (See Job ID - ${quoteFound.value
              ?.bookedJobId})`
          : formatStatus(quoteStatus.value),
      client: `${job.client.id === 'CS' ? 'CS' : job.client.id} - ${
        job.client.clientName
      }`,
    };
    isLoading.value = false;
  } catch (error) {
    console.error('Error fetching quote:', error);
    quoteResult.value = null;
  }
};

/**
 * Applies the selected quote to the job.
 *
 * - Checks the status of the quote using `quoteStatus.value`.
 * - If the status is `'VALID'`, directly applies the job details from the quote.
 * - If the quote is not valid (e.g. expired or previously used), opens a confirmation dialog.
 * - If the user confirms via the dialog, proceeds to apply the job details anyway.
 *
 * This function is typically called when the user selects a quote to load into the job form.
 */
function applyQuoteId() {
  const appliedQuote: AppliedQuoteDetails = {
    quoteDetails: quoteFound.value,
    status: quoteResult.value?.status ?? '',
  };
  if (quoteStatus.value === QuoteStatus.VALID) {
    emit('applyQuoteId', appliedQuote);
    isViewingDialog.value = false;
  } else {
    if (confirmDialogActive.value) {
      emit('applyQuoteId', appliedQuote);
      isViewingDialog.value = false;
    } else {
      confirmDialogActive.value = true;
    }
  }
}

/**
 * Clears the currently selected quote.
 * - Closes the quote selection dialog.
 * - Resets relevant values.
 */
function clearQuote() {
  quoteFound.value = null;
  quoteResult.value = null;
  search.value = '';
  searchedQuotedId.value = '';
  isViewingDialog.value = false;
  confirmDialogActive.value = false;
}

// return formatted string for QuoteStatus
function formatStatus(status: QuoteStatus): string {
  switch (status) {
    case QuoteStatus.VALID:
      return 'Valid';
    case QuoteStatus.INVALID_EXPIRED:
      return 'Expired';
    case QuoteStatus.INVALID_PREVIOUSLY_USED:
      return 'Previously Used';
    default:
      return 'Invalid';
  }
}

// format list of pud items to display first and last stop
function formatSuburbList(pudList: PUDItem[]): string {
  if (!pudList || pudList.length === 0) {
    return '';
  }
  if (pudList.length === 1) {
    return pudList?.[0]?.address?.suburb || 'Unknown';
  }
  if (pudList.length === 2) {
    return `${pudList[0].address?.suburb} → ${pudList[1].address?.suburb}`;
  }

  return `${pudList[0].address?.suburb} (+ ${pudList.length - 2}) ${pudList[
    pudList.length - 1
  ].address?.suburb}`;
}
</script>

<style scoped lang="scss">
.quotes-container {
  display: flex;
}
.quoteSearch-btn {
  border-radius: $border-radius-btn;
  max-width: max-content;
  margin-right: 22px;
  color: $warning !important;
  padding: 12px;
  border: 1px solid !important;
}

.action-btn {
  border-radius: $border-radius-btn;
  box-shadow: none !important;
  min-width: 120px;
  min-height: 38px;
  &:hover {
    box-shadow: $box-shadow !important;
  }
}

.selected-job {
  display: flex;
  flex-direction: row;
  align-content: left;
  align-items: left;
  font-weight: 600;
  font-size: $font-size-18;
  margin: 0 auto !important;
  .selected-job-id {
    color: $success;
    padding-left: 8px;
  }
}
.quote-text {
  font-size: 36px;
  font-weight: 700;
  margin-right: 4px;
  color: var(--light-text-color);
}

.search-btn {
  border: 1px solid $primary-light;
  border-radius: $border-radius-base;
  background-color: $primary !important;
  color: white;
  margin-left: 28px;
  max-width: 200px;
  &:hover {
    cursor: pointer;
    transition: 0.3s;
    box-shadow: $box-shadow;
  }
}

.top-container {
  display: flex;
  flex-wrap: wrap;
  align-content: center;
  justify-content: center;
  margin: 0 20px;
  padding-top: 8px;
}

.no-data-text {
  font-size: $font-size-22 !important;
  color: var(--light-text-color);
}

.summary-container {
  display: flex;
  flex-wrap: wrap;
  align-content: center;
  justify-content: center;
  padding-bottom: 8px;

  .summary-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background-color: var(--background-color-400) !important;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.5s;

    tr:first-child {
      background-color: var(--table-row);
    }

    td {
      padding: 8px;
      border-bottom: 1px solid var(--border-color) !important;
    }

    .title-txt {
      font-weight: 400;
      color: var(--light-text-color);
      text-align: left;
      font-size: $font-size-16;
      width: 40%;
    }
    .value-txt {
      font-weight: 500;
      color: var(--text-color);
      text-align: right;
      font-size: $font-size-16;
      width: 60%;

      &.Valid {
        color: $success;
      }
      &.Expired {
        color: $error;
      }
      &.Previously {
        color: $warning;
      }
    }

    &.faded {
      opacity: 0.2;
    }
  }
}
</style>
