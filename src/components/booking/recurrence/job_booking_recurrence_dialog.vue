<template>
  <ContentDialog
    :showDialog.sync="dialogController"
    title="Recurrence Details"
    width="70%"
    contentPadding="pa-2"
    @cancel="dialogController = false"
    @confirm="applyChanges"
    :isConfirmUnsaved="false"
    :isDisabled="false"
    :isLoading="false"
    :showActions="false"
  >
    <v-flex class="body-scrollable--65 body-min-height--65 container">
      <v-flex md6 class="right-side">
        <h1 class="header--light ml-2">Summary Info</h1>
        <RecurrenceVisualisation
          :recurringJobDetails="editedRecurrenceDetails"
        />
      </v-flex>
      <v-divider vertical></v-divider>
      <v-flex md6 class="left-side">
        <CustomRecurrence :recurringJob="editedRecurrenceDetails" />
      </v-flex>
    </v-flex>

    <v-divider></v-divider>
    <v-flex class="btn-container">
      <v-btn
        class="action-btn"
        outline
        justify-start
        flat
        color="error"
        @click="dialogController = false"
        >Cancel</v-btn
      >
      <v-btn
        justify-end
        solo
        depressed
        color="info"
        class="action-btn confirm"
        @click="applyChanges"
        >Save Delivery Information</v-btn
      >
    </v-flex>
  </ContentDialog>
</template>

<script setup lang="ts">
import CustomRecurrence from '@/components/common/recurring_job/custom_recurrence.vue';
import RecurrenceVisualisation from '@/components/common/recurring_job/recurrence_visualisation.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { deepCopy } from '@/helpers/JsonHelpers/JsonHelpers';
import { RecurringJobDetails } from '@/interface-models/Jobs/RecurringJobDetails';
import { Ref, WritableComputedRef, computed, ref, toRef, watch } from 'vue';

const emit = defineEmits<{
  (event: 'update:isDialogOpen', payload: boolean): void;
  (event: 'applyChanges', payload: RecurringJobDetails): void;
}>();

const props = withDefaults(
  defineProps<{
    recurrenceDetails: RecurringJobDetails;
    isDialogOpen?: boolean;
  }>(),
  {
    isDialogOpen: false,
  },
);

const recurringJobDetails: Ref<RecurringJobDetails> = toRef(
  props,
  'recurrenceDetails',
);

const editedRecurrenceDetails: Ref<RecurringJobDetails> = ref(
  new RecurringJobDetails(),
);

/**
 * Controls visibility of ContentDialog. Gets and sets prop isDialogOpen.
 */
const dialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.isDialogOpen;
  },
  set(value: boolean): void {
    emit('update:isDialogOpen', value);
  },
});

/**
 * Watches for changes in the dialogController value. If the dialog is opened,
 * copy the current recurrence details from props to the working copy. If the
 * dialog is closed, reset editedRecurrenceDetails.
 */
watch(dialogController, (newValue) => {
  if (newValue) {
    editedRecurrenceDetails.value = deepCopy(props.recurrenceDetails);
  } else {
    editedRecurrenceDetails.value = new RecurringJobDetails();
  }
});

/**
 * Applies current changes to recurrenceDetails and emits the changes to the
 * parent.
 */
function applyChanges() {
  emit('applyChanges', editedRecurrenceDetails.value);
  dialogController.value = false;
}
</script>
<style scoped lang="scss">
.container {
  display: flex;
  flex-direction: row-reverse !important;

  .left-side {
    margin-right: 12px;
  }
  .right-side {
    margin-left: 12px;
  }
}

.btn-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-content: center;
  margin: 6px 12px;
  .action-btn {
    border-radius: 8px;
    width: 10%;
    &.confirm {
      width: 50%;
      margin-left: 20px;
    }
  }
}
</style>
