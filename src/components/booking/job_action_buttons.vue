<template>
  <v-tooltip bottom v-if="isIcon">
    <template v-slot:activator="{ on }">
      <v-btn
        class="booking-icon-btn"
        :disabled="disabled"
        :icon="isIcon"
        flat
        v-on="isIcon ? on : {}"
        @click="emit('click')"
      >
        <v-icon :size="iconSize" class="icon">{{ icon }}</v-icon>
        <span v-if="!isIcon">{{ label }}</span>
      </v-btn>
    </template>
    <span>{{ label }}</span>
  </v-tooltip>

  <v-btn
    v-else
    flat
    class="card-btn"
    :disabled="disabled"
    @click="emit('click')"
  >
    <div class="btn-content">
      <v-icon :size="iconSize" class="icon">{{ icon }}</v-icon>
      <span>{{ label }}</span>
    </div>
  </v-btn>
</template>

<script setup lang="ts">
import { computed, defineEmits, defineProps } from 'vue';
const props = defineProps<{
  icon: string;
  label: string;
  isIcon: boolean;
  disabled: boolean;
}>();
const emit = defineEmits(['click', 'scroll']);
const iconSize = computed(() => (props.isIcon ? 18 : 22));
</script>

<style scoped lang="scss">
.card-btn {
  text-transform: uppercase;
  background-color: transparent !important;
  transition: all 0.3s ease;
  width: 100%;
  height: 100%;
  height: 54px;
  margin: 0;
  padding: 0;
  .btn-content {
    display: flex;
    width: 100%;
    color: var(--light-text-color);
    font-weight: 600;
    font-size: $font-size-14;
    .icon {
      margin-right: 8px;
      padding-left: 8px;
      color: var(--accent) !important;
    }
  }

  &:hover {
    background-color: $info !important;
    transform: translate(-3px, -2px);
    .btn-content,
    .icon {
      color: white !important;
    }
  }
}
.booking-icon-btn {
  bottom: 60px;
  margin: 6px;
}

// Responsive Styles
@media (max-width: 1500px) {
  .btn-content {
    font-size: $font-size-10 !important;
    font-weight: 400 !important;
  }
  .booking-icon-btn {
    bottom: 40px;
  }
}

@media (max-width: 1024px) {
  .btn-content {
    width: 50% !important;
    font-size: 0px !important;
  }
}
</style>
