<template>
  <v-layout justify-space-between align-center>
    <div
      class="pud-scroll-btn left"
      @mouseover="startScrollToRight()"
      @mouseleave="stopScroll"
      @click="fastScrollToLeft"
      v-if="jobDetails.pudItems.length > 4"
    >
      <v-icon>arrow_back_ios</v-icon>
    </div>
    <v-layout
      class="pud-items-pills"
      id="scrollBar"
      ref="scrollBar"
      style="overflow-x: auto; white-space: nowrap"
    >
      <v-flex
        class="pud-bar"
        md12
        v-for="(pudItem, pudIndex) in jobDetails.pudItems"
        :key="pudItem.uniqueId"
        :class="{
          'hover-active': isHovered === pudItem.uniqueId,
        }"
        :id="'pudItem-' + pudItem.uniqueId"
        @click="scrollToItem(pudItem.uniqueId ?? '')"
        @mouseover="handleMouseOver(pudItem.uniqueId)"
        @mouseleave="handleMouseLeave()"
      >
        <v-tooltip bottom :disabled="!pudProgress[pudIndex]">
          <template v-slot:activator="{ on }">
            <div
              v-on="on"
              class="pud-item-flag"
              :class="[
                pudItem.legTypeFlag,
                {
                  'status-arrived': pudItem.status === 'ARRIVED',
                  'status-finished': pudItem.status === 'FINISHED',
                  'status-none': !pudItem.status || pudItem.status === null,
                },
              ]"
            ></div>
            <span class="pud-item-flag-text">
              <span class="pud-type" :class="pudItem.legTypeFlag">
                {{ pudItem.legTypeFlag }}</span
              >-
              {{
                pudItem.customerDeliveryName || pudItem.address.formattedAddress
              }}</span
            >
          </template>
          <span>{{ pudProgress[pudIndex] }}</span>
        </v-tooltip>
      </v-flex>
    </v-layout>
    <div
      class="pud-scroll-btn right"
      @mouseover="startScrollToLeft()"
      @mouseleave="stopScroll"
      @click="fastScrollToRight"
      v-if="jobDetails.pudItems.length > 4"
    >
      <v-icon>arrow_forward_ios</v-icon>
    </div>
  </v-layout>
</template>
<script setup lang="ts">
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { JobRouteProgress } from '@/interface-models/Jobs/JobRouteProgress';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { computed, onUnmounted, Ref, ref } from 'vue';

const props = defineProps<{
  jobDetails: JobDetails;
  jobBookingDeliverySummaryRef: JobBookingDeliverySummaryMethods;
}>();

interface JobBookingDeliverySummaryMethods {
  scrollToPudItem: (elementId: string) => void;
}
const isHovered: Ref<string> = ref('');
// const jobBookingDeliverySummary: Ref<any> = ref(null);

// Mouse event handlers for pud pill bar
function handleMouseOver(uniqueId: string | undefined) {
  isHovered.value = uniqueId ?? '';
}
function handleMouseLeave() {
  isHovered.value = '';
}
const scrollBar = ref<HTMLElement | null>(null);
const scrollSpeed = ref(300); // Initial scroll speed
let scrollInterval: number | undefined = undefined;

// Smooth scroll to the left when hovering over the left button
const startScrollToLeft = () => {
  stopScroll();
  scrollInterval = window.setInterval(() => {
    if (scrollBar.value) {
      scrollBar.value.scrollLeft = Math.min(
        scrollBar.value.scrollLeft + scrollSpeed.value,
        scrollBar.value.scrollWidth - scrollBar.value.clientWidth, // Prevent scrolling beyond the right end
      );
    }
  }, 64); // 60 FPS
};

// Smooth scroll to the right when hovering over the right button
const startScrollToRight = () => {
  stopScroll();
  scrollInterval = window.setInterval(() => {
    if (scrollBar.value) {
      scrollBar.value.scrollLeft = Math.max(
        scrollBar.value.scrollLeft - scrollSpeed.value,
        0, // Prevent scrolling beyond the left end
      );
    }
  }, 64); // 60 FPS
};

// Stop the scrolling (on mouse leave)
const stopScroll = () => {
  if (scrollInterval) {
    clearInterval(scrollInterval);
    scrollInterval = undefined;
  }
};

// Fast scroll to the left (when clicking the right button)
const fastScrollToLeft = () => {
  stopScroll();
  if (scrollBar.value) {
    scrollBar.value.scrollTo({
      left: 0,
      behavior: 'smooth',
    });
  }
};

// Fast scroll to the right (when clicking the left button)
const fastScrollToRight = () => {
  stopScroll();
  if (scrollBar.value) {
    scrollBar.value.scrollTo({
      left: scrollBar.value.scrollWidth,
      behavior: 'smooth',
    });
  }
};

function scrollToItem(pudId: string) {
  if (props.jobBookingDeliverySummaryRef) {
    props.jobBookingDeliverySummaryRef.scrollToPudItem(pudId);
  }
}

// Computed property for pudProgress
const pudProgress = computed((): string[] => {
  if (!props.jobDetails || !props.jobDetails.additionalJobData) {
    return [];
  }

  const progressData: JobRouteProgress[] =
    props.jobDetails.additionalJobData.routeProgress;
  const expectedArrivals: string[] = [];

  for (const pudProgressData of progressData) {
    const pudItem: PUDItem | undefined = props.jobDetails.pudItems.find(
      (x: PUDItem) => x.pudId === pudProgressData.pudId,
    );

    if (!pudItem) {
      expectedArrivals.push('');
      continue;
    }

    let progressTooltipValue = '';

    if (!pudItem.status) {
      progressTooltipValue =
        pudProgressData.expectedArrivalTimeReadable &&
        pudProgressData.expectedArrivalTime
          ? 'Estimate Arrival: ' +
            returnFormattedDate(pudProgressData.expectedArrivalTime) +
            ' ' +
            pudProgressData.expectedArrivalTimeReadable
          : '';
    } else if (
      pudItem.status === 'ARRIVED' &&
      pudProgressData.expectedDepartureTime &&
      pudProgressData.expectedDepartureTimeReadable
    ) {
      progressTooltipValue =
        'Estimate Departure ' +
        returnFormattedDate(
          pudProgressData.expectedDepartureTime,
          'DD/MM/YY HH:mm',
        );
    } else if (
      pudItem.status === 'FINISHED' &&
      pudProgressData.actualArrivalTime &&
      pudProgressData.actualDepartureTime
    ) {
      progressTooltipValue =
        ' Arrived ' +
        returnFormattedDate(
          pudProgressData.actualArrivalTime,
          'DD/MM/YY HH:mm',
        ) +
        ' | Departed ' +
        returnFormattedDate(
          pudProgressData.actualDepartureTime,
          'DD/MM/YY HH:mm',
        );
    }

    expectedArrivals.push(progressTooltipValue);
  }

  return expectedArrivals;
});

onUnmounted(() => {
  stopScroll();
});
</script>

<style scoped lang="scss">
.pud-scroll-btn {
  cursor: pointer;
  border-radius: 20px;
  position: relative;
  background-color: transparent;
  transform: translate(0px, -10px);
  opacity: 0.1;
  transition: all 0.3s ease;
  &:hover {
    opacity: 0.7;
  }
}

.pud-items-pills {
  overflow-x: auto;
  white-space: nowrap;
  scroll-behavior: smooth;
  /* Hide scroll bar for Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  cursor: pointer;
  &::-webkit-scrollbar {
    display: none; /* Hides the scroll bar */
  }
  .pud-bar {
    display: inline-block;
    min-width: 200px;
    opacity: 0.7;
    .pud-item-flag {
      height: 10px;
      max-width: auto;
      min-width: none;
      margin-right: 10px;
      border-radius: $border-radius-base;
      opacity: 0.6;
      margin-bottom: 4px;
      transition: all 0.3s ease;
      opacity: 0.7;
      &.status-none {
        background-color: transparent;
        border: 1px solid var(--light-text-color);
      }
      &.status-arrived {
        opacity: 1;
        background-color: $translucent-highlight;
        border: 1px solid var(--accent);
      }
      &.status-finished {
        opacity: 1;
        background-color: var(--accent);
      }
      transition: opacity 0.3s ease;
    }
    .pud-item-flag-text {
      text-transform: uppercase;
      margin-right: 8px;
      font-size: $font-size-11;
      height: auto;
      width: auto;
      color: var(--text-color);
      display: -webkit-box;
      -webkit-box-orient: horizontal;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
      visibility: visible;
      transition:
        opacity 0.3s ease,
        visibility 0.3s ease;
      scale: 0.3s ease;
      .pud-type {
        font-weight: 800;
        padding-right: 2px;
        font-family: $font-sans;

        &.P {
          color: $pickup;
        }
        &.D {
          color: $drop;
        }
      }
    }
  }
  /* Hover effect */
  .hover-active {
    .pud-item-flag {
      opacity: 1;
      // scale: 1.05;
      &.P {
        background-color: $pickup-highlight !important;
        box-shadow: 1px 1px 10px $pickup-highlight;
      }
      &.D {
        background-color: $drop-highlight !important;
        box-shadow: 1px 1px 10px $drop-highlight;
      }
    }
    .pud-item-flag-text {
      opacity: 1;
      color: var(--text-color);
      font-weight: 700;
    }
  }
}
.clicked-item {
  border-color: var(--primary);
  transition: color 1s ease;
}
</style>
