<template>
  <div class="job-booking-progress-chips">
    <div class="pud-progress-container" v-if="pudRouteProgress">
      <span
        class="mr-2 tag-accent-card blue-outline"
        v-if="pudRouteProgress.expectedArrivalTimeReadable"
      >
        Est. Arrival: {{ pudRouteProgress.expectedArrivalTimeReadable }}
        <v-icon
          size="10"
          class="pl-1"
          v-if="!isClientPortal && pudRouteProgress.showLocationIcon"
          color="#FFFFFF"
        >
          fas fa-location
        </v-icon>
      </span>
      <span
        class="mr-2 tag-accent-card white-outline"
        v-if="driverLocationUnknown"
      >
        Drivers Location Unknown
      </span>
      <span
        class="mr-2 tag-accent-card"
        :class="
          pudRouteProgress.arrivalDifferenceWarning === null
            ? ''
            : !pudRouteProgress?.arrivalDifferenceWarning
              ? 'green-outline'
              : 'red-outline'
        "
        v-if="
          pudRouteProgress.arrivalDifferenceInMins &&
          (isNextStop || pudStatus !== null)
        "
      >
        {{ pudRouteProgress.arrivalDifferenceInMins }}
      </span>
      <span
        class="mr-2 tag-accent-card"
        v-if="pudRouteProgress.actualLoadTimeReadable"
        :class="
          pudRouteProgress.loadTimeWarning ? 'red-outline' : 'green-outline'
        "
      >
        Load: {{ pudRouteProgress.actualLoadTimeReadable }}
      </span>
      <span
        class="mr-2 tag-accent-card"
        v-if="
          pudRouteProgress.differenceInLoadTimeReadable &&
          !pudRouteProgress.actualLoadTimeReadable
        "
        :class="
          pudRouteProgress.loadTimeWarning ? 'red-outline' : 'green-outline'
        "
      >
        Load: {{ pudRouteProgress.differenceInLoadTimeReadable }}
      </span>
      <span
        class="mr-2 tag-accent-card blue-outline"
        v-if="!pudRouteProgress.actualDepartureTime && pudStatus === 'ARRIVED'"
      >
        Est. Departure:
        {{ pudRouteProgress.expectedDepartureTimeReadable }}
      </span>
    </div>

    <!-- <span class="mr-2 tag-accent-card amber-outline" v-if="createdByDriver">
      Created by Driver
    </span>
    <span class="mr-2 tag-accent-card amber-outline" v-if="isOutsideMetro">
      Outside Metro Area
    </span>
    <span class="mr-2 tag-accent-card blue-outline" v-if="isStandbyRate">
      Standby Address
    </span> -->
  </div>
</template>

<script setup lang="ts">
import { JobRouteProgress } from '@/interface-models/Jobs/JobRouteProgress';

const props = withDefaults(
  defineProps<{
    pudRouteProgress: JobRouteProgress;
    pudStatus?: 'ARRIVED' | 'FINISHED' | null;
    isNextStop?: boolean;
    isClientPortal?: boolean;
    createdByDriver?: boolean;
    isOutsideMetro?: boolean;
    isStandbyRate?: boolean;
    driverLocationUnknown?: boolean;
  }>(),
  {
    isClientPortal: false,
    isNextStop: false,
    pudStatus: null,
    createdByDriver: false,
    isOutsideMetro: false,
    isStandbyRate: false,
    driverLocationUnknown: false,
  },
);
</script>
<style scoped lang="scss">
.job-booking-progress-chips {
  display: flex;
  flex-direction: row;
  margin-left: 12px;

  .pud-progress-container {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .tag-accent-card {
    align-self: flex-start;
    border-radius: $border-radius-base;
    font-size: $font-size-10;
    padding: 2px 8px;
    font-weight: 800;
    font-family: $font-sans;
    display: flex;
    align-items: center;
    margin-top: 4px;
    margin-left: 4px;
    gap: 10px;

    &.green-outline {
      outline: 1px solid $success;
      color: var(--bg-light-green);
    }

    &.green-fill {
      background-color: $toast-success-text;
      border: 1px solid $toast-success-bg;
    }

    &.blue-outline {
      outline: 1px solid $highlight-dark;
      color: var(--bg-light-blue);
    }

    &.amber-outline {
      outline: 1px solid $warning;
      color: var(--bg-light-yellow);
    }

    &.red-outline {
      outline: 1px solid $error;
      color: var(--bg-light-red);
    }
  }
}

// Responsive Styles
@media (max-width: 1500px) {
  .tag-accent-card {
    padding: 2px 4px !important;
    font-size: 7px !important;
  }
}
</style>
