<template>
  <ContentDialog
    :showDialog.sync="dialogController"
    :title="
      viewType === RouteDialogViewType.REORDER
        ? 'Reorder Stops'
        : 'View Route on Map'
    "
    contentPadding="pa-0"
    @cancel="dialogController = false"
    @confirm="applyChanges"
    width="80%"
    :showActions="viewType === RouteDialogViewType.REORDER"
    @action="restoreJobToOriginal"
    :isDisabled="viewType === RouteDialogViewType.ROUTE || !orderHasChanged"
    :showActionButton="orderHasChanged"
    actionBtnText="Restore to Original"
    confirmBtnText="Confirm"
    :isConfirmUnsaved="orderHasChanged"
  >
    <div class="dialog-content">
      <div
        v-if="viewType === RouteDialogViewType.REORDER && jobDetailsCopy"
        id="route-dialog-order-scrollable"
        md12
        class="reorder-container pa-3"
      >
        <draggable
          v-model="jobDetailsCopy.pudItems"
          @change="stopOrderChanged"
          class="draggable-container"
          draggable=".active-drag"
          handle=".arrow-icon-container"
          v-bind="{
            chosenClass: 'app-bgcolor--700',
          }"
        >
          <transition-group>
            <div
              class="dashboard-card no-hover"
              v-for="(pudItem, index) in jobDetailsCopy.pudItems"
              :key="pudItem.uniqueId"
              :class="[
                disableDrag(pudItem, index) ? 'disable-drag' : 'active-drag',
                pudItem.legTypeFlag,
              ]"
              style="margin: 6px 0px"
            >
              <v-layout
                justify-space-between
                align-center
                class="dashboard-card__toprow"
              >
                <div
                  class="dashboard-card__bottomrow"
                  :class="{ disabled: pudItem.status !== null }"
                  style="user-select: none"
                >
                  <div class="icon-container">
                    <span class="index-number">#{{ index + 1 }}</span>
                  </div>
                </div>
                <v-flex pl-2 pr-4>
                  <v-layout column>
                    <span class="title-text">
                      <span
                        class="pr-2"
                        v-if="returnOriginalStopIndex(pudItem) !== index"
                      >
                        <InformationTooltip
                          :right="true"
                          :tooltipType="HealthLevel.WARNING"
                        >
                          <v-layout slot="content" row wrap>
                            <v-flex md12>
                              <p class="mb-1">
                                Originally Stop #{{
                                  returnOriginalStopIndex(pudItem) + 1
                                }}
                              </p>
                            </v-flex>
                          </v-layout>
                        </InformationTooltip>
                      </span>
                      <!-- <span :class="pudItem.legTypeFlag">{{
                        pudItem.legTypeFlag
                      }}</span> -->
                      <!-- - -->
                      {{
                        pudItem.customerDeliveryName || pudItem.address.suburb
                      }}
                      <span v-if="pudItem.status" class="pl-2"
                        >-
                        {{
                          pudItem.status === 'ARRIVED' ? 'On Site' : 'Finished'
                        }}</span
                      ></span
                    >
                    <span style="font-size: 10px; color: rgb(190, 192, 205)">
                      {{ pudItem.address.formattedAddress }}
                    </span>
                  </v-layout>
                </v-flex>

                <v-tooltip bottom>
                  <template v-slot:activator="{ on }">
                    <v-btn
                      flat
                      v-on="on"
                      icon
                      :disabled="disableDrag(pudItem, index)"
                      @click="sendToTop(pudItem)"
                      class="my-0"
                    >
                      <v-icon class="arrow-icon"> far fa-arrow-to-top </v-icon>
                    </v-btn>
                  </template>
                  {{
                    pudItem.legTypeFlag === 'P'
                      ? 'Send to Top (make this stop the first pickup)'
                      : 'Send to Top'
                  }}
                </v-tooltip>
                <v-tooltip bottom>
                  <template v-slot:activator="{ on }">
                    <v-btn
                      flat
                      v-on="on"
                      icon
                      :disabled="disableDrag(pudItem, index)"
                      @click="sendToBottom(pudItem)"
                      class="my-0"
                    >
                      <v-icon class="arrow-icon">far fa-arrow-to-bottom</v-icon>
                    </v-btn>
                  </template>
                  Send to Bottom
                </v-tooltip>
                <v-divider vertical></v-divider>
                <v-btn
                  flat
                  icon
                  class="my-0 arrow-icon-container"
                  :disabled="disableDrag(pudItem, index)"
                >
                  <v-icon class="arrow-icon">far fa-grip-lines</v-icon>
                </v-btn>
              </v-layout>
            </div>
          </transition-group>
        </draggable>
        <!-- <v-layout row wrap v-if="editingServiceTypeList !== null" md12>
      </v-layout> -->
      </div>
      <div
        class="map-container"
        :class="{
          'route-view': viewType === RouteDialogViewType.ROUTE,
          'reorder-view': viewType === RouteDialogViewType.REORDER,
        }"
      >
        <JobMapRoute
          v-if="showMap && jobDetailsCopy"
          ref="jobMapRouteComponent"
          :jobDetails="jobDetailsCopy"
          :mapConfig="jobMapConfig"
          mapId="bookingRouteDialog"
        >
        </JobMapRoute>
      </div>
    </div>
  </ContentDialog>
</template>

<script setup lang="ts">
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import {
  JobRouteMapConfig,
  RouteViewType,
} from '@/components/operations/maps/job_map_route/JobRouteMapConfig';
import JobMapRoute from '@/components/operations/maps/job_map_route/index.vue';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { initialiseJobDetails } from '@/helpers/classInitialisers/InitialiseJobDetails';
import { initialisePudItem } from '@/helpers/classInitialisers/InitialisePudItem';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import ORSRoute from '@/interface-models/Generic/Route/ORSRoute';
import type { JobDetails } from '@/interface-models/Jobs/JobDetails';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import {
  ComputedRef,
  Ref,
  WritableComputedRef,
  computed,
  nextTick,
  ref,
  toRef,
  watch,
} from 'vue';
import draggable from 'vuedraggable';

const emit = defineEmits<{
  (event: 'update:isDialogOpen', payload: boolean): void;
  (event: 'reorderPudItems', payload: ReorderPudPayload): void;
}>();

const props = withDefaults(
  defineProps<{
    isDialogOpen: boolean;
    jobDetails: JobDetails;
    type: RouteDialogViewType;
  }>(),
  {
    isDialogOpen: false,
  },
);

const viewType: Ref<RouteDialogViewType> = toRef(props, 'type');

const jobDetailsCopy: Ref<JobDetails | null> = ref(null);

const jobMapRouteComponent: Ref<any> = ref(null);

const showMap: Ref<boolean> = ref(false);
const jobMapConfig: JobRouteMapConfig = {
  defaultView: RouteViewType.PLANNED,
  enableViewToggle: false,
  showCapturedRoute: false,
  showPlannedRoute: true,
  showStopMarkers: false,
  showCapturedRouteSlider: false,
};

/**
 * Returns true if the pudItems order has changed when compared with the
 * original from jobDetails.pudItems
 */
const orderHasChanged: ComputedRef<boolean> = computed(() => {
  if (jobDetailsCopy.value === null) {
    return false;
  }
  if (!jobDetailsCopy.value.pudItems || !props.jobDetails.pudItems) {
    return false;
  }
  return jobDetailsCopy.value.pudItems.some(
    (pudItem, index) =>
      pudItem.uniqueId !== props.jobDetails.pudItems[index].uniqueId,
  );
});

/**
 * Gets and sets the isDialogOpen prop passed from parent
 */
const dialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.isDialogOpen;
  },
  set(value: boolean): void {
    emit('update:isDialogOpen', value);
  },
});

/**
 * Used to determine if a pudItem should be draggable. A pudItem should not be
 * draggable if it has been actioned or if it is the first stop.
 * @param pudItem - The pudItem to check
 * @param index - The index of the pudItem in the pudItems array
 */
function disableDrag(pudItem: PUDItem, index: number): boolean {
  return pudItem.status !== null || index === 0;
}

/**
 * Returns the index of the pudItem in the original jobDetails.pudItems array.
 * Called from the template so we can determine if a stop was moved.
 * @param {PUDItem} pudItem - The pudItem to find the index of
 *
 */
function returnOriginalStopIndex(pudItem: PUDItem): number {
  const idx = props.jobDetails.pudItems.findIndex(
    (s) => s.pudId === pudItem.pudId || s.uniqueId === pudItem.uniqueId,
  );
  return idx;
}

async function stopOrderChanged(): Promise<void> {
  try {
    const route = await jobDetailsCopy.value!.getPlannedRoute();
    if (
      !route?.routes?.length ||
      !route.routes[0].segments?.length ||
      route.routes[0].segments.length <
        jobDetailsCopy.value!.pudItems.length - 1
    ) {
      throw new Error(
        '1 or more points are not reachable. Please check the route.',
      );
    }
    jobDetailsCopy.value!.plannedRoute = route;

    // Update the map with the new route
    refreshMapInChild();
  } catch (e) {
    logConsoleError(
      'Something went wrong in job_booking_route-dialog > stopOrderChanged',
      e,
    );
    let errorMessage = GENERIC_ERROR_MESSAGE;
    if (e instanceof Error) {
      errorMessage = e.message;
    }
    showNotification(errorMessage);
    restoreJobToOriginal();
  }
}

/**
 * Called method in child to refresh the routes and markers on the map. This is
 * necessary because the map does not dynamically update when the route in the
 * job is updated.
 */
function refreshMapInChild() {
  const foundMapComponent = jobMapRouteComponent.value;
  if (foundMapComponent) {
    foundMapComponent.refreshPlannedRoute(status);
  }
}

/**
 * When the visibility of the dialog changes, set the local working copies of
 * the pudItems and plannedRoute
 */
watch(dialogController, (newValue) => {
  if (newValue) {
    restoreJobToOriginal();
    nextTick(() => {
      showMap.value = true;
    });
  } else {
    showMap.value = false;
    jobDetailsCopy.value = null;
  }
});

/**
 * Copies the jobDetails prop to the jobDetailsCopy
 */
function restoreJobToOriginal(): void {
  jobDetailsCopy.value = initialiseJobDetails(props.jobDetails);
}

/**
 * Moves the provided PUD Item to the index of the first PUD Item where status is null
 *
 * @param {PUDItem} pudItem - The selected PUD that we want to move to the first in the list
 */
function sendToTop(pudItem: PUDItem): void {
  if (!jobDetailsCopy.value) {
    return;
  }
  const id = pudItem.pudId || pudItem.uniqueId;
  // Find the pudItem in the editing list
  const pudIndex = jobDetailsCopy.value.pudItems.findIndex(
    (s) => s.pudId === id || s.uniqueId === id,
  );
  if (pudIndex === -1) {
    return;
  }
  // First remove it from the existing list
  const pudToMove = jobDetailsCopy.value.pudItems.splice(pudIndex, 1)[0];

  // If the pud we're re-inserting is a pickup, then we should allow it to be
  // moved to the first position to replace the current first pickup
  const allowMoveToFirstPud = pudToMove.legTypeFlag === 'P';

  // Find the index of the first PUD Item that is not disabled
  const firstEligibleIndex = jobDetailsCopy.value.pudItems.findIndex(
    (pudItem, index) =>
      // If allowMoveToFirstPud is true, then just find the first index where
      // status is null. If false, then use the same logic that we use to
      // disable dragging
      allowMoveToFirstPud
        ? pudItem.status === null
        : !disableDrag(pudItem, index),
  );

  // If no PUD Item with status null is found, push the item to the end
  const targetIndex =
    firstEligibleIndex === -1
      ? jobDetailsCopy.value.pudItems.length
      : firstEligibleIndex;

  // Insert the PUD Item at the target index
  jobDetailsCopy.value.pudItems.splice(targetIndex, 0, pudToMove);

  // Call function to update the route
  stopOrderChanged();
}

/**
 * Moves the provided PUD Item to the last of the list
 *
 * @param {PUDItem} pudItem - The selected PUD that we want to move to the last in the list
 */
function sendToBottom(pudItem: PUDItem): void {
  if (!jobDetailsCopy.value) {
    return;
  }
  const id = pudItem.pudId || pudItem.uniqueId;
  // Find the pudItem in the editing list and move it to the first index in
  // the array
  const pudIndex = jobDetailsCopy.value.pudItems.findIndex(
    (s) => s.pudId === id || s.uniqueId === id,
  );
  if (pudIndex === -1) {
    return;
  }
  const pudToMove = jobDetailsCopy.value.pudItems.splice(pudIndex, 1);
  jobDetailsCopy.value.pudItems.push(pudToMove[0]);

  // Call function to update the route
  stopOrderChanged();
}

/**
 * Request the route one last time using the current pud selection, and set it
 * to the job again. This is to account for any slowness in the drag and drop
 * operation. Emit the updated pudItems and plannedRoute to the parent.
 */
async function applyChanges() {
  if (!jobDetailsCopy.value) {
    return;
  }
  try {
    const route: ORSRoute | null = await jobDetailsCopy.value.getPlannedRoute();
    // Length of segments should be equal to the number of pud items minus 1
    if (
      !route?.routes?.length ||
      !route.routes[0].segments?.length ||
      route.routes[0].segments.length < jobDetailsCopy.value.pudItems.length - 1
    ) {
      throw new Error(
        '1 or more points are not reachable. Please check the route.',
      );
    }
    // Re-initialise the pudItems, but copy the uniqueId from the original. This
    // is to prevent component remounts due to key updates
    const pudItems = jobDetailsCopy.value.pudItems.map((pud) => {
      const newPud = initialisePudItem(pud);
      newPud.uniqueId = pud.uniqueId;
      return newPud;
    });

    // Emit to parent
    emit('reorderPudItems', {
      pudItems: pudItems,
      plannedRoute: route,
    });

    dialogController.value = false;
  } catch (e) {
    logConsoleError(
      'Something went wrong in job_booking_route-dialog > applyChanges',
      e,
    );
    let errorMessage = GENERIC_ERROR_MESSAGE;
    if (e instanceof Error) {
      errorMessage = e.message;
    }
    showNotification(errorMessage);
    return null;
  }
}
</script>
<script lang="ts">
export enum RouteDialogViewType {
  ROUTE = 'ROUTE',
  REORDER = 'REORDER',
  // OPTIMISE
}

export interface ReorderPudPayload {
  pudItems: PUDItem[];
  plannedRoute: ORSRoute;
}
</script>

<style scoped lang="scss">
.dialog-content {
  height: 80vh;
  width: 79.5vw;
  position: relative;
  overflow: hidden;

  display: flex;
  flex-direction: row;
}
.map-container {
  // max-height: 80vh;
  // max-width: 80vw;

  height: 80vh;
  // width: 90vw;

  &.route-view {
    width: 80vw;
  }
  &.reorder-view {
    width: 50vw;
  }
}
.reorder-container {
  height: 80vh;
  max-height: 80vh;
  width: 30vw;
  max-width: 30vw;
  overflow-y: auto;
}

.draggable-container {
  .dashboard-card {
    border-radius: 2px 8px 8px 2px;
    &.active-drag {
      background-color: var(--background-color-200);
    }
    &.P {
      border-left: 2px solid $pickup;
    }
    &.D {
      border-left: 2px solid $drop;
    }
    .arrow-icon {
      color: var(--text-color);
    }
    .dashboard-card__toprow {
      .title-text {
        color: var(--text-color);
        .P {
          color: $pickup;
        }
        .D {
          color: $drop;
        }
        &.disabled {
          color: var(--light-text-color);
          opacity: 0.8;
        }
      }
      .dashboard-card__bottomrow {
        .icon-container {
          .index-number {
            font-size: $font-size-16;
            color: var(--accent);
          }
          &.disabled {
            opacity: 0.8;
            background-color: var(--bg-light);
            // background-color: none !important;
            background: none !important;
            .index-number {
              color: var(--light-text-color);
              &.disabled {
                opacity: 0.5 !important;
                color: #000;
              }
            }
          }
        }
      }
    }

    &.disable-drag {
      background-color: none;
      opacity: 0.8;
    }
  }
  .v-btn__content {
    color: #000 !important;
  }
}
</style>
