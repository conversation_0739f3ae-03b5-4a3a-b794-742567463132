<template>
  <v-layout row wrap>
    <v-flex md12 mb-2 pa-2>
      <!-- top row -->
      <v-layout justify-space-between align-center>
        <!-- title and error icons -->
        <div class="pudType-container" align-center>
          <div class="leg-type" :class="pudItem.legTypeFlag">
            {{ pudItem.legTypeFlag }}
          </div>
          <div class="pud-title-column">
            <div class="pud-title-row">
              <v-layout>
                <span class="pud-title-txt"
                  >{{ props.index }} -
                  <span class="suburb">{{ pudItem.address.suburb }}</span></span
                >
                <span
                  class="zone-container"
                  :class="{ 'error-type': zoneDescription.isError }"
                  v-if="zoneDescription.isVisible"
                  >{{ zoneDescription.content }}</span
                >
              </v-layout>
              <v-spacer></v-spacer>
              <span
                v-if="hasErrors && hasErrors.isVisible"
                class="error-tooltips"
              >
                <v-tooltip bottom v-if="!sessionManager.isClientPortal()">
                  <template v-slot:activator="{ on }">
                    <v-icon
                      size="18"
                      v-on="on"
                      :color="hasErrors.tooltipColor"
                      >{{ hasErrors.tooltipType }}</v-icon
                    >
                  </template>
                  <div>
                    <v-layout
                      align-center
                      :class="
                        errorItemIndex !== hasErrors.items.length - 1
                          ? 'pb-1'
                          : ''
                      "
                      v-for="(errorItem, errorItemIndex) in hasErrors.items"
                      :key="errorItem.key"
                    >
                      <v-icon size="10" :color="errorItem.color">
                        {{ errorItem.value }}
                      </v-icon>
                      <span class="pl-2">{{ errorItem.description }}</span>
                    </v-layout>
                  </div>
                </v-tooltip>
              </span>
            </div>
          </div>
        </div>
        <!-- Tags chips and edit button -->
        <span class="action-btn-side-container">
          <!-- tags -->
          <v-layout class="tag-container">
            <span
              class="tag-accent-card green-fill"
              v-if="pudItem.status === 'FINISHED'"
            >
              FINISHED
              <v-icon class="black--text" size="10">fad fa-check</v-icon>
            </span>
            <span
              class="tag-accent-card blue-fill"
              v-if="pudItem.status === 'ARRIVED'"
            >
              ARRIVED
              <v-icon class="white--text" size="12">hourglass_empty</v-icon>
            </span>
            <JobBookingProgressChips
              v-if="pudRouteProgress"
              :pudRouteProgress="pudRouteProgress"
              :pudStatus="pudItem.status"
              :isNextStop="nextStopPudId === pudItem.pudId"
              :isClientPortal="sessionManager.isClientPortal()"
              :createdByDriver="pudItem.createdByDriver"
              :isOutsideMetro="pudItem.isOutsideMetro"
              :isStandbyRate="pudItem.isStandbyRate"
              :driverLocationUnknown="pudRouteProgress.driverLocationUnknown"
            >
            </JobBookingProgressChips>
          </v-layout>
          <v-btn
            v-if="
              (bookingData?.unassignedPudItems?.length ||
                pudItem.unassignedPudItemReference?.length) &&
              !sessionManager.isClientPortal()
            "
            class="link-icons-btn"
            flat
            depressed
            @click="emit('openStopForMatching', pudItem.pudId)"
          >
            <v-icon size="14">fas fa-link</v-icon>
            {{
              pudItem?.unassignedPudItemReference?.length ? 'Re-Link' : 'Link'
            }}
          </v-btn>
          <v-btn
            v-if="
              (sessionManager.isClientPortal() &&
                jobDetails._id === undefined) ||
              !sessionManager.isClientPortal()
            "
            class="edit-icons-btn"
            flat
            depressed
            @click="emit('editStop', pudItem.uniqueId ?? pudItem.pudId)"
          >
            <v-icon size="16">edit</v-icon>
            Edit
          </v-btn>
        </span>
      </v-layout>
    </v-flex>

    <v-flex
      md10
      class="pud-item__attachmentcontainer"
      v-if="sessionManager.isClientPortal()"
    >
      <v-layout row wrap>
        <v-tooltip
          bottom
          max-width="220px"
          close-delay="0"
          v-for="img in attachmentImageData"
          :key="img.id"
          content-class="v-tooltip__small-text"
        >
          <template v-slot:activator="{ on }">
            <div
              class="image-container__container"
              @click="requestFullSizeImage(img)"
              v-on="on"
            >
              <div
                class="image-container"
                :class="
                  img.attachment.documentTypeId === 14 ? 'signature-image' : ''
                "
              >
                <v-img
                  v-if="img.attachment"
                  :src="
                    img.attachment.data &&
                    img.attachment.mimeType.includes('image') &&
                    !img.attachment.mimeType.includes('heic') &&
                    !img.attachment.mimeType.includes('heif')
                      ? img.attachment.data
                      : ''
                  "
                  aspect-ratio="1"
                >
                  <template v-slot:placeholder>
                    <v-layout justify-center align-center fill-height column>
                      <v-icon size="30"
                        >{{ getIconByMimeType(img.attachment.mimeType) }}
                      </v-icon>
                    </v-layout>
                  </template>
                </v-img>
              </div>
            </div>
          </template>
          <span
            ><v-layout row wrap>
              <v-flex md12>
                <v-layout>
                  Attachment Type:
                  <v-spacer></v-spacer
                  >{{ returnAttachmentTypeName(img.attachment.documentTypeId) }}
                </v-layout></v-flex
              >
              <v-flex md12 v-if="img.attachment.signatureName">
                <v-layout>
                  Signed By: <v-spacer></v-spacer
                  >{{
                    img.attachment.signatureName
                      ? img.attachment.signatureName
                      : '-'
                  }}
                </v-layout></v-flex
              >
              <v-flex md12>
                <v-layout>
                  Created: <v-spacer></v-spacer
                  >{{
                    img.attachment.timestamp
                      ? returnFormattedTime(
                          img.attachment.timestamp,
                          `HH:mm
                  DD/MM/YY`,
                        )
                      : '-'
                  }}
                </v-layout></v-flex
              >
              <v-flex md12>
                <v-layout>
                  Longitude: <v-spacer></v-spacer
                  >{{
                    img.attachment.gpsLocation && img.attachment.gpsLocation[0]
                      ? img.attachment.gpsLocation[0].toFixed(4)
                      : 'Unknown'
                  }}
                </v-layout></v-flex
              >
              <v-flex md12>
                <v-layout>
                  Latitude: <v-spacer></v-spacer
                  >{{
                    img.attachment.gpsLocation && img.attachment.gpsLocation[1]
                      ? img.attachment.gpsLocation[1].toFixed(4)
                      : 'Unknown'
                  }}
                </v-layout></v-flex
              >
            </v-layout>
          </span>
        </v-tooltip>
      </v-layout>
    </v-flex>

    <v-flex md7 pa-2>
      <div class="summary-container">
        <v-flex
          v-for="item in summaryInfo"
          :key="item.id"
          md="6"
          :class="{
            'full-width': item.id === 'address',
            'half-width': item.id === 'location',
            reference: item.id === 'reference',
            filled: item.fillType === FillType.FILL,
            outlined: item.fillType === FillType.OUTLINE,
          }"
          class="summary-item"
        >
          <div class="summary-content">
            <span class="title-txt"
              >{{ item.title }}
              <v-tooltip right v-if="item.id === 'departure'">
                <template v-slot:activator="{ on }">
                  <v-icon size="16" color="info" v-on="on">help</v-icon>
                </template>
                <v-layout>
                  <v-flex md12>
                    <ul>
                      <li
                        v-for="tooltipText in item.tooltipList"
                        :key="tooltipText"
                      >
                        {{ tooltipText }}
                      </li>
                    </ul>
                  </v-flex>
                </v-layout>
              </v-tooltip>
            </span>
            <!-- REFERENCE TOOLTIPS -->
            <v-tooltip
              v-if="
                item.id === 'reference' &&
                typeof item.value === 'string' &&
                (pudItem.dropoffReference.length > 1 ||
                  pudItem.pickupReference.length > 1)
              "
              bottom
            >
              <template v-slot:activator="{ on }">
                <span v-on="on" class="value-txt reference">{{
                  getReference(item.value)
                }}</span>
              </template>

              <span>
                <ul>
                  <li
                    v-for="reference in getRemainingReferences(item.value)"
                    :key="`${reference}`"
                  >
                    {{ reference || '' }}
                  </li>
                </ul>
              </span>
            </v-tooltip>
            <span
              v-else-if="item.id !== 'departure'"
              class="value-txt"
              :title="String(item.value)"
            >
              {{ item.value }}
              <!-- status icon -->
              <v-icon class="status-icon" v-if="item.fillType === FillType.FILL"
                >check</v-icon
              >
              <v-icon
                class="status-icon"
                v-if="item.fillType === FillType.OUTLINE"
                >hourglass_empty</v-icon
              >
              <!-- SITE CONTACT PHONE NUMBER -->
              <span v-if="item.id === 'siteContact'" class="site-contact">
                <v-tooltip
                  bottom
                  v-if="
                    pudItem.siteContactMobileNumber ||
                    pudItem.siteContactLandLineNumber
                  "
                >
                  <template v-slot:activator="{ on }">
                    <a
                      class="site-contact-link"
                      v-on="on"
                      :href="`tel:${
                        pudItem.siteContactMobileNumber ||
                        pudItem.siteContactLandLineNumber
                      }`"
                    >
                      <v-icon size="14">phone</v-icon>
                      {{
                        formatPhoneNumber(
                          pudItem.siteContactMobileNumber ||
                            pudItem.siteContactLandLineNumber,
                        )
                      }}
                    </a>
                  </template>
                  Call {{ pudItem.siteContactName }}
                </v-tooltip>
              </span>
            </span>
            <!-- Time -->
            <span v-if="item.id === 'departure'" class="value-txt reference">{{
              item.value
            }}</span>
          </div>
        </v-flex>
      </div>
    </v-flex>
    <v-flex md5 pa-2 class="notes-bg">
      <span class="notes-txt"
        >Notes
        <span v-if="pudItem.notes.length > 0"
          >( {{ pudItem.notes.length }} )</span
        >
      </span>
      <v-flex class="notes-container custom-scrollbar">
        <NotesList
          :isBookingScreen="false"
          :communications="pudItem.notes"
          :jobId="jobDetails.jobId"
          :showVisibilityTypeName="true"
          :pudId="pudItem.pudId"
        >
        </NotesList>
      </v-flex>
    </v-flex>
    <v-layout row wrap>
      <v-flex
        class="Job-manifest-table"
        md12
        v-if="pudItem.manifest.length > 0"
      >
        <JobManifestTable
          :key="pudItem.uniqueId"
          :jobDetails="jobDetails"
          :pudItem="pudItem"
          :manifestList="pudItem.manifest"
          :formDisabled="true"
        ></JobManifestTable>
      </v-flex>
      <v-flex class="no-data-text" md12 v-else>
        <span>No Items Manifested</span></v-flex
      >
    </v-layout>
  </v-layout>
</template>

<script setup lang="ts">
import JobBookingProgressChips from '@/components/booking/delivery_details/job_booking_progress_chips.vue';
import JobManifestTable from '@/components/booking/delivery_details/job_manifest_table.vue';
import NotesList from '@/components/common/notes_list/notes_list.vue';
import {
  downloadAttachment,
  getIconByMimeType,
} from '@/helpers/AttachmentHelpers/AttachmentHelpers';
import { returnFormattedTime } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { returnCorrectDuration } from '@/helpers/DateTimeHelpers/DurationHelpers';
import {
  parseDimensionFromMetres,
  parseWeightFromKilograms,
} from '@/helpers/DimensionsHelpers/DimensionsHelpers';
import {
  PudPropertyChecker,
  returnAttachmentTypeName,
  returnPudValidationSummary,
} from '@/helpers/JobDataHelpers/JobDataHelpers';
import { isZoneToZoneRateTypeObject } from '@/helpers/RateHelpers/RateTableItemHelpers';
import {
  returnZoneToZoneName,
  returnZoneToZoneRateTypeForPudItem,
} from '@/helpers/RateHelpers/ZoneToZoneRateHelpers';
import { formatPhoneNumber } from '@/helpers/StringHelpers/StringHelpers';
import { ClientJobBookingData } from '@/interface-models/Booking/ClientJobBookingData';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import Attachment from '@/interface-models/Generic/Attachment/Attachment';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import type { JobDetails } from '@/interface-models/Jobs/JobDetails';
import JobReferenceDetails from '@/interface-models/Jobs/JobReferenceDetails';
import { JobRouteProgress } from '@/interface-models/Jobs/JobRouteProgress';
import type PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { WeightRequirement } from '@/interface-models/Jobs/WeightRequirement/WeightRequirement';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import UnitRate from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRate';
import ZoneRateType from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRateType';
import { useAttachmentStore } from '@/store/modules/AttachmentStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { sessionManager } from '@/store/session/SessionState';
import { ComputedRef, Ref, computed, ref, toRef } from 'vue';

const props = defineProps<{
  jobDetails: JobDetails;
  pudItem: PUDItem;
  selectedClientDetails: ClientDetails;
  bookingData: ClientJobBookingData | null;
  index: number;
}>();

const emit = defineEmits<{
  (event: 'editStop', payload: string): void;
  (event: 'openStopForMatching', payload: string): void;
}>();

const jobDetails: Ref<JobDetails> = toRef(props, 'jobDetails');
const pudItem: Ref<PUDItem> = toRef(props, 'pudItem');

const fleetAssetStore = useFleetAssetStore();
const lwhUnit = toRef(fleetAssetStore, 'lwhUnit'); // 'm' or 'cm'
const weightUnit = toRef(fleetAssetStore, 'weightUnit'); // 'kg', 'g', or 't'

const weightRequirement: Ref<WeightRequirement | null> = ref(
  props.selectedClientDetails.weightRequirement,
);

const rateToApply: ComputedRef<RateTableItems | null> = computed(() => {
  if (!!jobDetails.value?.accounting?.clientRates?.length) {
    return jobDetails.value.accounting.clientRates[0].rate;
  }
  return null;
});

// computed property to find zoneName
const zoneDescription: ComputedRef<{
  content: string;
  isVisible: boolean;
  isError: boolean;
}> = computed(() => {
  const config = { content: '', isVisible: false, isError: false };
  if (!rateToApply.value) {
    return config;
  }

  switch (rateToApply.value.rateTypeId) {
    case JobRateType.ZONE:
      config.isVisible = true;
      config.content = pudItem.value.rateDetails.zoneName(
        rateToApply.value.rateTypeObject as ZoneRateType[],
      );
      break;
    case JobRateType.UNIT:
      const amount = pudItem.value.isPickup
        ? pudItem.value.rateDetails.unitPickUps
        : pudItem.value.rateDetails.unitDropOffs;

      config.isVisible = true;
      config.content =
        pudItem.value.rateDetails.unitZoneName(
          rateToApply.value.rateTypeObject as UnitRate[],
        ) +
        ' (' +
        amount +
        ')';
      break;
    case JobRateType.ZONE_TO_ZONE:
      if (
        isZoneToZoneRateTypeObject(
          rateToApply.value.rateTypeId,
          rateToApply.value.rateTypeObject,
        ) &&
        !!rateToApply.value.rateTypeObject.length
      ) {
        if (pudItem.value.legTypeFlag === 'P') {
          config.isVisible = true;
          config.content = 'PICKUP';
        } else {
          const foundRate = returnZoneToZoneRateTypeForPudItem(
            pudItem.value,
            props.index,
            rateToApply.value.rateTypeObject,
          );
          if (foundRate) {
            config.isVisible = true;
            config.content = returnZoneToZoneName(foundRate);
          } else {
            config.isVisible = true;
            config.content = 'NOT AVAILABLE';
            config.isError = true;
          }
        }
      }
      break;
    default:
      config.isVisible = false;
      break;
  }
  return config;
});

// check if pud item has error and display tooltip as error Icons
const hasErrors = computed<PudPropertyChecker | null>(() => {
  return returnPudValidationSummary(
    pudItem.value,
    jobDetails.value.serviceTypeObject.rateTypeId,
    jobDetails.value?.pudItems[0].isPickup,
    weightRequirement.value,
  );
});

function formatReferences(references: JobReferenceDetails[]): string {
  if (!references || references.length === 0) {
    return '-';
  }
  return references
    .map((ref) => ref.reference)
    .filter((s) => !!s)
    .join(', ');
}

/**
 * Show first reference and count of additional references
 * If there's only one reference, return it
 */
function getReference(references: string): string {
  const referenceArray = references.split(',').map((ref) => ref.trim());
  const firstReference = truncateReferenceIfLong(referenceArray[0]);
  const remainingCount = referenceArray.length - 1;
  if (remainingCount > 0) {
    return `${firstReference} ( +${remainingCount} )`;
  } else {
    return firstReference;
  }
}
/**
 * Truncates the reference if it is too long. Displayed in the template.
 * @param reference - The reference to truncate.
 */
function truncateReferenceIfLong(reference: string): string {
  return reference.length > 14 ? `${reference.slice(0, 14)}...` : reference;
}

/**
 * Return the remaining references after the first one for tooltip
 */
function getRemainingReferences(references: string): string[] {
  const referenceArray = references.split(',').map((ref: string) => ref.trim()); // Split by comma and trim whitespace
  return referenceArray;
}

const pudRouteProgress: ComputedRef<JobRouteProgress | null> = computed(() => {
  const jobProgress = jobDetails.value.additionalJobData?.routeProgress;
  if (!jobProgress?.length) {
    return null;
  }
  const jobRouteProgress = jobProgress.find(
    (x: JobRouteProgress) => x.pudId === pudItem.value.pudId,
  );
  return jobRouteProgress ? jobRouteProgress : null;
});

const nextStopPudId: ComputedRef<string | null> = computed(() => {
  const nextPud = props.jobDetails.pudItems.find((x: PUDItem) => !x.status);
  return nextPud?.pudId || null;
});

/**
 * Returns a string description of the arrival and departure times for the stop.
 * If the pudItem has a status (ARRIVED or FINISHED), we will try to find the
 * associated event times and use those.
 */
function returnArrivalDepartureDescription(): {
  value: string;
  tooltipList?: string[];
} {
  const { loadTime, epochTime } = pudItem.value;

  const loadDuration: string = returnCorrectDuration(loadTime);
  const tooltipList: string[] = [];

  let arrivalTime = '';
  let departureTime = '';

  if (
    pudRouteProgress.value?.actualArrivalTimeReadable &&
    pudRouteProgress.value?.actualArrivalTimeReadable !== 'Invalid date'
  ) {
    // The stop has been completed - use the actual arrival time
    // TODO: Revert this
    arrivalTime = returnFormattedTime(
      pudRouteProgress.value.actualArrivalTime ?? 0,
      'HH:mm',
    );

    tooltipList.push(
      pudRouteProgress.value.arrivalDifferenceInMins
        ? `Arrived: ${pudRouteProgress.value.arrivalDifferenceInMins}`
        : '',
    );
    tooltipList.push(
      `Original planned arrival time of ${returnFormattedTime(epochTime)}`,
    );
  } else if (
    // The job is in progress - use the expected arrival time based on matrix info
    pudRouteProgress.value?.expectedArrivalTimeReadable &&
    pudRouteProgress.value?.expectedArrivalTimeReadable !== 'Invalid date'
  ) {
    // TODO: Revert this
    // arrivalTime = pudRouteProgress.value.expectedArrivalTimeReadable;
    arrivalTime = returnFormattedTime(
      pudRouteProgress.value.expectedArrivalTime ?? 0,
      'HH:mm',
    );

    tooltipList.push(
      `Original planned arrival time of ${returnFormattedTime(epochTime)}`,
    );
  } else {
    // The job is not yes started - use the planned arrival time based on routing info
    arrivalTime = returnFormattedTime(epochTime, 'HH:mm');
  }

  if (
    pudRouteProgress.value?.actualDepartureTimeReadable &&
    pudRouteProgress.value?.actualDepartureTimeReadable !== 'Invalid date'
  ) {
    // The stop has been completed - use the actual departure time
    departureTime = pudRouteProgress.value.actualDepartureTimeReadable;

    tooltipList.push(
      pudRouteProgress.value.actualLoadTimeReadable
        ? `Load time: ${pudRouteProgress.value.actualLoadTimeReadable} (${pudRouteProgress.value.differenceInLoadTimeReadable})`
        : '',
    );

    tooltipList.push(`Original estimate load time of ${loadDuration}`);
  } else if (
    pudRouteProgress.value?.expectedDepartureTimeReadable &&
    pudRouteProgress.value?.expectedDepartureTimeReadable !== 'Invalid date'
  ) {
    // The job is in progress - use the expected departure time based on matrix info
    departureTime = pudRouteProgress.value.expectedDepartureTimeReadable;
    tooltipList.push(
      `Original planned departure time of ${returnFormattedTime(
        epochTime + loadTime,
      )}`,
    );
    tooltipList.push(`Estimated load time of ${loadDuration}`);
  } else {
    // The job is not yes started - use the planned departure time based on routing info
    departureTime = returnFormattedTime(epochTime + loadTime);
    tooltipList.push(
      `Based on routing data and estimated load time of ${loadDuration}`,
    );
  }
  const filteredTooltipList = tooltipList.filter((item) => !!item);
  return {
    value: `${arrivalTime} / ${departureTime}`,
    tooltipList: filteredTooltipList ? filteredTooltipList : undefined,
  };
}

enum FillType {
  FILL = 'FILL',
  OUTLINE = 'OUTLINE',
  EMPTY = 'EMPTY',
}

interface SummaryInfo {
  id: string;
  title: string;
  value: string | number;
  tooltipList?: string[];
  fillType?: FillType;
}

interface AttachmentGroup {
  id: string;
  fileName: string;
  attachment: Attachment;
  idList: string[];
}

/**
 * Returns a list of summary details for the stop. Used to display the stop
 * details in the summary section.
 */
const summaryInfo: ComputedRef<SummaryInfo[]> = computed(() => {
  const summaryList: SummaryInfo[] = [];

  let status = 'Not actioned';
  let fillType = FillType.EMPTY;
  if (pudItem.value.status === 'ARRIVED') {
    status = 'On Site';
    fillType = FillType.OUTLINE;
  } else if (pudItem.value.status === 'FINISHED') {
    status = 'Complete';
    fillType = FillType.FILL;
  }

  const { length, width, height } = pudItem.value.dimensions || {};
  const { weight } = pudItem.value || {};
  // Prepare dimension and weight values
  const formattedLength = parseDimensionFromMetres(length, lwhUnit.value);
  const formattedWidth = parseDimensionFromMetres(width, lwhUnit.value);
  const formattedHeight = parseDimensionFromMetres(height, lwhUnit.value);
  const formattedWeight = parseWeightFromKilograms(weight, weightUnit.value);

  const dimensionValues = [
    formattedLength ? `${formattedLength} x` : '',
    formattedWidth ? `${formattedWidth} x` : '',
    formattedHeight ? `${formattedHeight} (${lwhUnit.value}),` : '',
    formattedWeight ? `${formattedWeight} (${weightUnit.value})` : '',
  ]
    .filter(Boolean)
    .join(' ');

  summaryList.push({
    id: 'pudStatus',
    title: 'Status',
    value: status,
    fillType: fillType,
  });
  summaryList.push({
    id: 'departure',
    title: 'Arrival / Departure',
    ...returnArrivalDepartureDescription(),
  });
  summaryList.push({
    id: 'reference',
    title: 'References',
    value:
      pudItem.value.legTypeFlag === 'P'
        ? formatReferences(pudItem.value.pickupReference)
        : formatReferences(pudItem.value.dropoffReference),
  });
  summaryList.push({
    id: 'location',
    title: 'Location',
    value: pudItem.value.customerDeliveryName
      ? pudItem.value.customerDeliveryName
      : '-',
  });
  summaryList.push({
    id: 'siteContact',
    title: 'Site Contact',
    value: pudItem.value.siteContactName ? pudItem.value.siteContactName : '-',
  });
  summaryList.push({
    id: 'address',
    title: 'Address',
    value: pudItem.value.address.formattedAddress
      ? pudItem.value.address.formattedAddress
      : '-',
  });
  summaryList.push({
    id: 'payload',
    title: 'Overall Payload',
    value: dimensionValues || '-',
  });
  return summaryList;
});

const attachmentImageData = computed((): AttachmentGroup[] => {
  if (!pudItem.value) {
    return [];
  }

  const attachments: Attachment[] = pudItem.value.attachments
    ? pudItem.value.attachments
    : [];

  const uniqueFileNames: string[] = [];
  attachments.forEach((att) => {
    if (!uniqueFileNames.includes(att.name)) {
      uniqueFileNames.push(att.name);
    }
  });

  const attGroupList: AttachmentGroup[] = [];

  uniqueFileNames.forEach((name) => {
    const attList = attachments.filter((att) => att.name === name);
    if (attList.length > 0) {
      const firstAtt = attList[0];

      const group: AttachmentGroup = {
        id: firstAtt.id,
        fileName: name,
        attachment: firstAtt,
        idList: attList.map((a) => a.id),
      };
      attGroupList.push(group);
    }
  });

  return attGroupList;
});

const isRequestingFullSizeAttachment: Ref<boolean> = ref(false);

async function requestFullSizeImage(imgGroup: AttachmentGroup) {
  isRequestingFullSizeAttachment.value = true;
  const attPromises = imgGroup.idList.map((id) =>
    useAttachmentStore().getAttachmentById(id),
  );
  const results = await Promise.all(attPromises);
  results.forEach((result) => {
    if (result?.data && attachmentListIncludesId(result.id)) {
      downloadAttachment(result);
    }
  });
  isRequestingFullSizeAttachment.value = false;
}

function attachmentListIncludesId(id: string): boolean {
  if (!props.pudItem) {
    return false;
  }
  return props.pudItem.attachments.map((pud) => pud.id).includes(id);
}
</script>
<style scoped lang="scss">
.pudType-container {
  display: flex;
  align-items: center;
  .leg-type {
    width: 25px;
    height: 25px;
    align-items: center;
    justify-content: center;
    padding-left: 6px;
    margin: 6px;
    border-radius: 20px;
    font-weight: 600;
    font-size: $font-size-16;
    color: white;
    &.P {
      background-color: $pickup;
      border-left: 2px solid $pickup-highlight;
      box-shadow: 0px 1px 2px 1px $pickup !important;
    }
    &.D {
      background-color: $drop;
      border-left: 2px solid $drop-highlight;
      box-shadow: 0px 1px 2px 1px $drop !important;
    }
  }

  .pud-title-column {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .pud-title-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    .pud-title-txt {
      font-family: $sub-font-family;
      padding: 2px 6px;
      margin-left: 2px;
      font-size: $font-size-18;
      text-transform: uppercase !important;
      color: var(--light-text-color);
      font-weight: 600;
      .suburb {
        color: var(text-color);
      }
    }
    .zone-container {
      color: #c7dbff;
      border-radius: 8px;
      font-weight: 700;
      align-items: center;
      align-self: center;
      letter-spacing: 1px;
      padding-right: 8px;
      padding-left: 8px;
      margin-left: 12px;
      background-color: #92b8ff60;

      &.error-type {
        background-color: $error;
        color: $bg-light-red;
        content: 'Not Available';
      }
    }
    .error-tooltips {
      margin-left: 15px;
    }
  }
}

.summary-container {
  display: flex;
  flex-wrap: wrap;
}

.summary-item {
  display: flex;
  flex-wrap: wrap;
  width: 33.3%;
  &.full-width {
    width: 66.6%;
  }
  &.half-width {
    width: 66.6% !important;
  }
  &.reference {
    .value-txt {
      max-width: 80%;
      overflow: hidden;
      display: inline-block;
      text-decoration: none;
      text-overflow: ellipsis;
      white-space: nowrap;
      &.reference {
        cursor: pointer;
      }
    }
  }

  &.filled {
    .summary-content {
      width: auto;
      align-self: flex-start;
      .value-txt {
        // border-bottom: 4px solid $success;
        border-radius: 4px;
        color: var(--text-color);
        text-transform: uppercase;
        font-weight: 800;
        align-content: center;
        .status-icon {
          color: $success-type !important;
          margin-left: 2px;
          font-weight: 900;
          border-radius: 100px;
          background-color: $toast-success-bg;
          scale: 0.9;
        }
      }
    }
  }
  &.outlined {
    .summary-content {
      width: auto;
      align-self: flex-start;

      .value-txt {
        // border-bottom: 4px double $translucent-highlight;
        border-radius: 4px;
        color: var(--text-color);
        text-transform: uppercase;
        font-weight: 800;
        align-content: center;
        .status-icon {
          color: var(--accent) !important;
          margin-left: 2px;
          font-weight: 900;
          border-radius: 100px;
          background-color: $translucent-highlight;
          scale: 0.9;
        }
      }
    }
  }
}

.summary-content {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.title-txt {
  font-weight: 400;
  color: var(--light-text-color);
  text-align: left;

  .v-icon {
    cursor: pointer;
  }
}

.value-txt {
  font-weight: 400;
  color: var(--text-color);
  text-align: left;
  margin-bottom: 16px;
  letter-spacing: 0.5px;

  .site-contact {
    transition: all 0.3s;
    position: relative;
    display: block;
    // transform: translate(-104px, 24px);
    .site-contact-link {
      color: var(--accent);
      transition: color 0.3s;
      .v-icon {
        color: var(--accent);
      }
      &:hover {
        color: var(--accent-secondary);
        .v-icon {
          color: var(--accent-secondary);
        }
      }
    }
  }
}

.action-btn-side-container {
  display: flex;
  flex-direction: row;
  align-content: center;
  justify-content: center;
  .tag-container {
    justify-content: right;
    margin-left: 12px;
    // margin-bottom: 6px;
    .tag-accent-card {
      align-self: flex-start;
      border-radius: $border-radius-base;
      font-size: $font-size-10;
      color: var(--light-text-color) !important;
      padding: 2px 8px;
      font-weight: 800;
      font-family: $font-sans;
      display: flex;
      align-items: center;
      margin-left: 4px;
      margin-top: 4px;
      gap: 10px;

      &.blue-fill {
        background-color: var(--accent);
        outline: 1px solid $highlight-dark;
        color: var(--text-color-reverse) !important;
        .v-icon {
          color: var(--text-color-reverse) !important;
        }
      }

      &.green-fill {
        background-color: $success-type;
        outline: 1px solid $success;
        color: #000 !important;
      }

      &.blue-outline {
        outline: 1px solid $highlight-dark;
      }

      &.amber-outline {
        outline: 1px solid var(--yellow);
      }

      &.white-outline {
        outline: 1px solid var(--bg-light);
      }

      &.red-outline {
        outline: 1px solid $error;
      }
    }
  }
  .edit-icons-btn {
    background-color: $info;
    border-radius: 200px;
    width: 32px;
    font-family: $font-sans;
    font-weight: 800;
    margin-top: -6px;
    color: white !important;
    .v-icon {
      padding-right: 4px;
    }
  }
  .link-icons-btn {
    background-color: var(--primary);
    border-radius: 20px;
    width: 32px;
    font-weight: 800;
    margin-top: -6px;
    color: white !important;
    .v-icon {
      padding-right: 4px;
    }
  }
}

.notes-bg {
  .notes-container {
    height: auto;
    max-height: 200px;
    padding: 2px;
    margin-right: 1px;
  }
  .notes-txt {
    padding-left: 8px;
    font-weight: 400;
    color: var(--light-text-color);
  }
}

.pud-item__attachmentcontainer {
  $thumbnail-res: 65px;
  margin: 2px;

  .image-container__container {
    height: $thumbnail-res;
    width: $thumbnail-res;
    position: relative;
    margin: 0px 6px;

    &:hover {
      cursor: pointer;

      .image-container__info {
        visibility: visible;
      }
    }

    .image-container__info {
      position: absolute;
      bottom: 0px;
      right: 0px;
      visibility: hidden;
      padding: 2px;
      transition: 0s;
    }

    .image-container {
      height: $thumbnail-res;
      width: $thumbnail-res;
      border: 1px solid $translucent-light;
      border-radius: $border-radius-lg;

      &.center-content {
        align-items: center;
        justify-content: center;
        flex-direction: column;
      }

      img {
        max-width: 100%;
        height: 100%;
        object-fit: contain !important;
      }
    }

    .delete-button {
      visibility: hidden;
      position: absolute;
      padding: 6px;
      top: 0px;
      right: 4px;
      cursor: pointer;
    }

    &:hover {
      .delete-button {
        visibility: visible;
      }
    }
  }
}

// Responsive Styles
@media (max-width: 1500px) {
  .tag-accent-card {
    font-size: smaller !important;
  }
  .leg-type {
    width: 20px !important;
    height: 20px !important;
    font-size: $font-size-14 !important;
    padding-left: 3px !important;
  }
  .pud-title-txt {
    font-size: $font-size-12 !important;
  }
  .edit-icons-btn {
    margin-top: 1px !important;
    max-height: 24px;
  }
  .link-icons-btn {
    margin-top: 1px !important;
    max-height: 24px;
  }
}

@media (max-width: 1200px) {
  .pud-title-txt {
    font-size: $font-size-10 !important;
  }
  .edit-icons-btn {
    margin-top: 1px !important;
    max-height: 20px;
  }
  .link-icons-btn {
    margin-top: 1px !important;
    max-height: 20px;
  }
}
</style>
