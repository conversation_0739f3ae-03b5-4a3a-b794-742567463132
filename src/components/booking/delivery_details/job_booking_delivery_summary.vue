<template>
  <v-layout wrap>
    <v-flex
      md12
      v-for="(pudItem, index) in pudItems"
      :key="pudItem.uniqueId"
      class="summary-card"
      :class="[
        pudItem.status === 'ARRIVED' ? 'activePudBorder' : '',
        scrolledItem === pudItem.uniqueId ? 'clicked-item' : '',
        errorItem === pudItem.uniqueId ? 'error-item' : '',
      ]"
    >
      <JobBookingStopSummary
        :jobDetails="jobDetails"
        :pudItem="pudItem"
        :index="index + 1"
        :id="pudItem.uniqueId"
        :selectedClientDetails="selectedClientDetails"
        :bookingData="bookingData"
        @editStop="emit('editStop', $event)"
        @openStopForMatching="emit('openStopForMatching', $event)"
      ></JobBookingStopSummary>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import JobBookingStopSummary from '@/components/booking/delivery_details/job_booking_stop_summary.vue';
import { ClientJobBookingData } from '@/interface-models/Booking/ClientJobBookingData';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import type { JobDetails } from '@/interface-models/Jobs/JobDetails';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { computed, ComputedRef, nextTick, Ref, ref, toRef } from 'vue';

const props = withDefaults(
  defineProps<{
    jobDetails: JobDetails;
    selectedClientDetails: ClientDetails;
    bookingData?: ClientJobBookingData | null;
  }>(),
  {
    bookingData: null,
  },
);

const emit = defineEmits<{
  (event: 'editStop', payload: string): void;
  (event: 'openStopForMatching', payload: string): void;
}>();
const scrolledItem: Ref<string> = ref('');
const errorItem: Ref<string> = ref('');
const jobDetails: Ref<JobDetails> = toRef(props, 'jobDetails');

const pudItems: ComputedRef<PUDItem[]> = computed(() => {
  return jobDetails.value.pudItems;
});
/**
 * Scrolls the element with the given ID into view smoothly.
 *
 * @param elementId - The ID of the element to scroll into view.
 */
function scrollToPudItem(elementId: string, isError?: boolean): void {
  const el = document.getElementById(elementId);
  if (isError) {
    errorItem.value = elementId;
  } else {
    scrolledItem.value = elementId;
  }
  if (el) {
    nextTick(() =>
      // Set timeout for expansion panel to open first before scrolling into view.
      el.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      }),
    );
  }
  setTimeout(() => {
    scrolledItem.value = '';
    errorItem.value = '';
  }, 1000);
}

// define Expose for parent component
defineExpose({ scrollToPudItem });
</script>

<style scoped lang="scss">
.summary-card {
  margin: 14px 7px;
  padding: 12px;
  border: 2px solid transparent;
  background-color: var(--background-color-550);
  border-radius: $border-radius-sm;
  transition: border 1s ease;

  &.activePudBorder {
    border: 0.5px solid var(--success);
    box-shadow:
      0px 4px 8px 0px #33b6794c,
      var(--box-shadow);
  }
}
.clicked-item {
  border-color: $info;
  transition: color 1s ease;
}

.error-item {
  border-color: $error;
  transition: color 1s ease;
}
</style>
