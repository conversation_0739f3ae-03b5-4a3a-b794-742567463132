<template>
  <v-flex class="notes-side-panel">
    <!-- Notes Section (60% of height) -->
    <div class="notes-section">
      <div class="notes-header">
        <h3 class="notes-title">
          <i class="far fa-sticky-note"></i>
          NOTES
        </h3>
      </div>
      <div class="notes-content">
        <JobBookingAllNotesSection
          :jobDetails="jobDetails"
          :clientDetails="clientDetails"
          :isSidePanel="true"
        />
      </div>
    </div>

    <!-- Driver Chat Section (40% of height) -->
    <div class="driver-chat-section">
      <DriverMessageComponent />
    </div>
  </v-flex>
</template>

<script setup lang="ts">
import JobBookingAllNotesSection from '@/components/booking/job_booking_all_notes_section.vue';
import DriverMessageComponent from '@/components/common/driver_message_component/driver_message_component.vue';
import { ClientDetails } from '@/interface-models/Client/ClientDetails/ClientDetails';
import { JobDetails } from '@/interface-models/Jobs/JobDetails';

const props = defineProps<{
  clientDetails: ClientDetails | null;
  jobDetails: JobDetails;
}>();
</script>

<style scoped lang="scss">
.notes-side-panel {
  background-color: var(--background-color-200);
  border-left: 2px solid $translucent;
  height: 100%;
  max-height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// Notes Section (60% of height)
.notes-section {
  flex: 0 0 60%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-bottom: 2px solid var(--border-color);
  min-height: 0;
}

.notes-header {
  padding: 12px 2px 2px 14px;
  background: var(--background-color-200);
  flex-shrink: 0;
}

.notes-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--warning);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  i {
    color: var(--warning);
    font-size: 18px;
  }
}

.notes-content {
  flex: 1;
  overflow: visible;
  display: flex;
  flex-direction: column;
  min-height: 0;
  height: 100%;
}

// Driver Chat Section (40% of height)
.driver-chat-section {
  flex: 0 0 40%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// Override TabbedNotesPanel styles for side panel context
:deep(.tabbed-notes-panel.side-panel) {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0;

  .notes-content {
    flex: 1;
    overflow-y: auto !important;
    min-height: 200px;
    padding: 8px;
    height: 100%;
    scroll-behavior: smooth;
  }

  .notes-section {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .notes-tabs {
    margin: 8px;
    border-radius: 4px;
  }
}
</style>
