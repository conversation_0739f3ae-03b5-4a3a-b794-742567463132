<template>
  <v-layout>
    <v-flex class="d-flex justify-end">
      <v-btn
        depressed
        solo
        outline
        class="v-btn-custom view"
        @click="(isViewingDialog = true), findClientRecentJobs()"
        :disabled="disabled"
      >
        View Recent Jobs
      </v-btn>
    </v-flex>
    <ContentDialog
      :showDialog.sync="isViewingDialog"
      title="View Recent Jobs"
      width="75%"
      @cancel="clearSelectedJob"
      contentPadding="pa-2"
      :showActions="false"
      class="v-dialog-custom"
    >
      <v-flex class="header-container">
        <v-layout align-center>
          <v-flex md5>
            <v-layout align-left :style="{ width: 'calc(100% - 100px)' }">
              <GTitle
                title="Select Job"
                subtitle="Select from recent jobs for client"
                :divider="false"
              >
                <template #right-aligned-content>
                  <div v-if="selectedJob?.jobId" class="selected-job">
                    Selected Job:
                    <p class="selected-job-id">{{ selectedJob.jobId }}</p>
                  </div>
                </template>
              </GTitle>
            </v-layout>
          </v-flex>
          <v-flex md7 class="filters-section">
            <v-layout align-center pr-2>
              <h6 class="subheader--faded pr-2 pb-1 pt-1">Sort By:</h6>
              <v-switch
                class="pr-4"
                v-model="sortByWorkDate"
                :false-value="false"
                :true-value="true"
                color="info"
                :disabled="allDataLoaded"
                :label="sortByWorkDate ? 'Work Date' : 'Booked Date'"
              />
              <v-checkbox
                class="pr-4"
                label="Include Cancelled Jobs"
                v-model="includeCancelJobs"
                :false-value="false"
                :true-value="true"
                color="info"
                :disabled="allDataLoaded"
              />
              <v-text-field
                v-model="search"
                append-icon="search"
                label="Search"
                hide-details
                color="orange"
                solo
                flat
                dense
                block
                class="v-solo-custom ml-2"
              />
            </v-layout>
          </v-flex>
        </v-layout>
        <v-divider></v-divider>

        <v-layout md12>
          <v-flex
            md12
            class="body-scrollable--65 body-min-height--65 pa-2"
            ref="contentsDiv"
          >
            <v-data-table
              :headers="headers"
              :items="filteredTableData"
              :loading="allDataLoaded"
              class="gd-dark-theme pa-2"
              hide-actions
            >
              <v-progress-linear
                color="#ffa000"
                indeterminate
                v-if="allDataLoaded"
              ></v-progress-linear>
              <template v-slot:items="dataProps">
                <tr
                  class="job-search-table-row"
                  :class="
                    selectedJob?.jobId === dataProps.item.jobId
                      ? 'selected'
                      : ''
                  "
                  @click="selectJobId(dataProps.item.jobId), scrollToBottom()"
                  style="cursor: pointer"
                >
                  <td>{{ dataProps.item.displayId }}</td>
                  <td>
                    {{ formatDate(dataProps.item.date, 'DD/MM/YY hh:mm a') }}
                  </td>
                  <td>{{ dataProps.item.clientService }}</td>
                  <td>{{ dataProps.item.reference }}</td>
                  <td>
                    {{
                      dataProps.item.from +
                      ' → ' +
                      (dataProps.item.to
                        ? dataProps.item.to +
                          (dataProps.item.numberOfLegs > 2
                            ? ' +' +
                              Math.max(0, dataProps.item.numberOfLegs - 2)
                            : '')
                        : '-')
                    }}
                  </td>

                  <td>{{ dataProps.item.dispatcherName.toUpperCase() }}</td>
                  <td>
                    {{ dataProps.item.status }}
                    <span class="red--text text--lighten-1">{{
                      dataProps.item.serviceFailure ? '(SF)' : ''
                    }}</span>
                  </td>
                  <td>
                    {{
                      formatDate(dataProps.item.bookedAt, 'DD/MM/YY hh:mm a')
                    }}
                  </td>
                  <td>
                    {{ dataProps.item.bookedBy }}

                    <v-icon
                      v-show="selectedJob?.jobId === dataProps.item.jobId"
                      class="check-icon"
                      size="18"
                      >check_circle</v-icon
                    >
                  </td>
                </tr>
              </template>
            </v-data-table>
            <v-layout justify-center>
              <Pagination
                @pageIncrement="pageIncrement"
                :pagination="pagination"
                @change="findClientRecentJobs"
                :rowsPerPage.sync="rowsPerPage"
                :rowsPerPageList="[10, 20, 50]"
              />
            </v-layout>
            <RebookJobSettings
              :rateTypeId="selectedJob?.clientRateTypeId"
              :workStatus="selectedJob?.workStatus"
              :selectedJobDetails="selectedJob"
              :clientDetails="clientDetails"
              :includeJobReferences.sync="includeJobReferences"
              :includePickupDropReferences.sync="includePickupDropReferences"
              :includeOverallPayloadInformation.sync="
                includeOverallPayloadInformation
              "
              :includePickupDropNotes.sync="includePickupDropNotes"
              :includeManifestPayloadInformation.sync="
                includeManifestPayloadInformation
              "
              :includeJobNotes.sync="includeJobNotes"
              :reverseLegOrder.sync="reverseLegOrder"
              :allowAsapTimeDefinition.sync="allowAsapTimeDefinition"
              :firstPudArrival="firstPudArrival"
              @update:firstPudArrival="($event) => (firstPudArrival = $event)"
              :modelValue="mainJobDate"
              @update:modelValue="($event) => (mainJobDate = $event)"
            />
          </v-flex>
        </v-layout>
        <v-divider></v-divider>
        <v-flex md12 class="btn-container">
          <v-btn
            class="action-btn"
            outline
            flat
            color="error"
            @click="clearSelectedJob"
            >Cancel</v-btn
          >
          <v-btn
            solo
            depressed
            color="success"
            class="action-btn save"
            @click="applyJobId"
            :disabled="!selectedJob?.jobId"
            >Confirm</v-btn
          >
        </v-flex>
      </v-flex>
    </ContentDialog>
  </v-layout>
</template>

<script setup lang="ts">
import Pagination from '@/components/common/pagination/index.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { initialiseJobDetails } from '@/helpers/classInitialisers/InitialiseJobDetails';
import {
  returnFormattedDate,
  returnFormattedTime,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { copyLegSpecificValuesToNewType } from '@/helpers/JobDataHelpers/JobDataHelpers';
import {
  getClientPrimaryTripRate,
  getFleetAssetPrimaryTripRate,
} from '@/helpers/RateHelpers/TripRateHelpers';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import { JobAccountingDetails } from '@/interface-models/Generic/Accounting/JobAccountingDetails';
import Communication from '@/interface-models/Generic/Communication/Communication';
import Dimensions from '@/interface-models/Generic/Dimensions/Dimensions';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import JobReferenceDetails from '@/interface-models/Jobs/JobReferenceDetails';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import SearchJobRequest from '@/interface-models/Jobs/SearchJob/SearchJobRequest';
import { SearchJobRequestSortByField } from '@/interface-models/Jobs/SearchJob/SearchJobRequestSortByFieldEnum';
import { SearchJobsSummary } from '@/interface-models/Jobs/SearchJob/SearchJobSummary';
import { SortDirection } from '@/interface-models/Jobs/SearchJob/SortDirectionEnum';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useJobStore } from '@/store/modules/JobStore';
import moment from 'moment-timezone';
import { computed, nextTick, Ref, ref, watch } from 'vue';
import RebookJobSettings from './rebook_job_settings.vue';

const props = defineProps<{
  disabled: boolean;
  clientDetails: ClientDetails | null;
}>();

const emit = defineEmits<{
  (event: 'applyJobId', payload: number | null): void;
  (event: 'applyNewJobDate', payload: number): void;
  (event: 'applyFirstPudArrival', payload: string): void;
}>();

defineExpose({
  createReBookJobDetails,
});

const jobStore = useJobStore();
const companyDetailsStore = useCompanyDetailsStore();
const contentsDiv = ref<HTMLElement | null>(null);

const selectedJob: Ref<SearchJobsSummary | null> = ref(null);
const isViewingDialog: Ref<boolean> = ref(false);

const allDataLoaded: Ref<boolean> = ref(false);

const tableData: Ref<SearchJobsSummary[]> = ref([]);
const includeCancelJobs: Ref<boolean> = ref(false);
const search: Ref<string> = ref('');

// job rebook properties
const includeJobReferences: Ref<boolean> = ref(true);
const includePickupDropReferences: Ref<boolean> = ref(true);
const includeManifestPayloadInformation: Ref<boolean> = ref(true);
const includeOverallPayloadInformation: Ref<boolean> = ref(true);
const includePickupDropNotes: Ref<boolean> = ref(false);
const includeJobNotes: Ref<boolean> = ref(false);
const reverseLegOrder: Ref<boolean> = ref(false);
const allowAsapTimeDefinition: Ref<boolean> = ref(true);

const now = moment().valueOf();
const firstPudArrival = ref(formatDate(now, 'HHmm'));
const mainJobDate: Ref<number> = ref(now ?? 0);

// Recent jobs table filters
const workStatusMin: Ref<WorkStatus> = ref(WorkStatus.BOOKED);
const sortByField: Ref<SearchJobRequestSortByField> = ref(
  SearchJobRequestSortByField.CREATED_DATE,
);
const sortByWorkDate = ref(true);

const page: Ref<number> = ref(1);
const rowsPerPage: Ref<number> = ref(10);

const pagination = computed(() => ({
  descending: true,
  page: page.value,
  rowsPerPage: rowsPerPage.value,
  totalItems: jobStore.requestedJobSearchSummaryCount,
}));

const headers: TableHeader[] = [
  {
    text: 'Job #',
    align: 'left',
    sortable: false,
    value: 'displayId',
    visible: true,
  },
  {
    text: 'Work Date',
    align: 'left',
    value: 'date',
    sortable: false,
    visible: true,
  },
  {
    text: 'Service',
    align: 'left',
    value: 'clientService',
    sortable: false,
    visible: true,
  },
  {
    text: 'Reference',
    align: 'left',
    sortable: false,
    value: 'reference',
    visible: true,
  },

  {
    text: 'Suburb',
    align: 'left',
    value: 'from',
    sortable: false,
    visible: true,
  },
  {
    text: 'Contact',
    align: 'left',
    value: 'dispatcherName',
    sortable: false,
    visible: true,
  },
  {
    text: 'Status',
    align: 'left',
    value: 'status',
    sortable: false,
    visible: true,
  },
  {
    text: 'Booked At',
    align: 'left',
    value: 'bookedAt',
    sortable: false,
    visible: true,
  },
  {
    text: 'Booked by',
    align: 'left',
    value: 'bookedBy',
    sortable: false,
    visible: true,
  },
];

function pageIncrement(value: number) {
  page.value += value;
  findClientRecentJobs();
}

function formatDate(value: number | string, format: string) {
  if (typeof value === 'number') {
    return returnFormattedDate(value, format);
  } else {
    return '-';
  }
}

/**
 * Fetches recent jobs for the given client and populates the table data.
 *
 * - Constructs a `SearchJobRequest` with the current client ID, filters, and sort parameters.
 * - Calls the job store's `getRecentJobs` method with paging information.
 * - Maps and enriches the returned job data using `SearchJobsSummary.setAdditionalInformation()`.
 */
async function findClientRecentJobs() {
  const searchJobRequest = new SearchJobRequest();
  searchJobRequest.clientId = props.clientDetails?.clientId ?? '';
  searchJobRequest.workStatusMin = workStatusMin.value;
  searchJobRequest.sortDirection = SortDirection.DESC;
  searchJobRequest.sortByField = sortByField.value;
  searchJobRequest.startEpoch = null;
  searchJobRequest.endEpoch = null;
  allDataLoaded.value = true;
  const response = await jobStore.getRecentJobs(
    searchJobRequest,
    page.value,
    rowsPerPage.value,
  );
  if (response) {
    tableData.value = response.jobDetailsList.map((job: SearchJobsSummary) => {
      const jobSummary = Object.assign(new SearchJobsSummary(), job);
      jobSummary.setAdditionalInformation();
      return jobSummary;
    });
    allDataLoaded.value = false;
  }
}

// filter table data based on search input
const filteredTableData = computed(() => {
  const lowercasedSearch = computed(() => search.value.toLowerCase());
  return tableData.value.filter((quote) => {
    return (
      quote.jobId.toString().toLowerCase().includes(lowercasedSearch.value) ||
      quote.clientService?.toLowerCase().includes(lowercasedSearch.value) ||
      quote.from?.toLowerCase().includes(lowercasedSearch.value) ||
      quote.to?.toLowerCase().includes(lowercasedSearch.value) ||
      quote.reference?.toLowerCase().includes(lowercasedSearch.value) ||
      quote.dispatcherName?.toLowerCase().includes(lowercasedSearch.value)
    );
  });
});

// apply selected job
function applyJobId() {
  emit('applyJobId', selectedJob.value?.jobId ?? 0);
  emit('applyNewJobDate', mainJobDate.value ?? 0);
  emit('applyFirstPudArrival', firstPudArrival.value ?? '');
  isViewingDialog.value = false;
}

// clear selected job
function clearSelectedJob() {
  selectedJob.value = null;
  emit('applyJobId', null);
  isViewingDialog.value = false;
}

/**
 * Selects a job from the table data by its job ID and updates the `selectedJob` ref.
 *
 * @param {number} id - The job ID to find and select.
 */
function selectJobId(id: number) {
  const foundJob = tableData.value.find((item) => item.jobId === id);
  if (foundJob) {
    selectedJob.value = foundJob;
  }
}

/**
 * Creates a copy of jobDetails for rebooking.
 *
 * - Clears identifiers and resets certain fields - jobId, _id, recurringJobId, recurringJobReference
 * - Optionally reverses leg order and updates leg-specific values.
 * - Applies conditions from feature toggles
 * - Sets new pickup/drop times and clears legacy or irrelevant data.
 *
 * @param {JobDetails} originalJob - The original job details to base the rebooked job on.
 * @returns {JobDetails} The new job details object prepared for rebooking.
 */
function createReBookJobDetails(originalJob: JobDetails): JobDetails {
  const jobDetails = initialiseJobDetails(originalJob);
  jobDetails.accounting = new JobAccountingDetails();

  delete jobDetails.jobId;
  delete jobDetails._id;
  jobDetails.recurringJobId = null;
  jobDetails.recurringJobReference = null;
  jobDetails.statusList = [25];

  // If job is TRIP/Quoted rate, then copy the original TripRate object into the new
  // accounting object
  if (jobDetails.rateTypeId === JobRateType.TRIP) {
    // Set client rate first
    const clientPrimaryRate = getClientPrimaryTripRate(
      originalJob.accounting?.clientRates?.[0]?.rate,
      jobDetails.serviceTypeId,
    );
    jobDetails.accounting.clientRates.push(clientPrimaryRate);
  }

  // If fleet asset rate is present then copy the original TripRate object into
  // the new accounting object
  if (
    originalJob.accounting?.fleetAssetRates?.[0]?.rate?.rateTypeId ===
    JobRateType.TRIP
  ) {
    const fleetAssetPrimaryRate = getFleetAssetPrimaryTripRate(
      originalJob.accounting?.fleetAssetRates?.[0]?.rate,
    );
    if (fleetAssetPrimaryRate) {
      jobDetails.accounting.fleetAssetRates.push(fleetAssetPrimaryRate);
    }
  }

  jobDetails.jobSourceType = null;
  jobDetails.exportType = null;

  if (!includeJobReferences.value) {
    jobDetails.jobReference = [new JobReferenceDetails()];
  }

  jobDetails.eventList = [];
  jobDetails.additionalAssets = [];
  jobDetails.driverId = '';
  jobDetails.fleetAssetId = '';

  if (reverseLegOrder.value) {
    // Perform side effects first
    jobDetails?.pudItems.reverse();

    jobDetails?.pudItems.forEach((pudItem) => {
      const switchToLegType =
        pudItem.legTypeFlag === 'P' ? 'dropoff' : 'pickup';
      copyLegSpecificValuesToNewType(
        switchToLegType,
        pudItem,
        props.clientDetails,
        jobDetails.serviceTypeId,
      );
    });

    updateRouteAndDurations(jobDetails);
  }

  // handle asap time definition
  if (allowAsapTimeDefinition.value) {
    jobDetails.pudItems[0].timeDefinition = 9;
  } else {
    jobDetails.pudItems[0].timeDefinition = 0;
  }

  const puds = jobDetails.pudItems.filter(
    (x: PUDItem) => x.legTypeFlag === 'P' || x.legTypeFlag === 'D',
  );
  if (jobDetails.pudItems.length > 0) {
    jobDetails.pudItems = setNewPudTimes(puds);

    jobDetails.pudItems.forEach((pud: PUDItem) => {
      pud.pudId = '';
      pud.attachments = [];
      pud.status = null;
      pud.pickupDate = returnStartOfDayFromEpoch(pud.epochTime);
      pud.pickupTime = returnFormattedTime(pud.epochTime);

      if (!includePickupDropReferences.value) {
        pud.pickupReference = [];
        pud.dropoffReference = [];
      }

      if (!includeManifestPayloadInformation.value) {
        pud.manifest = [];
      }

      if (!includeOverallPayloadInformation.value) {
        pud.weight = null;
        pud.dimensions = new Dimensions();
      }

      if (!includePickupDropNotes.value) {
        pud.notes = [];
      }

      pud.unassignedPudItemReference = [];
    });
  }

  // Handle job notes
  if (includeJobNotes.value) {
    jobDetails.notes = jobDetails.notes.filter(
      (x: Communication) => x.type && x.type.id !== 1,
    );
  } else {
    jobDetails.notes = jobDetails.notes.filter(
      (x: Communication) => x.type?.id === 2,
    );
  }
  return jobDetails;
}
/**
 * Calculates and assigns new epoch times to a sequence of PUD items
 * based on the time difference between each and a newly defined starting time.
 *
 * The first item's epochTime is set from `firstPudEpochTime`, and each
 * subsequent item's time is incremented by the previously recorded difference.
 *
 * Also resets the status of each PUD item (except the first).
 *
 * @param {PUDItem[]} puds - The list of pickup/drop PUD items.
 * @returns {PUDItem[]} The updated list of PUD items with adjusted times.
 */
function setNewPudTimes(puds: PUDItem[]): PUDItem[] {
  const timeDifferences: number[] = [];
  for (let i = 1; i < puds.length; i++) {
    timeDifferences.push(puds[i].epochTime - puds[i - 1].epochTime);
  }
  puds[0].epochTime = firstPudEpochTime.value;
  for (let i = 1; i < puds.length; i++) {
    puds[i].epochTime = puds[i - 1].epochTime + timeDifferences[i - 1];
    puds[i].status = null;
  }
  return puds;
}
/**
 * Computes the epoch time for the first PUD item based on the
 * main job date and the first PUD arrival time, using the user's timezone.
 *
 * @returns {number} The epoch time of the first PUD, or 0.
 */
const firstPudEpochTime = computed(() => {
  if (!mainJobDate.value || !firstPudArrival.value) {
    return 0;
  }
  return moment
    .tz(
      returnFormattedDate(mainJobDate.value, 'DDMMYYYY') +
        ' ' +
        firstPudArrival.value,
      'DDMMYYYY HHmm',
      companyDetailsStore.userLocale,
    )
    .valueOf();
});

// function to scroll to bottom when selected job from table
function scrollToBottom() {
  setTimeout(() => {
    nextTick(() => {
      const container = contentsDiv.value;
      if (container) {
        container.scrollTo({
          top: container.scrollHeight,
          behavior: 'smooth',
        });
      }
    });
  }, 50);
}

/**
 * Updates the planned route and depot durations for the job. This is called
 * the user reverses the leg order.
 */
async function updateRouteAndDurations(jobDetails: JobDetails): Promise<void> {
  await Promise.all([
    getPlannedRoute(jobDetails),
    jobDetails?.requestDepotDurations(),
  ]);
}

/**
 * Sends a request to get the job's planned route, based off its current Pud
 * Item coordinates.
 */
async function getPlannedRoute(jobDetails: JobDetails): Promise<void> {
  const result = await jobDetails?.getPlannedRoute();
  if (result && jobDetails) {
    jobDetails.plannedRoute = result;
    jobDetails?.setPudTimesFromPlannedRoute(jobDetails?.plannedRoute);
  }
}

/**
 * Watches changes to includeCancelJobs and updates workStatusMin.value.
 * If is set to true, re-fetch job list including cancelled jobs.
 */
watch(
  () => includeCancelJobs.value,
  (newValue) => {
    if (newValue === true) {
      workStatusMin.value = WorkStatus.CANCELLED;
      findClientRecentJobs();
    } else {
      workStatusMin.value = WorkStatus.BOOKED;
      findClientRecentJobs();
    }
  },
);
watch(
  () => sortByWorkDate.value,
  (newValue) => {
    if (newValue === true) {
      // Now sorting by Work Date
      sortByField.value = SearchJobRequestSortByField.WORK_DATE;
      findClientRecentJobs();
    } else {
      // Now sorting by Created Date
      sortByField.value = SearchJobRequestSortByField.CREATED_DATE;
      findClientRecentJobs();
    }
  },
);
</script>
<style scoped lang="scss">
.v-btn-custom {
  border-radius: $border-radius-Xlg !important;
  &.view {
    color: var(--accent);
    border: 2px solid var(--accent);

    &:disabled {
      border: 2px solid $translucent;
    }
  }
  &.apply {
    background-color: var(--primary-light) !important;
    background: var(--primary-gradient);
    border: 2px solid $translucent-light;
    color: white;

    &:hover {
      box-shadow: var(--box-shadow);
    }
  }
}

.check-icon {
  color: var(--success) !important;
  padding-left: 14px;
}

tr {
  cursor: pointer !important;
}
.header-container {
  padding-right: 20px;
  padding-left: 20px;
  margin-bottom: 0;

  .filters-section {
    justify-items: end;
  }
}

.btn-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;

  .action-btn {
    border-radius: $border-radius-btn;
    box-shadow: none !important;
    min-width: 120px;
    min-height: 38px;
    &:hover {
      box-shadow: $box-shadow !important;
    }
  }
}

.selected-job {
  display: flex;
  flex-direction: row;
  font-weight: 600;
  font-size: $font-size-18;
  margin: 0 auto;
  padding-top: 16px;
  .selected-job-id {
    color: $success;
    padding-left: 4px;
  }
}
</style>
