<template>
  <v-form ref="formRef">
    <v-layout row wrap pa-3>
      <v-flex md12>
        <v-alert
          type="info"
          :value="
            props.rateTypeId === 6 ||
            props.rateTypeId === 5 ||
            props.workStatus === 0
          "
        >
          <p class="ma-0">Please note:</p>
          <ul>
            <li v-if="props.rateTypeId === 6">
              You are about to rebook a <strong>QUOTED RATE</strong> job.
              Current Quoted Rate amounts for Client and Fleet Asset (where
              applicable) will be preserved where possible. Please review the
              dollar amounts after rebooking.
            </li>
            <li v-if="props.rateTypeId === 5">
              You are about to rebook a <strong>UNIT RATE</strong> job. All
              current Zone and Unit values will be included in the rebooked job.
            </li>
            <li v-if="props.rateTypeId === 5 && reverseLegOrderModel">
              All Zone and Unit rate information will be included when reversing
              leg order (all Zones will also be in reverse). Please closely
              review the job to ensure all Zone and Unit information is correct.
            </li>
            <li v-if="props.workStatus === 0">
              You are rebooking a <strong>CANCELLED</strong> job. Some rate
              information may be missing or incomplete. Please review the
              rebooked job to ensure all information is correct.
            </li>
          </ul>
        </v-alert>
      </v-flex>
      <v-flex md12 class="pt-2">
        <v-layout align-center>
          <h5 class="subheader--bold pr-3">Booking Options</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
          <div class="pl-2">
            <InformationTooltip :bottom="true">
              <v-layout slot="content"
                >Rebook includes allows you to define what should and should not
                be copied across to the new job.</v-layout
              >
            </InformationTooltip>
          </div>
        </v-layout>
      </v-flex>
      <v-flex md6 class="pt-2">
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Job References:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              v-model="includeJobReferencesModel"
              label="Include existing Job References"
              color="light-blue"
              persistent-hint
              hint="Rebook job with its current Job References"
              class="mt-2"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md6 class="pt-2">
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                Pickup/Drop References:
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              v-model="includePickupDropReferencesModel"
              label="Include existing Pickup/Drop References"
              color="light-blue"
              persistent-hint
              hint="Rebook job with its current Pickup/Dropoff references for all stops"
              class="mt-2"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md6 class="pt-2">
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">
                Payload (Manifest Items):
              </h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              label="Include Manifest Payload Information"
              v-model="includeManifestPayloadInformationModel"
              color="light-blue"
              persistent-hint
              hint="Rebook job with all current Manifest Payload Details for all stops"
              class="mt-2"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md6 class="pt-2">
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Payload (Overall):</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              label="Include Overall Payload Information"
              v-model="includeOverallPayloadInformationModel"
              color="light-blue"
              persistent-hint
              hint="Rebook job with its current values for Overall Payload"
              class="mt-2"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md6 class="pt-2">
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Job Notes:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              label="Include all existing Job Notes"
              v-model="includeJobNotesModel"
              color="light-blue"
              persistent-hint
              hint="Rebook job with all current Job Notes"
              class="mt-2"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md6 class="pt-2">
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Pickup/Drop Notes:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              label="Include all existing Pickup/Drop Notes"
              v-model="includePickupDropNotesModel"
              color="light-blue"
              persistent-hint
              hint="Rebook job with all current Pickup/Drop notes, for all stops"
              class="mt-2"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md6 class="pt-2">
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Leg Order:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              label="Rebook with reversed Leg Order"
              v-model="reverseLegOrderModel"
              color="light-blue"
              persistent-hint
              :disabled="
                props.selectedJobDetails
                  ? props.selectedJobDetails.numberOfLegs < 1
                  : true
              "
              hint="Rebook with the same stops in the reverse order. This is useful for creating a return trip."
              class="mt-2"
            />
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12 class="pt-4 pb-2">
        <v-layout align-center>
          <h5 class="subheader--bold pr-3">Booking Date and Time</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
          <div class="pl-2">
            <InformationTooltip :bottom="true">
              <v-layout slot="content"
                >The date the new job should be booked for.</v-layout
              >
            </InformationTooltip>
          </div>
        </v-layout>
      </v-flex>
      <v-flex md2 class="pr-1">
        <v-switch
          label="READY NOW"
          hide-details
          v-model="allowAsapTimeDefinitionModel"
          class="asap-switch"
          color="success"
        ></v-switch>
      </v-flex>
      <v-flex md5 class="pr-1">
        <DatePickerBasic
          @setEpoch="setMainJobDate"
          v-model="mainJobDateModel"
          :labelName="'Please select the first leg arrival time'"
          :epochTime="mainJobDateModel"
          flat
          color="orange"
          solo
          :soloInput="true"
          :disabled="allowAsapTimeDefinition"
          :formDisabled="allowAsapTimeDefinition"
        >
        </DatePickerBasic>
      </v-flex>
      <v-flex md5 class="pl-1">
        <v-text-field
          v-model="firstPudArrivalModel"
          :rules="[validate.twentyFourHourTime]"
          hint="24-hour time"
          mask="##:##"
          persistent-hint
          label="Arrival Time"
          color="orange"
          outline
          flat
          class="v-solo-custom"
          @focus="$event.target.select()"
          :disabled="allowAsapTimeDefinition"
        />
      </v-flex>
    </v-layout>
  </v-form>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import { SearchJobsSummary } from '@/interface-models/Jobs/SearchJob/SearchJobSummary';
import { computed, Ref, ref } from 'vue';

const props = withDefaults(
  defineProps<{
    rateTypeId?: number | null;
    workStatus?: number | null;
    selectedJobDetails: SearchJobsSummary | null;
    clientDetails: ClientDetails | null;
    includeJobReferences: boolean;
    includePickupDropReferences: boolean;
    includeManifestPayloadInformation: boolean;
    includeOverallPayloadInformation: boolean;
    includeJobNotes: boolean;
    includePickupDropNotes: boolean;
    reverseLegOrder: boolean;
    modelValue: number;
    firstPudArrival: string;
    allowAsapTimeDefinition: boolean;
  }>(),
  {
    rateTypeId: null,
    workStatus: null,
    selectedJobDetails: null,
    clientDetails: null,
    includeJobReferences: false,
    includePickupDropReferences: false,
    includeManifestPayloadInformation: false,
    includeOverallPayloadInformation: false,
    includeJobNotes: false,
    includePickupDropNotes: true,
    reverseLegOrder: false,
    modelValue: 0,
    firstPudArrival: '',
    allowAsapTimeDefinition: true,
  },
);

const validate = validationRules;
const mainDate: Ref<number> = ref(props.modelValue);

// emit updated value to parent
const emit = defineEmits<{
  (event: 'update:includeJobReferences', value: boolean): void;
  (event: 'update:includePickupDropReferences', value: boolean): void;
  (event: 'update:includeManifestPayloadInformation', value: boolean): void;
  (event: 'update:includeOverallPayloadInformation', value: boolean): void;
  (event: 'update:includeJobNotes', value: boolean): void;
  (event: 'update:includePickupDropNotes', value: boolean): void;
  (event: 'update:reverseLegOrder', value: boolean): void;
  (event: 'update:modelValue', value: number): void;
  (event: 'update:firstPudArrival', value: string): void;
  (event: 'update:allowAsapTimeDefinition', value: boolean): void;
}>();

// computed refs to update props
const includeJobReferencesModel = computed({
  get() {
    return props.includeJobReferences;
  },
  set(value: boolean) {
    emit('update:includeJobReferences', value);
  },
});

const includePickupDropReferencesModel = computed({
  get() {
    return props.includePickupDropReferences;
  },
  set(value: boolean) {
    emit('update:includePickupDropReferences', value);
  },
});

const includeManifestPayloadInformationModel = computed({
  get() {
    return props.includeManifestPayloadInformation;
  },
  set(value: boolean) {
    emit('update:includeManifestPayloadInformation', value);
  },
});

const includeOverallPayloadInformationModel = computed({
  get() {
    return props.includeOverallPayloadInformation;
  },
  set(value: boolean) {
    emit('update:includeOverallPayloadInformation', value);
  },
});

const includeJobNotesModel = computed({
  get() {
    return props.includeJobNotes;
  },
  set(value: boolean) {
    emit('update:includeJobNotes', value);
  },
});

const includePickupDropNotesModel = computed({
  get() {
    return props.includePickupDropNotes;
  },
  set(value: boolean) {
    emit('update:includePickupDropNotes', value);
  },
});

const reverseLegOrderModel = computed({
  get() {
    return props.reverseLegOrder;
  },
  set(value: boolean) {
    emit('update:reverseLegOrder', value);
  },
});

const allowAsapTimeDefinitionModel = computed({
  get(): boolean {
    return props.allowAsapTimeDefinition;
  },
  set(value: boolean): void {
    emit('update:allowAsapTimeDefinition', value);
  },
});

const mainJobDateModel = computed({
  get() {
    return props.modelValue;
  },
  set(value: number) {
    emit('update:modelValue', value);
  },
});

const firstPudArrivalModel = computed({
  get() {
    return props.firstPudArrival;
  },
  set(value: string) {
    emit('update:firstPudArrival', value);
  },
});

// function to set date and emit updated value
function setMainJobDate(epochDate: any) {
  mainDate.value = epochDate;
  emit('update:modelValue', epochDate);
}
</script>
