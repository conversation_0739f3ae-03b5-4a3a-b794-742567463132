<template>
  <div class="tabbed-notes-panel" :class="{ 'side-panel': isSidePanel }">
    <!-- Tabs Navigation -->
    <div class="notes-tabs">
      <button
        v-for="tab in availableTabs"
        :key="tab.id"
        class="notes-tab"
        :class="{ active: activeTab === tab.id }"
        @click="scrollToSection(tab.id)"
        :disabled="tab.count === 0"
      >
        <span class="tab-label">{{ tab.label }}</span>
        <span class="tab-count" v-if="tab.count > 0">({{ tab.count }})</span>
      </button>
    </div>

    <!-- Notes Content -->
    <div class="notes-content" ref="notesContent">
      <!-- CLIENT NOTES -->
      <div
        v-if="clientNotes.length > 0"
        :id="'section-' + 'client'"
        class="notes-section"
      >
        <div class="section-header">
          <h3 class="section-title">
            <i class="far fa-user-circle"></i>
            Client Notes
          </h3>
          <div class="section-divider"></div>
          <span class="section-count">({{ clientNotes.length }})</span>
        </div>
        <div class="section-content">
          <NotesList
            :communications="clientNotes"
            :isBookingScreen="false"
            :showVisibilityTypeName="true"
            :allowDelete="false"
            :allowEdit="false"
          />
        </div>
      </div>

      <!-- JOB NOTES -->
      <div
        v-if="jobNotes.length > 0"
        :id="'section-' + 'job'"
        class="notes-section"
      >
        <div class="section-header">
          <h3 class="section-title">
            <i class="far fa-file-alt"></i>
            Job Notes
          </h3>
          <div class="section-divider"></div>
          <span class="section-count">({{ jobNotes.length }})</span>
        </div>
        <div class="section-content">
          <NotesList
            :communications="jobNotes"
            :isBookingScreen="false"
            :showVisibilityTypeName="true"
            :allowDelete="false"
            :allowEdit="false"
          />
        </div>
      </div>

      <!-- PUD Notes -->
      <div
        v-if="pudNotes.length > 0"
        :id="'section-' + 'stop'"
        class="notes-section"
      >
        <div class="section-header">
          <h3 class="section-title">
            <i class="fas fa-route"></i>
            Stop Notes
          </h3>
          <div class="section-divider"></div>
          <span class="section-count">({{ pudNotes.length }})</span>
        </div>
        <div class="section-content">
          <NotesList
            :communications="pudNotes"
            :isBookingScreen="false"
            :showVisibilityTypeName="true"
            :allowDelete="false"
            :allowEdit="false"
            :showPudInfo="true"
          />
        </div>
      </div>

      <!-- No Data Message -->
      <div v-if="totalNotesCount === 0" class="no-data-message">
        <i class="far fa-sticky-note no-data-icon"></i>
        <p>No notes available for this job</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import NotesList from '@/components/common/notes_list/notes_list.vue';
import { ClientDetails } from '@/interface-models/Client/ClientDetails/ClientDetails';
import Communication from '@/interface-models/Generic/Communication/Communication';
import { AddToJobType } from '@/interface-models/Generic/Communication/CommunicationTypes/ClientInstructions';
import { JobDetails } from '@/interface-models/Jobs/JobDetails';
import { computed, nextTick, onMounted, Ref, ref } from 'vue';

// Interface for PUD notes with additional PUD information
interface PUDNoteWithInfo extends Communication {
  pudInfo: {
    legNumber: number;
    legType: string;
    suburb: string;
    customerName: string;
  };
}
// Define tab interface
interface TabItem {
  id: string;
  label: string;
  icon: string;
  count: number;
}

const props = defineProps<{
  jobDetails?: JobDetails | null;
  clientDetails?: ClientDetails | null;
  isSidePanel?: boolean;
  pudId?: number | string | null;
}>();

const notesContent = ref<HTMLElement>();
const activeTab: Ref<string> = ref('client');

// Computed properties for different note types
const clientNotes = computed(() => {
  if (!props.clientDetails?.specialInstructions) {
    return [];
  }
  // Include all client special instructions
  return props.clientDetails.specialInstructions.filter((note) => {
    // Include all notes except those with NEVER addToJobType
    // Only check addToJobType for ClientInstructions (type 2)
    if (note.type?.id === 2 && note.type.communicationDetails?.addToJobType) {
      return note.type.communicationDetails.addToJobType !== AddToJobType.NEVER;
    }
    return true;
  });
});

const jobNotes = computed(() => {
  if (!props.jobDetails?.notes) {
    return [];
  }

  // Filter to show only Job Notes (type 3) and exclude PUD notes
  const pudNoteIds = new Set(
    (props.jobDetails?.pudItems || []).flatMap((pud) =>
      (pud.notes || []).map((n) => n.id),
    ),
  );

  return props.jobDetails.notes.filter(
    (n) => n.type?.id === 3 && !pudNoteIds.has(n.id), // Only Job Notes (type 3)
  );
});

const pudNotes = computed((): PUDNoteWithInfo[] => {
  if (!props.jobDetails?.pudItems) {
    return [];
  }

  const allPudItems = props.jobDetails.pudItems;

  const result = allPudItems.flatMap((pud, pudIndex) => {
    // If pudId (uniqueId) is provided, filter out notes for that specific PUD
    if (props.pudId && pud.uniqueId === props.pudId) {
      return [];
    }

    // Add PUD information to each note
    return (pud.notes || []).map((note) => ({
      ...note,
      pudInfo: {
        legNumber: pudIndex + 1,
        legType: pud.legTypeFlag === 'P' ? 'Pickup' : 'Drop-off',
        suburb: pud.address.suburb || 'Unknown',
        customerName: pud.customerDeliveryName || '',
      },
    })) as PUDNoteWithInfo[];
  });

  return result;
});

const totalNotesCount = computed(() => {
  return (
    clientNotes.value.length + jobNotes.value.length + pudNotes.value.length
  );
});

// Available tabs - always show all three tabs
const availableTabs = computed((): TabItem[] => {
  return [
    {
      id: 'client',
      label: 'CLIENT',
      icon: 'far fa-user-circle',
      count: clientNotes.value.length,
    },
    {
      id: 'job',
      label: 'JOB',
      icon: 'far fa-file-alt',
      count: jobNotes.value.length,
    },
    {
      id: 'stop',
      label: 'STOP',
      icon: 'fas fa-route',
      count: pudNotes.value.length,
    },
  ];
});

// Scroll to section function
const scrollToSection = async (sectionId: string) => {
  activeTab.value = sectionId;

  await nextTick();

  const section = document.getElementById(`section-${sectionId}`);
  const container = notesContent.value;

  if (section && container) {
    // Get all sections to calculate the correct position
    const allSections = container.querySelectorAll('.notes-section');
    let targetOffset = 0;

    // Calculate the offset by summing up the heights of all sections before the target
    for (let i = 0; i < allSections.length; i++) {
      const currentSection = allSections[i] as HTMLElement;
      if (currentSection.id === `section-${sectionId}`) {
        break; // Found our target section
      }
      targetOffset += currentSection.offsetHeight;
    }
    // Scroll the container to the section's position
    container.scrollTo({
      top: targetOffset,
      behavior: 'smooth',
    });
  }
};

onMounted(() => {
  activeTab.value = 'client';
});
</script>

<style scoped lang="scss">
.tabbed-notes-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--background-color-200);

  &.side-panel {
    .notes-content {
      flex: 1;
      overflow-y: auto !important;
      padding: 12px;
      scroll-behavior: smooth;
    }
    .notes-tabs {
      margin: 8px;
    }
  }
}

/* Tabs Navigation */
.notes-tabs {
  display: flex;
  background: var(--background-color-200);
  border-bottom: 2px solid var(--border-color);
  padding: 4px;
  margin-right: 12px;

  &::-webkit-scrollbar {
    display: none;
  }
}

.notes-tab {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 2px 8px;
  background: transparent;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  min-width: 80px;
  justify-content: center;

  &:hover:not(:disabled) {
    background: var(--background-color-300);
    border-bottom-color: var(--accent);
  }

  // &.active {
  // background: var(--background-color-400);
  // border-bottom-color: var(--warning);
  // color: var(--warning);
  // }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.notes-tab:hover:not(:disabled) .tab-icon,
.notes-tab.active .tab-icon {
  transform: scale(1.1);
}

.tab-label {
  font-weight: 500;
  font-size: 0.9rem;
}

.tab-count {
  font-size: 0.8rem;
  opacity: 0.8;
  font-weight: 400;
}

/* Notes Content */
.notes-content {
  flex: 1;
  overflow-y: auto;
  scroll-behavior: smooth;
}

.notes-section {
  margin-bottom: 32px;
  animation: fadeInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  margin-top: 6px;
  padding-bottom: 12px;
  // border-bottom: 1px solid var(--warning);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: var(--warning);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  i {
    color: var(--warning);
    font-size: 14px;
    font-weight: 700;
  }
}

.section-divider {
  flex: 1;
  height: 1px;
  background: var(--border-color);
  margin: 0 8px;
}

.section-count {
  font-size: 12px;
  color: var(--light-text-color);
  font-weight: 500;
  padding-right: 12px;
  // border-radius: 4px;
}

.section-content {
  // background: var(--background-color-200);
  border-radius: 8px;
  padding-right: 8px;
  // border: 1px solid var(--border-color);
}

/* No Data Message */
.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--light-text-color);
  text-align: center;
  padding: 40px 20px;
  animation: fadeIn 0.6s ease-out;
}

.no-data-icon {
  font-size: 2.5rem;
  color: var(--light-text-color);
  margin-bottom: 16px;
  opacity: 0.4;
  transition: all 0.3s ease;
  animation: float 3s ease-in-out infinite;
}

.no-data-icon:hover {
  animation: bounce 0.6s ease-out;
  opacity: 0.6;
}

.no-data-message p {
  margin: 0;
  font-size: 0.9rem;
  font-weight: 400;
  color: var(--light-text-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .notes-tabs {
    padding: 0 8px;
  }

  .notes-tab {
    padding: 10px 12px;
    min-width: 100px;
    gap: 6px;
  }

  .tab-label {
    font-size: 0.8rem;
  }

  .notes-content {
    padding: 12px;
  }

  .section-header {
    margin-bottom: 12px;
    padding-bottom: 8px;
  }

  .section-title {
    font-size: 1rem;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes bounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translateY(0);
  }
  40%,
  43% {
    transform: translateY(-8px);
  }
  70% {
    transform: translateY(-4px);
  }
  90% {
    transform: translateY(-2px);
  }
}
</style>
