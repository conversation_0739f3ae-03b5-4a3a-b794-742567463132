import { ObjectId } from '@/helpers/JobDataHelpers/JobDataHelpers';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { QuoteDetails } from '@/interface-models/Jobs/Quote/QuoteDetails';
import { sessionManager } from '@/store/session/SessionState';

export function mockQuoteData(clientId: string): QuoteDetails[] {
  return [
    {
      id: ObjectId(),
      company: sessionManager.getCompanyId(),
      division: sessionManager.getDivisionId(),
      clientId: clientId,
      quoteId: 1,
      quoteCreationTime: 1722528000000,
      quoteExpiryTime: 1725206400000,
      jobDetails: new JobDetails(),
    },
  ];
}
