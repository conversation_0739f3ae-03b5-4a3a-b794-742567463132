<template>
  <JobContentDialog
    :showDialog.sync="dialogController"
    title="Adjust Job Pricing"
    width="90%"
    contentPadding="pa-2"
    @cancel="dialogController = false"
    @confirm="applyChanges"
    :showActions="false"
    :isContentDialog="true"
    class="v-dialog-custom"
    :jobDetails="jobDetails"
    :clientDetails="clientDetails"
    :driverId="jobDetails.driverId"
    :jobId="jobDetails.jobId"
  >
    <v-flex>
      <v-layout align-center class="header-container">
        <span class="subheader">Adjust Pricing Details:</span>
        <v-flex>
          <v-divider></v-divider>
        </v-flex>
        <v-tooltip bottom v-if="!sessionManager.isClientPortal()">
          <template v-slot:activator="{ on }">
            <v-btn
              depressed
              solo
              icon
              @click="additionalChargeDialogIsOpen = true"
              class="v-btn-confirm-custom add-btn"
              v-on="on"
            >
              <v-icon>add</v-icon>
            </v-btn>
          </template>
          <span>Add Additional Charges</span>
        </v-tooltip>
      </v-layout>

      <v-layout md12 class="body-scrollable--65 body-min-height--65 pa-2">
        <v-form ref="pricingDialogForm" style="width: 100%">
          <v-flex md12 class="dialog-body">
            <v-layout row wrap>
              <v-flex md12 justify-center>
                <v-flex class="button-group">
                  <v-tooltip
                    v-for="rateType in availableRateTypes"
                    :key="rateType.rateTypeId"
                    bottom
                    :disabled="!rateType.tooltipText"
                  >
                    <template v-slot:activator="{ on }">
                      <span v-on="on">
                        <v-btn
                          :class="{
                            'v-btn--active':
                              rateType.rateTypeId === rateTypeIdController,
                          }"
                          :disabled="rateFormDisabled || rateType.isDisabled"
                          :loading="rateType.isLoading"
                          flat
                          @click="rateTypeIdController = rateType.rateTypeId"
                        >
                          <span class="px-2"
                            ><strong>{{ rateType.buttonText }}</strong>
                            Rates</span
                          >
                        </v-btn>
                      </span>
                    </template>
                    {{ rateType.tooltipText }}
                  </v-tooltip>
                </v-flex>
                <v-layout pt-2>
                  <v-text-field
                    v-if="!!rateTypeIdController"
                    class="v-solo-custom"
                    v-model="serviceTypeFilter"
                    solo
                    flat
                    label="Search Service Types"
                    append-icon="search"
                    @keypress="handleEnterEvent"
                  ></v-text-field>
                </v-layout>
                <JobBookingServiceSelection
                  v-if="rateTypeIdController"
                  v-model="selectedServiceTypeId"
                  :rateSummaries="rateSummaries"
                  :selectedRateTypeId="rateTypeIdController"
                  :searchFilter="serviceTypeFilter"
                  :formDisabled="rateFormDisabled || serviceSelectionDisabled"
                ></JobBookingServiceSelection>
              </v-flex>
              <v-flex
                md12
                mt-3
                v-if="
                  computedAccountingDetails &&
                  (rateToApply?.rateTypeId === JobRateType.ZONE ||
                    (rateToApply?.rateTypeId === JobRateType.DISTANCE &&
                      !sessionManager.isClientPortal()) ||
                    rateToApply?.rateTypeId === JobRateType.UNIT ||
                    rateToApply?.rateTypeId === JobRateType.TRIP)
                "
              >
                <v-layout align-center pb-3>
                  <span class="subheader">Add More Details</span>
                  <v-flex>
                    <v-divider></v-divider>
                  </v-flex>
                  <v-tooltip
                    bottom
                    v-if="
                      rateTypeIdController === JobRateType.DISTANCE &&
                      computedAccountingDetails.finishedJobData
                        ?.clientTravelDelayBreakdown
                    "
                  >
                    <template v-slot:activator="{ on }">
                      <v-btn
                        class="booking-icon-btn"
                        icon
                        flat
                        v-on="on"
                        @click="demurrageDurationComponent?.openDialog()"
                      >
                        <v-icon :size="18">fas fa-traffic-light-stop</v-icon>
                      </v-btn>
                    </template>
                    <span>Add/Edit Traffic Delays</span>
                  </v-tooltip>
                </v-layout>
                <DemurrageDuration
                  v-if="
                    rateTypeIdController === JobRateType.DISTANCE &&
                    computedAccountingDetails.finishedJobData
                      ?.clientTravelDelayBreakdown
                  "
                  ref="demurrageDurationComponent"
                  :demurrageRateData="[]"
                  :travelDelayRateData="
                    computedAccountingDetails.finishedJobData
                      .clientTravelDelayBreakdown
                  "
                  @refreshAccountingTotals="refreshComputedAccountingDetails"
                  :pudItems="jobDetails.pudItems"
                  :isClient="true"
                  :readOnly="false"
                  :workDiaryList="[]"
                  :enabledDelayTypes="[JobDelayType.TRAVEL_DELAY]"
                  :showActivator="false"
                />
                <JobZoneRateDetails
                  v-if="
                    rateToApply.rateTypeId === JobRateType.ZONE &&
                    zoneRateInformation
                  "
                  :zones="rateToApply.rateTypeObject"
                  :zoneRateInformation="zoneRateInformation"
                  @updateZoneRateInformation="handleUpdateZoneRateInformation"
                >
                </JobZoneRateDetails>

                <DistanceRateBooking
                  v-if="
                    rateToApply.rateTypeId === JobRateType.DISTANCE &&
                    accountingDetails.additionalData?.distanceRate
                      ?.chargeableClientDistance.edited
                  "
                  :chargeBasis="rateToApply.rateTypeObject.chargeBasis"
                  :editedTravelDistance.sync="
                    accountingDetails.additionalData.distanceRate
                      .chargeableClientDistance.edited
                  "
                  :uneditedTravelDistance="
                    returnOriginalDistanceForChargeBasis(
                      accountingDetails.additionalData.distanceRate
                        ?.chargeableClientDistance,
                      rateToApply.rateTypeObject.chargeBasis,
                    )
                  "
                  :keyDistances="jobDetails.getRangeDeterminantValues"
                  @refreshAccountingDetails="refreshComputedAccountingDetails"
                >
                </DistanceRateBooking>
                <JobUnitRateDetails
                  v-if="
                    rateToApply.rateTypeId === JobRateType.UNIT &&
                    unitRateInformation
                  "
                  :unitRates="rateToApply.rateTypeObject"
                  :unitRateInformation="unitRateInformation"
                  @updateUnitRateInformation="handleUpdateUnitRateInformation"
                ></JobUnitRateDetails>
                <v-layout
                  row
                  wrap
                  v-if="
                    rateToApply.rateTypeId === JobRateType.TRIP &&
                    tripRateInformation
                  "
                >
                  <v-flex md6>
                    <TripRateBooking
                      class="client"
                      title="Client"
                      :enableTripRate.sync="tripRateInformation.client"
                      :fuelSurchargeRate.sync="
                        tripRateInformation.clientFuelSurcharge
                      "
                      :rateAmount.sync="tripRateInformation.clientRate"
                      @tripRateUpdated="refreshComputedAccountingDetails"
                    />
                  </v-flex>
                  <v-flex md6>
                    <TripRateBooking
                      class="driver"
                      title="Driver"
                      :enableTripRate.sync="tripRateInformation.fleetAsset"
                      :fuelSurchargeRate.sync="
                        tripRateInformation.fleetAssetFuelSurcharge
                      "
                      :rateAmount.sync="tripRateInformation.fleetAssetRate"
                      @tripRateUpdated="refreshComputedAccountingDetails"
                    />
                  </v-flex>
                  <v-flex
                    md2
                    class="direct-invoice-container"
                    v-if="recurrenceType === JobRecurrenceType.PERMANENT"
                  >
                    <v-layout align-center style="position: relative">
                      <span class="title-txt" :class="titleTxtClass"
                        >DIRECT TO INVOICING</span
                      >
                      <v-spacer></v-spacer>
                      <v-switch
                        v-model="directToInvoicing"
                        :false-value="false"
                        :true-value="true"
                        color="orange"
                        :disabled="!directToInvoiceEnabled"
                      />
                    </v-layout>
                  </v-flex>
                </v-layout>
              </v-flex>
              <v-flex
                md12
                pt-3
                pb-1
                v-if="computedAccountingDetails && selectedServiceTypeId"
              >
                <v-layout align-start px-2>
                  <span class="subheader pr-3 pt-2"
                    >Current Totals (estimate):</span
                  >
                  <v-flex pr-2>
                    <v-divider class="mt-4"></v-divider>
                  </v-flex>
                  <FuelSurchargeSelection
                    v-if="!sessionManager.isClientPortal()"
                    inputType="SELECT"
                    :key="fuelSurchargeSelectionKey"
                    :selectedFuelSurchargeId="selectedFuelSurchargeId"
                    :selectedBracketId="selectedFuelBracketId"
                    :items="props.fuelSurcharges"
                    :isFormDisabled="false"
                    :jobRangeData="jobDetails.getRangeDeterminantValues"
                    @updateSelection="handleUpdatedFuelSelection"
                  ></FuelSurchargeSelection>
                </v-layout>
                <JobBookingPricingSummary
                  :serviceTypeId="selectedServiceTypeId"
                  :pudItems="pudItems"
                  :accounting="computedAccountingDetails"
                  :readOnly="false"
                  @updateAdditionalCharge="updateExistingAdditionalCharge"
                ></JobBookingPricingSummary>
              </v-flex>

              <AddAdditionalCharge
                :isDialogOpen.sync="additionalChargeDialogIsOpen"
                :chargeItems="rootStore.additionalChargeItemList"
                :currentAppliedCharges="
                  computedAccountingDetails?.additionalCharges.chargeList ?? []
                "
                :showClientFuelApplied="
                  jobDetails.accounting.clientRates?.[0]?.rate
                    ?.isClientFuelApplied ?? true
                "
                @addChargesToJob="addAdditionalChargesToJob"
              ></AddAdditionalCharge>
            </v-layout>
          </v-flex>
        </v-form>
      </v-layout>
      <v-divider></v-divider>
      <v-flex md12 class="btn-container">
        <v-btn
          class="action-btn"
          outline
          flat
          color="error"
          @click="dialogController = false"
          >Cancel</v-btn
        >
        <v-btn
          solo
          depressed
          color="success"
          class="action-btn confirm"
          :disabled="computedAccountingDetails === null"
          @click="applyChanges"
          >Confirm</v-btn
        >
      </v-flex>
    </v-flex>
  </JobContentDialog>
</template>

<script setup lang="ts">
export type RateToApply =
  | {
      rateTypeId: 1;
      rateTypeName: string;
      serviceTypeName: string;
      rateTypeObject: TimeRateType;
      rateTableItem: RateTableItems;
    }
  | {
      rateTypeId: 2;
      rateTypeName: string;
      serviceTypeName: string;
      rateTypeObject: ZoneRateType[];
      rateTableItem: RateTableItems;
    }
  | {
      rateTypeId: 3;
      rateTypeName: string;
      serviceTypeName: string;
      rateTypeObject: DistanceRateType;
      rateTableItem: RateTableItems;
    }
  | {
      rateTypeId: 4;
      rateTypeName: string;
      serviceTypeName: string;
      rateTypeObject: PointToPointRateType[];
      rateTableItem: RateTableItems;
    }
  | {
      rateTypeId: 5;
      rateTypeName: string;
      serviceTypeName: string;
      rateTypeObject: UnitRate[];
      rateTableItem: RateTableItems;
    }
  | {
      rateTypeId: 6;
      rateTypeName: string;
      serviceTypeName: string;
      rateTypeObject: TripRate;
      rateTableItem: RateTableItems;
    }
  | {
      rateTypeId: 7;
      rateTypeName: string;
      serviceTypeName: string;
      rateTypeObject: ZoneToZoneRateType[];
      rateTableItem: RateTableItems;
    };

export interface JobPricingDetailsUpdate {
  serviceTypeId: number;
  rateTypeId: number;
  accounting: JobAccountingDetails;
  pudItems: PUDItem[];
  directToInvoicing: boolean;
}

interface ServiceTypeRateButton extends ServiceTypeRates {
  buttonText: string;
  isLoading: boolean;
  isDisabled: boolean;
  tooltipText: string;
}

import JobBookingPricingSummary from '@/components/booking/pricing_details/job_booking_pricing_summary.vue';
import AddAdditionalCharge from '@/components/common/additional_charge/add_additional_charge.vue';
import JobUnitRateDetails from '@/components/common/job_unit_rate_details.vue';
import JobZoneRateDetails from '@/components/common/job_zone_rate_details.vue';
import JobContentDialog from '@/components/common/ui-elements/job_content_dialog.vue';
import DistanceRateBooking from '@/components/operations/BookJob/service_rates_booking/distance_rate_booking/distance_rate_booking.vue';
import TripRateBooking from '@/components/operations/BookJob/service_rates_booking/trip_rate_booking/index.vue';
import DemurrageDuration from '@/components/operations/ReviewJob/demurrage_duration/demurrage_duration.vue';
import {
  addPercentageTo,
  DisplayCurrencyValue,
  getPercentageOf,
} from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { returnCorrectDuration } from '@/helpers/DateTimeHelpers/DurationHelpers';
import { addTripRateDetailsToJob } from '@/helpers/JobBooking/Accounting/JobBookingTripRateHelpers';
import { estimateClientTotals } from '@/helpers/JobBooking/JobBookingPricingHelpers';
import {
  mapPudItemToUnitRateSummary,
  mapPudItemToZoneRateSummary,
  outsideMetroChargeApplies,
  updateRateDetailsInPuds,
} from '@/helpers/JobBooking/JobBookingPudHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  applyAdditionalChargeUpdateToAccounting,
  tollAdminAndServicesFeeHandler,
} from '@/helpers/RateHelpers/AdditionalChargeHelpers';
import {
  returnChargeIncrementDescription,
  returnMinimumChargeDescription,
  returnOriginalDistanceForChargeBasis,
  returnRangeRateListSummary,
} from '@/helpers/RateHelpers/DistanceRateHelpers';
import {
  isFuelSurchargeApplicable,
  returnFuelSurchargeForAccounting,
  returnMostSuitableFuelSurcharge,
} from '@/helpers/RateHelpers/FuelSurchargeHelpers';
import {
  isDistanceRateTypeObject,
  isTimeRateTypeObject,
} from '@/helpers/RateHelpers/RateTableItemHelpers';
import { addOrReplaceRateTableItems } from '@/helpers/RateHelpers/ServiceRateHelpers';
import {
  getLegNamesFromTimeRate,
  timeRateChargeIncrement,
  timeRateDescription,
  timeRateMinCharge,
} from '@/helpers/RateHelpers/TimeRateHelpers';
import { returnInitialTripRateInfo } from '@/helpers/RateHelpers/TripRateHelpers';
import {
  requestZoneToZoneRatesForPudItems,
  returnZoneToZoneName,
} from '@/helpers/RateHelpers/ZoneToZoneRateHelpers';
import { initialiseJobAccountingDetails } from '@/helpers/classInitialisers/InitialiseJobDetails';
import { initialisePudItem } from '@/helpers/classInitialisers/InitialisePudItem';
import { AdditionalChargeItem } from '@/interface-models/AdditionalCharges/AdditionalChargeItem';
import { UpdateAdditionalChargeItem } from '@/interface-models/AdditionalCharges/AdditionalChargeUpdateOperation';
import { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import { AdditionalChargeList } from '@/interface-models/Generic/Accounting/AdditionalChargeList';
import { JobAccountingDetails } from '@/interface-models/Generic/Accounting/JobAccountingDetails';
import JobPrimaryRate from '@/interface-models/Generic/Accounting/JobPrimaryRate';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import serviceTypeRates, {
  JobRateType,
  ServiceTypeRates,
} from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { returnStartAndReturnLegsLongNameById } from '@/interface-models/Generic/StartAndReturnLegs/StartAndReturnLegs';
import type { JobDetails } from '@/interface-models/Jobs/JobDetails';
import { JobRecurrenceType } from '@/interface-models/Jobs/JobRecurrenceType';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { ClientFuelSurchargeRate } from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import { ClientServiceRate } from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import { ClientServiceRateVariations } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariations';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import { returnReadableChargeBasisName } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/ChargeBasis';
import DistanceRateType from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/DistanceRateType';
import { returnReadableRateBracketTypeName } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/RateBracketType';
import PointToPointRateType from '@/interface-models/ServiceRates/ServiceTypes/PointToPointServiceRate/PointToPointRateType';
import TimeRateType from '@/interface-models/ServiceRates/ServiceTypes/TimeServiceRate/TimeRateType';
import { TripRate } from '@/interface-models/ServiceRates/ServiceTypes/TripRate/TripRate';
import UnitRate from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRate';
import { UnitRatePudSummary } from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRatePudSummary';
import { ZoneRatePudSummary } from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRatePudSummary';
import { ZoneRateType } from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRateType';
import { ZoneToZoneRateSummary } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneToZoneRateSummary';
import { ZoneToZoneRateType } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneToZoneRateType';
import { TripRateInformation } from '@/interface-models/ServiceRates/TripRateInformation';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useRootStore } from '@/store/modules/RootStore';
import { sessionManager } from '@/store/session/SessionState';

import { JobDelayType } from '@/interface-models/ServiceRates/Demurrage/applicableDemurrages';
import {
  computed,
  ComputedRef,
  nextTick,
  Ref,
  ref,
  watch,
  WritableComputedRef,
} from 'vue';
import FuelSurchargeSelection from './fuel_surcharge_selection.vue';
import JobBookingServiceSelection, {
  DistanceRateSummary,
  RatesSummary,
  TimeRateSummary,
  TripRateSummary,
  ZoneRateSummary,
} from './job_booking_service_selection.vue';

const emit = defineEmits<{
  (event: 'update:isDialogOpen', payload: boolean): void;
  (event: 'applyChanges', payload: JobPricingDetailsUpdate): void;
}>();

const props = withDefaults(
  defineProps<{
    jobDetails: JobDetails;
    serviceRates: ClientServiceRate;
    rateVariations: ClientServiceRateVariations[] | null;
    fuelSurcharges: ClientFuelSurchargeRate[];
    commonAddresses: ClientCommonAddress[];
    recurrenceType: JobRecurrenceType;
    isDialogOpen?: boolean;
    clientDetails?: ClientDetails | null;
  }>(),
  {
    isDialogOpen: false,
    rateVariations: () => [],
    clientDetails: null,
  },
);

const pricingDialogForm: Ref<any> = ref(null);

const rootStore = useRootStore();

const pudItems: Ref<PUDItem[]> = ref([]);
const accountingDetails: Ref<JobAccountingDetails> = ref(
  new JobAccountingDetails(),
);
const computedAccountingDetails: Ref<JobAccountingDetails | null> = ref(null);

const rateSummaries: Ref<RatesSummary[]> = ref([]);

const availableRateTypeIds: Ref<JobRateType[]> = ref([]);

const selectedRateTypeId: Ref<JobRateType | null> = ref(null);
const selectedServiceTypeId: Ref<number> = ref(props.jobDetails.serviceTypeId);

const serviceTypeFilter: Ref<string> = ref('');

const zoneRateInformation: Ref<ZoneRatePudSummary[] | null> = ref(null);
const unitRateInformation: Ref<UnitRatePudSummary[] | null> = ref(null);
const tripRateInformation: Ref<TripRateInformation> = ref(
  new TripRateInformation(),
);
const zoneToZoneRatesAvailable: Ref<boolean> = ref(false);
const isLoadingZoneToZoneRates: Ref<boolean> = ref(false);

const directToInvoicing = ref(props.jobDetails.isDirectToInvoice);

const additionalChargeDialogIsOpen: Ref<boolean> = ref(false);

const fuelSurchargeSelectionKey: Ref<number> = ref(0);
const selectedFuelSurchargeId: Ref<string> = ref('');
const selectedFuelBracketId: Ref<string> = ref('');

const demurrageDurationComponent: Ref<any> = ref(null);

// Computed property to check if the direct to invoice switch should be enabled
const directToInvoiceEnabled = computed(() => {
  return (
    !!tripRateInformation.value.fleetAsset &&
    !!tripRateInformation.value.client &&
    (!!tripRateInformation.value.clientRate ||
      !!tripRateInformation.value.fleetAssetRate) &&
    accountingDetails.value.additionalCharges.chargeList.length === 0 &&
    props.jobDetails.pudItems.length === 2
  );
});

/**
 * Watches changes to props.jobDetails.isDirectToInvoice and updates directToInvoicing.value.
 * If is set to false, a error notification is displayed that Direct to Invoice is no longer.
 */
watch(
  () => props.jobDetails.isDirectToInvoice,
  (newValue) => {
    directToInvoicing.value = newValue;
    if (directToInvoicing.value === false) {
      showNotification(
        'Not available after recent changes applied to the job.',
        {
          title: 'Direct to Invoice Disabled',
          type: HealthLevel.ERROR,
        },
      );
    }
  },
);

// Computed class for title text
const titleTxtClass = computed(() => {
  if (!directToInvoiceEnabled.value) {
    return 'disabled-title'; // Class for disabled state
  }
  return directToInvoicing.value ? 'true-title' : 'false-title'; // Classes for true/false states
});

/**
 * Determines if service type and rate type selection is available. False if job
 * is preallocated or later.
 */
const rateFormDisabled: ComputedRef<boolean> = computed(() => {
  return props.jobDetails.workStatus >= WorkStatus.PREALLOCATED;
});

const serviceSelectionDisabled: ComputedRef<boolean> = computed(() => {
  return (
    rateTypeIdController.value === JobRateType.ZONE_TO_ZONE &&
    pudItems.value.filter((p) => p.legTypeFlag === 'P').length >= 2
  );
});

/**
 * Modelled to rate type selection in the template. Returns the selected rate
 * type id, and when setting it, updates the rate specific information
 */
const rateTypeIdController: WritableComputedRef<JobRateType | null> = computed({
  get(): number | null {
    return selectedRateTypeId.value;
  },
  set(rateTypeId: number | null): void {
    setRateSpecificInformation(accountingDetails.value, rateTypeId);

    // Reset the serviceTypeId if we're switching to zone to zone rate
    if (
      rateTypeId === JobRateType.ZONE_TO_ZONE &&
      selectedRateTypeId.value !== JobRateType.ZONE_TO_ZONE
    ) {
      selectedServiceTypeId.value = 0;
    }
    selectedRateTypeId.value = rateTypeId;

    // Reset the selectedServiceTypeId if the rateTypeId is changed, resulting
    // in no rateToApply able to found
    nextTick(() => {
      if (rateToApply.value === null) {
        selectedServiceTypeId.value = 0;
      }
      refreshComputedAccountingDetails();
    });
  },
});

/**
 * Updates the computedAccountingDetails when the selectedServiceTypeId changes
 */
watch(selectedServiceTypeId, (newValue, oldValue) => {
  // Reset the chargeable distance if the service type id changes (distance rate
  // only)
  if (!!newValue && !!oldValue && newValue !== oldValue) {
    resetChargeableDistanceIfRequired(accountingDetails.value);
  }
  refreshComputedAccountingDetails();
});

/**
 * Returns the rate to apply based on the selectedServiceTypeId and
 * rateTypeIdController. RateToApply uses typescript union types to model for
 * type safety based on the rateTypeId (it would be awesome to implement this
 * elsewhere in the future)
 */
const rateToApply: ComputedRef<RateToApply | null> = computed(() => {
  if (!selectedServiceTypeId.value || !rateTypeIdController.value) {
    return null;
  }
  if (rateTypeIdController.value === 6) {
    // If trip/Quoted rate, then we don't need to look at the rate tables
    const tripRate = new TripRate(
      tripRateInformation.value?.clientRate
        ? tripRateInformation.value.clientRate
        : 0,
    );
    const rateTableItem = new RateTableItems();
    rateTableItem.rateTypeId = rateTypeIdController.value;
    rateTableItem.serviceTypeId = selectedServiceTypeId.value;
    rateTableItem.fuelSurcharge = tripRateInformation.value.clientFuelSurcharge;
    rateTableItem.rateTypeObject = tripRate;
    return {
      rateTypeId: 6,
      rateTypeName: 'Quoted Rate',
      serviceTypeName: rateTableItem.serviceShortName,
      rateTypeObject: rateTableItem.rateTypeObject,
      rateTableItem,
    };
  }

  // Find rate to apply based on selectedServiceTypeId and rateTypeIdController
  let toApply: RateTableItems | null = props.serviceRates.rateToApplyToJob(
    selectedServiceTypeId.value,
    rateTypeIdController.value,
  );
  if (!toApply) {
    return null;
  }
  toApply = Object.assign(new RateTableItems(), toApply);
  switch (toApply.rateTypeId) {
    case JobRateType.TIME: // TIME
      return {
        rateTypeId: 1,
        rateTypeName: 'Hourly Rate',
        serviceTypeName: toApply.serviceShortName,
        rateTypeObject: toApply.rateTypeObject as TimeRateType,
        rateTableItem: toApply,
      };
    case JobRateType.ZONE: // ZONE
      return {
        rateTypeId: 2,
        rateTypeName: 'Zone Rate',
        serviceTypeName: toApply.serviceShortName,
        rateTypeObject: toApply.rateTypeObject as ZoneRateType[],
        rateTableItem: toApply,
      };
    case JobRateType.DISTANCE: // DISTANCE
      // setDistanceRateInformation();
      return {
        rateTypeId: 3,
        rateTypeName: 'Distance Rate',
        serviceTypeName: toApply.serviceShortName,
        rateTypeObject: toApply.rateTypeObject as DistanceRateType,
        rateTableItem: toApply,
      };
    case JobRateType.POINT_TO_POINT: // P2P
      return {
        rateTypeId: 4,
        rateTypeName: 'Point to Point Rate',
        serviceTypeName: toApply.serviceShortName,
        rateTypeObject: toApply.rateTypeObject as PointToPointRateType[],
        rateTableItem: toApply,
      };
    case JobRateType.UNIT: // UNIT
      return {
        rateTypeId: 5,
        rateTypeName: 'Unit Rate',
        serviceTypeName: toApply.serviceShortName,
        rateTypeObject: toApply.rateTypeObject as UnitRate[],
        rateTableItem: toApply,
      };
    case JobRateType.ZONE_TO_ZONE: // UNIT
      return {
        rateTypeId: 7,
        rateTypeName: 'Zone to Zone Rate',
        serviceTypeName: toApply.serviceShortName,
        rateTypeObject: toApply.rateTypeObject as ZoneToZoneRateType[],
        rateTableItem: toApply,
      };
    default:
      return null;
  }
});

/**
 * Handles emit from JobZoneRateDetails component to update the zone rate information
 */
function handleUpdateZoneRateInformation(zoneSummaries: ZoneRatePudSummary[]) {
  zoneRateInformation.value = zoneSummaries;
  refreshComputedAccountingDetails();
}
/**
 * Handles emit from JobUnitRateDetails component to update the unit rate information
 */
function handleUpdateUnitRateInformation(unitSummary: UnitRatePudSummary[]) {
  unitRateInformation.value = unitSummary;
  refreshComputedAccountingDetails();
}

/**
 * Resets the adjusted value for chargeableClientDistance. Called when the
 * service type id changes
 * @param accounting The accounting details to reset the distance in
 */
function resetChargeableDistanceIfRequired(accounting: JobAccountingDetails) {
  // Reset the edited distance rate if it exists
  if (
    accounting.additionalData?.distanceRate?.chargeableClientDistance.edited
  ) {
    accounting.additionalData.distanceRate.chargeableClientDistance.edited =
      undefined;
  }
}

/**
 * Called after values are changed that require us to update the
 * computedAccountingDetails value, such that it can be passed into the pricing
 * summary component to display the current totals based on the selections
 */
function refreshComputedAccountingDetails() {
  console.time('computedAccountingDetails'); // Start timing
  try {
    // If dialog is closed, return null for performance
    if (!dialogController.value) {
      console.timeEnd('computedAccountingDetails'); // End timing
      computedAccountingDetails.value = null;
      return;
    }

    if (!rateToApply.value) {
      console.timeEnd('computedAccountingDetails'); // End timing
      computedAccountingDetails.value = null;
      return;
    }

    // Make a copy of the accounting details to avoid modifying the original,
    // and update it with the current rate table item
    const accounting: JobAccountingDetails = initialiseJobAccountingDetails(
      accountingDetails.value,
    );

    updateRateDetailsInPuds(
      pudItems.value,
      rateToApply.value.rateTypeId,
      zoneRateInformation.value,
      unitRateInformation.value,
    );
    updateAccountingDetails(accounting, rateToApply.value.rateTableItem);

    // Check if an outside metro suburb was hit, and determine the outside metro
    // surcharge
    const outsideMetroAreaHit: boolean = outsideMetroChargeApplies(
      useCompanyDetailsStore().insideMetroSuburbs,
      pudItems.value,
    );
    const clientOutsideMetroRate: number =
      accounting.clientRates[0]?.outsideMetroRate &&
      outsideMetroAreaHit &&
      accounting.clientRates[0].rate.rateTypeId === 1
        ? accounting.clientRates[0].outsideMetroRate
        : 0;
    accounting.clientRates[0].outsideMetroRate = clientOutsideMetroRate;

    // Compute the totals based on the updated accounting details
    const updatedTotals: JobAccountingDetails | null = estimateClientTotals({
      jobDetails: props.jobDetails,
      pudItems: pudItems.value,
      accounting,
      additionalChargeTypeList: useRootStore().additionalChargeTypeList ?? [],
      clientOutsideMetroRate,
      commonAddresses: props.commonAddresses,
      chargeableDistance:
        accountingDetails.value.additionalData?.distanceRate
          ?.chargeableClientDistance?.edited,
      isClientPortal: sessionManager.isClientPortal(),
      divisionDetails: useCompanyDetailsStore().divisionDetails,
    });
    computedAccountingDetails.value = updatedTotals;

    // Set the edited distance in the accounting details
    if (
      accountingDetails.value.additionalData?.distanceRate &&
      updatedTotals?.additionalData?.distanceRate
    ) {
      accountingDetails.value.additionalData.distanceRate.chargeableClientDistance.edited =
        updatedTotals.additionalData.distanceRate.chargeableClientDistance.edited;
    }
    console.timeEnd('computedAccountingDetails'); // End timing
  } catch (error) {
    logConsoleError('Error in computedAccountingDetails', error);
    console.timeEnd('computedAccountingDetails'); // End timing
    computedAccountingDetails.value = null;
  }
}

/**
 * Handles emit from AddAdditionalCharge component to add a list of charges to the
 * job. Adds the full objects and the ids to the accounting details, then
 * recalculates the toll admin and handling charge if the new charge is a toll
 * charge.
 * @param items The new charges to add to the job
 *
 */
function addAdditionalChargesToJob(items: AdditionalChargeItem[]) {
  if (!items || items.length === 0) {
    logConsoleError('No additional charges provided to add to the job.');
    return;
  }
  // Iterate over the items and add them to the charge list
  items.forEach((item: AdditionalChargeItem) => {
    // Check if the charge already exists in the list.
    const foundExistingCharge =
      accountingDetails.value.additionalCharges.chargeList.find(
        (x) => x._id === item._id,
      );

    // If it does, increment the quantity. If not, add it to the list
    if (foundExistingCharge) {
      foundExistingCharge.quantity++;
    } else {
      accountingDetails.value.additionalCharges.chargeList.push(item);
      accountingDetails.value.additionalCharges.chargeIdList.push(item._id!);
    }
  });
  // Update the toll admin and handling charge in the job
  updateTollAndAdminCharge(accountingDetails.value.additionalCharges);
  refreshComputedAccountingDetails();
}

/**
 * Handles emit from JobBookingPricingSummary component to increment, decrement
 * or remove an additional charge that is currently in the accountingDetails
 * charge list.
 * @param payload - Contains the update type (INCREMENT, DECREMENT or REMOVE)
 * and the id of the charge to update
 */
function updateExistingAdditionalCharge(payload: UpdateAdditionalChargeItem) {
  const isUpdateSuccess = applyAdditionalChargeUpdateToAccounting(
    payload,
    accountingDetails.value,
  );
  if (!isUpdateSuccess) {
    return;
  }
  // Update the toll admin and handling charge in the job
  updateTollAndAdminCharge(accountingDetails.value.additionalCharges);
  refreshComputedAccountingDetails();
}

/**
 * Updates the toll admin and handling charge in the job.
 * @param additionalCharges - The additional charges object from accounting details
 */
function updateTollAndAdminCharge(additionalCharges: AdditionalChargeList) {
  const tollAndHandlingItem = rootStore.additionalChargeItemList.find(
    (x: AdditionalChargeItem) =>
      x._id === useRootStore().tollAdminAndHandlingId,
  );

  tollAdminAndServicesFeeHandler(
    additionalCharges,
    tollAndHandlingItem,
    useRootStore().tollAdminAndHandlingId,
    rootStore.tollChargeTypeId,
  );
}

/**
 * Populate zoneRateInformation with values from PudItems.rateDetails.
 * This is passed into JobZoneRateDetails as a prop and used as a working copy
 * which is either saved (and set to job) or discarded
 */
function setZoneRateInformation() {
  zoneRateInformation.value = pudItems.value.map((pudItem: PUDItem) =>
    mapPudItemToZoneRateSummary(pudItem, true),
  );
  refreshComputedAccountingDetails();
}
/**
 * Populate unitRateInformation with values from PudItems.rateDetails.
 * This is passed into JobUnitRateDetails as a prop and used as a working copy
 * which is either saved (and set to job) or discarded
 */
function setUnitRateInformation() {
  unitRateInformation.value = pudItems.value.map((pudItem: PUDItem) =>
    mapPudItemToUnitRateSummary(pudItem, true),
  );
  refreshComputedAccountingDetails();
}

const availableRateTypes: ComputedRef<ServiceTypeRateButton[]> = computed(
  () => {
    // Helper to strip " rate" suffix from longName
    const stripRateSuffix = (longName: string) =>
      longName.replace(/\s+rate$/i, '');

    // Map availableRateTypeIds to full rate objects with extra props
    const mappedRates: ServiceTypeRateButton[] = availableRateTypeIds.value.map(
      (rateTypeId) => {
        const rateType = serviceTypeRates.find(
          (x) => x.rateTypeId === rateTypeId,
        )!;

        let isLoading = false;
        let isDisabled = false;
        let tooltipText = '';

        if (rateTypeId === JobRateType.ZONE_TO_ZONE) {
          isLoading = isLoadingZoneToZoneRates.value;
          const pickupCount = pudItems.value.filter(
            (p) => p.legTypeFlag === 'P',
          ).length;

          isDisabled = pudItems.value.length < 2 || pickupCount >= 2;
          if (isDisabled) {
            if (pudItems.value.length < 2) {
              tooltipText =
                'Please add another stop to select Zone to Zone rates.';
            } else if (pickupCount >= 2) {
              tooltipText = 'Zone to Zone jobs cannot have more than 1 pickup.';
            }
          }
        }

        return {
          ...rateType,
          buttonText: stripRateSuffix(rateType.longName),
          isLoading,
          isDisabled,
          tooltipText,
        };
      },
    );

    // Get the display order from the store
    const displayOrder =
      useCompanyDetailsStore().divisionCustomConfig?.operations
        ?.rateTypeDisplayOrder;

    // Sort mappedRates based on displayOrder
    if (
      displayOrder &&
      Array.isArray(displayOrder) &&
      displayOrder.length > 0
    ) {
      return mappedRates.slice().sort((a, b) => {
        const aIdx = displayOrder.indexOf(a.rateTypeId);
        const bIdx = displayOrder.indexOf(b.rateTypeId);
        if (aIdx === -1 && bIdx === -1) {
          return 0;
        }
        if (aIdx === -1) {
          return 1;
        }
        if (bIdx === -1) {
          return -1;
        }
        return aIdx - bIdx;
      });
    }

    return mappedRates;
  },
);

/**
 * Controls visibility of ContentDialog. Gets and sets prop isDialogOpen.
 */
const dialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.isDialogOpen;
  },
  set(value: boolean): void {
    emit('update:isDialogOpen', value);
  },
});

/**
 * Watches for changes in the dialogController value. If the dialog is opened,
 * copy the current recurrence details from props to the working copy. If the
 * dialog is closed, reset editedRecurrenceDetails.
 */
watch(dialogController, (newValue) => {
  if (newValue) {
    // Set the initial values of the form fields
    setWorkingVariables();
  } else {
    // Reset the form fields
    resetWorkingVariables();
  }
});

/**
 * Called when the dialog is opened. Sets values from the props and job to local
 * working copies for editing.
 */
async function setWorkingVariables() {
  // Get a list of unique rate type ids
  const baseRateTypeIds = Array.from(
    new Set(props.serviceRates.rateTableItems.map((x) => x.rateTypeId)),
  );
  // Set availableRateTypeIds
  availableRateTypeIds.value = !sessionManager.isClientPortal()
    ? [...baseRateTypeIds, JobRateType.TRIP]
    : baseRateTypeIds.filter((id) => id !== JobRateType.TRIP); // Remove tripRateId client portal users
  if (!availableRateTypeIds.value.length) {
    showAppNotification('No rate types available for this client');
    return;
  }

  pudItems.value = props.jobDetails.pudItems.map((pud) =>
    initialisePudItem(pud),
  );
  accountingDetails.value = initialiseJobAccountingDetails(
    props.jobDetails.accounting,
  );

  const store = useCompanyDetailsStore();
  // Init default values for rate type and service type from job if they are set
  // If rateTypeId is not set in job, use the first result from the list
  rateTypeIdController.value = props.jobDetails.serviceTypeObject.rateTypeId
    ? props.jobDetails.serviceTypeObject.rateTypeId
    : store.divisionCustomConfig?.operations?.defaultRateTypeId ??
      availableRateTypeIds.value[0];
  selectedServiceTypeId.value = props.jobDetails.serviceTypeId
    ? props.jobDetails.serviceTypeId
    : 0;

  setDefaultFuelSelections(props.jobDetails);

  // Set the rate specific information based on the rateTypeId (unit, zone, trip)
  setRateSpecificInformation(
    props.jobDetails.accounting,
    rateTypeIdController.value,
  );
  zoneToZoneRatesAvailable.value = props.serviceRates.rateTableItems.some(
    (x) => x.rateTypeId === JobRateType.ZONE_TO_ZONE,
  );
  setRateSummaries();

  refreshComputedAccountingDetails();
}

/**
 * Called when the dialog opens. Sets the values for currently selected/applies
 * fuel surcharge. If no fuel surcharge is selected, it tries to find the most
 * suitable one based on the job details and available fuel surcharges.
 */
function setDefaultFuelSelections(jobDetails: JobDetails) {
  // Set the selected fuel bracket (if one is selected)
  selectedFuelSurchargeId.value =
    jobDetails.accounting?.additionalCharges?.clientFuelSurcharge?.id ?? '';
  selectedFuelBracketId.value =
    jobDetails.accounting?.additionalCharges?.clientFuelSurcharge
      ?.appliedRateBracketId ?? '';

  // If no fuel surcharge has been manually set, try to find the most suitable
  // one and set it to selectedFuelSurchargeId
  if (!selectedFuelBracketId.value) {
    const clientFuelSurcharge = returnMostSuitableFuelSurcharge({
      jobDetails,
      fuelSurchargeRates: props.fuelSurcharges,
    });
    selectedFuelSurchargeId.value = clientFuelSurcharge?.id ?? '';
  }
}

/**
 * This is called from the FuelSurchargeSelection component when the
 * user selects a fuel bracket. It updates the selectedFuelBracketId and
 * selectedFuelSurchargeId based on the selected bracket.
 * @param bracketId - The id of the selected fuel bracket
 */
function handleUpdatedFuelSelection(bracketId: string) {
  if (!bracketId) {
    setDefaultFuelSelections(props.jobDetails);
    nextTick(() => {
      fuelSurchargeSelectionKey.value++;
      refreshComputedAccountingDetails();
    });
    return;
  }
  const foundFuelSurcharge = props.fuelSurcharges.find((fuel) =>
    fuel.rateBrackets.some((bracket) => bracket.bracketId === bracketId),
  );

  if (!foundFuelSurcharge?.id) {
    logConsoleError(
      `No fuel surcharge found for bracket id: ${bracketId}. Please check the fuel surcharges.`,
    );
    return;
  }
  // If a fuel surcharge is found, set the selected fuel surcharge id
  // based on the bracket id
  selectedFuelSurchargeId.value = foundFuelSurcharge.id;
  selectedFuelBracketId.value = bracketId;
  refreshComputedAccountingDetails();
}

/**
 * Sets the rate specific information based on the rateTypeId. This is used for
 * specific rate types (specifically zone, unit and trip/Quoted rates), where we have
 * some other information that needs to be updated or displayed in the template.
 * @param accounting The accounting details from the job
 * @param rateTypeId The rate type id to set the information for
 */
function setRateSpecificInformation(
  accounting: JobAccountingDetails,
  rateTypeId: number | null,
) {
  if (rateTypeId === 2) {
    // If ZONE rate then init zoneRateInformation with values from Pud Item
    setZoneRateInformation();
    // } else if (rateTypeId === 3) {
    //   // If DISTANCE rate then init distanceRateInformation with values from accounting
    //   setDistanceRateInformation();
  } else if (rateTypeId === 5) {
    // If UNIT rate then init unitRateInformation with values from Pud Item
    setUnitRateInformation();
  } else if (rateTypeId === 6) {
    tripRateInformation.value = returnInitialTripRateInfo(accounting);
  } else {
    zoneRateInformation.value = null;
    unitRateInformation.value = null;
    tripRateInformation.value = new TripRateInformation();
  }
}

/**
 * Constructs a summary of rate information to be displayed in a child
 * component. Sets result to rateSummaries ref. Called when dialog is opened.
 */
function setRateSummaries() {
  const timeSummaryList: TimeRateSummary[] = [];
  const distanceSummaryList: DistanceRateSummary[] = [];
  const zoneSummaryList: ZoneRateSummary[] = [];
  const tripSummaryList: TripRateSummary[] = [];

  // Iterate over the rateTypeIds so that we can create a group for each rate
  // type
  availableRateTypeIds.value.forEach((rateTypeId) => {
    // if trip/Quoted rate push all service types to trip/Quoted rate table
    if (rateTypeId === JobRateType.TRIP) {
      const serviceTypes = useCompanyDetailsStore().getServiceTypesList;
      tripSummaryList.push(
        ...serviceTypes.map((serviceType) => ({
          serviceTypeId: serviceType.serviceTypeId,
          shortName: serviceType.shortServiceTypeName,
          longName: serviceType.longServiceTypeName,
        })),
      );
    }
    // Find all rate table items that have the current rateTypeId
    const rateTableItems = props.serviceRates.rateTableItems.filter(
      (x) => x.rateTypeId === rateTypeId,
    );
    rateTableItems.forEach((rateTableItem) => {
      if (!rateTableItem.serviceTypeId) {
        return;
      }
      rateTableItem.serviceTypeId = rateTableItem?.serviceTypeId ?? 0;

      // Find the associated rate variation percentage for this rateTableItem
      const rateVariation = rateTableItem.getClientVariation(
        props.rateVariations,
      );

      switch (rateTypeId) {
        case JobRateType.TIME: // TIME
          if (
            isTimeRateTypeObject(
              rateTableItem.rateTypeId,
              rateTableItem.rateTypeObject,
            )
          ) {
            timeSummaryList.push({
              serviceTypeId: rateTableItem.serviceTypeId!,
              shortName: rateTableItem.serviceShortName,
              longName: rateTableItem.serviceLongName,
              rate: timeRateDescription({
                rate: rateTableItem.rateTypeObject.rate,
                multiplier: rateTableItem.rateTypeObject.rateMultiplier,
                variancePct: rateVariation,
              }),
              minimumCharge: timeRateMinCharge(rateTableItem.rateTypeObject),
              chargeIncrement: timeRateChargeIncrement(
                rateTableItem.rateTypeObject,
              ),
              additionalTimes: getLegNamesFromTimeRate(
                rateTableItem.rateTypeObject,
                props.jobDetails.legDurations,
              ),
              fuelLevy: isFuelSurchargeApplicable(
                rateTableItem.rateTypeObject.appliedFuelSurchargeId,
                null,
              )
                ? 'Yes'
                : 'No',
            });
          }
          break;
        case JobRateType.ZONE: // ZONE
          zoneSummaryList.push({
            serviceTypeId: rateTableItem.serviceTypeId,
            shortName: rateTableItem.serviceShortName,
            longName: rateTableItem.serviceLongName,
            rate: 'Various',
            zoneCount: (rateTableItem.rateTypeObject as ZoneRateType[]).length,
          });
          break;
        case JobRateType.DISTANCE: // DISTANCE
          if (
            isDistanceRateTypeObject(
              rateTableItem.rateTypeId,
              rateTableItem.rateTypeObject,
            )
          ) {
            const baseFreight = rateTableItem.rateTypeObject.baseFreightCharge
              ? addPercentageTo(
                  rateTableItem.rateTypeObject.baseFreightCharge,
                  rateVariation,
                )
              : '$0';
            const shortName = rateTableItem.serviceShortName;
            const longName = rateTableItem.serviceLongName;
            distanceSummaryList.push({
              serviceTypeId: rateTableItem.serviceTypeId,
              shortName,
              longName,
              serviceName: `${shortName} - ${longName}`,
              calculation: `${returnReadableChargeBasisName(
                rateTableItem.rateTypeObject.chargeBasis,
              )} (${returnReadableRateBracketTypeName(
                rateTableItem.rateTypeObject.rateBracketType,
              )})`,
              baseFreight: `$${baseFreight} (Min. ${returnMinimumChargeDescription(
                rateTableItem.rateTypeObject,
              )})`,
              chargeIncrement: returnChargeIncrementDescription(
                rateTableItem.rateTypeObject,
              ),
              additionalTimes: `${
                returnStartAndReturnLegsLongNameById(
                  rateTableItem.rateTypeObject.firstLegTypeId,
                ) || 'N/A'
              } / ${
                returnStartAndReturnLegsLongNameById(
                  rateTableItem.rateTypeObject.lastLegTypeId,
                ) || 'N/A'
              }`,
              rate: returnRangeRateListSummary(
                rateTableItem.rateTypeObject.rates,
                rateVariation,
              ),
              fuelLevy: isFuelSurchargeApplicable(
                rateTableItem.rateTypeObject.appliedFuelSurchargeId,
                null,
              )
                ? 'Yes'
                : 'No',
            });
          }
          break;
      }
    });
  });
  if (timeSummaryList.length) {
    rateSummaries.value.push({
      rateTypeId: JobRateType.TIME,
      rateTypeName: 'Time',
      rateSummaries: timeSummaryList,
    });
  }
  if (zoneSummaryList.length) {
    rateSummaries.value.push({
      rateTypeId: JobRateType.ZONE,
      rateTypeName: 'Zone',
      rateSummaries: zoneSummaryList,
    });
  }
  if (distanceSummaryList.length) {
    rateSummaries.value.push({
      rateTypeId: JobRateType.DISTANCE,
      rateTypeName: 'Distance',
      rateSummaries: distanceSummaryList,
    });
  }
  if (availableRateTypeIds.value.includes(JobRateType.UNIT)) {
    rateSummaries.value.push({
      rateTypeId: JobRateType.UNIT,
      rateTypeName: 'Unit',
      rateSummaries: timeSummaryList,
    });
  }
  if (availableRateTypeIds.value.includes(JobRateType.TRIP)) {
    rateSummaries.value.push({
      rateTypeId: JobRateType.TRIP,
      rateTypeName: 'Quoted',
      rateSummaries: tripSummaryList,
    });
  }
  if (availableRateTypeIds.value.includes(JobRateType.ZONE_TO_ZONE)) {
    setZoneToZoneRateSummaries();
  }
}

/**
 * Sets the zone to zone rate summaries. This is a special case as it requires
 * an API request to fetch the rates. Requests the zones based on the current
 * pudItems, constructs the response into a format that can be displayed in the
 * template, then adds entry to rateSummaries.
 */
async function setZoneToZoneRateSummaries() {
  // Do nothing if zone to zone rates are not available, or if there are less
  // than 2 stops, as a single Zone to Zone rate requires at least 2 stops. Also
  // do nothing if there are more than 1 pickups, as ZONE TO ZONE rate should only
  // have 1 pickup pud
  if (
    !zoneToZoneRatesAvailable.value ||
    pudItems.value.length < 2 ||
    pudItems.value.filter((p) => p.legTypeFlag === 'P').length >= 2
  ) {
    return;
  }
  try {
    isLoadingZoneToZoneRates.value = true;
    const zzRateTableItems = await requestZoneToZoneRatesForPudItems({
      type: RateEntityType.CLIENT,
      entityId: props.jobDetails.client.id,
      pudItems: pudItems.value,
      serviceTypeId: null,
    });
    if (!zzRateTableItems) {
      throw new Error('Zone to Zone rate response was empty');
    }
    // Add or replace the zone to zone rate table items in the service rates
    addOrReplaceRateTableItems(
      props.serviceRates.rateTableItems,
      zzRateTableItems,
    );
    // Construct the zone to zone rate summaries
    const zoneToZoneSummaries: ZoneToZoneRateSummary[] = zzRateTableItems
      .sort(
        (a, b) =>
          (a.rateTypeObject as ZoneToZoneRateType[])[0].rate -
          (b.rateTypeObject as ZoneToZoneRateType[])[0].rate,
      )
      .flatMap((rateTableItem) => {
        return (rateTableItem.rateTypeObject as ZoneToZoneRateType[]).map(
          (rateType, rateTypeIndex) => {
            // Find the associated rate variation percentage for this rateTableItem
            const rateVariation = rateTableItem.getClientVariation(
              props.rateVariations,
            );

            // Get dollar amount with any variations applied
            const zoneRateToApply =
              rateType.rate + getPercentageOf(rateType.rate, rateVariation);

            const zoneName = returnZoneToZoneName(rateType);
            const formattedRate = `$${DisplayCurrencyValue(zoneRateToApply)}`;
            const isFuelApplied = isFuelSurchargeApplicable(
              rateType.appliedFuelSurchargeId,
              null,
            );
            // Construct service name. Only display for first item for this
            // service type
            const serviceName =
              rateTypeIndex === 0
                ? `${rateTableItem.serviceShortName} - ${rateTableItem.serviceLongName}`
                : '';
            return {
              serviceTypeId: rateTableItem.serviceTypeId!,
              serviceName: serviceName,
              zoneApplied: zoneName,
              rate: formattedRate,
              fuelLevy: isFuelApplied ? 'Yes' : 'No',
              allowedLoadTime: returnCorrectDuration(
                rateType.demurrage.graceTimeInMilliseconds,
              ),
              fuelOnDemurrage: rateType.demurrage.demurrageFuelSurchargeApplies
                ? 'Yes'
                : 'No',
            };
          },
        );
      });
    rateSummaries.value.push({
      rateTypeId: JobRateType.ZONE_TO_ZONE,
      rateTypeName: 'Zone to Zone',
      rateSummaries: zoneToZoneSummaries,
    });
    isLoadingZoneToZoneRates.value = false;
  } catch (error) {
    logConsoleError('Error setting zone to zone rate summaries', error);
    isLoadingZoneToZoneRates.value = false;
  }
}

/**
 * Resets the working variables to their initial state. Called when the dialog
 * is closed.
 */
function resetWorkingVariables() {
  rateSummaries.value = [];
  availableRateTypeIds.value = [];
  selectedRateTypeId.value = null;
  selectedServiceTypeId.value = props.jobDetails.serviceTypeId ?? 0;
  serviceTypeFilter.value = '';
  zoneRateInformation.value = null;
  unitRateInformation.value = null;
  tripRateInformation.value = new TripRateInformation();
  computedAccountingDetails.value = null;
  selectedFuelSurchargeId.value = '';
  selectedFuelBracketId.value = '';
}

/**
 * Validates the selections made in the dialog. Returns a string with an error
 * message if the selections are invalid, or true if the selections are valid.
 * @returns A string with an error message if the selections are invalid, or true
 */
function validateSelections(): string | true {
  try {
    if (
      !selectedServiceTypeId.value ||
      !rateTypeIdController.value ||
      !rateToApply.value
    ) {
      return 'Please select a valid service and rate type.';
    }
    if (!pricingDialogForm.value?.validate()) {
      return FORM_VALIDATION_FAILED_MESSAGE;
    }
    const isZoneRate = rateTypeIdController.value === 2;
    const isUnitRate = rateTypeIdController.value === 5;
    if (isZoneRate || isUnitRate) {
      if (pudItems.value.some((pud) => !pud.rateDetails.zoneReference)) {
        return 'One or more stops is missing zone information.';
      }
    }
  } catch (error) {
    logConsoleError('Error validating selections', error);
    if (error instanceof Error && error.message) {
      return error.message;
    }
    return 'An unknown error occurred during validation.';
  }

  return true;
}

/**
 * Validates selections, applies changes to the jobDetails and emit the changes
 * to the parent. This function is called when the confirm button is clicked.
 *
 */
function applyChanges() {
  const formIsValid = validateSelections();
  if (formIsValid !== true) {
    showAppNotification(formIsValid);
    return;
  }
  // If the rate type is a trip/Quoted rate, then we need to add the trip/Quoted rate details
  // to the job using the tripRateInformation object
  if (rateToApply.value?.rateTypeId === JobRateType.TRIP) {
    const addedRateToJob = addTripRateDetailsToJob(
      props.jobDetails,
      selectedServiceTypeId.value!,
      accountingDetails.value,
      tripRateInformation.value,
      rateToApply.value?.rateTableItem ?? null,
      props.serviceRates,
    );
    if (addedRateToJob !== true) {
      showAppNotification(addedRateToJob);
      return;
    }
  }
  // Update the pudItems with the new rate information for zone and unit rate types
  updateRateDetailsInPuds(
    pudItems.value,
    rateTypeIdController.value!,
    zoneRateInformation.value,
    unitRateInformation.value,
  );
  updateAccountingDetails(
    accountingDetails.value,
    rateToApply.value!.rateTableItem,
  );

  // Set the latest FinishedJobData to the accounting details
  if (computedAccountingDetails.value) {
    accountingDetails.value.finishedJobData =
      computedAccountingDetails.value.finishedJobData;
  }
  if (!directToInvoiceEnabled.value) {
    directToInvoicing.value = false;
  }
  // Apply changes to the jobDetails
  // Emit the changes to the parent
  emit('applyChanges', {
    serviceTypeId: selectedServiceTypeId.value!,
    rateTypeId: rateTypeIdController.value!,
    accounting: accountingDetails.value,
    pudItems: pudItems.value,
    directToInvoicing: directToInvoicing.value,
  });
  dialogController.value = false;
}

/**
 * Apples properties from the rate table item to the accounting details object.
 * Called when the confirm button is clicked, or when the rate type or service type is changed.
 * @param accounting The accounting details object to update
 * @param rateTableItem The rate table item to apply
 */
function updateAccountingDetails(
  accounting: JobAccountingDetails,
  rateTableItem: RateTableItems,
) {
  // Create a new JobPrimaryRate object if the clientRates array is empty
  const clientRate: JobPrimaryRate = accounting.clientRates?.length
    ? accounting.clientRates[0]
    : new JobPrimaryRate();
  clientRate.rate = rateTableItem;
  clientRate.rateTableName = props.serviceRates.name;
  clientRate.validFromDate = props.serviceRates.validFromDate;
  clientRate.validToDate = props.serviceRates.validToDate;
  clientRate.outsideMetroRate = props.serviceRates.outsideMetroRate;

  accounting.clientRates = [clientRate];

  // If the rateTableItem is a distance rate type and undefined (after resetting
  // it in resetChargeableDistanceIfRequired), then we reset it to the original
  // unedited value.
  if (
    isDistanceRateTypeObject(
      rateTableItem.rateTypeId,
      rateTableItem.rateTypeObject,
    ) &&
    accounting.additionalData?.distanceRate?.chargeableClientDistance &&
    !accounting.additionalData?.distanceRate.chargeableClientDistance.edited
  ) {
    accounting.additionalData.distanceRate.chargeableClientDistance.edited =
      returnOriginalDistanceForChargeBasis(
        accounting.additionalData?.distanceRate.chargeableClientDistance,
        rateTableItem.rateTypeObject.chargeBasis,
      );
  }

  // Get the editedTravelDistance for distance rate job, which we will use to
  // determine the selected fuel bracket
  const editedTravelDistance: number | undefined =
    accounting.additionalData?.distanceRate?.chargeableClientDistance.edited;

  // Handle fuel surcharge logic
  const updatedFuelSurcharge = returnFuelSurchargeForAccounting({
    jobDetails: props.jobDetails,
    isFuelApplicable: rateTableItem.isClientFuelApplied,
    fuelSurcharges: props.fuelSurcharges,
    selectedFuelBracketId: selectedFuelBracketId.value,
    serviceTypeId: rateTableItem.serviceTypeId,
    rateTypeId: rateTableItem.rateTypeId,
    userEnteredDistance: editedTravelDistance,
  });
  accounting.additionalCharges.clientFuelSurcharge = updatedFuelSurcharge;

  const updatedId = accounting.additionalCharges.clientFuelSurcharge?.id ?? '';
  selectedFuelSurchargeId.value = updatedId;
  fuelSurchargeSelectionKey.value++;

  // Update rate variation
  accounting.clientServiceRateVariations = rateTableItem.getRateVariation(
    props.rateVariations,
  );

  // Update travel delays from the computed version
  if (
    computedAccountingDetails.value?.finishedJobData?.clientTravelDelayBreakdown
      ?.length
  ) {
    accounting.finishedJobData.clientTravelDelayBreakdown =
      computedAccountingDetails.value.finishedJobData.clientTravelDelayBreakdown;
  }
}

// Trigger app notification. Defaults to ERROR type message, but type can be
// provided to produce other types. Includes componentTitle as a title for the
// notification.
function showAppNotification(text: string, type?: HealthLevel): void {
  showNotification(text, {
    type,
    title: 'Job Booking - Pricing',
  });
}

/**
 * Handles the enter key event on the rate type selection input. If the key is
 * not the enter key, the function returns early. If the key is the enter key,
 * the function prevents the default action and sets the selected service type
 * id to the first service type id in the rate summary.
 */
function handleEnterEvent(e: KeyboardEvent) {
  if (e.key !== 'Enter') {
    return;
  }
  e.preventDefault();
  const foundSummary = rateSummaries.value.find(
    (x) => x.rateTypeId === rateTypeIdController.value,
  );
  if (
    foundSummary?.rateSummaries?.length &&
    [1, 2, 4, 5].includes(foundSummary.rateTypeId)
  ) {
    selectedServiceTypeId.value = foundSummary.rateSummaries[0].serviceTypeId;
  }
}
</script>
<style scoped lang="scss">
.subheader {
  color: var(--bg-light-blue);
  font-size: $font-size-22;
  font-weight: 600;
}

.header-container {
  padding-right: 20px;
  padding-left: 20px;
  margin-top: 12px;
  margin-bottom: 0;
  .add-btn {
    height: 44px;
    width: 44px;
    border-radius: 100px !important;
    background-color: var(--primary-light) !important;
    color: var(--background-color-100) !important;
    font-weight: 700;
    margin-bottom: 0;
    &:hover {
      box-shadow: var(--box-shadow);
      scale: 1.05;
    }
  }
}

.btn-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between; // Ensures buttons are aligned left and right
  align-items: center; // Vertically aligns buttons if they are in a different height
  margin-top: 8px;

  .action-btn {
    border-radius: 10px;
    margin: 8px 16px;
    padding: 20px;

    &.confirm {
      width: 300px;
    }
  }
}

.direct-invoice-container {
  border: 1px solid $translucent;
  background-color: $translucent-bg;
  border-radius: $border-radius-base;
  margin: 12px;

  .title-txt {
    font-weight: bold;
    font-size: $font-size-16;
    padding-left: 12px;
    color: var(--text-color);
  }

  .true-title {
    color: var(--primary); /* Color when directToInvoicing is true */
  }

  .false-title {
    color: var(--text-color); /* Color when directToInvoicing is false */
  }

  .disabled-title {
    color: gray; /* Color when the switch is disabled */
  }

  &:hover {
    box-shadow: $box-shadow-dark;
    background-color: var(--background-color-300);
  }
}
</style>
