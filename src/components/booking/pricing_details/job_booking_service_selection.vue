<template>
  <v-layout row>
    <v-flex md12>
      <v-layout
        v-for="rateSummary of rateSummaries"
        :key="rateSummary.rateTypeId"
      >
        <v-flex md12>
          <v-data-table
            v-if="
              rateSummary.rateTypeId === JobRateType.TIME &&
              selectedRateTypeId === JobRateType.TIME
            "
            :headers="timeHeaders"
            :items="filterTimeRateSummaries(rateSummary.rateSummaries)"
            item-key="id"
            class="gd-dark-theme pricing"
            :class="{ disabled: formDisabled }"
            :search="serviceTypeFilter"
          >
            <template #items="tableProps">
              <tr
                @click="
                  !formDisabled
                    ? emit('input', tableProps.item.serviceTypeId)
                    : null
                "
                :class="{
                  'row-highlight':
                    tableProps.item.serviceTypeId === serviceTypeId,
                  'row-disabled':
                    formDisabled &&
                    tableProps.item.serviceTypeId !== serviceTypeId,
                }"
              >
                <td>
                  {{ tableProps.item.shortName }} -
                  {{ tableProps.item.longName }}
                </td>
                <td>
                  {{ tableProps.item.rate }}
                </td>
                <td>{{ tableProps.item.minimumCharge }}</td>
                <td>{{ tableProps.item.chargeIncrement }}</td>
                <td>{{ tableProps.item.additionalTimes }}</td>
                <td>
                  {{ tableProps.item.fuelLevy }}
                  <v-icon
                    class="selected-icon"
                    color="'orange"
                    v-if="tableProps.item.serviceTypeId === serviceTypeId"
                    >check_box</v-icon
                  >
                </td>
              </tr>
            </template>
            <template v-slot:no-results>
              <v-alert
                :value="true"
                color="error"
                icon="warning"
                class="mt-4 mb-4"
              >
                Your search for "{{ serviceTypeFilter }}" found no results.
              </v-alert>
            </template>
          </v-data-table>
          <v-data-table
            v-if="
              rateSummary.rateTypeId === JobRateType.ZONE &&
              selectedRateTypeId === JobRateType.ZONE
            "
            :headers="zoneHeaders"
            :items="filterTimeRateSummaries(rateSummary.rateSummaries)"
            item-key="id"
            class="gd-dark-theme pricing"
            :class="{ disabled: formDisabled }"
            :search="serviceTypeFilter"
          >
            <template #items="tableProps">
              <tr
                @click="emit('input', tableProps.item.serviceTypeId)"
                :class="{
                  'row-highlight':
                    tableProps.item.serviceTypeId === serviceTypeId,
                  'row-disabled':
                    formDisabled &&
                    tableProps.item.serviceTypeId !== serviceTypeId,
                }"
              >
                <td>
                  {{ tableProps.item.shortName }} -
                  {{ tableProps.item.longName }}
                </td>
                <td>{{ tableProps.item.rate }}</td>
                <td>
                  {{ tableProps.item.zoneCount }}
                  <v-icon
                    class="selected-icon"
                    color="'orange"
                    v-if="tableProps.item.serviceTypeId === serviceTypeId"
                    >check_box</v-icon
                  >
                </td>
              </tr>
            </template>
            <template v-slot:no-results>
              <v-alert :value="true" color="error" icon="warning">
                Your search for "{{ serviceTypeFilter }}" found no results.
              </v-alert>
            </template>
          </v-data-table>
          <v-data-table
            v-if="
              rateSummary.rateTypeId === JobRateType.DISTANCE &&
              selectedRateTypeId === JobRateType.DISTANCE
            "
            :headers="distanceHeaders"
            :items="rateSummary.rateSummaries"
            item-key="id"
            class="gd-dark-theme pricing"
            :class="{ disabled: formDisabled }"
            :search="serviceTypeFilter"
          >
            <template #items="tableProps">
              <tr
                @click="emit('input', tableProps.item.serviceTypeId)"
                :class="{
                  'row-highlight':
                    tableProps.item.serviceTypeId === serviceTypeId,
                  'row-disabled':
                    formDisabled &&
                    tableProps.item.serviceTypeId !== serviceTypeId,
                }"
              >
                <td>{{ tableProps.item.serviceName }}</td>
                <td>{{ tableProps.item.calculation }}</td>
                <td>{{ tableProps.item.baseFreight }}</td>
                <td>{{ tableProps.item.chargeIncrement }}</td>
                <td>{{ tableProps.item.additionalTimes }}</td>
                <td>{{ tableProps.item.rate }}</td>
                <td>{{ tableProps.item.fuelLevy }}</td>
              </tr>
            </template>
            <template v-slot:no-results>
              <v-alert :value="true" color="error" icon="warning">
                Your search for "{{ serviceTypeFilter }}" found no results.
              </v-alert>
            </template>
          </v-data-table>
          <v-data-table
            v-if="
              rateSummary.rateTypeId === JobRateType.UNIT &&
              selectedRateTypeId === JobRateType.UNIT
            "
            :headers="unitHeaders"
            :items="filterTimeRateSummaries(rateSummary.rateSummaries)"
            item-key="id"
            class="gd-dark-theme pricing"
            :class="{ disabled: formDisabled }"
            :search="serviceTypeFilter"
          >
            <template #items="tableProps">
              <tr
                @click="emit('input', tableProps.item.serviceTypeId)"
                :class="{
                  'row-highlight':
                    tableProps.item.serviceTypeId === serviceTypeId,
                  'row-disabled':
                    formDisabled &&
                    tableProps.item.serviceTypeId !== serviceTypeId,
                }"
              >
                <td>
                  {{ tableProps.item.shortName }} -
                  {{ tableProps.item.longName }}
                </td>
                <td>
                  {{ tableProps.item.rateCount }}
                  <v-icon
                    class="selected-icon"
                    color="'orange"
                    v-if="tableProps.item.serviceTypeId === serviceTypeId"
                    >check_box</v-icon
                  >
                </td>
              </tr>
            </template>
            <template v-slot:no-results>
              <v-alert :value="true" color="error" icon="warning">
                Your search for "{{ serviceTypeFilter }}" found no results.
              </v-alert>
            </template>
          </v-data-table>
          <v-data-table
            v-if="
              rateSummary.rateTypeId === JobRateType.ZONE_TO_ZONE &&
              selectedRateTypeId === JobRateType.ZONE_TO_ZONE
            "
            :headers="zoneToZoneHeaders"
            :items="rateSummary.rateSummaries"
            item-key="id"
            class="gd-dark-theme pricing"
            :class="{ disabled: formDisabled }"
            :search="serviceTypeFilter"
            disable-initial-sort
          >
            <template #items="tableProps">
              <tr
                @click="emit('input', tableProps.item.serviceTypeId)"
                :class="{
                  'row-highlight':
                    tableProps.item.serviceTypeId === serviceTypeId,
                  'row-disabled':
                    formDisabled &&
                    tableProps.item.serviceTypeId !== serviceTypeId,
                }"
                class="table-rows"
              >
                <td>
                  {{ tableProps.item.serviceName }}
                </td>
                <td>
                  {{ tableProps.item.zoneApplied }}
                </td>
                <td>
                  {{ tableProps.item.rate }}
                </td>
                <td>
                  {{ tableProps.item.fuelLevy }}
                </td>
                <td>
                  {{ tableProps.item.allowedLoadTime }}
                </td>
                <td>
                  {{ tableProps.item.fuelOnDemurrage }}
                </td>
                <td>
                  <v-icon
                    class="selected-icon"
                    v-if="tableProps.item.serviceTypeId === serviceTypeId"
                    >check_box</v-icon
                  >
                </td>
              </tr>
            </template>
            <template v-slot:no-results>
              <v-alert :value="true" color="error" icon="warning">
                Your search for "{{ serviceTypeFilter }}" found no results.
              </v-alert>
            </template>
          </v-data-table>
          <v-data-table
            v-if="rateSummary.rateTypeId === 6 && selectedRateTypeId === 6"
            :headers="tripHeaders"
            :items="filterTimeRateSummaries(rateSummary.rateSummaries)"
            item-key="id"
            class="gd-dark-theme pricing"
            :class="{ disabled: formDisabled }"
            :search="serviceTypeFilter"
          >
            <template #items="tableProps">
              <tr
                @click="emit('input', tableProps.item.serviceTypeId)"
                :class="{
                  'row-highlight':
                    tableProps.item.serviceTypeId === serviceTypeId,
                  'row-disabled':
                    formDisabled &&
                    tableProps.item.serviceTypeId !== serviceTypeId,
                }"
                class="table-rows"
              >
                <td>
                  {{ tableProps.item.shortName }} -
                  {{ tableProps.item.longName }}
                  <v-icon
                    class="selected-icon"
                    v-if="tableProps.item.serviceTypeId === serviceTypeId"
                    >check_box</v-icon
                  >
                </td>
              </tr>
            </template>
            <template v-slot:no-results>
              <v-alert :value="true" color="error" icon="warning">
                Your search for "{{ serviceTypeFilter }}" found no results.
              </v-alert>
            </template>
          </v-data-table>
        </v-flex>
      </v-layout>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
export interface TimeRateSummary {
  serviceTypeId: number;
  shortName: string;
  longName: string;
  rate: string;
  minimumCharge: string;
  chargeIncrement: string;
  additionalTimes: string;
  fuelLevy: string;
}
export interface ZoneRateSummary {
  serviceTypeId: number;
  shortName: string;
  longName: string;
  rate: string;
  zoneCount: number;
}
export interface DistanceRateSummary {
  serviceTypeId: number;
  shortName: string;
  serviceName: string;
  longName: string;
  calculation: string;
  baseFreight: string;
  chargeIncrement: string;
  additionalTimes: string;
  rate: string;
  fuelLevy: string;
}

export interface PointToPointRateSummary {
  serviceTypeId: number;
  shortName: string;
  longName: string;
  rate: string;
  minimumCharge: string;
  chargeIncrement: string;
  additionalTimes: string;
  fuelLevy: string;
}
export interface UnitRateSummary {
  serviceTypeId: number;
  shortName: string;
  longName: string;
  rate: string;
  rateCount: number;
}

export interface TripRateSummary {
  serviceTypeId: number;
  shortName: string;
  longName: string;
}

export type RatesSummary =
  | {
      rateTypeId: JobRateType.TIME;
      rateTypeName: string;
      rateSummaries: TimeRateSummary[];
    }
  | {
      rateTypeId: JobRateType.ZONE;
      rateTypeName: string;
      rateSummaries: ZoneRateSummary[];
    }
  | {
      rateTypeId: JobRateType.DISTANCE;
      rateTypeName: string;
      rateSummaries: DistanceRateSummary[];
    }
  | {
      rateTypeId: JobRateType.POINT_TO_POINT;
      rateTypeName: string;
      rateSummaries: PointToPointRateSummary[];
    }
  | {
      rateTypeId: JobRateType.UNIT;
      rateTypeName: string;
      rateSummaries: TimeRateSummary[];
    }
  | {
      rateTypeId: JobRateType.TRIP;
      rateTypeName: string;
      rateSummaries: TripRateSummary[];
    }
  | {
      rateTypeId: JobRateType.ZONE_TO_ZONE;
      rateTypeName: string;
      rateSummaries: ZoneToZoneRateSummary[];
    };

import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { ZoneToZoneRateSummary } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneToZoneRateSummary';
import { Ref, toRef } from 'vue';

const emit = defineEmits<{
  (event: 'input', payload: number): void;
}>();

const props = defineProps<{
  value: number;
  selectedRateTypeId: JobRateType;
  rateSummaries: RatesSummary[];
  searchFilter: string;
  formDisabled: boolean;
}>();

const serviceTypeId: Ref<number> = toRef(props, 'value');
const serviceTypeFilter: Ref<string> = toRef(props, 'searchFilter');

/** Table headers for use in the v-data-table component in the template */
const timeHeaders: TableHeader[] = [
  {
    text: 'Service',
    align: 'left',
    value: 'serviceName',
    sortable: false,
  },
  {
    text: 'Rate',
    align: 'left',
    value: 'rate',
    sortable: false,
  },
  {
    text: 'Minimum Charge',
    align: 'left',
    value: 'minimumCharge',
    sortable: false,
  },
  {
    text: 'Charge Increment',
    align: 'left',
    value: 'chargeIncrement',
    sortable: false,
  },
  {
    text: 'First Leg / Last Leg',
    align: 'left',
    value: 'additionalTimes',
    sortable: false,
  },
  {
    text: 'Fuel Levy',
    align: 'left',
    value: 'fuelLevy',
    sortable: false,
  },
];
/** Table headers for use in the v-data-table component in the template */
const zoneHeaders: TableHeader[] = [
  {
    text: 'Service',
    align: 'left',
    value: 'serviceName',
    sortable: false,
  },
  {
    text: 'Rate',
    align: 'left',
    value: 'rate',
    sortable: false,
  },
  {
    text: 'Zone Count',
    align: 'left',
    value: 'zoneCount',
    sortable: false,
  },
];
/** Table headers for use in the v-data-table component in the template */
const distanceHeaders: TableHeader[] = [
  {
    text: 'Service',
    align: 'left',
    value: 'serviceName',
    sortable: false,
  },
  {
    text: 'Calculation',
    align: 'left',
    value: 'calculation',
    sortable: false,
  },
  {
    text: 'Base Freight',
    align: 'left',
    value: 'baseFreight',
    sortable: false,
  },
  {
    text: 'Charge Increment',
    align: 'left',
    value: 'chargeIncrement',
    sortable: false,
  },
  {
    text: 'Additional Travel',
    align: 'left',
    value: 'additionalTimes',
    sortable: false,
  },
  {
    text: 'Rate(s)',
    align: 'left',
    value: 'rate',
    sortable: false,
  },
  {
    text: 'Fuel Levy',
    align: 'left',
    value: 'fuelLevy',
    sortable: false,
  },
];

/** Table headers for use in the v-data-table component in the template */
const unitHeaders: TableHeader[] = [
  {
    text: 'Service',
    align: 'left',
    value: 'serviceName',
    sortable: false,
  },
  {
    text: '',
    align: 'left',
    value: 'rateCount',
    sortable: false,
  },
];
/** Table headers for use in the v-data-table component in the template */
const tripHeaders: TableHeader[] = [
  {
    text: 'Service',
    align: 'left',
    value: 'serviceName',
    sortable: false,
  },
];
/** Table headers for use in the v-data-table component in the template */
const zoneToZoneHeaders: TableHeader[] = [
  {
    text: 'Service',
    align: 'left',
    value: 'serviceName',
    sortable: false,
  },
  {
    text: 'Zone Applied',
    align: 'left',
    value: 'zoneApplied',
    sortable: false,
  },
  {
    text: 'Rate ($)',
    align: 'left',
    value: 'rate',
    sortable: false,
  },
  {
    text: 'Fuel Levy',
    align: 'left',
    value: 'fuelLevy',
    sortable: false,
  },
  {
    text: 'Allowed Load Time',
    align: 'left',
    value: 'allowedLoadTime',
    sortable: false,
  },
  {
    text: 'Fuel on Demurrage',
    align: 'left',
    value: 'fuelOnDemurrage',
    sortable: false,
  },
];

// Function to filter TimeRateSummary | ZoneRateSummary items with no name or rate as 0
function filterTimeRateSummaries(
  summaries: TimeRateSummary[] | ZoneRateSummary[] | TripRateSummary[],
) {
  return summaries
    .filter(
      (item) =>
        item.shortName.trim() !== '' &&
        (item as TimeRateSummary | ZoneRateSummary).rate !== '$0.00/min' &&
        (item as TimeRateSummary | ZoneRateSummary).rate !== '$0.00/hr' &&
        (item as TimeRateSummary | ZoneRateSummary).rate !== '$0.00',
    )
    .map((item) => ({
      ...item,
      serviceName: `${item.shortName} - ${item.longName}`, // Combine shortName & longName
    }));
}
</script>
<style scoped lang="scss">
.row-highlight {
  border-left: 3px solid var(--accent);
  position: relative;
  background-color: var(--background-color-400);
  .selected-icon {
    position: absolute;
    left: 20px;
    margin-left: auto;
    pointer-events: none;
    color: var(--accent) !important;
  }
}
.row-disabled {
  background-color: var(--background-color-300);
  pointer-events: none;
  td {
    color: var(--background-color-600);
  }
}
</style>
