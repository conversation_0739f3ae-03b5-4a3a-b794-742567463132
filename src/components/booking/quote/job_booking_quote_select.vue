<template>
  <v-layout>
    <v-flex class="quotes-container d-flex justify-end">
      <v-btn
        depressed
        solo
        outline
        class="v-btn-custom view"
        @click="isViewingDialog = true"
        :disabled="disabled"
      >
        View available Quotes
      </v-btn>
    </v-flex>
    <ContentDialog
      :showDialog.sync="isViewingDialog"
      title="View All Job Quotes"
      width="75%"
      @cancel="clearQuote"
      contentPadding="pa-2"
      :showActions="false"
    >
      <v-layout md12 class="pa-4">
        <v-flex>
          <v-layout align-center :style="{ width: 'calc(100% - 300px)' }">
            <GTitle
              title="Select Quote"
              subtitle="Select from available quotes for client"
              :divider="false"
            >
              <template #right-aligned-content>
                <div v-if="selectedQuoteId" class="selected-job">
                  Selected Quote:
                  <p class="selected-job-id">{{ selectedQuote?.quoteId }}</p>
                </div>
              </template>
            </GTitle>
          </v-layout>
        </v-flex>
        <v-flex md4>
          <v-layout align-center>
            <v-flex md12>
              <v-text-field
                v-model="search"
                append-icon="search"
                label="Search"
                hide-details
                color="orange"
                solo
                flat
                outline
                class="v-solo-custom"
              ></v-text-field>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>

      <v-layout md12 class="body-scrollable--60 body-min-height--65 pa-4">
        <v-flex>
          <v-data-table
            class="gd-dark-theme"
            :headers="headers"
            :items="tableData"
            :rows-per-page-items="[10, 20]"
          >
            <template v-slot:items="dataProps">
              <tr
                :class="selectedQuoteId === dataProps.item.id ? 'selected' : ''"
                @click="selectedQuoteId = dataProps.item.id"
              >
                <td>{{ dataProps.item.quoteId }}</td>
                <td>{{ dataProps.item.date }}</td>
                <td v-if="clientKey === 'CS'">
                  {{ dataProps.item.companyName }}
                </td>
                <td>{{ dataProps.item.serviceTypeName }}</td>
                <td>{{ dataProps.item.references }}</td>
                <td
                  class="text-center job-list-name-column-header"
                  style="cursor: pointer"
                >
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <span v-bind="attrs" v-on="on" class="suburb-tooltip">
                        {{
                          formatSuburbList(
                            dataProps.item.pickUpDropOffSuburbList,
                          )
                        }}
                      </span>
                    </template>
                    <div class="tooltip-content">
                      {{
                        getTooltipText(dataProps.item.pickUpDropOffSuburbList)
                      }}
                    </div>
                  </v-tooltip>
                </td>
                <td>{{ dataProps.item.dispatcherName }}</td>
                <td>{{ dataProps.item.clientCharge }}</td>
                <td>{{ dataProps.item.quoteCreationTime }}</td>
                <td>
                  {{ dataProps.item.quoteExpiryTime }}
                  <v-icon
                    v-if="selectedQuoteId === dataProps.item.id"
                    class="check-icon"
                    size="18"
                    >check_circle</v-icon
                  >
                </td>
              </tr>
            </template>
          </v-data-table>
        </v-flex>
      </v-layout>
      <v-divider></v-divider>
      <v-layout wrap mt-2 mb-2 pl-2 pr-2>
        <v-btn outline flat color="error" class="action-btn" @click="clearQuote"
          >Cancel</v-btn
        >
        <v-spacer></v-spacer>
        <v-btn
          solo
          depressed
          color="success"
          class="action-btn save"
          @click="applyQuoteId"
          >Confirm</v-btn
        >
      </v-layout>
    </ContentDialog>
  </v-layout>
</template>

<script setup lang="ts">
interface QuoteSummary {
  id: string | undefined;
  quoteId: string;
  quoteDescription: string;
  date: string;
  serviceTypeName: string;
  references: string;
  pickUpDropOffSuburbList: PUDItem[];
  dispatcherName: string;
  clientCharge: string;
  quoteCreationTime: string;
  quoteExpiryTime: string;
  companyName: string;
}
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { quoteIdPrefix } from '@/helpers/JobBooking/JobBookingQuoteHelpers.ts';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import type { QuoteDetails } from '@/interface-models/Jobs/Quote/QuoteDetails';
import { computed, ComputedRef, onMounted, Ref, ref } from 'vue';

const props = defineProps<{
  clientKey: string;
  appliedQuoteId: string;
  quoteDetailsList: QuoteDetails[];
  disabled: boolean;
}>();

const emit = defineEmits<{
  (event: 'applyQuoteId', payload: string | null): void;
}>();

const selectedQuoteId: Ref<string | null> = ref(null);
const isViewingDialog: Ref<boolean> = ref(false);
// search table
const search: Ref<string> = ref('');

// Computed property to find the selected quote based on selectedQuoteId
const selectedQuote = computed(() => {
  return (
    tableData.value.find((quote) => quote.id === selectedQuoteId.value) || null
  );
});

const headers: TableHeader[] = [
  {
    text: '#',
    align: 'left',
    sortable: true,
    value: 'quoteId',
    visible: true,
  },
  {
    text: 'Job Date',
    align: 'left',
    value: 'date',
    sortable: true,
    visible: true,
  },
  // "Company Name" will be conditionally inserted here on CashSales
  {
    text: 'Service',
    align: 'left',
    value: 'serviceTypeName',
    sortable: true,
    visible: true,
  },
  {
    text: 'Reference',
    align: 'left',
    sortable: true,
    value: 'reference',
    visible: true,
  },
  {
    text: 'Suburbs',
    align: 'left',
    value: 'fromAddress',
    sortable: true,
    visible: true,
  },
  {
    text: 'Dispatcher',
    align: 'left',
    value: 'dispatcherName',
    sortable: true,
    visible: true,
  },
  {
    text: 'Charge',
    align: 'left',
    value: 'clientCharge',
    sortable: true,
    visible: true,
  },
  {
    text: 'Created At',
    align: 'left',
    value: 'quoteCreationTime',
    sortable: true,
    visible: true,
  },
  {
    text: 'Expires At',
    align: 'left',
    value: 'quoteExpiryTime',
    sortable: true,
    visible: true,
  },
];

// Insert "Company Name" column if key is 'CS'
if (props.clientKey === 'CS') {
  headers.splice(2, 0, {
    text: 'Company Name',
    align: 'left',
    value: 'companyName',
    sortable: true,
    visible: true,
  });
}

const tableData: ComputedRef<QuoteSummary[]> = computed(() => {
  if (!props.quoteDetailsList) {
    return [];
  }

  // Filter based on search input
  const lowercasedSearch = search.value.toLowerCase();

  return props.quoteDetailsList
    .filter((quote) => quote.jobDetails)
    .map((quote) => {
      // Description format: Q-5 - BD dd-mm-yyyy - SUBURB to SUBURB
      const description = [
        `${quoteIdPrefix} ${quote.quoteId}`,
        `BD ${quote.jobDetails.jobDate}`,
        [
          quote.jobDetails.pudItems?.[0]?.address?.suburb,
          quote.jobDetails.pudItems?.[1]?.address?.suburb,
        ].join(' to '),
      ]
        .filter((x) => !!x)
        .join(' - ');
      const isTimeRate = quote.jobDetails.rateTypeName === 'Time';
      const jobDate = quote.jobDetails.jobDate || 'Unknown';
      const references = quote.jobDetails.allJobReferences || '-';
      const serviceTypeName =
        [quote.jobDetails.serviceTypeShortName, quote.jobDetails.rateTypeName]
          .filter((x) => !!x)
          .join(' - ') || 'N/A';
      // Set from and to address
      const pickUpDropOffSuburbList = quote.jobDetails.pudItems || [];
      let toAddress =
        quote.jobDetails.pudItems?.[1]?.address?.suburb || 'Unknown';
      if (quote.jobDetails.pudItems.length > 2) {
        const additionalStops = quote.jobDetails.pudItems.length - 2;
        toAddress += ` +${additionalStops}`;
      }
      const dispatcherName =
        [
          quote.jobDetails.clientDispatcher.firstName,
          quote.jobDetails.clientDispatcher.lastName,
        ]
          .filter((x) => !!x)
          .join(' ') || 'Unknown';

      const cashSaleCompanyName =
        quote.jobDetails.cashSaleClientDetails?.companyName ?? '';

      const cashSaleDispatcher =
        quote.jobDetails.cashSaleClientDetails?.contactName ?? '';

      return {
        id: quote.id,
        quoteId: quote.quoteId ? `${quoteIdPrefix}${quote.quoteId}` : '-',
        quoteDescription: description,
        date: jobDate,
        serviceTypeName: serviceTypeName,
        references: references,
        pickUpDropOffSuburbList: pickUpDropOffSuburbList,
        toAddress: toAddress,
        clientCharge: isTimeRate
          ? 'Variable'
          : `${
              quote.jobDetails.accounting?.totals?.finalTotal?.client ?? 'N/A'
            }`,
        dispatcherName:
          props.clientKey === 'CS' ? cashSaleDispatcher : dispatcherName,
        quoteCreationTime: returnFormattedDate(
          quote.quoteCreationTime,
          'DD/MM/YYYY HH:mma',
        ),
        quoteExpiryTime: returnFormattedDate(quote.quoteExpiryTime),
        companyName: cashSaleCompanyName,
      };
    })
    .filter((quote) => {
      // Search through relevant fields for the search term
      return (
        quote.quoteId.toLowerCase().includes(lowercasedSearch) ||
        quote.quoteDescription.toLowerCase().includes(lowercasedSearch) ||
        quote.serviceTypeName.toLowerCase().includes(lowercasedSearch) ||
        // quote.fromAddress.toLowerCase().includes(lowercasedSearch) ||
        quote.toAddress.toLowerCase().includes(lowercasedSearch) ||
        quote.references.toLowerCase().includes(lowercasedSearch) ||
        quote.dispatcherName.toLowerCase().includes(lowercasedSearch)
      );
    });
});

// format list of pud items to display first and last stop
function formatSuburbList(pudList: PUDItem[]): string {
  if (!pudList || pudList.length === 0) {
    return '';
  }
  if (pudList.length === 1) {
    // return suburbList[0].suburb;
    return pudList?.[0]?.address?.suburb || 'Unknown';
  }
  if (pudList.length === 2) {
    return `${pudList[0].address?.suburb} → ${pudList[1].address?.suburb}`;
  }

  return `${pudList[0].address?.suburb} (+ ${pudList.length - 2}) ${pudList[
    pudList.length - 1
  ].address?.suburb}`;
}

// Format tooltip text with all pud stops
function getTooltipText(pudList: PUDItem[]): string {
  return pudList
    .map((s) => `${s.legTypeFlag} - ${s.address?.suburb}`)
    .join(', ');
}

// apply selected quote to job
function applyQuoteId() {
  emit('applyQuoteId', selectedQuoteId.value);
  isViewingDialog.value = false;
}

// clear selected quote
function clearQuote() {
  if (selectedQuoteId.value === null) {
    emit('applyQuoteId', selectedQuoteId.value);
  }
  isViewingDialog.value = false;
}

onMounted(() => {
  if (props.appliedQuoteId) {
    selectedQuoteId.value = props.appliedQuoteId;
  }
});
</script>
<style scoped lang="scss">
.quotes-container {
  display: flex;
  //   // align-content: right;
  //   // align-items: right;
  //   // justify-content: space-between;
  //   .active {
  //     color: var(--accent-secondary) !important;
  //     font-weight: 500;
  //   }
  // span {
  //   padding-left: 20px;
  //   color: var(--light-text-color);
  // }
}
.v-btn-custom {
  border-radius: $border-radius-Xlg !important;
  &.view {
    color: var(--accent-secondary);
    border: 2px solid var(--accent-secondary);

    &:disabled {
      border: 2px solid $translucent;
    }
  }
  &.apply {
    background-color: var(--primary-light) !important;
    background: var(--primary-gradient);
    border: 2px solid $translucent-light;
    color: white;

    &:hover {
      box-shadow: var(--box-shadow);
    }
  }
}

.check-icon {
  padding-left: 14px;
  color: var(--success) !important;
}

tr {
  cursor: pointer !important;
}

.action-btn {
  border-radius: $border-radius-btn;
  box-shadow: none !important;
  min-width: 120px;
  min-height: 38px;
  &:hover {
    box-shadow: $box-shadow !important;
  }
}

.selected-job {
  display: flex;
  flex-direction: row;
  align-content: left;
  align-items: left;
  font-weight: 600;
  font-size: $font-size-18;
  margin: 0 auto !important;
  .selected-job-id {
    color: $success;
    padding-left: 8px;
  }
}
</style>
