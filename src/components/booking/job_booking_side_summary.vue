<template>
  <v-flex class="side-summary">
    <div class="side-summary-action-list custom-scrollbar">
      <div class="steps-list-container">
        <ul class="steps-list">
          <li
            v-for="(step, index) in requiredSteps"
            :key="step.title"
            class="step-list-item"
            :class="{
              completed: step.completed,
              'highlight-line': step.completed && step.previousStepsComplete,
            }"
          >
            <div
              class="status-icon"
              :class="{ 'enable-pop-animation': !jobDetails.jobId }"
            >
              <i v-if="step.completed" class="far fa-check"></i>
              <i v-else :class="step.icon"></i>
            </div>
            <div class="step-content">
              <div
                class="step-title-row"
                @click="step.scrollToId ? scrollTo(step.scrollToId) : null"
              >
                <div class="step-subtitle">Step {{ index + 1 }}</div>
                <div class="step-title">{{ step.title }}</div>
              </div>
              <div class="step-additional-content">
                <ul>
                  <template v-if="step.subSteps?.length">
                    <li
                      v-for="subStep in step.subSteps"
                      :key="subStep.id"
                      class=""
                      :class="[
                        subStep.class,
                        subStep.scrollToId ? 'allow-click' : '',
                      ]"
                      @click="
                        subStep.scrollToId ? scrollTo(subStep.scrollToId) : null
                      "
                    >
                      <span>{{ subStep.label }}</span>
                      <span>{{ subStep.value }}</span>
                    </li>
                  </template>
                  <template
                    v-if="
                      !step.subSteps?.length &&
                      index !== requiredSteps.length - 1
                    "
                  >
                    <li class="py-3"></li>
                  </template>
                </ul>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </v-flex>
</template>

<script setup lang="ts">
interface PricingTableData {
  value: string;
  label: string;
}

interface FormProgressStep {
  title: string;
  completed: boolean;
  previousStepsComplete: boolean;
  icon: string;
  scrollToId: string;
  subSteps?: FormProgressSubStep[];
}

interface FormProgressSubStep {
  id: string;
  label: string;
  value: string;
  class: string;
  isVisible: boolean;
  scrollToId?: string;
}

import {
  returnFormattedDate,
  returnFormattedTime,
  returnTimeNow,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  getDriverFromDriverId,
  getFleetAssetFromFleetAssetId,
} from '@/helpers/JobDataHelpers/JobDataHelpers';
// import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import type { JobDetails } from '@/interface-models/Jobs/JobDetails';
import { JobRecurrenceType } from '@/interface-models/Jobs/JobRecurrenceType';
import { RecurringJobDetails } from '@/interface-models/Jobs/RecurringJobDetails';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import moment from 'moment-timezone';
import { ComputedRef, Ref, computed, toRef } from 'vue';

const emit = defineEmits<{
  (event: 'scrollToTop'): void;
  (event: 'scrollTo', payload: string | undefined): void;
  (event: 'highlightElement', payload: string): void;
  (event: 'openPricingDialog'): void;
}>();

const props = withDefaults(
  defineProps<{
    jobDate?: number;
    jobDetails: JobDetails;
    recurrenceType: JobRecurrenceType;
    recurrenceDetails: RecurringJobDetails | null;
  }>(),
  {
    jobDate: moment().valueOf(),
  },
);

const jobDetails = toRef(props, 'jobDetails');

// func to scroll to pud item
function scrollTo(id: string | undefined) {
  emit('scrollTo', id);
}

/**
 * Returns the date of the job as a string to be displayed in the template. Uses
 * the first pud time if available, otherwise uses the jobDate prop, otherwise
 * uses the current time.
 */
const expectedJobDate: ComputedRef<string> = computed(() => {
  if (jobDetails.value.pudItems.length > 0) {
    return jobDetails.value.jobDate;
  } else if (props.jobDate) {
    return returnFormattedDate(props.jobDate);
  } else {
    return returnFormattedDate(returnTimeNow());
  }
});

const departureTime: Ref<string | null> = computed(() => {
  const pudItems = props.jobDetails?.pudItems;
  if (pudItems && pudItems.length > 0) {
    // Destructure loadTime and epochTime from the last item in pudItems
    const { loadTime, epochTime } = pudItems[pudItems.length - 1];
    // Compute the departure time by adding loadTime and epochTime
    const departureTimestamp = epochTime + loadTime;
    // Return the formatted departure time
    return returnFormattedTime(departureTimestamp);
  }
  return null;
});

const requiredSteps: ComputedRef<FormProgressStep[]> = computed(() => {
  const enumKey = WorkStatus[props.jobDetails.workStatus];
  const readableWorkStatus = enumKey.replace(/_/g, ' ');
  const fleetAsset = getFleetAssetFromFleetAssetId(
    props.jobDetails.fleetAssetId,
  );
  const driver = getDriverFromDriverId(props.jobDetails.driverId);
  const keyInfoStep = {
    title: 'Key Information',
    completed: true,
    previousStepsComplete: true,
    icon: 'fas fa-info-circle',
    scrollToId: 'jobSummary',
    subSteps: [
      {
        id: 'status',
        label: 'Status',
        value: readableWorkStatus,
        isVisible: true,
        class: '',
      },
      {
        id: 'jobDate',
        label: 'Job Date',
        value: expectedJobDate.value,
        isVisible: true,
        class: '',
      },
      {
        id: 'jobType',
        label: 'Type',
        value:
          props.recurrenceType === JobRecurrenceType.PERMANENT
            ? 'Permanent'
            : 'Adhoc',
        isVisible: true,
        class: '',
      },
      {
        id: 'fleetAsset',
        label: 'Fleet Asset',
        value: fleetAsset?.csrAssignedId || 'Unknown',
        isVisible: !!props.jobDetails.fleetAssetId,
        class: '',
      },
      {
        id: 'driver',
        label: 'Driver',
        value: driver?.displayName || 'Unknown',
        isVisible: !!props.jobDetails.driverId,
        class: '',
      },
      {
        id: 'firstStop',
        label: 'First Stop',
        value: props.jobDetails.pudItems[0]
          ? props.jobDetails.pudItems[0].pickupTime
          : '',
        isVisible: props.jobDetails.pudItems.length,
        class: 'time',
      },
      {
        id: 'lastStop',
        label: 'Last Stop',
        value: departureTime.value,
        isVisible: props.jobDetails.pudItems.length && departureTime.value,
        class: 'time',
      },
    ].filter((subStep) => subStep.isVisible),
  };

  const recurrenceStep = {
    title: 'Recurrence',
    completed: !!props.recurrenceDetails?.nextScheduledRecurrence,
    previousStepsComplete: keyInfoStep.completed,
    icon: 'far fa-sync-alt',
    scrollToId: 'recurrence',
    subSteps: [
      {
        id: 'activeFrom',
        label: 'Active From',
        value: props.recurrenceDetails?.commencementDate
          ? returnFormattedDate(props.recurrenceDetails.commencementDate)
          : '',
        class: '',
        isVisible: !!props.recurrenceDetails?.nextScheduledRecurrence,
      },
      {
        id: 'recurrenceType',
        label: 'First Booking',
        value: props.recurrenceDetails?.nextScheduledRecurrence
          ? returnFormattedDate(props.recurrenceDetails.nextScheduledRecurrence)
          : '',
        class: '',
        isVisible: !!props.recurrenceDetails?.nextScheduledRecurrence,
      },
    ].filter((subStep) => subStep.isVisible),
  };
  const deliverySubSteps = props.jobDetails.pudItems.map((pudItem, index) => ({
    id: pudItem.uniqueId,
    label: `${index + 1}`,
    value: pudItem.address?.suburb
      ? pudItem.address.suburb.toUpperCase()
      : 'Unknown',
    class: pudItem.legTypeFlag,
    isVisible: true,
    scrollToId: pudItem.uniqueId,
  }));

  const deliveryInfoStep = {
    title: 'Delivery Information',
    completed: props.jobDetails.pudItems.length > 0,
    previousStepsComplete:
      keyInfoStep.completed &&
      (props.recurrenceType === JobRecurrenceType.ADHOC ||
        recurrenceStep.completed),
    icon: 'far fa-route',
    scrollToId: 'deliverySummary',
    subSteps: deliverySubSteps,
  };

  const serviceStep = {
    id: 'service',
    label: 'Service',
    value:
      props.jobDetails.serviceTypeShortName !== '-'
        ? `${props.jobDetails.serviceTypeShortName} - ${props.jobDetails.rateTypeName}`
        : 'Service TBD',
    class: '',
    isVisible: true,
  };
  const pricingSubSteps = [
    serviceStep,
    ...pricingTableData.value.map((item) => ({
      id: item.label,
      label: item.label,
      value: item.value,
      class: '',
      isVisible: true,
    })),
  ];

  const pricingStep = {
    title: 'Pricing',
    completed:
      props.jobDetails.accounting.clientRates.length > 0 &&
      !!props.jobDetails.serviceTypeId &&
      !!props.jobDetails.rateTypeId,
    previousStepsComplete: true,
    icon: 'far fa-dollar-sign',
    scrollToId: 'pricing',
    subSteps: pricingSubSteps,
  };

  return [
    keyInfoStep,
    props.recurrenceType === JobRecurrenceType.PERMANENT
      ? recurrenceStep
      : null,
    deliveryInfoStep,
    pricingStep,
  ].filter((step) => step !== null) as FormProgressStep[];
});

const pricingTableData: ComputedRef<PricingTableData[]> = computed(() => {
  // Combine all additional charges into a single string
  const combinedAdditionalCharges = `$${(
    props.jobDetails.accounting.additionalCharges.chargeList || []
  ).reduce((total, charge) => {
    return total + charge.client.charge * charge.quantity; // Calculate the total sum
  }, 0)}`;

  const subtotal = props.jobDetails.accounting.totals.subtotals.lessGst.client;
  const total = props.jobDetails.accounting.totals.finalTotal.client;
  const gst = total - subtotal; // Calculate GST

  return [
    {
      label: 'Fuel (%)',
      value: `${
        props.jobDetails.accounting.additionalCharges.clientFuelSurcharge
          ?.appliedFuelSurchargeRate ?? 0
      }%`,
    },
    {
      label: 'Fuel ($)',
      value: `$${props.jobDetails.accounting.totals.subtotals.fuelSurcharges.client}`,
    },
    {
      label: 'Freight',
      value: `$${props.jobDetails.accounting.totals.subtotals.freightCharges.client}`,
    },
    {
      label: 'Add. Charges',
      value: combinedAdditionalCharges || '-',
    },
    {
      label: 'Est Subtotal',
      value: `$${props.jobDetails.accounting.totals.subtotals.lessGst.client}`,
    },
    { label: 'Est GST', value: `$${gst.toFixed(2)}` },
    {
      label: 'Est Total',
      value: `$${props.jobDetails.accounting.totals.finalTotal.client}`,
    },
  ];
});
</script>
<style scoped lang="scss">
.side-summary {
  position: fixed;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-content: center;

  width: 12.5%;
  max-height: 800px;
  overflow-y: auto;
  margin-top: 8px;

  /* Keyframes for the pop animation */
  @keyframes pop {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.4);
    }
    100% {
      transform: scale(1);
    }
  }

  .steps-list-container {
    padding: 10px;
    .steps-list {
      list-style: none;
      padding: 0;
      position: relative;

      .step-list-item {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        font-family: $sub-font-family;
        position: relative;
        margin-top: 3px;

        &:last-child {
          .step-content .step-additional-content ul {
            &::before {
              display: none;
            }

            li {
              &:last-child {
                padding-bottom: 0px;
              }
            }
          }
        }

        &.completed {
          .status-icon {
            background-color: var(--success-secondary);

            &.enable-pop-animation {
              animation: pop 0.5s ease-in-out 0.2s forwards;
            }
          }
        }

        .step-content {
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          width: 100%;
          .step-title-row {
            // display: flex;
            // flex-direction: column;

            .step-subtitle {
              font-size: $font-size-10;
              font-weight: 700;
              text-transform: uppercase;
              color: var(--light-text-color);
            }

            .step-title {
              font-size: $font-size-16;
              font-weight: 400;
              color: var(--light-text-color);
            }
            transition: 0.1s;
            &:hover {
              cursor: pointer;
              scale: 1.05;
              // transform-origin: left;
            }
          }

          .step-additional-content {
            ul {
              position: relative;
              list-style: none;
              padding: 0;
              margin: 0;

              li {
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                padding: 2px 0;
                font-size: $font-size-13;
                color: var(--light-text-color);
                span {
                  &:first-child {
                    // font-weight: 600;
                    margin-right: 6px;

                    &::after {
                      content: ':';
                    }
                  }
                }

                &.allow-click {
                  cursor: pointer;
                  transition: 0.1s;
                  &:hover {
                    color: var(--text-color);
                    scale: 1.05;
                  }
                }

                &:last-child {
                  padding-bottom: 36px;
                }
              }

              &::before {
                content: '';
                position: absolute;
                left: -26px; /* Center the line with the status icon */
                height: 100%;
                // top: -20px; /* Adjust to start a few pixels before the status icon */
                // bottom: 40px; /* Adjust to end a few pixels after the previous status icon */
                width: 2px; /* Adjust the width of the line */
                border-radius: 2px;
                background-color: var(
                  --line-color
                ); /* Use CSS variable for the color */
                z-index: 0; /* Ensure the line is behind the icons */
              }
            }
          }
        }

        .status-icon {
          min-width: 28px;
          min-height: 28px;
          margin-top: 4px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 10px;
          font-size: $font-size-14;
          color: white;
          background-color: grey;
          position: relative;
          z-index: 1; /* Ensure the icon is above the line */
          transition: background-color 0.3s ease-in-out;
        }

        /* Remove the line for the first item */
        &:first-child {
          &::before {
            display: none;
          }
        }
        &:last-child {
          margin-bottom: 0px; /* Adjusted to make space for the line */
        }
        /* Highlight line class */
        &.highlight-line {
          --line-color: var(--success-secondary); /* Green color */
        }

        /* Default line color */
        &:not(.highlight-line) {
          --line-color: grey; /* Grey color */
        }
      }
    }
  }

  .side-summary-action-list {
    margin-bottom: 10px;
    padding: 0px 8px 8px 8px;
    overflow-x: hidden;
    background-color: transparent;
  }
}

// Responsive Styles
@media only screen and (min-device-width: 1025px) and (max-device-width: 1440px) {
  .side-summary {
    position: absolute; // Change from fixed to relative
    max-width: 11.5%; // Make it take full width on smaller screens
    min-height: 435px;
  }
}

@media (max-width: 768px) {
  .side-summary {
    position: absolute; // Change from fixed to relative
    max-width: 11%; // Make it take full width on smaller screens
    min-height: 455px;
  }
}

// ipad air
@media only screen and (min-device-width: 1024px) and (max-device-width: 1180px) {
  .side-summary {
    position: absolute; // Change from fixed to relative
    max-width: 11%; // Make it take full width on smaller screens
    max-height: 380px;
  }

  .side-summary-action-list {
    min-height: 50px;
  }
}

// very large screens
@media only screen and (min-device-width: 2560px) {
  .side-summary {
    max-height: 80vh;
    .side-summary-action-list {
      padding: 30px;
    }
  }
}
</style>
