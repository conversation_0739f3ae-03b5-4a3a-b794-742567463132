<template>
  <JobContentDialog
    :showDialog.sync="isDialogOpen"
    title="Booking successful"
    width="40%"
    @cancel="bookNewJob"
    :showActions="false"
    :isConfirmUnsaved="false"
    contentPadding="pa-0"
    :isDisabled="false"
    :isLoading="false"
    confirmBtnText="Confirm"
    :isContentDialog="true"
    :hideDriverChat="true"
    :jobDetails="getJobDetails"
    :clientDetails="bookingDetails.clientDetails"
    :sidePanelDoesNotExpandContent="true"
    :contentClass="
      sessionManager.isClientPortal()
        ? 'v-dialog-custom client-portal'
        : 'v-dialog-custom'
    "
  >
    <v-layout row wrap>
      <v-flex
        md12
        class="body-scrollable--75 body-min-height--65"
        id="success-dialog-body"
      >
        <v-layout row wrap pa-2>
          <v-flex md12 class="title-container">
            <v-icon class="check-icon" size="60">task_alt</v-icon>
            <v-layout column align-start>
              <div class="title-row-txt">
                <span class="booking-number-text">{{ displayId.prefix }}</span>
                <span
                  class="booking-number-text id"
                  ref="message"
                  v-if="displayId.value"
                >
                  {{ displayId.value }}
                </span>
                <v-tooltip bottom v-if="displayId.value">
                  <template v-slot:activator="{ on }">
                    <v-btn
                      @click="copyTextNoInput"
                      icon
                      flat
                      solo
                      class="copy-btn"
                      v-on="on"
                    >
                      <v-icon>content_copy</v-icon>
                    </v-btn>
                  </template>
                  <span>Copy {{ displayId.type }} ID</span>
                </v-tooltip>
                <span v-if="copied" class="copied-message">Copied</span>
              </div>
              <span class="subline-txt"
                >{{ displayId.type }} has been successfully
                {{
                  bookingDetails.type !== JobBookingType.QUOTE
                    ? 'booked'
                    : 'saved'
                }}
                for
                {{
                  bookingDetails.type !== JobBookingType.QUOTE
                    ? bookingDetails.details.client?.clientName ?? 'Unknown'
                    : bookingDetails.details.jobDetails.client.clientName
                }}.</span
              >
              <span class="subline-txt" v-if="displayId.suffix">{{
                displayId.suffix
              }}</span>
            </v-layout>
          </v-flex>

          <v-flex md12 class="summary-container">
            <table class="summary-table">
              <tbody>
                <tr v-for="item in summaryInfo" :key="item.id">
                  <td class="title-txt">{{ item.title }}</td>
                  <td class="value-txt">{{ item.value }}</td>
                </tr>
              </tbody>
            </table>
            <v-layout
              column
              class="send-email-container"
              v-if="bookingDetails.type === JobBookingType.QUOTE"
            >
              <v-layout
                row
                align-center
                justify-space-between
                class="send-email-header"
              >
                <div class="d-flex align-center">
                  <span class="subheader--faded">EMAIL QUOTE DETAILS</span>
                  <v-icon
                    v-if="quoteEmailSendSuccessful"
                    color="success"
                    class="pl-2"
                    >done_all</v-icon
                  >
                </div>
                <div class="d-flex align-center">
                  <v-checkbox
                    class="email-checkbox"
                    v-model="sendEmailToMe"
                    label="Send to Me"
                    hide-details
                  />
                  <v-btn
                    depressed
                    plain
                    large
                    class="send-email-btn ml-4"
                    :loading="isLoading || isSendingQuoteEmail"
                    @click="generateQuoteReport"
                    :disabled="
                      quoteEmailSendSuccessful ||
                      sendToEmailAddressList.length <= 0
                    "
                  >
                    <v-icon class="pl-1 pr-2" size="16">send</v-icon>
                    {{ quoteEmailSendSuccessful ? 'Email Sent' : 'Send Email' }}
                  </v-btn>
                </div>
              </v-layout>

              <v-layout row>
                <v-flex xs12>
                  <v-combobox
                    v-model="sendToEmailAddressList"
                    :items="sendToEmailAddressList"
                    chips
                    class="v-solo-custom"
                    solo
                    multiple
                    flat
                    :disabled="quoteEmailSendSuccessful"
                    color="light-blue"
                    hint="Enter an email address then press enter to add"
                    label="Add Email Recipients"
                    :rules="[validateEmailAddressList]"
                    ref="emailAddressCombobox"
                    clearable
                    persistent-hint
                  >
                    <template v-slot:selection="data">
                      <v-chip
                        :key="JSON.stringify(data.item)"
                        :selected="data.selected"
                        :disabled="data.disabled"
                        class="v-chip--select-multi"
                        @click.stop="data.parent.selectedIndex = data.index"
                        @input="data.parent.selectItem(data.item)"
                        :class="
                          validate.email(data.item) === true
                            ? 'green darken-4'
                            : 'red darken-4'
                        "
                        close
                      >
                        <v-avatar
                          class="white--text"
                          :class="
                            validate.email(data.item) === true
                              ? 'green darken-2'
                              : 'red darken-2'
                          "
                        >
                          {{ data.item.slice(0, 2).toUpperCase() }}
                        </v-avatar>
                        {{ data.item }}
                      </v-chip>
                    </template>
                  </v-combobox>
                </v-flex>
              </v-layout>
            </v-layout>

            <v-layout
              v-if="
                bookingDetails.type === JobBookingType.PERMANENT &&
                bookingDetails.details.directToInvoicing
              "
            >
              <span class="directInvoice-txt"
                >Pre-allocate Driver to book job direct to invoicing
              </span>
            </v-layout>
            <v-expansion-panel
              v-if="
                showPreallocationInputs &&
                (bookingDetails.type === JobBookingType.ADHOC ||
                  bookingDetails.type === JobBookingType.PERMANENT ||
                  bookingDetails.type === JobBookingType.REBOOK)
              "
              v-model="preallocationExpansionPanel"
              class="expansion-panel-container mb-2"
            >
              <v-expansion-panel-content class="expansion-item-container pa-3">
                <template v-slot:actions>
                  <div align-center class="expand-icon">
                    <v-icon size="15">keyboard_arrow_down</v-icon>
                  </div>
                </template>
                <template v-slot:header>
                  <v-layout align-center pr-2>
                    <span class="expansion-header">Preallocate</span>
                    <v-divider class="mx-2"></v-divider>
                    <v-btn
                      v-if="hasValidAllocationSelections"
                      flat
                      small
                      outline
                      class="v-btn-rounded"
                      color="accent"
                      @click="deallocateCurrentSelection"
                      ><v-icon left size="16">fal fa-times</v-icon>Deallocate
                    </v-btn>
                  </v-layout>
                </template>
                <AllocateDriver
                  ref="allocateDriverComponent"
                  v-if="bookingDetails.details.client?.id"
                  :type="
                    bookingDetails.type === JobBookingType.PERMANENT
                      ? ObjectToAllocate.PERMANENT_JOB
                      : ObjectToAllocate.JOB_DETAILS
                  "
                  :onValidSelection="OnValidAllocationTarget.EMIT"
                  :fleetAssetId.sync="fleetAssetId"
                  :driverId.sync="driverId"
                  :serviceTypeId="allocationServiceTypeId"
                  :rateTypeId="allocationRateTypeId"
                  :fleetAssetRates="fleetAssetRates"
                  :jobId="
                    bookingDetails.type === JobBookingType.ADHOC ||
                    bookingDetails.type === JobBookingType.REBOOK
                      ? bookingDetails.details.jobId
                      : undefined
                  "
                  :clientId="bookingDetails.details.client?.id"
                  :searchDate="bookingDetails.details.jobRunEpoch"
                  :isFormDisabled="!isAllocationEnabled"
                  @submitPreallocation="handleAllocationDetails"
                />
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-divider></v-divider>
        <div class="btn-container">
          <v-btn
            class="action-btn"
            outline
            flat
            @click="returnToDashboard"
            :disabled="isSendingQuoteEmail"
            >Close</v-btn
          >
          <v-spacer></v-spacer>
          <template
            v-if="
              bookingDetails.type !== JobBookingType.PRELOAD &&
              bookingDetails.type !== JobBookingType.REBOOK
            "
          >
            <v-btn
              class="action-btn return-to-dashboard"
              @click="returnToDashboard"
              color="green"
              :loading="isLoading || isSendingQuoteEmail"
              :disabled="!!fleetAssetId && !driverId"
            >
              {{
                hasValidAllocationSelections
                  ? 'Pre-allocate & Return to Dashboard'
                  : 'Return to Dashboard'
              }}
            </v-btn>
            <v-btn
              class="action-btn book-another"
              @click="bookNewJob"
              color="blue"
              :loading="isLoading || isSendingQuoteEmail"
              :disabled="!!fleetAssetId && !driverId"
              >{{
                hasValidAllocationSelections
                  ? 'Preallocate & Book Another'
                  : 'Book Another'
              }}</v-btn
            >
          </template>
          <template v-if="bookingDetails.type === JobBookingType.REBOOK">
            <v-btn
              v-if="hasValidAllocationSelections"
              class="action-btn return-to-dashboard"
              @click="returnToDashboard"
              color="green"
              :loading="isLoading || isSendingQuoteEmail"
              :disabled="!!fleetAssetId && !driverId"
            >
              Pre-allocate & Close
            </v-btn>
            <v-btn
              class="action-btn book-another"
              @click="editRebookedJob"
              color="#f17d2a"
              :loading="isLoading || isSendingQuoteEmail"
              >{{
                hasValidAllocationSelections
                  ? 'Pre-allocate & Edit Job'
                  : 'Edit Job'
              }}</v-btn
            >
          </template>
          <v-btn
            class="action-btn book-another"
            @click="bookPreloadJob"
            color="blue"
            :loading="isLoading || isSendingQuoteEmail"
            v-if="bookingDetails.type === JobBookingType.PRELOAD"
            >Book Follow-on Job</v-btn
          >
        </div>
      </v-flex>
    </v-layout>
  </JobContentDialog>
</template>
<script setup lang="ts">
import AllocateDriver from '@/components/common/allocate_driver/allocate_driver.vue';
import JobContentDialog from '@/components/common/ui-elements/job_content_dialog.vue';
import {
  ObjectToAllocate,
  OnValidAllocationTarget,
} from '@/helpers/AllocationHelpers.ts/AllocationHelpers';
import { initialiseJobAccountingDetails } from '@/helpers/classInitialisers/InitialiseJobDetails';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { bookPreloadFollowOnJob } from '@/helpers/JobBooking/JobBookingPreloadHelpers';
import {
  getDriverFromDriverId,
  getFleetAssetFromFleetAssetId,
} from '@/helpers/JobDataHelpers/JobDataHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { isTripRateTableItem } from '@/helpers/RateHelpers/RateTableItemHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { JobAccountingDetails } from '@/interface-models/Generic/Accounting/JobAccountingDetails';
import JobPrimaryRate from '@/interface-models/Generic/Accounting/JobPrimaryRate';
import { Communication } from '@/interface-models/Generic/Communication/Communication';
import { JobCommunication } from '@/interface-models/Generic/Communication/CommunicationTypes/JobCommunication';
import { communicationVisibility } from '@/interface-models/Generic/Communication/CommunicationVisibility';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { Validation } from '@/interface-models/Generic/Validation';
import {
  AllocateJobRequest,
  AllocationSummary,
} from '@/interface-models/Jobs/Allocation/AllocateJobRequest';
import {
  JobBookingType,
  JobSuccessDialogConfig,
} from '@/interface-models/Jobs/JobBookingType';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import {
  GenerateQuoteReportRequest,
  QuoteReportRequestType,
} from '@/interface-models/Jobs/Quote/GenerateQuoteReportRequest';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { useAllocationStore } from '@/store/modules/AllocationStore';
import { useJobBookingStore } from '@/store/modules/JobBookingStore';
import { useRecurringJobStore } from '@/store/modules/RecurringJobStore';
import { sessionManager } from '@/store/session/SessionState';
import moment from 'moment-timezone';
import { v4 as uuidv4 } from 'uuid';
import {
  computed,
  ComputedRef,
  onMounted,
  Ref,
  ref,
  WritableComputedRef,
} from 'vue';

const props = withDefaults(
  defineProps<{
    bookingDetails: JobSuccessDialogConfig;
    isDialogOpen: boolean;
  }>(),
  {
    isDialogOpen: false,
  },
);

interface SummaryInfo {
  id: string;
  title: string;
  value: string | number;
}

const emit = defineEmits<{
  (event: 'returnToDashboard'): void;
  (event: 'bookNewJob'): void;
  (event: 'editRebookJob'): void;
}>();

const message = ref<HTMLElement | null>(null);
const copied = ref(false);

const preallocationExpansionPanel: Ref<boolean[]> = ref([false]);
const driverId: Ref<string> = ref('');
const fleetAssetId: Ref<string> = ref('');
const fleetAssetRates: Ref<JobPrimaryRate[]> = ref([]);
const isAllocationEnabled: Ref<boolean> = ref(true);
const allocateDriverComponent = ref<InstanceType<typeof AllocateDriver> | null>(
  null,
);

const driverIsTripRate: Ref<boolean> = ref(false);
const outsideHireNote: Ref<Communication | null> = ref(null);

const sendToEmailAddressList: Ref<string[]> = ref([]);

const isSendingQuoteEmail: Ref<boolean> = ref(false);
const quoteEmailSendSuccessful: Ref<boolean> = ref(false);
const isLoading: Ref<boolean> = ref(false);

const currentUserEmail: Ref<string> = ref(sessionManager.getUserName());

/**
 * A computed property that checks whether the current user's email
 * is included in the `sendToEmailAddressList`.
 *
 * - Returns `true` if the current user's email is in the list.
 * - Sets; Adds or removes the current user's email from the list based on the boolean value.
 *
 */
const sendEmailToMe: WritableComputedRef<boolean> = computed({
  get: () => sendToEmailAddressList.value.includes(currentUserEmail.value),
  set: (val: boolean) => {
    const email = currentUserEmail.value;
    if (val && !sendToEmailAddressList.value.includes(email)) {
      sendToEmailAddressList.value.push(email);
    } else if (!val) {
      sendToEmailAddressList.value = sendToEmailAddressList.value.filter(
        (e) => e !== email,
      );
    }
  },
});

const displayId: ComputedRef<{
  type: string;
  prefix: string;
  value: string;
  suffix?: string;
}> = computed(() => {
  if (props.bookingDetails.type === JobBookingType.QUOTE) {
    return {
      type: 'Quote',
      prefix: 'Quote #',
      value: `Q-${props.bookingDetails.details.quoteId}`,
    };
  } else if (
    props.bookingDetails.type === JobBookingType.ADHOC ||
    props.bookingDetails.type === JobBookingType.REBOOK
  ) {
    return {
      type: 'Job',
      prefix: 'Job #',
      value: props.bookingDetails.details.displayId,
    };
  } else if (props.bookingDetails.type === JobBookingType.PRELOAD) {
    return {
      type: 'Pre-load',
      prefix: 'Book Follow-On Job',
      value: '',
      suffix: 'Book the follow-on job below.',
    };
  } else {
    return {
      type: 'Permanent Job',
      prefix: 'Permanent Job Booked',
      value: '',
    };
  }
});

// computed ref that returns email of job dispatcher to push to email combobox
const dispatcherEmail: ComputedRef<string> = computed(() => {
  if (props.bookingDetails.type === JobBookingType.QUOTE) {
    return (
      props.bookingDetails.details?.jobDetails?.clientDispatcher
        ?.emailAddress?.[0] || ''
    );
  }
  if (
    props.bookingDetails.type === JobBookingType.ADHOC ||
    props.bookingDetails.type === JobBookingType.REBOOK
  ) {
    return (
      props.bookingDetails.details?.clientDispatcher?.emailAddress?.[0] || ''
    );
  }
  return '';
});

// computed ref that returns job details for JobContentDialog
const getJobDetails: ComputedRef<JobDetails | null> = computed(() => {
  if (props.bookingDetails.type === JobBookingType.QUOTE) {
    return props.bookingDetails.details?.jobDetails || null;
  } else if (
    props.bookingDetails.type === JobBookingType.ADHOC ||
    props.bookingDetails.type === JobBookingType.REBOOK ||
    props.bookingDetails.type === JobBookingType.PRELOAD
  ) {
    return props.bookingDetails.details || null;
  }
  return null;
});

/**
 * Used in template on the 'return to dashboard' button. Sends preallocation
 * request if required and emits to parent component to navigate to dashboard
 */
async function returnToDashboard(): Promise<void> {
  isLoading.value = true;
  await preallocateIfRequired();
  isLoading.value = false;
  emit('returnToDashboard');
}

/**
 * Used in template on the 'book another' button. Sends preallocation request if
 * required and emits to parent component to close dialog and view the new job
 * component
 */
async function bookNewJob(): Promise<void> {
  isLoading.value = true;
  await preallocateIfRequired();
  isLoading.value = false;
  emit('bookNewJob');
}

async function editRebookedJob(): Promise<void> {
  isLoading.value = true;
  await preallocateIfRequired();
  isLoading.value = false;
  emit('editRebookJob');
}

async function bookPreloadJob(): Promise<void> {
  if (props.bookingDetails.type !== JobBookingType.PRELOAD) {
    return;
  }
  isLoading.value = true;
  await bookPreloadFollowOnJob(props.bookingDetails.details);
  isLoading.value = false;
  emit('bookNewJob');
}

/**
 * Check if we need to preallocate before emitting to close dialog. If both
 * driverId and fleetAssetId are set, we need to preallocate
 */
async function preallocateIfRequired(): Promise<void> {
  if (hasValidAllocationSelections.value) {
    let isSuccess = false;
    if (fleetAssetRates.value) {
      isSuccess = await sendPreallocationRequest();
    } else {
      console.error('Allocation failed due to missing rate information.');
    }
    // Show notification based on success or failure
    if (isSuccess) {
      // Get the driver and fleet asset names so we can display them in a notification
      const csrAssignedId =
        getFleetAssetFromFleetAssetId(fleetAssetId.value)?.csrAssignedId ||
        'Unknown';
      const driverName =
        getDriverFromDriverId(driverId.value)?.displayName || 'Unknown';
      // Show notification based on job type
      if (
        props.bookingDetails.type === JobBookingType.ADHOC ||
        props.bookingDetails.type === JobBookingType.REBOOK
      ) {
        const jobId = displayId.value.value;
        if (jobId) {
          showNotification(
            `Job #${jobId} has been pre-allocated to ${csrAssignedId} (${driverName}).`,
            {
              type: HealthLevel.SUCCESS,
            },
          );
        }
      } else {
        showNotification(
          `Permanent job has been pre-allocated to ${csrAssignedId} (${driverName}).`,
          {
            type: HealthLevel.SUCCESS,
          },
        );
      }
    } else {
      showNotification(
        'Something went wrong while preallocating the job. Please try again later.',
      );
    }
  }
}

/**
 * Sends a request to preallocate the job. If the job is an ADHOC job, a job
 * preallocation API request is sent. If the job is a permanent job, we update
 * the full document.
 * @returns boolean indicating success or failure of the preallocation request
 */
async function sendPreallocationRequest(): Promise<boolean> {
  if (
    props.bookingDetails.type === JobBookingType.ADHOC ||
    props.bookingDetails.type === JobBookingType.REBOOK
  ) {
    // For ADHOC job, call preallocation API. Construct request and dispatch
    const allocateJobRequest = new AllocateJobRequest();
    allocateJobRequest.jobId = props.bookingDetails.details.jobId!;
    allocateJobRequest.fleetAssetId = fleetAssetId.value;
    allocateJobRequest.driverId = driverId.value;
    allocateJobRequest.fleetAssetRate = fleetAssetRates.value;

    // Send request to preallocate the job
    const result =
      await useAllocationStore().preAllocateJob(allocateJobRequest);
    if (!result) {
      return false;
    }
  } else if (props.bookingDetails.type === JobBookingType.PERMANENT) {
    // For permanent job, add the driverId and fleetAssetId to the template and
    // save the full document
    const recurringJobTemplate = props.bookingDetails.details;
    recurringJobTemplate.driverId = driverId.value;
    recurringJobTemplate.fleetAssetId = fleetAssetId.value;

    // Send request to save document
    const result =
      await useRecurringJobStore().saveRecurringJobTemplate(
        recurringJobTemplate,
      );
    if (!result) {
      return false;
    }
  }

  return true;
}

/**
 * Selects the text content of the message element and copies it to the
 * clipboard
 */
function copyTextNoInput() {
  if (message.value) {
    const storage = document.createElement('textarea');
    storage.value = message.value.innerText;
    document.body.appendChild(storage);
    storage.select();
    storage.setSelectionRange(0, 99999);
    document.execCommand('copy');
    document.body.removeChild(storage);

    // Set copied state to true
    copied.value = true;
    // Hide the "Copied" message after 2 seconds
    setTimeout(() => {
      copied.value = false;
    }, 3000);
  }
}

/**
 * Returns the service type ID that will be passed in as a prop to the
 * AllocateDriver component.
 */
const allocationServiceTypeId: ComputedRef<number> = computed(() => {
  if (props.bookingDetails.type === JobBookingType.QUOTE) {
    return -1;
  }
  const serviceTypeId =
    props.bookingDetails.type === JobBookingType.ADHOC ||
    props.bookingDetails.type === JobBookingType.REBOOK
      ? props.bookingDetails.details.fleetAssetServiceTypeId
      : props.bookingDetails.details.serviceTypeId;
  return serviceTypeId ?? -1;
});

/**
 * Returns true if all required fields have been returned from the
 * AllocateDriver component to satisfy pre-allocation
 */
const hasValidAllocationSelections: ComputedRef<boolean> = computed(() => {
  return (
    fleetAssetId.value !== '' &&
    driverId.value !== '' &&
    fleetAssetRates.value.length > 0
  );
});

/**
 * Handles the emit from the AllocateDriver component containing the details of
 * an allocation. If the selection is not valid, it resets the fleet asset and
 * driver IDs in the current edited recurring job.
 * @param isValid - boolean indicating whether the selection is valid or not.
 */
function handleAllocationDetails(allocationDetails: AllocationSummary | null) {
  const isValid = allocationDetails !== null;
  // If the selection is not valid, reset the fleet asset and driver IDs
  if (isValid) {
    fleetAssetId.value = allocationDetails.fleetAssetId;
    driverId.value = allocationDetails.driverId;
    fleetAssetRates.value = allocationDetails.fleetAssetRate;
  } else {
    fleetAssetId.value = '';
    driverId.value = '';
    fleetAssetRates.value = [];
  }
  isAllocationEnabled.value = !isValid;
}

/**
 * Clears the fleet asset and driver from the currently edited recurring job.
 */
function deallocateCurrentSelection() {
  allocateDriverComponent.value?.clearInputs();
  isAllocationEnabled.value = true;
}

/**
 * Determines whether or not we display the preallocation inputs in the
 * template.
 */
const showPreallocationInputs: ComputedRef<boolean> = computed(() => {
  // Do not display for a QUOTE type dialog, or if we're on the client portal
  if (
    sessionManager.isClientPortal() ||
    props.bookingDetails.type === JobBookingType.QUOTE
  ) {
    return false;
  }
  // If it's an ADHOC job, validate that we have a jobId, serviceType and
  // rateTypeId
  if (
    props.bookingDetails.type === JobBookingType.ADHOC ||
    props.bookingDetails.type === JobBookingType.REBOOK
  ) {
    return (
      !!props.bookingDetails.details.jobId &&
      !!props.bookingDetails.details.fleetAssetServiceTypeId &&
      !!props.bookingDetails.details.fleetAssetRateTypeId &&
      props.bookingDetails.details.workStatus === WorkStatus.BOOKED
    );
  } else {
    // If it's a RECURRING job, validate that we have a mongo id, serviceType
    // and rateTypeId
    return (
      !!props.bookingDetails.details._id &&
      !!props.bookingDetails.details.serviceTypeId &&
      !!props.bookingDetails.details.serviceTypeObject?.rateTypeId
    );
  }
});

/**
 * Returns a list of summary details for the stop. Used to display the stop
 * details in the summary section.
 */
const summaryInfo: ComputedRef<SummaryInfo[]> = computed(() => {
  const summaryList: SummaryInfo[] = [];

  const dayNames: { [key: number]: string } = {
    1: 'Mon',
    2: 'Tue',
    3: 'Wed',
    4: 'Thu',
    5: 'Fri',
    6: 'Sat',
    7: 'Sun',
  };
  const bookingDetails = props.bookingDetails;

  const details =
    bookingDetails.type === JobBookingType.QUOTE
      ? bookingDetails.details.jobDetails
      : bookingDetails.details;

  summaryList.push({
    id: 'client',
    title: 'Customer',
    value:
      bookingDetails.type === JobBookingType.QUOTE
        ? bookingDetails.details.jobDetails.client?.clientName
        : bookingDetails.details.client?.clientName ?? '-',
  });
  if (bookingDetails.type === JobBookingType.PERMANENT) {
    summaryList.push({
      id: 'date',
      title: 'First Booking Date',
      value: returnFormattedDate(
        bookingDetails.details.recurrenceDetails.nextScheduledRecurrence,
      ),
    });
  } else {
    if (
      bookingDetails.type === JobBookingType.ADHOC ||
      bookingDetails.type === JobBookingType.REBOOK
    ) {
      summaryList.push({
        id: 'date',
        title: 'Job Date',
        value: bookingDetails.details.jobDate,
      });
    }
  }
  summaryList.push({
    id: 'serviceType',
    title: 'Service Type',
    value: `${details.serviceTypeLongName} - ${details.rateTypeName}`,
  });
  summaryList.push({
    id: 'Dispatcher',
    title: 'Dispatcher',
    value: details.clientDispatcher?.firstName ?? '',
  });
  summaryList.push({
    id: 'pickup',
    title: 'First Pickup',
    value: details.pudItems?.[0]?.address?.suburb ?? 'Unknown',
  });
  if (bookingDetails.type !== JobBookingType.PRELOAD) {
    summaryList.push({
      id: 'delivery',
      title: 'Last Delivery',
      value:
        details.pudItems?.[details.pudItems.length - 1]?.address?.suburb ??
        'Unknown',
    });
    summaryList.push({
      id: 'stops',
      title: '# of Stops',
      value: details.pudItems?.length ?? 0,
    });
  }

  if (bookingDetails.type === JobBookingType.QUOTE) {
    summaryList.push({
      id: 'quoteExpiryTime',
      title: 'Expiry Date',
      value: returnFormattedDate(bookingDetails.details.quoteExpiryTime),
    });
  }
  if (bookingDetails.type === JobBookingType.PERMANENT) {
    summaryList.push({
      id: 'activeFrom',
      title: 'Active From',
      value: returnFormattedDate(
        bookingDetails.details.recurrenceDetails.commencementDate,
      ),
    });
    summaryList.push({
      id: 'validDays',
      title: 'Valid Days',
      value:
        bookingDetails.details.recurrenceDetails.repeatModifier === 1
          ? 'Daily (incl. weekends)'
          : bookingDetails.details.recurrenceDetails.repeatDays
              .map((dayNumber: number) => dayNames[dayNumber]) // Convert day numbers to names
              .join(', ') || 'No valid days',
    });
  }
  if (
    bookingDetails.type === JobBookingType.PERMANENT &&
    !bookingDetails.details.directToInvoicing
  ) {
    summaryList.push({
      id: 'directToInvoicing',
      title: 'Direct to Invoicing',
      value: bookingDetails.details.directToInvoicing ? 'Yes' : 'No',
    });
  }
  return summaryList;
});

/**
 * Used in the template as a prop for the AllocateDriver component. Used to tell
 * the component whether the job is a trip/Quoted rate or not, which will determine how
 * it selects a rate object.
 */
const allocationRateTypeId: ComputedRef<number> = computed(() => {
  let rateTypeId = JobRateType.TIME;

  if (driverIsTripRate.value) {
    rateTypeId = JobRateType.TRIP;
  } else {
    if (
      props.bookingDetails.type === JobBookingType.ADHOC ||
      props.bookingDetails.type === JobBookingType.REBOOK
    ) {
      rateTypeId = props.bookingDetails.details.fleetAssetRateTypeId ?? 1;
    } else if (props.bookingDetails.type === JobBookingType.PERMANENT) {
      rateTypeId =
        props.bookingDetails.details.serviceTypeObject?.rateTypeId ?? 1;
    }
  }
  return rateTypeId;
});

/**
 * Handles emit from AllocateDriver component, containing a description of the
 * Outside Hire who was allocated to the job
 */
function setOutsideHireNote(content: string) {
  const communication = new Communication();
  communication.id = uuidv4().split('-').join('');
  communication.body = content;
  // Set CommunicationType.communicationDetails to be JobCommunication
  communication.visibleTo = [
    communicationVisibility.find((cv) => cv.shortName === 'O')?.id ?? 2,
  ];
  communication.type = {
    id: 3,
    communicationDetails: new JobCommunication(),
  };
  communication.user = sessionManager.getUserName();
  communication.epoch = moment().valueOf();
  outsideHireNote.value = communication;
}

/**
 * Sets component state for preallocation when the component is mounted.
 */
function setRatesForPreallocation(
  accounting: JobAccountingDetails | undefined,
) {
  if (!accounting) {
    return;
  }
  if (
    props.bookingDetails.type === JobBookingType.ADHOC ||
    props.bookingDetails.type === JobBookingType.REBOOK ||
    props.bookingDetails.type === JobBookingType.PERMANENT
  ) {
    const job = props.bookingDetails.details;
    driverIsTripRate.value = !!job?.accounting?.fleetAssetRates?.[0]
      ? job.accounting.fleetAssetRates[0].rate.rateTypeId === JobRateType.TRIP
      : false;

    // Set the default values for fleetAssetId and driverId. These SHOULD be
    // empty for every type except rebook, as they're coming from the booking
    // screen. Whereas for rebook we have the allocation inputs available
    // also, so the job may already be allocated at the time we display
    fleetAssetId.value = job.fleetAssetId || '';
    driverId.value = job.driverId || '';
  }

  // Preserve trip/Quoted rate information if the driver has a trip/Quoted rate information
  // already from the booking process
  if (driverIsTripRate.value) {
    const initAcc = initialiseJobAccountingDetails(accounting);
    fleetAssetRates.value = initAcc.fleetAssetRates;
  } else {
    fleetAssetRates.value = [];
  }
}

/**
 * Computes the validation rules from the store.
 * @returns {ComputedRef<Validation>} A computed reference to the validation rules.
 */
const validate: ComputedRef<Validation> = computed(() => {
  return validationRules;
});

/**
 * Validates a list of email addresses.
 *
 * @param {string[]} emailAddresses - The list of email addresses to validate.
 * @returns {boolean|string} - Returns `true` if all email addresses are valid,
 * otherwise returns an error message.
 */
function validateEmailAddressList(emailAddresses: string[]): boolean | string {
  return emailAddresses.every((e) => validate.value.email(e) === true)
    ? true
    : 'One or more email addresses is invalid.';
}

/**
 * Sends a quote email if the booking type is a quote.
 * Constructs the email request object and triggers the email sending process.
 */
function generateQuoteReport(): void {
  if (props.bookingDetails.type === JobBookingType.QUOTE) {
    const request: GenerateQuoteReportRequest = {
      quoteMongoId: props.bookingDetails.details.id ?? '',
      requestType: QuoteReportRequestType.SEND,
      emailTo: sendToEmailAddressList.value,
      accessType: ReportAccessMethodTypes.EMAIL,
    };
    sendQuoteEmail(request);
  }
}

/**
 * Sends a quote email by calling the job booking store action.
 * Sets loading and success flags accordingly.
 *
 * @param request - The quote report request object containing email and quote details.
 */
async function sendQuoteEmail(
  request: GenerateQuoteReportRequest,
): Promise<void> {
  isSendingQuoteEmail.value = true;
  try {
    await useJobBookingStore().generateQuoteReport(request);
    quoteEmailSendSuccessful.value = true;
  } finally {
    isSendingQuoteEmail.value = false;
  }
}

onMounted(() => {
  if (
    (props.bookingDetails.type === JobBookingType.ADHOC ||
      props.bookingDetails.type === JobBookingType.REBOOK ||
      props.bookingDetails.type === JobBookingType.PERMANENT) &&
    showPreallocationInputs.value
  ) {
    setRatesForPreallocation(props.bookingDetails.details.accounting);
  }

  // push dispatcher email to email list to send quote details
  if (dispatcherEmail.value !== '') {
    sendToEmailAddressList.value.push(dispatcherEmail.value);
  }
  quoteEmailSendSuccessful.value = false;
});
</script>

<style scoped lang="scss">
.subheader {
  color: var(--bg-light);
  font-size: $font-size-22;
  font-weight: 600;
}

.btn-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;

  .action-btn {
    border-radius: 10px;
    margin: 8px 10px;
    padding: 20px;
    color: var(--text-color);
    transition: all 0.3s;

    &.return-to-dashboard {
      width: 300px;
      color: white !important;
    }
    &.book-another {
      width: 300px;
      color: white !important;
    }

    &:hover {
      box-shadow: var(--box-shadow);
    }
  }
}

.title-container {
  align-items: center;
  justify-items: center;
  // justify-content: space-evenly;
  padding: 30px 50px 0 50px;
  display: flex;

  .title-row-txt {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .booking-number-text {
    font-size: 36px;
    font-family: $sub-font-family;
    font-weight: 200;
    color: var(--text-color);

    &.id {
      color: var(--accent);
      padding: 3px 12px;
      margin-left: 6px;
      border-radius: 8px;
      font-weight: 400;
      border: 1px solid var(--border-color);
      background-color: var(--background-color-200);
    }
  }
  .check-icon {
    margin-right: 30px;
    color: $success;
  }
  .copy-btn {
    color: var(--light-text-color);

    &:hover {
      color: var(--primary);
      scale: 1.1;
    }
  }
}

.summary-container {
  display: flex;
  flex-wrap: wrap;
  align-content: center;
  justify-content: center;
  padding: 28px;

  .summary-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background-color: var(--background-color-400) !important;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;

    tr:first-child {
      background-color: var(--table-row);
    }

    td {
      padding: 18px;
      border-bottom: 1px solid var(--border-color) !important;
    }

    .title-txt {
      font-weight: 400;
      color: var(--light-text-color);
      text-align: left;
      font-size: $font-size-18;
      width: 30%;
    }
    .value-txt {
      font-weight: 500;
      color: var(--text-color);
      text-align: right;
      font-size: $font-size-18;
      width: 70%;
    }
  }
}

.directInvoice-txt {
  font-size: $font-size-20;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--accent);
  padding-top: 12px;
  padding-left: 22px;
}

.subline-txt {
  font-weight: 400;
  color: var(--light-text-color);
  font-size: $font-size-18;
}

.copied-message {
  color: var(--accent);
  margin-left: 10px;
  font-weight: 500;
}

.expansion-panel-container {
  border: 1px solid $border-color;
  border-radius: $border-radius-sm;
  margin-top: 20px;
  .expansion-item-container {
    border-radius: $border-radius-sm !important;
    background-color: var(--background-color-300) !important;

    .expand-icon {
      background-color: var(--accent-secondary);
      border-radius: 20px;
      height: 20px;
      width: 20px;
      padding: 2px !important;
      .v-icon {
        position: absolute;
        color: black !important;
        font-weight: 800;
      }
    }

    .expansion-header {
      font-size: $font-size-16;
      font-weight: 400;
      color: var(--accent-secondary);
      &.invalid {
        color: $error !important;
      }
    }
    .lineitem__label {
      font-size: $font-size-12;
      text-transform: uppercase;
      font-weight: 600;
      color: var(--light-text-color);
    }
    .lineitem__value {
      font-size: $font-size-14;
      font-weight: 400;
    }
  }
}

.send-email-container {
  padding-top: 22px;
  padding-bottom: 44px;
  .send-email-header {
    margin: 0 6px;
    .subheader--faded {
      color: var(--warning) !important;
      font-size: $font-size-18 !important;
    }
    .send-email-btn {
      width: max-content;
      border-radius: 44px;
      color: var(--warning);
      margin-bottom: 12px;
      height: 34px;
      background-color: transparent !important;
      border: 1.5px solid var(--warning);
      &:disabled {
        border: none !important;
      }
    }
  }
  .email-checkbox {
    transform: translateY(-10px);
  }
}

.notes-panel-container {
  border-left: 1px solid $warning;
  background-color: var(--background-color-200);
  .notes-header {
    color: $warning;
    display: flex;
    align-items: center;
    padding-left: 12px;
    padding-bottom: 8px;
    margin-top: 4px;
    margin-left: 2px;

    .notes-icon {
      margin-right: 10px;
      color: $warning;
    }

    .notes-title {
      font-size: $font-size-18;
      font-weight: 600;
    }
  }
}

.notes-side-bar {
  height: 100%;
  overflow: auto;
  max-height: 75vh;
  min-height: 75vh;
}
</style>
