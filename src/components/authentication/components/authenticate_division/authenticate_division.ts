import { SESSION_STORAGE_TOKEN } from '@/helpers/AppLayoutHelpers/LocalStorageHelpers';
import { Token } from '@/interface-models/Authentication/Token';
import { useAuthenticationStore } from '@/store/modules/AuthenticationStore';
import * as jwt from 'jose';
import { Component, Vue, Watch } from 'vue-property-decorator';
interface ClientSelect {
  clientName: string;
  clientId: string;
  company: string;
  division: string;
}

interface CompanyDivisionSelect {
  company: string;
  division: string;
}
@Component({})
export default class DivisionSelect extends Vue {
  public selectedDivision: any = null;
  public authenticationLoading: boolean = false;
  public divisionOrClientSelectList: ClientSelect[] | CompanyDivisionSelect[] =
    [];

  get loginState() {
    return useAuthenticationStore().authToken.accessToken;
  }
  @Watch('loginState')
  public setUser(token: string, oldToken: string) {
    if (token !== oldToken && token) {
      const decode = jwt.decodeJwt(token);
      sessionStorage.setItem(SESSION_STORAGE_TOKEN, token);
      if (decode) {
        this.$router.push('/');
      }
    }
  }

  get decodedToken() {
    return jwt.decodeJwt(this.loginState);
  }

  public submit(): void {
    if (!this.selectedDivision) {
      return;
    }
    this.authenticationLoading = true;
    this.$emit('authenticateDivision', this.selectedDivision);
  }

  public setDivisionOrClientSelectList() {
    const token = useAuthenticationStore().authToken.accessToken;
    const decode: Token = jwt.decodeJwt(token);
    if (!decode) {
      return;
    }
    this.divisionOrClientSelectList = decode.companies;

    this.selectedDivision = this.divisionOrClientSelectList[0]
      ? this.divisionOrClientSelectList[0]
      : null;
  }

  public mounted() {
    this.setDivisionOrClientSelectList();
  }
}
