<section class="login-basic">
  <v-form>
    <v-alert
      type="warning"
      :value="showCapsLockAlert"
      icon="fas fa-font-case"
      :class="{'mb-2' : showCapsLockAlert}"
    >
      <strong>Warning:</strong> Caps Lock is ON
    </v-alert>
    <v-text-field
      autocomplete="username"
      background-color="grey darken-1"
      outline
      color="white"
      label="Email"
      :disabled="isLoadingInitialCompanyDetails"
      v-model="loginDetails.username"
    >
    </v-text-field>

    <div class="password-container">
      <v-text-field
        autocomplete="current-password"
        background-color="grey darken-1"
        outline
        color="white"
        v-model="loginDetails.password"
        hide-details
        label="Password"
        :disabled="isLoadingInitialCompanyDetails"
        :append-icon="show ? 'visibility' : 'visibility_off'"
        :type="show ? 'text' : 'password'"
        @click:append="show = !show"
      >
      </v-text-field>
      <span class="caption forgot-password" @click="forgotPassword"
        >Forgot Password?</span
      >
    </div>

    <v-btn
      class="ma-0"
      v-shortkey.once="[`enter`]"
      block
      @shortkey="authenticateCompany"
      large
      light
      @click="authenticateCompany"
    >
      SIGN IN
    </v-btn>
  </v-form>
</section>
