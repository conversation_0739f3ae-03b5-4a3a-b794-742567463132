import { authenticateUser } from '@/helpers/AuthenticationHelpers/LoginHelpers';
import LoginDetails from '@/interface-models/Login/Login';
import { useAuthenticationStore } from '@/store/modules/AuthenticationStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import Mitt from '@/utils/mitt';
import * as jwt from 'jose';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';

@Component({})
export default class LoginForm extends Vue {
  @Prop() public loginDetails: LoginDetails;
  // handles showing of password characters
  public show: boolean = false;
  public showCapsLockAlert: boolean = false;

  public isLoadingInitialCompanyDetails: boolean = false;

  get loginState() {
    return useAuthenticationStore().authToken.accessToken;
  }

  @Watch('loginState')
  public setUser(token: string) {
    if (token.length > 0) {
      const decoded = jwt.decodeJwt(token);
      if (decoded.division === 'MULTI') {
        this.$emit('setDivisionSelect');
      } else {
        this.$router.push('/');
      }
    }
  }

  public authenticateCompany(): void {
    // clear all persistent notifications from login screen that are in the alertsGeneric group
    this.$notify({
      group: 'alertsGeneric',
      clean: true,
    });
    this.loginDetails.username = this.loginDetails.username.toLowerCase();
    const companyDetailsStore = useCompanyDetailsStore();
    // Set company id to loginDetails Request if it is known. Sending along the companyId in the initial login request restricts the response to only divisions in that company.
    if (companyDetailsStore.companyDetails?.companyNameId) {
      this.loginDetails.company =
        companyDetailsStore.companyDetails.companyNameId;
    }
    authenticateUser(this.loginDetails);
  }

  public forgotPassword(): void {
    this.$emit('forgotPassword');
  }

  // On keypress check the state of CapsLock and show/hide alert in html
  public handleCapsLockPress(e: KeyboardEvent) {
    if (e && e.getModifierState && e.getModifierState('CapsLock')) {
      this.showCapsLockAlert = true;
    } else {
      this.showCapsLockAlert = false;
    }
  }

  public setIsLoadingInitialCompanyDetails(isLoading: boolean) {
    this.isLoadingInitialCompanyDetails = isLoading;
  }

  public beforeDestroy() {
    // Add remove listener on component unmount
    document.removeEventListener('keyup', this.handleCapsLockPress);
    Mitt.off(
      'isLoadingInitialCompanyDetails',
      this.setIsLoadingInitialCompanyDetails,
    );
  }

  public mounted() {
    // Add listener on keypress to check caps lock state
    document.addEventListener('keyup', this.handleCapsLockPress);
    Mitt.on(
      'isLoadingInitialCompanyDetails',
      this.setIsLoadingInitialCompanyDetails,
    );
  }
}
