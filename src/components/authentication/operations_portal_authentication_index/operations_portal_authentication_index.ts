import ForgottenPassword from '@/authentication/forgotten_password/forgotten_password_index.vue';
import AuthenticateCompany from '@/components/authentication/components/authenticate_company/index.vue';
import AuthenticateDivision from '@/components/authentication/components/authenticate_division/index.vue';
import { authenticateUser } from '@/helpers/AuthenticationHelpers/LoginHelpers';
import { Portal } from '@/interface-models/Generic/Portal';
import LoginDetails from '@/interface-models/Login/Login';
import { useAuthenticationStore } from '@/store/modules/AuthenticationStore';
import { Component, Vue } from 'vue-property-decorator';

@Component({
  components: {
    AuthenticateCompany,
    AuthenticateDivision,
    ForgottenPassword,
  },
})
export default class OperationsPortalAuthenticationIndex extends Vue {
  public Portal = Portal;
  public isCompanyAuthenticate: boolean = true;
  public isDivisionSelect: boolean = false;
  public isForgotPassword: boolean = false;
  public loginDetails: LoginDetails = new LoginDetails();

  public setDivisionSelect() {
    this.isCompanyAuthenticate = false;
    this.isDivisionSelect = true;
  }

  public authenticateDivision(auth: any) {
    this.loginDetails.company = auth.company;
    this.loginDetails.division = auth.division;
    this.loginDetails.clientId = auth.clientId;
    useAuthenticationStore().disconnectWebsocket({
      isMultiTokenDisconnect: true,
      resetStore: false,
      resetCompanyDetails: false,
    });
    authenticateUser(this.loginDetails);
  }

  public forgotPassword(): void {
    this.isCompanyAuthenticate = false;
    this.isForgotPassword = true;
  }

  public backToLogin(): void {
    this.isForgotPassword = false;
    this.isCompanyAuthenticate = true;
  }
}
