<template>
  <div>
    <transition name=" fade" mode="out-in" v-if="showSplashPage()">
      <Splash />
    </transition>
    <div v-if="applicationHasLoaded">
      <div v-if="!isAuthenticationRoute() && !isPopoutWindow()">
        <OperationsAppBar></OperationsAppBar>
        <OperationsNavBar></OperationsNavBar>
      </div>
      <OperationsJobStatisticsBar
        v-if="showJobStatisticsBar()"
      ></OperationsJobStatisticsBar>
      <div
        :class="{
          'route-view-container': !isAuthenticationRoute() && !isPopoutWindow(),
        }"
      >
        <router-view></router-view>
      </div>
      <GlobalConfirmationDialog></GlobalConfirmationDialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import OperationsAppBar from '@/App/layouts/operations_portal_layout/components/operations_app_bar.vue';
import OperationsJobStatisticsBar from '@/App/layouts/operations_portal_layout/components/operations_job_statistics_bar/index.vue';
import OperationsNavBar from '@/App/layouts/operations_portal_layout/components/operations_nav_bar/index.vue';
import Splash from '@/authentication/splash/index.vue';
import GlobalConfirmationDialog from '@/components/common/ui-elements/global_confirmation_dialog.vue';
import { SESSION_STORAGE_TOKEN } from '@/helpers/AppLayoutHelpers/LocalStorageHelpers';
import { isTokenValid } from '@/helpers/AuthenticationHelpers/LoginHelpers';
import { getUserLocale } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { BroadcastChannelType } from '@/helpers/NotificationHelpers/BroadcastChannelHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { BroadcastMessage } from '@/interface-models/Generic/OperationScreenOptions/BroadcastMessage';
import { useAppNavigationStore } from '@/store/modules/AppNavigationStore';
import { useBroadcastChannelStore } from '@/store/modules/BroadcastChannelStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useFleetMapStore } from '@/store/modules/FleetMapStore';
import { useGpsStore } from '@/store/modules/GpsStore';
import { useNetworkingStore } from '@/store/modules/NetworkingStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { useRootStore } from '@/store/modules/RootStore';
import { useMittListener } from '@/utils/useMittListener';
import moment from 'moment-timezone';
import { computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';

const companyDetailsStore = useCompanyDetailsStore();
const operationsStore = useOperationsStore();
const networkingStore = useNetworkingStore();

const route = useRoute();
const router = useRouter();

// Whether the splash page component show be shown
function showSplashPage(): boolean {
  return (
    isTokenValid(sessionStorage.getItem(SESSION_STORAGE_TOKEN)) &&
    !applicationHasLoaded.value &&
    !isAuthenticationRoute() &&
    !isPopoutWindow()
  );
}

// Whether the current view is authentication related route.
const isAuthenticationRoute = (): boolean => {
  return !(route.meta?.requiresAuth ?? false);
};

// Whether the current view is a popout window
function isPopoutWindow() {
  return ['job_list', 'fleet_tracking'].includes(route.name ? route.name : '');
}

function showJobStatisticsBar(): boolean {
  return ['operations_index', 'Accounting'].includes(
    route.name ? route.name : '',
  );
}

const applicationHasLoaded = computed(() => {
  return !isAuthenticationRoute() && !isPopoutWindow()
    ? useRootStore().applicationHasLoaded
    : true;
});

// Watcher equivalent to @Watch('applicationHasLoaded')
watch(applicationHasLoaded, (hasLoaded) => {
  if (hasLoaded && !isAuthenticationRoute() && !isPopoutWindow()) {
    const userTimeZone = getUserLocale();
    operationsStore.setJobListDateFilter(moment().tz(userTimeZone).valueOf());
    networkingStore.setWebsocketIsDown(false);
    networkingStore.setInternetIsDown(false);
    networkingStore.setShowConnectionErrorDialog(false);
    networkingStore.setReconnectionAttempts(0);
    networkingStore.setInternetWasDown(false);
    setDivisionNotifications();
  }
});

// Handles notifications that should be shown to the user after
// the splash loading page. Current notifications include inactive division
// service rate and inactive division fuel surcharge.
const setDivisionNotifications = (): void => {
  const divisionActiveServiceRate =
    companyDetailsStore.activeDivisionServiceRate;
  const divisionActiveFuelSurcharge =
    companyDetailsStore.activeDivisionFuelSurchargeRate;

  const rateTypeDisplay =
    (!divisionActiveServiceRate ? 'Service' : '') +
    (!divisionActiveServiceRate && !divisionActiveFuelSurcharge
      ? ' and '
      : '') +
    (!divisionActiveFuelSurcharge ? 'Fuel Surcharge' : '');

  if (!divisionActiveServiceRate || !divisionActiveFuelSurcharge) {
    showNotification(
      `Division has inactive ${rateTypeDisplay} rates. Please contact Head Office.`,
      { title: `Division ${rateTypeDisplay} Inactive` },
    );
  }
};

// When the route changes, publish the updated value to any open windows such
// that actions can be enabled/disabled based on the active route
watch(
  () => route.name,
  (newValue) => {
    if (operationsStore.operationOptions.jobListWindowOpen) {
      const updatedJob = new BroadcastMessage('routeChanged', newValue);
      useBroadcastChannelStore().postMessageToChannel(
        BroadcastChannelType.JOB_LIST,
        updatedJob,
      );
    }
  },
);

// some pages don't require authentication. Check route and apply correct logic. Authenticate user if route is required
// public created() {
//   if (this.isAuthenticationRoute() || this.isPopoutWindow()) {
//     return;
//   }
//   const token = sessionStorage.getItem(SESSION_STORAGE_TOKEN);
//   const multiToken = sessionStorage.getItem(SESSION_STORAGE_MULTI_TOKEN);
//   if (multiToken !== null) {
//     AuthenticationModule.setMultiAuthToken(multiToken);
//   }
//   // Validate the token's expiry time. Remove tokens from sessionStorage if
//   // the token is expired or expiring soon
//   if (isTokenValid(token)) {
//     const headers = { accessToken: token, id: 0 };
//     AuthenticationModule.authenticateUser(headers);
//   } else {
//     sessionStorage.removeItem(SESSION_STORAGE_MULTI_TOKEN);
//     sessionStorage.removeItem(SESSION_STORAGE_TOKEN);
//   }
// }

// Computed property equivalent to fleetAssetTrackingWindowOpen
const fleetAssetTrackingWindowOpen = computed(() => {
  return useFleetMapStore().isFleetTrackingWindowOpen;
});

// Publish the full GPS list from the latestGpsPositions response to the
// popout window if it's open
function sendFullGpsListToWindow() {
  if (fleetAssetTrackingWindowOpen.value) {
    const data = new BroadcastMessage(
      'driverGpsData',
      Array.from(useGpsStore().allGpsPositions.values()),
    );
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.FLEET_TRACKING,
      data,
    );
    console.log('sendFullGpsListToWindow - sending to window');
  }
}

/**
 * Redirects the user to the login page if the `shouldJump` flag is true. Called
 * from AuthenticationStore when disconnecting websocket.

 * @param {boolean} shouldJump - Determines whether to perform the redirection
 * to the login page.
 */
function redirectToLogin(shouldJump: boolean | null) {
  if (!shouldJump) {
    return;
  }
  useAppNavigationStore().setPageTitle('');
  router.push('/login').catch((err) => {
    console.log(err);
  });
}

useMittListener('gpsListUpdated', sendFullGpsListToWindow);
useMittListener('shouldRedirectToLogin', redirectToLogin);
</script>

<style scoped lang="scss">
@import '@/styles/index.scss';

.v-toolbar__extension {
  padding: 0px !important;
}

#main-nav,
#app-footer {
  z-index: 10000000000;
}

// .disconnection-dialog {
//   z-index: 2147483647;
// }

.disable-text-highlight {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.notification-comp {
  z-index: 100000000000 !important;
}

#main-nav div.v-toolbar__content {
  padding-left: 0 !important;
}

.mapboxgl-popup {
  max-width: 200px;
}

.mapboxgl-popup-content {
  text-align: center;
  font-family: $font-sans;
}

.slide-enter-active {
  transition-delay: 1s;
  transition-duration: 3s;

  transition-timing-function: ease-in;
}

.slide-leave-active {
  transition-delay: 1s;
  transition-duration: 3s;

  transition-timing-function: cubic-bezier(0, 1, 0.5, 1);
}

.slide-enter-to,
.slide-leave {
  transform: translateY(-100%);
}

.slide-enter,
.slide-leave-to {
  transform: translateY(0);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.menu-route {
  border-right: 1px solid rgb(58, 58, 58);

  .route-group-icon {
    margin: 0px 6px 0px 0px;
    color: rgb(95, 95, 95) !important;
  }

  .nav-btns.v-btn {
    border-radius: 0px;
    box-sizing: border-box;
    transition: 0.3s ease;

    &.v-btn--active {
      & > .v-btn__content {
        color: var(--light-text-color);
      }

      &.csr-screen {
        border-bottom: 2px solid rgb(43, 243, 177);

        & > .v-btn__content {
          color: var(--light-text-color);
        }
      }

      &.admin-screen {
        border-bottom: 2px solid rgb(127, 63, 245);

        & > .v-btn__content {
          color: var(--light-text-color);
        }
      }
    }

    & > .v-btn__content {
      color: rgb(145, 145, 145);
      text-transform: uppercase;
      letter-spacing: 0.02em;
      font-weight: 500;
      font-size: $font-size-14;
    }
  }
}

.fill-content-container-height {
  height: 100%;
  max-height: 100%;
}

.main-nav {
  background-color: $bar-color !important;
  border-bottom: 1px solid $bar-border;
}

#app-footer {
  background-color: $bar-color;

  border-top: 1px solid $bar-border;
}

.brand {
  height: 30px;
}

.v-progress-linear {
  -moz-transform: scale(1, -1);
  -webkit-transform: scale(1, -1);
  -o-transform: scale(1, -1);
  -ms-transform: scale(1, -1);
  transform: scale(1, -1);
}

.time-display {
  margin-right: 100px;
}

.loading-div {
  width: 100%;
  height: 100%;
  z-index: 999999999999999999;
  background: transparent;
  position: fixed;
  top: 0;
  left: 0;
}

.toolbar-username {
  font-weight: 400;
  font-size: 1.2em;
}

.toolbar-comp-div {
  font-weight: 400;
  font-size: $font-size-small;
}

.route-view-container {
  padding-left: 50px !important;
  padding-top: 40px !important;
  position: relative;
  width: 100%;
  min-height: 100vh;
  background-color: var(--background-color-200);
  // APP MAIN BG
}

.notification-system {
  z-index: 200000000000 !important;
}
</style>
