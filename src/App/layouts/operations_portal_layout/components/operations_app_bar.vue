<template>
  <div
    class="app-status-bar__container app-theme__bg"
    :class="{ disableClickEvents: dialogIsOpen }"
  >
    <v-layout row justify-end align-center style="height: 100%">
      <AppMenu v-if="!dialogIsOpen"></AppMenu>

      <span
        class="app-status-bar__container--header"
        v-if="
          !dialogIsOpen &&
          !isSubcontractorRoute() &&
          !isClientMaintenanceRoute()
        "
        >{{ sectionTitle }}</span
      >

      <SubcontractorBreadCrumbs
        class="app-status-bar__container--header"
        v-if="isSubcontractorRoute()"
      />
      <ClientBreadCrumbs
        class="app-status-bar__container--header"
        v-if="isClientMaintenanceRoute()"
      />
      <v-spacer />

      <div class="app-status-bar__section">
        <v-layout align-center fill-height>
          <OperationsActionButtons v-if="isOperationsRoute" />
          <v-divider vertical class="mx-2" v-if="isOperationsRoute" />
          <v-tooltip bottom>
            <template v-slot:activator="{ on }">
              <v-icon size="24" v-on="on" color="white" class="keyboardIcon"
                >keyboard</v-icon
              >
            </template>
            <div>
              <v-layout justify-center class="key-list__legend">
                <span>Keyboard Shortcuts</span></v-layout
              >
              <v-divider class="pb-1"></v-divider>
              <v-layout align-center class="pb-1">
                <v-icon size="10" color="#"> arrow_right </v-icon>
                <span class="key-list__legend--head pl-2">Navigation</span>
              </v-layout>
              <v-layout align-center class="pb-1">
                <span class="key-list__legend--key"> Alt + C</span>
                <span class="key-list__legend--label pl-2">Client Details</span>
              </v-layout>
              <v-layout align-center class="pb-1">
                <span class="key-list__legend--key"> Alt + V</span>
                <span class="key-list__legend--label pl-2">Fleet Details</span>
              </v-layout>
              <v-layout align-center class="pb-1">
                <span class="key-list__legend--key"> Alt + A</span>
                <span class="key-list__legend--label pl-2">Accounting</span>
              </v-layout>
              <v-layout align-center class="pb-1">
                <span class="key-list__legend--key"> Alt + I</span>
                <span class="key-list__legend--label pl-2">Division</span>
              </v-layout>
              <v-layout align-center class="pb-1">
                <span class="key-list__legend--key"> Alt + S</span>
                <span class="key-list__legend--label pl-2">Search Job</span>
              </v-layout>
              <v-layout align-center class="pb-1">
                <span class="key-list__legend--key"> Alt + J</span>
                <span class="key-list__legend--label pl-2">Job Lists</span>
              </v-layout>
              <v-layout align-center class="pb-1">
                <span class="key-list__legend--key"> Alt + B</span>
                <span class="key-list__legend--label pl-2">Book Job</span>
              </v-layout>
              <v-layout align-center class="pb-1">
                <span class="key-list__legend--key"> Alt + T</span>
                <span class="key-list__legend--label pl-2">Fleet Tracking</span>
              </v-layout>
            </div>
          </v-tooltip>
          <v-divider vertical class="mx-2" />
          <DivisionFuelSurchargeSummary
            :isAppStatusBar="true"
          ></DivisionFuelSurchargeSummary>
          <v-divider vertical class="mx-2" />
          <DivisionRatesSummary :isAppStatusBar="true"></DivisionRatesSummary>
        </v-layout>
      </div>
      <div
        class="app-status-bar__section"
        :style="{ backgroundColor: appBarColor }"
      >
        <v-layout align-center fill-height>
          <DivisionSelect />

          <span class="px-1">
            <date-time-display></date-time-display>
          </span>
          <span style="position: relative">
            <ConnectionStatus></ConnectionStatus
          ></span>
        </v-layout>
      </div>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import ClientBreadCrumbs from '@/components/admin/SubcontractorIndex/components/client_bread_crumbs/index.vue';
import SubcontractorBreadCrumbs from '@/components/admin/SubcontractorIndex/components/subcontractor_bread_crumbs/index.vue';
import AppMenu from '@/components/common/app_menu/app_menu.vue';
import ConnectionStatus from '@/components/common/connection-status/connection_status.vue';
import DateTimeDisplay from '@/components/common/date_time_display/date_time_display.vue';
import DivisionSelect from '@/components/common/division-select/division_select.vue';
import OperationsActionButtons from '@/components/common/operations_action_buttons/operations_action_buttons.vue';
import DivisionFuelSurchargeSummary from '@/components/home/<USER>/division_fuel_surcharge_summary/division_fuel_surcharge_summary.vue';
import DivisionRatesSummary from '@/components/home/<USER>/division_rates_summary/index.vue';
import { BroadcastChannelType } from '@/helpers/NotificationHelpers/BroadcastChannelHelpers';
import { BroadcastMessage } from '@/interface-models/Generic/OperationScreenOptions/BroadcastMessage';
import { useAppNavigationStore } from '@/store/modules/AppNavigationStore';
import { useBroadcastChannelStore } from '@/store/modules/BroadcastChannelStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useNetworkingStore } from '@/store/modules/NetworkingStore';
import { computed, ComputedRef, watch } from 'vue';
import { useRoute } from 'vue-router/composables';

const companyDetailsStore = useCompanyDetailsStore();
const route = useRoute();

/**
 * Whether the current route is the operations route (dashboard, booking screen
 * etc.). Used to determine whether we show the OperationsActionButtons
 */
const isOperationsRoute: ComputedRef<boolean> = computed(() => {
  const routeName = route.name;

  return ['operations_index'].includes(routeName ?? '');
});

/**
 * Section title for the app bar, to display in the template.
 */
const sectionTitle: ComputedRef<string> = computed(() => {
  const routeName = route.name;
  if (typeof routeName !== 'string') {
    return '';
  }
  if (isOperationsRoute.value) {
    return useAppNavigationStore().subRouteTitle;
  } else {
    return routeName;
  }
});

/**
 * Whether the connection error dialog dialog is open
 */
const dialogIsOpen: ComputedRef<boolean> = computed(() => {
  return useNetworkingStore().showConnectionErrorDialog;
});

/**
 * Whether the current route is a subcontractor route. Used to determine if we
 * display the breadcrumbs component.
 */
function isSubcontractorRoute(): boolean {
  const routeName = route.name;
  return (
    routeName === 'Subcontractor' ||
    routeName === 'Owner' ||
    routeName === 'Driver' ||
    routeName === 'Truck' ||
    routeName === 'Trailer'
  );
}

/**
 * Whether the current route is a client maintenance route. Used to determine if we
 * display the breadcrumbs component.
 */
function isClientMaintenanceRoute(): boolean {
  const routeName = route.name;
  return routeName === 'Client' || routeName === 'Client Details';
}

/**
 * Watches for dialog open state and closes broadcast channels if needed.
 */
watch(dialogIsOpen, (value: boolean) => {
  if (value) {
    const updatedJob = new BroadcastMessage('closeWindow', true);
    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.JOB_LIST,
      updatedJob,
    );
    useBroadcastChannelStore().closeChannel(BroadcastChannelType.JOB_LIST);

    useBroadcastChannelStore().postMessageToChannel(
      BroadcastChannelType.FLEET_TRACKING,
      updatedJob,
    );
    useBroadcastChannelStore().closeChannel(
      BroadcastChannelType.FLEET_TRACKING,
    );
  }
});

/**
 * Returns the accent color defined in division details for the current logged in division.
 */
const appBarColor: ComputedRef<string | undefined> = computed(() => {
  return companyDetailsStore.divisionDetails?.customConfig?.theme?.accentColor;
});
</script>
<style scoped lang="scss">
.app-status-bar__container {
  position: fixed;
  z-index: 200;
  top: 0;
  left: 0;
  width: 100%;
  overflow: hidden;
  height: $app-bar-height;
  padding-left: $app-nav-bar-width;
  -webkit-transition: width 0.05s linear;
  transition: width 0.05s linear;
  -webkit-transform: translateZ(0) scale(1, 1);
  border-radius: 10px !important;

  .app-status-bar__container--header {
    font-size: $font-size-16;
    font-weight: 600;
    line-height: 1;
    color: var(--light-text-color);
    text-transform: uppercase;
  }

  .app-status-bar__section {
    position: relative;
    border: 1px solid $border-color;
    border-radius: 6px;
    border-top: 0px;
    height: 34px;
    margin: 0px 4px;
    padding: 0px 4px;
    background-color: $light-theme-text;

    &:last-child {
      margin: 0px 0px 0px 4px;
    }
  }
}

.disableClickEvents {
  pointer-events: none;
}

.company-logo {
  width: auto;
  height: 30px;
  display: block;
}

.user-container {
  display: flex;
  align-items: center;
  color: var(--text-color);
  font-size: $font-size-large;
  height: 100%;

  font-weight: 400;
}

.client-portal-nav-bar {
  padding-left: 25px !important;
}

.client-portal-nav-text {
  color: var(--text-color);
  display: flex;
  align-items: center;
}

.client-portal-nav-btn {
  color: var(--text-color);
}

.keyboardIcon {
  cursor: pointer;
  transition: transform 0.3s ease-in-out;
  margin: 4px;

  &:hover {
    transform: scale(1.2);
  }
}

.key-list__legend {
  font-weight: bold;
  font-size: 1.2em;
  color: var(--text-color);
}

.key-list__legend--head {
  font-weight: bold;
  font-size: 1.1em;
  color: var(--bg-light);
}

.key-list__legend--key {
  font-family: monospace;
  background-color: var(--background-color-600);
  padding: 2px 4px;
  border-radius: 4px;
  border: 2.5px solid $translucent;
  margin: 3px;
  color: var(--light-text-color);
  box-shadow: $shadow-primary;
}

.key-list__legend--label {
  color: var(--light-text-color);
  padding-left: 8px;
}

.indicator-icon-container {
  width: 16px;
  height: 16px;
  border-radius: 50%;
}
</style>
