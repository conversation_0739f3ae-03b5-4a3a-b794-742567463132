import Environment from '@/configuration/environment';
import { hasAdminOrCsrOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { useAppNavigationStore } from '@/store/modules/AppNavigationStore';
import { useAuthenticationStore } from '@/store/modules/AuthenticationStore';
import { useNetworkingStore } from '@/store/modules/NetworkingStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { sessionManager } from '@/store/session/SessionState';
import KeyboardManager from '@/utils/KeyboardManager';
import { Component, Vue } from 'vue-property-decorator';

interface GoToRoute {
  name: string;
  path: string;
}
interface OperationRoute {
  name: string;
  route: string;
  subHeading: string;
  color: string;
  children: OperationRouteChild[];
}

interface OperationRouteChild {
  id: string;
  value: string;
  subHeading: string;
  shortcut: string;
  disabled: boolean;
  primary: boolean;
  position: number;
  active: boolean;
  parent: string;
}

interface AdministrationRoute {
  group: number;
  name: string;
  path: string;
  icon: string;
  shortcut: string;
}
@Component({
  components: {},
})
export default class OperationsNavBar extends Vue implements IUserAuthority {
  public appNavigationStore = useAppNavigationStore();
  public networkingStore = useNetworkingStore();
  public keyboardManager = KeyboardManager;

  public administrationRoutes: AdministrationRoute[] = [
    {
      group: 2,
      name: 'Client Administration',
      shortcut: 'Alt + C',
      path: '/client',
      icon: 'fas fa-briefcase',
    },
    {
      group: 2,
      name: 'Fleet Administration',
      shortcut: 'Alt + V',
      path: '/subcontractor',
      icon: 'fad fa-truck-moving',
    },
    {
      group: 2,
      name: 'Accounting',
      shortcut: 'Alt + A',
      path: '/accounting',
      icon: 'fad fa-calculator',
    },
    {
      group: 2,
      name: 'Division',
      shortcut: 'Alt + I',
      path: '/administration',
      icon: 'fas fa-user-shield',
    },
    {
      group: 2,
      name: 'Job/Quote Search',
      shortcut: 'Alt + S',
      path: '/job-search',
      icon: 'fas fa-search',
    },
  ];
  get onOperations(): boolean {
    return this.currentRouteName === 'operations_index';
  }

  get currentRouteName(): string {
    const routeName = this.$route.name;
    return routeName ? routeName : '';
  }

  get currentRouteTitle(): string {
    const routeName = this.appNavigationStore.currentRouteTitle;
    return routeName ? routeName : '';
  }

  get dialogIsOpen(): boolean {
    return this.networkingStore.showConnectionErrorDialog;
  }

  get operationRoutes(): OperationRoute {
    return {
      name: 'Booking',
      route: 'operations_index',
      subHeading: 'Job Monitoring',
      color: 'teal',
      children: [
        {
          id: '#dashboard-main',
          value: 'fas fa-tachometer-fastest',
          subHeading: 'Dashboard',
          shortcut: 'Alt + J',
          disabled: false,
          primary: false,
          position: 0,
          active: true,
          parent: '',
        },

        {
          id: '#job-booking',
          value: 'fas fa-map-marker-plus',
          subHeading: 'Book Job',
          shortcut: 'Alt + B',
          disabled: false,
          primary: false,
          position: -48,
          active: true,
          parent: '',
        },
        {
          id: '#map-index',
          value: 'fas fa-map-marked-alt',
          subHeading: 'Fleet Tracking',
          shortcut: 'Alt + T',
          disabled: false,
          primary: false,
          position: -48,
          active: true,
          parent: '',
        },
        // {
        //   id: '#job-booking',
        //   value: 'fas fa-pizza-slice',
        //   subHeading: 'Job Booking (NEW)',
        //   shortcut: '',
        //   disabled: false,
        //   primary: false,
        //   position: -48,
        //   active: true,
        //   parent: '',
        // },
      ],
    };
  }

  get navigationChildren(): OperationRouteChild[] {
    if (this.operationRoutes) {
      return this.operationRoutes.children;
    }
    return [];
  }

  public navigateHome(): void {
    this.appNavigationStore.setCurrentRouteTitle('Home');
    this.$router.push('/');
  }
  public goToRoute(item: GoToRoute) {
    this.appNavigationStore.setCurrentRouteTitle(item.name);
    this.appNavigationStore.setCurrentComponentId('');
    this.$router.push(item.path);
  }
  public navigateTo(item: OperationRouteChild) {
    if (!this.onOperations) {
      this.$router.push('/operations');
    }
    this.appNavigationStore.setCurrentRouteTitle('operations_index');
    this.appNavigationStore.setAppbarTitle(item.subHeading);
    this.appNavigationStore.setCurrentComponentId(item.id);
  }

  public logout(): void {
    useOperationsStore().closeAllPopoutWindows();
    useAuthenticationStore().disconnectWebsocket();
  }
  get currentIndex(): number | undefined {
    if (this.operationRoutes === undefined) {
      return undefined;
    }
    const currentId = this.appNavigationStore.currentComponentId;
    const foundIndex = this.operationRoutes.children.findIndex(
      (item) => item.id === currentId,
    );
    if (foundIndex !== -1) {
      return foundIndex;
    } else {
      return 0;
    }
  }
  set currentIndex(index: number | undefined) {
    if (index !== undefined && this.operationRoutes !== undefined) {
      const data = this.operationRoutes.children[index];
      this.appNavigationStore.setCurrentComponentId(data.id);
    }
  }
  public jumpTo(index: number): void {
    if (this.operationRoutes === undefined) {
      return;
    }
    if (this.operationRoutes.name === 'Fleet Asset Owner') {
      const trueIndex = index + 1;
      const value = this.operationRoutes.children[trueIndex].id;
      this.appNavigationStore.setCurrentComponentId(value);
    } else {
      const value = this.operationRoutes.children[index].id;
      this.appNavigationStore.setCurrentComponentId(value);
    }
  }
  public jumpDown(): void {
    if (this.operationRoutes === undefined || this.currentIndex === undefined) {
      return;
    }
    if (this.currentIndex + 1 !== this.operationRoutes.children.length) {
      const value = this.operationRoutes.children[this.currentIndex + 1].id;
      this.appNavigationStore.setCurrentComponentId(value);
    }
  }
  public jumpUp(): void {
    if (this.operationRoutes === undefined || this.currentIndex === undefined) {
      return;
    }
    if (this.operationRoutes.name === 'Fleet Asset') {
      if (this.currentIndex !== 1) {
        const value = this.operationRoutes.children[this.currentIndex - 1].id;
        this.appNavigationStore.setCurrentComponentId(value);
      }
    } else if (this.currentIndex !== 0) {
      const value = this.operationRoutes.children[this.currentIndex - 1].id;
      this.appNavigationStore.setCurrentComponentId(value);
    }
  }
  get isLocalDevelopment() {
    const ENV: string | undefined = Environment.value('environment');
    return ENV === 'local' || ENV === 'staging';
  }

  get isGodestaAdminUser() {
    return sessionManager.getIsGodestaAdminUser();
  }

  public isAuthorised(): boolean {
    return hasAdminOrCsrOrTeamLeaderOrBranchManagerRole();
  }

  getChildByKey(key: string): OperationRouteChild {
    const childMap: { [key: string]: string } = {
      D: '#dashboard-main',
      B: '#job-booking',
      T: '#map-index',
    };

    const childId = childMap[key];
    return this.operationRoutes.children.find(
      (child) => child.id === childId,
    ) as OperationRouteChild;
  }

  mounted() {
    this.setupKeyHandlers();
    this.$router.afterEach((to) => {
      this.updateKeyHandlers(to.path);
    });
  }

  beforeDestroy() {
    this.keyboardManager.unmount();
  }

  public setupKeyHandlers() {
    this.keyboardManager.mount();
    this.updateKeyHandlers(this.$route.path);
  }

  public updateKeyHandlers(route: string) {
    // Remove all current handlers
    this.keyboardManager.clearAll();
    // dashboard-main
    this.keyboardManager.addKeyHandler(
      'KeyJ',
      () => this.navigateTo(this.getChildByKey('D')),
      { altKey: true },
    );
    // job-booking
    this.keyboardManager.addKeyHandler(
      'KeyB',
      () => this.navigateTo(this.getChildByKey('B')),
      { altKey: true },
    );
    // map-index
    this.keyboardManager.addKeyHandler(
      'KeyT',
      () => this.navigateTo(this.getChildByKey('T')),
      { altKey: true },
    );

    this.keyboardManager.addKeyHandler(
      'KeyC',
      () =>
        this.goToRoute({
          name: 'Client Administration',
          path: '/client',
        }),
      { altKey: true },
    );

    this.keyboardManager.addKeyHandler(
      'KeyV',
      () =>
        this.goToRoute({
          name: 'Fleet Administration',
          path: '/subcontractor',
        }),
      { altKey: true },
    );

    this.keyboardManager.addKeyHandler(
      'KeyA',
      () =>
        this.goToRoute({
          name: 'Accounting',
          path: '/accounting',
        }),
      { altKey: true },
    );

    this.keyboardManager.addKeyHandler(
      'KeyI',
      () =>
        this.goToRoute({
          name: 'Division',
          path: '/administration',
        }),
      { altKey: true },
    );

    this.keyboardManager.addKeyHandler(
      'KeyS',
      () =>
        this.goToRoute({
          name: 'Job/Quote Search',
          path: '/job-search',
        }),
      { altKey: true },
    );

    this.keyboardManager.addKeyHandler(
      'Enter',
      () => this.goToRoute({ name: 'Home', path: '/' }),
      { altKey: true },
    );
  }
}
