// @import url(//netdna.bootstrapcdn.com/font-awesome/4.0.3/css/font-awesome.css);

@import url(https://fonts.googleapis.com/css?family=Titillium+Web:300);

.navigation-bar {
  z-index: 200;
  position: relative;
  .fa-2x {
    font-size: $font-size-Xlarge;
  }

  .fa {
    position: relative;
    display: table-cell;

    height: 52px;
    text-align: center;
    vertical-align: middle;
    font-size: $font-size-14;

    &.small {
      font-size: $font-size-14;
      opacity: 0.7;
      color: var(--text-color);
    }
  }

  .main-menu:hover,
  nav.main-menu.expanded {
    // width: 240px;
    overflow: visible;
    // transition-delay: 0.5s;
  }

  .nav-header-container {
    transition: width 0.05s linear;
  }

  .nav-header {
    border-right: 1px solid $border-light;
    background-color: var(--background-color-300);
    transition: width 0.05s linear;
  }

  .main-menu {
    border-right: 1px solid $border-light;
    position: fixed;
    top: 0;
    bottom: 0;
    height: 100%;
    left: 0;
    width: 50px;
    overflow: hidden;
    -webkit-transition: width 0.05s linear;
    transition: width 0.05s linear;
    -webkit-transform: translateZ(0) scale(1, 1);
    transform: translateZ(0) scale(1, 1);
    z-index: 2147483646;
  }

  .godesta-logo__container {
    height: 100%;
    width: 100%;
    display: flex;
    height: 39px;
    align-items: center;
    padding-left: 10px;
    transition: 0.3s ease-out;

    &:hover {
      transition: 0.3s ease-in;
      transform: scale(1.2);
    }
  }

  .main-menu__divider {
    width: 100%;
    height: 1px;
    background-color: $border-light;
  }

  .nav-item-active {
    background-color: var(--background-color-600);
    i {
      color: var(--text-color);
    }
    .nav-text {
      color: var(--text-color);
      font-weight: 500;
    }
  }

  .nav-list-items {
    border-bottom: 1px solid $border-light;
    transition: 0.2 linear;
    transition: max-height 1s ease-in;
    overflow: hidden;
  }

  .minimised-nav-items {
    // max-height:1px;

    // transition: 0.2 linear;
    max-height: 0;
    transition: max-height 1s ease-out;
  }

  .main-menu li {
    position: relative;
    display: block;
    // width: 240px;
  }

  .main-menu li > a {
    position: relative;
    display: table;
    border-collapse: collapse;
    border-spacing: 0;
    color: var(--light-text-color);
    font-family: arial;
    font-size: $font-size-14;
    text-decoration: none;
    -webkit-transform: translateZ(0) scale(1, 1);
    -webkit-transition: all 0.1s linear;
    transition: all 0.1s linear;
    width: 50px;
  }

  .main-menu .nav-icon {
    position: relative;
    display: table-cell;
    width: 50px;
    height: 36px;
    text-align: center;
    vertical-align: middle;
    font-size: $font-size-18;
  }

  .main-menu .nav-text {
    position: relative;
    display: table-cell;
    vertical-align: middle;
    // width: 190px;
    overflow: hidden;
    font-family: $font-family-alternate;
  }

  .main-menu > ul.logout {
    position: absolute;
    left: 0;
    bottom: 0;
  }

  .no-touch .scrollable.hover {
    overflow-y: hidden;
  }

  .no-touch .scrollable.hover:hover {
    overflow-y: auto;
    overflow: visible;
  }

  a:hover,
  a:focus {
    text-decoration: none;
  }

  nav {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -o-user-select: none;
    user-select: none;
  }

  nav ul,
  nav li {
    outline: 0;
    margin: 0;
    padding: 0;
  }

  .main-menu li:hover > a,
  nav.main-menu li.active > a {
    color: var(--text-color);
    background-color: var(--hover-bg);
  }

  @font-face {
    font-family: $font-family-alternate;
    font-style: normal;
    font-weight: 300;
    src:
      local('Titillium WebLight'),
      local('TitilliumWeb-Light'),
      url(http://themes.googleusercontent.com/static/fonts/titilliumweb/v2/anMUvcNT0H1YN4FII8wpr24bNCNEoFTpS2BTjF6FB5E.woff)
        format('woff');
  }
}

.disableClickEvents {
  pointer-events: none;
}

.fake-dialog-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 50px;
  height: 100vh;
  background-color: var(--background-color-200);
  opacity: 0.9;
  z-index: 2147483647;
}
