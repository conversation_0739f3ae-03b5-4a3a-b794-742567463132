<section class="navigation-bar" :class="{disableClickEvents: dialogIsOpen}">
  <nav class="main-menu app-theme__nav">
    <ul>
      <li @click="navigateHome" style="cursor: pointer !important">
        <v-layout>
          <div class="godesta-logo__container">
            <img
              style="width: 30px; height: 30px"
              alt="GoDesta"
              src="@/static/images/godesta_logo.png"
            />
          </div>
        </v-layout>
      </li>
    </ul>

    <ul class="nav-list-items">
      <li
        v-for="(item, index) of administrationRoutes"
        :key="item.title"
        @click="goToRoute(item)"
        :class="{'nav-item-active' : currentRouteName === item.name}"
      >
        <v-tooltip right :content-class="'navigation-tool-tip'">
          <template v-slot:activator="{ on }">
            <a>
              <v-icon class="fa small" v-on="on"> {{item.icon}} </v-icon>
            </a>
          </template>
          <span>
            {{item.name}}
            <span class="shortcut-txt">( {{ item.shortcut }} )</span></span
          >
        </v-tooltip>
      </li>
    </ul>

    <ul class="nav-list-items" v-if="isAuthorised">
      <li
        v-for="(item, index) of navigationChildren"
        :key="item.id"
        @click="navigateTo(item)"
        :class="{'nav-item-active' : currentIndex === index && onOperations}"
      >
        <v-tooltip right :content-class="'navigation-tool-tip'">
          <template v-slot:activator="{ on }">
            <a>
              <v-icon class="fa small" v-on="on"> {{item.value}} </v-icon>
            </a>
          </template>
          <span>
            {{item.subHeading}}
            <span class="shortcut-txt">( {{ item.shortcut }} )</span></span
          >
        </v-tooltip>
      </li>
    </ul>

    <ul class="nav-list-items" v-if="isGodestaAdminUser">
      <li
        @click="goToRoute({name: 'Admin Tools', path: '/admin-tools'})"
        :class="{'nav-item-active' : currentRouteName === 'Admin Tools'}"
      >
        <v-tooltip right :content-class="'navigation-tool-tip'">
          <template v-slot:activator="{ on }">
            <a>
              <v-icon class="fa small" v-on="on"> fas fa-user-crown </v-icon>
            </a>
          </template>
          <span>GoDesta Admin Tools</span>
        </v-tooltip>
      </li>
    </ul>

    <ul class="nav-list-items" v-if="isLocalDevelopment">
      <li
        @click="goToRoute({name: 'maintenance', path: '/maintenance'})"
        :class="{'nav-item-active' : currentRouteName === 'maintenance'}"
      >
        <v-tooltip right :content-class="'navigation-tool-tip'">
          <template v-slot:activator="{ on }">
            <a>
              <v-icon class="fa small" v-on="on"> fad fa-code </v-icon>
            </a>
          </template>
          <span>Developer Maintenance</span>
        </v-tooltip>
      </li>
    </ul>
    <ul class="logout">
      <li @click="logout">
        <v-tooltip right :content-class="'navigation-tool-tip'">
          <template v-slot:activator="{ on }">
            <a>
              <v-icon class="fa small" v-on="on"> fas fa-power-off </v-icon>
            </a>
          </template>
          <span>Logout</span>
        </v-tooltip>
      </li>
    </ul>
  </nav>
  <div class="fake-dialog-overlay" v-if="dialogIsOpen"></div>
</section>
