import JobsSummary from '@/components/common/jobs_summary/jobs_summary.vue';
import InfiniteSlideBar from 'vue-infinite-slide-bar';
import { Component, Vue } from 'vue-property-decorator';

import { useJobStatisticsStore } from '@/store/modules/JobStatisticsStore';
import { useNetworkingStore } from '@/store/modules/NetworkingStore';
@Component({
  components: { JobsSummary, InfiniteSlideBar },
})
export default class OperationsJobStatisticsBar extends Vue {
  public scrollDuration: string = '0s';
  public isPaused: boolean = true;
  public activateScroll() {
    this.isPaused = !this.isPaused;
    const slider: HTMLElement | null =
      document.querySelector('.vifnslb-element');
    if (!slider) {
      return;
    }
    if (this.isPaused) {
      slider.style.cssText =
        'background: #1c1c1f, padding: 1px 0 !important, color: #bcbcbe !important; animation-duration: 45s; animation-direction: normal; animation-delay: 0s;-webkit-animation-play-state:paused; -moz-animation-play-state:paused; -o-animation-play-state:paused; animation-play-state:paused;';
    } else {
      slider.style.cssText =
        'background: #1c1c1f, padding: 1px 0 !important, color: #bcbcbe  !important; animation-duration: 45s; animation-direction: normal; animation-delay: 0s;';
    }
  }

  get dialogIsOpen() {
    return useNetworkingStore().showConnectionErrorDialog;
  }

  /**
   * Clear filtered selection when component is destroyed so we don't have extra
   * tab on job list
   */
  public beforeDestroy() {
    useJobStatisticsStore().setFilteredSelection(null);
  }
}
