<div class="app-statistics-bar" :class="{disableClickEvents: dialogIsOpen}">
  <span class="statistics-title" @click="activateScroll">Statistics</span>
  <infinite-slide-bar
    class="scroll-bar"
    :duration="scrollDuration"
    :bar-slider="'background: #1c1c1f, padding: 1px 0 !important, color: #bcbcbe !important; animation-duration: 45s; animation-direction: normal; animation-delay: 0s;-webkit-animation-play-state:paused; -moz-animation-play-state:paused; -o-animation-play-state:paused; animation-play-state:paused;'"
  >
    <!-- :class="scrollDuration === '0s' ? 'pl-2' : ''" -->
    <JobsSummary :isStatusBar="true" />
  </infinite-slide-bar>
  <div class="fake-dialog-overlay" v-if="dialogIsOpen"></div>
</div>
