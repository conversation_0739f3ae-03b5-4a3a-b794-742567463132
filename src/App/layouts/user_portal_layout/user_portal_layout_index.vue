<template>
  <div class="full-height">
    <transition name="fade" mode="out-in" v-if="showSplashPage">
      <Splash />
    </transition>
    <div
      class="route-view-container"
      v-if="!showSplashPage"
      :class="{ 'hide-side': isAuthenticationRoute }"
    >
      <UserAppBar class="user-app-bar" />
      <UserNavBar class="user-nav-bar" />
      <UserPortalAccountStatusDialog
        v-if="userPortalStore.showUserAccountStatusDialog"
      />
      <router-view class="router-view" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';

import UserAppBar from '@/App/layouts/user_portal_layout/components/user_app_bar/user_app_bar.vue';
import UserNavBar from '@/App/layouts/user_portal_layout/components/user_nav_bar/user_nav_bar.vue';
import Splash from '@/authentication/splash/index.vue';
import UserPortalAccountStatusDialog from '@/user_portal/components/user_portal_account_status_dialog.vue';

import { SESSION_STORAGE_TOKEN } from '@/helpers/AppLayoutHelpers/LocalStorageHelpers';
import { isTokenValid } from '@/helpers/AuthenticationHelpers/LoginHelpers';
import { useUserPortalStore } from '@/store/modules/UserPortalStore';
import { useMittListener } from '@/utils/useMittListener';
import { useAppNavigationStore } from '@/store/modules/AppNavigationStore';

const route = useRoute();
const router = useRouter();
const userPortalStore = useUserPortalStore();

const isAuthenticationRoute = computed(() => {
  return !(route.meta?.requiresAuth ?? false);
});

const showSplashPage = computed(() => {
  return (
    isTokenValid(sessionStorage.getItem(SESSION_STORAGE_TOKEN)) &&
    !userPortalStore.applicationHasLoaded &&
    !isAuthenticationRoute.value
  );
});

/**
 * Redirects the user to the login page if the `shouldJump` flag is true. Called
 * from AuthenticationStore when disconnecting websocket.

 * @param {boolean} shouldJump - Determines whether to perform the redirection
 * to the login page.
 */
function redirectToLogin(shouldJump: boolean | null) {
  if (!shouldJump) {
    return;
  }
  useAppNavigationStore().setPageTitle('');
  router.push('/login').catch((err) => {
    console.log(err);
  });
}

useMittListener('shouldRedirectToLogin', redirectToLogin);
</script>

<style lang="scss" scoped>
.route-view-container {
  height: 100%;
  display: grid;
  grid-template-areas:
    'appBar'
    'routerView';
  grid-template-rows: $app-bar-height 100%;
  grid-template-columns: 1fr;
}

.user-app-bar {
  grid-area: appBar;
}
.router-view {
  grid-area: routerView;
  height: 100%;
}
.user-nav-bar {
  grid-area: navBar;
  display: none;
}

@media (min-width: $user-portal-desktop-breakpoint) {
  .route-view-container {
    grid-template-areas:
      'appBar appBar'
      'navBar routerView';
    grid-template-rows: $app-bar-height calc(100vh - #{$app-bar-height});
    grid-template-columns: minmax(auto, 20%) 1fr;
  }

  .user-nav-bar {
    grid-area: navBar;
    display: grid;
  }

  .user-app-bar {
    grid-area: appBar;
  }
  .router-view {
    grid-area: routerView;
  }

  .hide-side {
    grid-template-areas:
      'appBar'
      'routerView';
    grid-template-rows: $app-bar-height calc(100vh - #{$app-bar-height});
    grid-template-columns: 1fr;
  }
}

.router-view {
  height: calc(100vh - #{$app-bar-height});
  overflow-y: auto;
}
</style>
