<template>
  <aside
    class="user-nav-bar-container"
    :class="{ 'nav-menu-open': menuIsOpen }"
    v-if="userPortalStore?.userDetailsSummary"
  >
    <div class="main-nav-links">
      <router-link to="/account-details" class="nav-item-container">
        <div class="nav-item" @click="closeMenu">
          <div class="active-route-highlight-container"></div>
          <div class="nav-icon-container">
            <v-icon class="nav-icon" size="16">far fa-briefcase</v-icon>
          </div>
          <div class="nav-name-container">Account Details</div>
        </div>
      </router-link>

      <router-link to="/account-security" class="nav-item-container">
        <div class="nav-item" @click="closeMenu">
          <div class="active-route-highlight-container"></div>
          <div class="nav-icon-container">
            <v-icon class="nav-icon" size="16">fas fa-shield-check</v-icon>
          </div>
          <div class="nav-name-container">Account Security</div>
        </div>
      </router-link>
    </div>

    <div class="nav-item-container logout-container">
      <div class="nav-item" @click="logout">
        <div class="active-route-highlight-container"></div>
        <div class="nav-icon-container">
          <v-icon class="nav-icon" size="16">fad fa-sign-out</v-icon>
        </div>
        <div class="nav-name-container">Logout</div>
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { useAuthenticationStore } from '@/store/modules/AuthenticationStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { useUserPortalStore } from '@/store/modules/UserPortalStore';
import { useRouter } from 'vue-router/composables';

const props = withDefaults(
  defineProps<{
    menuIsOpen?: boolean;
  }>(),
  {
    menuIsOpen: false,
  },
);

const emit = defineEmits<{
  (e: 'closeMenu', payload: boolean): void;
}>();

const router = useRouter();
const userPortalStore = useUserPortalStore();

function goToAccount() {
  router.push('/account-details');
}

function closeMenu() {
  emit('closeMenu', true);
}

function logout(): void {
  emit('closeMenu', true);
  useOperationsStore().closeAllPopoutWindows();
  useAuthenticationStore().disconnectWebsocket();
}
</script>

<style scoped lang="scss">
.user-nav-bar-container {
  height: calc(100vh - #{$app-bar-height});
  width: 100vw;
  position: absolute;
  top: #{$app-bar-height};
  right: 100%;
  transition: all 0.3s linear;
  border-right: 1px solid $border-color;
  background-color: #323135;
  display: grid;
  grid-template-areas:
    'mainLinks'
    'logout';

  grid-template-rows: 1fr 50px;
  grid-template-columns: 1fr;
  .nav-item-container {
    position: relative;
    cursor: pointer;
    height: 55px;
    display: flex;
    align-items: center;

    &:hover {
      cursor: pointer;
      background: hsla(0, 0%, 100%, 0.08);
    }

    .nav-item {
      display: flex;
      align-items: center;
      width: 100%;
    }

    .active-route-highlight-container {
      margin-right: 11px;
      height: $app-bar-height;
      width: 5px;
      background-color: #323135;
    }
    .nav-icon-container {
      width: 56px;
      .nav-icon {
        color: #49b9ff;
      }
    }

    .nav-name-container {
      text-transform: uppercase;
      width: calc(100vw - 77px - 77px);
      display: flex;
      justify-content: center;
    }
  }
}

.nav-menu-open {
  right: 0;
}

.main-nav-links {
  grid-area: mainLinks;
}

.logout-container {
  grid-area: logout;
  padding: 0 12px 12px;
  cursor: pointer !important;
}

@media (min-width: $user-portal-desktop-breakpoint) {
  .user-nav-bar-container {
    position: relative;
    z-index: 100;
    top: 0;
    left: 0;
    width: 100%;
    grid-template-rows: 1fr 50px;

    .nav-item-container {
      position: relative;
      height: $app-bar-height;
      display: flex;
      padding: 0 16px 0 0;
      align-items: center;

      .nav-item {
        display: flex;
        align-items: center;
        width: 100%;
      }

      .active-route-highlight-container {
        margin-right: 11px;
        height: $app-bar-height;
        width: 5px;
        background-color: #323135;
      }

      .nav-icon-container {
        width: 56px;

        .nav-icon {
          color: #49b9ff;
        }
      }

      .nav-name-container {
        text-transform: initial;
        width: 100%;
        display: block;
      }
      .main-nav-links {
        padding-top: 0;
      }
    }
    .logout-container {
      height: 50px;
    }
  }
}

.nav-item-container.router-link-active,
.nav-item-container.router-link-exact-active {
  background-color: #d2d8ef;
  color: black;

  .active-route-highlight-container {
    display: block;
    background-color: #49b9ff;
  }
  &:hover {
    background-color: #d2d8ef;
  }
}
</style>
