<template>
  <div class="app-status-bar__container">
    <v-layout row justify-end align-center style="height: 100%">
      <span class="ma-0 pa-0" :class="clientsName ? 'hide-element' : ''"
        ><b>CLIENT PORTAL</b></span
      >
      <v-divider vertical dark class="ml-2 bar-divider" />
      <v-btn
        :href="companyWebsite"
        rel="noopener noreferrer"
        flat
        dark
        class="ma-0 company-name-link"
      >
        <span class="ma-0 pa-0"
          ><b>{{ companyName }}</b></span
        >
      </v-btn>

      <v-divider class="bar-divider mr-3" vertical dark />

      <span
        ><b>{{ clientsName }}</b></span
      >
      <v-spacer />

      <v-divider
        v-if="activeUser !== ''"
        :class="!clientsName ? 'hide-element' : ''"
        vertical
        dark
        class="ml-3"
      />

      <v-divider v-if="activeUser" vertical dark class="bar-divider" />

      <div class="app-status-bar__section">
        <v-layout align-center fill-height>
          <div class="user-container px-2" v-if="activeUser">
            {{ activeUser }}
          </div>
          <v-divider vertical dark class="bar-divider ml-2" />

          <v-btn
            class="ma-0"
            v-if="$route.name !== 'login_index'"
            flat
            dark
            @click="logout"
          >
            <v-icon color="#FFFFFF" size="12" class="pr-1 ma-0 pl-0"
              >fad fa-sign-out</v-icon
            >Logout</v-btn
          >
          <v-divider vertical dark class="bar-divider" />
          <span class="pl-2">
            <date-time-display></date-time-display>
          </span>
          <span style="position: relative">
            <ConnectionStatus></ConnectionStatus
          ></span>
        </v-layout>
      </div>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import ConnectionStatus from '@/components/common/connection-status/connection_status.vue';
import DateTimeDisplay from '@/components/common/date_time_display/date_time_display.vue';
import { BroadcastChannelType } from '@/helpers/NotificationHelpers/BroadcastChannelHelpers';
import { useAuthenticationStore } from '@/store/modules/AuthenticationStore';
import { useBroadcastChannelStore } from '@/store/modules/BroadcastChannelStore';
import { useClientPortalStore } from '@/store/modules/ClientPortalStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useNetworkingStore } from '@/store/modules/NetworkingStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import * as jwt from 'jose';
import { computed, watch } from 'vue';

const companyDetailsStore = useCompanyDetailsStore();
const operationsStore = useOperationsStore();
const clientPortalStore = useClientPortalStore();
const networkingStore = useNetworkingStore();
const authenticationStore = useAuthenticationStore();

const dialogIsOpen = computed(() => {
  return networkingStore.showConnectionErrorDialog;
});

// if user is a client person we should get the clients name
const clientsName = computed(() => {
  const details = clientPortalStore.clientDetails;
  if (!details) {
    return '';
  }

  const clientName = details.tradingName || details.clientName || '';
  const divisionId = details.companyDivisionShortId || '';

  return `${clientName} - ${divisionId}`;
});

const activeUser = computed(() => {
  const token = authenticationStore.authToken.accessToken;
  if (!token) {
    return '';
  }

  try {
    const decoded: any = jwt.decodeJwt(token);
    return `${decoded.firstName ?? ''} ${decoded.lastName ?? ''}`.trim();
  } catch (e) {
    console.error('Invalid JWT:', e);
    return '';
  }
});

const companyWebsite = computed(() => {
  return companyDetailsStore.companyDetails?.companyWebsiteUrls?.[1] ?? null;
});

const companyName = computed(() => {
  return companyDetailsStore.companyDetails?.name
    ? companyDetailsStore.companyDetails.name.toUpperCase()
    : '';
});

function logout(): void {
  operationsStore.closeAllPopoutWindows();

  authenticationStore.disconnectWebsocket({
    isMultiTokenDisconnect: false,
    resetStore: true,
    resetCompanyDetails: false,
  });

  clientPortalStore.resetState();
}

watch(dialogIsOpen, (isOpen) => {
  if (!isOpen) {
    return;
  }

  const message = { id: 'closeWindow', value: true };

  const broadcastChannelStore = useBroadcastChannelStore();
  // Broadcast to operations
  broadcastChannelStore.postMessageToChannel(
    BroadcastChannelType.JOB_LIST,
    message,
  );
  broadcastChannelStore.closeChannel(BroadcastChannelType.JOB_LIST);

  // Broadcast to fleet tracking
  broadcastChannelStore.postMessageToChannel(
    BroadcastChannelType.FLEET_TRACKING,
    message,
  );
  broadcastChannelStore.closeChannel(BroadcastChannelType.FLEET_TRACKING);
});
</script>

<style lang="scss" scoped>
.app-status-bar__container {
  height: $app-bar-height;
  color: white;
  position: fixed;
  padding: 0 8px;
  z-index: 999;
  top: 0;
  left: 0;
  width: 100%;
  border-bottom: 1px solid var(--border-color);
  overflow: hidden;
  -webkit-transition: width 0.05s linear;
  transition: width 0.05s linear;
  -webkit-transform: translateZ(0) scale(1, 1);
  transform: translateZ(0) scale(1, 1);
  $side-margin: 5px;
  .app-status-bar__section {
    position: relative;
    height: 36px;
    margin: 0px $side-margin;
    padding: 0px 4px;

    &:last-child {
      margin: 0px 0px 0px $side-margin;
    }
  }
}

.disableClickEvents {
  pointer-events: none;
}

.company-logo {
  width: auto;
  height: 30px;
  display: block;
}

.user-container {
  display: flex;
  align-items: center;
  font-size: 1rem;
  height: 100%;

  font-weight: 400;
}

.client-name {
  display: flex;
  align-items: center;
  padding-left: 16px;
}

@media (max-width: 479px) {
  .hide-element {
    display: none;
  }

  .bar-divider {
    display: none;
  }

  .company-name-link {
    display: none;
  }

  .user-container {
    font-size: $font-size-11;
  }
  .app-status-bar__section {
    display: none;
  }
  .divider-last {
    display: none;
  }
  .time-display-container {
    display: none;
  }
  .client-name {
    padding-left: 0px;
  }
}
</style>
