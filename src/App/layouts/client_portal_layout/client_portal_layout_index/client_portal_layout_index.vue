<template>
  <div>
    <transition name=" fade" mode="out-in" v-if="showSplashPage()">
      <Splash />
    </transition>
    <div class="route-view-container client-portal" v-if="!showSplashPage()">
      <ClientAppBar></ClientAppBar>
      <router-view></router-view>
    </div>
  </div>
</template>

<script setup lang="ts">
import ClientAppBar from '@/App/layouts/client_portal_layout/components/client_app_bar/client_app_bar.vue';
import Splash from '@/authentication/splash/index.vue';
import { SESSION_STORAGE_TOKEN } from '@/helpers/AppLayoutHelpers/LocalStorageHelpers';
import { isTokenValid } from '@/helpers/AuthenticationHelpers/LoginHelpers';
import { useAppNavigationStore } from '@/store/modules/AppNavigationStore';
import { useRootStore } from '@/store/modules/RootStore';
import { useMittListener } from '@/utils/useMittListener';
import { useRoute, useRouter } from 'vue-router/composables';

const route = useRoute();
const router = useRouter();
// Whether the current view is authentication related route.
function isAuthenticationRoute(): boolean {
  return !(route.meta?.requiresAuth ?? false);
}

// Whether the splash page component show be shown
function showSplashPage(): boolean {
  return (
    isTokenValid(sessionStorage.getItem(SESSION_STORAGE_TOKEN)) &&
    !useRootStore().applicationHasLoaded &&
    !isAuthenticationRoute()
  );
}

/**
 * Redirects the user to the login page if the `shouldJump` flag is true. Called
 * from AuthenticationStore when disconnecting websocket.

 * @param {boolean} shouldJump - Determines whether to perform the redirection
 * to the login page.
 */
function redirectToLogin(shouldJump: boolean | null) {
  if (!shouldJump) {
    return;
  }
  useAppNavigationStore().setPageTitle('');
  router.push('/login').catch((err) => {
    console.log(err);
  });
}

useMittListener('shouldRedirectToLogin', redirectToLogin);
</script>

<style scoped lang="scss">
@import '@/styles/index.scss';

.v-toolbar__extension {
  padding: 0px !important;
}

#main-nav,
#app-footer {
  z-index: 10000000000;
}

// .disconnection-dialog {
//   z-index: 2147483647;
// }

.disable-text-highlight {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.notification-comp {
  z-index: 100000000000 !important;
}

#main-nav div.v-toolbar__content {
  padding-left: 0 !important;
}

.mapboxgl-popup {
  max-width: 200px;
}

.mapboxgl-popup-content {
  text-align: center;
  font-family: $font-sans;
}

.slide-enter-active {
  transition-delay: 1s;
  transition-duration: 3s;

  transition-timing-function: ease-in;
}

.slide-leave-active {
  transition-delay: 1s;
  transition-duration: 3s;

  transition-timing-function: cubic-bezier(0, 1, 0.5, 1);
}

.slide-enter-to,
.slide-leave {
  transform: translateY(-100%);
}

.slide-enter,
.slide-leave-to {
  transform: translateY(0);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.menu-route {
  border-right: 1px solid #3a3a3a;

  .route-group-icon {
    margin: 0px 6px 0px 0px;
    color: #5f5f5f !important;
  }

  .nav-btns.v-btn {
    border-radius: 0px;
    box-sizing: border-box;
    transition: 0.3s ease;

    &.v-btn--active {
      & > .v-btn__content {
        color: white;
      }

      &.csr-screen {
        border-bottom: 2px solid #2bf3b1;

        & > .v-btn__content {
          color: #effffa;
        }
      }

      &.admin-screen {
        border-bottom: 2px solid #7f3ff5;

        & > .v-btn__content {
          color: #ede4fd;
        }
      }
    }

    & > .v-btn__content {
      color: #919191;
      text-transform: uppercase;
      letter-spacing: 0.02em;
      font-weight: 500;
      font-size: $font-size-14;
    }
  }
}

.fill-content-container-height {
  height: 100%;
  max-height: 100%;
}

.main-nav {
  background-color: $bar-color !important;
  border-bottom: 1px solid $bar-border;
}

#app-footer {
  background-color: $bar-color;

  border-top: 1px solid $bar-border;
}

.brand {
  height: 30px;
}

.v-progress-linear {
  -moz-transform: scale(1, -1);
  -webkit-transform: scale(1, -1);
  -o-transform: scale(1, -1);
  -ms-transform: scale(1, -1);
  transform: scale(1, -1);
}

.time-display {
  margin-right: 100px;
}

.toolbar-username {
  font-weight: 400;
  font-size: 1.2em;
}

.toolbar-comp-div {
  font-weight: 400;
  font-size: $font-size-small;
}

.route-view-container {
  padding-top: 40px !important;
  position: relative;
  width: 100%;
  min-height: 100vh;
}

.notification-system {
  z-index: 200000000000 !important;
}

// .vifnslb-element:hover{ -webkit-animation-play-state:paused; -moz-animation-play-state:paused; -o-animation-play-state:paused; animation-play-state:paused; }
</style>
