import { PrimaryDriverIdentifier } from '@/interface-models/Company/DivisionCustomConfig/Operations/PrimaryDriverIdentifier';
import type { DriverDetails } from '@/interface-models/Driver/DriverDetails/DriverDetails';
import type DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';

/**
 * Sets the frontend-only property for displayName on the DriverDetails and
 * DriverDetailsSummary objects. Called when the initial list of drivers is
 * fetched from the backend, when constructing DriverDetailsSummary from new
 * DriverDetails, and when requesting the full DriverDetails object.
 * @param driver - The driver object to set the displayName on, which can be
 * either DriverDetails or DriverDetailsSummary.
 * @param primaryIdentifier - The primary identifier (from operations
 * customConfig) to use to determine the appropriate displayName.
 */
export function setDriverDisplayNameFromCustomConfig(
  driver: DriverDetails | DriverDetailsSummary,
  primaryIdentifier?: PrimaryDriverIdentifier | null,
): void {
  const identifier = primaryIdentifier || PrimaryDriverIdentifier.DRIVER_NAME;

  // Set displayName according to the primary identifier
  if (identifier === PrimaryDriverIdentifier.ASSIGNED_ID) {
    driver.displayName = driver.assignedId ?? 'N/A';
  } else if (identifier === PrimaryDriverIdentifier.ASSIGNED_ID_WITH_NAME) {
    driver.displayName = `${driver.assignedId ?? 'N/A'} - ${driver.name}`;
  } else {
    driver.displayName = driver.name;
  }
}
