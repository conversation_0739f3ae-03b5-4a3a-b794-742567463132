import {
  returnEndOfDayFromEpoch,
  returnNaturalDuration,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import { DriverNotificationType } from '@/interface-models/Generic/DriverLicenseTypes/DriverNotificationType';
import { DriverAppNotification } from '@/interface-models/Generic/DriverOnlineStatus/DriverAppNotification';
import JobProgressAlert from '@/interface-models/Jobs/JobProgressAlert/JobProgressAlert';
import { OperationJobSummary } from '@/interface-models/Jobs/OperationJobSummary';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';

export enum VacantTruckGroupType {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export interface VacantTruckGroup {
  type: VacantTruckGroupType;
  name: string;
  listItems: VacantTruckSummary[];
}

export interface VacantTruckSummary {
  fleetAssetId: string;
  csrAssignedId: string;
  driverId: string;
  driverName: string;
  secondaryDisplayName: string;
  clearedJobId: number | null;
  clearedJobDisplayId: string;
  clearedAtEpochTime: number;
  readableDurationClear: string;
  locationClear: string;
}

/**
 * Columns:
 * - CSR assigned id
 * - Duration they've been CLEAR
 * - Location they became clear
 *
 * @param allFleetAssets - The list of all fleet assets
 * @param allDrivers - The list of all drivers
 * @param jobProgressAlerts - The list of
 */
export function generateVacantTruckTableData({
  allFleetAssets,
  allDrivers,
  jobProgressAlerts,
  jobList,
  onlineEventsMap,
}: {
  allFleetAssets: FleetAssetSummary[];
  allDrivers: DriverDetailsSummary[];
  jobProgressAlerts: JobProgressAlert[];
  jobList: OperationJobSummary[];
  onlineEventsMap: Map<string, DriverAppNotification>;
}): VacantTruckGroup[] {
  const vacantTruckSummaries: VacantTruckSummary[] = [];
  const offlineFleetSummaries: VacantTruckSummary[] = [];
  const startOfToday = returnStartOfDayFromEpoch();
  const endOfToday = returnEndOfDayFromEpoch();

  const fleetAssetIdsWithWork = new Set<string>();
  const fleetAssetIdsWithClearedWork = new Set<string>();
  const driverIdsWithWork = new Set<string>();
  const driverIdsWithClearedWork = new Set<string>();
  const driverIdsWithOnlineOfflineStatuses = new Set<string>();

  // // --- 1. Clean up jobProgressAlerts with missing driver/fleet info ---
  // jobProgressAlerts.forEach((alert) => {
  //   if (!alert.driverId || !alert.fleetAssetId) {
  //     const matchingJob = mockData.get(alert.jobId);
  //     if (matchingJob) {
  //       alert.driverId = matchingJob.driverId;
  //       alert.fleetAssetId = matchingJob.fleetAssetId;
  //       alert.locationName =
  //         matchingJob.pudItems[matchingJob.pudItems.length - 1].address.suburb;
  //     }
  //   }
  // });

  // If there is any allocated work that is not complete, then this vehicle is not vacant
  jobList.forEach((job) => {
    const isAllocated =
      job.workStatus >= WorkStatus.ALLOCATED &&
      job.workStatus < WorkStatus.DRIVER_COMPLETED;

    const isToday = job.date >= startOfToday && job.date < endOfToday;

    if (isAllocated && isToday) {
      fleetAssetIdsWithWork.add(job.fleetAssetId);
      driverIdsWithWork.add(job.driverId);
    }
  });

  // --- 1. Find valid fleet assets with active drivers ---
  const activeDrivers = allDrivers.filter((d) => d.isActive);
  const validFleetAssets = allFleetAssets
    .filter(
      (asset) =>
        asset.isTruck && asset.isActiveForAllocation && !asset.outsideHire,
    )
    .filter((asset) =>
      activeDrivers.some((driver) =>
        asset.associatedDrivers.includes(driver.driverId),
      ),
    );

  // --- 2. For each valid fleet asset, find most recent completed job ---
  validFleetAssets.forEach((asset) => {
    // If there is any allocated work that is not complete, then this vehicle is not vacant
    if (fleetAssetIdsWithWork.has(asset.fleetAssetId)) {
      return;
    }

    // Find the most recent completed job alert for this asset
    const completedAlerts = jobProgressAlerts
      .filter(
        (alert) =>
          alert.fleetAssetId === asset.fleetAssetId &&
          alert.updatedStatus === 'CompletedJob',
      )
      .sort((a, b) => b.epochTime - a.epochTime);

    const mostRecentCompletedJobAlert = completedAlerts[0];
    if (!mostRecentCompletedJobAlert) {
      return;
    }

    const foundDriver = allDrivers.find(
      (driver) => driver.driverId === mostRecentCompletedJobAlert.driverId,
    );
    const foundJob = jobList.find(
      (job) => job.jobId === mostRecentCompletedJobAlert.jobId,
    );

    // Add a value to the sets so we can check them later
    fleetAssetIdsWithClearedWork.add(asset.fleetAssetId);
    driverIdsWithClearedWork.add(foundDriver?.driverId || '');

    // Construct table row
    vacantTruckSummaries.push({
      fleetAssetId: asset.fleetAssetId,
      csrAssignedId: asset.csrAssignedId,
      driverId: foundDriver?.driverId || '',
      driverName: foundDriver?.displayName || 'Unknown Driver',
      clearedJobId: mostRecentCompletedJobAlert.jobId,
      clearedJobDisplayId: foundJob?.displayId ? `${foundJob.displayId}` : '',
      secondaryDisplayName: asset.registrationNumber,
      clearedAtEpochTime: mostRecentCompletedJobAlert.epochTime,
      readableDurationClear: returnNaturalDuration(
        mostRecentCompletedJobAlert.epochTime,
      ),
      locationClear: mostRecentCompletedJobAlert.locationName || 'Unknown',
    });
  });

  // --- 3. Track most recent online/offline events for each driver ---
  const mostRecentOnlineEvents = new Map<string, DriverAppNotification>();
  const mostRecentOfflineEvents = new Map<string, DriverAppNotification>();

  // Place each driver in the correct map based on their most recent event type
  onlineEventsMap.forEach((event, driverId) => {
    // Don't display anything for drivers that are allocated to jobs, or drivers
    // that will appear because they have cleared jobs
    if (
      driverIdsWithWork.has(event.driverId) ||
      driverIdsWithClearedWork.has(event.driverId)
    ) {
      return;
    }
    if (event.notificationType === DriverNotificationType.ONLINE) {
      mostRecentOnlineEvents.set(driverId, event);
    } else if (event.notificationType === DriverNotificationType.OFFLINE) {
      mostRecentOfflineEvents.set(driverId, event);
    }
    driverIdsWithOnlineOfflineStatuses.add(driverId);
  });

  // --- 4. Add drivers with online events not already in vacantTruckSummaries ---
  mostRecentOnlineEvents.forEach((event, driverId) => {
    const foundDriver = allDrivers.find((d) => d.driverId === driverId);
    vacantTruckSummaries.push({
      fleetAssetId: '',
      csrAssignedId: '-',
      secondaryDisplayName: '',
      driverId: foundDriver?.driverId || '',
      driverName: foundDriver?.displayName || 'Unknown Driver',
      clearedJobId: null,
      clearedJobDisplayId: '',
      clearedAtEpochTime: event.epochTime,
      readableDurationClear: returnNaturalDuration(event.epochTime),
      locationClear: 'Logged in',
    });
  });

  // ---- 5. Construct lists for offline entities ---
  // Fleet assets that weren't included in previous lists
  validFleetAssets
    // Filter out assets with work or cleared work
    .filter(
      (asset) =>
        !fleetAssetIdsWithWork.has(asset.fleetAssetId) &&
        !fleetAssetIdsWithClearedWork.has(asset.fleetAssetId),
    )
    .forEach((asset) => {
      offlineFleetSummaries.push({
        fleetAssetId: asset.fleetAssetId,
        csrAssignedId: `${asset.csrAssignedId} (${asset.truckClass})`,
        secondaryDisplayName: '',
        driverId: '',
        driverName: '',
        clearedJobId: null,
        clearedJobDisplayId: '',
        clearedAtEpochTime: 0,
        readableDurationClear: '',
        locationClear: 'No allocated work today',
      });
    });

  // Drivers that have offline events
  mostRecentOfflineEvents.forEach((event, driverId) => {
    const foundDriver = allDrivers.find((d) => d.driverId === driverId);
    offlineFleetSummaries.push({
      fleetAssetId: '',
      csrAssignedId: '-',
      secondaryDisplayName: '',
      driverId: foundDriver?.driverId || '',
      driverName: foundDriver?.displayName || 'Unknown Driver',
      clearedJobId: null,
      clearedJobDisplayId: '',
      clearedAtEpochTime: event.epochTime,
      readableDurationClear: `Online ${returnNaturalDuration(event.epochTime)}`,
      locationClear: 'Offline',
    });
  });

  // Drivers with no work, progress alerts or online/offline events
  allDrivers.forEach((driver) => {
    if (!driverIdsWithOnlineOfflineStatuses.has(driver.driverId)) {
      offlineFleetSummaries.push({
        fleetAssetId: '',
        csrAssignedId: '-',
        secondaryDisplayName: '',
        driverId: driver.driverId || '',
        driverName: driver.displayName,
        clearedJobId: null,
        clearedJobDisplayId: '',
        clearedAtEpochTime: 0,
        readableDurationClear: '',
        locationClear: 'No allocated work today',
      });
    }
  });

  // Construct list of VacantTruckGroup objects and return (filter out empty)
  return [
    {
      type: VacantTruckGroupType.ACTIVE,
      name: 'Vacant Vehicles / Drivers',
      listItems: vacantTruckSummaries.sort(
        (a, b) => a.clearedAtEpochTime - b.clearedAtEpochTime,
      ),
    },
    {
      type: VacantTruckGroupType.INACTIVE,
      name: 'Offline / Inactive',
      listItems: offlineFleetSummaries,
    },
  ].filter((group) => group.listItems.length > 0);
}
