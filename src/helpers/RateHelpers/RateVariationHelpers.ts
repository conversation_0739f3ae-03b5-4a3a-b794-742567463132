import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { ClientServiceRateVariations } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariations';

/**
 * Retrieves the client adjustment percentage for a specific service and rate type from a list of client service rate variations.
 *
 * @param serviceTypeId - The ID of the service type to look up.
 * @param rateTypeId - The ID of the rate type to look up.
 * @param rateVariations - An array of client service rate variations to search within.
 * @returns The client adjustment percentage if found; otherwise, returns 0.
 */
export function getClientVariationPercent({
  serviceTypeId,
  rateTypeId,
  rateVariations,
}: {
  serviceTypeId: number;
  rateTypeId: number;
  rateVariations: ClientServiceRateVariations[];
}) {
  return (
    getRateVariation({ serviceTypeId, rateTypeId, rateVariations })
      ?.clientAdjustmentPercentage ?? 0
  );
}

/**
 * Retrieves the fleet asset adjustment percentage for a given service type and rate type
 * from a list of client service rate variations.
 *
 * @param serviceTypeId - The ID of the service type to look up.
 * @param rateTypeId - The ID of the rate type to look up.
 * @param rateVariations - An array of client service rate variations to search within.
 * @returns The fleet asset adjustment percentage if found; otherwise, returns 0.
 */
export function getFleetVariationPercent({
  serviceTypeId,
  rateTypeId,
  rateVariations,
}: {
  serviceTypeId: number;
  rateTypeId: number;
  rateVariations: ClientServiceRateVariations[];
}) {
  return (
    getRateVariation({ serviceTypeId, rateTypeId, rateVariations })
      ?.fleetAssetAdjustmentPercentage ?? 0
  );
}

/**
 * Retrieves a specific rate variation from a list based on the provided service
 * type ID and rate type ID. The matching logic is as follows:
 *   1. If rateTypeId === JobRateType.UNIT, only return a variation where rateTypeId matches and serviceTypeId is null or undefined.
 *   2. Otherwise:
 *      a. Try to find an exact match where both serviceTypeId and rateTypeId match.
 *      b. If not found, try to find a variation where rateTypeId matches and serviceTypeId is null or undefined.
 *      c. If still not found, try to find a variation where serviceTypeId matches and rateTypeId is null or undefined.
 *      d. If still not found, try to find a variation where BOTH serviceTypeId and rateTypeId are null or undefined.
 *
 * @param serviceTypeId - The unique identifier for the service type to match.
 * @param rateTypeId - The unique identifier for the rate type to match.
 * @param rateVariations - An array of `ClientServiceRateVariations` to search within.
 * @returns The matching `ClientServiceRateVariations` object if found; otherwise, `undefined`.
 */
export function getRateVariation({
  serviceTypeId,
  rateTypeId,
  rateVariations,
}: {
  serviceTypeId: number;
  rateTypeId: JobRateType;
  rateVariations: ClientServiceRateVariations[];
}): ClientServiceRateVariations | undefined {
  // Helper: checks if a variation has a combination matching given criteria
  const hasCombination = (
    variation: ClientServiceRateVariations,
    serviceCheck: (sid: number | null | undefined) => boolean,
    rateCheck: (rid: JobRateType | null | undefined) => boolean,
  ): boolean => {
    return variation.serviceRateCombinations.some(
      (combo) =>
        serviceCheck(combo.serviceTypeId) && rateCheck(combo.rateTypeId),
    );
  };

  // If rateTypeId === JobRateType.UNIT, only perform the rateTypeId-only lookup
  if (rateTypeId === JobRateType.UNIT) {
    return rateVariations.find((variation) =>
      hasCombination(
        variation,
        () => true, // ignore serviceType
        (rid) => rid === rateTypeId,
      ),
    );
  }

  // 1. Try to find an exact match
  let result = rateVariations.find((variation) =>
    hasCombination(
      variation,
      (sid) => sid === serviceTypeId,
      (rid) => rid === rateTypeId,
    ),
  );
  if (result) {
    return result;
  }

  // 2. Try to find a match where rateTypeId matches and serviceTypeId is null/undefined
  result = rateVariations.find((variation) =>
    hasCombination(
      variation,
      (sid) => sid === null || sid === undefined,
      (rid) => rid === rateTypeId,
    ),
  );
  if (result) {
    return result;
  }

  // 3. Try to find a match where serviceTypeId matches and rateTypeId is null/undefined
  result = rateVariations.find((variation) =>
    hasCombination(
      variation,
      (sid) => sid === serviceTypeId,
      (rid) => rid === null || rid === undefined,
    ),
  );
  if (result) {
    return result;
  }

  // 4. Try to find a match where BOTH serviceTypeId and rateTypeId are null/undefined
  result = rateVariations.find((variation) =>
    hasCombination(
      variation,
      (sid) => sid === null || sid === undefined,
      (rid) => rid === null || rid === undefined,
    ),
  );
  return result;
}
