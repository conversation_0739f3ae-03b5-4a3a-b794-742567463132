import {
  getPercentageOf,
  RoundCurrencyValue,
} from '@/helpers/AccountingHelpers/CurrencyHelpers';
import {
  ClientRateInfo,
  setDemurrageDurationsAndTotals,
} from '@/helpers/RateHelpers/DemurrageHelpers';
import { isFuelSurchargeApplicable } from '@/helpers/RateHelpers/FuelSurchargeHelpers';
import { isDistanceRateTypeObject } from '@/helpers/RateHelpers/RateTableItemHelpers';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import JobStatusUpdate from '@/interface-models/Jobs/Event/JobStatusUpdate';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { DemurrageRateData } from '@/interface-models/ServiceRates/Demurrage/DemurrageRateData';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import { DistanceRateType } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/DistanceRateType';
import { TravelDelay } from '@/interface-models/ServiceRates/TravelDelay/TravelDelay';
import { TravelDelayRateData } from '@/interface-models/ServiceRates/TravelDelay/TravelDelayRateData';

/**
 * Generates an array of `TravelDelayRateData` objects representing travel delay segments
 * between PUD (Pick Up/Drop Off) items for a given job, based on the provided rate, job details,
 * GST registration status, and other parameters. This function calculates travel durations,
 * applies existing user-entered demurrage durations if available, and computes travel delay
 * charges according to the rate type and entity type (client or fleet asset).
 *
 * - If the rate is not a distance rate or does not support travel delays, returns `undefined`.
 * - For each travel segment, determines the start and end times based on job status and PUD events.
 * - If existing travel delay data is provided, reuses demurrage durations where possible.
 * - Delegates further calculation to `generateDistanceTravelDelays` for distance rates.
 *
 * @param params - An object containing all required parameters:
 *   - `rate`: The rate table item to use for calculations.
 *   - `jobDetails`: The job details including PUD items and status.
 *   - `gstRegistered`: Whether GST is applicable.
 *   - `existingTravelDelayRateData`: Optional, existing travel delay data to reuse user-entered values.
 *   - `type`: The rate entity type (`CLIENT` or `FLEET_ASSET`).
 *   - `clientRateInfo`: Client rate info (required for `FLEET_ASSET`, null for `CLIENT`).
 *   - `variancePct`: Optional, variance percentage to apply to rates.
 *
 * @returns An array of `TravelDelayRateData` objects representing each travel delay segment,
 *          or `undefined` if travel delays are not applicable for the given rate.
 */
export function generateTravelDelays(
  params:
    | {
        rate: RateTableItems;
        jobDetails: JobDetails;
        gstRegistered: boolean;
        existingTravelDelayRateData: TravelDelayRateData[] | undefined;
        type: RateEntityType.CLIENT;
        clientRateInfo: null;
        variancePct: number | null | undefined;
      }
    | {
        rate: RateTableItems;
        jobDetails: JobDetails;
        gstRegistered: boolean;
        existingTravelDelayRateData: TravelDelayRateData[] | undefined;
        type: RateEntityType.FLEET_ASSET;
        clientRateInfo: ClientRateInfo;
        variancePct: number | null | undefined;
      },
): TravelDelayRateData[] | undefined {
  const {
    rate,
    jobDetails,
    gstRegistered,
    existingTravelDelayRateData,
    type,
    clientRateInfo,
    variancePct,
  } = params;

  let travelDelayRateData: TravelDelayRateData[] = [];

  // If not a distance rate, then do not generate travel delays
  if (
    !isDistanceRateTypeObject(rate.rateTypeId, rate.rateTypeObject) ||
    !rate.rateTypeObject.travelDelay
  ) {
    return undefined;
  }

  jobDetails.pudItems.forEach((pud, pudIndex, pudItems) => {
    let travelStartTime: number | undefined = undefined;
    let travelEndTime: number | undefined = undefined;

    // If job is before DRIVER COMPLETED status, use epoch + loadTime to get
    // the times.
    if (jobDetails.workStatus < WorkStatus.DRIVER_COMPLETED) {
      travelStartTime = pud.epochTime + pud.loadTime;
      if (pudItems[pudIndex + 1]) {
        travelEndTime = pudItems[pudIndex + 1].epochTime;
      }
    } else {
      // Get departure event of current pudItem
      const currentPudFinishedEvent = jobDetails.returnSpecifiedEvent(
        'FINISHED',
        pud.pudId,
      );
      // Get arrival event of next pud
      let nextPudArrivedEvent: JobStatusUpdate | undefined = undefined;
      if (pudItems[pudIndex + 1]) {
        nextPudArrivedEvent = jobDetails.returnSpecifiedEvent(
          'ARRIVED',
          pudItems[pudIndex + 1].pudId,
        );
      }
      travelStartTime = currentPudFinishedEvent?.correctEventTime;
      travelEndTime = nextPudArrivedEvent?.correctEventTime;
    }
    if (!travelStartTime || !travelEndTime) {
      return;
    }

    const travelDurationInMilliseconds =
      Math.floor((travelEndTime - travelStartTime) / 60000) * 60000;

    let demurrageDurationInMilliseconds = 0;
    let graceDurationInMilliseconds = travelDurationInMilliseconds;

    // Check if there is existing rate data, so we can re-use any user-entered
    // durations
    if (
      existingTravelDelayRateData &&
      existingTravelDelayRateData.length === pudItems.length - 1
    ) {
      const foundExisting = existingTravelDelayRateData.find(
        (data) => data.pudId === pud.pudId || data.pudId === `${pudIndex}`,
      );
      if (foundExisting) {
        // Set the demurrage duration based on the existing rate data
        demurrageDurationInMilliseconds =
          foundExisting.demurrageDurationInMilliseconds;
        // Re-check that the demurrage duration isn't greater than the travel
        // duration (since the travel duration could have been recomputed)
        if (demurrageDurationInMilliseconds > travelDurationInMilliseconds) {
          demurrageDurationInMilliseconds = travelDurationInMilliseconds;
        }
        // Re-set the grace duration to the remaining time after demurrage
        graceDurationInMilliseconds =
          travelDurationInMilliseconds - demurrageDurationInMilliseconds;
      }
    }

    const travelDelayRateDataItem: TravelDelayRateData =
      new TravelDelayRateData({
        pudId: pud.pudId || `${pudIndex}`,
        appliedDemurrageId: -1,
        // Set the grace as the full amount of time travel time. This will be
        // updated as the user updates the demurrage duration
        graceDurationInMilliseconds: graceDurationInMilliseconds,
        totalDurationInMilliseconds: travelDurationInMilliseconds,
        demurrageDurationInMilliseconds: demurrageDurationInMilliseconds,
        demurrageChargeExclGst: 0,
        demurrageChargeGst: 0,
        demurrageChargeTotal: 0,
        demurrageFuelSurchargeApplies: false,
        startTimeInEpoch: travelStartTime,
        endTimeInEpoch: travelEndTime,
        rate: 0.0,
      });

    travelDelayRateData.push(travelDelayRateDataItem);
  });

  switch (rate.rateTypeId) {
    case JobRateType.DISTANCE:
      if (
        isDistanceRateTypeObject(rate.rateTypeId, rate.rateTypeObject) &&
        rate.rateTypeObject.travelDelay instanceof TravelDelay
      ) {
        if (type === RateEntityType.CLIENT) {
          travelDelayRateData = generateDistanceTravelDelays({
            type: RateEntityType.CLIENT,
            distanceRate: rate.rateTypeObject as DistanceRateType & {
              travelDelay: TravelDelay;
            },
            travelDelayRateData,
            gstRegistered,
            clientRateInfo: null,
            variancePct,
          });
        } else {
          travelDelayRateData = generateDistanceTravelDelays({
            type: RateEntityType.FLEET_ASSET,
            distanceRate: rate.rateTypeObject as DistanceRateType & {
              travelDelay: TravelDelay;
            },
            travelDelayRateData,
            gstRegistered,
            clientRateInfo: clientRateInfo,
            variancePct,
          });
        }
      }
      break;
  }
  return travelDelayRateData;
}

/**
 * Called from generateDemurrages when the rate type is DISTANCE to calculate
 * the demurrage charges based on the rateTypeObject (distanceRate)
 * @param type - The rate entity type.
 * @param distanceRate - The distance rate object, containing the demurrage
 * information
 * @param demurrageRateData - The demurrage rate data containing mappings to
 * pudIds
 * @param gstRegistered - Whether the entity is GST registered
 * @param clientRateInfo - The client rate information. Supplied only if the
 * entity is a fleet asset
 * @returns - a modified demurrageRateData array
 */
function generateDistanceTravelDelays(
  params:
    | {
        type: RateEntityType.CLIENT;
        distanceRate: DistanceRateType & { travelDelay: TravelDelay };
        travelDelayRateData: TravelDelayRateData[];
        gstRegistered: boolean;
        clientRateInfo: null;
        variancePct: number | null | undefined;
      }
    | {
        type: RateEntityType.FLEET_ASSET;
        distanceRate: DistanceRateType & { travelDelay: TravelDelay };
        travelDelayRateData: TravelDelayRateData[];
        gstRegistered: boolean;
        clientRateInfo: ClientRateInfo;
        variancePct: number | null | undefined;
      },
): TravelDelayRateData[] {
  const {
    type,
    distanceRate,
    travelDelayRateData,
    gstRegistered,
    clientRateInfo,
    variancePct,
  } = params;
  // Array of indexes to remove from our demurrage rate data. We remove these
  // items if the demurrage rate should not be applied
  const indexToRemove: number[] = [];

  travelDelayRateData.forEach((rateDataItem: TravelDelayRateData, index) => {
    // set the applied Demurrage Id
    rateDataItem.appliedDemurrageId =
      distanceRate.travelDelay.appliedDemurrageId;
    // set whether fuel surcharge is applied to demurrage charge.
    rateDataItem.demurrageFuelSurchargeApplies =
      distanceRate.travelDelay.demurrageFuelSurchargeApplies &&
      isFuelSurchargeApplicable(
        distanceRate.appliedFuelSurchargeId,
        type === RateEntityType.CLIENT
          ? null
          : clientRateInfo.clientFuelSurchargeRate,
      );
    // // set how much grace time will be allowed against the total demurrage duration.
    // rateDataItem.graceDurationInMilliseconds =
    //   distanceRate.travelDelay.graceTimeInMilliseconds;

    // Set rate based on the zone rate demurrage rate, plus any variance
    // percentage that may be applied to the rate.
    rateDataItem.rate = RoundCurrencyValue(
      distanceRate.travelDelay.rate +
        getPercentageOf(distanceRate.travelDelay.rate, variancePct ?? 0),
    );

    // If we are calculating fleet asset demurrages we utilise the applied grace
    // duration from the client and apply it to the fleet asset
    if (
      type === RateEntityType.FLEET_ASSET &&
      isDistanceRateTypeObject(
        clientRateInfo.clientRate.rateTypeId,
        clientRateInfo.clientRate.rateTypeObject,
      )
    ) {
      // Check if client demurrage is applied if the fleet assets applied
      // demurrageId is 4 (client demurrage exists)
      const demurrageAppliedIfClientDemurrageExists =
        rateDataItem.appliedDemurrageId === 4;
      if (
        demurrageAppliedIfClientDemurrageExists &&
        clientRateInfo.clientDemurrages
      ) {
        const clientDemurrage = clientRateInfo.clientDemurrages.find(
          (x: DemurrageRateData) => x.pudId === rateDataItem.pudId,
        );
        // If client does not have demurrage applied for this stop we set the
        // fleet assets appliedDemurrageId to 3 (don't apply)
        if (!clientDemurrage) {
          rateDataItem.appliedDemurrageId = 3;
        } else {
          rateDataItem.appliedDemurrageId = 1;
        }
      }
    }
    // Set the charge totals and other durations
    setDemurrageDurationsAndTotals(
      rateDataItem,
      distanceRate.travelDelay,
      gstRegistered,
      indexToRemove,
      index,
    );
  });
  // Remove travel delay rate data from array. This happens if the travel delay rate
  // should not be applied. Sort in descending order to avoid index shifting.
  for (const i of indexToRemove.sort((a, b) => b - a)) {
    travelDelayRateData.splice(i, 1);
  }
  return travelDelayRateData;
}
