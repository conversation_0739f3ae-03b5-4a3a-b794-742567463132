import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import { requestZoneToZoneRatesForPudItems } from '@/helpers/RateHelpers/ZoneToZoneRateHelpers';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import { ClientServiceRateVariations } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariations';
import FleetAssetFuelSurchargeRate from '@/interface-models/ServiceRates/FleetAssetOwner/AdditionalCharges/FuelSurcharge/FleetAssetFuelSurchargeRate';
import FleetAssetServiceRate from '@/interface-models/ServiceRates/FleetAssetOwner/FleetAssetServiceRate/FleetAssetServiceRate';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useFuelLevyStore } from '@/store/modules/FuelLevyStore';
import { useServiceRateStore } from '@/store/modules/ServiceRateStore';
import moment from 'moment-timezone';

export enum JobRateDataType {
  CLIENT_SERVICE_RATE,
  CLIENT_FUEL_SURCHARGE,
  FLEET_ASSET_SERVICE_RATE,
  FLEET_ASSET_FUEL_SURCHARGE,
  CLIENT_SERVICE_RATE_VARIATIONS,
}

export type JobRateData =
  | {
      type: JobRateDataType.CLIENT_SERVICE_RATE;
      data: ClientServiceRate | null;
      errorMessages?: string[];
    }
  | {
      type: JobRateDataType.CLIENT_FUEL_SURCHARGE;
      data: ClientFuelSurchargeRate[] | null;
      errorMessages?: string[];
    }
  | {
      type: JobRateDataType.FLEET_ASSET_SERVICE_RATE;
      data: FleetAssetServiceRate | null;
      errorMessages?: string[];
    }
  | {
      type: JobRateDataType.FLEET_ASSET_FUEL_SURCHARGE;
      data: FleetAssetFuelSurchargeRate[] | null;
      errorMessages?: string[];
    }
  | {
      type: JobRateDataType.CLIENT_SERVICE_RATE_VARIATIONS;
      data: ClientServiceRateVariations[] | null;
      errorMessages?: string[];
    };

export interface JobKeyRateData {
  clientServiceRate: JobRateData;
  clientFuelSurcharges: JobRateData;
  fleetAssetServiceRate: JobRateData;
  fleetAssetFuelSurcharges: JobRateData;
  clientServiceRateVariations?: JobRateData;
  errorMessages?: string[];
}

/**
 * Returns the correct search date for rate requests.
 */
function getRateRequestTime(searchDate?: number): number {
  return searchDate || moment().valueOf();
}

/**
 * Validates the passed in serviceRate object for the presence of a
 * rateTableItem matching the rateTypeId and serviceTypeId.
 * Also requests zone to zone rate table items if the rateTypeId is ZONE TO ZONE.
 */
async function validateRateCard(
  jobDetails: JobDetails,
  rateData:
    | { type: RateEntityType.CLIENT; serviceRate: ClientServiceRate }
    | { type: RateEntityType.FLEET_ASSET; serviceRate: FleetAssetServiceRate },
  rateTypeId: number,
  serviceTypeId: number,
): Promise<true | string> {
  if (rateTypeId === JobRateType.ZONE_TO_ZONE) {
    const zzRateTableItems = await requestZoneToZoneRatesForPudItems({
      type: rateData.type,
      entityId:
        rateData.type === RateEntityType.CLIENT
          ? jobDetails.client.id
          : jobDetails.fleetAssetId,
      pudItems: jobDetails.pudItems,
      serviceTypeId,
    });
    if (zzRateTableItems) {
      rateData.serviceRate.rateTableItems.push(...zzRateTableItems);
    }
  } else if (rateTypeId === JobRateType.TRIP) {
    return true;
  }

  let foundRate: RateTableItems | null = null;
  if (rateData.type === RateEntityType.CLIENT) {
    foundRate = rateData.serviceRate.rateToApplyToJob(
      serviceTypeId,
      rateTypeId,
    );
  } else {
    foundRate = rateData.serviceRate.rateToApplyToJob({
      serviceTypeId,
      rateTypeId,
      operationsCustomConfig:
        useCompanyDetailsStore().divisionCustomConfig?.operations,
    });
  }

  if (!foundRate) {
    return `No applicable rates found for ${
      rateData.type === RateEntityType.CLIENT ? 'Client' : 'Fleet Asset'
    }.`;
  }
  return true;
}

/**
 * Requests merged client service rate and handles response.
 * Populates clientServiceRate, clientFuelSurcharge, and clientServiceRateVariations in result.
 */
async function getClientRatesAndFuel(
  jobDetails: JobDetails,
  searchDate: number | undefined,
  result: JobKeyRateData,
): Promise<void> {
  const serviceRateStore = useServiceRateStore();

  const { clientRates, clientFuelRates, serviceRateVariations } =
    await serviceRateStore.getCurrentRatesAndFuelForClientId(
      jobDetails.client.id,
      getRateRequestTime(searchDate),
    );

  // Client Service Rate
  if (clientRates?.clientServiceRate) {
    result.clientServiceRate.data = clientRates.clientServiceRate;
    const isValid = await validateRateCard(
      jobDetails,
      {
        type: RateEntityType.CLIENT,
        serviceRate: result.clientServiceRate.data,
      },
      jobDetails.serviceTypeObject.rateTypeId,
      jobDetails.serviceTypeId,
    );
    if (isValid !== true) {
      result.clientServiceRate.errorMessages = [isValid];
    }
  } else {
    result.clientServiceRate.errorMessages = [
      'Client does not have any Service Rates available.',
    ];
  }

  // Client Fuel Surcharge
  if (clientFuelRates?.length) {
    result.clientFuelSurcharges.data = clientFuelRates;
  } else {
    result.clientFuelSurcharges.errorMessages = [
      'Client does not have any applicable Fuel Surcharges available.',
    ];
  }

  // Service Rate Variations
  if (result.clientServiceRateVariations) {
    result.clientServiceRateVariations.data = serviceRateVariations || [];
  }
}

/**
 * Requests merged fleet asset service rate and handles response.
 * Populates fleetAssetServiceRate and fleetAssetFuelSurcharge in result.
 */
async function getFleetAssetRatesAndFuel(
  jobDetails: JobDetails,
  searchDate: number | undefined,
  result: JobKeyRateData,
): Promise<void> {
  const serviceRateStore = useServiceRateStore();
  const fuelLevyStore = useFuelLevyStore();

  const response = await serviceRateStore.getMergedFleetAssetServiceRates(
    jobDetails.fleetAssetId,
    getRateRequestTime(searchDate),
  );

  const fleetAssetRates = jobDetails.accounting.fleetAssetRates[0];
  const fleetAssetRateId =
    fleetAssetRates?.rate?.rateTypeId ??
    jobDetails.serviceTypeObject.rateTypeId;
  const fleetAssetServiceRateId =
    fleetAssetRates?.rate?.serviceTypeId ?? jobDetails.serviceTypeId;

  // Fleet Asset Service Rate
  if (response?.fleetAssetServiceRate) {
    result.fleetAssetServiceRate.data = response.fleetAssetServiceRate;
    const isValid = await validateRateCard(
      jobDetails,
      {
        type: RateEntityType.FLEET_ASSET,
        serviceRate: result.fleetAssetServiceRate.data,
      },
      fleetAssetRateId,
      fleetAssetServiceRateId,
    );
    if (isValid !== true) {
      result.fleetAssetServiceRate.errorMessages = [isValid];
    }
  } else {
    result.fleetAssetServiceRate.errorMessages = [
      'Fleet Asset does not have any Service Rates available.',
    ];
  }

  // Fleet Asset Fuel Surcharge
  const currentFuelRate =
    await fuelLevyStore.getCurrentFleetAssetFuelSurchargeRates(
      jobDetails.fleetAssetId,
      getRateRequestTime(searchDate),
    );
  if (currentFuelRate) {
    result.fleetAssetFuelSurcharges.data = currentFuelRate;
  } else {
    result.fleetAssetFuelSurcharges.errorMessages = [
      'Fleet Asset does not have an applicable Fuel Surcharge available.',
    ];
  }
}

/**
 * Retrieves all service rates and fuel surcharges for a job.
 * Returns a JobKeyRateData object with error messages if any rates are missing.
 * Throws on timeout or unexpected error.
 */
export async function retrieveJobRateDataForPricing(
  jobDetails: JobDetails,
  searchDate?: number,
  timeoutMs = 10000,
): Promise<JobKeyRateData> {
  // The result object that will be returned, containing all rate data and error messages
  const result: JobKeyRateData = {
    clientServiceRate: {
      type: JobRateDataType.CLIENT_SERVICE_RATE,
      data: null,
      errorMessages: [],
    },
    clientFuelSurcharges: {
      type: JobRateDataType.CLIENT_FUEL_SURCHARGE,
      data: null,
      errorMessages: [],
    },
    fleetAssetServiceRate: {
      type: JobRateDataType.FLEET_ASSET_SERVICE_RATE,
      data: null,
      errorMessages: [],
    },
    fleetAssetFuelSurcharges: {
      type: JobRateDataType.FLEET_ASSET_FUEL_SURCHARGE,
      data: null,
      errorMessages: [],
    },
    clientServiceRateVariations: {
      type: JobRateDataType.CLIENT_SERVICE_RATE_VARIATIONS,
      data: null,
      errorMessages: [],
    },
    errorMessages: [],
  };

  // Timeout logic: If the rate retrieval takes too long, reject with error
  let timeoutHandle: ReturnType<typeof setTimeout> | undefined;
  const timeoutPromise = new Promise<never>((_, reject) => {
    timeoutHandle = setTimeout(() => {
      logConsoleError(
        'ServiceRateRetriever took too long to receive response from service.',
        result,
      );
      result.errorMessages = [
        'Timeout retrieving service rates. Please try again later.',
      ];
      reject(new Error('Timeout retrieving service rates'));
    }, timeoutMs);
  });

  try {
    // If job is allocated, request both client and fleet asset rates
    // Otherwise, only request client rates
    const isAllocated = !!jobDetails.fleetAssetId;
    const mainPromise = isAllocated
      ? Promise.all([
          getClientRatesAndFuel(jobDetails, searchDate, result),
          getFleetAssetRatesAndFuel(jobDetails, searchDate, result),
        ])
      : getClientRatesAndFuel(jobDetails, searchDate, result);

    await Promise.race([mainPromise, timeoutPromise]);
    if (timeoutHandle) {
      clearTimeout(timeoutHandle);
    }
    return result;
  } catch (err) {
    if (timeoutHandle) {
      clearTimeout(timeoutHandle);
    }
    // Return the result with error messages
    return result;
  }
}
