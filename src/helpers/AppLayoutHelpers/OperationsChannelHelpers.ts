import { LOCAL_STORAGE_CUSTOM_OPERATIONS_CHANNEL } from '@/helpers/AppLayoutHelpers/LocalStorageHelpers';
import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import type { OperationJobSummary } from '@/interface-models/Jobs/OperationJobSummary';
import {
  FilterByValues,
  FilterMode,
} from '@/interface-models/OperationsChannels/FilterByValues';
import { OperationsChannel } from '@/interface-models/OperationsChannels/OperationsChannel';
import { OperationsChannelFleetFilterOptions } from '@/interface-models/OperationsChannels/OperationsChannelFleetFilterOptions';
import { OperationsChannelJobFilterOptions } from '@/interface-models/OperationsChannels/OperationsChannelJobFilterOptions';
import { sessionManager } from '@/store/session/SessionState';

export enum OperationsChannelLevel {
  DIVISION = 'DIVISION',
  USER = 'USER',
}

export function filterMatches<T>(
  filter: FilterByValues<T> | null,
  value: T,
): boolean {
  if (filter === null || filter.isAny) {
    return true;
  }
  if (filter.isNone && !!value) {
    return false;
  }
  const values = filter.values;
  return values?.includes(value) ?? false;
}

/**
 * Combine two or more SetFilters into a single filter.
 *
 * Rules:
 * - ANY + ANY → ANY
 * - NONE + ANY → NONE (NONE wins)
 * - NONE + NONE → NONE
 * - ONLY + ANY → ONLY (keep values)
 * - ONLY + NONE → NONE (NONE wins)
 * - ONLY + ONLY → ONLY (intersection of values)
 *
 * Empty ONLY lists will behave as "accept nothing".
 */
export function combineSetFilters<T>(
  ...filters: (FilterByValues<T> | undefined)[]
): FilterByValues<T> {
  // Remove undefined filters
  const validFilters = filters.filter(
    (f): f is FilterByValues<T> => f !== undefined,
  );

  if (validFilters.length === 0) {
    // Default to ANY (no restriction)
    return new FilterByValues({ mode: FilterMode.ANY, values: [] });
  }

  // Start with the first filter as the "accumulator"
  return validFilters.reduce((acc, current) => {
    if (acc.mode === FilterMode.NONE || current.mode === FilterMode.NONE) {
      // If any filter says "NONE", the result is NONE
      return new FilterByValues({ mode: FilterMode.NONE, values: [] });
    }

    if (acc.mode === FilterMode.ANY && current.mode === FilterMode.ANY) {
      return new FilterByValues({ mode: FilterMode.ANY, values: [] });
    }

    if (acc.mode === FilterMode.ANY && current.mode === FilterMode.ONLY) {
      return new FilterByValues({
        mode: FilterMode.ONLY,
        values: current.values,
      });
    }

    if (acc.mode === FilterMode.ONLY && current.mode === FilterMode.ANY) {
      return new FilterByValues({
        mode: FilterMode.ONLY,
        values: acc.values,
      });
    }

    if (acc.mode === FilterMode.ONLY && current.mode === FilterMode.ONLY) {
      // Union of values
      const union = Array.from(
        new Set([...(acc.values ?? []), ...(current.values ?? [])]),
      );
      return new FilterByValues({
        mode: FilterMode.ONLY,
        values: union,
      });
    }

    return acc;
  });
}

/**
 * Merge two arrays safely, avoiding duplicates.
 */
function mergeArrays<T>(a?: T[], b?: T[]): T[] | undefined {
  if (!a && !b) {
    return undefined;
  }
  const set = new Set([...(a ?? []), ...(b ?? [])]);
  return Array.from(set);
}

/**
 * Merge two job filter options.
 */
function mergeJobFilterOptions(
  a: OperationsChannelJobFilterOptions,
  b: OperationsChannelJobFilterOptions,
): OperationsChannelJobFilterOptions {
  return {
    clientFilter: b.clientFilter ?? a.clientFilter, // overwrite if provided
    serviceTypeIds: combineSetFilters(a.serviceTypeIds, b.serviceTypeIds),
    rateTypeIds: combineSetFilters(a.rateTypeIds, b.rateTypeIds),
  };
}

/**
 * Merge two fleet filter options.
 */
function mergeFleetFilterOptions(
  a: OperationsChannelFleetFilterOptions,
  b: OperationsChannelFleetFilterOptions,
): OperationsChannelFleetFilterOptions {
  return {
    clientFilter: b.clientFilter ?? a.clientFilter,
    fleetAssetTypeIds: combineSetFilters(
      a.fleetAssetTypeIds,
      b.fleetAssetTypeIds,
    ),
    vehicleClasses: combineSetFilters(a.vehicleClasses, b.vehicleClasses),
    ownerAffiliations: combineSetFilters(
      a.ownerAffiliations,
      b.ownerAffiliations,
    ),
    includeFleetAssetIds: mergeArrays(
      a.includeFleetAssetIds,
      b.includeFleetAssetIds,
    ),
    excludeFleetAssetIds: mergeArrays(
      a.excludeFleetAssetIds,
      b.excludeFleetAssetIds,
    ),
    includeDriverIds: mergeArrays(a.includeDriverIds, b.includeDriverIds),
    excludeDriverIds: mergeArrays(a.excludeDriverIds, b.excludeDriverIds),
  };
}
/**
 * Combine a list of OperationsChannel objects into one.
 * Later channels in the array override or augment earlier ones.
 */
export function combineOperationsChannels(
  channels: OperationsChannel[],
): OperationsChannel {
  if (channels.length === 0) {
    throw new Error('No OperationsChannel documents provided to combine.');
  }
  return channels.reduce((acc, curr) => mergeOperationsChannels(acc, curr));
}

/**
 * Merge two full OperationsChannel objects.
 * "b" overrides or augments "a".
 */
export function mergeOperationsChannels(
  a: OperationsChannel,
  b: OperationsChannel,
): OperationsChannel {
  return new OperationsChannel({
    _id: '',
    company: b.company ?? a.company,
    division: b.division ?? a.division,
    companyUserId: sessionManager.getUserId(), // Always set to user for merged channels
    name: 'Multi Channel',
    jobFilterOptions: mergeJobFilterOptions(
      a.jobFilterOptions,
      b.jobFilterOptions,
    ),
    fleetFilterOptions: mergeFleetFilterOptions(
      a.fleetFilterOptions,
      b.fleetFilterOptions,
    ),
  });
}

export function getCustomChannelFromLocalStorage(): OperationsChannel | null {
  const json = localStorage.getItem(LOCAL_STORAGE_CUSTOM_OPERATIONS_CHANNEL);
  if (!json) {
    return null;
  }
  try {
    const data = JSON.parse(json);
    const channel = new OperationsChannel(data);
    channel.isLocalChannel = true;
    return channel;
  } catch {
    return null;
  }
}

export enum ChannelFilterType {
  JOB,
  FLEET,
}
export enum ChannelJobFilterOptions {
  CLIENT,
  NATIONAL_CLIENT,
  SERVICE_TYPES,
  RATE_TYPES,
}

export enum ChannelFleetFilterOptions {
  CLIENT,
  NATIONAL_CLIENT,
  FLEET_ASSET_TYPE,
  VEHICLE_CLASS,
  OWNER_AFFILIATION,
  INCLUDE_EXCLUDE_FLEET,
  INCLUDE_EXCLUDE_DRIVERS,
}
/**
 * Toggles a specific filter option ON or OFF for a given operations channel.
 *
 * Depending on the filter type (`JOB` or `FLEET`) and the `on` parameter, this
 * function sets the corresponding filter option's `isAny` (OFF) or `isOnly`
 * (ON) property to `true`, or deletes specific inclusion filter properties.
 *
 * @param params - An object containing the channel, filter type, filter option,
 * and whether to toggle ON or OFF.
 * - If `filterType` is `ChannelFilterType.JOB`, `option` must be a
 *   `ChannelJobFilterOptions`.
 * - If `filterType` is `ChannelFilterType.FLEET`, `option` must be a
 *   `ChannelFleetFilterOptions`.
 * - `on` determines whether to toggle ON (`isOnly = true`) or OFF (`isAny =
 *   true` or delete).
 *
 * @remarks
 * - For certain fleet filter options (`INCLUDE_EXCLUDE_FLEET` and
 *   `INCLUDE_EXCLUDE_DRIVERS`), toggling OFF deletes the corresponding
 *   properties.
 * - For other options, toggling ON sets `isOnly = true`, toggling OFF sets
 *   `isAny = true`.
 */
export function toggleChannelFilterOption(
  params:
    | {
        channel: OperationsChannel;
        filterType: ChannelFilterType.JOB;
        option: ChannelJobFilterOptions;
        on: boolean;
      }
    | {
        channel: OperationsChannel;
        filterType: ChannelFilterType.FLEET;
        option: ChannelFleetFilterOptions;
        on: boolean;
      },
) {
  const { channel, filterType, option, on } = params;
  if (filterType === ChannelFilterType.JOB) {
    switch (option) {
      case ChannelJobFilterOptions.CLIENT:
        channel.jobFilterOptions.clientFilter.clientIds.isAny = !on;
        channel.jobFilterOptions.clientFilter.clientIds.isOnly = on;
        break;
      case ChannelJobFilterOptions.NATIONAL_CLIENT:
        channel.jobFilterOptions.clientFilter.nationalClientIds.isAny = !on;
        channel.jobFilterOptions.clientFilter.nationalClientIds.isOnly = on;
        break;
      case ChannelJobFilterOptions.SERVICE_TYPES:
        channel.jobFilterOptions.serviceTypeIds.isAny = !on;
        channel.jobFilterOptions.serviceTypeIds.isOnly = on;
        break;
      case ChannelJobFilterOptions.RATE_TYPES:
        channel.jobFilterOptions.rateTypeIds.isAny = !on;
        channel.jobFilterOptions.rateTypeIds.isOnly = on;
        break;
    }
  } else if (filterType === ChannelFilterType.FLEET) {
    switch (option) {
      case ChannelFleetFilterOptions.CLIENT:
        channel.fleetFilterOptions.clientFilter.clientIds.isAny = !on;
        channel.fleetFilterOptions.clientFilter.clientIds.isOnly = on;
        break;
      case ChannelFleetFilterOptions.NATIONAL_CLIENT:
        channel.fleetFilterOptions.clientFilter.nationalClientIds.isAny = !on;
        channel.fleetFilterOptions.clientFilter.nationalClientIds.isOnly = on;
        break;
      case ChannelFleetFilterOptions.FLEET_ASSET_TYPE:
        channel.fleetFilterOptions.fleetAssetTypeIds.isAny = !on;
        channel.fleetFilterOptions.fleetAssetTypeIds.isOnly = on;
        break;
      case ChannelFleetFilterOptions.VEHICLE_CLASS:
        channel.fleetFilterOptions.vehicleClasses.isAny = !on;
        channel.fleetFilterOptions.vehicleClasses.isOnly = on;
        break;
      case ChannelFleetFilterOptions.OWNER_AFFILIATION:
        channel.fleetFilterOptions.ownerAffiliations.isAny = !on;
        channel.fleetFilterOptions.ownerAffiliations.isOnly = on;
        break;
      case ChannelFleetFilterOptions.INCLUDE_EXCLUDE_FLEET:
        if (!on) {
          delete channel.fleetFilterOptions.includeFleetAssetIds;
        } else {
          channel.fleetFilterOptions.includeFleetAssetIds =
            channel.fleetFilterOptions.includeFleetAssetIds ?? [];
        }
        break;
      case ChannelFleetFilterOptions.INCLUDE_EXCLUDE_DRIVERS:
        if (!on) {
          delete channel.fleetFilterOptions.includeDriverIds;
        } else {
          channel.fleetFilterOptions.includeDriverIds =
            channel.fleetFilterOptions.includeDriverIds ?? [];
        }
        break;
    }
  }
}

/**
 * Filters a list of jobs based on filter criteria defined in an
 * OperationsChannel object.
 *
 * @param jobs - The list of jobs to be filtered.
 * @returns A filtered list of jobs matching the selected service type.
 */
export function filterJobsByOperationsChannel(
  jobs: OperationJobSummary[],
  operationsChannel: OperationsChannel | null,
): OperationJobSummary[] {
  // If selectedOperationsChannel is null, then it means we have no filter
  // applied. Return the full job list
  if (!operationsChannel) {
    return jobs;
  }

  const jobFilter = operationsChannel.jobFilterOptions;

  const filtered = jobs.filter((job) => {
    if (job.isVisibleInAllChannels) {
      return true;
    }
    const serviceTypeMatch = filterMatches(
      jobFilter.serviceTypeIds,
      job.serviceTypeId,
    );
    const rateTypeMatches = filterMatches(
      jobFilter.rateTypeIds,
      job.clientRateTypeId,
    );
    const clientIdMatches = filterMatches(
      jobFilter.clientFilter.clientIds,
      job.clientId,
    );
    const nationalClientIdMatches = filterMatches(
      jobFilter.clientFilter.nationalClientIds,
      job.nationalClientId,
    );

    return (
      serviceTypeMatch &&
      rateTypeMatches &&
      clientIdMatches &&
      nationalClientIdMatches
    );
  });

  return filtered;
}

/**
 * Filters a list of fleet assets based on the operations channel's fleet filter options.
 *
 * Filtering logic:
 * - If no filter is applied, all fleet assets are returned.
 * - Assets explicitly included via `includeFleetAssetIds` are always returned.
 * - Assets explicitly excluded via `excludeFleetAssetIds` are always omitted.
 * - Remaining assets are filtered by vehicle class and owner affiliation.
 *
 * @param fleet - The array of `FleetAssetSummary` objects to filter.
 * @param ownerAffiliationMap - Map of owner IDs to owner affiliation strings.
 * @param operationsChannel - The `OperationsChannel` object containing filter options, or `null` if no filter is applied.
 * @returns The filtered array of fleet assets according to the operations channel's fleet filter.
 */
export function isFleetAssetValidForChannel(
  fleet: FleetAssetSummary,
  ownerAffiliationMap: Map<string, string>,
  operationsChannel: OperationsChannel | null,
): boolean {
  if (!operationsChannel) {
    return true;
  }
  // Always include assets explicitly listed in includeFleetAssetIds
  const isIncluded =
    operationsChannel?.fleetFilterOptions?.includeFleetAssetIds?.includes(
      fleet.fleetAssetId,
    );
  if (isIncluded === true) {
    return true;
  }

  // Always exclude assets explicitly listed in excludeFleetAssetIds
  const isExcluded =
    operationsChannel?.fleetFilterOptions?.excludeFleetAssetIds?.includes(
      fleet.fleetAssetId,
    );
  if (isExcluded === true) {
    return false;
  }

  // Check if the fleet asset matches the selected vehicle class filter
  const isVehicleClassMatch = filterMatches(
    operationsChannel.fleetFilterOptions.vehicleClasses,
    fleet.truckClass,
  );

  // Check if the fleet asset matches the selected owner affiliation filter
  const isOwnerAffiliationMatch = filterMatches(
    operationsChannel.fleetFilterOptions.ownerAffiliations,
    ownerAffiliationMap.get(fleet.fleetAssetOwnerId) || '',
  );

  // Return true if either condition is satisfied
  return isVehicleClassMatch || isOwnerAffiliationMatch;
}

/**
 * Determines whether a given driver is valid for a specific operations channel.
 *
 * The function checks the following, in order:
 * 1. If `operationsChannel` is null, all drivers are considered valid.
 * 2. If the driver's ID is explicitly included in `includeDriverIds`, the driver is valid.
 * 3. If the driver's ID is explicitly excluded in `excludeDriverIds`, the driver is invalid.
 * 4. Otherwise, the driver is valid if at least one associated fleet asset passes the truck class and affiliation checks for the channel.
 *
 * @param driver - The driver details to validate.
 * @param allFleetAssets - The list of all fleet assets, used to check driver associations.
 * @param ownerAffiliationMap - A map of owner IDs to their affiliations.
 * @param operationsChannel - The operations channel to validate against, or `null` to allow all drivers.
 * @returns `true` if the driver is valid for the channel, `false` otherwise.
 */
export function isDriverValidForChannel(
  driver: DriverDetailsSummary,
  allFleetAssets: FleetAssetSummary[],
  ownerAffiliationMap: Map<string, string>,
  operationsChannel: OperationsChannel | null,
): boolean {
  if (!operationsChannel) {
    return true;
  }
  // Always include assets explicitly listed in includeDriverIds
  const isIncluded =
    operationsChannel?.fleetFilterOptions?.includeDriverIds?.includes(
      driver.driverId,
    );
  if (isIncluded === true) {
    return true;
  }

  // Always exclude assets explicitly listed in excludeDriverIds
  const isExcluded =
    operationsChannel?.fleetFilterOptions?.excludeDriverIds?.includes(
      driver.driverId,
    );
  if (isExcluded === true) {
    return false;
  }

  // If at least one vehicle passes the truck class and affiliation check, then
  // we display this driver
  return allFleetAssets
    .filter((fa) => fa.associatedDrivers.includes(driver.driverId))
    .some((fa) => {
      return isFleetAssetValidForChannel(
        fa,
        ownerAffiliationMap,
        operationsChannel,
      );
    });
}

/**
 * Creates and returns a new default `OperationsChannel` instance with
 * pre-configured filter options.
 *
 * The returned channel is initialized with:
 * - The current company and division IDs from the session manager.
 * - `jobFilterOptions` configured to allow any service type and rate type, and
 *   any client or national client.
 * - `fleetFilterOptions` configured to allow any fleet asset type and vehicle
 *   class, but client, national client and subcontractor groups hidden
 * - The `isDefaultChannel` flag set to `true`.
 *
 * @returns {OperationsChannel} A new default operations channel instance with
 * clean filter settings.
 */
export function returnCleanDefaultChannel(): OperationsChannel {
  return new OperationsChannel({
    company: sessionManager.getCompanyId(),
    division: sessionManager.getDivisionId(),
    name: 'All',
    jobFilterOptions: {
      clientFilter: {
        nationalClientIds: new FilterByValues({
          mode: FilterMode.ANY,
        }),
        clientIds: new FilterByValues({
          mode: FilterMode.ANY,
        }),
      },
      serviceTypeIds: new FilterByValues({
        mode: FilterMode.ANY,
      }),
      rateTypeIds: new FilterByValues({
        mode: FilterMode.ANY,
      }),
    },
    fleetFilterOptions: {
      clientFilter: {
        nationalClientIds: new FilterByValues({
          mode: FilterMode.NONE,
        }),
        clientIds: new FilterByValues({
          mode: FilterMode.NONE,
        }),
      },
      fleetAssetTypeIds: new FilterByValues({
        mode: FilterMode.ANY,
      }),
      vehicleClasses: new FilterByValues({
        mode: FilterMode.ANY,
      }),
      ownerAffiliations: new FilterByValues({
        mode: FilterMode.NONE,
      }),
    },
    isDefaultChannel: true,
  });
}
