import Mitt from '@/utils/mitt';

/**
 * Requests user confirmation by displaying a confirmation dialog.
 *
 * Emits a `showInConfirmationDialog` event with the provided message and optional title.
 * The dialog should resolve the returned promise with `true` if the user confirms,
 * or `false` if the user cancels.
 *
 * @param message - The message to display in the confirmation dialog.
 * @param title - (Optional) The title to display in the confirmation dialog.
 * @returns A promise that resolves to `true` if the user confirms, or `false` otherwise.
 */
export function requestUserConfirmation(
  message: string,
  title?: string,
): Promise<boolean> {
  return new Promise((resolve) => {
    Mitt.emit('showInConfirmationDialog', { message, title, resolve });
  });
}
