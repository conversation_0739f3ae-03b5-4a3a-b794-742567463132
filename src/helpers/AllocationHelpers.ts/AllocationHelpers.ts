import { initialiseFleetAssetServiceRate } from '@/helpers/classInitialisers/InitialiseServiceRate';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import {
  BroadcastChannelType,
  BroadcastIds,
} from '@/helpers/NotificationHelpers/BroadcastChannelHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { isTripRateTableItem } from '@/helpers/RateHelpers/RateTableItemHelpers';
import { createTripRateTableItem } from '@/helpers/RateHelpers/TripRateHelpers';
import {
  returnRateTypeLongNameFromId,
  returnServiceTypeShortNameFromId,
} from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { DivisionOperationDetails } from '@/interface-models/Company/DivisionCustomConfig/Operations/DivisionOperationDetails';
import JobPrimaryRate from '@/interface-models/Generic/Accounting/JobPrimaryRate';
import AddNoteToJobRequest from '@/interface-models/Generic/Communication/AddNoteToJobRequest';
import Communication from '@/interface-models/Generic/Communication/Communication';
import JobCommunication from '@/interface-models/Generic/Communication/CommunicationTypes/JobCommunication';
import communicationVisibility from '@/interface-models/Generic/Communication/CommunicationVisibility';
import { BroadcastMessage } from '@/interface-models/Generic/OperationScreenOptions/BroadcastMessage';
import serviceTypeRates, {
  JobRateType,
} from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { AllocateJobRequest } from '@/interface-models/Jobs/Allocation/AllocateJobRequest';
import { JobEventSummary } from '@/interface-models/Jobs/JobEventSummary';
import { FleetAssetServiceRateResponse } from '@/interface-models/ServiceRates/FleetAssetOwner/FleetAssetServiceRate/FleetAssetServiceRateResponse';
import { AllocationRateRequest } from '@/interface-models/ServiceRates/RateRequest';
import type { RateTableItems } from '@/interface-models/ServiceRates/RateTableItems';
import { useAllocationStore } from '@/store/modules/AllocationStore';
import { useBroadcastChannelStore } from '@/store/modules/BroadcastChannelStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { sessionManager } from '@/store/session/SessionState';
import moment from 'moment-timezone';
import { v4 as uuidv4 } from 'uuid';
uuidv4();

// Default to TIME if no rate type is found
export const FALLBACK_RATE_TYPE_ID = JobRateType.TIME;

export enum ObjectToAllocate {
  JOB_DETAILS = 'JOB_DETAILS',
  PERMANENT_JOB = 'PERMANENT_JOB',
}

export enum OnValidAllocationTarget {
  SEND = 'SEND_REQUEST',
  EMIT = 'EMIT',
}

export interface OutsideHireDetails {
  driverName: string;
  contactNumber: string;
  registrationNumber: string;
}

/**
 * Requests the rates for the provided fleetAssetId, validates the result and
 * attempts to return the primary job rate matching the provided serviceTypeId
 * and rateTypeId. Supports window mode for rate fetching. Returns either a
 * JobPrimaryRate object if successful, or a string describing the error.
 *
 * @param {Object} params - The parameters for rate lookup.
 * @param {string} fleetAssetId - The ID of the fleet asset to allocate.
 * @param {number} serviceTypeId - The service type ID for the job.
 * @param {JobRateType} rateTypeId - The rate type (e.g., TIME, TRIP).
 * @param {number} jobDate - The date of the job (timestamp).
 * @param {number} jobId - The job ID.
 * @param {JobPrimaryRate[]} fleetAssetRates - List of available fleet asset
 * rates (used for TRIP type).
 * @param {DivisionOperationDetails | null} [operationsCustomConfig] - Optional
 * division operation config.
 * @param {boolean} [isWindowMode=false] - If true, fetch rates using window
 * mode (broadcast channel).
 * @returns {Promise<JobPrimaryRate | string>} The job primary rate or an error
 * message string.
 */
export async function returnJobPrimaryRateForAllocation({
  fleetAssetId,
  serviceTypeId,
  rateTypeId,
  jobDate,
  jobId,
  fleetAssetRates,
  operationsCustomConfig,
  isWindowMode = false,
}: {
  fleetAssetId: string;
  serviceTypeId: number;
  rateTypeId: JobRateType;
  jobDate: number;
  jobId: number;
  fleetAssetRates: JobPrimaryRate[];
  operationsCustomConfig?: DivisionOperationDetails | null;
  isWindowMode?: boolean;
}): Promise<JobPrimaryRate | string> {
  try {
    if (!fleetAssetId) {
      return 'No fleet asset selected.';
    }
    if (serviceTypeId <= 0) {
      return 'No service type selected.';
    }
    const jobPrimaryRate = new JobPrimaryRate();
    // If the rate type is TRIP, then we can construct the JobPrimaryRate
    if (
      rateTypeId === JobRateType.TRIP &&
      isTripRateTableItem(fleetAssetRates?.[0]?.rate)
    ) {
      jobPrimaryRate.rate = createTripRateTableItem(
        serviceTypeId,
        fleetAssetRates[0].rate.rateTypeObject.rate,
        fleetAssetRates[0].rate.fuelSurcharge,
      );
      return jobPrimaryRate;
    }
    const rateRequest: AllocationRateRequest = {
      entityId: fleetAssetId,
      searchDate: jobDate,
      jobId: jobId!,
    };

    let response: FleetAssetServiceRateResponse | null = null;
    if (!isWindowMode) {
      response =
        await useOperationsStore().getFleetAssetServiceRatesForAllocation(
          rateRequest,
        );
    } else {
      response = await getFleetAssetRatesForWindowMode(rateRequest);
    }

    if (!response?.fleetAssetServiceRate) {
      return 'No service rate found for the selected fleet asset.';
    }
    const rateToApply: RateTableItems | null = initialiseFleetAssetServiceRate(
      response.fleetAssetServiceRate,
    ).rateToApplyToJob({
      serviceTypeId: serviceTypeId,
      rateTypeId: rateTypeId,
      operationsCustomConfig: operationsCustomConfig,
    });
    if (!rateToApply) {
      const rateName: string = [
        returnServiceTypeShortNameFromId(serviceTypeId),
        serviceTypeRates.find((rate) => rate.rateTypeId === rateTypeId)
          ?.shortName,
        ,
      ]
        .filter(Boolean)
        .join(' ');
      return `No rates found for ${rateName}.`;
    }

    jobPrimaryRate.rate = rateToApply;
    return jobPrimaryRate;
  } catch (error) {
    logConsoleError(
      'Something went wrong while fetching rates for allocation',
      error,
    );
    return 'An error occurred while fetching rates for allocation.';
  }
}

/**
 * Fetches fleet asset service rates for allocation in window mode using a
 * broadcast channel. Used when the allocation context is running in a separate
 * window.
 *
 * @param {AllocationRateRequest} rateRequest - The request object containing
 * entityId, searchDate, and jobId.
 * @returns {Promise<FleetAssetServiceRateResponse | null>} The service rate
 * response or null if not found or on error.
 */
async function getFleetAssetRatesForWindowMode(
  rateRequest: AllocationRateRequest,
): Promise<FleetAssetServiceRateResponse | null> {
  try {
    const broadcastMessage = new BroadcastMessage(
      BroadcastIds.JOB_LIST.TO_MAIN.GET_FLEET_RATES_FROM_WINDOW,
      rateRequest,
    );
    const result =
      await useBroadcastChannelStore().sendMessageAndWaitForResponse(
        BroadcastChannelType.JOB_LIST,
        broadcastMessage,
        BroadcastIds.JOB_LIST.TO_EXTERNAL.GET_FLEET_RATES_FROM_WINDOW_RESPONSE,
      );

    return result.value as FleetAssetServiceRateResponse | null;
  } catch (error) {
    logConsoleError('Error fetching fleet asset rates for window mode', error);
    return null;
  }
}

// Construct preallocation request and publish
export async function sendPreallocationRequest({
  jobId,
  fleetAssetId,
  driverId,
  fleetAssetRates,
  windowMode,
}: {
  jobId: number | undefined;
  fleetAssetId: string;
  driverId: string;
  fleetAssetRates: JobPrimaryRate[];
  windowMode: boolean;
}): Promise<boolean> {
  try {
    if (!jobId) {
      throw new Error('Job ID is required for preallocation.');
    }
    if (!fleetAssetId || !driverId) {
      throw new Error(
        `Fleet asset ID and Driver ID are required for preallocation. Fleet Asset ID: ${fleetAssetId}, Driver ID: ${driverId}`,
      );
    }
    if (!fleetAssetRates || fleetAssetRates.length === 0) {
      throw new Error('Fleet asset rates are required for preallocation.');
    }

    const allocateJobRequest = new AllocateJobRequest();
    allocateJobRequest.jobId = jobId;
    allocateJobRequest.fleetAssetId = fleetAssetId;
    allocateJobRequest.driverId = driverId;
    allocateJobRequest.fleetAssetRate = fleetAssetRates;

    let result: JobEventSummary | null = null;
    if (windowMode) {
      result = await sendPreAllocationRequestFromWindow(allocateJobRequest);
    } else {
      result = await useAllocationStore().preAllocateJob(allocateJobRequest);
    }
    if (!result) {
      throw new Error(
        'Preallocation failed: No result returned from preallocation request.',
      );
    }
    return true;
  } catch (error) {
    logConsoleError('Error during preallocation request', error);
    if (error instanceof Error) {
      showNotification(error.message, {
        title: 'Preallocation Error',
      });
    } else {
      showNotification('An unexpected error occurred during preallocation.', {
        title: 'Preallocation Error',
      });
    }
    return false;
  }
}

// Handler for emit from AllocateDriver > JobListActionButtons component to be
// sent as broadcast message
async function sendPreAllocationRequestFromWindow(
  request: AllocateJobRequest,
): Promise<JobEventSummary | null> {
  try {
    const broadcastMessage = new BroadcastMessage(
      BroadcastIds.JOB_LIST.TO_MAIN.PREALLOCATE_FROM_WINDOW,
      request,
    );
    const result =
      await useBroadcastChannelStore().sendMessageAndWaitForResponse(
        BroadcastChannelType.JOB_LIST,
        broadcastMessage,
        BroadcastIds.JOB_LIST.TO_EXTERNAL.PREALLOCATE_FROM_WINDOW_RESPONSE,
      );

    return result.value as JobEventSummary | null;
    return null;
  } catch (error) {
    logConsoleError('Error sending preallocation request from window', error);
    return null;
  }
}

function constructOutsideHireNoteContent(details: OutsideHireDetails): string {
  return `OUTSIDE HIRE - ${details.driverName} - Ph: ${
    details.contactNumber
  } - Reg: ${details.registrationNumber || 'Unknown'}`;
}

export async function addOutsideHireNoteToJob(
  outsideHire: OutsideHireDetails,
  windowMode: boolean,
  jobId: number,
): Promise<boolean> {
  const communication = new Communication();
  communication.body = constructOutsideHireNoteContent(outsideHire);

  // Set CommunicationType.communicationDetails to be JobCommunication
  const operationsVisibilityId = communicationVisibility.find(
    (cv) => cv.shortName === 'O',
  );
  communication.type = {
    id: 3,
    communicationDetails: new JobCommunication(),
  };
  communication.visibleTo = [
    operationsVisibilityId ? operationsVisibilityId.id : 2,
  ];
  communication.id = uuidv4().split('-').join('');
  communication.user = sessionManager.getUserName();
  communication.epoch = moment().valueOf();

  // Construct request
  const request: AddNoteToJobRequest = {
    jobId: jobId,
    note: communication,
  };

  let result: AddNoteToJobRequest | null = null;
  if (windowMode) {
    result = await addOutsideHireNoteToJobFromWindow(request);
  } else {
    result = await useJobStore().addNoteToJob(request);
  }

  return result !== null;
}

async function addOutsideHireNoteToJobFromWindow(
  request: AddNoteToJobRequest,
): Promise<AddNoteToJobRequest | null> {
  try {
    const result =
      await useBroadcastChannelStore().sendMessageAndWaitForResponse(
        BroadcastChannelType.JOB_LIST,
        new BroadcastMessage(
          BroadcastIds.JOB_LIST.TO_MAIN.SEND_ADD_NOTE_REQUEST,
          request,
        ),
        BroadcastIds.JOB_LIST.TO_EXTERNAL.SEND_ADD_NOTE_RESPONSE,
      );

    return result.value as AddNoteToJobRequest | null;
  } catch (error) {
    logConsoleError('Error sending preallocation request from window', error);
    return null;
  }
}
