<template>
  <section>
    <v-flex
      :class="
        selectedTabId === 5 ? `client-access-bookJob` : `client-access-index`
      "
    >
      <v-toolbar class="nav-bar" tabs light height="0" flat>
        <template v-slot:extension>
          <v-tabs
            centered
            v-model="selectedTabId"
            :slider-color="clientBrandIdentity.accent"
            v-if="clientBrandIdentity"
          >
            <v-tab
              v-for="item in menuItems"
              :key="item.id"
              class="px-3"
              :class="{ tooltip: item.id === 4 && !isAccount }"
              @click="setSelectedTab(item.id)"
              :disabled="item.id === 4 && !isAccount"
            >
              <!-- Only show tooltip for item with id 4 when disabled -->
              <div v-if="item.id === 4 && !isAccount">
                {{ item.longName }}
                <div class="tooltiptext">Account Role is Required</div>
              </div>
              <!-- Display the tab content normally for other items or when not disabled -->
              <span v-else>{{ item.longName }}</span>
            </v-tab>
          </v-tabs>
        </template>
      </v-toolbar>
      <v-layout column class="body-container">
        <v-layout
          justify-center
          align-center
          v-if="clientDetails && clientBrandIdentity"
          v-show="selectedJobDetails === null"
        >
          <v-flex md12>
            <ClientAccessDashboard
              v-if="selectedTabId === 0"
              :clientDetails="clientDetails"
              :interfaceColors="clientBrandIdentity"
              :jobList="jobList"
              :driverList="driverList"
              :fleetAssetList="fleetAssetList"
              :allDataLoaded="allDataLoaded"
              @dispatchJobListRequest="dispatchJobListRequest"
            >
            </ClientAccessDashboard>
            <ClientAccessDriverMap
              v-if="selectedTabId === 1"
              :clientDetails="clientDetails"
              :interfaceColors="clientBrandIdentity"
              :jobList="jobList"
              :driverList="driverList"
              :fleetAssetList="fleetAssetList"
              @dispatchJobListRequest="dispatchJobListRequest"
            >
            </ClientAccessDriverMap>
            <JobBooking
              v-if="selectedTabId === 2"
              class="job-booking"
              :clientDetails="clientDetails"
              :clientId="clientDetails.clientId"
              @selectMenuItem="setSelectedTab"
            />
            <ClientAccessJobSearch
              v-if="selectedTabId === 3"
              :clientJobList="jobList"
              :isClientPortal="true"
              :clientDriverList="driverList"
              :fleetAssetList="fleetAssetList"
              :interfaceColors="clientBrandIdentity"
              :allDataLoaded="allDataLoaded"
              @dispatchJobListRequest="dispatchJobListRequest"
            />

            <SearchLedger v-if="selectedTabId === 4" />
            <ClientAccessManage
              v-if="selectedTabId === 5"
              :clientDetails="clientDetails"
            />
          </v-flex>
        </v-layout>
        <v-flex v-if="selectedJobDetails !== null">
          <v-btn
            solo
            flat
            outline
            class="v-btn-custom my-2 py-2"
            @click="clientStore.setSelectedJobId(null)"
          >
            <v-icon class="px-2">arrow_back</v-icon>Return To Invoice</v-btn
          >
          <v-flex class="pa-10">
            <ClientAccessJobReport
              :jobDetails="selectedJobDetails"
              :clientDetails="clientDetails"
              :driverList="driverList"
              :fleetAssetList="fleetAssetList"
              :interfaceColors="clientBrandIdentity"
            ></ClientAccessJobReport>
          </v-flex>
        </v-flex>
      </v-layout>
    </v-flex>
  </section>
</template>

<script setup lang="ts">
interface MenuItem {
  id: number;
  longName: string;
}

import ClientAccessDashboard from '@/client_portal/Dashboard/index.vue';
import ClientAccessDriverMap from '@/client_portal/DriverMap/index.vue';
import ClientAccessJobReport from '@/client_portal/JobReport/index.vue';
import ClientAccessManage from '@/client_portal/Manage/manage.vue';
import ClientAccessJobSearch from '@/client_portal/SearchIndex/JobSearch/index.vue';
import SearchLedger from '@/components/admin/Accounting/ledger/components/search_ledger/index.vue';
import JobBooking from '@/components/booking/job_booking.vue';
import { initialiseClientDetails } from '@/helpers/classInitialisers/InitialiseClientDetails';
import { hasAccountRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import { InterfaceColors } from '@/interface-models/Company/BrandIdentity/InterfaceColors';
import DriverDetailsShort from '@/interface-models/Driver/DriverDetails/DriverDetailsShort';
import { FleetAsset } from '@/interface-models/FleetAsset/FleetAsset';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import SearchJobRequest from '@/interface-models/Jobs/SearchJob/SearchJobRequest';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useClientPortalStore } from '@/store/modules/ClientPortalStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useMittListener } from '@/utils/useMittListener';
import {
  ComputedRef,
  Ref,
  WritableComputedRef,
  computed,
  onBeforeUnmount,
  onMounted,
  ref,
} from 'vue';

const clientPortalStore = useClientPortalStore();
const companyDetailsStore = useCompanyDetailsStore();

const clientId: Ref<string> = ref('');
const clientDetails: Ref<ClientDetails | null> = ref(null);

const isRequestingCurrentJobList: Ref<boolean> = ref(false);
const isRequestingDriverDetailsList: Ref<boolean> = ref(false);
const isRequestingFleetAssetList: Ref<boolean> = ref(false);
const routeProgressInterval: Ref<ReturnType<typeof setInterval> | null> =
  ref(null);

const clientStore = useClientPortalStore();

const selectedJobDetails: ComputedRef<JobDetails | null> = computed(() => {
  if (!clientStore.selectedJobId) {
    return null;
  }
  return (
    clientStore.currentLedgerJobList.find(
      (j) => j.jobId === clientStore.selectedJobId,
    ) ?? null
  );
});

// check if user has Account Role for Ledger
const isAccount = computed(() => hasAccountRole());

const menuItems: Ref<MenuItem[]> = ref([
  { id: 0, longName: 'Dashboard' },
  { id: 1, longName: 'Live Driver Map' },
  { id: 2, longName: 'Book a Job' },
  { id: 3, longName: 'Query Jobs' },
  { id: 4, longName: 'Invoice History' },
  { id: 5, longName: 'Manage' },
]);

/**
 * Gets and sets the selected tab id value in the store. Used to control the
 * visibility of the child components.
 */
const selectedTabId: WritableComputedRef<number> = computed({
  get(): number {
    return clientPortalStore.currentNavigationId;
  },
  set(value: number): void {
    clientPortalStore.setCurrentNavigationId(value);
  },
});

/**
 * Contains the brand identity colors for the client's company. Passed into
 * child component to utilise the correct colors.
 */
const clientBrandIdentity: ComputedRef<InterfaceColors | undefined> = computed(
  () => {
    const companyDetails = companyDetailsStore.companyDetails;
    return companyDetails
      ? companyDetails.brandIdentity.theme.light
      : undefined;
  },
);

/**
 * Handles emit from child components, and sends search request with the
 * request object in the emit.
 * @param request - SearchJobRequest object containing various search criteria
 */
async function dispatchJobListRequest(request: SearchJobRequest) {
  isRequestingCurrentJobList.value = true;
  const result = await clientPortalStore.requestJobSearchForClient(request);
  if (isRequestingCurrentJobList.value && result) {
    handleJobListResponse(result);
    isRequestingCurrentJobList.value = false;
  }
}

/**
 * Used in template and emits to control the current visible tab.
 * @param value - The id of the tab to be set as the selected tab
 */
function setSelectedTab(value: number) {
  if (value !== selectedTabId.value) {
    if (value === 0 || value === 1 || value === 3) {
      clientPortalStore.resetJobListState();
    }
    selectedTabId.value = value;
  }
}

/**
 * Computed property that returns true if all data has been loaded from the
 * server. This is used to determine when to show the loading spinner.
 */
const allDataLoaded: ComputedRef<boolean> = computed(() => {
  return (
    !isRequestingCurrentJobList.value &&
    !isRequestingDriverDetailsList.value &&
    !isRequestingFleetAssetList.value
  );
});

const jobList: ComputedRef<JobDetails[]> = computed(() => {
  const allJobs = clientPortalStore.clientJobList;
  return allJobs && allJobs.length > 0 ? allJobs : [];
});

const driverList: ComputedRef<DriverDetailsShort[]> = computed(() => {
  const drivers = clientPortalStore.driverList;
  return drivers && drivers.length > 0 ? drivers : [];
});

const fleetAssetList: ComputedRef<FleetAsset[]> = computed(() => {
  const fleetAssets = clientPortalStore.fleetAssetList;
  return fleetAssets && fleetAssets.length > 0 ? fleetAssets : [];
});

/**
 * Handles the response from the server for the clientDetails associated with
 * the current user, which is called on component mount.
 */
function handleClientDetailsResponse(
  clientDetailsResponse: ClientDetails | null,
) {
  if (!clientDetailsResponse) {
    return;
  }
  // If the response is captured by mitt and not pulled from the store, we need
  // to initialise it so we have access to class methods/getters
  if (!(clientDetailsResponse instanceof ClientDetails)) {
    clientDetailsResponse = initialiseClientDetails(clientDetailsResponse);
  }
  clientDetails.value = clientDetailsResponse;
  clientId.value = clientDetailsResponse.clientId
    ? clientDetailsResponse.clientId
    : '';
}

/**
 * Handles the response from the job list request, and sends requests for
 * driver and fleet asset details based on the job list.
 * @param jobList - List of JobDetails objects that were returned
 */
function handleJobListResponse(jobList: JobDetails[]) {
  const jobIds = jobList.map((job) => (job.jobId ? job.jobId : 0));

  if (jobIds.length > 0) {
    requestDrivers(jobIds);
    requestFleetAssets(jobIds);
  }
}

/**
 * Requests driver details for a list of jobIds. The response updates the
 * store.
 */
async function requestDrivers(jobIds: number[]) {
  isRequestingDriverDetailsList.value = true;
  await clientPortalStore.requestDriverDetailsFromJobIdList(jobIds);
  isRequestingDriverDetailsList.value = false;
}

/**
 * Requests fleet asset details for a list of jobIds. The response updates the
 * store.
 */
async function requestFleetAssets(jobIds: number[]) {
  isRequestingFleetAssetList.value = true;
  const result =
    await clientPortalStore.requestFleetAssetDetailsFromJobIdList(jobIds);
  // Request the last known locations for the list of fleet asset ids
  if (result && selectedTabId.value === 1) {
    const fleetAssetIds = result.map((f) => f.fleetAssetId);
    clientPortalStore.requestLastKnownLocationsFromIdList(fleetAssetIds);
  }
  isRequestingFleetAssetList.value = false;
}

function setRouteProgressInterval() {
  routeProgressInterval.value = setInterval(() => {
    if (!jobList.value || jobList.value.length === 0) {
      return;
    }
    for (const jobDetails of jobList.value) {
      const routeProgressRequired: boolean =
        jobDetails.workStatus >= WorkStatus.BOOKED &&
        jobDetails.workStatus <= WorkStatus.ACCEPTED;
      if (routeProgressRequired) {
        jobDetails.getRouteProgressMatrix();
      }
    }
  }, 60000);
}

useMittListener('authenticatedClientDetails', handleClientDetailsResponse);

onMounted(() => {
  setRouteProgressInterval();
  if (!clientDetails.value && clientPortalStore.clientDetails) {
    handleClientDetailsResponse(clientPortalStore.clientDetails);
  }
});

onBeforeUnmount(() => {
  if (routeProgressInterval.value) {
    clearInterval(routeProgressInterval.value);
  }
});
</script>

<style scoped lang="scss">
// FOR ALL OTHER TABS IN CLIENT PORTAL EXCEPT BOOK JOB
.client-access-index {
  background-color: var(--background-color-400);
  padding: 20px 40px;
  overflow-y: auto;
  height: calc(100vh - 40px);
  .nav-bar {
    z-index: 99;
    top: 40px;
    left: 0;
    position: fixed;
  }
  .body-container {
    padding: 20px;
    min-width: 100%;
    margin-top: 30px;
    height: auto;
    background-color: var(--background-color-400);
  }
}

// FOR CLIENT PORTAL BOOK JOB TAB
.client-access-bookJob {
  background-color: var(--background-color-400);
  padding: 24px;
  overflow-y: auto;
  height: calc(100vh - 40px);
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  .nav-bar {
    z-index: 99;
    top: 40px;
    left: 0;
    position: fixed;
  }

  .body-container {
    padding: 24px;
    padding-top: 40px;
    min-width: 100%;
    background-color: var(--background-color-400);
    .job-booking-form {
      height: auto;
      min-height: 100vh;
      overflow-y: visible;
    }
  }
}

.tooltip {
  position: relative;
  display: inline-block;
}

/* Tooltip text */
.tooltip .tooltiptext {
  visibility: hidden;
  box-shadow: 0px 8px 32px var(--shadow-color) !important;
  padding: 8px !important;
  margin: 4px !important;
  border-radius: 8px !important;
  font-size: $font-size-14;
  font-weight: 600;
  background: black;
  border: 1px solid $translucent;
  color: white;
  position: fixed;
  top: 1px;
  left: 51%;
}
.tooltip:hover .tooltiptext {
  visibility: visible;
}
</style>
