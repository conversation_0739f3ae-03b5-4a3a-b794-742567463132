import { Component, Vue } from 'vue-property-decorator';
import AuthenticateUser from '@/client_portal/authentication/components/authenticate_user/index.vue';
import AuthenticateClient from '@/client_portal/authentication/components/authenticate_client/index.vue';
import LoginDetails from '@/interface-models/Login/Login';
import ForgottenPassword from '@/authentication/forgotten_password/forgotten_password_index.vue';
import { useAuthenticationStore } from '@/store/modules/AuthenticationStore';
import { authenticateUser } from '@/helpers/AuthenticationHelpers/LoginHelpers';

@Component({
  components: {
    AuthenticateUser,
    AuthenticateClient,
    ForgottenPassword,
  },
})
export default class ClientPortalAuthenticationIndex extends Vue {
  public isAuthenticateUser: boolean = true;
  public isDivisionSelect: boolean = false;
  public isForgotPassword: boolean = false;
  public loginDetails: LoginDetails = new LoginDetails();

  public setDivisionSelect() {
    this.isAuthenticateUser = false;
    this.isDivisionSelect = true;
  }

  public authenticateDivision(auth: any) {
    this.loginDetails.company = auth.company;
    this.loginDetails.division = auth.division;
    this.loginDetails.clientId = auth.clientId;
    useAuthenticationStore().disconnectWebsocket({
      isMultiTokenDisconnect: true,
      resetStore: false,
      resetCompanyDetails: false,
    });
    authenticateUser(this.loginDetails);
  }

  public forgotPassword(): void {
    this.isAuthenticateUser = false;
    this.isForgotPassword = true;
  }

  public backToLogin(): void {
    this.isForgotPassword = false;
    this.isAuthenticateUser = true;
  }
}
