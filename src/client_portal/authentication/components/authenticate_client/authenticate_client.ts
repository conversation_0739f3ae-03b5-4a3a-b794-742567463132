import { SESSION_STORAGE_TOKEN } from '@/helpers/AppLayoutHelpers/LocalStorageHelpers';
import { Portal } from '@/interface-models/Generic/Portal';
import { useAuthenticationStore } from '@/store/modules/AuthenticationStore';
import { sessionManager } from '@/store/session/SessionState';
import * as jwt from 'jose';
import { Component, Vue, Watch } from 'vue-property-decorator';
interface ClientSelect {
  clientName: string;
  clientId: string;
  company: string;
  division: string;
}

interface CompanyDivisionSelect {
  company: string;
  division: string;
}
@Component({})
export default class DivisionSelect extends Vue {
  public selectedDivision: any = null;
  public authenticationLoading: boolean = false;
  public divisionOrClientSelectList: ClientSelect[] | CompanyDivisionSelect[] =
    [];

  get isClientPortal(): boolean {
    return sessionManager.getPortalType() === Portal.CLIENT;
  }

  get loginState() {
    return useAuthenticationStore().authToken.accessToken;
  }
  @Watch('loginState')
  public setUser(token: string, oldToken: string) {
    if (token !== oldToken && token) {
      const decode = jwt.decodeJwt(token);
      sessionStorage.setItem(SESSION_STORAGE_TOKEN, token);
      if (decode && decode.securityLevel !== 'Client') {
        this.$router.push('/');
      } else {
        this.$router.push('/client-portal');
      }
    }
  }

  get decodedToken() {
    return jwt.decodeJwt(this.loginState);
  }

  public submit(): void {
    if (!this.selectedDivision) {
      return;
    }
    this.authenticationLoading = true;
    this.$emit('authenticateDivision', this.selectedDivision);
  }

  public setDivisionOrClientSelectList() {
    const token = useAuthenticationStore().authToken.accessToken;
    const decode = jwt.decodeJwt(token);
    if (!decode) {
      return;
    }
    this.divisionOrClientSelectList = !this.isClientPortal
      ? (decode.companies as CompanyDivisionSelect[])
      : useAuthenticationStore().userAssociatedClients;

    this.selectedDivision = this.divisionOrClientSelectList[0]
      ? this.divisionOrClientSelectList[0]
      : null;
  }

  public mounted() {
    this.setDivisionOrClientSelectList();
  }
}
