<section class="login-priority">
  <v-form>
    <v-select
      :label="'Please Select A ' + (!isClientPortal ? ' Division' : ' Client')"
      :items="divisionOrClientSelectList"
      v-model="selectedDivision"
      hide-details
      class="pb-3"
      background-color="grey darken-1"
      outline
      color="white"
    >
      <template slot="selection" slot-scope="data">
        <span v-if="!isClientPortal"
          >{{data.item.company}} - {{data.item.division}}</span
        >
        <span v-if="isClientPortal"
          >{{data.item.clientName}} - {{data.item.division}}</span
        >
      </template>
      <template slot="item" slot-scope="data">
        <span v-if="!isClientPortal"
          >{{data.item.company}} - {{data.item.division}}</span
        >
        <span v-if="isClientPortal"
          >{{data.item.clientName}} - {{data.item.division}}</span
        >
      </template>
    </v-select>
    <div class="submit-container">
      <v-btn
        v-shortkey.once="[`enter`]"
        block
        @shortkey="selectedDivision !== {} ? submit() : null"
        block
        light
        large
        :disabled="selectedDivision === {}"
        @click="submit"
        :loading="authenticationLoading"
        >Select</v-btn
      >
    </div>
  </v-form>
</section>
