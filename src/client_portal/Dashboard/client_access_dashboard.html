<section class="client-access-dashboard">
  <v-layout justify-center pt-2 v-if="clientDetails">
    <!-- <v-flex px-1 md4>
      <v-select
        solo
        flat
        dark
        label="Navigation"
        v-model="selectedNavigationMenuId"
        :background-color="interfaceColors.accent"
        :items="navigationMenuItems"
        item-value="id"
        item-text="longName"
        color="blue"
      ></v-select>
    </v-flex> -->
    <v-flex md12 v-if="!viewingJobDetailsReport">
      <v-layout>
        <v-flex px-1>
          <v-select
            solo
            flat
            class="v-solo-custom"
            label="Job Progress"
            hint="Job Progress"
            v-model="progressTypeId"
            :items="progressMenuItems"
            item-value="id"
            item-text="longName"
            color="blue"
            persistent-hint
          ></v-select>
        </v-flex>
        <v-flex px-1>
          <DatePickerBasic
            @setEpoch="setDateFilterStart"
            :soloInput="true"
            labelName="Date Filter"
            :formDisabled="false"
            :epochTime="dateFilter"
            :isClientPortal="true"
          >
          </DatePickerBasic>
        </v-flex>
      </v-layout>
    </v-flex>

    <v-flex md12 v-else>
      <v-layout>
        <v-flex md4>
          <v-btn
            solo
            flat
            outline
            class="v-btn-custom"
            :color="interfaceColors.secondary"
            @click="cancelJobDetailsView"
            ><v-icon class="px-2">arrow_back</v-icon> Back to Dashboard</v-btn
          >
        </v-flex>
      </v-layout>
    </v-flex>
  </v-layout>
  <v-layout pb-2> </v-layout>
  <v-layout row wrap class="dashboard-joblist" v-if="allDataLoaded">
    <v-flex md12 v-if="viewingJobDetailsReport && selectedJobDetails">
      <ClientAccessJobReport
        :clientDetails="clientDetails"
        :jobDetails="selectedJobDetails"
        :driverList="driverList"
        :fleetAssetList="fleetAssetList"
        :interfaceColors="interfaceColors"
        @cancelJobDetailsView="cancelJobDetailsView"
      ></ClientAccessJobReport>
    </v-flex>
    <v-flex
      md3
      v-for="(job, index) in jobList"
      class="pa-2"
      :key="job.jobId"
      v-if="!viewingJobDetailsReport && jobList && jobList.length > 0"
      @click="jobSelected(job.jobId)"
    >
      <ClientAccessJobCard
        :jobDetails="job"
        :driverList="driverList"
        :fleetAssetList="fleetAssetList"
        :interfaceColors="interfaceColors"
      >
      </ClientAccessJobCard>
    </v-flex>

    <v-layout
      v-if="!viewingJobDetailsReport && (!jobList || jobList.length === 0)"
      pt-4
    >
      <v-layout justify-center>
        <span class="body--subheader"> No jobs found. </span>
      </v-layout>
    </v-layout>
  </v-layout>
  <v-layout v-if="!allDataLoaded" justify-center pa-4>
    <v-progress-circular
      :size="50"
      :color="interfaceColors.accent"
      indeterminate
    ></v-progress-circular>
  </v-layout>
</section>
