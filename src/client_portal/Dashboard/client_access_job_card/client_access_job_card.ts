import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { InterfaceColors } from '@/interface-models/Company/BrandIdentity/InterfaceColors';
import DriverDetailsShort from '@/interface-models/Driver/DriverDetails/DriverDetailsShort';
import FleetAsset from '@/interface-models/FleetAsset/FleetAsset';
import equipmentTypes from '@/interface-models/Generic/EquipmentTypes/EquipmentTypes';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import JobReferenceDetails from '@/interface-models/Jobs/JobReferenceDetails';
import { JobRouteProgress } from '@/interface-models/Jobs/JobRouteProgress';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { Component, Prop, Vue } from 'vue-property-decorator';

interface KeyValuePair {
  id: string;
  title: string;
  value: string;
}

@Component({
  components: { DatePickerBasic },
})
export default class ClientAccessJobCard extends Vue {
  @Prop() public jobDetails: JobDetails;
  @Prop() public driverList: DriverDetailsShort[];
  @Prop() public fleetAssetList: FleetAsset[];
  @Prop() public interfaceColors: InterfaceColors;
  @Prop({ default: false }) public detailedView: boolean;
  @Prop({ default: true }) public hasElevation: boolean;

  public fontSizeString = (color: string) => `color: ${color}`;

  get currentStatusName(): string {
    const workStatus: WorkStatus = this.jobDetails.workStatus;
    if (this.jobDetails.statusList.includes(57)) {
      return 'PENDING';
    }
    if (workStatus >= WorkStatus.DRIVER_COMPLETED) {
      return 'COMPLETED';
    }
    return 'CURRENT';
  }

  get pudCompletionString() {
    const puds = this.jobDetails.pudItems.filter(
      (p) => p.legTypeFlag === 'P' || p.legTypeFlag === 'D',
    );
    const total = puds.length;
    const done = puds.filter((p) => p.status === 'FINISHED').length;

    return `${done} of ${total} Completed`;
  }

  get jobCardStyle() {
    const ic = this.interfaceColors;
    return !this.detailedView ? `border: 1px solid ${ic.fontColor100}` : '';
  }
  // Return a summary of the jobs
  get jobDetailList(): KeyValuePair[] {
    const detailed = this.detailedView;
    const x = this.jobDetails;
    const dataList: KeyValuePair[] = [];
    const driver = this.driverList.find((d) => d.driverId === x.driverId);
    const fleet = this.fleetAssetList.find(
      (f) => f.fleetAssetId === x.fleetAssetId,
    );

    const jobEpoch = x.pudItems && x.pudItems[0] ? x.pudItems[0].epochTime : 0;
    const jobDate = jobEpoch ? returnFormattedDate(jobEpoch) : '';
    const reference = this.formatReferences(x.jobReference);

    if (detailed) {
      dataList.push({
        id: 'jobDate',
        title: 'Date',
        value: jobDate,
      });
      if (x.clientDispatcher.firstName || x.clientDispatcher.lastName) {
        dataList.push({
          id: 'caller',
          title: 'Caller',
          value:
            (x.clientDispatcher.firstName ? x.clientDispatcher.firstName : '') +
            (x.clientDispatcher.lastName ? x.clientDispatcher.lastName : ''),
        });
      }
    }

    dataList.push({
      id: 'reference',
      title: 'Reference',
      value: reference ? reference : '-',
    });
    if (!detailed) {
      if (
        fleet &&
        driver &&
        this.jobDetails.workStatus >= WorkStatus.ACCEPTED
      ) {
        dataList.push({
          id: 'fleetdriver',
          title: 'Truck',
          value: `${fleet.csrAssignedId ? fleet.csrAssignedId : '-'} (${
            driver.name ? driver.name : '-'
          })`,
        });
      } else {
        dataList.push({
          id: 'fleetdriver',
          title: 'Truck',
          value: 'TBD',
        });
      }
      dataList.push({
        id: 'status',
        title: 'Job Status',
        value: this.jobDetails.clientCurrentExactJobStatus,
      });
    }
    return dataList;
  }

  // Return a summary of the jobs
  get jobDetailListDriver(): KeyValuePair[] {
    const detailed = this.detailedView;
    const x = this.jobDetails;
    const dataList: KeyValuePair[] = [];
    const driver = this.driverList.find((d) => d.driverId === x.driverId);
    const fleet = this.fleetAssetList.find(
      (f) => f.fleetAssetId === x.fleetAssetId,
    );
    const additionalEquipments = this.formatAdditionalEquipments(
      x.additionalEquipments,
    );
    if (detailed) {
      dataList.push({
        id: 'serviceRate',
        title: 'Service',
        value: `${x.serviceTypeLongName} - ${x.rateTypeName}`,
      });
      if (
        fleet &&
        driver &&
        this.jobDetails.workStatus >= WorkStatus.ACCEPTED
      ) {
        dataList.push({
          id: 'fleet',
          title: 'Truck',
          value: `${fleet?.csrAssignedId ? fleet.csrAssignedId : '-'}`,
        });
        dataList.push({
          id: 'driver',
          title: 'Driver',
          value: `${driver?.name ? driver.name : '-'}`,
        });
      } else {
        dataList.push({
          id: 'fleet',
          title: 'Truck',
          value: 'TBD',
        });
        dataList.push({
          id: 'driver',
          title: 'Driver',
          value: 'TBD',
        });
      }
      dataList.push({
        id: 'additionalEquipments',
        title: 'Req Equipment',
        value: additionalEquipments,
      });
    }

    return dataList;
  }

  public returnStyleForPudStatus(status: string) {
    if (!status) {
      return `none`;
    } else {
      if (status === 'ARRIVED') {
        return `arrived`;
      }
      if (status === 'FINISHED') {
        return `finished`;
      }
    }
  }
  get pudProgress(): string[] {
    if (!this.jobDetails || !this.jobDetails.additionalJobData) {
      return [];
    }
    const progressData: JobRouteProgress[] =
      this.jobDetails.additionalJobData.routeProgress;
    const expectedArrivals: string[] = [];
    for (const pudProgressData of progressData) {
      const pudItem: PUDItem | undefined = this.jobDetails.pudItems.find(
        (x: PUDItem) => x.pudId === pudProgressData.pudId,
      );
      if (!pudItem) {
        expectedArrivals.push('');
        continue;
      }

      let progressTooltipValue = '';

      if (!pudItem.status) {
        progressTooltipValue =
          pudProgressData.expectedArrivalTimeReadable &&
          pudProgressData.expectedArrivalTime
            ? 'Estimate Arrival: ' +
              returnFormattedDate(pudProgressData.expectedArrivalTime) +
              ' ' +
              pudProgressData.expectedArrivalTimeReadable
            : '';
      } else if (
        pudItem.status === 'ARRIVED' &&
        pudProgressData.expectedDepartureTime &&
        pudProgressData.expectedDepartureTimeReadable
      ) {
        progressTooltipValue =
          'Estimate Departure ' +
          returnFormattedDate(
            pudProgressData.expectedDepartureTime,
            'DD/MM/YY HH:mm',
          );
      } else if (
        pudItem.status === 'FINISHED' &&
        pudProgressData.actualArrivalTime &&
        pudProgressData.actualDepartureTime
      ) {
        progressTooltipValue =
          ' Arrived ' +
          returnFormattedDate(
            pudProgressData.actualArrivalTime,
            'DD/MM/YY HH:mm',
          ) +
          ' | Departed ' +
          returnFormattedDate(
            pudProgressData.actualDepartureTime,
            'DD/MM/YY HH:mm',
          );
      }
      expectedArrivals.push(progressTooltipValue);
    }

    return expectedArrivals;
  }

  public getAdditionalEquipments(additionalEquipments): string {
    if (additionalEquipments.length === 0) {
      return '-';
    }
    const referenceArray = additionalEquipments
      .split(',')
      .map((ref) => ref.trim());
    const firstReference = referenceArray[0];
    const remainingCount = referenceArray.length - 1;
    if (remainingCount > 0) {
      return `${firstReference} ( +${remainingCount} )`;
    } else {
      return firstReference;
    }
  }

  public formatAdditionalEquipments(additionalEquipments: number[]): string {
    if (!additionalEquipments || additionalEquipments.length === 0) {
      return '-';
    }

    const equipmentNames = additionalEquipments
      .map((id) => {
        const equipment = equipmentTypes.find((item) => item.id === id);
        return equipment ? equipment.longName : '';
      })
      .filter((name) => !!name); // Filter out empty names

    return equipmentNames.length > 0 ? equipmentNames.join(', ') : '-';
  }

  public getRemainingEquipments(equipmentNames: any): string[] {
    const equipmentArray = equipmentNames.split(',').map((name) => name.trim()); // Split by comma and trim whitespace
    return equipmentArray.slice(1).join(', '); // Return all but the first equipment name
  }

  /**
   * Show first reference and count of additional references
   * If there's only one reference, return it
   */
  public getReference(references): string {
    const referenceArray = references.split(',').map((ref) => ref.trim());
    const firstReference = referenceArray[0];
    const remainingCount = referenceArray.length - 1;
    if (remainingCount > 0) {
      return `${firstReference} ( +${remainingCount} )`;
    } else {
      return firstReference;
    }
  }

  public formatReferences(references: JobReferenceDetails[]): string {
    if (!references || references.length === 0) {
      return '-';
    }
    return references
      .map((ref) => ref.reference)
      .filter((s) => !!s)
      .join(', ');
  }
  /**
   * Return the remaining references after the first one for tooltip
   */
  public getRemainingReferences(references: any): string[] {
    const referenceArray = references
      .split(',')
      .map((ref: string) => ref.trim()); // Split by comma and trim whitespace
    return referenceArray.slice(1).join(', ');
  }
}
