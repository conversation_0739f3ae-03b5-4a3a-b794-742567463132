<v-layout
  :class="detailedView ? 'client-access-detailed-View' :'client-access-job-card'"
>
  <v-flex
    class="dashboard-jobitem"
    :style="jobCardStyle"
    :class="[hasElevation ? 'add-shadow' : '', !detailedView ? 'has-hover' :'']"
  >
    <v-layout class="job-progressindicator" v-if="!detailedView">
      <v-flex
        v-for="(pud, pudIndex) in jobDetails.pudItems"
        :key="pud.pudId"
        v-if="pud.legTypeFlag === 'P' || pud.legTypeFlag === 'D' "
        class="job-progressindicator__notch"
        :class="returnStyleForPudStatus(pud.status ? pud.status : '')"
      >
        <v-tooltip bottom v-if="pudProgress[pudIndex]">
          <template v-slot:activator="{ on }">
            <div
              v-on="on"
              style="width: 100%; height: 100%; background: transparent"
            ></div
          ></template>
          <span>{{pudProgress[pudIndex]}}</span>
        </v-tooltip>
      </v-flex>
    </v-layout>
    <v-layout justify-center v-if="!detailedView">
      <span class="pud-count-txt">{{pudCompletionString}}</span>
    </v-layout>

    <v-flex :class="detailedView ? 'content' : ''">
      <!-- First Line (Job ID and Status in non-detailed view) -->
      <!-- <v-divider></v-divider> -->
      <v-layout justify-space-between pt-2 pb-2>
        <span class="jobitem--jobid" :style="fontSizeString('primary')">
          Job # {{ jobDetails.displayId }}
        </span>
        <v-sheet
          :class="currentStatusName"
          class="status-pill"
          v-if="!detailedView"
        >
          <span class="jobitem--status"> {{ currentStatusName }} </span>
        </v-sheet>
      </v-layout>

      <!-- PICKUP DROP -->
      <div v-if="!detailedView">
        <v-divider></v-divider>
        <v-layout row wrap>
          <span class="stop-txt-container" :style="fontSizeString('primary')">
            <span class="stop-txt"
              >{{ jobDetails.pudItems[0].address.suburb }}</span
            >
            <span class="stop-txt-count">----</span>
            <span class="stop-txt-count" v-if="jobDetails.pudItems.length > 2"
              >{{ jobDetails.pudItems.length - 2 }} STOPS ----</span
            >
            <span class="stop-txt"
              >{{ jobDetails.pudItems[(jobDetails.pudItems.length -
              1)].address.suburb }}</span
            >
          </span>
        </v-layout>
      </div>

      <!-- Additional job details -->
      <v-layout v-for="(data, index) in jobDetailList" :key="data.id" row wrap>
        <span class="jobitem--lineitem" :style="fontSizeString('primary')">
          <span class="lineItem-title">{{ data.title }}:</span>
          <span class="lineItem-value" v-if="data.id !== 'reference'"
            >{{ data.value }}</span
          >
          <!-- REFERENCE TOOLTIPS -->
          <v-tooltip v-if="data.id === 'reference'" bottom>
            <template v-slot:activator="{ on }">
              <span
                v-on="on"
                v-if="data.id === 'reference'"
                class="lineItem-value"
                >{{ getReference(data.value) }}</span
              >
            </template>
            {{ getRemainingReferences(data.value) }}
          </v-tooltip>
        </span>
      </v-layout>
    </v-flex>
    <!-- Second Line (Status pill and completion string in detailed view) -->
    <v-layout py-1 v-if="detailedView"><v-divider></v-divider></v-layout>
    <v-flex :class="detailedView ? 'content' : ''" row wrap>
      <v-sheet
        class="status-pill"
        :class="currentStatusName"
        v-if="detailedView"
      >
        <span class="jobitem--status"> {{ currentStatusName }} </span>
      </v-sheet>
      <span class="pud-count-txt" v-if="detailedView">
        {{ pudCompletionString }}
      </span>
      <v-layout v-for="(data, index) in jobDetailListDriver" :key="data.id">
        <span class="lineitem">
          <span class="lineItem-title">{{ data.title }}:</span>
          <span
            class="lineItem-value"
            v-if="data.id !== 'additionalEquipments'"
          >
            {{ data.value }}
          </span>
          <!-- additionalEquipments TOOLTIPS -->
          <v-tooltip v-if="data.id === 'additionalEquipments'" bottom>
            <template v-slot:activator="{ on }">
              <span
                v-on="on"
                v-if="data.id === 'additionalEquipments'"
                class="lineItem-value"
                >{{ getAdditionalEquipments(data.value) }}</span
              >
            </template>
            {{ getRemainingEquipments(data.value) }}
          </v-tooltip>
        </span>
      </v-layout>
    </v-flex>
  </v-flex>
</v-layout>
