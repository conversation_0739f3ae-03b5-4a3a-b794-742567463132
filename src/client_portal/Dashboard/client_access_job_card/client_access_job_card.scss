.client-access-job-card {
  .dashboard-jobitem {
    padding: 12px;
    border-radius: 10px;
    margin: 8px;
    transition: all 0.6s ease;

    &.add-shadow {
      box-shadow: var(--box-shadow);
    }

    .jobitem--lineitem {
      // transform: translate(60px);
      display: inline-flex;
      gap: 8px;
      font-size: $font-size-14;
      font-weight: 500;
      padding-top: 10px;
      .lineItem-title {
        color: var(--light-text-color);
      }
      .lineItem-value {
        color: var(--text-color);
        text-transform: capitalize;
      }
    }
    .pud-count-txt {
      font-size: $font-size-14;
      color: var(--light-text-color);
      padding-top: 2px;
    }

    .job-progressindicator {
      .job-progressindicator__notch {
        background-color: var(--background-color-100) !important;
        height: 10px;
        margin: 2px;
        border-radius: $border-radius-lg;
        &.none {
          background-color: transparent !important;
          border: 1px solid var(--light-text-color) !important;
        }
        &.arrived {
          opacity: 1;
          background-color: $translucent-highlight !important;
          border: 1.5px solid var(--accent) !important;
        }
        &.finished {
          opacity: 1;
          background-color: var(--accent) !important;
        }
      }
    }
    &.has-hover {
      background-color: var(--background-color-100) !important;
      &:hover {
        background-color: var(--background-color-400) !important;
        cursor: pointer;
        scale: 1.1;
        cursor: pointer;
      }
    }
  }

  .stop-txt-container {
    display: inline-flex;
    gap: 10px;
    align-items: center;
    font-size: $font-size-16;
    font-weight: 600;
    padding-top: 10px;
    .stop-txt-count {
      color: var(--heading-text-color);
      font-size: $font-size-13;
    }
  }
}

// DETAILED VIEW
.client-access-detailed-View {
  .content {
    display: flex;
    align-items: center;
    gap: 12px;
    .jobitem--lineitem {
      transform: translate(-48px);
      display: inline-flex;
      gap: 8px;
      font-size: $font-size-18;
      font-weight: 500;
      .lineItem-title {
        color: var(--light-text-color);
      }
      .lineItem-value {
        color: var(--text-color);
      }
    }
    .lineitem {
      display: inline-flex;
      gap: 8px;
      font-size: $font-size-18;
      font-weight: 500;
      margin-top: 8px;
      margin-left: 30px;
      .lineItem-title {
        color: var(--light-text-color);
      }
      .lineItem-value {
        color: var(--text-color);
      }
    }
    .pud-count-txt {
      font-size: $font-size-18;
      color: var(--light-text-color);
      padding-top: 8px;
    }
  }
}

.jobitem--jobid {
  font-size: $font-size-22;
  font-weight: 600;
  color: var(--primary);
}

.status-pill {
  border-radius: 20px;
  padding: 2px 8px;
  text-align: center;
  align-self: center;
  background-color: var(--accent) !important;
  &.CURRENT {
    background-color: var(--accent) !important;
    border: 2px solid $accent !important;
  }
  &.PENDING {
    background-color: var(--background-color-400) !important;
    border: 2px solid var(--light-text-color) !important;
    .jobitem--status {
      color: var(--light-text-color);
    }
  }
  &.COMPLETED {
    background-color: $success !important;
    border: 2px solid $success-type !important;
  }

  .jobitem--status {
    font-weight: 600;
    color: white;
    font-size: $font-size-12;
    padding: 0px 4px;
  }
}
