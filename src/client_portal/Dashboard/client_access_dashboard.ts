import ClientAccessJobCard from '@/client_portal/Dashboard/client_access_job_card/index.vue';
import ClientAccessJobReport from '@/client_portal/JobReport/index.vue';
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import { shadeColor } from '@/helpers/AppLayoutHelpers/AppLayoutHelpers';
import {
  millisecondsInOneDay,
  returnEndOfDayFromEpoch,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import { InterfaceColors } from '@/interface-models/Company/BrandIdentity/InterfaceColors';
import DriverDetailsShort from '@/interface-models/Driver/DriverDetails/DriverDetailsShort';
import FleetAsset from '@/interface-models/FleetAsset/FleetAsset';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import SearchJobRequest from '@/interface-models/Jobs/SearchJob/SearchJobRequest';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useClientPortalStore } from '@/store/modules/ClientPortalStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import Mitt from '@/utils/mitt';
import moment from 'moment';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component({
  components: { DatePickerBasic, ClientAccessJobReport, ClientAccessJobCard },
})
export default class ClientAccessDashboard extends Vue {
  @Prop() public clientDetails: ClientDetails;
  @Prop() public jobList: JobDetails[];
  @Prop() public driverList: DriverDetailsShort[];
  @Prop() public fleetAssetList: FleetAsset[];
  @Prop() public interfaceColors: InterfaceColors;
  @Prop({ default: true }) public allDataLoaded: boolean;

  public companyDetailsStore = useCompanyDetailsStore();
  public clientPortalStore = useClientPortalStore();

  public selectedJobId: number = -1;
  public viewingJobDetailsReport: boolean = false;
  public dateFilterStart: number = returnStartOfDayFromEpoch();
  public dateFilterEnd: number = returnEndOfDayFromEpoch();

  public shadeColor = shadeColor;

  public navigationMenuItems = [
    {
      id: 1,
      longName: 'Dashboard',
    },
    {
      id: 2,
      longName: 'Manifest History',
    },
    {
      id: 4,
      longName: 'Search',
    },
    {
      id: 5,
      longName: 'Build Manifest',
    },
  ];

  public selectedProgressMenuId: number = 1;
  public progressMenuItems = [
    {
      id: 1,
      longName: 'All',
    },
    {
      id: 2,
      longName: 'Current',
    },
    {
      id: 3,
      longName: 'Complete',
    },
  ];

  get dateFilter() {
    return this.dateFilterStart;
  }
  set dateFilter(value: number) {
    const numberOfDays = 1;
    const msInOneDay = millisecondsInOneDay;
    this.dateFilterStart = value;
    this.dateFilterEnd = value + (msInOneDay * numberOfDays - 1);
    this.requestJobList([]);
  }

  get progressTypeId() {
    return this.selectedProgressMenuId;
  }
  set progressTypeId(value: number) {
    this.selectedProgressMenuId = value;
    this.requestJobList([]);
  }

  get selectedJobDetails() {
    if (this.selectedJobId === -1 || !this.viewingJobDetailsReport) {
      return;
    }
    const foundJob = this.jobList.find((j) => j.jobId === this.selectedJobId);
    return foundJob;
  }

  public setDateFilterStart(epoch: number): void {
    this.dateFilter = epoch;
  }

  public jobSelected(jobId: number) {
    this.selectedJobId = jobId;
    this.viewingJobDetailsReport = true;
  }

  public cancelJobDetailsView() {
    this.selectedJobId = -1;
    this.viewingJobDetailsReport = false;
  }

  public setInitialDateFilter() {
    const userTimeZone = this.companyDetailsStore.userLocale;
    this.dateFilter = moment().tz(userTimeZone).startOf('day').valueOf();
  }

  // supply an empty array of jobIds to return all jobs that fit the criteria
  public requestJobList(jobIds: number[]) {
    // End of current day
    const endEpoch = this.dateFilterEnd;
    // Replace this with START OF TODAY later
    const startEpoch = this.dateFilterStart;
    let workStatusMin: WorkStatus = WorkStatus.BOOKED;
    let workStatusMax: WorkStatus = WorkStatus.FINALISED;

    const statusList: number[] = [];
    if (this.progressTypeId === 2) {
      workStatusMax = WorkStatus.IN_PROGRESS;
    } else if (this.progressTypeId === 3) {
      workStatusMin = WorkStatus.DRIVER_COMPLETED;
    }

    const request: SearchJobRequest = {
      statusList,
      workStatusMin,
      workStatusMax,
      workStatus: null,
      serviceFailure: false,
      jobSourceType: null,
      startEpoch,
      endEpoch,
      clientId: '',
      jobId: '',
      jobReference: '',
      dispatcherName: '',
      siteContactName: '',
      customerDeliveryName: '',
      suburbName: '',
      fleetAssetId: '',
      driverId: '',
      jobIds,
      invoiceId: '',
      rctiId: '',
      sortByField: null,
      sortDirection: null,
    };
    this.$emit('dispatchJobListRequest', request);
  }

  /**
   * Handles mitt event for job status update and requests the job details if
   * the jobId does not exist in state.
   * @param jobId - The jobId that has been updated
   */
  private handleJobUpdate(jobId: number) {
    if (!jobId) {
      return;
    }
    // We should only action here if the updated jobId does not currently exist
    // in state. If the job does not yet exist in state we will use the selected
    // date range along with the jobId to request the job.
    const foundJob: JobDetails | undefined =
      this.clientPortalStore.clientJobList.find((j) => j.jobId === jobId);
    if (foundJob) {
      return;
    }
    this.clientPortalStore.awaitingUpdatedJobDetails.push(jobId);
    this.requestJobList([jobId]);
  }

  public mounted() {
    Mitt.on('clientJobStatusUpdate', this.handleJobUpdate);
  }

  public beforeDestroy() {
    Mitt.off('clientJobStatusUpdate', this.handleJobUpdate);
  }
}
