<section class="client-access-driver-map">
  <v-layout px-2 pt-1>
    <v-flex md12 :style="'height: ' + mapHeight">
      <TrackingMap
        v-if="allDataLoaded"
        :driverGpsCoordinates="driverLastKnownLocations"
        @markerSelectedOnMap="markerSelectedOnMap"
        :mapHeight="mapHeight"
        :initialMapCenter="divisionDetails && divisionDetails.depotAddress ? divisionDetails.depotAddress.geoLocation : undefined"
        :jumpToLocationCoordinates="jumpToLocationCoordinates"
        :showVehicleList="true"
      ></TrackingMap>
    </v-flex>
  </v-layout>
  <v-layout py-2>
    <v-divider></v-divider>
  </v-layout>
  <v-layout pt-2>
    <!-- <v-btn @click="sendReq">Send</v-btn> -->
    <v-flex md4>
      <v-select
        solo
        flat
        label="Select a Driver"
        v-model="selectedDriverId"
        :items="driverList"
        item-value="driverId"
        item-text="name"
        :background-color="interfaceColors.accent"
        dark
        clearable
        hide-details
      ></v-select>
    </v-flex>

    <v-flex md4 pl-2 v-if="selectedDriverId">
      <v-select
        solo
        flat
        label="Current Work for Driver"
        v-model="selectedJobId"
        :items="jobListForDriver"
        item-value="jobId"
        item-text="displayId"
        light
        clearable
        hide-details
      ></v-select>
    </v-flex>
  </v-layout>

  <v-slide-y-transition hide-on-leave>
    <v-layout v-if="selectedJobForDriver" class="pt-3">
      <v-flex md12>
        <ClientAccessJobReport
          :key="selectedJobId"
          :jobDetails="selectedJobForDriver"
          :clientDetails="clientDetails"
          :driverList="driverList"
          :fleetAssetList="fleetAssetList"
          :interfaceColors="interfaceColors"
        ></ClientAccessJobReport>
      </v-flex>
    </v-layout>
  </v-slide-y-transition>
</section>
