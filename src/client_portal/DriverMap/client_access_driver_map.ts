import ClientAccessJobReport from '@/client_portal/JobReport/index.vue';
import TrackingMap from '@/components/operations/maps/tracking_map/index.vue';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import { InterfaceColors } from '@/interface-models/Company/BrandIdentity/InterfaceColors';
import DivisionDetails from '@/interface-models/Company/DivisionDetails';
import DriverDetailsShort from '@/interface-models/Driver/DriverDetails/DriverDetailsShort';
import FleetAsset from '@/interface-models/FleetAsset/FleetAsset';
import GpsMarkerDetails from '@/interface-models/Generic/Position/GpsMarkerDetails';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import SearchJobRequest from '@/interface-models/Jobs/SearchJob/SearchJobRequest';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useClientPortalStore } from '@/store/modules/ClientPortalStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import moment from 'moment';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';

@Component({
  components: { TrackingMap, ClientAccessJobReport },
})
export default class ClientAccessDriverMap extends Vue {
  @Prop() public clientDetails: ClientDetails;
  @Prop() public jobList: JobDetails[];
  @Prop() public driverList: DriverDetailsShort[];
  @Prop() public fleetAssetList: FleetAsset[];
  @Prop({ default: true }) public allDataLoaded: boolean;
  @Prop() public interfaceColors: InterfaceColors;

  public companyDetailsStore = useCompanyDetailsStore();
  public clientPortalStore = useClientPortalStore();

  public selectedDriverId: string = '';

  public currentJobId: number = 0;
  public selectedJobId: number = 0;

  public mapHeight: string = '500px';

  public driverLastKnownLocations: GpsMarkerDetails[] = [];

  public jumpToLocationCoordinates: [number, number] = [0, 0];

  public requestCurrentJobList() {
    const userTimeZone = this.companyDetailsStore.userLocale;
    const startOfToday: number = moment()
      .tz(userTimeZone)
      .startOf('day')
      .valueOf();

    const endOfToday: number = moment().tz(userTimeZone).endOf('day').valueOf();

    const request: SearchJobRequest = {
      statusList: [],
      workStatus: null,
      workStatusMin: WorkStatus.BOOKED,
      workStatusMax: WorkStatus.FINALISED,
      jobSourceType: null,
      serviceFailure: false,
      startEpoch: startOfToday,
      endEpoch: endOfToday,
      clientId: '',
      jobId: '',
      jobReference: '',
      dispatcherName: '',
      siteContactName: '',
      customerDeliveryName: '',
      suburbName: '',
      fleetAssetId: '',
      driverId: '',
      jobIds: [],
      invoiceId: '',
      rctiId: '',
      sortByField: null,
      sortDirection: null,
    };
    this.$emit('dispatchJobListRequest', request);
  }

  @Watch('fleetAssetList')
  public requestLastKnownLocations(value: FleetAsset[]) {
    const fleetAssetIds = value.map((f) => f.fleetAssetId);
    this.clientPortalStore.requestLastKnownLocationsFromIdList(fleetAssetIds);
  }

  get jobListForDriver(): JobDetails[] | undefined {
    if (!this.selectedDriverId) {
      return;
    }
    return this.jobList.filter(
      (j: JobDetails) => j.driverId === this.selectedDriverId,
    );
  }

  get selectedJobForDriver(): JobDetails | undefined {
    const allJobs = this.jobListForDriver;
    if (
      this.selectedDriverId &&
      this.selectedJobId &&
      allJobs &&
      allJobs.length > 0
    ) {
      const foundJob = allJobs.find(
        (job) =>
          job.driverId === this.selectedDriverId &&
          job.jobId === this.selectedJobId,
      );
      return foundJob;
    }
  }

  get allMapDataArrived() {
    return;
  }

  // Clients will always only get back the division for which their ClientDetails exists.
  get divisionDetails(): DivisionDetails | undefined {
    return this.companyDetailsStore.companyDetails?.divisions?.[0];
  }

  get gpsUpdateCounter() {
    return this.clientPortalStore.gpsUpdateCounter;
  }

  @Watch('gpsUpdateCounter')
  public gpsDataUpdated() {
    this.driverLastKnownLocations = Array.from(
      this.clientPortalStore.allGpsPositions.values(),
    );
  }
  /**
   * Handles emit from TrackingMap component. Set the selectedDriverId to the driverId contained in the GpdMarkerDetails string. Also set jumpToLocationCoordinates, which will center the map on the selected driver
   * @param gpsString stringified GpsMarkerDetails object
   */
  public markerSelectedOnMap(gpsString: string) {
    const gpsData: GpsMarkerDetails = JSON.parse(gpsString) as GpsMarkerDetails;
    this.jumpToLocationCoordinates = [gpsData.longitude, gpsData.latitude];
    this.selectedDriverId = gpsData.driverId;
  }

  // 1. Request JobList for current day
  // 2. Get unique driverId list from job list response
  // 3. Request DriverDetails list from driverId list
  // 4. Request FleetAssetDetails list from fleetAssetId list
  // 5. Request last known GPS location for each of the drivers
  // 6. Display information on Map
  public mounted() {
    this.requestCurrentJobList();
  }
}
