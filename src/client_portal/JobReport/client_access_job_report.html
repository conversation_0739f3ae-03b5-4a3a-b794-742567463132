<section class="client-access-job-report pr-2 pt-2">
  <v-layout px-4 py-2>
    <!-- <span class="job-report__title"
      >Job #{{ jobDetails.displayId ? jobDetails.displayId :
      jobDetails.jobId }}</span
    > -->
    <ClientAccessJobCard
      :jobDetails="jobDetails"
      :interfaceColors="interfaceColors"
      :driverList="driverList"
      :fleetAssetList="fleetAssetList"
      :detailedView="true"
      :hasElevation="false"
    >
    </ClientAccessJobCard>
    <v-flex align-center justify-end class="action-btn-container">
      <v-btn dense @click="getConsignmentNote" class="v-btn-custom">
        <span class="pr-2 ma-0"
          ><v-icon size="18" class="px-1">download</v-icon>
          Download Consignment Note
        </span>
      </v-btn>
      <v-btn dense @click="scrollToMapSection" class="v-btn-custom map">
        <span class="ma-0"><v-icon size="18" class="px-1">map</v-icon> </span>
        map
      </v-btn>
      <v-btn
        v-if="reportsSettings?.allowedAccessMethods?.includes('DOWNLOAD')"
        dense
        @click="getProofOfDelivery('DOWNLOAD')"
        :disabled="!podReady"
        class="v-btn-custom download"
      >
        <span class="pr-2 ma-0"
          ><v-icon size="18" class="px-1">downloading</v-icon>
          Download POD
        </span>
      </v-btn>
      <v-btn
        v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  'EMAIL',
                )
              "
        dense
        @click="getProofOfDelivery('EMAIL')"
        :disabled="!podReady"
        class="v-btn-custom email"
      >
        <span class="pr-2 ma-0"
          ><v-icon size="18" class="px-1">forward_to_inbox</v-icon>
          Email POD
        </span>
      </v-btn>
    </v-flex>
  </v-layout>

  <v-layout :style="borderSideString"> </v-layout>
  <v-layout py-1><v-divider></v-divider></v-layout>
  <v-layout row wrap :style="borderSideString">
    <v-flex md12 py-2 px-2>
      <v-layout>
        <span class="job-report__section-header"> Stops </span>
      </v-layout>
      <PudPillsBar
        class="px-2"
        v-if="jobDetails.pudItems.length > 0"
        :jobDetails="jobDetails"
        :jobBookingDeliverySummaryRef="jobBookingDeliverySummary"
      ></PudPillsBar>
    </v-flex>
    <v-flex md12>
      <JobBookingDeliverySummary
        class="px-2"
        ref="jobBookingDeliverySummary"
        :jobDetails="jobDetails"
        :selectedClientDetails="clientDetails"
      ></JobBookingDeliverySummary>
    </v-flex>
    <v-flex md4 pa-2>
      <v-layout>
        <v-flex md12>
          <p class="ma-0 job-report__section-header">Job Notes</p>
          <v-divider class="mb-2"></v-divider>
          <NotesList
            :style="'opacity: 1'"
            :isBookingScreen="false"
            :communications="getVisibleNotesByUserRole(jobDetails.notes ? jobDetails.notes : [])"
            :jobId="jobDetails.jobId"
            :showVisibilityTypeName="true"
          >
          </NotesList>
        </v-flex>
      </v-layout>
    </v-flex>

    <v-flex md8 pa-2>
      <v-layout row wrap>
        <v-flex md12>
          <v-layout>
            <span class="job-report__section-header"> Route Taken </span>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout
            v-if="finishedLoadingRouteData"
            style="height: 340px"
            px-3
            pt-2
            pb-2
            id="map"
          >
            <JobMapRoute
              :key="jobDetails.jobId"
              :jobDetails="jobDetails"
              :gpsPositionData="gpsRouteData"
              :mapConfig="jobMapConfig"
            >
            </JobMapRoute>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>
  </v-layout>

  <v-layout v-if="jobDetails.currentJobStatus === 'Completed'" py-1
    ><v-divider></v-divider
  ></v-layout>

  <v-layout
    row
    wrap
    v-if="jobDetails.currentJobStatus === 'Completed'"
    :style="borderSideString"
  >
    <v-flex md12>
      <v-layout>
        <span class="job-report__section-header"> Job Charges </span>
      </v-layout>
    </v-flex>
    <v-flex md12
      ><ClientAccessJobTotals :jobDetails="jobDetails"> </ClientAccessJobTotals
    ></v-flex>
  </v-layout>
  <ConsignmentNoteDialog
    v-if="isViewingConsignmentNoteDialog && jobDetails"
    :jobDetails="jobDetails"
    :isDialogOpen.sync="isViewingConsignmentNoteDialog"
    :isClientPortal="true"
  ></ConsignmentNoteDialog>
</section>
