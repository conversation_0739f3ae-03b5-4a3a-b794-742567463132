.client-access-job-report {
  padding: auto;
  background-color: var(--background-color-200);
  box-shadow: rgba(0, 0, 0, 0.2) 0px 4px 12px 4px;
  border-radius: $border-radius-lg;

  .action-btn-container {
    display: grid;
    grid-template-columns: auto auto;
    gap: 8px 2px;
    max-width: fit-content;

    @media (min-width: 600px) {
      grid-template-columns: 1fr;
      grid-auto-flow: dense;
    }

    .v-btn-custom {
      white-space: nowrap;
      width: auto;
      max-width: 100%;
      border: 1px solid var(--accent) !important;
      color: var(--accent);
      background-color: transparent !important;
      border-radius: $border-radius-lg;
      transition: all 0.3s ease;

      &.map {
        border: 1px solid var(--accent-secondary) !important;
        color: var(--accent-secondary);
        &:hover {
          background-color: var(--accent-secondary) !important;
        }
      }

      &.email {
        border: 1px solid var(--primary) !important;
        color: var(--primary);
        &:hover {
          background-color: var(--primary) !important;
        }
      }

      &:disabled {
        border: none !important;
      }

      &:hover {
        background-color: var(--accent) !important;
        color: white;
        scale: 1.05;
        box-shadow: var(--box-shadow);
      }
    }

    // Specific button placement to control the first button size and second row behavior
    .v-btn-custom:nth-child(1),
    .v-btn-custom:nth-child(2) {
      grid-row: 2;
    }

    // Place the next two buttons in the second row
    .v-btn-custom:nth-child(3),
    .v-btn-custom:nth-child(4) {
      grid-row: 1;
    }
  }

  .job-report__title {
    font-size: $font-size-22;
    font-weight: 600;
    padding-bottom: 12px;
    color: var(--primary);
  }
  .job-report__section-header {
    font-size: $font-size-18;
    font-weight: 600;
    width: 100%;
    padding: 8px;
    text-transform: uppercase;
    color: var(--heading-text-color);
  }
  .estimated-time-note {
    font-size: $font-size-12;
    font-weight: 400;
    color: rgb(164, 167, 182);
    font-style: italic;
  }
}
