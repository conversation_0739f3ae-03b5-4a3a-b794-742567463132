import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { returnServiceTypeLongNameFromId } from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { AdditionalChargeSubtotal } from '@/interface-models/Generic/Accounting/JobAccountingTotals/AdditionalChargeSubtotal';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import JobReferenceDetails from '@/interface-models/Jobs/JobReferenceDetails';
import TimeRateType from '@/interface-models/ServiceRates/ServiceTypes/TimeServiceRate/TimeRateType';
import { Component, Prop, Vue } from 'vue-property-decorator';

interface FreightAdjustmentLineItems {
  name: string;
  rate: string;
  amount: string;
}
interface PudLineItem {
  suburb: string;
  reference: string;
}

interface OutsideMetroCharge {
  exists: boolean;
  rate: string;
  total: string;
  gst: string;
}

interface FuelLevy {
  rate: string;
  total: string;
}

interface OverallAdjustmentLineItem {
  rate: string;
  name: string;
  total: string;
}
interface JobTotalSummary {
  jobId: number;
  reference: string;
  service: string;
  date: string;
  start: string;
  end: string;
  units: string;
  clientRate: any;
  freight: string;
  freightGst: string;
  puds: PudLineItem[];
  freightAdjustments: FreightAdjustmentLineItems[];
  outsideMetroCharge: OutsideMetroCharge;
  freightSubtotal: string;
  freightGstSubtotal: string;
  tolls: string;
  tollsGst: string;
  fuelLevy: FuelLevy;
  fuelLevyGst: string;
  overallAdjustments: OverallAdjustmentLineItem[];
  jobSubtotal: string;
  jobSubtotalGst: string;
  gst: string;
  jobTotal: string;
}

@Component({
  components: {},
})
export default class ClientAccessJobTotals extends Vue {
  @Prop() public jobDetails: JobDetails;

  public rowHeight = 24;

  get jobTotalsSummary(): JobTotalSummary | string {
    const x = this.jobDetails;
    if (x.jobId === undefined) {
      return '-';
    }
    const jobEpoch = x.pudItems && x.pudItems[0] ? x.pudItems[0].epochTime : 0;
    const jobDate = jobEpoch ? returnFormattedDate(jobEpoch) : '';
    let rate: number | string = '';
    let units: string = '';
    const clientRateTypeId = x.accounting.clientRates[0].rate.rateTypeId;
    switch (clientRateTypeId) {
      case 1:
        rate = (
          x.accounting.clientRates[0].rate.rateTypeObject as TimeRateType
        ).rate.toFixed(2);

        const billedDuration =
          x.accounting.finishedJobData.clientDurations.readableBilledDuration;
        const breakDuration =
          x.accounting.finishedJobData.clientDurations.readableBreakDuration;

        if (breakDuration === '0m') {
          units = billedDuration;
        } else {
          units = breakDuration + ' brk = ' + billedDuration;
        }
        break;
      case 6:
        rate = (x.accounting.clientRates[0].rate.rateTypeObject as any).rate;
        units = 'Quoted Rate';
        break;
    }

    // service type short name
    const serviceTypeName = returnServiceTypeLongNameFromId(x.serviceTypeId);

    // pud line items
    const pudLineItems: PudLineItem[] = [];
    for (const pud of x.pudItems) {
      if (pud.legTypeFlag !== 'P' && pud.legTypeFlag !== 'D') {
        continue;
      } else {
        const pudReferences = pud.pickupReference.concat(pud.dropoffReference);
        let builtStr = '';
        pudReferences.forEach((ref: JobReferenceDetails, index: number) => {
          if (index === pudReferences.length - 1) {
            builtStr += ref.reference;
          } else {
            builtStr += ref.reference + ', ';
          }
        });

        pudLineItems.push({
          suburb: pud.address.suburb,
          reference: builtStr,
        });
      }
    }

    // // freight adjustments
    // const additionalChargeLineItems: FreightAdjustmentLineItems[] = [];
    const additionalCharges: AdditionalChargeSubtotal[] =
      x.accounting.totals.subtotals.additionalChargeItems;
    // // find index of tolls charge items

    // if (additionalCharges && additionalCharges.length > 0) {
    //   for (const subtotal of additionalCharges) {
    //     const quantity = subtotal.items?.length ?? 0;

    //     additionalChargeLineItems.push({
    //       name: subtotal.longName + ' (' + quantity + ')',
    //       rate: subtotal.total.client.toFixed(2),
    //       amount: subtotal.total.client.toFixed(2),
    //     });
    //   }
    // }

    // outside metro
    const outsideMetroRateExists: boolean =
      x.accounting.totals.subtotals.outsideMetroChargeTotals.client !==
        undefined &&
      x.accounting.totals.subtotals.outsideMetroChargeTotals.client !== null &&
      x.accounting.totals.subtotals.outsideMetroChargeTotals.client > 0;

    // overall adjustments
    const overallAdjustmentLineItems: OverallAdjustmentLineItem[] = [];
    // find index of tolls charge items
    const overallAdjustments = additionalCharges.filter(
      (a) => a.longName !== 'Tolls',
    );

    if (overallAdjustments && overallAdjustments.length > 0) {
      for (const adjustment of overallAdjustments) {
        const quantity = adjustment?.items?.length || 0;

        overallAdjustmentLineItems.push({
          rate: '',
          name: adjustment.longName + ' (' + quantity + ')',
          total: adjustment.total.client.toFixed(2),
        });
      }
    }

    let jobReference = '';
    if (x.jobReference && x.jobReference.length > 0) {
      for (let i = 0; i < x.jobReference.length; i++) {
        if (i === x.jobReference.length - 1) {
          jobReference += x.jobReference[i].reference;
        } else if (x.jobReference[i].reference !== '') {
          jobReference += x.jobReference[i].reference + ', ';
        }
      }
    }
    const acc = x.accounting;

    return {
      jobId: x.jobId,
      reference: jobReference,
      service: serviceTypeName,
      date: jobDate,
      start: acc.finishedJobData.clientDurations.readableStartTime,
      end: acc.finishedJobData.clientDurations.readableEndTime,
      units,
      clientRate: rate,
      freight: acc.totals.subtotals.freightCharges.client.toFixed(2),
      freightGst: acc.totals.subtotals.freightGstCharges.client.toFixed(2),
      puds: pudLineItems,
      freightAdjustments: [],
      outsideMetroCharge: {
        exists: outsideMetroRateExists,
        rate: outsideMetroRateExists
          ? acc.clientRates[0].outsideMetroRate + '%'
          : '',
        total: outsideMetroRateExists
          ? acc.totals.subtotals.outsideMetroChargeTotals.client.toFixed(2)
          : '',
        gst: outsideMetroRateExists
          ? acc.totals.subtotals.outsideMetroChargeGstTotals.client.toFixed(2)
          : '',
      },
      freightSubtotal:
        acc.totals.subtotals.freightChargeTotals.client.toFixed(2),
      freightGstSubtotal:
        acc.totals.subtotals.freightChargeGstTotals.client.toFixed(2),
      tolls: acc.totals.subtotals.tollCharges.tollCharges.client.toFixed(2),
      tollsGst:
        acc.totals.subtotals.tollCharges.tollGstCharges.client.toFixed(2),
      fuelLevy: {
        rate: acc.additionalCharges.clientFuelSurcharge
          ?.appliedFuelSurchargeRate
          ? acc.additionalCharges.clientFuelSurcharge.appliedFuelSurchargeRate +
            '%'
          : '',
        total: acc.totals.subtotals.fuelSurcharges.client
          ? acc.totals.subtotals.fuelSurcharges.client.toFixed(2)
          : '',
      },
      fuelLevyGst: acc.totals.subtotals.fuelGstSurcharges.client
        ? acc.totals.subtotals.fuelGstSurcharges.client.toFixed(2)
        : '',
      overallAdjustments: overallAdjustmentLineItems,
      jobSubtotal: acc.totals.subtotals.lessGst.client.toFixed(2),
      jobSubtotalGst: acc.totals.subtotals.gstCharges.client.toFixed(2),
      gst: acc.totals.subtotals.gstCharges.client.toFixed(2),
      jobTotal: acc.totals.finalTotal.client.toFixed(2),
    };
  }
}
