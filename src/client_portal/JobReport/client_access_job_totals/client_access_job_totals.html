<section class="client-access-jobTotalsSummary-totals">
   <v-layout px-3>
     <table
      class="table"
      v-if="jobTotalsSummary"
      style="border-spacing: 0px; width:100%; font-size:11px; "
      
       >
      <thead class="tableRow" style="box-sizing: border-box;" :style="{height: rowHeight + 'px' }">
        <tr :style="{height: rowHeight + 'px' }">
        <th
          style="text-align: left; font-weight:500;position:relative;  "
        >
        <b> Job ID</b>
          <div
            style="position:absolute; left:0;top:0; width:100%;  border-bottom:2px solid black;"
            :style="{height: rowHeight + 'px' }"
          ></div>
        </th>
        <th
          style="text-align: left; font-weight:500; position:relative; "
        >
        <b>  Reference</b>
          <div
            style="position:absolute; left:0;top:0; width:100%;  border-bottom:2px solid black;"
            :style="{height: rowHeight + 'px' }"
          ></div>
        </th>
        <th
          style="text-align: left; font-weight:500; position:relative;"
        >
        <b>  Service</b>
          <div
            style="position:absolute; left:0;top:0; width:100%;  border-bottom:2px solid black;"
            :style="{height: rowHeight + 'px' }"
          ></div>
        </th>
        <th
          style="text-align: left; font-weight:500; position:relative; "
        >
        <b> Date</b>
          <div
            style="position:absolute; left:0;top:0; width:100%;  border-bottom:2px solid black;"
            :style="{height: rowHeight + 'px' }"
          ></div>
        </th>
        <th
          style="text-align: left; font-weight:500; position:relative; "
        >
        <b> Start</b>
          <div
            style="position:absolute; left:0;top:0; width:100%;  border-bottom:2px solid black;"
            :style="{height: rowHeight + 'px' }"
          ></div>
        </th>
        <th
          style="text-align: left; font-weight:500; position:relative; "
        >
        <b> End</b>
          <div
            style="position:absolute; left:0;top:0; width:100%;  border-bottom:2px solid black;"
            :style="{height: rowHeight + 'px' }"
          ></div>
        </th>
        <th
          style="text-align: left; font-weight:500;position:relative; "
        >
        <b>  Units</b>
          <div
            style="position:absolute; left:0;top:0; width:100%;  border-bottom:2px solid black;"
            :style="{height: rowHeight + 'px' }"
          ></div>
        </th>
        <th style="text-align: left; font-weight:500; position:relative;">
          <b>Rate ($)</b>
          <div
            style="position:absolute; left:0;top:0; width:100%;  border-bottom:2px solid black;"
            :style="{height: rowHeight + 'px' }"
          ></div>
        </th>
        <th style="text-align: left; font-weight:500;position:relative;">
         <b>Amount ($)</b>
          <div
            style="position:absolute; left:0;top:0; width:100%;  border-bottom:2px solid black;"
            :style="{height: rowHeight + 'px' }"
          ></div>
        </th>
        <th style="text-align: left; font-weight:500;position:relative;">
          <b>GST ($)</b>
           <div
             style="position:absolute; left:0;top:0; width:100%;  border-bottom:2px solid black;"
             :style="{height: rowHeight + 'px' }"
           ></div>
         </th>
        </tr>
      </thead>
      <tbody>
        <tr class="tableRow">
          <td :style="{height: rowHeight + 'px' }" >
            <span style="overflow: hidden; display:block;
           text-overflow: ellipsis; width:60px; white-space: nowrap;"> {{jobTotalsSummary.jobId}}</span>
          </td>
          <td :style="{height: rowHeight + 'px' }" >
           <span style="overflow: hidden; display:block;
           text-overflow: ellipsis; width:80px; white-space: nowrap;"> {{jobTotalsSummary.reference}}</span>
          </td>
          <td :style="{height: rowHeight + 'px' }">
            <span style="overflow: hidden; display:block;
            text-overflow: ellipsis; width:60px; white-space: nowrap;">
            {{jobTotalsSummary.service}}</span>
          </td>
          <td :style="{height: rowHeight + 'px' }">
            <span style="overflow: hidden; display:block;
           text-overflow: ellipsis; width:60px; white-space: nowrap;"> {{jobTotalsSummary.date}}</span>
          </td>
          <td :style="{height: rowHeight + 'px' }">
            <span style="overflow: hidden; display:block;
            text-overflow: ellipsis; width:60px; white-space: nowrap;">{{jobTotalsSummary.start}}</span>
          </td>
          <td :style="{height: rowHeight + 'px' }">
            <span style="overflow: hidden; display:block;
            text-overflow: ellipsis; width:60px; white-space: nowrap;">{{jobTotalsSummary.end}}</span>
          </td>
          <td :style="{height: rowHeight + 'px' }">
            <span style="overflow: hidden; display:block;
            text-overflow: ellipsis; width:110px; white-space: nowrap;"> {{jobTotalsSummary.units}}</span>
          </td>
          <td :style="{height: rowHeight + 'px' }">
            <span style="overflow: hidden; display:block;
            text-overflow: ellipsis; width:80px; white-space: nowrap;">{{jobTotalsSummary.clientRate}}</span>
          </td>
          <td :style="{height: rowHeight + 'px' }">
            <span style="overflow: hidden; display:block;
            text-overflow: ellipsis; width:80px; white-space: nowrap;">{{jobTotalsSummary.freight}}</span>
          </td>
          <td :style="{height: rowHeight + 'px' }">
            <span style="overflow: hidden; display:block;
            text-overflow: ellipsis; width:80px; white-space: nowrap;">{{jobTotalsSummary.freightGst}}</span>
          </td>
        </tr>
      </tbody>
      <tbody>
        <tr class="tableRow" v-for="(pud, pudIndex) of jobTotalsSummary.puds">
          <td :style="{height: rowHeight + 'px' }"></td>
          <td :style="{height: rowHeight + 'px' }" colspan="5">
            <span style="overflow: hidden; display:block;
            text-overflow: ellipsis; max-width:110px; white-space: nowrap;"> {{pud.reference}}</span>
          </td>
          <td :style="{height: rowHeight + 'px' }" colspan="3">   <span style="overflow: hidden; display:block;
            text-overflow: ellipsis; max-width:110px; white-space: nowrap;">{{pud.suburb}}</span></td>
     
        </tr>
      </tbody>
      <tbody>
        <tr
          class="tableRow"
          v-for="(freightAdjustment, freightAdjustmentIndex) of jobTotalsSummary.freightAdjustments"
        >
          <td :style="{height: rowHeight + 'px' }" colspan="6"></td>
          <td :style="{height: rowHeight + 'px' }"><span style="overflow: hidden; display:block;
            text-overflow: ellipsis; width:110px; white-space: nowrap;"> {{freightAdjustment.name}}</span></td>
          <td :style="{height: rowHeight + 'px' }">{{freightAdjustment.rate}}</td>
          <td :style="{height: rowHeight + 'px' }">
            {{freightAdjustment.total}}
          </td>
        </tr>
      </tbody>
      <tbody v-if="jobTotalsSummary.outsideMetroCharge.exists">
        <tr class="tableRow">
          <td :style="{height: rowHeight + 'px' }" colspan="6"></td>
          <td :style="{height: rowHeight + 'px' }"><span style="overflow: hidden; display:block;
            text-overflow: ellipsis; width:110px; white-space: nowrap;"> Outside Metro</span></td>
          <td :style="{height: rowHeight + 'px' }">
            {{jobTotalsSummary.outsideMetroCharge.rate}}
          </td>
          <td :style="{height: rowHeight + 'px' }">
            {{jobTotalsSummary.outsideMetroCharge.total}}
          </td>
          <td :style="{height: rowHeight + 'px' }">
            {{jobTotalsSummary.outsideMetroCharge.gst}}
          </td>
        </tr>
      </tbody>
      <tbody>
        <tr class="tableRow">
          <td :style="{height: rowHeight + 'px' }" colspan="6"></td>
          <td :style="{height: rowHeight + 'px' }"><b><span style="overflow: hidden; display:block;
            text-overflow: ellipsis; width:110px; white-space: nowrap;"> Freight Subtotal</span></b></td>
          <td :style="{height: rowHeight + 'px' }"></td>
          <td :style="{height: rowHeight + 'px' }">
            <b>{{jobTotalsSummary.freightSubtotal}}</b>
          </td>
          <td :style="{height: rowHeight + 'px' }">
            <b>{{jobTotalsSummary.freightGstSubtotal}}</b>
          </td>
        </tr>
      </tbody>
      <tbody v-if="jobTotalsSummary.tolls !== '0.00'">
        <tr class="tableRow">
          <td :style="{height: rowHeight + 'px' }" colspan="6"></td>
          <td :style="{height: rowHeight + 'px' }"><b>Tolls</b></td>
          <td :style="{height: rowHeight + 'px' }"></td>
          <td :style="{height: rowHeight + 'px' }">
            <b>{{jobTotalsSummary.tolls}}</b>
          </td>
          <td :style="{height: rowHeight + 'px' }">
            <b>{{jobTotalsSummary.tollsGst}}</b>
          </td>
        </tr>
      </tbody>
      <tbody>
        <tr class="tableRow">
          <td :style="{height: rowHeight + 'px' }" colspan="6"></td>
          <td :style="{height: rowHeight + 'px' }"><b>Fuel Levy</b></td>
          <td :style="{height: rowHeight + 'px' }">
            <b>{{jobTotalsSummary.fuelLevy.rate}}</b>
          </td>
          <td :style="{height: rowHeight + 'px' }">
            <b>{{jobTotalsSummary.fuelLevy.total}}</b>
          </td>
          <td :style="{height: rowHeight + 'px' }">
            <b>{{jobTotalsSummary.fuelLevyGst}}</b>
          </td>
        </tr>
      </tbody>
      <tbody>
        <tr
          class="tableRow"
          v-for="(overallAdjustment, overallAdjustmentIndex) of jobTotalsSummary.overallAdjustments"
        >
          <td :style="{height: rowHeight + 'px' }" colspan="6"></td>
          <td :style="{height: rowHeight + 'px' }"><b><span style="overflow: hidden; display:block;
            text-overflow: ellipsis; width:110px; white-space: nowrap;"> {{overallAdjustment.name}}</span></b></td>
          <td :style="{height: rowHeight + 'px' }"><b>{{overallAdjustment.rate}}</b></b></td>
          <td :style="{height: rowHeight + 'px' }">
            <b>{{overallAdjustment.total}}</b>
          </td>
        </tr>
      </tbody>
      <tbody>
        <tr class="tableRow">
          <td :style="{height: rowHeight + 'px' }" colspan="6"></td>
          <td :style="{height: rowHeight + 'px' }"><b>Job Subtotal</b></td>
          <td :style="{height: rowHeight + 'px' }"></td>
          <td :style="{height: rowHeight + 'px' }">
            <b>{{jobTotalsSummary.jobSubtotal}}</b>
          </td>
          <td :style="{height: rowHeight + 'px' }">
            <b>{{jobTotalsSummary.jobSubtotalGst}}</b>
          </td>
        </tr>
      </tbody>
      <!-- <tbody>
        <tr class="tableRow">
          <td :style="{height: rowHeight + 'px' }" colspan="6"></td>
          <td :style="{height: rowHeight + 'px' }"><b>GST</b></td>
          <td :style="{height: rowHeight + 'px' }"></td>
          <td :style="{height: rowHeight + 'px' }">
            <b>{{jobTotalsSummary.gst}}</b>
          </td>
        </tr>
      </tbody> -->
      <tbody>
        <tr class="tableRow">
          <td
            :style="{height: rowHeight + 'px' }"
            colspan="6"
            style="position:relative"
          >
            <div
              style="position:absolute; left:0;top:0; width:100%;  border-bottom:1px solid black; border-top:1px solid black; background-color: #E0E0E0"
              :style="{height: rowHeight + 'px' }"
            ></div>
          </td>
          <td :style="{height: rowHeight + 'px' }" style="position:relative">
            <div
              style="position:absolute; left:0;top:0; width:100%;  border-bottom:1px solid black; border-top:1px solid black; background-color: #E0E0E0;z-index: 1;"
              :style="{height: rowHeight + 'px' }"
            >
            <span :style="{'line-height': rowHeight + 'px'}"> <b>Job Total</b></span>
            </div>
          </td>
          <td :style="{height: rowHeight + 'px' }" style="position:relative">
            <div
              style="position:absolute; left:0;top:0; width:100%;  border-bottom:1px solid black; border-top:1px solid black; background-color: #E0E0E0;z-index: 1;"
              :style="{height: rowHeight + 'px' }"
            >  <span :style="{'line-height': rowHeight + 'px'}"><b>$</b></span></div>
          </td>
          <td :style="{height: rowHeight + 'px' }" style="position:relative">
            <div
              style="position:absolute; left:0;top:0; width:100%;  border-bottom:1px solid black; border-top:1px solid black; background-color: #E0E0E0;z-index: 1;"
              :style="{height: rowHeight + 'px' }"
            >
           <span :style="{'line-height': rowHeight + 'px'}"> <b>{{jobTotalsSummary.jobTotal}}</b></span>
            </div>
          </td>
          <td :style="{height: rowHeight + 'px' }" style="position:relative">
            <div
              style="position:absolute; left:0;top:0; width:100%;  border-bottom:1px solid black; border-top:1px solid black; background-color: #E0E0E0;z-index: 1;"
              :style="{height: rowHeight + 'px' }"
            >
            </div>
          </td>
        </tr>
        <tr class="tableRow" >
          <td :style="{height: rowHeight + 'px' }" colspan="9"></td>
        </tr>
     
      </tbody>
     
       </table>
   </v-layout>
</section>
