import ClientAccessJobCard from '@/client_portal/Dashboard/client_access_job_card/index.vue';
import ClientAccessJobTotals from '@/client_portal/JobReport/client_access_job_totals/index.vue';
import JobBookingDeliverySummary from '@/components/booking/delivery_details/job_booking_delivery_summary.vue';
import PudPillsBar from '@/components/booking/delivery_details/pud_pills_bar.vue';
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import NotesList from '@/components/common/notes_list/notes_list.vue';
import ConsignmentNoteDialog from '@/components/operations/OperationDashboard/components/JobDetails/consignment_note_dialog/index.vue';
import {
  JobRouteMapConfig,
  RouteViewType,
} from '@/components/operations/maps/job_map_route/JobRouteMapConfig';
import JobMapRoute from '@/components/operations/maps/job_map_route/index.vue';
import { validateGpsData } from '@/helpers/DistanceHelpers/DistanceHelpers';
import { getVisibleNotesByUserRole } from '@/helpers/JobDataHelpers/JobDataHelpers';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import { InterfaceColors } from '@/interface-models/Company/BrandIdentity/InterfaceColors';
import { DivisionReportSettings } from '@/interface-models/Company/DivisionCustomConfig/DivisionCustomConfig';
import DriverDetailsShort from '@/interface-models/Driver/DriverDetails/DriverDetailsShort';
import FleetAsset from '@/interface-models/FleetAsset/FleetAsset';
import GpsPosition from '@/interface-models/Generic/Position/GpsPosition';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import {} from '@/interface-models/Reporting/GenerateReportRequest';
import { ProofOfDeliveryRequest } from '@/interface-models/Reporting/ProofOfDeliveryRequest';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { useAttachmentStore } from '@/store/modules/AttachmentStore';
import { useClientPortalStore } from '@/store/modules/ClientPortalStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useRootStore } from '@/store/modules/RootStore';
import Mitt from '@/utils/mitt';
import { nextTick, ref } from 'vue';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component({
  components: {
    DatePickerBasic,
    JobMapRoute,
    ClientAccessJobTotals,
    ClientAccessJobCard,
    NotesList,
    ConsignmentNoteDialog,
    JobBookingDeliverySummary,
    PudPillsBar,
  },
})
export default class ClientAccessJobReport extends Vue {
  @Prop() public clientDetails: ClientDetails;
  @Prop() public jobDetails: JobDetails;
  @Prop() public driverList: DriverDetailsShort[];
  @Prop() public fleetAssetList: FleetAsset[];
  @Prop() public interfaceColors: InterfaceColors;

  public isRequestingRouteData: boolean = false;
  public finishedLoadingRouteData: boolean = false;
  public gpsRouteData: GpsPosition[] = [];
  public getVisibleNotesByUserRole = getVisibleNotesByUserRole;
  public jobBookingDeliverySummary = ref({});
  public borderSideString: string = '';
  public bgColorString: string = '';

  public company = useCompanyDetailsStore().companyDetails;
  public reportsSettings: DivisionReportSettings | null =
    this.company?.divisions?.[0]?.customConfig?.reports ?? null;

  public isViewingConsignmentNoteDialog: boolean = false;

  public jobMapConfig: JobRouteMapConfig = {
    defaultView: RouteViewType.CAPTURED,
    enableViewToggle: false,
    showCapturedRoute: true,
    showPlannedRoute: false,
    showStopMarkers: true,
    showCapturedRouteSlider: false,
  };

  /**
   * Requests the GPS data from the job and sets the response to local
   * properties
   */
  public async requestJobRoute() {
    if (this.jobDetails.jobId) {
      this.isRequestingRouteData = true;
      this.finishedLoadingRouteData = false;
      // Send request
      const result =
        await useClientPortalStore().retrieveClientGPSPositionListByJobId(
          this.jobDetails.jobId,
        );
      // Validate and clean data if it's non-null
      this.setJobRouteResponse(validateGpsData(result ?? []));
    }
  }

  /**
   * Sets the response from the request to the local properties.
   * @param routeData The clean and validated GPS data associated with the job.
   */
  public setJobRouteResponse(routeData: GpsPosition[]) {
    this.gpsRouteData = routeData;
    this.isRequestingRouteData = false;
    this.finishedLoadingRouteData = true;
  }

  /**
   * Dispatch request to generate and download a POD for the current job.
   */
  public getProofOfDelivery(accessMethod: ReportAccessMethodTypes) {
    if (!this.jobDetails?.jobId) {
      return;
    }
    useRootStore().setGlobalLoader(true);
    this.setResponseListener(true);
    const request: ProofOfDeliveryRequest = {
      jobId: this.jobDetails.jobId,
      accessType: accessMethod,
    };
    useAttachmentStore().generateProofOfDelivery(request);
  }

  /**
   * Callback for mitt listener
   * @param response response payload containing report
   */
  public handleReportResponse(): void {
    useRootStore().setGlobalLoader(false);
    this.setResponseListener(false);
  }
  /**
   * When we make the request to download a report, we turn on the mitt listener so
   * we can reset the loader when the response comes in
   * @param value boolean value to set the listener
   */
  public setResponseListener(value: boolean) {
    if (value) {
      Mitt.on('encodedReport', this.handleReportResponse);
    } else {
      Mitt.off('encodedReport', this.handleReportResponse);
    }
  }
  /**
   * When the component is destroyed, we remove the mitt listener
   */
  public beforeDestroy() {
    this.setResponseListener(false);
  }

  /**
   * Show the consignment note dialog, which is used to dispatch request to
   * generate and download a consignment note for the current job.
   */
  public getConsignmentNote() {
    if (!this.jobDetails) {
      return;
    }
    this.isViewingConsignmentNoteDialog = true;
  }

  /**
   * Used in template to determine if we should show the button that allows a
   * POD to be downloaded
   */
  get podReady() {
    if (!this.jobDetails) {
      return false;
    }
    return this.jobDetails.workStatus >= WorkStatus.REVIEWED;
  }

  public scrollToMapSection(): void {
    const el = document.getElementById('map');
    if (el) {
      nextTick(() => {
        el.scrollIntoView({
          behavior: 'smooth', // Smooth scroll to the element
          block: 'start', // Align to the top of the element
        });
      });
    }
  }

  public mounted() {
    if (this.interfaceColors) {
      this.borderSideString = `border-left: 1px solid ${this.interfaceColors.info}`;
      this.bgColorString = `background-color:${this.interfaceColors.info}`;
    }
    this.requestJobRoute();
  }
}
