<template>
  <v-layout class="pt-2">
    <v-flex md12>
      <ClientDetailsClientContacts
        :clientId="props.clientDetails.clientId"
        :client_id="props.clientDetails._id"
        :clientName="props.clientDetails.displayName"
        :clientPersonIds="props.clientDetails.clientPersonDispatchers"
        :defaultDispatcherId="props.clientDetails.defaultDispatcherId"
      />
      <ClientDetailsCommonAddressManagement
        :clientId="props.clientDetails.clientId"
        :client_id="props.clientDetails._id"
        :clientPersonIds="props.clientDetails.clientPersonDispatchers"
        :isDialogOpen="true"
      />
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
// import JobDetails from '@/interface-models/Jobs/JobDetails';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
// import DriverDetailsShort from '@/interface-models/Driver/DriverDetails/DriverDetailsShort';
// import FleetAsset from '@/interface-models/FleetAsset/FleetAsset';
import ClientDetailsCommonAddressManagement from '@/components/admin/ClientDetails/components/client_details_common_address/client_details_common_address_management.vue';
import ClientDetailsClientContacts from '@/components/admin/ClientDetails/components/client_details_contacts/client_details_client_contacts_management.vue';

const props = withDefaults(
  defineProps<{
    // clientJobList: JobDetails[];
    // clientDriverList: DriverDetailsShort[];
    // fleetAssetList: FleetAsset[];
    clientDetails: ClientDetails;
  }>(),
  {
    // clientJobList: () => [],
    // clientDriverList: () => [],
    // fleetAssetList: () => [],
    clientDetails: () => ({}) as ClientDetails,
  },
);
</script>
