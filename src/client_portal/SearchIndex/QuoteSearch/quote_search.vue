<template>
  <v-layout wrap pt-2 class="job-search pa-4">
    <v-flex md12 class="job-search-header-section pa-2">
      <v-layout wrap class="mb-2">
        <v-flex md12>
          <v-form ref="jobSearchParametersForm">
            <v-layout wrap>
              <v-flex md2 pr-2 grow>
                <v-select
                  solo
                  flat
                  label="Search By"
                  v-model="selectedSearchByMenuId"
                  :items="searchByOptions"
                  item-value="id"
                  item-text="longName"
                  class="v-solo-custom"
                  hint="Search by"
                  persistent-hint
                  @change="selectOptionChanged"
                  color="orange"
                ></v-select>
              </v-flex>
              <v-flex md4 pr-2 grow>
                <v-text-field
                  v-if="selectedSearchByMenuId === 2"
                  solo
                  flat
                  label="Quote ID"
                  class="v-solo-custom"
                  :prefix="quoteIdPrefix"
                  hint="Quote ID"
                  persistent-hint
                  v-model="searchQuoteRequest.quoteId"
                  :rules="[validationRules.required]"
                  @keyup.enter="runQuery"
                >
                </v-text-field>

                <v-autocomplete
                  id="client-job-select"
                  v-if="selectedSearchByMenuId === 1"
                  solo
                  flat
                  v-model.sync="searchQuoteRequest.clientId"
                  :items="clientSelectList"
                  item-value="clientId"
                  :item-text="
                    (item) => `${item.clientId} - ${item.tradingName}`
                  "
                  label="Client Select"
                  hint="Client Select"
                  class="v-solo-custom"
                  browser-autocomplete="off"
                  :allow-overflow="false"
                  hide-selected
                  color="orange"
                  cache-items
                  clearable
                  persistent-hint
                >
                </v-autocomplete>
              </v-flex>
              <v-flex md3 pr-2 grow v-if="selectedSearchByMenuId === 1">
                <DatePickerBasic
                  solo
                  flat
                  :key="selectedSearchByMenuId"
                  @setEpoch="setStartDate"
                  :soloInput="true"
                  :labelName="'Quote Created Start Date'"
                  :yearOnly="false"
                  :clearable="true"
                  :epochTime="searchQuoteRequest.startEpoch"
                />
              </v-flex>
              <v-flex md3 pr-2 grow v-if="selectedSearchByMenuId === 1">
                <DatePickerBasic
                  solo
                  flat
                  :key="selectedSearchByMenuId"
                  @setEpoch="setEndDate"
                  :soloInput="true"
                  :labelName="'Quote Created End Date'"
                  :clearable="true"
                  :yearOnly="false"
                  :epochTime="searchQuoteRequest.endEpoch"
                />
              </v-flex>
              <v-flex md3 pr-2 grow v-if="selectedSearchByMenuId === 1">
                <v-text-field
                  solo
                  flat
                  persistent-hint
                  hint="References"
                  label="References"
                  class="v-solo-custom"
                  v-model="searchQuoteRequest.jobReference"
                >
                </v-text-field>
              </v-flex>
              <v-flex md3 pr-2 grow v-if="selectedSearchByMenuId === 1">
                <v-text-field
                  solo
                  flat
                  persistent-hint
                  hint="Dispatcher Name"
                  label="Dispatcher Name"
                  v-model="searchQuoteRequest.dispatcherName"
                  class="v-solo-custom"
                >
                </v-text-field>
              </v-flex>
              <v-flex md3 pr-2 grow v-if="selectedSearchByMenuId === 1">
                <v-text-field
                  solo
                  flat
                  persistent-hint
                  hint="Suburb"
                  label="Suburb"
                  v-model="searchQuoteRequest.suburbName"
                  class="v-solo-custom"
                >
                </v-text-field>
              </v-flex>
              <v-flex pr-4 mr-2>
                <v-btn
                  block
                  @click="runQuery"
                  class="search-button"
                  :loading="isLoadingTableData"
                >
                  <v-icon class="pr-2" size="18">search</v-icon>
                  Search Quote</v-btn
                >
                <p class="btn-hint">Press Enter or click to search.</p>
              </v-flex>
            </v-layout>
          </v-form>
        </v-flex>

        <v-flex md12>
          <v-divider></v-divider>
        </v-flex>
      </v-layout>
    </v-flex>
    <v-flex md12>
      <v-data-table
        :headers="tableHeaders"
        class="accounting-data-table"
        :items="quoteSearchTableData"
        :loading="isLoadingTableData"
        hide-actions
      >
        <template v-slot:items="props">
          <tr
            class="job-search-table-row"
            @click="viewQuoteDetails(props.item.id)"
            style="cursor: pointer"
          >
            <td>{{ quoteIdPrefix }}{{ props.item.quoteId }}</td>
            <td>
              {{
                returnFormattedDate(
                  props.item.jobDetails.date,
                  'DD/MM/YY hh:mm',
                )
              }}
            </td>
            <td>
              {{
                returnFormattedDate(
                  props.item.quoteCreationTime,
                  'DD/MM/YY hh:mm',
                )
              }}
            </td>
            <td>
              {{ props.item.jobDetails.clientId }} ({{
                props.item.jobDetails.clientName
              }})
            </td>
            <td>
              {{
                returnServiceTypeLongNameFromId(
                  props.item.jobDetails.clientServiceTypeId,
                )
              }}
              - {{ rateTypeName(props.item.jobDetails.clientRateTypeId) }}
            </td>
            <td>{{ props.item.jobDetails.reference }}</td>
            <td>
              {{ props.item.jobDetails.from }}
              <template v-if="props.item.jobDetails.numberOfLegs > 2">
                →
                {{ ' +' + Math.max(0, props.item.jobDetails.numberOfLegs - 2) }}
              </template>
              <template v-if="props.item.jobDetails.to">
                → {{ props.item.jobDetails.to }}
              </template>
            </td>
            <td>{{ props.item.jobDetails.dispatcherName }}</td>
            <td class="charge-value">
              {{
                '$' + displayCurrencyValue(props.item.jobDetails.clientCharge)
              }}
            </td>
            <td class="value-txt" :class="props.item.status">
              {{ formatStatus(props.item.status) }}
            </td>
            <td class="value-txt" :class="props.item.status">
              {{ props.item?.bookedJobId ? props.item?.bookedJobId : '-' }}
            </td>
          </tr>
        </template>
      </v-data-table>

      <v-layout justify-center>
        <Pagination
          @pageIncrement="pageIncrement"
          :pagination="pagination"
          @change="searchQuote"
          :rowsPerPage.sync="rowsPerPage"
        />
      </v-layout>
    </v-flex>

    <ContentDialog
      :showDialog.sync="isViewingDialog"
      :title="`Selected Quote ${quoteIdPrefix}${selectedQuoteDetails?.quoteId}`"
      width="45%"
      @cancel="clearQuote"
      contentPadding="pa-2"
      :showActions="false"
    >
      <v-flex md12 class="body-scrollable--65 body-min-height--65 pa-2">
        <v-flex md12 class="summary-container">
          <table class="summary-table">
            <tbody>
              <tr v-for="(item, index) in quoteSummaryItems" :key="index">
                <td class="title-txt">{{ item.title }}</td>
                <td
                  class="value-txt"
                  :class="item.title === 'Status' ? item.value : ''"
                >
                  {{ item.value }}
                </td>
              </tr>
            </tbody>
          </table>
        </v-flex>

        <v-layout column class="send-email-container">
          <v-layout
            row
            align-center
            justify-space-between
            class="send-email-header"
          >
            <div class="d-flex align-center">
              <span class="subheader--faded">EMAIL QUOTE DETAILS</span>
              <v-icon
                v-if="quoteEmailSendSuccessful"
                color="success"
                class="pl-2"
                >done_all</v-icon
              >
            </div>
            <div class="d-flex align-center">
              <v-checkbox
                class="email-checkbox"
                v-model="sendEmailToMe"
                label="Send to Me"
                hide-details
              />
              <v-btn
                depressed
                plain
                large
                class="send-email-btn ml-4"
                :loading="isSendingQuoteEmail"
                @click="generateQuoteReport"
                :disabled="
                  quoteEmailSendSuccessful || sendToEmailAddressList.length <= 0
                "
              >
                <v-icon class="pl-1 pr-2" size="16">send</v-icon>
                {{ quoteEmailSendSuccessful ? 'Email Sent' : 'Send Email' }}
              </v-btn>
            </div>
          </v-layout>

          <v-layout row>
            <v-flex xs12>
              <v-combobox
                v-model="sendToEmailAddressList"
                :items="sendToEmailAddressList"
                chips
                class="v-solo-custom"
                solo
                multiple
                flat
                :disabled="quoteEmailSendSuccessful"
                color="light-blue"
                hint="Enter an email address then press enter to add"
                label="Add Email Recipients"
                :rules="[validateEmailAddressList]"
                ref="emailAddressCombobox"
                clearable
                persistent-hint
              >
                <template v-slot:selection="data">
                  <v-chip
                    :key="JSON.stringify(data.item)"
                    :selected="data.selected"
                    :disabled="data.disabled"
                    class="v-chip--select-multi"
                    @click.stop="data.parent.selectedIndex = data.index"
                    @input="data.parent.selectItem(data.item)"
                    :class="
                      validationRules.email(data.item) === true
                        ? 'green darken-4'
                        : 'red darken-4'
                    "
                    close
                  >
                    <v-avatar
                      class="white--text"
                      :class="
                        validationRules.email(data.item) === true
                          ? 'green darken-2'
                          : 'red darken-2'
                      "
                    >
                      {{ data.item.slice(0, 2).toUpperCase() }}
                    </v-avatar>
                    {{ data.item }}
                  </v-chip>
                </template>
              </v-combobox>
            </v-flex>
          </v-layout>
        </v-layout>
      </v-flex>
      <v-divider></v-divider>
      <v-layout wrap mt-2 mb-2 pl-2 pr-2>
        <v-btn outline flat color="error" class="action-btn" @click="clearQuote"
          >Cancel</v-btn
        >
        <v-spacer></v-spacer>
        <v-btn
          solo
          class="action-btn save"
          @click="applyQuoteId"
          :loading="isAwaitingSaveResponse || isSendingQuoteEmail"
          :disabled="selectedQuoteDetails?.status !== 'VALID'"
          >Book Job</v-btn
        >
      </v-layout>
    </ContentDialog>
    <JobBookingSuccessDialog
      v-if="successDialogIsOpen"
      class="success-dialog"
      :isDialogOpen="successDialogIsOpen"
      :bookingDetails="successDialogConfig"
      @bookNewJob="successDialogIsOpen = false"
      @editRebookJob="editJob"
      @returnToDashboard="successDialogIsOpen = false"
    />
  </v-layout>
</template>

<script setup lang="ts">
import JobBookingSuccessDialog from '@/components/booking/job_booking_success_dialog.vue';
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import Pagination from '@/components/common/pagination/index.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import {
  returnEndOfDayFromEpoch,
  returnFormattedDate,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { quoteIdPrefix } from '@/helpers/JobBooking/JobBookingQuoteHelpers.ts';
import {
  GENERIC_ERROR_MESSAGE,
  showJobBookedNotification,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { returnServiceTypeLongNameFromId } from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import ClientSearchSummary from '@/interface-models/Client/ClientDetails/ClientSearchSummary';
import { SearchByOption } from '@/interface-models/Generic/SearchByOption';
import {
  ServiceTypeRates,
  serviceTypeRates,
} from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { VPagination } from '@/interface-models/Generic/VPagination';
import { AdditionalJobData } from '@/interface-models/Jobs/AdditionalJobData/AdditionalJobData';
import JobStatusUpdate from '@/interface-models/Jobs/Event/JobStatusUpdate';
import {
  JobBookingType,
  JobSuccessDialogConfig,
} from '@/interface-models/Jobs/JobBookingType';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { JobOperationType } from '@/interface-models/Jobs/JobOperationType';
import {
  GenerateQuoteReportRequest,
  QuoteReportRequestType,
} from '@/interface-models/Jobs/Quote/GenerateQuoteReportRequest';
import {
  SearchQuoteRequest,
  SearchQuoteRequestModel,
} from '@/interface-models/Jobs/SearchQuote/SearchQuoteRequest';
import { SearchQuoteRequestSortByField } from '@/interface-models/Jobs/SearchQuote/SearchQuoteRequestSortByFieldEnum';
import {
  SearchQuoteStatusEnum,
  SearchQuoteSummary,
} from '@/interface-models/Jobs/SearchQuote/SearchQuoteSummary';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { useAppNavigationStore } from '@/store/modules/AppNavigationStore';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useJobBookingStore } from '@/store/modules/JobBookingStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { sessionManager } from '@/store/session/SessionState';
import moment from 'moment-timezone';
import { computed, onBeforeUnmount, Ref, ref, WritableComputedRef } from 'vue';
import { useRouter } from 'vue-router/composables';

const router = useRouter();

const jobStore = useJobStore();
const clientDetailsStore = useClientDetailsStore();
const jobBookingStore = useJobBookingStore();

const isViewingDialog: Ref<boolean> = ref(false);
const successDialogIsOpen: Ref<boolean> = ref(false);

const isLoadingTableData: Ref<boolean> = ref(false);
const isAwaitingSaveResponse: Ref<boolean> = ref(false);

const quoteEmailSendSuccessful: Ref<boolean> = ref(false);
const isSendingQuoteEmail: Ref<boolean> = ref(false);

const sendToEmailAddressList: Ref<string[]> = ref([]);
const currentUserEmail: Ref<string> = ref(sessionManager.getUserName());

const searchQuoteRequest: Ref<SearchQuoteRequest> = ref(
  new SearchQuoteRequestModel(),
);

const bookedQuoteId: Ref<number> = ref(0);
const clientId: Ref<string> = ref('');

const savedJobDetails: Ref<JobDetails> = ref(new JobDetails());

const successDialogConfig: Ref<JobSuccessDialogConfig> =
  ref<JobSuccessDialogConfig>({
    type: JobBookingType.REBOOK,
    details: new JobDetails(),
  });

const page: Ref<number> = ref(1);
const rowsPerPage: Ref<number> = ref(50);
const jobSearchParametersForm: Ref<any> = ref(null);

const selectedQuoteDetails: Ref<SearchQuoteSummary | null> = ref(null);
const selectedSearchByMenuId: Ref<number> = ref(1);

const quoteList = computed(() => jobStore.requestedQuoteSearchSummary);
const quoteSearchTableData = computed(() => setTableData());

const clientSelectList = computed<ClientSearchSummary[]>(() => {
  return clientDetailsStore.clientListForBooking;
});

// search options
const searchByOptions = computed<SearchByOption[]>(() => [
  {
    id: 1,
    longName: 'Quote Details',
    keyName: 'quoteDetails',
  },
  {
    id: 2,
    longName: 'Quote ID',
    keyName: 'quoteId',
  },
]);

const tableHeaders: TableHeader[] = [
  {
    text: 'Quote #',
    align: 'left',
    value: 'quoteId',
    sortable: false,
    visible: true,
  },
  {
    text: 'Created Date',
    align: 'left',
    value: 'createdDate',
    sortable: false,
    visible: true,
  },
  {
    text: 'Work Date',
    align: 'left',
    value: 'workDate',
    sortable: false,
    visible: true,
  },
  {
    text: 'Client',
    align: 'left',
    value: 'clientName',
    sortable: false,
    visible: true,
  },
  {
    text: 'Service/Rate',
    align: 'left',
    value: 'clientService',
    sortable: false,
    visible: true,
  },
  {
    text: 'Reference',
    align: 'left',
    sortable: false,
    value: 'reference',
    visible: true,
  },
  {
    text: 'Stop',
    align: 'left',
    value: 'from',
    sortable: false,
    visible: true,
  },
  {
    text: 'Dispatcher',
    align: 'left',
    value: 'dispatcherName',
    sortable: false,
    visible: true,
  },
  {
    text: 'Client Charge (inc. GST)',
    align: 'left',
    value: 'clientCharge',
    sortable: false,
    visible: true,
    width: '120px',
  },
  {
    text: 'Status',
    align: 'left',
    value: 'status',
    sortable: true,
    visible: true,
  },
  {
    text: 'Booked Job ID',
    align: 'left',
    value: 'bookedJobId',
    sortable: true,
    visible: true,
  },
];

const pagination = computed<VPagination>(() => ({
  descending: true,
  page: page.value,
  rowsPerPage: rowsPerPage.value,
  totalItems: jobStore.requestedQuoteSearchSummaryCount,
}));

function setStartDate(startDateEpoch: number | null) {
  searchQuoteRequest.value.startEpoch = startDateEpoch;
}

function setEndDate(endDateEpoch: number | null) {
  searchQuoteRequest.value.endEpoch =
    endDateEpoch !== null ? returnEndOfDayFromEpoch(endDateEpoch) : null;
}

// function to increment page and send search request
function pageIncrement(value: number) {
  page.value += value;
  searchQuote();
}

// creates quote summary object for view quote dialog
const quoteSummaryItems = computed(() => {
  if (!selectedQuoteDetails.value) {
    return [];
  }

  const job = selectedQuoteDetails.value.jobDetails;
  return [
    {
      title: 'Quote ID',
      value: `${quoteIdPrefix}${selectedQuoteDetails.value.quoteId}`,
    },
    {
      title: 'Status',
      value:
        selectedQuoteDetails.value.status ===
        SearchQuoteStatusEnum.INVALID_PREVIOUSLY_USED
          ? `${formatStatus(
              selectedQuoteDetails.value.status,
            )} (See Job ID - ${selectedQuoteDetails.value?.bookedJobId})`
          : formatStatus(selectedQuoteDetails.value.status),
    },
    {
      title: 'Client',
      value: `${job.clientId} - ${job.clientName}`,
    },
    {
      title: 'Created',
      value: returnFormattedDate(
        selectedQuoteDetails.value.quoteCreationTime,
        'DD/MM/YYYY HH:mm',
      ),
    },
    {
      title: 'Job Date',
      value: returnFormattedDate(job.date, 'DD/MM/YYYY HH:mm'),
    },
    {
      title: 'Dispatcher',
      value: job.dispatcherName,
    },
    {
      title: 'Service Type',
      value: returnServiceTypeLongNameFromId(job.clientServiceTypeId),
    },
    {
      title: 'Client Charge',
      value: '$' + displayCurrencyValue(job.clientCharge),
    },
    {
      title: 'Reference',
      value: job.reference,
    },
    {
      title: 'Stops',
      value: `${job.from} → ${job.to}`,
    },
    {
      title: 'Expires',
      value: returnFormattedDate(
        selectedQuoteDetails.value.quoteExpiryTime,
        'DD/MM/YYYY',
      ),
    },
  ];
});

// checks if search is QuoteID or QuoteDetails
function selectOptionChanged(selectedId: number) {
  // if selected is QuoteID we reset the other params
  if (selectedId === 2) {
    searchQuoteRequest.value.startEpoch = null;
    searchQuoteRequest.value.endEpoch = null;
    searchQuoteRequest.value.clientId = '';
    searchQuoteRequest.value.jobReference = '';
    searchQuoteRequest.value.dispatcherName = '';
    searchQuoteRequest.value.suburbName = '';
    return;
  } else {
    // if quoteDetails is selected we reset quoteId
    searchQuoteRequest.value.quoteId = null;
  }
  if (
    searchQuoteRequest.value.startEpoch === null ||
    searchQuoteRequest.value.endEpoch === null
  ) {
    searchQuoteRequest.value.startEpoch = moment
      .tz(moment.tz.guess())
      .startOf('day')
      .valueOf();
    searchQuoteRequest.value.endEpoch = moment
      .tz(moment.tz.guess())
      .endOf('day')
      .valueOf();
  }
}

/**
 * A computed property that checks whether the current user's email
 * is included in the `sendToEmailAddressList`.
 *
 * - Returns `true` if the current user's email is in the list.
 * - Sets; Adds or removes the current user's email from the list based on the boolean value.
 *
 */
const sendEmailToMe: WritableComputedRef<boolean> = computed({
  get: () => sendToEmailAddressList.value.includes(currentUserEmail.value),
  set: (val: boolean) => {
    const email = currentUserEmail.value;
    if (val && !sendToEmailAddressList.value.includes(email)) {
      sendToEmailAddressList.value.push(email);
    } else if (!val) {
      sendToEmailAddressList.value = sendToEmailAddressList.value.filter(
        (e) => e !== email,
      );
    }
  },
});

/**
 * Sends a quote email.
 * Constructs the email request object and triggers the email sending process.
 */
function generateQuoteReport(): void {
  if (selectedQuoteDetails.value) {
    const request: GenerateQuoteReportRequest = {
      quoteMongoId: selectedQuoteDetails.value.id ?? '',
      requestType: QuoteReportRequestType.SEND,
      emailTo: sendToEmailAddressList.value,
      accessType: ReportAccessMethodTypes.EMAIL,
    };
    sendQuoteEmail(request);
  }
}
/**
 * Sends a quote email by calling the job booking store action.
 * Sets loading and success flags accordingly.
 *
 * @param request - The quote report request object containing email and quote details.
 */
async function sendQuoteEmail(
  request: GenerateQuoteReportRequest,
): Promise<void> {
  isSendingQuoteEmail.value = true;
  try {
    await jobBookingStore.generateQuoteReport(request);
    quoteEmailSendSuccessful.value = true;
  } finally {
    isSendingQuoteEmail.value = false;
  }
}

/**
 * Validates a list of email addresses.
 *
 * @param {string[]} emailAddresses - The list of email addresses to validate.
 * @returns {boolean|string} - Returns `true` if all email addresses are valid,
 * otherwise returns an error message.
 */
function validateEmailAddressList(emailAddresses: string[]): boolean | string {
  return emailAddresses.every((e) => validationRules.email(e) === true)
    ? true
    : 'One or more email addresses is invalid.';
}

/**
 * Executes the quote search by resetting the pagination if needed,
 * then triggers the search logic.
 */
function runQuery() {
  if (pagination.value.page) {
    if (pagination.value.page > 1) {
      pageIncrement(-(pagination.value.page - 1));
    }
  }
  searchQuote();
}
/**
 * Validates the quoteID, prepares the request parameters,
 * calls the store function to fetch quote summaries.
 */
function searchQuote() {
  // Validate the fields if not using the client portal (to restrict results)
  if (!jobSearchParametersForm.value.validate()) {
    const searchByName = searchByOptions.value.find(
      (x: SearchByOption) => x.id === selectedSearchByMenuId.value,
    );
    const valueName = searchByName ? searchByName.longName : 'value';
    showNotification(
      `Please enter the ${valueName} you wish to search for, then try again.`,
    );
    return;
  }
  isLoadingTableData.value = true;
  searchQuoteRequest.value.jobIds = [];
  searchQuoteRequest.value.sortByField =
    SearchQuoteRequestSortByField.ACCEPTED_DATE;
  searchQuoteRequest.value.sortDirection = null;
  // sends search quote request
  jobStore.searchQuoteSummary(
    searchQuoteRequest.value,
    pagination.value.page,
    pagination.value.rowsPerPage,
  );
}

// display currency as two decimal
function displayCurrencyValue(
  value: number,
  decimalPlaces: number = 2,
): string {
  if (!value) {
    return '0.00';
  }
  const rounded = value
    .toFixed(decimalPlaces)
    .replace(/\d(?=(\d{3})+\.)/g, '$&,');

  return rounded;
}

// finds relevant quote from quote list using quote id
// opens quote details dialog
function viewQuoteDetails(id: string) {
  const summary = quoteList.value.find(
    (item) => String(item.id) === String(id),
  );
  if (!summary) {
    console.warn(`Quote ID ${id} not found in summary list`);
    return;
  }
  selectedQuoteDetails.value = summary;
  isViewingDialog.value = true;
}

// sets table data and loading state
function setTableData() {
  isLoadingTableData.value = false;
  return quoteList.value;
}

// function to get rateType type long name using rateTypeId
function rateTypeName(rateTypeId: number) {
  const rateType = serviceTypeRates.find(
    (item: ServiceTypeRates) => item.rateTypeId === rateTypeId,
  );
  return rateType?.longName || '-';
}

// returns the formatted status from SearchQuoteStatusEnum
function formatStatus(status: SearchQuoteStatusEnum | string): string {
  switch (status) {
    case SearchQuoteStatusEnum.VALID:
      return 'Valid';
    case SearchQuoteStatusEnum.INVALID_EXPIRED:
      return 'Expired';
    case SearchQuoteStatusEnum.INVALID_PREVIOUSLY_USED:
      return 'Previously Used';
    default:
      return 'Unknown';
  }
}

// clear selected quote and close view quote dialog
function clearQuote() {
  selectedQuoteDetails.value = null;
  isViewingDialog.value = false;
}

/**
 * Attempts to retrieve a jobDetails based on the selected quote's ID
 * and initiate the booking process.
 */
async function applyQuoteId() {
  if (selectedQuoteDetails.value) {
    const id = selectedQuoteDetails.value.quoteId;
    if (id) {
      const result = await jobBookingStore.getQuoteById(id);
      if (result) {
        bookJob(result?.jobDetails);
      }
    }
  }
}

/**
 * Books the provided job details and handles quote acceptance logic.
 * Displays job booking success dialog if job is successfully booked.
 *
 * @param jobDetails - The job details to be booked. Must contain valid PUD items and route data.
 */
async function bookJob(jobDetails: JobDetails | null) {
  if (
    !jobDetails ||
    (jobDetails.pudItems.length > 1 && !jobDetails.plannedRoute)
  ) {
    showNotification(`Job booking failed. No route data.`, {
      title: 'Rebook Job',
    });

    console.error(
      `Failed to rebook job from ${jobDetails?.displayId}, with addresses:`,
      jobDetails?.pudItems.map((x) => x.address),
    );
    return;
  }
  isAwaitingSaveResponse.value = true;

  jobDetails.additionalJobData = new AdditionalJobData();
  const appliedQuoteId = selectedQuoteDetails.value?.id ?? '';
  jobDetails.additionalJobData.appliedQuoteId = appliedQuoteId;
  const result = await jobStore.saveJobDetails(jobDetails);

  // If we're booking a new job, booking from a quote from a quote, and the
  // job save was successful, then we need to mark the quote as accepted
  if (result && result.jobDetails?.jobId) {
    await jobBookingStore.acceptQuote(appliedQuoteId, result.jobDetails.jobId);
  }

  if (result?.jobDetails) {
    bookedQuoteId.value = result.jobId;
    clientId.value = result.jobDetails.client.id;
    savedJobDetails.value = result.jobDetails;

    const bookedEvent = result.jobDetails.eventList.find(
      (x: JobStatusUpdate) => x.updatedStatus === 'Booked',
    );
    if (bookedEvent?.editedBy !== sessionManager.getActiveUser()) {
      showJobBookedNotification({
        jobId: result.jobId,
        jobDate: result.jobDetails.jobDate,
        clientName: result.jobDetails.client.clientName,
      });
    } else {
      successDialogConfig.value = {
        type: JobBookingType.REBOOK,
        details: result.jobDetails,
      };
      // clear selected quote and close quote dialog
      clearQuote();
      // update table to reflect new changes
      searchQuote();
      successDialogIsOpen.value = true;
    }
  } else {
    showNotification(GENERIC_ERROR_MESSAGE, { title: 'Quote Job' });
  }
  isAwaitingSaveResponse.value = false;
}

/**
 * Prepares and navigates to the job booking page to edit a quote booked job.
 * Sets the necessary job config in the operations store and updates the app's navigation context.
 */
function editJob() {
  const reBookJobBookingPageConfig = {
    jobId: bookedQuoteId.value ?? 0,
    operationType: JobOperationType.EDIT,
    jobDetails: savedJobDetails.value,
    clientId: clientId.value ?? '',
  };
  // Store the config to be accessed in the job booking component
  useOperationsStore().setReBookJobBookingPageConfig(
    reBookJobBookingPageConfig,
  );
  // Navigate to the operations module and set UI context
  router.push('/operations').then(() => {
    useAppNavigationStore().setCurrentRouteTitle('operations_index');
    useAppNavigationStore().setCurrentComponentId('#job-booking');
  });
}

onBeforeUnmount(() => {
  // resets table on exit
  jobStore.searchQuoteSummary();
});
</script>

<style lang="scss" scoped>
.job-search {
  .search-button {
    border: 1px solid var(--accent) !important;
    background-color: var(--info) !important;
  }
}
.btn-hint {
  position: absolute;
  color: var(--light-text-color);
  display: flex;
  flex-direction: row;
  justify-content: left;
  align-items: left;
  margin-left: 32px;
  font-size: $font-size-12;
}

.action-btn {
  border-radius: $border-radius-btn;
  box-shadow: none !important;
  min-width: 140px;
  min-height: 38px;
  &:hover {
    box-shadow: $box-shadow !important;
  }

  &.save {
    min-width: 200px;
    background-color: var(--info) !important;
  }
}

:deep(.value-txt) {
  font-weight: 600 !important;
}

:deep(.VALID) {
  color: var(--success);
}

:deep(.INVALID_EXPIRED) {
  color: var(--error);
}

:deep(.INVALID_PREVIOUSLY_USED) {
  color: var(--warning);
}

.summary-container {
  display: flex;
  flex-wrap: wrap;
  align-content: center;
  justify-content: center;
  padding-bottom: 8px;

  .summary-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background-color: var(--background-color-400) !important;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.5s;

    tr:first-child {
      background-color: var(--table-row);
    }

    td {
      padding: 8px;
      border-bottom: 1px solid var(--border-color) !important;
    }

    .title-txt {
      font-weight: 400;
      color: var(--light-text-color);
      text-align: left;
      font-size: $font-size-16;
      width: 40%;
    }
    .value-txt {
      font-weight: 500;
      color: var(--text-color);
      text-align: right;
      font-size: $font-size-16;
      width: 60%;

      &.Valid {
        color: $success;
      }
      &.Expired {
        color: $error;
      }
      &.Previously {
        color: $warning;
      }
    }
  }
}
.charge-value {
  width: 120px !important;
}

.send-email-container {
  padding-top: 22px;
  padding-bottom: 44px;
  .send-email-header {
    margin: 0 6px;
    .subheader--faded {
      color: $pud-flag !important;
      font-size: $font-size-18 !important;
    }
    .send-email-btn {
      width: max-content;
      border-radius: 44px;
      color: var(--warning);
      margin-bottom: 12px;
      height: 40px;
      background-color: transparent !important;
      border: 1.5px solid var(--warning);
      &:disabled {
        border: none !important;
      }
    }
  }
  .email-checkbox {
    transform: translateY(-10px);
  }
}
</style>
