<template>
  <v-container fluid class="pa-0 fill-height">
    <v-layout row align-center class="wrapper-container">
      <v-flex>
        <v-layout row justify-center class="btn-container">
          <v-btn
            outline
            :color="activeTab === 0 ? 'warning' : ''"
            class="mx-2 tab-btn"
            :class="activeTab === 0 ? 'active' : ''"
            @click="activeTab = 0"
          >
            <span class="btn-txt">Job Search</span>
          </v-btn>
          <v-btn
            outline
            :color="activeTab === 1 ? 'info' : ''"
            class="mx-2 tab-btn"
            :class="activeTab === 1 ? 'quote_active' : ''"
            @click="activeTab = 1"
          >
            <span class="btn-txt">Quote Search</span>
          </v-btn>
        </v-layout>
      </v-flex>
    </v-layout>

    <div class="tab-content">
      <JobSearch v-if="activeTab === 0" />
      <QuoteSearch v-else />
    </div>
  </v-container>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import JobSearch from './JobSearch/index.vue';
import QuoteSearch from './QuoteSearch/quote_search.vue';

const activeTab = ref<number>(0); // 0 = Job, 1 = Quote
</script>

<style scoped lang="scss">
.wrapper-container {
  position: fixed;
  top: 42px;
  display: flex;
  align-items: center;
  height: fit-content !important;
  border-top: 1px solid var(--background-color-600);
  background-color: var(----table-bg-100);
  box-shadow: $shadow-primary;
  width: 100%;
  z-index: 99;

  .btn-container {
    gap: 20px;
  }
}
.tab-btn {
  min-width: 200px;
  border-radius: 12px 12px 0px 0px;
  border: none !important;
  min-height: 42px;
  font-weight: 500;

  .btn-txt {
    font-size: $font-size-22;
    font-family: $sub-font-family;
    text-transform: capitalize;
  }

  &.active {
    border-bottom: 2px solid var(--warning) !important;
  }
  &.quote_active {
    border-bottom: 2px solid var(--accent) !important;
  }

  &:hover {
    scale: 1.1;
  }
}

.tab-content {
  margin-top: 42px;
  height: 90vh;
  overflow-y: hidden;
}
</style>
