import ClientAccessJobReport from '@/client_portal/JobReport/index.vue';
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import {
  returnDurationFromMilliseconds,
  returnEndOfDayFromEpoch,
  returnFormattedDate,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import { InterfaceColors } from '@/interface-models/Company/BrandIdentity/InterfaceColors';
import DriverDetailsShort from '@/interface-models/Driver/DriverDetails/DriverDetailsShort';
import FleetAsset from '@/interface-models/FleetAsset/FleetAsset';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import SearchJobRequest from '@/interface-models/Jobs/SearchJob/SearchJobRequest';
import { Component, Prop, Vue } from 'vue-property-decorator';

import Pagination from '@/components/common/pagination/index.vue';
import JobDetailsDialog from '@/components/operations/OperationDashboard/components/JobDetailsDialog/index.vue';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { useOperationsStore } from '@/store/modules/OperationsStore';

import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { useClientPortalStore } from '@/store/modules/ClientPortalStore';

import SelectEntity from '@/components/common/inputs/select_entity/index.vue';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import ClientListSummary from '@/interface-models/Client/ClientDetails/ClientListSummary';
import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { SearchByOption } from '@/interface-models/Generic/SearchByOption';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { Validation } from '@/interface-models/Generic/Validation';
import { VPagination } from '@/interface-models/Generic/VPagination';
import { JobSourceType } from '@/interface-models/Jobs/JobSourceType';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useJobStore } from '@/store/modules/JobStore';
import moment from 'moment-timezone';

interface FleetAssetSelectItem {
  fleetAssetId: string;
  displayName: string;
  statusList: number[];
}

interface DriverName {
  driverId: string;
  name: string;
  statusList: number[];
}

interface ProgressMenu {
  id: number;
  longName: string;
}

@Component({
  components: {
    DatePickerBasic,
    ClientAccessJobReport,
    JobDetailsDialog,
    Pagination,
    SelectEntity,
  },
})
export default class ClientAccessJobSearch extends Vue {
  @Prop({ default: () => [] }) public clientJobList: JobDetails[];
  @Prop({ default: () => [] }) public clientDriverList: DriverDetailsShort[];
  @Prop({ default: () => [] }) public fleetAssetList: FleetAsset[];
  @Prop({ default: () => [] }) public clientDetails: ClientDetails;
  @Prop() public interfaceColors: InterfaceColors;
  @Prop({ default: true }) public allDataLoaded: boolean;
  @Prop({ default: false }) public isClientPortal: boolean;

  public fleetAssetStore = useFleetAssetStore();
  public jobStore = useJobStore();
  public operationsStore = useOperationsStore();
  public clientPortalStore = useClientPortalStore();

  public entityType = EntityType;

  public returnFormattedDate = returnFormattedDate;
  public returnDurationFromMilliseconds = returnDurationFromMilliseconds;
  public displayCurrencyValue = DisplayCurrencyValue;
  public WorkStatus = WorkStatus;
  public searchJobRequest: SearchJobRequest = new SearchJobRequest();
  public selectedSearchByMenuId: number = 1;
  public selectedProgressMenuId: number = 1;
  public selectedJobId: number = 0;
  public viewingJobReport: boolean = false;
  public tableSearch: string = '';
  public isLoadingTableData: boolean = false;
  public page: number = 1;
  public rowsPerPage: number = 50;

  public searchRecurringJob: boolean = false;

  get progressMenuItems(): ProgressMenu[] {
    const progressMenuItems: ProgressMenu[] = [];

    progressMenuItems.push({
      id: 1,
      longName: 'All',
    });

    progressMenuItems.push({
      id: 2,
      longName: 'Current',
    });

    progressMenuItems.push({
      id: 3,
      longName: 'Complete',
    });

    if (!this.isClientPortal) {
      progressMenuItems.push({
        id: 4,
        longName: 'Cancelled',
      });
      progressMenuItems.push({
        id: 5,
        longName: 'Service Failure',
      });

      progressMenuItems.push({
        id: 6,
        longName: 'Client Booking',
      });

      progressMenuItems.push({
        id: 7,
        longName: 'Import Booking',
      });
    }

    return progressMenuItems;
  }

  get pagination(): VPagination {
    return {
      descending: true,
      page: this.page,
      rowsPerPage: this.rowsPerPage,
      totalItems: this.jobStore.requestedJobSearchSummaryCount,
    };
  }

  get jobList() {
    return this.jobStore.requestedJobSearchSummary;
  }

  get driverList() {
    if (this.isClientPortal) {
      return this.clientDriverList;
    }
    return [];
  }

  get searchByOptions(): SearchByOption[] {
    const searchByOptions = [
      {
        id: 1,
        longName: 'Progress',
        keyName: 'jobProgress',
      },
      {
        id: 2,
        longName: 'Job Number',
        keyName: 'jobId',
      },
    ];

    if (!this.isClientPortal) {
      searchByOptions.push({
        id: 7,
        longName: 'Job Details',
        keyName: 'jobDetails',
      });
    } else {
      searchByOptions.push({
        id: 3,
        longName: 'Reference',
        keyName: 'jobReference',
      });

      searchByOptions.push({
        id: 4,
        longName: 'Caller Name',
        keyName: 'dispatcherName',
      });

      searchByOptions.push({
        id: 5,
        longName: 'Customer Name',
        keyName: 'customerDeliveryName',
      });
      searchByOptions.push({
        id: 8,
        longName: 'Invoice #',
        keyName: 'invoiceId',
      });
    }

    return searchByOptions;
  }
  public pageIncrement(value: number) {
    this.page += value;
    this.searchJob();
  }

  get fleetAssets(): FleetAssetSelectItem[] {
    if (this.isClientPortal) {
      return [];
    }
    return this.fleetAssetStore.getAllFleetAssetList
      .filter((x: FleetAssetSummary) => x.csrAssignedId)
      .map((y: FleetAssetSummary) => {
        const regoNumber = y.registrationNumber ?? '';
        return {
          fleetAssetId: y.fleetAssetId,
          displayName:
            y.csrAssignedId +
            ' - ' +
            regoNumber +
            (y.statusList.includes(13)
              ? ' - RETIRED'
              : y.statusList.includes(47)
                ? ' - CLOSED'
                : ''),
          statusList: y.statusList,
        };
      });
  }

  get drivers(): DriverName[] {
    if (this.isClientPortal) {
      return [];
    }
    return useDriverDetailsStore()
      .getDriverList.filter((x: DriverDetailsSummary) => x.displayName)
      .map((y: DriverDetailsSummary) => {
        return {
          driverId: y.driverId,
          name:
            y.displayName +
            (y.statusList.includes(13)
              ? ' - RETIRED'
              : y.statusList.includes(47)
                ? ' - CLOSED'
                : ''),
          statusList: y.statusList,
        };
      });
  }

  // we reset old search values back to string in this.searchJobRequest
  public setRequiredSearch() {
    const searchByName = this.searchByOptions.find(
      (x: SearchByOption) => x.id === this.selectedSearchByMenuId,
    );
    if (!searchByName) {
      return;
    }

    // we need to clear fields that are no longer needed in the search.
    Object.keys(this.searchJobRequest).forEach((v: string) => {
      const isJobDetailsSearch = searchByName.keyName === 'jobDetails';
      const searchRequestFieldIsClearable =
        v !== searchByName.keyName &&
        v !== 'statusList' &&
        v !== 'startEpoch' &&
        v !== 'endEpoch' &&
        v !== 'jobIds' &&
        v !== 'serviceFailure' &&
        v !== 'jobSourceType' &&
        v !== 'sortDirection' &&
        v !== 'sortByField';
      if (searchRequestFieldIsClearable) {
        // if the search is a "job details" search we should not clear the fields/inputs that
        // are available on a job details search.
        if (
          isJobDetailsSearch &&
          v !== 'dispatcherName' &&
          v !== 'jobReference' &&
          v !== 'customerDeliveryName' &&
          v !== 'clientId' &&
          v !== 'driverId' &&
          v !== 'fleetAssetId' &&
          v !== 'suburbName' &&
          v !== 'serviceFailure' &&
          v !== 'jobSourceType' &&
          v !== 'sortDirection' &&
          v !== 'sortByField'
        ) {
          (this.searchJobRequest as any)[v] = '';
        } else if (!isJobDetailsSearch) {
          (this.searchJobRequest as any)[v] = '';
        }
      }
    });
    // add canceled jobs to request if on operations job search page.
    this.searchJobRequest.workStatusMin = null;
    this.searchJobRequest.workStatusMax = null;
    this.searchJobRequest.workStatus = null;
    this.searchJobRequest.serviceFailure = false;
    this.searchJobRequest.jobSourceType = null;

    if (searchByName.keyName === 'jobProgress') {
      if (this.selectedProgressMenuId === 1) {
        // All
        this.searchJobRequest.workStatusMin = !this.isClientPortal
          ? WorkStatus.CANCELLED
          : WorkStatus.BOOKED;
        this.searchJobRequest.workStatusMax = WorkStatus.FINALISED;
      } else if (this.selectedProgressMenuId === 2) {
        // Current
        this.searchJobRequest.workStatusMin = WorkStatus.BOOKED;
        this.searchJobRequest.workStatusMax = WorkStatus.IN_PROGRESS;
      } else if (this.selectedProgressMenuId === 3) {
        // Complete
        this.searchJobRequest.workStatusMin = WorkStatus.DRIVER_COMPLETED;
        this.searchJobRequest.workStatusMax = WorkStatus.FINALISED;
      } else if (this.selectedProgressMenuId === 4 && !this.isClientPortal) {
        // Cancelled
        this.searchJobRequest.workStatus = WorkStatus.CANCELLED;
      } else if (this.selectedProgressMenuId === 5 && !this.isClientPortal) {
        // Service Failure
        this.searchJobRequest.serviceFailure = true;
      } else if (this.selectedProgressMenuId === 6 && !this.isClientPortal) {
        // Client Booking
        this.searchJobRequest.jobSourceType = JobSourceType.CLIENT;
      } else if (this.selectedProgressMenuId === 7 && !this.isClientPortal) {
        // Import Booking
        this.searchJobRequest.jobSourceType = JobSourceType.IMPORT;
      }
    }
  }

  // resets the pagination to first page-1 if page number is not 1
  public runQuery() {
    if (this.pagination.page) {
      if (this.pagination.page > 1) {
        this.pageIncrement(-(this.pagination.page - 1));
      }
    }
    this.searchJob();
  }

  public searchJob() {
    // Validate the fields if not using the client portal (to restrict results)
    if (
      !this.isClientPortal &&
      !(this.$refs.jobSearchParametersForm as any).validate()
    ) {
      const searchByName = this.searchByOptions.find(
        (x: SearchByOption) => x.id === this.selectedSearchByMenuId,
      );
      const valueName = searchByName ? searchByName.longName : 'value';
      showNotification(
        `Please enter the ${valueName} you wish to search for, then try again.`,
      );
      return;
    }
    this.isLoadingTableData = true;
    this.setRequiredSearch();

    if (this.isClientPortal) {
      this.clientPortalStore.requestJobSummarySearchForClient(
        this.searchJobRequest,
        this.pagination.page,
        this.pagination.rowsPerPage,
      );
    } else {
      this.jobStore.searchJobSummary(
        this.searchJobRequest,
        this.pagination.page,
        this.pagination.rowsPerPage,
      );
    }
  }

  public setStartDate(startDateEpoch: number | null) {
    this.searchJobRequest.startEpoch = startDateEpoch;
  }
  public setEndDate(endDateEpoch: number | null) {
    this.searchJobRequest.endEpoch =
      endDateEpoch !== null ? returnEndOfDayFromEpoch(endDateEpoch) : null;
  }

  get progressTypeId() {
    return this.selectedProgressMenuId;
  }
  set progressTypeId(value: number) {
    this.selectedProgressMenuId = value;
  }

  get validate(): Validation {
    return validationRules;
  }

  public selectOptionChanged(selectedId: number) {
    // if selected is jobId we remove the date filter
    if (selectedId === 2) {
      this.searchJobRequest.startEpoch = null;
      this.searchJobRequest.endEpoch = null;
      return;
    }
    if (
      this.searchJobRequest.startEpoch === null ||
      this.searchJobRequest.endEpoch === null
    ) {
      this.searchJobRequest.startEpoch = moment
        .tz(moment.tz.guess())
        .startOf('day')
        .valueOf();
      this.searchJobRequest.endEpoch = moment
        .tz(moment.tz.guess())
        .endOf('day')
        .valueOf();
    }
  }

  /**
   * Modelled to a top-level html element to set styles in vuetify-customised
   * based on props and options
   */
  get componentId() {
    if (this.isClientPortal) {
      return '';
    }
    return this.selectedSearchByMenuId === 7
      ? 'job-search-small'
      : 'job-search';
  }

  get tableHeaders(): TableHeader[] {
    const tableHeaders: TableHeader[] = [
      {
        text: 'Job #',
        align: 'left',
        sortable: true,
        value: 'displayId',
        visible: true,
      },
      {
        text: 'Date',
        align: 'left',
        value: 'date',
        sortable: true,
        visible: true,
      },
      {
        text: 'Client',
        align: 'left',
        value: 'clientName',
        sortable: true,
        visible: !this.isClientPortal,
      },
      {
        text: 'Service',
        align: 'left',
        value: 'clientService',
        sortable: true,
        visible: true,
      },
      {
        text: 'Reference',
        align: 'left',
        sortable: true,
        value: 'reference',
        visible: true,
      },

      {
        text: 'Stop',
        align: 'left',
        value: 'from',
        sortable: true,
        visible: true,
      },
      {
        text: 'Contact',
        align: 'left',
        value: 'dispatcherName',
        sortable: true,
        visible: true,
      },

      {
        text: 'Driver',
        align: 'left',
        value: 'driverName',
        sortable: true,
        visible: !this.isClientPortal,
      },
      {
        text: 'Fleet Asset',
        align: 'left',
        value: 'csrAssignedId',
        sortable: true,
        visible: !this.isClientPortal,
      },
      {
        text: 'Service',
        align: 'left',
        value: 'driverService',
        sortable: true,
        visible: !this.isClientPortal,
      },
      {
        text: 'Duration',
        align: 'left',
        value: 'billedDuration',
        sortable: true,
        visible: true,
      },
      {
        text: this.isClientPortal
          ? 'Charge (inc. GST)'
          : 'Client Charge (inc. GST)',
        align: 'left',
        value: 'clientCharge',
        sortable: true,
        visible: true,
      },
      {
        text: 'Driver Pay (inc. GST)',
        align: 'left',
        value: 'driverPay',
        sortable: true,
        visible: !this.isClientPortal,
      },
      {
        text: 'Status',
        align: 'left',
        value: 'status',
        sortable: true,
        visible: true,
      },
    ];

    return tableHeaders.filter((x: TableHeader) => x.visible);
  }

  public formatDate(value: number | string, format: string) {
    if (typeof value === 'number') {
      return returnFormattedDate(value, format);
    } else {
      return '-';
    }
  }

  public getBilledDuration(duration: number, workStatus: WorkStatus): string {
    const actualBilledDuration = returnDurationFromMilliseconds(duration);
    const isAccountingReady: boolean = workStatus >= WorkStatus.COMPLETED;
    const jobIsReviewed: boolean = workStatus === WorkStatus.REVIEWED;
    return this.isClientPortal && jobIsReviewed
      ? '-'
      : jobIsReviewed
        ? actualBilledDuration
        : isAccountingReady
          ? actualBilledDuration
          : '-';
  }

  public setTableData() {
    this.isLoadingTableData = false;
    return this.jobList;
  }
  // boolean to flag whether we should show the price of the job or not.
  public showJobCharges(workStatus: WorkStatus): boolean {
    // client portal users should only see the price if the job is in an accounting state
    if (this.isClientPortal) {
      return workStatus >= WorkStatus.COMPLETED;
    }
    // show price to operation users if the job is at least reviewed
    return workStatus >= WorkStatus.REVIEWED;
  }

  get jobResultTableData() {
    return this.setTableData();
  }

  public viewJobDetails(jobId: number) {
    if (this.isClientPortal) {
      this.selectedJobId = jobId;
      this.viewingJobReport = true;
      const request: SearchJobRequest = {
        statusList: [],
        workStatusMin: null,
        workStatusMax: null,
        workStatus: null,
        serviceFailure: false,
        jobSourceType: null,
        startEpoch: this.searchJobRequest.startEpoch,
        endEpoch: this.searchJobRequest.endEpoch,
        clientId: '',
        jobId: '',
        jobReference: '',
        dispatcherName: '',
        siteContactName: '',
        customerDeliveryName: '',
        suburbName: '',
        fleetAssetId: '',
        driverId: '',
        jobIds: [jobId],
        invoiceId: '',
        rctiId: '',
        sortDirection: this.searchJobRequest.sortDirection,
        sortByField: this.searchJobRequest.sortByField,
      };
      this.$emit('dispatchJobListRequest', request);
      return;
    }
    this.operationsStore.getFullJobDetails(jobId);
    this.operationsStore.setSelectedJobId(jobId);
    this.operationsStore.setViewingJobDetailsDialog(true);
  }

  public cancelJobReportView() {
    this.selectedJobId = 0;
    this.viewingJobReport = false;
  }

  get showJobDetailsDialog() {
    return this.operationsStore.viewingJobDetailsDialog;
  }

  get selectedJobDetails() {
    if (this.isClientPortal) {
      return this.clientPortalStore.clientJobList.find(
        (x: JobDetails) => x.jobId === this.selectedJobId,
      );
    }
    if (!this.showJobDetailsDialog) {
      return;
    }
    const jobId = this.operationsStore.selectedJobId;
    if (jobId === -1) {
      return;
    }
    // If we have no selectedJobDetails then return null
    if (!this.operationsStore.selectedJobDetails) {
      return null;
    } else {
      // Return the selectedJobDetails if it correctly matches the
      // selectedJobId. If not, then return null to prevent showing the dialog
      return this.operationsStore.selectedJobDetails.jobId === jobId
        ? this.operationsStore.selectedJobDetails
        : null;
    }
  }

  // get list of clients with CashSales for client selector
  get clientSelectList(): ClientListSummary[] {
    return useClientDetailsStore().clientListForSearching;
  }

  public beforeMount() {
    // Before mount is complete (to prevent error messages from dateTime
    // component on key change), set the default selectedSearchByMenuId value
    if (!this.isClientPortal) {
      this.selectedSearchByMenuId = 7;
      this.selectOptionChanged(this.selectedSearchByMenuId);
    } else {
      this.rowsPerPage = 10;
    }
  }

  public beforeDestroy() {
    // clear the searched jobs list when the user leaves the component.
    this.jobStore.updateJobSummaryState({
      totalCount: 0,
      jobDetailsList: [],
    });
  }
}
