<template>
  <div class="password-strength-container">
    <div class="strength-meter" :style="gradientStyle"></div>
    <p class="strength-text">{{ strengthText }}</p>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, watch, watchEffect, onMounted } from 'vue';
import zxcvbn, { ZXCVBNResult } from 'zxcvbn';
interface IProps {
  password: string;
}
const props = withDefaults(defineProps<IProps>(), {
  password: '',
});
const emit = defineEmits(['setPasswordStrengthScore']);
// Password score strangth. 0 to 5l
const strengthScore = ref(0);

/**
 * Watch for changes to password prop and calculate password strength via library zxcvbn
 * (https://github.com/dropbox/zxcvbn)
 */
watchEffect(() => {
  const calculateStrength: ZXCVBNResult = zxcvbn(props.password);
  let score: number = 0;
  if (props.password !== '' && calculateStrength) {
    // zxcvbn utilises a 0 index based scoring system so we will add 1 to the score. This is because 0 is reserved for empty password.
    score = calculateStrength.score + 1;
  }
  strengthScore.value = score;
  emit('setPasswordStrengthScore', score);
});

const gradientStyle = computed(() => {
  const percentage = strengthScore.value * 20; // Convert score to percentage (0-100%)
  let color: string;
  switch (strengthScore.value) {
    case 1:
      color = 'red';
      break;
    case 2:
      color = 'orange';
      break;
    case 3:
      color = 'yellow';
      break;
    case 4:
      color = 'lightgreen';
      break;
    case 5:
      color = 'green';
      break;
    default:
      color = '#ddd';
  }
  return {
    background: `linear-gradient(to right, ${color} ${percentage}%, #ddd ${percentage}%)`,
  };
});
const strengthText = computed(() => {
  switch (strengthScore.value) {
    case 0:
      return 'Password Strength';
    case 1:
      return 'Very Weak Strength';
    case 2:
      return 'Weak Strength';
    case 3:
      return 'Average Strength';
    case 4:
      return 'Strong Strength';
    case 5:
      return 'Very Strong Strength';
    default:
      return '';
  }
});
</script>

<style scoped lang="scss">
.password-strength-container {
  width: 100%;
  .strength-meter {
    height: 10px;
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin: 10px 0 5px;
  }
  .strength-text {
    font-size: $font-size-12;
    margin: 0 0 5px 0;
  }
}
</style>
