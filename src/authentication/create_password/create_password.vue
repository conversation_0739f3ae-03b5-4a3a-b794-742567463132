<template>
  <div class="create-password-wrapper" :class="showForm ? '' : 'align-center'">
    <form
      class="create-password-container"
      @submit.prevent="submit"
      v-if="showForm"
    >
      <GTitle
        title="Account Setup"
        subtitle="To enhance account security, please answer
        three security questions and create a new password."
      />

      <div v-if="isLoading" class="loading-container">
        <img
          src="@/static/loader/infinity-loader-light.svg"
          height="100px"
          width="100px"
        />
      </div>

      <div v-if="!isLoading">
        <GSelect
          placeholder="Security Question One"
          label="Security Question One"
          v-model="securityQuestionOne"
          itemValue="question"
          itemText="question"
          :items="securityQuestions"
          :disabledValues="disabledValues"
        />

        <GTextField
          v-model="securityQuestionOneAnswer"
          :placeholder="'Answer For Security Question One'"
          :rules="[rules.required]"
        ></GTextField>

        <GSelect
          placeholder="Security Question Two"
          label="Security Question Two"
          v-model="securityQuestionTwo"
          itemValue="question"
          itemText="question"
          :items="securityQuestions"
          :disabledValues="disabledValues"
        />
        <GTextField
          v-model="securityQuestionTwoAnswer"
          :placeholder="'Answer For Security Question Two'"
          :rules="[rules.required]"
        ></GTextField>
        <GSelect
          placeholder="Security Question Three"
          label="Security Question Three"
          v-model="securityQuestionThree"
          itemValue="question"
          itemText="question"
          :items="securityQuestions"
          :disabledValues="disabledValues"
        />
        <GTextField
          v-model="securityQuestionThreeAnswer"
          :placeholder="'Answer For Security Question Three'"
          :rules="[rules.required]"
        ></GTextField>

        <GTitle class="mt-3" title="Password" />
        <PasswordStrength
          :password="password"
          @setPasswordStrengthScore="setPasswordStrengthScore"
        />
        <GTextField
          v-model="password"
          type="password"
          :placeholder="'Password'"
          :rules="[rules.required]"
          :customErrorMessage="customPasswordErrorMessage"
        ></GTextField>

        <GTextField
          v-model="confirmPassword"
          :placeholder="'Confirm Password'"
          type="password"
          :rules="[rules.required]"
        >
        </GTextField>

        <div class="flex-row space-between content-end">
          <GButton type="submit" :isLoading="isLoadingSubmitRequest"
            >Submit</GButton
          >
        </div>
      </div>
    </form>

    <div class="response-message-container border" v-if="!showForm">
      <div class="flex-row content-center">
        <v-icon v-if="success" class="icon" :color="'success'"
          >fa fa-check-circle</v-icon
        >
        <v-icon v-if="!success" class="icon" :color="'error'"
          >fa fa-exclamation-circle</v-icon
        >
      </div>

      <div v-if="success">
        <p>Security Questions & Password Successfully Saved!</p>

        <p>
          Your security questions & password were successfully saved. You can
          now
          <router-link to="/login">login to your account</router-link>.
        </p>
      </div>
      <div v-if="!success">
        <div v-if="linkExpired">
          <p>
            Sorry, the link to set your security questions and password has
            expired.
          </p>
          <p v-if="!accountAlreadySetup">
            Update links are typically time-sensitive for security reasons.
            Please request a new update link, and we'll send you a fresh one. If
            you continue to encounter issues, feel free to contact the GoDesta
            support team for further assistance.
          </p>
          <p v-else>
            Please visit the
            <a :href="userPortalEndpoint">User Portal</a> to manage your
            account.
          </p>
        </div>
        <div v-else>
          <p>Oops! Something Went Wrong.</p>
          <p>
            We encountered an issue while processing your request. Please
            request a new update link, and we'll send you a fresh one. If you
            continue to encounter issues, feel free to contact the GoDesta
            support team for further assistance.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import PasswordStrength from '@/authentication/create_password/password_strength.vue';
import Environment from '@/configuration/environment';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  validate,
  validationRules,
} from '@/helpers/ValidationHelpers/ValidationHelpers';
import { SecurityQuestion } from '@/interface-models/Authentication/SecurityQuestion';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { CheckAlreadySetUpResponse } from '@/interface-models/UserPortal/CheckAlreadySetUpResponse';
import useHttp from '@/utils/http';
import {
  ComputedRef,
  Ref,
  computed,
  getCurrentInstance,
  nextTick,
  onMounted,
  ref,
} from 'vue';

const instance: any = getCurrentInstance();
const http = useHttp();

const securityQuestions: Ref<SecurityQuestion[]> = ref([]);
const securityQuestionOne: Ref<string> = ref('');
const securityQuestionOneAnswer: Ref<string> = ref('');
const securityQuestionTwo: Ref<string> = ref('');
const securityQuestionTwoAnswer: Ref<string> = ref('');
const securityQuestionThree: Ref<string> = ref('');
const securityQuestionThreeAnswer: Ref<string> = ref('');
const password: Ref<string> = ref('');
const confirmPassword: Ref<string> = ref('');
const passwordStrength: Ref<number> = ref(0);
const customPasswordErrorMessage: Ref<string> = ref('');

// Link Query params
const company: Ref<string | undefined> = ref(undefined);
const linkKey: Ref<string | undefined> = ref(undefined);
const username: Ref<string | undefined> = ref(undefined);
// template render conditionals
const showForm: Ref<boolean> = ref(true);
const success: Ref<boolean> = ref(false);
const linkExpired: Ref<boolean> = ref(false);
const otherException: Ref<boolean> = ref(false);
const isLoadingSecurityQuestions: Ref<boolean> = ref(true);
const isLoadingAccountSetupCheck: Ref<boolean> = ref(true);
const isLoadingSubmitRequest: Ref<boolean> = ref(false);
const accountAlreadySetup: Ref<boolean> = ref(false);

onMounted(() => {
  setLinkQueryParams();
  checkUserAccountAlreadySetup();
  getSecurityQuestions();
});

const disabledValues: ComputedRef<string[]> = computed(() => {
  return [
    securityQuestionOne.value,
    securityQuestionTwo.value,
    securityQuestionThree.value,
  ].filter((question) => question !== '');
});

/**
 * returns the current environments userportal endpoint. We detimine this endpoint by a mixture of VUE_APP_ENV and window host location.
 * @return {string} string link of user portal endpoint.
 */
const userPortalEndpoint: ComputedRef<string> = computed(() => {
  const ENV: string | undefined = Environment.value('environment');
  if (!ENV || ENV === 'local') {
    return 'http://userportal.development.lan/#/';
  }
  const host: string = window.location.host;
  if (host.includes('dev.godesta.lan')) {
    return 'http://userportal.dev.godesta.lan/#/';
  } else if (host.startsWith('demo.') || host.startsWith('userportal.demo.')) {
    return 'https://userportal.demo.gode.st/#/';
  }
  return 'https://userportal.gode.st/#/';
});

/**
 * Sets local state values based on link query params.
 * @return {void}
 */
function setLinkQueryParams(): void {
  try {
    company.value = instance.proxy.$route.query.company;
    linkKey.value = instance.proxy.$route.params.id;
    username.value = instance.proxy.$route.query.username;
    if (!username.value || !company.value || !linkKey.value) {
      throw new Error('One of the link query params was undefined');
    }
  } catch (e: unknown) {
    console.error(e);
    success.value = false;
    showForm.value = false;
    otherException.value = true;
  }
}

/**
 * Single loading handler for our http requests
 * @return {boolean} boolean indicating whether we are waiting for responses or not
 */
const isLoading: ComputedRef<boolean> = computed(() => {
  return isLoadingSecurityQuestions.value || isLoadingAccountSetupCheck.value;
});

/**
 * Http Post request to find out if the current account associated with the link already has their account setup.
 * @return {boolean} boolean indicating whether we are waiting for responses or not
 */
async function checkUserAccountAlreadySetup(): Promise<void> {
  try {
    isLoadingAccountSetupCheck.value = true;
    const payload = [username.value, company.value, linkKey.value];
    const response = await http.post<CheckAlreadySetUpResponse>(
      'app/password/forgotPassword/checkUserAccountAlreadySetUp',
      payload,
    );
    if (!response.ok) {
      throw new Error(
        'Something went wrong when attempting to check if this user has already set up their account.',
      );
    } else {
      if (response.data.isAlreadySetup) {
        throw new Error('This account was setup previously.');
      }
    }
    isLoadingAccountSetupCheck.value = false;
  } catch (e: unknown) {
    isLoadingAccountSetupCheck.value = false;
    console.error(e);
    showForm.value = false;
    success.value = false;
    linkExpired.value = true;
    accountAlreadySetup.value = true;
  }
}

/**
 * Get a list of security questions via HTTP.
 * @return {Promise<void>}
 */
async function getSecurityQuestions(): Promise<void> {
  try {
    isLoadingSecurityQuestions.value = true;
    const request = await http.get<SecurityQuestion[]>(
      'app/password/securityQuestions/get/listAll',
    );
    if (!request.ok) {
      throw new Error(
        'Something went wrong when trying to get security questions',
      );
    } else if (request.data.length < 3) {
      throw new Error(
        'At least three security questions required by get security questions request',
      );
    }
    securityQuestions.value = request.data;
    nextTick(() => {
      securityQuestionOne.value = securityQuestions.value[0].question;
      securityQuestionTwo.value = securityQuestions.value[1].question;
      securityQuestionThree.value = securityQuestions.value[2].question;
    });
    isLoadingSecurityQuestions.value = false;
  } catch (e: unknown) {
    console.error(e);
    success.value = false;
    showForm.value = false;
    otherException.value = true;
    showNotification('Something went wrong.', {
      title: 'Error',
      type: HealthLevel.ERROR,
    });
  }
}
/**
 * Emitted event from our password strength component
 */
function setPasswordStrengthScore(score: number) {
  passwordStrength.value = score;
}

/**
 * validate data and submit security questions and password.
 */
async function submit(): Promise<void> {
  try {
    // validate inputs
    const isValid = validate(instance);
    if (!isValid) {
      return;
    }

    // validate that passwords match.
    if (password.value !== confirmPassword.value) {
      showNotification('Passwords do not match. ', {
        title: 'Confirm Password',
        type: HealthLevel.ERROR,
      });
      customPasswordErrorMessage.value = 'Passwords do not match.';
      return;
    }

    const requiredPasswordStrength: number =
      Environment.value('environment') === 'local' ? 0 : 1;
    // Validate that the password strength is greater than 1 ( n > Very Weak)
    if (passwordStrength.value <= requiredPasswordStrength) {
      showNotification('Password is too weak. ', {
        title: 'Confirm Password',
        type: HealthLevel.ERROR,
      });
      customPasswordErrorMessage.value = 'Password is too weak.';
      return;
    }
    if (!username.value || !company.value || !linkKey.value) {
      throw new Error('One of the link query params was undefined');
    }
    const postBody: string[] = [
      username.value,
      company.value,
      password.value,
      linkKey.value,
      securityQuestionOne.value,
      securityQuestionOneAnswer.value,
      securityQuestionTwo.value,
      securityQuestionTwoAnswer.value,
      securityQuestionThree.value,
      securityQuestionThreeAnswer.value,
    ];

    interface CreatePasswordResponse {
      linkKey: string;
      response: boolean;
      message: string;
    }
    isLoadingSubmitRequest.value = true;
    const createPasswordRequest = await http.post<CreatePasswordResponse>(
      'app/password/create-password',
      postBody,
    );

    if (!createPasswordRequest.ok) {
      throw new Error('Something went wrong.');
    } else {
      success.value = true;
      showForm.value = false;
    }
  } catch (e: unknown) {
    console.error(e);
    success.value = false;
    showForm.value = false;
    otherException.value = false;
  } finally {
    isLoadingSubmitRequest.value = false;
  }
}

const rules = computed(() => {
  return validationRules;
});
</script>
<style scoped lang="scss">
.create-password-wrapper {
  padding: 10px;
  width: 100%;
  display: flex;
  justify-content: center;

  .create-password-container {
    display: flex;
    flex-direction: column;
    max-width: 500px;
  }

  .response-message-container {
    max-width: 400px;
    padding: 20px 12px 12px 12px;
    text-align: center;
    .icon {
      font-size: 40px;
      margin: 0 0 20px 0;
    }
  }
}

a {
  color: var(--highlight);
}

.loading-container {
  display: flex;
  justify-content: center;
}
</style>
