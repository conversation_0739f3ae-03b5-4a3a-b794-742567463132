import { Component, Vue } from 'vue-property-decorator';

import { useOperationsStore } from '@/store/modules/OperationsStore';
import UserPortalLoadedApplication from '@/interface-models/Generic/LoadedApplication/UserPortalLoadedApplication';
import { Portal } from '@/interface-models/Generic/Portal';
import OperationsPortalLoadedApplication from '@/interface-models/Generic/LoadedApplication/OperationsPortalLoadedApplication';
import ClientPortalLoadedApplication from '@/interface-models/Generic/LoadedApplication/ClientPortalLoadedApplication';

import { useRootStore } from '@/store/modules/RootStore';
import { useUserPortalStore } from '@/store/modules/UserPortalStore';
import { useAuthenticationStore } from '@/store/modules/AuthenticationStore';
import { sessionManager } from '@/store/session/SessionState';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
interface ApplicationLoad {
  value: string;
  isLoaded: boolean;
}
@Component({
  components: {},
})
export default class Splash extends Vue {
  public Portal = Portal;

  get appPortal(): Portal {
    return sessionManager.getPortalType();
  }

  get dataToBeLoaded():
    | OperationsPortalLoadedApplication
    | UserPortalLoadedApplication
    | ClientPortalLoadedApplication {
    const rootStore = useRootStore();
    let data:
      | OperationsPortalLoadedApplication
      | UserPortalLoadedApplication
      | ClientPortalLoadedApplication =
      rootStore.operationsPortalLoadedData as OperationsPortalLoadedApplication;

    if (this.appPortal === Portal.CLIENT) {
      data = rootStore.clientPortalLoadedData as ClientPortalLoadedApplication;
    } else if (this.appPortal === Portal.USER) {
      data = useUserPortalStore()
        .userPortalLoadedData as UserPortalLoadedApplication;
    }

    return data;
  }

  get percentageLoaded(): number {
    const dataLength: number = Object.keys(this.dataToBeLoaded).length;
    const totalAmountLoaded: number = Object.values(this.dataToBeLoaded).reduce(
      (a: any, item: boolean) => a + item,
      0,
    );
    return Math.trunc((totalAmountLoaded / dataLength) * 100);
  }

  // Get the company name from divisionDetails once it has arrived
  get companyName() {
    return useCompanyDetailsStore().divisionDetails?.divisionShortName || '';
  }

  public logout(): void {
    useOperationsStore().closeAllPopoutWindows();
    useAuthenticationStore().disconnectWebsocket();
  }

  get applicationLoad(): ApplicationLoad[] {
    const applicationLoad: ApplicationLoad[] = [];
    Object.keys(this.dataToBeLoaded).forEach((key, index) => {
      applicationLoad.push({
        value:
          key === 'GPS_POSITIONS'
            ? 'GPS Positions'
            : this.titleCase(key.replaceAll('_', ' ')),
        isLoaded: this.dataToBeLoaded[key],
      });
    });
    return applicationLoad;
  }

  public titleCase(str: string) {
    const splitStr = str.toLowerCase().split(' ');
    for (let i = 0; i < splitStr.length; i++) {
      splitStr[i] =
        splitStr[i].charAt(0).toUpperCase() + splitStr[i].substring(1);
    }
    return splitStr.join(' ');
  }
}
