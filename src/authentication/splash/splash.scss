.splash-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 2147483646;
  background-color: var(--background-color-100);
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--text-color);
}

.company-name {
  font-family: $sub-font-family;
  font-size: 30px;
  font-weight: 800;
  height: 30px;
}

.fade-in-text {
  opacity: 0; /* Initially set the opacity to 0 to create the fade-in effect */
  transform: translateY(
    20px
  ); /* You can also add some vertical offset for a subtle motion */
  transition:
    opacity 0.8s ease,
    transform 0.8s ease; /* Define the transition properties */

  &.fade-in {
    opacity: 1; /* When the class 'fade-in' is added, change opacity to 1 */
    transform: translateY(0); /* Reset the vertical offset */
  }
}

.center-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.percentage-container {
  padding: 5px 10px 10px 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  position: fixed;
  right: 0;
  bottom: 0;
}
.logout-button {
  position: fixed;
  left: 0;
  bottom: 0;
}
