<div class="splash-container">
  <div class="center-container">
    <img
      src="@/static/loader/infinity-loader-light.svg"
      height="100px"
      width="100px"
    />
    <p class="company-name fade-in-text" :class="{'fade-in': !!companyName}">
      {{companyName}}
    </p>
    <p class="mt-1">Loading data, please wait...</p>
  </div>
  <v-tooltip top>
    <template v-slot:activator="{ on }">
      <div v-on="on" class="percentage-container">{{percentageLoaded}}%</div>
    </template>
    <v-layout wrap style="width: 200px">
      <v-flex md12 v-for="(data, index) of applicationLoad" :key="index">
        <v-layout justify-space-between>
          <span>{{data.value}}</span>
          <v-icon
            size="14"
            v-html="data.isLoaded ? 'fal fa-check' : 'fal fa-times'"
            :color="data.isLoaded ? 'success' : 'error'"
          ></v-icon>
        </v-layout>
      </v-flex>
    </v-layout>
  </v-tooltip>
  <div class="logout-button">
    <v-btn flat @click="logout"> Logout </v-btn>
  </div>
</div>
