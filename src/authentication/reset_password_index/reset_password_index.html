<v-layout class="password-reset-container pa-4" column>
  <v-layout column align-center justify-center>
    <img class="login-logo mb-5" src="@/static/images/godesta_login_logo.png" />
  </v-layout>
  <v-layout v-if="!linkIsValid">
    <v-flex md12 v-if="!isForgotPassword">
      <v-alert
        :value="true"
        type="error"
        class="center-text reset-password-alert"
        @click="isForgotPassword = true"
        >This reset link is expired or invalid.</v-alert
      >
    </v-flex>
  </v-layout>

  <div>
    <PasswordStrength
      :password="password"
      @setPasswordStrengthScore="setPasswordStrengthScore"
    />

    <GTextField
      v-model="password"
      type="password"
      :placeholder="'Password'"
      :rules="[validate.required]"
      :customErrorMessage="customPasswordErrorMessage"
    ></GTextField>

    <GTextField
      v-model="confirmPassword"
      :placeholder="'Confirm Password'"
      type="password"
      :rules="[validate.required]"
    >
    </GTextField>
  </div>

  <!-- <v-layout
    justify-center
    wrap
    v-if="linkIsValid && !isSuccessfulRequest"
    column
  >
    <p class="title center-text">Reset Password</p>

    <v-flex md12>
      <p class="center-text">Please enter your new password.</p>

      <v-alert
        type="warning"
        :value="showCapsLockAlert"
        icon="fas fa-font-case"
        :class="{'mb-2' : showCapsLockAlert}"
      >
        <strong>Warning:</strong> Caps Lock is ON
      </v-alert>

      <v-form ref="resetForm">
        <v-text-field
          background-color="grey darken-1"
          outline
          color="white"
          v-model="newPassword"
          autocomplete
          hide-details
          class="form-field-required"
          :rules="[validate.required]"
          label="New Password"
          :append-icon="showPassword ? 'visibility' : 'visibility_off'"
          :type="showPassword ? 'text' : 'password'"
          @click:append="showPassword = !showPassword"
        >
        </v-text-field>
        <div justify-center align-center class="password-strength-container">
          <v-layout>
            <div
              class="strength-block"
              v-for="item of validatePasswordStrength.color"
              :style="{backgroundColor: item, border: newPassword === '' ? '1px solid grey' : 'initial'}"
            ></div>
          </v-layout>
          <v-layout justify-end>
            <span class="password-strength-caption pr-1"
              >{{validatePasswordStrength.strength}}</span
            >
          </v-layout>
        </div>
        <v-text-field
          background-color="grey darken-1"
          class="pt-2 form-field-required"
          outline
          autocomplete
          :rules="[validate.required]"
          color="white"
          hide-details
          v-model="matchPassword"
          label="Confirm Password"
          :append-icon="showMatchPassword ? 'visibility' : 'visibility_off'"
          :type="showMatchPassword ? 'text' : 'password'"
          @click:append="showMatchPassword = !showMatchPassword"
        >
        </v-text-field>
        <v-layout justify-end class="pt-3">
          <v-btn
            class="ma-0"
            v-shortkey.once="[`enter`]"
            light
            :disabled="!linkIsValid"
            block
            large
            depressed
            @shortkey="resetPasswordRequest"
            @click="resetPasswordRequest"
          >
            <span v-if="onPasswordCreation">Create Password</span
            ><span v-else>Reset Password</span></v-btn
          >
        </v-layout>
      </v-form>
    </v-flex>
  </v-layout>

  <v-layout v-if="showDriverPasswordChangeSuccess">
    <v-flex>
      <v-alert class="pa-3 center-text" :value="true" type="info">
        Your Password was successfully set. Please proceed to login from your
        GoDesta mobile application.
      </v-alert>
    </v-flex>
  </v-layout>

  <v-layout v-if="showClientPasswordChangeSuccess">
    <v-flex>
      <v-alert class="pa-3 center-text" :value="true" type="info">
        Your Password was successfully set. Please proceed to login via the
        client Portal.
      </v-alert>
    </v-flex>
  </v-layout> -->
</v-layout>
