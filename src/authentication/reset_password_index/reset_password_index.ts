import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { INotificationProducer } from '@/interface-models/Generic/NotificationMessage/NotificationProducerInterface';
import { Validation } from '@/interface-models/Generic/Validation';
import { VForm } from '@/interface-models/Generic/VForm/VForm';
import useHttp from '@/utils/http';
import moment from 'moment-timezone';
import { Component, Vue } from 'vue-property-decorator';

// EXAMPLE LINK
// http://localhost:8080/#/forgot-password/000a7ee2-f359-47fe-9bb9-3bfc5d37296f?username=username&expiry=1648690019000
@Component({
  // components: { ForgottenPassword },
})
export default class ResetPassword
  extends Vue
  implements INotificationProducer
{
  public readonly componentTitle: string = 'Reset Password';

  public username: string = '';
  public showPassword: boolean = false;
  public showMatchPassword: boolean = false;
  public newPassword: string = '';
  public matchPassword: string = '';
  public success: boolean = false;
  public linkIsValid: boolean = true;
  public linkExpiryInEpoch: number | null = null;
  public showDriverPasswordChangeSuccess: boolean = false;
  public showClientPasswordChangeSuccess: boolean = false;
  public expiryTimeInterval: any;
  public isForgotPassword: boolean = false;

  public showCapsLockAlert: boolean = false;
  public http = useHttp();

  public $refs!: {
    resetForm: VForm;
  };

  get isSuccessfulRequest() {
    return this.success || this.showDriverPasswordChangeSuccess;
  }

  public async resetPasswordRequest() {
    // Check password is valid and that a password is also entered.
    if (!this.$refs.resetForm.validate() || !this.passwordMatches) {
      if (!this.$refs.resetForm.validate()) {
        this.showAppNotification('Please enter a new password.');
        return;
      }
      if (!this.passwordMatches) {
        showNotification('Passwords do not match.');
        return;
      }
    }
    try {
      const resetHash = this.$route.params.id;

      const endPoint = this.onPasswordCreation
        ? 'app/password/set-password'
        : 'app/password/change-password';

      interface PasswordResetResponse {
        linkKey: string;
        response: string;
        message: string;
      }

      const response = await this.http.post<PasswordResetResponse>(endPoint, [
        this.username,
        this.newPassword,
        resetHash,
      ]);

      if (response.data && response.data.message.startsWith('success')) {
        this.success = true;
        this.showAppNotification(
          'Your Password was successfully ' +
            (this.onPasswordCreation ? 'set.' : 'reset.'),
          HealthLevel.INFO,
        );
        const responseLength = response.data.message.length;
        const redirectType = response.data.message.substring(
          10,
          responseLength,
        );
        this.goToLogin(redirectType);
        return;
      }

      // request was unsuccessful
      this.showAppNotification(
        'Something went wrong. ' +
          (this.onPasswordCreation
            ? 'Please contact GoDesta <NAME_EMAIL>'
            : 'Please try again.'),
      );
      this.goToLogin('operations');
    } catch (err) {
      console.error((err as any).message);
    }
  }

  get onPasswordCreation() {
    return this.currentRouteName === 'create_password';
  }

  get currentRouteName() {
    const routeName = this.$route.name;
    return routeName ? routeName : '';
  }

  get validate(): Validation {
    return validationRules;
  }

  get passwordMatches() {
    return this.newPassword === this.matchPassword;
  }
  // we check the response message to find where the user should be redirected.
  public goToLogin(redirectType: string) {
    if (redirectType === 'client') {
      this.showClientPasswordChangeSuccess = true;
      return;
    } else if (redirectType === 'driver') {
      this.showDriverPasswordChangeSuccess = true;
      return;
    } else {
      this.$router.push({ name: 'login_index' });
    }
  }

  get validatePasswordStrength() {
    const password = this.newPassword;

    // Do not show anything when the length of password is zero.
    // Create an array and push all possible values that you want in password
    const matchedCase: string[] = [];
    matchedCase.push('[$@$!%*#?&]'); // Special Character
    matchedCase.push('[A-Z]'); // Uppercase alphabet
    matchedCase.push('[0-9]'); // Numbers
    matchedCase.push('[a-z]'); // Lowercase alphabet

    // Check the conditions
    let ctr = 0;
    for (let i = 0; i < matchedCase.length; i++) {
      if (new RegExp(matchedCase[i]).test(password)) {
        ctr++;
      }
    }
    // Display it
    let color: string[] = [];
    let strength = '';
    let textColor: string = '';
    switch (ctr) {
      case 0:
      case 1:
      case 2:
        strength = 'Weak';
        color = ['red', 'transparent', 'transparent'];
        textColor = 'red';
        break;
      case 3:
        strength = 'Good';
        color = ['orange', 'orange', 'transparent'];
        textColor = 'orange';
        break;
      case 4:
        strength = 'Great';
        color = ['green', 'green', 'green'];
        break;
    }

    if (this.newPassword === '') {
      color = ['transparent', 'transparent', 'transparent'];
      textColor = 'green';
      strength = 'Password Strength';
    }

    return {
      strength,
      color,
      textColor,
    };
  }

  // On keypress check the state of CapsLock and show/hide alert in html
  public handleCapsLockPress(e: KeyboardEvent) {
    if (e && e.getModifierState && e.getModifierState('CapsLock')) {
      this.showCapsLockAlert = true;
    } else {
      this.showCapsLockAlert = false;
    }
  }

  // Trigger app notification. Defaults to ERROR type message, but type can be
  // provided to produce other types. Includes componentTitle as a title for the
  // notification.
  public showAppNotification(text: string, type?: HealthLevel): void {
    showNotification(text, {
      type,
      title: this.componentTitle,
    });
  }

  // When the component mounts we get the username and expiry from the url query.
  // With this we set the username and also check if the link is still valid.
  public mounted() {
    if (!this.$route.query.username || !this.$route.query.expiry) {
      this.linkIsValid = false;
      return;
    }
    const username: string = this.$route.query.username as string;
    this.linkExpiryInEpoch = parseInt(this.$route.query.expiry as string, 10);
    if (moment.now() > this.linkExpiryInEpoch) {
      this.linkIsValid = false;
      return;
    }
    // Because url encoding replaces certain characters with eg "%40" we need to convert these values back to the correct characters.
    // reference: https://www.w3schools.com/tags/ref_urlencode.asp
    this.username = username.includes('%40')
      ? username.replace('%40', '@')
      : username;
    if (this.username.includes(' ')) {
      this.username = username.replace(' ', '+');
    }

    // set interval timer to check if link is expired.
    this.expiryTimeInterval = setInterval(() => {
      if (
        this.linkExpiryInEpoch !== null &&
        moment.now() > this.linkExpiryInEpoch
      ) {
        this.linkIsValid = false;
      }
    }, 1000);

    // Add listener on keypress to check caps lock state
    document.addEventListener('keyup', this.handleCapsLockPress);
  }

  public beforeDestroy() {
    clearInterval(this.expiryTimeInterval);
    // Add remove listener on component unmount
    document.removeEventListener('keyup', this.handleCapsLockPress);
  }
}
