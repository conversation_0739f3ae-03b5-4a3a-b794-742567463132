<template>
  <div>
    <GTitle
      title="Account Recovery Options"
      subtitle="Please select how you would like to recover your account."
      :divider="false"
    />
    <div class="radio mt-3">
      <input
        type="radio"
        name="sms"
        id="sms"
        class="radio__input"
        checked
        value="sms"
        v-model="selectedOption"
      />
      <label for="sms" class="radio__label"
        ><strong>Verify by SMS</strong><br />{{
          props.availableProcessOptions[0]
        }}</label
      >
    </div>

    <div class="radio mb-3" v-if="availableProcessOptions.length > 1">
      <input
        type="radio"
        name="none"
        id="none"
        class="radio__input"
        value="none"
        v-model="selectedOption"
      />
      <label for="none" class="radio__label"
        ><strong>I cannot access my mobile or email</strong><br />{{
          props.availableProcessOptions[1]
        }}</label
      >
    </div>

    <GButton
      :disabled="false"
      color="white"
      @click="selectProcessOption"
      block
      light
      class="ma-0"
      large
      :isLoading="isLoading"
      darkText
      >Proceed</GButton
    >
  </div>
</template>

<script setup lang="ts">
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import useHttp from '@/utils/http';
import { getCurrentInstance, ref, Ref } from 'vue';

const http = useHttp();
interface IProps {
  availableProcessOptions: string[];
  emailOrMobileNumber: string;
}
const props = withDefaults(defineProps<IProps>(), {});
const emit = defineEmits(['backToLogin']);
const isLoading: Ref<boolean> = ref(false);
const instance: any = getCurrentInstance();
const selectedOption = ref('sms');

/**
 * Selects the password recovery process based on the user's chosen option. If the selected option is 'sms',
 * it triggers the process to send an SMS password recovery link. If the selected option is 'none', it navigates
 * the user to the account recovery page, passing the username (email or mobile number) as a query parameter.
 *
 * Assumes `selectedOption.value` contains the user's choice for the password recovery process and that
 * `props.emailOrMobileNumber` contains the necessary user identification for the process.
 *
 * @returns {void} Does not return a value. Effects are either sending an SMS or navigating to a different route.
 */
function selectProcessOption(): void {
  try {
    if (selectedOption.value === 'sms') {
      sendSMSPasswordRecoveryLink();
    } else if (selectedOption.value === 'none') {
      instance.proxy.$router.push({
        name: 'account_recovery',
        query: { username: props.emailOrMobileNumber },
      });
    } else {
      throw new Error('Account recovery select option was undefined.');
    }
  } catch (e: unknown) {
    showNotification('Sorry, something went wrong.', {
      title: 'Account Recovery',
      type: HealthLevel.ERROR,
    });
  }
}

/**
 * Asynchronously sends a password recovery link via SMS to the user's mobile number.
 * It makes an HTTP POST request to send the link. If the request is successful, a
 * notification is displayed to inform the user that an SMS verification link will be
 * received shortly. Upon success, it also triggers a transition back to the login view.
 * In case of failure, it displays an error notification to the user.
 *
 * Assumes `props.emailOrMobileNumber` contains the mobile number to which the SMS should be sent.
 * Uses `isLoading` reactive property to indicate the loading state of the request.
 *
 * @async
 * @returns {Promise<void>}
 * @throws {Error} Throws an error if the HTTP request fails or doesn't return an expected result.
 */
async function sendSMSPasswordRecoveryLink(): Promise<void> {
  try {
    if (!props.emailOrMobileNumber) {
      throw new Error('Something went wrong.');
    }
    isLoading.value = true;
    interface SendSMSForPasswordRecoveryResponse {
      response: boolean;
      message: boolean;
    }
    const submitRequest = await http.post<SendSMSForPasswordRecoveryResponse>(
      'app/password/forgotPassword/sendLink/' + props.emailOrMobileNumber,
      null,
    );
    if (submitRequest.ok) {
      showNotification(
        'A verification link has been sent via SMS to the mobile number linked to your account. Please follow the link to proceed with the next steps.',
        {
          title: 'SMS Verification',
          type: HealthLevel.INFO,
          duration: 60000,
        },
      );
    } else {
      throw new Error('Something went wrong.');
    }
  } catch (err) {
    console.error(err);
    showNotification('Sorry, something went wrong.', {
      title: 'Account Recovery',
      type: HealthLevel.ERROR,
    });
  } finally {
    isLoading.value = false;
    emit('backToLogin');
  }
}
</script>

<style scoped lang="scss">
.radio {
  position: relative;
  margin: 0 0 10px;
  font-size: $font-size-16;
  line-height: 24px;
  margin-bottom: 10px;
  display: flex;
  min-width: 110%;
  color: var(--light-text-color);
  strong {
    color: var(--text-color);
  }

  &__input {
    position: absolute;
    top: 4px;
    left: 0;
    width: 36px;
    height: 20px;
    opacity: 0;
    z-index: 0;
  }
  &__label {
    display: block;
    padding: 0 0 0 24px;
    cursor: pointer;

    &:before {
      content: '';
      position: absolute;
      top: 4px;
      left: 0;
      width: 16px;
      height: 16px;
      background-color: transparent;
      border: 2px solid var(--text-color-light);
      border-radius: 14px;
      z-index: 1;
      transition: border-color 0.28s cubic-bezier(0.4, 0, 0.2, 1);
    }

    &:after {
      content: '';
      position: absolute;
      top: 8px;
      left: 4px;
      width: 8px;
      height: 8px;
      background-color: var(--highlight);
      border-radius: 50%;
      z-index: 2;
      transform: scale(0, 0);
      transition: transform 0.28s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }
  &__input:checked + &__label {
    &:before {
      border-color: var(--highlight);
    }

    &:after {
      transform: scale(1, 1);
    }
  }
}
</style>
