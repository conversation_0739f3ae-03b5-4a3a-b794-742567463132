<template>
  <div class="password-reset-wrapper">
    <div class="password-reset-container">
      <img
        v-if="!isSuccess"
        class="login-logo mb-3"
        src="@/static/images/godesta_login_logo.png"
      />

      <GTitle
        v-if="!isSuccess"
        title="Password Reset"
        :subtitle="
          !isSuccess
            ? 'Please enter your new password'
            : 'Password successfully set'
        "
      />
      <form class="mt-2" v-if="!isSuccess">
        <PasswordStrength
          :password="password"
          @setPasswordStrengthScore="setPasswordStrengthScore"
        />

        <GTextField
          v-model="password"
          type="password"
          :placeholder="'Password'"
          :rules="[rules.required]"
          :customErrorMessage="customPasswordErrorMessage"
        ></GTextField>

        <GTextField
          v-model="confirmPassword"
          :placeholder="'Confirm Password'"
          type="password"
          :rules="[rules.required]"
        >
        </GTextField>

        <GButton
          :disabled="false"
          @click="setNewPassword"
          large
          color="white"
          :isLoading="isLoading"
          darkText
          block
          >Set Password</GButton
        >
      </form>
      <div v-if="isSuccess" class="border response-message-container">
        <div class="flex-row content-center">
          <v-icon class="icon" :color="'success'">fa fa-check-circle</v-icon>
        </div>
        <p>Password Updated Successfully!</p>
        <p>
          Your password has been successfully updated. You can now use your new
          password to log in to your account.
          <router-link to="/login">Return to Login</router-link>.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import PasswordStrength from '@/authentication/create_password/password_strength.vue';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  validate,
  validationRules,
} from '@/helpers/ValidationHelpers/ValidationHelpers';
import { Validation } from '@/interface-models/Generic/Validation';
import useHttp from '@/utils/http';
import { ComputedRef, Ref, computed, getCurrentInstance, ref } from 'vue';

const http = useHttp();

const isLoading: Ref<boolean> = ref(false);
const password: Ref<string> = ref('');
const confirmPassword: Ref<string> = ref('');
const passwordStrength: Ref<number> = ref(0);
const customPasswordErrorMessage: Ref<string> = ref('');
const instance: any = getCurrentInstance();

const isSuccess: Ref<boolean> = ref(false);

interface ChangeUserPasswordResponse {
  response: boolean;
  message: string;
}
async function setNewPassword() {
  try {
    const isValid = validate(instance);
    if (!isValid) {
      return;
    }
    const linkKey: string | undefined = instance.proxy.$route.params.id;
    const username: string | undefined = instance.proxy.$route.query.username;
    if (!linkKey || !username || !password.value) {
      return;
    }
    isLoading.value = true;
    const payload: string[] = [linkKey, username, password.value];
    const submitRequest = await http.post<ChangeUserPasswordResponse>(
      'app/password/forgotPassword/changePassword',
      payload,
    );
    isLoading.value = false;

    if (submitRequest.ok) {
      isSuccess.value = true;
    } else {
      throw new Error(submitRequest.errorMessage);
    }
  } catch (err) {
    console.error(err);
    showNotification('Something went wrong.');
  }
}

/**
 * Emitted event from our password strength component
 */
function setPasswordStrengthScore(score: number) {
  passwordStrength.value = score;
}

/**
 * Computes the validation rules from the store.
 * @returns {ComputedRef<Validation>} A computed reference to the validation rules.
 */
const rules: ComputedRef<Validation> = computed(() => {
  return validationRules;
});
</script>

<style scoped lang="scss">
.password-reset-wrapper {
  width: 100%;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ffffff;

  .password-reset-container {
    max-width: 350px;
  }
}

.login-logo {
  width: 80%;
  height: auto;
  display: block;
  margin: 0 auto 26px;
}

a {
  color: var(--highlight);
}

.response-message-container {
  max-width: 400px;
  padding: 20px 12px 12px 12px;
  text-align: center;
  .icon {
    font-size: 40px;
    margin: 0 0 20px 0;
  }
}
</style>
