<template>
  <div>
    <GTitle
      title="Account Recovery"
      subtitle="Please enter your accounts email address"
      :divider="false"
    />
    <form class="mt-2">
      <GTextField
        v-model="emailOrMobileNumber"
        placeholder="Email"
        outline
        :rules="[rules.required, rules.email]"
      ></GTextField>
      <div
        class="submit-container pt-2"
        v-shortkey.once="[`enter`]"
        @shortkey="getUsersAvailableAccountRecoveryOptions"
      >
        <GButton
          light
          class="ma-0"
          block
          large
          :isLoading="isLoading"
          @click="getUsersAvailableAccountRecoveryOptions"
          darkText
          color="white"
          >NEXT</GButton
        >
      </div>
    </form>

    <v-flex class="mt-4 pt-4">
      <v-divider class="mb-2"></v-divider>
      <span class="text-grey">
        <i class="fad fa-info-circle mr-2"></i>
        If you’ve forgotten the email address or credentials used to set up your
        login, please contact your transport operator’s customer service team
        for assistance.
      </span>
    </v-flex>
  </div>
</template>

<script setup lang="ts">
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { removeWhitespace } from '@/helpers/StringHelpers/StringHelpers';
import {
  validate,
  validationRules,
} from '@/helpers/ValidationHelpers/ValidationHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { Validation } from '@/interface-models/Generic/Validation';
import useHttp from '@/utils/http';
import {
  computed,
  ComputedRef,
  getCurrentInstance,
  ref,
  Ref,
  watch,
} from 'vue';
const http = useHttp();

const emit = defineEmits([
  'setEmailOrMobileNumber',
  'setAvailableProcessOptions',
]);
const emailOrMobileNumber: Ref<string> = ref('');
const isLoading: Ref<boolean> = ref(false);
const instance: any = getCurrentInstance();

interface UserOptionsResponse {
  response: boolean;
  message: string;
  userOptions: string[];
}

/**
 * Formats number string into the (#### ### ###) presentation format.
 * If the input is not number, returns the original string.
 *
 * @param {string | undefined} value - The phone number to be formatted.
 * @returns {string} The formatted phone number or the original number if no matching format is found.
 */
function formatInput(value: string): string {
  // Check if the input consists of digits only
  if (/^\d+$/.test(value)) {
    return value.replace(/(\d{4})(\d{3})(\d{3})/, '$1 $2 $3'); // Format the digits
  } else {
    return value; // Otherwise, keep it unchanged
  }
}

// Watcher to format the mobile number input whenever its value changes
watch(emailOrMobileNumber, (newValue, oldValue) => {
  emailOrMobileNumber.value = formatInput(newValue);
});

/**
 * Asynchronously retrieves available account recovery options for a user based on their provided email or mobile number.
 * Validates the instance before making the HTTP request. If the request succeeds and returns exactly 2 recovery options,
 * these options are emitted for further processing. If any step fails, an error is logged and a notification is shown to the user.
 *
 * @async
 * @returns {Promise<void>}
 * @throws {Error} If the number of recovery options is not exactly 2 or if the HTTP request fails.
 */
async function getUsersAvailableAccountRecoveryOptions(): Promise<void> {
  try {
    const isValid = validate(instance);
    if (!isValid) {
      return;
    }
    isLoading.value = true;
    // Strip whitespace from phone number formatting
    const payload = removeWhitespace(emailOrMobileNumber.value);
    // Send http request
    const submitRequest = await http.get<UserOptionsResponse>(
      'app/password/forgotPassword/userOptions/' + payload,
    );

    if (submitRequest.ok) {
      // The options that are returned are defined within a string array. We should make sure there is at least 2 options.
      const numberOfOptions: number =
        submitRequest.data && submitRequest.data.userOptions
          ? submitRequest.data.userOptions.length
          : 0;
      if (numberOfOptions < 1) {
        throw new Error(
          'Expected at least 1 option for the users forgot password process',
        );
      }

      isLoading.value = false;
      emit('setEmailOrMobileNumber', emailOrMobileNumber.value);
      emit('setAvailableProcessOptions', submitRequest.data.userOptions);
    } else {
      throw new Error(submitRequest.errorMessage);
    }
  } catch (err: unknown) {
    isLoading.value = false;
    emailOrMobileNumber.value = '';
    console.error(err);

    const errorMessage: string =
      err instanceof Error && err.message !== 'Invalid uername.'
        ? err.message
        : 'Something went wrong.';
    showNotification(errorMessage, {
      title: 'Account Recovery',
      type: HealthLevel.ERROR,
    });
  }
}

/**
 * Computes the validation rules from the store.
 * @returns {ComputedRef<Validation>} A computed reference to the validation rules.
 */
const rules: ComputedRef<Validation> = computed(() => {
  return validationRules;
});
</script>

<style scoped lang="scss">
.text-grey {
  font-size: 14px;
  color: $light-text-color;
  font-weight: 500;
}
</style>
