<template>
  <div class="password-recovery-container">
    <div class="center-container">
      <div v-if="isLoading" class="loading-container">
        <img
          src="@/static/loader/infinity-loader-light.svg"
          height="100px"
          width="100px"
        />
      </div>

      <div v-if="success" class="border response-message-container">
        <div class="flex-row content-center">
          <v-icon class="icon" :color="'success'">fa fa-check-circle</v-icon>
        </div>
        <p>SMS Verification Successful!</p>
        <p>
          You will soon receive a password recovery email with a link to reset
          your password.
          <router-link to="/login">Return to Login</router-link>.
        </p>
      </div>

      <div v-if="success === false" class="border response-message-container">
        <div class="flex-row content-center">
          <v-icon class="icon" :color="'error'"
            >fa fa-exclamation-circle</v-icon
          >
        </div>
        <p>Oops! Something Went Wrong.</p>
        <p>
          We encountered an issue while processing your link. Please request a
          new link, and we'll send you a fresh one. If you continue to encounter
          issues, feel free to contact the GoDesta support team for further
          assistance.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getCurrentInstance, onMounted, ref, Ref } from 'vue';

// example link:  http://localhost:8080/passwordRecovery/4ef56294-eab5-43ec-9f3b-ea37a8326e5e?contactNumber=0483478175&expiry=1708576710502

import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import useHttp from '@/utils/http';
const http = useHttp();
const instance: any = getCurrentInstance();

const isLoading: Ref<boolean> = ref(true);
const success: Ref<boolean | null> = ref(null);
async function submitLinkVerification() {
  try {
    const linkKey: string = instance.proxy.$route.params.id;
    const contactNumber: string = instance.proxy.$route.query.contactNumber;
    const email: string = instance.proxy.$route.query.emailAddress;

    const payload = [email, contactNumber, linkKey];
    interface SendOTPEmailResponse {
      response: boolean;
      message: string;
    }
    const response = await http.post<SendOTPEmailResponse>(
      'app/password/forgotPassword/resetPassword',
      payload,
    );

    if (!response.ok) {
      throw new Error(response.errorMessage);
    }
    success.value = true;
    isLoading.value = false;
  } catch (error: unknown) {
    console.error(error);
    isLoading.value = false;
    success.value = false;
    showNotification('Something went wrong.');
  }
}

onMounted(async () => {
  await submitLinkVerification();
});
</script>

<style scoped lang="scss">
.password-recovery-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ffffff;

  .center-container {
    max-width: 350px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .response-message-container {
    max-width: 400px;
    padding: 20px 12px 12px 12px;
    text-align: center;
    .icon {
      font-size: 40px;
      margin: 0 0 20px 0;
    }
  }
}

a {
  color: var(--highlight);
}
</style>
