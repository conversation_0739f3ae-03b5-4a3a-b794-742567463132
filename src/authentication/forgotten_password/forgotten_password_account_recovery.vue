<template>
  <div class="account-recovery-page pb-4">
    <div class="account-recovery-container">
      <form class="account-recovery-form">
        <div class="step-header-container">
          <div class="header-content">
            <GTitle
              :title="headingText.title"
              :subtitle="headingText.subtitle"
              :divider="false"
            />

            <p class="step-counter">
              {{ currentStepNumber }} / {{ isBrandedEndpoint ? 4 : 5 }}
            </p>
          </div>

          <hr class="divider" />
        </div>

        <div class="step-content-container" v-if="isLoading">
          <div v-if="isLoading" class="loading-container">
            <img
              src="@/static/loader/infinity-loader-light.svg"
              height="100px"
              width="100px"
            />
          </div>
        </div>

        <div class="step-content-container" v-if="!isLoading">
          <div
            class="photo-license-step"
            v-if="currentStep === AccountRecoveryProcess.LICENSE_UPLOAD"
          >
            <FileUpload
              class="upload"
              :imageLabel="'Front Of Drivers Licence'"
              :attachmentSingle="true"
              :documentTypeId="2"
              :isAccountRecovery="true"
              :attachment="licensePhoto"
              @setAttachment="setLicenseAttachment"
            />
          </div>

          <div
            v-if="
              currentStep ===
              AccountRecoveryProcess.SECONDARY_VERIFICATION_SELECT
            "
          >
            <div class="radio mt-2 mb-3">
              <input
                type="radio"
                name="sms"
                id="sms"
                class="radio__input"
                checked
                value="securityQuestions"
                v-model="selectedSecondaryValidationType"
              />
              <label for="sms" class="radio__label"
                ><strong>Security Questions</strong><br />I would like to answer
                my security questions</label
              >
            </div>

            <div class="radio mb-2">
              <input
                type="radio"
                name="none"
                id="none"
                class="radio__input"
                value="workplacePhoto"
                v-model="selectedSecondaryValidationType"
              />
              <label for="none" class="radio__label"
                ><strong>Recent Photo Verification</strong><br />Upload a photo
                of myself in front of my Truck</label
              >
            </div>
          </div>

          <div v-if="currentStep === AccountRecoveryProcess.SECURITY_QUESTIONS">
            <div
              v-for="(securityQuestion, i) of securityQuestionAnswers"
              :key="i"
            >
              <GTextField
                v-model="securityQuestion.answer"
                :placeholder="securityQuestion.question"
                :rules="[rules.required]"
              ></GTextField>
            </div>
          </div>

          <div v-if="currentStep === AccountRecoveryProcess.WORKPLACE_PHOTO">
            <FileUpload
              :imageLabel="'Workplace Photo'"
              :attachmentSingle="true"
              :documentTypeId="2"
              :isAccountRecovery="true"
              :attachment="workplacePhoto"
              @setAttachment="setWorkplacePhoto"
            />
          </div>

          <div v-if="currentStep === AccountRecoveryProcess.COMPANY_REVIEWER">
            <GSelect
              v-model="selectedCompany"
              :items="companies"
              :disabled="false"
              :item-text="'name'"
              :item-value="'code'"
              :placeholder="'The company that will verify your request'"
              :label="'The company that will verify your request'"
              hint="The company that will verify your request"
            />
          </div>

          <div v-if="currentStep === AccountRecoveryProcess.NEW_CREDENTIALS">
            <GTextField
              v-model="newEmailAddress"
              :placeholder="'Please enter your new email address'"
              :rules="[rules.required, rules.email]"
            ></GTextField>

            <GTextField
              v-model="newMobileNumber"
              :placeholder="'Please enter your new mobile number'"
              :rules="[
                rules.required,
                rules.AUSMobile,
                rules.noSpace,
                rules.number,
              ]"
            ></GTextField>
          </div>

          <div v-if="currentStep === AccountRecoveryProcess.SUCCESS">
            <p>
              Your Account Recovery request has been successfully submitted and
              is currently being processed.
            </p>
          </div>
        </div>

        <div class="step-footer-container">
          <hr class="divider" />
          <div class="action-btn-container">
            <GButton
              v-if="currentStep !== AccountRecoveryProcess.SUCCESS"
              :disabled="isLoadingSubmitRequest"
              class="mb-2"
              small
              outline
              @click="goPrevious"
              :icon="'fa fa-chevron-left'"
              >Back</GButton
            >

            <v-btn
              outline
              small
              color="accent"
              v-if="currentStep === AccountRecoveryProcess.SUCCESS"
            >
              <v-icon size="12" class="mr-2">fa fa-chevron-left</v-icon>
              <router-link to="/login">Return to Login</router-link>
            </v-btn>

            <GButton
              v-if="currentStep !== AccountRecoveryProcess.SUCCESS"
              class="mb-2"
              small
              iconRight
              @click="goNext"
              :isLoading="isLoadingSubmitRequest"
              :disabled="nextDisabled() || isLoadingSubmitRequest"
              :icon="'fa fa-chevron-right'"
              >{{
                currentStepNumber === (isBrandedEndpoint ? 4 : 5)
                  ? 'Submit'
                  : 'Next'
              }}</GButton
            >
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import FileUpload from '@/components/common/file-upload/index.vue';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import Attachment from '@/interface-models/Generic/Attachment/Attachment';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import useHttp from '@/utils/http';
import {
  computed,
  ComputedRef,
  getCurrentInstance,
  onMounted,
  ref,
  Ref,
} from 'vue';

import {
  validate,
  validationRules,
} from '@/helpers/ValidationHelpers/ValidationHelpers';
import { SecurityQuestion } from '@/interface-models/Authentication/SecurityQuestion';
import { Validation } from '@/interface-models/Generic/Validation';
import {
  FetchSecurityQuestionsResponse,
  FetchTransportCompanyNamesResponse,
  MFAPasswordRecoveryRequest,
  SecurityQuestionAnswer,
  TransportCompanyDetailsForMFA,
} from '@/interface-models/User/PasswordRecovery';
import { useRouter } from 'vue-router/composables';

enum AccountRecoveryProcess {
  LICENSE_UPLOAD = 'LICENSE_UPLOAD',
  SECONDARY_VERIFICATION_SELECT = 'SECONDARY_VERIFICATION_SELECT',
  SECURITY_QUESTIONS = 'SECURITY_QUESTIONS',
  WORKPLACE_PHOTO = 'WORKPLACE_PHOTO',
  COMPANY_REVIEWER = 'COMPANY_REVIEWER',
  NEW_CREDENTIALS = 'NEW_CREDENTIALS',
  SUCCESS = 'SUCCESS',
}

const instance: any = getCurrentInstance();

const http = useHttp();
interface IProps {
  username: string;
}
const props = withDefaults(defineProps<IProps>(), {});

const companyDetailsStore = useCompanyDetailsStore();
const router = useRouter();

const currentStep: Ref<AccountRecoveryProcess> = ref(
  AccountRecoveryProcess.LICENSE_UPLOAD,
);

const selectedSecondaryValidationType: Ref<
  'securityQuestions' | 'workplacePhoto'
> = ref('securityQuestions');

const licensePhoto: Ref<Attachment | null> = ref(null);
const workplacePhoto: Ref<Attachment | null> = ref(null);
const companies: Ref<TransportCompanyDetailsForMFA[]> = ref([]);
const selectedCompany: Ref<any> = ref('');
const isSecurityQuestions: Ref<boolean> = ref(true);
const securityQuestionAnswers: Ref<SecurityQuestionAnswer[]> = ref([]);
const newEmailAddress: Ref<string> = ref('');
const newMobileNumber: Ref<string> = ref('');
const isBrandedEndpoint: Ref<boolean> = ref(false);
const isLoadingSecurityQuestion: Ref<boolean> = ref(true);
const isLoadingCompanyList: Ref<boolean> = ref(true);
const isLoadingSubmitRequest: Ref<boolean> = ref(false);

/**
 * Computes whether all security questions are present. utilised for whether the next button is disabled or not.
 * @returns {void}
 */
const allSecurityQuestionsAnswered: ComputedRef<boolean> = computed(() => {
  const unanswered: SecurityQuestionAnswer | undefined =
    securityQuestionAnswers.value.find(
      (x: SecurityQuestionAnswer) => x.answer === '',
    );
  if (unanswered === undefined) {
    return false;
  } else {
    return true;
  }
});

/**
 * Handles emit event from attachment component for when a workplace photo is selected.
 * @returns {void}
 */
function setWorkplacePhoto(attachment: Attachment): void {
  if (!attachment) {
    return;
  }
  workplacePhoto.value = attachment;
}

/**
 * Handles emit event from attachment component for when a licence photo is selected.
 * @returns {void}
 */
function setLicenseAttachment(attachment: Attachment): void {
  licensePhoto.value = attachment;
}

/**
 * Submits an account recovery request. It performs several checks before
 * sending the request, including verifying that a driver's licence photo is attached,
 * validating input fields, ensuring that the passwords match and meet the required strength,
 * and conditionally including workplace photo or security question answers based on the recovery method.
 * Notifications are displayed to the user based on the success or failure of these validations
 * and the submission process.
 *
 * @async
 * @function submitAccountRecovery
 * @returns {Promise<void>}
 * @throws {Error}
 */
async function submitAccountRecovery(): Promise<void> {
  try {
    if (!licensePhoto.value) {
      showNotification("A driver's licence must be attached ", {
        title: 'Account Recovery',
        type: HealthLevel.ERROR,
      });
      return;
    }
    // validate inputs
    const isValid = validate(instance);
    if (!isValid) {
      return;
    }

    const request: MFAPasswordRecoveryRequest = {
      username: props.username,
      company: selectedCompany.value,
      licensePhoto: licensePhoto.value,
      workplacePhoto: !isSecurityQuestions.value ? workplacePhoto.value : null,
      securityQuestionAnswers: isSecurityQuestions.value
        ? securityQuestionAnswers.value
        : null,
      newEmailAddress: newEmailAddress.value,
      newContactNumber: newMobileNumber.value,
    };

    interface MFAPasswordRecoveryResponse {
      response: boolean;
      message: string;
    }
    isLoadingSubmitRequest.value = true;
    const submitRequest = await http.post<MFAPasswordRecoveryResponse>(
      'app/password/forgotPassword/saveAccountRecoveryRequest',
      request,
    );
    if (submitRequest.ok) {
      if (submitRequest.data.message === 'SUCCESS!') {
        currentStep.value = AccountRecoveryProcess.SUCCESS;
      }
    } else {
      throw new Error(
        submitRequest.errorMessage
          ? submitRequest.errorMessage
          : 'Something went wrong.',
      );
    }
  } catch (err: unknown) {
    console.error(err);
    const errorMessage: string =
      err instanceof Error && err.message !== 'Wrong answers!'
        ? err.message
        : 'Something went wrong!';
    showNotification(errorMessage, {
      title: 'Account Recovery Request Failed',
      type: HealthLevel.ERROR,
    });
  } finally {
    isLoadingSubmitRequest.value = false;
  }
}

/**
 * HTTP GET request to retrieve transport company names associated with the provided username.
 * If the request is successful and the user is on a branded endpoint, it automatically selects
 * the applicable company by matching the `companyNameId` from the `CompanyDetailsStore` with
 * the fetched companies list.
 *
 * @async
 * @function getCompanyDivisionList
 * @returns {Promise<void>}
 * @throws {Error} Throws an error if the HTTP request fails or if the server response indicates an error,
 * indicating issues in fetching transport companies.
 */
async function getCompanyDivisionList(): Promise<void> {
  try {
    const submitRequest = await http.get<FetchTransportCompanyNamesResponse>(
      'app/password/forgotPassword/fetchTransportCompanies/' + props.username,
    );
    if (submitRequest.ok) {
      companies.value = submitRequest.data.companies;
      // If the user is on a branded endpoint we should auto select the applicable company

      if (companyDetailsStore.companyDetails?.companyNameId) {
        const existsInCompanyList: TransportCompanyDetailsForMFA | undefined =
          submitRequest.data.companies.find(
            (x: TransportCompanyDetailsForMFA) =>
              x.code === companyDetailsStore.companyDetails!.companyNameId,
          );
        if (existsInCompanyList) {
          isBrandedEndpoint.value = true;
          selectedCompany.value =
            companyDetailsStore.companyDetails.companyNameId;
        }
      }
    } else {
      throw new Error('Something went wrong.');
    }
  } catch (err) {
    console.error('Failed to get users company list.');
  } finally {
    isLoadingCompanyList.value = false;
  }
}

/**
 * HTTP GET request to retrieve the user's security questions.
 * If the request is successful, it populates the `securityQuestionAnswers` reactive variable
 * with the fetched security questions, setting up an empty string for the answer of each question.
 * In case of a failure we set the security questions to an empty array and set secondary verification
 * method to workplace photo.
 *
 * @async
 * @function getUserSecurityQuestions
 * @returns {Promise<void>}
 * @throws {Error} Throws an error if the HTTP request fails or if the server response indicates an error.
 */
async function getUserSecurityQuestions(): Promise<void> {
  try {
    const submitRequest = await http.get<FetchSecurityQuestionsResponse>(
      'app/password/forgotPassword/fetchSecurityQuestions/' + props.username,
    );
    if (submitRequest.ok) {
      securityQuestionAnswers.value = submitRequest.data.securityQuestions.map(
        (x: SecurityQuestion) => {
          return {
            question: x.question,
            answer: '',
          };
        },
      );
    } else {
      throw new Error('Something went wrong.');
    }
  } catch (err) {
    console.error('Failed to get users security questions.');
    securityQuestionAnswers.value = [];
    isSecurityQuestions.value = false;
  } finally {
    isLoadingSecurityQuestion.value = false;
  }
}
/**
 * Handles the disabled state of the next button.
 * @returns {boolean}
 */
function nextDisabled(): boolean {
  if (currentStep.value === AccountRecoveryProcess.LICENSE_UPLOAD) {
    return !licensePhoto.value;
  } else if (
    currentStep.value === AccountRecoveryProcess.SECONDARY_VERIFICATION_SELECT
  ) {
    return (
      selectedSecondaryValidationType.value !== 'securityQuestions' &&
      selectedSecondaryValidationType.value !== 'workplacePhoto'
    );
  } else if (currentStep.value === AccountRecoveryProcess.SECURITY_QUESTIONS) {
    return allSecurityQuestionsAnswered.value;
  } else if (currentStep.value === AccountRecoveryProcess.WORKPLACE_PHOTO) {
    return !workplacePhoto.value;
  } else if (currentStep.value === AccountRecoveryProcess.NEW_CREDENTIALS) {
    return newEmailAddress.value === '' || newMobileNumber.value === '';
  } else if (currentStep.value === AccountRecoveryProcess.COMPANY_REVIEWER) {
    return selectedCompany.value === '';
  }
  return true;
}

/**
 * Handles the Next button functionality based off of the current step.
 * @returns {void}
 */
function goNext(): void {
  if (nextDisabled()) {
    return;
  }
  if (currentStep.value === AccountRecoveryProcess.LICENSE_UPLOAD) {
    if (securityQuestionAnswers.value.length > 0) {
      currentStep.value = AccountRecoveryProcess.SECONDARY_VERIFICATION_SELECT;
    } else {
      currentStep.value = AccountRecoveryProcess.WORKPLACE_PHOTO;
    }
  } else if (
    currentStep.value === AccountRecoveryProcess.SECONDARY_VERIFICATION_SELECT
  ) {
    if (selectedSecondaryValidationType.value === 'securityQuestions') {
      currentStep.value = AccountRecoveryProcess.SECURITY_QUESTIONS;
      isSecurityQuestions.value = true;
    } else {
      currentStep.value = AccountRecoveryProcess.WORKPLACE_PHOTO;
      isSecurityQuestions.value = false;
    }
  } else if (
    currentStep.value === AccountRecoveryProcess.SECURITY_QUESTIONS ||
    currentStep.value === AccountRecoveryProcess.WORKPLACE_PHOTO
  ) {
    if (!isBrandedEndpoint.value) {
      currentStep.value = AccountRecoveryProcess.COMPANY_REVIEWER;
    } else {
      currentStep.value = AccountRecoveryProcess.NEW_CREDENTIALS;
    }
  } else if (currentStep.value === AccountRecoveryProcess.COMPANY_REVIEWER) {
    currentStep.value = AccountRecoveryProcess.NEW_CREDENTIALS;
  } else if (currentStep.value === AccountRecoveryProcess.NEW_CREDENTIALS) {
    const isValid = validate(instance);
    if (!isValid) {
      return;
    }
    submitAccountRecovery();
  }
}

/**
 * Handles the back button functionality based off of the current step.
 * @returns {void}
 */
function goPrevious(): void {
  if (currentStep.value === AccountRecoveryProcess.LICENSE_UPLOAD) {
    router.push('login');
  } else if (
    currentStep.value === AccountRecoveryProcess.SECONDARY_VERIFICATION_SELECT
  ) {
    currentStep.value = AccountRecoveryProcess.LICENSE_UPLOAD;
  } else if (
    currentStep.value === AccountRecoveryProcess.SECURITY_QUESTIONS ||
    currentStep.value === AccountRecoveryProcess.WORKPLACE_PHOTO
  ) {
    if (securityQuestionAnswers.value.length > 0) {
      currentStep.value = AccountRecoveryProcess.SECONDARY_VERIFICATION_SELECT;
    } else {
      currentStep.value = AccountRecoveryProcess.LICENSE_UPLOAD;
    }
  } else if (currentStep.value === AccountRecoveryProcess.COMPANY_REVIEWER) {
    if (isSecurityQuestions.value) {
      currentStep.value = AccountRecoveryProcess.SECURITY_QUESTIONS;
    } else {
      currentStep.value = AccountRecoveryProcess.WORKPLACE_PHOTO;
    }
  } else if (currentStep.value === AccountRecoveryProcess.NEW_CREDENTIALS) {
    if (isSecurityQuestions.value) {
      currentStep.value = AccountRecoveryProcess.SECURITY_QUESTIONS;
    } else {
      currentStep.value = AccountRecoveryProcess.WORKPLACE_PHOTO;
    }
  }
}

/**
 * Computes the validation rules from the store.
 * @returns {ComputedRef<Validation>} A computed reference to the validation rules.
 */
const rules: ComputedRef<Validation> = computed(() => {
  return validationRules;
});

/**
 * Computes the overall loading state
 * @returns {ComputedRef<boolean>} A computed reference to the validation rules.
 */
const isLoading: ComputedRef<boolean> = computed(() => {
  return isLoadingSecurityQuestion.value || isLoadingCompanyList.value;
});

const currentStepNumber: ComputedRef<number> = computed(() => {
  let currentStepNumber: number = 1;
  if (currentStep.value === AccountRecoveryProcess.LICENSE_UPLOAD) {
    currentStepNumber = 1;
  } else if (
    currentStep.value === AccountRecoveryProcess.SECONDARY_VERIFICATION_SELECT
  ) {
    currentStepNumber = 2;
  } else if (
    currentStep.value === AccountRecoveryProcess.SECURITY_QUESTIONS ||
    currentStep.value === AccountRecoveryProcess.WORKPLACE_PHOTO
  ) {
    currentStepNumber = 3;
  } else if (currentStep.value === AccountRecoveryProcess.COMPANY_REVIEWER) {
    currentStepNumber = 4;
  } else if (currentStep.value === AccountRecoveryProcess.NEW_CREDENTIALS) {
    currentStepNumber = isBrandedEndpoint.value ? 4 : 5;
  } else if (currentStep.value === AccountRecoveryProcess.SUCCESS) {
    currentStepNumber = isBrandedEndpoint.value ? 4 : 5;
  }

  return currentStepNumber;
});

interface HeadingText {
  title: string;
  subtitle: string;
}
/**
 * Computes the GTitle title and subtitle based on what step the user is on
 * @returns {ComputedRef<HeadingText>} A computed reference to the validation rules.
 */
const headingText: ComputedRef<HeadingText> = computed(() => {
  let title = 'Account Recovery';
  let subtitle = currentStepNumber.value + '. ';
  if (currentStep.value === AccountRecoveryProcess.LICENSE_UPLOAD) {
    subtitle += "Please provide a copy of your driver's licence";
  } else if (
    currentStep.value === AccountRecoveryProcess.SECONDARY_VERIFICATION_SELECT
  ) {
    subtitle += 'Please select a secondary verification method';
  } else if (currentStep.value === AccountRecoveryProcess.SECURITY_QUESTIONS) {
    subtitle += 'Please answer all of your security questions.';
  } else if (currentStep.value === AccountRecoveryProcess.NEW_CREDENTIALS) {
    subtitle += 'Please enter your new email address and mobile number.';
  } else if (currentStep.value === AccountRecoveryProcess.WORKPLACE_PHOTO) {
    subtitle += 'Please upload a photo of yourself in front of your Truck.';
  } else if (currentStep.value === AccountRecoveryProcess.COMPANY_REVIEWER) {
    subtitle +=
      'Please select the company that you wish to verify your request.';
  } else if (currentStep.value === AccountRecoveryProcess.SUCCESS) {
    subtitle = 'Account Recovery Request Sent';
  }
  return {
    title,
    subtitle,
  };
});

onMounted(() => {
  getCompanyDivisionList();
  getUserSecurityQuestions();
});
</script>

<style scoped lang="scss">
.account-recovery-page {
  width: 100%;
}

.account-recovery-container {
  max-width: 700px;
  margin: 0 auto;
  padding: 28px;
}
.account-recovery-form {
  max-width: 700px;
  display: grid;
  padding: 12px;
  border-radius: 16px;
  color: var(--text-color);
  background-color: var(--background-color-200);
  border: 1px solid var(--border-color);
  grid-template-rows: 60px 1fr 50px;
  grid-template-areas:
    'header'
    'content'
    'actions';
}

.checkmark {
  color: green;
  margin-left: 8px;
}

.completed {
  font-weight: bold;
  color: #4caf50;
}

.loading-container {
  display: flex;
  justify-content: center;
}

.step-header-container {
  grid-area: header;
}

.header-content {
  display: flex;
  justify-content: space-between;
}
.step-content-container {
  grid-area: content;
  padding: 15px;
}
.step-footer-container {
  width: 100%;

  grid-area: actions;
}

.action-btn-container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 15px;
}

.step-counter {
  font-family: $sub-font-family;
  font-size: $font-size-20;
  font-weight: 700;
  letter-spacing: 1px;
  width: 50px;
}

.radio {
  position: relative;
  margin: 0 0 10px;
  font-size: $font-size-16;
  line-height: 24px;
  margin-bottom: 10px;
  display: flex;

  color: var(--light-text-color);
  strong {
    color: var(--text-color);
  }

  &__input {
    position: absolute;
    top: 4px;
    left: 0;
    width: 36px;
    height: 20px;
    opacity: 0;
    z-index: 0;
  }
  &__label {
    display: block;
    padding: 0 0 0 24px;
    cursor: pointer;

    &:before {
      content: '';
      position: absolute;
      top: 4px;
      left: 0;
      width: 16px;
      height: 16px;
      background-color: transparent;
      border: 2px solid var(--text-color-light);
      border-radius: 14px;
      z-index: 1;
      transition: border-color 0.28s cubic-bezier(0.4, 0, 0.2, 1);
    }

    &:after {
      content: '';
      position: absolute;
      top: 8px;
      left: 4px;
      width: 8px;
      height: 8px;
      background-color: var(--highlight);
      border-radius: 50%;
      z-index: 2;
      transform: scale(0, 0);
      transition: transform 0.28s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }
  &__input:checked + &__label {
    &:before {
      border-color: var(--highlight);
    }

    &:after {
      transform: scale(1, 1);
    }
  }
}

a {
  color: var(--highlight);
}

@media (max-width: $user-portal-desktop-breakpoint) {
  .account-recovery-form {
    grid-template-rows: 65px 1fr 50px;
  }

  .header-content {
    margin-bottom: 10px;
  }

  .account-recovery-container {
    padding: 8px;
  }
}
</style>
