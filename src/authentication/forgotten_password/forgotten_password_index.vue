<template>
  <div>
    <div class="flex-row pl-1">
      <GButton
        :disabled="false"
        xsmall
        class="v-btn-custom"
        @click="backToLogin"
        :icon="'fa fa-chevron-left'"
        >Back To Login</GButton
      >
    </div>

    <hr class="divider" />
    <ForgottenPasswordAccountDetails
      v-if="currentStep === ForgotPasswordProcess.ACCOUNT_DETAILS"
      @setAvailableProcessOptions="setAvailableProcessOptions"
      @setEmailOrMobileNumber="setEmailOrMobileNumber"
    />
    <ForgottenPasswordProcessOptionSelect
      v-if="
        currentStep === ForgotPasswordProcess.PROCESS_OPTION_SELECT &&
        availableProcessOptions
      "
      :emailOrMobileNumber="emailOrMobileNumber"
      :availableProcessOptions="availableProcessOptions"
      @backToLogin="backToLogin"
    />
  </div>
</template>

<script setup lang="ts">
import ForgottenPasswordAccountDetails from '@/authentication/forgotten_password/forgotten_password_account_details.vue';
import ForgottenPasswordProcessOptionSelect from '@/authentication/forgotten_password/forgotten_password_process_option_select.vue';
import { ref, Ref } from 'vue';

const emailOrMobileNumber: Ref<string> = ref('');

enum ForgotPasswordProcess {
  ACCOUNT_DETAILS = 'ACCOUNT_DETAILS',
  PROCESS_OPTION_SELECT = 'PROCESS_OPTION_SELECT',
}

const emit = defineEmits(['backToLogin']);

const currentStep: Ref<ForgotPasswordProcess> = ref(
  ForgotPasswordProcess.ACCOUNT_DETAILS,
);
// holds a list of options available to the user. At this point in time options are sms or photo and security questions.
const availableProcessOptions: Ref<string[] | null> = ref(null);

function backToLogin(): void {
  emit('backToLogin');
}

function setAvailableProcessOptions(selectOptions: string[]) {
  availableProcessOptions.value = selectOptions;
  currentStep.value = ForgotPasswordProcess.PROCESS_OPTION_SELECT;
}

function setEmailOrMobileNumber(emailOrMobileNumberForProcess: string) {
  emailOrMobileNumber.value = emailOrMobileNumberForProcess;
}
</script>

<style scoped lang="scss"></style>
