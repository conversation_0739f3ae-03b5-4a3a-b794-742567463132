<template>
  <div class="confirm-username-change">
    <div class="center-container">
      <div v-if="isLoading" class="loading-container">
        <img
          src="@/static/loader/infinity-loader-light.svg"
          height="100px"
          width="100px"
        />
      </div>

      <div v-if="!isLoading" class="response-message-container border">
        <div class="flex-row content-center">
          <v-icon v-if="success" class="icon" :color="'success'"
            >fa fa-check-circle</v-icon
          >
          <v-icon v-if="!success" class="icon" :color="'error'"
            >fa fa-exclamation-circle</v-icon
          >
        </div>

        <div v-if="success">
          <p>
            <span v-if="type === 'email'">Email address</span>
            <span v-else>Mobile number </span>
            updated successfully!
          </p>

          <p>
            Your <span v-if="type === 'email'">email address</span>
            <span v-else>mobile number </span> has been successfully updated.
            You can now use your new
            <span v-if="type === 'email'">email address</span>
            <span v-else>mobile number </span> to log in to your account.
            <router-link to="/login">Return to Login</router-link>.
          </p>
        </div>
        <div v-if="!success">
          <div v-if="linkExpired">
            <p>Sorry, the link to update your email address has expired.</p>
            <p>
              <span v-if="type === 'email'">Email address</span>
              <span v-else>Mobile number </span> update links are typically
              time-sensitive for security reasons. Please request a new update
              link, and we'll send you a fresh one. If you continue to encounter
              issues, feel free to contact the GoDesta support team for further
              assistance.
            </p>
          </div>
          <div v-if="otherException">
            <p>Oops! Something Went Wrong.</p>
            <p>
              We encountered an issue while processing your email update
              request. Please request a new update link, and we'll send you a
              fresh one. If you continue to encounter issues, feel free to
              contact the GoDesta support team for further assistance.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// example link: http://localhost:8080/#/update-details/af22ae28-0cf1-4e2a-8415-fc9160718c9f?authRefId=5e8578da345a910001b0c038&type=email&value=<EMAIL>&expiry=1702092543600

import { getCurrentInstance, onMounted, ref, Ref } from 'vue';

import useHttp from '@/utils/http';
const http = useHttp();
const instance: any = getCurrentInstance();

const isLoading: Ref<boolean> = ref(true);
const linkExpired: Ref<boolean> = ref(false);
const type: Ref<string> = ref('');
const success: Ref<boolean> = ref(false);
const otherException: Ref<boolean> = ref(false);
async function submitUpdateDetailsVerification() {
  try {
    // construct payload from link params and query.
    const linkKey: string = instance.proxy.$route.params.id;
    const authRefId: string = instance.proxy.$route.query.authRefId;
    type.value = instance.proxy.$route.query.type;
    const value: string = instance.proxy.$route.query.value;
    const payload = [linkKey, authRefId, type.value, value];

    interface UpdateDetailsResponse {
      linkKey: string;
      response: string;
      message: string;
      errorMessage: string;
    }

    const response = await http.post<UpdateDetailsResponse>(
      'app/password/confirm-changes',
      payload,
    );

    if (!response.ok) {
      throw new Error(response.errorMessage);
    }

    success.value = true;
    isLoading.value = false;
  } catch (error: unknown) {
    console.error(error);
    // Link Expired || Invalid link || other exception
    if (error instanceof Error) {
      linkExpired.value = error.message === 'Link Expired!';
      otherException.value = error.message !== 'Link Expired!';
    } else {
      otherException.value = true;
    }
    isLoading.value = false;
  }
}

onMounted(async () => await submitUpdateDetailsVerification());
</script>

<style scoped lang="scss">
.confirm-username-change {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ffffff;

  .center-container {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}

.response-message-container {
  max-width: 400px;
  padding: 20px 12px 12px 12px;
  text-align: center;
  .icon {
    font-size: 40px;
    margin: 0 0 20px 0;
  }
}

a {
  color: var(--highlight);
}

@media (min-width: $user-portal-desktop-breakpoint) {
}
</style>
