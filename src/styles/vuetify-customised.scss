/* ========================================================================== */
/* GLOBAL APP UI STYLES */
/* GLOBAL APP UI STYLES OVERWRITE FOR VUETIFY */
/* ========================================================================== */
html {
  overflow-y: auto;
  background-color: $app-dark-primary-200;
}
#app {
  height: 100%;
  &.dark-theme {
    background-color: var(--background-color-100);
  }

  &.light-theme {
    background-color: $app-dark-primary-100;
  }
}
.app-border-radius {
  border-radius: $border-radius-base;
}
.app-bgcolor {
  &--100 {
    background-color: var(--background-color-100);
  }

  &--200 {
    background-color: var(--background-color-200);
  }

  &--250 {
    background-color: var(--background-color-250);
  }

  &--300 {
    background-color: var(--background-color-300);
  }

  &--400 {
    background-color: var(--background-color-400);
  }

  &--500 {
    background-color: var(--background-color-500);
  }

  &--600 {
    background-color: var(--background-color-600);
  }

  &--650 {
    background-color: var(--background-color-650);
  }

  &--700 {
    background: var(--primary-gradient);
  }

  &--750 {
    background-color: var(--highlight);
  }

  &--760 {
    background: $highlightgradient;
  }

  &--800 {
    background-color: var(--text-color);
  }
}

.app-bordercolor {
  &--100 {
    border-color: var(--background-color-100);
  }

  &--200 {
    border-color: var(--background-color-200);
  }

  &--300 {
    border-color: var(--background-color-300);
  }

  &--400 {
    border-color: var(--background-color-400);
  }

  &--500 {
    border-color: var(--background-color-500);
  }

  &--600 {
    border-color: var(--background-color-600);
  }

  &--650 {
    border-color: var(--background-color-650);
  }

  &--700 {
    border-color: var(--primary-gradient);
  }

  &--800 {
    border-color: var(--text-color);
  }
}

.app-borderside {
  &--h {
    border-style: solid;
    border-width: 0px 1px;
  }

  &--v {
    border-style: solid;
    border-width: 1px 0px;
  }

  &--a {
    border-style: solid;
    border-width: 1px;
    border-radius: 8px 8px 4px 4px;
    &-2x {
      border-style: solid;
      border-width: 1px;
      border-radius: 6px;
    }
  }

  &--l {
    border-style: solid;
    border-width: 0px 0px 0px 1px;
    border-radius: 8px;
  }

  &--r {
    border-style: solid;
    border-width: 0px 1px 0px 0px;
    border-radius: 2px;

    &-2x {
      border-style: solid;
      border-width: 0px 2px 0px 0px;
      border-radius: 4px;
    }

    &-shadow {
      border-style: solid;
      border-width: 0px 1px 0px 0px;
      -webkit-box-shadow: 10px 0px 13px -7px $shadow-color;
      -moz-box-shadow: 10px 0px 13px -7px $shadow-color;
      box-shadow: 10px 0px 13px -7px $shadow-color;
    }
  }

  &--t {
    border-style: solid;
    border-width: 1px 0px 0px 0px;

    &-2x {
      border-style: solid;
      border-width: 2px 0px 0px 0px;
    }
  }

  &--b {
    border-style: solid;
    border-width: 0px 0px 1px 0px;

    &-2x {
      border-style: solid;
      border-width: 0px 0px 2px 0px;
    }
  }
}

.view-more-btn {
  background-color: none;
  box-shadow: none !important;
  .v-btn {
    border-radius: $border-radius-Xlg;
  }
}

.app-theme__side-panel {
  background-color: var(--background-color-400);
}

.app-theme__center-content--body {
  background-color: var(--background-color-400);

  &--250 {
    background-color: var(--background-color-250);
  }
  .body-scrollable {
    &--85 {
      overflow-y: scroll;
      max-height: 85vh;
    }
    &--80 {
      overflow-y: scroll;
      max-height: 80vh;
    }
    &--75 {
      overflow-y: scroll;
      max-height: 75vh;
    }
    &--70 {
      overflow-y: scroll;
      max-height: 70vh;
    }

    &--65 {
      overflow-y: scroll;
      max-height: 65vh;
    }

    &--60 {
      overflow-y: scroll;
      max-height: 60vh;
    }
  }
  .body-min-height {
    &--80 {
      min-height: 80vh;
    }

    &--70 {
      min-height: 70vh;
    }

    &--65 {
      min-height: 65vh;
    }

    &--60 {
      min-height: 60vh;
    }
  }
}

.app-theme__center-content--side-bar {
  background-color: var(--background-color-200);
  border-right: 1px solid var(--border-color);
}

.app-theme__center-content--header {
  span {
    color: var(--text-color) !important;
  }
  background-color: var(--background-color-200);
  border-bottom: 1px solid $translucent;
  padding: 6px 8px;
  font-size: $font-size-medium;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;

  &.header-border-highlight {
    color: var(--text-color);

    &.outside-hire {
      border-top: 4px solid $warning;

      .app-theme__center-content--closebutton {
        background-color: var(--background-color-200);
      }
    }

    &.untracked {
      background-color: $swatch;

      .app-theme__center-content--closebutton {
        background-color: var(--background-color-200);
      }
    }
  }

  .app-theme__center-content--settings-icon {
    color: $border-color-alternate;

    &.active-state {
      color: $active-state;
    }
  }

  .app-theme__center-content--closebutton {
    // border-left: 1px solid var(--background-color-600);
    background-color: var(--background-color-200);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0px 20px;
    position: absolute;
    top: 0px;
    right: 0px;
    border-radius: $border-radius-base;

    &--icon {
      font-size: $font-size-16;
    }

    &:hover {
      background-color: var(--background-color-500);
      filter: brightness(130%);
      cursor: pointer;
    }
  }
}

.app-theme__bg {
  background-color: var(--background-color-200);
}

.app-theme__nav {
  background-color: var(--background-color-200);
}

.center-section__top-toolbar {
  height: 42px;
  padding: 0px 10px;
  border-style: solid;
  border-width: 0px 0px 1px 0px;
  z-index: 10;
  position: relative;
  background-color: var(--background-color-400);
  border-color: var(--background-color-600);

  -webkit-box-shadow: 0 10px 12px -10px $shadow-color;
  -moz-box-shadow: 0 10px 12px -10px $shadow-color;
  box-shadow: 0 10px 12px -10px $shadow-color;

  .center-section__top-toolbar--pop-down {
    width: 100%;
    position: absolute;
    top: 42px;
    left: 0px;
    z-index: 10;
    background-color: var(--background-color-300);
    border-style: solid;
    border-color: $enabled-color;
    border-width: 0px 0px 1px 0px;
  }
}

.no-highlight {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.recurring-job-id__highlight {
  font-style: italic;
}

.v-dialog {
  transition: 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.v-overlay::before {
  background-color: $bg-overlay;
}
.v-overlay--active::before {
  opacity: 0.9;
}

// Add as class to VUETIFY INPUT
.form-field-required label::after {
  content: '*';
  padding-left: 4px;
  font-weight: 700;
  color: red;
}

// Add as class to div/span
.form-field-required-marker::after {
  content: '*';
  padding-left: 4px;
  font-weight: 700;
  color: red;
}

// Label for input
.form-field-label-container {
  height: 48px;
}

.title--bold {
  font-family: $sub-font-family;
  font-size: $font-size-17;
  font-weight: 700;
}

.tags-chip {
  padding: 6px 12px;
  border-radius: 12px;
  background-color: $border-light;
  color: var(--text-color);
  font-weight: 700;
  transition: all 0.2s ease-in-out;
  border: 1px solid $border-light;

  .subheader {
    font-size: $font-size-11;
    font-weight: 300;
    color: var(--text-color);
  }

  &.highlighted {
    border: 1px solid $border-light-client;
  }

  &.selectable {
    &:hover {
      cursor: pointer;

      transform: scale(1.1);
      z-index: 10;
      border: 1px solid var(--group-chip-color);
    }

    &.selected {
      border: 1px solid var(--group-chip-color);
      background-color: var(--chip-color);
    }

    &.disabled {
      background-color: var(--background-color-600);
      pointer-events: none;
      color: var(--disabled-text-color);
    }

    // &.disabled {
    //   background-color: lighten($border-light, 8%);
    //   color: lighten($border-light-client, 25%);
    //   border: 1px dashed lighten($border-light, 15%);
    //   opacity: 0.6;
    //   pointer-events: none;
    //   cursor: not-allowed;
    //   transform: none !important;
    //   z-index: auto;
    // }
  }
}

.detail-item--vertical {
  padding: 8px 0px;

  &.light {
    .title-text {
      color: $enabled-color;
    }
    .value-text {
      color: black;
    }
  }
  .title-text {
    color: var(--light-text-color);
    text-transform: uppercase;
    font-size: $font-size-13;
    font-weight: 500;
    padding-bottom: 2px;
  }
  .value-text {
    font-weight: 400;
    color: var(--text-color);
    font-size: $font-size-15;
  }

  &.is-link {
    .title-text {
      padding-bottom: 6px;
    }
    .value-text {
      &:hover {
        cursor: pointer;
        background-color: $detail-item-value-bg-hover;
      }
      background-color: $detail-item-value-bg;
      border-radius: 3px;
      padding: 3px 12px;
      border: 1px solid var(--primary-light);
    }
  }
}

.helper-box {
  max-height: 300px;
  max-width: 300px;
  position: absolute;
  left: 0px;
  top: 0px;
  background-color: white;
  color: black;
  overflow-y: scroll;
  font-size: $font-size-10;
  padding: 6px;
  z-index: 10000000;
}

.helper-box-right {
  max-height: 300px;
  max-width: 300px;
  position: absolute;
  right: 0px;
  top: 0px;
  background-color: white;
  color: black;
  overflow-y: scroll;
  font-size: $font-size-10;
  padding: 6px;
  z-index: 10000000;
}

/* ========================================================================== */
/* SIDE MENU */
/* ========================================================================== */
.vertical-split-scroll__container {
  width: 100%;
  min-height: calc(100% - 48px);
  height: calc(100% - 48px);
  display: flex;
  flex-direction: column;

  .vertical-split-scroll__section {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;

    // max-height: auto;
    &.restricted-height {
      min-height: 30%;
      max-height: 30%;
    }

    &.minimized {
      flex-grow: 0;
      min-height: 0;
      max-height: auto;

      .vertical-split-scroll__body {
        display: none;
      }
    }
  }

  .vertical-split-scroll__header {
    width: 100%;
  }

  .vertical-split-scroll__body {
    flex-grow: 1;
    overflow: auto;
    min-height: 0;
    display: block;
  }
}

.view-details-button {
  min-height: 46px !important;
  border: 1px solid var(--primary-light);
  border-radius: $border-radius-sm;
  background-color: var(--primary) !important;
  margin: 1px;
  color: $primary-text-color !important;
  &:hover {
    cursor: pointer;
    transition: 0.3s;
    background-color: var(--primary-dark) !important;
  }
}

.job-search {
  .search-button {
    min-height: 46px !important;
    border: 1px solid var(--primary-light);
    border-radius: $border-radius-base;
    background-color: var(--primary) !important;
    color: white;
    margin-bottom: 6px;
    margin-left: 28px;
    &:hover {
      cursor: pointer;
      transition: 0.3s;
      box-shadow: var(--box-shadow);
    }
  }
}

/* ========================================================================== */
/* ACCOUNTING SCROLLING TABLE */
/* ========================================================================== */

.accounting-data-table {
  .inner-table > .v-table__overflow {
    overflow-y: auto;
    border: 0px;
    border-radius: $border-radius-base !important;
  }

  .inner-table > .v-table__overflow > .theme--dark.v-table {
    background-color: var(--table-bg-100) !important;
  }

  .inner-table > .v-table__overflow > .v-datatable {
    position: relative;

    & > thead {
      color: var(--text-color);
      background-color: var(--table-bg-100);
      border-top: 2px solid var(--border-color);

      & > tr {
        color: var(--text-color);
        height: 30px;
      }
    }

    thead,
    tbody tr {
      color: var(--text-color);
      display: table;
      width: 100%;
      table-layout: fixed;
      background-color: var(--table-bg-100);
    }

    & > tbody {
      display: block;
      overflow-y: scroll;
      overscroll-behavior-y: contain;
      min-height: 40vh !important;
      max-height: 40vh !important;
    }
  }

  .v-table__overflow {
    overflow-y: auto;
    position: relative;
    border-radius: $border-radius-sm !important;
    border: 1px solid var(--border-color);

    $cell-padding: 4px;

    & > .theme--dark.v-table {
      background-color: var(--background-color-250);
    }

    & > .v-datatable {
      &.v-datatable--select-all > thead > tr > th:first-child {
        width: 6%;
      }

      & > thead {
        background-color: var(--background-color-200);
        -webkit-box-shadow: $box-shadow-lg;
        -moz-box-shadow: $box-shadow-lg;
        box-shadow: $box-shadow-lg;

        & > tr {
          text-transform: uppercase;

          // font-weight: 600;
          & > td {
            font-weight: 700 !important;
            color: var(--text-color);
          }
        }
      }

      thead,
      tbody tr {
        display: table;
        width: 100%;
        table-layout: fixed;
      }

      & > thead {
        & > tr {
          & > th {
            color: var(--text-color);
            padding: 0px $cell-padding;
            white-space: normal;

            &:first-child {
              padding: 0 $cell-padding 0 12px;
            }

            &:last-child {
              padding: 0 12px 0 $cell-padding;
            }
          }
        }
      }

      & > tbody {
        display: block;
        overflow-y: scroll !important;
        overscroll-behavior-y: contain;
        // height removes nav bar, navigation tabs, actions btn bar, table header
        min-height: calc(80vh - 226px);
        max-height: calc(80vh - 226px);

        & > tr {
          color: var(--text-color);
          background-color: var(--background-color-300);
          border-bottom: $border-dark;

          & > td {
            color: var(--text-color);
            padding: 0 $cell-padding;

            &:first-child {
              padding: 0 $cell-padding 0 12px;
            }

            &:last-child {
              padding: 0 12px 0 $cell-padding;
            }
          }

          &:hover {
            background-color: var(--background-color-600);
          }

          & > .inner-table__cell {
            padding: 0px 12px;
            height: 36px;

            & > .segmented-cell {
              padding: 0px 4px;

              background-color: transparent !important;
              border-color: transparent !important;

              &:hover {
                pointer-events: none;
              }

              td {
                text-align: center;
                border-right: 1px solid $border-dark;

                &:last-child {
                  border-right-width: 0px;
                }
              }
            }
          }
        }
      }
    }
  }
}

// Define a mixin for common styles
@mixin setDataTableStyles(
  $height,
  $viewHeight: 100vh,
  $hasPagination: false,
  $scrollable: true
) {
  .v-table__overflow {
    border-radius: 10px !important;
    @if $hasPagination {
      // border-radius: 3px 3px 0px 0px !important;
      // border-bottom: 0px !important;
    }
    & > .v-datatable {
      border-radius: $border-radius-base !important;
      & > tbody {
        @if $scrollable {
          display: block;
          overflow-y: scroll !important;
          overscroll-behavior-y: contain;
          min-height: calc(#{$viewHeight} - #{$height}) !important;
          max-height: calc(#{$viewHeight} - #{$height}) !important;
        }
      }
    }
  }
}

// Apply the mixin for each specific case
.company-user-data-table,
.recurring-job-scheduler-maintenance,
.recurring-job-list {
  @include setDataTableStyles(40px + 42px + 76px + 8px + 56px + 16px);
}

.accounting-ledger-search-table {
  @include setDataTableStyles(40px + 135px + 48px + 56px + 50px + 15px);
}
.sms-message-history {
  @include setDataTableStyles(0px, 100vh, true, false);
}
.email-message-history {
  @include setDataTableStyles(0px, 100vh, true, false);
}
.safety-checklist-history {
  @include setDataTableStyles(0px, 100vh, true, false);
}
.sales-management {
  @include setDataTableStyles(39px + 42px + 90px + 32px + 56px + 64px + 27px);
}
.national-client-id-maintenance {
  @include setDataTableStyles(39px + 42px + 90px + 32px + 42px);
}
.import-requires-attention-container {
  @include setDataTableStyles(82px + 56px + 58px, 90vh);
}
.point-manager-table {
  @include setDataTableStyles(34px + 48px + 87px + 48px + 56px + 59px, 90vh);
}
.invoice-search-dialog {
  .accounting-ledger-search-table {
    @include setDataTableStyles(40px + 135px + 48px + 56px + 50px);
  }
}
#job-search {
  @include setDataTableStyles(
    40px + 24px + 135px + 56px + 56px + 26px,
    100vh,
    true
  );
}
#client-job-search {
  @include setDataTableStyles(
    40px + 24px + 135px + 56px + 56px + 26px,
    100vh,
    true,
    false
  );
}
// when the search is by client we show extra inputs. Because of this we remove some of the height from the table
#job-search-small {
  @include setDataTableStyles(
    40px + 24px + 135px + 56px + 56px + 24px + 81px,
    100vh,
    true
  );
}

.hire_contract_summary_list_container {
  @include setDataTableStyles(240px + 49px, 90vh, true);
}

.accounting-job-table-inner {
  @include setDataTableStyles(378px);
}

.ledger-table {
  @include setDataTableStyles(40px + 57px + 50px + 51px + 40px + 8px + 40px);
}

.accounting-index-page {
  .v-input {
    &.v-text-field.v-text-field--box.v-text-field--enclosed.theme--dark {
      & > .v-input__control {
        & > .v-input__slot {
          min-height: auto;
          max-height: 44px;

          .v-label {
            font-size: $font-size-14;
          }
        }

        & > .v-text-field__details {
          display: none;
        }
      }

      .v-label--active {
        transform: translateY(-12px) scale(0.75);
      }
    }
  }

  .v-input--checkbox
    > .v-input__control
    > .v-input__slot
    > .v-input--selection-controls__input
    > input {
    z-index: 2;
  }
}

.v-image {
  border-radius: $border-radius-sm;
}

.v-input__slot {
  color: $highlight !important;
  border-radius: $border-radius-sm !important;
}

.v-menu__content.menuable__content__active.v-autocomplete__content {
  border-radius: $border-radius-base;
  background-color: var(--background-color-300);
  & > .v-select-list.v-card > .v-list {
    .v-list__tile {
      font-size: $font-size-15;
      height: 42px;
    }
  }
}

// JOB LIST PULL OUT WINDOW STYLES
.job-list-container table.v-table tbody td {
  height: 25px !important;
}

.job-list-container table.v-table tbody tr {
  transition: none !important;
}

.job-list-container table.v-table tbody td,
table.v-table tbody td {
  font-size: $font-size-11;

  padding: 0 8px;

  &:first-child {
    padding-left: 8px;
  }
}

.job-list-container table.v-table tbody .table-row__highlight td {
  color: $error;
  font-weight: 600;
}

.job-list-container .v-datatable__expand-row .v-datatable__expand-col {
  height: 0px !important;
}

.job-list-container .serviceTypeJobs table.v-table thead {
  height: 0px !important;
  max-height: 0px !important;
  min-height: 0px !important;
  font-size: 0px !important;
  overflow: hidden;
  margin: 0 !important;
  visibility: hidden;
}

.job-list-container .serviceTypeJobs table.v-table thead tr {
  height: 0px !important;
  max-height: 0px !important;
  font-size: 0px !important;
  overflow: hidden;
  margin: 0 !important;
  visibility: hidden;

  .column .sortable {
    height: 0px !important;
  }

  &:first-child {
    border: 0px;
  }
}

.job-list-container .serviceTypeJobs table.v-table thead th {
  height: 0px !important;
  max-height: 0px !important;
  min-height: 0px !important;
  font-size: 0px !important;
  margin: 0 !important;

  .v-icon {
    visibility: hidden;
    display: none;
  }
}

// .job-list-container table.v-table tbody tr.table-row__expanded {
//   td {
//     padding: 6px 0px;
//     vertical-align: top;
//   }
// }

.job-list-container .headers-only-table table.v-table tbody {
  visibility: hidden !important;
  display: none !important;
}

.job-list-container .headers-only-table table.v-table thead tr {
  color: red;
}

.headers-only-table table.v-table thead tr {
  height: 30px !important;
}

.headers-only-table table.v-table thead tr.v-datatable__progress {
  height: 1px !important;
}

.serviceTypeJobs .v-table__overflow .theme--dark.v-table {
  background-color: var(--table-bg-100);
}

.serviceTypeJobs .v-table__overflow .v-datatable__expand-content {
  background-color: var(--table-bg-100);
}

.serviceTypeJobs.side-panel-type .v-table__overflow .theme--dark.v-table {
  background-color: var(--table-bg-100);
}

.serviceTypeJobs.side-panel-type
  .v-table__overflow
  .v-datatable__expand-content {
  background-color: var(--table-bg-100);
}

.headers-only-table > .v-table__overflow > .theme--dark.v-table {
  background-color: var(--table-bg-100);
}

.common-address-table {
  width: 100%;
  cursor: pointer;
  .selected {
    border-left: 3px solid var(--accent) !important;
  }

  .v-table thead {
    border-radius: 20px;
    background-color: var(--background-color-300) !important;
  }

  .v-table {
    border-radius: 10px;
    background-color: var(--background-color-400) !important;
  }

  .v-datatable__actions {
    margin-top: 6px;
    border-radius: 10px;
    border: 0.5px solid var(--border-color);
    background-color: transparent !important;
  }

  .v-table tbody tr {
    &:hover {
      background-color: var(--info) !important;
    }
    &:active {
      background: var(--background-color-200);
    }
  }

  &.bordered {
    .v-table__overflow {
      border: 1px solid var(--border-color);
    }
  }

  &.dense {
    table.v-table tbody td {
      height: 36px;
    }
  }

  &.pricing {
    table.v-table thead th {
      border-radius: 20px;
      font-size: 18px;
    }
    table.v-table tbody td {
      font-size: 16px;
      font-weight: 600;
    }
    .v-table thead {
      background-color: var(--background-color-200);
    }

    .v-table tbody tr {
      color: var(--light-text-color);
    }
    .selected-icon {
      position: relative;
      margin-left: 20px;
      color: var(--accent) !important;
    }
  }
}

.gd-dark-theme {
  border-radius: 10px;

  &.bordered {
    .v-table__overflow {
      border-radius: 10px;
      border: 1px solid var(--border-color);
    }
  }

  .selected {
    border-left: 3px solid var(--success) !important;
  }

  .v-table thead {
    color: var(--text-color);
    background-color: var(--background-color-300) !important;
  }

  .v-table th {
    color: var(--text-color) !important;
  }

  .v-table {
    background-color: var(--background-color-400) !important;
  }

  .v-datatable__actions {
    color: var(--light-text-color) !important;
    margin-top: 6px;
    border-radius: 10px;
    border: 0.5px solid var(--border-color);
    background-color: transparent !important;

    .v-select__selections {
      color: var(--light-text-color) !important;
    }
    .v-input__append-inner .v-icon {
      color: var(--text-color) !important;
    }

    .v-datatable__actions__range-controls {
      color: var(--text-color) !important;
      .v-icon {
        color: var(--text-color) !important;
      }
    }
  }

  .v-table tbody tr {
    color: var(--text-color);
    &:hover {
      background-color: var(--background-color-500) !important;
    }
    &:active {
      background: var(--background-color-200);
    }
  }

  &.dense {
    table.v-table tbody td {
      height: 36px;
    }
  }

  &.pricing {
    table.v-table thead th {
      border-radius: 20px;
      font-size: 18px;
    }
    table.v-table tbody td {
      font-size: 16px;
      font-weight: 600;
    }
    .v-table thead {
      background-color: var(--background-color-200);
    }

    .v-table tbody tr {
      color: var(--light-text-color);
    }
    .selected-icon {
      position: relative;
      margin-left: 20px;
      color: var(--accent) !important;
    }
  }

  &.fullWidth {
    width: 100%;
  }
}
.gd-data-table__dense {
  table.v-table tbody td {
    height: 26px !important;
  }

  table.v-table tfoot tr td {
    height: 26px !important;
  }
}

/* ========================================================================== */
/* Checkboxes */
/* ========================================================================== */

.custom-input {
  input[type='checkbox'],
  input[type='radio'] {
    --active: $highlight-dark;
    --active-inner: var(--text-color);
    --focus: 2px $translucent-highlight;
    --border: $custom-input-border;
    --border-hover: $highlight-dark;
    --background: var(--text-color);
    --disabled: var(--light-text-color);
    --disabled-inner: var(--bg-light);
    -webkit-appearance: none;
    -moz-appearance: none;
    height: 14px;
    outline: none;
    display: inline-block;
    vertical-align: top;
    position: relative;
    margin: 0;
    cursor: pointer;
    border: 1px solid var(--bc, var(--border));
    background: var(--b, var(--background));
    transition:
      background 0.3s,
      border-color 0.3s,
      box-shadow 0.2s;

    &:after {
      content: '';
      display: block;
      left: 0;
      top: 0;
      position: absolute;
      transition:
        transform var(--d-t, 0.3s) var(--d-t-e, ease),
        opacity var(--d-o, 0.2s);
    }

    &:checked {
      --b: var(--active);
      --bc: var(--active);
      --d-o: 0.3s;
      --d-t: 0.6s;
      --d-t-e: cubic-bezier(0.2, 0.85, 0.32, 1.2);
    }

    &:disabled {
      --b: var(--disabled);
      cursor: not-allowed;
      opacity: 0.9;

      &:checked {
        --b: var(--disabled-inner);
        --bc: var(--border);
      }

      & + label {
        cursor: not-allowed;
      }
    }

    &:hover {
      &:not(:checked) {
        &:not(:disabled) {
          --bc: var(--border-hover);
        }
      }
    }

    &:focus {
      box-shadow: 0 0 0 var(--focus);
    }

    &:not(.switch) {
      width: 14px;

      &:after {
        opacity: var(--o, 0);
      }

      &:checked {
        --o: 1;
      }
    }

    & + label {
      font-size: $font-size-10;
      line-height: 18px;
      display: inline-block;
      vertical-align: top;
      cursor: pointer;
      margin-left: 6px;
      color: $custom-input-label;
    }
  }

  input[type='checkbox'] {
    &:not(.switch) {
      border-radius: 4px;

      &:after {
        width: 5px;
        height: 9px;
        border: 2px solid var(--active-inner);
        border-top: 0;
        border-left: 0;
        left: 4px;
        top: 1px;
        transform: rotate(var(--r, 20deg));
      }

      &:checked {
        --r: 43deg;
      }
    }

    &.switch {
      width: 38px;
      border-radius: 11px;

      &:after {
        left: 2px;
        top: 2px;
        border-radius: 50%;
        width: 15px;
        height: 15px;
        background: var(--ab, var(--border));
        transform: translateX(var(--x, 0));
      }

      &:checked {
        --ab: var(--active-inner);
        --x: 17px;
      }

      &:disabled {
        &:not(:checked) {
          &:after {
            opacity: 0.6;
          }
        }
      }
    }
  }

  input[type='radio'] {
    border-radius: 50%;

    &:after {
      width: 19px;
      height: 19px;
      border-radius: 50%;
      background: var(--active-inner);
      opacity: 0;
      transform: scale(var(--s, 0.7));
    }

    &:checked {
      --s: 0.5;
    }
  }
}

.rate-hover__container {
  width: 300px;
  background-color: var(--background-color-300);

  max-height: 300px;
  overflow-y: scroll;

  .lineitem__label {
    font-size: $font-size-12;
    text-transform: uppercase;
    font-weight: 600;
    color: var(--border-color);
  }

  .lineitem__value {
    font-size: $font-size-14;
    font-weight: 400;
  }
}

/* ========================================================================== */
/* V-SWITCH No Padding */
/* ========================================================================== */

.v-switch__remove-padding {
  &.v-input--selection-controls {
    margin-top: 0;
    padding-top: 0;
  }
}

/* ========================================================================== */
/* Vuetify Solo V-Select */
/* ========================================================================== */
.v-select-solo__dense {
  &.v-text-field.v-text-field--solo {
    .v-input__control {
      min-height: 34px;

      .v-label {
        font-size: $font-size-14;
      }
      text-transform: uppercase;
      font-weight: 500;
    }
  }
}

.v-textfield-solo__dense {
  &.v-text-field.v-text-field--solo {
    .v-input__control {
      min-height: 34px;

      font-size: $font-size-14;
      text-align: right;
      font-weight: 600;
    }
  }
}

.service-rate-table-time,
.zone {
  .theme--dark.v-text-field > .v-input__control {
    > .v-input__slot:before {
      border: 0.5px solid $info;
    }
    i {
      color: $accent;
    }
  }
  .v-input__control > .v-input__slot:hover:before {
    border-color: $accent !important;
  }
}

.v-tooltip__small-text {
  font-size: $font-size-10;
}

.v-tooltip__content {
  box-shadow: 0px 8px 32px var(--shadow-color) !important;
  padding: 8px !important;
  margin: 4px !important;
  background-image: linear-gradient(#3c3c50 0%, #2a2b38 100%);
  background-color: var(--background-color-200);
  backdrop-filter: blur(3px);
  border-radius: 8px !important;
  font-size: $font-size-14;
  font-weight: 400;
  // text-transform: capitalize !important;
  opacity: 1 !important;
  border: 1px solid $translucent;
}

.badge-custom {
  .v-badge__badge {
    font-size: $font-size-13;
    top: -5px;
    right: -18px;
    height: 18px;
    width: 18px;
    font-weight: 700;
  }
}

.v-alert {
  border-radius: $border-radius-base;
  font-weight: 500;
  box-shadow: var(--box-shadow);
  .title {
    font-family: $sub-font-family;
    font-size: $font-size-14;
    font-weight: 600;
  }
  .v-icon {
    border-radius: 20px;
    size: 22px;
  }
  &.warning {
    color: $toast-warning-text;
    border-top: 4px solid $warning-type !important;

    background-color: $bg-light !important;
    .title {
      color: $warning-type;
    }
    .v-icon {
      color: $warning-type;
      border: 2px solid $warning-type;
      padding: 2px;
    }
  }
  &.success {
    color: $toast-success-text;
    border-top: 4px solid $toast-success-bg !important;
    background-color: $bg-light-green !important;
    .title {
      color: $toast-success-bg;
    }
    .v-icon {
      color: $toast-success-bg;
      border-color: $toast-success-bg;
    }
  }
  &.error {
    color: $toast-error-text;
    border-top: 4px solid $error-type !important;

    background-color: $bg-light-red !important;
    .title {
      color: $error-type;
    }
    .v-icon {
      color: $error-type;
    }
  }
  &.info {
    color: $toast-info-text;
    border-top: 4px solid $info !important;

    background-color: $bg-light-blue !important;
    .title {
      color: $info;
    }
    .subheader {
      font-weight: 600;
      font-size: $font-size-large;
      color: $info;
    }
    .v-icon {
      color: $info;
    }
  }
}

.v-alert-custom {
  color: var(--background-color-400);
  border-radius: $border-radius-base;
  box-shadow: var(--box-shadow);
  .title {
    font-family: $sub-font-family;
    font-size: $font-size-14;
    font-weight: 600;
  }

  &.warning {
    border: 1px solid $warning-type;
    background-color: $bg-light;
    .title {
      color: $toast-warning-bg;
    }
    .v-icon {
      color: $toast-warning-bg;
    }
  }
}

.v-dialog-custom {
  border-radius: $border-radius-base;
  border: 2px solid $translucent;

  .confirmation-dialog-label {
    &:hover {
      cursor: pointer;
    }
  }

  &.client-portal {
    background-color: var(--background-color-500);
    .app-theme__center-content--body {
      background-color: var(--background-color-200);
    }

    .app-theme__center-content--header {
      /* Inverted Colors */
      color: var(--text-color) !important;
      background-color: var(--background-color-500);
      border: 1px solid $client-theme-border;
      padding: 6px 8px;
      font-size: $font-size-medium;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      position: relative;

      /* Nested Styles */
      .app-theme__center-content--closebutton {
        border-left: 1px solid $client-theme-border;
        background-color: var(--background-color-500);
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 0px 26px;
        position: absolute;
        top: 0px;
        right: 0px;

        /* Nested Styles */
        &--icon {
          font-size: $font-size-16;
        }

        &:hover {
          background-color: $border-light-client;
          filter: brightness(80%); /* Darken on hover */
          cursor: pointer;
        }
      }
    }
  }
}
.v-dialog-custom-warning {
  border: 1px solid $error;
  border-radius: $border-radius-base;

  .confirmation-dialog-label {
    &:hover {
      cursor: pointer;
    }
  }
}

.v-list-custom {
  &.appbar {
    &.theme--dark.v-list {
      color: var(--text-color);
      border-radius: 12px;
      background-color: var(--background-color-100);
      border: 1px solid var(--background-color-600);
    }
  }
  &.theme--dark.v-list {
    color: var(--text-color);
    border-radius: 12px;
    background-color: var(--background-color-400);
    border: 1px solid var(--border-color);
    .v-list--disabled {
      color: var(--disabled-text-color) !important;
    }
  }
}

.v-list-tile-avatar__custom {
  &.v-list__tile__avatar {
    min-width: 26px !important;
  }
}

.v-date-picker-custom {
  &.v-picker {
    .v-date-picker-table {
      background-color: var(--background-color-300);
    }

    .v-date-picker-header,
    .v-date-picker-header * {
      background-color: var(--background-color-300);
      color: var(--text-color) !important;
    }
  }
}

.date-filter-container .v-text-field__slot input {
  color: var(--text-color) !important;
}

.v-date-picker-table.theme--dark .v-btn.v-btn--active .v-btn__content {
  color: black !important;
}

.v-date-picker-table.theme--dark .v-btn__content {
  color: var(--text-color) !important;
}

.v-expansion-panel-custom {
  .v-expansion-panel__header {
    padding: 6px 16px;
    min-height: 36px;
    font-size: $font-size-13;
  }
}

.v-expansion-panel__container.expansion-panel.v-expansion-panel__container--active {
  transition: 0s;
}
.v-expansion-panel__body {
  transition: 0s;
}

.expansion-panel {
  height: fit-content;
  .v-expansion-panel__header {
    height: 1px;
  }
}

// FLEET ASSET LIST ICONS
.indicator-icon-container {
  // border-radius: 1px;
  // margin-right: 3px;
  // border-radius: 0 3px;
  // width: 6px;
  // height: 12px;
  width: 0px;
  height: 0px;
  border-style: solid;
  border-width: 0 6px 10px 6px;
  border-color: transparent;
  transform: rotate(0deg);
  margin-right: 4px;
  margin-left: 2px;

  &.fleet-availability {
    &-none {
      // background-color: $drop;
      border-color: transparent transparent $busy-color transparent;
      transform: rotate(180deg);
    }

    &-partial {
      // border: 1px solid var(--primary);
      border-color: transparent transparent $warning transparent;
      transform: rotate(90deg);
    }

    &-full {
      // background-color: $pickup-highlight;
      border-color: transparent transparent $accent-secondary transparent;
    }
  }
}

.icon-hover--primary {
  color: var(--light-text-color) !important;

  &:hover {
    color: var(--accent-secondary) !important;
  }
}

.accent-text--primary {
  color: var(--highlight) !important;
}

.accent-text--card {
  padding: 1px 6px !important;
  font-weight: 600;
  background-color: $highlight-dark;
  border-radius: $border-radius-base;
  letter-spacing: 0.03em;
  font-family: $font-family;

  &.success-type {
    background-color: $success-type !important;
  }

  &.success-type-outline {
    background-color: transparent !important;
    border: 1px solid $success-type;
  }

  &.warning-type {
    background-color: $warning-type !important;
  }

  &.warning-type-outline {
    background-color: transparent;
    border: 1px solid $warning-type !important;
    color: $warning;
    border-radius: 2px;
  }

  &.error-type {
    background-color: $error-type !important;
  }

  &.outline-type {
    border: 1px solid $outline-type;
    background: none;
  }

  &.untracked-job-type {
    background-color: $untracked-job-type !important;
  }
}

.accent-text-button {
  color: $highlight;
  border-radius: 3px;
  &:hover {
    cursor: pointer;
    color: var(--primary-light);
    background-color: $highlight-text-button;
  }
}
.accent-subtext-button {
  font-size: $font-size-11;
  color: var(--primary-light);
  &:hover {
    cursor: pointer;
    color: $highlight-dark;
    background-color: $highlight-text-button;
  }
}

.v-datatable__expand-content {
  transition: unset !important;
}

.mapbox-popup-text {
  text-align: left;
  color: black;
  .mapboxgl-popup-content {
    text-align: left;
  }
  .mapboxgl-popup-close-button {
    font-size: $font-size-16;
    font-weight: 600;
    padding: 0px 4px;
  }
}

.accent-brightness--highlight {
  filter: brightness(110%);
}

.job-map-route--marker-circle {
  background-color: var(--primary);
  border: 2px solid var(--text-color);
  outline: 2px solid black;
  border-radius: 12px;
  padding: 6px;
}

.job-map-route--marker {
  $dark-outline: var(--background-color-400);
  // position: relative;
  text-align: center;
  border: 2px solid var(--text-color);
  outline: 1px solid $dark-outline;
  border-radius: 10px;
  padding: 1px 8px;
  font-weight: 700;
  color: var(--text-color);

  &:hover {
    cursor: pointer;
  }

  &__tail-1 {
    position: absolute;
    bottom: -20px;
    left: 50%;
    top: calc(100% + 3px);
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-color: $dark-outline transparent transparent transparent;
    border-width: 6px;
    border-style: solid;
    z-index: -1;
  }
}

/* ERROR INPUTS */
.v-input.error--text {
  animation: wiggle 0.4s ease-in-out;
}
.v-input.error--text .v-label {
  color: var(--error-color) !important;
}
.v-input.error--text .v-messages__message {
  color: var(--error-color) !important;
}

.v-input__slot::after {
  border-color: $primary !important;

  &.v-input--is-focused {
    .v-input__control .v-input__slot {
      border: 1px solid $primary;
    }
  }
}

.v-input.v-input--checkbox,
.v-input.v-input--selection-controls {
  // Text label color
  .v-label {
    color: var(--text-color) !important;
  }

  // Checkbox icon color (e.g., check_box or check_box_outline_blank)
  .v-icon {
    color: var(--accent) !important;
  }

  // Input ripple (for ripple effect)
  .v-input--selection-controls__ripple {
    background-color: transparent !important;
    color: var(--accent) !important;
  }

  // Apply to input if needed (caret color for accessibility)
  input[type='checkbox'] {
    accent-color: var(--accent);
  }

  .v-messages__message {
    color: var(--light-text-color);
  }

  // disabled colors
  &.v-input--is-disabled,
  &.disabled {
    .v-label,
    .v-icon,
    .v-input--selection-controls__ripple {
      color: var(--disabled-text-color) !important;
      opacity: 1 !important;
    }

    input[type='checkbox'] {
      accent-color: var(--disabled-text-color);
    }
  }
}

.v-solo-custom {
  .v-messages__message {
    color: var(--light-text-color);
  }
  .v-select__selection.v-select__selection--comma.v-select__selection--disabled {
    color: var(--disabled-text-color) !important;
    opacity: 1 !important; /* prevent Vuetify dimming */
  }
  // Common input text and caret color
  .v-input__slot,
  .v-input__control,
  input,
  textarea,
  .v-select__selections {
    color: var(--text-color) !important;
    caret-color: var(--text-color) !important;
  }

  .v-text-field__prefix {
    color: $accent !important;
  }
  .v-text-field__suffix {
    color: $accent !important;
  }

  // Label color
  .v-label {
    color: grey !important;
  }

  // Placeholder
  ::placeholder {
    color: var(--disabled-text-color) !important;
  }

  // Dropdown icons and selection chips
  .v-input__append-inner .v-icon,
  .v-input__append-outer .v-icon,
  .v-icon {
    color: var(--text-color) !important;
  }

  .v-chip {
    color: var(--text-color) !important;
    background-color: var(--chip-color) !important;

    &.v-chip--disabled,
    &[aria-disabled='true'] {
      color: var(--disabled-text-color) !important;
      background-color: var(--background-color-600) !important;
    }
  }

  // Dropdown list text
  .v-list-item,
  .v-list-item__title {
    color: var(--text-color) !important;
  }

  .v-list-item--active,
  .v-list-item--active .v-list-item__title {
    background-color: var(--background-color-400) !important;
    color: var(--text-color) !important;
  }

  // Required asterisk
  &.required .v-label::after {
    content: ' *';
    color: var(--error-color) !important;
  }

  // Focused state
  &.v-input--is-focused {
    .v-input__control .v-input__slot {
      border: 1px solid var(--primary) !important;

      .v-label,
      .v-icon {
        color: var(--primary-light) !important;
      }
    }
  }

  // Solo Flat Variant
  &.v-text-field--solo-flat {
    .v-input__control .v-input__slot {
      background-color: var(--background-color-300);
      border: 1px solid var(--border-color);
    }

    &.v-input--is-focused .v-input__slot {
      border: 1px solid var(--primary);
    }

    &.error--text .v-input__control .v-input__slot {
      border: 1px solid var(--error);
    }
    &.v-input--is-focused.error--text .v-input__slot {
      border: 1px solid $error !important;
    }

    &.v-input--is-readonly .v-input__slot,
    &.v-input--is-disabled .v-input__slot,
    &.solo-input-disable-display .v-input__slot {
      color: var(--disabled-text-color) !important;
      background-color: var(--background-color-550) !important;
      border: none !important;
    }
  }

  // Outline Variant
  &.v-text-field--outline {
    .v-input__control .v-input__slot {
      background-color: var(--background-color-300) !important;
      border: 1px solid var(--border-color) !important;

      &:hover {
        border: 1px solid var(--primary) !important;
      }
    }

    &.v-input--is-focused .v-input__control .v-input__slot {
      border: 1px solid var(--primary) !important;
    }

    &.v-input--is-focused .v-label,
    &.v-input--is-focused .v-icon {
      color: var(--primary-light-color) !important;
    }

    &.v-input--is-readonly .v-input__slot,
    &.v-input--is-disabled .v-input__slot,
    &.solo-input-disable-display .v-input__slot {
      color: var(--disabled-text-color) !important;
      background-color: var(--background-color-550) !important;
      border: none !important;
    }
  }

  &.v-text-field--box {
    ::before {
      border-color: var(--light-text-color) !important;
    }
    .v-input__slot {
      background-color: var(--background-color-300) !important;
      border: none !important;
    }
    &:hover {
      ::before {
        border-color: var(--primary) !important;
      }
    }
    // Focused state
    &.v-input--is-focused {
      .v-input__control .v-input__slot {
        border: none !important;
      }
      .v-label,
      .v-icon {
        color: var(--primary) !important;
      }
    }
  }

  // Disabled or readonly states
  &.v-input--is-disabled,
  &.v-input--is-readonly {
    .v-input__slot,
    .v-input__control,
    .v-select__selections,
    .v-label,
    .v-icon,
    input,
    textarea {
      color: var(--disabled-text-color) !important;
      opacity: 1 !important;
    }

    ::placeholder {
      color: var(--disabled-text-color) !important;
      opacity: 0.8 !important;
    }

    .v-text-field__prefix {
      color: var(--disabled-text-color) !important;
    }
    .v-text-field__suffix {
      color: var(--disabled-text-color) !important;
    }
  }

  &.v-solo-custom--compact {
    max-height: 36px;
    .v-input__control {
      min-height: 32px;
      .v-input__slot {
        min-height: 32px;
        max-height: 38px;
        padding-top: 3px;
        padding-bottom: 3px;
      }
      .v-label {
        font-size: $font-size-12;
        line-height: 1;
      }
      .v-input__append-inner,
      .v-input__prepend-inner {
        // height: 32px;
        // min-height: 32px;
        .v-icon {
          font-size: $font-size-18;
        }
      }
    }
    .v-input__slot,
    .v-text-field__slot {
      min-height: 32px;
      max-height: 38px;
      font-size: $font-size-13;
      padding-top: 3px;
      padding-bottom: 3px;
    }
    .v-label {
      padding-top: 4px;
      font-size: $font-size-12;
    }
    .v-chip {
      height: 24px;
      font-size: $font-size-12;
      padding: 0 6px;
    }
    .v-messages {
      position: absolute;
      top: 44px;
      z-index: 100;
      // padding: 8px;
    }
  }
}

.theme--dark.v-list {
  border-radius: $border-radius-sm !important;
  background: var(--background-color-300);
  color: var(--text-color);
}
.theme--dark.v-stepper {
  background-color: var(--background-color-250);
  box-shadow: none;

  .v-stepper__header {
  }

  .v-stepper__step {
    border-radius: 5px;
    &.v-stepper__step--active {
      background: var(--background-color-600);
    }
    &.v-stepper__step--editable:hover {
      background: $translucent;
    }
  }
}

.table-header-custom {
  padding: 8px;
  font-size: $font-size-12;
  text-transform: uppercase;

  .pud-status-list-item__header {
    color: $highlight;
    text-transform: uppercase;
    font-size: $font-size-12;
    font-weight: 600;

    &.header-type {
      font-size: $font-size-13;
      font-weight: 700;
      color: $highlight-dark;
    }
  }
}

.table-row-custom {
  padding-bottom: 4px;
  font-size: $font-size-12;
  text-transform: uppercase;
  color: var(--text-color);

  .table-row-custom__header {
    color: $highlight;
    text-transform: uppercase;
    font-size: $font-size-12;
    font-weight: 600;

    &.header-type {
      font-size: $font-size-13;
      font-weight: 700;
      color: $highlight-dark;
    }
  }

  .table-row-cell {
    &.left-align {
      text-align: left;
    }

    &.right-align {
      text-align: right;
    }
  }
}

.v-btn {
  border-radius: $border-radius-btn;
  color: white;
}
.v-btn-confirm-custom {
  font-weight: 700;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba($translucent, 0.7);
  }
  70% {
    box-shadow: 0 0 0 14px rgba($summary-bg, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba($translucent, 0);
  }
}

.subcontractor-table {
  .v-table__overflow {
    border-top-left-radius: 0px !important;
    border-top-right-radius: 0px !important;
    border: 1px solid var(--background-color-600);

    & > .v-datatable {
      & > tbody {
        // height removes nav bar, navigation tabs, client driver bar, actions btn bar, table header

        min-height: calc(
          (
              100vh - 40px - 24px - 66px - 131px - 26px -
                ((33px + 56px + 49px) * 2)
            ) / 2
        ) !important;
        max-height: calc(
          (
              100vh - 40px - 24px - 66px - 131px - 26px -
                ((33px + 56px + 49px) * 2)
            ) / 2
        ) !important;

        // min-height: 207px !important;
        // max-height: 207px !important;
      }
    }
  }
}

.toast-notification-custom {
  border-radius: 8px;
  // Style of the notification itself
  &:hover {
    cursor: pointer;
  }

  &.job-type {
    padding: 10px;

    .notification-title {
      // Style for title line
      font-size: $font-size-22;
      font-weight: 700;
      text-align: center;
    }

    .notification-content {
      // Style for content
      font-size: $font-size-15;
      text-align: center;
    }
  }

  padding: 20px;
  margin: 0 5px 5px;

  font-size: $font-size-12;

  color: $primary-text-color;
  // background: $primary;
  // border-left: 6px solid $dark-text-color !important;
  border-left-width: 6px !important;
  border-left-style: solid !important;

  .notification-title {
    // Style for title line
    font-size: $font-size-16;
  }

  .notification-content {
    // Style for content
    font-size: $font-size-15;
  }

  &.warning {
    background-color: $toast-warning-bg !important;
    border-left-color: $toast-warning-border !important;
  }

  &.error {
    background: $toast-error-bg !important;
    border-left-color: $toast-error-border !important;
  }

  &.info {
    background-color: $toast-info-bg !important;
    border-left-color: $toast-info-border !important;
  }

  &.success {
    background: $toast-success-bg !important;
    border-left-color: $toast-success-border !important;
    // border-top-color: var(--text-color) !important;
  }
}
.default-table-dark {
  .inner-table > .v-table__overflow {
    overflow-y: auto;
  }

  .inner-table > .v-table__overflow > .v-table {
    background-color: var(--table-bg-100) !important;
  }

  .inner-table > .v-table__overflow > .v-datatable {
    position: relative;

    & > thead {
      background-color: var(--table-bg-100);
      border-top: 2px solid var(--border-color);
    }
  }

  .v-table__overflow {
    overflow-y: auto;
    position: relative;
    border: 1px solid $translucent;
    border-radius: $border-radius-base;

    $cell-padding: 2px;

    & > .v-table {
      border-radius: $border-radius-base;
    }

    & > .v-datatable {
      & > thead {
        & > tr {
          text-transform: uppercase;
          color: var(--text-color);

          & > td {
            font-weight: 700 !important;
            color: var(--text-color);
          }
        }
      }

      thead,
      tbody tr {
        display: table;
        width: 100%;
        table-layout: fixed;
      }

      & > thead {
        & > tr {
          & > th {
            padding: 0px $cell-padding;
            white-space: normal;

            &:first-child {
              padding: 0 $cell-padding 0 12px;
            }

            &:last-child {
              padding: 0 25px 0 $cell-padding;
            }
          }
        }
      }

      & > tbody {
        // display: block;
        overflow-y: scroll !important;
        overscroll-behavior-y: contain;
        max-height: 70vh;

        & > tr {
          background-color: var(--background-color-400);

          & > td {
            padding: 0 $cell-padding;

            &:first-child {
              padding: 0 $cell-padding 0 12px;
            }
          }

          & > .inner-table__cell {
            & > .segmented-cell {
              background-color: transparent !important;
              border-color: transparent !important;

              &:hover {
                pointer-events: none;
              }

              td {
                text-align: center;
                border-right: 1px solid $border-dark;

                &:last-child {
                  border-right-width: 0px;
                }
              }
            }
          }
        }
      }
    }
  }
  .inner-table__container {
    background-color: var(--background-color-500);
  }
}

.v-divider {
  background-color: var(--border-color) !important;
  opacity: 0.6;
}

.accounting-job-list-table {
  .v-table tbody tr {
    background-color: var(--background-color-400);

    &:hover {
      background-color: var(--table-bg-100) !important;
    }
  }

  // .job-list-checkbox-column-cell {
  //   width: 110px !important;
  //   padding-left: 12px !important;
  // }

  // .job-list-checkbox-column-header {
  //   width: 110px !important;
  // }

  // .job-list-date-column-header,
  // .job-list-date-column-cell {
  //   width: 140px !important;
  // }

  // .job-list-jobId-column-header,
  // .job-list-jobId-column-cell {
  //   width: 100px !important;
  // }

  .job-list-name-column-header,
  .job-list-name-column-cell {
    width: 200px !important;
  }

  // .job-list-viewJob-column-header {
  //   padding-right: 50px !important;
  // }

  // .job-list-service-column-header {
  //   width: 100px !important;
  // }

  .v-table__overflow {
    & > .v-datatable {
      & > tbody {
        min-height: calc(100vh - 57px - 50px - 51px - 40px - 8px - 30px);
        max-height: calc(100vh - 57px - 50px - 51px - 40px - 8px - 30px);
      }
    }
  }
}

.client-invoice-accounting-table {
  .client-invoice-invoiceId-column-header,
  .client-invoice-invoiceId-column-cell {
    width: 150px !important;

    @media screen and (max-width: 2300px) {
      width: 100px !important;
    }
  }

  .client-invoice-checkbox-column-header {
    width: 110px !important;

    @media screen and (max-width: 2300px) {
      width: 80px !important;
    }
  }

  .client-invoice-checkbox-column-cell {
    width: 110px !important;
    padding-left: 12px !important;

    @media screen and (max-width: 2300px) {
      width: 80px !important;
    }
  }

  .client-invoice-name-column-header,
  .client-invoice-name-column-cell {
    width: 300px;

    @media screen and (max-width: 2300px) {
      width: 200px !important;
    }
  }

  .client-invoice-tradingTermName-column-header,
  .client-invoice-tradingTermName-column-cell {
    width: 150px !important;

    @media screen and (max-width: 2300px) {
      width: 135px !important;
    }
  }

  .client-invoice-dueDate-column-header,
  .client-invoice-dueDate-column-cell {
    width: 150px !important;

    @media screen and (max-width: 2300px) {
      width: 100px !important;
    }
  }

  .client-invoice-number-of-jobs-column-header,
  .client-invoice-number-of-jobs-column-cell {
    width: 150px !important;

    @media screen and (max-width: 2300px) {
      width: 110px !important;
    }
  }

  .client-invoice-margins-column-header,
  .client-invoice-margins-column-cell {
    width: 366px !important;

    @media screen and (max-width: 2300px) {
      width: 300px !important;
    }
  }

  .client-invoice-margins-column-header {
    text-align: center !important;
  }

  .client-invoice-freight-column-header,
  .client-invoice-freight-column-cell {
    width: 20% !important;
  }

  .client-invoice-additional-column-header,
  .client-invoice-additional-column-cell {
    width: 20% !important;
  }

  .client-invoice-fuel-column-header,
  .client-invoice-fuel-column-cell {
    width: 20% !important;
  }

  .client-invoice-gst-column-header,
  .client-invoice-gst-column-cell {
    width: 20% !important;
  }

  .client-invoice-invoiced-column-header,
  .client-invoice-invoiced-column-cell {
    width: 20% !important;
  }

  .client-invoice-invoiced-column-cell {
    padding-right: 12px !important;
  }

  .client-invoice-document-column-header,
  .client-invoice-document-column-cell {
    width: 20% !important;
  }

  @include setDataTableStyles(57px + 50px + 51px + 40px + 8px + 30px);
}

.client-invoice-accounting-table-client-selected {
  @include setDataTableStyles(200px);
  .v-datatable__expand-row {
    height: 0 !important;
    border: 0 !important;
    display: none;
  }
}

.ledger-table-selected-row {
  .v-datatable__expand-row {
    height: 0 !important;
    border: 0 !important;
    display: none;
  }
  .accounting-job-table-inner {
    @include setDataTableStyles(418px);
  }
}

.search-ledger-table {
  @include setDataTableStyles(333px);
  .accounting-job-table-inner {
    @include setDataTableStyles(466px);
  }
}

.ledger-table-dialog {
  @include setDataTableStyles(407px);
  .accounting-job-table-inner {
    @include setDataTableStyles(540px);
  }
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.rcti-accounting-table {
  .checkbox-column-header,
  .checkbox-column-cell {
    width: 80px !important;

    @media screen and (max-width: 2300px) {
      width: 60px !important;
    }
  }

  .checkbox-column-cell {
    padding-left: 12px !important;
  }

  .invoiceId-column-header,
  .invoiceId-column-cell {
    width: 120px !important;

    @media screen and (max-width: 2300px) {
      width: 100px !important;
    }
  }

  .ownerName-column-header,
  .ownerName-column-cell {
    width: 230px !important;

    @media screen and (max-width: 2300px) {
      width: 180px !important;
    }
  }

  .numberOfFleetAssets-column-header,
  .numberOfFleetAssets-column-cell {
    width: 150px;

    @media screen and (max-width: 2300px) {
      width: 100px !important;
    }
  }

  .tradingTermName-column-cell,
  .tradingTermName-column-header {
    width: 150px !important;

    @media screen and (max-width: 2300px) {
      width: 120px !important;
    }
  }

  .dueDate-column-cell,
  .dueDate-column-header {
    width: 81px !important;
  }

  .taxInvoiceAmountExclGst-column-cell,
  .total-column-cell {
    padding-left: 45px !important;
    @media screen and (max-width: 1650px) {
      padding-left: 10px !important;
    }
  }

  .rctiAmount-column-header,
  .rctiAmount-column-cell,
  .rctiGst-column-cell,
  .rctiGst-column-header,
  .taxAllowanceExclGst-column-cell,
  .taxAllowanceExclGst-column-header,
  .taxAllowanceGst-column-header,
  .taxAllowanceGst-column-cell,
  .taxInvoiceAmountExclGst-column-cell,
  .taxInvoiceAmountExclGst-column-header,
  .taxInvoiceAmountGst-column-header,
  .taxInvoiceAmountGst-column-cell,
  .total-column-cell,
  .total-column-header,
  .action-column-cell,
  .action-column-header {
    width: calc(
      (100% / 8) - ((80px + 120px + 230px + 150px + 80px + 160px) / 8)
    );

    @media screen and (max-width: 2300px) {
      width: calc(
        (100% / 8) - ((60px + 100px + 180px + 120px + 80px + 160px) / 8)
      );
    }
  }
  @include setDataTableStyles(57px + 50px + 51px + 40px + 8px + 30px);
}

.fleet-asset-accounting-table {
  .checkbox-column-header,
  .checkbox-column-cell {
    width: 80px !important;

    @media screen and (max-width: 2300px) {
      width: 60px !important;
    }
  }

  .checkbox-column-cell {
    padding-left: 12px !important;
  }

  .csrAssignedId-column-header,
  .csrAssignedId-column-cell {
    width: 162px !important;

    @media screen and (max-width: 2300px) {
      width: 90px !important;
    }
  }

  .numberOfJobs-column-header,
  .numberOfJobs-column-cell {
    width: 151px !important;
    box-sizing: border-box !important;

    @media screen and (max-width: 2300px) {
      width: 103px !important;
      max-width: 103px !important;
    }
  }

  .tradingTermName-column-cell,
  .tradingTermName-column-header {
    width: 150px !important;

    @media screen and (max-width: 2300px) {
      width: 120px !important;
    }
  }

  .dueDate-column-cell,
  .dueDate-column-header {
    width: 80px !important;
  }

  .taxInvoiceAmountExclGst-column-cell,
  .total-column-cell {
    padding-left: 45px !important;
    @media screen and (max-width: 1650px) {
      padding-left: 10px !important;
    }
  }

  .action-column-cell,
  .action-column-header {
    padding-right: 16px !important;
  }

  .fleet-asset-total-column-header,
  .fleet-asset-total-column-cell {
    padding-right: 12px !important;
  }

  .v-table__overflow {
    border-top: 0;
    overflow: visible !important;
    & > .v-datatable {
      & > thead {
        display: none !important;
      }

      & > tbody {
        min-height: 100% !important;
        max-height: 100% !important;
        overflow: hidden !important;
      }
    }
  }

  .accounting-job-list-table {
    .v-table__overflow {
      & > .v-datatable {
        & > thead {
          display: table !important;
        }
      }
    }

    .job-list-checkbox-column-cell,
    .job-list-checkbox-column-header {
      width: 80px !important;
    }

    .job-list-checkbox-column-cell {
      padding-left: 12px !important;
    }

    .job-list-date-column-header,
    .job-list-date-column-cell {
      width: 150px !important;
    }

    .job-list-jobId-column-header,
    .job-list-jobId-column-cell {
      width: 150px !important;

      @media screen and (max-width: 2300px) {
        width: 100px !important;
      }
    }

    .job-list-name-column-header,
    .job-list-name-column-cell {
      width: 350px !important;

      @media screen and (max-width: 2300px) {
        width: 250px !important;
      }
    }

    .job-list-viewJob-column-header {
      padding-left: 50px !important;
    }
  }
}
// Styles for our main accounting equipment hire table
.equipment-hire-accounting-table {
  .checkbox-column-header,
  .checkbox-column-cell {
    width: 80px !important;

    @media screen and (max-width: 2300px) {
      width: 60px !important;
    }
  }

  .checkbox-column-cell {
    padding-left: 12px !important;
  }

  .invoiceId-column-header,
  .invoiceId-column-cell {
    width: 120px !important;

    @media screen and (max-width: 2300px) {
      width: 100px !important;
    }
  }

  .ownerName-column-header,
  .ownerName-column-cell {
    width: 350px !important;

    @media screen and (max-width: 2300px) {
      width: 180px !important;
    }
  }

  .tradingTermName-column-cell,
  .tradingTermName-column-header {
    width: 120px !important;

    @media screen and (max-width: 2300px) {
      width: 120px !important;
    }
  }

  .dueDate-column-cell,
  .dueDate-column-header {
    width: 120px !important;

    @media screen and (max-width: 2300px) {
      width: 120px !important;
    }
  }

  .rate-column-cell,
  .rate-column-header {
    width: 80px !important;

    @media screen and (max-width: 2300px) {
      width: 80px !important;
    }
  }

  .numberOfDaysCharged-column-cell,
  .numberOfDaysCharged-column-header {
    width: 120px !important;

    @media screen and (max-width: 2300px) {
      width: 120px !important;
    }
  }
  @include setDataTableStyles(57px + 50px + 51px + 40px + 8px + 30px);
}
// Css styles for our child accounting equipment hire table. The table that shows the assets. A child element to the main table.
.equipment-hire-asset-accounting-table {
  .csrAssignedId-column-cell,
  .csrAssignedId-column-header {
    width: 282px !important;
    @media screen and (max-width: 2300px) {
      width: 94px !important;
    }
  }

  .numberOfDaysCharged-column-cell,
  .numberOfDaysCharged-column-header {
    width: 120px;
    @media screen and (max-width: 2300px) {
      width: 120px !important;
    }
  }

  .contractNumber-column-cell,
  .contractNumber-column-header {
    width: 120px !important;

    @media screen and (max-width: 2300px) {
      width: 120px !important;
    }
  }

  .documentNumber-column-cell,
  .documentNumber-column-header {
    width: 120px !important;

    @media screen and (max-width: 2300px) {
      width: 120px !important;
    }
  }

  .rate-column-cell,
  .rate-column-header {
    width: 80px !important;

    @media screen and (max-width: 2300px) {
      width: 80px !important;
    }
  }

  .rctiAmount-column-header,
  .rctiAmount-column-cell,
  .rctiGst-column-cell,
  .rctiGst-column-header,
  .taxAllowanceExclGst-column-cell,
  .taxAllowanceExclGst-column-header,
  .taxAllowanceGst-column-header,
  .taxAllowanceGst-column-cell,
  .taxInvoiceAmountExclGst-column-cell,
  .taxInvoiceAmountExclGst-column-header,
  .taxInvoiceAmountGst-column-header,
  .taxInvoiceAmountGst-column-cell,
  .total-column-cell,
  .total-column-header {
    // width: calc((100% / 8) - ((80px + 662px + 191px + 170px) / 8)) !important;
    width: 190px;

    @media screen and (max-width: 2300px) {
      width: 148.5px !important;
    }
  }

  .v-table__overflow {
    border-top: 0;
    table-layout: fixed !important;
    width: 100% !important;

    & > .v-datatable {
      & > thead {
        display: none !important;
      }

      & > tbody {
        min-height: 100% !important;
        max-height: 100% !important;
        overflow: hidden !important;
      }
    }
  }
}

.rounded-input.v-input .v-input__slot {
  border-radius: 20px;
  border: 2px solid $primary-text-color !important;
  background-color: $highlight-dark !important;
  transform: scale(0.8);
  transform-origin: center;
  // transform-origin: right;
}

.side-menu__button {
  text-align: center;
  padding: 7px 0px;

  $side-padding: 16px;

  .button-label {
    font-size: $font-size-12;
    text-transform: uppercase;
    font-weight: 500;
    color: var(--border-color);
    padding-left: $side-padding;
    letter-spacing: 0.02em;
  }
  .button-icon__container {
    padding-right: $side-padding;
    .button-icon__icon {
      transition: 0s;
      font-size: $font-size-18;
      &.icon-done {
        color: var(--primary-light);
        font-weight: 700;
      }
      &.icon-pending {
        font-weight: 500;
        color: var(--background-color-650);
      }
    }
  }

  &.menu-item-disabled {
    pointer-events: none;
    .button-label {
      color: $app-dark-primary-550;
    }
    .button-icon__container {
      .button-icon__icon {
        color: $app-dark-primary-550;
      }
    }
  }

  &:hover {
    color: var(--text-color) !important;
    filter: brightness(110%);
    cursor: pointer;
  }

  &.active-state {
    background-color: black;
    color: var(--text-color);
    .button-label {
      color: var(--text-color);
    }
    .button-icon__container {
      .button-icon__icon {
        color: var(--text-color);
      }
    }
  }
}

.alert-data-table {
  border-collapse: collapse;
  width: 100%;
  thead {
    th {
      text-align: center;
      font-weight: 600;
      padding: 4px 8px 10px 4px;
      font-size: $font-size-16;

      @media (max-width: 1263px) {
        text-wrap: nowrap;
        font-size: $font-size-14;
      }
    }

    th:first-child {
      text-align: left;
    }
    th:last-child {
      text-align: right;
    }
  }
  tbody {
    tr:nth-child(odd) {
      background-color: var(--table-row);
    }
    tr {
      td {
        text-align: center;
        padding: 4px 8px;
        font-size: $font-size-14;
        @media (max-width: 1263px) {
          font-size: $font-size-12;
        }
      }

      td:first-child {
        text-align: left;
      }
      td:last-child {
        text-align: right;
      }
    }
  }
}

.simple-data-table {
  border-collapse: collapse;
  width: 100%;
  thead {
    th {
      color: var(--light-text-color);
      text-align: center;
      font-weight: 600;
      padding: 4px 8px 10px 4px;
      font-size: $font-size-16;

      @media (max-width: 1263px) {
        text-wrap: nowrap;
        font-size: $font-size-14;
      }
    }

    th:first-child {
      text-align: left;
    }
    th:last-child {
      text-align: right;
    }
  }
  tbody {
    color: var(--text-color);
    tr:nth-child(odd) {
      background-color: var(--table-row);
    }
    tr {
      // color: var(--text-color);
      &.section-start {
        border-top: 2px solid var(--border-color);
      }
      td {
        text-align: center;
        padding: 4px 8px;
        font-size: $font-size-14;
        @media (max-width: 1263px) {
          font-size: $font-size-12;
        }
      }

      td:first-child {
        text-align: left;
      }
      td:last-child {
        text-align: right;
      }

      &.row-selectable {
        &:hover {
          cursor: pointer;
          background-color: var(--background-color-250);
        }
      }

      &.row-selected {
        background-color: var(--info);
        color: var(--text-color);
        &:hover {
          background-color: var(--info);
        }
      }
    }
  }
}

.dashboard-card {
  padding: 8px 16px;
  margin: 10px 0px;
  &:first-child {
    margin-top: 0px;
  }
  &:last-child {
    margin-bottom: 0px;
  }
  border-radius: $border-radius-sm;
  transition: 0.3s;

  &.no-hover {
    &:hover {
      cursor: auto;
    }
  }

  &:hover {
    cursor: pointer;
  }

  &.item-active,
  &.v-dragged {
    .dashboard-card__toprow {
      .title-text {
        color: white;
      }
      .subtitle-text {
        color: white;
      }
      .arrow-icon {
        color: white;
      }
    }
    .dashboard-card__bottomrow {
      .icon-container {
        box-shadow: $shadow-primary;
        background-color: $translucent;
        .header-icon {
          color: white;
        }
      }
      .item-count {
        color: white;
        font-weight: 600;
      }
    }
  }

  .dashboard-card__toprow {
    color: var(--text-color);
    padding-bottom: 4px;
    .title-text {
      font-size: $font-size-15;
      font-weight: 600;
      font-family: $sub-font-family;

      @media (max-width: 1263px) {
        font-size: $font-size-13;
      }
    }
    .subtitle-text {
      color: var(--light-text-color);
      font-size: $font-size-12;
      font-weight: 400;
    }
    .arrow-icon {
      color: var(--light-text-color);
      font-size: $font-size-16;
    }
  }
  .dashboard-card__bottomrow {
    .icon-container {
      border-radius: 48px;
      // padding: 8px;
      background: transparent;
      height: 32px;
      width: 32px;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
      .header-icon {
        color: var(--primary);
        font-size: $font-size-20;
      }
      &.disabled {
        background-color: $custom-input-label;
      }
    }
    .item-count {
      color: var(--text-color);
      font-family: $sub-font-family;
      font-size: $font-size-24;
      padding-left: 16px;
      font-weight: 400;
    }
  }
}

.priority-button-group {
  $error-icon: '\f071';
  $warn-icon: '\f06a';
  $border-radius: 12px;
  $font-size: $font-size-15;
  $small-font-size: $font-size-13;

  display: flex;
  flex-direction: row;
  color: $border-light-client;
  font-weight: 700;
  font-size: $font-size;
  border-radius: $border-radius;
  border: 1px solid $border-light;
  background-color: $translucent-bg;

  &.light {
    color: $border-light;
    border-color: $border-light-client;
    .priority-button.active {
      background-color: var(--highlight);
    }
  }

  &.small {
    font-size: $small-font-size;
    .priority-button {
      padding: 4px 8px;
    }
  }

  .priority-button {
    color: var(--light-text-color);
    transition: 0.3s;
    // Add rounded borders to first and last child
    &:first-child {
      border-top-left-radius: $border-radius;
      border-bottom-left-radius: $border-radius;
    }
    &:last-child {
      border-top-right-radius: $border-radius;
      border-bottom-right-radius: $border-radius;
    }
    padding: 8px 16px;
    &:hover {
      cursor: pointer;
      background-color: $highlight-text-button;
    }

    &.active {
      background-color: var(--background-color-600);
      filter: brightness(120%);
      -webkit-filter: brightness(120%);
    }
    &.error-type {
      &::before {
        content: $error-icon;
        font-family: $font-awesome-family;
        text-decoration: none;
        font-weight: 400;
        color: $error;
      }
    }
    &.warn-type {
      &::before {
        content: $warn-icon;
        font-family: $font-awesome-family;
        font-weight: 400;
        color: $warning-type;
      }
    }
  }
}

.button-group {
  // background-color: var(--background-color-300);
  border-top: 2px solid var(--border-color);
  border-bottom: 2px solid var(--border-color);
  position: relative;
  max-width: fit-content;

  // Center the container within its parent
  margin: 4px auto; // Centers horizontally
  .v-btn {
    border-radius: 20px;
    transition: all 0.2s ease;
    font-weight: 700;
    &:hover {
      color: $warning;
      scale: 1.05;
      border-color: $warning;
    }

    &:focus {
      border: 2px solid var(--warning) !important;
      // box-shadow: 0 0 5px var(--primary-light);
    }
  }
  .v-btn--active {
    scale: 1.1;
    border: 1px solid var(--warning);
    color: var(--warning);
  }
}

.select-contact-type-items {
  cursor: pointer;
  padding: 8px 15px;
  border-radius: 18px;
  margin: 15px 8px;
  border: 2px solid var(--border-color);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  transition: all 0.1s;

  &.disabled {
    pointer-events: none;
    cursor: not-allowed;

    .tooltiptxt {
      padding: 4px 12px 10px 2px;
      border-radius: 10px;
      font-size: $font-size-14;
      font-weight: 600;
      background: var(--overlay-background);
      color: var(--light-text-color);
      .v-icon {
        margin-top: 4px;
        color: var(--light-text-color) !important;
      }
    }
    .item-value {
      opacity: 0.5;
    }
    .item-des {
      opacity: 0.4;
    }
  }

  .item-value {
    font-size: $font-size-18;
    font-weight: 800;
    color: var(--text-color);
  }
  .item-des {
    margin-top: 6px;
    font-weight: 400;
    color: var(--text-color);
    opacity: 0.7;
  }
}
.select-contact-type-items:hover {
  margin-left: 2px;
  box-shadow: $box-shadow-dark;
  background-color: var(--primary);
  border: 1px solid var(--text-color-light);
}

.invoice-index {
  padding: 0px;

  .invoice-index__header-bar {
    background-color: var(--background-color-300);
    padding: 0px 0px;
    border-bottom: 1px solid $translucent;
    border-radius: 0 0 30px 0;

    .invoice-index__header-card {
      position: relative;
      padding: 14px 14px 14px 26px;
      background-color: var(--background-color-300);
      height: 50px;
      width: 260px;
      transition: 0.2s ease;
      cursor: pointer;

      &.disabled {
        pointer-events: none;
        background-color: var(--background-color-200);

        .card-text {
          color: var(--background-color-400);
        }

        .offset-chevron {
          border-left: 12px solid var(--background-color-200) !important;
        }

        &.active-header__client {
          color: var(--text-color);
          background-color: var(--background-color-300);

          .offset-chevron {
            border-left: 12px solid var(--background-color-200) !important;
          }
        }

        &.active-header__driver {
          background-color: var(--background-color-300);

          .offset-chevron {
            border-left: 12px solid var(--background-color-200) !important;
          }
        }
      }

      &:hover {
        background-color: var(--background-color-400);

        .offset-chevron {
          border-left: 12px solid var(--background-color-400);
        }
      }

      &.active-header__client {
        transition: 0.2s ease;
        padding: 14px 32px 12px 30px;
        background-color: var(--background-color-400);
        border-bottom: 2px solid $warning;

        &:hover {
          background-color: var(--background-color-500);
          border-bottom: 2px solid $average;

          .offset-chevron {
            border-left: 12px solid var(--background-color-500);
          }
        }

        .card-text {
          color: var(--text-color);
        }

        .offset-chevron {
          border-left: 12px solid var(--background-color-400);
        }
      }

      &.active-header__driver {
        transition: 0.2s ease;
        padding: 14px 32px 12px 30px;
        background-color: var(--background-color-400);
        border-bottom: 2px solid #3be4b1;

        &:hover {
          background-color: var(--background-color-400);
          border-bottom: 2px solid #70fad1;

          .offset-chevron {
            border-left: 12px solid var(--background-color-500);
          }
        }

        .card-text {
          color: #ffffff;
        }

        .offset-chevron {
          border-left: 12px solid var(--background-color-400);
        }
      }

      .card-text {
        font-size: 1em;
        text-transform: uppercase;
        font-weight: 500;
        letter-spacing: 0.03em;
        padding: 4px 0px;
        transition: 0.15s ease;
        color: #4e4e4e;
        border-bottom: 2px solid transparent;
        -webkit-user-select: none;
        /* Safari */
        -moz-user-select: none;
        /* Firefox */
        -ms-user-select: none;
        /* IE10+/Edge */
        user-select: none;
        /* Standard */
      }

      .tally-text {
        position: relative;
        bottom: 8px;
        left: 3px;
        padding: 2px 4px;
        background-color: rgb(255, 136, 0);
        font-size: $font-size-small;
        font-weight: 600;
        color: white;
        border-radius: 10px;
      }

      .offset-chevron {
        transition: 0.2s ease;
        width: 0;
        height: 0;
        border-top: 25px solid transparent;
        border-left: 12px solid var(--background-color-300);
        border-bottom: 25px solid transparent;
        position: absolute;
        right: -12px;
        top: 0;
        z-index: 3;
      }
    }
  }
  .invoice-index-table-container {
    background-color: var(--background-color-250);
    padding: 0px 12px;
  }
}

.division-details-key-information {
  background-color: var(--background-color-250);
  padding-top: 70px;
  padding-bottom: 70px;
}

.top-panel {
  position: fixed;
  transition: 0.2s;
  background-color: var(--background-color-400);
  top: 40px;
  height: 49px;
  left: calc(25% + 37px);
  width: calc(75% - 37px);
  border-bottom: 1px solid $translucent;
  padding: 0 8px;
  display: flex;
  align-items: center;
  z-index: 9;
}

.draggable-container {
  .dashboard-card {
    .dashboard-card__toprow {
      .title-text {
        letter-spacing: 0.7px;
        color: var(--text-color);
        &.disabled {
          color: var(--light-text-color);
          opacity: 0.5;
        }
      }
      .dashboard-card__bottomrow {
        .icon-container {
          background-color: var(--background-color-200);
          &.disabled {
            opacity: 0.8;
            background-color: none !important;
            background: none !important;
            .header-icon {
              color: var(--light-text-color);
              opacity: 0.4;
            }
          }
          &.disabled {
            border: 1px solid var(--light-text-color);
            opacity: 0.5;
          }
        }
      }
    }
  }
}

.v-btn__content {
  .v-icon.fa-ellipsis-v {
    color: $primary-text-color !important;
  }
}

.action-bar {
  position: fixed;
  transition: 0.2s;
  background-color: var(--background-color-400);
  bottom: 0px;
  height: 52px;
  left: calc(25% + 37px);
  width: calc(75% - 37px);
  border-top: 1px solid $translucent;
  padding: 0 8px;
  z-index: 200;
  .v-btn {
    min-width: 182px;
    min-height: 38px;
    margin-right: 18px;

    border-radius: $border-radius-sm !important;
    &:hover {
      cursor: pointer;
      transition: 0.3s;
      scale: 1.1;
    }

    &.email {
      border: 1px solid var(--primary-light) !important;
      background-color: var(--primary) !important;
    }

    &.download {
      border: 1px solid $accent !important;
      background-color: $info !important;
    }
  }
}

.ledger-table-header {
  height: 50px;
}

.show-asterisk-marker-left {
  // Add a marker to indicate something is
  &:after {
    content: '*';
    color: var(--primary-light);
    position: absolute;
    font-size: $font-size-18;
    font-weight: 600;
    left: -10px;
    top: -4px;
  }
}

.asterisk-message {
  font-size: $font-size-11;
  color: var(--light-text-color);
  margin: 0px;
  padding: 0px 10px;
  position: relative;

  &:after {
    content: '*';
    color: var(--primary-light);
    position: absolute;
    font-size: $font-size-18;
    font-weight: 600;
    left: -2px;
    top: -4px;
  }
}

.theme--dark.v-icon {
  color: var(--text-color);
}
.theme--dark.v-icon.v-icon--disabled {
  color: var(--disabled-text-color) !important;
}

.theme--dark.v-table {
  color: var(--light-text-color) !important;
  thead th {
    color: var(--light-text-color) !important;
  }
}

.theme--dark {
  .v-btn.v-btn--disabled {
    color: var(--disabled-text-color) !important;
    .v-icon {
      color: var(--disabled-text-color) !important;
    }
  }
}

.theme--dark.v-icon.v-icon--disabled {
  color: var(--disabled-text-color) !important;
}

// MAIN-HEADERS
h1 {
  color: var(--primary);
  font-weight: 700;

  &.header--light {
    color: var(--primary-light);
    font-weight: 500;
    font-size: $font-size-22;

    @media (max-width: 1280px) {
      font-size: $font-size-20;
    }
  }
}

h2 {
  font-weight: 700;
  text-transform: uppercase;
  font-size: $font-size-17;
  color: var(--text-color);
  @media (max-width: 1280px) {
    font-size: $font-size-15;
  }
}

// SUB HEADERS
h3 {
  // used in GTitle
  font-family: $sub-font-family;
  font-weight: 700;
  font-size: $font-size-20;
  letter-spacing: 0.75px;
  color: var(--text-color);
  @media (max-width: 1280px) {
    font-size: $font-size-18;
  }

  &.subheader--light {
    font-weight: 600;
    font-size: $font-size-18;
    color: var(--primary-light);
    @media (max-width: 1280px) {
      font-size: $font-size-16;
    }
  }
}

// H3 subtitle // used in GTitle
h4 {
  font-family: $sub-font-family;
  font-size: $font-size-15;
  font-weight: 500;
  color: var(--accent);
  @media (max-width: 1280px) {
    font-size: $font-size-13;
  }
}

// FORM HEADERS
h5 {
  &.subheader--bold {
    font-family: $sub-font-family;
    letter-spacing: 0.2px;
    font-weight: 700;
    font-size: $font-size-16;
    color: var(--bg-light);
    text-transform: uppercase;
    padding-bottom: 6px;

    @media (max-width: 1280px) {
      font-size: $font-size-14;
    }

    &--12 {
      color: var(--bg-light);
      text-transform: uppercase;
      padding-bottom: 6px;
      font-size: $font-size-12;

      @media (max-width: 1280px) {
        font-size: $font-size-10;
      }
    }

    &--14 {
      color: var(--bg-light);
      text-transform: uppercase;
      padding-bottom: 6px;
      font-size: $font-size-14;
    }
    @media (max-width: 1280px) {
      font-size: $font-size-12;
    }
  }
}

// FORM SUBHEADER
h6 {
  color: var(--heading-text-color);
  font-size: $font-size-14;
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  text-transform: uppercase;
  padding-bottom: 6px;

  &.subheader--faded {
    font-weight: 500;
    display: block;
    text-align: right;
    width: 100%;
  }

  @media (max-width: 1280px) {
    font-size: $font-size-12;
  }
}

.v-btn-rounded {
  border-radius: $border-radius-Xlg;
}

.booking-icon-btn {
  margin: 6px;
  height: 36px;
  width: 48px;
  background: var(--bg-light-blue) !important;
  border-radius: 50px !important;
  transition: all 0.3s ease !important;
  .v-icon {
    color: var(--background-color-300) !important;
  }
  &:hover {
    scale: 1.1 !important;
    width: 56px;
    background-color: $info !important;
    .v-icon {
      color: white !important;
    }
  }
}

// Responsive Styles
@media (max-width: 1500px) {
  .booking-icon-btn {
    height: 24px;
    width: 28px;

    &:hover {
      width: 28px !important;
    }
  }
}

@media (max-width: 1024px) {
  .booking-icon-btn {
    display: none;
  }
}

.table-with-expansion {
  border: 1px solid $border-color;
  border-radius: 10px !important;

  > .v-table__overflow {
    max-height: 75vh;
    thead {
      position: sticky;
      top: 0;
      z-index: 2;
      background-color: var(--background-color-300);
    }

    overflow-y: auto;
    position: relative;
    // border: 1px solid $translucent;
    border-radius: $border-radius-base;

    > .theme--dark.v-table {
      color: var(--text-color) !important;
      background-color: var(--background-color-500);
      border-radius: 10px !important;
      border-collapse: collapse;

      > tbody {
        overflow-y: scroll !important;

        > tr {
          height: 12px;
          &:hover {
            background-color: var(--background-color-300) !important;
            box-shadow: $box-shadow;
          }
        }
      }
    }
  }
  .group-expanded {
    background-color: var(--background-color-300);
  }
}

.tab-select-container {
  border-radius: $border-radius-base;

  .tab-selector {
    background-color: var(--background-color-200);
  }

  .tab-text {
    font-size: $font-size-medium;
    font-weight: 600;
    letter-spacing: 0.5px;
    color: var(--light-text-color);
  }

  .tab-active .tab-text {
    color: var(--text-color) !important;
  }
}
