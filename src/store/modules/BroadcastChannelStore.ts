import {
  BroadcastChannelType,
  FLEET_TRACKING_CHANNEL_ID,
} from '@/helpers/NotificationHelpers/BroadcastChannelHelpers';
import { BroadcastMessage } from '@/interface-models/Generic/OperationScreenOptions/BroadcastMessage';
import { defineStore } from 'pinia';
import { Ref, ref } from 'vue';

/**
 * Pinia store for managing BroadcastChannel instances for different notification types.
 */
export const useBroadcastChannelStore = defineStore(
  'broadcastChannelStore',
  () => {
    const fleetTrackingChannel: Ref<BroadcastChannel | null> = ref(null);
    const jobListChannel: Ref<BroadcastChannel | null> = ref(null);

    const operationsBroadcastId: Ref<string> = ref('');

    /**
     * Initializes and returns a BroadcastChannel for the given type.
     * @param type The type of broadcast channel to initialize.
     * @returns The initialized BroadcastChannel instance.
     */
    function initBroadcastChannel(
      type: BroadcastChannelType,
    ): BroadcastChannel {
      switch (type) {
        case BroadcastChannelType.FLEET_TRACKING:
          if (!fleetTrackingChannel.value) {
            console.log(`Initializing BroadcastChannel for type: ${type}`);
            fleetTrackingChannel.value = new BroadcastChannel(
              FLEET_TRACKING_CHANNEL_ID,
            );
          }
          return fleetTrackingChannel.value;
        case BroadcastChannelType.JOB_LIST:
          if (!jobListChannel.value) {
            console.log(
              `Initializing BroadcastChannel for type: ${type}, with id: ${operationsBroadcastId.value}`,
            );
            jobListChannel.value = new BroadcastChannel(
              operationsBroadcastId.value,
            );
          }
          return jobListChannel.value;
        default:
          throw new Error(`Unknown broadcast channel type: ${type}`);
      }
    }

    /**
     * Gets (and initializes if necessary) the BroadcastChannel for the given type.
     * @param type The type of broadcast channel to get.
     * @returns The BroadcastChannel instance.
     */
    function getBroadcastChannel(type: BroadcastChannelType): BroadcastChannel {
      switch (type) {
        case BroadcastChannelType.FLEET_TRACKING:
          if (!fleetTrackingChannel.value) {
            return initBroadcastChannel(BroadcastChannelType.FLEET_TRACKING)!;
          }
          return fleetTrackingChannel.value;
        case BroadcastChannelType.JOB_LIST:
          if (!jobListChannel.value) {
            return initBroadcastChannel(BroadcastChannelType.JOB_LIST)!;
          }
          return jobListChannel.value;
        default:
          throw new Error(`Unknown broadcast channel type: ${type}`);
      }
    }

    /**
     * Gets the current BroadcastChannel instance for the given type, or null if not initialized.
     * @param type The type of broadcast channel.
     * @returns The BroadcastChannel instance or null.
     */
    function getCurrentBroadcastChannel(
      type: BroadcastChannelType,
    ): BroadcastChannel | null {
      switch (type) {
        case BroadcastChannelType.FLEET_TRACKING:
          return fleetTrackingChannel.value;
        case BroadcastChannelType.JOB_LIST:
          return jobListChannel.value;
        default:
          throw new Error(`Unknown broadcast channel type: ${type}`);
      }
    }

    /**
     * Closes and resets all broadcast channels.
     */
    function resetState() {
      fleetTrackingChannel.value?.close();
      fleetTrackingChannel.value = null;
      jobListChannel.value?.close();
      jobListChannel.value = null;
    }

    /**
     * Sets the broadcast ID for the operations/job list channel.
     * @param id The broadcast channel ID to use for job list.
     */
    function setOperationsBroadcastId(id: string) {
      operationsBroadcastId.value = id;
    }

    /**
     * Posts a message to the specified broadcast channel.
     * @param type The type of broadcast channel.
     * @param message The message to post.
     */
    function postMessageToChannel(
      type: BroadcastChannelType,
      message: BroadcastMessage,
    ) {
      const channel = getBroadcastChannel(type);
      if (channel) {
        channel.postMessage(message);
      } else {
        console.warn(`No broadcast channel found for type: ${type}`);
      }
    }

    /**
     * Sends a message to a broadcast channel and waits for a response with a
     * matching id.
     *
     * @param type - The broadcast channel type to send the message on.
     * @param message - The message to send (should include a unique id).
     * @param responseId - The id to match in the response.
     * @param timeout - Optional timeout in ms (default 5000).
     * @returns A Promise that resolves with the response message or rejects on
     * timeout.
     */
    async function sendMessageAndWaitForResponse(
      type: BroadcastChannelType,
      message: BroadcastMessage,
      responseId: string,
      timeout = 10000,
    ): Promise<BroadcastMessage> {
      return new Promise((resolve, reject) => {
        const channel = getBroadcastChannel(type);

        // Handler for incoming messages
        const handler = (event: MessageEvent) => {
          const data = event.data as BroadcastMessage;
          if (data && data.id === responseId) {
            channel.removeEventListener('message', handler);
            resolve(data);
          }
        };

        channel.addEventListener('message', handler);

        // Send the message
        channel.postMessage(message);

        // Timeout logic
        setTimeout(() => {
          channel.removeEventListener('message', handler);
          reject(
            new Error(`Timeout waiting for response with id: ${responseId}`),
          );
        }, timeout);
      });
    }

    /**
     * Closes the specified broadcast channel and resets its reference.
     * @param type The type of broadcast channel to close.
     */
    function closeChannel(type: BroadcastChannelType) {
      const channel = getCurrentBroadcastChannel(type);
      if (channel) {
        channel.close();
        if (type === BroadcastChannelType.FLEET_TRACKING) {
          fleetTrackingChannel.value = null;
        } else if (type === BroadcastChannelType.JOB_LIST) {
          jobListChannel.value = null;
        }
      }
    }

    return {
      setOperationsBroadcastId,
      initBroadcastChannel,
      getBroadcastChannel,
      getCurrentBroadcastChannel,
      resetState,
      postMessageToChannel,
      sendMessageAndWaitForResponse,
      closeChannel,
    };
  },
);
