import { ClientServiceRateVariations } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariations';
import { ClientServiceRateVariationsResponse } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariationsResponse';

export type ServiceRateVariationEvents = {
  /**
   * Handler for websocket request sent from the Operations portal to save or
   * update a ClientServiceRateVariations document. Called from the
   * administration screens.
   *
   * If the company or division does not match the user's credentials, an error
   * message string is returned. Otherwise, the saved or updated
   * ClientServiceRateVariations is returned.
   *
   * @request
   * * payload:   ClientServiceRateVariations
   * * endpoint:  '/clientServiceRateVariations/save'
   * @response
   * * payload:   ClientServiceRateVariations | string (error message)
   * * id:        savedClientServiceRateVariations
   * @portal OPERATIONS
   * @subscription USER
   */
  savedClientServiceRateVariations: ClientServiceRateVariations | string | null;

  /**
   * Handler for websocket request sent from the Operations portal to save or
   * update a list of ClientServiceRateVariations documents. Called from the administration
   * screens.
   *
   * If the company or division does not match the user's credentials, an error
   * message string is returned. Otherwise, the saved or updated
   * ClientServiceRateVariations[] are returned.
   *
   * @request
   * * payload:   ClientServiceRateVariations[]
   * * endpoint:  '/clientServiceRateVariations/saveAll'
   * @response
   * * payload:   ClientServiceRateVariations[] | string (error message)
   * * id:        savedClientServiceRateVariationsList
   * @portal OPERATIONS
   * @subscription USER
   */
  savedClientServiceRateVariationsList:
    | ClientServiceRateVariations[]
    | string
    | null;

  /**
   * Handler for websocket request sent from the Operations portal to fetch
   * client service rate variations for a client.
   *
   * @request
   * * payload:   ClientServiceRateVariationsRequest
   * * endpoint:
   *   '/clientServiceRateVariations/getServiceRateVariationsByClient'
   * @response
   * * payload:   ClientServiceRateVariationsResponse
   * * id:        serviceRateVariationsByClient
   * @portal OPERATIONS
   * @subscription USER
   */
  serviceRateVariationsByClient: ClientServiceRateVariationsResponse | null;

  /**
   * Handler for websocket request sent from the Operations portal to fetch a
   * list of division-level client service rate variations.
   *
   * @request
   * * payload:   ClientServiceRateVariationsStatus
   * * endpoint:
   *   '/clientServiceRateVariations/getClientServiceRateVariationsByDivision'
   * @response
   * * payload:   ClientServiceRateVariations[]
   * * id:        serviceRateVariationsByDivision
   * @portal OPERATIONS
   * @subscription USER
   */
  serviceRateVariationsByDivision: ClientServiceRateVariations[] | null;
};
