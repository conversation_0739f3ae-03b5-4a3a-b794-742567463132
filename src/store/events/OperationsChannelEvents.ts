import { OperationsChannel } from '@/interface-models/OperationsChannels/OperationsChannel';

export type OperationsChannelEvents = {
  /**
   * Request and response for saving or updating an OperationsChannel. Called
   * from frontend operations portal.
   * @request
   * * payload:   OperationsChannel
   * * endpoint:  '/operationsChannel/save'
   * @response
   * * payload:   OperationsChannel
   * * id:        savedOperationsChannel
   * @portal OPERATIONS
   * @subscription USER, DIVISION
   */
  savedOperationsChannel: OperationsChannel | null;

  /**
   * Request and response for deleting an OperationsChannel. Called from
   * frontend operations portal.
   * @request
   * * payload:   OperationsChannel
   * * endpoint:  '/operationsChannel/delete'
   * @response
   * * payload:   OperationsChannel
   * * id:        deletedOperationsChannel
   * @portal OPERATIONS
   * @subscription USER, DIVISION
   */
  deletedOperationsChannel: OperationsChannel | string | null;

  /**
   * Request and response for retrieving all OperationsChannels. Called from
   * frontend operations portal.
   * @request
   * * endpoint:  '/operationsChannel/getAll'
   * @response
   * * payload:   OperationsChannel[]
   * * id:        getAllOperationsChannels
   * @portal OPERATIONS
   * @subscription USER
   */
  getAllOperationsChannels: OperationsChannel[] | null;
};
