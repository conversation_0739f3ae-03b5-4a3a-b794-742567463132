/**
 * Defines the sequence in which a job's driver and vehicle
 * should be allocated.
 *
 * This enum is used to configure company-specific preferences for how the app
 * presents and handles job allocation.
 */
export enum JobAllocationOrder {
  /**
   * Indicates that the driver should be selected before the vehicle when
   * allocating a job.
   *
   * This is used for companies like TEAM where the driver is the primary
   * resource ie they allocate to a specific driver and the vehicle is less
   * important (since they change vehicles often
   */
  DRIVER_FIRST = 'DRIVER_FIRST',

  /**
   * Indicates that the vehicle should be selected before the driver when
   * allocating a job, ie with METEXP
   */
  VEHICLE_FIRST = 'VEHICLE_FIRST',
}
