import { AllocationNotificationType } from '@/interface-models/Company/DivisionCustomConfig/Operations/AllocationNotificationType';
import { JobAllocationOrder } from '@/interface-models/Company/DivisionCustomConfig/Operations/JobAllocationOrder';
import { PrimaryDriverIdentifier } from '@/interface-models/Company/DivisionCustomConfig/Operations/PrimaryDriverIdentifier';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';

interface IDivisionOperationDetails {
  /**
   * The default duration for loading at PICKUP stops in ms for the division.
   * When a PUDItem is created on the frontend, this value is used to set
   * PUDItem.loadTime.
   *
   * If the client that the job belongs to has a specific pickupLoadDuration
   * set, then that will take precedence.
   *
   * The order of precedence for pickupLoadDuration is:
   * 1. Client-specific pickupLoadDuration (ClientDetails.pickupLoadDuration)
   * 2. Division-specific pickupLoadDuration (this)
   * 3. Hardcoded default value of 30 mins (in ms) on the frontend
   */
  pickupLoadDuration: number | null;
  /**
   * The default duration for unloading at DROPOFF stops in ms for the
   * division. When a PODItem is created on the frontend, this value is used
   * to set PODItem.loadTime.
   *
   * If the client that the job belongs to has a specific dropoffLoadDuration
   * set, then that will take precedence.
   *
   * The order of precedence for dropoffLoadDuration is:
   * 1. Client-specific dropoffLoadDuration
   * (ClientDetails.dropoffLoadDuration)
   * 2. Division-specific dropoffLoadDuration (this)
   * 3. Hardcoded default value of 30 mins (in ms) on the frontend
   */
  dropoffLoadDuration: number | null;
  /**
   * Determines the order in which job resources (driver and vehicle) are
   * allocated.
   *
   * This can be either DRIVER_FIRST or VEHICLE_FIRST. This is used on the
   * frontend to determine the process for allocation to a job, including the
   * order that of the inputs are shown, the requests etc.
   */
  jobAllocationOrder: JobAllocationOrder | null;

  /**
   * Determines the type of notification that should be sent to a driver when
   * a job is allocated to them. Sent when a job is progressed from
   * PREALLOCATED to ALLOCATED.
   */
  allocationNotificationType: AllocationNotificationType | null;

  /**
   * Enum representing the primary identifier to use when displaying or referring
   * to a driver. This enum is used in division.customConfig.operations to
   * specify which field should be treated as the primary identifier for drivers
   * on the frontend
   */
  primaryDriverIdentifier: PrimaryDriverIdentifier | null;

  /**
   * Represents the rateTypeId that the fleetAsset will use if it doesn't have
   * rates available for the selected rateTypeId.
   *
   * Example:
   * - If this value is 1 (TIME), and
   * - the job rateTypeId is 5 (UNIT), and
   * - the fleetAsset does not have a UNIT rate,
   * - then the fleetAsset rates will attempt to find a TIME rateTableItem to
   * use as a fallback.
   */
  defaultRateTypeId: number | null;
  /**
   * Determine the order that the rate types will be displayed on screen on
   * the frontend. This is used alongside defaultRateTypeId to present the
   * rate types in an order that is practice for the users in that
   * company/division.
   */
  rateTypeDisplayOrder: JobRateType[] | null;

  /**
   * Determines whether the frontend allows users to create their own,
   * user-specific OperationsChannels. If this is false, then users will only
   * be able to select from predefined channels available at the division
   * level.
   */
  allowCustomOperationsChannel: boolean | null;
}

export class DivisionOperationDetails implements IDivisionOperationDetails {
  constructor(
    public pickupLoadDuration: number | null = null,
    public dropoffLoadDuration: number | null = null,
    public jobAllocationOrder: JobAllocationOrder | null = null,
    public allocationNotificationType: AllocationNotificationType | null = null,
    public primaryDriverIdentifier: PrimaryDriverIdentifier | null = null,
    public defaultRateTypeId: number | null = null,
    public rateTypeDisplayOrder: JobRateType[] | null = null,
    public allowCustomOperationsChannel: boolean | null = null,
  ) {}
}
