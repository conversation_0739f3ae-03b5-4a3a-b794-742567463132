/**
 * Used in DivisionOperationDetails to determine the type of notification that
 * should be sent to the driver when a job is allocated to them.
 */
export enum AllocationNotificationType {
  /**
   * Send a chat message to the driver (via godesta driver app) when a job is
   * allocated to them.
   */
  CHAT_MESSAGE = 'CHAT_MESSAGE',

  /**
   * Send a text message to the driver's mobile app when a job is allocated to
   * them.
   */
  SMS = 'SMS',

  /**
   * Do not send any notification to the driver when a job is allocated to
   * them. They will still receive the allocation in the driver app if they
   * are logged into that vehicle, but otherwise will not receive any
   * supplementary notification.
   */
  NONE = 'NONE',
}
