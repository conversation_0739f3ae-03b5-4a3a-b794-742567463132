/**
 * Enum representing the primary identifier to use when displaying or referring
 * to a driver. This enum is used in division.customConfig.operations to
 * specify which field should be treated as the primary identifier for drivers
 * on the frontend
 */
export enum PrimaryDriverIdentifier {
  /**
   * Use the driver's name (DriverDetails.name) as the primary identifier.
   */
  DRIVER_NAME = 'DRIVER_NAME',

  /**
   * Use the assignedId field as the primary identifier.
   */
  ASSIGNED_ID = 'ASSIGNED_ID',

  /**
   * Use the assignedId field along with the driver's name as the primary
   * identifier.
   */
  ASSIGNED_ID_WITH_NAME = 'ASSIGNED_ID_WITH_NAME',
}
