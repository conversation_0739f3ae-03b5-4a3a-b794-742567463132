import {
  IOperationsChannelFleetFilterOptions,
  OperationsChannelFleetFilterOptions,
} from '@/interface-models/OperationsChannels/OperationsChannelFleetFilterOptions';
import {
  IOperationsChannelJobFilterOptions,
  OperationsChannelJobFilterOptions,
} from '@/interface-models/OperationsChannels/OperationsChannelJobFilterOptions';
import { sessionManager } from '@/store/session/SessionState';

/**
 * Represents an Operations Channel document.
 */
export interface IOperationsChannel {
  /**
   * Identifier for the document. Generated by Mongo.
   * Mongo id of the client service rate variations document.
   */
  _id?: string;

  /**
   * The company that this document belongs to.
   */
  company: string;

  /**
   * The division within the company that this document belongs to.
   */
  division: string;

  /**
   * The user ID of the company user that this document belongs to.
   * References CompanyUserDetails#companyUserId.
   *
   * This is used for user-specific filters. Null if the document is
   * division-level.
   */
  companyUserId?: string | null;

  /**
   * Indicates whether this channel is the default channel for the division.
   * If this is true, then it means 'clearing' filters on the frontend will
   * default to this channel, rather than resetting to viewing all channels.
   *
   * Ideally this channel should result in minimal filtering, such that all
   * jobs/fleet are visible.
   */
  isDefaultChannel: boolean;

  /**
   * The name of the channel. This will be displayed on the frontend in a
   * dropdown select, where users can select from the available channels.
   */
  name: string;

  /**
   * Filter options that affect the jobs being displayed on the operations
   * dashboard.
   */
  jobFilterOptions: IOperationsChannelJobFilterOptions;

  /**
   * Filter options that affect the fleet list (drivers and vehicles) being
   * displayed on the operations dashboard.
   */
  fleetFilterOptions: IOperationsChannelFleetFilterOptions;
}

/**
 * Class implementation of OperationsChannel.
 */
export class OperationsChannel implements IOperationsChannel {
  _id?: string;
  company: string;
  division: string;
  companyUserId?: string | null;
  isDefaultChannel: boolean;
  name: string;
  jobFilterOptions: OperationsChannelJobFilterOptions;
  fleetFilterOptions: OperationsChannelFleetFilterOptions;

  _isLocalChannel: boolean;

  constructor(params: Partial<IOperationsChannel> = {}) {
    this._id = params._id;
    this.company = params.company ?? '';
    this.division = params.division ?? '';
    this.companyUserId = params.companyUserId ?? null;
    this.isDefaultChannel = params.isDefaultChannel ?? false;
    this.name = params.name ?? '';
    this.jobFilterOptions = new OperationsChannelJobFilterOptions(
      params.jobFilterOptions,
    );
    this.fleetFilterOptions = new OperationsChannelFleetFilterOptions(
      params.fleetFilterOptions,
    );
  }

  get isNew(): boolean {
    return !this._id;
  }

  get isDivisionDefault(): boolean {
    return !this.companyUserId;
  }

  get isMine(): boolean {
    return this.companyUserId === sessionManager.getUserId();
  }

  get selectName(): string {
    let suffix = '';
    if (this.isDefaultChannel) {
      suffix = '(default)';
    } else if (this.isDivisionDefault) {
      suffix = '';
    } else {
      suffix = '(personal)';
    }
    return `${this.name} ${suffix}`.trim();
  }

  get isLocalChannel(): boolean {
    return this._isLocalChannel;
  }
  set isLocalChannel(value: boolean) {
    this._isLocalChannel = value;
  }
}
