import { FilterMode } from '@/interface-models/OperationsChannels/FilterByValues';
import { FilterByValues } from '@/interface-models/OperationsChannels/FilterByValues';

/**
 * Common client-based filtering shared by Job and Fleet filters.
 *
 * Resolution rule:
 * - We treat client selection as the UNION of:
 * (a) clients derived from nationalClientIds
 * (b) clients explicitly listed in clientIds
 *
 * Both fields use FilterByValues to avoid null/empty ambiguity:
 * - ANY : no filtering by that dimension
 * - ONLY : restrict to the provided IDs (or derived IDs for national client)
 * - NONE : match nothing via that dimension
 *
 * Effective client set:
 * - If both dimensions are ANY → all clients allowed
 * - Otherwise, union of each dimension's effective set (NONE contributes empty)
 */
export interface IOperationsChannelClientFilter {
  /**
   * National client IDs to include.
   * Uses FilterByValues semantics (ANY/ONLY/NONE).
   */
  nationalClientIds: FilterByValues<string>;

  /**
   * Specific client IDs to include.
   * Uses FilterByValues semantics (ANY/ONLY/NONE).
   */
  clientIds: FilterByValues<string>;
}

export class OperationsChannelClientFilter
  implements IOperationsChannelClientFilter
{
  nationalClientIds: FilterByValues<string>;
  clientIds: FilterByValues<string>;

  constructor(params: Partial<IOperationsChannelClientFilter> = {}) {
    this.nationalClientIds = new FilterByValues(
      params.nationalClientIds ?? { mode: FilterMode.ANY },
    );
    this.clientIds = new FilterByValues(
      params.clientIds ?? { mode: FilterMode.ANY },
    );
  }
}
