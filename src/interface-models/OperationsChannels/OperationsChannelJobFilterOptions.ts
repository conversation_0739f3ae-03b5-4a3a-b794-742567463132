import { FilterByValues } from '@/interface-models/OperationsChannels/FilterByValues';
import { OperationsChannelClientFilter } from '@/interface-models/OperationsChannels/OperationsChannelClientFilter';

/**
 * Client-based filter options for jobs.
 */
export interface IOperationsChannelJobFilterOptions {
  /**
   * Client-based filter options for jobs.
   */
  clientFilter: OperationsChannelClientFilter;

  /**
   * List of service type IDs to include in the filter.
   *
   * If mode=ANY → no service type ID-based filtering (all service types are visible).
   * If mode=NONE → no service types will be visible (no jobs will be visible).
   * If mode=ONLY → only jobs where `jobDetails.serviceTypeId` is in `values`.
   */
  serviceTypeIds: FilterByValues<number>;

  /**
   * List of rate type IDs to include in the filter.
   *
   * If mode=ANY → no rate type ID-based filtering (all rate types are visible).
   * If mode=NONE → no rate types will be visible (no jobs will be visible).
   * If mode=ONLY → only jobs where `jobDetails.rateTypeId` is in `values`.
   */
  rateTypeIds: FilterByValues<number>;
}

export class OperationsChannelJobFilterOptions
  implements IOperationsChannelJobFilterOptions
{
  clientFilter: OperationsChannelClientFilter;
  serviceTypeIds: FilterByValues<number>;
  rateTypeIds: FilterByValues<number>;

  constructor(options?: Partial<IOperationsChannelJobFilterOptions>) {
    this.clientFilter = new OperationsChannelClientFilter(
      options?.clientFilter,
    );
    this.serviceTypeIds = new FilterByValues(options?.serviceTypeIds);
    this.rateTypeIds = new FilterByValues(options?.rateTypeIds);
  }
}
