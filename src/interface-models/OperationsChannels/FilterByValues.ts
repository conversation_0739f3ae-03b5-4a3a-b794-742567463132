// src/interface-models/OperationsChannels/FilterByValues.ts

/**
 * A tri-state, set-based filter used by core/supplementary filters.
 *
 * Modes:
 * - ANY : do not filter by this field (all values allowed)
 * - ONLY : include only the provided values (values must be non-empty to have effect)
 * - NONE : include nothing (hard filter to nothing)
 *
 * Notes:
 * - `values` is only used in ONLY mode. It may be null/empty in other modes.
 */
export enum FilterMode {
  ANY = 'ANY',
  ONLY = 'ONLY',
  NONE = 'NONE',
}

export interface IFilterByValues<T> {
  mode?: FilterMode;
  values?: T[] | null;
}

export class FilterByValues<T> implements IFilterByValues<T> {
  mode: FilterMode;
  values?: T[] | null;

  constructor(params?: Partial<IFilterByValues<T>>) {
    this.mode = params?.mode ?? FilterMode.ANY;
    this.values = params?.values ?? null;
  }

  /**
   * Returns true if this filter is disabled.
   */
  get isAny(): boolean {
    return this.mode === FilterMode.ANY;
  }

  /**
   * Used to toggle the isAny value to true. Also resets the values state to
   * null. Note: should not be used for setting to false, as we don't know what
   * the mode should become
   */
  set isAny(value: boolean) {
    if (value) {
      this.mode = FilterMode.ANY;
      this.values = null;
    }
  }

  /**
   * Returns true if this filter is "match nothing".
   */
  get isNone(): boolean {
    return this.mode === FilterMode.NONE;
  }
  set isNone(value: boolean) {
    if (value) {
      this.mode = FilterMode.NONE;
      this.values = null;
    }
  }

  /**
   * Returns true if this filter is "match nothing".
   */
  get isOnly(): boolean {
    return this.mode === FilterMode.ONLY;
  }
  set isOnly(value: boolean) {
    if (value) {
      this.mode = FilterMode.ONLY;
      this.values = [];
    }
  }

  /**
   * Returns true if this filter is "match nothing".
   */
  get requiresValues(): boolean {
    return !this.isAny && !this.isNone;
  }
}
