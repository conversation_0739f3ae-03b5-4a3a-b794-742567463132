/**
 * Filtering logic (evaluation order):
 *
 * 1) Start with all vehicles/drivers in scope.
 * 2) Apply client-based filtering (union of ClientFilter national/client
 * sets), resolved via ClientDetails.preAssignedVehicleDetails
 * (VEHICLE_FIRST allocation) or
 * ClientDetails.preAssignedDriverDetails (DRIVER_FIRST
 * allocation) to construct the base set of visible entities.
 * 3) Apply fleetAssetTypeIds (FilterByValues semantics; TRUCKS, TRAILERS, BOTH).
 * 4) Apply vehicleClasses (FilterByValues semantics).
 * 5) Apply ownerAffiliations (FilterByValues semantics; internal, subcontractor,
 * outside hire).
 * 6) Apply overrides: 6a) Add includeFleetAssetIds / includeDriverIds even
 * if excluded by prior filters. 6b) Remove excludeFleetAssetIds /
 * excludeDriverIds.
 */

import { OperationsChannelClientFilter } from './OperationsChannelClientFilter';
import { FilterByValues, FilterMode } from './FilterByValues';

/**
 * Options for filtering fleets in Operations Channel.
 */
export interface IOperationsChannelFleetFilterOptions {
  /**
   * Client-based filter options for fleet.
   */
  clientFilter: OperationsChannelClientFilter;

  /**
   * List of fleet asset type IDs to include in the filter.
   * Contains a list of FleetAssetDetails.fleetAssetTypeId, indicating
   * TRUCKS, TRAILERS or BOTH.
   *
   * ANY → all fleet asset types (TRUCK and TRAILER) are visible.
   * NONE → no fleet assets are visible.
   * ONLY → restrict to the provided asset types.
   */
  fleetAssetTypeIds: FilterByValues<number>;

  /**
   * List of vehicle classes to include in the filter.
   * Contains a list of FleetAssetDetails.fleetAssetTypeObject.truckClass.
   *
   * ANY → all vehicle classes are visible.
   * NONE → no vehicle classes are visible.
   * ONLY → restrict to the provided classes.
   */
  vehicleClasses: FilterByValues<string>;

  /**
   * List of affiliations to include in the filter. I.e. only show vehicles
   * owned by owners of affiliation internal, subcontractor, outside hire etc.
   *
   * ANY → vehicles for all affiliations will be visible.
   * NONE → no vehicles will be visible.
   * ONLY → restrict to the provided affiliations.
   */
  ownerAffiliations: FilterByValues<string>;

  // ---- Overrides (exceptions) ----
  // These are applied after all SetFilters above.

  /**
   * Fleet asset IDs to include even if excluded by other filters.
   * Contains a list of FleetAssetDetails.fleetAssetId.
   *
   * Null/empty → no additional inclusions.
   */
  includeFleetAssetIds?: string[];

  /**
   * Fleet asset IDs to exclude even if included by other filters.
   * Contains a list of FleetAssetDetails.fleetAssetId.
   *
   * Null/empty → no exclusions.
   */
  excludeFleetAssetIds?: string[];

  /**
   * Driver IDs to include even if excluded by other filters.
   * Contains a list of DriverDetails.driverId.
   *
   * Null/empty → no additional inclusions.
   */
  includeDriverIds?: string[];

  /**
   * Driver IDs to exclude even if included by other filters.
   * Contains a list of DriverDetails.driverId.
   *
   * Null/empty → no exclusions.
   */
  excludeDriverIds?: string[];
}

/**
 * Class implementation for OperationsChannelFleetFilterOptions.
 */
export class OperationsChannelFleetFilterOptions
  implements IOperationsChannelFleetFilterOptions
{
  clientFilter: OperationsChannelClientFilter;
  fleetAssetTypeIds: FilterByValues<number>;
  vehicleClasses: FilterByValues<string>;
  ownerAffiliations: FilterByValues<string>;
  includeFleetAssetIds?: string[];
  excludeFleetAssetIds?: string[];
  includeDriverIds?: string[];
  excludeDriverIds?: string[];

  constructor(init?: Partial<IOperationsChannelFleetFilterOptions>) {
    this.clientFilter = new OperationsChannelClientFilter(init?.clientFilter);
    this.fleetAssetTypeIds = new FilterByValues<number>(
      init?.fleetAssetTypeIds,
    );
    this.vehicleClasses = new FilterByValues<string>(init?.vehicleClasses);
    this.ownerAffiliations = new FilterByValues<string>(
      init?.ownerAffiliations,
    );
    this.includeFleetAssetIds = init?.includeFleetAssetIds;
    this.excludeFleetAssetIds = init?.excludeFleetAssetIds;
    this.includeDriverIds = init?.includeDriverIds;
    this.excludeDriverIds = init?.excludeDriverIds;
  }
}
