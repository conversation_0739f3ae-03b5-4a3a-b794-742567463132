import moment from 'moment-timezone';
import { SortDirection } from '../SearchJob/SortDirectionEnum';
import { SearchQuoteRequestSortByField } from './SearchQuoteRequestSortByFieldEnum';

export interface SearchQuoteRequest {
  /**
   * Start epoch time which is used to search the quotes using quote creation time.
   */
  startEpoch: number | null;
  /**
   * End epoch time which is used to search the quotes using quote creation time.
   */
  endEpoch: number | null;
  /**
   * Client id for which created quotes needs to be searched.
   */
  clientId: string;
  /**
   * Booked job id which is created from the quote.
   */
  jobId: string;

  /**
   * Job reference by which quotes needs to be searched.
   */
  jobReference: string;
  /**
   * Dispatcher name by which quotes needs to be searched.
   */
  dispatcherName: string;
  /**
   * Site contact name by which quotes needs to be searched.
   */
  siteContactName: string;
  /**
   * Customer delivery name by which quotes needs to be searched.
   */
  customerDeliveryName: string;
  /**
   * Suburb name by which quotes needs to be searched.
   */
  suburbName: string;
  /**
   * Booked job ids by which quotes needs to be searched.
   */
  jobIds: number[];

  /**
   * Quote id by which quote needs to be searched.
   */
  quoteId: number | null;

  /**
   * Determines what order the results are returned in, based on the quote's creation time. If not provided, no
   * sorting will be applied which inherently means the results will be returned in the order they are found in the
   * database.
   */
  sortDirection: SortDirection | null;
  /**
   * Determines what field the results are sorted by. This field is only used if sortDirection is provided. If
   * sortDirection is provided but sortBy is not, the results will be sorted by quote's creation time.
   *
   * If sortBy is ACCEPTED_DATE, the results will be sorted by the quote's accepted time field.
   * If sortBy is EXPIRY_DATE, the results will be sorted by the quote's expiry time field.
   */
  sortByField: SearchQuoteRequestSortByField | null;
}

export class SearchQuoteRequestModel implements SearchQuoteRequest {
  startEpoch: number | null;
  endEpoch: number | null;
  clientId: string;
  jobId: string;
  jobReference: string;
  dispatcherName: string;
  siteContactName: string;
  customerDeliveryName: string;
  suburbName: string;
  jobIds: number[];
  quoteId: number | null;
  sortDirection: SortDirection | null;
  sortByField: SearchQuoteRequestSortByField | null;

  constructor(data?: Partial<SearchQuoteRequest>) {
    this.startEpoch =
      data?.startEpoch ?? moment.tz(moment.tz.guess()).startOf('day').valueOf();
    this.endEpoch =
      data?.endEpoch ?? moment.tz(moment.tz.guess()).endOf('day').valueOf();
    this.clientId = data?.clientId ?? '';
    this.jobId = data?.jobId ?? '';
    this.jobReference = data?.jobReference ?? '';
    this.dispatcherName = data?.dispatcherName ?? '';
    this.siteContactName = data?.siteContactName ?? '';
    this.customerDeliveryName = data?.customerDeliveryName ?? '';
    this.suburbName = data?.suburbName ?? '';
    this.jobIds = data?.jobIds ?? [];
    this.quoteId = data?.quoteId ?? null;
    this.sortDirection = null;
    this.sortByField = null;
  }
}
