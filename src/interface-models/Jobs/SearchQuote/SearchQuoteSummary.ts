import { SearchJobsSummary } from '../SearchJob/SearchJobSummary';

export enum SearchQuoteStatusEnum {
  VALID = 'VALID',
  INVALID_EXPIRED = 'INVALID_EXPIRED',
  INVALID_PREVIOUSLY_USED = 'INVALID_PREVIOUSLY_USED',
}

export interface SearchQuoteSummary {
  /**
   * Mongo id of the quote.
   */
  id?: string;

  /**
   * Company to which quote belongs to.
   */
  company: string;

  /**
   * Division to which quote belongs to.
   */
  division: string;

  /**
   * Client id for which quote is created.
   */
  clientId: string;

  /**
   * Quote id for quote. It is created using globalData.maxQuoteId for company and division.
   * It is unique for company division.
   */
  quoteId?: number;

  /**
   * Time when quote is created in epoch millis. Set by the frontend when quote is created.
   */
  quoteCreationTime: number;

  /**
   * Time when quote will be considered as expired in epoch millis. Set by the
   * frontend when quote is created, using quoteValidityPeriod from division
   * custom config.
   */
  quoteExpiryTime: number;

  /**
   * Time when quote is accepted by client and actual job is booked out of quote in epoch millis.
   */
  quoteAcceptedTime?: number;

  /**
   * Job id of job booked when quote is accepted by client.
   */
  bookedJobId?: number;

  /**
   * Job details object which is used to book the job when job is accepted by client.
   */
  jobDetails: SearchJobsSummary;

  /**
   * Status of the quote which can be useful on the frontend to show all the searched quotes.
   * Status is calculated using booked job id, quote's expiry time and division's grace period for quote.
   *
   * VALID, if bookedJobId is null and quote's expiry time + division's grace period >= end of the day today.
   * INVALID_PREVIOUSLY_USED, if bookedJobId is not null which means quote is already converted to job.
   * INVALID_EXPIRED, if bookedJobId is null and quote's expiry time + division's grace period < end of the day today.
   */
  status: SearchQuoteStatusEnum;
}
