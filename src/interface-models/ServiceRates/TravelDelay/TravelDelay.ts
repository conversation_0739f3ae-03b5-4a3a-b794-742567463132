import {
  Demurrage,
  IDemurrage,
} from '@/interface-models/ServiceRates/Demurrage/Demurrage';

// ...but removes `graceTimeInMilliseconds` at compile time.
export class TravelDelay extends Demurrage {
  // Hide the property from the type system
  // declare readonly graceTimeInMilliseconds: 0;

  constructor(init?: Partial<IDemurrage>) {
    // Pass a dummy value for graceTimeInMilliseconds to the base constructor
    super({
      ...init,
      demurrageFuelSurchargeApplies: true,
    });
  }
}
