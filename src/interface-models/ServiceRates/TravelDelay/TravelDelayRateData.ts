import {
  DemurrageRateData,
  IDemurrageRateData,
} from '@/interface-models/ServiceRates/Demurrage/DemurrageRateData';

export class TravelDelayRateData extends DemurrageRateData {
  // Hide properties from the type system
  declare readonly zoneId: null;
  // declare readonly graceDurationInMilliseconds: 0;
  declare readonly breakOverlapDurationInMilliseconds: 0;
  declare readonly isCommonAddressDemurrageRate: false;

  constructor(init?: Partial<IDemurrageRateData>) {
    super({
      ...init,
      zoneId: null,
      // graceDurationInMilliseconds: 0,
      breakOverlapDurationInMilliseconds: 0,
      isCommonAddressDemurrageRate: false,
    });
  }
}
