import JobDistanceTravelled from '@/interface-models/Jobs/JobDistanceTravelled';

/**
 * Contains details that are required to calculate the fuel surcharge for a job.
 * This includes the distance travelled, estimated distance, suburb centres
 * distance, and actual time taken for the job.
 */
export interface JobRangeDeterminantValues extends JobDistanceTravelled {
  /**
   * The actual time taken for the job in milliseconds. This is the time
   * captured throughout the course of a job, depending on the epochTime on the
   * puds or from actual event times, depending on the status of the job.
   */
  timeTaken: number;

  /**
   * Distance rate only: contains a user editable value for the travel distance. This defaults to
   * the uneditedTravelDistance, but can be changed by the user on the
   * frontend. This is the value that is passed into the calculations.
   */
  clientEditedTravelDistance: number | undefined;

  /**
   * Distance rate only: contains a user editable value for the travel distance. This defaults to
   * the uneditedTravelDistance, but can be changed by the user on the
   * frontend. This is the value that is passed into the calculations.
   */
  fleetAssetEditedTravelDistance: number | undefined;

  /**
   * The suburb that is farthest from the pickup suburb, based on calculated
   * distances.
   */
  farthestSuburbFromPickup: string | undefined;

  /**
   * The distance in metres from the pickup suburb to the farthest delivery suburb.
   */
  pickupToFarthestDeliveryInKm: number | undefined;
}
