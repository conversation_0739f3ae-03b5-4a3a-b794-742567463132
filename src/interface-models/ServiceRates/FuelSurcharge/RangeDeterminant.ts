/**
 * Determines how the ranges should be calculated for fuel surcharges.
 */
export enum RangeDeterminant {
  /**
   * Determines that the fuel surcharge should be calculated by applying range brackets to distance travelled for
   * any job.
   */
  DISTANCE_TRAVELLED = 'DISTANCE_TRAVELLED',

  /**
   * Determines that the fuel surcharge should be calculated by applying range brackets to anticipated route for
   * any job.
   */
  ANTICIPATED_ROUTE = 'ANTICIPATED_ROUTE',

  /**
   * Determines that the fuel surcharge should be calculated by applying range brackets to suburb centres for
   * any job.
   */
  SUBURB_CENTRES = 'SUBURB_CENTRES',

  /**
   * Determines that the fuel surcharge should be calculated by applying range brackets to actual time taken by
   * any job.
   */
  ACTUAL_TIME = 'ACTUAL_TIME',
}

/**
 * Returns a human-readable string representation of a given `RangeDeterminant` enum value.
 *
 * @param rangeDeterminant - The `RangeDeterminant` enum value to convert to a readable string.
 * @returns A string describing the range determinant in a user-friendly format. Returns 'Unknown Range Determinant' if the value is not recognized.
 */
export function returnReadableRangeDeterminant(
  rangeDeterminant: RangeDeterminant,
): string {
  switch (rangeDeterminant) {
    case RangeDeterminant.DISTANCE_TRAVELLED:
      return 'Distance Travelled (GPS)';
    case RangeDeterminant.ANTICIPATED_ROUTE:
      return 'Anticipated Route';
    case RangeDeterminant.SUBURB_CENTRES:
      return 'Suburb Centres';
    case RangeDeterminant.ACTUAL_TIME:
      return 'Job Duration';
    default:
      return 'Unknown Range Determinant';
  }
}
