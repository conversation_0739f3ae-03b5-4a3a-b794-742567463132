import { RangeDeterminant } from '@/interface-models/ServiceRates/FuelSurcharge/RangeDeterminant';
import { RangedFlexRate } from '@/interface-models/ServiceRates/FuelSurcharge/RangedFlexRate';
import { RateBracketType } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/RateBracketType';

export interface FuelLevyItem {
  /**
   * Name of the fuel levy item eg “CEVA Linehaul"
   */
  name: string;

  /**
   * Rate bracket type. Can be ‘PROGRESSIVE’ or ‘ABSOLUTE’.
   */
  rateBracketType: RateBracketType;

  /**
   * Determines how the range is calculated. Can be ‘DISTANCE_TRAVELLED’ or ‘ANTICIPATED_ROUTE’ or ‘SUBURB_CENTRES’
   * or ‘ACTUAL_TIME’.
   */
  rangeDeterminant: RangeDeterminant;

  /**
   * List of actual ranged rate with rate brackets.
   */
  rateBrackets: RangedFlexRate[];
}
