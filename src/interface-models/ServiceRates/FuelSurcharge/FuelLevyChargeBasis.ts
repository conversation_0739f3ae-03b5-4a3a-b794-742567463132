/**
 * Used to determine how the rate of fuel surcharge is calculated, either PERCENTAGE or FIXED_CHARGE (dollar) amount.
 */
export enum FuelLevyChargeBasis {
  /**
   * Indicates that the charge property on fuel levy item is a percentage value.
   */
  PERCENTAGE = 'PERCENTAGE',
  /**
   * Indicates that the charge property on fuel levy item is a fixed dollar value.
   */
  FIXED_CHARGE = 'FIXED_CHARGE',
}
