/**
 * Status values for client service rate variations for division request.
 */
export enum ValidityStatus {
  /**
   * For listing only current client service rate variations for division.
   */
  CURRENT = 'CURRENT',

  /**
   * For listing only expired client service rate variations for division.
   */
  EXPIRED = 'EXPIRED',

  /**
   * For listing only future client service rate variations for division.
   */
  FUTURE = 'FUTURE',

  /**
   * For listing current and future client service rate variations for division.
   */
  CURRENT_AND_FUTURE = 'CURRENT_AND_FUTURE',

  /**
   * For listing all current as well as expired client service rate variations for division.
   */
  ALL = 'ALL',
}
