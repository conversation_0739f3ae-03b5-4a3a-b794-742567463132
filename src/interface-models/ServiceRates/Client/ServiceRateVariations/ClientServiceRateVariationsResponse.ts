import { ClientServiceRateVariations } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariations';

export interface ClientServiceRateVariationsResponse {
  /**
   * client id for which service rate variations are fetched.
   */
  clientId: string;

  /**
   * Date specified in epoch time for which client service rate variations are fetched.
   */
  searchDate: number;

  /**
   * Fetched client service rate variations as per the request
   */
  clientServiceRateVariations: ClientServiceRateVariations[];

  /**
   * List of error messages occurred while fetching client service rate variations.
   */
  recordedErrorMessages?: string[] | null;
}
