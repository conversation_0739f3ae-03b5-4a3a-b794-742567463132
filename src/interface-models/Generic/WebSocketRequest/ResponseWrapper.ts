/**
 * A generic wrapper interface for responses that ties a response payload
 * back to the original request using the requestId.
 *
 * @template T The type of the response payload
 */
export interface ResponseWrapper<T> {
  /**
   * The identifier of the originating request.
   * Should match the requestId in the corresponding RequestWrapper.
   */
  requestId: string;

  /**
   * The response payload returned for the given request.
   * May be undefined if an error occurred.
   */
  payload?: T;

  /**
   * An optional error message if the request failed.
   */
  error?: string;
}
