/**
 * A generic wrapper interface for requests that ties a payload
 * to a unique identifier (requestId).
 *
 * CURRENTLY NOT USED - kept for future implementation maybe.
 *
 * @template T The type of the request payload
 */
export interface RequestWrapper<T> {
  /**
   * A unique identifier for tracking this request/response pair.
   * Typically generated using something like UUID.
   */
  requestId: string;

  /**
   * The payload of the request, containing the data being sent.
   */
  payload: T;
}
