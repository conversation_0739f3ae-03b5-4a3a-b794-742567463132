import DriverDetailsSummary, {
  DriverDetailsSummaryI,
} from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import FleetOperationsListSummary from '@/interface-models/FleetAsset/Summary/FleetOperationsListSummary';

export interface DriverOperationsListSummaryI extends DriverDetailsSummaryI {
  associatedFleetAssets: FleetOperationsListSummary[];
}

export default class DriverOperationsListSummary
  extends DriverDetailsSummary
  implements DriverOperationsListSummaryI
{
  public associatedFleetAssets: FleetOperationsListSummary[] = [];

  /**
   * Construct from a DriverDetailsSummary and associatedFleetAssets.
   * @param summary - The base DriverDetailsSummary object.
   * @param associatedFleetAssets - The associated fleet assets for this driver.
   */
  constructor(
    summary: DriverDetailsSummary,
    associatedFleetAssets: FleetOperationsListSummary[] = [],
  ) {
    super(
      summary.id,
      summary.company,
      summary.division,
      summary.driverId,
      summary.name,
      summary.mobile,
      summary.email,
      summary.statusList,
      summary.displayName,
    );
    // Copy optional fields if present
    this.expirationSummaries = summary.expirationSummaries;
    this.complianceOverrides = summary.complianceOverrides;
    this.assignedId = summary.assignedId;
    this.associatedFleetAssets = associatedFleetAssets;
  }
}
