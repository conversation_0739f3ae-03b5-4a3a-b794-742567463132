import DriverOperationsListSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverOperationsListSummary';
import FleetAssetSummary, {
  FleetAssetSummaryI,
} from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';

export interface FleetOperationsListSummaryI extends FleetAssetSummaryI {
  associatedDriverSummaries: DriverOperationsListSummary[];
}

export default class FleetOperationsListSummary
  extends FleetAssetSummary
  implements FleetOperationsListSummaryI
{
  public associatedDriverSummaries: DriverOperationsListSummary[] = [];

  /**
   * Construct from a FleetAssetSummary and associatedDrivers.
   * @param summary - The base FleetAssetSummary object.
   * @param associatedDrivers - The associated drivers for this fleet asset.
   */
  constructor(
    summary: FleetAssetSummary,
    associatedDriverSummaries: DriverOperationsListSummary[] = [],
  ) {
    super(
      summary.company,
      summary.division,
      summary.fleetAssetId,
      summary.csrAssignedId,
      summary.fleetAssetOwnerId,
      summary.fleetAssetTypeId,
      summary.associatedDrivers,
      summary.statusList,
      summary.outsideHire,
      summary.registrationNumber,
      summary.make,
      summary.payload,
      summary.dimensions,
    );
    // Copy optional fields if present
    this._id = summary._id;
    this.trailerType = summary.trailerType;
    this.truckClass = summary.truckClass;
    this.defaultDriver = summary.defaultDriver;
    this.expirationSummaries = summary.expirationSummaries;
    this.associatedDriverSummaries = associatedDriverSummaries;
  }
}
