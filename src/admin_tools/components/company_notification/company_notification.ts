import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { hasAdminRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { VForm } from '@/interface-models/Generic/VForm/VForm';
import { Validation } from '@/interface-models/Generic/Validation';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { Component, Vue } from 'vue-property-decorator';

@Component({
  components: { InformationTooltip },
})
export default class CompanyNotification extends Vue implements IUserAuthority {
  public companyWideMessage: string = '';
  public confirmationDialog: boolean = false;

  public $refs!: {
    form: VForm;
  };

  // only GoDesta admin users should have the ability to action requests
  public isAuthorised(): boolean {
    return hasAdminRole();
  }

  get validate(): Validation {
    return validationRules;
  }
  // returns the company name for who the message will be sending out to
  get companyName(): string {
    const companyDetails = useCompanyDetailsStore().companyDetails;
    return companyDetails?.name ?? 'company';
  }

  // validates the input and sets the confirmation dialog
  public setConfirmationDialog(): void {
    if (!this.$refs.form.validate()) {
      showNotification('Company notification must include a message.');
      return;
    }
    this.confirmationDialog = true;
  }

  /**
   * Sends the message that will be shown to all company users.
   */
  public async sendCompanyWideMessage() {
    this.confirmationDialog = false;
    // Send message and display notification
    const result = await useCompanyDetailsStore().sendCompanyWideMessage(
      this.companyWideMessage,
    );
    if (result) {
      showNotification('Company wide message sent successfully.', {
        type: HealthLevel.SUCCESS,
      });
    } else {
      showNotification('Failed to send company wide message.');
    }
    this.$refs.form.resetValidation();
    this.companyWideMessage = '';
  }

  // cancels the sending of a notification.
  public cancel(): void {
    this.confirmationDialog = false;
  }
}
