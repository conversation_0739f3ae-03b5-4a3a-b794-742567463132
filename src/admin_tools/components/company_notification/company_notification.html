<v-layout class="company-notification-container" wrap>
  <v-flex md12>
    <v-layout
      class="task-bar app-theme__center-content--header no-highlight justify-space-between"
    >
      <p class="ma-0">Company Notification</p>
      <div>
        <InformationTooltip :bottom="true">
          <v-layout slot="content"
            >Sends a system alert message to all active company users.</v-layout
          >
        </InformationTooltip>
      </div>
    </v-layout>
  </v-flex>
  <v-flex md12 class="pa-2">
    <v-form ref="form">
      <v-text-field
        label="Please Enter Company Notification"
        v-model="companyWideMessage"
        hint="Send a notification to all users within your authenticated company."
        persistent-hint
        :rules="[validate.required]"
        class="v-solo-custom form-field-required"
        outline
      ></v-text-field>
      <v-layout justify-end>
        <v-btn
          color="info"
          @click="setConfirmationDialog"
          :disabled="!isAuthorised()"
          >Send Message</v-btn
        >
        <v-dialog
          v-model="confirmationDialog"
          persistent
          :width="400"
          class="ma-0"
          content-class="v-dialog-custom"
        >
          <v-card color="#242329">
            <v-layout
              justify-space-between
              align-center
              class="task-bar app-theme__center-content--header no-highlight"
            >
              <div>
                <span>Confirm Company Wide Notification</span>
              </div>

              <div
                class="app-theme__center-content--closebutton"
                @click="cancel"
              >
                <v-icon class="app-theme__center-content--closebutton--icon"
                  >fal fa-times</v-icon
                >
              </div>
            </v-layout>
            <v-layout row wrap class="px-3 pt-3 pb-2">
              <v-flex md12>
                <p>
                  Please confirm that you wish to send the notification below as
                  an alert to all active {{companyName}} users.
                </p>
                <p class="pl-1 mb-1"><b>Notification:</b></p>
                <p class="ma-0 pa-2 company-wide-notification-text">
                  {{companyWideMessage ? companyWideMessage : "N/A"}}
                </p>
              </v-flex>
            </v-layout>
            <v-divider class="mt-2"></v-divider>
            <v-layout justify-space-between>
              <v-btn color="error" depressed small @click="cancel"
                >Cancel</v-btn
              >
              <v-btn
                color="info"
                depressed
                small
                @click="sendCompanyWideMessage"
                >Confirm And Send</v-btn
              >
            </v-layout>
          </v-card>
        </v-dialog>
      </v-layout>
    </v-form>
  </v-flex>
</v-layout>
